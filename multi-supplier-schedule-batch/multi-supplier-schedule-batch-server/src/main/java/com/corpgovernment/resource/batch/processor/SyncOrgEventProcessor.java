package com.corpgovernment.resource.batch.processor;

import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.api.organization.bo.SyncOrgInfoReq;
import com.corpgovernment.api.organization.bo.SyncOrgInfoRsp;
import com.corpgovernment.api.organization.soa.IEmployeeClient;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.event.BatchExecuteContext;
import com.corpgovernment.resource.batch.event.BatchItem;
import com.corpgovernment.resource.batch.event.processor.AbstractEventProcessor;
import com.corpgovernment.resource.batch.exception.RetryBatchExecutionException;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.corpgovernment.resource.batch.validator.SyncOrgInfoValidator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class SyncOrgEventProcessor extends AbstractEventProcessor<SyncOrgInfoReq.SyncOrganizationInfo> {
    private final IEmployeeClient iEmployeeClient;

    private final SyncOrgInfoValidator validator;

    public SyncOrgEventProcessor(BatchTaskService batchTaskService, IEmployeeClient iEmployeeClient,
                                      SyncOrgInfoValidator validator) {
        super(batchTaskService);
        this.iEmployeeClient = iEmployeeClient;
        this.validator = validator;
    }

    @Override
    protected BatchItemValidator<SyncOrgInfoReq.SyncOrganizationInfo> getItemValidator() {
        return validator;
    }

    @Override
    protected BatchItemResult doProcess(BatchItem<SyncOrgInfoReq.SyncOrganizationInfo> batchItem, BatchExecuteContext context) {
        SyncOrgInfoReq req = new SyncOrgInfoReq();
        SyncOrgInfoReq.SyncOrganizationInfo value = batchItem.getValue();
        req.setOrgInfoList(Collections.singletonList(value));

        SyncOrgInfoRsp resp = iEmployeeClient.syncOrgInfo(req);
        if (resp == null) {
            return new BatchItemResult(batchItem.getId(), value.getBasicInfo().getOrgId(), BatchItemStatus.FAILED, "调用员工接口失败");
        }
        if ("200".equals(resp.getStatus())) {
            return new BatchItemResult(batchItem.getId(), value.getBasicInfo().getOrgId(), BatchItemStatus.SUCCESS, null);
        }
        return assembleBatchResult(batchItem, resp);
    }

    private BatchItemResult assembleBatchResult(BatchItem<SyncOrgInfoReq.SyncOrganizationInfo> batchItem, SyncOrgInfoRsp rsp) {
        BatchItemResult batchItemResult = new BatchItemResult();

        batchItemResult.setId(batchItem.getId());
        batchItemResult.setBizItemId(batchItem.getValue().getBasicInfo().getOrgId());
        if (rsp.getStatus() == null) {
            batchItemResult.setBatchItemStatus(BatchItemStatus.FAILED);
            batchItemResult.setErrorMessage("rsp status不能为null");
            return batchItemResult;
        }

        List<SyncOrgInfoRsp.ErrorReason> errorReasons = rsp.getErrorReasons();
        if (errorReasons == null || errorReasons.isEmpty()) {
            batchItemResult.setBatchItemStatus(BatchItemStatus.SUCCESS);
            return batchItemResult;
        }

        // Assert 一次只有一个错误
        if (errorReasons.size() == 1 && errorReasons.get(0) != null) {
            SyncOrgInfoRsp.ErrorReason errorReason = errorReasons.get(0);
            if(shouldRetry(errorReason.getMessage())) {
                throw new RetryBatchExecutionException(errorReason.getMessage());
            }
            batchItemResult.setErrorMessage(errorReason.getMessage());
            batchItemResult.setBatchItemStatus(BatchItemStatus.FAILED);
            return batchItemResult;
        }


        if (!"200".equals(rsp.getStatus())) {
            batchItemResult.setErrorMessage(rsp.getMessage());
            batchItemResult.setBatchItemStatus(BatchItemStatus.FAILED);
            return batchItemResult;
        }
        return batchItemResult;
    }

    @Override
    protected Class<SyncOrgInfoReq.SyncOrganizationInfo> getParameterType() {
        return SyncOrgInfoReq.SyncOrganizationInfo.class;
    }

    @Override
    public BatchProcessType getBatchTaskType() {
        return BatchProcessType.SYNC_ORG_INFO;
    }
}