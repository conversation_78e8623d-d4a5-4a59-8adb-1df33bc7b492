package com.corpgovernment.resource.batch.processor;

import com.corpgovernment.api.basic.request.ApplyTripTemporaryStorageRequest;
import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.api.organization.model.user.employee.OrgEmployeeVo;
import com.corpgovernment.api.organization.soa.IOrganizationEmployeeClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.dto.LisResponse;
import com.corpgovernment.resource.batch.event.BatchExecuteContext;
import com.corpgovernment.resource.batch.event.BatchItem;
import com.corpgovernment.resource.batch.event.processor.AbstractEventProcessor;
import com.corpgovernment.resource.batch.exception.RetryBatchExecutionException;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.corpgovernment.resource.batch.service.LisApplyTripLoader;
import com.corpgovernment.resource.batch.validator.SyncApplyTripValidator;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class SyncApplyTripEventProcessor extends AbstractEventProcessor<ApplyTripTemporaryStorageRequest> {
    private final LisApplyTripLoader applyTripLoader;

    private final SyncApplyTripValidator validator;

    private final IOrganizationEmployeeClient organizationEmployeeClient;

    public SyncApplyTripEventProcessor(BatchTaskService batchTaskService, LisApplyTripLoader applyTripLoader,
                                       SyncApplyTripValidator validator,IOrganizationEmployeeClient organizationEmployeeClient) {
        super(batchTaskService);
        this.applyTripLoader = applyTripLoader;
        this.validator = validator;
        this.organizationEmployeeClient = organizationEmployeeClient;
    }

    @Override
    protected BatchItemValidator<ApplyTripTemporaryStorageRequest> getItemValidator() {
        return validator;
    }

    @Override
    protected BatchItemResult doProcess(BatchItem<ApplyTripTemporaryStorageRequest> batchItem, BatchExecuteContext context) {
        ApplyTripTemporaryStorageRequest value = batchItem.getValue();
        List<String> uidList = value.getUidList();
        Boolean userOrgExists = true;
        for(String uid : uidList){
            if (!userOrgExists(uid)){
                userOrgExists = false;
                break;
            }
        }
        log.info("处理的入参信息：{}，用户和组织校验结果：{}", JsonUtils.toJsonString(value),userOrgExists);
        if(userOrgExists){
            LisResponse resp = applyTripLoader.lisApprovalSave(value.getRequestJson());
            return assembleBatchResult(batchItem, resp);
        }else{
            throw new RetryBatchExecutionException("未找到员工信息或组织信息");
        }
    }

    private Boolean userOrgExists(String uid) {
        if (StringUtils.isBlank(uid)){
            return false;
        }
        JSONResult<List<OrgEmployeeVo>> result = organizationEmployeeClient.findOrgInfoByUid(uid);
		if (result == null || result.getData() == null) {
			log.error("查询用户组织信息异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
			return false;
		}
        List<OrgEmployeeVo> orgEmployeeVoList = result.getData();
        if (CollectionUtils.isEmpty(orgEmployeeVoList)){
            return false;
        }
        return true;
    }

    private BatchItemResult assembleBatchResult(BatchItem<ApplyTripTemporaryStorageRequest> batchItem, LisResponse rsp) {
        BatchItemResult batchItemResult = new BatchItemResult();

        batchItemResult.setId(batchItem.getId());
        batchItemResult.setBizItemId(batchItem.getValue().getApplyNo());
        if (rsp.getStatus() == null) {
            batchItemResult.setBatchItemStatus(BatchItemStatus.FAILED);
            batchItemResult.setErrorMessage("rsp status不能为null");
            return batchItemResult;
        }

        if (rsp.getStatus() != null && Boolean.TRUE.equals(rsp.getStatus().getSuccess())) {
            batchItemResult.setBatchItemStatus(BatchItemStatus.SUCCESS);
            return batchItemResult;
        }

        batchItemResult.setErrorMessage(rsp.getStatus().getMessage());
        batchItemResult.setBatchItemStatus(BatchItemStatus.FAILED);
        return batchItemResult;
    }

    @Override
    protected Class<ApplyTripTemporaryStorageRequest> getParameterType() {
        return ApplyTripTemporaryStorageRequest.class;
    }

    @Override
    public BatchProcessType getBatchTaskType() {
        return BatchProcessType.SYNC_APPLYTRIP_TEMPORARYSTORAGE_INFO;
    }
}
