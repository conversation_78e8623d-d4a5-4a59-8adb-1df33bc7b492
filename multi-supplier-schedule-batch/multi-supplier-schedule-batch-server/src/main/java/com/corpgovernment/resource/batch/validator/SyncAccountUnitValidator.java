package com.corpgovernment.resource.batch.validator;

import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.organization.dto.request.accounting.unit.datasync.AccountUnitDataDto;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.event.BatchItem;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class SyncAccountUnitValidator implements BatchItemValidator<AccountUnitDataDto> {

    @Override
    public List<BatchItemResult> proactiveValidate(List<AccountUnitDataDto> items) {
        return checkAccountUnit(items);
    }

    @Override
    public BatchItemResult validate(BatchItem<AccountUnitDataDto> item) {
        return null;
    }

    public List<BatchItemResult> checkAccountUnit(List<AccountUnitDataDto> unitDataDtoList) {
        List<BatchItemResult> itemResults = new ArrayList<>();
        if (CollectionUtils.isEmpty(unitDataDtoList)) {
            return itemResults;
        }

        for (AccountUnitDataDto dataDto : unitDataDtoList) {
            BatchItemResult result = new BatchItemResult();
            result.setBizItemId(dataDto.getName());
            if (StringUtils.isBlank(dataDto.getName())) {
                result.setErrorMessage("数据名称不能为空");
                result.setBatchItemStatus(BatchItemStatus.FAILED);
                itemResults.add(result);
                continue;
            }
            if (StringUtils.isBlank(dataDto.getCode())) {
                BatchItemResult errorDetail = new BatchItemResult();
                errorDetail.setErrorMessage("数据编码不能为空");
                errorDetail.setBizItemId(dataDto.getName());
                errorDetail.setBatchItemStatus(BatchItemStatus.FAILED);
                itemResults.add(errorDetail);
                continue;
            }
            result.setBatchItemStatus(BatchItemStatus.SUCCESS);
            itemResults.add(result);
        }
        return itemResults;
    }
}
