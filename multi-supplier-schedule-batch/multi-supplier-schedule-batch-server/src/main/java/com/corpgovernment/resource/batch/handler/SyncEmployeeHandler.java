package com.corpgovernment.resource.batch.handler;

import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.api.organization.bo.SyncOrgEmployeeReq;
import com.corpgovernment.resource.batch.AbstractBatchItemHandler;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.corpgovernment.resource.batch.validator.SyncEmployeeValidator;
import org.springframework.stereotype.Component;

@Component
public class SyncEmployeeHandler extends AbstractBatchItemHandler<SyncOrgEmployeeReq.Employee> {

    private final SyncEmployeeValidator syncEmployeeValidator;

    public SyncEmployeeHandler(BatchTaskService batchTaskService, SyncEmployeeValidator validator) {
        super(batchTaskService);
        this.syncEmployeeValidator = validator;
    }

    @Override
    public BatchProcessType getBatchTaskType() {
        return BatchProcessType.SYNC_EMPLOYEE_INFO;
    }

    @Override
    public Class<SyncOrgEmployeeReq.Employee> getBatchItemClass() {
        return SyncOrgEmployeeReq.Employee.class;
    }

    @Override
    public String generateBizItemId(SyncOrgEmployeeReq.Employee item) {
        return item.getBasicInfo().getUid();
    }

    @Override
    public BatchItemValidator<SyncOrgEmployeeReq.Employee> validator() {
        return syncEmployeeValidator;
    }
}
