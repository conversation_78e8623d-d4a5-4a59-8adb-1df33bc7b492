package com.corpgovernment.resource.batch.subscriber;

import com.corpgovernment.common.mq.enums.MessageBizTypeEnum;
import com.corpgovernment.resource.batch.event.AbstractBatchEventSubscriber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SyncEmployeeRemoteSubscriber extends AbstractBatchEventSubscriber {


    @Override
    protected MessageBizTypeEnum getMessageBizType() {
        return MessageBizTypeEnum.BATCH_EXECUTION_EMPLOYEE_SYNC;
    }
}
