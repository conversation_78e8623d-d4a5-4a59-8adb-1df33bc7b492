package com.corpgovernment.resource.batch.validator;

import com.corpgovernment.api.basic.request.ApplyTripTemporaryStorageRequest;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.event.BatchItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SyncApplyTripValidator implements BatchItemValidator<ApplyTripTemporaryStorageRequest> {

    @Override
    public List<BatchItemResult> proactiveValidate(List<ApplyTripTemporaryStorageRequest> items) {
        return null;
    }

    @Override
    public BatchItemResult validate(BatchItem<ApplyTripTemporaryStorageRequest> item) {
        return null;
    }

}
