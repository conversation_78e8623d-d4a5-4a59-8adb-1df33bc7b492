package com.corpgovernment.resource.batch.processor;

import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.api.organization.bo.SyncOrgEmployeeReq;
import com.corpgovernment.api.organization.bo.SyncOrgEmployeeRsp;
import com.corpgovernment.api.organization.soa.IEmployeeClient;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.event.BatchExecuteContext;
import com.corpgovernment.resource.batch.event.BatchItem;
import com.corpgovernment.resource.batch.event.processor.AbstractEventProcessor;
import com.corpgovernment.resource.batch.exception.RetryBatchExecutionException;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.corpgovernment.resource.batch.validator.SyncEmployeeValidator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class SyncEmployeeEventProcessor extends AbstractEventProcessor<SyncOrgEmployeeReq.Employee> {
    private final IEmployeeClient iEmployeeClient;

    private final SyncEmployeeValidator validator;

    public SyncEmployeeEventProcessor(BatchTaskService batchTaskService, IEmployeeClient iEmployeeClient,
                                      SyncEmployeeValidator validator) {
        super(batchTaskService);
        this.iEmployeeClient = iEmployeeClient;
        this.validator = validator;
    }

    @Override
    protected BatchItemValidator<SyncOrgEmployeeReq.Employee> getItemValidator() {
        return validator;
    }

    @Override
    protected BatchItemResult doProcess(BatchItem<SyncOrgEmployeeReq.Employee> batchItem, BatchExecuteContext context) {
        SyncOrgEmployeeReq req = new SyncOrgEmployeeReq();
        SyncOrgEmployeeReq.Employee value = batchItem.getValue();
        req.setEmployeeList(Collections.singletonList(value));
        req.setUpdateFlag(value.getUpdateFlag());

        SyncOrgEmployeeRsp resp = iEmployeeClient.syncEmployeeInfo(req);
        if (resp == null) {
            return new BatchItemResult(batchItem.getId(), value.getBasicInfo().getUid(), BatchItemStatus.FAILED, "调用员工接口失败");
        }
        if ("200".equals(resp.getStatus())) {
            return new BatchItemResult(batchItem.getId(), value.getBasicInfo().getUid(), BatchItemStatus.SUCCESS, null);
        }
        return assembleBatchResult(batchItem, resp);
    }

    private BatchItemResult assembleBatchResult(BatchItem<SyncOrgEmployeeReq.Employee> batchItem, SyncOrgEmployeeRsp rsp) {
        BatchItemResult batchItemResult = new BatchItemResult();

        batchItemResult.setId(batchItem.getId());
        batchItemResult.setBizItemId(batchItem.getValue().getBasicInfo().getUid());
        if (rsp.getStatus() == null) {
            batchItemResult.setBatchItemStatus(BatchItemStatus.FAILED);
            batchItemResult.setErrorMessage("rsp status不能为null");
            return batchItemResult;
        }

        List<SyncOrgEmployeeRsp.ErrorReason> errorReasons = rsp.getErrorReasons();
        if (errorReasons == null || errorReasons.isEmpty()) {
            batchItemResult.setBatchItemStatus(BatchItemStatus.SUCCESS);
            return batchItemResult;
        }

        // Assert 一次只有一个错误
        if (errorReasons.size() == 1 && errorReasons.get(0) != null) {
            SyncOrgEmployeeRsp.ErrorReason errorReason = errorReasons.get(0);
            if(shouldRetry(errorReason.getMessage())) {
                throw new RetryBatchExecutionException(errorReason.getMessage());
            }
            batchItemResult.setErrorMessage(errorReason.getMessage());
            batchItemResult.setBatchItemStatus(BatchItemStatus.FAILED);
            return batchItemResult;
        }


        if (!"200".equals(rsp.getStatus())) {
            batchItemResult.setErrorMessage(rsp.getMessage());
            batchItemResult.setBatchItemStatus(BatchItemStatus.FAILED);
            return batchItemResult;
        }
        return batchItemResult;
    }

    @Override
    protected Class<SyncOrgEmployeeReq.Employee> getParameterType() {
        return SyncOrgEmployeeReq.Employee.class;
    }

    @Override
    public BatchProcessType getBatchTaskType() {
        return BatchProcessType.SYNC_EMPLOYEE_INFO;
    }
}
