package com.corpgovernment.resource.batch.validator;

import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.organization.bo.SyncOrgEmployeeReq;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.event.BatchItem;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class SyncEmployeeValidator implements BatchItemValidator<SyncOrgEmployeeReq.Employee> {

    @Override
    public List<BatchItemResult> proactiveValidate(List<SyncOrgEmployeeReq.Employee> items) {
        return checkEmployee(items);
    }

    @Override
    public BatchItemResult validate(BatchItem<SyncOrgEmployeeReq.Employee> item) {
        return null;
    }

    public List<BatchItemResult> checkEmployee(List<SyncOrgEmployeeReq.Employee> employeeList) {
        List<BatchItemResult> itemResultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(employeeList)) {
            return itemResultList;
        }

        for (SyncOrgEmployeeReq.Employee employee : employeeList) {
            if(employee.getBasicInfo() == null){
                BatchItemResult errorDetail = new BatchItemResult();
                errorDetail.setErrorMessage("员工基本信息不能为空");
                errorDetail.setBizItemId("-1");
                errorDetail.setBatchItemStatus(BatchItemStatus.FAILED);
                itemResultList.add(errorDetail);
                continue;
            }

            BatchItemResult result = checkEmployeeBaseInfo(employee.getBasicInfo());
            itemResultList.add(result);
        }
        return itemResultList;
    }

    private BatchItemResult checkEmployeeBaseInfo(SyncOrgEmployeeReq.BasicInfo basicInfo) {
        List<String> errorMsgs = new ArrayList<>();

        if (StringUtils.isBlank(basicInfo.getUid())) {
            errorMsgs.add("员工uid不能为空");
        }
        if (StringUtils.isBlank(basicInfo.getName())) {
            errorMsgs.add("员工姓名不能为空");
        }
        if (StringUtils.isBlank(basicInfo.getOrgId())) {
            errorMsgs.add("组织ID不能为空");
        }
        if (basicInfo.getStatus() == null) {
            errorMsgs.add("员工状态不能为空");
        }

        BatchItemResult result = new BatchItemResult();
        result.setBizItemId(basicInfo.getUid());
        if (!errorMsgs.isEmpty()) {
            result.setErrorMessage(String.join("；", errorMsgs));
            result.setBatchItemStatus(BatchItemStatus.FAILED);
            return result;
        }
        result.setBatchItemStatus(BatchItemStatus.SUCCESS);
        return result;
    }
}
