package com.corpgovernment.resource.batch.handler;

import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.api.organization.bo.SyncOrgInfoReq;
import com.corpgovernment.resource.batch.AbstractBatchItemHandler;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.corpgovernment.resource.batch.validator.SyncOrgInfoValidator;
import org.springframework.stereotype.Component;

@Component
public class SyncOrgHandler extends AbstractBatchItemHandler<SyncOrgInfoReq.SyncOrganizationInfo> {

    private final SyncOrgInfoValidator syncOrgInfoValidator;

    public SyncOrgHandler(BatchTaskService batchTaskService, SyncOrgInfoValidator validator) {
        super(batchTaskService);
        this.syncOrgInfoValidator = validator;
    }

    @Override
    public BatchProcessType getBatchTaskType() {
        return BatchProcessType.SYNC_ORG_INFO;
    }
    @Override
    public Class<SyncOrgInfoReq.SyncOrganizationInfo> getBatchItemClass() {
        return SyncOrgInfoReq.SyncOrganizationInfo.class;
    }

    @Override
    public String generateBizItemId(SyncOrgInfoReq.SyncOrganizationInfo item) {
        return item.getBasicInfo().getOrgId();
    }

    @Override
    public BatchItemValidator<SyncOrgInfoReq.SyncOrganizationInfo> validator() {
        return syncOrgInfoValidator;
    }
}
