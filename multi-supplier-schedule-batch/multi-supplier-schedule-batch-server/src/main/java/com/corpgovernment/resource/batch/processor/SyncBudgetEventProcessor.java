package com.corpgovernment.resource.batch.processor;

import com.corpgovernment.api.basic.request.ApplyTripTemporaryStorageRequest;
import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.api.organization.model.user.employee.OrgEmployeeVo;
import com.corpgovernment.api.organization.soa.IOrganizationEmployeeClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.dto.LisResponse;
import com.corpgovernment.resource.batch.event.BatchExecuteContext;
import com.corpgovernment.resource.batch.event.BatchItem;
import com.corpgovernment.resource.batch.event.processor.AbstractEventProcessor;
import com.corpgovernment.resource.batch.exception.RetryBatchExecutionException;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.corpgovernment.resource.batch.service.LisBudgetLoader;
import com.corpgovernment.resource.batch.validator.SyncApplyTripValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class SyncBudgetEventProcessor extends AbstractEventProcessor<ApplyTripTemporaryStorageRequest> {
    private final LisBudgetLoader budgetLoader;

    private final SyncApplyTripValidator validator;


    public SyncBudgetEventProcessor(BatchTaskService batchTaskService, LisBudgetLoader budgetLoader,
                                    SyncApplyTripValidator validator) {
        super(batchTaskService);
        this.budgetLoader = budgetLoader;
        this.validator = validator;
    }

    @Override
    protected BatchItemValidator<ApplyTripTemporaryStorageRequest> getItemValidator() {
        return validator;
    }

    @Override
    protected BatchItemResult doProcess(BatchItem<ApplyTripTemporaryStorageRequest> batchItem, BatchExecuteContext context) {
        ApplyTripTemporaryStorageRequest value = batchItem.getValue();
        try{
            LisResponse resp = budgetLoader.lisBudgetSave(value);
            return assembleBatchResult(batchItem, resp);
        }catch(Exception e){
            throw new RetryBatchExecutionException(e.getMessage());
        }
    }


    private BatchItemResult assembleBatchResult(BatchItem<ApplyTripTemporaryStorageRequest> batchItem, LisResponse rsp) {
        BatchItemResult batchItemResult = new BatchItemResult();

        batchItemResult.setId(batchItem.getId());
        batchItemResult.setBizItemId(batchItem.getValue().getApplyNo());
        if (rsp.getStatus() == null) {
            batchItemResult.setBatchItemStatus(BatchItemStatus.FAILED);
            batchItemResult.setErrorMessage("rsp status不能为null");
            return batchItemResult;
        }

        if (rsp.getStatus() != null && Boolean.TRUE.equals(rsp.getStatus().getSuccess())) {
            batchItemResult.setBatchItemStatus(BatchItemStatus.SUCCESS);
            return batchItemResult;
        }

        batchItemResult.setErrorMessage(rsp.getStatus().getMessage());
        batchItemResult.setBatchItemStatus(BatchItemStatus.FAILED);
        return batchItemResult;
    }

    @Override
    protected Class<ApplyTripTemporaryStorageRequest> getParameterType() {
        return ApplyTripTemporaryStorageRequest.class;
    }

    @Override
    public BatchProcessType getBatchTaskType() {
        return BatchProcessType.BUDGET_INFO;
    }
}
