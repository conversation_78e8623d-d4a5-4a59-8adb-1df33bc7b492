package com.corpgovernment.resource.batch.validator;

import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.organization.bo.SyncOrgInfoReq;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.event.BatchItem;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class SyncOrgInfoValidator implements BatchItemValidator<SyncOrgInfoReq.SyncOrganizationInfo> {

    @Override
    public List<BatchItemResult> proactiveValidate(List<SyncOrgInfoReq.SyncOrganizationInfo> items) {
        return checkOrgInfo(items);
    }

    @Override
    public BatchItemResult validate(BatchItem<SyncOrgInfoReq.SyncOrganizationInfo> item) {
        return null;
    }

    public List<BatchItemResult> checkOrgInfo(List<SyncOrgInfoReq.SyncOrganizationInfo> orgInfoList) {
        List<BatchItemResult> itemResultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orgInfoList)) {
            return itemResultList;
        }

        log.info("开始校验组织信息，组织:{}", JsonUtils.toJsonString(orgInfoList));
        for (SyncOrgInfoReq.SyncOrganizationInfo orgInfo : orgInfoList) {
            if(orgInfo.getBasicInfo() == null){
                BatchItemResult errorDetail = new BatchItemResult();
                errorDetail.setErrorMessage("组织基本信息不能为空");
                errorDetail.setBizItemId("-1");
                errorDetail.setBatchItemStatus(BatchItemStatus.FAILED);
                itemResultList.add(errorDetail);
                continue;
            }

            BatchItemResult result = checkOrgInfoBaseInfo(orgInfo.getBasicInfo());
            itemResultList.add(result);
        }
        return itemResultList;
    }

    private BatchItemResult checkOrgInfoBaseInfo(SyncOrgInfoReq.BasicInfo basicInfo) {
        List<String> errorMsgs = new ArrayList<>();

        if (StringUtils.isBlank(basicInfo.getOrgId())) {
            errorMsgs.add("组织ID不能为空");
        }
        if (StringUtils.isBlank(basicInfo.getName())) {
            errorMsgs.add("组织名称不能为空");
        }
        if (basicInfo.getType() == null) {
            errorMsgs.add("组织类型不能为空");
        }

        BatchItemResult result = new BatchItemResult();
        result.setBizItemId(basicInfo.getOrgId());
        if (!errorMsgs.isEmpty()) {
            result.setErrorMessage(String.join("；", errorMsgs));
            result.setBatchItemStatus(BatchItemStatus.FAILED);
            return result;
        }
        result.setBatchItemStatus(BatchItemStatus.SUCCESS);
        return result;
    }
}