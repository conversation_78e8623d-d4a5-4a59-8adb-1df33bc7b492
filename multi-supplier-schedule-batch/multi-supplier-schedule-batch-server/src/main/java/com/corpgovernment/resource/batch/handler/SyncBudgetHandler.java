package com.corpgovernment.resource.batch.handler;

import com.corpgovernment.api.basic.request.ApplyTripTemporaryStorageRequest;
import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.resource.batch.AbstractBatchItemHandler;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.corpgovernment.resource.batch.validator.SyncApplyTripValidator;
import org.springframework.stereotype.Component;

@Component
public class SyncBudgetHandler extends AbstractBatchItemHandler<ApplyTripTemporaryStorageRequest> {

    private final SyncApplyTripValidator validator;

    public SyncBudgetHandler(BatchTaskService batchTaskService, SyncApplyTripValidator validator) {
        super(batchTaskService);
        this.validator = validator;
    }

    @Override
    public BatchProcessType getBatchTaskType() {
        return BatchProcessType.BUDGET_INFO;
    }

    @Override
    public Class<ApplyTripTemporaryStorageRequest> getBatchItemClass() {
        return ApplyTripTemporaryStorageRequest.class;
    }

    @Override
    public String generateBizItemId(ApplyTripTemporaryStorageRequest item) {
        return item.getApplyNo();
    }

    @Override
    public BatchItemValidator<ApplyTripTemporaryStorageRequest> validator() {
        return validator;
    }
}
