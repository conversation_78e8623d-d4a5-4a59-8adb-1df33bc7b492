<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.corpgovernment</groupId>
        <artifactId>multi-supplier-schedule-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>multi-supplier-schedule-batch</artifactId>
    <packaging>pom</packaging>


    <modules>
        <module>multi-supplier-schedule-batch-core</module>
        <module>multi-supplier-schedule-batch-server</module>
    </modules>


    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <api-revision>1.1.47-SNAPSHOT</api-revision>
        <arch.generic.version>1.0.8</arch.generic.version>

    </properties>


    <dependencies>
        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-event</artifactId>
            <version>${arch.generic.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-core</artifactId>
            <version>${arch.generic.version}</version>
        </dependency>
    </dependencies>
</project>