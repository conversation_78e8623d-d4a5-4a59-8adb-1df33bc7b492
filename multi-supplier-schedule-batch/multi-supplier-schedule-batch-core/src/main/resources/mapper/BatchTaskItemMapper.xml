<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.batch.data.mapper.BatchTaskItemMapper">
    <resultMap id="BaseResultMap" type="com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="biz_item_id" jdbcType="VARCHAR" property="bizItemId"/>
        <result column="batch_id" jdbcType="BIGINT" property="batchId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="batch_process_type" jdbcType="VARCHAR" property="batchProcessType"/>
        <result column="retry_count" jdbcType="INTEGER" property="retryCount"/>
        <result column="next_retry_time" jdbcType="TIMESTAMP" property="nextRetryTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity">
        <result column="parameter" jdbcType="LONGVARCHAR" property="parameter"/>
        <result column="error_message" jdbcType="LONGVARCHAR" property="errorMessage"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, biz_item_id, batch_id, status, batch_process_type, retry_count, next_retry_time, create_time,
        update_time
    </sql>
    <sql id="Blob_Column_List">
        parameter, error_message
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from batch_task_item
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from batch_task_item
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into batch_task_item (biz_item_id, batch_id, status,
        batch_process_type, retry_count, next_retry_time, create_time,
        update_time, parameter, error_message
        )
        values (#{bizItemId,jdbcType=VARCHAR}, #{batchId,jdbcType=BIGINT}, #{status,jdbcType=VARCHAR},
        #{batchProcessType,jdbcType=VARCHAR}, #{retryCount,jdbcType=INTEGER},  #{nextRetryTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{parameter,jdbcType=LONGVARCHAR}, #{errorMessage,jdbcType=LONGVARCHAR}
        )
    </insert>
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into batch_task_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizItemId != null">
                biz_item_id,
            </if>
            <if test="batchId != null">
                batch_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="batchProcessType != null">
                batch_process_type,
            </if>
            <if test="retryCount != null">
                retry_count,
            </if>
            <if test="nextRetryTime != null">
                nextRetryTime,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="parameter != null">
                parameter,
            </if>
            <if test="errorMessage != null">
                error_message,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bizItemId != null">
                #{bizItemId,jdbcType=VARCHAR},
            </if>
            <if test="batchId != null">
                #{batchId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="batchProcessType != null">
                #{batchProcessType,jdbcType=VARCHAR},
            </if>
            <if test="retryCount != null">
                #{retryCount,jdbcType=INTEGER},
            </if>
            <if test="nextRetryTime != null">
                #{nextRetryTime, jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="parameter != null">
                #{parameter,jdbcType=LONGVARCHAR},
            </if>
            <if test="errorMessage != null">
                #{errorMessage,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity">
        update batch_task_item
        <set>
            <if test="bizItemId != null">
                biz_item_id = #{bizItemId,jdbcType=VARCHAR},
            </if>
            <if test="batchId != null">
                batch_id = #{batchId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="batchProcessType != null">
                batch_process_type = #{batchProcessType,jdbcType=VARCHAR},
            </if>
            <if test="retryCount != null">
                retry_count = #{retryCount,jdbcType=INTEGER},
            </if>
            <if test="nextRetryTime != null">
                next_retry_time = #{nextRetryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="parameter != null">
                parameter = #{parameter,jdbcType=LONGVARCHAR},
            </if>
            <if test="errorMessage != null">
                error_message = #{errorMessage,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs"
            parameterType="com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity">
        update batch_task_item
        set biz_item_id = #{bizItemId,jdbcType=VARCHAR},
        batch_id = #{batchId,jdbcType=BIGINT},
        status = #{status,jdbcType=VARCHAR},
        batch_process_type = #{batchProcessType,jdbcType=VARCHAR},
        retry_count = #{retryCount,jdbcType=INTEGER},
        next_retry_time = #{nextRetryTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        parameter = #{parameter,jdbcType=LONGVARCHAR},
        error_message = #{errorMessage,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity">
        update batch_task_item
        set biz_item_id = #{bizItemId,jdbcType=VARCHAR},
        batch_id = #{batchId,jdbcType=BIGINT},
        status = #{status,jdbcType=VARCHAR},
        batch_process_type = #{batchProcessType,jdbcType=VARCHAR},
        retry_count = #{retryCount,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="groupAndCountByStatus" resultType="com.corpgovernment.resource.batch.data.BatchStatusCount">
        SELECT
        status as status,
        COUNT(*) as count
        FROM
        batch_task_item
        WHERE
        batch_id = #{batchId}
        GROUP BY
        status
    </select>


    <select id="selectItemByStatus" resultType="com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity">
        SELECT
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        FROM batch_task_item
        WHERE batch_id = #{batchId}
        and status = #{status}
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">

        INSERT INTO batch_task_item (
        biz_item_id,
        batch_id,
        status,
        batch_process_type,
        parameter,
        error_message,
        retry_count,
        next_retry_time,
        create_time,
        update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.bizItemId},
            #{item.batchId},
            #{item.status},
            #{item.batchProcessType},
            #{item.parameter},
            #{item.errorMessage},
            #{item.retryCount},
            #{item.nextRetryTime},
            #{item.createTime},
            #{item.updateTime}
            )
        </foreach>
    </insert>

    <insert id="insertBatchSelective" useGeneratedKeys="true" keyProperty="id">
        <foreach collection="list" item="item" separator=";">
            INSERT INTO batch_task_item (
            <trim suffixOverrides=",">
                <if test="item.bizItemId != null">biz_item_id,</if>
                <if test="item.batchId != null">batch_id,</if>
                <if test="item.status != null">status,</if>
                <if test="item.batchProcessType != null">batch_process_type,</if>
                <if test="item.parameter != null">parameter,</if>
                <if test="item.errorMessage != null">error_message,</if>
                <if test="item.retryCount != null">retry_count,</if>
                <if test="item.nextRetryTime != null">next_retry_time,</if>
                <if test="item.createTime != null">create_time,</if>
                <if test="item.updateTime != null">update_time,</if>
            </trim>
            ) VALUES (
            <trim suffixOverrides=",">
                <if test="item.bizItemId != null">#{item.bizItemId},</if>
                <if test="item.batchId != null">#{item.batchId},</if>
                <if test="item.status != null">#{item.status},</if>
                <if test="item.batchProcessType != null">#{item.batchProcessType},</if>
                <if test="item.parameter != null">#{item.parameter},</if>
                <if test="item.errorMessage != null">#{item.errorMessage},</if>
                <if test="item.retryCount != null">#{item.retryCount},</if>
                <if test="item.nextRetryTime != null">#{item.nextRetryTime},</if>
                <if test="item.createTime != null">#{item.createTime},</if>
                <if test="item.updateTime != null">#{item.updateTime},</if>
            </trim>
            )
        </foreach>
    </insert>

    <select id="pageQueryBatchDetail" resultType="com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity" parameterType="com.corpgovernment.resource.batch.data.query.BatchTaskItemDataQuery">
        SELECT
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        FROM batch_task_item
        <where>
            <if test="batchId != null">
                AND batch_id = #{batchId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="countBatchTaskDetails" resultType="int">
        SELECT COUNT(*)
        FROM batch_task_item
        <where>
            <if test="batchId != null">
                AND batch_id = #{batchId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

</mapper>