<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.batch.data.mapper.EHREmployeeMapper">
    
    <sql id="Base_Column_List">
        employee_code, employee_name, sex, email, section_code, section, department, department_code,
        factory, factory_code, division, division_code, business_group_code, business_group,
        work_place, juridical, position_code, position_name, position_level, personal_level,
        status, line_manager_num, line_manager_name, allowance_status, allowance_city,
        orgid, orgid_name, juridical_code
    </sql>

    <select id="queryEHREmployeeList" resultType="com.corpgovernment.resource.batch.data.entity.EHREmployeeEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employees
        <where>
            <if test="employeeCode != null and employeeCode != ''">
                AND employee_code = #{employeeCode}
            </if>
            <if test="orgid != null and orgid != ''">
                AND orgid = #{orgid}
            </if>
        </where>
    </select>

    <select id="queryMissedEmployeeList"  resultType="com.corpgovernment.resource.batch.data.entity.EHREmployeeEntity">
    SELECT
        e.*
    FROM
        employees e 
        LEFT JOIN mb_user_org_relation o ON o.uid = e.employee_code and o.delete_time = '1970-01-01 08:00:00' and o.status = 1
    WHERE
        o.org_id is null
        AND e.status = '在职'
        <if test="batchSize != null and batchSize > 0">
        limit #{batchSize}
        </if>
    </select>
    <!-- queryMoldyEmployeeList --> 

    <select id="queryMoldyEmployeeList"  resultType="com.corpgovernment.resource.batch.data.entity.EHREmployeeEntity">
    SELECT
        e.*
    FROM
        mb_user_org_relation o
        LEFT JOIN employees e ON o.uid = e.employee_code
    WHERE
        o.org_id != e.orgid
        AND o.status = 1
        AND e.status = '在职'
        AND o.delete_time = '1970-01-01 08:00:00'
        <if test="batchSize != null and batchSize > 0">
        limit #{batchSize}
        </if>
    </select>

    <select id="queryByEmployeeCodes" resultType="com.corpgovernment.resource.batch.data.entity.EHREmployeeEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employees
        WHERE employee_code IN
        <foreach collection="employeeCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="queryByOrgIds" resultType="com.corpgovernment.resource.batch.data.entity.EHREmployeeEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM employees
        WHERE orgid IN
        <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
    </select>

    <!-- queryEmptyLevelEmployeeList --> 

    <select id="queryEmptyLevelEmployeeList" resultType="com.corpgovernment.resource.batch.data.entity.EHREmployeeEntity">
    SELECT
        e.*
    FROM
        mb_user_org_relation o
        LEFT JOIN employees e ON o.uid = e.employee_code
    WHERE
        o.status = 1
        AND e.status = '在职'
        AND o.post_id is null 
        AND o.delete_time = '1970-01-01 08:00:00'
        <if test="batchSize != null and batchSize > 0">
        limit #{batchSize}
        </if>

    </select>
    

    <!-- queryApplyTripMoldyEmployeeList --> 
    <select id="queryApplyTripMoldyEmployeeList" resultType="com.corpgovernment.resource.batch.data.entity.EHREmployeeEntity">
        SELECT
            e.*
        FROM
            (
                SELECT DISTINCT
                    apply_uid,
                    org_id
                FROM
                    ao_apply_trip
                WHERE
                    now() BETWEEN valid_start_date AND valid_return_date
            ) ao
            INNER JOIN employees e ON ao.apply_uid = e.employee_code
        WHERE
            ao.org_id != e.orgid
            AND e.status = '在职'
        <if test="batchSize != null and batchSize > 0">
        limit #{batchSize}
        </if>
    </select>    

    </mapper> 