<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.batch.data.mapper.BatchTaskMapper">
  <resultMap id="BaseResultMap" type="com.corpgovernment.resource.batch.data.entity.BatchTaskEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_process_type" jdbcType="VARCHAR" property="batchProcessType" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="failed_count" jdbcType="INTEGER" property="failedCount" />
    <result column="processed_count" jdbcType="INTEGER" property="processedCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.corpgovernment.resource.batch.data.entity.BatchTaskEntity">
    <result column="meta_data" jdbcType="LONGVARCHAR" property="metaData" />
  </resultMap>
  <sql id="Base_Column_List">
    id, batch_process_type, channel, status, total_count, failed_count, processed_count, 
    create_time, update_time, start_time, end_time
  </sql>
  <sql id="Blob_Column_List">
    meta_data
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from batch_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from batch_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.corpgovernment.resource.batch.data.entity.BatchTaskEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into batch_task (batch_process_type, channel, status, 
      total_count, failed_count, processed_count, 
      create_time, update_time, start_time, 
      end_time, meta_data)
    values (#{batchProcessType,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{totalCount,jdbcType=INTEGER}, #{failedCount,jdbcType=INTEGER}, #{processedCount,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{metaData,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.corpgovernment.resource.batch.data.entity.BatchTaskEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into batch_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batchProcessType != null">
        batch_process_type,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="totalCount != null">
        total_count,
      </if>
      <if test="failedCount != null">
        failed_count,
      </if>
      <if test="processedCount != null">
        processed_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="metaData != null">
        meta_data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batchProcessType != null">
        #{batchProcessType,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="totalCount != null">
        #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="failedCount != null">
        #{failedCount,jdbcType=INTEGER},
      </if>
      <if test="processedCount != null">
        #{processedCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="metaData != null">
        #{metaData,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.corpgovernment.resource.batch.data.entity.BatchTaskEntity">
    update batch_task
    <set>
      <if test="batchProcessType != null">
        batch_process_type = #{batchProcessType,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="totalCount != null">
        total_count = #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="failedCount != null">
        failed_count = #{failedCount,jdbcType=INTEGER},
      </if>
      <if test="processedCount != null">
        processed_count = #{processedCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="metaData != null">
        meta_data = #{metaData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.corpgovernment.resource.batch.data.entity.BatchTaskEntity">
    update batch_task
    set batch_process_type = #{batchProcessType,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      total_count = #{totalCount,jdbcType=INTEGER},
      failed_count = #{failedCount,jdbcType=INTEGER},
      processed_count = #{processedCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      meta_data = #{metaData,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.corpgovernment.resource.batch.data.entity.BatchTaskEntity">
    update batch_task
    set batch_process_type = #{batchProcessType,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      total_count = #{totalCount,jdbcType=INTEGER},
      failed_count = #{failedCount,jdbcType=INTEGER},
      processed_count = #{processedCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryBatchTaskByStatus" resultType="com.corpgovernment.resource.batch.data.entity.BatchTaskEntity">
    SELECT
    <include refid="Base_Column_List" />
    FROM batch_task
    where status = #{status}
    ORDER BY create_time DESC
  </select>
    <select id="pageQueryBatchTasks" resultType="com.corpgovernment.resource.batch.data.entity.BatchTaskEntity">
        SELECT 
         <include refid="Base_Column_List" />
        FROM batch_task
        <where>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

      <select id="countBatchTasks" resultType="int">
        SELECT COUNT(*)
        FROM batch_task
        <where>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
    </select>

</mapper>