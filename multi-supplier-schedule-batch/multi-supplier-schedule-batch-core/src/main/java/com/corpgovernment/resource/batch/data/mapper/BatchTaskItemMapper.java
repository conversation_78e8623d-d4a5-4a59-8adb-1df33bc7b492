package com.corpgovernment.resource.batch.data.mapper;


import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.resource.batch.data.BatchStatusCount;
import com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity;
import com.corpgovernment.resource.batch.data.query.BatchTaskItemDataQuery;
import org.apache.ibatis.annotations.MapKey;

import java.util.List;
import java.util.Map;

public interface BatchTaskItemMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BatchTaskItemEntity row);

    int insertSelective(BatchTaskItemEntity row);

    BatchTaskItemEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BatchTaskItemEntity row);

    int updateByPrimaryKeyWithBLOBs(BatchTaskItemEntity row);

    int updateByPrimaryKey(BatchTaskItemEntity row);

    List<BatchStatusCount> groupAndCountByStatus(Long batchId);

    List<BatchTaskItemEntity> selectItemByStatus(Long batchId, String status);

    List<BatchTaskItemEntity> selectRetryItem(Long batchId);

    int batchInsert(List<BatchTaskItemEntity> batchTaskItemEntities);

    int insertBatchSelective(List<BatchTaskItemEntity> batchTaskItemEntities);

    int countBatchTaskDetails(BatchTaskItemDataQuery query);

    List<BatchTaskItemEntity> pageQueryBatchDetail(BatchTaskItemDataQuery query);

}