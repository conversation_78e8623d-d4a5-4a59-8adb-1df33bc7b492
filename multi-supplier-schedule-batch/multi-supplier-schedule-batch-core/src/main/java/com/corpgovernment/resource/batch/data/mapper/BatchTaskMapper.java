package com.corpgovernment.resource.batch.data.mapper;

import com.corpgovernment.resource.batch.data.entity.BatchTaskEntity;
import com.corpgovernment.resource.batch.data.query.BatchTaskDataQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BatchTaskMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BatchTaskEntity row);

    int insertSelective(BatchTaskEntity row);

    BatchTaskEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BatchTaskEntity row);

    int updateByPrimaryKeyWithBLOBs(BatchTaskEntity row);

    int updateByPrimaryKey(BatchTaskEntity row);


    List<BatchTaskEntity> queryBatchTaskByStatus(String status);

    List<BatchTaskEntity> pageQueryBatchTasks(BatchTaskDataQuery query);
    
    int countBatchTasks(BatchTaskDataQuery query);
    
}