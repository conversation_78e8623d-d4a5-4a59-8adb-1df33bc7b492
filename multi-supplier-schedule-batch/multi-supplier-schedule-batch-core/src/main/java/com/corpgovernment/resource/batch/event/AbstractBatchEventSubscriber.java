package com.corpgovernment.resource.batch.event;

import com.corpgovernment.common.mq.consumer.AbstractMultiTenantRocketMQConsumer;
import com.corpgovernment.redis.cache.RedisUtils;
import com.corpgovernment.resource.batch.event.processor.BatchEventProcessor;
import com.corpgovernment.resource.batch.event.processor.BatchEventProcessorRegistry;
import com.ctrip.corp.obt.generic.core.event.TenantEvent;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractBatchEventSubscriber extends AbstractMultiTenantRocketMQConsumer {

    @Autowired
    private BatchEventProcessorRegistry processorRegistry;

    @Autowired
    private RedisUtils redisUtils;

    private static final String REDIS_KEY_PREFIX = "batch_item_event_";

    //默认6个小时
    private static final int REDIS_CACHE_UPDATE_DURATIONS = 6 * 60 * 24;

    protected Long getItemLastTimestamp(String batchBizItemId) {
        return redisUtils.getCache(getKey(batchBizItemId), Long.class);
    }

    protected String getKey(String batchBizItemId) {
        return REDIS_KEY_PREFIX + getMessageBizType().getTopic() +  batchBizItemId;
    }

    protected void setItemLastTimestamp(String batchBizItemId, Long timestamp) {
        redisUtils.setCache(getKey(batchBizItemId), timestamp, REDIS_CACHE_UPDATE_DURATIONS);
    }

    public void handleEvent(BatchExecuteEvent event) {
        log.info("received batch event :{}", JsonUtils.toJsonString(event));
        String batchType = event.getBatchType();
        String batchBizItemId = event.getBatchBizItemId();
        Long eventTimestamp = event.getEventTimestamp();
        BatchEventProcessor batchEventProcessor = processorRegistry.getBatchEventProcessor(batchType);
        if (batchEventProcessor != null) {
            BatchExecuteContext context = new BatchExecuteContext(event);
            Long itemLastTimestamp = getItemLastTimestamp(batchBizItemId);
            if (itemLastTimestamp != null && eventTimestamp < itemLastTimestamp) {
                log.info("Discard obsoleted Batch Execute Event , batchId:{}, bizItemId:{}", event.getBatchId(), event.getBatchBizItemId());
                return;
            }
            batchEventProcessor.process(event, context);
            setItemLastTimestamp(batchBizItemId, eventTimestamp);
        } else {
            log.error("batchEventProcessorMap not contains key: {}, event:{}", batchType, JsonUtils.toJsonString(event));
        }
    }

    @Override
    protected void consume(MessageExt messageExt, ConsumeConcurrentlyContext context) {
        String msg = new String(messageExt.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(msg)){
            log.error("Event为空");
            return;
        }
        BatchExecuteEvent executeEvent = JsonUtils.parse(msg, BatchExecuteEvent.class);
        if (executeEvent == null) {
            return;
        }
        handleEvent(executeEvent);
    }

}
