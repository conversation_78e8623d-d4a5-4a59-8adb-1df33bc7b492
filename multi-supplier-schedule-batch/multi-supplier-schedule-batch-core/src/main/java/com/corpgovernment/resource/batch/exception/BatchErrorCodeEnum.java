package com.corpgovernment.resource.batch.exception;


import com.ctrip.corp.obt.generic.exception.ResponseStatus;

public enum BatchErrorCodeEnum implements ResponseStatus {
    SUCCESS(0, "调用成功"),
    REQUEST_IS_NULL(20121101, "请求报文为空"),
    PARAM_INVALID(20121102, "请求参数错误：参数验证无效"),
    SERVER_ERROR(20121500, "接口内部错误");
    /**
     * 值
     */
    private final int code;
    /**
     * 描述
     */
    private final String message;


    BatchErrorCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int code() {
        return code;
    }

    @Override
    public String message() {
        return message;
    }
}
