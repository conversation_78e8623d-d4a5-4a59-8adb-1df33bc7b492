package com.corpgovernment.resource.batch.event;

import com.ctrip.corp.obt.generic.core.event.Event;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class BatchExecuteEvent extends Event {
    public BatchExecuteEvent() {
    }

    public BatchExecuteEvent(String topic) {
        super(topic);
    }

    private Boolean isRetry = false;

    private Long batchId;

    private String batchType;

    private Long batchDetailId;

    private String batchBizItemId;

    private Date batchDetailCreateTime;

    private String parameter;

    private Map<Object, Object> metaData;

    private Integer retryCount;

    private Long eventTimestamp;


}
