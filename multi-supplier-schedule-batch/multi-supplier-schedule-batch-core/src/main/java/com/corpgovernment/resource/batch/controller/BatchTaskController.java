package com.corpgovernment.resource.batch.controller;

import com.corpgovernment.api.batch.enums.BatchStatus;
import com.corpgovernment.api.batch.request.CreateBatchTaskRequest;
import com.corpgovernment.api.batch.request.QueryBatchDetailRequest;
import com.corpgovernment.api.batch.request.QueryBatchRequest;
import com.corpgovernment.api.batch.request.UploadBatchItemsRequest;
import com.corpgovernment.api.batch.response.*;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.batch.domain.BatchTask;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/batch")
@Slf4j
@RequiredArgsConstructor
public class BatchTaskController {

    private final BatchTaskService batchTaskService;

    @PostMapping("/createBatchTask")
    public JSONResult<CreateBatchTaskResponse> createBatchTask(@RequestBody CreateBatchTaskRequest request) {
        try {
            CreateBatchTaskResponse response = new CreateBatchTaskResponse();
            Long batchId = batchTaskService.saveBatchTask(request);
            response.setBatchId(batchId);
            response.setStatus(BatchStatus.INIT.name());
            return JSONResult.success(response);
        } catch (Exception e) {
            log.error("CreateBatchTask 失败, req:{}", JsonUtils.toJsonString(request), e);
            return JSONResult.errorMsg("创建BatchTask失败");
        }
    }

    @PostMapping("/uploadBatchItems")
    public JSONResult<UploadBatchItemsResponse> uploadBatchItems(@RequestBody UploadBatchItemsRequest request) {
        UploadBatchItemsResponse response;
        try {
            response = batchTaskService.uploadBatchItems(request);
        } catch (Exception e) {
            log.error("UploadBatchItems失败, req:{}", JsonUtils.toJsonString(request), e);
            return JSONResult.errorMsg("UploadBatchItems失败");
        }
        return JSONResult.success(response);
    }

    @GetMapping("/getBatchTask")
    public JSONResult<BatchTaskModel> getBatch(@RequestParam("batchId") Long batchId) {
        BatchTask batchTask = batchTaskService.getBatchTask(batchId);
        if (batchTask != null) {
            return JSONResult.success(batchTask.toResp());
        }
        return JSONResult.ok();
    }

    @PostMapping("/queryBatchTasks")
    public JSONResult<QueryBatchResponse> queryBatchTasks(@RequestBody QueryBatchRequest request) {
        QueryBatchResponse response = batchTaskService.queryBatchTasks(request);
        return JSONResult.success(response);
    }

    @PostMapping("/pageQueryBatchDetail")
    public JSONResult<QueryBatchDetailResponse> pageQueryBatchDetail(@RequestBody QueryBatchDetailRequest request) {
        // type 不能为空
        if (StringUtils.isBlank(request.getBatchProcessType())) {
            return JSONResult.errorMsg("type不能为空");
        }
        // batchId 和 bizItemId 不能同时为空
        if (request.getBatchId() == null && CollectionUtils.isEmpty(request.getBizItemIds())) {
            return JSONResult.errorMsg("batchId和bizItemId不能同时为空");
        }
        QueryBatchDetailResponse response = batchTaskService.pageQueryBatchDetail(request);
        return JSONResult.success(response);
    }

}
