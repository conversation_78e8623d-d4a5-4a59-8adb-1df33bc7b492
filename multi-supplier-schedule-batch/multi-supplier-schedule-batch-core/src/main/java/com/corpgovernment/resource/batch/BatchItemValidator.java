package com.corpgovernment.resource.batch;

import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.event.BatchItem;
import com.corpgovernment.resource.batch.exception.BatchParametersInvalidException;

import java.util.List;

public interface BatchItemValidator<T> {


    /**
     * 前置校验，不应该依赖任何API或者数据库
     *
     * @param items Batch Items
     * @return
     * @throws BatchParametersInvalidException
     */
    List<BatchItemResult> proactiveValidate(List<T> items);

    /**
     * 正式校验，校验通过之后才能进行业务操作
     *
     * @param item
     * @return
     * @throws BatchParametersInvalidException
     */
    BatchItemResult validate(BatchItem<T> item);

}
