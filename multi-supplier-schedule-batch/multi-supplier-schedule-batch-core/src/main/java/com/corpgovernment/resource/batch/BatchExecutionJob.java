package com.corpgovernment.resource.batch;

import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.batch.enums.BatchStatus;
import com.corpgovernment.resource.batch.data.entity.BatchTaskEntity;
import com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity;
import com.corpgovernment.resource.batch.data.mapper.BatchTaskItemMapper;
import com.corpgovernment.resource.batch.data.mapper.BatchTaskMapper;
import com.corpgovernment.resource.batch.domain.BatchJobParameter;
import com.corpgovernment.resource.batch.domain.BatchTaskItem;
import com.corpgovernment.resource.batch.event.BatchEventDispatcher;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.shard.holder.ShardSourceHolder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class BatchExecutionJob {

    private final BatchTaskMapper batchTaskMapper;

    private final BatchTaskItemMapper batchTaskItemMapper;

    private final BatchEventDispatcher batchEventDispatcher;

    private final BatchTaskService batchTaskService;

    private static final String UNKNOWN_HOST = "unknown-host";

    private static final String BATCH_JOB_CLIENT_ID_PREFIX = "batch-job-client-";

    @XxlJob("batch.center.executeBatchTasks")
    public ReturnT<String> executeBatchTasks(String param) {
        log.info("scan and execute batch task, param:{}", param);
        Set<String> tenantIds = getTenantIds(param);
        if (CollectionUtils.isEmpty(tenantIds)) {
            return ReturnT.FAIL;
        }
        for (String tenantId : tenantIds) {
            try {
                TenantContext.setTenantId(tenantId);
                TenantContext.setClientId(BATCH_JOB_CLIENT_ID_PREFIX + resolveHostname());
                TraceContext.setRequestId(String.valueOf(UUID.randomUUID()));
                doExecuteBatchTask();
            } finally {
                TenantContext.unset();
            }
        }
        return ReturnT.SUCCESS;
    }

    private String resolveHostname() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            return UNKNOWN_HOST;
        }
    }

    private Set<String> getTenantIds(String param) {
        if (StringUtils.isBlank(param)) {
            return ShardSourceHolder.getCachedTenantIds();
        }
        BatchJobParameter jobParameter = JsonUtils.parse(param, BatchJobParameter.class);
        if (jobParameter == null) {
            return ShardSourceHolder.getCachedTenantIds();
        }
        return jobParameter.getTenantIds();
    }

    private void doExecuteBatchTask() {
        // find all batch task with ready status
        List<BatchTaskEntity> batchTaskEntities = batchTaskMapper.queryBatchTaskByStatus(
            Arrays.asList(BatchStatus.READY.name(), BatchStatus.PROCESSING.name())
        );
        if (CollectionUtils.isEmpty(batchTaskEntities)) {
            return;
        }
        log.info("scan and execute batch task, size:{}", batchTaskEntities.size());
        for (BatchTaskEntity batchTaskEntity : batchTaskEntities) {
            try {
                List<BatchTaskItem> batchTaskItems = queryBatchTaskItems(batchTaskEntity.getId(), BatchItemStatus.INIT);
                if( CollectionUtils.isEmpty(batchTaskItems)) {
                    log.info("batch task items is empty, batchId:{}", batchTaskEntity.getId());
                    if( BatchStatus.READY.name().equals(batchTaskEntity.getStatus())) {
                        batchTaskService.updateBatchTaskStatus(batchTaskEntity.getId(), BatchStatus.STOPPED);
                    }
                    continue;
                }
                batchEventDispatcher.dispatchEvent(batchTaskEntity.getId(), batchTaskEntity.getBatchProcessType(), batchTaskItems, false);
                batchTaskService.updateBatchTaskStatus(batchTaskEntity.getId(), BatchStatus.PROCESSING);
            } catch (Exception e) {
                log.error("scan and execute batch task error , batch id:{}, tenantId:{}", batchTaskEntity.getId(), TenantContext.getTenantId(), e);
                batchTaskService.updateBatchTaskStatus(batchTaskEntity.getId(), BatchStatus.STOPPED);
            }
        }
    }

    @XxlJob("batch.center.retryBatchTasks")
    public ReturnT<String> retryBatchTasks(String param) {
        Set<String> tenantIds = getTenantIds(param);
        if (CollectionUtils.isEmpty(tenantIds)) {
            return ReturnT.FAIL;
        }
        for (String tenantId : tenantIds) {
            try {
                TenantContext.setTenantId(tenantId);
                TenantContext.setClientId(BATCH_JOB_CLIENT_ID_PREFIX + resolveHostname());
                TraceContext.setRequestId(String.valueOf(UUID.randomUUID()));
                doRetryBatchTask();
            } finally {
                TenantContext.unset();
            }
        }
        return ReturnT.SUCCESS;
    }

    private void doRetryBatchTask() {
        List<BatchTaskEntity> batchTaskEntities = batchTaskMapper.queryBatchTaskByStatus(
            Collections.singletonList(BatchStatus.PROCESSING.name())
        );
        if (CollectionUtils.isEmpty(batchTaskEntities)) {
            return;
        }
        for (BatchTaskEntity batchTaskEntity : batchTaskEntities) {
            try {
                List<BatchTaskItem> batchTaskItems = queryRetryTaskItems(batchTaskEntity.getId());
                if (CollectionUtils.isEmpty(batchTaskItems)) {
                   continue;
                }
                log.info("scan and retry batch task, batchId:{}, retry size:{}", batchTaskEntity.getId(), batchTaskItems.size());
                batchEventDispatcher.dispatchEvent(batchTaskEntity.getId(), batchTaskEntity.getBatchProcessType(), batchTaskItems, true);
            } catch (Exception e) {
                log.error("scan and retry batch task error , batch id:{}, tenantId:{}", batchTaskEntity.getId(), TenantContext.getTenantId(), e);
                batchTaskService.updateBatchTaskStatus(batchTaskEntity.getId(), BatchStatus.STOPPED);
            }
        }
    }

    @XxlJob("batch.center.settleBatchTasks")
    public ReturnT<String> settleBatchTasks(String param) {
        if (StringUtils.isBlank(param)) {
            return ReturnT.FAIL;
        }
        log.info("settle batch task, param:{}", param);
        Set<String> tenantIds = getTenantIds(param);
        if (CollectionUtils.isEmpty(tenantIds)) {
            return ReturnT.FAIL;
        }
        for (String tenantId : tenantIds) {
            try {
                TenantContext.setTenantId(tenantId);
                TraceContext.setRequestId(String.valueOf(UUID.randomUUID()));
                doSettleBatchTasks();
            } finally {
                TenantContext.unset();
            }
        }
        return ReturnT.SUCCESS;
    }

    private void doSettleBatchTasks() {
        List<BatchTaskEntity> batchTaskEntities = batchTaskMapper.queryBatchTaskByStatus(
            Collections.singletonList(BatchStatus.PROCESSING.name())
        );
        if (CollectionUtils.isEmpty(batchTaskEntities)) {
            return;
        }

        log.info("scan settle batch tasks size:{} ", batchTaskEntities.size());
        for (BatchTaskEntity batchTask : batchTaskEntities) {
            try {
                batchTaskService.updateBatchStatusAndCount(batchTask);
            } catch (Exception e) {
                log.error("settle batch task error , batch id:{}", batchTask.getId(), e);
            }
        }
    }

    private List<BatchTaskItem> queryBatchTaskItems(Long batchId, BatchItemStatus batchItemStatus) {
        List<BatchTaskItemEntity> batchTaskItemEntities = batchTaskItemMapper.selectItemByStatus(batchId, batchItemStatus.name());
        if (CollectionUtils.isEmpty(batchTaskItemEntities)) {
            return Collections.emptyList();
        }

        return batchTaskItemEntities.stream().map(BatchTaskItem::from).collect(Collectors.toList());
    }


    private List<BatchTaskItem> queryRetryTaskItems(Long batchId) {
        List<BatchTaskItemEntity> batchTaskItemEntities = batchTaskItemMapper.selectRetryItem(batchId);
        if (CollectionUtils.isEmpty(batchTaskItemEntities)) {
            return Collections.emptyList();
        }

        return batchTaskItemEntities.stream().map(BatchTaskItem::from).collect(Collectors.toList());
    }

}
