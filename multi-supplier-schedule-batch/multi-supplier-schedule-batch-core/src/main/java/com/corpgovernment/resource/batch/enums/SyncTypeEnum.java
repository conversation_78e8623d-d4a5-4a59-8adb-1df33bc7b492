package com.corpgovernment.resource.batch.enums;

public enum SyncTypeEnum {
    MISSED_USER("MISSED_USER", "缺失人员数据同步"),
    MOLDY_ORG("MOLDY_ORG", "所有变更组织人员数据同步"),
    EMPTY_LEVEL("EMPTY_LEVEL", "职级数据为空人员数据同步"),
    APPLY_TRIP_SYNC("APPLY_TRIP_SYNC", "申请单人员数据同步"),
    UID_LIST("UID_LIST", "根据员工ID列表同步"),
    ORG_ID("ORG_ID", "根据组织ID同步");

    private final String code;
    private final String desc;

    SyncTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SyncTypeEnum getByCode(String code) {
        for (SyncTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 