package com.corpgovernment.resource.batch;

import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.api.batch.request.CreateBatchTaskRequest;
import com.corpgovernment.api.batch.request.UploadBatchItemsRequest;
import com.corpgovernment.api.organization.bo.SyncOrgEmployeeReq;
import com.corpgovernment.api.organization.bo.SyncOrgEmployeeRsp;
import com.corpgovernment.api.organization.model.employee.EmployeeInfoBo;
import com.corpgovernment.api.organization.soa.IEmployeeClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.batch.data.entity.EHREmployeeEntity;
import com.corpgovernment.resource.batch.data.mapper.EHREmployeeMapper;
import com.corpgovernment.resource.batch.dto.MDMResponse;
import com.corpgovernment.resource.batch.dto.MDMResponse.Item;
import com.corpgovernment.resource.batch.enums.SyncTypeEnum;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.corpgovernment.resource.batch.service.MDMEmployeeLoader;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.Data;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class EHREmployeeSyncJob {

    private final IEmployeeClient iEmployeeClient;

    private final EHREmployeeMapper ehrEmployeeMapper;

    private final MDMEmployeeLoader mdmEmployeeLoader;

    private final BatchTaskService batchTaskService;

    @Value("${batch.syncEmployeeUseBatch:true}")
    private Boolean syncEmployeeUseBatch;

    @Value("${batch.syncEmployee.partitionSize:50}")
    private int partitionSize;

    @Value("${batch.useLocalEmployee:false}")
    private Boolean useLocalEmployee;

    /**
     * 批量同步员工信息
     * Param 格式:
     * {
     * "tenant": "BYD", // 租户ID
     * "syncType": "MOLDY_SYNC" | "UID_LIST" | "ORG_ID",
     * "batchSize": 100,
     * "uids": ["uid1", "uid2"], // 当syncType为UID_LIST时必填
     * "orgIds": ["orgId1", "orgId2"] // 当syncType为ORG_ID时必填
     * }
     * 
     * @param param
     * @return
     */

    @XxlJob("batch.center.syncEhrEmployees")
    public ReturnT<String> syncEhrEmployees(String param) {
        try {
            SyncEmployeeParam syncParam = SyncEmployeeParam.fromJson(param);
            TenantContext.setTenantId(syncParam.getTenant());
            TenantContext.setClientId(UUID.randomUUID().toString());
            TraceContext.setRequestId(UUID.randomUUID().toString());
            log.info("Starting syncEhrEmployees task, param: {}", param);

            List<EHREmployeeEntity> employees = queryEmployeesBySyncType(syncParam);
            
            log.info("Found {} employees to sync for tenant: {}", employees.size(), syncParam.getTenant());

            if (employees.isEmpty()) {
                log.info("No employees to sync");
                return ReturnT.SUCCESS;
            }

            // Convert and sync employees
            List<List<EHREmployeeEntity>> partitionedList = Lists.partition(employees, partitionSize);

            // 使用Batch Service
            if (BooleanUtils.isTrue(syncEmployeeUseBatch)) {
                Long batchId = getBatchId(employees);
                int totalChunks = partitionedList.size();

                for (int i = 0; i < totalChunks; i++) {
                    List<EHREmployeeEntity> partitionedEmployees = partitionedList.get(i);
                    uploadBatchItems(batchId, totalChunks, i, partitionedEmployees);
                }
            } else {
                for (List<EHREmployeeEntity> partitionedEmployees : partitionedList) {
                    List<SyncOrgEmployeeReq> reqList = convertToSyncOrgEmployeeReq(partitionedEmployees);
                    for (SyncOrgEmployeeReq req : reqList) {
                        SyncOrgEmployeeRsp rsp = iEmployeeClient.syncEmployeeInfo(req);
                        if (rsp != null) {
                            if ("200".equals(rsp.getStatus())) {
                                log.info("Successfully synced employee info for request: {}",
                                        JsonUtils.toJsonString(req));
                            } else {
                                log.error("Failed to sync employee info for request: {}, error: {}",
                                        JsonUtils.toJsonString(req), rsp.getMessage());
                            }
                        } else {
                            log.error("sync employee rsp null request: {}, error: {}", JsonUtils.toJsonString(req));
                        }
                    }
                }
            }

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("Failed to sync employees", e);
            return ReturnT.FAIL;
        } finally {
            TenantContext.unset();
        }
    }

    private void uploadBatchItems(Long batchId, int totalChunks, int i, List<EHREmployeeEntity> partitionedEmployees) {
        List<SyncOrgEmployeeReq> reqList = convertToSyncOrgEmployeeReq(partitionedEmployees);

        List<String> reqItemList = reqList.stream()
                .flatMap(req -> req.getEmployeeList().stream()
                        .map(employee -> {
                            employee.setUpdateFlag(req.getUpdateFlag());
                            return JsonUtils.toJsonString(employee);
                        }))
                .collect(Collectors.toList());
        UploadBatchItemsRequest req = new UploadBatchItemsRequest();
        req.setBatchId(batchId);
        req.setItems(reqItemList);
        req.setChunkSequence(i + 1);
        req.setIsLastChunk(i == totalChunks - 1);
        batchTaskService.uploadBatchItems(req);
    }

    private Long getBatchId(List<EHREmployeeEntity> employees) {
        CreateBatchTaskRequest createBatchTaskRequest = new CreateBatchTaskRequest();
        createBatchTaskRequest.setBatchProcessType(BatchProcessType.SYNC_EMPLOYEE_INFO.name());
        createBatchTaskRequest.setTotalItemCount(employees.size());
        createBatchTaskRequest.setChannel("OPENAPI");
        Long batchId = batchTaskService.saveBatchTask(createBatchTaskRequest);
        return batchId;
    }

    private EmployeeInfoBo findByUid(String uid) {
        JSONResult<EmployeeInfoBo> employeeResult = iEmployeeClient.findByUid(uid);
        if (employeeResult.isSUCCESS() && employeeResult.getData() != null) {
            return employeeResult.getData();
        } else {
            log.error("Failed to find employee by UID: {}, error: {}", uid, employeeResult.getMsg());
            return null;
        }
    }

    private List<SyncOrgEmployeeReq> convertToSyncOrgEmployeeReq(List<EHREmployeeEntity> ehrEmployees) {

        List<String> uids = ehrEmployees.stream().map(EHREmployeeEntity::getEmployeeCode).collect(Collectors.toList());
        MDMResponse response = mdmEmployeeLoader.queryEmployeesByUids(uids);
        Map<String, MDMResponse.BPI> mdmDataMap = getMdmData(response);

        List<SyncOrgEmployeeReq> resultList = new ArrayList<>();

        for (EHREmployeeEntity ehrEmployee : ehrEmployees) {
            String uid = ehrEmployee.getEmployeeCode();
            MDMResponse.BPI mdmData = mdmDataMap.get(uid);

            if (mdmData == null) {
                log.error("MDM data not found for employee code: {}", uid);
                continue;
            }

            // Create request objects
            SyncOrgEmployeeReq req = new SyncOrgEmployeeReq();

            SyncOrgEmployeeReq.Employee employee = new SyncOrgEmployeeReq.Employee();

            SyncOrgEmployeeReq.BasicInfo basicInfo = new SyncOrgEmployeeReq.BasicInfo();
            if (mdmData.getEMP_ID() == null || !mdmData.getEMP_ID().equals(uid)) {
                log.error("MDM EMP_ID does not match EHR employee code for employee: {}", uid);
                continue;
            }

            basicInfo.setUid(mdmData.getEMP_ID());

            if (StringUtils.isBlank(mdmData.getMIN_UNIT_CODE())) {
                log.error("MDM MIN_UNIT_CODE is empty for employee: {}", uid);
                continue;
            }

            basicInfo.setOrgId(mdmData.getMIN_UNIT_CODE());

            if (useLocalEmployee) {
                // local Employee
                EmployeeInfoBo localEmployee = findByUid(uid);
                if (localEmployee != null) {
                    if (mdmData.getMIN_UNIT_CODE().equals(localEmployee.getOrgId())) {
                        log.info("MDM MIN_UNIT_CODE matches local employee orgId for employee: {}", uid);
                        continue;
                    } else {
                        log.warn("MDM MIN_UNIT_CODE does not match local employee orgId for employee: {}", uid);
                        basicInfo.setOldOrgId(localEmployee.getOrgId());
                    }
                } else {
                    log.info("Local employee not found, will create Employee for UID: {}", uid);
                }
            }

            basicInfo.setName(mdmData.getNAME());
            basicInfo.setEmail(mdmData.getEMP_EMAIL());
            basicInfo.setStatus(getEmployeeStatus(mdmData.getSTATUS()));
            basicInfo.setManageUid(mdmData.getDIR_LEADER_ID());
            basicInfo.setPostCode(mdmData.getLV());
            basicInfo.setPostName(mdmData.getLV());
            basicInfo.setJobName(mdmData.getPOST());
            basicInfo.setJobCode(mdmData.getPOST_CODE());
            basicInfo.setSyncLoginEmail("N");
            basicInfo.setSyncLoginPhone("N");
            employee.setBasicInfo(basicInfo);

            req.setEmployeeList(Collections.singletonList(employee));
            resultList.add(req);

        }

        return resultList;
    }

    private Map<String, MDMResponse.BPI> getMdmData(MDMResponse response) {
        Map<String, MDMResponse.BPI> mdmData = new HashMap<>();
        if (response != null && response.getItems() != null && !response.getItems().isEmpty()) {
            for (Item item : response.getItems()) {
                MDMResponse.BPI bpi = item.getBPI();
                if (bpi != null && bpi.getEMP_ID() != null) {
                    mdmData.put(bpi.getEMP_ID(), bpi);
                } else {
                    log.warn("MDM BPI data is incomplete for item: {}", item);
                }
            }
        }
        return mdmData;
    }

    private List<EHREmployeeEntity> queryEmployeesBySyncType(SyncEmployeeParam syncParam) {
        SyncTypeEnum syncTypeEnum = SyncTypeEnum.getByCode(syncParam.getSyncType());
        if (syncTypeEnum == null) {
            log.error("Invalid sync type: {}", syncParam.getSyncType());
            throw new IllegalArgumentException("Invalid sync type: " + syncParam.getSyncType());
        }

        List<EHREmployeeEntity> employees;
        switch (syncTypeEnum) {
            case MISSED_USER:
                log.info("Querying missed employees with batch size: {}", syncParam.getBatchSize());
                employees = ehrEmployeeMapper.queryMissedEmployeeList(syncParam.getBatchSize());
                break;
            case MOLDY_ORG:
                log.info("Querying moldy employees with batch size: {}", syncParam.getBatchSize());
                employees = ehrEmployeeMapper.queryMoldyEmployeeList(syncParam.getBatchSize());
                break;
            case EMPTY_LEVEL:
                log.info("Querying empty level employees with batch size: {}", syncParam.getBatchSize());
                employees = ehrEmployeeMapper.queryEmptyLevelEmployeeList(syncParam.getBatchSize());
                break;
            case APPLY_TRIP_SYNC:
                log.info("Querying apply trip moldy employees with batch size: {}", syncParam.getBatchSize());
                employees = ehrEmployeeMapper.queryApplyTripMoldyEmployeeList(syncParam.getBatchSize());
                break;
            case UID_LIST:
                if (syncParam.getUids() == null || syncParam.getUids().isEmpty()) {
                    log.error("UID_LIST sync type requires non-empty uids list");
                    throw new IllegalArgumentException("UID_LIST sync type requires non-empty uids list");
                }
                log.info("Querying employees by UIDs: {}", syncParam.getUids());
                employees = ehrEmployeeMapper.queryByEmployeeCodes(syncParam.getUids());
                break;
            case ORG_ID:
                if (syncParam.getOrgIds() == null || syncParam.getOrgIds().isEmpty()) {
                    log.error("ORG_ID sync type requires non-empty orgIds list");
                    throw new IllegalArgumentException("ORG_ID sync type requires non-empty orgIds list");
                }
                log.info("Querying employees by Org IDs: {}", syncParam.getOrgIds());
                employees = ehrEmployeeMapper.queryByOrgIds(syncParam.getOrgIds());
                break;
            default:
                log.error("Unsupported sync type: {}", syncParam.getSyncType());
                throw new IllegalArgumentException("Unsupported sync type: " + syncParam.getSyncType());
        }
        return employees;
    }

    private String getEmployeeStatus(String mdmStatus){
        // 1：正常 2：禁用 3：离职 4：删除
        switch (mdmStatus) {
            case "在职":
                return "1";
            case "离职":
                return "3";
            default:
                log.warn("Unknown MDM status: {}, defaulting to NORMAL", mdmStatus);
                return "0"; // Default to NORMAL if status is unknown
        }
    }


    @Data
    public static class SyncEmployeeParam {
        private String tenant;
        private String syncType;
        private Integer batchSize;
        private List<String> uids;
        private List<String> orgIds;

        public static SyncEmployeeParam fromJson(String json) {
            return JsonUtils.parse(json, SyncEmployeeParam.class);
        }
    }
}


