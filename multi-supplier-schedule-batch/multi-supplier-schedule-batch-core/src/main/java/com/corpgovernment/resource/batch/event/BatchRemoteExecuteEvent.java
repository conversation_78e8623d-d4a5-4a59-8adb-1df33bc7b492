package com.corpgovernment.resource.batch.event;

import com.ctrip.corp.obt.generic.core.event.EventType;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BatchRemoteExecuteEvent extends BatchExecuteEvent {

    public BatchRemoteExecuteEvent() {
    }

    public BatchRemoteExecuteEvent(String topic) {
        super(topic);
    }

    @Override
    public EventType eventType() {
        return EventType.REMOTE;
    }
}
