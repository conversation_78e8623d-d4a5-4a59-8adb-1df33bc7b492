package com.corpgovernment.resource.batch.data.mapper;

import com.corpgovernment.resource.batch.data.entity.EHREmployeeEntity;
import com.corpgovernment.resource.batch.data.query.EHREmployeeDataQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EHREmployeeMapper {

    List<EHREmployeeEntity> queryEHREmployeeList(EHREmployeeDataQuery query);

    List<EHREmployeeEntity> queryMissedEmployeeList(@Param("batchSize") int batchSize);

    List<EHREmployeeEntity> queryApplyTripMoldyEmployeeList(@Param("batchSize") int batchSize);

    List<EHREmployeeEntity> queryEmptyLevelEmployeeList(@Param("batchSize") int batchSize);

    List<EHREmployeeEntity> queryMoldyEmployeeList(@Param("batchSize") int batchSize);

    List<EHREmployeeEntity> queryByEmployeeCodes(@Param("employeeCodes") List<String> employeeCodes);

    List<EHREmployeeEntity> queryByOrgIds(@Param("orgIds") List<String> orgIds);

}
