package com.corpgovernment.resource.batch.domain;

import java.util.Date;

import com.corpgovernment.api.batch.enums.BatchItemStatus;

import lombok.Data;

@Data
public class BatchItemResult {
    private Long id;
    private String bizItemId;
    private BatchItemStatus batchItemStatus;
    private Date nextRetryTime;
    private String errorMessage;

    public BatchItemResult(Long id, String bizItemId, BatchItemStatus batchItemStatus, String errorMessage) {
        this.id = id;
        this.bizItemId = bizItemId;
        this.batchItemStatus = batchItemStatus;
        this.errorMessage = errorMessage;
    }

    public BatchItemResult(String bizItemId, String errorMessage) {
        this.bizItemId = bizItemId;
        this.errorMessage = errorMessage;
    }

    public BatchItemResult() {
    }
}
