package com.corpgovernment.resource.batch.data.entity;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "batch_task_item")
public class BatchTaskItemEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String bizItemId;

    private Long batchId;

    private String status;

    private String batchProcessType;

    private String parameter;

    private String errorMessage;

    private Integer retryCount;

    private Date nextRetryTime;

    private Date createTime;

    private Date updateTime;


}
