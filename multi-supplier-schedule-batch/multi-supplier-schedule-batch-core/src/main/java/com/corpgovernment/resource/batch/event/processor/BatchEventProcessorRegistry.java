package com.corpgovernment.resource.batch.event.processor;

import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.resource.batch.BatchItemHandler;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class BatchEventProcessorRegistry implements ApplicationContextAware, ApplicationRunner {

    private ApplicationContext applicationContext;

    private final Map<BatchProcessType, BatchEventProcessor> batchEventProcessorMap = new HashMap<>();

    private final Map<BatchProcessType, BatchItemHandler<?>> batchItemHandlerMap = new HashMap<>();



    public void init() {
        applicationContext.getBeansOfType(BatchEventProcessor.class).values()
                .forEach(batchEventProcessor -> batchEventProcessorMap.put(batchEventProcessor.getBatchTaskType(), batchEventProcessor));
        log.info("batch event process init :{}", JsonUtils.toJsonString(batchEventProcessorMap));

        applicationContext.getBeansOfType(BatchItemHandler.class).values()
                .forEach(batchItemHandler -> batchItemHandlerMap.put(batchItemHandler.getBatchTaskType(), batchItemHandler));
        log.info("batch item handler init :{}", JsonUtils.toJsonString(batchEventProcessorMap));
    }

    public BatchItemHandler<?> getBatchItemHandler(String batchProcessType) {
        BatchProcessType batchProcessTypeEnum = BatchProcessType.valueOf(batchProcessType);
        return batchItemHandlerMap.get(batchProcessTypeEnum);
    }

    public BatchEventProcessor getBatchEventProcessor(String batchProcessType) {
        BatchProcessType batchProcessTypeEnum = BatchProcessType.valueOf(batchProcessType);
        return batchEventProcessorMap.get(batchProcessTypeEnum);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
       init();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
