package com.corpgovernment.resource.batch.event;

import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.common.mq.enums.MessageBizTypeEnum;
import com.corpgovernment.common.mq.producer.CommonMQProducer;
import com.corpgovernment.resource.batch.domain.BatchTaskItem;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.corpgovernment.common.mq.enums.MessageBizTypeEnum.BATCH_EXECUTION_ACCOUNT_UNIT_SYNC;
import static com.corpgovernment.common.mq.enums.MessageBizTypeEnum.BATCH_EXECUTION_EMPLOYEE_SYNC;
import static com.corpgovernment.common.mq.enums.MessageBizTypeEnum.BATCH_EXECUTION_ORG_SYNC;

@Component
@RequiredArgsConstructor
@Slf4j
public class BatchEventDispatcher {

    private final CommonMQProducer commonMQProducer;

    private static final Map<BatchProcessType, MessageBizTypeEnum> batchTypeTagMap = new HashMap<>();

    static {
        batchTypeTagMap.put(BatchProcessType.SYNC_ACCOUNT_UNIT_INFO, BATCH_EXECUTION_ACCOUNT_UNIT_SYNC);
        batchTypeTagMap.put(BatchProcessType.SYNC_EMPLOYEE_INFO, BATCH_EXECUTION_EMPLOYEE_SYNC);
        batchTypeTagMap.put(BatchProcessType.SYNC_ORG_INFO, BATCH_EXECUTION_ORG_SYNC);
    }

    public void dispatchEvent(Long batchId, String batchType, List<BatchTaskItem> items, Boolean isRetry) {
        log.info("dispatch task event start, batchId:{}, item size:{}", batchId, items.size());
        for (BatchTaskItem batchTaskItem : items) {
            BatchExecuteEvent batchExecuteEvent = new BatchRemoteExecuteEvent();
            batchExecuteEvent.setBatchId(batchTaskItem.getBatchId());
            batchExecuteEvent.setBatchType(batchType);
            if (StringUtils.isBlank(batchTaskItem.getParameter())) {
                log.error("Batch item parameter null, item:{}", JsonUtils.toJsonString(batchTaskItem));
                continue;
            }
            batchExecuteEvent.setParameter(batchTaskItem.getParameter());
            batchExecuteEvent.setBatchDetailId(batchTaskItem.getId());
            batchExecuteEvent.setBatchDetailCreateTime(batchTaskItem.getCreateTime());
            batchExecuteEvent.setBatchBizItemId(batchTaskItem.getBizItemId());
            batchExecuteEvent.setRetryCount(batchTaskItem.getRetryCount());
            batchExecuteEvent.setEventTimestamp(System.currentTimeMillis());
            batchExecuteEvent.setIsRetry(isRetry);
            sendBatchExecuteEvent(batchExecuteEvent);
        }
        log.info("dispatch task event end, batchId:{}", batchId);
    }

    public void sendBatchExecuteEvent(BatchExecuteEvent batchExecuteEvent) {
        try {
            Message message = new Message();
            String batchType = batchExecuteEvent.getBatchType();
            MessageBizTypeEnum messageBizTypeEnum = batchTypeTagMap.get(BatchProcessType.valueOf(batchType));
            if(messageBizTypeEnum == null) {
                log.error("BatchExecuteEvent send failed, batchType not found, batchType:{}", batchType);
                return;
            }
            message.setTopic(messageBizTypeEnum.getTopic());
            message.setBody(JsonUtils.toJsonString(batchExecuteEvent).getBytes());
            message.setTags(messageBizTypeEnum.getTags());

            SendResult result = commonMQProducer.send(message);
            if (result != null && result.getSendStatus() == SendStatus.SEND_OK) {
                log.info("send BatchExecute Event:{}", JsonUtils.toJsonString(batchExecuteEvent));
            }
        } catch (Exception e) {
            log.error("send BatchExecute Event failed:{}", JsonUtils.toJsonString(batchExecuteEvent), e);
        }
    }
}
