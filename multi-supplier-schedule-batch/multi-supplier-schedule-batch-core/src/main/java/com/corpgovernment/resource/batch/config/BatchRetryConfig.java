package com.corpgovernment.resource.batch.config;

import lombok.Data;

/**
 * 批处理重试配置
 */
@Data
public class BatchRetryConfig {
    
    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;
    
    /**
     * 初始延迟时间（秒）
     */
    private Integer initialDelaySeconds = 6;
    
    /**
     * 退避因子
     */
    private Integer backOffFactor = 10;
    
    public BatchRetryConfig() {
    }
    
    public BatchRetryConfig(Integer maxRetryCount, Integer initialDelaySeconds, Integer backOffFactor) {
        this.maxRetryCount = maxRetryCount;
        this.initialDelaySeconds = initialDelaySeconds;
        this.backOffFactor = backOffFactor;
    }
}
