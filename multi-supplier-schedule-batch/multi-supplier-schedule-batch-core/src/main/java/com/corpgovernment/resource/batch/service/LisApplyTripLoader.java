package com.corpgovernment.resource.batch.service;

import com.corpgovernment.api.basic.request.ApplyTripTemporaryStorageRequest;
import com.corpgovernment.resource.batch.dto.LisResponse;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class LisApplyTripLoader {

    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");

    private final OkHttpClient httpClient;

    @Value("${lis.apply.trip.api.url:http://lis-outboud-gateway:8080/docking/approval/save}")
    private String lisApiUrl;

    public LisApplyTripLoader() {
        // Configure OkHttpClient with reasonable timeouts
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    public LisResponse lisApprovalSave(ApplyTripTemporaryStorageRequest applyTripStorage) {
        try {
            if (applyTripStorage==null || StringUtils.isEmpty(applyTripStorage.getRequestJson())) {
                log.warn("requestJson is empty, returning empty LisResponse");
                throw new IllegalArgumentException("requestJson cannot be empty");
            }

            log.info("/lis/gateway/docking/approval/save post lis API withapplyTripStorage: {}", JsonUtils.toJsonString(applyTripStorage));

            // Create request body
            RequestBody body = RequestBody.create(JSON_MEDIA_TYPE, applyTripStorage.getRequestJson());

            // Build request
            Request httpRequest = new Request.Builder()
                    .url(lisApiUrl)
                    .post(body)
                    .header("Content-Type", "application/json")
                    .header("Accept", "application/json")
                    .header("client_id", applyTripStorage.getClientId())
                    .header("tenant_id", applyTripStorage.getTenantId())
                    .build();
            
            // Execute request
            try (Response response = httpClient.newCall(httpRequest).execute()) {
                if(response == null || response.body() == null) {   
                    throw new IOException("Response is null");
                }

                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected response code: " + response.code());
                }
                
                String responseBody = response.body().string();
                log.info("Received lis API response: {}", responseBody);
                return JsonUtils.parse(responseBody, LisResponse.class);
            }
        } catch (Exception e) {
            log.error("Failed to query lis API", e);
            throw new RuntimeException("Failed to lisApprovalSave lis API", e);
        }
    }
}
