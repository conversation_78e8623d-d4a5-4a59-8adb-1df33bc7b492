package com.corpgovernment.resource.batch;

import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.batch.enums.BatchStatus;
import com.corpgovernment.api.batch.request.UploadBatchItemsRequest;
import com.corpgovernment.api.batch.response.UploadBatchItemsResponse;
import com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.domain.BatchTaskItem;
import com.corpgovernment.resource.batch.exception.BatchInitException;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
public abstract class AbstractBatchItemHandler<T> implements BatchItemHandler<T> {

    private final BatchTaskService batchTaskService;

    private static final int MAX_BATCH_SIZE = 1000;

    @Override
    public T convert(String item) {
        try {
            return JsonUtils.getObjectMapper().readValue(item, getBatchItemClass());
        } catch (Exception e) {
            log.info("Batch Item Value Convert Error, item:{}", item);
            return null;
        }
    }

    @Override
    public UploadBatchItemsResponse process(UploadBatchItemsRequest request) {
        if (request == null) {
            return null;
        }

        Long batchId = request.getBatchId();
        if (batchId == null) {
            throw new BatchInitException("BatchId不能为空");
        }

        if (CollectionUtils.isEmpty(request.getItems())) {
            throw new BatchInitException("Batch 明细不能为空");
        }

        if (request.getItems().size() > MAX_BATCH_SIZE) {
            throw new BatchInitException("Batch明细行不能超过:" + MAX_BATCH_SIZE);
        }

        // Basic Param Check
        List<String> items = request.getItems();

        List<T> convertItems = items.stream().map(this::convert).collect(Collectors.toList());

        UploadBatchItemsResponse response = new UploadBatchItemsResponse();
        BatchItemValidator<T> validator = validator();
        if (validator == null) {
            throw new BatchInitException("Batch Validator不能为空");
        }
        List<BatchItemResult> batchItemResults = validator.proactiveValidate(convertItems);
        if (CollectionUtils.isNotEmpty(batchItemResults)) {
            List<UploadBatchItemsResponse.BatchItemError> itemErrors = batchItemResults
                    .stream()
                    .filter( r-> BatchItemStatus.FAILED.equals(r.getBatchItemStatus()))
                    .map(error -> new UploadBatchItemsResponse.BatchItemError(error.getId(), error.getBizItemId(), error.getErrorMessage()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(itemErrors)){
                response.setErrorReasons(itemErrors);
                response.setStatus(BatchStatus.FAILED.name());
                response.setMessage("Batch前置校验失败");
                return response;
            }
        }

        saveItems(batchId, convertItems);

        if (BooleanUtils.isTrue(request.getIsLastChunk())) {
            batchTaskService.updateBatchTaskStatus(batchId, BatchStatus.READY);
        }
        response.setBatchId(batchId);
        response.setStatus(BatchStatus.READY.name());
        return response;
    }


    @Transactional(rollbackFor = Exception.class)
    public List<BatchTaskItem> saveItems(Long batchId, List<T> items) {
        // save Batch & Item
        List<BatchTaskItem> batchTaskItems = new ArrayList<>();
        List<BatchTaskItemEntity> batchTaskItemEntities = new ArrayList<>(items.stream()
                .map(item -> {
                    BatchTaskItemEntity entity = new BatchTaskItemEntity();
                    entity.setBatchId(batchId);
                    entity.setBizItemId(generateBizItemId(item));
                    entity.setParameter(JsonUtils.toJsonString(item));
                    entity.setBatchProcessType(getBatchTaskType().name());
                    entity.setRetryCount(0);
                    entity.setStatus(BatchStatus.INIT.name());
                    entity.setCreateTime(new Date());
                    entity.setUpdateTime(new Date());
                    return entity;
                })
                .collect(Collectors.toMap(
                        BatchTaskItemEntity::getBizItemId,
                        entity -> entity,
                        (existing, replacement) -> existing))
                .values());

        batchTaskService.batchInsert(batchTaskItemEntities);
        return batchTaskItems;
    }

}
