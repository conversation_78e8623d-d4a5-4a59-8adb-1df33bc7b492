package com.corpgovernment.resource.batch.service;

import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.batch.enums.BatchStatus;
import com.corpgovernment.api.batch.request.CreateBatchTaskRequest;
import com.corpgovernment.api.batch.request.QueryBatchDetailRequest;
import com.corpgovernment.api.batch.request.QueryBatchRequest;
import com.corpgovernment.api.batch.request.UploadBatchItemsRequest;
import com.corpgovernment.api.batch.response.QueryBatchDetailResponse;
import com.corpgovernment.api.batch.response.QueryBatchResponse;
import com.corpgovernment.api.batch.response.UploadBatchItemsResponse;
import com.corpgovernment.resource.batch.BatchItemHandler;
import com.corpgovernment.resource.batch.data.BatchStatusCount;
import com.corpgovernment.resource.batch.data.entity.BatchTaskEntity;
import com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity;
import com.corpgovernment.resource.batch.data.mapper.BatchTaskItemMapper;
import com.corpgovernment.resource.batch.data.mapper.BatchTaskMapper;
import com.corpgovernment.resource.batch.data.query.BatchTaskDataQuery;
import com.corpgovernment.resource.batch.data.query.BatchTaskItemDataQuery;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.domain.BatchTask;
import com.corpgovernment.resource.batch.domain.BatchTaskItem;
import com.corpgovernment.resource.batch.event.BatchExecuteEvent;
import com.corpgovernment.resource.batch.event.processor.BatchEventProcessorRegistry;
import com.corpgovernment.resource.batch.exception.BatchInitException;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.base.MoreObjects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class BatchTaskService {

    private static final int BACK_OFF_FACTOR = 20;
    private static final int INITIAL_DELAY_IN_SECONDS = 10;
    private final BatchTaskMapper batchTaskMapper;

    private final BatchTaskItemMapper batchTaskItemMapper;

    private final BatchEventProcessorRegistry batchEventProcessorRegistry;

    @Resource
    private ThreadPoolExecutor batchThreadPoolExecutor;

    private final static Long BATCH_TASK_TIMEOUT = 24 * 60 * 60 * 1000L;

    public UploadBatchItemsResponse uploadBatchItems(UploadBatchItemsRequest request) {
        if (request == null) {
            throw new BatchInitException("request不能为空");
        }

        if (request.getBatchId() == null) {
            throw new BatchInitException("BatchId不能为空");
        }
        Long batchId = request.getBatchId();
        BatchTaskEntity batchTaskEntity = batchTaskMapper.selectByPrimaryKey(batchId);
        if (batchTaskEntity == null) {
            throw new BatchInitException("Batch未找到");
        }
        String batchProcessType = batchTaskEntity.getBatchProcessType();
        BatchItemHandler<?> batchItemHandler = batchEventProcessorRegistry.getBatchItemHandler(batchProcessType);
        if (batchItemHandler == null) {
            throw new BatchInitException("没有找到该 Batch 处理器， Batch Type:" + batchProcessType);
        }
        log.info("Upload Batch Items Request:{}, handler:{}", JsonUtils.toJsonString(request), batchItemHandler.getBatchTaskType());
        UploadBatchItemsResponse response = null;
        try {
            response = batchItemHandler.process(request);
            log.info("Upload Batch Items Response:{}", JsonUtils.toJsonString(response));
            return response;
        } catch (Exception e) {
            log.error("batch item upload failed, batchId:{}", batchId, e);
            updateBatchTaskStatus(batchId, BatchStatus.FAILED);
            throw new BatchInitException("Batch Item 上传失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Long saveBatchTask(CreateBatchTaskRequest request) {
        BatchTask batchTask = new BatchTask();
        batchTask.setChannel(request.getChannel());
        batchTask.setBatchProcessType(request.getBatchProcessType());
        batchTask.setTotalCount(request.getTotalItemCount());
        batchTask.setStatus(BatchStatus.INIT.name());
        batchTask.setCreateTime(new Date());
        batchTask.setStartTime(new Date());
        batchTask.setMetaData(request.getMetaData());

        BatchTaskEntity batchTaskEntity = batchTask.to();
        batchTaskMapper.insert(batchTaskEntity);
        return batchTaskEntity.getId();
    }

    public BatchTask getBatchTask(Long batchId) {
        BatchTaskEntity batchTaskEntity = batchTaskMapper.selectByPrimaryKey(batchId);
        if (batchTaskEntity == null) {
            return null;
        }
        if (BatchStatus.PROCESSING.name().equals(batchTaskEntity.getStatus())) {
            updateBatchStatusAndCount(batchTaskEntity);
        }
        return BatchTask.from(batchTaskEntity);
    }

    public void updateBatchStatusAndCount(BatchTaskEntity batchTask) {
        if (!BatchStatus.PROCESSING.name().equals(batchTask.getStatus())) {
            return;
        }

        Long batchId = batchTask.getId();
        Date createTime = batchTask.getCreateTime();
        if (createTime == null) {
            return;
        }
        Date now = new Date();
        // 兜底处理超时情况，超过指定时间依然还在执行，设置为终态
        if (now.getTime() - createTime.getTime() > BATCH_TASK_TIMEOUT) {
            batchTask.setStatus(BatchStatus.STOPPED.name());
            batchTask.setEndTime(now);
            batchTaskMapper.updateByPrimaryKeySelective(batchTask);
            log.info("batch task 执行超时 batchId:{}", batchId);
            return;
        }

        int totalCount = MoreObjects.firstNonNull(batchTask.getTotalCount(), 0);
        // 根据 Batch Item执行情况，设置Batch主表的状态
        List<BatchStatusCount> batchItemStatuses = batchTaskItemMapper.groupAndCountByStatus(batchTask.getId());
        if (CollectionUtils.isEmpty(batchItemStatuses)) {
            return;
        }
        Map<String, Integer> statusCountMap = batchItemStatuses.stream().collect(Collectors.toMap(BatchStatusCount::getStatus, BatchStatusCount::getCount) );
        log.info("query batch task status batch id:{}, countMap:{}", batchTask.getId(), JsonUtils.toJsonString(statusCountMap));

        // update batch status and count
        int failedCount = statusCountMap.getOrDefault(BatchItemStatus.FAILED.name(), 0);
        int successCount = statusCountMap.getOrDefault(BatchItemStatus.SUCCESS.name(), 0);
        int skippedCount = statusCountMap.getOrDefault(BatchItemStatus.SKIPPED.name(), 0);

        int processedCount = failedCount + successCount + skippedCount;

        batchTask.setFailedCount(failedCount);
        batchTask.setProcessedCount(processedCount);

        if (failedCount == 0 && processedCount == totalCount) {
            batchTask.setStatus(BatchStatus.SUCCESS.name());
            batchTask.setEndTime(now);
        } else if (failedCount > 0 && processedCount == totalCount && failedCount < totalCount) {
            batchTask.setStatus(BatchStatus.PARTIALLY_SUCCESS.name());
            batchTask.setEndTime(now);
        } else if (failedCount > 0 && failedCount == totalCount) {
            batchTask.setStatus(BatchStatus.FAILED.name());
            batchTask.setEndTime(now);
        }
        batchTaskMapper.updateByPrimaryKeySelective(batchTask);
    }


    public void updateBatchItemResult(BatchItemResult batchItemResult) {
        BatchTaskItemEntity batchTaskItemEntity = new BatchTaskItemEntity();
        batchTaskItemEntity.setId(batchItemResult.getId());

        batchTaskItemEntity.setStatus(batchItemResult.getBatchItemStatus().name());
        if (StringUtils.isNotBlank(batchItemResult.getErrorMessage())) {
            batchTaskItemEntity.setErrorMessage(batchItemResult.getErrorMessage());
        }
        batchTaskItemMapper.updateByPrimaryKeySelective(batchTaskItemEntity);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateBatchItemError(BatchItemResult result) {
        BatchTaskItemEntity taskItemEntity = batchTaskItemMapper.selectByPrimaryKey(result.getId());
        if (taskItemEntity == null) {
            return;
        }

        if (result.getBatchItemStatus() == null) {
            taskItemEntity.setStatus(BatchItemStatus.FAILED.name());
        } else {
            taskItemEntity.setStatus(result.getBatchItemStatus().name());
        }

        taskItemEntity.setErrorMessage(result.getErrorMessage());
        batchTaskItemMapper.updateByPrimaryKeySelective(taskItemEntity);
    }

    public void saveRetryBatchItem(BatchExecuteEvent event, String errorMessage) {
        Long batchDetailId = event.getBatchDetailId();
        try {
            BatchTaskItemEntity taskItemEntity = batchTaskItemMapper.selectByPrimaryKey(batchDetailId);
            if (taskItemEntity == null) {
                return;
            }

            if (BatchItemStatus.INIT.name().equals(taskItemEntity.getStatus())) {
                taskItemEntity.setStatus(BatchItemStatus.RETRYING.name());
                taskItemEntity.setRetryCount(0);
                Date nextRetryTime = calculateNextRetryTime(event.getBatchDetailCreateTime(), 1);
                taskItemEntity.setNextRetryTime(nextRetryTime);
            }else {
                int retryCount = MoreObjects.firstNonNull(taskItemEntity.getRetryCount(), 0);
                taskItemEntity.setRetryCount(retryCount + 1);
                if (retryCount >= 2) {
                    taskItemEntity.setStatus(BatchItemStatus.FAILED.name());
                } else {
                    Date nextRetryTime = calculateNextRetryTime(event.getBatchDetailCreateTime(), retryCount);
                    taskItemEntity.setNextRetryTime(nextRetryTime);
                }
            }

            taskItemEntity.setErrorMessage(errorMessage);
            log.info("save retry batch item, batchDetailId:{}, taskItemEntity:{}", batchDetailId, JsonUtils.toJsonString(taskItemEntity));
            batchTaskItemMapper.updateByPrimaryKeySelective(taskItemEntity);
        } catch (Exception e) {
            log.error("save retry batch item error, event:{}", JsonUtils.toJsonString(event), e);
        }
    }

    public void updateBatchItemError(Long batchDetailId, String errorMessage) {
        BatchTaskItemEntity taskItemEntity = new BatchTaskItemEntity();
        taskItemEntity.setId(batchDetailId);
        taskItemEntity.setStatus(BatchItemStatus.FAILED.name());
        taskItemEntity.setErrorMessage(errorMessage);
        batchTaskItemMapper.updateByPrimaryKeySelective(taskItemEntity);
    }

    public QueryBatchResponse queryBatchTasks(QueryBatchRequest request) {
        BatchTaskDataQuery query = BatchTaskDataQuery.builder()
                .status(request.getStatus())
                .startTime(request.getStartTime())
                .endTime(request.getEndTime())
                .batchProcessType(request.getBatchProcessType())
                .offset((request.getPageNo() - 1) * request.getPageSize())
                .limit(request.getPageSize())
                .build();

        List<BatchTaskEntity> entities = batchTaskMapper.pageQueryBatchTasks(query);
        int total = batchTaskMapper.countBatchTasks(query);

        QueryBatchResponse response = new QueryBatchResponse();
        response.setTotal(total);
        response.setPageNo(request.getPageNo());
        response.setPageSize(request.getPageSize());
        response.setItems(entities.stream()
                .map(BatchTask::from)
                .map(BatchTask::toResp)
                .collect(Collectors.toList()));

        return response;
    }

    public QueryBatchDetailResponse pageQueryBatchDetail(QueryBatchDetailRequest request) {
        BatchTaskItemDataQuery query = BatchTaskItemDataQuery.builder()
                .batchId(request.getBatchId())
                .status(request.getStatus())
                .offset((request.getPageNo() - 1) * request.getPageSize())
                .limit(request.getPageSize())
                .build();

        List<BatchTaskItemEntity> entities = batchTaskItemMapper.pageQueryBatchDetail(query);
        int total = batchTaskItemMapper.countBatchTaskDetails(query);

        QueryBatchDetailResponse response = new QueryBatchDetailResponse();
        response.setPageNo(request.getPageNo());
        response.setPageSize(request.getPageSize());
        response.setTotal(total);
        response.setItems(entities.stream()
                .map(BatchTaskItem::from)
                .map(BatchTaskItem::toResp).collect(Collectors.toList()));
        return response;
    }


    public void insert(BatchTaskEntity batchTaskEntity) {
        batchTaskMapper.insert(batchTaskEntity);
    }

    public void batchInsert(List<BatchTaskItemEntity> batchTaskItemEntities) {
        batchTaskItemMapper.batchInsert(batchTaskItemEntities);
    }

    public void updateBatchTaskStatus(Long id, BatchStatus batchStatus) {

        BatchTaskEntity batchTaskEntity = new BatchTaskEntity();
        batchTaskEntity.setId(id);
        batchTaskEntity.setStatus(batchStatus.name());
        batchTaskMapper.updateByPrimaryKeySelective(batchTaskEntity);
    }

    public Date calculateNextRetryTime(Date createTime, int retries) {
        long delay = (long) (INITIAL_DELAY_IN_SECONDS * Math.pow(BACK_OFF_FACTOR, retries));
        Instant instant = createTime.toInstant().plusSeconds(delay);
        return Date.from(instant);
    }
}
