package com.corpgovernment.resource.batch.service;

import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.batch.enums.BatchStatus;
import com.corpgovernment.api.batch.request.CreateBatchTaskRequest;
import com.corpgovernment.api.batch.request.QueryBatchDetailRequest;
import com.corpgovernment.api.batch.request.QueryBatchRequest;
import com.corpgovernment.api.batch.request.UploadBatchItemsRequest;
import com.corpgovernment.api.batch.response.QueryBatchDetailResponse;
import com.corpgovernment.api.batch.response.QueryBatchResponse;
import com.corpgovernment.api.batch.response.UploadBatchItemsResponse;
import com.corpgovernment.resource.batch.BatchItemHandler;
import com.corpgovernment.resource.batch.config.BatchRetryConfig;
import com.corpgovernment.resource.batch.data.BatchStatusCount;
import com.corpgovernment.resource.batch.data.entity.BatchTaskEntity;
import com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity;
import com.corpgovernment.resource.batch.data.mapper.BatchTaskItemMapper;
import com.corpgovernment.resource.batch.data.mapper.BatchTaskMapper;
import com.corpgovernment.resource.batch.data.query.BatchTaskDataQuery;
import com.corpgovernment.resource.batch.data.query.BatchTaskItemDataQuery;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.domain.BatchTask;
import com.corpgovernment.resource.batch.domain.BatchTaskItem;
import com.corpgovernment.resource.batch.event.BatchExecuteEvent;
import com.corpgovernment.resource.batch.event.processor.BatchEventProcessorRegistry;
import com.corpgovernment.resource.batch.exception.BatchInitException;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.google.common.base.MoreObjects;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@RefreshScope
public class BatchTaskService {

    private static final int BACK_OFF_FACTOR = 10;

    private static final int INITIAL_DELAY_IN_SECONDS = 6;

    private final BatchTaskMapper batchTaskMapper;

    private final BatchTaskItemMapper batchTaskItemMapper;

    private final BatchEventProcessorRegistry batchEventProcessorRegistry;

    @Value("${batch.retry.item.max.count:3}")
    private Integer batchItemRetryCount;

    private String batchTypeRetryConfigJson;

    private volatile Map<String, BatchRetryConfig> batchTypeRetryConfig;

    @Resource
    private ThreadPoolExecutor batchThreadPoolExecutor;

    private final static Long BATCH_TASK_TIMEOUT = 24 * 60 * 60 * 1000L;

    @PostConstruct
    public void init() {
        refreshBatchTypeRetryConfig();
    }

    /**
     * 初始化批处理类型重试配置
     *   {
     *    "SCHEDULE_TASK": {
     *      "maxRetryCount": 5,
     *      "initialDelaySeconds": 10,
     *      "backOffFactor": 15
     *    },
     *    "DATA_IMPORT": {
     *      "maxRetryCount": 2,
     *      "initialDelaySeconds": 3,
     *      "backOffFactor": 5
     *    },
     *    "NOTIFICATION": {
     *      "maxRetryCount": 3,
     *      "initialDelaySeconds": 6,
     *      "backOffFactor": 10
     *    },
     *    "DEFAULT": {
     *      "maxRetryCount": 3,
     *      "initialDelaySeconds": 6,
     *      "backOffFactor": 10
     *    }
     *  }
     */
    @Value("${batch.retry.item.type-specific-json:{}}")
    public void setBatchTypeRetryConfigJson(String json) {
        this.batchTypeRetryConfigJson = json;
        refreshBatchTypeRetryConfig();
    }

    /**
     * 刷新批处理类型重试配置
     * 支持Apollo配置动态更新
     */
    public synchronized void refreshBatchTypeRetryConfig() {
        Map<String, BatchRetryConfig> newConfig = new HashMap<>();
        try {
            if (StringUtils.isNotBlank(batchTypeRetryConfigJson)) {
                TypeReference<Map<String, BatchRetryConfig>> typeRef = new TypeReference<Map<String, BatchRetryConfig>>() {};
                newConfig = JsonUtils.parse(batchTypeRetryConfigJson, typeRef);
                log.info("Successfully refreshed batch type retry config: {}", JsonUtils.toJsonString(newConfig));
            }
        } catch (Exception e) {
            log.error("Failed to parse batch type retry config json: {}, using previous config", batchTypeRetryConfigJson, e);
            // 如果解析失败，保持当前配置不变
            if (batchTypeRetryConfig == null) {
                newConfig = new HashMap<>();
            } else {
                return; // 保持原有配置
            }
        }
        
        // 原子性更新配置
        batchTypeRetryConfig = newConfig;
    }

    public UploadBatchItemsResponse uploadBatchItems(UploadBatchItemsRequest request) {
        if (request == null) {
            throw new BatchInitException("request不能为空");
        }

        if (request.getBatchId() == null) {
            throw new BatchInitException("BatchId不能为空");
        }
        Long batchId = request.getBatchId();
        BatchTaskEntity batchTaskEntity = batchTaskMapper.selectByPrimaryKey(batchId);
        if (batchTaskEntity == null) {
            throw new BatchInitException("Batch未找到");
        }
        String batchProcessType = batchTaskEntity.getBatchProcessType();
        BatchItemHandler<?> batchItemHandler = batchEventProcessorRegistry.getBatchItemHandler(batchProcessType);
        if (batchItemHandler == null) {
            throw new BatchInitException("没有找到该 Batch 处理器， Batch Type:" + batchProcessType);
        }
        log.info("Upload Batch Items Request:{}, handler:{}", JsonUtils.toJsonString(request), batchItemHandler.getBatchTaskType());
        UploadBatchItemsResponse response = null;
        try {
            response = batchItemHandler.process(request);
            log.info("Upload Batch Items Response:{}", JsonUtils.toJsonString(response));
            return response;
        } catch (Exception e) {
            log.error("batch item upload failed, batchId:{}", batchId, e);
            updateBatchTaskStatus(batchId, BatchStatus.FAILED);
            throw new BatchInitException("Batch Item 上传失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Long saveBatchTask(CreateBatchTaskRequest request) {
        BatchTask batchTask = new BatchTask();
        batchTask.setChannel(request.getChannel());
        batchTask.setBatchProcessType(request.getBatchProcessType());
        batchTask.setTotalCount(request.getTotalItemCount());
        batchTask.setStatus(BatchStatus.INIT.name());
        batchTask.setCreateTime(new Date());
        batchTask.setStartTime(new Date());
        batchTask.setMetaData(request.getMetaData());

        BatchTaskEntity batchTaskEntity = batchTask.to();
        batchTaskMapper.insert(batchTaskEntity);
        return batchTaskEntity.getId();
    }

    public BatchTask getBatchTask(Long batchId) {
        BatchTaskEntity batchTaskEntity = batchTaskMapper.selectByPrimaryKey(batchId);
        if (batchTaskEntity == null) {
            return null;
        }
        if (BatchStatus.PROCESSING.name().equals(batchTaskEntity.getStatus())) {
            updateBatchStatusAndCount(batchTaskEntity);
        }
        return BatchTask.from(batchTaskEntity);
    }

    public void updateBatchStatusAndCount(BatchTaskEntity batchTask) {
        if (!BatchStatus.PROCESSING.name().equals(batchTask.getStatus())) {
            return;
        }

        Long batchId = batchTask.getId();
        Date createTime = batchTask.getCreateTime();
        if (createTime == null) {
            return;
        }
        Date now = new Date();
        // 兜底处理超时情况，超过指定时间依然还在执行，设置为终态
        if (now.getTime() - createTime.getTime() > BATCH_TASK_TIMEOUT) {
            batchTask.setStatus(BatchStatus.STOPPED.name());
            batchTask.setEndTime(now);
            batchTaskMapper.updateByPrimaryKeySelective(batchTask);
            log.info("batch task 执行超时 batchId:{}", batchId);
            return;
        }

        int totalCount = MoreObjects.firstNonNull(batchTask.getTotalCount(), 0);
        // 根据 Batch Item执行情况，设置Batch主表的状态
        List<BatchStatusCount> batchItemStatuses = batchTaskItemMapper.groupAndCountByStatus(batchTask.getId());
        if (CollectionUtils.isEmpty(batchItemStatuses)) {
            return;
        }
        Map<String, Integer> statusCountMap = batchItemStatuses.stream().collect(Collectors.toMap(BatchStatusCount::getStatus, BatchStatusCount::getCount) );
        log.info("query batch task status batch id:{}, countMap:{}", batchTask.getId(), JsonUtils.toJsonString(statusCountMap));

        // update batch status and count
        int failedCount = statusCountMap.getOrDefault(BatchItemStatus.FAILED.name(), 0);
        int successCount = statusCountMap.getOrDefault(BatchItemStatus.SUCCESS.name(), 0);
        int skippedCount = statusCountMap.getOrDefault(BatchItemStatus.SKIPPED.name(), 0);

        int processedCount = failedCount + successCount + skippedCount;

        batchTask.setFailedCount(failedCount);
        batchTask.setProcessedCount(processedCount);

        if (failedCount == 0 && processedCount == totalCount) {
            batchTask.setStatus(BatchStatus.SUCCESS.name());
            batchTask.setEndTime(now);
        } else if (failedCount > 0 && processedCount == totalCount && failedCount < totalCount) {
            batchTask.setStatus(BatchStatus.PARTIALLY_SUCCESS.name());
            batchTask.setEndTime(now);
        } else if (failedCount > 0 && failedCount == totalCount) {
            batchTask.setStatus(BatchStatus.FAILED.name());
            batchTask.setEndTime(now);
        }
        batchTaskMapper.updateByPrimaryKeySelective(batchTask);
    }


    public void updateBatchItemResult(BatchItemResult batchItemResult) {
        BatchTaskItemEntity batchTaskItemEntity = new BatchTaskItemEntity();
        batchTaskItemEntity.setId(batchItemResult.getId());

        batchTaskItemEntity.setStatus(batchItemResult.getBatchItemStatus().name());
        if (StringUtils.isNotBlank(batchItemResult.getErrorMessage())) {
            batchTaskItemEntity.setErrorMessage(batchItemResult.getErrorMessage());
        }
        batchTaskItemMapper.updateByPrimaryKeySelective(batchTaskItemEntity);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateBatchItemError(BatchItemResult result) {
        BatchTaskItemEntity taskItemEntity = batchTaskItemMapper.selectByPrimaryKey(result.getId());
        if (taskItemEntity == null) {
            return;
        }

        if (result.getBatchItemStatus() == null) {
            taskItemEntity.setStatus(BatchItemStatus.FAILED.name());
        } else {
            taskItemEntity.setStatus(result.getBatchItemStatus().name());
        }

        taskItemEntity.setErrorMessage(result.getErrorMessage());
        batchTaskItemMapper.updateByPrimaryKeySelective(taskItemEntity);
    }

    public void saveRetryBatchItem(BatchExecuteEvent event, String errorMessage) {
        Long batchDetailId = event.getBatchDetailId();
        try {
            BatchTaskItemEntity taskItemEntity = batchTaskItemMapper.selectByPrimaryKey(batchDetailId);
            if (taskItemEntity == null) {
                return;
            }

            // 根据批处理类型获取重试配置
            BatchRetryConfig retryConfig = getRetryConfigByBatchType(event.getBatchType());

            if (BatchItemStatus.INIT.name().equals(taskItemEntity.getStatus())) {
                taskItemEntity.setStatus(BatchItemStatus.RETRYING.name());
                taskItemEntity.setRetryCount(0);
                Date nextRetryTime = calculateNextRetryTime(event.getBatchDetailCreateTime(), 1, retryConfig);
                taskItemEntity.setNextRetryTime(nextRetryTime);
                log.info("init retry batch item, batchDetailId:{}, batchType:{}, maxRetryCount:{}, nextRetryTime:{}", 
                        batchDetailId, event.getBatchType(), retryConfig.getMaxRetryCount(), DateUtil.formatDateTime(nextRetryTime));
            }else {
                int retryCount = MoreObjects.firstNonNull(taskItemEntity.getRetryCount(), 0) + 1;
                taskItemEntity.setRetryCount(retryCount);
                if (retryCount >= retryConfig.getMaxRetryCount()) {
                    taskItemEntity.setStatus(BatchItemStatus.FAILED.name());
                    log.info("stop retry batch item, batchDetailId:{}, batchType:{}, current retryCount:{}, maxRetryCount:{}", 
                            batchDetailId, event.getBatchType(), retryCount, retryConfig.getMaxRetryCount());
                } else {
                    Date nextRetryTime = calculateNextRetryTime(event.getBatchDetailCreateTime(), retryCount, retryConfig);
                    taskItemEntity.setStatus(BatchItemStatus.RETRYING.name());
                    taskItemEntity.setNextRetryTime(nextRetryTime);
                    log.info("retry batch item, batchDetailId:{}, batchType:{}, retryCount:{}, maxRetryCount:{}, nextRetryTime:{}", 
                            batchDetailId, event.getBatchType(), retryCount, retryConfig.getMaxRetryCount(), DateUtil.formatDateTime(nextRetryTime));
                }
            }

            taskItemEntity.setErrorMessage(errorMessage);
            batchTaskItemMapper.updateByPrimaryKeySelective(taskItemEntity);
        } catch (Exception e) {
            log.error("save retry batch item error, event:{}", JsonUtils.toJsonString(event), e);
        }
    }

    public void updateBatchItemError(Long batchDetailId, String errorMessage) {
        BatchTaskItemEntity taskItemEntity = new BatchTaskItemEntity();
        taskItemEntity.setId(batchDetailId);
        taskItemEntity.setStatus(BatchItemStatus.FAILED.name());
        taskItemEntity.setErrorMessage(errorMessage);
        batchTaskItemMapper.updateByPrimaryKeySelective(taskItemEntity);
    }

    public QueryBatchResponse queryBatchTasks(QueryBatchRequest request) {
        BatchTaskDataQuery query = BatchTaskDataQuery.builder()
                .status(request.getStatus())
                .startTime(request.getStartTime())
                .endTime(request.getEndTime())
                .batchProcessType(request.getBatchProcessType())
                .offset((request.getPageNo() - 1) * request.getPageSize())
                .limit(request.getPageSize())
                .build();

        List<BatchTaskEntity> entities = batchTaskMapper.pageQueryBatchTasks(query);
        int total = batchTaskMapper.countBatchTasks(query);

        QueryBatchResponse response = new QueryBatchResponse();
        response.setTotal(total);
        response.setPageNo(request.getPageNo());
        response.setPageSize(request.getPageSize());
        response.setItems(entities.stream()
                .map(BatchTask::from)
                .map(BatchTask::toResp)
                .collect(Collectors.toList()));

        return response;
    }

    public QueryBatchDetailResponse pageQueryBatchDetail(QueryBatchDetailRequest request) {
        BatchTaskItemDataQuery query = BatchTaskItemDataQuery.builder()
                .batchId(request.getBatchId())
                .status(request.getStatus())
                .batchProcessType(request.getBatchProcessType())
                .offset((request.getPageNo() - 1) * request.getPageSize())
                .limit(request.getPageSize())
                .build();
        if(CollectionUtils.isNotEmpty(request.getBizItemIds())) {
            query.setBizItemIds(new ArrayList<>(request.getBizItemIds()));
        }

        List<BatchTaskItemEntity> entities = batchTaskItemMapper.pageQueryBatchDetail(query);
        int total = batchTaskItemMapper.countBatchTaskDetails(query);

        QueryBatchDetailResponse response = new QueryBatchDetailResponse();
        response.setPageNo(request.getPageNo());
        response.setPageSize(request.getPageSize());
        response.setTotal(total);
        response.setItems(entities.stream()
                .map(BatchTaskItem::from)
                .map(BatchTaskItem::toResp).collect(Collectors.toList()));
        return response;
    }


    public void insert(BatchTaskEntity batchTaskEntity) {
        batchTaskMapper.insert(batchTaskEntity);
    }

    public void batchInsert(List<BatchTaskItemEntity> batchTaskItemEntities) {
        batchTaskItemMapper.batchInsert(batchTaskItemEntities);
    }

    public void updateBatchTaskStatus(Long id, BatchStatus batchStatus) {

        BatchTaskEntity batchTaskEntity = new BatchTaskEntity();
        batchTaskEntity.setId(id);
        batchTaskEntity.setStatus(batchStatus.name());
        batchTaskMapper.updateByPrimaryKeySelective(batchTaskEntity);
    }

    public void updateBatchTaskItemStatus(List<Long> idList, BatchItemStatus batchStatus) {
        batchTaskItemMapper.updateBatchItemStatus(idList, batchStatus.name());
    }

    /**
     * 计算下次重试时间
     * @param createTime
     * @param retries
     * @return
     */
    public Date calculateNextRetryTime(Date createTime, int retries) {
        return calculateNextRetryTime(createTime, retries, null);
    }

    /**
     * 计算下次重试时间（使用自定义重试配置）
     * @param createTime
     * @param retries
     * @param retryConfig
     * @return
     */
    public Date calculateNextRetryTime(Date createTime, int retries, BatchRetryConfig retryConfig) {
        int initialDelay = INITIAL_DELAY_IN_SECONDS;
        int backOffFactor = BACK_OFF_FACTOR;
        
        if (retryConfig != null) {
            initialDelay = retryConfig.getInitialDelaySeconds();
            backOffFactor = retryConfig.getBackOffFactor();
        }
        
        long delay = (long) (initialDelay * Math.pow(backOffFactor, retries));
        Instant instant = createTime.toInstant().plusSeconds(delay);
        return Date.from(instant);
    }

    /**
     * 根据批处理类型获取重试配置
     * @param batchType 批处理类型
     * @return 重试配置
     */
    private BatchRetryConfig getRetryConfigByBatchType(String batchType) {
        if (StringUtils.isBlank(batchType)) {
            return getDefaultRetryConfig();
        }
        
        if (batchTypeRetryConfig != null && batchTypeRetryConfig.containsKey(batchType)) {
            return batchTypeRetryConfig.get(batchType);
        }
        
        // 如果没有找到特定类型的配置，尝试使用DEFAULT配置
        if (batchTypeRetryConfig != null && batchTypeRetryConfig.containsKey("DEFAULT")) {
            return batchTypeRetryConfig.get("DEFAULT");
        }
        
        // 最后使用全局默认配置
        return getDefaultRetryConfig();
    }

    /**
     * 获取默认重试配置
     * @return 默认重试配置
     */
    private BatchRetryConfig getDefaultRetryConfig() {
        return new BatchRetryConfig(batchItemRetryCount, INITIAL_DELAY_IN_SECONDS, BACK_OFF_FACTOR);
    }
}
