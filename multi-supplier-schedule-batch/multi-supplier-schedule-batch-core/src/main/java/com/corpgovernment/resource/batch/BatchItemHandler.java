package com.corpgovernment.resource.batch;

import com.corpgovernment.api.batch.enums.BatchProcessType;
import com.corpgovernment.api.batch.request.CreateBatchTaskRequest;
import com.corpgovernment.api.batch.request.UploadBatchItemsRequest;
import com.corpgovernment.api.batch.response.CreateBatchTaskResponse;
import com.corpgovernment.api.batch.response.UploadBatchItemsResponse;

public interface BatchItemHandler<T> {

    BatchProcessType getBatchTaskType();

    Class<T> getBatchItemClass();

    T convert(String item);

    String generateBizItemId(T item);

    BatchItemValidator<T> validator();

   UploadBatchItemsResponse process(UploadBatchItemsRequest request);


}
