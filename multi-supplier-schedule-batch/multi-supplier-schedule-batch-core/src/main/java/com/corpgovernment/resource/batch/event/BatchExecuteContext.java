package com.corpgovernment.resource.batch.event;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@SuppressWarnings("unchecked")
@AllArgsConstructor
public class BatchExecuteContext {

    private final Map<Object, Object> map = new ConcurrentHashMap<>();

    @Getter
    private BatchExecuteEvent batchExecuteEvent;

    public <T> T put(Object key, T object) {
        return (T) this.map.put(key, object);
    }

    public <T> T get(Object key) {
        return (T) this.map.get(key);
    }

}