package com.corpgovernment.resource.batch.event.processor;

import com.corpgovernment.api.batch.enums.BatchItemStatus;
import com.corpgovernment.api.batch.enums.BatchStatus;
import com.corpgovernment.basicdata.utils.StringUtil;
import com.corpgovernment.resource.batch.BatchItemValidator;
import com.corpgovernment.resource.batch.domain.BatchItemResult;
import com.corpgovernment.resource.batch.event.BatchExecuteContext;
import com.corpgovernment.resource.batch.event.BatchExecuteEvent;
import com.corpgovernment.resource.batch.event.BatchItem;
import com.corpgovernment.resource.batch.exception.BatchParametersInvalidException;
import com.corpgovernment.resource.batch.exception.RetryBatchExecutionException;
import com.corpgovernment.resource.batch.service.BatchTaskService;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.utils.JsonUtils;

import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractEventProcessor<T> implements BatchEventProcessor {

    private static final String CIRCUIT_BREAKER_KEY_PREFIX = "batch:circuit:breaker:";

    @Value("${batch.circuit.breaker.error.threshold:100}")
    private int errorThreshold;

    @Value("${batch.circuit.breaker.window.size.seconds:60}")
    private int windowSizeSeconds;

    // 是否启用熔断器
    @Value("${batch.circuit.breaker.enabled:true}")
    private boolean circuitBreakerEnabled;

    @Value("${batch.retry.keywords:''}")
    private String retryKeywords;

    private final BatchTaskService batchTaskService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public void process(BatchExecuteEvent event, BatchExecuteContext context) {
        log.info("start to process batch event, batchId:{}, batchDetailId:{}, parameter:{}",
                event.getBatchId(), event.getBatchDetailId(), event.getParameter());
        BatchItem<T> itemParameter = getItemParameters(event);
        BatchItemValidator<T> itemValidator = getItemValidator();
        if (itemValidator == null) {
            throw new BatchParametersInvalidException("参数校验失败");
        }
        BatchItemResult result = itemValidator.validate(itemParameter);
        if (result != null && (BatchItemStatus.FAILED == result.getBatchItemStatus()
                || BatchItemStatus.SKIPPED == result.getBatchItemStatus())) {
            // update batch item id and error message
            batchTaskService.updateBatchItemError(result);
            return;
        }

        Long batchId = event.getBatchId();
        try {
            preProcess(itemParameter, context);
            BatchItemResult batchItemResult = doProcess(itemParameter, context);

            if (circuitBreakerEnabled && batchItemResult.getBatchItemStatus() == BatchItemStatus.FAILED) {
                // 如果处理失败，记录错误信息
                if (recordError(batchId)) {
                    suspendBatch(batchId);
                }
            }

            postProcess(itemParameter, context);
            batchTaskService.updateBatchItemResult(batchItemResult);
        } catch (FeignException | RetryBatchExecutionException exception) {
            log.error("process batch event with retry error, batch id:{}, detail id:{}, type:{}, param:{}",
                    event.getBatchId(), event.getBatchDetailId(), event.getBatchType(), event.getParameter(),
                    exception);
            batchTaskService.saveRetryBatchItem(event, exception.getMessage());
        } catch (Exception e) {
            log.error("process batch event error, batch id:{}, detail id:{}, type:{}, param:{}",
                    event.getBatchId(), event.getBatchDetailId(), event.getBatchType(), event.getParameter(), e);
            Long batchDetailId = event.getBatchDetailId();
            batchTaskService.updateBatchItemError(batchDetailId, e.getMessage());
            onError(itemParameter, context, e);
        } finally {
            log.info("finish processing batch event, batchId:{}, batchDetailId:{}", batchId, event.getBatchBizItemId());
        }
    }

    public BatchItem<T> getItemParameters(BatchExecuteEvent event) {
        T parse = JsonUtils.parse(event.getParameter(), getParameterType());
        return new BatchItem<>(event.getBatchDetailId(), parse);
    }

    protected void onError(BatchItem<T> parameter, BatchExecuteContext context, Exception e) {

    }

    protected void preProcess(BatchItem<T> parameter, BatchExecuteContext context) {

    }

    protected void postProcess(BatchItem<T> parameter, BatchExecuteContext context) {

    }


    protected boolean shouldRetry(String errorMessage) {
        if (StringUtil.isBlank(errorMessage)) {
            return false;
        }

        if (StringUtil.isBlank(retryKeywords)) {
            return false;
        }

        String[] retryKeywordsArray = retryKeywords.split(",");
        return Arrays.stream(retryKeywordsArray)
                .filter(StringUtil::isNotBlank)
                .anyMatch(keyword -> {
                    if (errorMessage.contains(keyword)) {
                        log.info("Batch item execution failed, retrying, keyword {}", keyword);
                        return true;
                    }
                    return false;
                });

    }

    protected abstract BatchItemValidator<T> getItemValidator();

    protected abstract BatchItemResult doProcess(BatchItem<T> parameter, BatchExecuteContext context);

    protected abstract Class<T> getParameterType();

    private String getCircuitBreakerKey(Long batchId) {
        return CIRCUIT_BREAKER_KEY_PREFIX + ":" + TenantContext.getTenantId() + ":" + batchId;
    }

    /**
     * 记录错误到 Redis 的 ZSet 中，并检查是否触发熔断
     * 
     * @param batchId
     * @return
     */
    protected boolean recordError(Long batchId) {
        String key = getCircuitBreakerKey(batchId);
        long currentTime = System.currentTimeMillis();

        // 1. 添加当前错误记录到 ZSet
        redisTemplate.opsForZSet().add(key, String.valueOf(currentTime), currentTime);

        // 2. 移除窗口外的数据
        long windowStart = currentTime - windowSizeSeconds * 1000L;
        redisTemplate.opsForZSet().removeRangeByScore(key, 0, windowStart);

        // 3. 设置过期时间
        redisTemplate.expire(key, windowSizeSeconds, TimeUnit.SECONDS);

        // 4. 获取窗口内的错误数量
        long errorCount = redisTemplate.opsForZSet().zCard(key);

        // 5. 检查是否触发熔断
        if (errorCount >= errorThreshold) {
            log.error("Batch Circuit breaker triggered for batch {}: {} errors in {} seconds",
                    batchId, errorCount, windowSizeSeconds);
            return true;
        }

        return false;
    }

    protected void suspendBatch(Long batchId) {
        batchTaskService.updateBatchTaskStatus(batchId, BatchStatus.STOPPED);
    }

    public long getErrorCount(Long batchId) {
        String key = getCircuitBreakerKey(batchId);
        return redisTemplate.opsForZSet().zCard(key);
    }
}
