package com.corpgovernment.resource.batch.domain;

import cn.hutool.core.date.DateUtil;
import com.corpgovernment.api.batch.response.QueryBatchDetailResponse;
import com.corpgovernment.resource.batch.data.entity.BatchTaskItemEntity;
import lombok.Data;

import java.util.Date;

@Data
public class BatchTaskItem {

    private Long id;

    private String bizItemId;

    private Long batchId;

    private String status;

    private String bizType;

    private String parameter;

    private Integer retryCount;

    private Date nextRetryTime;

    private String errorMessage;

    private Date createTime;

    private Date updateTime;

    public static BatchTaskItem from(BatchTaskItemEntity entity) {
        if (entity == null) {
            return null;
        }

        BatchTaskItem item = new BatchTaskItem();
        item.setId(entity.getId());
        item.setBizItemId(entity.getBizItemId());
        item.setBatchId(entity.getBatchId());
        item.setStatus(entity.getStatus());
        item.setErrorMessage(entity.getErrorMessage());
        item.setRetryCount(entity.getRetryCount());
        item.setCreateTime(entity.getCreateTime());
        item.setUpdateTime(entity.getUpdateTime());
        item.setParameter(entity.getParameter());
        item.setNextRetryTime(entity.getNextRetryTime());

        return item;
    }

    public static BatchTaskItemEntity to(BatchTaskItem item) {
        if (item == null) {
            return null;
        }

        BatchTaskItemEntity entity = new BatchTaskItemEntity();
        entity.setId(item.getId());
        entity.setBizItemId(item.getBizItemId());
        entity.setStatus(item.getStatus());
        entity.setErrorMessage(item.getErrorMessage());
        entity.setCreateTime(item.getCreateTime());
        entity.setUpdateTime(item.getUpdateTime());
        entity.setRetryCount(item.getRetryCount());
        entity.setParameter(item.getParameter());
        entity.setNextRetryTime(item.getNextRetryTime());

        return entity;
    }

    public QueryBatchDetailResponse.BatchDetail toResp() {
        QueryBatchDetailResponse.BatchDetail batchDetail = new QueryBatchDetailResponse.BatchDetail();
        batchDetail.setBatchId(this.batchId);
        batchDetail.setId(this.id);
        batchDetail.setStatus(this.status);
        batchDetail.setParameter(this.parameter);
        batchDetail.setBizBatchDetailId(this.bizItemId);
        batchDetail.setCreateTime(DateUtil.formatDateTime(this.getCreateTime()));
        batchDetail.setUpdateTime(DateUtil.formatDateTime(this.getUpdateTime()));
        batchDetail.setRetryCount(this.retryCount);
        batchDetail.setErrorMsg(this.errorMessage);
        return batchDetail;
    }
}
