<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.corpgovernment</groupId>
        <artifactId>multi-supplier-schedule-batch</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>multi-supplier-schedule-batch-core</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <redis-handler.version>2.0.0</redis-handler.version>
        <api-version>1.1.47-SNAPSHOT</api-version>

    </properties>

    <dependencies>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-event</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>batch-center-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>redis-handler</artifactId>
            <version>${redis-handler.version}</version>
        </dependency>


        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>service-mysql</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>arch-event</artifactId>
                    <groupId>com.ctrip.corp.obt</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>