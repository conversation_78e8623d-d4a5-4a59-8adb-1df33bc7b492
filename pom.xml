<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ctrip.corp.obt</groupId>
        <artifactId>service-parent</artifactId>
        <version>1.0.4-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>com.corpgovernment</groupId>
    <artifactId>multi-supplier-schedule-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <start-class>com.corpgovernment.resource.schedule.Application</start-class>
        <revision>1.0.0-SNAPSHOT</revision>
        <ctrip.ibu.shark.version>5.1.2</ctrip.ibu.shark.version>
        <api-version>2.67.7</api-version>
        <common.version>2.2.8</common.version>
        <arch-shard-starter.version>********</arch-shard-starter.version>

    </properties>

    <modules>
        <module>multi-supplier-schedule-server</module>
        <module>multi-supplier-schedule-dto</module>
        <module>multi-supplier-schedule-sdk</module>
        <module>multi-supplier-schedule-batch</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!--IUB-Shark-->
<!--        <dependency>
            <groupId>com.ctrip.ibu.platform</groupId>
            <artifactId>ibu-shark-sdk</artifactId>
            <version>${ctrip.ibu.shark.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

        <!--joda-time-->
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.10.10</version>
        </dependency>

        <!-- excel poi -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <!--baidu mapper-->
        <dependency>
            <groupId>com.baidu.unbiz</groupId>
            <artifactId>easy-mapper</artifactId>
            <version>1.0.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>organization-manage-api</artifactId>
            <version>${api-version}</version>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.corpgovernment</groupId>
                <artifactId>multi-supplier-schedule-dto</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.corpgovernment</groupId>
                <artifactId>multi-supplier-schedule-sdk</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.corpgovernment</groupId>
                <artifactId>multi-supplier-core-dto</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.corpgovernment</groupId>
                <artifactId>common</artifactId>
                <version>${common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.corp.obt</groupId>
                <artifactId>arch-shard-starter</artifactId>
                <version>${arch-shard-starter.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
