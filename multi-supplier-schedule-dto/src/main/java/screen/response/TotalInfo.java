package screen.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class TotalInfo {

    /**
     * Total count
     */
    public BigDecimal total;

    /**
     * Flight count
     */
    private BigDecimal flightCount;


    /**
     * Hotel count
     */
    private BigDecimal hotelCount;

    /**
     * Train count
     */
    private BigDecimal trainCount;

    /**
     * Car count
     */
    private BigDecimal carCount;


    public TotalInfo(BigDecimal flightCount, BigDecimal hotelCount, BigDecimal trainCount, BigDecimal carCount) {
        this.flightCount = flightCount;
        this.hotelCount = hotelCount;
        this.trainCount = trainCount;
        this.carCount = carCount;

        this.total = flightCount.add(hotelCount).add(trainCount).add(carCount);
    }

    public TotalInfo(Integer flightCount, Integer hotelCount, Integer trainCount, Integer carCount) {
        this.flightCount = new BigDecimal(flightCount);
        this.hotelCount = new BigDecimal(hotelCount);
        this.trainCount = new BigDecimal(trainCount);
        this.carCount = new BigDecimal(carCount);

        this.total = this.flightCount.add(this.hotelCount).add(this.trainCount).add(this.carCount);
    }

    public TotalInfo(Double total, Double flightCount, Double hotelCount, Double trainCount, Double carCount) {
        this.total = new BigDecimal(total);
        this.flightCount = new BigDecimal(flightCount);
        this.hotelCount = new BigDecimal(hotelCount);
        this.trainCount = new BigDecimal(trainCount);
        this.carCount = new BigDecimal(carCount);
    }
}
