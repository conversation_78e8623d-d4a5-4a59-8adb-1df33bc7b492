package screen.response;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class MapResp {

    /**
     * 出发/到达城市
     */
    private String name;


    /**
     * <pre>
     * [
     * 	[{
     * 		"name": "北京"
     *        }, {
     * 		"name": "上海"
     *    }],
     * 	[{
     * 		"name": "上海"
     *    }, {
     * 		"name": "北京"
     *    }]
     * ]
     * </pre>
     */
    public static List<MapResp> getMapResp(String label) {
        if (label == null) {
            log.error("label is null");
            return Lists.newArrayList();
        }

        String[] split = label.split("-");
        if (split.length != 2) {
            log.error("label is error: {}", label);
            return Lists.newArrayList();
        }


        return Lists.newArrayList(new MapResp().setName(split[0]), new MapResp().setName(split[1]));
    }
}
