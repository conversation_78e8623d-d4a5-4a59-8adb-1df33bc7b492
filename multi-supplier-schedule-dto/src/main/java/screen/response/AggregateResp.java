package screen.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AggregateResp {

    /**
     * 大屏显示名称
     */
    public String name;

    /**
     * Total orders traffic info
     */
    public TotalInfo trafficTotalInfo;


    /**
     * 总消费金额信息
     */
    public TotalInfo trafficTotalAmount;

    /**
     * Total save traffic info
     */
    public TotalInfo trafficSaveAmount;

    /**
     * 消费部门（公司/部门） 排行
     */
    public List<ConsumeRankResp> consumeRanks;

    /**
     * 消费部门（成本中心） 排行
     */
    public List<ConsumeRankResp> consumeCenterRanks;


    /**
     * 法人主体消费排行
     */
    public List<ConsumeRankResp> consumeLegalRanks;


    /**
     * 差标合标率
     */
    public StandardRateResp standardRate;

    /**
     * 差标行程排行
     */
    public List<TripRankResp> tripRanks;

    /**
     * 热门出差城市
     */
    public List<HotCityResp> hotCities;

}
