package screen.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MapInfoDTO {

    /**
     * <pre>
     * {
     *     "成都":
     *     [
     *         104.0667,
     *         30.6667
     *     ],
     *     "绵阳":
     *     [
     *         104.2167,
     *         31.4667
     *     ]
     *  }
     *  </pre>
     */
    public Map<String, Collection<Double>> data;

}
