package screen.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AggregateReq {

    /**
     * 1-国内 2-国际
     */
    @NotNull(message = "areaType cannot be blank")
    private Integer areaType;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 开始时间
     */
    @NotNull(message = "startTime cannot be blank")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "endTime cannot be blank")
    private LocalDateTime endTime;
}
