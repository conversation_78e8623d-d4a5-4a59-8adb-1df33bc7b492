package com.corpgovernment.pricecomparison;

import lombok.Data;

@Data
public class FlightPriceCompareModel {
    // 航线类型:I-国际,N-国内
    private String flightType;
    // 出发城市三字码
    private String departCityCode;
    // 到达城市三字码
    private String arriveCityCode;
    // 舱等
    private String clazzCode;
    // 出发日期
    private String departDate;
    // 用户唯一标识
    private String uid;
    // 企业ID
    private String corpId;
}
