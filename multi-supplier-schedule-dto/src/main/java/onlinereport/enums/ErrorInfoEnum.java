package onlinereport.enums;


/**
 * @Auther: <PERSON>
 * @Date: 2021/8/28 16:07
 * @Description:
 */
public enum ErrorInfoEnum {

	// 14001000 开始
	PARAME_NULL(14001000, "parameter is null"),
	PASSAGENAME_NULL(14001001, "passenger name is null"),
	RECOMMEND_PARAME_NULL(14001002, "Recommend  parameter is null"),
	ISSELF_ERROR(14001003, "ISSELF value error"),
	BIZTYPE_ERROR(14001004, "biztype value error"),


	// 结束
	E(0, "end");

	private int value;

	private String description;

	ErrorInfoEnum(int value, String description){
		this.value = value;
		this.description = description;
	}

	public int getValue() {
		return value;
	}

	public String getDescription() {
		return description;
	}
}
