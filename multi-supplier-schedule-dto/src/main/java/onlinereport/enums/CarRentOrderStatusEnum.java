package onlinereport.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import onlinereport.enums.reportlib.FlightOrderStatusNewEnum;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 描述: 订单状态
 * </p>
 * <p>
 * 创建时间: 2024-10-31
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public enum CarRentOrderStatusEnum {
    /**
     * 提交中
     */
    Submitting(1, "提交中"),
    /**
     * 已提交
     */
    Submitted(2, "已提交"),
    /**
     * 确认中
     */
    Confirming(3, "确认中"),
    /**
     * 已确认
     */
    Confirmed(4, "已确认"),
    /**
     * 用车中
     */
    InService(5, "用车中"),
    /**
     * 已还车
     */
    ReturnCar(6, "已还车"),
    /**
     * 已完成
     */
    Dealed(7, "已完成"),
    /**
     * 取消中
     */
    Cancelling(8, "取消中"),
    /**
     * 已取消
     */
    Cancelled(9, "已取消"),
    Invalid(1000, "无效"),
    WaitReply(2000, "等待应答"),
    WaitService(3000, "等待接驾"),
    Redispatched(3500, "改派中"),
    DriverArrived(4000, "司机已到达"),
    EndService(6000, "行程结束"),
    Canceled(7000, "已取消"),
    Completed(8000, "已完成");
    ;

    private final Integer code;

    private final String message;

    CarRentOrderStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static boolean getCancelFlag(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        return Arrays.asList(Dealed.name(), ReturnCar.name(), InService.name(), Cancelling.name(), Cancelled.name())
            .contains(value);
    }

    /**
     * 获取Message
     */
    public static String getMessage(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return Arrays.stream(CarRentOrderStatusEnum.values()).filter(e -> value.equals(e.name())).findFirst()
            .map(CarRentOrderStatusEnum::getMessage)
            .orElse(null);

    }

    // 新增的方法，将枚举转换为 Map
    public static Map<String, String> toMap() {
        Map<String, String> map = new HashMap<>();
        for (CarRentOrderStatusEnum item : CarRentOrderStatusEnum.values()) {
            map.put(item.name(), item.getMessage());
        }
        return map;
    }
}