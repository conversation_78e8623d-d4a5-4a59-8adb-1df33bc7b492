package onlinereport.enums;

/*
 * <AUTHOR>
 *
 * @date 2022/1/11 10:26
 *
 * @Desc
 */
public enum SubQueryReportBuTypeEnum {
    // 所有
    ALL_Detail("overview"),
    // 机票
    FLIGHT_Detail("flight"),
    // 国内机票
    FLIGHT_N_Detail("flight"),
    // 国际机票
    FLIGHT_I_Detail("flight"),
    // 协议机票
    FLIGHT_AGREEMENT("flight"),
    // 非协议机票
    FLIGHT_N_AGREEMENT("flight"),
    // 酒店
    HOTEL_Detail("hotel"),
    // 国内酒店
    HOTEL_N_Detail("hotel"),
    // 海外酒店
    HOTEL_I_Detail("hotel"),
    // 协议酒店
    HOTEL_AGREEMENT("hotel"),
    // 非协议酒店
    HOTEL_N_AGREEMENT("hotel"),
    // 火车
    TRAIN_Detail("train"),
    // 用车
    CAR_Detail("car"),
    // 打车
    CAR_TAXI_Detail("car"),
    // 国内接送机
    CAR_AIRPORTPICK_N("car"),
    // 租车
    CAR_RENTAL_Detail("car"),
    // 包车
    CAR_CHARTERED("car"),
    // 国际接送机
    CAR_AIRPORTPICK_I("car"),
    // 汽车票
    BUS_TICKETS_Detail("bus"),
    // 增值
    VASO_ORDER_Detail("vaso"),
    // 国际打车
    CAR_TAXI_Detail_I("car"),
    // 国际租车
    CAR_RENTAL_Detail_I("car"),
    // 国内火车票
    TRAIN_N_Detail("train"),
    // 国际火车票
    TRAIN_I_Detail("train"),
    // 心程贝
    WELFARE("welfare"),
    ;

    private String bizType;

    SubQueryReportBuTypeEnum(String i) {
        this.bizType = i;
    }

    public String getBizType() {
        return bizType;
    }
    public static SubQueryReportBuTypeEnum getByOrdinal(String str) {
        SubQueryReportBuTypeEnum[] subQueryReportBuTypeEnums = SubQueryReportBuTypeEnum.values();
        int i = Integer.parseInt(str);
        for (SubQueryReportBuTypeEnum deptStatisticalsEnum : subQueryReportBuTypeEnums) {
            if (deptStatisticalsEnum.ordinal() == i) {
                return deptStatisticalsEnum;
            }
        }
        return null;
    }
}
