package onlinereport.enums.enums;

/**
 * 排序
 */
public enum SortedTypeEnum implements Enumerable<SortedTypeEnum> {

    ASC(0, "ASC"),
    DESC(1, "DESC");

    /**
     * 值
     */
    private int value;
    /**
     * 描述
     */
    private String description;

    SortedTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    @Override
    public SortedTypeEnum getEnum() {
        return this;
    }
}
