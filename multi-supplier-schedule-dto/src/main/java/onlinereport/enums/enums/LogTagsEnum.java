package onlinereport.enums.enums;

/**
 * clog tags
 */
public enum LogTagsEnum implements Enumerable<LogTagsEnum> {
    /**
     * orderid
     */
    ORDERID(1, "orderid"),

    /**
     * uid
     */
    UID(2, "uid"),

    /**
     * corpid
     */
    CORPID(3, "corpid"),

    /**
     * eid
     */
    EID(4, "eid"),

    /**
     * session id
     */
    SESSION(5, "sessionid"),

    /**
     * message id
     */
    MESSAGEID(6, "messageid"),

    /**
     * tripid
     */
    TRIP(7, "tripid"),

    /**
     * elapsed time
     */
    ELAPSEDTIME(8, "elapsedtime"),

    /**
     * method
     */
    METHOD(9, "method"),

    /**
     * rpc method
     */
    RPC_MEHOD(10, "rpc_method"),

    /**
     * start time
     */
    STARTTIME(11, "starttime"),

    /**
     * end time
     */
    ENDTIME(12, "endtime"),;

    /**
     * 值
     */
    private int value;
    /**
     * 描述
     */
    private String description;

    LogTagsEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public LogTagsEnum getEnum() {
        return this;
    }
}
