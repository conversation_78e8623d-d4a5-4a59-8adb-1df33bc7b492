package onlinereport.enums.dept;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2024-01-25 20:59
 * @desc 部门分析总概指标
 */
public enum OverViewDeptFieldEnum {

    OVERVIEW_AMOUNT("overviewAmount", "Supplier.totalcostamount", true, true, "Unit.Yuan", true, 1),

    OVERVIEW_OVER_AMOUNT("overviewOverAmount", "Save.CaliberNote20", false, false, "Unit.Yuan", true, 1),

    OVERVIEW_RC_PERCENT("overviewRcPercent", "Exceltopname.RCPercentage", true, false, "%", false, 3),
    ;

    private String name;

    private String headerKey;

    private boolean isDefault;

    private boolean isMust;

    private String unit;

    private boolean supportPie;

    private Integer dataType;

    OverViewDeptFieldEnum(String name, String headerKey, boolean isDefault, boolean must, String unit, boolean supportPie, Integer dataType) {
        this.name = name;
        this.headerKey = headerKey;
        this.isDefault = isDefault;
        this.isMust = must;
        this.unit = unit;
        this.supportPie = supportPie;
        this.dataType = dataType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHeaderKey() {
        return headerKey;
    }

    public void setHeaderKey(String headerKey) {
        this.headerKey = headerKey;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public boolean isMust() {
        return isMust;
    }

    public void setMust(boolean must) {
        isMust = must;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public boolean isSupportPie() {
        return supportPie;
    }

    public void setSupportPie(boolean supportPie) {
        this.supportPie = supportPie;
    }

    public static OverViewDeptFieldEnum getEnumByName(String name) {
        for (OverViewDeptFieldEnum value : OverViewDeptFieldEnum.values()) {
            if (StringUtils.equalsIgnoreCase(value.getName(), name)) {
                return value;
            }
        }
        return null;
    }

    public Integer getDataType() {
        return dataType;
    }
}
