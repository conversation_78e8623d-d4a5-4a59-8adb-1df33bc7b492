package onlinereport.enums.dept;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2024-01-25 20:59
 * @desc 部门分析酒店指标
 */

public enum HotelDeptFieldEnum {

    HOTEL_AMOUNT("hotelAmount", "Overview.HotelAmount", true, true, "Unit.Yuan", true, 1),

    HOTEL_QUANTITY("hotelQuantity", "Overview.HotelNum", false, false, "Unit.NightNum", true, 2),
    HOTEL_AVG_PRICE("hotelAvgPrice", "Travelanalysis.averagehotel", false, false, "Unit.Yuan", false, 1),
    HOTEL_OVER_AMOUNT("hotelOverAmount", "RCAnalysis.OutRCPrice", false, false, "Unit.Yuan", true, 1),

    HOTEL_RC_PERCENT("hotelRcPercent", "RCAnalysis.HotelRCPercentage", false, false, "%", false, 3),
    HOTEL_SAVE_3C_RATE("hotelSave3cRate", "savings.HtlAgreesavingsrate", false, false, "%", false, 3),
    //HOTEL_SAVE_2C_RATE("hotelSave2cRate", "Save.HtlTmcSavingsRate", false, false, "%", false, 3),
    //HOTEL_SAVE_CONTROL_RATE("hotelSaveControlRate", "savings.HtlControlsavingsrate", false, false, "%", false, 3),
    //HOTEL_SAVE_PROMOTION_RATE("hotelSavePromotionRate", "Save.HtlTripSavingsRate", false, false, "%", false, 3),
    ;

    private String name;

    private String headerKey;

    private boolean isDefault;

    private boolean isMust;

    private String unit;

    private boolean supportPie;

    // 数据类型,1金额，2整数，3百分数
    private Integer dataType;

    HotelDeptFieldEnum(String name, String headerKey, boolean isDefault, boolean must, String unit, boolean supportPie, Integer dataType) {
        this.name = name;
        this.headerKey = headerKey;
        this.isDefault = isDefault;
        this.isMust = must;
        this.unit = unit;
        this.supportPie = supportPie;
        this.dataType = dataType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHeaderKey() {
        return headerKey;
    }

    public void setHeaderKey(String headerKey) {
        this.headerKey = headerKey;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public boolean isMust() {
        return isMust;
    }

    public void setMust(boolean must) {
        isMust = must;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public boolean isSupportPie() {
        return supportPie;
    }

    public void setSupportPie(boolean supportPie) {
        this.supportPie = supportPie;
    }

    public static HotelDeptFieldEnum getEnumByName(String name) {
        for (HotelDeptFieldEnum hotelDeptFieldEnum : HotelDeptFieldEnum.values()) {
            if (StringUtils.equalsIgnoreCase(hotelDeptFieldEnum.getName(), name)) {
                return hotelDeptFieldEnum;
            }
        }
        return null;
    }

    public Integer getDataType() {
        return dataType;
    }
}
