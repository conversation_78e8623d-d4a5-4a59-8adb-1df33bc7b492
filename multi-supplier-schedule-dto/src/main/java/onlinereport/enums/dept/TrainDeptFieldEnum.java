package onlinereport.enums.dept;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2024-01-25 20:59
 * @desc 部门分析火车指标
 */

public enum TrainDeptFieldEnum {

    TRAIN_AMOUNT("trainAmount", "Overview.TrainAmount", true, true, "Unit.Yuan", true, 1),

    TRAIN_QUANTITY("trainQuantity", "Overview.TrainNum", false, false, "Unit.Num", true, 2),
    TRAIN_AVG_PRICE("trainAvgPrice", "deptAnalyse.train.avgPrice", false, false, "Unit.Yuan", false, 1),

    TRAIN_REFUND_RATE("trainRefundRate", "deptAnalyse.train.Refund", false, false, "%", false, 3),

    TRAIN_REBOOK_RATE("trainRebookRate", "deptAnalyse.train.Rebook", false, false, "%", false, 3),

    ;

    private String name;

    private String headerKey;

    private boolean isDefault;

    private boolean isMust;

    private String unit;

    private boolean supportPie;

    private Integer dataType;

    TrainDeptFieldEnum(String name, String headerKey, boolean isDefault, boolean must, String unit, boolean supportPie, Integer dataType) {
        this.name = name;
        this.headerKey = headerKey;
        this.isDefault = isDefault;
        this.isMust = must;
        this.unit = unit;
        this.supportPie = supportPie;
        this.dataType = dataType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHeaderKey() {
        return headerKey;
    }

    public void setHeaderKey(String headerKey) {
        this.headerKey = headerKey;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public boolean isMust() {
        return isMust;
    }

    public void setMust(boolean must) {
        isMust = must;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public boolean isSupportPie() {
        return supportPie;
    }

    public void setSupportPie(boolean supportPie) {
        this.supportPie = supportPie;
    }

    public static TrainDeptFieldEnum getEnumByName(String name) {
        for (TrainDeptFieldEnum deptFieldEnum : TrainDeptFieldEnum.values()) {
            if (StringUtils.equalsIgnoreCase(deptFieldEnum.getName(), name)) {
                return deptFieldEnum;
            }
        }
        return null;
    }

    public Integer getDataType() {
        return dataType;
    }
}
