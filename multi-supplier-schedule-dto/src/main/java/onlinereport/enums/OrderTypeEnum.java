package onlinereport.enums;

import org.apache.commons.lang3.EnumUtils;

public enum OrderTypeEnum implements Enumerable<OrderTypeEnum> {
    //无类型
    NONE(-999,"none", "",""),
    //国内机票
    N(-6,"N", "国内机票", "Domestic Flight"),

    //国际机票
    I(-5,"I", "国际机票", "International Flight"),

    //国内，国际机票
    F(-4,"N/I", "机票", "Flight"),

    // 会员酒店
    M(-3,"M"),

    //协议酒店
    C(2,"C"),

    //协议，会员酒店
    H(-1,"M/C", "酒店", "Hotel"),

    //火车票
    A(0,"T", "火车票", "Train"),

    //domestic 国内接送机
    D(1,"DomTransfer","国内接送机/代驾"),

    //国际接送机
    S(2,"IntlTransfer","国际接送机/代驾"),
    //国内包车
    B(3,"<PERSON><PERSON><PERSON><PERSON>","国内包车"),
    //rent 租车
    R(4,"DomRental","租车/自驾"),
    //pick up 新国内接送机
    P(5,"NewDomTransfer","新国内接送机"),
    // 打车
    U(6,"OnCall","打车"),

    //用车
    V(99,"C", "用车", "Car"),
    //国内+国际接送机
    DS(100,"Transfer");

    private Integer orderType;

    /**
     * 名称
     */
    private String name;

    private String desCn;
    private String desEn;

    private OrderTypeEnum(int orderType, String name) {
        this.orderType = orderType;
        this.name = name;
    }

    private OrderTypeEnum(int orderType, String name, String desCN) {
        this.orderType = orderType;
        this.name = name;
        this.desCn = desCN;
    }

    private OrderTypeEnum(int orderType, String name, String desCN, String desEn) {
        this.orderType = orderType;
        this.name = name;
        this.desCn = desCN;
        this.desEn = desEn;
    }

    @Override
    public int getValue() {
        return this.orderType;
    }

    @Override
    public String getDescription() {
        return this.name;
    }

    @Override
    public OrderTypeEnum getEnum() {
        return this;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public String getName() {
        return name;
    }

    public String getDesCn() {
        return desCn;
    }

    public String getDesEn() {
        return desEn;
    }

    public static OrderTypeEnum getEnumByName(String name){
        return EnumUtils.isValidEnum(OrderTypeEnum.class, name) ? EnumUtils.getEnum(OrderTypeEnum.class, name) : OrderTypeEnum.NONE;
    }
}
