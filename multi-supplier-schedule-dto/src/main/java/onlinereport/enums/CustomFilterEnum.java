package onlinereport.enums;

import org.apache.commons.lang.StringUtils;

/**
 * Auther:abguo
 * Date:2019/8/13
 * Description:
 * Project:onlinereportweb
 */
public enum CustomFilterEnum {

    //通用
    LANG("lang", 0),
    CORP("corp", 1),
    ACCOUNTID("accountid", 2),
    UID("uid", 3),
    PREPAYTYPE("prepaytype", 4),
    BOOKTYPE("booktype", 5),
    ISOVERTRAVELCONTROL("isovertravelcontrol",6 ),
    STARTTIME("starttime", 7),
    ENDTIME("endtime", 8),
    DEPT1("dept1", 9),
    DEPT2("dept2", 10),
    DEPT3("dept3", 11),
    DEPT4("dept4", 12),
    DEPT5("dept5", 13),
    DEPT6("dept6", 14),
    COSTCENTER1("costcenter1",15),
    COSTCENTER2("costcenter2",16),
    COSTCENTER3("costcenter3",17),
    COSTCENTER4("costcenter4",18),
    COSTCENTER5("costcenter5",19),
    COSTCENTER6("costcenter6",20),
    ORDERTYPE("ordertype", 21),
    REPORTDEPTTYPE("reportdepttype",22),
    LOGONUID("logonuid", 23),
    ISDOWNLOAD("isdownload", 24),
    MONTH("month", 25),
    QUARTER("quarter", 26),
    REPORTDEPTTYPE_FILTER_VALUE("reportdepttype_filter_value", 27),
    ACITY("acity", 28),
    RANK("rank", 29),
    STATUS("rank", 30),
    ACCOUNTCODE("accountCode", 31),
    ACCOUNTNAME("accountName", 32),
    BIZTYPE("bizType", 33),
    PRODUCTLINE("productLine",33),
    PROJECT("project", 34),
    JOURNEYREASON("journeyreason", 35),




    WORKCITY("workcity", 209),
    CUSTOMREPORTINFOID("customreportinfoid", 210),
    NEEDDATASOURCE("needdatasource", 211),

    //机票相关 100~199
    FLT_FLIGHTSTATUS("flt_flightstatus", 100),
    FLT_FLIGHTCLASS("flt_flightclass", 101),
    FLT_REALCLASS("flt_realclass", 102),
    FLT_PASSENGERNAME("flt_passengername",103),
    FLT_AIRLINE("flt_airline", 104),
    FLT_AIRROUTE("flt_airroute", 105),
    FLT_DESTINATION("flt_destination",106),
    FLT_AGREEMENTAIR("flt_agreementair",107),
    FLT_LOWPRICERC("flt_lowpricerc",108),
    FLT_CLASSICRC("flt_classicrc",109),
    FLT_PRERC("flt_prerc", 110),
    FLT_INTERCONTINENTALTPYE("flt_intercontinentaltpye", 111),
    FLT_RCTYPE("flt_rctype", 158),



    //酒店相关 200~299
    HTL_ORDERTYPE("htl_ordertype", 200),
    HTL_TYPE("htl_type", 201),
    HTL_ISMIXPAYMENT("htl_ismixpayment",202),
    HTL_CITY("htl_city", 203),
    HTL_PROVINCE("htl_province", 204),
    HTL_COUNTRY("htl_country", 205),
    HTL_STAR("htl_star", 206),
    HTL_ISOVERSEA("htl_isoversea", 209),
    HTL_ORDERTYPE_ID("htl_ordertype_id", 215),
    HTL_CITY_LEVEL("htl_citylevel", 222),
    HTL_RCTYPE("htl_rctype", 223),



    //火车票相关 300~399
    TRAIN_DEPARTURE_CITY("train_departure_city",300),
    TRAIN_SEAT_TYPE("train_seat_type",301),
    TRAIN_RCTYPE("train_rctype",302);


    //用车相关 400~499


    private String name;
    private int val;

    private CustomFilterEnum(String name, int val) {
        this.name = name;
        this.val = val;
    }

    /**
     * 判断值是否枚举值
     *
     * @param val
     * @return
     */
    public static boolean isFilterEnum(String val) {
        if (StringUtils.isEmpty(val)) {
            return false;
        }
        boolean isFilterEnum = true;
        try {
            FilterNameEnum.valueOf(val.trim().toUpperCase());
        } catch (Exception ex) {
            isFilterEnum = false;
        }
        return isFilterEnum;
    }

    public String getName() {
        return name;
    }

    public int getValue() {
        return val;
    }
}
