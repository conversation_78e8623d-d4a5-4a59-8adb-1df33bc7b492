package onlinereport.enums;

/**
 * Auther:abguo
 * Date:2019/9/2
 * Description:
 * Project:onlinereportweb
 */
public enum TrainRCTypeEnum {

    S(1,"坐席RC"),
    T(2,"票张RC");

    private int type;
    private String typeDesc;

    TrainRCTypeEnum(int type, String typeDesc) {
        this.type = type;
        this.typeDesc = typeDesc;
    }

    public int getType() {
        return type;
    }

    public String getTypeDesc() {
        return typeDesc;
    }
}
