package onlinereport.enums;

/**
 * @Description: 机票RC类型,rctype对应下列枚举的位运算,例举rctype=3，代表此RC类型是低价RC和提前RC
 * <AUTHOR>
 * @Date 2019/6/18
 */
public enum FlightRCTypeEnum {

    /**
     * 低价RC
     */
    LOWRC("LowRC", 1),

    /**
     * 提前RC
     */
    ADVRC("AdvRC", 2),

    /**
     * 舱等RC
     */
    CLASSRC("ClassRC", 4),

    /**
     * 协议RC
     */
    PROTOCOLRC("ProtocolRc", 8),

    /**
     * 公里数RC
     */
    DISTANCERC("DistanceRC", 16),

    /**
     * 改签RC
     */
    ALTERRC("AlterRc", 32),

    /**
     * 时间RC
     */
    TIMERC("TimeRC", 64);

    private String rcName;
    private int rcType;

    private FlightRCTypeEnum(String rcName, int rcType) {
        this.rcName = rcName;
        this.rcType = rcType;
    }

    public int getRcType() {
        return rcType;
    }

    /**
     * 检查targetRCType 是否属于rc对应类型
     * @param rc
     * @param targetRCType
     * @return
     */
    public static boolean checkRCType(FlightRCTypeEnum rc, int targetRCType) {
        int i = targetRCType | rc.getRcType();
        return i == targetRCType;
    }


}
