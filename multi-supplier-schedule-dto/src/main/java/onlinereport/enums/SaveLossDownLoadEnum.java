package onlinereport.enums;

public enum SaveLossDownLoadEnum {
    /**
     * 贵司票张数
     */
    COMPANY_TICKET("Save.ExcelCpyTkt"),
    /**
     * 间夜均价
     */
    COMPANY_NIGHT("Save.ExcelCpyRoomNight"),
    /**
     * 占比
     */
    PROPORTION("Exceltopname.numberPercentage"),
    /**
     * 商旅占比
     */
    CORP_PROPORTION("Save.ExcelCtripAvgTimes"),
    /**
     * 行业占比
     */
    INDUSTRY_PROPORTION("Save.ExcelIdyAvgTimes"),
    ;

    String sharkKey;


    private SaveLossDownLoadEnum(String sharkKey) {
        this.sharkKey = sharkKey;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public void setSharkKey(String sharkKey) {
        this.sharkKey = sharkKey;
    }

    public static SaveLossDownLoadEnum findByName(String name) {
        for (SaveLossDownLoadEnum e : SaveLossDownLoadEnum.values()) {
            if (e.toString().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
