package onlinereport.enums;

/**
 * 语言版本
 */
public enum LanguageEnum implements Enumerable<LanguageEnum> {
    /**
     * ZH_CN(1, "中文","ZH-CN")
     */
    ZH_CN(1, "ZH_CN", "ZH_CN"),

    /**
     * ZH_HK(1, "繁体","ZH_HK")
     */
    ZH_HK(1, "ZH_HK", "ZH_HK"),

    /**
     * EN_US(3, "英文", "EN-US")
     */
    EN_US(3, "EN_US", "EN_US");

    /**
     * 值
     */
    private int value;
    /**
     * 描述
     */
    private String description;
    /**
     * 编码
     */
    private String code;

    LanguageEnum(int value, String description, String code) {
        this.value = value;
        this.description = description;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public LanguageEnum getEnum() {
        return this;
    }
}
