package onlinereport.enums;

/**
 * 统计维度
 */
public enum DimensionEnum {
        //通用
        EMPTY("empty", -1, "空"),//不按照字段分组
        CORP("corp", 0, "公司名称"),
        ACCOUNT("account", 1, "主账户名称"),
        BOOKTYPE("booktype", 2, "预订渠道"),
        MONTH("month", 3, "月"),
        QUARTER("quarter", 4, "季度"),
        YEAR("year", 5, "年"),
        DEPT1("dept1", 6, "部门1"),
        DEPT2("dept2", 7, "部门2"),
        DEPT3("dept3", 8, "部门3"),
        DEPT4("dept4", 9, "部门4"),
        DEPT5("dept5", 10, "部门5"),
        DEPT6("dept6", 11, "部门6"),
        COSTCENTER1("costcenter1", 12, "成本中心1"),
        COSTCENTER2("costcenter2", 13, "成本中心2"),
        COSTCENTER3("costcenter3", 14, "成本中心3"),
        COSTCENTER4("costcenter4", 16, "成本中心4"),
        COSTCENTER5("costcenter5", 17, "成本中心5"),
        COSTCENTER6("costcenter6", 18, "成本中心6"),
        DCITY("dcity", 19, "出发城市"),
        ACITY("acity", 20, "到达城市"),
        UID("uid", 21, "卡号"),
        ORDERID("orderid", 22, "订单ID"),
        CORPID("corpid", 23, "公司ID"),
        ACCOUNTID("accountid", 24, "主账户ID"),
        WORKCITY("workcity", 25, "工作所在城市"),
        WORKCITYID("workcityid", 26, "工作所在城市id"),
        BOOKTYPE_SINGLE("booktype_single", 27, "预订渠道-不转"),
        CITY_LAT("city_lat", 28, "维度"),
        CITY_LON("city_lon", 29, "经度"),
        CONTINENTID("continentid",30,"航线抵达城市所在洲id"),
        CONTINENT_NAME("continent_name",31,"航线抵达城市所在洲名称"),
        ORDERTYPE("ordertype",32,"订单类型(OrderTypeEnum国内机票、国际机票、协议酒店、会员酒店、火车票、用车)"),
        ORDERTIME("ordertime",33,"订单归属时间"),
        DCITY_NAME("dcity_name",34,"出发城市名称"),
        ACITY_NAME("acity_name",35,"到达城市名称"),
        CLENTINAME("clientname",36,"机票乘客、酒店入住人"),
        CONTACTNAME("contactname",37,"联系人"),
        CONTACTPHONE("contactphone",38,"联系人手机"),
        CONTACTMAIL("contactmail",39,"联系人email"),
        ARRIVAL("arrival",40,"出发时间/入住时间"),
        DEPARTURE("departure",41,"到达时间/离店时间"),
        HALFYEAR("halfyear", 42, "半年"),
        RCTYPE("rctype",43,"rc类型"),
        RCCODE("rccode",44,"rc code"),
        RCCODE_NAME("rcName",44,"rc code name"),
        RCCODE_NAME_EN("rcNameEn",45,"rc code name en"),
//        PREORDERDAY("preorderday",46,"提前预订天数"),
        STATUS("status",47,"成交\\退订"),
        USERNAME("username",48,"持卡人姓名"),
        PASSENGERNAME("passengerName",49,"实际使用人姓名"),
        PASSENGERID("passengerId",49,"实际使用人ID"),
        AUTHNAME("authName",50,"授权人姓名"),
        PROJECT("project", 51, "项目"),
        JOURNEYREASON("journeyreason", 52, "出行目的"),
        RANK("rank", 53, "职级"),
        ACCOUNTCODE("accountCode", 54, "主账户代号"),
        ACCOUNTNAME("accountName", 55, "主账户名称"),
        TIME("time", 56, "时间"),
        PRODUCTLINE("productLine", 57, "产线"),
        RCDESC("rcdesc",58,"RC描述"),
        EMPLOYEEID("employeeId", 53, "员工编号"),
        AUTHID("authId",54,"授权人ID"),
        SUBACCOUNTCODE("subAccountCode", 55, "子账户代号"),
        SUBACCOUNTNAME("subAccountName", 56, "子账户名称"),








        //机票 100~199
        FLT_RANK("flt_rank", 100, "职级"),
        FLT_PASSENGERNAME("flt_passengername", 101, "乘机人"),
        FLT_AIRLINE("flt_airline", 102, "航空公司"),
        FLT_REALCLASS("flt_realclass", 103, "物理舱位"),
        FLT_FLIGHTCLASS("flt_flightclass", 104, "航班类型"),
        FLT_FLIGHTSTATUS("flt_flightstatus", 105, "航段状态"),
        FLT_PREORDERDAY("flt_preorderday", 106, "提前预订天数（区间）"),
        FLT_AIRROUTE("flt_airroute", 107, "航线-当前航程"),
        FLT_DESTINATION("flt_destination", 108, "目的地"),
        FLT_AGREEMENTAIR("flt_agreementair", 109, "是否协议航空"),
        FLT_PRERC("flt_prerc", 110, "提前预订RC"),
        FLT_LOWPRICERC("flt_lowpricerc", 111, "低价RC"),
        FLT_CLASSICRC("flt_classicrc", 112, "舱等RC"),
        FLT_AGETYPE("flt_agetype", 113, "类型（成人、儿童）"),
        FLT_AGREEMENTRC("flt_agreementrc", 114, "协议RC"),
        FLT_PRERC_NAME("flt_prerc_name", 115, "提前预订RC描述"),
        FLT_LOWPRICERC_NAME("flt_lowpricerc_name", 116, "低价RC描述"),
        FLT_CLASSICRC_NAME("flt_classicrc_name", 117, "舱等RC描述"),
        //国内、洲内、洲际
        FLT_INTERCONTINENTALTPYE("flt_intercontinentaltpye", 118, "国际航班类型"),
        FLT_AIRLINE_CODE("flt_airline_code", 119, "航空公司二字码"),
        FLT_DESTINATION_ID("flt_destination_id", 120, "目的地城市id"),
        FLT_AIRROUTE2("flt_airroute2", 121, "航线-所有程"),
        FLT_PASSENGERNAME_UID("flt_passengername_uid", 122, "乘机人UID"),
        FLT_AGREEMENTRC_NAMEEN("flt_agreementrc_nameen", 123, "协议RC描述英文"),
        FLT_PRERC_NAMEEN("flt_prerc_nameen", 124, "提前预订RC描述英文"),
        FLT_LOWPRICERC_NAMEEN("flt_lowpricerc_nameen", 125, "低价RC描述英文"),
        FLT_CLASSICRC_NAMEEN("flt_classicrc_nameen", 126, "舱等RC描述英文"),
        FLT_CLASS_ID("flt_class_id", 127, "舱等"),
        FLT_DISCOUNT_RATE_RANGE("flt_discount_rate_range", 128, "折扣区间"),
        FLT_FLIGHTCLASS_ID("flt_flightclass_id", 129, "航班类型-不转换中文"),
        FLT_CARRIERS("flt_carriers", 130, "承运商"),
		FLT_TPM_TYPE("flt_tpm_type", 131, "旅行里程类型"),
        FLT_TAKEOFF_TIME_RANGE("flt_takeoff_time_range",132,"起飞时间段"),
        FLT_DATETIME("flt_datetime", 133, "机票出票日期"),
        FLT_SEQUENCE("flt_sequence", 134, "航段"),
        FLT_IS_BOSS("flt_isboss", 135, "Boss标签(T/F)"),
        FLT_ISREFUND("flt_isrefund", 136, "是否退票"),
        FLT_NICKNAME("flt_nickname", 137, "乘机人昵称"),
        FLT_PRERC_ID("flt_prerc_id", 138, "提前预订RCID"),
        FLT_LOWPRICERC_ID("flt_lowpricerc_id", 139, "低价RCID"),
        FLT_CLASSICRC_ID("flt_classicrc_id", 140, "舱等RCID"),
        FLT_AGREEMENTRC_ID("flt_agreementrc_id", 141, "协议RCID"),
        FLT_TICKET_NO("flt_ticket_no",142,"机票票号"),
        FLT_TICKET_WARNING_LEVEL("flt_ticket_warninglevel",143,"票号风险级别"),
		FLT_AIRROUTE_ALL("flt_airroute_all", 144, "航线(包括国内外)"),
		FLT_IS_BOSS_SIMPLE("flt_isboss_simple", 145, "Boss标签(原始)"),
		FLT_AGREEMENTRC_NAME("flt_agreementrc_name", 146, "协议RC描述"),
        FLT_AIRROUTE_ALL_EN("flt_airroute_all_en", 147, "航线(包括国内外)"),
        FLT_PREORDERDATE("flt_preorderdate", 148, "提前预订天数"),
		FLT_AIRROUTE3("flt_airroute3", 149, "航线-所有程"),
        FLT_RCTIME("flt_rctime", 150, "机票RCTime"),
        FLT_PREORDERDATE_RANGE_START("flt_preorderdate_range_start", 151, "提前预订天数区间开始天数"),
        FLT_FLIGHTNO("flt_flightno", 152, "航班号"),
        FLT_REALCLASS_EN("flt_realclass_en", 153, "物理舱位EN"),
        FLT_CUSTOMERID("flt_customerid", 154, "协议类型"),
        FLT_LINEDETAIL("flt_linedetail", 155, "详细航程"),
        FLT_DIMCITITY("flt_dimcitity", 156, "城市对"),
        FLT_SUBCLASS("flt_subclass", 157, "子仓位"),
        FLT_RCTYPE("flt_rctype", 158, "RC类型"),
        FLT_IS_REBOOK("flt_isrebook", 159, "是否改签"),
        FLT_BFRETURN("flt_bfreturn", 160, "是否前返"),
        FLT_PRODUCTTYPE("flt_productType", 161, "产品类型"),
        FLT_PROVIDEBILLTYPE("flt_provideBillType", 162, "开票方式"),
        FLT_TICKETWAY("flt_ticketWay", 163, "配送方式"),




        //酒店 200-299
        HTL_ORDERTYPE("htl_ordertype", 200, "订单类型"),
        HTL_HTLTYPE("htl_htltype", 201, "酒店类型"),
        HTL_CITY("htl_city", 202, "酒店城市名称"),
        HTL_PROVINCE("htl_province", 203, "酒店省份名称"),
        HTL_COUNTRY("htl_country", 204, "酒店国家名称"),
        HTL_CITY_ID("htl_city_id", 205, "酒店城市ID"),
        HTL_PROVINCE_ID("htl_province_id", 206, "酒店省份ID"),
        HTL_COUNTRY_ID("htl_country_id", 207, "酒店国家ID"),
        //小于2星级合并到2星级
        HTL_STAR("htl_star", 208, "酒店星级"),
        HTL_ISOVERSEA("htl_isoversea", 209, "是否海外"),
        HTL_LOWPRICERCCODE("htl_lowpricerccode", 210, "低价RC CODE"),
        HTL_LOWPRICERCCODE_NAME("htl_lowpricerccode_name", 211, "低价RC描述"),
        HTL_LOWPRICERCCODE_NAMEEN("htl_lowpricerccode_nameen", 212, "低价RC描述英文"),
        HTL_AGREEMENTRCCODE("htl_agreementrccode", 212, "协议RC CODE"),
        HTL_AGREEMENTRCCODE_NAME("htl_agreementrccode_name", 213, "协议RC描述"),
        HTL_AGREEMENTRCCODE_NAMEEN("htl_agreementrccode_nameen", 214, "协议RC描述英文"),
        HTL_ORDERTYPE_ID("htl_ordertype_id", 215, "酒店类型"),
        HTL_HOTEL_ID("htl_hotel_id", 215, "酒店ID"),
        HTL_ISOVERSEA_SINGLE("htl_isoversea_single", 216, "酒店类型"),
        HTL_DATETIME("htl_datetime", 217, "酒店DateTime"),
        HTL_RCTIME("htl_rctime", 218, "酒店RCTime"),
        HTL_ISREFUND("htl_isrefund", 219, "是否退订"),
        HTL_HOTEL_MASTERID("htl_hotel_masterid", 220, "母酒店ID"),
        HTL_STAR_SINGLE("htl_star_single", 221, "酒店星级"),
        HTL_CITY_LEVEL("htl_citylevel", 222, "酒店城市等级"),
        HTL_HOTELNAME("htl_hotelname", 222, "酒店名称"),
        HTL_RCTYPE("htl_rctype", 223, "RC类型"),
        HTL_CITY_EN("htl_cityEn", 202, "酒店城市名称英文)"),
        HTL_HOTELNAME_EN("htl_hotelnameEn", 223, "酒店名称英文"),
        HTL_ORDERSTATUS("htl_orderStatus", 224, "订单状态"),





        //用车 300-399
        CAR_ORDERTYPE("car_ordertype", 300, "用车产品类型"),
//        CAR_ADDRESS_CITY_ID("car_address_city_id", 301, "接送机/马上叫车用车城市ID"),
        CAR_ORDERSTATUS("car_orderstatus", 302, "订单状态"),
        CAR_PREPAYTYPE("car_prepaytype", 303, "支付方式"),
        CAR_VEHICLETYPE("car_vehicletype",304,"车型"),
        CAR_DATETIME("car_datetime", 305, "用车DateTime"),
        CAR_ORDERTYPE_ESCAPE("car_ordertype_escape", 306, "用车产品类型(转义)"),
        CAR_DEPARTURE_CITYNAME("car_departure_cityname",307,"用车出发城市名称(中文)"),
        CAR_ARRIVAL_CITYNAME("car_arrival_cityname",308,"用车到达城市名称(中文)"),
        CAR_RCTYPE("car_rctype", 309, "RC类型"),



        //火车票 400-499
        TRAIN_NAME("train_name",400,"车次"),
        TRAIN_SEAT_TYPE("train_seat_type",401,"席位"),
        TRAIN_CITIES("train_cities",402,"行程(出发城市-到达城市)"),
        TRAIN_FIRST_SEAT_NAME("train_first_seat_name",403,"首选坐席"),
        TRAIN_ISREFUND("train_isrefund",404,"是否退票"),
        TRAIN_ISCHANGE("train_ischange",405,"是否改签"),
        TRAIN_REFUND_TICKET_STATUS("train_refund_ticket_status",406,"退票状态"),
        TRAIN_TICKETTYPE("train_tickettype",407,"票张类型"),
        TRAIN_STATIONS("train_stations",408,"路线(出发站-到达站)"),
        TRAIN_RCCODE("train_rccode",409,"rc code"),
        TRAIN_RCTIME("train_rctime",410,"rcTime"),
        TRAIN_TYPE("train_type",411,"车型"),
        TRAIN_DATETIME("train_datetime",412,"出票时间"),
        TRAIN_RCTYPE("train_rctype", 413, "RC类型"),
        TRAIN_RC_NAME("train_rc_name", 414, "RC描述"),
        TRAIN_RC_NAME_EN("train_rc_name_en", 415, "RC描述英文"),
        TRAIN_SEAT_TYPE_EN("train_seat_type_en",416,"席位英文"),
        TRAIN_CITIES_EN("train_cities_en",417,"行程(出发城市-到达城市)英文"),
        TRAIN_STATIONS_EN("train_stations_en",418,"路线(出发站-到达站)英文"),
        TRAIN_PRINTTICKET_TYPE("train_printTicket_type",419,"paper/Electronic"),


        //总概
        GENERAL_ORDER_TYPE("orderType", 501, "产线类型"),
        GENERAL_INDUSTRY("industryType", 505, "industry"),
        ;


        private String name;
        private int val;
        private String des;

        private DimensionEnum(String name, int val, String des) {
                this.name = name;
                this.val = val;
                this.des = des;
        }


        public String getName() {
                return name;
        }

        public int getValue() {
                return val;
        }

        public String getDes() {
                return des;
        }

        public static DimensionEnum lookUp(String name) {
                for (DimensionEnum e : DimensionEnum.values()) {
                        if (e.getName().equalsIgnoreCase(name)) {
                                return e;
                        }
                }
                return null;
        }

}
