package onlinereport.enums.reportlib;

/**
 * Auther:abguo
 * Date:2019/8/22
 * Description:
 * Modify:jhe3 20200708
 * Project:onlinereportweb
 */
public enum TrainOrderDetailEnum implements OrderDetailEnumerable<TrainOrderDetailEnum> {
    // 基础信息
    ORDERID("order_id", "TravelPosition.order", 0),//订单号
    ORDERSTATUS("order_status", "Exceltopname.orderstatus", 1),//订单状态
    ORDERDATE("order_date", "Exceltopname.date", 2),//预订日期
    ISONLINE("is_online", "Travelanalysis.scheduledstyle", 20),//预订方式
    PASSENGERNAME("passenger_name", "Exceltopname.passengername", 102),//乘客姓名
    PASSENGER_EID("passengerEid", "index.topCarbonByEmployee.filter.employee", 102), // 出行人员工号
    QUANTITY("quantity", "Index.num", 103),//火车票张数
    CHANGEQUANTITY("change_quantity", "Index.changenum", 104),//火车票改签张数
    CHANGESTATUS("change_status", "Exceltopname.changestatus", 105),//改签状态
    REFUNDSTATUS("refund_status", "Exceltopname.refundstatus", 106),//退票状态
    TICKETTYPE("ticket_type", "Exceltopname.tickettype", 107),//车票类型
    TRAINNAME("train_name", "Exceltopname.trainno", 108),//车次
    FIRSTSEATTYPENAME("first_seat_type_name", "Exceltopname.trainseat", 109),//席别
    LINECITY("line_city", "Exceltopname.deparrcity", 124),//出发-到达城市
    DEPARTUREDATETIME("departure_date_time", "Exceltopname.depaturedate", 125),//出发日期
    DEPARTURESTATIONNAME("departure_station_name", "Exceltopname.depaturepoint", 126),//出发站
    DEPARTURECITYNAME("departure_city_name", "Index.depcity", 127),//出发城市
    DEPARTUREPROVINCENAME("departure_province_name", "Exceltopname.depatureprovince", 128),//出发省份
    ARRIVALDATETIME("arrival_date_time", "Exceltopname.arrvialdate", 129),//到达日期
    ARRIVALSTATIONNAME("arrival_station_name", "Exceltopname.arrvialpoint", 130),//到达站(中)
    ARRIVALCITYNAME("arrival_city_name", "Index.arrcity", 131),//到达城市
    ARRIVALPROVINCENAME("arrival_province_name", "Exceltopname.arrvialprovince", 132),//到达省份

    //    详细信息
    PREPAYTYPE("acb_prepay", "Exceltopname.paytype", 22),//支付方式
    TRIPID("trip_id", "Exceltopname.belongtravel", 25),//关联行程单号
    JOURNEYNO("journey_no", "Exceltopname.linltravel", 26),//所属申请单号
    GROUPMONTH("group_month", "Exceltopname.outticketyear", 32), //出票年月
    PRINTTIME("printTime", "Exceltopname.outticketdate", 101),//出票日期
    ISRC("is_rc", "Exceltopname.isrc", 133),//是否有RC
    SEATTYPERCCODENAME("seattype_rccodename", "Exceltopname.seatrcexp", 134),//坐席RC

    // 公司信息
//    CORP_CORPORATION("corp_corporation", "Index.companyid", 3),//公司ID
    CORPNAME("corp_name", "Public.confirmname", 4),//公司名称
//    STDINDUSTRY1("std_industry1", "index.industrybig", 14),//行业大类
//    STDINDUSTRY2("std_industry2", "index.industrysmall", 14),//行业小类


    // 人员信息
//    UID("uid", "Exceltopname.uidnumber", 15),//卡号
    EMPLOYEID("employe_id", "Exceltopname.cardholdernumber", 16),//员工编号
    USERNAME("user_name", "Exceltopname.train.cardholder", 17),//持卡人
//    RANKNAME("rank_name", "Exceltopname.corplevel", 19),//职级
    DEPT1("dept1", "Exceltopname.train.depone", 905),//部门1
//    DEPT2("dept2", "Exceltopname.deptwo", 915),//部门2
//    DEPT3("dept3", "Exceltopname.depthree", 925),//部门3
//    DEPT4("dept4", "Exceltopname.depfour", 935),//部门4
//    DEPT5("dept5", "Exceltopname.depfive", 945),//部门5
//    DEPT6("dept6", "Exceltopname.depsix", 955),//部门6
//    DEPT7("dept7", "Exceltopname.depseven", 965),//部门7
//    DEPT8("dept8", "Exceltopname.depeight", 975),//部门8
//    DEPT9("dept9", "Exceltopname.depnight", 985),//部门9
//    DEPT10("dept10", "Exceltopname.depten", 995),//部门10
    COSTCENTER1("cost_center1", "Exceltopname.train.costcenterone", 1005),//成本中心1
//    COSTCENTER2("cost_center2", "Exceltopname.costcentertwo", 1015),//成本中心2
//    COSTCENTER3("cost_center3", "Exceltopname.costcenterthree", 1025),//成本中心3
//    COSTCENTER4("cost_center4", "Exceltopname.costcenterfour", 1035),//成本中心4
//    COSTCENTER5("cost_center5", "Exceltopname.costcenterfive", 1045),//成本中心5
//    COSTCENTER6("cost_center6", "Exceltopname.costcentersix", 1055),//成本中心6


    //订单钱款信息
    REALPAY("real_pay", "Exceltopname.realpay", 111),//实收实付
    TICKETPRICE("ticket_price", "Exceltopname.priceticket", 112),//原始出票金额
    EST_FEE_12306("est_fee_12306", "index.12306fee", 113),//预估手续费12306
    REFUNDTICKETFEE("refund_ticket_fee", "Exceltopname.refundticketprice", 115),//退票金额
//    DELIVERFEE("deliver_fee", "Exceltopname.letterfee", 116),//配送费
    DEALCHANGESERVICEFEE("deal_change_service_fee", "Exceltopname.train.changeserfee", 118),//改签服务费
    AFTERSERVICEFEE("after_service_fee", "Exceltopname.laterserfee", 120),//后收服务费
    AFTERCHANGESERVICEFEE("afterchangeservicefee", "Exceltopname.laterchangefee", 121),//后收改签服务费
    CHANGEBALANCE("changebalance", "Exceltopname.changeprice", 136),//改签差价
//    FEETYPE("fee_type", "Exceltopname.personalycost", 21),//是否个人消费
    SERVICEFEE("service_fee", "Exceltopname.train.baseservicefee", 113),//前收服务费

    /*COMPANYGROUP("companygroup", "Index.belonggroup", 5),//公司集团
    COMPANYGROUPID("companygroupid", "Index.groupid", 6),//公司集团ID
    ACCOUNTID("account_id", "Exceltopname.accountid", 7),//主账户账号
    ACCOUNTCODE("account_code", "Exceltopname.accountname", 8),//主账户代号
    ACCOUNTNAME("account_name", "Exceltopname.accountcorpname", 9),//主账户公司名称
    SUBACCOUNTID("sub_account_id", "Exceltopname.subaccountid", 10), //子账户账号
    SUBACCOUNTCODE("sub_account_code", "Exceltopname.subaccountname", 11),//子账户代号
    SUBACCOUNTNAME("sub_account_name", "Exceltopname.subaccountcorp", 12),//子账户公司名称


    WORKCITY("work_city", "Exceltopname.workcity", 18),//工作所在城市


    DEFINDFLAG("defineflag", "Report.FltDetail.CostCenterUser1", 1055),//自定义字段1
    DEFINDFLAG2("defineflag2", "Report.FltDetail.CostCenterUser2", 1055),//自定义字段2





    ACBPREPAYTYPE("acb_prepay_type", "Exceltopname.settlementtype", 23),//结算类型
    ISBOSS("is_boss", "Exceltopname.bosstype", 24),//BOSS类型

    JOUNARYREASON("journey_reason", "Exceltopname.outpurpose", 2025),//出行目的
    PROJECT("project", "Exceltopname.projectname", 2001),//项目名称
    VERBALAUTHORIZE("verbal_authorize", "Exceltopname.verbalauthorization", 800),//是否口头授权
    CONFIRMPERSON("confirmPerson", "Exceltopname.firstauthorizer", 27),//一次授权人
    CONFIRMPTYPE("confirmType", "Exceltopname.authorizationtype", 28),//一次授权方式

    AUDITORID2("auditorid2", "Exceltopname.Auditor2ID", 29), //二次授权人id

    CONFIRMPERSON2("confirmPerson2", "Exceltopname.secondauthorizer", 30),//二次授权人
    CONFIRMPTYPE2("confirmType2", "Exceltopname.seauthorizationtype", 31),//二次授权方式

    USERDEFINEDRID("userdefined_rid", "Exceltopname.diyrc", 2001),//用户自定义RC
    USERDEFINEDRC("userdefined_rc", "Exceltopname.diyrcexplanation", 2002),//用户自定义RC说明




    TAKETICKETSTATUS("taketicketstatus", "index.taketicketstatus", 106),  // 后取票状态




    INSURANCEFEE("insurance_fee", "Exceltopname.insurancefee", 114),//保险费


    PAPERTICKETFEE("paper_ticket_fee", "Exceltopname.outticketfee", 117),//纸质出票费

    GRABSERVICEFEE("grab_service_fee", "Exceltopname.priceticketfee", 119),//抢票服务费


    AFTERTAKETICKETFEE("aftertaketicketfee", "Exceltopname.lateroutfee", 122),//后取票服务费
    AFTERAFTERTAKETICKETFEE("afteraftertaketicketfee", "Exceltopname.lateroutticketfee", 123),//后收后取票服务费
    OCURRENCY("o_currency", "Exceltopname.orgcurrency", 123),//原币种(客户支付币种)
    OEXCHANGERATE("o_exchangerate", "Exceltopname.currencyrate", 124),//汇率(客户支付币种汇率)
    TRAINCARBONS("trainCarbons", "index.carbon", 124),//合并碳排量
    TRAINMEDIANCARBONS("trainMedianCarbons", "index.carbonmedian", 124),//碳排中位数
    TRAINCARBONSAVE("trainCarbonSave", "index.carbonsavings", 124),//碳节省额




    TICKETRCCODENAME("ticket_rccodename", "Exceltopname.volumercex", 135),//票张RC说明
    REFUND_RC("refund_rc", "index.refundrc", 135),//退票rc code
    REFUND_RC_DESC("refund_rc_desc", "index.refundrcdetail", 135),//退票rc code说明
    AUDITORID("auditorid", "Exceltopname.AuditorID", 26), // 审核人id
*/

    DELAY_RESCHEDULE_FEE("delay_reschedule_fee", "Exceltopname.delayRescheduleFee", 114), // 改签费
    ;


    private String code;
    private String name;
    private int order;

    TrainOrderDetailEnum(String code, String name, int order) {
        this.code = code;
        this.name = name;
        this.order = order;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public int getOrder() {
        return order;
    }

    @Override
    public TrainOrderDetailEnum getEnum() {
        return this;
    }
}
