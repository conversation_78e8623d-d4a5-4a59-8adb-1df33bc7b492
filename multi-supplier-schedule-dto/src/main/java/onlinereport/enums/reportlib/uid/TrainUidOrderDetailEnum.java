package onlinereport.enums.reportlib.uid;


import onlinereport.enums.reportlib.OrderDetailEnumerable;

/**
 * Auther:abguo
 * Date:2019/8/22
 * Description:
 * Modify:jhe3 20200708
 * Project:onlinereportweb
 */
public enum TrainUidOrderDetailEnum implements OrderDetailEnumerable<TrainUidOrderDetailEnum> {
    UID("uid","Exceltopname.uidnumber",0),//卡号
    USERNAME("user_name","Exceltopname.cardholder",0),//持卡人
    EMPLOYEID("employe_id","Exceltopname.cardholdernumber",0),//员工编号
    PASSENGERNAME("passenger_name","Exceltopname.passengername",0),//乘客姓名
    RANKNAME("rank_name","Exceltopname.corplevel",0),//职级
    ORDERID("order_id","TravelPosition.order",0),//订单号
    ORDERSTATUS("order_status","Exceltopname.orderstatus",0),//订单状态
    CORP_CORPORATION("corp_corporation","Index.companyid",0),//公司ID
    COMPANYGROUPID("companygroupid","Index.groupid",0),//公司集团ID
    ACCOUNTID("account_id","Exceltopname.accountid",0),//主账户账号
    ACCOUNTCODE("account_code","Exceltopname.accountname",0),//主账户代号
    ORDERDATE("order_date","Exceltopname.date",0),//预订日期
    PRINTTIME("printTime","Exceltopname.outticketdate",0),//出票日期
    TRAINNAME("train_name","Exceltopname.trainno",0),//车次
    FIRSTSEATTYPENAME("first_seat_type_name","Exceltopname.trainseat",0),//席别
    REALPAY("real_pay","Exceltopname.realpay",0),//实收实付
    TICKETPRICE("ticket_price","Exceltopname.priceticket",0),//原始出票金额
    CHANGEBALANCE("changebalance", "Exceltopname.changeprice",0),//改签差价
    QUANTITY("quantity","Index.num",0),//火车票张数
    CHANGEQUANTITY("change_quantity","Index.changenum",0),//火车票改签张数
    CHANGESTATUS("change_status","Exceltopname.changestatus",0),//改签状态
    REFUNDSTATUS("refund_status","Exceltopname.refundstatus",0),//退票状态
    DEPARTUREDATETIME("departure_date_time","Exceltopname.depaturedate",0),//出发日期
    DEPARTURESTATIONNAME("departure_station_name","Exceltopname.depaturepoint",0),//出发站
    DEPARTURECITYNAME("departure_city_name","Index.depcity",0),//出发城市
    ARRIVALDATETIME("arrival_date_time","Exceltopname.arrvialdate",0),//到达日期
    ARRIVALSTATIONNAME("arrival_station_name","Exceltopname.arrvialpoint",0),//到达站(中)
    ARRIVALCITYNAME("arrival_city_name","Index.arrcity",0),//到达城市

    COSTCENTER1("cost_center1","Exceltopname.costcenterone",0),//成本中心1
    COSTCENTER2("cost_center2","Exceltopname.costcentertwo",0),//成本中心2
    COSTCENTER3("cost_center3","Exceltopname.costcenterthree",0),//成本中心3
    COSTCENTER4("cost_center4","Exceltopname.costcenterfour",0),//成本中心4
    COSTCENTER5("cost_center5","Exceltopname.costcenterfive",0),//成本中心5
    COSTCENTER6("cost_center6","Exceltopname.costcentersix",0),//成本中心6
    DEPT1("dept1","Exceltopname.depone",0),//部门1
    DEPT2("dept2","Exceltopname.deptwo",0),//部门2
    DEPT3("dept3","Exceltopname.depthree",0),//部门3
    DEPT4("dept4","Exceltopname.depfour",0),//部门4
    DEPT5("dept5","Exceltopname.depfive",0),//部门5
    DEPT6("dept6","Exceltopname.depsix",0),//部门6
    DEPT7("dept7","Exceltopname.depseven",0),//部门7
    DEPT8("dept8","Exceltopname.depeight",0),//部门8
    DEPT9("dept9","Exceltopname.depnight",0),//部门9
    DEPT10("dept10","Exceltopname.depten",0),//部门10
    DELAY_RESCHEDULE_FEE("delay_reschedule_fee", "Exceltopname.delayRescheduleFee", 0), // 改签费
    ;


    private String code;
    private String name;
    private int order;

    TrainUidOrderDetailEnum(String code, String name, int order) {
        this.code = code;
        this.name = name;
        this.order = order;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public int getOrder() {
        return order;
    }

    @Override
    public TrainUidOrderDetailEnum getEnum() {
        return this;
    }
}
