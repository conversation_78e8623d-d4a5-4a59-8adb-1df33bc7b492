package onlinereport.enums.reportlib.uid;


import onlinereport.enums.reportlib.OrderDetailEnumerable;

/**
 * Auther:abguo
 * Date:2019/8/22
 * Description:
 */
public enum CarUidOrderDetailEnum implements OrderDetailEnumerable<CarUidOrderDetailEnum> {
    UID("uid","Exceltopname.uidnumber",0),//卡号
    USERNAME("user_name","Exceltopname.cardholder",0),//持卡人
    EMPLOYEID("employe_id","Exceltopname.cardholdernumber",0),//员工编号
    PASSENGERNAME("passenger_name","Exceltopname.passengername",0),//乘客姓名
    RANKNAME("rank_name","Exceltopname.corplevel",0),//职级
    ORDERID("order_id","TravelPosition.order",0),//订单号
    ORDERSTATUS("order_status","Exceltopname.orderstatus",0),//订单状态
    CORP_CORPORATION("corp_corporation","Index.companyid",0),//公司ID
    COMPANYGROUPID("companygroupid", "Index.groupid",0),//公司集团ID
    ACCOUNTID("account_id", "Exceltopname.accountid",0),//主账户账号
    ACCOUNTCODE("account_code","Exceltopname.accountname",0),//主账户代号
    BOOKINGTYPE_DES("bookingtype_des","index.bookingtype",0),//预订类型
    ORDERTYPE("order_type","Exceltopname.usecartype",0),//订单类型 order_type

    ORDERDATE("order_date","Exceltopname.date",0),//预订日期
    PERSONS("persons","Exceltopname.travelnumber",0),//出行人数
    DELREALPAY("real_pay","Exceltopname.realpayprice",0),//实际支付金额
    BASICFEE("basic_fee","Exceltopname.basefee",0),//基础费用
    DEPARTYRECITYNAME("departure_city_name","Index.depcity",0),//出发城市
    STARTADDRESSDES("start_address_des","Exceltopname.depatureaddress",0),//出发地址
    ARRIVALCITYNAME("arrival_city_name","Index.arrcity",0),//到达城市
    ENDADDRESSDES("end_address_des","Exceltopname.arrivaladdress",0),//到达地址
    ACTUALSTARTSERVICETIME("actualStartServiceTime","Exceltopname.startservicetime",0),//开始服务时间
    ACTUALDRIVEDURATION("actualDriveDuration","Exceltopname.drivetimeactually",0),//实际行驶时长
    NORMALDISTANCE("normal_distance","Exceltopname.miles",0),//公里数
    USEDURATION("use_duration","Exceltopname.usecarday",0),//用车天数
    VEHICLENAME("vehicle_name","Exceltopname.cartype",0),//车型
    COSTCENTER1("cost_center1","Exceltopname.costcenterone",0),//成本中心1
    COSTCENTER2("cost_center2","Exceltopname.costcentertwo",0),//成本中心2
    COSTCENTER3("cost_center3","Exceltopname.costcenterthree",0),//成本中心3
    COSTCENTER4("cost_center4","Exceltopname.costcenterfour",0),//成本中心4
    COSTCENTER5("cost_center5","Exceltopname.costcenterfive",0),//成本中心5
    COSTCENTER6("cost_center6","Exceltopname.costcentersix",0),//成本中心6
    DEPT1("dept1","Exceltopname.depone",0),//部门1
    DEPT2("dept2","Exceltopname.deptwo",0),//部门2
    DEPT3("dept3","Exceltopname.depthree",0),//部门3
    DEPT4("dept4","Exceltopname.depfour",0),//部门4
    DEPT5("dept5","Exceltopname.depfive",0),//部门5
    DEPT6("dept6","Exceltopname.depsix",0),//部门6
    DEPT7("dept7","Exceltopname.depseven",0),//部门7
    DEPT8("dept8","Exceltopname.depeight",0),//部门8
    DEPT9("dept9","Exceltopname.depnight",0),//部门9
    DEPT10("dept10","Exceltopname.depten",0),//部门10
    ;

    private String code;
    private String name;
    private int order;

    CarUidOrderDetailEnum(String code, String name, int order) {
        this.code = code;
        this.name = name;
        this.order = order;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public int getOrder() {
        return order;
    }

    @Override
    public CarUidOrderDetailEnum getEnum() {
        return this;
    }
}
