package onlinereport.enums.reportlib.uid;


import onlinereport.enums.reportlib.OrderDetailEnumerable;

/**
 * Auther:abguo
 * Date:2019/8/22
 * Description:
 */
public enum HotelUidOrderDetailEnum  implements OrderDetailEnumerable<HotelUidOrderDetailEnum> {

    UID("uid","Exceltopname.uidnumber",0),//卡号
    USERNAME("user_name","Exceltopname.cardholder",0),//持卡人
    EMPLOYEID("employe_id","Exceltopname.cardholdernumber",0),//员工编号
    CLIENTNAME("client_name","Exceltopname.hotelpassenger",0),//入住人
    RANKNAME("rank_name","Exceltopname.corplevel",0),//职级
    ORDERID("order_id","TravelPosition.order",0),//订单号
    ORDERSTATUS("order_status","Exceltopname.orderstatus",0),//订单状态
    ORDERDATE("order_date","Exceltopname.date",0),//预订日期
    CORP_CORPORATION("corp_corporation","Index.companyid",0),//公司ID
    COMPANYGROUPID("companygroupid","Index.groupid",0),//公司集团ID
    ACCOUNTID("account_id","Exceltopname.accountid",0),//主账户账号
    ACCOUNTCODE("account_code","Exceltopname.accountname",0),//主账户代号
    ARRIVALDATETIME("arrival_date_time","Exceltopname.livedate",0),//入住日期
    DEPARTUREDATETIME("departure_date_time","Exceltopname.leavedate",0),//离店日期
    HOTELNAME("hotel_name","Index.hotelname",0),//酒店名称
    HOTELGROUPNAME("hotel_group_name","Exceltopname.hotelgroup",0),//酒店集团
    BASICROOMTYPENAME("basic_room_type_name","Exceltopname.roomtype",0),//酒店房型
    STAR("star","Index.star",0),//星级
    CUSTOMEREVAL("customereval", "index.dimondlvl", 0),//钻级
    CITYNAME("city_name","Exceltopname.city",0),//城市
    PROVINCENAME("province_name","Exceltopname.province",0),//省份
    COUNTRYNAME("country_name","Exceltopname.country",0),//国家
    REALPAYWITHSERVICE("real_pay_with_servicefee", "Report.HotelRealPaywithServiceFee", 0), // 实收实付（含服务费)
    DEADPRICE("dead_price","Exceltopname.standardpay",0),//标付
    ROOMPRICE("room_price", "HotelFeeDetail.room_price", 0), //房价
    AVGPRICE("avg_price", "Index.avgmoney", 0), // 平均价格
    QUANTITY("quantity","Exceltopname.RoomNight",0),//酒店间夜
    ORDERTYPE("order_type","Exceltopname.memberandCorporate",0),//会员/协议
    COSTCENTER1("cost_center1","Exceltopname.costcenterone",0),//成本中心1
    COSTCENTER2("cost_center2","Exceltopname.costcentertwo",0),//成本中心2
    COSTCENTER3("cost_center3","Exceltopname.costcenterthree",0),//成本中心3
    COSTCENTER4("cost_center4","Exceltopname.costcenterfour",0),//成本中心4
    COSTCENTER5("cost_center5","Exceltopname.costcenterfive",0),//成本中心5
    COSTCENTER6("cost_center6","Exceltopname.costcentersix",0),//成本中心6
    DEPT1("dept1","Exceltopname.depone",0),//部门1
    DEPT2("dept2","Exceltopname.deptwo",0),//部门2
    DEPT3("dept3","Exceltopname.depthree",0),//部门3
    DEPT4("dept4","Exceltopname.depfour",0),//部门4
    DEPT5("dept5","Exceltopname.depfive",0),//部门5
    DEPT6("dept6","Exceltopname.depsix",0),//部门6
    DEPT7("dept7","Exceltopname.depseven",0),//部门7
    DEPT8("dept8","Exceltopname.depeight",0),//部门8
    DEPT9("dept9","Exceltopname.depnight",0),//部门9
    DEPT10("dept10","Exceltopname.depten",0),//部门10
    ;
    private String code;

    private String name;

    private int order;

    HotelUidOrderDetailEnum(String code, String name, int order) {
        this.code = code;
        this.name = name;
        this.order = order;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public int getOrder() {
        return order;
    }

    @Override
    public HotelUidOrderDetailEnum getEnum() {
        return this;
    }

}
