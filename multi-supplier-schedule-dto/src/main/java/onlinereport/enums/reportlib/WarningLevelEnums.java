package onlinereport.enums.reportlib;

/**
 * Auther:abguo
 * Date:2019/10/14
 * Description:
 * Project:onlinereportweb
 */
public enum WarningLevelEnums {
    //票号风险级别(0.未知 1.提示 2.预警 3.高风险 4.正常)
    WARNINGLEVEL0(0,"未知"),
    WARNINGLEVEL1(1,"提示"),
    WARNINGLEVEL2(2,"预警"),
    WARNINGLEVEL3(3,"高风险"),
    WARNINGLEVEL4(4,"正常"),;

    private int code;
    private String name;

    WarningLevelEnums(int code,String name){
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
