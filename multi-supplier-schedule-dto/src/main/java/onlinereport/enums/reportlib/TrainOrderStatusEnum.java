package onlinereport.enums.reportlib;

/**
 * Auther:abguo
 * Date:2019/10/15
 * Description:
 * Project:onlinereportweb
 */
public enum TrainOrderStatusEnum {

    N("Report.TrainOrderStatus1"),
    WP("Report.TrainOrderStatus2"),
    PP("Report.TrainOrderStatus3"),
    PF("Report.TrainOrderStatus4"),
    WA("Report.TrainOrderStatus5"),
    AR("Report.TrainOrderStatus6"),
    WT("Report.TrainOrderStatus7"),
    TP("Report.TrainOrderStatus8"),
    TD("Report.TrainOrderStatus9"),
    TF("Report.TrainOrderStatus10"),
    C("RiskOrder.ReportTip10");

    private String name;

    TrainOrderStatusEnum(String name){
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
