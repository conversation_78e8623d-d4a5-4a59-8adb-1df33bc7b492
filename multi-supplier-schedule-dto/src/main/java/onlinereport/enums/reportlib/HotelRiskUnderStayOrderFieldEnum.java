package onlinereport.enums.reportlib;


/**
 * <AUTHOR>
 * @date ：Created in 2022/7/20 16:56
 * @description：
 * @modified By：
 * @version: $
 */
public enum HotelRiskUnderStayOrderFieldEnum {

    ORDER_ID("orderId", "TravelPosition.order", true, true),

    RISK_SCENE("riskScene", "RiskOrder.SceneType", true, false), // 风险场景

    ORDER_STATUS("orderStatus", "Exceltopname.orderstatus", true, false), // 订单状态1

    UID("uid", "Exceltopname.uidnumber", true, false), // uid

    UID_NAME("uidName", "UserSurvey.Q1.Opt5", true, false), // 持卡人姓名

    UID_ISVALID("uidIsvalid", "RiskOrder.CardholdStatus", true, false), // 持卡人是否在职

    CLIENT_NAME("clientName", "RiskOrder.ClientEarlyDeparture", true, false), // 提前离店人

    JOURNEY_NO("journeyNo", "Exceltopname.linltravel", true, false),

    CORPORATION_NAME("corporationName", "Public.confirmname", true, false), // 公司名称

    HOTEL_NAME("hotelName", "Index.hotelname", true, false), // 酒店名称

    CITY("city", "Exceltopname.city", true, false),

    ORDER_CHECKIN_TIME("orderCheckInTime", "RiskOrder.OrderArrivalDate", true, false), // 预订入住日期

    ORDER_CHECKOUT_TIME("orderCheckOutTime", "RiskOrder.OriginalLeaveTime", true, false), // 预定离店时间

    ORDER_QUANTITY("orderQuantity", "RiskOrder.BookedRoomNight", true, false), // 预订间夜数

    REFUND_QUANTITY("refundQuantity", "RiskOrder.RefundRoomNight", true, false), // 退款间夜数

    ACTUAL_QUANTITY("actualQuantity", "RiskOrder.ActualRoomNight", true, false), // 实际间夜数

    AMOUNT("actualAmount", "HotelFeeDetail.room_price", true, false),

    IS_OTHER_CLIENT_UNDER_STAY("isOtherClientUnderStay", "RiskOrder.OtherClient", true, false),

//    HAS_AUDIT("hasAudit", "RiskOrder.AuditRecord", true, false),
//
//    AUDIT_QUANTITY("auditQuantity", "RiskOrder.AuditRecordNight", true, false),
//
//    AUDIT_STATUS("auditStatus", "RiskOrder.AuditRecordStatus", true, false),
//
//    AUDIT_WASTE_QUANTITY("auditWasteQuantity", "RiskOrder.AuditRecordWaste", true, false),

    WASTE_QUANTITY("wasteQuantity", "RiskOrder.WastedRoomNight", true, false),

    TRF_ORDER("trfOrder", "RiskOrder.OtherCityFltTrain", true, false),

    CAR_ORDER("carOrder", "RiskOrder.OtherCityCar", true, false),

    ACCOUNT_ID("accountId", "App.subaccouont", false, false),

    DEPT1("dept1", "Exceltopname.depone", false, false),

    DEPT2("dept2", "Exceltopname.deptwo", false, false),

    DEPT3("dept3", "Exceltopname.depthree", false, false),

    DEPT4("dept4", "Exceltopname.depfour", false, false),

    DEPT5("dept5", "Exceltopname.depfive", false, false),

    DEPT6("dept6", "Exceltopname.depsix", false, false),

    DEPT7("dept7", "Exceltopname.depseven", false, false),

    DEPT8("dept8", "Exceltopname.depeight", false, false),

    DEPT9("dept9", "Exceltopname.depnight", false, false),

    DEPT10("dept10", "Exceltopname.depten", false, false),

    COST_CENTER1("costcenter1", "Exceltopname.costcenterone", false, false),

    COST_CENTER2("costcenter2", "Exceltopname.costcentertwo", false, false),

    COST_CENTER3("costcenter3", "Exceltopname.costcenterthree", false, false),

    COST_CENTER4("costcenter4", "Exceltopname.costcenterfour", false, false),

    COST_CENTER5("costcenter5", "Exceltopname.costcenterfive", false, false),

    COST_CENTER6("costcenter6", "Exceltopname.costcentersix", false, false),

    OPERATE_STATUS("operateStatus", "RiskOrder.OperateStatus", true, false), // 处理状态

    OPERATOR("operator", "RiskOrder.OperatePerson", true, false), // 处理人

    OPERATOR_TIME("operatorTime", "RiskOrder.OperateTime", true, false), // 处理时间

    OPERATE_MARK("operatorMark", "RiskOrder.OperateNote", true, false), // 处理说明

    ;

    private String name;

    private String headerKey;

    // 是否默认展示字段
    private boolean isDefault;

    private boolean isMust;

    HotelRiskUnderStayOrderFieldEnum(String name, String headerKey, boolean isDefault, boolean must) {
        this.name = name;
        this.headerKey = headerKey;
        this.isDefault = isDefault;
        this.isMust = must;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHeaderKey() {
        return headerKey;
    }

    public void setHeaderKey(String headerKey) {
        this.headerKey = headerKey;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public boolean isMust() {
        return isMust;
    }

    public void setMust(boolean must) {
        isMust = must;
    }
}
