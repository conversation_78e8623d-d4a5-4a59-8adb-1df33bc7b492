package onlinereport.enums.reportlib;

/**
 * Auther:abguo
 * Date:2019/8/22
 * Description:
 * Modify:jhe3 20200709
 * Project:onlinereportweb
 */
public enum CarOrderDetailEnum implements OrderDetailEnumerable<CarOrderDetailEnum> {

    //基础信息
    ORDERID("order_id", "TravelPosition.order", 0),//订单号
    ORDERSTATUS("order_status", "Exceltopname.orderstatus", 1),//订单状态
    ORDERDATE("order_date", "Exceltopname.date", 2),//预订日期
    ISONLINE("is_online", "Travelanalysis.scheduledstyle", 20),//预订方式
    ORDERTYPE("order_type", "Exceltopname.usecartype", 100),//用车类型
    BOOKINGTYPE_DES("bookingtype_des", "index.bookingtype", 100),//预订类型
    PAYMENTSTATUS("payment_status", "Exceltopname.paystatus", 101),//支付状态
    PASSENGERNAME("passenger_name", "Exceltopname.passengername", 102),//乘客姓名
    PERSONS("persons", "Exceltopname.travelnumber", 103),//出行人数
    CONTACTNAME("contact_name", "Exceltopname.contactperson", 104),//联系人姓名
    QUANTITY("quantity", "Exceltopname.usecarquality", 105),//数量
    DEPARTYRECITYNAME("departure_city_name", "Index.depcity", 111),//出发城市
    ARRIVALCITYNAME("arrival_city_name", "Index.arrcity", 112),//到达城市
    ADDRESS("address", "Exceltopname.address", 113),//地址
    FLIGHTTRAINNUM("flight_train_num", "Exceltopname.AirportTransferFlight", 115),//接送机航班

//    PATTERNTYPE("pattern_type", "Exceltopname.usecarstatus", 116),//用车形态
    STARTADDRESSDES("start_address_des", "Exceltopname.depatureaddress", 117),//出发地址
    TAKETIME("start_time", "Exceltopname.begintime", 118),//开始时间
    ENDADDRESSDES("end_address_des", "Exceltopname.arrivaladdress", 119),//到达地址

    ENDTIME("end_time", "Index.arrivaltime", 120),//到达时间
    ESTIMATEDISTANCE("estimate_distance", "Exceltopname.estimatemile", 121),//预计里程
    NORMALDISTANCE("normal_distance", "Exceltopname.miles", 122),//公里数
    ACTUALSTARTSERVICETIME("actualStartServiceTime", "Exceltopname.startservicetime", 134),//开始服务时间
    ACTUALDRIVEDURATION("actualDriveDuration", "Exceltopname.drivetimeactually", 135),//实际行驶时长
    DRIVERNAME("drivername","Exceltopname.drivername",136),//司机姓名
//    CANCELFEERATE("cancelFeeRate", "Exceltopname.cancelfeerate", 136),//有损取消比例

    // 详细信息
    PREPAYTYPE("acb_prepay", "Exceltopname.paytype", 22),//支付方式
    TRIPID("trip_id", "Exceltopname.belongtravel", 25),//所属行程号
    JOURNEYNO("journey_no", "Exceltopname.linltravel", 26),//关联行程单号
    GROUPMONTH("group_month", "Exceltopname.outticketyear", 32),//出票年月
    VEHICLENAME("vehicle_name", "Exceltopname.cartype", 128),//车型
    VENDORNAME("vendor_name", "Exceltopname.suppliername", 129),//供应商名称

    //    公司信息
//    CORP_CORPORATION("corp_corporation", "Index.companyid", 3),//公司ID
    CORPNAME("corp_name", "Public.confirmname", 4),//公司名称
//    STDINDUSTRY1("std_industry1", "index.industrybig", 14),//行业大类
//    STDINDUSTRY2("std_industry2", "index.industrysmall", 14),//行业小类

    // 人员信息
    //    UID("uid","Exceltopname.uidnumber",15),//卡号
    EMPLOYEID("employe_id", "Exceltopname.cardholdernumber", 16),//预定人员工编号
    USERNAME("user_name", "Exceltopname.cardholder", 17),//预定人姓名
//    RANKNAME("rank_name", "Exceltopname.corplevel", 19),//职级
    DEPT1("dept1", "Exceltopname.car.depone", 905),//部门1
//    DEPT2("dept2", "Exceltopname.deptwo", 915),//部门2
//    DEPT3("dept3", "Exceltopname.depthree", 925),//部门3
//    DEPT4("dept4", "Exceltopname.depfour", 935),//部门4
//    DEPT5("dept5", "Exceltopname.depfive", 945),//部门5
//    DEPT6("dept6", "Exceltopname.depsix", 955),//部门6
//    DEPT7("dept7", "Exceltopname.depseven", 965),//部门7
//    DEPT8("dept8", "Exceltopname.depeight", 975),//部门8
//    DEPT9("dept9", "Exceltopname.depnight", 985),//部门9
//    DEPT10("dept10", "Exceltopname.depten", 995),//部门10
    COSTCENTER1("cost_center1", "Exceltopname.car.costcenterone", 1005),//成本中心1
//    COSTCENTER2("cost_center2", "Exceltopname.costcentertwo", 1015),//成本中心2
//    COSTCENTER3("cost_center3", "Exceltopname.costcenterthree", 1025),//成本中心3
//    COSTCENTER4("cost_center4", "Exceltopname.costcenterfour", 1035),//成本中心4
//    COSTCENTER5("cost_center5", "Exceltopname.costcenterfive", 1045),//成本中心5
//    COSTCENTER6("cost_center6", "Exceltopname.costcentersix", 1055),//成本中心6

    //    订单钱款信息
    ESTIMATEAMOUNT("estimate_amount", "Exceltopname.estimateprice", 106),//预估金额

    DELREALPAY("real_pay", "Exceltopname.realpayprice", 107),//实际支付金额
    SERVICEFEE("service_fee", "Exceltopname.baseservicefee", 109),//基础服务费

//    REFUNDAMOUNT("refund_amount", "Exceltopname.refundprice", 110),//退款金额
//    ISMIXPAYMENT("is_mix_payment", "Exceltopname.mixpay", 110),//随心订混付
//    SETTLEMENTPERSONAMT("settlementPersonalAmt", "Exceltopname.personalcost", 110),//混付个人账户支付金额
//    SETTLEMENTACCNTAMT("settlementAccntAmt", "Exceltopname.corpcost", 110),//混付公司账户支付金额

    ACTUALSTARTADRESS("actual_start_address", "Exceltopname.departaddress", 1051),//实际出发地址
    ACTUALENDADRESS("actual_end_address", "Exceltopname.arriveaddress", 1052), //实际到达地址

    BASICFEE("basic_fee", "Exceltopname.basefee", 108),//基础费用
    /*COMPANYGROUP("companygroup", "Index.belonggroup", 5),//公司集团
    COMPANYGROUPID("companygroupid", "Index.groupid", 6),//公司集团ID
    ACCOUNTID("account_id", "Exceltopname.accountid", 7),//主账户账号
    ACCOUNTCODE("account_code", "Exceltopname.accountname", 8),//主账户代号
    ACCOUNTNAME("account_name", "Exceltopname.accountcorpname", 9),//主账户公司名称
    SUBACCOUNTID("sub_account_id", "Exceltopname.subaccountid", 10),//子账户账号
    SUBACCOUNTCODE("sub_account_code", "Exceltopname.subaccountname", 11),//子账户代号
    SUBACCOUNTNAME("sub_account_name", "Exceltopname.subaccountcorp", 12),//子账户公司名称


    WORKCITY("work_city", "Exceltopname.workcity", 18),//工作所在城市


    DEFINDFLAG("defineflag", "Report.FltDetail.CostCenterUser1", 1055),//自定义字段1
    DEFINDFLAG2("defineflag2", "Report.FltDetail.CostCenterUser2", 1055),//自定义字段2



    FEETYPE("fee_type", "Exceltopname.personalycost", 21),//是否个人消费

    ACBPREPAYTYPE("acb_prepay_type", "Exceltopname.settlementtype", 23),//结算类型
    ISBOSS("is_boss", "Exceltopname.bosstype", 24),//BOSS类型

    JOUNARYREASON("journey_reason", "Exceltopname.outpurpose", 2025),//出行目的
    PROJECT("project", "Exceltopname.projectname", 2001),//项目名称
    VERBALAUTHORIZE("verbal_authorize", "Exceltopname.verbalauthorization", 800),//是否口头授权
    CONFIRMPERSON("confirmPerson", "Exceltopname.firstauthorizer", 27),//一次授权人
    CONFIRMPTYPE("confirmType", "Exceltopname.authorizationtype", 28),//一次授权方式

    AUDITORID2("auditorid2", "Exceltopname.Auditor2ID", 29), //二次授权人id

    CONFIRMPERSON2("confirmPerson2", "Exceltopname.secondauthorizer", 30),//二次授权人
    CONFIRMPTYPE2("confirmType2", "Exceltopname.seauthorizationtype", 31),//二次授权方式

    USERDEFINEDRID("userdefined_rid", "Exceltopname.diyrc", 2001),//用户自定义RC
    USERDEFINEDRC("userdefined_rc", "Exceltopname.diyrcexplanation", 2002),//用户自定义RC说明





    OCURRENCY("o_currency", "Exceltopname.orgcurrency", 110),//原币种(客户支付币种)
    OEXCHANGERATE("o_exchangerate", "Exceltopname.currencyrate", 111),//汇率(客户支付币种汇率)

    FIXEDLOCATIONNAME("fixed_location_name", "Exceltopname.detailedaddress", 114),//固定点名称

    CARCARBONS("carCarbons", "index.carbon", 116),//碳排放量


    USEDURATION("use_duration", "Exceltopname.usecarday", 126),//用车天数
    IS_GREEN_CAR("isGreenCar", "index.isgreencar", 126),//用车天数

    USETYPE("use_type", "Exceltopname.usearea", 127),//使用区域

    SCENENAME("scene_name", "Exceltopname.usecarscenes", 130),//用车场景
    SERVICE_TIME("service_time", "index.carservicetime", 130), // 用车服务时长
    VENDORORDERID("vendororderid", "Exceltopname.Suppliernumber", 131),//供应商流水号

    PACKAGENAME("package_name", "Exceltopname.carpackagename", 1050),//套餐名称


    AUDITORID("auditorid", "Exceltopname.AuditorID", 26),

    ISABNORMAL("is_abnormal", "Exceltopname.AbnormalCar", 132), // 疑似敏感订单

    ISABNORMALCONFIRM("is_abnormal_userconfirm", "Exceltopname.AbnormalCarConfirmed", 133), // 疑似敏感且客户确认订单

    ONLINE_HIT_ABNORMAL_RULE_DESC("online_hit_abnormal_rule_desc", "RiskOrder.SceneType", 133), // 明细线上命中敏感订单场景*/
    ;
    /// //////////////////////////////////////////////////////////////////////////
    private String code;
    private String name;
    private int order;

    CarOrderDetailEnum(String code, String name, int order) {
        this.code = code;
        this.name = name;
        this.order = order;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public int getOrder() {
        return order;
    }

    @Override
    public CarOrderDetailEnum getEnum() {
        return this;
    }
}
