package onlinereport.enums.reportlib;

/**
 * Auther:abguo
 * Date:2019/8/22
 * Description:异常机票
 * Project:onlinereportweb
 */
public enum AbnormalFlightOrderDetailEnum implements OrderDetailEnumerable<AbnormalFlightOrderDetailEnum>{

    ORDERID("orderId","TravelPosition.order",0),//订单号
    ORDERSTATUS("order_status","Exceltopname.orderstatus",1),//订单状态
    UID("uid","Exceltopname.uidnumber",5),//卡号
    ACCOUNTCOMPANYNAME("accountCompanyName","Exceltopname.accountcorpname",15),//主账户公司名称
    TICKETNO("ticketNO","Exceltopname.planecorpnumber",20),//票号
    TICKETSTATUS("ticketStatus","Exceltopname.numberstatus",25),//票号状态
    TICKETWARNINGLEVEL("ticketWarningLevel","Exceltopname.risklab",30),//风险标识
    PASSENGENAME("passengerName","Exceltopname.planepassenger",35),//乘机人
    FLIGHTCITY("flightCity","Exceltopname.planecode",40),//航段
    FLIGHTNO("flightNO","Index.flightno",50),//航班号
    TAKEOFFTIME("takeOffTime","Exceltopname.takeofftime",55),//起飞时间
    STDPRICE("stdPrice","Exceltopname.allprice",60),//机票价
    FLIGHTSTATUS("flightStatus","Exceltopname.planestatus",84);//航段状态

    private String code;
    private String name;
    private int order;

    AbnormalFlightOrderDetailEnum(String code, String name, int order) {
        this.code = code;
        this.name = name;
        this.order = order;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public int getOrder() {
        return order;
    }

    @Override
    public AbnormalFlightOrderDetailEnum getEnum() {
        return this;
    }

}
