package onlinereport.enums.reportlib;

/**
 * Auther:abguo
 * Date:2019/8/22
 * Description:
 */
public enum HotelOrderAuditEnum implements OrderDetailEnumerable<HotelOrderAuditEnum>{

    ORDERID("order_id","TravelPosition.order",0),//订单号
    ORDERSTATUS("order_status","Exceltopname.orderstatus",0),//订单状态
    CORPNAME("corp_name","Public.confirmname",0),//公司名称
    ORDERDATE("order_date","Exceltopname.date",0),//预订日期
    PROVINCENAME("province_name","Exceltopname.province",0),//省份
    CITYNAME("city_name","Exceltopname.city",0),//城市
    HOTELGROUPNAME("hotel_group_name","Exceltopname.hotelgroup",0),//酒店集团
    HOTELNAME("hotel_name","Index.hotelname",0),//酒店名称
    PRODUCTTYPE("producttype","Exceltopname.producttype",0), //产品类型
    BALANCETYPENAME("balancetypename","nightcheck.balancetype",0),//支付类型
    ROOM_TYPE("room_type","index.roomtype",0),// 房间类型
    ROOM_NO("room_no","nightcheck.roomno",0),// 房间号
    ARRIVALDATETIME("arrival_date_time","Exceltopname.livedate",0),//入住日期
    DEPARTUREDATETIME("departure_date_time","Exceltopname.leavedate",0),//离店日期
    AUDIT_CHECKIN_DATE("audit_checkin_date","nightcheck.checkintime",0),//审核入住时间
    AUDIT_CHECKOUT_DATE("audit_checkout_date","nightcheck.checkoutime",0),//审核离店时间
    QUANTITY("quantity","Exceltopname.RoomNight",0),//酒店间夜
    AUDIT_QUANTITY("audit_quantity","nightcheck.roomnights",0),//审核成交间夜
    AMOUNT("amount","RiskOrder.CostMoney",0),//订单金额
    IS_AUDIT("is_audit","nightcheck.ischecked",0),//是否已审核
    AUDIT_STATUS("audit_status","nightcheck.checkstatus",0),//审核状态
    AUDIT_CLIENT_NAME("audit_client_name","nightcheck.checker",0),//审核入住人
    AUDIT_REFUND_QUANTITY("auditRefundQuantity","nightcheck.refundquantity",0),//夜审退款间夜量
    AUDIT_REFUND_AMOUNT("auditRefundAmount","nightcheck.refundamount",0),//夜审退款金额

    ;
    private String code;

    private String name;

    private int order;

    HotelOrderAuditEnum(String code, String name, int order) {
        this.code = code;
        this.name = name;
        this.order = order;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public int getOrder() {
        return order;
    }

    @Override
    public HotelOrderAuditEnum getEnum() {
        return this;
    }

}
