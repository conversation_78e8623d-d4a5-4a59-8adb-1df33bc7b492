package onlinereport.enums.reportlib;

/**
 * Author:abguo
 * Date:2019/8/22
 * Description:
 * Project:onlinereportweb
 */
public enum FlightOrderDetailEnum implements OrderDetailEnumerable<FlightOrderDetailEnum>{

    // 基础信息
    ORDERID("order_id","TravelPosition.order",0),//订单号
    ORDERSTATUS("order_status","Exceltopname.orderstatus",1),//订单状态
    ORDERDATE("order_date","Exceltopname.date",2),//预订日期
    ISONLINE("is_online", "Travelanalysis.scheduledstyle",20),//预订方式
    QUANTITY("quantity","Index.number",52),//张数
    FULLQUANTITY("fullQuantity","Index.fullprice",53),//全价票张数
    ORDERTKT("ordertkt", "Exceltopname.TicketQuantity",54),//出票张数 todo 出票张数是否等于票张数
    REFUNDTKT("refundtkt", "Index.refundnum",55),//退票张数
    PASSENGENO("passenger_no","Exceltopname.PassengerEmployeeID",80),//出行人员工编号
    SEQUENCE("sequence","Exceltopname.travelno",82),//行程序号
    FLIGHTNO("flightNO", "Index.flightno", 83),//航班号
    SUBCLASS("subClass", "Exceltopname.subcarbin", 84),//子舱位
    CHANGEFLIGHTNO("changeFlightNO","Exceltopname.changeairline",283),//改签后航班号
    LOWSUBCLASS("low_subclass","Exceltopname.lowpriceaircarbin",275),//最低价航班舱位
    FLIGHTSTATUS("flightStatus","Exceltopname.planestatus",85),//航段状态
    FLIGHTTIME("flightTime","Exceltopname.planetime",86),//飞行时间（min）
    AIRLINE("airLine","Exceltopname.planecorpcode",87),//航空公司二字码
    AIRLINECN("airLineCn","Exceltopname.planecorpname",88),//航空公司名称
    TICKETNO("ticketNO","Exceltopname.planecorpnumber",89),//航空公司票号
    TICKETSTATUS("ticketStatus","Exceltopname.numberstatus",90),//票号状态
    REALCLASS("realClass","Exceltopname.classlevel",91),//物理舱位
    FLIGHTCITYCODE("flight_city_code","Exceltopname.citycode",92),//航段(城市)三字码
    FLIGHTCITY("flightCity","Exceltopname.planecode",93),//航段

    FLIGHTCITY2("flightCity2","Exceltopname.detailedplanecode",94),//合并航段/详细航程
    TPMS("tpms","Exceltopname.totalmileagekm",95),//合并里程（公里）
    TPMSEN("tpmsEN","Exceltopname.totalmileage",96),//合并里程（英里）
    DEPARTUREPORTNAME("departurePortName","Exceltopname.takeoffairport",97),//起飞机场
    DPORTCODE("dport_code","Exceltopname.takeoffairportcode",98),//起飞机场三字码
    DEPARTURECITYNAME("departureCityName","Index.depcity",99),//出发城市
    DEPARTURECITYCODE("departureCityCode","Exceltopname.takeoffcitycode",100),//出发城市三字码
    DEPARTURECOUNTRY("departure_country","Exceltopname.takeoffcountry",101),//起飞国家

    DEPARTURECONTINENT("departure_continent","Exceltopname.departurecontinent",102), //出发城市大洲


    TAKEOFFTIME("takeOffTime","Exceltopname.takeofftime",103),//起飞时间

    ARRIVALCITYNAME("arrivalCityName","Index.arrcity",105),//到达城市
    ARRIVALCITYCODE("arrivalCityCode","Exceltopname.arrivalcitycode",106),//到达城市三字码
    ARRIVALPORTNAME("arrivalPortName","Exceltopname.arrivalcity",107),//到达机场
    APORTCODE("aport_code","Exceltopname.arrivalairportcode",108),//到达机场三字码
    ARRIVALCONUNTRY("arrival_country","Exceltopname.arrivalcountry",109),//到达国家
    ARRIVALDATETIME("arrival_date_time","Index.arrivaltime",110),//到达时间
    AGETYPE("age_type","Exceltopname.airtickettype",144),//机票类型
    FLIGHTCLASS("flight_class","Exceltopname.airlinetype",294),//航班类型

    // 详细信息
    PREPAYTYPE("acb_prepay", "Exceltopname.paytype",22),//支付方式
    TRIPID("trip_id","Exceltopname.belongtravel",25),//所属行程号
    GROUPMONTH("group_month", "Exceltopname.outticketyear",32),//出票年月
    PRINTTICKETTIME("printTicketTime","Exceltopname.outtickettime",50),//出退票时间
    PROVIDEBILLTYPE("provideBillType","Exceltopname.outtickettype",51),//开票类型
    REBOOK_PREPAYTYPENAME("rebook_prepaytypename", "index.rebookpayment", 65),//改签支付方式
    LOWDTIME("lowDTime","Exceltopname.lowairlinetime",130),//最低价航班起飞时间
    CLASSRID("classRid","ComplianceMonitor.cabinrc",131),//舱等Reason Code
    CLASSRC("classRCEN","Exceltopname.carbinrcexplanation",132),//舱等RC说明

    AGREEMENTRID("agreement_rid","ComplianceMonitor.pactrc",133),//协议RC
    AGREEMENTRC("agreement_rc","Exceltopname.pactrcexplantion",134),//协议RC说明
    AGREEMENTRCVV("agreement_rc_vv","Exceltopname.pactRCDIYReason" ,134),//协议RC自定义备注

    LOWRID("low_rid","ComplianceMonitor.lowpricerc",135),//低价RC
    LOWRC("low_rc","Exceltopname.lowrcexplanation",136),//低价RC说明

    LOWRC_MARK("low_rc_note", "Exceltopname.LowRCNote", 137), //超过低价RC备注
    LOWPRICERCVV("lowprice_rc_vv", "Exceltopname.LowRCDIYReason", 137), //低价RC自定义备注



    PRERID("preRid","Exceltopname.nonadvancerc",138),//未提前预订ReasonCode
    PRERC("preRC","Exceltopname.nonadvancercex",139),//未提前预订RC说明

    TIMERID("time_rid","ComplianceMonitor.timerc",140),//时间RC
    TIMERC("time_rc","Exceltopname.timercexp",141),//时间RC说明
    TIMERCVV("time_rc_vv","Exceltopname.TimeRCDIYReason",141),//时间RC自定义备注
    DISTANCERCVV("distance_rc_vv","Exceltopname.DistanceRCDIYReason",141),//距离RC自定义备注

    ISREFUND("is_refund","Exceltopname.isrefund",276),//是否退票
    REFUNDREASONDESC("refundResonDesc","Exceltopname.refundreason",277),//退票原因
    REFUNDTYPE("refundType","Exceltopname.FlightRefundType",277),//退票类型
    REFUNDRTIME("refund_time","Exceltopname.checkdate",278),//退供应商审核日期
    ISREBOOK("isRebook","Exceltopname.changelab",279),//改签标签
    REBOOKREASONDESC("rebookResonDesc","Exceltopname.changereason",280),//改签原因
    REBOOKTYPE("rebookType","Exceltopname.FlightRebookType",280),//改签类型
    REBOOKTIME("rebook_time","Exceltopname.changetime",281),//改签时间
    ORIGINALORDERID("originalOrderID","Exceltopname.exchangeorder",282),//国际机票改签前订单号
    CHANGETAKEOFFTIME("changeTakeOffTime","Exceltopname.changetakeofftime",284),//改签后起飞时间
    CHANGEARRIVALTIME("change_arrival_datetime","Exceltopname.changearrvaltime",285),//改签后到达时间
    BFRETURN("bfReturn","Exceltopname.isreback",287),//是否前返
    CONTRACTTYPE("contract_type","Exceltopname.ispact",289),//是否协议
    AGREEMENTTYPENAME("agreement_type_name","Exceltopname.pacttype",290),//协议类型
    AGREEMENTRATE("agreementRate","Exceltopname.pactrate",291),//协议航空折扣率
    PREORDERDATE("preorderDate","Travelanalysis.advancedbooking",292),//提前预订天数
    FLIGHTWAYDESC("flight_way_desc","Exceltopname.airtyoe",293),//航程类型
    CARRIERFLIGHTNO("carrierflightno","Exceltopname.carrierflightno",2502),//承运航班号
    DESTCITYNAME("dest_city_name", "Exceltopname.destinationcity", 118),//目的地城市名称

    // 公司信息
    CORP_CORPORATION("corp_corporation","Index.companyid",3),//公司ID
    CORPNAME("corp_name","Public.confirmname",4),//公司名称
    STDINDUSTRY1("std_industry1", "index.industrybig",14),//行业大类
    STDINDUSTRY2("std_industry2", "index.industrysmall",14),//行业小类

    // 预定人信息
    UID("uid","Exceltopname.uidnumber",16),//卡号
    EMPLOYEID("employe_id","Exceltopname.cardholdernumber",17),//员工编号
    USERNAME("user_name","Exceltopname.cardholder",18),//持卡人
    RANKNAME("rank_name","Exceltopname.corplevel",20),//职级

    DEPT1("dept1","Exceltopname.depone",905),//部门1
    DEPT2("dept2","Exceltopname.deptwo",915),//部门2
    DEPT3("dept3","Exceltopname.depthree",925),//部门3
    DEPT4("dept4","Exceltopname.depfour",935),//部门4
    DEPT5("dept5","Exceltopname.depfive",945),//部门5
    DEPT6("dept6","Exceltopname.depsix",955),//部门6
    DEPT7("dept7","Exceltopname.depseven",965),//部门7
    DEPT8("dept8","Exceltopname.depeight",975),//部门8
    DEPT9("dept9","Exceltopname.depnight",985),//部门9
    DEPT10("dept10","Exceltopname.depten",995),//部门10

    COSTCENTER1("cost_center1","Exceltopname.costcenterone",1005),//成本中心1
    COSTCENTER2("cost_center2","Exceltopname.costcentertwo",1015),//成本中心2
    COSTCENTER3("cost_center3","Exceltopname.costcenterthree",1025),//成本中心3
    COSTCENTER4("cost_center4","Exceltopname.costcenterfour",1035),//成本中心4
    COSTCENTER5("cost_center5","Exceltopname.costcenterfive",1045),//成本中心5
    COSTCENTER6("cost_center6","Exceltopname.costcentersix",1055),//成本中心6
    DEFINDFLAG("defineflag","Report.FltDetail.CostCenterUser1",1055),//自定义字段1
    DEFINDFLAG2("defineflag2","Report.FltDetail.CostCenterUser2",1055),//自定义字段2

    //订单钱款信息
    REALPAY("realpay","Exceltopname.realpay",56),//实收实付
    PRICE("price","Index.netprice",57),//成交净价
    NETFARE("netfare", "Exceltopname.netprice",58),//成交净价(不含改签价差)
    TAX("tax","Exceltopname.fee",59),//民航基金/税
    OILFEE("oilFee","Exceltopname.oilfee",60),//燃油费

    SERVICEFEE("serviceFee","Exceltopname.baseservicefee",61),//基础服务费
    INSERANCEFEE("insuranceFee","Exceltopname.insurancefee",62),//保险费
    BINDAMOUNT("bindAmount","Exceltopname.hotelfeeprice",63),//绑定酒店优惠券金额
    CHANGEFEE("changeFee","Exceltopname.changefee",64),//改签费
    REBOOKPRICEDIFFERENT("rebook_price_differential","Exceltopname.changeprice",65),//改签差价
    REBOOKSERVICEFEE("rebook_service_fee","Exceltopname.changeserfee",65),//改签服务费
    TAX_DIFFERENTIAL("tax_differential","index.taxdifferential",65),//税差
    REFUNDFEE("reFundFee","Exceltopname.refundfee",66),//退票费
    REFUNDSERVICEFEE("refundServiceFee","Exceltopname.refundserfee",67),//退票服务费
    SENDTICKETFEE("sendticketfee","Exceltopname.sendfee",68),//送票费

    TICKETBEHINDSERVICEFEE("ticket_behind_service_fee", "Exceltopname.laterfee",69),//出票后收服务费
    REBOOKBEHINDSERVICEFEE("rebook_behind_service_fee", "Exceltopname.changelaterfee",70),//改签后收服务费
    REFUNDTBEHINDSERVICEFEE("refund_behind_service_fee", "Exceltopname.refundlaterfee",71),//退票后收服务费
    ISMIXPAYMENT("is_mix_payment","Exceltopname.mixpay",71),//是否混付
    SETTLEMENTACCNTAMT("settlementAccntAmt","index.trainmixpay",71),//混付公司账户支付金额
    SETTLEMENTPERSONAMT("settlementPersonalAmt","index.trainselfpay",71),//混付个人账户支付金额

    STDPRICE("stdPrice","Exceltopname.allprice",72),//全价
    PRINTPRICE("print_price", "Exceltopname.ticketprice",73),//票面价
    PUBLISHPRICE("publishprice", "Exceltopname.PublishPrice", 74), // 公布运价
    PRICERATE("priceRate","Travelanalysis.ticketsales",75),//折扣
    CORPPRICEADJ("corp_price_adj","Travelanalysis.reallowest",76),//最低价
    SAVE_AMOUNT_3C("save_amount_3c", "Save.AgreeAmt", 78),//三方节省金额
    SAVE_AMOUNT_PREMIUM("save_amount_premium", "Save.TMCAmt", 78),//两方节省金额

    CONTROL_SAVE("controlSave", "savings.controlsavings", 78),//差旅管控节省


//    COMPANYGROUP("companygroup", "Index.belonggroup", 5),//公司集团
//    COMPANYGROUPID("companygroupid", "Index.groupid", 6),//公司集团ID
//    ACCOUNTID("account_id", "Exceltopname.accountid", 7),//主账户账号
//    ACCOUNTCODE("account_code", "Exceltopname.accountname", 8),//主账户代号
//    ACCOUNTNAME("account_name", "Exceltopname.accountcorpname", 9),//主账户公司名称
//    SUBACCOUNTID("sub_account_id", "Exceltopname.subaccountid", 10),//子账户账号
//    SUBACCOUNTCODE("sub_account_code", "Exceltopname.subaccountname", 11),//子账户代号
//    SUBACCOUNTNAME("sub_account_name", "Exceltopname.subaccountcorp", 12),//子账户公司名称
//
//
//    CUSTOMERID("customerid", "Report.ThreePartyCustomerID", 15), // 三方协议 大客户编号
//
//
//    WORKCITY("work_city", "Exceltopname.workcity", 19),//工作所在城市
//
//
//    FEETYPE("fee_type", "Exceltopname.personalycost", 21),//是否个人消费
//
//    ACBPREPAYTYPE("acb_prepay_type", "Exceltopname.settlementtype", 23),//结算类型
////    ISBOSS("is_boss", "Exceltopname.bosstype",24),//BOSS类型
//
//    JOURNEYNO("journey_no", "Exceltopname.linltravel", 26),//关联行程单号
//    JOUNARYREASON("journey_reason", "Exceltopname.outpurpose", 2025),//出行目的
//    PROJECT("project", "Exceltopname.projectname", 2001),//项目名称
//    VERBALAUTHORIZE("verbal_authorize", "Exceltopname.verbalauthorization", 800),//是否口头授权
//
////    AUDITORID("auditorid", "Exceltopname.AuditorID", 26), //授权人id
////    CONFIRMPERSON("confirmPerson","Exceltopname.firstauthorizer",27),//一次授权人
////    CONFIRMPTYPE("confirmType","Exceltopname.authorizationtype",28),//一次授权方式
////
////    AUDITORID2("auditorid2", "Exceltopname.Auditor2ID", 29), //二次授权人id
////
////    CONFIRMPERSON2("confirmPerson2","Exceltopname.secondauthorizer",30),//二次授权人
////    CONFIRMPTYPE2("confirmType2","Exceltopname.seauthorizationtype",31),//二次授权方式
//
////    USERDEFINEDRID("userdefined_rid", "Exceltopname.diyrc",2001),//用户自定义RC
////    USERDEFINEDRC("userdefined_rc", "Exceltopname.diyrcexplanation",2002),//用户自定义RC说明
//
//
//    OILFEEDIFFERENTIAL("oilfeedifferential", "index.oilfeediffernetial", 65),//改签燃油差
//
//
//    //    CORPPRICE("corpPrice","Travelanalysis.lowest",75),//最低价
//
//
//    SERVICEPACKAGEFEE("servicepackage_fee", "Exceltopname.serpackagefee", 77),//增值服务包费
//
//
//    OCURRENCY("o_currency", "Exceltopname.orgcurrency", 78),//原币种(客户支付币种)
//    OEXCHANGERATE("o_exchangerate", "Exceltopname.currencyrate", 79),//汇率(客户支付币种汇率)
//    LOWFLIGHT("low_flight", "Exceltopname.lowpriceair", 274),//最低价航班号
//
//
//    PRODUCTCATEGORY("productcategory", "Exceltopname.producttype", 286),//产品类型
//
//    CARBONS("carbons", "Exceltopname.CarbonEmissions", 288),//合并碳排量
//    FLTMEDIANCARBONS("fltMedianCarbons", "index.carbonmedian", 288),//碳排中位数
//    FLTCARBONSAVE("fltCarbonSave", "index.carbonsavings", 288),//碳节省额
//
//
//    FLIGHTCONTINENT("flight_continent", "Exceltopname.aircontiner", 295),//航程洲际
//    TICKETWAYNAME("ticket_way_name", "Exceltopname.delivertype", 301),//配送方式
//    PASSENGENAME("passengerName", "Exceltopname.planepassenger", 81),//乘机人
//
//
//
//
//    DESTCONTINENT("dest_continent", "Exceltopname.destinationcontinent", 115),//目的地大洲
//    DESTCOUNTRY("dest_country", "Exceltopname.destinationcountry", 116),//目的地国家
//    DESTPROVINCE("dest_province", "Exceltopname.destinationprovince", 117),//目的地省份

//
//
//    CUSTOM_REFUND_REASON_CODE("custom_refund_reason_code", "index.refundrc", 141),//退票RC
//
//    CUSTOM_REFUND_REASON_CODE_DESC("custom_refund_reason_code_desc", "index.refundrcdetail", 141),//退票RC说明
//
//    HOTELORDERRC("hotel_order_rc", "Exceltopname.NoHtlOrderRC", 142),//机酒交叉RC
//    HOTELORDERRCDETAIL("hotel_order_rc_detail", "Exceltopname.NoHtlOrderRCDetail", 143),//机酒交叉RC说明
//
//
//    ISSHARED("isshared", "Exceltopname.isshared", 2500),//是否共享航班
//
//    REFUNDCUSTOMERSTATUS("refundcustomerstatus", "Exceltopname.refundcustomerstatus", 2505),//退款状态字段
//
//    NOTSELECTINSURANCEFEE("notselect_insurance_fee", "Exceltopname.notselect_insurance_fee", 2506),//用户取消勾选赠险，赠险对应服务费
//    COMPANYFEE("companyfee", "Exceltopname.companyfee", 2506),//应收款调整费用
//
//    DISCOUNT_REASON_CODE("discount_reason_code", "ComplianceMonitor.discountrc", 141),//折扣rc
//    CUSTOM_DISCOUNT_REASON("custom_discount_reason", "ComplianceMonitor.discountrcDesc", 141),//用户自定义折扣RC
//    COUNTOFPASSENGERFLIGHT("countofpassengerflight", "Exceltopname.countofpassengerflight", 55),//人程数
    ;

    FlightOrderDetailEnum(String code, String name, int order) {
        this.code = code;
        this.name = name;
        this.order = order;
    }

    private String code;
    private String name;
    private int order;

    public String getCode() {
        return code;
    }


    public String getName() {
        return name;
    }

    public int getOrder() {
        return order;
    }

    @Override
    public FlightOrderDetailEnum getEnum() {
        return this;
    }

}
