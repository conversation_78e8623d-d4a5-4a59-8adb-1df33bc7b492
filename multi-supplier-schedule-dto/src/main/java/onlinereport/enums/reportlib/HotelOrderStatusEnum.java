package onlinereport.enums.reportlib;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 酒店订单状态枚举
 *
 * <AUTHOR> din<PERSON><PERSON><PERSON>
 * @date : 2024/6/27 17:34
 * @since : 1.0
 */
@Getter
public enum HotelOrderStatusEnum {

    SI("SI", "提交中", 1),
    PW("PW", "待支付", 2),
    TW("TW", "待确认", 3),
    TA("TA", "已确认", 4),
    CA("CA", "已取消", 5),
    AW("AW", "待审批", 7),
    ED("ED", "已完成", 6);

    private final String type;
    private final String name;
    private final Integer node;

    HotelOrderStatusEnum(String type, String name, Integer node) {
        this.type = type;
        this.name = name;
        this.node = node;
    }

    public static HotelOrderStatusEnum getByType(String orderStatus) {
        return Arrays.stream(values()).filter((e) -> orderStatus.equals(e.getType())).findFirst().orElse(null);
    }

    // 新增的方法，将枚举转换为 Map
    public static Map<String, String> toMap() {
        Map<String, String> map = new HashMap<>();
        for (HotelOrderStatusEnum item : HotelOrderStatusEnum.values()) {
            map.put(item.getType(), item.getName());
        }
        return map;
    }
}
