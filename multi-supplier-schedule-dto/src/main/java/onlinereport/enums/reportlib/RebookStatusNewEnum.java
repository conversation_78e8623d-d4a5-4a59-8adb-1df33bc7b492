package onlinereport.enums.reportlib;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 改签状态枚举
 */
public enum RebookStatusNewEnum {
	/**
	 * 待审批
	 */
	W("W", "待审批"),
	/**
	 * 改签中
	 */
	U("U", "改签中"),
	/**
	 * 改签成功
	 */
	S("S", "改签成功"),
	/**
	 * 改签失败
	 */
	F("F", "改签失败"),
	/**
	 * 改签待支付
	 */
	PW("PW", "改签待支付"),

	/**
	 * 占座成功
	 */
	OS("OS", "占座成功"),
	/**
	 * 占座失败
	 */
	OF("OF", "占座失败"),

	/**
	 * 待出票
	 */
	TW("TW", "待出票"),

	;

	private String code;

	private String name;

	RebookStatusNewEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	/**
	 * 根据code转换枚举
	 */
	public static RebookStatusNewEnum getEnumByCode(String code) {
		RebookStatusNewEnum result = null;
		for (RebookStatusNewEnum item : RebookStatusNewEnum.values()) {
			if (item.getCode().equalsIgnoreCase(code)){
				result = item;
				break;
			}
		}
		return result;
	}

	// 新增的方法，将枚举转换为 Map
	public static Map<String, String> toMap() {
		Map<String, String> map = new HashMap<>();
		for (RebookStatusNewEnum item : RebookStatusNewEnum.values()) {
			map.put(item.getCode(), item.getName());
		}
		return map;
	}

}
