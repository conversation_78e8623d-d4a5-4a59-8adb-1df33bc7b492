package onlinereport.enums.reportlib;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/7 16:12
 * @description： 机票风险订单字段
 * @modified By：
 * @version: $
 */

public enum FlightRiskOrderFieldEnum {

    ORDER_ID("orderId", "TravelPosition.order", true, true),

//    WARNING_LEVEL("warningLevel", "Exceltopname.risklab", true, false),

    REASON_TYPE("reasonType", "RiskOrder.ReasonType", true, false),

    RISK_TYPE("riskType", "RiskOrder.ReasonDetail", true, false),

    ORDER_DATE("orderDate", "Exceltopname.date", true, false),

    ORDER_STATUS("orderStatus", "Exceltopname.orderstatus", true, false),

    TICKET_STATUS("ticketStatus", "Exceltopname.numberstatus", true, false),

//    SEGMENT_STATUS("segmentStatus", "Exceltopname.planestatus"),

    UID("uid", "Exceltopname.uidnumber", true, false),

    UID_NAME("uidName", "UserSurvey.Q1.Opt5", true, false),

    UID_ISVALID("uidIsvalid", "RiskOrder.CardholdStatus", true, false),

    ACCOUNT_ID("accountId", "App.subaccouont", false, false),

    JOURNEY_NO("journeyNo", "Exceltopname.linltravel", true, false),

    PASSENGER_NAME("passengerName", "Exceltopname.planepassenger", true, false),

    PASSENGER_ISVALID("passengerIsvalid", "RiskOrder.PassengerStatus2", true, false),

    CORPORATION_NAME("corporationName", "Public.confirmname", true, false),

    TICKET_NO("ticketNo", "Exceltopname.planecorpnumber", true, false),

    SEGMENT("segment", "Exceltopname.planecode", true, false),

    AIRLINE_NAME("airlineName", "SupplierMonitor.Airlines", true, false),

    FLIGHT_NO("flightNo", "Index.flightno", true, false),

    TAKEOFF_TIME("takeOffTime", "Exceltopname.takeofftime", true, false),
    DEPARTURE_PORT_NAME("departurePortName", "Exceltopname.takeoffairport", false, false),
    ARRIVAL_DATE_TIME("arrivalDateTime", "Index.arrivaltime", false, false),
    ARRIVAL_PORT_NAME("arrivalPortName", "Exceltopname.arrivalcity", false, false),

    QUANTITY("quantity", "Index.number", true, false),

    PRICE("price", "Index.netprice", true, false),

    DEPT1("dept1", "Exceltopname.depone", false, false),

    DEPT2("dept2", "Exceltopname.deptwo", false, false),

    DEPT3("dept3", "Exceltopname.depthree", false, false),

    DEPT4("dept4", "Exceltopname.depfour", false, false),

    DEPT5("dept5", "Exceltopname.depfive", false, false),

    DEPT6("dept6", "Exceltopname.depsix", false, false),

    DEPT7("dept7", "Exceltopname.depseven", false, false),

    DEPT8("dept8", "Exceltopname.depeight", false, false),

    DEPT9("dept9", "Exceltopname.depnight", false, false),

    DEPT10("dept10", "Exceltopname.depten", false, false),

    COST_CENTER1("costcenter1", "Exceltopname.costcenterone", false, false),

    COST_CENTER2("costcenter2", "Exceltopname.costcentertwo", false, false),

    COST_CENTER3("costcenter3", "Exceltopname.costcenterthree", false, false),

    COST_CENTER4("costcenter4", "Exceltopname.costcenterfour", false, false),

    COST_CENTER5("costcenter5", "Exceltopname.costcenterfive", false, false),

    COST_CENTER6("costcenter6", "Exceltopname.costcentersix", false, false),

    OPERATE_STATUS("operateStatus", "RiskOrder.OperateStatus", true, false),

    OPERATOR("operator", "RiskOrder.OperatePerson", true, false),

    OPERATOR_TIME("operatorTime", "RiskOrder.OperateTime", true, false),

    OPERATE_MARK("operatorMark", "RiskOrder.OperateNote", true, false),

    ;

    private String name;

    private String headerKey;

    private boolean isDefault;

    private boolean isMust;

    FlightRiskOrderFieldEnum(String name, String headerKey, boolean isDefault, boolean must) {
        this.name = name;
        this.headerKey = headerKey;
        this.isDefault = isDefault;
        this.isMust = must;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHeaderKey() {
        return headerKey;
    }

    public void setHeaderKey(String headerKey) {
        this.headerKey = headerKey;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public boolean isMust() {
        return isMust;
    }

    public void setMust(boolean must) {
        isMust = must;
    }
}
