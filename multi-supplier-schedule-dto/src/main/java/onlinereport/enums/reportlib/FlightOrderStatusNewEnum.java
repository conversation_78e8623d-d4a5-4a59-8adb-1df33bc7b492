package onlinereport.enums.reportlib;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 国内机票订单状态枚举
 */
@Getter
public enum FlightOrderStatusNewEnum {

    SI("SI", "提交中"),
    PW("PW", "待支付"),
    TW("TW", "待出票"),
    TI("TI", "出票中"),
    TA("TA", "已出票"),
    CA("CA", "已取消"),
    CI("CI", "取消中"),
    RP("RP", "部分退票"),
    EP("EP", "部分改签"),
    RA("RA", "全部退票"),
    EA("EA", "全部改签"),
    AW("AW", "待审批"),
    AR("AR", "审批拒绝"),
    TF("TF", "出票失败");

    private String type;

    private String name;

    FlightOrderStatusNewEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

//    public static FlightOrderStatusEnum getByType(String orderStatus) {
//        return Arrays.stream(values()).filter(e -> orderStatus.equals(e.getType())).findFirst().orElse(null);
//    }

    // 新增的方法，将枚举转换为 Map
    public static Map<String, String> toMap() {
        Map<String, String> map = new HashMap<>();
        for (FlightOrderStatusNewEnum item : FlightOrderStatusNewEnum.values()) {
            map.put(item.getType(), item.getName());
        }
        return map;
    }
}
