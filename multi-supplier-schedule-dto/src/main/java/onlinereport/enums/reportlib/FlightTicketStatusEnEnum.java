package onlinereport.enums.reportlib;


/**
 * @Description: 机票票号状态
 * <AUTHOR>
 * @Date 2022/5/11
 */
public enum FlightTicketStatusEnEnum {

    Unknown_0("0", "RiskOrder.FltTicketStatus0"),
    Unknown_1("-1",  "RiskOrder.FltTicketStatus0"),
    Unknown_2("-2", "RiskOrder.FltTicketStatus0"),
    UnUse("1",  "RiskOrder.FltTicketStatus1"),
    Used("2",  "RiskOrder.FltTicketStatus2"),
    CheckIn("3",  "RiskOrder.FltTicketStatus3"),
    OutGoing("4",  "RiskOrder.FltTicketStatus4"),
    Cancelled("5",  "RiskOrder.FltTicketStatus5"),
    Refunded("6",  "RiskOrder.FltTicketStatus6"),
    Changed("7",  "RiskOrder.FltTicketStatus7"),
    Pending("8",  "RiskOrder.FltTicketStatus8"),
    Control9("9",  "RiskOrder.FltTicketStatus9"),
    Control10("10",  "RiskOrder.FltTicketStatus10"),
    Disabled("11",  "RiskOrder.FltTicketStatus11");

    /**
     * 值
     */
    private String value;

    /**
     * shark key
     */
    private String sharkKey;

    FlightTicketStatusEnEnum(String value, String sharkKey) {
        this.value = value;
        this.sharkKey = sharkKey;
    }

    public String getValue() {
        return value;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public FlightTicketStatusEnEnum getEnum() {
        return this;
    }

    public static FlightTicketStatusEnEnum getEnumByCode(String code) {
        switch (code) {
            case "0":
                return FlightTicketStatusEnEnum.Unknown_0;
            case "-1":
                return FlightTicketStatusEnEnum.Unknown_1;
            case "-2":
                return FlightTicketStatusEnEnum.Unknown_2;
            case "1":
                return FlightTicketStatusEnEnum.UnUse;
            case "2":
                return FlightTicketStatusEnEnum.Used;
            case "3":
                return FlightTicketStatusEnEnum.CheckIn;
            case "4":
                return FlightTicketStatusEnEnum.OutGoing;
            case "5":
                return FlightTicketStatusEnEnum.Cancelled;
            case "6":
                return FlightTicketStatusEnEnum.Refunded;
            case "7":
                return FlightTicketStatusEnEnum.Changed;
            case "8":
                return FlightTicketStatusEnEnum.Pending;
            case "9":
                return FlightTicketStatusEnEnum.Control9;
            case "10":
                return FlightTicketStatusEnEnum.Control10;
            case "11":
                return FlightTicketStatusEnEnum.Disabled;
            default:
                return FlightTicketStatusEnEnum.Unknown_0;
        }
    }
}
