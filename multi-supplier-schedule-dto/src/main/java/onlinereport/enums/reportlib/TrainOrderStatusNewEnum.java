package onlinereport.enums.reportlib;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单状态
 * SI提交中、PW待支付、TW待出票、TI出票中（拆单1程已出票，1程未出票）、TA已出票、CA已取消、RP部分退票、EP部分改签、RA全部退票、EA全部改签
 */
public enum TrainOrderStatusNewEnum {
    /**
     * 占座中
     */
    OI("OI", "占座中"),
    /**
     * 占座成功
     */
    OS("OS", "占座成功"),
    /**
     * 占座失败
     */
    OF("OF", "占座失败"),
    /**
     * 提交中
     */
    SI("SI", "提交中"),
    /**
     * 待支付
     */
    PW("PW", "待支付"),
    /**
     * 支付成功
     */
    PS("PS", "支付成功"),
    /**
     * 待出票
     */
    TW("TW", "待出票"),
    /**
     * 已出票
     */
    TA("TA", "已出票"),
    /**
     * 已取消
     */
    CI("CI", "取消中"),
    /**
     * 已取消
     */
    CA("CA", "已取消"),
    /**
     * 部分退票
     */
    RP("RP", "部分退票"),
    /**
     * 部分改签
     */
    EP("EP", "部分改签"),
    /**
     * 全部退票
     */
    RA("RA", "全部退票"),
    /**
     * 全部改签
     */
    EA("EA", "全部改签"),

    /**
     * 出票失败
     */
    TF("TF", "出票失败"),
    ;

    private String type;

    private String name;

    TrainOrderStatusNewEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static TrainOrderStatusNewEnum getEnum(String type) {
        for (TrainOrderStatusNewEnum item : TrainOrderStatusNewEnum.values()) {
            if (item.getType().equalsIgnoreCase(type)) {
                return item;
            }
        }
        return null;
    }

    // 新增的方法，将枚举转换为 Map
    public static Map<String, String> toMap() {
        Map<String, String> map = new HashMap<>();
        for (TrainOrderStatusNewEnum item : TrainOrderStatusNewEnum.values()) {
            map.put(item.getType(), item.getName());
        }
        return map;
    }
}
