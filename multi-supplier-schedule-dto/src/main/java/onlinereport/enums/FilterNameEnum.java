package onlinereport.enums;

import org.apache.commons.lang.StringUtils;

/**
 * 筛选项
 */
public enum FilterNameEnum {
    //通用
    LANG("lang", 0, "语言"),
    CORP("corp", 1, "公司ID"),
    ACCOUNTID("accountid", 2, "主账户ID"),
    UID("uid", 3, "UID"),
    PREPAYTYPE("prepaytype", 4, "支付类型"),
    BOOKTYPE("booktype", 5, "预定渠道"),
    ISOVERTRAVELCONTROL("isovertravelcontrol", 6, "差旅标准"),
    STARTTIME("starttime", 7, "开始时间"),
    ENDTIME("endtime", 8, "结束时间"),
    STARTSHORTDATE("startShortDate", 7, "开始年月"),
    ENDSHORTDATE("endShortDate", 8, "结束年月"),
    DEPT1("dept1", 9, "部门1"),
    DEPT2("dept2", 10, "部门2"),
    DEPT3("dept3", 11, "部门3"),
    DEPT4("dept4", 12, "部门4"),
    DEPT5("dept5", 13, "部门5"),
    DEPT6("dept6", 14, "部门6"),
    COSTCENTER1("costcenter1", 15, "成本中心1"),
    COSTCENTER2("costcenter2", 16, "成本中心2"),
    COSTCENTER3("costcenter3", 17, "成本中心3"),
    COSTCENTER4("costcenter4", 18, "成本中心4"),
    COSTCENTER5("costcenter5", 19, "成本中心5"),
    COSTCENTER6("costcenter6", 20, "成本中心6"),
    ORDERTYPE("ordertype", 21, "订单类型（国际、国内、会员、协议）"),
    REPORTDEPTTYPE("reportdepttype", 22, "部门类型(部门1、部门2、成本中1...)"),
    LOGONUID("logonuid", 23, "登录UID"),
    ISDOWNLOAD("isdownload", 24, "是否下载"),
    MONTH("month", 25, "月"),
    QUARTER("quarter", 26, "季"),
    REPORTDEPTTYPE_FILTER_VALUE("reportdepttype_filter_value", 27, "分析维度过滤值"),
    ACITY("acity", 28, "到达城市/酒店所在城市"),
    BIZTYPE("bizType", 29, "产线类型（机票、酒店、火车、用车）"),

    WORKCITY("workcity", 209, "工作所在城市"),
    CUSTOMREPORTINFOID("customreportinfoid", 210, "自定义模板配置ID"),
    NEEDDATASOURCE("needdatasource", 211, "是否需要数据源"),

    //机票相关 100~199
    FLT_FLIGHTSTATUS("flt_flightstatus", 100, "航段状态"),
    //FLT_FLIGHTCLASS("flt_flightclass", 101, "航班类型"),
    FLT_REALCLASS("flt_realclass", 102, "物理舱位"),
    FLT_PASSENGERNAME("flt_passengername", 103, "乘机人"),
    FLT_AIRLINE("flt_airline", 104, "航空公司"),
    FLT_AIRROUTE("flt_airroute", 105, "航线"),
    FLT_DESTINATION("flt_destination", 106, "目的地"),
    FLT_AGREEMENTAIR("flt_agreementair", 107, "是否协议航空"),
    FLT_LOWPRICERC("flt_lowpricerc", 108, "低价RC"),
    FLT_CLASSICRC("flt_classicrc", 109, "舱等RC"),
    FLT_PRERC("flt_prerc", 110, "提前预订RC"),
    FLT_INTERCONTINENTALTPYE("flt_intercontinentaltpye", 111, "国际航班类型"),

    //酒店相关 200~299
    //HTL_ORDERTYPE("htl_ordertype", 200, "订单类型"),
    HTL_TYPE("htl_type", 201, "酒店类型"),
    HTL_ISMIXPAYMENT("htl_ismixpayment", 202, "是否随心订(混付)"),
    HTL_CITY("htl_city", 203, "酒店城市"),
    HTL_PROVINCE("htl_province", 204, "酒店省份"),
    HTL_COUNTRY("htl_country", 205, "酒店国家"),
    HTL_STAR("htl_star", 206, "酒店星级"),


    //火车票相关 300~399
    TRAIN_DEPARTURE_CITY("train_departure_city",300,"出发城市");
    //用车相关 400~499


    private String name;
    private int val;
    private String des;

    private FilterNameEnum(String name, int val, String des) {
        this.name = name;
        this.val = val;
        this.des = des;
    }

    /**
     * 判断值是否枚举值
     *
     * @param val
     * @return
     */
    public static boolean isFilterEnum(String val) {
        if (StringUtils.isEmpty(val)) {
            return false;
        }
        boolean isFilterEnum = true;
        try {
            FilterNameEnum.valueOf(val.trim().toUpperCase());
        } catch (Exception ex) {
            isFilterEnum = false;
        }
        return isFilterEnum;
    }

    public String getName() {
        return name;
    }

    public int getValue() {
        return val;
    }

    public String getDes() {
        return des;
    }

}
