package onlinereport.enums;

/**
 * @Description: 席位枚举
 * <AUTHOR>
 * @Date 2019/6/21
 */
public enum TrainSeatTypeEnum {
    TRAIN_SEAT_1ST_CLASS_SEAT("一等座","1st Class seat",5),
    TRAIN_SEAT_2ND_CLASS_SEAT("二等座","2nd Class seat",6),
    TRAIN_SEAT_PREMIER_SEAT("软座","Premier Seat",1),
    TRAIN_SEAT_BUSINESS_CLASS_SEAT("商务座","Business Class Seat",3),
    TRAIN_SEAT_HARD_SEAT("硬座","Hard seat",2),
    TRAIN_SEAT_HARD_SLEEPER("硬卧","Hard sleeper",8),
    TRAIN_SEAT_SLEEPER("动卧","Sleeper",14),
    TRAIN_SEAT_DELUXE_SOFT("特等座","Deluxe soft",4),
    TRAIN_SEAT_SOFT_SLEEPER("软卧","Soft sleeper",9),
    TRAIN_SEAT_STANDING("无座","Standing",10),
    TRAIN_SEAT_1ST_DOUBLE_SLEEPER("一等双软","1st C Double Sft Slp",17),
    TRAIN_SEAT_2ND_DOUBLE_SLEEPER("二等双软","2nd C Double Sft Slp",18),
    TRAIN_SEAT_1_BED_SOFT_SLEEPER("一人软包","Sft Compt for 1",13),
    TRAIN_SEAT_OTHERS("其他","Others",0),
    TRAIN_LIE_GENERATION("卧代二等座","train_lie_generation",0),
    TRAIN_SEAT_HIGH_GRADE_SOFT_SLEEPER("高级软卧","high-grate Soft sleeper",7),
    TRAIN_SEAT_1ND_ClASS_SOFT_SEAT("一等软座","1st Class Soft Seat",11),
    TRAIN_SEAT_2ND_ClASS_SOFT_SEAT("二等软座","2nd Class Soft Seat",12),
    TRAIN_SEAT_HIGH_GRADE_MOTORTRAIN_SLEEPER("高级动卧","high-grate motor train sleeper",16),

    ;

    private String name;
    private String nameEn;
    private int code;

    public static TrainSeatTypeEnum lookUp(String name){
        if(name == null){
            return TRAIN_SEAT_OTHERS;
        }
        for(TrainSeatTypeEnum e : TrainSeatTypeEnum.values()){
            if(e.getName().equalsIgnoreCase(name)){
                return e;
            }
        }
        return TRAIN_SEAT_OTHERS;
    }

    TrainSeatTypeEnum(String name,String nameEn,int code) {
        this.name = name;
        this.nameEn = nameEn;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getNameEn(){
        return nameEn;
    }

    public int getCode() {
        return code;
    }
}
