<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.schedule.pricecomparison.mapper.MbJobSupplierCompareTaskMapper">
  <resultMap id="BaseResultMap" type="com.corpgovernment.resource.schedule.pricecomparison.mysql.entity.MbJobSupplierCompareTask">
    <!--@mbg.generated-->
    <!--@Table mb_job_supplier_compare_task-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
    <result column="TASK_TYPE" jdbcType="VARCHAR" property="taskType" />
    <result column="TASK_STATUS" jdbcType="INTEGER" property="taskStatus" />
    <result column="CREATE_EID" jdbcType="VARCHAR" property="createEid" />
    <result column="CREATE_NAME" jdbcType="VARCHAR" property="createName" />
    <result column="COMPARE_DATA" jdbcType="VARCHAR" property="compareData" />
    <result column="REQUESTID" jdbcType="VARCHAR" property="requestid" />
    <result column="DATACHANGE_CREATETIME" jdbcType="TIMESTAMP" property="datachangeCreatetime" />
    <result column="DATACHANGE_LASTTIME" jdbcType="TIMESTAMP" property="datachangeLasttime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TASK_ID, TASK_TYPE, TASK_STATUS, CREATE_EID, CREATE_NAME, COMPARE_DATA, REQUESTID, 
    DATACHANGE_CREATETIME, DATACHANGE_LASTTIME
  </sql>

<!--auto generated by MybatisCodeHelper on 2025-08-14-->
  <insert id="insertList">
    INSERT INTO mb_job_supplier_compare_task(
    ID,
    TASK_ID,
    TASK_TYPE,
    TASK_STATUS,
    CREATE_EID,
    CREATE_NAME,
    COMPARE_DATA,
    REQUESTID
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.id,jdbcType=BIGINT},
      #{element.taskId,jdbcType=VARCHAR},
      #{element.taskType,jdbcType=VARCHAR},
      #{element.taskStatus,jdbcType=INTEGER},
      #{element.createEid,jdbcType=VARCHAR},
      #{element.createName,jdbcType=VARCHAR},
      #{element.compareData,jdbcType=VARCHAR},
      #{element.requestid,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <select id="selectByTaskTypeAndDatachangeCreatetime" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM mb_job_supplier_compare_task
    <where>
      LOWER(TASK_TYPE) = LOWER(#{taskType,jdbcType=VARCHAR})
      <if test="datachangeCreatetimeStart != null and datachangeCreatetimeEnd != null">
        AND DATACHANGE_CREATETIME BETWEEN #{datachangeCreatetimeStart,jdbcType=TIMESTAMP}
        AND #{datachangeCreatetimeEnd,jdbcType=TIMESTAMP}
      </if>
      <if test="datachangeCreatetimeStart != null and datachangeCreatetimeEnd == null">
        AND DATACHANGE_CREATETIME &gt;= #{datachangeCreatetimeStart,jdbcType=TIMESTAMP}
      </if>
      <if test="datachangeCreatetimeStart == null and datachangeCreatetimeEnd != null">
        AND DATACHANGE_CREATETIME &lt;= #{datachangeCreatetimeEnd,jdbcType=TIMESTAMP}
      </if>
     <if test="taskStatus != null">
        AND TASK_STATUS = #{taskStatus,jdbcType=INTEGER}
     </if>
    </where>
    order by id asc
    LIMIT #{offset}, #{pageSize}
  </select>

  <select id="countByTaskTypeAndDatachangeCreatetime" resultType="java.lang.Long">
    SELECT
    count(1)
    FROM mb_job_supplier_compare_task
    <where>
      LOWER(TASK_TYPE) = LOWER(#{taskType,jdbcType=VARCHAR})
      <if test="datachangeCreatetimeStart != null and datachangeCreatetimeEnd != null">
        AND DATACHANGE_CREATETIME BETWEEN #{datachangeCreatetimeStart,jdbcType=TIMESTAMP}
        AND #{datachangeCreatetimeEnd,jdbcType=TIMESTAMP}
      </if>
      <if test="datachangeCreatetimeStart != null and datachangeCreatetimeEnd == null">
        AND DATACHANGE_CREATETIME &gt;= #{datachangeCreatetimeStart,jdbcType=TIMESTAMP}
      </if>
      <if test="datachangeCreatetimeStart == null and datachangeCreatetimeEnd != null">
        AND DATACHANGE_CREATETIME &lt;= #{datachangeCreatetimeEnd,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>

<!--auto generated by MybatisCodeHelper on 2025-08-15-->
  <update id="updateTaskStatusById">
    update mb_job_supplier_compare_task
    set TASK_STATUS=#{updatedTaskStatus,jdbcType=INTEGER}
    where ID=#{id,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2025-08-15-->
  <select id="selectByTaskId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mb_job_supplier_compare_task
    where TASK_ID=#{taskId,jdbcType=VARCHAR}
  </select>
</mapper>