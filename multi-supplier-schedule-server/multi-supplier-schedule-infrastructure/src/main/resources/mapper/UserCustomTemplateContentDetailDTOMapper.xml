<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.schedule.usertemplate.mysql.mapper.UserCustomTemplateContentDetailDTOMapper">
  <resultMap id="BaseResultMap" type="com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateContentDetailDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="content_no" jdbcType="VARCHAR" property="contentNo" />
    <result column="flied_key" jdbcType="VARCHAR" property="fliedKey" />
    <result column="flied_name" jdbcType="VARCHAR" property="fliedName" />
    <result column="flied_value" jdbcType="VARCHAR" property="fliedValue" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="is_valid" jdbcType="BIT" property="isValid" />
    <result column="datachange_createtime" jdbcType="TIMESTAMP" property="datachangeCreatetime" />
    <result column="datachange_lasttime" jdbcType="TIMESTAMP" property="datachangeLasttime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, content_no, flied_key, flied_name, flied_value, sort, is_valid, datachange_createtime,
    datachange_lasttime
  </sql>

  <delete id="deleteByContentNo">
    delete from reportlib_user_custom_template_content_detail
    where content_no = #{contentNo,jdbcType=BIGINT}
  </delete>

  <select id="selectAllByContentNo" resultMap="BaseResultMap">
    /*appid: 100027258  author:wujiaxing reviewcode: LC202412100124*/
    select
    <include refid="Base_Column_List" />
    from reportlib_user_custom_template_content_detail
    where is_valid=1 and content_no = #{contentNo,jdbcType=VARCHAR}
  </select>
  <select id="selectByContentNolist" resultMap="BaseResultMap">
    /*appid: 100027258  author:wujiaxing reviewcode: LC202412100126*/
    select <include refid="Base_Column_List" />
    from reportlib_user_custom_template_content_detail
    where is_valid=1
    <if test="contentNoList != null and contentNoList.size() > 0">
      and content_no in
      <foreach collection="contentNoList" item="contentNo" open="(" separator="," close=")">
        #{contentNo}
      </foreach>
    </if>
  </select>

  <insert id="insertSelective" parameterType="com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateContentDetailDo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into reportlib_user_custom_template_content_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
        content_no,
        is_valid,
      <if test="fliedKey != null">
        flied_key,
      </if>
      <if test="fliedName != null">
        flied_name,
      </if>
      <if test="fliedValue != null">
        flied_value,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="datachangeCreatetime != null">
        datachange_createtime,
      </if>
      <if test="datachangeLasttime != null">
        datachange_lasttime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
        #{contentNo,jdbcType=VARCHAR},
        1,
      <if test="fliedKey != null">
        #{fliedKey,jdbcType=VARCHAR},
      </if>
      <if test="fliedName != null">
        #{fliedName,jdbcType=VARCHAR},
      </if>
      <if test="fliedValue != null">
        #{fliedValue,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="datachangeCreatetime != null">
          #{datachangeCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="datachangeLasttime != null">
          #{datachangeLasttime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <insert id="insertBatchSelective" parameterType="java.util.List">
    insert into reportlib_user_custom_template_content_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      content_no,
      <if test="list[0].fliedKey != null">flied_key,</if>
      <if test="list[0].fliedName != null">flied_name,</if>
      <if test="list[0].fliedValue != null">flied_value,</if>
      <if test="list[0].sort != null">sort,</if>
      is_valid,
      <if test="list[0].datachangeCreatetime != null">datachange_createtime,</if>
      <if test="list[0].datachangeLasttime != null">datachange_lasttime,</if>
    </trim>
    values
    <foreach collection="list" item="item" index="index" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.contentNo,jdbcType=VARCHAR},
        <if test="item.fliedKey != null">#{item.fliedKey,jdbcType=VARCHAR},</if>
        <if test="item.fliedName != null">#{item.fliedName,jdbcType=VARCHAR},</if>
        <if test="item.fliedValue != null">#{item.fliedValue,jdbcType=VARCHAR},</if>
        <if test="item.sort != null">#{item.sort,jdbcType=INTEGER},</if>
        1,
        <if test="item.datachangeCreatetime != null">#{item.datachangeCreatetime,jdbcType=TIMESTAMP},</if>
        <if test="item.datachangeLasttime != null">#{item.datachangeLasttime,jdbcType=TIMESTAMP},</if>
      </trim>
    </foreach>
  </insert>

  <update id="updateByContentNoSelective" parameterType="com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateContentDetailDo">
    update reportlib_user_custom_template_content_detail
    <set>
      <if test="fliedKey != null">
        flied_key = #{fliedKey,jdbcType=VARCHAR},
      </if>
      <if test="fliedName != null">
        flied_name = #{fliedName,jdbcType=VARCHAR},
      </if>
      <if test="fliedValue != null">
        flied_value = #{fliedValue,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=BIT},
      </if>
      <if test="datachangeCreatetime != null">
        datachange_createtime = #{datachangeCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="datachangeLasttime != null">
        datachange_lasttime = #{datachangeLasttime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where content_no = #{contentNo,jdbcType=VARCHAR}
  </update>

</mapper>