<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.schedule.tenant.mysql.mapper.TenantInfoMapper">

    <resultMap id="TenantInfoResultMap" type="com.corpgovernment.resource.schedule.tenant.mysql.entity.TenantInfoDo">
        <id property="id" column="id" />
        <result property="tenantName" column="tenant_name" />
        <result property="stdIndustry1" column="std_industry1" />
        <result property="stdIndustry2" column="std_industry2" />
        <result property="startTime" column="start_time" />
        <result property="remark" column="remark" />
        <result property="deleteTime" column="delete_time" />
        <result property="tenantId" column="tenant_id" />
        <result property="datachangeCreatetime" column="datachange_createtime" />
        <result property="datachangeLasttime" column="datachange_lasttime" />
    </resultMap>

    <select id="selectTenantInfoByTenantId" resultMap="TenantInfoResultMap">
        SELECT * FROM mb_tenant_info
    </select>

</mapper>