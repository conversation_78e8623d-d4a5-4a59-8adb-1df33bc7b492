<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.schedule.usertemplate.mysql.mapper.UserCustomTemplateDTOMapper">
    <resultMap id="BaseResultMap"
               type="com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateDo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="template_no" jdbcType="VARCHAR" property="templateNo"/>
        <result column="template_name" jdbcType="VARCHAR" property="templateName"/>
        <result column="uid" jdbcType="VARCHAR" property="uid"/>
        <result column="report_key" jdbcType="VARCHAR" property="reportKey"/>
        <result column="custom_column_no" jdbcType="VARCHAR" property="customColumnNo"/>
        <result column="custom_condition_no" jdbcType="VARCHAR" property="customConditionNo"/>
        <result column="datachange_createtime" jdbcType="TIMESTAMP" property="datachangeCreatetime"/>
        <result column="datachange_lasttime" jdbcType="TIMESTAMP" property="datachangeLasttime"/>
        <result column="is_valid" jdbcType="BIT" property="isValid"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, template_no, template_name, uid, report_key, custom_column_no, custom_condition_no,
        datachange_createtime, datachange_lasttime, is_valid
    </sql>


    <select id="selectByUidAndTemplateNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from reportlib_user_custom_template
        where is_valid = 1
        <if test="uid != null and uid != ''">
            and uid = #{uid,jdbcType=VARCHAR}
        </if>
        <if test="reportKey != null and reportKey != ''">
            and report_key = #{reportKey,jdbcType=VARCHAR}
        </if>
        <if test="templateNo != null and templateNo != ''">
            and template_no = #{templateNo,jdbcType=VARCHAR}
        </if>
        order by datachange_lasttime desc
    </select>

    <select id="countByTemplateNo" resultType="java.lang.Integer">
        select count(1)
        from reportlib_user_custom_template
        where is_valid = 1
        and template_no = #{templateNo,jdbcType=VARCHAR}
    </select>

    <select id="countByTemplateNameAndUid" resultType="java.lang.Integer">
        select count(1)
        from reportlib_user_custom_template
        where is_valid = 1
        and template_name = #{templateName,jdbcType=VARCHAR}
        and uid = #{uid,jdbcType=VARCHAR}
        and report_key = #{reportKey,jdbcType=VARCHAR}
    </select>


    <update id="deleteByTemplateNo">
        update reportlib_user_custom_template set is_valid = 0
        where template_no = #{templateNo,jdbcType=BIGINT}
    </update>


    <update id="deleteBatchTemplateNos">
        update reportlib_user_custom_template set is_valid = 0
        where template_no in
        <foreach collection="templateNos" item="templateNo" open="(" separator="," close=")">
            #{templateNo}
        </foreach>
    </update>

    <insert id="insertSelective"
            parameterType="com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateDo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into reportlib_user_custom_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            template_no,
            <if test="templateName != null">
                template_name,
            </if>
            uid,
            <if test="reportKey != null">
                report_key,
            </if>
            <if test="customColumnNo != null">
                custom_column_no,
            </if>
            <if test="customConditionNo != null">
                custom_condition_no,
            </if>
            <if test="datachangeCreatetime != null">
                datachange_createtime,
            </if>
            <if test="datachangeLasttime != null">
                datachange_lasttime,
            </if>
            is_valid,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{templateNo,jdbcType=VARCHAR},
            <if test="templateName != null">
                #{templateName,jdbcType=VARCHAR},
            </if>
            #{uid,jdbcType=VARCHAR},
            <if test="reportKey != null">
                #{reportKey,jdbcType=VARCHAR},
            </if>
            <if test="customColumnNo != null">
                #{customColumnNo,jdbcType=VARCHAR},
            </if>
            <if test="customConditionNo != null">
                #{customConditionNo,jdbcType=VARCHAR},
            </if>
            <if test="datachangeCreatetime != null">
                #{datachangeCreatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="datachangeLasttime != null">
                #{datachangeLasttime,jdbcType=TIMESTAMP},
            </if>
            1,
        </trim>
    </insert>

    <update id="updateByTemplateNoSelective"
            parameterType="com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateDo">
        update reportlib_user_custom_template
        <set>
            <if test="templateName != null">
                template_name = #{templateName,jdbcType=VARCHAR},
            </if>
            <if test="reportKey != null">
                report_key = #{reportKey,jdbcType=VARCHAR},
            </if>
            <if test="customColumnNo != null">
                custom_column_no = #{customColumnNo,jdbcType=VARCHAR},
            </if>
            <if test="customConditionNo != null">
                custom_condition_no = #{customConditionNo,jdbcType=VARCHAR},
            </if>
            <if test="datachangeCreatetime != null">
                datachange_createtime = #{datachangeCreatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="datachangeLasttime != null">
                datachange_lasttime = #{datachangeLasttime,jdbcType=TIMESTAMP},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=BIT},
            </if>
        </set>
        where template_no = #{templateNo,jdbcType=VARCHAR}
    </update>

</mapper>