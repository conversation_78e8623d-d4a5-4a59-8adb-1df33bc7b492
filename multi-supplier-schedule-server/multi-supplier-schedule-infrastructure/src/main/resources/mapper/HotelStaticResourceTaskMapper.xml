<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.schedule.hotel.mysql.mapper.HotelStaticResourceTaskMapper">

    <update id="truncateTable">
        TRUNCATE table `ms_hotel_static_resource_task`;
    </update>

    <insert id="batchSave" parameterType="java.util.List">
        INSERT INTO ms_hotel_static_resource_task
        (supplier_code, hotel_id_list, status)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.supplierCode}, #{item.hotelIdList}, #{item.status})
        </foreach>
    </insert>

    <update id="resetFailTask">
        update ms_hotel_static_resource_task set status = 0, fail_count = fail_count + 1 where status = 3 and fail_count <![CDATA[ < ]]> #{maxFailCount}
    </update>

</mapper>