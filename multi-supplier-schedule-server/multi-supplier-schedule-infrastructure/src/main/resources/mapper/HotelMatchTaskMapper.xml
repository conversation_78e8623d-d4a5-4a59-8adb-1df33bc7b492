<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.schedule.hotel.mysql.mapper.HotelMatchTaskMapper">

    <update id="truncateTable">
        TRUNCATE table `ms_hotel_match_task`;
    </update>

    <insert id="batchSave" parameterType="java.util.List">
        INSERT INTO ms_hotel_match_task
        (supplier_code, hotel_id, status)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.supplierCode}, #{item.hotelId}, #{item.status})
        </foreach>
    </insert>

    <update id="resetFailTask">
        update ms_hotel_match_task set status = 0, fail_count = fail_count + 1 where status = 3 and fail_count <![CDATA[ < ]]> #{maxFailCount}
    </update>

</mapper>