<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.schedule.hotelaudit.mysql.mapper.IHotelInfoMapper">

    <select id="selectHotelInfoByOrderIdList"
            resultType="com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.HotelInfoDo">
        select *
        from ho_hotel
        where order_id in
        <foreach collection="orderIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectHotelIntlInfoByOrderIdList"
            resultType="com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.HotelInfoDo">
        select *
        from hio_hotel
        where order_id in
        <foreach collection="orderIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>