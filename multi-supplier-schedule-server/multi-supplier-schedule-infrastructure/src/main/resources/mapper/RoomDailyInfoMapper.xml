<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.schedule.hotelaudit.mysql.mapper.RoomDailyInfoMapper">

    <select id="listHotelRoomDailyInfo"
            resultType="com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.RoomDailyInfoDo">
        select *
        from ho_room_daily_info
        where order_id in
        <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")" index="">
            #{orderId}
        </foreach>
    </select>

    <select id="listHotelIntlRoomDailyInfo"
            resultType="com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.RoomDailyInfoDo">
        select *
        from hio_room_daily_info
        where order_id in
        <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")" index="">
            #{orderId}
        </foreach>
    </select>

</mapper>