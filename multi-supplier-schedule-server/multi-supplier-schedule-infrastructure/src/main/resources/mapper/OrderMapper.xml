<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.corpgovernment.resource.schedule.hotelaudit.mysql.mapper.OrderMapper">

    <select id="listHotelOrderDoByTimeRange"
            resultType="com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.OrderDo">
        select *
        from ho_order
        where order_date &gt;= #{startOrderTime}
        and order_date &lt; #{endOrderTime}
    </select>

    <select id="listHotelOrderDoByOrderIdList"
            resultType="com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.OrderDo">
        select *
        from ho_order
        where order_id in
        <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")" index="">
            #{orderId}
        </foreach>
    </select>

    <select id="listHotelIntlOrderDoByTimeRange"
            resultType="com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.OrderDo">
        select *
        from hio_order
        where order_date &gt;= #{startOrderTime}
          and order_date &lt; #{endOrderTime}
    </select>

    <select id="listHotelIntlOrderDoByOrderIdList"
            resultType="com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.OrderDo">
        select *
        from hio_order
        where order_id in
        <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")" index="">
            #{orderId}
        </foreach>
    </select>

</mapper>