package com.corpgovernment.resource.schedule.onlinereport.clickhouse;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportOverviewTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportOverviewTrendJPDTO;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.SearchDeptAndCostcneterDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/5 13:38 @description：
 * @modified By：
 * @version: $
 */
@Repository
@Slf4j
public class OnlineReportOverviewTrendStarRocksDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "OnlineReportOverviewTrendDao";
    private static final HashMap<String, String> CHAIN_DURATION = new HashMap<String, String>() {
        {
            put("month", "1");
            put("quarter", "3");
            put("half", "6");
        }
    };

    private static final String JOIN_DATE_CONDITION_TEMPLATE =
            "" + "subStr(CAST(add_Months(cast({type} as date), {duration}) AS STRING), 1, 10)";

    private static final String OVERVIEW_SQL_TEMPLATE = "SELECT \n"
            + "  if(crt.date != '', crt.date, if(chain.date != '', {join_date_chain}, {join_date_yoy})) as date,\n"
            + "  if(crt.dim != '', crt.dim, if(chain.dim != '', chain.dim, yoy.dim)) as dim,\n" +
            // 销售额环比
            "  coalesce(round(CASE WHEN coalesce(chain.totalAmount, 0) != 0 THEN 1.0 * (coalesce(crt.totalAmount, 0) - coalesce(chain.totalAmount, 0)) " +
            "/ abs(coalesce(chain.totalAmount, 0)) END * 100, 2), 0) AS amountTotalChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.fltAmount, 0) != 0 THEN 1.0 * (coalesce(crt.fltAmount, 0) - coalesce(chain.fltAmount, 0)) " +
            "/ abs(coalesce(chain.fltAmount, 0)) END * 100, 2), 0) AS amountFltChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.htlAmount, 0) != 0 THEN 1.0 * (coalesce(crt.htlAmount, 0) - coalesce(chain.htlAmount, 0)) " +
            "/ abs(coalesce(chain.htlAmount, 0)) END * 100, 2), 0) AS amountHtlChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.trainAmount, 0) != 0 THEN 1.0 * (coalesce(crt.trainAmount, 0) - coalesce(chain.trainAmount, 0)) " +
            "/ abs(coalesce(chain.trainAmount, 0)) END * 100, 2), 0) AS amountTrainChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.carAmount, 0) != 0 THEN 1.0 * (coalesce(crt.carAmount, 0) - coalesce(chain.carAmount, 0)) " +
            "/ abs(coalesce(chain.carAmount, 0)) END * 100, 2), 0) AS amountCarChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.busAmount, 0) != 0 THEN 1.0 * (coalesce(crt.busAmount, 0) - coalesce(chain.busAmount, 0)) " +
            "/ abs(coalesce(chain.busAmount, 0)) END * 100, 2), 0) AS amountBusChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.addAmount, 0) != 0 THEN 1.0 * (coalesce(crt.addAmount, 0) - coalesce(chain.addAmount, 0)) " +
            "/ abs(coalesce(chain.addAmount, 0)) END * 100, 2), 0) AS amountAddChain,\n"
            +
            // 行程数 环比
            "  coalesce(round(CASE WHEN coalesce(chain.totalQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.totalQuantity, 0) - coalesce(chain.totalQuantity, 0)) " +
            "/ abs(coalesce(chain.totalQuantity, 0)) END * 100, 2), 0) as quantityTotalChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.fltQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.fltQuantity, 0) - coalesce(chain.fltQuantity, 0)) " +
            "/ abs(coalesce(chain.fltQuantity, 0)) END * 100, 2), 0) as quantityFltChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.htlQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.htlQuantity, 0) - coalesce(chain.htlQuantity, 0)) " +
            "/ abs(coalesce(chain.htlQuantity, 0)) END * 100, 2), 0) as quantityHtlChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.trainQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.trainQuantity, 0) - coalesce(chain.trainQuantity, 0)) " +
            "/ abs(coalesce(chain.trainQuantity, 0)) END * 100, 2), 0) as quantityTrainChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.carQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.carQuantity, 0) - coalesce(chain.carQuantity, 0)) " +
            "/ abs(coalesce(chain.carQuantity, 0)) END * 100, 2), 0) as quantityCarChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.busQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.busQuantity, 0) - coalesce(chain.busQuantity, 0)) " +
            "/ abs(coalesce(chain.busQuantity, 0)) END * 100, 2), 0) as quantityBusChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.addQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.addQuantity, 0) - coalesce(chain.addQuantity, 0)) " +
            "/ abs(coalesce(chain.addQuantity, 0)) END * 100, 2), 0) as quantityAddChain,\n"
            +
            // 销售额同比
            "  coalesce(round(CASE WHEN coalesce(yoy.totalAmount, 0) != 0 THEN 1.0 * (coalesce(crt.totalAmount, 0) - coalesce(yoy.totalAmount, 0)) " +
            "/ abs(coalesce(yoy.totalAmount, 0)) END * 100, 2), 0) AS amountTotalYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.fltAmount, 0) != 0 THEN 1.0 * (coalesce(crt.fltAmount, 0) - coalesce(yoy.fltAmount, 0)) " +
            "/ abs(coalesce(yoy.fltAmount, 0)) END * 100, 2), 0) AS amountFltYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.htlAmount, 0) != 0 THEN 1.0 * (coalesce(crt.htlAmount, 0) - coalesce(yoy.htlAmount, 0)) " +
            "/ abs(coalesce(yoy.htlAmount, 0)) END * 100, 2), 0) AS amountHtlYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.trainAmount, 0) != 0 THEN 1.0 * (coalesce(crt.trainAmount, 0) - coalesce(yoy.trainAmount, 0)) " +
            "/ abs(coalesce(yoy.trainAmount, 0)) END * 100, 2), 0) AS amountTrainYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.carAmount, 0) != 0 THEN 1.0 * (coalesce(crt.carAmount, 0) - coalesce(yoy.carAmount, 0)) " +
            "/ abs(coalesce(yoy.carAmount, 0)) END * 100, 2), 0) AS amountCarYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.busAmount, 0) != 0 THEN 1.0 * (coalesce(crt.busAmount, 0) - coalesce(yoy.busAmount, 0)) " +
            "/ abs(coalesce(yoy.busAmount, 0)) END * 100, 2), 0) AS amountBusYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.addAmount, 0) != 0 THEN 1.0 * (coalesce(crt.addAmount, 0) - coalesce(yoy.addAmount, 0)) " +
            "/ abs(coalesce(yoy.addAmount, 0)) END * 100, 2), 0) AS amountAddYoy,\n"
            +
            // 行程数同比
            "  coalesce(round(CASE WHEN coalesce(yoy.totalQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.totalQuantity, 0) - coalesce(yoy.totalQuantity, 0)) " +
            "/ abs(coalesce(yoy.totalQuantity, 0)) END * 100, 2), 0) as quantityTotalYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.fltQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.fltQuantity, 0) - coalesce(yoy.fltQuantity, 0)) " +
            "/ abs(coalesce(yoy.fltQuantity, 0)) END * 100, 2), 0) as quantityFltYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.htlQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.htlQuantity, 0) - coalesce(yoy.htlQuantity, 0)) " +
            "/ abs(coalesce(yoy.htlQuantity, 0)) END * 100, 2), 0) as quantityHtlYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.trainQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.trainQuantity, 0) - coalesce(yoy.trainQuantity, 0)) " +
            "/ abs(coalesce(yoy.trainQuantity, 0)) END * 100, 2), 0) as quantityTrainYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.carQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.carQuantity, 0) - coalesce(yoy.carQuantity, 0)) " +
            "/ abs(coalesce(yoy.carQuantity, 0)) END * 100, 2), 0) as quantityCarYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.busQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.busQuantity, 0) - coalesce(yoy.busQuantity, 0)) " +
            "/ abs(coalesce(yoy.busQuantity, 0)) END * 100, 2), 0) as quantityBusYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.addQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.addQuantity, 0) - coalesce(yoy.addQuantity, 0)) " +
            "/ abs(coalesce(yoy.addQuantity, 0)) END * 100, 2), 0) as quantityAddYoy,\n"
            + "  coalesce(crt.totalAmount, 0) as totalAmount,\n" + "  coalesce(crt.fltAmount, 0) as fltAmount,\n"
            + "  coalesce(crt.htlAmount, 0) as htlAmount,\n" + "  coalesce(crt.trainAmount, 0) as trainAmount,\n"
            + "  coalesce(crt.carAmount, 0) as carAmount,\n"
            + "  coalesce(crt.busAmount, 0) as busAmount,\n"
            + "  coalesce(crt.addAmount, 0) as addAmount,\n"
            + "  coalesce(crt.totalQuantity, 0) as totalQuantity,\n"
            + "  coalesce(crt.fltQuantity, 0) as fltQuantity,\n"
            + "  coalesce(crt.htlQuantity, 0) as htlQuantity,\n"
            + "  coalesce(crt.trainQuantity, 0) as trainQuantity,\n"
            + "  coalesce(crt.carQuantity, 0) as carQuantity,\n"
            + "  coalesce(crt.busQuantity, 0) as busQuantity,\n"
            + "  coalesce(crt.addQuantity, 0) as addQuantity\n"
            + "  FROM ({current_sql}) crt\n"
            + "FULL JOIN ({chain_sql}) chain on crt.date = {join_date_chain} and crt.dim = chain.dim\n"
            + "FULL JOIN ({yoy_sql}) yoy on crt.date = {join_date_yoy} and crt.dim = yoy.dim";

    private static final String OVERVIEW_SQL_TEMPLATE_ORDER_NUM = "SELECT \n"
            + "  if(crt.date != '', crt.date, if(chain.date != '', {join_date_chain}, {join_date_yoy})) as date,\n"
            + "  if(crt.dim != '', crt.dim, if(chain.dim != '', chain.dim, yoy.dim)) as dim,\n" +

            // 行程数 环比
            "  coalesce(round(CASE WHEN coalesce(chain.totalQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.totalQuantity, 0) - coalesce(chain.totalQuantity, 0)) " +
            "/ abs(coalesce(chain.totalQuantity, 0)) END * 100, 2), 0) as quantityTotalChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.fltQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.fltQuantity, 0) - coalesce(chain.fltQuantity, 0)) " +
            "/ abs(coalesce(chain.fltQuantity, 0)) END * 100, 2), 0) as quantityFltChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.htlQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.htlQuantity, 0) - coalesce(chain.htlQuantity, 0)) " +
            "/ abs(coalesce(chain.htlQuantity, 0)) END * 100, 2), 0) as quantityHtlChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.trainQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.trainQuantity, 0) - coalesce(chain.trainQuantity, 0)) " +
            "/ abs(coalesce(chain.trainQuantity, 0)) END * 100, 2), 0) as quantityTrainChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.carQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.carQuantity, 0) - coalesce(chain.carQuantity, 0)) " +
            "/ abs(coalesce(chain.carQuantity, 0)) END * 100, 2), 0) as quantityCarChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.busQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.busQuantity, 0) - coalesce(chain.busQuantity, 0)) " +
            "/ abs(coalesce(chain.busQuantity, 0)) END * 100, 2), 0) as quantityBusChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.addQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.addQuantity, 0) - coalesce(chain.addQuantity, 0)) " +
            "/ abs(coalesce(chain.addQuantity, 0)) END * 100, 2), 0) as quantityAddChain,\n"
            +

            // 行程数同比
            "  coalesce(round(CASE WHEN coalesce(yoy.totalQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.totalQuantity, 0) - coalesce(yoy.totalQuantity, 0)) " +
            "/ abs(coalesce(yoy.totalQuantity, 0)) END * 100, 2), 0) as quantityTotalYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.fltQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.fltQuantity, 0) - coalesce(yoy.fltQuantity, 0)) " +
            "/ abs(coalesce(yoy.fltQuantity, 0)) END * 100, 2), 0) as quantityFltYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.htlQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.htlQuantity, 0) - coalesce(yoy.htlQuantity, 0)) " +
            "/ abs(coalesce(yoy.htlQuantity, 0)) END * 100, 2), 0) as quantityHtlYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.trainQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.trainQuantity, 0) - coalesce(yoy.trainQuantity, 0)) " +
            "/ abs(coalesce(yoy.trainQuantity, 0)) END * 100, 2), 0) as quantityTrainYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.carQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.carQuantity, 0) - coalesce(yoy.carQuantity, 0)) " +
            "/ abs(coalesce(yoy.carQuantity, 0)) END * 100, 2), 0) as quantityCarYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.busQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.busQuantity, 0) - coalesce(yoy.busQuantity, 0)) " +
            "/ abs(coalesce(yoy.busQuantity, 0)) END * 100, 2), 0) as quantityBusYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.addQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.addQuantity, 0) - coalesce(yoy.addQuantity, 0)) " +
            "/ abs(coalesce(yoy.addQuantity, 0)) END * 100, 2), 0) as quantityAddYoy,\n"

            + "  coalesce(crt.totalQuantity, 0) as totalQuantity,\n"
            + "  coalesce(crt.fltQuantity, 0) as fltQuantity,\n"
            + "  coalesce(crt.htlQuantity, 0) as htlQuantity,\n"
            + "  coalesce(crt.trainQuantity, 0) as trainQuantity,\n"
            + "  coalesce(crt.carQuantity, 0) as carQuantity,\n"
            + "  coalesce(crt.busQuantity, 0) as busQuantity,\n"
            + "  coalesce(crt.addQuantity, 0) as addQuantity\n"
            + "  FROM ({current_sql}) crt\n"
            + "FULL JOIN ({chain_sql}) chain on crt.date = {join_date_chain} and crt.dim = chain.dim\n"
            + "FULL JOIN ({yoy_sql}) yoy on crt.date = {join_date_yoy} and crt.dim = yoy.dim";

    private static final String OVERVIEW_TRIP_SQL_TEMPLATE = "SELECT \n"
            + "  if(crt.date != '', crt.date, if(chain.date != '', {join_date_chain}, {join_date_yoy})) as date,\n"
            + "  if(crt.dim != '', crt.dim, if(chain.dim != '', chain.dim, yoy.dim)) as dim,\n" +
            // 行程数 环比
            "  coalesce(round(CASE WHEN coalesce(chain.totalQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.totalQuantity, 0) - coalesce(chain.totalQuantity, 0)) " +
            "/ abs(coalesce(chain.totalQuantity, 0)) END * 100, 2), 0) as quantityTotalChain,\n"
            +
            // 行程数同比
            "  coalesce(round(CASE WHEN coalesce(yoy.totalQuantity, 0) != 0 THEN 1.0 * (coalesce(crt.totalQuantity, 0) - coalesce(yoy.totalQuantity, 0)) " +
            "/ abs(coalesce(yoy.totalQuantity, 0)) END * 100, 2), 0) as quantityTotalYoy,\n"
            +
            // 行程数原值
            "  coalesce(crt.totalQuantity, 0) as totalQuantity\n" + "FROM ({current_sql}) crt\n"
            + "FULL JOIN ({chain_sql}) chain on crt.date = {join_date_chain} and crt.dim = chain.dim\n"
            + "FULL JOIN ({yoy_sql}) yoy on crt.date = {join_date_yoy} and crt.dim = yoy.dim";

    /**
     * 查询 在线报告概况-订单数趋势数据 - 概览 （bu + 字段名 -> 数据聚合维度 日期聚合 -> 数据聚合粒度 同环比)
     *
     * @param request
     * @return
     * @throws Exception
     */
    public List<OnlineReportOverviewTrendDTO> queryOnlineReportCurrentOverviewTrendOrderNum(OnlineTrendRequestDto request)
            throws Exception {

        String startTime = request.getStartTime();
        String endTime = request.getEndTime();
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dim = "''";
        if (request.getDateDimension().equals(QueryReportAggDateDimensionEnum.day.toString())
                || request.getDateDimension().equals(QueryReportAggDateDimensionEnum.week.toString())) {
            queryAccOverviewTripTrendOrderNumSubProductSql(request, sqlBuilder, parmList, dim);
        } else {
            StringBuilder currentsqlBuilder = new StringBuilder();
            List<Object> currentparmList = new ArrayList<>();
            queryAccOverviewTripTrendOrderNumSubProductSql(request, currentsqlBuilder, currentparmList, dim);

            StringBuilder chainsqlBuilder = new StringBuilder();
            List<Object> chainparmList = new ArrayList<>();
            request.setStartTime(request.getChainStartTime());
            request.setEndTime(request.getChainEndTime());
            request.getBaseQueryCondition().setStartTime(request.getChainStartTime());
            request.getBaseQueryCondition().setEndTime(request.getChainEndTime());
            queryAccOverviewTripTrendOrderNumSubProductSql(request, chainsqlBuilder, chainparmList, dim);

            StringBuilder yoysqlBuilder = new StringBuilder();
            List<Object> yoyparmList = new ArrayList<>();
            request.setStartTime(request.getYoyStartTime());
            request.setEndTime(request.getYoyEndTime());
            request.getBaseQueryCondition().setStartTime(request.getYoyStartTime());
            request.getBaseQueryCondition().setEndTime(request.getYoyEndTime());
            queryAccOverviewTripTrendOrderNumSubProductSql(request, yoysqlBuilder, yoyparmList, dim);

            parmList.addAll(currentparmList);
            parmList.addAll(chainparmList);
            parmList.addAll(yoyparmList);

            String overviewSql = StringUtils.replace(OVERVIEW_SQL_TEMPLATE_ORDER_NUM, "{current_sql}", currentsqlBuilder.toString());
            overviewSql = StringUtils.replace(overviewSql, "{chain_sql}", chainsqlBuilder.toString());
            overviewSql = StringUtils.replace(overviewSql, "{yoy_sql}", yoysqlBuilder.toString());

            String chainJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "chain.date");
            chainJoin = StringUtils.replace(chainJoin, "{duration}", CHAIN_DURATION.get(request.getDateDimension()));
            String yoyJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "yoy.date");
            yoyJoin = StringUtils.replace(yoyJoin, "{duration}", String.valueOf(request.getYoyDuration()));

            overviewSql = StringUtils.replace(overviewSql, "{join_date_chain}", chainJoin);
            overviewSql = StringUtils.replace(overviewSql, "{join_date_yoy}", yoyJoin);

            // 回复请求数据
            request.setStartTime(startTime);
            request.setEndTime(endTime);
            request.getBaseQueryCondition().setStartTime(startTime);
            request.getBaseQueryCondition().setEndTime(endTime);

            sqlBuilder.append(overviewSql);
        }

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryCurrentOverviewTripTrendOrderNum", logSql);

        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportOverviewTrendDTO.class, "queryOnlineReportCurrentOverviewTrendOrderNum");

    }

    /**
     * 查询 在线报告概况-订单数趋势数据 - 概览 （bu + 字段名 -> 数据聚合维度 日期聚合 -> 数据聚合粒度 同环比)
     *
     * @param request
     * @return
     * @throws Exception
     */
    public List<OnlineReportOverviewTrendJPDTO> queryOnlineReportCurrentJPOverviewTrendOrderNum(OnlineTrendRequestDto request)
            throws Exception {

        String startTime = request.getStartTime();
        String endTime = request.getEndTime();
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dim = "''";
        if (request.getDateDimension().equals(QueryReportAggDateDimensionEnum.day.toString())
                || request.getDateDimension().equals(QueryReportAggDateDimensionEnum.week.toString())) {
            queryAccJPOverviewTripTrendOrderNumSubProductSql(request, sqlBuilder, parmList, dim);
        } else {
            StringBuilder currentsqlBuilder = new StringBuilder();
            List<Object> currentparmList = new ArrayList<>();
            queryAccJPOverviewTripTrendOrderNumSubProductSql(request, currentsqlBuilder, currentparmList, dim);

            StringBuilder chainsqlBuilder = new StringBuilder();
            List<Object> chainparmList = new ArrayList<>();
            request.setStartTime(request.getChainStartTime());
            request.setEndTime(request.getChainEndTime());
            request.getBaseQueryCondition().setStartTime(request.getChainStartTime());
            request.getBaseQueryCondition().setEndTime(request.getChainEndTime());
            queryAccJPOverviewTripTrendOrderNumSubProductSql(request, chainsqlBuilder, chainparmList, dim);

            StringBuilder yoysqlBuilder = new StringBuilder();
            List<Object> yoyparmList = new ArrayList<>();
            request.setStartTime(request.getYoyStartTime());
            request.setEndTime(request.getYoyEndTime());
            request.getBaseQueryCondition().setStartTime(request.getYoyStartTime());
            request.getBaseQueryCondition().setEndTime(request.getYoyEndTime());
            queryAccJPOverviewTripTrendOrderNumSubProductSql(request, yoysqlBuilder, yoyparmList, dim);

            parmList.addAll(currentparmList);
            parmList.addAll(chainparmList);
            parmList.addAll(yoyparmList);

            String overviewSql = StringUtils.replace(OVERVIEW_SQL_TEMPLATE_ORDER_NUM, "{current_sql}", currentsqlBuilder.toString());
            overviewSql = StringUtils.replace(overviewSql, "{chain_sql}", chainsqlBuilder.toString());
            overviewSql = StringUtils.replace(overviewSql, "{yoy_sql}", yoysqlBuilder.toString());

            String chainJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "chain.date");
            chainJoin = StringUtils.replace(chainJoin, "{duration}", CHAIN_DURATION.get(request.getDateDimension()));
            String yoyJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "yoy.date");
            yoyJoin = StringUtils.replace(yoyJoin, "{duration}", String.valueOf(request.getYoyDuration()));

            overviewSql = StringUtils.replace(overviewSql, "{join_date_chain}", chainJoin);
            overviewSql = StringUtils.replace(overviewSql, "{join_date_yoy}", yoyJoin);

            // 回复请求数据
            request.setStartTime(startTime);
            request.setEndTime(endTime);
            request.getBaseQueryCondition().setStartTime(startTime);
            request.getBaseQueryCondition().setEndTime(endTime);

            sqlBuilder.append(overviewSql);
        }

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryCurrentOverviewTripTrendOrderNum", logSql);

        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportOverviewTrendJPDTO.class, "queryOnlineReportCurrentJPOverviewTrendOrderNum");

    }

    /**
     * 查询 在线报告概况-消费金额趋势数据 - 概览 （bu + 字段名 -> 数据聚合维度 日期聚合 -> 数据聚合粒度 同环比)
     *
     * @param request
     * @return
     * @throws Exception
     */
    public List<OnlineReportOverviewTrendDTO> queryOnlineReportCurrentOverviewTrend(OnlineTrendRequestDto request)
            throws Exception {
        String sql = "";
        if (request.getDateDimension().equals(QueryReportAggDateDimensionEnum.day.toString())
                || request.getDateDimension().equals(QueryReportAggDateDimensionEnum.week.toString())) {
            sql = buildAccOverViewSql(request);
        } else {
            BaseQueryConditionDTO baseQueryConditionDTO = request.getBaseQueryCondition();
            sql = buildCurrentOverViewSql(request, overviewTrend(baseQueryConditionDTO.getPos()
                            , baseQueryConditionDTO.getStatisticalCaliber(), baseQueryConditionDTO.getBlueSpace(), baseQueryConditionDTO.getCurrency()).toString()
                    , OVERVIEW_SQL_TEMPLATE);
        }

        // 当期时 为join 语句

        List<OnlineReportOverviewTrendDTO> poJos = queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                e.printStackTrace();
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportOverviewTrendDTO.class, "queryOnlineReportCurrentOverviewTrend");
        return poJos;
    }

    public List<OnlineReportOverviewTrendDTO> queryOnlineReportAccOverviewTrend(OnlineTrendRequestDto request)
            throws Exception {
        String sql = buildAccOverViewSql(request);
        // 累计时为当期子语句
        return queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportOverviewTrendDTO.class, "queryOnlineReportAccOverviewTrend");
    }

    public List<OnlineReportOverviewTrendDTO> queryOnlineReportAccOverviewTrendOrderNum(OnlineTrendRequestDto request)
            throws Exception {

        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dim = "''";
        queryAccOverviewTripTrendOrderNumSubProductSql(request, sqlBuilder, parmList, dim);

        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportOverviewTrendDTO.class, "queryOnlineReportAccOverviewTrendOrderNum");
    }

    public List<OnlineReportOverviewTrendJPDTO> queryOnlineReportAccJPOverviewTrendOrderNum(OnlineTrendRequestDto request)
            throws Exception {

        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dim = "''";
        queryAccJPOverviewTripTrendOrderNumSubProductSql(request, sqlBuilder, parmList, dim);

        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportOverviewTrendJPDTO.class, "queryOnlineReportAccJPOverviewTrendOrderNum");
    }

    public List<OnlineReportOverviewTrendDTO> queryCurrentOverviewTripTrend(OnlineTrendRequestDto requestDto)
            throws Exception {
        String dim = "''";
        String sql = "";
        if (requestDto.getDateDimension().equals(QueryReportAggDateDimensionEnum.day.toString())) {
            sql = conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getStartTime(),
                    requestDto.getEndTime(), buildOneTripSql().toString(), requestDto.getBaseQueryCondition(),
                    requestDto.getQueryType());
        } else if (requestDto.getDateDimension().equals(QueryReportAggDateDimensionEnum.week.toString())) {
            sql = conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getStartTime(),
                    requestDto.getEndTime(), buildOneTripSql().toString(), requestDto.getBaseQueryCondition(),
                    requestDto.getQueryType());
        } else {
            sql = buildCurrentOverViewSql(requestDto, buildOneTripSql().toString(), OVERVIEW_TRIP_SQL_TEMPLATE);
        }

        // 当期时 为join 语句

        List<OnlineReportOverviewTrendDTO> poJos = queryBySql(sql, requestDto, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                e.printStackTrace();
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportOverviewTrendDTO.class, "queryOnlineReportCurrentOverviewTrend");
        return poJos;
    }


    public List<OnlineReportOverviewTrendDTO> queryCurrentOverviewTripTrendOrderNum(OnlineTrendRequestDto requestDto)
            throws Exception {
        String dim = "''";
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String startTime = requestDto.getStartTime();
        String endTime = requestDto.getEndTime();

        if (requestDto.getDateDimension().equals(QueryReportAggDateDimensionEnum.day.toString())
                || requestDto.getDateDimension().equals(QueryReportAggDateDimensionEnum.week.toString())) {
            queryAccOverviewTripTrendOrderNumSql(requestDto, sqlBuilder, parmList, dim);
        } else {
            StringBuilder currentsqlBuilder = new StringBuilder();
            List<Object> currentparmList = new ArrayList<>();
            queryAccOverviewTripTrendOrderNumSql(requestDto, currentsqlBuilder, currentparmList, dim);

            StringBuilder chainsqlBuilder = new StringBuilder();
            List<Object> chainparmList = new ArrayList<>();
            requestDto.setStartTime(requestDto.getChainStartTime());
            requestDto.setEndTime(requestDto.getChainEndTime());
            requestDto.getBaseQueryCondition().setStartTime(requestDto.getChainStartTime());
            requestDto.getBaseQueryCondition().setEndTime(requestDto.getChainEndTime());
            queryAccOverviewTripTrendOrderNumSql(requestDto, chainsqlBuilder, chainparmList, dim);

            StringBuilder yoysqlBuilder = new StringBuilder();
            List<Object> yoyparmList = new ArrayList<>();
            requestDto.setStartTime(requestDto.getYoyStartTime());
            requestDto.setEndTime(requestDto.getYoyEndTime());
            requestDto.getBaseQueryCondition().setStartTime(requestDto.getYoyStartTime());
            requestDto.getBaseQueryCondition().setEndTime(requestDto.getYoyEndTime());
            queryAccOverviewTripTrendOrderNumSql(requestDto, yoysqlBuilder, yoyparmList, dim);

            String overviewSql = StringUtils.replace(OVERVIEW_TRIP_SQL_TEMPLATE, "{current_sql}", currentsqlBuilder.toString());
            overviewSql = StringUtils.replace(overviewSql, "{chain_sql}", chainsqlBuilder.toString());
            overviewSql = StringUtils.replace(overviewSql, "{yoy_sql}", yoysqlBuilder.toString());

            parmList.addAll(currentparmList);
            parmList.addAll(chainparmList);
            parmList.addAll(yoyparmList);

            String chainJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "chain.date");
            chainJoin = StringUtils.replace(chainJoin, "{duration}", CHAIN_DURATION.get(requestDto.getDateDimension()));
            String yoyJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "yoy.date");
            yoyJoin = StringUtils.replace(yoyJoin, "{duration}", String.valueOf(requestDto.getYoyDuration()));

            overviewSql = StringUtils.replace(overviewSql, "{join_date_chain}", chainJoin);
            overviewSql = StringUtils.replace(overviewSql, "{join_date_yoy}", yoyJoin);

            // 回复请求数据
            requestDto.setStartTime(startTime);
            requestDto.setEndTime(endTime);
            requestDto.getBaseQueryCondition().setStartTime(startTime);
            requestDto.getBaseQueryCondition().setEndTime(endTime);

            sqlBuilder.append(overviewSql);
        }
        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryCurrentOverviewTripTrendOrderNum", logSql);

        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportOverviewTrendDTO.class, "queryCurrentOverviewTripTrendOrderNum");
    }

    public List<OnlineReportOverviewTrendJPDTO> queryCurrentJPOverviewTripTrendOrderNum(OnlineTrendRequestDto requestDto)
            throws Exception {
        String dim = "''";
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String startTime = requestDto.getStartTime();
        String endTime = requestDto.getEndTime();

        if (requestDto.getDateDimension().equals(QueryReportAggDateDimensionEnum.day.toString())
                || requestDto.getDateDimension().equals(QueryReportAggDateDimensionEnum.week.toString())) {
            queryAccJPOverviewTripTrendOrderNumSql(requestDto, sqlBuilder, parmList, dim);
        } else {
            StringBuilder currentsqlBuilder = new StringBuilder();
            List<Object> currentparmList = new ArrayList<>();
            queryAccJPOverviewTripTrendOrderNumSql(requestDto, currentsqlBuilder, currentparmList, dim);

            StringBuilder chainsqlBuilder = new StringBuilder();
            List<Object> chainparmList = new ArrayList<>();
            requestDto.setStartTime(requestDto.getChainStartTime());
            requestDto.setEndTime(requestDto.getChainEndTime());
            requestDto.getBaseQueryCondition().setStartTime(requestDto.getChainStartTime());
            requestDto.getBaseQueryCondition().setEndTime(requestDto.getChainEndTime());
            queryAccJPOverviewTripTrendOrderNumSql(requestDto, chainsqlBuilder, chainparmList, dim);

            StringBuilder yoysqlBuilder = new StringBuilder();
            List<Object> yoyparmList = new ArrayList<>();
            requestDto.setStartTime(requestDto.getYoyStartTime());
            requestDto.setEndTime(requestDto.getYoyEndTime());
            requestDto.getBaseQueryCondition().setStartTime(requestDto.getYoyStartTime());
            requestDto.getBaseQueryCondition().setEndTime(requestDto.getYoyEndTime());
            queryAccJPOverviewTripTrendOrderNumSql(requestDto, yoysqlBuilder, yoyparmList, dim);

            String overviewSql = StringUtils.replace(OVERVIEW_TRIP_SQL_TEMPLATE, "{current_sql}", currentsqlBuilder.toString());
            overviewSql = StringUtils.replace(overviewSql, "{chain_sql}", chainsqlBuilder.toString());
            overviewSql = StringUtils.replace(overviewSql, "{yoy_sql}", yoysqlBuilder.toString());

            parmList.addAll(currentparmList);
            parmList.addAll(chainparmList);
            parmList.addAll(yoyparmList);

            String chainJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "chain.date");
            chainJoin = StringUtils.replace(chainJoin, "{duration}", CHAIN_DURATION.get(requestDto.getDateDimension()));
            String yoyJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "yoy.date");
            yoyJoin = StringUtils.replace(yoyJoin, "{duration}", String.valueOf(requestDto.getYoyDuration()));

            overviewSql = StringUtils.replace(overviewSql, "{join_date_chain}", chainJoin);
            overviewSql = StringUtils.replace(overviewSql, "{join_date_yoy}", yoyJoin);

            // 回复请求数据
            requestDto.setStartTime(startTime);
            requestDto.setEndTime(endTime);
            requestDto.getBaseQueryCondition().setStartTime(startTime);
            requestDto.getBaseQueryCondition().setEndTime(endTime);

            sqlBuilder.append(overviewSql);
        }
        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryCurrentOverviewTripTrendOrderNum", logSql);

        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportOverviewTrendJPDTO.class, "queryCurrentOverviewTripTrendOrderNum");
    }

    public List<OnlineReportOverviewTrendDTO> queryAccOverviewTripTrend(OnlineTrendRequestDto requestDto)
            throws Exception {
        String dim = "''";
        String sql = conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getStartTime(),
                requestDto.getEndTime(), buildOneTripSql().toString(), requestDto.getBaseQueryCondition(),
                requestDto.getQueryType());
        return queryBySql(sql, requestDto, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportOverviewTrendDTO.class, "queryOnlineReportAccOverviewOneTripTrend");
    }

    public List<OnlineReportOverviewTrendDTO> queryAccOverviewTripTrendOrderNum(OnlineTrendRequestDto requestDto)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dim = "''";
        queryAccOverviewTripTrendOrderNumSql(requestDto, sqlBuilder, parmList, dim);

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryAccOverviewTripTrendOrderNum", logSql);

        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportOverviewTrendDTO.class, "queryAccOverviewTripTrendOrderNum");
    }

    public List<OnlineReportOverviewTrendJPDTO> queryAccJPOverviewTripTrendOrderNum(OnlineTrendRequestDto requestDto)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dim = "''";
        queryAccJPOverviewTripTrendOrderNumSql(requestDto, sqlBuilder, parmList, dim);

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryAccJPOverviewTripTrendOrderNum", logSql);

        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportOverviewTrendJPDTO.class, "queryAccJPOverviewTripTrendOrderNum");
    }

    private void queryAccOverviewTripTrendOrderNumSql(OnlineTrendRequestDto requestDto,
                                                      StringBuilder sqlBuilder,
                                                      List<Object> parmList,
                                                      String dim) {
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getBaseQueryCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }

        ClickHouseTable clickHouseTable = null;

        sqlBuilder.append(String.format(" select sum(order_num) as totalQuantity , %s as dim, dt as date from ", dim));
        sqlBuilder.append(" (");
        sqlBuilder.append("select count(distinct order_id) as order_num, '机票' as order_type, ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" AND fee_type = '因公' and  audited <> 'F' and order_status in ('RA','TA','EP','EA','RP')");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(distinct order_id) as order_num, '酒店' as order_type, ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" AND order_status = '已完成' AND is_oversea IN ('F','O','T') ");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(distinct a.order_id) as order_num, '火车' as order_type,  a.dt as dt from ");
        sqlBuilder.append(" (select order_id, sum(quantity) cnt , ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" and order_status  in ('TA','RP','EP','EA') ");
        sqlBuilder.append(" group by order_id, dt");
        sqlBuilder.append(" )a");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(DISTINCT case when flag= 0 then orderid end)-count(DISTINCT case when flag= 1 then orderid end ) " +
                "as order_num, '汽车' as order_type, ");
        sqlBuilder.append(getTimeField("querydate", requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.BUS_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.BUS_ORDERDETAIL));
        sqlBuilder.append(" and  isself = '因公' and orderstatusdesc = '已购票' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corpid", requestDto.getBaseQueryCondition().getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "querydate"));
        sqlBuilder.append(" group by dt , order_type ");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(DISTINCT case when vd.flag= 0 then vd.vaso_id end)-count(DISTINCT case when vd.flag= 1 then vd.vaso_id end )" +
                " as order_num, '增值' as order_type, ");
        sqlBuilder.append(getTimeField("query_refund_date", requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(" (select vaso_id, flag, query_refund_date from ");
        sqlBuilder.append(ClickHouseTable.VASO_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.VASO_ORDERDETAIL));
        sqlBuilder.append(" AND orderstatus in ('S','5')");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("company", requestDto.getBaseQueryCondition().getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "query_refund_date"));
        sqlBuilder.append(" ) vd");
        sqlBuilder.append(" group by dt, order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append(" select sum(coalesce(cnt_order,0)) as order_num, '用车' as order_type, ");
        sqlBuilder.append(getTimeField("orderdt", requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD));
        sqlBuilder.append(" AND fee_type = '因公' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "orderdt"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corp_corporation", requestDto.getBaseQueryCondition().getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildRolePermission());
        sqlBuilder.append(" group by dt, order_type ");
        sqlBuilder.append(" \n )  group by date, dim ");
    }

    private void queryAccJPOverviewTripTrendOrderNumSql(OnlineTrendRequestDto requestDto,
                                                        StringBuilder sqlBuilder,
                                                        List<Object> parmList,
                                                        String dim) {
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getBaseQueryCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }

        sqlBuilder.append(String.format(" select sum(order_num) as totalQuantity , %s as dim, dt as date from ", dim));
        sqlBuilder.append(" (");
        sqlBuilder.append("select count(distinct order_id) as order_num, '机票' as order_type, ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD_FOREIGN.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD_FOREIGN));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" AND fee_type = '因公' and  audited <> 'F' and order_status in ('RA','TA','EP','EA','RP')");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(distinct order_id) as order_num, '酒店' as order_type, ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD_FOREIGN.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD_FOREIGN));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" AND order_status = '已完成' AND is_oversea IN ('F','O','T') ");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(distinct a.order_id) as order_num, '火车' as order_type,  a.dt as dt from ");
        sqlBuilder.append(" (select order_id, sum(quantity) cnt , ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD_FOREIGN.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD_FOREIGN));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" and order_status  in ('TA','RP','EP','EA') ");
        sqlBuilder.append(" group by order_id, dt");
        sqlBuilder.append(" )a");
        sqlBuilder.append(" group by dt , order_type");
        sqlBuilder.append(" \n )  group by date, dim ");
    }

    private void queryAccOverviewTripTrendOrderNumSubProductSql(OnlineTrendRequestDto requestDto,
                                                                StringBuilder sqlBuilder,
                                                                List<Object> parmList,
                                                                String dim) {
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getBaseQueryCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }

        ClickHouseTable clickHouseTable = null;

        sqlBuilder.append(String.format(" select sum(order_num) as totalQuantity , %s as dim, dt as date ", dim));
        sqlBuilder.append(", sum(if(order_type='机票', order_num, 0)) as fltQuantity");
        sqlBuilder.append(", sum(if(order_type='酒店', order_num, 0)) as htlQuantity");
        sqlBuilder.append(", sum(if(order_type='火车', order_num, 0)) as trainQuantity");
        sqlBuilder.append(", sum(if(order_type='用车', order_num, 0)) as carQuantity");
        sqlBuilder.append(", sum(if(order_type='汽车', order_num, 0)) as busQuantity");
        sqlBuilder.append(", sum(if(order_type='增值', order_num, 0)) as addQuantity");
        sqlBuilder.append(" from ");
        sqlBuilder.append(" (");
        sqlBuilder.append("select count(distinct order_id) as order_num, '机票' as order_type, ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" AND fee_type = '因公' and  audited <> 'F' and order_status in ('RA','TA','EP','EA','RP')");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(distinct order_id) as order_num, '酒店' as order_type, ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" AND order_status = '已完成' AND is_oversea IN ('F','O','T') ");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(distinct a.order_id) as order_num, '火车' as order_type,  a.dt as dt from ");
        sqlBuilder.append(" (select order_id, sum(quantity) cnt , ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" and order_status  in ('TA','RP','EP','EA') ");
        sqlBuilder.append(" group by order_id, dt");
        sqlBuilder.append(" )a");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(DISTINCT case when flag= 0 then orderid end)-count(DISTINCT case when flag= 1 then orderid end ) " +
                "as order_num, '汽车' as order_type, ");
        sqlBuilder.append(getTimeField("querydate", requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.BUS_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.BUS_ORDERDETAIL));
        sqlBuilder.append(" and  isself = '因公' and orderstatusdesc = '已购票' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corpid", requestDto.getBaseQueryCondition().getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "querydate"));
        sqlBuilder.append(" group by dt , order_type ");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(DISTINCT case when vd.flag= 0 then vd.vaso_id end)-count(DISTINCT case when vd.flag= 1 then vd.vaso_id end )" +
                " as order_num, '增值' as order_type, ");
        sqlBuilder.append(getTimeField("query_refund_date", requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(" (select vaso_id, flag, query_refund_date from ");
        sqlBuilder.append(ClickHouseTable.VASO_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.VASO_ORDERDETAIL));
        sqlBuilder.append(" AND orderstatus in ('S','5')");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("company", requestDto.getBaseQueryCondition().getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "query_refund_date"));
        sqlBuilder.append(" ) vd");
        sqlBuilder.append(" group by dt, order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append(" select sum(coalesce(cnt_order,0)) as order_num, '用车' as order_type, ");
        sqlBuilder.append(getTimeField("orderdt", requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD));
        sqlBuilder.append(" AND fee_type = '因公' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "orderdt"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corp_corporation", requestDto.getBaseQueryCondition().getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildRolePermission());
        sqlBuilder.append(" group by dt, order_type ");
        sqlBuilder.append(" \n )  group by date, dim ");
    }


    private void queryAccJPOverviewTripTrendOrderNumSubProductSql(OnlineTrendRequestDto requestDto,
                                                                  StringBuilder sqlBuilder,
                                                                  List<Object> parmList,
                                                                  String dim) {
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getBaseQueryCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }

        ClickHouseTable clickHouseTable = null;

        sqlBuilder.append(String.format(" select sum(order_num) as totalQuantity , %s as dim, dt as date ", dim));
        sqlBuilder.append(", sum(if(order_type='机票', order_num, 0)) as fltQuantity");
        sqlBuilder.append(", sum(if(order_type='酒店', order_num, 0)) as htlQuantity");
        sqlBuilder.append(", sum(if(order_type='火车', order_num, 0)) as trainQuantity");
        sqlBuilder.append(", sum(if(order_type='用车', order_num, 0)) as carQuantity");
        sqlBuilder.append(", sum(if(order_type='汽车', order_num, 0)) as busQuantity");
        sqlBuilder.append(", sum(if(order_type='增值', order_num, 0)) as addQuantity");
        sqlBuilder.append(" from ");
        sqlBuilder.append(" (");
        sqlBuilder.append("select count(distinct order_id) as order_num, '机票' as order_type, ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD_FOREIGN.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD_FOREIGN));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" AND fee_type = '因公' and  audited <> 'F' and order_status in ('RA','TA','EP','EA','RP')");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(distinct order_id) as order_num, '酒店' as order_type, ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD_FOREIGN.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD_FOREIGN));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" AND order_status = '已完成' AND is_oversea IN ('F','O','T') ");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(distinct a.order_id) as order_num, '火车' as order_type,  a.dt as dt from ");
        sqlBuilder.append(" (select order_id, sum(quantity) cnt , ");
        sqlBuilder.append(getTimeField(col, requestDto.getDateDimension()));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD_FOREIGN.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD_FOREIGN));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto.getBaseQueryCondition(), parmList, col));
        sqlBuilder.append(" and order_status  in ('TA','RP','EP','EA') ");
        sqlBuilder.append(" group by order_id, dt");
        sqlBuilder.append(" )a");
        sqlBuilder.append(" group by dt , order_type");
        sqlBuilder.append(" \n )  group by date, dim ");
    }

    private String getTimeField(String field, String dateDimension) {
        String group_date = "";
        switch (dateDimension) {
            case "month":
                group_date = String.format("concat(substr(%s,1,7), '-01')", field);
                break;
            case "half":
                group_date = String.format("date_format(toStartOfInterval(parseDateTimeBestEffort(%s), INTERVAL 6 MONTH), '%%Y-%%m-%%d')", field);;
                break;
            case "quarter":
                group_date = String.format("date_format(toStartOfInterval(parseDateTimeBestEffort(%s), INTERVAL 3 MONTH), '%%Y-%%m-%%d')", field);
                break;
            case "day":
                group_date = String.format("substr(%s, 1, 10)", field);
                break;
            case "week":
                // 获得report_date所在周的第一天
                group_date = String.format("date_format(toStartOfInterval(parseDateTimeBestEffort(%s), INTERVAL 1 WEEK), '%%Y-%%m-%%d')", field);
                break;
            default:
                break;
        }
        return group_date;
    }


    /**
     * JA-JP
     *
     * @param request
     * @return
     * @throws Exception
     */
    public List<OnlineReportOverviewTrendJPDTO> queryOnlineReportCurrentJPOverviewTrend(OnlineTrendRequestDto request)
            throws Exception {
        BaseQueryConditionDTO baseQueryConditionDTO = request.getBaseQueryCondition();
        String sql = "";
        if (request.getDateDimension().equals(QueryReportAggDateDimensionEnum.day.toString())
                || request.getDateDimension().equals(QueryReportAggDateDimensionEnum.week.toString())) {
            sql = buildAccOverViewSql(request);
        } else {
            sql = buildCurrentOverViewSql(request, overviewTrend(baseQueryConditionDTO.getPos()
                            , baseQueryConditionDTO.getStatisticalCaliber(), baseQueryConditionDTO.getBlueSpace(), baseQueryConditionDTO.getCurrency()).toString()
                    , OVERVIEW_SQL_TEMPLATE);
        }
        // 当期时 为join 语句
        return queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                e.printStackTrace();
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportOverviewTrendJPDTO.class, "queryOnlineReportCurrentOverviewTrend");
    }

    /**
     * JA-JP
     *
     * @param request
     * @return
     * @throws Exception
     */
    public List<OnlineReportOverviewTrendJPDTO> queryOnlineReportAccJPOverviewTrend(OnlineTrendRequestDto request)
            throws Exception {
        String sql = buildAccOverViewSql(request);
        // 累计时为当期子语句
        return queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportOverviewTrendJPDTO.class, "queryOnlineReportAccOverviewTrend");
    }

    /**
     * JA-JP
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    public List<OnlineReportOverviewTrendJPDTO> queryCurrentJPOverviewTripTrend(OnlineTrendRequestDto requestDto)
            throws Exception {
        String dim = "''";
        String sql = "";
        if (requestDto.getDateDimension().equals(QueryReportAggDateDimensionEnum.day.toString())) {
            sql = conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getStartTime(),
                    requestDto.getEndTime(), buildOneTripSql().toString(), requestDto.getBaseQueryCondition(),
                    requestDto.getQueryType());
        } else if (requestDto.getDateDimension().equals(QueryReportAggDateDimensionEnum.week.toString())) {
            sql = conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getStartTime(),
                    requestDto.getEndTime(), buildOneTripSql().toString(), requestDto.getBaseQueryCondition(),
                    requestDto.getQueryType());
        } else {
            sql = buildCurrentOverViewSql(requestDto, buildOneTripSql().toString(), OVERVIEW_TRIP_SQL_TEMPLATE);
        }
        // 当期时 为join 语句
        return queryBySql(sql, requestDto, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                e.printStackTrace();
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportOverviewTrendJPDTO.class, "queryOnlineReportCurrentOverviewTrend");
    }

    /**
     * JA-JP
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    public List<OnlineReportOverviewTrendJPDTO> queryAccJPOverviewTripTrend(OnlineTrendRequestDto requestDto)
            throws Exception {
        String dim = "''";
        String sql = conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getStartTime(),
                requestDto.getEndTime(), buildOneTripSql().toString(), requestDto.getBaseQueryCondition(),
                requestDto.getQueryType());
        return queryBySql(sql, requestDto, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportOverviewTrendJPDTO.class, "queryOnlineReportAccOverviewOneTripTrend");
    }

    public StringBuilder buildOneTripSql() {
        String partition = queryPartition(ClickHouseTable.ADM_INDEX_ONE_TRIP_CORP);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {group_date} as date, {group_dim} as dim, ");
        stringBuilder.append("sum(one_trip_number) totalQuantity ");
        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.ADM_INDEX_ONE_TRIP_CORP.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        stringBuilder.append("report_date >= '{start}' and report_date <= '{end}' and ({scope_condition})  ");
        stringBuilder.append("group by {group_date}, {group_dim}");
        return stringBuilder;
    }

    public StringBuilder buildOrderNumSql() {
        String partition = queryPartition(ClickHouseTable.ADM_INDEX_ONE_TRIP_CORP);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {group_date} as date, {group_dim} as dim, ");
        stringBuilder.append("sum(ordercnt_total) totalQuantity ");
        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.ADM_INDEX_ONE_TRIP_CORP.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        stringBuilder.append("report_date >= '{start}' and report_date <= '{end}' and ({scope_condition})  ");
        stringBuilder.append("group by {group_date}, {group_dim}");
        return stringBuilder;
    }

    public String buildAccOverViewSql(OnlineTrendRequestDto requestDto) {
        String dim = "''";
        BaseQueryConditionDTO baseQueryConditionDTO = requestDto.getBaseQueryCondition();
        return conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getStartTime(),
                requestDto.getEndTime(), overviewAccTrend(baseQueryConditionDTO.getPos(), baseQueryConditionDTO.getStatisticalCaliber(),
                        baseQueryConditionDTO.getBlueSpace(), baseQueryConditionDTO.getCurrency()).toString()
                , requestDto.getBaseQueryCondition(),
                requestDto.getQueryType());
    }

    // 新增pos参数区分中文站和日本站

    public String buildCurrentOverViewSql(OnlineTrendRequestDto requestDto, String subSqlTemplate, String sqlTemplate) {
        String dim = "''";
        String currentSubSql = conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getStartTime(),
                requestDto.getEndTime(), subSqlTemplate, requestDto.getBaseQueryCondition(), requestDto.getQueryType());
        String chainSubSql = conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getChainStartTime(),
                requestDto.getChainEndTime(), subSqlTemplate, requestDto.getBaseQueryCondition(),
                requestDto.getQueryType());
        String yoySubSql = conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getYoyStartTime(),
                requestDto.getYoyEndTime(), subSqlTemplate, requestDto.getBaseQueryCondition(), requestDto.getQueryType());
        String overviewSql = StringUtils.replace(sqlTemplate, "{current_sql}", currentSubSql);
        overviewSql = StringUtils.replace(overviewSql, "{chain_sql}", chainSubSql);
        overviewSql = StringUtils.replace(overviewSql, "{yoy_sql}", yoySubSql);

        String chainJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "chain.date");
        chainJoin = StringUtils.replace(chainJoin, "{duration}", CHAIN_DURATION.get(requestDto.getDateDimension()));
        String yoyJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "yoy.date");
        yoyJoin = StringUtils.replace(yoyJoin, "{duration}", String.valueOf(requestDto.getYoyDuration()));

        overviewSql = StringUtils.replace(overviewSql, "{join_date_chain}", chainJoin);
        overviewSql = StringUtils.replace(overviewSql, "{join_date_yoy}", yoyJoin);
        return overviewSql;
    }

    public StringBuilder overviewTrend(String pos, String statisticalCaliber, String blueSpace, String currency) {
        ClickHouseTable clickHouseTable;
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN_MULTI_CURRENCY;
            } else {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN;
            }
        } else {
            if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
            }
        }
        // 整体与分产线 - 月/季/半年 - 日期
        String partition = queryPartition(clickHouseTable);
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("select {group_date} as date, {group_dim} as dim, ");
        stringBuilder.append("CAST(sum(amount_total) AS DOUBLE) totalAmount, ");
        stringBuilder.append("CAST(sum(amount_flt) AS DOUBLE) fltAmount, ");
        stringBuilder.append("CAST(sum(amount_htl) AS DOUBLE) htlAmount, ");
        stringBuilder.append("CAST(sum(amount_train) AS DOUBLE) trainAmount, ");
        stringBuilder.append("CAST(sum(amount_car) AS DOUBLE) carAmount, ");
        stringBuilder.append("CAST(sum(amount_bus) AS DOUBLE) busAmount, ");
        stringBuilder.append("CAST(sum(amount_vas) AS DOUBLE) addAmount, ");
        stringBuilder.append("sum(quantity_total) totalQuantity, ");
        stringBuilder.append("sum(quantity_flt) fltQuantity, ");
        stringBuilder.append("sum(quantity_htl) htlQuantity, ");
        stringBuilder.append("sum(quantity_train) trainQuantity, ");
        stringBuilder.append("sum(quantity_car) carQuantity, ");
        stringBuilder.append("CAST(sum(quantity_bus) AS DOUBLE) busQuantity, ");
        stringBuilder.append("CAST(sum(amount_vas) AS DOUBLE) addQuantity ");
        stringBuilder.append("from ");
        stringBuilder.append(clickHouseTable.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(currency)) {
                // 币种条件
                stringBuilder.append(String.format(" termcurrency = '%s'", currency)).append(" and ");
            }
        }
        stringBuilder.append("report_date >= '{start}' and report_date <= '{end}' and ({scope_condition})  ");
        stringBuilder.append("group by {group_date}, {group_dim}");
        return stringBuilder;
    }

    // 新增pos参数区分中文站和日本站
    public StringBuilder overviewAccTrend(String pos, String statisticalCaliber, String blueSpace, String currency) {
        ClickHouseTable clickHouseTable;
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN_MULTI_CURRENCY;
            } else {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN;
            }
        } else {
            if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
            }
        }
        // 整体与分产线 - 月/季/半年 - 日期
        String partition = queryPartition(clickHouseTable);
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("select {group_date} as date, {group_dim} as dim, ");
        stringBuilder.append("sum(amount_total) totalAmount, ");
        stringBuilder.append("sum(amount_flt) fltAmount, ");
        stringBuilder.append("sum(amount_htl) htlAmount, ");
        stringBuilder.append("sum(amount_train) trainAmount, ");
        stringBuilder.append("sum(amount_car) carAmount, ");
        stringBuilder.append("sum(amount_bus) busAmount, ");
        stringBuilder.append("sum(amount_vas) addAmount, ");
        stringBuilder.append("sum(quantity_total) totalQuantity, ");
        stringBuilder.append("sum(quantity_flt) fltQuantity, ");
        stringBuilder.append("sum(quantity_htl) htlQuantity, ");
        stringBuilder.append("sum(quantity_train) trainQuantity, ");
        stringBuilder.append("sum(quantity_car) carQuantity, ");
        stringBuilder.append("sum(quantity_bus) busQuantity, ");
        stringBuilder.append("sum(quantity_vas) addQuantity ");
        stringBuilder.append("from ");
        stringBuilder.append(clickHouseTable.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(currency)) {
                // 币种条件
                stringBuilder.append(String.format(" termcurrency = '%s'", currency)).append(" and ");
            }
        }
        stringBuilder.append("report_date >= '{start}' and report_date <= '{end}' and ({scope_condition})  ");
        stringBuilder.append("group by {group_date}, {group_dim}");
        return stringBuilder;
    }

    private String conditionWrapAsSubSql(String dim, String dateDimension, String start, String end, String sqlTemplate,
                                         BaseQueryConditionDTO baseQueryCondition, String queryType) {
         /*
         group_date group_dim
         start end chain_start chain_end yoy_start yoy_end
         scope_condition
         */
        String group_date = "";
        switch (dateDimension) {
            case "month":
                group_date = "firstday_of_month";
                break;
            case "half":
                group_date = "firstday_of_halfyear";
                break;
            case "quarter":
                group_date = "firstday_of_quarter";
                break;
            case "day":
                group_date = "substr(report_date, 1, 10)";
                break;
            case "week":
                // 获得report_date所在周的第一天
                group_date = "dateTrunc('week',CAST(report_date AS DATETIME))";
                break;
            default:
                break;
        }
        String scope_condition = buildScopeFilter(baseQueryCondition);
        if (dim.equals("''") && queryType.equals("numberOfTrips")) {
            scope_condition = buildCorporationScopeFilter(baseQueryCondition);
        }
        sqlTemplate = sqlTemplate.replace("{group_date}", group_date).replace("{group_dim}", dim)
                .replace("{scope_condition}", scope_condition).replace("{start}", start).replace("{end}", end);
        return sqlTemplate;
    }


    public static String buildCorporationScopeFilter(BaseQueryConditionDTO baseQueryCondition) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" 1 = 1");
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            stringBuilder.append(bulidCorpAndAccount("corp_corporation", baseQueryCondition.getCorpIds()));
        }
        return stringBuilder.toString();
    }

    public static String buildScopeFilter(BaseQueryConditionDTO baseQueryCondition) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" 1 = 1");
        if (StringUtils.isNotEmpty(baseQueryCondition.getGroupId())) {
            stringBuilder.append(" and companygroupid = ").append("'").append(baseQueryCondition.getGroupId())
                    .append("'");
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            stringBuilder.append(bulidCorpAndAccount("corp_corporation", baseQueryCondition.getCorpIds()));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getAccountIds())) {
            stringBuilder.append(bulidCorpAndAccount("account_id", baseQueryCondition.getAccountIds()));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCostCenterList())) {
            stringBuilder.append(bulidDeptAndCostcenter("cost_center", baseQueryCondition.getCostCenterList()));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getDeptList())) {
            stringBuilder.append(buildOrgId(baseQueryCondition.getDeptList()));
        }
        return stringBuilder.toString();

    }

    /**
     * 构建公司和成本中心的查询条件
     *
     * @param key
     * @param list
     * @param
     */
    public static String bulidCorpAndAccount(String key, List<String> list) {
        StringBuilder sqlBuffer = new StringBuilder();
        String sqlList =
                StringUtils.join(list.stream().map(corp -> "'" + corp + "'").collect(Collectors.toList()), ",");
        sqlBuffer.append(" and ").append(key).append(" in (").append(sqlList).append(")");
        return sqlBuffer.toString();
    }

    /**
     * 构建成本中心和部门的查询条件
     *
     * @param key
     * @param list
     * @param
     */
    public static String bulidDeptAndCostcenter(String key, List<SearchDeptAndCostcneterDTO> list) {
        StringBuffer sqlBuffer = new StringBuffer();
        for (SearchDeptAndCostcneterDTO searchDeptAndCostcneterDto : list) {
            Integer level = searchDeptAndCostcneterDto.getKey();
            if (Objects.isNull(level)) {
                continue;
            }
            List<String> vals = searchDeptAndCostcneterDto.getVals();
            String filedName = key + level;
            if (Objects.isNull(searchDeptAndCostcneterDto.getWay()) || searchDeptAndCostcneterDto.getWay() != OrpConstants.THREE) {
                // 包含
                if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                    sqlBuffer.append(" and coalesce(" + filedName + ", '') <> ''");
                }
                if (CollectionUtils.isEmpty(vals)) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(vals)) {
                    String valsSql =
                            StringUtils.join(vals.stream().map(val -> "'" + val + "'").collect(Collectors.toList()), ",");
                    sqlBuffer.append(" and " + filedName + " in (" + valsSql + ")");
                }
            } else {
                // 排除
                if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                    sqlBuffer.append(" and coalesce(" + filedName + ", '') = ''");
                }
                if (CollectionUtils.isNotEmpty(vals)) {
                    String valsSql =
                            StringUtils.join(vals.stream().map(val -> "'" + val + "'").collect(Collectors.toList()), ",");
                    sqlBuffer.append(" and " + filedName + " not in (" + valsSql + ")");
                }
                List<String> permitVals = searchDeptAndCostcneterDto.getPermitVals();
                if (CollectionUtils.isNotEmpty(permitVals)) {
                    String valsSql =
                            StringUtils.join(permitVals.stream().map(val -> "'" + val + "'").collect(Collectors.toList()), ",");
                    sqlBuffer.append(" and " + filedName + " in (" + valsSql + ")");
                }
            }
        }
        return sqlBuffer.toString();
    }

    public static String buildOrgId(List<SearchDeptAndCostcneterDTO> list) {
        StringBuffer sqlBuffer = new StringBuffer();
        for (SearchDeptAndCostcneterDTO searchDeptAndCostcneterDto : list) {
            Integer level = searchDeptAndCostcneterDto.getKey();
            if (Objects.isNull(level)) {
                continue;
            }
            List<String> vals = searchDeptAndCostcneterDto.getVals();
            String filedName = "org_id";
            if (Objects.isNull(searchDeptAndCostcneterDto.getWay()) || searchDeptAndCostcneterDto.getWay() != OrpConstants.THREE) {
                // 包含
                if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                    return "";
                }
                if (CollectionUtils.isEmpty(vals)) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(vals)) {
                    String valsSql =
                            StringUtils.join(vals.stream().map(val -> "'" + val + "'").collect(Collectors.toList()), ",");
                    sqlBuffer.append(" and " + filedName + " in (" + valsSql + ")");
                }
            } else {
                // 排除
                if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                    sqlBuffer.append(" and coalesce(" + filedName + ", '') = ''");
                }
                if (CollectionUtils.isNotEmpty(vals)) {
                    String valsSql =
                            StringUtils.join(vals.stream().map(val -> "'" + val + "'").collect(Collectors.toList()), ",");
                    sqlBuffer.append(" and " + filedName + " not in (" + valsSql + ")");
                }
                List<String> permitVals = searchDeptAndCostcneterDto.getPermitVals();
                if (CollectionUtils.isNotEmpty(permitVals)) {
                    String valsSql =
                            StringUtils.join(permitVals.stream().map(val -> "'" + val + "'").collect(Collectors.toList()), ",");
                    sqlBuffer.append(" and " + filedName + " in (" + valsSql + ")");
                }
            }
            break;
        }
        return sqlBuffer.toString();
    }

    private PreparedStatement mapRequest(OnlineTrendRequestDto requestDto, PreparedStatement statement) {
        return statement;
    }
}
