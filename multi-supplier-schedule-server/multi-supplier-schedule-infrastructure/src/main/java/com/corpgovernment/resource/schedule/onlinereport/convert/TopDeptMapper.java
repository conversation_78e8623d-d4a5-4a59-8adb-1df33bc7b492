package com.corpgovernment.resource.schedule.onlinereport.convert;

import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.BusDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.BusTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.CarDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.CarTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.FltDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.FltTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.HtlDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.HtlTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.OverviewDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.OverviewTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.TrainDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.TrainTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.VasoDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.VasoTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.WelfareDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.WelfareDeptDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-12 10:34
 * @desc
 */
@Mapper(componentModel = "spring")
public interface TopDeptMapper {
    TopDeptMapper INSTANCE = Mappers.getMapper(TopDeptMapper.class);

    FltDeptDTO convertFltTopDeptDTO(FltTopDeptDTO fltTopDeptDTO);

    HtlDeptDTO convertHtlTopDeptDTO(HtlTopDeptDTO htlTopDeptDTO);

    TrainDeptDTO convertTrainTopDeptDTO(TrainTopDeptDTO trainTopDeptDTO);

    CarDeptDTO convertCarTopDeptDTO(CarTopDeptDTO carTopDeptDTO);

    List<OverviewDeptDTO> convertOverviewTopDeptDTOs(List<OverviewTopDeptDTO> list);
    OverviewDeptDTO convertOverviewTopDeptDTO(OverviewTopDeptDTO overviewTopDeptDTO);

    BusDeptDTO convertBusTopDeptDTO(BusTopDeptDTO busTopDeptDTO);

    VasoDeptDTO convertVasoTopDeptDTO(VasoTopDeptDTO vasoTopDeptDTO);

    WelfareDeptDTO convertWelfareTopDeptDTO(WelfareDeptDetailDTO welfareDeptDetailDTO);

}
