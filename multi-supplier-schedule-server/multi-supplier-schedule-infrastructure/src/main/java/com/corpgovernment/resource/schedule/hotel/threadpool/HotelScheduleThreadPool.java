package com.corpgovernment.resource.schedule.hotel.threadpool;

import com.ctrip.corp.obt.generic.threadpool.core.spring.EnableDynamicTp;
import com.ctrip.corp.obt.generic.threadpool.core.support.DynamicTp;
import com.ctrip.corp.obt.generic.threadpool.core.support.ThreadPoolBuilder;
import com.ctrip.corp.obt.generic.threadpool.core.support.task.wrapper.TaskWrappers;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.ctrip.corp.obt.generic.threadpool.common.em.QueueTypeEnum.ARRAY_BLOCKING_QUEUE;

/**
 * <AUTHOR>
 * @date 2024/4/30
 */
@Configuration
@Slf4j
@EnableDynamicTp
public class HotelScheduleThreadPool {

    @Bean(name = "hotelExecuteTaskThreadPool")
    @DynamicTp("hotelExecuteTaskThreadPool")
    public ThreadPoolExecutor hotelExecuteTaskThreadPool() {
        int corePoolSize = 100;
        int maximumPoolSize = 100;
        int threadQueueSize = 10000;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();
    }

}
