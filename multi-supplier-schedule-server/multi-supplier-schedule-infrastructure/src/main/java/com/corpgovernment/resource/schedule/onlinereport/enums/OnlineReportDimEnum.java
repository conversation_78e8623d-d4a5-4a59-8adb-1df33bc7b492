package com.corpgovernment.resource.schedule.onlinereport.enums;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/10 19:30
 * @description：
 * @modified By：
 * @version: $
 */
public enum OnlineReportDimEnum {

    /**
     * 总计
     */
    ALL("", "''", "总计", "Index.sum"),

    /**
     * 协议 非协议（机票）
     */
    AGREEMENT("C", "contract_type", "协议", "Index.Agreement"),
    NONE_AGREEMENT("NC", "contract_type", "非协议", "Index.NonAgreement"),

    /**
     * 国内 国际 (机票)
     */
    DOMESTIC("N", "flight_class", "国内", "lbl_Domestic"),
    OVERSEA("I", "flight_class", "国际", "lbl_International"),

    /**
     * 国内 - 海外 -港澳台 （酒店）
     */
    INLAND("O/F", "is_oversea", "国内", "lbl_HtlOverseaF"),
    ABROAD("T", "is_oversea", "海外", "lbl_HtlOverseaT"),

    /**
     * 国内/国际（火车）
     */
    TRAIN_DOM("N", "train_type", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ProductTypeEnum.dom"), "lbl_Domestic"),
    TRAIN_INT("I", "train_type", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ProductTypeEnum.inter"), "lbl_International"),

    /**
     * 协议 会员 （酒店）
     */
    MEMBER("M", "order_type", "非协议", "Index.NonAgreement"),
    HOTEL_AGREEMENT("C", "order_type", "协议", "Index.pact"),

    /**
     * 用车类型
     */
    DOM_AIRPORT_TRANSPORTATION("1", "order_type", "接送机", "Index.domairportpick"),
    INTER_AIRPORT_TRANSPORTATION("2", "order_type", "接送机", "Index.intetrairportpick"),
    DOMESTIC_CHARTER("4", "order_type", "rentcar", "Index.rentcar"),
    DOMESTIC_CAR_RENTAL("3", "order_type", "CharteredCar", "Index.CharteredCar"),
    DOM_TAXI("6/1", "order_type", "taxi", "index.domtaxi"),

    INTER_TAXI("6/CAR_TAXI_INTL", "order_type", "taxi", "index.inttaxi"),
    INTER_CHARTER("18", "order_type", "intcarrental", "Exceltopname.intcarrental"),;


    static HashMap<String, List<OnlineReportDimEnum>> dims = new HashMap<>();

    static {
        dims.put("overview", all());
        dims.put("flight_", all());
        dims.put("flight_flight_class", flightClass());
        dims.put("flight_contract_type", contractType());
        dims.put("hotel_", all());
        dims.put("hotel_is_oversea", isOversea());
        dims.put("hotel_order_type", hotelOrderType());
        dims.put("train_", all());
        dims.put("train_train_type", trainType());
        dims.put("car_", all());
        dims.put("car_order_type", carOrderType());
    }

    private String name;

    private String column;

    private String desc;

    private String descKey;

    OnlineReportDimEnum(String name, String column, String desc, String descKey) {
        this.name = name;
        this.column = column;
        this.desc = desc;
        this.descKey = descKey;
    }

    public String getDescKey() {
        return descKey;
    }

    public void setDescKey(String descKey) {
        this.descKey = descKey;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getColumn() {return column;}

    public void setName(String name) {
        this.name = name;
    }

    public static List<OnlineReportDimEnum> all() {
        List<OnlineReportDimEnum> dims = new ArrayList<>();
        dims.add(ALL);
        return dims;
    }

    public static List<OnlineReportDimEnum> flightClass() {
        List<OnlineReportDimEnum> dims = new ArrayList<>();
        dims.add(DOMESTIC);
        dims.add(OVERSEA);
        return dims;
    }

    public static List<OnlineReportDimEnum> contractType() {
        List<OnlineReportDimEnum> dims = new ArrayList<>();
        dims.add(AGREEMENT);
        dims.add(NONE_AGREEMENT);
        return dims;
    }

    public static List<OnlineReportDimEnum> isOversea() {
        List<OnlineReportDimEnum> dims = new ArrayList<>();
        dims.add(INLAND);
        dims.add(ABROAD);
        return dims;
    }

    public static List<OnlineReportDimEnum> hotelOrderType() {
        List<OnlineReportDimEnum> dims = new ArrayList<>();
        dims.add(HOTEL_AGREEMENT);
        dims.add(MEMBER);
        return dims;
    }

    public static List<OnlineReportDimEnum> trainType() {
        List<OnlineReportDimEnum> dims = new ArrayList<>();
        dims.add(TRAIN_DOM);
        dims.add(TRAIN_INT);
        return dims;
    }

    public static List<OnlineReportDimEnum> carOrderType() {
        List<OnlineReportDimEnum> dims = new ArrayList<>();
        dims.add(DOM_TAXI);
        //dims.add(INTER_TAXI);
        dims.add(DOM_AIRPORT_TRANSPORTATION);
        //dims.add(INTER_AIRPORT_TRANSPORTATION);
        //dims.add(DOMESTIC_CHARTER);
        //dims.add(DOMESTIC_CAR_RENTAL);
        //dims.add(INTER_CHARTER);
        return dims;
    }

    public static List<OnlineReportDimEnum> getDims(String key) {
        return dims.get(key);
    }
}
