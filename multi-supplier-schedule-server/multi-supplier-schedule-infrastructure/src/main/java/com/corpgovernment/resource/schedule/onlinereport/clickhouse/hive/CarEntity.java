package com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/18 11:32
 * @Desc
 */
@Data
public class CarEntity {
    private Long order_id;// "订单号",
    private String order_status;//	"订单状态",
    private String order_date;//	"下单时间",
    private String corp_corporation;//	"公司ID",
    private String corp_name;//	"公司名称",
    private String companygroupid;//	"公司集团id",
    private String companygroup;//	"公司集团id",
    private Long account_id;//	"主账户ID",
    private String account_code;//	"开户账号名称",
    private String account_name;//	"公司名称",
    private Long sub_account_id;//	"子账户ID",
    private String sub_account_code;//	"子账户名称",
    private String sub_account_name;//	"子账户公司名称",
    private String industry_type;//	"行业",
    private String industry_type_name;//	"工业类型描述",
    private String uid;//	"卡号",
    private String user_name;//	"持卡人姓名",
    private String employe_id;//	"员工id",
    private String work_city;//	"工作城市",
    private String rank_name;//	"职级名称",
    private String cost_center1;//	"成本中心1",
    private String cost_center2;//	"成本中心2",
    private String cost_center3;//	"成本中心3",
    private String cost_center4;//	"成本中心4",
    private String cost_center5;//	"成本中心5",
    private String cost_center6;//	"成本中心6",
    private String dept1;//	"部门1",
    private String dept2;//	"部门2",
    private String dept3;//	"部门3",
    private String dept4;//	"部门4",
    private String dept5;//	"部门5",
    private String dept6;//	"部门6",
    private String dept7;//	"部门7",
    private String dept8;//	"部门8",
    private String dept9;//	"部门9",
    private String dept10;//	"部门10",
    private String fee_type;//	"是否个人消费行为:因公,因私",
    private String is_online;//	"预订方式",
    private String prepay_type;//	"支付方式（ACCNT:公司账户CCARD:信用卡",
    private String acb_prepay_type;//	"结算类型",
    private String bosstype;//	"boss类型",
    private Long trip_id;//	"行程号",
    private String journey_no;//	"关联行程单号",
    private String journey_reason;//	"出行目的",
    private String journey_reason_code;//	"出行目的编号",
    private String project_code;//	"项目号编号",
    private String project;//	"项目",
    private String verbal_authorize;//	"口头授权次数",
    private String confirm_person;//	"一次授权人UID",
    private String confirm_type;//	"当前审批方式（位运算）",
    private String confirm_person2;//	"二次授权人UID",
    private String confirm_type2;//	"当前审批方式2（位运算）",
    private Integer order_type;//	"订单类型（1:国内接送4：国内自驾）",
    private String payment_status;//	"支付状态（支付中：PP支付失败：PF支付",
    private Integer group_month;//	"年月",
    private Integer print_month;//	"出票、退票审核月份",
    private Integer print_year;//	"出票日期-年",
    private Integer quantity;//	"订车数量，目前恒等于1",
    private String passenger_name;//	"乘客姓名",
    private String passenger_name_py;//	"乘客姓名",
    private Integer persons;//	"出行人数",
    private String contact_name;//	"联系人名",
    private Float estimate_amount;//	"预估金额",
    private Float real_pay;//	"实际支付金额",
    private Float basic_fee;//	"应收金额",
    private Float service_fee;//	"商旅服务费",
    private Float refund_amount;//	"退款金额",
    private String departure_city_id;//	"出发城市id",
    private String departure_city_name;//	"出发城市",
    private String arrival_city_id;//	"到达城市id",
    private String arrival_city_name;//	"到达城市名称",
    private String address;//	"地址",
    private String fixed_location_name;//	"固定点名称",
    private String flight_train_num;//	"接送机航班,",
    private String pattern_type;//	"用车形态17：接18：送",
    private String start_address;//	"出发地址",
    private String start_address_des;//	"出发地址",
    private String start_time;//	"开始时间",
    private String end_address;//	"到达地址",
    private String end_address_des;//	"到达地址",
    private String end_time;//	"结束时间",
    private Integer estimate_distance;//	"预计里程-公里数,仅打车",
    private Float normal_distance;//	"公里数",
    private String use_duration;//	"用车天数,",
    private String use_type;//	"1:市内,2:跨城,",
    private Integer vehicle_id;//	"1:快车,其他:专车",
    private String vehicle_name;//	"车型",
    private String vehicle_type;//	"车型",
    private String vendor_id;//	"供应商id",
    private String vendor_name;//	"供应商名称",
    private String userdefined_rid;//	"用户自定义RCcode",
    private String userdefined_rc;//	"用户自定义RC说明",
    private String datachange_lasttime;//	"创建时间",
    private Long report_etltime;// '同步job时间'
    private String scenename;//用车场景
    private String o_currency;//原币种(客户支付币种)
    private Float o_exchangerate;//汇率(客户支付币种汇率)

    private String approvalpasstime;//授权通过时间
    private String actionname;//授权结果
    private String defineflag;//自定义成本中心
    private String defineflag2;//自定义成本中心2
    private String confirmtimepoint;//一次授权时间
    private String confirmtimepoint2;//二次授权时间
    private String vendororderid;//	"供应商流水号",

    private String actual_startservicetime;//开始服务时间
    private String actual_driveduration;//实际行驶时长
    private Float cancelfee_rate;//	有损取消比例,
    private String package_name;//套餐名称
    private String actual_start_address;//实际出发地址
    private String actual_end_address;//实际到达地址
    private Long cnt_order;// 订单数

    private String auditorid; // 一次授权人uid
    private String auditorid2; // 二次授权人uid

    private Integer is_abnormal;//	"疑似敏感订单",
    private Integer is_abnormal_userconfirm;//	"疑似敏感且客户确认订单",

    private String ismixpayment; // 是否混付

    private Float settlementaccntamt;//混付公司账户支付

    private Float settlementpersonamt;//混付个人账户支付

    private String sub_product_line;// 用车订单子类型

    private String bookingtype_des;// 预订类型

    private String service_time;// 用车服务时长

    private String online_hit_abnormal_rule_desc;// 明细线上命中敏感订单场景

    private Integer carbon_emission; // 碳排放

    private Integer isgreencar; // 是否绿色新能源车(1是0否)

    private String std_industry1;//	行业大类,

    private String std_industry2;//	行业小类,

}
