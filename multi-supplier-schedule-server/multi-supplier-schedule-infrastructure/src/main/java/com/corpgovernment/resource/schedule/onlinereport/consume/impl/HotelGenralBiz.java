package com.corpgovernment.resource.schedule.onlinereport.consume.impl;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.HotelConsume;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.OnlineReportConsumeBO;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportConsumeDao;
import com.corpgovernment.resource.schedule.onlinereport.consume.AbstractGenralConsume;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Service
public class HotelGenralBiz extends AbstractGenralConsume {

    @Autowired
    private OnlineReportConsumeDao reportConsumeDao;

    @Override
    protected BigDecimal getTotalAmount1(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneAmount())
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalTwoAmount()));
    }

    @Override
    protected Integer getTotalQuantity1(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneQuantity())
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalTwoQuantity()));
    }

    @Override
    protected BigDecimal getTotalAmount3(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalThreeAmount())
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFourAmount()));
    }

    @Override
    protected Integer getTotalQuantity3(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalThreeQuantity())
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFourQuantity()));
    }

    @Override
    protected OnlineReportConsumeBO aggreationGenralConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        List<HotelConsume> hotelGenralDTOList = (List<HotelConsume>) reportConsumeDao.aggreationHotelWithCondition(baseQueryConditionDto, HotelConsume.class, false);
        return convert(hotelGenralDTOList);
    }

    @Override
    protected OnlineReportConsumeBO aggreationCurrentConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        List<HotelConsume> hotelGenralDTOList = (List<HotelConsume>) reportConsumeDao.aggreationHotelWithCondition(baseQueryConditionDto, HotelConsume.class, true);
        OnlineReportConsumeBO genralConsume = convert(hotelGenralDTOList);
        // 商旅服务费
        genralConsume.setTotalCorpServiceFee(OrpReportUtils.formatBigDecimal(new BigDecimal(hotelGenralDTOList.stream()
                .mapToDouble(i -> Optional.ofNullable(i.getTotalCorpServiceFee()).orElse(0d)).sum())));

        Double totalRoomPrice = hotelGenralDTOList.stream()
                .mapToDouble(i -> Optional.ofNullable(i.getTotalRoomPrice()).orElse(0d)).sum();
        int totalQuantity = hotelGenralDTOList.stream()
                .mapToInt(i -> Optional.ofNullable(i.getTotalQuantity()).orElse(0)).sum();
        // 间夜均价
        genralConsume.setAvgPrice(OrpReportUtils.divide(new BigDecimal(totalRoomPrice)
                , new BigDecimal(totalQuantity), OrpConstants.TWO));
        // 退订间夜
        genralConsume.setTotalRefundQuantity(hotelGenralDTOList.stream().mapToInt(i -> Optional.ofNullable(i.getRefundQuantity()).orElse(0)).sum());
        /* 订单数
        List<HotelConsume> hotelOrderNumList = reportConsumeDao. aggreationHotelOrderNumWithCondition(baseQueryConditionDto, HotelConsume.class);
        genralConsume.setTotalCntOrder(hotelOrderNumList.stream().mapToInt(i-> Optional.ofNullable(i.getTotalCntOrder()).orElse(0)).sum());
         */
        return genralConsume;
    }

    private OnlineReportConsumeBO convert(List<HotelConsume> hotelGenralDTOList) {
        OnlineReportConsumeBO genralConsume = new OnlineReportConsumeBO();
        Double currentTotalAmount = 0d;
        Integer currentTotalQuantity = 0;
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        if (CollectionUtils.isNotEmpty(hotelGenralDTOList)) {
            currentTotalAmount = hotelGenralDTOList.stream().mapToDouble(HotelConsume::getTotalAmount).filter(Objects::nonNull).sum();
            currentTotalQuantity = hotelGenralDTOList.stream().mapToInt(HotelConsume::getTotalQuantity).filter(Objects::nonNull).sum();
            // 国内-港澳台
            List<HotelConsume> fList = hotelGenralDTOList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getIsOversea(), "F")
                    || StringUtils.equalsIgnoreCase(i.getIsOversea(), "O")).collect(Collectors.toList());
            genralConsume.setTotalOneAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(fList.stream().mapToDouble(HotelConsume::getTotalAmount).sum())));
            genralConsume.setTotalOneQuantity(fList.stream().mapToInt(HotelConsume::getTotalQuantity).sum());
            // 海外
            List<HotelConsume> tList = hotelGenralDTOList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getIsOversea(), "T")).collect(Collectors.toList());
            genralConsume.setTotalTwoAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(tList.stream().mapToDouble(HotelConsume::getTotalAmount).sum())));
            genralConsume.setTotalTwoQuantity(tList.stream().mapToInt(HotelConsume::getTotalQuantity).sum());
            // 三方协议
            List<HotelConsume> cList = hotelGenralDTOList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getOrderType(), ta)).collect(Collectors.toList());
            genralConsume.setTotalThreeAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(cList.stream().mapToDouble(HotelConsume::getTotalAmount).sum())));
            genralConsume.setTotalThreeQuantity(cList.stream().mapToInt(HotelConsume::getTotalQuantity).sum());
            // 非三方协议
            List<HotelConsume> mList = hotelGenralDTOList.stream().filter(i -> !StringUtils.equalsIgnoreCase(i.getOrderType(), ta)).collect(Collectors.toList());
            genralConsume.setTotalFourAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(mList.stream().mapToDouble(HotelConsume::getTotalAmount).sum())));
            genralConsume.setTotalFourQuantity(mList.stream().mapToInt(HotelConsume::getTotalQuantity).sum());
        }
        genralConsume.setTotalAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(currentTotalAmount)));
        genralConsume.setTotalQuantity(currentTotalQuantity);
        genralConsume.setTotalAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(currentTotalAmount)));
        genralConsume.setTotalQuantity(currentTotalQuantity);
        return genralConsume;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum s) {
        return QueryReportBuTypeEnum.hotel == s;
    }
}
