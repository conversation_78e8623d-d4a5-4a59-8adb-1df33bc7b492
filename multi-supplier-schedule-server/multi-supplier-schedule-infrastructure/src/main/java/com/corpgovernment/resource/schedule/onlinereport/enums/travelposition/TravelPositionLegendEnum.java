package com.corpgovernment.resource.schedule.onlinereport.enums.travelposition;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.travelposition
 * @description:
 * @author: md_wang
 * @create: 2022-08-22 16:35
 **/
public enum TravelPositionLegendEnum {
    /**
     * 订单号
     */
    ORDER_ID("orderId", "TravelPosition.order"),

    /**
     * 订单类型
     */
    ORDER_TYPE("orderType", "Index.ordertype"),

    /**
     * 预订人卡号
     */
    UID("uid", "Exceltopname.uidnumber"),
    /**
     * 预订人姓名
     */
    UID_NAME("uidName", "UserSurvey.Q1.Opt5"),
    /**
     * 出行人姓名
     */
    PASSENGER_NAME("passengerName", "TravelPosition.passenger"),
    /**
     * 航班号/火车班次/酒店名
     */
    FLIGHTNO_TRAINNO_HOTELNAME("flightnoTrainnoHotelname", "TripMap.ProductType"),
    /**
     * 出发省份
     */
    START_PROVINCE_NAME("startProvinceName", "Exceltopname.depatureprovince"),
    /**
     * 出发城市
     */
    START_CITY_NAME("startCityName", "Index.depcity"),
    /**
     * 到达省份
     */
    END_PROVINCE_NAME("endProvinceName", "Exceltopname.arrvialprovince"),

    /**
     * 到达城市
     */
    END_CITY_NAME("endCityName", "Index.arrcity"),

    /**
     * 行程开始时间
     */
    START_SUB_TRIP_DATE("startSubTripDate", "TripMap.TripStartTime"),
    /**
     * 行程结束时间
     */
    END_SUB_TRIP_DATE("endSubTripDate", "TripMap.TripEndTime"),
    /**
     * 出发时间/入住时间
     */
    START_TIME("startTime", "TripMap.DepartureTime"),
    /**
     * 到达时间/离店时间
     */
    END_TIME("endTime", "TripMap.ArriveTime"),

    DEPT1("dept1", "Exceltopname.depone"),
    COSTCENTER1("costcenter1", "Exceltopname.costcenterone"),

    // 以下字段需求删除 【2】在线报告-差旅定位功能调整 ( https://idev.ctripcorp.com?5976435 )
    /*DEPT2("dept2", "Exceltopname.deptwo"),
    DEPT3("dept3", "Exceltopname.depthree"),
    DEPT4("dept4", "Exceltopname.depfour"),
    DEPT5("dept5", "Exceltopname.depfive"),
    DEPT6("dept6", "Exceltopname.depsix"),
    DEPT7("dept7", "Exceltopname.depseven"),
    DEPT8("dept8", "Exceltopname.depeight"),
    DEPT9("dept9", "Exceltopname.depnight"),
    DEPT10("dept10", "Exceltopname.depten"),
    COSTCENTER2("costcenter2", "Exceltopname.costcentertwo"),
    COSTCENTER3("costcenter3", "Exceltopname.costcenterthree"),
    COSTCENTER4("costcenter4", "Exceltopname.costcenterfour"),
    COSTCENTER5("costcenter5", "Exceltopname.costcenterfive"),
    COSTCENTER6("costcenter6", "Exceltopname.costcentersix"),*/
    ;

    private String key;
    private String sharkKey;

    TravelPositionLegendEnum(String key, String sharkKey) {
        this.key = key;
        this.sharkKey = sharkKey;
    }


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public void setSharkKey(String sharkKey) {
        this.sharkKey = sharkKey;
    }
}
