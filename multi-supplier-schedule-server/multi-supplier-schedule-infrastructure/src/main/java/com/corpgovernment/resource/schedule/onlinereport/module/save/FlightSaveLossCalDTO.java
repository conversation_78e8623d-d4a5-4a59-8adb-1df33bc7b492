package com.corpgovernment.resource.schedule.onlinereport.module.save;


import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FlightAvgMileageDist;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FlightAvgTicketPriceDist;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FlightDiscountDist;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FlightPositionDist;
import lombok.Data;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.save
 * @description:
 * @author: md_wang
 * @create: 2022-08-03 23:01
 **/
@Data
public class FlightSaveLossCalDTO {

    /**
     * 查询机票仓位分布
     * * sum(经济舱/头等、公务舱票张数)/(票张数）
     * * sum(case when class_type = 'Y' then quantity else 0 end )/ SUM(quantity)
     */
    private FlightPositionDist flightPositionDist;

    /**
     * 机票折扣分布 折扣*张数/张数
     */
    private FlightDiscountDist flightDiscountDist;

    /**
     * 机票平均票价分布
     */
    private FlightAvgTicketPriceDist flightAvgTicketPriceDist;
    /**
     * 机票里程均价分布
     */
    private FlightAvgMileageDist flightAvgMileageDist;

    public FlightSaveLossCalDTO(FlightPositionDist flightPositionDist, FlightDiscountDist flightDiscountDist) {
        this.flightPositionDist = flightPositionDist;
        this.flightDiscountDist = flightDiscountDist;
    }

    public FlightSaveLossCalDTO(FlightAvgTicketPriceDist flightAvgTicketPriceDist,
                                FlightAvgMileageDist flightAvgMileageDist) {
        this.flightAvgTicketPriceDist = flightAvgTicketPriceDist;
        this.flightAvgMileageDist = flightAvgMileageDist;
    }
}
