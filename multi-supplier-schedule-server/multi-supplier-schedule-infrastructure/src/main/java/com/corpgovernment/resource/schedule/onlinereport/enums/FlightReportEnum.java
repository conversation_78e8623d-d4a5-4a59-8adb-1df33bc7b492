package com.corpgovernment.resource.schedule.onlinereport.enums;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-09 19:57
 **/
public enum FlightReportEnum {

    /**
     * 国内
     */
    DOMESTIC("N"),
    /**
     * 国际
     */
    INTERNATIONAL("I"),
    /**
     * (会员) 非协议
     */
    NOT_AGREEMENT("NC"),
    /**
     * 协议
     */
    AGREEMENT("C");
    private String name;

    FlightReportEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


}
