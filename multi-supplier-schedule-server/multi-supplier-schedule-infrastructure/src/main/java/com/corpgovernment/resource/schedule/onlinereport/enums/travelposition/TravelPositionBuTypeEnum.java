package com.corpgovernment.resource.schedule.onlinereport.enums.travelposition;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.travelposition
 * @description:差旅定位bu缩写
 * @author: md_wang
 * @create: 2022-08-18 16:36
 **/
public enum TravelPositionBuTypeEnum {
    /**
     * htl
     */
    HTL("htl"),
    /**
     * flt
     */
    FLT("flt"),
    /**
     * train
     */
    TRAIN("train");

    private String bu;

    TravelPositionBuTypeEnum(String bu) {
        this.bu = bu;
    }


    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }
}
