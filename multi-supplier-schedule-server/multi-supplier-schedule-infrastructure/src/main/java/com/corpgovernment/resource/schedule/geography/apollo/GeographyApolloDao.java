package com.corpgovernment.resource.schedule.geography.apollo;

import cn.hutool.log.Log;
import com.corpgovernment.common.common.CorpBusinessException;
import com.ctrip.corp.obt.generic.utils.StringUtils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zhang
 * @date 2023/12/20 14:04
 */
@Slf4j
@Data
@Component
public class GeographyApolloDao {

    public static final String SUPPLIER_PRE_ENVIRONMENT = "PRE";
    private static final Integer GEOGRAPHY_APOLLO_ERROR_CODE = 61000;
    /**
     * 基础地理信息认证信息
     */
    @Value("${supplier_distinguish_pre_uat: }")
    private String supplierDistinguishPreUat;
    /**
     * 缓存数据同步更新：数据状态redisKey
     */
    @Value("${supplier_geography_redis_city_status_key: }")
    private String supplierGeographyRedisCityStatusKey;

    public Boolean getSupplierDistinguishPreUat() {
        log.info("supplierDistinguishPreUat {}", supplierDistinguishPreUat);
        if (StringUtils.isBlank(supplierDistinguishPreUat)) {
            throw new CorpBusinessException(GEOGRAPHY_APOLLO_ERROR_CODE, "SupplierDistinguishPreUat is not found");
        }
        return SUPPLIER_PRE_ENVIRONMENT.equals(supplierDistinguishPreUat);
    }

    public String getSupplierGeographyRedisCityStatusKey() {
        if (StringUtils.isBlank(supplierGeographyRedisCityStatusKey)) {
            throw new CorpBusinessException(GEOGRAPHY_APOLLO_ERROR_CODE, "SupplierGeographyRedisCityStatusKey is not found");
        }
        return this.supplierGeographyRedisCityStatusKey;
    }


}
