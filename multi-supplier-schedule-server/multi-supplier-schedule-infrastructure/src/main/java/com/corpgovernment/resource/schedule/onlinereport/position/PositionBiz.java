package com.corpgovernment.resource.schedule.onlinereport.position;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.EventCityInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.EventInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.EventPositionInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HtlCityInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopCityRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopHotelRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTravelPassengerRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTravelPositionRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTravelPositionSummaryRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PositionInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TopHotelInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TopTripCityInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelEventPositionInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelEventPositionTrackInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelGeneralInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPassengerInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionSummaryInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelTrackInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TripCityInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.position.EventDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.position.RiskAreaDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.position.TopCityInfoDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.position.TopHotelInfoDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.position.TripCityInfoDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.position.TripInfoDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.position.TripPassengerCityInfoDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.position.TripPassengerInfoDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.position.TripTrackInfoDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.PositionSrDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.deadprice.DeadPriceCalculatorSrDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.geo.CorpGeoLocationDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.position.PositionDao;
import com.corpgovernment.resource.schedule.onlinereport.common.JacksonMapper;
import com.corpgovernment.resource.schedule.onlinereport.common.StarRocksSwitchConfig;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.convert.TrvavelPassengerInfoMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.GeoCategoryEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.RiskAreaEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.geo.GeoLocationDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022-08-18 14:37
 */
@Service
public class PositionBiz {

    @Autowired
    private PositionDao positionDao;

    @Autowired
    private PositionSrDao positionSrDao;

    @Autowired
    private DeadPriceCalculatorSrDaoImpl deadPriceCalculatorSrDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private TrvavelPassengerInfoMapper trvavelPassengerInfoMapper;

    @Autowired
    private CorpGeoLocationDao corpGeoLocationDao;

    @Autowired
    private StarRocksSwitchConfig starRocksSwitchConfig;


    @Qualifier("screenThreadPoolExecutor")
    @Resource
    private ThreadPoolExecutor executorService;


    public TravelEventPositionInfo positionInfo(OnlineReportTravelPositionRequest request) throws Exception {
        BaseQueryConditionDTO conditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<EventDTO> eventDTOList = null;
        // 蓝色空间不支持event
        if (!BlueSpaceUtils.isForeign(conditionDTO.getPos(), conditionDTO.getBlueSpace())) {
            //eventDTOList = positionDao.queryEvent(conditionDTO, EventDTO.class, request.getProductType());
        }
        eventDTOList = Optional.ofNullable(eventDTOList).orElse(new ArrayList<>());
        Map extMap = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        Boolean isOpen = starRocksSwitchConfig.isOpenSrSwitch(PositionSrDao.class, "queryTrip");
        if (StringUtils.equalsIgnoreCase((String) extMap.get("index"), "FRONT_PAGE")) {
            // 大首页
            List<TripInfoDTO> tripInfoDTOList = null;
            if (isOpen) {
                tripInfoDTOList = positionSrDao.queryTrip(conditionDTO, TripInfoDTO.class, request.getQueryBu(), request.getProductType(), true, request.lang);
            } else {
                tripInfoDTOList = positionDao.queryTrip(conditionDTO, TripInfoDTO.class, request.getQueryBu(), request.getProductType(), true, request.lang);
            }
            Map cityMap = new HashMap<>();
            List<Integer> cityIds = eventDTOList.stream().map(EventDTO::getCityId).distinct().collect(Collectors.toList());
            // 非中文和英文，才走多语言
            if (!SharkUtils.isEN(request.getLang()) && !SharkUtils.isZH(request.getLang())) {
                List<Integer> tripCityIdlist = tripInfoDTOList.stream().map(TripInfoDTO::getCityId).distinct().collect(Collectors.toList());
                cityIds.addAll(tripCityIdlist);
            }
            cityMap = queryGeoLocationMap(cityIds, GeoCategoryEnum.CITY, request.lang);
            return mergeEventAndTripInfo(eventDTOList, tripInfoDTOList, cityMap);
        } else {
            Future<List<TripInfoDTO>> future1 = executorService.submit(new Callable<List<TripInfoDTO>>() {
                @Override
                public List<TripInfoDTO> call() throws Exception {
                    if (isOpen) {
                        return positionSrDao.queryTrip(conditionDTO, TripInfoDTO.class, request.getQueryBu(), request.getProductType(), true, request.getLang());
                    } else {
                        return positionDao.queryTrip(conditionDTO, TripInfoDTO.class, request.getQueryBu(), request.getProductType(), true, request.getLang());
                    }
                }
            });
            Future<TripInfoDTO> future2 = executorService.submit(new Callable<TripInfoDTO>() {
                @Override
                public TripInfoDTO call() throws Exception {
                    List<TripInfoDTO> sumList = null;
                    if (isOpen) {
                        sumList = positionSrDao.queryTrip(conditionDTO, TripInfoDTO.class, request.getQueryBu(), request.getProductType(), false, request.getLang());
                    } else {
                        sumList = positionDao.queryTrip(conditionDTO, TripInfoDTO.class, request.getQueryBu(), request.getProductType(), false, request.getLang());
                    }
                    return Optional.ofNullable(sumList).orElse(new ArrayList<>()).get(0);
                }
            });
            List<TripInfoDTO> tripInfoDTOList = future1.get();
            TripInfoDTO tripInfoDTO = future2.get();
            Map cityMap = new HashMap<>();
            List<Integer> cityIds = eventDTOList.stream().map(EventDTO::getCityId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            // 非中文和英文，才走多语言
            if (!SharkUtils.isEN(request.getLang()) && !SharkUtils.isZH(request.getLang())) {
                List<Integer> tripCityIdlist = tripInfoDTOList.stream().map(TripInfoDTO::getCityId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                cityIds.addAll(tripCityIdlist);
            }
            cityMap = queryGeoLocationMap(cityIds, GeoCategoryEnum.CITY, request.lang);
            return mergeEventAndTripInfo(eventDTOList, tripInfoDTOList, tripInfoDTO, cityMap, request.lang);
        }
    }


    public TravelPositionSummaryInfo positionSummaryInfo(OnlineReportTravelPositionSummaryRequest request) throws Exception {
        TravelPositionSummaryInfo summaryInfo = new TravelPositionSummaryInfo();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<TripInfoDTO> sumList = positionDao.queryTrip(baseQueryConditionDto, TripInfoDTO.class, null, request.getProductType(), false, "");
        TripInfoDTO tripInfoDTO = Optional.ofNullable(sumList).orElse(new ArrayList<>()).get(0);
        summaryInfo.setHCount(Optional.ofNullable(tripInfoDTO.getHCount()).orElse(0));
        summaryInfo.setMCount(Optional.ofNullable(tripInfoDTO.getMCount()).orElse(0));
        summaryInfo.setWCount(Optional.ofNullable(tripInfoDTO.getWCount()).orElse(0));
        return summaryInfo;
    }

    /**
     *  合并城市事件和行程信息
     *
     * @param eventDTOList 事件信息
     * @param tripInfoDTOList 出行信息
     */
    private TravelEventPositionInfo mergeEventAndTripInfo(List<EventDTO> eventDTOList, List<TripInfoDTO> tripInfoDTOList,
                                                          TripInfoDTO sumTripInfoDTO, Map cityMap, String lang) throws Exception {
        TravelEventPositionInfo travelEventPositionBO = mergeEventAndTripInfo(eventDTOList, tripInfoDTOList, cityMap);
        sumTripInfoDTO = Optional.ofNullable(sumTripInfoDTO).orElse(new TripInfoDTO());
        travelEventPositionBO.setTotalHcount(Optional.ofNullable(sumTripInfoDTO.getHCount()).orElse(0));
        travelEventPositionBO.setTotalMcount(Optional.ofNullable(sumTripInfoDTO.getMCount()).orElse(0));
        travelEventPositionBO.setTotalWcount(Optional.ofNullable(sumTripInfoDTO.getWCount()).orElse(0));
        travelEventPositionBO.setTotalHMcount(travelEventPositionBO.getTotalHcount() + travelEventPositionBO.getTotalMcount());
        travelEventPositionBO.setTotalHtlTripCnt(Optional.ofNullable(sumTripInfoDTO.getWHtlTripCnt()).orElse(0));
        return addRiskAreaEvent(travelEventPositionBO, lang);
    }

    /**
     *  合并城市事件和行程信息
     *
     * @param eventDTOList 事件信息
     * @param tripInfoDTOList 出行信息
     */
    private TravelEventPositionInfo mergeEventAndTripInfo(List<EventDTO> eventDTOList, List<TripInfoDTO> tripInfoDTOList, Map cityMap) {
        List<TravelPositionInfo> tripPositionList = new ArrayList<>();
        Optional.ofNullable(tripInfoDTOList).orElse(new ArrayList<>()).stream()
                .filter(i -> notEqualsZeroOrNull(i.getHCount()) || notEqualsZeroOrNull(i.getMCount()) || notEqualsZeroOrNull(i.getWCount()))
                .forEach(i -> {
                    TravelPositionInfo tripPosition = new TravelPositionInfo();
                    PositionInfo positionInfo = new PositionInfo();
                    positionInfo.setCountryId(i.getCountryId());
                    positionInfo.setCounttryName(i.getCountryName());
                    positionInfo.setCityId(i.getCityId());
                    // cityMap不为空并且有值，说明走了多语言
                    positionInfo.setCityName((MapUtils.isEmpty(cityMap) || StringUtils.isEmpty((String) cityMap.get(i.getCityId()))) ?
                            i.getCityName() : (String) cityMap.get(i.getCityId()));
                    positionInfo.setCityGlat(i.getCityGlat());
                    positionInfo.setCityGlon(i.getCityGlon());
                    tripPosition.setPosition(positionInfo);

                    tripPosition.setHCount(i.getHCount());
                    tripPosition.setMCount(i.getMCount());
                    tripPosition.setWCount(i.getWCount());
                    tripPositionList.add(tripPosition);
                });

        TravelEventPositionInfo travelEventPositionBO = new TravelEventPositionInfo();
        travelEventPositionBO.setTripPositionList(tripPositionList);
        travelEventPositionBO.setEventList(convertEventPositionAndAgg(eventDTOList, cityMap));
        return travelEventPositionBO;
    }


    public TravelEventPositionTrackInfo positionTrackInfo(OnlineReportTravelPositionRequest request) throws Exception {
        TravelEventPositionTrackInfo travelPositionTrackInfo = new TravelEventPositionTrackInfo();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<EventDTO> eventDTOList = null;
        // 蓝色空间不支持event
        if (!BlueSpaceUtils.isForeign(baseQueryConditionDto.getPos(), baseQueryConditionDto.getBlueSpace())) {
            // 不做事件查询
           // eventDTOList = positionDao.queryEvent(baseQueryConditionDto, EventDTO.class, request.getProductType());
        }
        List<TravelTrackInfo> fltlist = new ArrayList<>();
        List<TravelTrackInfo> trainlist = new ArrayList<>();
        AtomicReference<Integer> totalHMcount = new AtomicReference<>(0);
        AtomicReference<Integer> totalWcount = new AtomicReference<>(0);
        Boolean isOpen = starRocksSwitchConfig.isOpenSrSwitch(PositionSrDao.class, "queryTravelTrack");

        List<EventDTO> finalEventDTOList = Optional.ofNullable(eventDTOList).orElse(new ArrayList<>());
        Future<List<TripInfoDTO>> future1 = executorService.submit(new Callable<List<TripInfoDTO>>() {
            @Override
            public List<TripInfoDTO> call() throws Exception {
                List<TripTrackInfoDTO> tripInfoDTOList = null;
                if (isOpen) {
                    tripInfoDTOList = positionSrDao.queryTravelTrack(baseQueryConditionDto, TripTrackInfoDTO.class, request.getProductType(), true, request.getLang());
                } else {
                    tripInfoDTOList = positionDao.queryTravelTrack(baseQueryConditionDto, TripTrackInfoDTO.class, request.getProductType(), true, request.getLang());
                }
                Map cityMap = new HashMap();
                List<Integer> cityIds = finalEventDTOList.stream().map(EventDTO::getCityId).distinct().collect(Collectors.toList());
                // 非中文和英文，才走多语言
                if (!SharkUtils.isEN(request.getLang()) && !SharkUtils.isZH(request.getLang())) {
                    List<Integer> startTripCityIdlist = tripInfoDTOList.stream().map(TripTrackInfoDTO::getStartCityId).distinct().collect(Collectors.toList());
                    List<Integer> endTripCityIdlist = tripInfoDTOList.stream().map(TripTrackInfoDTO::getEndCityId).distinct().collect(Collectors.toList());
                    cityIds.addAll(startTripCityIdlist);
                    cityIds.addAll(endTripCityIdlist);
                }
                cityMap = queryGeoLocationMap(cityIds, GeoCategoryEnum.CITY, request.lang);
                travelPositionTrackInfo.setEventPositionList(convertEventPositionAndAgg(finalEventDTOList, cityMap));
                Map finalCityMap = cityMap;
                Optional.ofNullable(tripInfoDTOList).orElse(new ArrayList<>()).stream()
                        .filter(i -> Objects.nonNull(i.getTripCount()) && i.getTripCount() != 0)
                        .forEach(i -> {
                            if (StringUtils.equalsIgnoreCase(i.getOrderType(), "flt")) {
                                mergeTravelTrach(fltlist, convertTripTrackInfoDto2TravelTrack(i, i.getLineType(), finalCityMap, request.lang));
                            }
                            if (StringUtils.equalsIgnoreCase(i.getOrderType(), "train")) {
                                mergeTravelTrach(trainlist, convertTripTrackInfoDto2TravelTrack(i, i.getLineType(), finalCityMap, request.lang));
                            }
                        });
                return null;
            }
        });
        Future<TripInfoDTO> future2 = executorService.submit(new Callable<TripInfoDTO>() {
            @Override
            public TripInfoDTO call() throws Exception {
                List<TripTrackInfoDTO> sumTripInfoDTOList = null;
                if (isOpen) {
                    sumTripInfoDTOList = positionDao.queryTravelTrack(baseQueryConditionDto, TripTrackInfoDTO.class, request.getProductType(), false, request.getLang());
                } else {
                    sumTripInfoDTOList = positionDao.queryTravelTrack(baseQueryConditionDto, TripTrackInfoDTO.class, request.getProductType(), false, request.getLang());
                }
                Optional.ofNullable(sumTripInfoDTOList).orElse(new ArrayList<>()).stream().forEach(i -> {
                    if (StringUtils.equalsIgnoreCase(i.getLineType(), "h") || StringUtils.equalsIgnoreCase(i.getLineType(), "m")) {
                        totalHMcount.updateAndGet(v -> v + Optional.ofNullable(i.getTripCount()).orElse(0));
                    }
                    if (StringUtils.equalsIgnoreCase(i.getLineType(), "w")) {
                        totalWcount.updateAndGet(v -> v + Optional.ofNullable(i.getTripCount()).orElse(0));
                    }
                });
                return null;
            }
        });
        future1.get();
        future2.get();
        // 机票
        travelPositionTrackInfo.setFltList(fltlist);
        // 酒店
        travelPositionTrackInfo.setTrainList(trainlist);
        travelPositionTrackInfo.setTotalHMcount(totalHMcount.get());
        travelPositionTrackInfo.setTotalWcount(totalWcount.get());
        return travelPositionTrackInfo;
    }

    /**
     *  转化dto并且对同一个城市的的事件进行聚合
     * @param eventDTOList
     * @return
     */
    private List<EventPositionInfo> convertEventPositionAndAgg(List<EventDTO> eventDTOList, Map cityMap) {
        List<EventPositionInfo> eventPositionList = new ArrayList<>();
        Optional.ofNullable(eventDTOList).orElse(new ArrayList<>()).forEach(i -> {
            // 存在城市信息
            if (Objects.nonNull(i.getCityId()) && i.getCityId() != 0) {
                if (eventPositionList.stream().anyMatch(j -> compareInteger(i.getCityId(), j.getPosition().getCityId()))) {
                    eventPositionList.stream()
                            .filter(j -> compareInteger(i.getCityId(), j.getPosition().getCityId()))
                            .findFirst()
                            .ifPresent(k -> {
                                List<EventInfo> eventBOList = Optional.ofNullable(k.getEventList()).orElse(new ArrayList<>());
                                eventBOList.add(convertEventDto2EventInfo(i));
                                k.setEventList(eventBOList);
                            });
                } else {
                    PositionInfo positionInfo = convertEventDto2PositionInfo(i, cityMap);

                    EventPositionInfo eventPositionInfo = new EventPositionInfo();
                    eventPositionInfo.setPosition(positionInfo);
                    eventPositionInfo.setEventList(new ArrayList<EventInfo>() {{
                        add(convertEventDto2EventInfo(i));
                    }});
                    eventPositionList.add(eventPositionInfo);
                }
            } else { // 不存在城市信息
                if (eventPositionList.stream()
                        .anyMatch(j -> compareInteger(i.getProvinceId(), j.getPosition().getProvinceId())
                                && (j.getPosition().getCityId() == null || j.getPosition().getCityId() == 0))) {
                    eventPositionList.stream()
                            .filter(j -> compareInteger(i.getProvinceId(), j.getPosition().getProvinceId())
                                    && (j.getPosition().getCityId() == null || j.getPosition().getCityId() == 0))
                            .findFirst()
                            .ifPresent(k -> {
                                List<EventInfo> eventBOList = Optional.ofNullable(k.getEventList()).orElse(new ArrayList<>());
                                eventBOList.add(convertEventDto2EventInfo(i));
                                k.setEventList(eventBOList);
                            });
                } else {
                    PositionInfo positionInfo = convertEventDto2PositionInfo(i, cityMap);

                    EventPositionInfo eventPositionInfo = new EventPositionInfo();
                    eventPositionInfo.setPosition(positionInfo);
                    eventPositionInfo.setEventList(new ArrayList<EventInfo>() {{
                        add(convertEventDto2EventInfo(i));
                    }});
                    eventPositionList.add(eventPositionInfo);
                }
            }
        });
        return eventPositionList;
    }

    private EventInfo convertEventDto2EventInfo(EventDTO eventDTO) {
        EventInfo eventInfo = new EventInfo();
        eventInfo.setEventId(eventDTO.getEventId());
        eventInfo.setEventName(eventDTO.getEventName());
        eventInfo.setEventTime(eventDTO.getEventTime());
        eventInfo.setEventContent(eventDTO.getEventContent());
        eventInfo.setLevelTwoId(eventDTO.getLevelTwoId());
        eventInfo.setResponseLevel(eventDTO.getResponseLevel());
        return eventInfo;
    }

    private PositionInfo convertEventDto2PositionInfo(EventDTO eventDTO, Map cityMap) {
        PositionInfo positionInfo = new PositionInfo();
        positionInfo.setCountryId(eventDTO.getCountryId());
        positionInfo.setCounttryName(eventDTO.getCounttryName());
        positionInfo.setProvinceId(eventDTO.getProvinceId());
        positionInfo.setProvinceName(eventDTO.getProvinceName());
        positionInfo.setProvinceGlat(eventDTO.getProvinceGlat());
        positionInfo.setProvinceGlon(eventDTO.getProvinceGlon());
        positionInfo.setCityId(eventDTO.getCityId());
        // cityMap不是空并且有值，说明走了多语言
        positionInfo.setCityName((MapUtils.isEmpty(cityMap) || StringUtils.isEmpty((String) cityMap.get(eventDTO.getCityId()))) ?
                eventDTO.getCityName() : (String) cityMap.get(eventDTO.getCityId()));
        positionInfo.setCityGlat(eventDTO.getCityGlat());
        positionInfo.setCityGlon(eventDTO.getCityGlon());
        return positionInfo;
    }


    private TravelTrackInfo convertTripTrackInfoDto2TravelTrack(TripTrackInfoDTO tripInfoLineDTO, String lineType, Map cityMap, String lang) {
        TravelTrackInfo travelTrackBO = new TravelTrackInfo();
        PositionInfo start = new PositionInfo();
        start.setCityId(tripInfoLineDTO.getStartCityId());
        // cityMap不是空并且有值，说明走了多语言
        start.setCityName((MapUtils.isEmpty(cityMap) || StringUtils.isEmpty((String) cityMap.get(tripInfoLineDTO.getStartCityId()))) ? tripInfoLineDTO.getStartCityName()
                : (String) cityMap.get(tripInfoLineDTO.getStartCityId()));
        start.setCityGlat(tripInfoLineDTO.getStartCityGlat());
        start.setCityGlon(tripInfoLineDTO.getStartCityGlon());
        travelTrackBO.setStartPosition(start);

        PositionInfo end = new PositionInfo();
        end.setCityId(tripInfoLineDTO.getEndCityId());
        // cityMap不是空并且有值，说明走了多语言
        end.setCityName((MapUtils.isEmpty(cityMap) || StringUtils.isEmpty((String) cityMap.get(tripInfoLineDTO.getEndCityId()))) ? tripInfoLineDTO.getEndCityName()
                : (String) cityMap.get(tripInfoLineDTO.getEndCityId()));
        end.setCityGlat(tripInfoLineDTO.getEndCityGlat());
        end.setCityGlon(tripInfoLineDTO.getEndCityGlon());
        travelTrackBO.setEndPosition(end);
        if (StringUtils.equalsIgnoreCase(lineType, "h") || StringUtils.equalsIgnoreCase(lineType, "m")) {
            travelTrackBO.setTripHMcount(tripInfoLineDTO.getTripCount());
            travelTrackBO.setTripWcount(0);
        }
        if (StringUtils.equalsIgnoreCase(lineType, "w")) {
            travelTrackBO.setTripHMcount(0);
            travelTrackBO.setTripWcount(tripInfoLineDTO.getTripCount());
        }
        return travelTrackBO;
    }

    /**
     * 因tack不区分开始和结束城市，所以需要合并数据
     * @param travelLineList
     * @return
     */
    private void mergeTravelTrach(List<TravelTrackInfo> travelLineList, TravelTrackInfo trackInfo) {
        if (travelLineList.stream().anyMatch(i -> (compareInteger(i.getStartPosition().getCityId(), trackInfo.getStartPosition().getCityId())
                && compareInteger(i.getEndPosition().getCityId(), trackInfo.getEndPosition().getCityId()))
                || (compareInteger(i.getEndPosition().getCityId(), trackInfo.getStartPosition().getCityId())
                && compareInteger(i.getStartPosition().getCityId(), trackInfo.getEndPosition().getCityId())))) {
            travelLineList.stream().filter(i -> (compareInteger(i.getStartPosition().getCityId(), trackInfo.getStartPosition().getCityId())
                            && compareInteger(i.getEndPosition().getCityId(), trackInfo.getEndPosition().getCityId()))
                            || (compareInteger(i.getEndPosition().getCityId(), trackInfo.getStartPosition().getCityId())
                            && compareInteger(i.getStartPosition().getCityId(), trackInfo.getEndPosition().getCityId())))
                    .findFirst()
                    .ifPresent(k -> {
                        k.setTripHMcount(Optional.ofNullable(k.getTripHMcount()).orElse(0) + Optional.ofNullable(trackInfo.getTripHMcount()).orElse(0));
                        k.setTripWcount(Optional.ofNullable(k.getTripWcount()).orElse(0) + Optional.ofNullable(trackInfo.getTripWcount()).orElse(0));
                    });
        } else {
            travelLineList.add(trackInfo);
        }
    }

    /**
     * 出行人信息
     * @param request
     * @return
     * @throws Exception
     */

    public List<TravelPassengerInfo> passengerInfo(OnlineReportTravelPassengerRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        Boolean isOpen = starRocksSwitchConfig.isOpenSrSwitch(PositionSrDao.class, "queryTripPassenger");
        List<TripPassengerInfoDTO> tripInfoDTOList = null;
        if (isOpen) {
            tripInfoDTOList = positionSrDao.queryTripPassenger(baseQueryConditionDto, TripPassengerInfoDTO.class, request.getProductType());
        } else {
            tripInfoDTOList = positionDao.queryTripPassenger(baseQueryConditionDto, TripPassengerInfoDTO.class, request.getProductType());
        }
        if (CollectionUtils.isNotEmpty(tripInfoDTOList)) {
            return trvavelPassengerInfoMapper.tBOs(tripInfoDTOList);
        }
        return new ArrayList<>();
    }

    /**
     * 差旅定位概览
     * @param request
     * @return
     * @throws Exception
     */

    public TravelGeneralInfo positionGenralInfo(OnlineReportTravelPositionRequest request) throws Exception {
        TravelGeneralInfo travelGeneralInfo = new TravelGeneralInfo();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        Boolean isOpen = starRocksSwitchConfig.isOpenSrSwitch(PositionSrDao.class, "queryTopTrip");
        List<TripPassengerCityInfoDTO> topTripInfoDTOList = null;
        List<TripPassengerCityInfoDTO> topwTripInfoDTOList = null;
        if (isOpen) {
            // 正在出差
            topTripInfoDTOList = positionSrDao.queryTopTrip(baseQueryConditionDto, TripPassengerCityInfoDTO.class, request.getProductType(), "M", request.lang);
            // 将要去
            topwTripInfoDTOList = positionSrDao.queryTopTrip(baseQueryConditionDto, TripPassengerCityInfoDTO.class, request.getProductType(), "W", request.lang);
        } else {
            // 正在出差
            topTripInfoDTOList = positionDao.queryTopTrip(baseQueryConditionDto, TripPassengerCityInfoDTO.class, request.getProductType(), "M", request.lang);
            // 将要去
            topwTripInfoDTOList = positionDao.queryTopTrip(baseQueryConditionDto, TripPassengerCityInfoDTO.class, request.getProductType(), "W", request.lang);
        }
        Map cityMap = new HashMap();
        // 非中文和英文，才走多语言
        if (!SharkUtils.isEN(request.getLang()) && !SharkUtils.isZH(request.getLang())) {
            List<Integer> cityIds = topTripInfoDTOList.stream().map(TripPassengerCityInfoDTO::getCityId).distinct().collect(Collectors.toList());
            List<Integer> tripCityIdList = topwTripInfoDTOList.stream().map(TripPassengerCityInfoDTO::getCityId).distinct().collect(Collectors.toList());
            cityIds.addAll(tripCityIdList);
            cityMap = queryGeoLocationMap(cityIds, GeoCategoryEnum.CITY, request.lang);
        }
        travelGeneralInfo.setTopTripCityInfo(convertTopTrip(topTripInfoDTOList, cityMap));
        travelGeneralInfo.setTopWTripCityInfo(convertTopTrip(topwTripInfoDTOList, cityMap));
        return travelGeneralInfo;
    }

    /**
     * 差旅事件概览
     * @param request
     * @return
     * @throws Exception
     */

    public TravelGeneralInfo eventGenralInfo(OnlineReportTravelPositionRequest request) throws Exception {
        TravelGeneralInfo travelGeneralInfo = new TravelGeneralInfo();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<EventDTO> eventDTOList = null;
        // 蓝色空间不支持event
        if (!BlueSpaceUtils.isForeign(baseQueryConditionDto.getPos(), baseQueryConditionDto.getBlueSpace())) {
            eventDTOList = positionDao.queryTopEvent(baseQueryConditionDto, EventDTO.class);
        }
        eventDTOList = Optional.ofNullable(eventDTOList).orElse(new ArrayList<>());
        List<Integer> eventCityIds = eventDTOList
                .stream()
                .filter(i -> Objects.nonNull(i.getCityId()))
                .map(i -> i.getCityId()).collect(Collectors.toList());
        List<TripCityInfoDTO> tripInfoDTOList = null;
        Boolean isOpen = starRocksSwitchConfig.isOpenSrSwitch(PositionSrDao.class, "queryTripByCity");
        if (isOpen) {
            tripInfoDTOList = positionSrDao.queryTripByCity(baseQueryConditionDto, TripCityInfoDTO.class, request.getProductType(), eventCityIds, request.getLang());
        } else {
            tripInfoDTOList = positionDao.queryTripByCity(baseQueryConditionDto, TripCityInfoDTO.class, request.getProductType(), eventCityIds, request.getLang());
        }
        List<Integer> cityIds = eventDTOList.stream().map(EventDTO::getCityId).distinct().collect(Collectors.toList());
        // 非中文和英文，才走多语言
        if (!SharkUtils.isEN(request.getLang()) && !SharkUtils.isZH(request.getLang())) {
            List<Integer> tripCityIdList = tripInfoDTOList.stream().map(TripCityInfoDTO::getCityId).distinct().collect(Collectors.toList());
            cityIds.addAll(tripCityIdList);
        }
        Map cityMap = queryGeoLocationMap(cityIds, GeoCategoryEnum.CITY, request.lang);
        travelGeneralInfo.setTopEventCityInfoList(convertTopEvent(eventDTOList, tripInfoDTOList, cityMap));
        return travelGeneralInfo;
    }

    private List<EventCityInfo> convertTopEvent(List<EventDTO> eventDTOList, List<TripCityInfoDTO> tripInfoDTOList, Map cityMap) {
        List<EventCityInfo> list = new ArrayList<>();
        Optional.ofNullable(eventDTOList).orElse(new ArrayList<>()).forEach(i -> {
            EventCityInfo eventCityInfo = new EventCityInfo();
            eventCityInfo.setProvinceId(i.getProvinceId());
            eventCityInfo.setProvinceName(i.getProvinceName());
            eventCityInfo.setCityId(i.getCityId());
            eventCityInfo.setCityName((String) cityMap.get(i.getCityId()));
            eventCityInfo.setEventId(i.getEventId());
            eventCityInfo.setEventName(i.getEventName());
            eventCityInfo.setEventContent(i.getEventContent());
            eventCityInfo.setEventTime(i.getEventTime());
            eventCityInfo.setHCount(0);
            eventCityInfo.setMCount(0);
            eventCityInfo.setWCount(0);
            if (tripInfoDTOList.stream().anyMatch(j -> compareInteger(i.getCityId(), j.getCityId()))) {
                tripInfoDTOList.stream()
                        .filter(j -> compareInteger(i.getCityId(), j.getCityId()))
                        .findFirst()
                        .ifPresent(k -> {
                            eventCityInfo.setHCount(Optional.ofNullable(k.getHCount()).orElse(0));
                            eventCityInfo.setMCount(Optional.ofNullable(k.getMCount()).orElse(0));
                            eventCityInfo.setWCount(Optional.ofNullable(k.getWCount()).orElse(0));
                        });
            }
            list.add(eventCityInfo);
        });
        return list;
    }

    private TopTripCityInfo convertTopTrip(List<TripPassengerCityInfoDTO> topTripInfoDTOList, Map cityMap) {
        TopTripCityInfo topTripCity = new TopTripCityInfo();
        List<TripCityInfo> list = new ArrayList<>();
        AtomicReference<Integer> totalPassengerCount = new AtomicReference<>(0);
        Optional.ofNullable(topTripInfoDTOList).orElse(new ArrayList<>()).forEach(i -> {
            TripCityInfo tripCityInfo = new TripCityInfo();
            tripCityInfo.setCityId(i.getCityId());
            tripCityInfo.setCityName((String) cityMap.get(i.getCityId()));
            tripCityInfo.setTripCount(i.getTripCount());
            list.add(tripCityInfo);
            totalPassengerCount.updateAndGet(v -> v + Optional.ofNullable(i.getPassengerCount()).orElse(0));
        });
        topTripCity.setCityTripList(list);
        topTripCity.setPassengerCount(totalPassengerCount.get());
        return topTripCity;
    }

    /**
     * 比较两个Integer数据类型
     * @param a
     * @param b
     * @return
     */
    private boolean compareInteger(Integer a, Integer b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }
        return a.compareTo(b) == 0;
    }

    /**
     * 不等于0
     * @param a
     * @return
     */
    private boolean notEqualsZeroOrNull(Integer a) {
        if (a == null) {
            return false;
        }
        if (a == 0) {
            return false;
        }
        return true;
    }

    private Map queryGeoLocationMap(List<Integer> idList, GeoCategoryEnum categoryEnum, String lang) throws Exception {
       // return Maps.newHashMap();
        Map result = new HashMap();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(idList)) {
            return result;
        }
        lang = StringUtils.equalsIgnoreCase(lang, "zh-hk") ? "zh-CHT" : (StringUtils.equalsIgnoreCase(lang, "zh-cn") ? "zh-CHS" : lang);
        List<GeoLocationDTO> list = corpGeoLocationDao.queryGeoLocation(idList, lang, GeoLocationDTO.class, categoryEnum);
        list.stream().forEach(i -> result.put(i.getGeoId(), i.getGeoPlaceName()));
        return result;
    }


    public List<TopHotelInfo> queryTopHotel(OnlineReportTopHotelRequest request) throws Exception {
        List<TopHotelInfo> topHotelInfoBOS = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        Map extParams = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        String orderType = (String) extParams.get("htlOrderType");
        String contractType = (String) extParams.get("contractType");
        List<Integer> cityIds = baseQueryConditionDto.getCityIds();
        // 正在出差
        List<TopHotelInfoDTO> list = positionDao.queryTopHotel(baseQueryConditionDto, TopHotelInfoDTO.class, contractType, orderType, cityIds, request.getLang());
        for (Integer cityId : cityIds) {
            List<TopHotelInfoDTO> topHotelInfoDTOS = list.stream().filter(Objects::nonNull).filter(i -> cityId.compareTo(i.getCityId()) == 0).collect(Collectors.toList());
            int sumQuantity = 0;
            BigDecimal sumAmount = BigDecimal.ZERO;
            BigDecimal sumRoomPrice = BigDecimal.ZERO;
            for (TopHotelInfoDTO topHotelInfoDTO : topHotelInfoDTOS) {
                sumQuantity += topHotelInfoDTO.getTotalQuantity();
                sumAmount = sumAmount.add(Optional.ofNullable(topHotelInfoDTO.getTotalAmount()).orElse(BigDecimal.ZERO));
                sumRoomPrice = sumRoomPrice.add(Optional.ofNullable(topHotelInfoDTO.getTotalRoomPrice()).orElse(BigDecimal.ZERO));
            }
            List<TopHotelInfoDTO> top3 = topHotelInfoDTOS.size() > 3 ? topHotelInfoDTOS.subList(0, 3) : topHotelInfoDTOS;
            int sumTop3Quantity = 0;
            BigDecimal sumTop3Amount = BigDecimal.ZERO;
            BigDecimal sumTop3RoomPrice = BigDecimal.ZERO;
            for (TopHotelInfoDTO topHotelInfoDTO : top3) {
                TopHotelInfo topHotelInfoBO = new TopHotelInfo();
                sumTop3Quantity += topHotelInfoDTO.getTotalQuantity();
                sumTop3Amount = sumTop3Amount.add(Optional.ofNullable(topHotelInfoDTO.getTotalAmount()).orElse(BigDecimal.ZERO));
                sumTop3RoomPrice = sumTop3Amount.add(Optional.ofNullable(topHotelInfoDTO.getTotalRoomPrice()).orElse(BigDecimal.ZERO));
                topHotelInfoBO.setCityId(cityId);
                topHotelInfoBO.setStar(topHotelInfoDTO.getStar());
                topHotelInfoBO.setHotelName(topHotelInfoDTO.getHtlName());
                topHotelInfoBO.setTotalAmount(OrpReportUtils.formatBigDecimal(topHotelInfoDTO.getTotalAmount()));
                topHotelInfoBO.setTotalQuantity(topHotelInfoDTO.getTotalQuantity());
                topHotelInfoBO.setAmountPercent(OrpReportUtils.divideWithPercent(topHotelInfoDTO.getTotalAmount(), sumAmount).doubleValue());
                topHotelInfoBO.setQuantityPercent(OrpReportUtils.divideWithPercent(topHotelInfoDTO.getTotalQuantity(), sumQuantity).doubleValue());
                topHotelInfoBO.setAvgPrice(OrpReportUtils.divide(topHotelInfoDTO.getTotalRoomPrice(),
                        new BigDecimal(Optional.ofNullable(topHotelInfoDTO.getTotalQuantity()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
                topHotelInfoBOS.add(topHotelInfoBO);
            }
            if (topHotelInfoDTOS.size() > 3) {
                TopHotelInfo topHotelInfoBO = new TopHotelInfo();
                topHotelInfoBO.setCityId(cityId);
                topHotelInfoBO.setHotelName(SharkUtils.get("Index.others", request.getLang()));
                topHotelInfoBO.setTotalAmount(OrpReportUtils.formatBigDecimal(sumAmount.subtract(sumTop3Amount)));
                topHotelInfoBO.setTotalQuantity(sumQuantity - sumTop3Quantity);
                topHotelInfoBO.setAmountPercent(OrpReportUtils.divideWithPercent(topHotelInfoBO.getTotalAmount(), sumAmount).doubleValue());
                topHotelInfoBO.setQuantityPercent(OrpReportUtils.divideWithPercent(topHotelInfoBO.getTotalQuantity(), sumQuantity).doubleValue());
                topHotelInfoBO.setAvgPrice(OrpReportUtils.divide(sumRoomPrice.subtract(sumTop3RoomPrice),
                        new BigDecimal(topHotelInfoBO.getTotalQuantity()), OrpConstants.TWO));
                topHotelInfoBOS.add(topHotelInfoBO);
            }
        }

        return topHotelInfoBOS;
    }


    public List<HtlCityInfo> queryTopCity(OnlineReportTopCityRequest request) throws Exception {
        List<HtlCityInfo> htlCityInfos = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        Map extParams = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        String orderType = (String) extParams.get("htlOrderType");
        String contractType = (String) extParams.get("contractType");
        // 如过只有在一个城市直接返回
        if (CollectionUtils.isNotEmpty(baseQueryConditionDto.getCityIds()) && baseQueryConditionDto.getCityIds().size() == 1) {
            Map cityMap = queryGeoLocationMap(baseQueryConditionDto.getCityIds(), GeoCategoryEnum.CITY, request.lang);
            HtlCityInfo htlCityInfo = new HtlCityInfo();
            htlCityInfo.setCityId(baseQueryConditionDto.getCityIds().get(0));
            htlCityInfo.setCityName((String) cityMap.get(htlCityInfo.getCityId()));
            htlCityInfos.add(htlCityInfo);
            return htlCityInfos;
        }
        // top城市
        List<TopCityInfoDTO> topHotelInfoDTOS = positionDao.queryTopCity(baseQueryConditionDto, TopCityInfoDTO.class, contractType, orderType);
        List<Integer> tripCityIdlist = topHotelInfoDTOS.stream().map(TopCityInfoDTO::getCityId).distinct().collect(Collectors.toList());
        Map cityMap = queryGeoLocationMap(tripCityIdlist, GeoCategoryEnum.CITY, request.lang);
        for (TopCityInfoDTO topCityInfoDTO : topHotelInfoDTOS) {
            HtlCityInfo htlCityInfo = new HtlCityInfo();
            htlCityInfo.setCityId(topCityInfoDTO.getCityId());
            htlCityInfo.setCityName((String) cityMap.get(topCityInfoDTO.getCityId()));
            htlCityInfos.add(htlCityInfo);
        }
        return htlCityInfos;
    }

    private TravelEventPositionInfo addRiskAreaEvent(TravelEventPositionInfo positionInfo, String lang) throws Exception {
        JacksonMapper jacksonMapper = JacksonMapper.DEFAULT;
        String serialize = jacksonMapper.toJson(positionInfo);
        TravelEventPositionInfo copyPositionInfo = jacksonMapper.parse(serialize, new TypeReference<TravelEventPositionInfo>() {
        });
        List<RiskAreaDto> riskAreaEntities = deadPriceCalculatorSrDao.searchRiskArea(RiskAreaDto.class, OrpDateTimeUtils.getCurrentDate());

        Map<Integer, RiskAreaDto> countryRiskEntityMap = riskAreaEntities.stream().filter(area -> Objects.isNull(area.getCityId()))
                .filter(area -> Objects.nonNull(area.getCountryId())).collect(Collectors.toMap(RiskAreaDto::getCountryId, Function.identity()));
        Map<Integer, RiskAreaDto> cityRiskEntityMap = riskAreaEntities.stream()
                .filter(area -> Objects.nonNull(area.getCityId())).collect(Collectors.toMap(RiskAreaDto::getCityId, Function.identity()));

        List<TravelPositionInfo> validPositionList = copyPositionInfo.getTripPositionList().stream().filter(position -> null != position.getPosition()
                && null != position.getPosition().getCityId()).collect(Collectors.toList());
        Map<TravelPositionInfo, Integer> positionRiskTypeMapOnCountry = validPositionList.stream()
                .filter(position -> null != position.getPosition().getCountryId())
                .filter(position -> countryRiskEntityMap.containsKey(position.getPosition().getCountryId()))
                .collect(Collectors.toMap(Function.identity(), position -> countryRiskEntityMap.get(position.getPosition().getCountryId()).getRiskType()));
        Map<TravelPositionInfo, Integer> positionRiskTypeMapOnCity = validPositionList.stream().filter(position ->
                cityRiskEntityMap.containsKey(position.getPosition().getCityId())).collect(Collectors.toMap(Function.identity(),
                position -> cityRiskEntityMap.get(position.getPosition().getCityId()).getRiskType()));
        positionRiskTypeMapOnCountry.putAll(positionRiskTypeMapOnCity);

        for (Map.Entry<TravelPositionInfo, Integer> entry : positionRiskTypeMapOnCountry.entrySet()) {
            EventPositionInfo eventPositionInfo = buildRiskAreaEvent(entry.getKey().getPosition(), entry.getValue(), lang);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(copyPositionInfo.eventList)) {
                copyPositionInfo.setEventList(Lists.newArrayList());
            }
            copyPositionInfo.getEventList().add(eventPositionInfo);
        }

        return copyPositionInfo;
    }

    private EventPositionInfo buildRiskAreaEvent(PositionInfo value, Integer riskType, String lang) {

        EventPositionInfo eventPositionInfo = new EventPositionInfo();
        eventPositionInfo.setPosition(value);
        List<EventInfo> eventInfoList = new ArrayList<>();
        eventInfoList.add(new EventInfo(-9999L, SharkUtils.get(RiskAreaEnum.getSharkKeyByCode(riskType), lang),
                "", SharkUtils.get("Risk.Area.Reminder.Content", lang), -9999, riskType));
        eventPositionInfo.setEventList(eventInfoList);
        return eventPositionInfo;
    }

}
