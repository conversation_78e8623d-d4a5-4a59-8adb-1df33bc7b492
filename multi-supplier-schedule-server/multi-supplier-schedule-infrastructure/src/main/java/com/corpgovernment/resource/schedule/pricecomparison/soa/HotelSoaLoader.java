package com.corpgovernment.resource.schedule.pricecomparison.soa;

import cn.hutool.core.util.ObjectUtil;
import com.corpgovernment.api.hotel.booking.core.PriceComparisonReqVo;
import com.corpgovernment.api.hotel.booking.soa.HotelSoaClient;
import com.corpgovernment.api.supplier.bo.suppliercompany.SupplierProductBo;
import com.corpgovernment.common.base.JSONResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class HotelSoaLoader {
    @Autowired
    private HotelSoaClient hotelSoaClient;

    /**
     * 酒店价格比较接口
     * @param request
     * @return
     */
    public Boolean priceComparison(PriceComparisonReqVo request){
        if(ObjectUtil.isNull(request)){
            return null;
        }
        JSONResult<Boolean>  result = null;
        try {
            result = hotelSoaClient.priceComparison(request);
        } catch (Exception e) {
            log.error("酒店价格比较接口异常,e" , e);
        }
        if (result == null || result.getData() == null) {
            log.error("酒店价格比较接口异常:" + Optional.ofNullable(result).map(JSONResult::getMsg).orElse("接口无响应"));
            return null;
        }
        return result.getData();
    }
}
