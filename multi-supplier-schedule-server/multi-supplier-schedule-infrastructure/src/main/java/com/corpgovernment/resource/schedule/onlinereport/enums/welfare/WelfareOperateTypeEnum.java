package com.corpgovernment.resource.schedule.onlinereport.enums.welfare;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/9
 */
public enum WelfareOperateTypeEnum {
    /**
     * 发放
     */
    ALLOT("allot", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.welfare.operateType.allot")),
    /**
     * 回收
     */
    RETURN("return", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.welfare.operateType.return")),
    /**
     * 退还
     */
    REFUND("refund", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.welfare.operateType.refund")),
    /**
     * 扣减
     */
    DEDUCT("deduct", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.welfare.operateType.deduct")),

    ;

    private String code;
    private String value;

    WelfareOperateTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static List<String> buildRecycleOperateType() {
        return Lists.newArrayList(WelfareOperateTypeEnum.RETURN.getValue());
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
