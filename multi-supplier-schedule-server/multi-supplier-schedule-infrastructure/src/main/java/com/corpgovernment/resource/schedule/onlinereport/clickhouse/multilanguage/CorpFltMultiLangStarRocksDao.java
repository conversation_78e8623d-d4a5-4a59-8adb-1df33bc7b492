package com.corpgovernment.resource.schedule.onlinereport.clickhouse.multilanguage;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-01-12 21:00
 * @desc
 */
@Repository
public class CorpFltMultiLangStarRocksDao extends AbstractCommonDao {

    /**
     *
     * 机票
     * corpbi_onlinereport.dimcorp_flt_multi_language_airport  机场
     * corpbi_onlinereport.dimcorp_flt_multi_language_airline  航线
     * corpbi_onlinereport.dimcorp_flt_citymultilang_all       城市
     *
     * 酒店
     * corpbi_onlinereport.dimcorp_htl_brandmultilang     品牌
     * corpbi_onlinereport.dimcorp_htl_mgrgroupmultilang  集团
     * corpbi_onlinereport.dimcorp_htl_roommultilang      房型
     * corpbi_onlinereport.dimcorp_htl_htlinfomultilang    酒店名称表待开发
     */

    /**
     * 查询 城市 多语言
     *
     * @param ids
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryGeoLocation(List<Integer> ids, String lang, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ClickHouseTable clickHouseTable = ClickHouseTable.DIMCORP_FLT_CITYMULTILANG;
        sqlBuilder.append("select  cityid as id, multilanguage as multiLangName");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> paramList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlPartition(paramList, querySingleTablePartition(clickHouseTable)));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlSingleVal(paramList, lang, "locale"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(ids, "cityid", paramList));
        sqlBuilder.append(" and status = 1");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    /**
     * 查询 城市 多语言
     *
     * @param names
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryGeoLocationByNames(List<String> names, String lang, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(names)) {
            return Lists.newArrayList();
        }
        ClickHouseTable clickHouseTable = ClickHouseTable.DIMCORP_FLT_CITYMULTILANG;
        sqlBuilder.append("select  cityname as strId, multilanguage as multiLangName");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> paramList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlPartition(paramList, querySingleTablePartition(clickHouseTable)));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlSingleVal(paramList, lang, "locale"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(names, "cityname", paramList));
        sqlBuilder.append(" and status = 1");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    /**
     * 查询 航司 多语言
     *
     * @param ids
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryFltAirlineMultiLang(List<String> ids, String lang, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ClickHouseTable clickHouseTable = ClickHouseTable.DIMCORP_FLT_MULTI_LANGUAGE_AIRLINE;
        sqlBuilder.append("select  airlineid as strId, multilanguage as multiLangName");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> paramList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlPartition(paramList, querySingleTablePartition(clickHouseTable)));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlSingleVal(paramList, lang, "locale"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(ids, "airlineid", paramList));
        sqlBuilder.append(" and status = 1");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    /**
     * 查询 机场 多语言
     *
     * @param ids
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryFltAirportMultiLang(List<String> ids, String lang, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ClickHouseTable clickHouseTable = ClickHouseTable.DIMCORP_FLT_MULTI_LANGUAGE_AIRPORT;
        sqlBuilder.append("select  airportid as strId, multilanguage as multiLangName");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> paramList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlPartition(paramList, querySingleTablePartition(clickHouseTable)));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlSingleVal(paramList, lang, "locale"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(ids, "airportid", paramList));
        sqlBuilder.append(" and status = '1'");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }
}
