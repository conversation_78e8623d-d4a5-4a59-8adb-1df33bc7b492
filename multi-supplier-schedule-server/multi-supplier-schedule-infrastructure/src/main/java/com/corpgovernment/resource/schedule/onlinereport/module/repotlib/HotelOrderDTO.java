package com.corpgovernment.resource.schedule.onlinereport.module.repotlib;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-10-13 11:26
 * @desc
 */

@Data
public class HotelOrderDTO {

    // 订单号
    @Column(name = "order_id")
    @Type(value = Types.BIGINT)
    private Long orderId;

    // 订单状态
    @Column(name = "order_status")
    @Type(value = Types.VARCHAR)
    private String orderStatus;

    // 订单预定时间
    @Column(name = "order_date")
    @Type(value = Types.VARCHAR)
    private String orderDate;

    // 公司ID
    @Column(name = "corp_corporation")
    @Type(value = Types.VARCHAR)
    private String corpCorporation;

    // 公司名称
    @Column(name = "corp_name")
    @Type(value = Types.VARCHAR)
    private String corpName;

    // 公司集团ID
    @Column(name = "companygroupid")
    @Type(value = Types.VARCHAR)
    private String companygroupid;

    // 公司集团
    @Column(name = "companygroup")
    @Type(value = Types.VARCHAR)
    private String companygroup;

    // 主账户账号
    @Column(name = "account_id")
    @Type(value = Types.BIGINT)
    private Long accountId;

    // 主账户代号
    @Column(name = "account_code")
    @Type(value = Types.VARCHAR)
    private String accountCode;

    // 主账户公司名称
    @Column(name = "account_name")
    @Type(value = Types.VARCHAR)
    private String accountName;

    // 子账户账号
    @Column(name = "sub_account_id")
    @Type(value = Types.BIGINT)
    private Long subAccountId;

    // 子账户代号
    @Column(name = "sub_account_code")
    @Type(value = Types.VARCHAR)
    private String subAccountCode;

    // 子账户公司名称
    @Column(name = "sub_account_name")
    @Type(value = Types.VARCHAR)
    private String subAccountName;

    // 行业类型
    @Column(name = "industry_type")
    @Type(value = Types.VARCHAR)
    private String industryType;

    // 行业名称
    @Column(name = "industry_type_name")
    @Type(value = Types.VARCHAR)
    private String industryTypeName;

    // 持卡人卡号
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    // 持卡人姓名
    @Column(name = "user_name")
    @Type(value = Types.VARCHAR)
    private String userName;

    // 持卡人员工编号
    @Column(name = "employe_id")
    @Type(value = Types.VARCHAR)
    private String employeId;

    // 工作所在城市
    @Column(name = "work_city")
    @Type(value = Types.VARCHAR)
    private String workCity;

    // 持卡人职级CN
    @Column(name = "rank_name")
    @Type(value = Types.VARCHAR)
    private String rankName;

    // 成本中心1
    @Column(name = "cost_center1")
    @Type(value = Types.VARCHAR)
    private String costCenter1;

    // 成本中心2
    @Column(name = "cost_center2")
    @Type(value = Types.VARCHAR)
    private String costCenter2;

    // 成本中心3
    @Column(name = "cost_center3")
    @Type(value = Types.VARCHAR)
    private String costCenter3;

    // 成本中心4
    @Column(name = "cost_center4")
    @Type(value = Types.VARCHAR)
    private String costCenter4;

    // 成本中心5
    @Column(name = "cost_center5")
    @Type(value = Types.VARCHAR)
    private String costCenter5;

    // 成本中心6
    @Column(name = "cost_center6")
    @Type(value = Types.VARCHAR)
    private String costCenter6;

    // 部门1
    @Column(name = "dept1")
    @Type(value = Types.VARCHAR)
    private String dept1;

    // 部门2
    @Column(name = "dept2")
    @Type(value = Types.VARCHAR)
    private String dept2;

    // 部门3
    @Column(name = "dept3")
    @Type(value = Types.VARCHAR)
    private String dept3;

    // 部门4
    @Column(name = "dept4")
    @Type(value = Types.VARCHAR)
    private String dept4;

    // 部门5
    @Column(name = "dept5")
    @Type(value = Types.VARCHAR)
    private String dept5;
    // 部门6
    @Column(name = "dept6")
    @Type(value = Types.VARCHAR)
    private String dept6;
    // 部门7
    @Column(name = "dept7")
    @Type(value = Types.VARCHAR)
    private String dept7;
    // 部门8
    @Column(name = "dept8")
    @Type(value = Types.VARCHAR)
    private String dept8;
    // 部门9
    @Column(name = "dept9")
    @Type(value = Types.VARCHAR)
    private String dept9;
    // 部门10
    @Column(name = "dept10")
    @Type(value = Types.VARCHAR)
    private String dept10;
    // 是否个人消费行为:因公,因私,
    @Column(name = "fee_type")
    @Type(value = Types.VARCHAR)
    private String feeType;
    // 预订方式
    @Column(name = "is_online")
    @Type(value = Types.VARCHAR)
    private String isOnline;
    // 支付方式
    @Column(name = "prepay_type")
    @Type(value = Types.VARCHAR)
    private String prepayType;
    // 结算类型
    @Column(name = "acb_prepay_type")
    @Type(value = Types.VARCHAR)
    private String acbPrepayType;
    // 是否BOSS(T/F)
    @Column(name = "bosstype")
    @Type(value = Types.VARCHAR)
    private String bosstype;
    // 所属行程订单号
    @Column(name = "trip_id")
    @Type(value = Types.BIGINT)
    private Long tripId;
    // 关联行程单号
    @Column(name = "journey_no")
    @Type(value = Types.VARCHAR)
    private String journeyNo;
    // 出行目的
    @Column(name = "journey_reason")
    @Type(value = Types.VARCHAR)
    private String journeyReason;
    // 出行目的编号
    @Column(name = "journey_reason_code")
    @Type(value = Types.VARCHAR)
    private String journeyReasonCode;
    // 项目编号
    @Column(name = "project_code")
    @Type(value = Types.VARCHAR)
    private String projectCode;
    // 项目名称
    @Column(name = "project")
    @Type(value = Types.VARCHAR)
    private String project;
    // 是否口头授权T/F
    @Column(name = "verbal_authorize")
    @Type(value = Types.VARCHAR)
    private String verbalAuthorize;
    // 一次授权人姓名
    @Column(name = "confirm_person")
    @Type(value = Types.VARCHAR)
    private String confirmPerson;
    // 一次授权方式
    @Column(name = "confirm_type")
    @Type(value = Types.VARCHAR)
    private String confirmType;
    // 二次授权人姓名
    @Column(name = "confirm_person2")
    @Type(value = Types.VARCHAR)
    private String confirmPerson2;
    // 二次授权方式
    @Column(name = "confirm_type2")
    @Type(value = Types.VARCHAR)
    private String confirmType2;
    // 授权通过时间
    @Column(name = "approvalpasstime")
    @Type(value = Types.VARCHAR)
    private String approvalpasstime;
    // 授权结果
    @Column(name = "actionname")
    @Type(value = Types.VARCHAR)
    private String actionname;
    // 自定义成本中心
    @Column(name = "defineflag")
    @Type(value = Types.VARCHAR)
    private String defineflag;
    // 自定义成本中心2
    @Column(name = "defineflag2")
    @Type(value = Types.VARCHAR)
    private String defineflag2;
    // 一次授权时间
    @Column(name = "confirmtimepoint")
    @Type(value = Types.VARCHAR)
    private String confirmtimepoint;
    // 二次授权时间
    @Column(name = "confirmtimepoint2")
    @Type(value = Types.VARCHAR)
    private String confirmtimepoint2;
    // 一次授权人uid
    @Column(name = "AuditorID")
    @Type(value = Types.VARCHAR)
    private String auditorid;
    // 二次授权人uid
    @Column(name = "AuditorID2")
    @Type(value = Types.VARCHAR)
    private String auditorid2;
    // 成交日期会员是成交日期(离店日期)协议是入住日期
    @Column(name = "deal_date")
    @Type(value = Types.VARCHAR)
    private String dealDate;
    // 年月201908
    @Column(name = "group_month")
    @Type(value = Types.INTEGER)
    private Integer groupMonth;
    // 出票、退票审核月份对应上面的date_time字段的月份
    @Column(name = "print_month")
    @Type(value = Types.INTEGER)
    private Integer printMonth;
    // 出票日期-年对应上面的date_time字段的年
    @Column(name = "print_year")
    @Type(value = Types.INTEGER)
    private Integer printYear;
    // 标付/差标乘以间夜数
    @Column(name = "dead_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal deadPrice;
    // 混付:公司账户支付金额
    @Column(name = "settlement_accnt_amt")
    @Type(value = Types.DECIMAL)
    private BigDecimal settlementAccntAmt;
    // 混付:个人账户支付金额
    @Column(name = "settlement_person_amt")
    @Type(value = Types.DECIMAL)
    private BigDecimal settlementPersonAmt;
    // 酒店ID
    @Column(name = "hotel")
    @Type(value = Types.BIGINT)
    private Long hotel;
    // 酒店名称根据酒店ID去缓存里查询对应值直接存
    @Column(name = "hotel_name")
    @Type(value = Types.VARCHAR)
    private String hotelName;
    // 酒店集团
    @Column(name = "hotel_group_name")
    @Type(value = Types.VARCHAR)
    private String hotelGroupName;
    // 品牌
    @Column(name = "hotel_brand_name")
    @Type(value = Types.VARCHAR)
    private String hotelBrandName;
    // 酒店类型F(国内)O(港澳台)T(海外)
    @Column(name = "is_oversea")
    @Type(value = Types.VARCHAR)
    private String isOversea;
    // 星级
    @Column(name = "star")
    @Type(value = Types.VARCHAR)
    private String star;
    // 酒店所属商业区
    @Column(name = "location")
    @Type(value = Types.VARCHAR)
    private String location;
    // 酒店行政区
    @Column(name = "zone")
    @Type(value = Types.VARCHAR)
    private String zone;
    // 城市ID
    @Column(name = "city")
    @Type(value = Types.INTEGER)
    private Integer city;
    // 城市等级没有
    @Column(name = "city_level")
    @Type(value = Types.VARCHAR)
    private String cityLevel;
    // 城市名称
    @Column(name = "city_name")
    @Type(value = Types.VARCHAR)
    private String cityName;
    // 省份
    @Column(name = "province_name")
    @Type(value = Types.VARCHAR)
    private String provinceName;
    // 国家
    @Column(name = "country_name")
    @Type(value = Types.VARCHAR)
    private String countryName;
    // 入住人多个用分割
    @Column(name = "client_name")
    @Type(value = Types.VARCHAR)
    private String clientName;
    // 会员/协议
    @Column(name = "order_type")
    @Type(value = Types.VARCHAR)
    private String orderType;
    // 酒店间夜day_num*order_room_num
    @Column(name = "quantity")
    @Type(value = Types.INTEGER)
    private Integer quantity;
    // 房间数
    @Column(name = "order_room_num")
    @Type(value = Types.INTEGER)
    private Integer orderRoomNum;
    // 入住人数
    @Column(name = "persons")
    @Type(value = Types.INTEGER)
    private Integer persons;
    // 入住天数算出来的departure_date_time-arrival_date_time
    @Column(name = "day_num")
    @Type(value = Types.INTEGER)
    private Integer dayNum;
    // 入住日期
    @Column(name = "arrival_date_time")
    @Type(value = Types.VARCHAR)
    private String arrivalDateTime;
    // 离店日期
    @Column(name = "departure_date_time")
    @Type(value = Types.VARCHAR)
    private String departureDateTime;
    // 是否加早
    @Column(name = "is_breakfast")
    @Type(value = Types.VARCHAR)
    private String isBreakfast;
    // 加早总数-加早
    @Column(name = "add_breakfast")
    @Type(value = Types.VARCHAR)
    private String addBreakfast;
    // 实收实付直接取产线值
    @Column(name = "real_pay")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPay;
    // 加早总金额加早价格可能不准字段先保留
    @Column(name = "add_breakfast_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal addBreakfastPrice;
    // 配送费
    @Column(name = "post_amount")
    @Type(value = Types.DECIMAL)
    private BigDecimal postAmount;
    // 服务费
    @Column(name = "service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal serviceFee;
    // 是否退票T(是)orF(否)
    @Column(name = "is_refund")
    @Type(value = Types.VARCHAR)
    private String isRefund;
    // 退票主键
    @Column(name = "refund_id")
    @Type(value = Types.VARCHAR)
    private String refundId;
    // 退票时间
    @Column(name = "refund_time")
    @Type(value = Types.VARCHAR)
    private String refundTime;
    // 退补金额-退客户
    @Column(name = "rfd_amount")
    @Type(value = Types.DECIMAL)
    private BigDecimal rfdAmount;
    // 退补间夜-退客户
    @Column(name = "rfd_quantity")
    @Type(value = Types.INTEGER)
    private Integer rfdQuantity;
    // GDS订单全部为空
    @Column(name = "gds_order")
    @Type(value = Types.VARCHAR)
    private String gdsOrder;
    // 是否GDS订单:T(是)orF(否)全部为空
    @Column(name = "is_gds_order")
    @Type(value = Types.VARCHAR)
    private String isGdsOrder;
    // 是否双非酒店订单:T(是)orF(否)
    @Column(name = "is_cu")
    @Type(value = Types.VARCHAR)
    private String isCu;
    // 是否随心订订单是否混付订单:T(是)orF(否)
    @Column(name = "is_mix_payment")
    @Type(value = Types.VARCHAR)
    private String isMixPayment;
    // 是否商旅尊享是or否TMC2N/Y
    @Column(name = "is_tmcp_enjoy")
    @Type(value = Types.VARCHAR)
    private String isTmcpEnjoy;
    // 酒店房型
    @Column(name = "basic_room_type_name")
    @Type(value = Types.VARCHAR)
    private String basicRoomTypeName;
    // 房型ID
    @Column(name = "room_id")
    @Type(value = Types.BIGINT)
    private String roomId;
    // 子房型名称
    @Column(name = "room_name")
    @Type(value = Types.VARCHAR)
    private String roomName;
    // 是否有RCT(是)/F(否)
    @Column(name = "is_rc")
    @Type(value = Types.VARCHAR)
    private String isRc;
    // 低价RC低价ReasonCode一套
    @Column(name = "reason_code")
    @Type(value = Types.VARCHAR)
    private String reasonCode;
    // 低价RC说明
    @Column(name = "low_reasoninfo")
    @Type(value = Types.VARCHAR)
    private String lowReasoninfo;
    // 预订选择的低价RC为其它时用户自己输入的超标原因。注：该字段值当且仅当LowPriceRC=VV时才有意义
    @Column(name = "low_price_en_vv")
    @Type(value = Types.VARCHAR)
    private String lowPriceEnVv;
    // 最低价RC：用户没有预订最低价房型时选择的RC二字码三套
    @Column(name = "min_price_rc")
    @Type(value = Types.VARCHAR)
    private String minPriceRc;
    // 预订选择的最低价RC为其它时用户自己输入的超标原因
    @Column(name = "min_price_rc_vv")
    @Type(value = Types.VARCHAR)
    private String minPriceRcVv;
    // 协议RCCODE二套
    @Column(name = "agreement_rc")
    @Type(value = Types.VARCHAR)
    private String agreementRc;
    // 预订选择的协议RC为其它时用户自己输入的超标原因。注：该字段值当且仅当AgreementRC=VV时才有意义
    @Column(name = "agreement_rc_vv")
    @Type(value = Types.VARCHAR)
    private String agreementRcVv;
    // 协议RC说明
    @Column(name = "agreement_reasoninfo")
    @Type(value = Types.VARCHAR)
    private String agreementReasoninfo;
    // 用户自定义RCcode无
    @Column(name = "userdefined_rid")
    @Type(value = Types.VARCHAR)
    private String userdefinedRid;
    // 用户自定义RC说明
    @Column(name = "userdefined_rc")
    @Type(value = Types.VARCHAR)
    private String userdefinedRc;
    // 母酒店ID
    @Column(name = "masterhotelid")
    @Type(value = Types.INTEGER)
    private Integer masterhotelid;
    // 早餐份数
    @Column(name = "breakfast")
    @Type(value = Types.BIGINT)
    private Long breakfast;
    // 原币种(客户支付币种)
    @Column(name = "o_currency")
    @Type(value = Types.VARCHAR)
    private String oCurrency;
    // 汇率(客户支付币种汇率)
    @Column(name = "o_exchangerate")
    @Type(value = Types.DECIMAL)
    private BigDecimal oExchangerate;
    // 父级城市等级,
    @Column(name = "pcitylevel")
    @Type(value = Types.VARCHAR)
    private String pcitylevel;
    // 酒店名称en,
    @Column(name = "hotel_name_en")
    @Type(value = Types.VARCHAR)
    private String hotelNameEn;
    // 酒店集团en,
    @Column(name = "hotel_group_name_en")
    @Type(value = Types.VARCHAR)
    private String hotelGroupNameEn;
    // 城市名称en,
    @Column(name = "city_name_en")
    @Type(value = Types.VARCHAR)
    private String cityNameEn;
    // 省份en,
    @Column(name = "province_name_en")
    @Type(value = Types.VARCHAR)
    private String provinceNameEn;
    // 国家en,
    @Column(name = "country_name_en")
    @Type(value = Types.VARCHAR)
    private String countryNameEn;
    // 低价RC说明en,
    @Column(name = "low_reasoninfo_en")
    @Type(value = Types.VARCHAR)
    private String lowReasoninfoEn;
    // 协议RC说明en,
    @Column(name = "agreement_reasoninfo_en")
    @Type(value = Types.VARCHAR)
    private String agreementReasoninfoEn;
    // 后收服务费,
    @Column(name = "postservicefee")
    @Type(value = Types.DECIMAL)
    private BigDecimal postservicefee;
    // 支付类型,
    @Column(name = "balancetype")
    @Type(value = Types.VARCHAR)
    private String balancetype;
    // 支付类型描述,
    @Column(name = "balancetypename")
    @Type(value = Types.VARCHAR)
    private String balancetypename;
    // 星级（国家标准）,
    @Column(name = "star_true")
    @Type(value = Types.VARCHAR)
    private String starTrue;
    // 房价
    @Column(name = "room_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal roomPrice;
    // 优惠券
    @Column(name = "couponamount")
    @Type(value = Types.DECIMAL)
    private BigDecimal couponamount;
    // 预订来源
    @Column(name = "serverfromtype")
    @Type(value = Types.VARCHAR)
    private String serverfromtype;
    // 酒店邮编
    @Column(name = "zipcode")
    @Type(value = Types.VARCHAR)
    private String zipcode;
    // 地址
    @Column(name = "address")
    @Type(value = Types.VARCHAR)
    private String address;
    // 是否当天
    @Column(name = "is_sameday")
    @Type(value = Types.VARCHAR)
    private String isSameday;
    // 是否工作日
    @Column(name = "is_worktime")
    @Type(value = Types.VARCHAR)
    private String isWorktime;
    // 平均价格
    @Column(name = "avgprice")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgprice;
    // 实收实付(房价-优惠券)
    @Column(name = "corp_real_pay")
    @Type(value = Types.DECIMAL)
    private BigDecimal corpRealPay;
    // 最低价RC说明,
    @Column(name = "min_price_rc_reasoninfo")
    @Type(value = Types.VARCHAR)
    private String minPriceRcReasoninfo;
    // 最低价RC说明 english,
    @Column(name = "min_price_rc_reasoninfo_en")
    @Type(value = Types.VARCHAR)
    private String minPriceRcReasoninfoEn;
    // 单间夜数差标
    @Column(name = "dead_price_onenight")
    @Type(value = Types.DECIMAL)
    private BigDecimal deadPriceOnenight;
    // 入住人编号
    @Column(name = "hotelpassengerid")
    @Type(value = Types.VARCHAR)
    private String hotelpassengerid;
    // 重复预订订单id
    @Column(name = "DoubleBookedOrderid")
    @Type(value = Types.VARCHAR)
    private String doublebookedorderid;
    // 重复预订RC代码
    @Column(name = "DoubleBookedRC")
    @Type(value = Types.VARCHAR)
    private String doublebookedrc;
    // 重复预订RC说明
    @Column(name = "DoubleBookedRCDetail")
    @Type(value = Types.VARCHAR)
    private String doublebookedrcdetail;
    // 重复预定RC为其他时用户填写的具体内容
    @Column(name = "DoubleBookedRCReason")
    @Type(value = Types.VARCHAR)
    private String doublebookedrcreason;
    // 三方协议的节省金额
    @Column(name = "save_amount_3c")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveAmount3c;
    // 促销优惠活动的节省金额
    @Column(name = "save_amount_promotion")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveAmountPromotion;
    // 商旅尊享节省金额
    @Column(name = "save_amount_premium")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveAmountPremium;
    // 实收实付（含服务费）
    @Column(name = "real_pay_with_servicefee")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPayWithService;
    // 钻级
    @Column(name = "customereval")
    @Type(value = Types.DECIMAL)
    private BigDecimal customereval;

    // 国家
    @Column(name = "countryid")
    @Type(value = Types.INTEGER)
    private Integer countryid;

    // 品牌id
    @Column(name = "brandid")
    @Type(value = Types.INTEGER)
    private Integer brandid;

    // 集团id
    @Column(name = "mgrgroupid")
    @Type(value = Types.INTEGER)
    private Integer mgrgroupid;

    // 管控节省金额
    @Column(name = "saving_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal controlSave;

    // 每日房价列表
    @Column(name = "origin_room_distribution_prices")
    @Type(value = Types.VARCHAR)
    private String originRoomDistributionPrices;

    // 行业大类
    @Column(name = "std_industry1")
    @Type(value = Types.VARCHAR)
    private String stdIndustry1;

    // 行业小类
    @Column(name = "std_industry2")
    @Type(value = Types.VARCHAR)
    private String stdIndustry2;

    // 拼房模式下的拼房类型
    @Column(name = "houseshare_mode_type")
    @Type(value = Types.VARCHAR)
    private String houseshareModeType;

    // 费用分摊模式
    @Column(name = "allocation_mode")
    @Type(value = Types.VARCHAR)
    private String allocationMode;

    // 费用分摊模式
    @Column(name = "allocation_mode_desc")
    @Type(value = Types.VARCHAR)
    private String allocationModeDesc;

    // 取消原因
    @Column(name = "cancelreason_desc")
    @Type(value = Types.VARCHAR)
    private String cancelreason;
}
