package com.corpgovernment.resource.schedule.onlinereport.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/10/7
 */
public class BizFiledNameConvertUtils {
    /**
     * 成本中心字段名称转换为数据库字段（costcenter1->cost_center1）
     *
     * @param originalName
     * @return
     */
    public static String convertCostCenterName(String originalName) {
        if (StringUtils.isNotEmpty(originalName) &&
                originalName.toLowerCase().startsWith("costcenter")) {
            return originalName.toLowerCase().replace("costcenter", "cost_center");
        }
        return originalName;
    }
}
