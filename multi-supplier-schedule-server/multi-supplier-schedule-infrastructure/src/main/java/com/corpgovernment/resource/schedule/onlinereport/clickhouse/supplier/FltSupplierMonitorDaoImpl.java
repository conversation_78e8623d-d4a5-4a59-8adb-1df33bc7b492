package com.corpgovernment.resource.schedule.onlinereport.clickhouse.supplier;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.CostCenterAndDepartSearchType;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SqlFieldValidator;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.math3.analysis.function.Cos;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 * @date 2022/6/20 13:08
 * @Desc
 */
@Repository
@Slf4j
public class FltSupplierMonitorDaoImpl extends AbstractSupplierMonitorDao  {

    private static final String LOG_TITLE = "FltSupplierMonitorDaoImpl";

    /**
     * @param requestDto
     * @param clazz
     * @param productType
     * @param dim
     * @param <T>
     * @return
     * @throws Exception
     */

    public <T> List<T> queryTop(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String dim, String destinationType) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        // dim值 airline_en_name,airline_cn_name,flight_city_en,flight_city,departure_city_name_en,departure_city_name,arrival_city_name_en,arrival_city_name
        if (StringUtils.equalsIgnoreCase("airline_cn_name", dim) || StringUtils.equalsIgnoreCase("airline_en_name", dim)) {
            sqlBuilder.append(String.format("select coalesce(airline, '') as dimId, %s as dim ", dim));
        } else if (StringUtils.equalsIgnoreCase("flight_city", dim) || StringUtils.equalsIgnoreCase("flight_city_en", dim)) {
            sqlBuilder.append(String.format("select %s as dim, CONCAT(departure_country,'-', arrival_country) as dim_country", dim));
        } else {
            sqlBuilder.append(String.format("select %s as dim ", dim));
        }
        sqlBuilder.append(", round(SUM(coalesce(price, 0)),2) as sumPrice \n");
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) as sumQuantity \n");
        sqlBuilder.append(", round(SUM(coalesce(real_pay, 0)),2) as sumAmount \n");
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(quantity, 0) else 0 end)) != 0 " +
                "THEN toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(price, 0) else 0 end)) " +
                "/ toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(quantity, 0) else 0 end)) ELSE 0 END,2) AS avgPrice  \n");
        sqlBuilder.append(", round(case when toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(tpms, 0) else 0 end)) != 0  " +
                "then toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(price, 0) else 0 end))/toFloat64(SUM(CASE WHEN class_type = 'Y' " +
                "THEN coalesce(tpms, 0) else 0 end)) else 0 end,2) avgTpmsPrice  \n");
        sqlBuilder.append(", round(case when toFloat64(SUM(CASE WHEN class_type = 'Y' and flight_class = 'N'  THEN coalesce(quantity, 0) else 0 end)) != 0  " +
                "then toFloat64(SUM(CASE WHEN class_type = 'Y' and flight_class = 'N' THEN coalesce(price_rate*quantity, 0) else 0 end))" +
                "/toFloat64(SUM(CASE WHEN class_type = 'Y' and flight_class = 'N' THEN coalesce(quantity, 0) else 0 end)) else 0 end,2) avgDiscount  \n");

        sqlBuilder.append(", round(SUM(CASE WHEN agreement_type in('TA','B2G') THEN coalesce(price, 0) else 0 end),2) AS sumPrice_ta  \n");
        sqlBuilder.append(", round(CASE when toFloat64(SUM(coalesce(price, 0))) != 0 \n" +
                "    then toFloat64(SUM(CASE WHEN agreement_type in('TA','B2G') THEN coalesce(price, 0) else 0 end))" +
                "/ toFloat64(SUM(coalesce(price, 0))) else 0 end * 100,2) AS sumPrice_ta_percent  \n");
        sqlBuilder.append(", round(SUM(CASE WHEN agreement_type in('TA','B2G') THEN coalesce(real_pay, 0) else 0 end),2) AS sumAmount_ta  \n");
        sqlBuilder.append(", round(CASE when toFloat64(SUM(coalesce(real_pay, 0))) != 0 \n" +
                "    then toFloat64(SUM(CASE WHEN agreement_type in('TA','B2G') THEN coalesce(real_pay, 0) else 0 end))" +
                "/ toFloat64(SUM(coalesce(real_pay, 0))) else 0 end * 100,2) AS sumAmount_ta_percent   \n");
        sqlBuilder.append(", round(SUM(CASE WHEN agreement_type in('TA','B2G') THEN coalesce(quantity, 0) else 0 end),2) AS sumQuantity_ta  \n");
        sqlBuilder.append(", round(CASE when toFloat64(SUM(coalesce(quantity, 0))) != 0 \n" +
                "    then toFloat64(SUM(CASE WHEN agreement_type in('TA','B2G') THEN coalesce(quantity, 0) else 0 end))" +
                "/ toFloat64(SUM(coalesce(quantity, 0))) else 0 end * 100,2) AS sumQuantity_ta_percent   \n");
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(CASE WHEN agreement_type in('TA','B2G') and class_type = 'Y' THEN coalesce(quantity, 0) else 0 end)) != 0 THEN " +
                "toFloat64(SUM(CASE WHEN agreement_type in('TA','B2G') and class_type = 'Y' THEN coalesce(price, 0) else 0 end)) " +
                "/ toFloat64(SUM(CASE WHEN agreement_type in('TA','B2G') and class_type = 'Y' THEN coalesce(quantity, 0) else 0 end)) ELSE 0 END,2) AS avgPrice_ta  \n");
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(CASE WHEN agreement_type in('TA','B2G') and class_type = 'Y' THEN coalesce(tpms, 0) else 0 end)) != 0 " +
                "THEN toFloat64(SUM(CASE WHEN agreement_type in('TA','B2G') and class_type = 'Y' THEN coalesce(price, 0) else 0 end)) " +
                "/ toFloat64(SUM(CASE WHEN agreement_type in('TA','B2G') and class_type = 'Y' THEN coalesce(tpms, 0) else 0 end)) ELSE 0 END,2) AS avgTpmsPrice_ta \n");
        sqlBuilder.append(", round(case when toFloat64(SUM(CASE WHEN class_type = 'Y' and flight_class = 'N' and agreement_type in('TA','B2G') " +
                "THEN coalesce(quantity, 0) else 0 end)) != 0 then toFloat64(SUM(CASE WHEN class_type = 'Y' and flight_class = 'N' and agreement_type in('TA','B2G') " +
                "THEN coalesce(price_rate*quantity, 0) else 0 end)) /toFloat64(SUM(CASE WHEN class_type = 'Y' and flight_class = 'N' and agreement_type in('TA','B2G') " +
                "THEN coalesce(quantity, 0) else 0 end)) else 0 end,2) avgDiscount_ta  \n");


        sqlBuilder.append(", round(SUM(CASE WHEN agreement_type not in('TA','B2G') THEN coalesce(price, 0) else 0 end),2) AS sumPrice_nta \n");
        sqlBuilder.append(", round(CASE when toFloat64(SUM(coalesce(price, 0))) != 0 \n" +
                "    then toFloat64(SUM(CASE WHEN agreement_type not in('TA','B2G') THEN coalesce(price, 0) else 0 end))" +
                "/ toFloat64(SUM(coalesce(price, 0))) else 0 end * 100,2) AS sumPrice_nta_percent \n");
        sqlBuilder.append(", SUM(CASE WHEN agreement_type not in('TA','B2G') THEN coalesce(quantity, 0) else 0 end) AS sumQuantity_nta  \n");
        sqlBuilder.append(", round(CASE when toFloat64(SUM(coalesce(quantity, 0))) != 0 \n" +
                "    then toFloat64(SUM(CASE WHEN agreement_type not in('TA','B2G') THEN coalesce(quantity, 0) else 0 end))" +
                "/toFloat64(SUM(coalesce(quantity, 0))) else 0 end * 100,2) as sumQuantity_nta_percent  \n");
        sqlBuilder.append(", round(SUM(CASE WHEN agreement_type not in('TA','B2G') THEN coalesce(real_pay, 0) else 0 end),2) AS sumAmount_nta \n");
        sqlBuilder.append(", round(CASE when toFloat64(SUM(coalesce(real_pay, 0))) != 0 \n" +
                "    then toFloat64(SUM(CASE WHEN agreement_type not in('TA','B2G') THEN coalesce(real_pay, 0) else 0 end))" +
                "/ toFloat64(SUM(coalesce(real_pay, 0))) else 0 end * 100,2) AS sumAmount_nta_percent \n");
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(CASE WHEN agreement_type not in('TA','B2G') and class_type = 'Y' THEN coalesce(quantity, 0) else 0 end)) != 0 " +
                "THEN toFloat64(SUM(CASE WHEN agreement_type not in('TA','B2G') and class_type = 'Y' THEN coalesce(price, 0) else 0 end)) " +
                "/ toFloat64(SUM(CASE WHEN agreement_type not in('TA','B2G') and class_type = 'Y' THEN coalesce(quantity, 0) else 0 end)) ELSE 0 END,2) as avgPrice_nta \n");
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(CASE WHEN agreement_type not in('TA','B2G') and class_type = 'Y' THEN coalesce(tpms, 0) else 0 end)) != 0 " +
                "THEN toFloat64(SUM(CASE WHEN agreement_type not in('TA','B2G') and class_type = 'Y' THEN coalesce(price, 0) else 0 end)) " +
                "/ toFloat64(SUM(CASE WHEN agreement_type not in('TA','B2G') and class_type = 'Y' THEN coalesce(tpms, 0) else 0 end)) ELSE 0 END,2) AS avgTpmsPrice_nta ");
        sqlBuilder.append(", round(case when toFloat64(SUM(CASE WHEN class_type = 'Y' and flight_class = 'N' and agreement_type not in('TA','B2G') " +
                "THEN coalesce(quantity, 0) else 0 end)) != 0 then toFloat64(SUM(CASE WHEN class_type = 'Y' and flight_class = 'N' and agreement_type not in('TA','B2G') " +
                "THEN coalesce(price_rate*quantity, 0) else 0 end)) /toFloat64(SUM(CASE WHEN class_type = 'Y' and flight_class = 'N' and agreement_type not in('TA','B2G') " +
                "THEN coalesce(quantity, 0) else 0 end)) else 0 end,2) avgDiscount_nta  \n");
        // 三方协议节省
        sqlBuilder.append(", SUM(CASE WHEN (flight_class = 'N' and class_type = 'Y') or flight_class = 'I' THEN coalesce(save_amount_3c, 0) ELSE 0 END) AS saveAmount3c ");

        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        if (!StringUtils.equalsIgnoreCase("pcitylevel", dim) && !StringUtils.equalsIgnoreCase("star", dim)) {
            sqlBuilder.append(String.format("and coalesce(%s, '') != '' ", dim));
        }
        if ((StringUtils.equalsIgnoreCase(dim, "arrival_city_name") || StringUtils.equalsIgnoreCase(dim, "arrival_city_name_en")) && StringUtils.isNotEmpty(destinationType)){
            sqlBuilder.append(getDestCondition(destinationType));
        }
        if (StringUtils.equalsIgnoreCase("airline_cn_name", dim) || StringUtils.equalsIgnoreCase("airline_en_name", dim)) {
            sqlBuilder.append(" group by dimId, dim ");
        } else if (StringUtils.equalsIgnoreCase("flight_city", dim) || StringUtils.equalsIgnoreCase("flight_city_en", dim)) {
            sqlBuilder.append(" group by dim, dim_country");
        } else {
            sqlBuilder.append(" group by dim ");
        }
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "agreementView",true);
    }

    /**
     * @param requestDto
     * @param clazz
     * @param productType
     * @param dim
     * @param <T>
     * @return
     * @throws Exception
     */

    public <T> List<T> queryTop(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String dim,
                                boolean onlycContract, String flightCtiy, String lang) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        // dim值 airline_en_name,airline_cn_name,flight_city_en,flight_city,departure_city_name_en,departure_city_name,arrival_city_name_en,arrival_city_name
        sqlBuilder.append(String.format("select %s as dim ", dim));
        sqlBuilder.append(", SUM(coalesce(price, 0)) as sumPrice \n");
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) as sumQuantity \n");
        sqlBuilder.append(", round(SUM(coalesce(real_pay, 0)),2) as sumAmount \n");
        if (onlycContract){
            // 只查协议航司
            sqlBuilder.append(",  sum(case when toFloat64(coalesce(agreement_rate, 0)) != 0 then " +
                    "toFloat64(coalesce(price, 0))/toFloat64(coalesce(agreement_rate, 0)) - toFloat64(coalesce(price, 0)) else 0 end) as forecastAmount" );
        }
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        if (StringUtils.isNotEmpty(flightCtiy)){
            if (SharkUtils.isZH(lang)){
                sqlBuilder.append(" and flight_city = ?");
            } else {
                sqlBuilder.append(" and flight_city_en = ?");
            }
            parmList.add(flightCtiy);
        }
        if (onlycContract) {
            // 只查协议航司
            sqlBuilder.append(" and airline in(").append(agreementAirlineQuery(parmList, requestDto, productType, isBookCaliber)).append(")");
        }
        sqlBuilder.append(" group by dim ");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 非协议航司airlines预估节省金额查询
     * @param requestDto
     * @param clazz
     * @param productType
     * @param dim
     * @param airlines
     * @return
     * @param <T>
     * @throws Exception
     */

    public <T> List<T> queryEstimateSave(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String dim,
                                         List<String> airlines, String exAirlineSqlCondition) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        // dim值 airline_en_name,airline_cn_name
        sqlBuilder.append(String.format("select %s as dim ", dim));
        sqlBuilder.append(", SUM(CASE WHEN (flight_class = 'N' and class_type = 'Y') or flight_class = 'I' THEN coalesce(save_amount_3c, 0) ELSE 0 END) AS saveAmount3c ");
        sqlBuilder.append(", SUM(coalesce(save_amount_premium, 0)) AS saveAmountPremium ");
        sqlBuilder.append(", SUM(CASE WHEN (class_type = 'Y' and flight_class = 'N') or (flight_class = 'I') then coalesce(netfare_3c, 0) else 0 end) AS netfare3c ");
        sqlBuilder.append(", SUM(coalesce(netfare_premium, 0)) AS netfarePremium ");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(requestDto, parmList));
        }
        sqlBuilder.append(" and agreement_type in ('B2G','TA') ");
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount(dim, airlines, parmList));
        if (StringUtils.isNotEmpty(exAirlineSqlCondition)) {
            sqlBuilder.append(exAirlineSqlCondition);
        }
//        sqlBuilder.append(" and corp_corporation in(").append(queryCorpSql(requestDto, productType,airlines, parmList, isBookCaliber, dim)).append(")");
        sqlBuilder.append(" group by dim ");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }


    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */

    public <T> List<T> jianbao(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String dim, String extCondition) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        // dim值 航司 airline_cn_name或airline_en_name,航线 flight_city或 flight_city_en
        sqlBuilder.append(String.format("select %s as dim ", dim));
        // 机票均价
        sqlBuilder.append(", CASE WHEN toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(quantity, 0) else 0 end)) != 0 " +
                "THEN toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(price, 0) else 0 end)) " +
                "/ toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(quantity, 0) else 0 end)) ELSE 0 END AS avgPrice \n");
        sqlBuilder.append(", SUM(coalesce(price, 0)) as  sumPrice \n");
        sqlBuilder.append(", SUM(coalesce(real_pay, 0)) as sumAmount \n");
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) as sumQuantity \n");
        sqlBuilder.append(", SUM(case when agreement_type in('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 END) as sumPriceTa \n");
        sqlBuilder.append(" , round(SUM(CASE WHEN (flight_class = 'N' and class_type = 'Y') or flight_class = 'I' " +
                "THEN coalesce(save_amount_3c, 0) ELSE 0 END),2) AS totalSaveAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, "orderdt"));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        if (!StringUtils.equalsIgnoreCase("pcitylevel", dim) && !StringUtils.equalsIgnoreCase("star", dim)) {
            sqlBuilder.append(String.format("and coalesce(%s, '') != '' ", dim));
        }
        if (StringUtils.isNotEmpty(extCondition)) {
            sqlBuilder.append(extCondition);
        }
        sqlBuilder.append(" group by dim ");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 查询协议航司
     *
     * @param request
     * @return
     * @throws Exception
     */

    public List<String> queryAgreementAirlinesName(BaseQueryConditionDTO request, String dim, String productType) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(request.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(String.format("select %s as dim ", dim));
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        sqlBuilder.append(" and agreement_type in ('B2G','TA') ");
        sqlBuilder.append(" and audited <> 'F' ");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(request, parmList, isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(String.format("and coalesce(%s, '') != '' ", dim));
        sqlBuilder.append("group by dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return DbResultMapUtils.mapStrResultList(u, "dim");
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "queryAgreementAirlinesName");
    }


    public <T> List<T> listCorpAndIndustryDataByDimList(BaseQueryConditionDTO request, String productType,
                                                        List<String> dimIdList, String dimFiled, Class<T> clazz) {
        return null;
    }

    /**
     * 查询协议出票航司
     *
     * @param request
     * @return
     * @throws Exception
     */

    public <T> List<T> queryAgreementAirlines(BaseQueryConditionDTO request, Class<T> clazz, String dim, String productType) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(request.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(String.format("select airline as dimId, %s as dim ", dim));
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        sqlBuilder.append(" and agreement_type in ('B2G','TA') ");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(request, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(request, parmList));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append("group by dimId, dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryAgreementAirlines");
    }

    /**
     * 查询协议航司
     *
     * @param ticketairlineList 出票航司协议列表
     * @param clazz       orm实体类
     * @param corpIds     公司id
     * @return
     * @throws Exception
     */

    public <T> List<T> queryAgreementDetail(List<String> ticketairlineList, Class<T> clazz, List<String> corpIds) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select agreement_id, agreement_name, agreement_create_date, agreement_start_date, agreement_end_date, ");
        sqlBuilder.append("arrayJoin(splitByString('/', if(position(all_airline_code, airline_code)>0, coalesce(all_airline_code,''), " +
                "concat(coalesce(airline_code,''), if(all_airline_code<>'','/',''), coalesce(all_airline_code,''))))) as dim_id ");
        sqlBuilder.append("from ");
        sqlBuilder.append(ClickHouseTable.FLT_AGREEMENT_DETAIL.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.FLT_AGREEMENT_DETAIL));
        sqlBuilder.append(BaseConditionPrebuilder.buildAgreementPreSql(ticketairlineList, corpIds, parmList));
        sqlBuilder.append("order by dim_id, agreement_end_date desc ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryAgreementDetail");
    }


    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */

    public <T> List<T> index(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String dim) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        // 航司 airline_cn_name或airline_en_name
        sqlBuilder.append(String.format("select %s as dim ", dim));
        // 机票净价
        sqlBuilder.append(", SUM(coalesce(price, 0)) as  sumPrice \n");
        sqlBuilder.append(", SUM(case when agreement_type in('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 END) as sumPriceTa \n");
        sqlBuilder.append(", SUM(case when agreement_type in('B2G', 'TA') THEN coalesce(real_pay, 0) ELSE 0 END) as sumAmountTa \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(" group by dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "agreementView");
    }

    /**
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */

    public <T> List<T> queryTop(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select  as dim ");
        sqlBuilder.append(", SUM(coalesce(price, 0)) as sumPrice \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(" group by dim  ORDER BY sumPrice DESC");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }


    /**
     * 概览sql
     *
     * @param parmList
     * @param requestDto
     * @param productType
     * @param isBookCaliber
     * @return
     * @throws Exception
     */

    public String agreementViewSql(List<Object> parmList, BaseQueryConditionDTO requestDto, String productType, boolean isBookCaliber) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 是否协议（Y:三方协议、N:非三方协议）
        sqlBuilder.append(" CASE WHEN agreement_type IN ('B2G', 'TA') THEN 'TA' ELSE 'NTA' END AS isAgreement \n");
        // 成交净价
        sqlBuilder.append(", SUM(coalesce(price, 0)) AS totalPrice \n");
        // 票张数
        sqlBuilder.append(", SUM(coalesce(quantity,0)) AS totalQuantity \n");
        // 成交净价（经济舱）
        sqlBuilder.append(", SUM(CASE WHEN class_type = 'Y' THEN coalesce(price, 0) ELSE 0 END) AS totalPriceEconomy\n");
        // 票张数（经济舱）
        sqlBuilder.append(", SUM(CASE WHEN class_type = 'Y' THEN coalesce(quantity, 0) ELSE 0 END) AS totalQuantiyEconomy\n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(" group by isAgreement");
        // 查询clickhouse
        return sqlBuilder.toString();
    }

    /**
     * 协议同环比sql
     *
     * @param parmList
     * @param requestDto
     * @param productType
     * @param isBookCaliber
     * @return
     * @throws Exception
     */

    public String agreementMomAndYoySql(List<Object> parmList, BaseQueryConditionDTO requestDto, String productType, boolean isBookCaliber) {
        String partion = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select curr.totalPrice as currentTotalPrice, mom.totalPrice as momTotalPrice, yoy.totalPrice as yoyTotalPrice,totalQuantity,avgPrice  \n");
        sqlBuilder.append(" FROM ( \n");
        sqlBuilder.append(" SELECT SUM(coalesce(price, 0)) AS totalPrice \n");
        // 票张数
        sqlBuilder.append(", SUM(coalesce(quantity,0)) AS totalQuantity \n");
        // 机票均价
        sqlBuilder.append(", CASE WHEN toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(quantity, 0) else 0 end)) != 0 " +
                "THEN toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(price, 0) else 0 end)) " +
                "/ toFloat64(SUM(CASE WHEN class_type = 'Y' THEN coalesce(quantity, 0) else 0 end)) ELSE 0 END AS avgPrice \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                    requestDto.getStartTime(), requestDto.getEndTime(), partion, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                    requestDto.getStartTime(), requestDto.getEndTime(), partion, OrpConstants.REPORT_DATE));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        // 三方协议
        sqlBuilder.append(" and agreement_type IN ('B2G', 'TA')");
        sqlBuilder.append(" ) curr ");
        sqlBuilder.append(" cross join ( \n");
        sqlBuilder.append(" SELECT SUM(coalesce(price, 0)) AS totalPrice \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<String> momTimes = OrpDateTimeUtils.momTime(requestDto.getStartTime(), requestDto.getEndTime());
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                    momTimes.get(0), momTimes.get(1), partion, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                    momTimes.get(0), momTimes.get(1), partion, OrpConstants.REPORT_DATE));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        // 三方协议
        sqlBuilder.append(" and agreement_type IN ('B2G', 'TA')");
        sqlBuilder.append(" ) mom ");
        sqlBuilder.append(" cross join ( \n");
        sqlBuilder.append(" SELECT SUM(coalesce(price, 0)) AS totalPrice \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<String> yoyTimes = OrpDateTimeUtils.yoyTime(requestDto.getStartTime(), requestDto.getEndTime(), OrpConstants.ONE_YEAR_DAYS);
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                    yoyTimes.get(0), yoyTimes.get(1), partion, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                    yoyTimes.get(0), yoyTimes.get(1), partion, OrpConstants.REPORT_DATE));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        // 三方协议
        sqlBuilder.append(" and agreement_type IN ('B2G', 'TA')");
        sqlBuilder.append(" ) yoy ");
        // 查询clickhouse
        return sqlBuilder.toString();
    }


    public String agreementAggSql(List<Object> parmList, BaseQueryConditionDTO requestDto, String productType, boolean isBookCaliber) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 国内三方成交净价
        sqlBuilder.append(" SUM(CASE WHEN flight_class = 'N' AND agreement_type IN ('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 end) AS totalPriceDomTa \n");
        // 国内非三方成交净价
        sqlBuilder.append(", SUM(CASE WHEN flight_class = 'N' AND agreement_type not IN ('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 end) AS totalPriceDomNta \n");
        // 国际三方成交净价
        sqlBuilder.append(", SUM(CASE WHEN flight_class = 'I' AND agreement_type IN ('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 end) AS totalPriceInterTa \n");
        // 国际非三方成交净价
        sqlBuilder.append(", SUM(CASE WHEN flight_class = 'I' AND agreement_type not IN ('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 end) AS totalPriceInterNta \n");
        // 国内三方票张数
        sqlBuilder.append(", SUM(CASE WHEN flight_class = 'N' AND agreement_type IN ('B2G', 'TA') THEN coalesce(quantity, 0) ELSE 0 end) AS totalQuantityDomTa \n");
        // 国内非三方票张数
        sqlBuilder.append(", SUM(CASE WHEN flight_class = 'N' AND agreement_type not IN ('B2G', 'TA') THEN coalesce(quantity, 0) ELSE 0 end) AS totalQuantityDomNta \n");
        // 国际三方票张数
        sqlBuilder.append(", SUM(CASE WHEN flight_class = 'I' AND agreement_type IN ('B2G', 'TA') THEN coalesce(quantity, 0) ELSE 0 end) AS totalQuantityInterTa \n");
        // 国际非三方票张数
        sqlBuilder.append(", SUM(CASE WHEN flight_class = 'I' AND agreement_type not IN ('B2G', 'TA') THEN coalesce(quantity, 0) ELSE 0 end) AS totalQuantityInterNta \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        // 查询clickhouse
        return sqlBuilder.toString();
    }


    public String agreementDetailSql(List<Object> parmList, BaseQueryConditionDTO requestDto, String productType,
                                     boolean isBookCaliber, String lang, boolean needGroup) {
        StringBuilder sqlBuilder = new StringBuilder();
        if (needGroup) {
            sqlBuilder.append("select airline, ").append(SharkUtils.isEN(lang) ? "airline_en_name as airlineName" : "airline_cn_name as airlineName");
        } else {
            sqlBuilder.append("select '" + SharkUtils.get("Index.sum", lang) + "' as airlineName");
        }
        // 总成交净价
        sqlBuilder.append(" , SUM(coalesce(price, 0)) AS FLT_TOTAL_PRICE \n");
        // 三方协议成交净价
        sqlBuilder.append(", SUM(CASE WHEN agreement_type IN ('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 END) AS FLT_TOTAL_PRICE_TA \n");
        // 三方协议成交净价占比
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(coalesce(price, 0))) != 0 " +
                "THEN toFloat64(SUM(CASE WHEN agreement_type IN ('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 END))/toFloat64(SUM(coalesce(price, 0))) ELSE 0 end * 100, 2) " +
                "as FLT_PRICE_TA_PERCENT\n");
        // 三方协议前返成交净价
        sqlBuilder.append(", SUM(CASE WHEN agreement_type IN ('B2G', 'TA') AND bf_return = 'T' THEN coalesce(price, 0) ELSE 0 END) AS  FLT_TOTAL_PRICE_TA_BF_RETURN\n");
        // 总票张数
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) AS FLT_TOTAL_QUANTITY \n");
        // 三方协议票张数
        sqlBuilder.append(", SUM(CASE WHEN agreement_type IN ('B2G', 'TA') THEN coalesce(quantity, 0) ELSE 0 END) AS FLT_TOTAL_QUANTITY_TA \n");
        // 三方协议票张数占比
        sqlBuilder.append(", round(CASE WHEN SUM(coalesce(quantity, 0)) != 0 " +
                "THEN SUM(CASE WHEN agreement_type IN ('B2G', 'TA') THEN coalesce(quantity, 0) ELSE 0 END)/SUM(coalesce(quantity, 0)) " +
                "ELSE 0 END * 100, 4) AS FLT_QUANTITY_TA_PERCENT \n");
        // 三方协议前返票张数
        sqlBuilder.append(", SUM(CASE WHEN agreement_type IN ('B2G', 'TA') AND bf_return = 'T' THEN coalesce(quantity, 0) ELSE 0 END) AS FLT_TOTAL_QUANTITY_TA_BF_RETURN \n");
        // 三方协议前返票张数占比
        sqlBuilder.append(", round(CASE WHEN SUM(coalesce(quantity, 0)) != 0 " +
                "THEN SUM(CASE WHEN agreement_type IN ('B2G', 'TA') AND bf_return = 'T' THEN coalesce(quantity, 0) ELSE 0 END)/SUM(coalesce(quantity, 0)) " +
                "ELSE 0 END * 100, 2)  AS FLT_QUANTITY_TA_BF_RETURN_PERCENT \n");
        // 三方协议机票均价（仅经济舱）
        sqlBuilder.append(" , round(CASE WHEN SUM(CASE WHEN class_type IN ('Y') AND agreement_type IN ('B2G', 'TA') THEN coalesce(quantity,0) ELSE 0 END) != 0\n" +
                "    THEN toFloat64(SUM(CASE WHEN class_type IN ('Y') AND agreement_type IN ('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 END)) / SUM(CASE \n" +
                "        WHEN class_type IN ('Y') AND agreement_type IN ('B2G', 'TA') THEN coalesce(quantity,0) ELSE 0 END) ELSE 0 END, 2) AS FLT_AVG_PRICE_TA_ECONOMY \n");
        // 三方协议前返机票均价（仅经济舱）
        sqlBuilder.append(" , round(CASE WHEN SUM(CASE WHEN class_type IN ('Y') AND agreement_type IN ('B2G', 'TA') AND bf_return = 'T' " +
                "    THEN coalesce(quantity,0) ELSE 0 END) != 0 THEN toFloat64(SUM(CASE WHEN class_type IN ('Y') AND agreement_type IN ('B2G', 'TA') AND bf_return = 'T' " +
                "    THEN coalesce(price, 0) ELSE 0 END)) / SUM(CASE WHEN class_type IN ('Y') AND agreement_type IN ('B2G', 'TA') AND bf_return = 'T' " +
                "    THEN coalesce(quantity,0) ELSE 0 END) ELSE 0 END, 2) AS FLT_AVG_PRICE_TA_BF_RETURN_ECONOMY \n");
        // 非三方协议机票均价（仅经济舱）
        sqlBuilder.append(" , round(CASE WHEN SUM(CASE WHEN class_type IN ('Y') AND agreement_type NOT IN ('B2G', 'TA') THEN coalesce(quantity,0) ELSE 0 END) != 0 THEN\n" +
                "      toFloat64(SUM(CASE WHEN class_type IN ('Y') AND agreement_type NOT IN ('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 END))" +
                "  / SUM(CASE WHEN class_type IN ('Y') AND agreement_type NOT IN ('B2G', 'TA') THEN coalesce(quantity,0) ELSE 0 END) ELSE 0 END, 2) " +
                "AS FLT_AVG_PRICE_NTA_ECONOMY \n");
        // RC订单数占比
        sqlBuilder.append(" , round(CASE WHEN COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END) != 0 \n" +
                "    then COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END) " +
                "/ COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END) ELSE 0 END, 4) * 100 AS FLT_RC_PERCENT \n");
        // 三方协议节省金额(仅国内经济舱）
        sqlBuilder.append(" , round(SUM(CASE WHEN (flight_class = 'N' and class_type = 'Y') or flight_class = 'I' " +
                "THEN coalesce(save_amount_3c, 0) ELSE 0 END),2) AS FLT_TOTAL_SAV_AMOUNT_3C \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(" and airline in(").append(agreementAirlineQuery(parmList, requestDto, productType, isBookCaliber)).append(")");
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(buildListConditionPreSql(requestDto.getAirLines(), parmList, "airline"));
        sqlBuilder.append(SharkUtils.isEN(lang) ? " and coalesce(airline_en_name,'') != '' " : " and coalesce(airline_cn_name,'') != ''");
        if (needGroup) {
            sqlBuilder.append(" group by airline,airlineName");
        }
        sqlBuilder.append(" order by FLT_TOTAL_PRICE desc");
        // 查询clickhouse
        return sqlBuilder.toString();
    }


    public String agreementDeptDetailSql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO requestDto, Pager pager,
                                         String productType, boolean isBookCaliber, String user) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select %s");
        // 总成交净价
        sqlBuilder.append(" , SUM(coalesce(price, 0)) AS FLT_TOTAL_PRICE \n");
        // 三方协议成交净价
        sqlBuilder.append(", SUM(CASE WHEN agreement_type IN ('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 END) AS FLT_TOTAL_PRICE_TA \n");
        // 三方协议成交净价占比
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(coalesce(price, 0))) != 0 " +
                "THEN toFloat64(SUM(CASE WHEN agreement_type IN ('B2G', 'TA') THEN coalesce(price, 0) ELSE 0 END))/toFloat64(SUM(coalesce(price, 0))) ELSE 0 end * 100, 4)" +
                " as FLT_PRICE_TA_PERCENT\n");
        // 总票张数
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) AS FLT_TOTAL_QUANTITY \n");
        // 三方协议票张数
        sqlBuilder.append(", SUM(CASE WHEN agreement_type IN ('B2G', 'TA') THEN coalesce(quantity, 0) ELSE 0 END) AS FLT_TOTAL_QUANTITY_TA \n");
        // 三方协议票张数占比
        sqlBuilder.append(", round(CASE WHEN SUM(coalesce(quantity, 0)) != 0 " +
                "THEN SUM(CASE WHEN agreement_type IN ('B2G', 'TA') THEN coalesce(quantity, 0) ELSE 0 END)/SUM(coalesce(quantity, 0)) " +
                "ELSE 0 END, 4) * 100 AS FLT_QUANTITY_TA_PERCENT \n");
        // 三方协议节省金额(仅国内经济舱）
        sqlBuilder.append(" , round(SUM(CASE WHEN (flight_class = 'N' and class_type = 'Y') or flight_class = 'I' " +
                "THEN coalesce(save_amount_3c, 0) ELSE 0 END),2) AS FLT_TOTAL_SAV_AMOUNT_3C \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        JoinCondition biz = joinCondition(analysisObjectEnum);
        sqlBuilder.append(" and airline in(").append(airlineSubQuery(parmList, requestDto, productType, isBookCaliber)).append(")");
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(buildListConditionPreSql(requestDto.getAirLines(), parmList, "airline"));
        sqlBuilder.append(buildPreSqlUser(user, parmList));
        sqlBuilder.append(" group by %s");
        sqlBuilder.append(String.format(" order by FLT_TOTAL_PRICE desc, %s", biz.getGroupFields()));
        sqlBuilder.append(" limit ?, ? ");
        parmList.add((pager.pageIndex - 1) * pager.pageSize);
        parmList.add(pager.pageSize);
        // 查询clickhouse
        return String.format(sqlBuilder.toString(), biz.getResultFields(), biz.getGroupFields());
    }


    public String agreementDeptDetailCountSql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO requestDto,
                                              String productType, boolean isBookCaliber, String user) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select count(1) as countAll from( select %s");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(" and airline in(").append(airlineSubQuery(parmList, requestDto, productType, isBookCaliber)).append(")");
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(buildPreSqlUser(user, parmList));
        sqlBuilder.append(buildListConditionPreSql(requestDto.getAirLines(), parmList, "airline"));
        sqlBuilder.append(" group by %s)");
        JoinCondition biz = joinCondition(analysisObjectEnum);
        return String.format(sqlBuilder.toString(), biz.getResultFields(), biz.getGroupFields());
    }

    /**
     * 协议航司查询
     * @param parmList
     * @param requestDto
     * @param productType
     * @param isBookCaliber
     * @return
     */
    private String agreementAirlineQuery(List<Object> parmList, BaseQueryConditionDTO requestDto, String productType, boolean isBookCaliber) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select distinct airline");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        sqlBuilder.append(" and agreement_type in ('B2G','TA') ");
//        sqlBuilder.append(" AND ((customerid <> '' and agreement_airline <> '') or (agreement_airline <> '' and agreement_status in('SIGNED','EXPIRED'))) ");
        sqlBuilder.append(" and audited <> 'F' ");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        return sqlBuilder.toString();
    }

    protected String getDestCondition(String destType) {
        StringBuilder stringBuilder = new StringBuilder();
        String china = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.country.china");
        // 国内
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(destType, "dom")) {
            stringBuilder.append(" and arrival_country = '" + china + "'  ");
        }
        // 国际
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(destType, "inter")) {
            stringBuilder.append(" and arrival_country != '" + china + "'  ");
        }
        return stringBuilder.toString();
    }

    /**
     * 与airlines签协议的公司
     * @param requestDto
     * @param productType
     * @param airlines
     * @param parmList
     * @param isBookCaliber
     * @return
     */
    private String queryCorpSql(BaseQueryConditionDTO requestDto, String productType, List<String> airlines, List<Object> parmList, boolean isBookCaliber, String dim) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select DISTINCT corp_corporation ");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount(dim, airlines, parmList));
        return sqlBuilder.toString();
    }

    private String airlineSubQuery(List<Object> parmList, BaseQueryConditionDTO requestDto, String productType, boolean isBookCaliber) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select distinct airline");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(" AND agreement_type IN ('B2G', 'TA')");
//        sqlBuilder.append(" AND customerid <> '' ");
        sqlBuilder.append(" AND airline <> '' ");
        return sqlBuilder.toString();
    }


    public List<String> queryCostCenterOrDepartmentOrCorpId(CostCenterAndDepartSearchType searchType) throws Exception {

        return super.queryCostCenterOrDepartmentOrCorpId(searchType,ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
    }


}
