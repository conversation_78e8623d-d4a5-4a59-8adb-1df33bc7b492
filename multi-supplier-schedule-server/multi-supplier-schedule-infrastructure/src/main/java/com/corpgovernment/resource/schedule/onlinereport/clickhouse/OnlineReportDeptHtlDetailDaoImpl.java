package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptDetailDaoService;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDER_DATE;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
public class OnlineReportDeptHtlDetailDaoImpl extends AbstractOnlineReportDeptDetailDao implements OnlineReportDeptDetailDaoService {

    @Override
    protected String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        String condition = String.format(" and order_status = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus"));
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        switch (subQueryReportBuTypeEnum) {
            case HOTEL:
                break;
            case HOTEL_N:
                condition += " and  (is_oversea = 'F' or  is_oversea = 'O')";
                break;
            case HOTEL_I:
                condition += " and  is_oversea = 'T'";
                break;
            case HOTEL_AGREEMENT:
                condition += " and  producttype_all = '" + ta + "'";
                break;
            case HOTEL_NON_AGREEMENT:
                condition += " and  producttype_all != '" + ta + "'";
                break;
            default:
                break;
        }
        return condition;
    }

    @Override
    protected String getProcutTypeCondition(String productType) {
        return getHotelCondition(productType);
    }

    /**
     * 酒店
     *
     * @return
     */
    protected String getHotelCondition(String hotelType) {
        StringBuilder stringBuilder = new StringBuilder(String.format(" and order_status = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus")));
        // 国内、港澳台
        if (StringUtils.equalsIgnoreCase(hotelType, "dom")) {
            stringBuilder.append(" and is_oversea in ('F','O') ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(hotelType, "inter")) {
            stringBuilder.append(" and is_oversea in ('T') ");
        }
        return stringBuilder.toString();
    }

    /**
     * 统计字段
     *
     * @return
     */
    public String statical() {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        List sql = new ArrayList();
        sql.add("sum(coalesce(quantity, 0)) as TOTAL_HTL_QUANTITY");
        // 三方协议间夜数
        sql.add("sum(case when producttype_all='" + ta + "' then coalesce(quantity, 0) else 0 end) as THREE_PART_QUANTITY");
        sql.add("sum(coalesce(real_pay_with_servicefee, 0)) as TOTAL_REAL_PAY");
        sql.add("count(distinct case when (is_refund = 'F' AND is_rc = 'T') then order_id end ) AS TOTAL_HTL_RC_TIMES");
        sql.add("sum(coalesce(room_price, 0)) as TOTAL_ROOM_PRICE");
        // 三方协议金额
        // sql.add("sum(case when producttype_all='三方协议' then coalesce(room_price, 0) else 0 end) as THREE_PART_ROOM_PRICE");
        sql.add("sum(case when producttype_all = '" + ta + "' then toDecimal64(coalesce(real_pay, 0), 4) + coalesce(postservicefee, 0) else 0 end) as THREE_PART_ROOM_PRICE");
        sql.add("count(distinct case when is_refund = 'F' then order_id end ) AS TOTAL_ISSUE_CNT_ORDER");
        sql.add("SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as TOTAL_HTL_OVER_AMOUNT");
        String shareRoom = String.format(" houseshare_mode_type in ('%s', '%s', '%s') ",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.room"),
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.travel"),
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.all")
        );

        sql.add(" SUM(coalesce(dead_price_onenight, 0)*coalesce(quantity, 0)) AS TOTAL_DEAD_PRICE_ONENIGHT");

        sql.add(" toFloat64(SUM(coalesce(quantity, 0) * CASE WHEN " + shareRoom + " THEN persons ELSE 1 END)) AS TOTAL_PERSONS");

        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    /**
     * 统计字段
     *
     * @return
     */
    public String staticalForeign() {
        List sql = new ArrayList();
        sql.add("sum(coalesce(quantity, 0)) as TOTAL_HTL_QUANTITY");
        sql.add("sum(coalesce(real_pay_with_servicefee, 0)) as TOTAL_REAL_PAY");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    /**
     * 同环比，总计数据
     *
     * @return
     */
    protected String momAndYoy() {
        return "SUM(toDecimal64(coalesce(real_pay_with_servicefee, 0), 4)) AS TOTAL_REAL_PAY";
    }

    /**
     * 占比
     *
     * @return
     */
    @Override
    protected String totalField() {
        return "SUM(coalesce(real_pay_with_servicefee, 0)) AS TOTAL_REAL_PAY, SUM(coalesce(quantity, 0)) AS TOTAL_HTL_QUANTITY";
    }

    /**
     * 返回字段
     *
     * @return
     */
    protected String baseQueryField() {
        List sql = new ArrayList();
        // REAL_PAY:
        sql.add("round(current.TOTAL_REAL_PAY, 4) AS REAL_PAY");
        // REAL_PAY_PERCENT:
        sql.add("round(case when coalesce(total.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0))"
                + ", toFloat64(coalesce(total.TOTAL_REAL_PAY, 0))) * 100 else 0 end, 4) as REAL_PAY_PERCENT");
        // YOY_LAST:
        sql.add("round(case when coalesce(yoy.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(yoy.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(yoy.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_LAST");
        // YOY_BEFORE_LAST:
        sql.add("round(case when coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_BEFORE_LAST");
        // MOM:
        sql.add("round(case when coalesce(mom.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(mom.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(mom.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as MOM");
        // add: THREE_PARTY_AMT
        // add: THREE_PARTY_AMT_PERCENT
        sql.add("round(current.THREE_PART_ROOM_PRICE,4) AS THREE_PARTY_AMT");
        sql.add("round(CASE WHEN toFloat64(coalesce(current.TOTAL_REAL_PAY, 0)) != 0 THEN " +
                " divide(toFloat64(coalesce(current.THREE_PART_ROOM_PRICE, 0)),toFloat64(coalesce(current.TOTAL_REAL_PAY, 0))) * 100 ELSE 0 END, 4) AS THREE_PARTY_AMT_PERCENT");
        // HTL_QUANTITY:
        sql.add("current.TOTAL_HTL_QUANTITY AS HTL_QUANTITY");
        // HTL_QUANTITY_PERCENT:
        sql.add("round(case when coalesce(total.TOTAL_HTL_QUANTITY, 0) != 0 "
                + "then divide(coalesce(current.TOTAL_HTL_QUANTITY, 0), coalesce(total.TOTAL_HTL_QUANTITY, 0)) * 100 else 0 end, 4) as HTL_QUANTITY_PERCENT");
        // add: THREE_PARTY_QUANTITY
        // add: THREE_PARTY_QUANTITY_PERCENT
        sql.add("current.THREE_PART_QUANTITY AS THREE_PARTY_QUANTITY");
        sql.add("round(CASE WHEN toFloat64(coalesce(current.TOTAL_HTL_QUANTITY, 0)) != 0 THEN " +
                "divide(toFloat64(coalesce(current.THREE_PART_QUANTITY, 0)), toFloat64(coalesce(current.TOTAL_HTL_QUANTITY, 0))) * 100 ELSE 0 END, 4) " +
                "AS THREE_PARTY_QUANTITY_PERCENT");

        // HTL_AVG_PRICE:
        sql.add("round(case when coalesce(current.TOTAL_HTL_QUANTITY, 0) != 0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_ROOM_PRICE, 0)), coalesce(current.TOTAL_HTL_QUANTITY, 0)) else 0 end, 4) as HTL_AVG_PRICE");
        // HTL_RC_TIMES:
        sql.add("round(current.TOTAL_HTL_RC_TIMES, 0) AS HTL_RC_TIMES");
        // HTL_RC_PERCENT:
        sql.add("round(case when coalesce(current.TOTAL_ISSUE_CNT_ORDER, 0) != 0 "
                + "then divide(coalesce(current.TOTAL_HTL_RC_TIMES, 0), coalesce(current.TOTAL_ISSUE_CNT_ORDER, 0)) * 100 else 0 end, 4) as HTL_RC_PERCENT");
        // HTL_OVER_AMOUNT:
        sql.add("round(current.TOTAL_HTL_OVER_AMOUNT, 4) AS HTL_OVER_AMOUNT");

        // HTL_AVG_DEAD_PRICE:
        sql.add("round(CASE WHEN toFloat64(current.TOTAL_PERSONS) != 0 THEN toFloat64(current.TOTAL_DEAD_PRICE_ONENIGHT) / " +
                "toFloat64(current.TOTAL_PERSONS) else 0 end, 2) AS HTL_AVG_DEAD_PRICE");
        // HTL_USAGE_RATE:
        sql.add("round(CASE WHEN toFloat64(current.TOTAL_DEAD_PRICE_ONENIGHT) != 0 THEN toFloat64(current.TOTAL_ROOM_PRICE) / " +
                "toFloat64(current.TOTAL_DEAD_PRICE_ONENIGHT)  else 0 end, 4)* 100 AS HTL_USAGE_RATE");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    @Override
    protected String orderByField() {
        return "TOTAL_REAL_PAY desc";
    }

    @Override
    protected BaseConditionPrebuilder.TableAndTimeColBind getTargetTableAndTimeColumn(String statisticalCaliber, Boolean isForegin) {
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = new BaseConditionPrebuilder.TableAndTimeColBind();
        if (isForegin) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                tableAndTimeColBind.setClickHouseTable(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD_FOREIGN_MULTI_CURRENCY);
            } else {
                tableAndTimeColBind.setClickHouseTable(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD_FOREIGN);
            }
            tableAndTimeColBind.setDateColumn(ORDER_DATE);
        } else {
            tableAndTimeColBind.setClickHouseTable(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
            tableAndTimeColBind.setDateColumn(REPORT_DATE);
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                tableAndTimeColBind.setDateColumn(ORDER_DATE);
            }
        }
        return tableAndTimeColBind;
    }
}
