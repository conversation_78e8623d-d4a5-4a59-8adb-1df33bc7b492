package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @className OnlineReportDeptOtherDaoService
 * @date 2024/9/10
 */
public interface OnlineReportDeptOtherDaoService {

    <T> List<T> deptAnalysis(BaseQueryConditionDTO requestDto, AnalysisTypeEnum analysisTypeEnum,
                             AnalysisObjectEnum analysisObjectEnum, Class<T> clazz, Pager pager) throws Exception;

    Integer count(AnalysisObjectEnum analysisObjectEnum, AnalysisTypeEnum analysisTypeEnum,
                  BaseQueryConditionDTO baseQueryConditionDto) throws Exception;

    /**
     * 商旅部门数据
     */
    BigDecimal deptCorpAnalysis(String startTime, String endTime, String statisticalCaliber, AnalysisTypeEnum analysisTypeEnum,
                                String consumptionLevel, String compareCorpSameLevel)
            throws Exception;

}
