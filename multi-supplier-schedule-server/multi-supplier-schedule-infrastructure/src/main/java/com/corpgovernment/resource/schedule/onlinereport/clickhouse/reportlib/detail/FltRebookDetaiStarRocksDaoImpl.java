package com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail;

import com.corpgovernment.resource.schedule.onlinereport.dto.TimeFilterTypeInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-21 13:39
 * @desc 改签
 */
@Service
@Repository
public class FltRebookDetaiStarRocksDaoImpl extends FltDetaiDaoImpl {

    @Override
    protected String buildPreSqlTime(List<TimeFilterTypeInfoDTO> timeFilterTypeInfoList, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isEmpty(timeFilterTypeInfoList)) {
            return sqlBuffer.toString();
        }
        for (TimeFilterTypeInfoDTO timeFilterTypeInfo : timeFilterTypeInfoList) {
            // 预订时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate")) {
                sqlBuffer.append(buildTimeFilter(timeFilterTypeInfo, parmList));
            }
            // 改签时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    sqlBuffer.append(" and ").append("subString(rebook_time, 1, 10)").append(" >= ? ");
                    parmList.add(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    sqlBuffer.append(" and ").append("subString(rebook_time, 1, 10)").append(" <= ? ");
                    parmList.add(timeFilterTypeInfo.getEndTime());
                }
            }
            // 起飞时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "usedate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    sqlBuffer.append(" and ").append("subString(takeoff_time, 1, 10)").append(" >= ? ");
                    parmList.add(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    sqlBuffer.append(" and ").append("subString(takeoff_time, 1, 10)").append(" <= ? ");
                    parmList.add(timeFilterTypeInfo.getEndTime());
                }
            }
        }
        return sqlBuffer.toString();
    }

    /**
     * 改签
     *
     * @return
     */
    @Override
    protected String buildPreSqlSpecial() {
        return " and is_rebook = 'T'";
    }
}
