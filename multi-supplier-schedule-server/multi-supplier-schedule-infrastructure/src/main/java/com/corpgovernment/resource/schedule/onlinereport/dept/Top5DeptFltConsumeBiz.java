package com.corpgovernment.resource.schedule.onlinereport.dept;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FltTopDeptConsumeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptUidConsumeDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptUidConsumeDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportFltTopDeptConsume;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopDeptConsumeDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.FltDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.FltTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.HotTargetDTO;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportDeptConsumeDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptConsumeDaoService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.dept.sr.SrOnlineReportDeptConsumeDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.convert.TopDeptMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.PaginationHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:22
 * @Desc 成本中心消费金额排名
 */
@Service
public class Top5DeptFltConsumeBiz {

    @Autowired
    private OnlineReportDeptConsumeDaoImpl reportConsumeDao;

    @Autowired
    private SrOnlineReportDeptConsumeDaoImpl srReportConsumeDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private TopDeptMapper topDeptMapper;

    @Qualifier("screenThreadPoolExecutor")
    @Resource
    private ThreadPoolExecutor executorService;

    /**
     * 获取部门消费dao
     * @return
     */
    private OnlineReportDeptConsumeDaoService getDeptConsumeDaoService(BaseQueryCondition baseQueryCondition) {
        boolean useSr = baseQueryCondition != null && BooleanUtils.isTrue(baseQueryCondition.useStarRocks);
        return useSr ? srReportConsumeDao : reportConsumeDao;
    }

    /**
     * 获取部门消费dao
     * @return
     */
    private OnlineReportDeptConsumeDaoService getDeptConsumeDaoService(BaseQueryConditionDTO baseQueryCondition) {
        boolean useSr = baseQueryCondition != null && BooleanUtils.isTrue(baseQueryCondition.useStarRocks);
        return useSr ? srReportConsumeDao : reportConsumeDao;
    }

    
    public FltTopDeptConsumeInfo topDeptConsume(OnlineReportTopDeptConsumeDetailRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        baseQueryConditionDto.setLang(request.getLang());
        int topLimit = Optional.ofNullable(request.getTopLimit()).orElse(OrpConstants.HUNDRED_1);
        AnalysisObjectEnum downObjectEnum = request.getDrillDownObjectEnum();
        List<FltTopDeptDTO> deptConsumeList = getDeptConsumeDaoService(request.getBasecondition()).topDeptAnalysis(baseQueryConditionDto, request.getAnalysisObjectEnum(),
                FltTopDeptDTO.class, request.getQueryBu(), request.getAnalysisObjectOrgInfo(), downObjectEnum, request.getDrillDownVal());
        FltTopDeptConsumeInfo result = new FltTopDeptConsumeInfo();
        List<OnlineReportFltTopDeptConsume> onlineReportDeptConsumes = new ArrayList<>();
        FltDeptDTO sumTop = new FltDeptDTO();
        FltDeptDTO sumOther = new FltDeptDTO();
        FltDeptDTO sumAll = new FltDeptDTO();
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        if (CollectionUtils.isNotEmpty(deptConsumeList)) {
            List<FltDeptDTO> consumeList = convertToFltDeptList(deptConsumeList);
            // 获得排序指标
            String sort = (String) map.get("sortStatistics");
            if (StringUtils.equalsIgnoreCase(sort, "flightAmount")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getTotalAmount).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightQuantity")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getTotalQuantity).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightAvgTpmsPrice")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getAvgTpmsPrice).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightAvgPrice")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getAvgPrice).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightFullFaretktPercent")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getFullfaretktPercent).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightSaveRate")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getSaveRate).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightRefundRate")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getRefundRate).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightRebookRate")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getRebookRate).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightRcPercent")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getRcPercent).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightSave3cRate")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getSaveAmount3cRate).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightSave2cRate")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getSaveAmount2cRate).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightSaveControlRate")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getControlSaveRate).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightCarbons")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getTotalCarbons).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightCarbonsSaveRate")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getCarbonSaveRate).reversed().thenComparing(FltDeptDTO::getAggId));
            } else if (StringUtils.equalsIgnoreCase(sort, "flightOverAmount")) {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getTotalOverAmount).reversed().thenComparing(FltDeptDTO::getAggId));
            } else {
                consumeList.sort(Comparator.comparing(FltDeptDTO::getTotalAmount).reversed().thenComparing(FltDeptDTO::getAggId));
            }
            if (CollectionUtils.isNotEmpty(consumeList)) {
                for (int i = 0; i < consumeList.size(); i++) {
                    FltDeptDTO deptConsume = consumeList.get(i);
                    selfAdd(sumAll, deptConsume);
                    if (i < topLimit) {
                        selfAdd(sumTop, deptConsume);
                        OnlineReportFltTopDeptConsume temp = convertToFltTopDeptBO(deptConsume, StringUtils.EMPTY);
                        if (!needFilterZero(temp, sort)) {
                            onlineReportDeptConsumes.add(convertToFltTopDeptBO(deptConsume, StringUtils.EMPTY));
                        }
                    } else {
                        // 超过topLimit以外的数据都聚合成“other”
                        selfAdd(sumOther, deptConsume);
                        result.setOtherConsume(convertToFltTopDeptBO(sumOther, "other"));
                    }
                }
                result.setSumConsume(convertToFltTopDeptBO(sumAll, "all"));
            }
        }
        // 是否需要查询行业和商旅
        String needIndustry = (String) map.get("needIndustry");
        // 下钻的时候，needIndustry为T的时候，需要查询公司和下钻对象的数据
        if (StringUtils.equalsIgnoreCase(needIndustry, "T")) {
            if (downObjectEnum == null) {
                List<FltTopDeptDTO> corp = getDeptConsumeDaoService(request.getBasecondition()).topDeptAnalysisCorpAndIndustry(baseQueryConditionDto.getStatisticalCaliber(),
                        FltTopDeptDTO.class, request.getQueryBu(), baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(),
                        request.getProductType(), getIndustryList(baseQueryConditionDto.getIndustryType()), DataTypeEnum.CORP, baseQueryConditionDto.getCompareSameLevel(),
                        baseQueryConditionDto.getConsumptionLevel(),
                        baseQueryConditionDto.getCompareCorpSameLevel());
                result.setCorpConsume(convertToFltTopDeptBO(convertToFltDeptList(corp).get(0), "corp"));
                List<FltTopDeptDTO> industry = getDeptConsumeDaoService(request.getBasecondition()).topDeptAnalysisCorpAndIndustry(baseQueryConditionDto.getStatisticalCaliber(),
                        FltTopDeptDTO.class, request.getQueryBu(), baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(),
                        request.getProductType(), getIndustryList(baseQueryConditionDto.getIndustryType()), DataTypeEnum.INDUSTRY, baseQueryConditionDto.getCompareSameLevel(),
                        baseQueryConditionDto.getConsumptionLevel(),
                        baseQueryConditionDto.getCompareCorpSameLevel());
                result.setIndustryConsume(convertToFltTopDeptBO(convertToFltDeptList(industry).get(0), "industry"));
            } else {
                // 下钻的时候CorpConsume存的是公司的数据，IndustryConsume存的是部门的数据
                List<FltTopDeptDTO> company = getDeptConsumeDaoService(request.getBasecondition()).topDeptAnalysisDown(baseQueryConditionDto,
                        FltTopDeptDTO.class, request.getQueryBu(), request.getAnalysisObjectOrgInfo(), null);
                result.setCorpConsume(convertToFltTopDeptBO(convertToFltDeptList(company).get(0), "company"));
            }
        }
        result.setTopList(onlineReportDeptConsumes);
        return result;
    }

    /**
     * 需要过滤 0值
     *
     * @param t
     * @param sort
     * @return
     */
    private boolean needFilterZero(OnlineReportFltTopDeptConsume t, String sort) {
        if (StringUtils.equalsIgnoreCase(sort, "flightFullFaretktPercent")) {
            return t.getFullfaretktPercent() == null || BigDecimal.ZERO.compareTo(t.getFullfaretktPercent()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "flightSaveRate")) {
            return t.getSaveRate() == null || BigDecimal.ZERO.compareTo(t.getSaveRate()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "flightRefundRate")) {
            return t.getRefundRate() == null || BigDecimal.ZERO.compareTo(t.getRefundRate()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "flightRebookRate")) {
            return t.getRebookRate() == null || BigDecimal.ZERO.compareTo(t.getRebookRate()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "flightRcPercent")) {
            return t.getRcPercent() == null || BigDecimal.ZERO.compareTo(t.getRcPercent()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "flightSave3cRate")) {
            return t.getSaveAmount3cRate() == null || BigDecimal.ZERO.compareTo(t.getSaveAmount3cRate()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "flightSave2cRate")) {
            return t.getSaveAmount2cRate() == null || BigDecimal.ZERO.compareTo(t.getSaveAmount2cRate()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "flightSaveControlRate")) {
            return t.getControlSaveRate() == null || BigDecimal.ZERO.compareTo(t.getControlSaveRate()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "flightCarbons")) {
            return t.getTotalCarbons() == null || BigDecimal.ZERO.compareTo(t.getTotalCarbons()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "flightCarbonsSaveRate")) {
            return t.getCarbonSaveRate() == null || BigDecimal.ZERO.compareTo(t.getCarbonSaveRate()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "flightOverAmount")) {
            return t.getTotalOverAmount() == null || BigDecimal.ZERO.compareTo(t.getTotalOverAmount()) == 0;
        }
        return false;
    }

    
    public OnlineReportDeptUidConsumeDetailInfo topDeptUidConsume(OnlineReportDeptUidConsumeDetailRequest request) throws Exception {
        OnlineReportDeptUidConsumeDetailInfo result = new OnlineReportDeptUidConsumeDetailInfo();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        baseQueryConditionDto.setLang(request.getLang());
        AnalysisObjectEnum downObjectEnum = request.getDrillDownObjectEnum();
        List<FltTopDeptDTO> deptConsumeList = getDeptConsumeDaoService(request.getBasecondition()).topDeptUidAnalysis(baseQueryConditionDto, AnalysisObjectEnum.UID,
                FltTopDeptDTO.class, request.getQueryBu(), request.getAnalysisObjectOrgInfo(),
                downObjectEnum, request.getDrillDownVal(), true, request.getUser());
        List<OnlineReportFltTopDeptConsume> onlineReportDeptConsumes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deptConsumeList)) {
            List<FltDeptDTO> consumeList = convertToFltDeptList(deptConsumeList);
            BigDecimal sumAmount = consumeList.stream().map(FltDeptDTO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (int i = 0; i < consumeList.size(); i++) {
                FltDeptDTO deptConsume = consumeList.get(i);
                onlineReportDeptConsumes.add(convertToFltTopDeptBO(deptConsume, StringUtils.EMPTY, sumAmount));
            }
            onlineReportDeptConsumes.sort(Comparator.comparing(OnlineReportFltTopDeptConsume::getTotalAmount).reversed()
                    .thenComparing(OnlineReportFltTopDeptConsume::getDimId).thenComparing(OnlineReportFltTopDeptConsume::getDim));
            Pager pager = BizUtils.initPager(request.getPage());
            PaginationHelper<OnlineReportFltTopDeptConsume> paginationHelper = new PaginationHelper<>();
            List<OnlineReportFltTopDeptConsume> fltList = paginationHelper.paginateFromAero(onlineReportDeptConsumes, pager.getPageIndex(), pager.getPageSize());
            List<String> uids = fltList.stream().map(OnlineReportFltTopDeptConsume::getDimId).collect(Collectors.toList());
            List<HotTargetDTO> hotFlightCityDTOList = getHotFlightCity(uids, baseQueryConditionDto, request.getLang());
            Map<String, List<HotTargetDTO>> group = hotFlightCityDTOList.stream().collect(Collectors.groupingBy(HotTargetDTO::getUid));
            Map map = sortMap(group);
            for (OnlineReportFltTopDeptConsume bo : fltList) {
                bo.setTopFlightCity(StringUtils.trimToEmpty((String) map.get(bo.getDimId())));
            }
            result.setFltList(fltList);
            result.setTotalRecords(deptConsumeList.size());
        }
        return result;
    }

    private List<HotTargetDTO> getHotFlightCity(List<String> uids, BaseQueryConditionDTO dto, String lang) throws ExecutionException, InterruptedException {
        int batchSize = ConfigUtils.getUidBatchSize();
        List<List<String>> list = OrpCommonUtils.splitList(uids, batchSize);
        List<HotTargetDTO> result = new ArrayList<>();
        List<Future<List<HotTargetDTO>>> futureList = new ArrayList<>();
        for (List<String> subList : list) {
            futureList.add(executorService.submit(new Callable<List<HotTargetDTO>>() {
                @Override
                public List<HotTargetDTO> call() throws Exception {
                    return getDeptConsumeDaoService(dto).getHotFlightCitySql(dto, subList, HotTargetDTO.class, lang);
                }
            }));
        }
        for (Future<List<HotTargetDTO>> future : futureList) {
            result.addAll(future.get());
        }
        return result;
    }

    private Map sortMap(Map<String, List<HotTargetDTO>> mapOfEntities) {
        // 创建一个Map来存储每个Key对应的最大Entity
        Map<String, String> maxEntities = new HashMap<>();
        Comparator<HotTargetDTO> comparator = Comparator
                .comparingInt(HotTargetDTO::getCountSort)
                .thenComparing(HotTargetDTO::getTopTarget);
        for (Map.Entry<String, List<HotTargetDTO>> entry : mapOfEntities.entrySet()) {
            String key = entry.getKey();
            List<HotTargetDTO> entities = entry.getValue();
            if (entities != null && !entities.isEmpty()) {
                HotTargetDTO maxEntity = Collections.max(entities, comparator); // 使用Entity的compareTo方法
                maxEntities.put(key, maxEntity.getTopTarget());
            }
        }
        return maxEntities;
    }

    private void selfAdd(FltDeptDTO sumTop, FltDeptDTO deptConsume) {
        sumTop.setTotalAmount(OrpCommonUtils.addBigDecimal(deptConsume.getTotalAmount(), sumTop.getTotalAmount()));
        sumTop.setTotalQuantity(Optional.ofNullable(deptConsume.getTotalQuantity()).orElse(OrpConstants.ZERO) +
                Optional.ofNullable(sumTop.getTotalQuantity()).orElse(OrpConstants.ZERO));
        sumTop.setTotalFullfaretkt(Optional.ofNullable(deptConsume.getTotalFullfaretkt()).orElse(OrpConstants.ZERO) +
                Optional.ofNullable(sumTop.getTotalFullfaretkt()).orElse(OrpConstants.ZERO));
        sumTop.setTotalEconmyTpms(deptConsume.getTotalEconmyTpms().add(Optional.ofNullable(sumTop.getTotalEconmyTpms()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalEconmyQuantity(deptConsume.getTotalEconmyQuantity() + Optional.ofNullable(sumTop.getTotalEconmyQuantity()).orElse(OrpConstants.ZERO));
        sumTop.setTotalEconmyPrice(deptConsume.getTotalEconmyPrice().add(Optional.ofNullable(sumTop.getTotalEconmyPrice()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalDomEconmyDiscount(deptConsume.getTotalDomEconmyDiscount().add(Optional.ofNullable(sumTop.getTotalDomEconmyDiscount()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalDomEconmyQuantity(deptConsume.getTotalDomEconmyQuantity() + Optional.ofNullable(sumTop.getTotalDomEconmyQuantity()).orElse(OrpConstants.ZERO));
        sumTop.setTotalDomEconmyFullfaretkt(deptConsume.getTotalDomEconmyFullfaretkt() + Optional.ofNullable(sumTop.getTotalDomEconmyFullfaretkt()).orElse(OrpConstants.ZERO));
        sumTop.setTotalDomEconmyOrdertkt(deptConsume.getTotalDomEconmyOrdertkt() + Optional.ofNullable(sumTop.getTotalDomEconmyOrdertkt()).orElse(OrpConstants.ZERO));
        sumTop.setTotalRebookFee(deptConsume.getTotalRebookFee().add(Optional.ofNullable(sumTop.getTotalRebookFee()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalRefundFee(deptConsume.getTotalRefundFee().add(Optional.ofNullable(sumTop.getTotalRefundFee()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalRefundtkt(deptConsume.getTotalRefundtkt() + Optional.ofNullable(sumTop.getTotalRefundtkt()).orElse(OrpConstants.ZERO));
        sumTop.setTotalRebooktkt(deptConsume.getTotalRebooktkt() + Optional.ofNullable(sumTop.getTotalRebooktkt()).orElse(OrpConstants.ZERO));
        sumTop.setTotalOrdertkt(deptConsume.getTotalOrdertkt() + Optional.ofNullable(sumTop.getTotalOrdertkt()).orElse(OrpConstants.ZERO));
        sumTop.setTotalOverAmount(deptConsume.getTotalOverAmount().add(Optional.ofNullable(sumTop.getTotalOverAmount()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalRcTimes(deptConsume.getTotalRcTimes() + Optional.ofNullable(sumTop.getTotalRcTimes()).orElse(OrpConstants.ZERO));
        sumTop.setTotalOrderCount(deptConsume.getTotalOrderCount() + Optional.ofNullable(sumTop.getTotalOrderCount()).orElse(OrpConstants.ZERO));
        sumTop.setTotalSaveAmount3c(deptConsume.getTotalSaveAmount3c().add(Optional.ofNullable(sumTop.getTotalSaveAmount3c()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalNetfare3c(deptConsume.getTotalNetfare3c().add(Optional.ofNullable(sumTop.getTotalNetfare3c()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalSaveAmountPremium(deptConsume.getTotalSaveAmountPremium().add(Optional.ofNullable(sumTop.getTotalSaveAmountPremium()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalNetfarePremium(deptConsume.getTotalNetfarePremium().add(Optional.ofNullable(sumTop.getTotalNetfarePremium()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalControlSave(deptConsume.getTotalControlSave().add(Optional.ofNullable(sumTop.getTotalControlSave()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalControlNetfare(deptConsume.getTotalControlNetfare().add(Optional.ofNullable(sumTop.getTotalControlNetfare()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalCarbons(deptConsume.getTotalCarbons().add(Optional.ofNullable(sumTop.getTotalCarbons()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalMedianCarbons(deptConsume.getTotalMedianCarbons().add(Optional.ofNullable(sumTop.getTotalMedianCarbons()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalCarbonSave(deptConsume.getTotalCarbonSave().add(Optional.ofNullable(sumTop.getTotalCarbonSave()).orElse(BigDecimal.ZERO)));
    }

    private List<FltDeptDTO> convertToFltDeptList(List<FltTopDeptDTO> topDeptDTO) {
        List<FltDeptDTO> boList = new ArrayList<>();
        for (FltTopDeptDTO dto : topDeptDTO) {
            FltDeptDTO fltDeptDTO = topDeptMapper.convertFltTopDeptDTO(dto);
            // 里程均价
            fltDeptDTO.setAvgTpmsPrice(OrpReportUtils.divideUp(dto.getTotalEconmyPrice(),
                    Optional.ofNullable(dto.getTotalEconmyTpms()).orElse(BigDecimal.ZERO), OrpConstants.TWO));
            // 平均票价
            fltDeptDTO.setAvgPrice(OrpReportUtils.divideUp(dto.getTotalEconmyPrice(),
                    new BigDecimal(Optional.ofNullable(dto.getTotalEconmyQuantity()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
            // 平均折扣
            fltDeptDTO.setAvgDiscount(OrpReportUtils.divideUp(dto.getTotalDomEconmyDiscount(),
                    new BigDecimal(Optional.ofNullable(dto.getTotalDomEconmyQuantity()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
            // 全价票占比
            fltDeptDTO.setFullfaretktPercent(OrpReportUtils.divideUp(new BigDecimal(Optional.ofNullable(dto.getTotalDomEconmyFullfaretkt()).orElse(OrpConstants.ZERO)),
                    new BigDecimal(Optional.ofNullable(dto.getTotalDomEconmyOrdertkt()).orElse(OrpConstants.ZERO)), OrpConstants.FOUR));
            BigDecimal totalSaveAmount = OrpCommonUtils.addBigDecimal(dto.getTotalSaveAmount3c(), dto.getTotalSaveAmountPremium(), dto.getTotalControlSave());
            BigDecimal totalNetfare = OrpCommonUtils.addBigDecimal(dto.getTotalNetfare3c(), dto.getTotalNetfarePremium(), dto.getTotalControlNetfare());
            // 节省率
            fltDeptDTO.setSaveRate(OrpReportUtils.divideUp(totalSaveAmount, totalNetfare, OrpConstants.FOUR));
            // 退票率
            fltDeptDTO.setRefundRate(OrpReportUtils.divideHalfUp(Optional.ofNullable(dto.getTotalRefundtkt()).orElse(OrpConstants.ZERO),
                    Optional.ofNullable(dto.getTotalOrdertkt()).orElse(OrpConstants.ZERO), OrpConstants.FOUR));
            // 改签率
            fltDeptDTO.setRebookRate(OrpReportUtils.divideHalfUp(Optional.ofNullable(dto.getTotalRebooktkt()).orElse(OrpConstants.ZERO),
                    Optional.ofNullable(dto.getTotalOrdertkt()).orElse(OrpConstants.ZERO), OrpConstants.FOUR));
            // RC订单数占比
            fltDeptDTO.setRcPercent(OrpReportUtils.divideUp(dto.getTotalRcTimes(), dto.getTotalOrderCount(), OrpConstants.FOUR));
            // 三方协议节省率
            fltDeptDTO.setSaveAmount3cRate(OrpReportUtils.divideUp(dto.getTotalSaveAmount3c(), dto.getTotalNetfare3c(), OrpConstants.FOUR));
            // 尊享节省率
            fltDeptDTO.setSaveAmount2cRate(OrpReportUtils.divideUp(dto.getTotalSaveAmountPremium(), dto.getTotalNetfarePremium(), OrpConstants.FOUR));
            // 管控节省率
            fltDeptDTO.setControlSaveRate(OrpReportUtils.divideUp(dto.getTotalControlSave(), dto.getTotalControlNetfare(), OrpConstants.FOUR));
            // 碳排放
            fltDeptDTO.setTotalCarbons(dto.getTotalCarbons());
            // 碳排放中位数
            fltDeptDTO.setTotalMedianCarbons(dto.getTotalMedianCarbons());
            fltDeptDTO.setTotalCarbonSave(dto.getTotalCarbonSave());
            // 碳排节省率
            fltDeptDTO.setCarbonSaveRate(OrpReportUtils.divideUp(fltDeptDTO.getTotalCarbonSave(),
                    dto.getTotalMedianCarbons(), OrpConstants.FOUR));
            boList.add(fltDeptDTO);
        }
        return boList;
    }

    private OnlineReportFltTopDeptConsume convertToFltTopDeptBO(FltDeptDTO topDeptDTO, String aggType) {
        OnlineReportFltTopDeptConsume bo = new OnlineReportFltTopDeptConsume();
        bo.setDimId(StringUtils.trimToEmpty(topDeptDTO.getAggId()));
        bo.setDim(StringUtils.isEmpty(aggType) ? topDeptDTO.getAggType() : aggType);
        // 金额
        bo.setTotalAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalAmount()));
        // 票张数
        bo.setTotalQuantity(topDeptDTO.getTotalQuantity());
        // 全价票张数
        bo.setTotalFullfaretkt(Optional.ofNullable(topDeptDTO.getTotalFullfaretkt()).orElse(OrpConstants.ZERO));
        // 里程均价
        bo.setAvgTpmsPrice(OrpReportUtils.divideUp(topDeptDTO.getTotalEconmyPrice(),
                Optional.ofNullable(topDeptDTO.getTotalEconmyTpms()).orElse(BigDecimal.ZERO), OrpConstants.TWO));
        // 平均票价
        bo.setAvgPrice(OrpReportUtils.divideUp(topDeptDTO.getTotalEconmyPrice(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getTotalEconmyQuantity()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
        // 平均折扣
        bo.setAvgDiscount(OrpReportUtils.divideUp(topDeptDTO.getTotalDomEconmyDiscount(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getTotalDomEconmyQuantity()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
        // 全价票占比
        bo.setFullfaretktPercent(OrpReportUtils.divideUp(new BigDecimal(Optional.ofNullable(topDeptDTO.getTotalDomEconmyFullfaretkt()).orElse(OrpConstants.ZERO)),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getTotalDomEconmyOrdertkt()).orElse(OrpConstants.ZERO)), OrpConstants.FOUR));
        BigDecimal totalSaveAmount = OrpCommonUtils.addBigDecimal(topDeptDTO.getTotalSaveAmount3c(), topDeptDTO.getTotalSaveAmountPremium(), topDeptDTO.getTotalControlSave());
        BigDecimal totalNetfare = OrpCommonUtils.addBigDecimal(topDeptDTO.getTotalNetfare3c(), topDeptDTO.getTotalNetfarePremium(), topDeptDTO.getTotalControlNetfare());
        // 总节省金额
        bo.setTotalSaveAmount(OrpReportUtils.formatBigDecimal(totalSaveAmount));
        // 节省率
        bo.setSaveRate(OrpReportUtils.divideUp(totalSaveAmount, totalNetfare, OrpConstants.FOUR));
        // 退票费
        bo.setTotalRefundFee(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalRefundFee()));
        // 退票率
        bo.setRefundRate(OrpReportUtils.divideUp(new BigDecimal(Optional.ofNullable(topDeptDTO.getTotalRefundtkt()).orElse(OrpConstants.ZERO)),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getTotalOrdertkt()).orElse(OrpConstants.ZERO)), OrpConstants.FOUR));
        // 改签费
        bo.setTotalRebookFee(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalRebookFee()));
        // 改签率
        bo.setRebookRate(OrpReportUtils.divideUp(new BigDecimal(Optional.ofNullable(topDeptDTO.getTotalRebooktkt()).orElse(OrpConstants.ZERO)),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getTotalOrdertkt()).orElse(OrpConstants.ZERO)), OrpConstants.FOUR));
        // RC订单损失金额
        bo.setTotalOverAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalOverAmount()));
        // RC订单数占比
        bo.setRcPercent(OrpReportUtils.divideUp(topDeptDTO.getTotalRcTimes(), topDeptDTO.getTotalOrderCount(), OrpConstants.FOUR));
        // 三方协议节省率
        bo.setSaveAmount3cRate(OrpReportUtils.divideUp(topDeptDTO.getTotalSaveAmount3c(), topDeptDTO.getTotalNetfare3c(), OrpConstants.FOUR));
        // 尊享节省率
        bo.setSaveAmount2cRate(OrpReportUtils.divideUp(topDeptDTO.getTotalSaveAmountPremium(), topDeptDTO.getTotalNetfarePremium(), OrpConstants.FOUR));
        // 管控节省率
        bo.setControlSaveRate(OrpReportUtils.divideUp(topDeptDTO.getTotalControlSave(), topDeptDTO.getTotalControlNetfare(), OrpConstants.FOUR));
        // 碳排放(转为千克)
        bo.setTotalCarbons(OrpReportUtils.divideUp(topDeptDTO.getTotalCarbons(), new BigDecimal(OrpConstants.THOUSAND), OrpConstants.TWO));
        // 碳排节省率
        bo.setCarbonSaveRate(OrpReportUtils.divideUp(topDeptDTO.getTotalCarbonSave(),
                topDeptDTO.getTotalMedianCarbons(), OrpConstants.FOUR));
        return bo;
    }

    private OnlineReportFltTopDeptConsume convertToFltTopDeptBO(FltDeptDTO topDeptDTO, String aggType, BigDecimal sumAmount) {
        OnlineReportFltTopDeptConsume bo = convertToFltTopDeptBO(topDeptDTO, aggType);
        /*订单数*/
        bo.setTotalAllOrderCount(topDeptDTO.getTotalAllOrderCount());
        /*潜在节省金额（退、改、RC）*/
        bo.setTotalSavePotential(OrpCommonUtils.addBigDecimal(topDeptDTO.getTotalOverAmount(), topDeptDTO.getTotalRefundloss(), topDeptDTO.getTotalRebookloss()));
        /*Top航线*/
        bo.setTopFlightCity(topDeptDTO.getHotFlightCity());
        /*提前预订天数*/
        bo.setAvgPreOrderDate(OrpReportUtils.divideHalfUp(topDeptDTO.getTotalPreOrderDate(), topDeptDTO.getTotalPreOrderDateQuantity(), OrpConstants.ZERO).intValue());
        /*里程碳排*/
        bo.setAvgTpmsCarbons(OrpReportUtils.divideUp(bo.getTotalCarbons(), topDeptDTO.getTotalCarbonsTpms(), OrpConstants.TWO));
        /*消费金额占比*/
        bo.setAmountPercent(OrpReportUtils.divideUp(topDeptDTO.getTotalAmount(), sumAmount, OrpConstants.FOUR));
        bo.setTotalRcTimes(topDeptDTO.getTotalRcTimes());
        return bo;
    }

    protected List getIndustryList(String industryType) {
        if (StringUtils.isNotEmpty(industryType)) {
            return Arrays.asList(industryType);
        }
        return null;
    }
}
