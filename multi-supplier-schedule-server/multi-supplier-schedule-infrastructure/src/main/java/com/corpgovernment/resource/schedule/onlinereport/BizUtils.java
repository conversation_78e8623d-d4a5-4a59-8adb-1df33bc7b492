package com.corpgovernment.resource.schedule.onlinereport;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2022/5/11 11:02
 * @Desc
 */
public class BizUtils {

    public static String dateFormat(String date, QueryReportAggDateDimensionEnum span) {
        String formatDate = date;
        if (span == QueryReportAggDateDimensionEnum.year) {
            formatDate = date.substring(0, 4);
        } else {
            String month = date.substring(5, 7);
            if (span == QueryReportAggDateDimensionEnum.month) {
                formatDate = date.substring(0, 4) + "-" + month;
            } else if (span == QueryReportAggDateDimensionEnum.quarter) {
                formatDate = date.substring(0, 4) + "-Q" + ((Integer.parseInt(month) - 1) / 3 + 1);
            } else if (span == QueryReportAggDateDimensionEnum.half) {
                formatDate = date.substring(0, 4) + "-H" + ((Integer.parseInt(month) - 1) / 6 + 1);
            }
        }
        return formatDate;
    }

    public static Set getDatePoints(String startTime, String endTime, QueryReportAggDateDimensionEnum span) {
        Set result = new HashSet();
        LocalDate start = OrpDateTimeUtils.getLocalDateStartTimeByString(startTime).toLocalDate();
        LocalDate end = OrpDateTimeUtils.getLocalDateEndTimeByString(endTime).toLocalDate();
        LocalDate startLocalDateFirstDate = start.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endLocalDateFirstDate = end.with(TemporalAdjusters.firstDayOfMonth());
        while (startLocalDateFirstDate.compareTo(endLocalDateFirstDate) <= 0) {
            int tempYear = startLocalDateFirstDate.getYear();
            int tempMonth = startLocalDateFirstDate.getMonthValue();
            if (span == QueryReportAggDateDimensionEnum.month) {
                result.add(String.format("%d-%02d", tempYear, tempMonth));
            }
            if (span == QueryReportAggDateDimensionEnum.quarter) {
                result.add(String.format("%d-Q%d", tempYear, (tempMonth - 1) / 3 + 1));
            }
            if (span == QueryReportAggDateDimensionEnum.half) {
                result.add(String.format("%d-H%d", tempYear, (tempMonth - 1) / 6 + 1));
            }
            if (span == QueryReportAggDateDimensionEnum.year) {
                result.add(String.format("%d", tempYear));
            }
            startLocalDateFirstDate = startLocalDateFirstDate.plusMonths(OrpConstants.ONE);
        }
        return result;
    }

    public static List getDatePointsByAsc(String startTime, String endTime, QueryReportAggDateDimensionEnum span) {
        Set points = new HashSet();
        LocalDate start = OrpDateTimeUtils.getLocalDateStartTimeByString(startTime).toLocalDate();
        LocalDate end = OrpDateTimeUtils.getLocalDateEndTimeByString(endTime).toLocalDate();
        LocalDate startLocalDateFirstDate = start.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endLocalDateFirstDate = end.with(TemporalAdjusters.firstDayOfMonth());
        while (startLocalDateFirstDate.compareTo(endLocalDateFirstDate) <= 0) {
            int tempYear = startLocalDateFirstDate.getYear();
            int tempMonth = startLocalDateFirstDate.getMonthValue();
            if (span == QueryReportAggDateDimensionEnum.month) {
                points.add(String.format("%d-%02d", tempYear, tempMonth));
            }
            if (span == QueryReportAggDateDimensionEnum.quarter) {
                points.add(String.format("%d-Q%d", tempYear, (tempMonth - 1) / 3 + 1));
            }
            if (span == QueryReportAggDateDimensionEnum.half) {
                points.add(String.format("%d-H%d", tempYear, (tempMonth - 1) / 6 + 1));
            }
            if (span == QueryReportAggDateDimensionEnum.year) {
                points.add(String.format("%d", tempYear));
            }
            startLocalDateFirstDate = startLocalDateFirstDate.plusMonths(OrpConstants.ONE);
        }
        List<String> list = new ArrayList<>(points);
        Collections.sort(list);
        return list;
    }

    /**
     * 初始化分页参数
     *
     * @param pager
     * @return
     */
    public static Pager initPager(Pager pager) {
        pager = Objects.isNull(pager) ? new Pager(0L, OrpConstants.FIVE, OrpConstants.ZERO, OrpConstants.ZERO) : pager;
        pager.setPageSize((Objects.isNull(pager.getPageSize()) || pager.getPageSize() < OrpConstants.ZERO)
                ? OrpConstants.FIVE : pager.getPageSize());
        pager.setPageIndex((Objects.isNull(pager.getPageIndex()) || pager.getPageIndex() < OrpConstants.ZERO)
                ? OrpConstants.ZERO : pager.getPageIndex());
        return pager;
    }

    /**
     * 初始化分页参数
     *
     * @param pager
     * @return
     */
    public static Pager initPagerStartOne(Pager pager) {
        pager = Objects.isNull(pager) ? new Pager(0L, OrpConstants.FIVE, OrpConstants.ONE, OrpConstants.ZERO) : pager;
        pager.setPageSize((Objects.isNull(pager.getPageSize()) || pager.getPageSize() < OrpConstants.ZERO)
                ? OrpConstants.FIVE : pager.getPageSize());
        pager.setPageIndex((Objects.isNull(pager.getPageIndex()) || pager.getPageIndex() < OrpConstants.ONE)
                ? OrpConstants.ONE : pager.getPageIndex());
        return pager;
    }

    public static BigDecimal field2Value(Object target, Field filed) {
        BigDecimal value = new BigDecimal("0.0");
        try {
            filed.setAccessible(true);
            value = (BigDecimal) filed.get(target);
            if (value == null) {
                value = new BigDecimal("0.0");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    public static BigDecimal round2(BigDecimal d) {
        if (d == null) {
            d = BigDecimal.ZERO;
        }
        return d.setScale(2, BigDecimal.ROUND_HALF_UP);
    }


    public static String getHalfStartTime(String date) {
        if (Integer.parseInt(date.substring(5, 7)) < 7) {
            return date.substring(0, 5) + "01-01";
        }
        return date.substring(0, 5) + "07-01";
    }

    public static String getHalfEndTime(String date) {
        if (Integer.parseInt(date.substring(5, 7)) < 7) {
            return date.substring(0, 5) + "06-30";
        }
        return date.substring(0, 5) + "12-31";
    }

    public static String getQuarterStartTime(String date) {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
        String now = null;
        try {
            Date dateTime = shortSdf.parse(date);
            c.setTime(dateTime);
            int currentMonth = c.get(Calendar.MONTH) + 1;
            if (currentMonth <= 3) {
                c.set(Calendar.MONTH, 0);
            } else if (currentMonth <= 6) {
                c.set(Calendar.MONTH, 3);
            } else if (currentMonth <= 9) {
                c.set(Calendar.MONTH, 6);
            } else if (currentMonth <= 12) {
                c.set(Calendar.MONTH, 9);
            }
            c.set(Calendar.DATE, 1);
            now = shortSdf.format(c.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return now;
    }

    public static String getQuarterEndTime(String date) {
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            cal.setTime(shortSdf.parse(getQuarterStartTime(date)));
            cal.add(Calendar.MONTH, 2);
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return shortSdf.format(cal.getTime());
    }

    public static List<String> getDateRangeFullItem(String startTime, String endTime, QueryReportAggDateDimensionEnum span) {
        List<String> allDate = new ArrayList<>();
        if (span == QueryReportAggDateDimensionEnum.month) {
            List<String> monthsShort = OrpDateTimeUtils.getMonthBetween(startTime.substring(0, 7), endTime.substring(0, 7));
            return monthsShort.stream().map(month -> month + "-01").collect(Collectors.toList());
        }
        if (span == QueryReportAggDateDimensionEnum.quarter) {
            String startQuarter = getQuarterStartTime(startTime);
            String endQuarter = getQuarterStartTime(endTime);
            allDate.add(startQuarter);
            while (startQuarter.compareTo(endQuarter) < 0) {
                startQuarter = OrpDateTimeUtils.startTimeMom(startQuarter, -3);
                allDate.add(startQuarter);
            }
        }
        if (span == QueryReportAggDateDimensionEnum.half) {
            String startHalf = getHalfStartTime(startTime);
            String endHalf = getHalfStartTime(endTime);
            allDate.add(startHalf);
            while (startHalf.compareTo(endHalf) < 0) {
                startHalf = OrpDateTimeUtils.startTimeMom(startHalf, -6);
                allDate.add(startHalf);
            }
        }
        return allDate;
    }
}
