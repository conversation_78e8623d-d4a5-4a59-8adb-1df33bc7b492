package com.corpgovernment.resource.schedule.hotelaudit.gateway;

import com.corpgovernment.api.basic.enums.HotelAreaConfigurationEnum;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.utils.Null;
import com.corpgovernment.converter.model.context.ApplyTripContextModel;
import com.corpgovernment.converter.model.context.HotelContextModel;
import com.corpgovernment.converter.model.passenger.PassengerParamModel;
import com.corpgovernment.converter.model.queryparam.QueryParamModel;
import com.corpgovernment.dto.snapshot.SnapshotQtyCmd;
import com.corpgovernment.dto.snapshot.dto.QuerySnapshotResponseDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.BasicRoomInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.HotelBaseInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.RoomInfoDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.request.GetHotelProductSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelProductSnapshotResponse;
import com.corpgovernment.dto.token.request.HotelOrderTravelStandardGetReqVo;
import com.corpgovernment.dto.token.response.HotelOrderTravelStandardGetRespVo;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardByTokenRequest;
import com.corpgovernment.dto.travelstandard.response.TravelStandardResponse;
import com.corpgovernment.dto.travelstandard.response.TravelStandardRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.BrandRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.CohabitRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.FloatPriceRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.HotelStepRuleVo;
import com.corpgovernment.dto.travelstandard.response.rule.OffPeakSeasonRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.PriceRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.RuleChainVO;
import com.corpgovernment.dto.travelstandard.response.rule.StarRuleVO;
import com.corpgovernment.dto.travelstandard.response.rule.StepStandardVo;
import com.corpgovernment.resource.schedule.domain.hotelaudit.gateway.IHotelAuditGateway;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.*;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.EmployeeTypeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.OrderSourceEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.OverLimitModeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.PayTypeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.ProductTypeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.SnapshotDataTypeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelModeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardRuleEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardSourceEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardStrategyEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardTypeEnum;
import com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.HotelInfoDo;
import com.corpgovernment.resource.schedule.hotelaudit.mysql.mapper.IHotelInfoMapper;
import com.corpgovernment.resource.schedule.hotelaudit.mysql.mapper.OrderMapper;
import com.corpgovernment.resource.schedule.hotelaudit.mysql.mapper.RoomDailyInfoMapper;
import com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.OrderDo;
import com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.RoomDailyInfoDo;
import com.corpgovernment.resource.schedule.hotelaudit.openfeign.IHotelAuditOpenfeignDao;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.OrderTravelStandardResult.buildAvgPriceRuleResult;
import static com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.OrderTravelStandardResult.buildOrderTravelStandardResult;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-23 16:24
 */
@Slf4j
@Repository
public class HotelAuditGateway implements IHotelAuditGateway {
    
    @Resource
    private OrderMapper orderMapper;
    
    @Resource
    private RoomDailyInfoMapper roomDailyInfoMapper;
    
    @Resource
    private IHotelAuditOpenfeignDao hotelAuditOpenfeignDao;
    
    @Resource
    private IHotelInfoMapper hotelInfoMapper;
    
    @Override
    public List<OrderInfo> getHotelOrderInfoList(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            log.info("输入参数异常");
            return null;
        }
        
        // 获取时间范围内的订单数据
        List<OrderDo> orderDoList = orderMapper.listHotelOrderDoByTimeRange(startTime, endTime);
        log.info("orderDoList={}", JsonUtils.toJsonString(orderDoList));
        
        // 获取订单对应的房型价格信息
        Map<String, List<RoomDailyInfoDo>> roomDailyInfoDoListMap = getHotelRoomDailyInfoDoListMap(orderDoList);
        
        // 获取酒店信息
        Map<String, HotelInfoDo> hotelInfoDoMap = getHotelInfoDoMap(orderDoList, ProductTypeEnum.HOTEL);
        
        // 转换成orderInfoList
        return buildOrderInfoList(orderDoList, roomDailyInfoDoListMap, hotelInfoDoMap, ProductTypeEnum.HOTEL);
    }
    
    @Override
    public List<OrderInfo> getHotelIntlOrderInfoList(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            log.info("输入参数异常");
            return null;
        }
        
        // 获取时间范围内的订单数据
        List<OrderDo> orderDoList = orderMapper.listHotelIntlOrderDoByTimeRange(startTime, endTime);
        log.info("orderDoList={}", JsonUtils.toJsonString(orderDoList));
        
        // 获取订单对应的房型价格信息
        Map<String, List<RoomDailyInfoDo>> roomDailyInfoDoListMap = getHotelIntlRoomDailyInfoDoListMap(orderDoList);
        
        // 获取酒店信息
        Map<String, HotelInfoDo> hotelInfoDoMap = getHotelInfoDoMap(orderDoList, ProductTypeEnum.HOTEL_INTL);
        
        // 转换成orderInfoList
        return buildOrderInfoList(orderDoList, roomDailyInfoDoListMap, hotelInfoDoMap, ProductTypeEnum.HOTEL_INTL);
    }
    
    @Override
    public List<OrderInfo> getHotelOrderInfoList(List<String> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return null;
        }
        
        // 获取订单ID对应的房型价格信息
        List<OrderDo> orderDoList = orderMapper.listHotelOrderDoByOrderIdList(orderIdList);
        log.info("orderDoList={}", JsonUtils.toJsonString(orderDoList));
        
        // 获取订单对应的房型价格信息
        Map<String, List<RoomDailyInfoDo>> roomDailyInfoDoListMap = getHotelRoomDailyInfoDoListMap(orderDoList);
        
        // 获取酒店信息
        Map<String, HotelInfoDo> hotelInfoDoMap = getHotelInfoDoMap(orderDoList, ProductTypeEnum.HOTEL);
        
        // 转换成orderInfoList
        return buildOrderInfoList(orderDoList, roomDailyInfoDoListMap, hotelInfoDoMap, ProductTypeEnum.HOTEL);
    }
    
    @Override
    public List<OrderInfo> getHotelIntlOrderInfoList(List<String> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return null;
        }
        
        // 获取订单ID对应的房型价格信息
        List<OrderDo> orderDoList = orderMapper.listHotelIntlOrderDoByOrderIdList(orderIdList);
        log.info("orderDoList={}", JsonUtils.toJsonString(orderDoList));
        
        // 获取订单对应的房型价格信息
        Map<String, List<RoomDailyInfoDo>> roomDailyInfoDoListMap = getHotelIntlRoomDailyInfoDoListMap(orderDoList);
        
        // 获取酒店信息
        Map<String, HotelInfoDo> hotelInfoDoMap = getHotelInfoDoMap(orderDoList, ProductTypeEnum.HOTEL_INTL);
        
        // 转换成orderInfoList
        return buildOrderInfoList(orderDoList, roomDailyInfoDoListMap, hotelInfoDoMap, ProductTypeEnum.HOTEL_INTL);
    }
    
    @Override
    public TravelApplication getTravelApplication(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        SnapshotQtyCmd snapshotQtyCmd = new SnapshotQtyCmd();
        snapshotQtyCmd.setToken(token);
        snapshotQtyCmd.setDataTypeList(Collections.singletonList(SnapshotDataTypeEnum.APPLY_TRIP_CONTEXT.getCode()));
        JSONResult<QuerySnapshotResponseDTO> jsonResult = hotelAuditOpenfeignDao.getSnapshot(snapshotQtyCmd);
        log.info("applyTripContextModel={}", JsonUtils.toJsonString(jsonResult));
        ApplyTripContextModel applyTripContextModel = Optional.ofNullable(jsonResult)
                .map(JSONResult::getData)
                .map(QuerySnapshotResponseDTO::getSnapshotList)
                .flatMap(item -> item.stream().findFirst())
                .map(item -> JsonUtils.parse(item.getSnapshotData(), ApplyTripContextModel.class))
                .orElse(null);
        return buildTravelApplication(applyTripContextModel);
    }
    
    @Override
    public TravelStandard getTravelStandard(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        
        // 获取hotelContext
        HotelContextModel hotelContextModel = getHotelContextModel(token);
        
        // 获取差标
        List<TravelStandardResponse> travelStandardResponseList = getTravelStandardResponseList(token);
        if (CollectionUtils.isEmpty(travelStandardResponseList)) {
            return null;
        }
        
        // 阶梯差标处理
        TravelStandardResponse ladderTravelStandardResponse = travelStandardResponseList.stream()
                .filter(item -> item.getTravelStandardToken() != null
                        && item.getTravelStandardToken().getOwnerType() != null
                        && item.getTravelStandardToken().getOwnerType() == 5)
                .findFirst()
                .orElse(null);
        
        // 房间维度差标
        List<TravelStandardResponse> roomTravelStandardResponseList = travelStandardResponseList.stream()
                .filter(item -> item.getTravelStandardToken() != null
                        && item.getTravelStandardToken().getOwnerType() != null
                        && item.getTravelStandardToken().getOwnerType() == 2)
                .collect(Collectors.toList());
        
        // 标准差标
        TravelStandardResponse standardTravelStandardResponse = travelStandardResponseList.stream()
                .filter(item -> item.getTravelStandardToken() != null
                        && item.getTravelStandardToken().getOwnerType() != null
                        && item.getTravelStandardToken().getOwnerType() == 4)
                .findFirst()
                .orElse(null);
        
        // 人差标
        List<TravelStandardResponse> guestTravelStandardResponseList = travelStandardResponseList.stream()
                .filter(item -> item.getTravelStandardToken() != null
                        && item.getTravelStandardToken().getOwnerType() != null
                        && item.getTravelStandardToken().getOwnerType() == 1)
                .collect(Collectors.toList());
        
        // 同住差标
        CohabitRuleVO cohabitRuleVO = Optional.ofNullable(standardTravelStandardResponse)
                .map(TravelStandardResponse::getRuleChain)
                .map(RuleChainVO::getRuleList).flatMap(item -> item.stream()
                        .filter(a -> a != null && StringUtils.equalsIgnoreCase(a.getName(), TravelStandardRuleEnum.COHABIT_RULE.getCode()))
                        .map(CohabitRuleVO.class::cast)
                        .findFirst())
                .orElse(null);
        
        // 浮动差标
        FloatPriceRuleVO floatPriceRuleVO = Optional.ofNullable(standardTravelStandardResponse)
                .map(TravelStandardResponse::getRuleChain)
                .map(RuleChainVO::getRuleList).flatMap(item -> item.stream()
                        .filter(a -> a != null && StringUtils.equalsIgnoreCase(a.getName(), TravelStandardRuleEnum.FLOAT_PRICE_RULE.getCode()))
                        .map(FloatPriceRuleVO.class::cast)
                        .findFirst())
                .orElse(null);
        
        // 价格差标
        PriceRuleVO priceRuleVO = Optional.ofNullable(standardTravelStandardResponse)
                .map(TravelStandardResponse::getRuleChain)
                .map(RuleChainVO::getRuleList).flatMap(item -> item.stream()
                        .filter(a -> a != null && StringUtils.equalsIgnoreCase(a.getName(), TravelStandardRuleEnum.PRICE_RULE.getCode()))
                        .map(PriceRuleVO.class::cast)
                        .findFirst())
                .orElse(null);
        
        // 淡旺季差标
        OffPeakSeasonRuleVO offPeakSeasonRuleVO = Optional.ofNullable(standardTravelStandardResponse)
                .map(TravelStandardResponse::getRuleChain)
                .map(RuleChainVO::getRuleList).flatMap(item -> item.stream()
                        .filter(a -> a != null && StringUtils.equalsIgnoreCase(a.getName(), TravelStandardRuleEnum.OFF_PEAK_SEASON_RULE.getCode()))
                        .map(OffPeakSeasonRuleVO.class::cast)
                        .findFirst())
                .orElse(null);
        
        
        return TravelStandard.builder()
                .travelStandardTypeEnumList(getTravelStandardTypeEnumList(ladderTravelStandardResponse, cohabitRuleVO, floatPriceRuleVO))
                .travelStandardStrategyEnum(Optional.ofNullable(cohabitRuleVO)
                        .map(CohabitRuleVO::getControllerType)
                        .map(TravelStandardStrategyEnum::getEnum)
                        .orElse(TravelStandardStrategyEnum.ORDER))
                .travelStandardSourceEnum(TravelStandardSourceEnum.TRAVEL_APPLICATION_SYNC.equals(getTravelStandardSourceEnum(standardTravelStandardResponse))
                        ? TravelStandardSourceEnum.TRAVEL_APPLICATION_SYNC
                        : TravelStandardSourceEnum.ADMIN_POST_MANAGEMENT)
                .ladderLevel(getLadderLevel(hotelContextModel))
                .ladderTravelStandardItemList(buildLadderTravelStandardItemList(ladderTravelStandardResponse))
                .standardTravelStandardItem(buildTravelStandardItem(standardTravelStandardResponse))
                .roomTravelStandardItemList(roomTravelStandardResponseList.stream()
                        .map(this::buildTravelStandardItem)
                        .collect(Collectors.toList()))
                .guestTravelStandardItemList(guestTravelStandardResponseList.stream()
                        .map(this::buildTravelStandardItem)
                        .collect(Collectors.toList()))
                .overLimitModeEnumList(getOverLimitModeEnumList(cohabitRuleVO, offPeakSeasonRuleVO, priceRuleVO))
                .floatOverLimitModeEnumList(Optional.ofNullable(floatPriceRuleVO)
                        .map(TravelStandardRuleVO::getRejectTypes)
                        .map(item -> Arrays.stream(item)
                                .map(OverLimitModeEnum::getEnum)
                                .collect(Collectors.toList()))
                        .orElse(null))
                .build();
    }
    
    private List<OverLimitModeEnum> getOverLimitModeEnumList(CohabitRuleVO cohabitRuleVO, OffPeakSeasonRuleVO offPeakSeasonRuleVO, PriceRuleVO priceRuleVO) {
        if (cohabitRuleVO != null && CollectionUtils.isNotEmpty(cohabitRuleVO.getRejectTypes())) {
            return Arrays.stream(cohabitRuleVO.getRejectTypes())
                    .map(OverLimitModeEnum::getEnum)
                    .collect(Collectors.toList());
        }
        
        if (offPeakSeasonRuleVO != null && CollectionUtils.isNotEmpty(offPeakSeasonRuleVO.getRejectTypes())) {
            return Arrays.stream(offPeakSeasonRuleVO.getRejectTypes())
                    .map(OverLimitModeEnum::getEnum)
                    .collect(Collectors.toList());
        }
        
        if (priceRuleVO != null && CollectionUtils.isNotEmpty(priceRuleVO.getRejectTypes())) {
            return Arrays.stream(priceRuleVO.getRejectTypes())
                    .map(OverLimitModeEnum::getEnum)
                    .collect(Collectors.toList());
        }
        
        return null;
    }
    
    private List<TravelStandardTypeEnum> getTravelStandardTypeEnumList(TravelStandardResponse ladderTravelStandardResponse, CohabitRuleVO cohabitRuleVO, FloatPriceRuleVO floatPriceRuleVO) {
        if (ladderTravelStandardResponse != null) {
            return Collections.singletonList(TravelStandardTypeEnum.LADDER_TRAVEL_STANDARD);
        }
        
        List<TravelStandardTypeEnum> travelStandardTypeEnumList = new ArrayList<>();
        travelStandardTypeEnumList.add(TravelStandardTypeEnum.STANDARD_TRAVEL_STANDARD);
        if (cohabitRuleVO != null) {
            travelStandardTypeEnumList.add(TravelStandardTypeEnum.COHABIT_TRAVEL_STANDARD);
        }
        if (floatPriceRuleVO != null) {
            travelStandardTypeEnumList.add(TravelStandardTypeEnum.FLOAT_TRAVEL_STANDARD);
        }
        return travelStandardTypeEnumList;
    }
    
    @Override
    public List<GuestInfo> getGuestInfoList(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        
        PassengerParamModel passengerParamModel = getPassengerParamModel(token);
        if (passengerParamModel == null || CollectionUtils.isEmpty(passengerParamModel.getPassengerInfoSnapshotMap())) {
            return null;
        }
        
        List<GuestInfo> guestInfoList = new ArrayList<>();
        passengerParamModel.getPassengerInfoSnapshotMap().forEach((key, value) -> {
            if (value == null) {
                return;
            }
            
            value.forEach(item -> {
                GuestInfo guestInfo = GuestInfo.builder()
                        .roomIndex(key)
                        .orgId(item.getOrgId())
                        .uid(item.getUid())
                        .employeeTypeEnum(EmployeeTypeEnum.getEnum(Optional.ofNullable(item.getEmployeeType())
                                .map(Object::toString)
                                .orElse(null)))
                        .build();
                guestInfoList.add(guestInfo);
            });
        });
        
        return guestInfoList;
    }
    
    @Override
    public GuestInfo getPolicyExecutor(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        
        QueryParamModel queryParamModel = getQueryParamModel(token);
        if (queryParamModel == null) {
            return null;
        }
        
        return GuestInfo.builder()
                .orgId(queryParamModel.getPolicyOrgId())
                .uid(queryParamModel.getPolicyUid())
                .employeeTypeEnum(EmployeeTypeEnum.getEnum(Optional.ofNullable(queryParamModel.getPolicyEmployeeType())
                        .map(Object::toString)
                        .orElse(null)))
                .build();
    }
    
    @Override
    public HotelRoomInfo getHotelRoomInfo(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        
        GetHotelProductSnapshotResponse hotelProductSnapshotResponse = getHotelProductSnapshotResponse(token);
        HotelBaseInfoDTO hotelBaseInfoDTO = Optional.ofNullable(hotelProductSnapshotResponse)
                .map(GetHotelProductSnapshotResponse::getHotelInfo)
                .orElse(null);
        BasicRoomInfoDTO basicRoomInfoDTO = Optional.ofNullable(hotelProductSnapshotResponse)
                .map(GetHotelProductSnapshotResponse::getBasicRoomInfo)
                .flatMap(item -> item.stream().findFirst())
                .orElse(null);
        RoomInfoDTO roomInfoDTO = Optional.ofNullable(hotelProductSnapshotResponse)
                .map(GetHotelProductSnapshotResponse::getBasicRoomInfo)
                .flatMap(item -> item.stream().findFirst())
                .map(BasicRoomInfoDTO::getRoomCardList)
                .flatMap(item -> item.stream().findFirst())
                .orElse(null);
        
        HotelRoomInfo.HotelRoomInfoBuilder hotelRoomInfoBuilder = HotelRoomInfo.builder();
        if (hotelBaseInfoDTO != null) {
            hotelRoomInfoBuilder
                    .cityName(hotelBaseInfoDTO.getCityName())
                    .hotelName(hotelBaseInfoDTO.getName());
        }
        if (basicRoomInfoDTO != null) {
            hotelRoomInfoBuilder
                    .basicRoomName(basicRoomInfoDTO.getName());
        }
        if (roomInfoDTO != null) {
            hotelRoomInfoBuilder
                    .supplierCode(roomInfoDTO.getSupplierCode())
                    .supplierName(roomInfoDTO.getSupplierName())
                    .cityId(roomInfoDTO.getCityId())
                    .hotelId(roomInfoDTO.getHotelId())
                    .basicRoomId(roomInfoDTO.getBasicRoomId())
                    .roomId(roomInfoDTO.getRoomId())
                    .productId(roomInfoDTO.getProductId());
        }
        
        
        return hotelRoomInfoBuilder.build();
    }

    @Override
    public OrderTravelStandardResult getOrderTravelStandardResult(String token) {
        HotelOrderTravelStandardGetRespVo response = this.getHotelOrderTravelStandardResponse(token);
        if (response == null){
            return null;
        }
        OrderTravelStandardResult.AvgPriceRuleResult maxPriceRuleResult = OrderTravelStandardResult.buildAvgPriceRuleResult(response.getMaxAvgPriceRule());
        OrderTravelStandardResult.StarRuleResult starRuleResult = OrderTravelStandardResult.buildStarRuleResult(response.getStarRule());
        OrderTravelStandardResult.BrandRuleResult brandRuleResult = OrderTravelStandardResult.buildBrandRuleResult(response.getBrandRule());
        return OrderTravelStandardResult.buildOrderTravelStandardResult(maxPriceRuleResult, starRuleResult, brandRuleResult);
    }

    private HotelOrderTravelStandardGetRespVo getHotelOrderTravelStandardResponse(String token){
        if (StringUtils.isBlank(token)) {
            return null;
        }
        HotelOrderTravelStandardGetReqVo hotelOrderTravelStandardGetReqVo = new HotelOrderTravelStandardGetReqVo();
        hotelOrderTravelStandardGetReqVo.setToken(token);
        JSONResult<HotelOrderTravelStandardGetRespVo> jsonResult = hotelAuditOpenfeignDao.getHotelOrderTravelStandard(hotelOrderTravelStandardGetReqVo);
        log.info("getOrderTravelStandardResult = {}", JsonUtils.toJsonString(jsonResult));
        return Optional.ofNullable(jsonResult).map(JSONResult::getData).orElse(null);
    }


    private GetHotelProductSnapshotResponse getHotelProductSnapshotResponse(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        
        GetHotelProductSnapshotRequest getHotelProductSnapshotRequest = new GetHotelProductSnapshotRequest();
        getHotelProductSnapshotRequest.setToken(token);
        JSONResult<GetHotelProductSnapshotResponse> jsonResult = hotelAuditOpenfeignDao.getHotelProductSnapshot(getHotelProductSnapshotRequest);
        log.info("hotelProductSnapshot={}", JsonUtils.toJsonString(jsonResult));
        return Optional.ofNullable(jsonResult)
                .map(JSONResult::getData)
                .orElse(null);
    }
    
    private PassengerParamModel getPassengerParamModel(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        SnapshotQtyCmd snapshotQtyCmd = new SnapshotQtyCmd();
        snapshotQtyCmd.setToken(token);
        snapshotQtyCmd.setDataTypeList(Collections.singletonList(SnapshotDataTypeEnum.PASSENGER_DATA.getCode()));
        JSONResult<QuerySnapshotResponseDTO> jsonResult = hotelAuditOpenfeignDao.getSnapshot(snapshotQtyCmd);
        log.info("passengerParamModel={}", JsonUtils.toJsonString(jsonResult));
        return Optional.ofNullable(jsonResult)
                .map(JSONResult::getData)
                .map(QuerySnapshotResponseDTO::getSnapshotList)
                .flatMap(item -> item.stream().findFirst())
                .map(item -> JsonUtils.parse(item.getSnapshotData(), PassengerParamModel.class))
                .orElse(null);
    }
    
    private QueryParamModel getQueryParamModel(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        SnapshotQtyCmd snapshotQtyCmd = new SnapshotQtyCmd();
        snapshotQtyCmd.setToken(token);
        snapshotQtyCmd.setDataTypeList(Collections.singletonList(SnapshotDataTypeEnum.QUERY_PARAM.getCode()));
        JSONResult<QuerySnapshotResponseDTO> jsonResult = hotelAuditOpenfeignDao.getSnapshot(snapshotQtyCmd);
        log.info("queryParamModel={}", JsonUtils.toJsonString(jsonResult));
        return Optional.ofNullable(jsonResult)
                .map(JSONResult::getData)
                .map(QuerySnapshotResponseDTO::getSnapshotList)
                .flatMap(item -> item.stream().findFirst())
                .map(item -> JsonUtils.parse(item.getSnapshotData(), QueryParamModel.class))
                .orElse(null);
    }
    
    private List<TravelStandardResponse> getTravelStandardResponseList(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        GetTravelStandardByTokenRequest getTravelStandardByTokenRequest = new GetTravelStandardByTokenRequest();
        getTravelStandardByTokenRequest.setTokenList(Collections.singletonList(token));
        getTravelStandardByTokenRequest.setReturnSubToken(true);
        List<TravelStandardResponse> travelStandardResponseList = hotelAuditOpenfeignDao.getTravelStandardByToken(getTravelStandardByTokenRequest);
        log.info("travelStandardResponseList={}", JsonUtils.toJsonString(travelStandardResponseList));
        return travelStandardResponseList;
    }
    
    private HotelContextModel getHotelContextModel(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        SnapshotQtyCmd snapshotQtyCmd = new SnapshotQtyCmd();
        snapshotQtyCmd.setToken(token);
        snapshotQtyCmd.setDataTypeList(Collections.singletonList(SnapshotDataTypeEnum.HOTEL_CONTEXT.getCode()));
        JSONResult<QuerySnapshotResponseDTO> jsonResult = hotelAuditOpenfeignDao.getSnapshot(snapshotQtyCmd);
        log.info("hotelContextModel={}", JsonUtils.toJsonString(jsonResult));
        return Optional.ofNullable(jsonResult)
                .map(JSONResult::getData)
                .map(QuerySnapshotResponseDTO::getSnapshotList)
                .flatMap(item -> item.stream().findFirst())
                .map(item -> JsonUtils.parse(item.getSnapshotData(), HotelContextModel.class))
                .orElse(null);
    }
    
    private TravelStandardSourceEnum getTravelStandardSourceEnum(TravelStandardResponse travelStandardResponse) {
        return Optional.ofNullable(travelStandardResponse)
                .map(TravelStandardResponse::getRuleChain)
                .map(RuleChainVO::getRuleList).flatMap(item -> item.stream()
                        .filter(a -> a != null && StringUtils.equalsIgnoreCase(a.getName(), TravelStandardRuleEnum.PRICE_RULE.getCode()))
                        .map(PriceRuleVO.class::cast)
                        .findFirst())
                .map(PriceRuleVO::getSource)
                .map(TravelStandardSourceEnum::getEnum)
                .orElse(null);
    }
    
    private TravelStandardItem buildTravelStandardItem(TravelStandardResponse travelStandardResponse) {
        List<TravelStandardRuleVO> travelStandardRuleVOList = Optional.ofNullable(travelStandardResponse)
                .map(TravelStandardResponse::getRuleChain)
                .map(this::getPriorityRuleList)
                .orElse(null);
        
        // 差标不限
        if (CollectionUtils.isEmpty(travelStandardRuleVOList)) {
            return null;
        }
        
        CohabitRuleVO cohabitRuleVO = travelStandardRuleVOList.stream()
                .filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), TravelStandardRuleEnum.COHABIT_RULE.getCode()))
                .map(CohabitRuleVO.class::cast)
                .findFirst()
                .orElse(null);
        OffPeakSeasonRuleVO offPeakSeasonRuleVO = travelStandardRuleVOList.stream()
                .filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), TravelStandardRuleEnum.OFF_PEAK_SEASON_RULE.getCode()))
                .map(OffPeakSeasonRuleVO.class::cast)
                .findFirst()
                .orElse(null);
        PriceRuleVO priceRuleVO = travelStandardRuleVOList.stream()
                .filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), TravelStandardRuleEnum.PRICE_RULE.getCode()))
                .map(PriceRuleVO.class::cast)
                .findFirst()
                .orElse(null);
        FloatPriceRuleVO floatPriceRuleVO = travelStandardRuleVOList.stream().filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), TravelStandardRuleEnum.FLOAT_PRICE_RULE.getCode()))
                .map(FloatPriceRuleVO.class::cast)
                .findFirst()
                .orElse(null);
        StarRuleVO starRuleVO = travelStandardRuleVOList.stream()
                .filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), TravelStandardRuleEnum.STAR_RULE.getCode()))
                .map(StarRuleVO.class::cast)
                .findFirst()
                .orElse(null);
        
        return TravelStandardItem.builder()
                .index(getIndex(travelStandardResponse))
                .maxPrice(getMaxPrice(cohabitRuleVO, offPeakSeasonRuleVO, priceRuleVO))
                .minPrice(getMinPrice(cohabitRuleVO, offPeakSeasonRuleVO, priceRuleVO))
                .floatMaxPrice(getFloatMaxPrice(cohabitRuleVO, floatPriceRuleVO))
                .starList(getStarList(Optional.ofNullable(starRuleVO)
                        .map(StarRuleVO::getStarList)
                        .orElse(null)))
                .build();
    }

    private BigDecimal getFloatMaxPrice(CohabitRuleVO cohabitRuleVO, FloatPriceRuleVO floatPriceRuleVO) {
        if (cohabitRuleVO != null) {
            return cohabitRuleVO.getMaxFloatPrice();
        }
        if (floatPriceRuleVO != null) {
            return floatPriceRuleVO.getMaxPrice();
        }
        return null;
    }
    
    private List<Integer> getStarList(List<Integer> starList) {
        if (CollectionUtils.isEmpty(starList)) {
            return null;
        }
        
        // 如果只有0就是不限
        if (starList.size() == 1 && starList.get(0) != null && starList.get(0) == 0) {
            return null;
        }
        
        Set<Integer> starSet = new HashSet<>(starList);
        if (starSet.contains(2)) {
            starSet.add(1);
        }
        return new ArrayList<>(starSet);
    }
    
    private BigDecimal getMinPrice(CohabitRuleVO cohabitRuleVO, OffPeakSeasonRuleVO offPeakSeasonRuleVO, PriceRuleVO priceRuleVO) {
        if (cohabitRuleVO != null) {
            return cohabitRuleVO.getMinPrice();
        }
        if (offPeakSeasonRuleVO != null) {
            return offPeakSeasonRuleVO.getMinPrice();
        }
        if (priceRuleVO != null) {
            return priceRuleVO.getMinPrice();
        }
        return null;
    }
    
    private BigDecimal getMaxPrice(CohabitRuleVO cohabitRuleVO, OffPeakSeasonRuleVO offPeakSeasonRuleVO, PriceRuleVO priceRuleVO) {
        if (cohabitRuleVO != null) {
            return cohabitRuleVO.getMaxPrice();
        }
        if (offPeakSeasonRuleVO != null) {
            return offPeakSeasonRuleVO.getMaxPrice();
        }
        if (priceRuleVO != null) {
            return priceRuleVO.getMaxPrice();
        }
        return null;
    }
    
    private Integer getIndex(TravelStandardResponse travelStandardResponse) {
        try {
            if (travelStandardResponse == null || travelStandardResponse.getTravelStandardToken() == null || !Objects.equals(travelStandardResponse.getTravelStandardToken().getOwnerType(), 2)) {
                return null;
            }
            return Integer.valueOf(travelStandardResponse.getTravelStandardToken().getOwnerId());
        } catch (Exception exception) {
            log.info("无索引");
            return null;
        }
    }
    
    private List<TravelStandardRuleVO> getPriorityRuleList(RuleChainVO ruleChain) {
        if (ruleChain == null || CollectionUtils.isEmpty(ruleChain.getRuleList())) {
            return null;
        }
        
        // 按照城市类型分组
        Map<String, List<TravelStandardRuleVO>> travelStandardRuleVoListMap = ruleChain.getRuleList().stream()
                .filter(Objects::nonNull).collect(Collectors.groupingBy(this::getCityType));
        log.info("按照城市类型分组 ruleChain={} travelStandardRuleVoListMap={}", JsonUtils.toJsonString(ruleChain), JsonUtils.toJsonString(travelStandardRuleVoListMap));
        
        // 城市类型数量
        long cityTypeCount = travelStandardRuleVoListMap.size();
        
        // 只有一个城市类型
        if (cityTypeCount == 1) {
            return travelStandardRuleVoListMap.values().stream().findFirst().orElse(null);
        }
        
        // 多个城市类型
        if (cityTypeCount > 1) {
            return travelStandardRuleVoListMap.get(String.valueOf(HotelAreaConfigurationEnum.HOTEL_CONFIGURATION_CITY.getAreaType()));
        }
        
        return null;
    }
    
    private String getCityType(TravelStandardRuleVO travelStandardRuleVO) {
        if (travelStandardRuleVO == null) {
            return "";
        }
        String name = travelStandardRuleVO.getName();
        if (StringUtils.equalsIgnoreCase(name, "CohabitRule")) {
            CohabitRuleVO tmp = (CohabitRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "PriceRule")) {
            PriceRuleVO tmp = (PriceRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "OffPeakSeasonRule")) {
            OffPeakSeasonRuleVO tmp = (OffPeakSeasonRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "StarRule")) {
            StarRuleVO tmp = (StarRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "FloatPriceRule")) {
            FloatPriceRuleVO tmp = (FloatPriceRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        } else if (StringUtils.equalsIgnoreCase(name, "BrandRule")) {
            BrandRuleVO tmp = (BrandRuleVO) travelStandardRuleVO;
            return Null.or(tmp.getCityType(), "");
        }
        return "";
    }
    
    private List<TravelStandardItem> buildLadderTravelStandardItemList(TravelStandardResponse travelStandardResponse) {
        // 规则列表
        List<TravelStandardRuleVO> travelStandardRuleVOList = Optional.ofNullable(travelStandardResponse)
                .map(TravelStandardResponse::getRuleChain)
                .map(RuleChainVO::getRuleList)
                .orElse(new ArrayList<>(0));
        
        // 阶梯差标
        List<StepStandardVo> stepStandardVoList = travelStandardRuleVOList.stream()
                .filter(item -> item != null && StringUtils.equalsIgnoreCase(item.getName(), TravelStandardRuleEnum.HOTEL_STEP_RULE.getCode()))
                .map(HotelStepRuleVo.class::cast)
                .findFirst()
                .map(HotelStepRuleVo::getStepStandardList)
                .map(item -> item.stream().filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(null);
        
        // 差标不限
        if (CollectionUtils.isEmpty(travelStandardRuleVOList) || CollectionUtils.isEmpty(stepStandardVoList)) {
            return null;
        }
        
        // 转换
        List<TravelStandardItem> travelStandardItemList = new ArrayList<>();
        for (StepStandardVo stepStandardVo : stepStandardVoList) {
            if (stepStandardVo == null) {
                continue;
            }
            
            travelStandardItemList.add(TravelStandardItem.builder()
                    .index(stepStandardVo.getSort())
                    .maxPrice(stepStandardVo.getUpperLimit())
                    .minPrice(stepStandardVo.getLowerLimit())
                    .starList(getStarList(stepStandardVo.getStarList()))
                    .build());
        }
        
        return travelStandardItemList;
    }
    
    private TravelApplication buildTravelApplication(ApplyTripContextModel applyTripContextModel) {
        if (applyTripContextModel == null) {
            return null;
        }
        
        return TravelApplication.builder()
                .travelApplicationId(applyTripContextModel.getApprovalNo())
                .travelId(applyTripContextModel.getTravelNo())
                .bookLat(applyTripContextModel.getLatitude())
                .bookLon(applyTripContextModel.getLongitude())
                .bookAddress(applyTripContextModel.getAddress())
                .build();
    }
    
    private Integer getLadderLevel(HotelContextModel hotelContextModel) {
        try {
            return Integer.valueOf(hotelContextModel.getTravelStandardMark());
        } catch (Exception e) {
            log.info("无阶梯差标等级");
            return null;
        }
    }
    
    private Map<String, List<RoomDailyInfoDo>> getHotelIntlRoomDailyInfoDoListMap(List<OrderDo> orderDoList) {
        if (CollectionUtils.isEmpty(orderDoList)) {
            return null;
        }
        
        List<String> orderIdList = orderDoList.stream()
                .filter(item -> item != null && StringUtils.isNotBlank(item.getOrderId()))
                .map(OrderDo::getOrderId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return null;
        }
        
        List<RoomDailyInfoDo> roomDailyInfoDoList = roomDailyInfoMapper.listHotelIntlRoomDailyInfo(orderIdList);
        log.info("roomDailyInfoDoList={}", JsonUtils.toJsonString(roomDailyInfoDoList));
        
        return Optional.ofNullable(roomDailyInfoDoList)
                .map(item -> item.stream()
                        .filter(a -> a != null && a.getOrderId() != null)
                        .collect(Collectors.groupingBy(RoomDailyInfoDo::getOrderId)))
                .orElse(null);
    }
    
    private Map<String, HotelInfoDo> getHotelInfoDoMap(List<OrderDo> orderDoList, ProductTypeEnum productTypeEnum) {
        if (CollectionUtils.isEmpty(orderDoList)) {
            return null;
        }
        
        List<String> orderIdList = orderDoList.stream()
                .filter(item -> item != null && StringUtils.isNotBlank(item.getOrderId()))
                .map(OrderDo::getOrderId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return null;
        }
        
        List<HotelInfoDo> hotelInfoDoList = null;
        if (productTypeEnum == ProductTypeEnum.HOTEL) {
            hotelInfoDoList = hotelInfoMapper.selectHotelInfoByOrderIdList(orderIdList);
        } else if (productTypeEnum == ProductTypeEnum.HOTEL_INTL) {
            hotelInfoDoList = hotelInfoMapper.selectHotelIntlInfoByOrderIdList(orderIdList);
        }
        log.info("hotelInfoDoList={}", JsonUtils.toJsonString(hotelInfoDoList));
        
        return Optional.ofNullable(hotelInfoDoList)
                .map(item -> item.stream()
                        .filter(a -> a != null && a.getOrderId() != null)
                        .collect(Collectors.toMap(HotelInfoDo::getOrderId, a -> a, (a, b) -> a)))
                .orElse(null);
    }
    
    private Map<String, List<RoomDailyInfoDo>> getHotelRoomDailyInfoDoListMap(List<OrderDo> orderDoList) {
        if (CollectionUtils.isEmpty(orderDoList)) {
            return null;
        }
        
        List<String> orderIdList = orderDoList.stream()
                .filter(item -> item != null && StringUtils.isNotBlank(item.getOrderId()))
                .map(OrderDo::getOrderId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return null;
        }
        
        List<RoomDailyInfoDo> roomDailyInfoDoList = roomDailyInfoMapper.listHotelRoomDailyInfo(orderIdList);
        log.info("roomDailyInfoDoList={}", JsonUtils.toJsonString(roomDailyInfoDoList));
        
        return Optional.ofNullable(roomDailyInfoDoList)
                .map(item -> item.stream()
                        .filter(a -> a != null && a.getOrderId() != null)
                        .collect(Collectors.groupingBy(RoomDailyInfoDo::getOrderId)))
                .orElse(null);
    }
    
    /**
     * 构建OrderInfoList
     */
    private List<OrderInfo> buildOrderInfoList(List<OrderDo> orderDoList, Map<String, List<RoomDailyInfoDo>> roomDailyInfoDoListMap, Map<String, HotelInfoDo> hotelInfoDoMap, ProductTypeEnum productTypeEnum) {
        if (CollectionUtils.isEmpty(orderDoList)) {
            return null;
        }
        
        return orderDoList.stream()
                .filter(Objects::nonNull)
                .map(item -> buildOrderInfo(item, roomDailyInfoDoListMap, hotelInfoDoMap, productTypeEnum))
                .collect(Collectors.toList());
    }
    
    private OrderInfo buildOrderInfo(OrderDo orderDo, Map<String, List<RoomDailyInfoDo>> roomDailyInfoDoListMap, Map<String, HotelInfoDo> hotelInfoDoMap, ProductTypeEnum productTypeEnum) {
        if (orderDo == null || StringUtils.isBlank(orderDo.getOrderId())) {
            return null;
        }
        
        HotelInfoDo hotelInfoDo = Optional.ofNullable(hotelInfoDoMap)
                .map(item -> item.get(orderDo.getOrderId()))
                .orElse(null);
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        BigDecimal roomNightAvgPrice = getRoomNightAvgPriceIfNotExist(orderDo.getRoomNightAvgPrice(), roomDailyInfoDoListMap.get(orderDo.getOrderId()));

        return OrderInfo.builder()
                .productTypeEnum(productTypeEnum)
                .orderId(orderDo.getOrderId())
                .orderTime(Optional.ofNullable(orderDo.getOrderDate())
                        .map(sdf::format)
                        .orElse(null))
                .orderStatus(orderDo.getOrderStatus())
                .travelModeEnum(TravelModeEnum.getEnum(orderDo.getCorpPayType()))
                .token(orderDo.getTravelStandardToken())
                .checkInDate(Optional.ofNullable(orderDo.getCheckInTime())
                        .map(sdf::format)
                        .orElse(null))
                .checkOutDate(Optional.ofNullable(orderDo.getCheckOutTime())
                        .map(sdf::format)
                        .orElse(null))
                .overLimit((orderDo.getRcType() != null && orderDo.getRcType() == 0) || orderDo.getPPayAmount() != null)
                .publicPayPrice(orderDo.getAPayAmount() != null ? orderDo.getAPayAmount() : orderDo.getAmount())
                .personPayPrice(orderDo.getPPayAmount())
                .totalOrderPrice(orderDo.getAmount())
                .roomNightAvgPrice(roomNightAvgPrice)
                .dailyRoomPriceList(buildDailyRoomPriceList(roomDailyInfoDoListMap.get(orderDo.getOrderId())))
                .orderSourceEnum(OrderSourceEnum.getEnum(orderDo.getSource()))
                .payTypeEnum(PayTypeEnum.getEnum(orderDo.getPaytype()))
                .star(hotelInfoDo == null ? null : hotelInfoDo.getStar())
                .isStarLicence(hotelInfoDo == null ? null : hotelInfoDo.getIsStarLicence())
                .build();
    }

    private BigDecimal getRoomNightAvgPriceIfNotExist(BigDecimal roomNightAvgPrice, List<RoomDailyInfoDo> roomDailyInfoDoList){
        if (roomNightAvgPrice != null || CollectionUtils.isEmpty(roomDailyInfoDoList)){
            return roomNightAvgPrice;
        }
        BigDecimal totalPrice = roomDailyInfoDoList.stream().map(RoomDailyInfoDo::getRoomPrice).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(null);
        if (totalPrice == null){
            return null;
        }
        BigDecimal roomDailyLength = new BigDecimal(roomDailyInfoDoList.size());
        return totalPrice.divide(roomDailyLength, 5, RoundingMode.HALF_UP);
    }
    
    private List<DailyRoomPrice> buildDailyRoomPriceList(List<RoomDailyInfoDo> roomDailyInfoDoList) {
        if (CollectionUtils.isEmpty(roomDailyInfoDoList)) {
            return null;
        }
        
        return roomDailyInfoDoList.stream()
                .filter(Objects::nonNull)
                .map(this::buildDailyRoomPrice)
                .collect(Collectors.toList());
    }
    
    private DailyRoomPrice buildDailyRoomPrice(RoomDailyInfoDo roomDailyInfoDo) {
        if (roomDailyInfoDo == null) {
            return null;
        }
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        return DailyRoomPrice.builder()
                .effectDate(Optional.ofNullable(roomDailyInfoDo.getEffectDate())
                        .map(sdf::format)
                        .orElse(null))
                .roomNightPrice(roomDailyInfoDo.getRoomPrice())
                .build();
    }
    
}
