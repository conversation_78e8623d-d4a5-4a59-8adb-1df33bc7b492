package com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 11:02
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class OnlineReportPotentialSaveHotelDTO {

    @Column(name = "hotelRcPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelRcPotentialSave;

    @Column(name = "hotelCancelPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelCancelPotentialSave;

    @Column(name = "hotelRcPotentialTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelRcPotentialTimes;

    @Column(name = "hotelRcPotentialTimesRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelRcPotentialTimesRate;

    @Column(name = "hotelCancelPotentialTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelCancelPotentialTimes;

    @Column(name = "hotelCancelPotentialTimesRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelCancelPotentialTimesRate;

}
