package com.corpgovernment.resource.schedule.onlinereport.dao;


import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.enums.RoleDataScopeEnum;
import com.corpgovernment.common.util.CommonPermissionUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.SearchDeptAndCostcneterEntity;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.SearchDeptAndCostcneterDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 *
 * @date 2021/11/19 16:19
 *
 * @Desc
 */
@Slf4j
public class BaseConditionPrebuilder {
    /**
     * 构建基本查询条件
     *
     * @param baseQueryCondition
     * @param paramList
     * @return
     */
    public static String buildPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTime(paramList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, paramList));
        return sqlBuffer.toString();
    }

    public static String buildPreSqlWithCol(BaseQueryConditionDTO baseQueryCondition, List<Object> paramList, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), col));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, paramList));
        return sqlBuffer.toString();
    }

    /**
     * 构建商旅基本查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildCorpPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTime(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        return sqlBuffer.toString();
    }

    /**
     * 构建商旅基本查询条件
     *
     * @param startTime
     * @param endTime
     * @param parmList
     * @return
     */
    public static String buildCorpPreSql(String startTime, String endTime, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTime(parmList, startTime, endTime));
        return sqlBuffer.toString();
    }

    public static String buildCorpPreSqlWithCol(BaseQueryConditionDTO baseQueryCondition, List<Object> paramList, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), col));
        return sqlBuffer.toString();
    }

    public static String buildCorpPreSqlWithCol(String startTime, String endTime, List<Object> paramList, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, startTime, endTime, col));
        return sqlBuffer.toString();
    }

    public static String buildCorpPreSqlWithCol(BaseQueryConditionDTO baseQueryCondition, List<Object> paramList, String partition, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(paramList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), col));
        return sqlBuffer.toString();
    }

    public static String buildCorpPreSqlWithCol(String starTime, String endTime, List<Object> paramList, String partition, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(paramList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, starTime, endTime, col));
        return sqlBuffer.toString();
    }

    public static String buildCorpPreSqlNoTime(List<Object> paramList, String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(paramList, partition));
        return sqlBuffer.toString();
    }

    /**
     * 构建商旅基本查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildCorpPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                         String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTime(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        return sqlBuffer.toString();
    }

    /**
     * 构建商旅基本查询条件
     *
     * @param startTime
     * @param endTime
     * @param parmList
     * @param partition
     * @return
     */
    public static String buildCorpPreSql(String startTime, String endTime, List<Object> parmList,
                                         String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTime(parmList, startTime, endTime));
        return sqlBuffer.toString();
    }

    /**
     * 构建行业基本查询条件
     */
    public static String buildIndustryPreSql(BaseQueryConditionDTO baseQueryCondition, List<String> industryList,
                                             List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTime(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        sqlBuffer.append(buildIndustryPreSql(industryList, parmList));
        return sqlBuffer.toString();
    }

    /**
     * 构建行业基本查询条件
     */
    public static String buildIndustryPreSql(String startTime, String endTime, List<String> industryList, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTime(parmList, startTime, endTime));
        sqlBuffer.append(buildIndustryPreSql(industryList, parmList));
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSqlWithCol(BaseQueryConditionDTO baseQueryCondition, List<String> industryList,
                                                    List<Object> paramList, String queryCol) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), queryCol));
        sqlBuffer.append(buildIndustryPreSql(industryList, paramList));
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSqlWithCol(String startTime, String endTime, List<String> industryList,
                                                    List<Object> paramList, String queryCol) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, startTime, endTime, queryCol));
        sqlBuffer.append(buildIndustryPreSql(industryList, paramList));
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSqlWithCol(BaseQueryConditionDTO baseQueryCondition, List<String> industryList, String partition,
                                                    List<Object> paramList, String queryCol) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(paramList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), queryCol));
        sqlBuffer.append(buildIndustryPreSql(industryList, paramList));
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSqlWithCol(String startTime, String endTime, List<String> industryList, String partition,
                                                    List<Object> paramList, String queryCol) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(paramList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, startTime, endTime, queryCol));
        sqlBuffer.append(buildIndustryPreSql(industryList, paramList));
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSqlWithColOverride(String startTime, String endTime, List<String> industryList, String partition,
                                                            List<Object> paramList, String queryCol) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(paramList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, startTime, endTime, queryCol));
        sqlBuffer.append(buildIndustryPreSqlOverride(industryList, paramList));
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSqlNoTime(List<String> industryList, String partition, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(paramList, partition));
        sqlBuffer.append(buildIndustryPreSql(industryList, paramList));
        return sqlBuffer.toString();
    }

    /**
     * 构建行业基本查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildIndustryPreSql(BaseQueryConditionDTO baseQueryCondition, List<String> industryList,
                                             List<Object> parmList, String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer
                .append(buildPreSqlWithTime(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        sqlBuffer.append(buildIndustryPreSql(industryList, parmList));
        return sqlBuffer.toString();
    }

    /**
     * 构建行业基本查询条件
     *
     * @param startTime
     * @param endTime
     * @param industryList
     * @param parmList
     * @param partition
     * @return
     */
    public static String buildIndustryPreSql(String startTime, String endTime, List<String> industryList,
                                             List<Object> parmList, String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTime(parmList, startTime, endTime));
        sqlBuffer.append(buildIndustryPreSql(industryList, parmList));
        return sqlBuffer.toString();
    }

    /**
     * 构建基本查询条件(不使用baseQueryCondition里面的startTime、endTime)
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList, String startTime,
                                     String endTime, String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTime(parmList, startTime, endTime));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));
        sqlBuffer.append(buildRolePermission(parmList));
        return sqlBuffer.toString();
    }


    /**
     * 构建机票基本查询条件(不使用baseQueryCondition里面的startTime、endTime)
     *
     * @param parmList
     * @return
     */
    public static String buildFlightSaveAmount(List<Object> parmList, String startTime, String endTime) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(startTime)) {
            sqlBuffer.append(" substr(print_ticket_time, 1, 10) >=  ? ");
            parmList.add(startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sqlBuffer.append(" AND substr(print_ticket_time, 1, 10) <= ?");
            parmList.add(endTime);
        }
        return sqlBuffer.toString();
    }


    /**
     * 构建酒店基本查询条件(不使用baseQueryCondition里面的startTime、endTime)
     *
     * @param parmList
     * @return
     */
    public static String buildHotelSaveAmount(List<Object> parmList, String startTime, String endTime) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(startTime)) {
            sqlBuffer.append(" substr(deal_date, 1, 10) >=  ? ");
            parmList.add(startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sqlBuffer.append(" AND substr(deal_date, 1, 10) <= ?");
            parmList.add(endTime);
        }
        return sqlBuffer.toString();
    }


    /**
     * 构建基本查询条件(不使用baseQueryCondition里面的startTime、endTime、col)
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSqlWithTimeWithCol(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                                    String startTime, String endTime, String partition,
                                                    String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(parmList, startTime, endTime, col));
        // sqlBuffer.append(buildPreSqlWithTime(parmList, startTime, endTime));

        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));


        return sqlBuffer.toString();
    }

    /**
     * 构建基本查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSqlWithTimeWithCol(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                                    String partition, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), col));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));
        return sqlBuffer.toString();
    }

    public static String buildTenantIdPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                                    String partition, String col, String tenantId) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), col));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));
        sqlBuffer.append(" and orderdbtype = ?" );
        parmList.add(tenantId);
        return sqlBuffer.toString();
    }


    /**
     * 构建基本查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSqlNoTime(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                           String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));
        return sqlBuffer.toString();
    }

    /**
     * 构建基本查询条件(使用baseQueryCondition里面的startTime、endTime)
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                     String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer
                .append(buildPreSqlWithTime(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));
        return sqlBuffer.toString();
    }

    /**
     * 构建基本查询条件(使用baseQueryCondition里面的startTime、endTime、col)
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSqlWithCol(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                            String partition, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(parmList,
                baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), col));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));
        return sqlBuffer.toString();
    }

    public static String buildPreSqlPartition(List<Object> parmList, String partition) {
        parmList.add(partition);

        return " d = ? ";
    }

    public static String buildPreSqlNoTime(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(baseQueryCondition.getGroupId())) {
            sqlBuffer.append(" and  companygroupid = ? ");
            parmList.add(baseQueryCondition.getGroupId());
        }
        sqlBuffer.append(buildRolePermission(parmList));
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            sqlBuffer.append(buildCorpAndAccount("corp_corporation", baseQueryCondition.getCorpIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getAccountIds())) {
            sqlBuffer.append(buildCorpAndAccount("account_id", baseQueryCondition.getAccountIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCostCenterList())) {
            sqlBuffer.append(buildDeptAndCostcenter("cost_center", baseQueryCondition.getCostCenterList(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getDeptList())) {
            sqlBuffer.append(buildDeptAndCostcenter("dept", baseQueryCondition.getDeptList(), parmList));
        }
        if (ConfigUtils.getBoolean("custom_dim_open", false)) {
            if (Objects.nonNull(baseQueryCondition.getDeptEntity())) {
                sqlBuffer.append(buildDeptAndCostcenterCustom("dept%d_custom", baseQueryCondition.getDeptEntity(), parmList));
            }
            if (Objects.nonNull(baseQueryCondition.getCostCenterEntity())) {

                sqlBuffer.append(buildDeptAndCostcenterCustom("costcenter%d_custom", baseQueryCondition.getCostCenterEntity(), parmList));
            }
        } else {
            if (Objects.nonNull(baseQueryCondition.getDeptEntity())) {
                sqlBuffer.append(buildDeptAndCostcenterDefault("dept", baseQueryCondition.getDeptEntity(), parmList));
            }
            if (Objects.nonNull(baseQueryCondition.getCostCenterEntity())) {
                sqlBuffer.append(buildDeptAndCostcenterDefault("cost_center", baseQueryCondition.getCostCenterEntity(), parmList));
            }
        }
        return sqlBuffer.toString();
    }

    public static String buildPreSqlCorpIds(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            sqlBuffer.append(buildCorpAndAccount("corp_corporation", baseQueryCondition.getCorpIds(), parmList));
        }
        return sqlBuffer.toString();
    }

    public static String buildPreBusSqlNoTime(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(baseQueryCondition.getGroupId())) {
            sqlBuffer.append(" and  companygroupid = ? ");
            parmList.add(baseQueryCondition.getGroupId());
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            sqlBuffer.append(buildCorpAndAccount("corpid", baseQueryCondition.getCorpIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getAccountIds())) {
            sqlBuffer.append(buildCorpAndAccount("account_id", baseQueryCondition.getAccountIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCostCenterList())) {
            sqlBuffer.append(buildDeptAndCostcenter("cost_center", baseQueryCondition.getCostCenterList(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getDeptList())) {
            sqlBuffer.append(buildDeptAndCostcenter("dept", baseQueryCondition.getDeptList(), parmList));
        }
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSqlOverride(List<String> industryList, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(industryList) && StringUtils.isNotEmpty(industryList.get(0))) {
            // industry是由#连接的两个值，前面是大类，后面是小类
            String industry = industryList.get(0);
            if (StringUtils.isNotEmpty(industry)) {
                paramList.add(industry);
                sqlBuffer.append(" and industry_type_name = ? ");
            }
        }
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSql(List<String> industryList, List<Object> parmList) {
        return buildIndustry12PreSql(industryList, parmList);
    }

    /**
     * industry有行业大类和行业小类的区分，行业小类为主大类为辅
     *
     * @param industryList
     * @param paramList
     * @return
     */
    public static String buildIndustry12PreSql(List<String> industryList, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(industryList) && StringUtils.isNotEmpty(industryList.get(0))) {
            // industry是由#连接的两个值，前面是大类，后面是小类
            String industry = industryList.get(0);
            return buildIndustry12PreSql(industry, paramList);
        } else {
            sqlBuffer.append(" and (std_industry1 is null or std_industry1 = '')");
        }
        return sqlBuffer.toString();
    }

    public static String buildIndustry12PreSql(String industry, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(industry)) {
            // industry是由#连接的两个值，前面是大类，后面是小类
            String[] strs = industry.split("#");
            // strs[0]行业d大类，strs[1]行业小类
            if (ArrayUtils.isNotEmpty(strs) && strs.length == OrpConstants.TWO && StringUtils.isNotEmpty(strs[1]) && StringUtils.isNotEmpty(strs[1])) {
                paramList.add(strs[1]);
                sqlBuffer.append(" and std_industry2 = ? ");
            } else if (ArrayUtils.isNotEmpty(strs) && StringUtils.isNotEmpty(strs[0]) && StringUtils.isNotEmpty(strs[0])) {
                paramList.add(strs[0]);
                sqlBuffer.append(" and std_industry1 = ? ");
            }
        }
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSqlByOne(String industry, List<Object> paramList) {
        return buildIndustry12PreSql(industry, paramList);
    }

    public static String buildPreSqlWithTime(List<Object> parmList, String startTime, String endTime) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(startTime)) {
            sqlBuffer.append(" and  report_date >= ? ");
            parmList.add(startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sqlBuffer.append(" and  report_date <= ? ");
            parmList.add(endTime);
        }
        return sqlBuffer.toString();
    }

    public static String buildPreSqlWithTimeWithCol(List<Object> paramList, String startTime, String endTime, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(startTime)) {
            sqlBuffer.append(" and ").append(col).append(" >= ? ");
            paramList.add(startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sqlBuffer.append(" and ").append(col).append(" <= ? ");
            paramList.add(endTime);
        }
        return sqlBuffer.toString();
    }

    public static String buildFlightAvgPriceSqlWithTime(List<Object> paramList, String startTime, String endTime, String dateColumn) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(startTime)) {
            sqlBuffer.append(String.format(" and %s >= ? ", dateColumn));
            paramList.add(startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sqlBuffer.append(String.format(" and %s <= ? ", dateColumn));
            paramList.add(endTime);
        }
        return sqlBuffer.toString();
    }

    /**
     * 构建基本查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSqlOneTrip(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(baseQueryCondition.getStartTime())) {
            sqlBuffer.append(" and  report_date >= ? ");
            parmList.add(baseQueryCondition.getStartTime());
        }
        if (StringUtils.isNotEmpty(baseQueryCondition.getEndTime())) {
            sqlBuffer.append(" and  report_date <= ? ");
            parmList.add(baseQueryCondition.getEndTime());
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            sqlBuffer.append(buildCorpAndAccount("corp_corporation", baseQueryCondition.getCorpIds(), parmList));
        }
        return sqlBuffer.toString();
    }

    /**
     * 构建公司和成本中心的查询条件
     *
     * @param key
     * @param list
     * @param parmList
     */
    public static String buildCorpAndAccount(String key, List<String> list, List<Object> parmList) {
        list = Optional.ofNullable(list).orElse(Collections.emptyList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }

        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = 0; i < list.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != list.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            parmList.add(list.get(i).toUpperCase());
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }
    /**
     * 构建当前登录用户角色权限的查询条件
     */
    public static String buildRolePermission(List<Object> parmList) {
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        boolean checkRes = CommonPermissionUtil.checkGrayscaleConfig();
        if(checkRes){
            List<String> orgIdAndCompanyId = null;
            List<String> companyId = null;
            //判断配置数据角色权限数据范围
            // 非后台用户,查询用户的数据权限
            if (baseUserInfo==null || baseUserInfo.getBgUser()==null|| !baseUserInfo.getBgUser() || !CommonPermissionUtil.isAll()) {
                orgIdAndCompanyId = CommonPermissionUtil.getPermissionOrgIdList();
                companyId = CommonPermissionUtil.getPermissionCorpIdList();
            }
            if(CollectionUtils.isNotEmpty(orgIdAndCompanyId)){
                List<String> list = orgIdAndCompanyId;
                StringBuffer sqlBuffer = new StringBuffer();
                sqlBuffer.append(" and (");
                sqlBuffer.append(" department_id in (");
                for (int i = 0; i < list.size(); i++) {
                    sqlBuffer.append(" ? ");
                    if (i != list.size() - 1) {
                        sqlBuffer.append(OrpConstants.COMMA);
                    }
                    parmList.add(list.get(i).toUpperCase());
                }
                sqlBuffer.append(") ");
                if(CollectionUtils.isNotEmpty(companyId)){
                    List<String> companyList = companyId;
                    sqlBuffer.append(" or corp_corporation in (");
                    for (int i = 0; i < companyList.size(); i++) {
                        sqlBuffer.append(" ? ");
                        if (i != companyList.size() - 1) {
                            sqlBuffer.append(OrpConstants.COMMA);
                        }
                        parmList.add(companyList.get(i).toUpperCase());
                    }
                    sqlBuffer.append(") ");
                }
                sqlBuffer.append(")");
                log.info("查询报告数据增加角色sql内容是：{}",sqlBuffer.toString());
                return sqlBuffer.toString();
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 构建list查询条件
     *
     * @param key
     * @param list
     * @param parmList
     */
    public static String buildListConditionNoAnd(String key, List<String> list, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(key + " in (");
        for (int i = 0; i < list.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != list.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            parmList.add(StringUtils.trimToEmpty(list.get(i)).toUpperCase());
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * 构建list查询条件
     *
     * @param key
     * @param list
     * @param parmList
     */
    public static String buildListConditionNoAnd(String key, List<String> list, List<Object> parmList, boolean isEncrypt) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(key + " in (");
        for (int i = 0; i < list.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != list.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            if (isEncrypt){
                parmList.add(DbResultMapUtils.sm4Encrypt(StringUtils.trimToEmpty(list.get(i))));
            }else{
                parmList.add(StringUtils.trimToEmpty(list.get(i)));
            }

        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * 构建list查询条件
     *
     * @param key
     * @param list
     * @param parmList
     */
    public static String buildListConditionWtihAnd(String key, List<String> list, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = 0; i < list.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != list.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            parmList.add(StringUtils.trimToEmpty(list.get(i)));
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * 构建list查询条件
     * 大小写敏感
     *
     * @param key
     * @param list
     * @param parmList
     */
    public static String buildListConditionWtihAndMustAndCase(String key, List<String> list, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(list)) {
            sqlBuffer.append(" and " + key + " in (");
            for (int i = 0; i < list.size(); i++) {
                sqlBuffer.append(" ? ");
                if (i != list.size() - 1) {
                    sqlBuffer.append(OrpConstants.COMMA);
                }
                parmList.add(StringUtils.trimToEmpty(list.get(i)));
            }
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }

    /**
     * city in (?,?)
     */
    public static String buildCityIdsSql(List<Integer> cityIds, String key, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = OrpConstants.ZERO; i < cityIds.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != cityIds.size() - OrpConstants.ONE) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            paramList.add(cityIds.get(i));
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * city in (?,?)
     */
    public static String buildRoomIdsSql(List<Long> ids, String key, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = OrpConstants.ZERO; i < ids.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != ids.size() - OrpConstants.ONE) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            paramList.add(ids.get(i));
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * cityName in (?,?)
     */
    public static String buildCityNameSql(List<String> cityNames, String key, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = 0; i < cityNames.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != cityNames.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            paramList.add(cityNames.get(i));
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * 排除空值
     *
     * @param key
     */
    public static String excludeEmpty(String key) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isEmpty(key)) {
            return sqlBuffer.toString();
        }
        sqlBuffer.append(" and " + key + " is not null");
        sqlBuffer.append(" and " + key + " <> ''");
        return sqlBuffer.toString();
    }

    /**
     * 构建成本中心和部门的下钻查询条件
     *
     * @param key
     * @param entity
     * @param parmList
     */
    public static String buildDeptAndCostcenterDefault(String key, SearchDeptAndCostcneterDTO entity,
                                                       List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        Integer level = entity.getKey();
        List vals = entity.getVals();
        if (Objects.nonNull(level) || CollectionUtils.isNotEmpty(vals)) {
            String filedName = key + level;
            sqlBuffer.append(" and coalesce(" + filedName + ", '') = ? ");
            parmList.add(entity.getVals().get(0));
        }
        return sqlBuffer.toString();
    }

    /**
     * 构建成本中心和部门的下钻查询条件
     *
     * @param key
     * @param entity
     * @param parmList
     */
    public static String buildDeptAndCostcenterCustom(String key, SearchDeptAndCostcneterDTO entity,
                                                      List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        Integer level = entity.getKey();
        List vals = entity.getVals();
        if (Objects.nonNull(level) || CollectionUtils.isNotEmpty(vals)) {
            String filedName = String.format(key, level);
            sqlBuffer.append(" and coalesce(" + filedName + ", '') = ? ");
            parmList.add(entity.getVals().get(0));
        }
        return sqlBuffer.toString();
    }

    /**
     * 构建成本中心和部门的查询条件
     * <br>
     * key代表部门的1-10，成本中心1-6
     * vals是部门和成本中心具体的值
     * selectall是否全选
     * way（有几个值，是代表包含还是排除，可以看代码）
     * |permitVals具体权限值（也是部门和成本中心）
     *
     * @param key
     * @param list
     * @param parmList
     */
    public static String buildDeptAndCostcenter(String key, List<SearchDeptAndCostcneterDTO> list,
                                                List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append("AND (");

        boolean isFirstCondition = true;

        for (SearchDeptAndCostcneterDTO searchDeptAndCostcneterDto : list) {
            if (CollectionUtils.isEmpty(searchDeptAndCostcneterDto.getVals())) {
                continue;
            }

            Integer level = searchDeptAndCostcneterDto.getKey();
            if (Objects.isNull(level)) {
                continue;
            }
            String filedName = key + level;

            if (!isFirstCondition) {
                sqlBuffer.append(" or");
            }
            isFirstCondition = false;

            if (Objects.isNull(searchDeptAndCostcneterDto.getWay()) || searchDeptAndCostcneterDto.getWay() != OrpConstants.THREE) {
                // 包含
                if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                    sqlBuffer.append(" coalesce(" + filedName + ", '') <> ''");
                }
                List<String> vals = searchDeptAndCostcneterDto.getVals();
                if (CollectionUtils.isNotEmpty(vals)) {
                    if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                        sqlBuffer.append(" or");
                    }
                    sqlBuffer.append(" " + filedName + " in (");
                    for (int i = 0; i < vals.size(); i++) {
                        sqlBuffer.append(" ? ");
                        if (i != vals.size() - 1) {
                            sqlBuffer.append(OrpConstants.COMMA);
                        }
                        parmList.add(vals.get(i));
                    }
                    sqlBuffer.append(")");
                }
            } else {
                // 剔除
                if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                    sqlBuffer.append(" coalesce(" + filedName + ", '') = ''");
                }
                List<String> vals = searchDeptAndCostcneterDto.getVals();
                if (CollectionUtils.isNotEmpty(vals)) {
                    if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                        sqlBuffer.append(" or");
                    }
                    sqlBuffer.append(" " + filedName + " not in (");
                    for (int i = 0; i < vals.size(); i++) {
                        sqlBuffer.append(" ? ");
                        if (i != vals.size() - 1) {
                            sqlBuffer.append(OrpConstants.COMMA);
                        }
                        parmList.add(vals.get(i));
                    }
                    sqlBuffer.append(")");
                }
            }
        }

        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * 构建成本中心和部门的查询条件
     *
     * @param key
     * @param list
     * @param parmList
     */
    public static String buildDetailSqlDeptAndCostcenter(String key,
                                                         List<SearchDeptAndCostcneterEntity> list,
                                                         List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        for (SearchDeptAndCostcneterEntity searchDeptAndCostcneterDto : list) {
            Integer level = searchDeptAndCostcneterDto.getKey();
            if (Objects.isNull(level)) {
                continue;
            }
            String filedName = key + level;
            if (Objects.isNull(searchDeptAndCostcneterDto.getWay()) || searchDeptAndCostcneterDto.getWay() != OrpConstants.THREE) {
                // 包含
                List<String> vals = searchDeptAndCostcneterDto.getVals();
                if (CollectionUtils.isEmpty(vals)) {
                    continue;
                }
                sqlBuffer.append(" and " + filedName + " in (");
                for (int i = 0; i < vals.size(); i++) {
                    sqlBuffer.append(" ? ");
                    if (i != vals.size() - 1) {
                        sqlBuffer.append(OrpConstants.COMMA);
                    }
                    parmList.add(vals.get(i));
                }
                sqlBuffer.append(")");
            } else {
                // 剔除
                List<String> vals = searchDeptAndCostcneterDto.getVals();
                if (CollectionUtils.isNotEmpty(vals)) {
                    sqlBuffer.append(" and " + filedName + " not in (");
                    for (int i = 0; i < vals.size(); i++) {
                        sqlBuffer.append(" ? ");
                        if (i != vals.size() - 1) {
                            sqlBuffer.append(OrpConstants.COMMA);
                        }
                        parmList.add(vals.get(i));
                    }
                    sqlBuffer.append(")");
                }

                List<String> permitVals = searchDeptAndCostcneterDto.getPermitVals();
                if (CollectionUtils.isNotEmpty(permitVals)) {
                    sqlBuffer.append(" and " + filedName + " in (");
                    for (int i = 0; i < permitVals.size(); i++) {
                        sqlBuffer.append(" ? ");
                        if (i != permitVals.size() - 1) {
                            sqlBuffer.append(OrpConstants.COMMA);
                        }
                        parmList.add(permitVals.get(i));
                    }
                    sqlBuffer.append(")");
                }
            }

        }
        return sqlBuffer.toString();
    }

    public static String buildPreSqlSingleVal(List<Object> parmList, Object val, String key) {
        parmList.add(val);
        return String.format(" and %s = ? ", key);
    }

    public static String buildPreSqlSingleVal(List<Object> parmList, String val, String key) {
        parmList.add(val);
        return String.format(" and UPPER(%s) = UPPER(?) ", key);
    }

    public static String buildPreSqlMultiVal(List<Object> paramList, List<Object> vals, String key) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = OrpConstants.ZERO; i < vals.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != vals.size() - OrpConstants.ONE) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            paramList.add(vals.get(i));
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    public static String buildCityPreSql(List<Long> list, List<Object> parmList, String conditionFiled) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(list)) {
            sqlBuffer.append(" and " + conditionFiled + " in (");
            for (int i = 0; i < list.size(); i++) {
                sqlBuffer.append(" ? ");
                if (i != list.size() - 1) {
                    sqlBuffer.append(OrpConstants.COMMA);
                }
                parmList.add(list.get(i));
            }
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }

    public static String buildAgreementPreSql(List<String> dimIds, List<String> corpIds, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(dimIds)) {
            sqlBuffer.append("and ");
            sqlBuffer.append("dim_id in (");
            for (int i = 0; i < dimIds.size(); i++) {
                sqlBuffer.append(" ? ");
                if (i != dimIds.size() - 1) {
                    sqlBuffer.append(OrpConstants.COMMA);
                }
                paramList.add(dimIds.get(i));
            }
            sqlBuffer.append(") ");
        }
        if (CollectionUtils.isNotEmpty(corpIds)) {
            String preCorpSql = corpIds.stream().map(corp -> "?").collect(Collectors.joining(", "));
            sqlBuffer.append("and ");
            sqlBuffer.append(String.format("notEmpty(arrayIntersect(splitByString('/', " +
                    "replaceRegexpAll(upper(coalesce(all_company_code, '')), ' ', '')), array(%s))) ", preCorpSql));
            sqlBuffer.append("and ");
            sqlBuffer.append("notEmpty(agreement_end_date)");
            paramList.addAll(corpIds);
        }
        return sqlBuffer.toString();
    }

    public static String buildTenantId(List<Object> paramList) {
        paramList.add(TenantContext.getTenantId());
        return " and orderdbtype = ? ";
    }

    /**
     * 处理之前赋值为string类型的话，clickhouse无法自动转为数值型，现在强制使用long型进行赋值
     * 构建订单id查询条件
     *
     * @param key
     * @param orderIds
     * @param paramList
     * @return
     */
    public static String buildOrderIdsConditionSql(String key, List<Long> orderIds, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = 0; i < orderIds.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != orderIds.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            paramList.add(orderIds.get(i));
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    @Data
    public static class TableAndTimeColBind {

        private ClickHouseTable clickHouseTable;

        private String dateColumn;

    }
}
