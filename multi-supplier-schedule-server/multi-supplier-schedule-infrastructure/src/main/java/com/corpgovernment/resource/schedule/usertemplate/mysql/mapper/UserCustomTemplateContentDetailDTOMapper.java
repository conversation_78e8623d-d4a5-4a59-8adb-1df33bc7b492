package com.corpgovernment.resource.schedule.usertemplate.mysql.mapper;

import com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateContentDetailDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserCustomTemplateContentDetailDTOMapper {

    int deleteByContentNo(@Param("contentNo") String contentNo);

    int insertSelective(UserCustomTemplateContentDetailDo record);

    int insertBatchSelective(List<UserCustomTemplateContentDetailDo> record);

    List<UserCustomTemplateContentDetailDo> selectAllByContentNo(@Param("contentNo") String contentNo);

    List<UserCustomTemplateContentDetailDo> selectByContentNolist(@Param("contentNoList") List<String> contentNoList);

    int updateByContentNoSelective(UserCustomTemplateContentDetailDo record);


}