package com.corpgovernment.resource.schedule.screen.mapper;


import com.corpgovernment.resource.schedule.config.TkMapper;
import com.corpgovernment.resource.schedule.domain.screen.model.ConsumeRankDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.HotCityDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDestinationInfoDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.StandardRateDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.TotalAmountDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.TotalOrderDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.TripRankDTO;
import com.corpgovernment.resource.schedule.screen.po.ConsumeRankDO;
import com.corpgovernment.resource.schedule.screen.po.HotCityDO;
import com.corpgovernment.resource.schedule.screen.po.MapDO;
import com.corpgovernment.resource.schedule.screen.po.MapDestinationInfoDO;
import com.corpgovernment.resource.schedule.screen.po.ScreenDO;
import com.corpgovernment.resource.schedule.screen.po.StandardRateDO;
import com.corpgovernment.resource.schedule.screen.po.TotalAmountDO;
import com.corpgovernment.resource.schedule.screen.po.TotalOrderDO;
import com.corpgovernment.resource.schedule.screen.po.TripRankDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> Smith
 */
public interface ScreenMapper extends TkMapper<ScreenDO> {


    TotalAmountDO totalAmount(@Param("totalAmountDTO") TotalAmountDTO totalAmountDTO);

    TotalOrderDO totalOrder(@Param("totalOrderDTO") TotalOrderDTO totalOrderDTO);

    List<ConsumeRankDO> consumeRankDept(@Param("consumeRankDTO") ConsumeRankDTO consumeRankDTO);

    List<HotCityDO> hotCity(@Param("hotCityDTO") HotCityDTO hotCityDTO);

    StandardRateDO standardRate(@Param("standardRateDTO") StandardRateDTO standardRateDTO);

    List<TripRankDO> tripRank(@Param("tripRankDTO") TripRankDTO tripRankDTO);

    List<MapDO> map(@Param("mapDTO") MapDTO mapDTO);

    List<ConsumeRankDO> consumeRankCostCenter(@Param("consumeRankDTO") ConsumeRankDTO consumeRankDTO);

    List<MapDestinationInfoDO> destinationInfo(@Param("mapDestinationInfoDTO") MapDestinationInfoDTO mapDestinationInfoDTO);
}
