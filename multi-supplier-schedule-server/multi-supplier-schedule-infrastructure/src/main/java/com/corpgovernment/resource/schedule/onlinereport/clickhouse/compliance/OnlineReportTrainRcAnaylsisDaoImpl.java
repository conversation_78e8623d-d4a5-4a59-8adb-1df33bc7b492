package com.corpgovernment.resource.schedule.onlinereport.clickhouse.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTimesDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTrendDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.TrainRcEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 *
 * @date 2022/4/10 20:49
 *
 * @Desc
 */
@Service
@Slf4j
public class OnlineReportTrainRcAnaylsisDaoImpl extends AbstractOnlineReportTopRcAnaylsisDao {

    private static final String LOG_TITLE = "OnlineReportTrainRcAnaylsisDaoImpl";

    private static final String TRAIN_RC_SQL = "SELECT %s, rcTimes as TRAIN_RC_TIMES"
            + ", round(case when coalesce(orderCount, 0) !=0  then divide(coalesce(rcTimes, 0), coalesce(orderCount, 0)) * 100 else 0 end, 2) as TRAIN_RC_PERCENT"
            + ", seattypeRcTimes as TRAIN_SEATTYPE_RC_TIMES, ticketRcTimes as TRAIN_TICKET_RC_TIMES FROM (\n"
            + "    SELECT * FROM(SELECT %s \n"
            + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
            + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes \n"
            + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc != 'T' THEN order_id  END) AS noRcTimes \n"
            + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND seattype_rccodeid IS NOT NULL AND seattype_rccodeid != '' " +
            " THEN order_id  END) AS seattypeRcTimes\n"
            + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND ticket_rccodeid IS NOT NULL AND ticket_rccodeid != '' " +
            " THEN order_id  END) AS ticketRcTimes"
            + "    FROM olrpt_indextraindownload_all  WHERE %s  AND order_status  in ('TA','RP','EP','EA') GROUP BY %s\n"
            + "    ) WHERE rcTimes > 0) f ORDER BY f.rcTimes";

    private static final String TRAIN_RC_COUNT_SQL = "SELECT count(1) as countAll FROM ( SELECT * FROM(SELECT %s \n"
            + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes \n"
            + "        FROM olrpt_indextraindownload_all  WHERE %s  AND order_status  in ('TA','RP','EP','EA')"
            + "    GROUP BY %s ) WHERE rcTimes > 0 ) f";

    @Override
    public String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum,
                           BaseQueryConditionDTO baseQueryConditionDto, Pager pager, String orderType, String user) {
        StringBuffer baseSql = new StringBuffer();
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        ClickHouseTable clickHouseTable = getTargetTable();
        String partion = queryPartition(clickHouseTable);
        String currentCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion) + buildPreSqlUser(user, parmList);
        AbstractOnlineReportTopRcAnaylsisDao.JoinCondition biz = joinCondition(analysisObjectEnum);
        baseSql.append(String.format(TRAIN_RC_SQL, biz.getResultFields(), biz.getGroupFields(), currentCondition,
                biz.getGroupFields()));
        baseSql.append(" limit ?, ? ");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        return baseSql.toString();
    }

    @Override
    protected ClickHouseTable getTargetTable() {
        return ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;
    }

    @Override
    public String countSql(List<Object> parmList, ClickHouseTable clickHouseTable,
                           AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, String orderType, String user) {
        StringBuffer baseSql = new StringBuffer();
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String partion = queryPartition(clickHouseTable);
        String currentCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion) + buildPreSqlUser(user, parmList);
        AbstractOnlineReportTopRcAnaylsisDao.JoinCondition biz = joinCondition(analysisObjectEnum);
        baseSql.append(String.format(TRAIN_RC_COUNT_SQL, biz.getGroupFields(), currentCondition, biz.getGroupFields()));
        return baseSql.toString();
    }

    @Override
    protected String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return null;
    }

    /**
     * rc概览
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    
    public List<RcTimesDTO> aggreationRcView(BaseQueryConditionDTO requestDto) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append(" COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
                + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes \n"
                + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc != 'T' THEN order_id  END) AS noRcTimes \n"
        );
        sqlBuilder.append(" from ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));

        /*
         * <a href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=2768787258">商旅与程曦在线报告状态相关字段对应关系</a>
         * 杨鑫：已出票/部分退票/部分改签/全部改签  =  已购票。
         * TA已出票
         * RP部分退票
         * EP部分改签
         * EA全部改签
         */
        sqlBuilder.append(" and order_status in ('TA', 'RP', 'EP', 'EA')");

        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTimesDTO.class, "aggreationRcView");
    }

    /**
     * rc概览
     *
     * @param startTime
     * @param endTime
     * @param industryList
     * @param dataTypeEnum
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @return
     * @throws Exception
     */
    
    public List<RcTimesDTO> aggreationRcViewCorpAndIndustry(String startTime, String endTime, List<String> industryList, DataTypeEnum dataTypeEnum,
                                                            String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append(" COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
                + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes \n"
                + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc != 'T' THEN order_id  END) AS noRcTimes \n"
        );
        sqlBuilder.append(" from ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        boolean ignoreTenantId = false;
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        }
        /*
         * <a href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=2768787258">商旅与程曦在线报告状态相关字段对应关系</a>
         * 杨鑫：已出票/部分退票/部分改签/全部改签  =  已购票。
         * TA已出票
         * RP部分退票
         * EP部分改签
         * EA全部改签
         */
        sqlBuilder.append(" and order_status in ('TA', 'RP', 'EP', 'EA')");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTimesDTO.class, "aggreationRcView", ignoreTenantId);
    }

    /**
     * rc原因
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationRcViewReason(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append(" COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
                + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes \n"
                + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc != 'T' THEN order_id  END) AS noRcTimes \n"
                + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND seattype_rccodeid IS NOT NULL AND seattype_rccodeid != '' " +
                " THEN order_id  END) AS seattypeRcTimes\n"
                + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND ticket_rccodeid IS NOT NULL AND ticket_rccodeid != '' " +
                " THEN order_id  END) AS ticketRcTimes"
                + "   , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND coalesce(refund_rc,'') != ''  THEN order_id  END) AS refundRcTimes"
        );
        sqlBuilder.append(" from ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        /*
         * <a href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=2768787258">商旅与程曦在线报告状态相关字段对应关系</a>
         * 杨鑫：已出票/部分退票/部分改签/全部改签  =  已购票。
         * TA已出票
         * RP部分退票
         * EP部分改签
         * EA全部改签
         */
        sqlBuilder.append(" and order_status in ('TA', 'RP', 'EP', 'EA')");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationRcViewReason");
    }

    /**
     * rc原因明细
     *
     * @param requestDto
     * @param clazz
     * @param trainRcEnum
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationRcViewReasonDetail(BaseQueryConditionDTO requestDto, Class<T> clazz,
                                                    TrainRcEnum trainRcEnum) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String sql = StringUtils.EMPTY;
        String groupField = StringUtils.EMPTY;
        String rcCondition = StringUtils.EMPTY;
        switch (trainRcEnum) {
            case S:
                // seattype_rccodename
                sql =
                        "seattype_rccodeid as rcCode, seattype_rccodename as rcDesc, COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) " +
                                " AND seattype_rccodeid IS NOT NULL AND seattype_rccodeid != '' THEN order_id  END) AS rcTimes";
                groupField = "seattype_rccodeid, seattype_rccodename";
                rcCondition = "seattype_rccodeid is not null and seattype_rccodeid != ''";
                break;
            case T:
                // ticket_rccodename
                sql =
                        "ticket_rccodeid as rcCode, ticket_rccodename as rcDesc, COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) " +
                                " AND ticket_rccodeid IS NOT NULL AND ticket_rccodeid != '' THEN order_id  END) AS rcTimes";
                groupField = "ticket_rccodeid, ticket_rccodename";
                rcCondition = "ticket_rccodeid is not null and ticket_rccodeid != ''";
                break;
            case R:
                // ticket_rccodename
                sql =
                        "refund_rc as rcCode, refund_rc_desc as rcDesc, COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) " +
                                " AND coalesce(refund_rc,'') != '' THEN order_id  END) AS rcTimes";
                groupField = "refund_rc, refund_rc_desc";
                rcCondition = "coalesce(refund_rc,'') != ''";
                break;
            default:
                break;
        }
        sqlBuilder.append("select ");
        sqlBuilder.append(sql);
        sqlBuilder.append(" from ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        sqlBuilder.append(" and ");
        sqlBuilder.append(rcCondition);
        /*
         * <a href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=2768787258">商旅与程曦在线报告状态相关字段对应关系</a>
         * 杨鑫：已出票/部分退票/部分改签/全部改签  =  已购票。
         * TA已出票
         * RP部分退票
         * EP部分改签
         * EA全部改签
         */
        sqlBuilder.append(" and order_status in ('TA', 'RP', 'EP', 'EA')");
        sqlBuilder.append(" group by ");
        sqlBuilder.append(groupField);

        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationRcViewReasonDetail");
    }

    /**
     * rc趋势
     *
     * @param requestDto
     * @param dateDimensionEnum
     * @return
     * @throws Exception
     */
    public List<RcTrendDTO> aggreationRcTrend(BaseQueryConditionDTO requestDto, QueryReportAggDateDimensionEnum dateDimensionEnum) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        Map map = getGroupFieldBy(dateDimensionEnum);
        String sql = (String) map.get(TREND_DIM_KEY);
        String groupField = (String) map.get(TREND_GROUP_KEY);
        sqlBuilder.append("select ");
        sqlBuilder.append(sql);
        sqlBuilder.append(" , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
                + " , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes \n");
        sqlBuilder.append(" from ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        /*
         * <a href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=2768787258">商旅与程曦在线报告状态相关字段对应关系</a>
         * 杨鑫：已出票/部分退票/部分改签/全部改签  =  已购票。
         * TA已出票
         * RP部分退票
         * EP部分改签
         * EA全部改签
         */
        sqlBuilder.append(" and order_status in ('TA', 'RP', 'EP', 'EA')");
        sqlBuilder.append(" group by");
        sqlBuilder.append(groupField);
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTrendDTO.class, "aggreationRcTrend");
    }

    /**
     * rc趋势
     *
     * @param startTime
     * @param endTime
     * @param dateDimensionEnum
     * @param industryList
     * @param dataTypeEnum
     * @param compareSameLevel  是否对比同级比较
     * @param consumptionLevel  消费等级
     * @return
     * @throws Exception
     */
    
    public List<RcTrendDTO> aggreationRcTrendCorpAndIndustry(String startTime, String endTime, QueryReportAggDateDimensionEnum dateDimensionEnum,
                                                             List<String> industryList, DataTypeEnum dataTypeEnum,
                                                             String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        Map map = getGroupFieldBy(dateDimensionEnum);
        String sql = (String) map.get(TREND_DIM_KEY);
        String groupField = (String) map.get(TREND_GROUP_KEY);
        sqlBuilder.append("select ");
        sqlBuilder.append(sql);
        sqlBuilder.append(" , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
                + " , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes \n");
        sqlBuilder.append(" from ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        boolean ignoreTenantId = false;
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        }
        /*
         * <a href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=2768787258">商旅与程曦在线报告状态相关字段对应关系</a>
         * 杨鑫：已出票/部分退票/部分改签/全部改签  =  已购票。
         * TA已出票
         * RP部分退票
         * EP部分改签
         * EA全部改签
         */
        sqlBuilder.append(" and order_status in ('TA', 'RP', 'EP', 'EA')");
        sqlBuilder.append(" group by");
        sqlBuilder.append(groupField);
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTrendDTO.class, "aggreationRcTrendCorpAndIndustry", ignoreTenantId);
    }
}
