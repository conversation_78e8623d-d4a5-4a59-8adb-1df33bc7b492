package com.corpgovernment.resource.schedule.geography.mysql.entity;

import cn.hutool.core.date.DateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;

/**
 * <AUTHOR> zhang
 * @date 2023/10/23 13:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CtripCityInfoDo {
    /**
     * 多供应商平台国家ID
     */
    @Id
    private Long id;
    private String cityId;
    private String cityName;
    private String cityEnName;
    private String cityPinYinName;
    private String cityFirstChar;
    private String cityJianPin;

    private String provinceId;
    private String countryId;

    private String parentCityId;
    private Short corpTag;

    private Double centerLat;
    private Double centerLon;

    private Byte hotelContain;

    private String hashCode;

    /**
     * 创建时间
     */
    private DateTime datachangeCreatetime;
    /**
     * 修改时间
     */
    private DateTime datachangeLasttime;
    /**
     * 逻辑删除
     */
    private Byte isDeleted;

}
