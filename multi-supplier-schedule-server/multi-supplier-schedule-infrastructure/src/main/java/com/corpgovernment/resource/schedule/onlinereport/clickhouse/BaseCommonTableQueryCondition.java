package com.corpgovernment.resource.schedule.onlinereport.clickhouse;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.SearchDeptAndCostcneterEntity;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.constants.CkTableFieldConstants;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.dao.base
 * @description: 在线报告表公共查询条件
 * @author: Chris <PERSON>
 * @create: 2021-11-19 17:08
 **/
@Component
public class BaseCommonTableQueryCondition {

    /**
     * sql 查询条件
     *
     * @param request
     * @param sqlBuilder
     */
    public void buildPreSqlCondition(OnlineDetailRequestDto request, StringBuilder sqlBuilder) {
        BaseQueryCondition queryCondition = request.getBaseCondition();
        if (Objects.isNull(queryCondition)) {
            return;
        }
        /**
         * 公司集团ID
         */
        if (StringUtils.isNotBlank(queryCondition.getGroupId())) {
            sqlBuilder.append(" and companygroupid = ? \n");
        }
        /**
         * 公司ID
         */
        /**
        if (CollectionUtils.isNotEmpty(queryCondition.getCorpIds())) {
            List<String> corpIdList = queryCondition.getCorpIds();
            sqlBuilder.append(" and corp_corporation  in ( ");
            sqlBuilder.append(corpIdList.stream().map(t -> OrpConstants.QUESTION_REMARK).collect(Collectors.joining(OrpConstants.COMMA)));
            sqlBuilder.append(" ) \n");
        }
        */
        /**
         * 主账户
         */
        if (CollectionUtils.isNotEmpty(queryCondition.getAccountIds())) {
            List<String> accountIdList = queryCondition.getAccountIds();
            sqlBuilder.append(" and account_id  in ( ");
            sqlBuilder.append(accountIdList.stream().map(t -> OrpConstants.QUESTION_REMARK).collect(Collectors.joining(OrpConstants.COMMA)));
            sqlBuilder.append(" ) \n");
        }
        /**
         * 部门
         */
        if (CollectionUtils.isNotEmpty(queryCondition.getDeptList())) {
            List<SearchDeptAndCostcneterEntity> deptList = queryCondition.getDeptList();
            buildOrgSql(sqlBuilder, deptList);
        }
        /**
         * 成本中心
         */
        if (CollectionUtils.isNotEmpty(queryCondition.getCostCenterList())) {
            List<SearchDeptAndCostcneterEntity> costCenterList = queryCondition.getCostCenterList();
            buildDeptCostCenterSql(OrpConstants.ONE, sqlBuilder, costCenterList);
        }

        /**
         * 用户数据权限
         */
        sqlBuilder.append(BaseConditionPrebuilder.buildRolePermission());
    }

    /**
     * 获取
     *
     * @param sqlBuilder
     * @param orgList
     */
    private void buildOrgSql(StringBuilder sqlBuilder, List<SearchDeptAndCostcneterEntity> orgList) {
        if(CollectionUtils.isEmpty(orgList)){
            return;
        }
        for(SearchDeptAndCostcneterEntity t : orgList){
            if(CollectionUtils.isEmpty(t.getVals())){
                continue;
            }
            if (Objects.isNull(t.getWay()) || t.getWay() != OrpConstants.THREE) {
                // 包含
                if (org.apache.commons.lang.BooleanUtils.isTrue(t.isSelectAll())) {
                    return;
                }
                sqlBuilder.append(" and org_id in (");
                for (int i = 0; i < t.getVals().size(); i++) {
                    sqlBuilder.append(" ? ");
                    if (i != t.getVals().size() - 1) {
                        sqlBuilder.append(OrpConstants.COMMA);
                    }
                }
                sqlBuilder.append(")");
            } else {
                // 剔除
                if (org.apache.commons.lang.BooleanUtils.isTrue(t.isSelectAll())) {
                    sqlBuilder.append(" and coalesce( org_id, '') = ''");
                } else {
                    sqlBuilder.append(" and org_id not in (");
                    for (int i = 0; i < t.getVals().size(); i++) {
                        sqlBuilder.append(" ? ");
                        if (i != t.getVals().size() - 1) {
                            sqlBuilder.append(OrpConstants.COMMA);
                        }
                    }
                    sqlBuilder.append(")");
                }
            }
            return;
        }
    }

    /**
     * 获取
     *
     * @param type
     * @param sqlBuilder
     * @param costCenterList
     */
    private void buildDeptCostCenterSql(int type, StringBuilder sqlBuilder, List<SearchDeptAndCostcneterEntity> costCenterList) {
        costCenterList.stream().filter(Objects::nonNull).filter(t -> Objects.nonNull(t.getKey())).forEach(t -> {
            Integer fieldKey = t.getKey();
            String columnsField = StringUtils.EMPTY;
            if (type == OrpConstants.ZERO) {
                columnsField = "org_id";
            } else {
                columnsField = String.format(CkTableFieldConstants.COST_CENTER_FIELD, fieldKey);
            }
            if (Objects.isNull(t.getWay()) || t.getWay() != OrpConstants.THREE) {
                // 包含
                if (org.apache.commons.lang.BooleanUtils.isTrue(t.isSelectAll())) {
                    sqlBuilder.append(" and coalesce(" + columnsField + ", '') <> ''");
                }
                List<String> vals = t.getVals();
                if (CollectionUtils.isNotEmpty(vals)) {
                    sqlBuilder.append(" and " + columnsField + " in (");
                    for (int i = 0; i < vals.size(); i++) {
                        sqlBuilder.append(" ? ");
                        if (i != vals.size() - 1) {
                            sqlBuilder.append(OrpConstants.COMMA);
                        }
                    }
                    sqlBuilder.append(")");
                }
            } else {
                // 剔除
                if (org.apache.commons.lang.BooleanUtils.isTrue(t.isSelectAll())) {
                    sqlBuilder.append(" and coalesce(" + columnsField + ", '') = ''");
                }
                List<String> vals = t.getVals();
                if (CollectionUtils.isNotEmpty(vals)) {
                    sqlBuilder.append(" and " + columnsField + " not in (");
                    for (int i = 0; i < vals.size(); i++) {
                        sqlBuilder.append(" ? ");
                        if (i != vals.size() - 1) {
                            sqlBuilder.append(OrpConstants.COMMA);
                        }
                    }
                    sqlBuilder.append(")");
                }
                List<String> permitVals = t.getPermitVals();
                if (CollectionUtils.isNotEmpty(permitVals)) {
                    sqlBuilder.append(" and " + columnsField + " in (");
                    for (int i = 0; i < permitVals.size(); i++) {
                        sqlBuilder.append(" ? ");
                        if (i != permitVals.size() - 1) {
                            sqlBuilder.append(OrpConstants.COMMA);
                        }
                    }
                    sqlBuilder.append(")");
                }
            }
        });
    }

    /**
     * sql 查询条件
     *
     * @param nextIndex
     * @param request
     * @param statement
     */
    public void setPreSqlCondition(AtomicInteger nextIndex, OnlineDetailRequestDto request, PreparedStatement statement) throws SQLException {
        BaseQueryCondition queryCondition = request.getBaseCondition();
        if (Objects.isNull(queryCondition)) {
            return;
        }
        /**
         * 公司集团ID
         */
        if (StringUtils.isNotBlank(queryCondition.getGroupId())) {
            statement.setString(nextIndex.getAndIncrement(), queryCondition.getGroupId());
        }
        /**
         * 公司ID
         */
        if (CollectionUtils.isNotEmpty(queryCondition.getCorpIds())) {
            List<String> corpIdList = queryCondition.getCorpIds();
            corpIdList.stream().forEach(t -> {
                try {
                    statement.setString(nextIndex.getAndIncrement(), t);
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            });
        }
        /**
         * 主账户
         */
        if (CollectionUtils.isNotEmpty(queryCondition.getAccountIds())) {
            List<String> accountIdList = queryCondition.getAccountIds();
            accountIdList.stream().forEach(t -> {
                try {
                    statement.setInt(nextIndex.getAndIncrement(), Integer.valueOf(t));
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            });
        }
        /**
         * 部门
         */
        if (CollectionUtils.isNotEmpty(queryCondition.getDeptList())) {
            List<SearchDeptAndCostcneterEntity> deptList = queryCondition.getDeptList();
            setDeptCostCenterCondition(nextIndex, deptList, statement);
        }
        /**
         * 成本中心
         */
        if (CollectionUtils.isNotEmpty(queryCondition.getCostCenterList())) {
            List<SearchDeptAndCostcneterEntity> costCenterList = queryCondition.getCostCenterList();
            setDeptCostCenterCondition(nextIndex, costCenterList, statement);
        }
    }

    /**
     * set 条件
     *
     * @param nextIndex
     * @param deptCostCenterList
     * @param statement
     */
    private void setDeptCostCenterCondition(AtomicInteger nextIndex, List<SearchDeptAndCostcneterEntity> deptCostCenterList, PreparedStatement statement) throws SQLException {
        for (SearchDeptAndCostcneterEntity t : deptCostCenterList) {

            if (Objects.isNull(t.getWay()) || t.getWay() != OrpConstants.THREE) {
                // 包含
                List<String> vals = t.getVals();
                if (CollectionUtils.isNotEmpty(vals)) {
                    for (int i = 0; i < vals.size(); i++) {
                        statement.setString(nextIndex.getAndIncrement(), vals.get(i));
                    }
                }
            } else {
                // 剔除
                List<String> vals = t.getVals();
                if (CollectionUtils.isNotEmpty(vals)) {
                    for (int i = 0; i < vals.size(); i++) {
                        statement.setString(nextIndex.getAndIncrement(), vals.get(i));
                    }
                }
                List<String> permitVals = t.getPermitVals();
                if (CollectionUtils.isNotEmpty(permitVals)) {
                    for (int i = 0; i < permitVals.size(); i++) {
                        statement.setString(nextIndex.getAndIncrement(), vals.get(i));
                    }
                }
            }
        }
    }
}
