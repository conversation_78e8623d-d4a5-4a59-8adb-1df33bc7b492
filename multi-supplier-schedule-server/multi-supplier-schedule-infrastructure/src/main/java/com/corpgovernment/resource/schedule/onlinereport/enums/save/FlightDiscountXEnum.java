package com.corpgovernment.resource.schedule.onlinereport.enums.save;

import java.util.Arrays;
import java.util.List;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.save
 * @description:机酒节省分析-机票折扣分布
 * @author: md_wang
 * @create: 2022-08-02 20:00
 **/
public enum FlightDiscountXEnum {
    ZERO("0-0.1", "ticket0", "ticketRate0"),
    ONE("0.1-0.2", "ticket1", "ticketRate1"),
    TWO("0.2-0.3", "ticket2", "ticketRate2"),
    THREE("0.3-0.4", "ticket3", "ticketRate3"),
    FOUR("0.4-0.5", "ticket4", "ticketRate4"),
    FIVE("0.5-0.6", "ticket5", "ticketRate5"),
    SIX("0.6-0.7", "ticket6", "ticketRate6"),
    SEVEN("0.7-0.8", "ticket7", "ticketRate7"),
    EIGHT("0.8-0.9", "ticket8", "ticketRate8"),
    NINE("0.9-1.0", "ticket9", "ticketRate9"),
    /**
     * 无折扣
     */
    TEN("1.0", "ticket10", "ticketRate10"),
    ;

    private String x;
    private String ticket;
    private String ticketRate;

    FlightDiscountXEnum(String x, String ticket, String ticketRate) {
        this.x = x;
        this.ticket = ticket;
        this.ticketRate = ticketRate;
    }

    /**
     * 无折扣
     */
    public static List<FlightDiscountXEnum> discountNoUp() {
        return Arrays.asList(TEN);
    }

    /**
     * 9折及以上
     */
    public static List<FlightDiscountXEnum> discount9Up() {
        return Arrays.asList(TEN, NINE);
    }

    /**
     * 7折及以上
     */
    public static List<FlightDiscountXEnum> discount7Up() {
        return Arrays.asList(TEN, NINE, EIGHT, SEVEN);
    }

    /**
     * 8折及以上
     */
    public static List<FlightDiscountXEnum> discount8Up() {
        return Arrays.asList(TEN, NINE, EIGHT);
    }

    public String getX() {
        return x;
    }

    public void setX(String x) {
        this.x = x;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public String getTicketRate() {
        return ticketRate;
    }

    public void setTicketRate(String ticketRate) {
        this.ticketRate = ticketRate;
    }
}
