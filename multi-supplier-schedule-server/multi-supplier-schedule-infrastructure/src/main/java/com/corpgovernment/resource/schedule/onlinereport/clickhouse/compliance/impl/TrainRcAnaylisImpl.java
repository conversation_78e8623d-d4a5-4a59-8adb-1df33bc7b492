package com.corpgovernment.resource.schedule.onlinereport.clickhouse.compliance.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcDetailDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTimesDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTrendDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.TrainRcTimesDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcTrend;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcView;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcViewBu;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcViewCount;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcViewReason;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcViewReasonDetail;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.compliance.AbstractRcAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.compliance.OnlineReportTrainRcAnaylsisDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.RcStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.TrainRcEnum;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.convert.RcDetailMapper;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

/*
 * <AUTHOR>
 *
 * @date 2022/4/6 19:12
 *
 * @Desc
 */
@Service
public class TrainRcAnaylisImpl extends AbstractRcAnalysisBiz {

    @Autowired
    private OnlineReportTrainRcAnaylsisDaoImpl rcAnaylsisDao;

    @Autowired
    private RcDetailMapper rcDetailMapper;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Qualifier("screenThreadPoolExecutor")
    @Resource
    private ThreadPoolExecutor executorService;

    
    @Override
    public RcView getRcView(BaseQueryCondition request, String orderType) throws Exception {
        RcView rcView = new RcView();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request);
        List<RcTimesDTO> trainList = rcAnaylsisDao.aggreationRcView(baseQueryConditionDto);
        if (CollectionUtils.isNotEmpty(trainList)) {
            rcView.setRcTimes(trainList.get(0).getRcTimes());
            rcView.setRcPercent(OrpReportUtils.divideWithPercent(new BigDecimal(rcView.getRcTimes()),
                    new BigDecimal(trainList.get(0).getOrderCount())));
        }
        return rcView;
    }

    
    @Override
    public RcViewCount getRcPercent(BaseQueryCondition request, String orderType) throws Exception {
        RcViewCount rcView = new RcViewCount();
        BaseQueryConditionDTO dto = baseQueryConditionMapper.toDTO(request);
        Future<List<RcTimesDTO>> future1 = executorService.submit(new Callable<List<RcTimesDTO>>() {
            @Override
            public List<RcTimesDTO> call() throws Exception {
                return rcAnaylsisDao.aggreationRcView(dto);
            }
        });
        Future<List<RcTimesDTO>> future2 = executorService.submit(new Callable<List<RcTimesDTO>>() {
            @Override
            public List<RcTimesDTO> call() throws Exception {
                return rcAnaylsisDao.aggreationRcViewCorpAndIndustry(dto.getStartTime(), dto.getEndTime(), null, DataTypeEnum.CORP,
                        dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
            }
        });
        Future<List<RcTimesDTO>> future3 = executorService.submit(new Callable<List<RcTimesDTO>>() {
            @Override
            public List<RcTimesDTO> call() throws Exception {
                return rcAnaylsisDao.aggreationRcViewCorpAndIndustry(dto.getStartTime(), dto.getEndTime(), getIndustryList(request.getIndustryType()), DataTypeEnum.INDUSTRY,
                        dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
            }
        });
        List<RcTimesDTO> rcList = future1.get();
        List<RcTimesDTO> corpRcList = future2.get();
        List<RcTimesDTO> industryRcList = future3.get();
        if (CollectionUtils.isNotEmpty(rcList)) {
            RcTimesDTO rcTimesDTO = rcList.get(0);
            rcView.setRcTimes(rcTimesDTO.getRcTimes());
            rcView.setNoRcTimes(rcTimesDTO.getNoRcTimes());
            rcView.setRcTimesPercent(OrpReportUtils.divideWithPercent(new BigDecimal(rcView.getRcTimes()),
                    new BigDecimal(rcTimesDTO.getOrderCount())).doubleValue());
            rcView.setNoRcTimesPercent(OrpReportUtils.divideWithPercent(new BigDecimal(rcView.getNoRcTimes()),
                    new BigDecimal(rcTimesDTO.getOrderCount())).doubleValue());
        }
        if (CollectionUtils.isNotEmpty(corpRcList)) {
            RcTimesDTO rcTimesDTO = corpRcList.get(0);
            rcView.setCorpRcTimes(rcTimesDTO.getRcTimes());
            rcView.setCorpNoRcTimes(rcTimesDTO.getNoRcTimes());
            rcView.setCorpRcTimesPercent(OrpReportUtils.divideWithPercent(new BigDecimal(rcView.getCorpRcTimes()),
                    new BigDecimal(rcTimesDTO.getOrderCount())).doubleValue());
            rcView.setCorpNoRcTimesPercent(OrpReportUtils.divideWithPercent(new BigDecimal(rcView.getCorpNoRcTimes()),
                    new BigDecimal(rcTimesDTO.getOrderCount())).doubleValue());
        }
        if (CollectionUtils.isNotEmpty(industryRcList)) {
            RcTimesDTO rcTimesDTO = industryRcList.get(0);
            rcView.setIndustryRcTimes(rcTimesDTO.getRcTimes());
            rcView.setIndustryNoRcTimes(rcTimesDTO.getNoRcTimes());
            rcView.setIndustryRcTimesPercent(OrpReportUtils.divideWithPercent(new BigDecimal(rcView.getIndustryRcTimes()),
                    new BigDecimal(rcTimesDTO.getOrderCount())).doubleValue());
            rcView.setIndustryNoRcTimesPercent(OrpReportUtils.divideWithPercent(new BigDecimal(rcView.getIndustryNoRcTimes()),
                    new BigDecimal(rcTimesDTO.getOrderCount())).doubleValue());
        }
        return rcView;
    }

    
    @Override
    public List<RcViewReason> getRcViewReason(BaseQueryCondition request, String orderType, String lang)
            throws Exception {
        List<RcViewReason> result = new ArrayList();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request);
        TrainRcEnum[] rcEnums = TrainRcEnum.values();
        List<TrainRcTimesDTO> trainRcList = rcAnaylsisDao.aggreationRcViewReason(baseQueryConditionDto, TrainRcTimesDTO.class);
        TrainRcTimesDTO trainRcTimes = null;
        if (CollectionUtils.isNotEmpty(trainRcList)) {
            trainRcTimes = trainRcList.get(0);
        }
        for (TrainRcEnum rcEnum : rcEnums) {
            RcViewReason rcViewAnalysis = new RcViewReason();
            rcViewAnalysis.setRcTypeCode(rcEnum.toString());
            rcViewAnalysis.setRcTypeName(SharkUtils.getHeaderVal(rcEnum.getSharkKey(), lang));
            if (trainRcTimes == null) {
                continue;
            }
            rcViewAnalysis.setRcTimes(getRcTimes(rcEnum, trainRcTimes));
            rcViewAnalysis.setRcPercent(getRcPercent(rcEnum, trainRcTimes));
            if (rcViewAnalysis.getRcTimes() != null && rcViewAnalysis.getRcTimes() > 0) {
                List<RcDetailDTO> trainDetailList = rcAnaylsisDao.aggreationRcViewReasonDetail(baseQueryConditionDto, RcDetailDTO.class, rcEnum);
                List<RcViewReasonDetail> detailList = rcDetailMapper.toBOs(Optional.ofNullable(trainDetailList).orElse(new ArrayList<>()), getRcTimes(rcEnum, trainRcTimes));
                detailList.sort((obj1, obj2) -> {
                    return Optional.ofNullable(obj2.getRcTimes()).orElse(0).compareTo(Optional.ofNullable(obj1.getRcTimes()).orElse(0));
                });
                rcViewAnalysis.setRcViewReasonDetailList(detailList);
            }
            result.add(rcViewAnalysis);
        }
        result.sort((obj1, obj2) -> {
            return Optional.ofNullable(obj2.getRcTimes()).orElse(0).compareTo(Optional.ofNullable(obj1.getRcTimes()).orElse(0));
        });
        return result;
    }

    @Override
    public RcViewBu getRcViewBu(BaseQueryCondition request, String orderType) throws Exception {
        return null;
    }

    
    @Override
    public List<RcTrend> getRcTrend(BaseQueryCondition request, QueryReportAggDateDimensionEnum dateDimensionEnum,
                                    String orderType) throws Exception {
        List<RcTrend> result = new ArrayList<>();
        BaseQueryConditionDTO dto = baseQueryConditionMapper.toDTO(request);
        Set<String> points = BizUtils.getDatePoints(dto.getStartTime(), dto.getEndTime(), dateDimensionEnum);
        Future<List<RcTrendDTO>> future1 = executorService.submit(new Callable<List<RcTrendDTO>>() {
            @Override
            public List<RcTrendDTO> call() throws Exception {
                return rcAnaylsisDao.aggreationRcTrend(dto, dateDimensionEnum);
            }
        });
        Future<List<RcTrendDTO>> future2 = executorService.submit(new Callable<List<RcTrendDTO>>() {
            @Override
            public List<RcTrendDTO> call() throws Exception {
                return rcAnaylsisDao.aggreationRcTrendCorpAndIndustry(dto.getStartTime(), dto.getEndTime(),
                        dateDimensionEnum, null, DataTypeEnum.CORP,
                        dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
            }
        });
        Future<List<RcTrendDTO>> future3 = executorService.submit(new Callable<List<RcTrendDTO>>() {
            @Override
            public List<RcTrendDTO> call() throws Exception {
                return rcAnaylsisDao.aggreationRcTrendCorpAndIndustry(dto.getStartTime(), dto.getEndTime(),
                        dateDimensionEnum, getIndustryList(request.getIndustryType()), DataTypeEnum.INDUSTRY,
                        dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
            }
        });
        List<RcTrendDTO> rcTrendDTOList = future1.get();
        List<RcTrendDTO> corpRcTrendDTOList = future2.get();
        List<RcTrendDTO> industryRcTrendDTOList = future3.get();
        for (String date : points) {
            RcTrend rcTrend = new RcTrend();
            rcTrend.setPoint(date);
            if (CollectionUtils.isNotEmpty(rcTrendDTOList)) {
                if (rcTrendDTOList.stream().anyMatch(i -> StringUtils.equalsIgnoreCase(BizUtils.dateFormat(i.getDate(), dateDimensionEnum), date))) {
                    RcTrendDTO rcTrendDTO = rcTrendDTOList.stream()
                            .filter(i -> StringUtils.equalsIgnoreCase(BizUtils.dateFormat(i.getDate(), dateDimensionEnum), date)).findFirst().get();
                    rcTrend.setRcTimes(rcTrendDTO.getRcTimes());
                    rcTrend.setOrderCount(rcTrendDTO.getOrderCount());
                    rcTrend.setRcPercent(OrpReportUtils.divideWithPercent(new BigDecimal(rcTrendDTO.getRcTimes()),
                            new BigDecimal(rcTrendDTO.getOrderCount())).doubleValue());
                }
            }
            if (CollectionUtils.isNotEmpty(corpRcTrendDTOList)) {
                if (corpRcTrendDTOList.stream().anyMatch(i -> StringUtils.equalsIgnoreCase(BizUtils.dateFormat(i.getDate(), dateDimensionEnum), date))) {
                    RcTrendDTO rcTrendDTO = corpRcTrendDTOList.stream()
                            .filter(i -> StringUtils.equalsIgnoreCase(BizUtils.dateFormat(i.getDate(), dateDimensionEnum), date)).findFirst().get();
                    rcTrend.setCorpRcTimes(rcTrendDTO.getRcTimes());
                    rcTrend.setCorpOrderCount(rcTrendDTO.getOrderCount());
                    rcTrend.setCorpRcPercent(OrpReportUtils.divideWithPercent(new BigDecimal(rcTrendDTO.getRcTimes()),
                            new BigDecimal(rcTrendDTO.getOrderCount())).doubleValue());
                }
            }
            if (CollectionUtils.isNotEmpty(industryRcTrendDTOList)) {
                if (industryRcTrendDTOList.stream().anyMatch(i -> StringUtils.equalsIgnoreCase(BizUtils.dateFormat(i.getDate(), dateDimensionEnum), date))) {
                    RcTrendDTO rcTrendDTO = industryRcTrendDTOList.stream()
                            .filter(i -> StringUtils.equalsIgnoreCase(BizUtils.dateFormat(i.getDate(), dateDimensionEnum), date)).findFirst().get();
                    rcTrend.setIndustryRcTimes(rcTrendDTO.getRcTimes());
                    rcTrend.setIndustryOrderCount(rcTrendDTO.getOrderCount());
                    rcTrend.setIndustryRcPercent(OrpReportUtils.divideWithPercent(new BigDecimal(rcTrendDTO.getRcTimes()),
                            new BigDecimal(rcTrendDTO.getOrderCount())).doubleValue());
                }
            }
            result.add(rcTrend);
        }
        result.sort((obj1, obj2) -> {
            return obj1.getPoint().compareTo(obj2.getPoint());
        });
        return result;
    }

    @Override
    public List<Map> deptDetail(BaseQueryCondition request, AnalysisObjectEnum analysisObjectEnum, Pager pager,
                                String orderType, String user) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request);
        List result = rcAnaylsisDao.topRcAnalysis(analysisObjectEnum, baseQueryConditionDto, BizUtils.initPager(pager), orderType,user);
        fomratResultData(result);
        return result;
    }

    
    @Override
    public int count(BaseQueryCondition request, AnalysisObjectEnum analysisObjectEnum, String orderType, String user)
            throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request);
        return rcAnaylsisDao.topRcAnalysisCount(analysisObjectEnum, baseQueryConditionDto, orderType,user);
    }

    private int getRcTimes(TrainRcEnum trainRcEnum, TrainRcTimesDTO trainRcTimes) {
        int rcTimes = 0;
        switch (trainRcEnum) {
            case S:
                rcTimes = Optional.ofNullable(trainRcTimes.getSeatRcTimes()).orElse(0);
                break;
            case T:
                rcTimes = Optional.ofNullable(trainRcTimes.getTicketRcTimes()).orElse(0);
                break;
            case R:
                rcTimes = Optional.ofNullable(trainRcTimes.getRefundRcTimes()).orElse(0);
                break;
            default:
                break;
        }
        return rcTimes;
    }

    /**
     * 当前rc占总rc次数比
     *
     * @param trainRcEnum
     * @param trainRcTimes
     * @return
     */
    private double getRcPercent(TrainRcEnum trainRcEnum, TrainRcTimesDTO trainRcTimes) {
        double rcPercent = 0;
        switch (trainRcEnum) {
            case S:
                rcPercent = OrpReportUtils
                        .divideWithPercent(new BigDecimal(Optional.ofNullable(trainRcTimes.getSeatRcTimes()).orElse(0)),
                                new BigDecimal(Optional.ofNullable(trainRcTimes.getRcTimes()).orElse(0)))
                        .doubleValue();
                break;
            case T:
                rcPercent = OrpReportUtils
                        .divideWithPercent(new BigDecimal(Optional.ofNullable(trainRcTimes.getTicketRcTimes()).orElse(0)),
                                new BigDecimal(Optional.ofNullable(trainRcTimes.getRcTimes()).orElse(0)))
                        .doubleValue();
                break;
            case R:
                rcPercent = OrpReportUtils
                        .divideWithPercent(new BigDecimal(Optional.ofNullable(trainRcTimes.getRefundRcTimes()).orElse(0)),
                                new BigDecimal(Optional.ofNullable(trainRcTimes.getRcTimes()).orElse(0)))
                        .doubleValue();
                break;
            default:
                break;
        }
        return rcPercent;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.train;
    }

    @Override
    public List<RcStatisticalsEnum> getStatisticalList(QueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return RcStatisticalsEnum.getStaticalsByBizType(QueryReportBuTypeEnum.train.toString());
    }
}
