package com.corpgovernment.resource.schedule.onlinereport.supplier;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSupplierFltTop;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSupplierTopRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.SupplierFltTopInfo;
import com.corpgovernment.resource.schedule.onlinereport.AbstractDeptDetailCommonBiz;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.supplier.FltSupplierMonitorDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.supplier.convert.SupplierTrendMapper;
import com.corpgovernment.resource.schedule.onlinereport.supplier.dto.AgreementTrendAnalysisDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-09-06 10:35
 * @desc
 */
@Service
public class SupplierMonitorFltBiz extends AbstractDeptDetailCommonBiz {

    @Autowired
    private FltSupplierMonitorDaoImpl fltSupplierMonitorDaoImpl;

    @Autowired
    SupplierTrendMapper supplierTrendMapper;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;



    
    public SupplierFltTopInfo trend(OnlineReportSupplierTopRequest request) throws Exception {

        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        SupplierFltTopInfo result = new SupplierFltTopInfo();
        List<OnlineReportSupplierFltTop> topList = new ArrayList<>();
        result.setTopList(topList);
        List<String> listTa = null;
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        String dim = (String) map.get("dim");
        // 协议航司
        String contractType = (String) map.get("contractType");
        String flightCtiy = (String) map.get("flight_city");
        List<AgreementTrendAnalysisDTO> list = fltSupplierMonitorDaoImpl.queryTop(baseQueryConditionDTO, AgreementTrendAnalysisDTO.class, request.getProductType(),
                dim, StringUtils.equalsIgnoreCase("C", contractType), flightCtiy, request.getLang());
        if (StringUtils.equalsIgnoreCase("airline_cn_name", dim) || StringUtils.equalsIgnoreCase(
                "airline_en_name", dim)) {
            listTa = fltSupplierMonitorDaoImpl.queryAgreementAirlinesName(baseQueryConditionDTO, dim, request.getProductType());
        }
        list.sort((o1, o2) -> o2.getSumPrice().subtract(o1.getSumPrice()).intValue());

        AgreementTrendAnalysisDTO sumTop = new AgreementTrendAnalysisDTO();
        AgreementTrendAnalysisDTO sumOther = new AgreementTrendAnalysisDTO();
        AgreementTrendAnalysisDTO sumAll = new AgreementTrendAnalysisDTO();
        int topLimit = Optional.ofNullable(request.getTopLimit()).orElse(OrpConstants.TEN);
        if (CollectionUtils.isNotEmpty(list)){
            for (int i = 0; i < list.size(); i++){
                AgreementTrendAnalysisDTO deptConsume = list.get(i);
                selfAdd(sumAll, deptConsume);
                if (i < topLimit){
                    selfAdd(sumTop, deptConsume);
                    topList.add(convertToFltBO(deptConsume, listTa));
                }else {
                    // 超过topLimit以外的数据都聚合成“other”
                    selfAdd(sumOther, deptConsume);
                    result.setOtherInfo(convertToFltBO(sumOther, listTa));
                }
            }
            result.setSumInfo(convertToFltBO(sumAll, listTa));;
        }
        return result;
    }

    private void selfAdd(AgreementTrendAnalysisDTO sumTop, AgreementTrendAnalysisDTO deptConsume){
        sumTop.setSumAmount(deptConsume.getSumAmount().add(Optional.ofNullable(sumTop.getSumAmount()).orElse(BigDecimal.ZERO)));
        sumTop.setSumQuantity(deptConsume.getSumQuantity() + Optional.ofNullable(sumTop.getSumQuantity()).orElse(OrpConstants.ZERO));
        sumTop.setSumPrice(deptConsume.getSumPrice().add(Optional.ofNullable(sumTop.getSumPrice()).orElse(BigDecimal.ZERO)));
        sumTop.setForecastAmount(Optional.ofNullable(deptConsume.getForecastAmount()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getForecastAmount()).orElse(BigDecimal.ZERO)));
    }

    private OnlineReportSupplierFltTop convertToFltBO(AgreementTrendAnalysisDTO topDeptDTO, List<String> listTa){
        OnlineReportSupplierFltTop fltTopDeptBO = new OnlineReportSupplierFltTop();
        fltTopDeptBO.setDim(topDeptDTO.getDim());
        fltTopDeptBO.setSumAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getSumAmount()));
        fltTopDeptBO.setSumPrice(OrpReportUtils.formatBigDecimal(topDeptDTO.getSumPrice()));
        fltTopDeptBO.setSumQuantity(topDeptDTO.getSumQuantity());
        fltTopDeptBO.setAgreementTag(isAgreement(topDeptDTO.getDim(), listTa));
        fltTopDeptBO.setForecastAmount(topDeptDTO.getForecastAmount());
        return fltTopDeptBO;
    }

    private Integer isAgreement(String airline, List<String> list){
        if (CollectionUtils.isNotEmpty(list) && StringUtils.isNotEmpty(airline)){
            return Optional.ofNullable(list).orElse(new ArrayList<>()).stream().anyMatch(i-> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i,airline)) ? 1 : 0;
        }else {
            return 0;
        }
    }
}
