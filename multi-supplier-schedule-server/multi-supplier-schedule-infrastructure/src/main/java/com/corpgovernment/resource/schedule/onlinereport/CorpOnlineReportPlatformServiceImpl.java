package com.corpgovernment.resource.schedule.onlinereport;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.ExtFiledEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.ReportLibTakeawayIndicatorConfigBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.CostCenterAndDepartmentSearchRequestType;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.CarbonsBiz;
import com.corpgovernment.resource.schedule.onlinereport.hotanalysis.HotAnalysis;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TravelPositionStepEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.*;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.DeptAnalysisDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.travelposition.TravelPositionCityDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.travelposition.TravelPositionDetailDTO;
import com.corpgovernment.resource.schedule.onlinereport.behavior.BehaviorAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.behavior.CarBehaviorAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.behavior.FlightBehaviorAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.behavior.HotelBehaviorAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.behavior.TrainBehaviorAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.compliance.RcAnalysis;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.consume.GenralConsumeBiz;
import com.corpgovernment.resource.schedule.onlinereport.consume.GenralConsumeOrdercntBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.DeptDetailAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.FactoryList;
import com.corpgovernment.resource.schedule.onlinereport.dept.Top5DeptBusConsumeBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.Top5DeptCarConsumeBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.Top5DeptConsumeBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.Top5DeptFltConsumeBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.Top5DeptHtlConsumeBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.Top5DeptOtherAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.Top5DeptOverviewConsumeBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.Top5DeptTrainConsumeBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.Top5DeptVasoConsumeBiz;
import com.corpgovernment.resource.schedule.onlinereport.dept.Top5DeptWelfareConsumeBiz;
import com.corpgovernment.resource.schedule.onlinereport.enums.GeoCategoryEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.ResponseCodeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.save.FlightIndustrySharkEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.travelposition.TravelPositionBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.travelposition.TravelPositionLegendEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.travelposition.TravelPositionQueryKeyEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.travelposition.TravelPositionStepTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.factory.DetailFactory;
import com.corpgovernment.resource.schedule.onlinereport.factory.OnlineDetailExecute;
import com.corpgovernment.resource.schedule.onlinereport.geo.GeoLocationLanguageBiz;
import com.corpgovernment.resource.schedule.onlinereport.hotanalysis.HotAnalysis;
import com.corpgovernment.resource.schedule.onlinereport.module.geo.GeoLocationDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.geo.MultiLangDTO;
import com.corpgovernment.resource.schedule.onlinereport.multilanguage.HtlMultiLangBiz;
import com.corpgovernment.resource.schedule.onlinereport.page.PageResult;
import com.corpgovernment.resource.schedule.onlinereport.partition.PartitionBiz;
import com.corpgovernment.resource.schedule.onlinereport.position.PositionBiz;
import com.corpgovernment.resource.schedule.onlinereport.position.TravelPositionDetailBiz;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.CarbonsBiz;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.cityhotel.CityHotelBiz;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.consumeprofile.ConsumeProfileBiz;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.HtlOrderAuditBiz;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.OrderDetailBiz;
import com.corpgovernment.resource.schedule.onlinereport.save.FlightSaveAndLossBiz;
import com.corpgovernment.resource.schedule.onlinereport.save.FlightSaveAndLossDetailBiz;
import com.corpgovernment.resource.schedule.onlinereport.save.HotelSaveAndLossBiz;
import com.corpgovernment.resource.schedule.onlinereport.save.OverviewSaveAndLossBiz;
import com.corpgovernment.resource.schedule.onlinereport.saveanalysis.SaveAnalysisTrend;
import com.corpgovernment.resource.schedule.onlinereport.saveanalysis.impl.PotentialSaveAnalysisDizBiz;
import com.corpgovernment.resource.schedule.onlinereport.saveanalysis.impl.SaveAnalysisDisBiz;
import com.corpgovernment.resource.schedule.onlinereport.settlement.SettlementDataBiz;
import com.corpgovernment.resource.schedule.onlinereport.supplier.SupplierMonitorBiz;
import com.corpgovernment.resource.schedule.onlinereport.travelmark.TravelMarkAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.trend.GeneralTrendBiz;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpGsonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.ThreadPartitionContextUtils;
import com.corpgovernment.resource.schedule.usertemplate.mysql.entity.CorpReportLibIndicatorConfigDo;
import com.corpgovernment.resource.schedule.usertemplate.mysql.mapper.CorpReportLibIndicatorConfigMapper;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2020/7/6 16:29
 * @Desc
 */
@Service
@Slf4j
public class CorpOnlineReportPlatformServiceImpl implements ICorpOnlineReportPlatformService {


    private static final Object NULL = null;
    @Autowired
    SettlementDataBiz settlementDataBiz;
    @Autowired
    @Qualifier("saveAnalysisDetailBiz")
    SaveAnalysisTrend saveAnalysisDetailBiz;
    @Autowired
    @Qualifier("saveAnalysisTrendBiz")
    SaveAnalysisTrend saveAnalysisTrendBiz;
    @Autowired
    private PartitionBiz partitionBiz;
    @Autowired
    private Top5DeptConsumeBiz top5DeptConsume;
    @Autowired
    private FactoryList<DeptDetailAnalysisBiz<OnlineReportDeptDetailAnalysisRequest>, SubQueryReportBuTypeEnum> deptDetailAnalysisBizs;
    @Autowired
    private FactoryList<RcAnalysis<BaseQueryCondition>, QueryReportBuTypeEnum> rcAnalyses;
    @Autowired
    private FactoryList<GenralConsumeBiz<OnlineReportConsumeRequest>, QueryReportBuTypeEnum> genralConsumeBizs;
    @Autowired
    private Top5DeptFltConsumeBiz top5DeptFltConsumeBiz;
    @Autowired
    private Top5DeptHtlConsumeBiz top5DeptHtlConsumeBiz;
    @Autowired
    private Top5DeptTrainConsumeBiz top5DeptTrainConsumeBiz;
    @Autowired
    private Top5DeptCarConsumeBiz top5DeptCarConsumeBiz;
    @Autowired
    private Top5DeptOverviewConsumeBiz top5DeptOverviewConsumeBiz;
    @Autowired
    private Top5DeptBusConsumeBiz top5DeptBusConsumeBiz;
    @Autowired
    private Top5DeptVasoConsumeBiz top5DeptVasoConsumeBiz;
    @Autowired
    private Top5DeptWelfareConsumeBiz top5DeptWelfareConsumeBiz;
    @Autowired
    private GenralConsumeOrdercntBiz genralConsumeOrdercntBiz;
    @Autowired
    private DetailFactory detailFactory;

    @Autowired
    private FactoryList<GeneralTrendBiz<OnlineReportTrendRequest>, QueryReportBuTypeEnum> genralTrendBizs;

    @Autowired
    private FactoryList<HotAnalysis<OnlineReportHotAanlysisRequest>, QueryReportBuTypeEnum> hotAnalysisBiz;

    @Autowired
    private TravelMarkAnalysisBiz travelMarkMetric;

    @Autowired
    private BehaviorAnalysisBiz behaviorAnalysisBiz;

    @Autowired
    private FlightBehaviorAnalysisBiz flightBehaviorAnalysisBiz;

    @Autowired
    private TrainBehaviorAnalysisBiz trainBehaviorAnalysisBiz;

    @Autowired
    private FlightSaveAndLossBiz flightSaveAndLossBiz;

    @Autowired
    private HotelSaveAndLossBiz hotelSaveAndLossBiz;

    @Autowired
    private OverviewSaveAndLossBiz overviewSaveAndLossBiz;

    @Autowired
    private SaveAnalysisDisBiz saveAnalysisDisBiz;

    @Autowired
    private FlightSaveAndLossDetailBiz saveAndLossDetailBiz;

    @Autowired
    private FactoryList<OrderDetailBiz<OnlineReportOrderDetailRequest>, QueryReportBuTypeEnum> factoryList;

    @Autowired
    private HtlOrderAuditBiz htlOrderAuditBiz;

    @Autowired
    private CityHotelBiz cityHotelBiz;

    @Autowired
    private CarBehaviorAnalysisBiz carBehaviorAnalysisBiz;

    @Autowired
    private HotelBehaviorAnalysisBiz hotelBehaviorAnalysisBiz;

    @Autowired
    private PotentialSaveAnalysisDizBiz potentialSaveAnalysisDizBiz;

    @Autowired
    private PositionBiz positionBiz;

    @Autowired
    private TravelPositionDetailBiz travelPositionDetailBiz;

    @Autowired
    private GeoLocationLanguageBiz geoLocationLanguageBiz;

    @Autowired
    private HtlMultiLangBiz htlMultiLangBiz;
    @Autowired
    private Top5DeptOtherAnalysisBiz top5DeptOtherAnalysisBiz;
    @Autowired
    @Qualifier("potentialSaveAnalysisTrendBiz")
    private SaveAnalysisTrend potentialSaveAnalysisTrendBiz;

    @Autowired
    @Qualifier("potentialSaveAnalysisDetailBiz")
    private SaveAnalysisTrend potentialSaveAnalysisDetailBiz;

    @Autowired
    private ConsumeProfileBiz consumeProfileBiz;
    @Qualifier("screenThreadPoolExecutor")
    @Resource
    private ThreadPoolExecutor executorService;
    @Resource
    private OnlineReportDao onlineReportDao;

    @Autowired
    private SupplierMonitorBiz supplierMonitorBiz;

    @Autowired
    private CarbonsBiz carbonsBiz;
    @Autowired
    private CorpReportLibIndicatorConfigMapper corpReportLibIndicatorConfigMapper;


    public static TravelPositionLegendEnum[] removeElement(TravelPositionLegendEnum[] arr, int index) {
        // 创建一个新的数组，长度比原数组小1
        TravelPositionLegendEnum[] newArr = new TravelPositionLegendEnum[arr.length - 1];
        // 遍历原数组，将不需要移除的元素复制到新数组中
        for (int i = 0, j = 0; i < arr.length; i++) {
            if (i != index) {
                newArr[j++] = arr[i];
            }
        }
        // 返回新数组
        return newArr;
    }

    @Override
    public OnlineReportRebookTrendResponse queryRebookTrendAnalysis(OnlineReportRebookTrendRequest request) {
        OnlineReportRebookTrendResponse response = new OnlineReportRebookTrendResponse();
        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            String from = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()).get("from");
            // reBook 和 rebookTrendAnalysis区别是rebook概览和趋势没有一起返回，rebookTrendAnalysis概览和趋势一起返回,并且rebookTrendAnalysis的该来没有行业和商旅数据
            if (StringUtils.equalsIgnoreCase(from, "pc")) {
                if (QueryReportBuTypeEnum.flight == request.getQueryBu()) {
                    response.setRebookInfo(flightBehaviorAnalysisBiz.reBook(request));
                } else if (QueryReportBuTypeEnum.train == request.getQueryBu()) {
                    response.setRebookInfo(trainBehaviorAnalysisBiz.reBook(request));
                }
            } else {
                if (QueryReportBuTypeEnum.overview == request.getQueryBu()) {
                    response.setRebookPercentInfo(behaviorAnalysisBiz.rebookPercentAnalysis(request));
                } else {
                    response.setRebookInfo(behaviorAnalysisBiz.rebookTrendAnalysis(request));
                }
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryRebookTrendAnalysis error", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportFltDiscountRangeResponse queryFltDiscountRangeAnalysis(OnlineReportFltDiscountRangeRequest request) {
        OnlineReportFltDiscountRangeResponse response = new OnlineReportFltDiscountRangeResponse();
        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setDiscountRangeList(behaviorAnalysisBiz.flightDiscountAnalysis(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryFltDiscountRangeAnalysis error", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportBookTypeResponse queryBookTypeAnalysis(OnlineReportBookTypeRequest request) {
        OnlineReportBookTypeResponse response = new OnlineReportBookTypeResponse();
        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setBookTypeList(behaviorAnalysisBiz.bookTypeAnalysis(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryBookTypeAnalysis error", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportBehaviorAnalysisResponse queryBehaviorAnalysis(OnlineReportBehaviorAnalysisRequest request) {
        OnlineReportBehaviorAnalysisResponse response = new OnlineReportBehaviorAnalysisResponse();

        try {
            // 不区分uid,提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            if (QueryReportBuTypeEnum.flight == request.getQueryBu()) {
                response.setFlightBehaviorList(flightBehaviorAnalysisBiz.behaviorInfos(request));
            } else if (QueryReportBuTypeEnum.hotel == request.getQueryBu()) {
                response.setHotelBehaviorList(hotelBehaviorAnalysisBiz.behaviorInfos(request));
            } else if (QueryReportBuTypeEnum.train == request.getQueryBu()) {
                response.setTrainBehaviorList(trainBehaviorAnalysisBiz.behaviorInfos(request));
            } else if (QueryReportBuTypeEnum.car == request.getQueryBu()) {
                response.setCarBehaviorList(carBehaviorAnalysisBiz.behaviorInfos(request));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryBehaviorAnalysis error", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportHtlCityResponse queryHtlCityList(OnlineReportHtlCityRequest request) {
        OnlineReportHtlCityResponse onlineReportHtlCityResponse = new OnlineReportHtlCityResponse();
        onlineReportHtlCityResponse.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
        return onlineReportHtlCityResponse;
    }

    @Override
    public OnlineReportMarkMetricTrendResponse queryMarkMetricTrend(OnlineReportMarkMetricTrendRequest request) {
        OnlineReportMarkMetricTrendResponse response = new OnlineReportMarkMetricTrendResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            Map extMap = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
            if (StringUtils.equalsIgnoreCase((String) extMap.get("aggType"), QueryReportAggTypeEnum.accumulated.toString())) {
                response.setMarkMetricInfo(travelMarkMetric.travelMarkMetric(request));
            } else if (StringUtils.equalsIgnoreCase((String) extMap.get("yoyAndMom"), "T")) {
                response.setYoyAndMom(travelMarkMetric.travelMarkMetricYoyAndMom(request));
            } else {
                MarkMetricTrendAndOverview markMetricTrendAndOverview = travelMarkMetric.travelMarkMetricTrend(request);
                response.setMarkMetric(travelMarkMetric.travelMarkMetricTrend(request));
                response.setMarkMetricTrend(Optional.ofNullable(markMetricTrendAndOverview).orElse(new MarkMetricTrendAndOverview()).getMarkMetricTrend());
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryMarkMetricTrend:", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportConsumeOrdercntResponse queryReportConsumeOrdercnt(OnlineReportConsumeOrdercntRequest request) throws Exception {
        OnlineReportConsumeOrdercntResponse response = new OnlineReportConsumeOrdercntResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            OnlineReportConsumeOrdercnt onlineReportConsume = genralConsumeOrdercntBiz.aggreationGenralConsumeOrdercnt(request);
            response.setReportConsume(onlineReportConsume);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryReportConsumeOrdercnt>>>Resp", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportConsumeResponse queryReportConsume(OnlineReportConsumeRequest request) throws Exception {
        OnlineReportConsumeResponse response = new OnlineReportConsumeResponse();
        GenralConsumeBiz genralConsumeBiz = genralConsumeBizs.getBean(request.getQueryBu());

        try {

            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            OnlineReportConsume onlineReportConsume = genralConsumeBiz.aggreationGenralConsume(request);
            response.setReportConsume(onlineReportConsume);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryReportConsume>>>Resp", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportDetailResponse queryReportDetail(OnlineReportDetailRequest request) throws Exception {

        try {

            OnlineDetailExecute execute = detailFactory.execute(request);
            OnlineReportData data = execute.queryDetailRecord(request);
            return new OnlineReportDetailResponse(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc(),
                    data, Maps.newHashMap());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            return new OnlineReportDetailResponse(ResponseCodeEnum.FAILED.getCode(), ResponseCodeEnum.FAILED.getDesc(),
                    mapEmptyData(), Maps.newHashMap());
        }
    }

    private OnlineReportData mapEmptyData() {
        return new OnlineReportData();
    }

    @Override
    public OnlineReportRefundTrendResponse queryRefundTrendAnalysis(OnlineReportRefundTrendRequest request) {
        OnlineReportRefundTrendResponse response = new OnlineReportRefundTrendResponse();
        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            String from = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()).get("from");
            if (StringUtils.equalsIgnoreCase(from, "pc")) {
                if (QueryReportBuTypeEnum.flight == request.getQueryBu()) {
                    response.setRefundInfo(flightBehaviorAnalysisBiz.refund(request));
                } else if (QueryReportBuTypeEnum.train == request.getQueryBu()) {
                    response.setRefundInfo(trainBehaviorAnalysisBiz.refund(request));
                }
            } else {
                if (QueryReportBuTypeEnum.overview == request.getQueryBu()) {
                    response.setRefundPercentInfo(behaviorAnalysisBiz.refundPercentAnalysis(request));
                } else {
                    response.setRefundInfo(behaviorAnalysisBiz.refundTrendAnalysis(request));
                }
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryRefundTrendAnalysis>>>Resp", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }


/*    @Override
    public OnlineMinDateResponse queryMinDate(OnlineMinDateRequest var1) throws Exception {
        try {
            OnlineMinDateResponse onlineMinDateResponse = onlineReportNewServiceAPIClient.queryMinDate(var1);

            return onlineMinDateResponse;
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }*/

    /*






      @Override
      public OnlineReportNoteResponse updateReportNote(OnlineReportNoteRequest var1) throws Exception {
          try {
              return onlineReportNewServiceAPIClient.updateNote(var1);
          } catch (Exception e) {
              throw new Exception(e.getMessage(), e);
          }
      }

      @Override
      public OnlineReportNoteListResponse queryReportNote(OnlineReportNoteListRequest var1) throws Exception {
          try {
              return onlineReportNewServiceAPIClient.queryNote(var1);
          } catch (Exception e) {
              throw new Exception(e.getMessage(), e);
          }
      }
  */

    @Override
    public OnlineReportDeptUidConsumeDetailResponse queryDeptUidConsumeDetail(OnlineReportDeptUidConsumeDetailRequest request) {
        OnlineReportDeptUidConsumeDetailResponse response = new OnlineReportDeptUidConsumeDetailResponse();
        try {
            // 不区分uid,提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                response.setDeptUidConsumeDetailInfo(top5DeptFltConsumeBiz.topDeptUidConsume(request));
            } else if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                response.setDeptUidConsumeDetailInfo(top5DeptHtlConsumeBiz.topDeptUidConsume(request));
            } else if (request.getQueryBu() == QueryReportBuTypeEnum.train) {
                response.setDeptUidConsumeDetailInfo(top5DeptTrainConsumeBiz.topDeptUidConsume(request));
            } else if (request.getQueryBu() == QueryReportBuTypeEnum.car) {
                response.setDeptUidConsumeDetailInfo(top5DeptCarConsumeBiz.topDeptUidConsume(request));
            } else if (request.getQueryBu() == QueryReportBuTypeEnum.overview) {
                response.setDeptUidConsumeDetailInfo(top5DeptOverviewConsumeBiz.topDeptUidConsume(request));
            } else if (request.getQueryBu() == QueryReportBuTypeEnum.bus) {
                response.setDeptUidConsumeDetailInfo(top5DeptBusConsumeBiz.topDeptUidConsume(request));
            } else if (request.getQueryBu() == QueryReportBuTypeEnum.vaso) {
                response.setDeptUidConsumeDetailInfo(top5DeptVasoConsumeBiz.topDeptUidConsume(request));
            } else if (QueryReportBuTypeEnum.welfare.equals(request.getQueryBu())) {
                response.setDeptUidConsumeDetailInfo(top5DeptWelfareConsumeBiz.deptUidConsumeDetailInfo(request));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTrendResponse queryReportTrend(OnlineReportTrendRequest request) throws Exception {
        OnlineReportTrendResponse response = new OnlineReportTrendResponse();
        OnlineReportTrendData data = new OnlineReportTrendData();
        GeneralTrendBiz trendBiz = genralTrendBizs.getBean(request.getQueryBu());

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            List<OnlineReportTrendPoint> trendPoints = trendBiz.trendBody(request);
            List<OnlineReportTrendLegend> trendLegends = trendBiz.trendLegend(request);
            data.setLegends(trendLegends);
            data.setData(trendPoints);
            response.setData(data);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTopFiveDeptConsumeResponse queryTopFiveDeptConsume(OnlineReportTopFiveDeptConsumeRequest request) throws Exception {
        OnlineReportTopFiveDeptConsumeResponse response = new OnlineReportTopFiveDeptConsumeResponse();

        try {

            // 不区分uid,提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setDeptCousumeList(top5DeptConsume.aggreationGenralConsume(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryTopFiveDeptConsume error", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

/*    @Override
    public OnlineReportTopFiveDeptAnalysisResponse queryTopFiveDeptAnalysis(OnlineReportTopFiveDeptAnalysisRequest var1) throws Exception {
        try {
            return onlineReportNewServiceAPIClient.queryTopFiveDeptAnalysis(var1);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public OnlineReportDeptDetailAnalysisResponse queryDeptDetailHeader(OnlineReportDeptDetailAnalysisRequest var1) throws Exception {
        try {
            return onlineReportNewServiceAPIClient.queryDeptDetailHeader(var1);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }*/

    @Override
    public OnlineReportTopFiveDeptAnalysisResponse queryTopFiveDeptAnalysis(OnlineReportTopFiveDeptAnalysisRequest request) throws Exception {
        OnlineReportTopFiveDeptAnalysisResponse response = new OnlineReportTopFiveDeptAnalysisResponse();

        try {

            // 不区分uid,提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setDeptAnalysisList(top5DeptOtherAnalysisBiz.deptOtherAnalysis(request));
            response.setCorpVal(top5DeptOtherAnalysisBiz.deptCorpAnalysis(request));
            response.setTotalRecords(top5DeptOtherAnalysisBiz.count(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

  /*  @Override
    public OnlineReportRiskOrderSummaryResponse queryRiskOrderSummary(OnlineReportRiskOrderSummaryRequest var1) throws Exception {
        try {
            return onlineReportNewServiceAPIClient.queryReportRiskOrderSummary(var1);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public OnlineReportFlightRiskOrderDetailResponse queryRiskFlightOrderDetail(OnlineReportRiskOrderDetailRequest var1) throws Exception {
        try {
            return onlineReportNewServiceAPIClient.queryReportRiskFlightOrder(var1);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public OnlineReportHotelRiskOrderDetailResponse queryRiskHotelOrderDetail(OnlineReportRiskOrderDetailRequest var1) throws Exception {
        try {
            return onlineReportNewServiceAPIClient.queryReportRiskHotelOrder(var1);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public OnlineReportHotelUnderStayRiskOrderDetailResponse queryRiskHotelUnderStayOrderDetail(OnlineReportRiskOrderDetailRequest var1) throws Exception {
        try {
            return onlineReportNewServiceAPIClient.queryReportRiskHotelUnderStayOrder(var1);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }

    @Override
    public OnlineReportCarRiskOrderDetailResponse queryRiskCarOrderDetail(OnlineReportRiskOrderDetailRequest var1) throws Exception {
        try {
            return onlineReportNewServiceAPIClient.queryReportRiskCarOrder(var1);
        } catch (Exception e) {
            throw new Exception(e.getMessage(), e);
        }
    }*/

    @Override
    public OnlineReportDeptDetailAnalysisResponse queryDeptDetailAnalysis(OnlineReportDeptDetailAnalysisRequest request) {
        OnlineReportDeptDetailAnalysisResponse response = new OnlineReportDeptDetailAnalysisResponse();
        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum = SubQueryReportBuTypeEnum.getByOrdinal(request.getSubQueryBu());
        DeptDetailAnalysisBiz deptDetailAnalysisBiz = deptDetailAnalysisBizs.getBean(SubQueryReportBuTypeEnum.getByOrdinal(request.getSubQueryBu()));

        try {
            // 非蓝色空间才返回头部信息
            if (!BlueSpaceUtils.isForeign(request.getBasecondition().getPos(), request.getBasecondition().getBlueSpace())) {

                response.setHeaderData(deptDetailAnalysisBiz.getHearder(request.getAnalysisObjectEnum(), request.getLang(), subQueryReportBuTypeEnum));
                if (subQueryReportBuTypeEnum == SubQueryReportBuTypeEnum.ALL) {
                    DeptAnalysisDTO dto = deptDetailAnalysisBiz.deptDetailWithCount(request);
                    List listMap = (List) dto.getData();
                    List listStr = (List) listMap.stream().map(i -> OrpGsonUtils.toJsonNullStr(i)).collect(Collectors.toList());
                    response.setDeptDetail(listStr);
                    response.setTotalRecords(dto.getCount());
                } else {
                    List listMap = deptDetailAnalysisBiz.deptDetail(request);
                    List listStr = (List) listMap.stream().map(i -> OrpGsonUtils.toJsonNullStr(i)).collect(Collectors.toList());
                    response.setDeptDetail(listStr);
                    response.setTotalRecords(deptDetailAnalysisBiz.count(request));
                }
            } else {
                List listMap;
                if (ConfigUtils.blueSpaceDeptAnalysisMulitThread()) {
                    listMap = deptDetailAnalysisBiz.deptDetailv2(request);
                } else {
                    listMap = deptDetailAnalysisBiz.deptDetail(request);
                }
                List listStr = (List) listMap.stream().map(i -> OrpGsonUtils.toJsonNullStr(i)).collect(Collectors.toList());
                response.setDeptDetail(listStr);
                response.setTotalRecords(deptDetailAnalysisBiz.count(request));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportRcViewResponse queryReportRcViewData(OnlineReportRcViewRequest request) {
        OnlineReportRcViewResponse response = new OnlineReportRcViewResponse();
        RcAnalysis genralConsumeBiz = rcAnalyses.getBean(request.getQueryBu());

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            RcView rcView = genralConsumeBiz.getRcView(request.getBasecondition(), request.getProductType());
            response.setRcView(rcView);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryReportRcViewData:", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportRcTrendResponse queryReportRcTrend(OnlineReportRcTrendRequest request) {
        OnlineReportRcTrendResponse response = new OnlineReportRcTrendResponse();
        RcAnalysis genralConsumeBiz = rcAnalyses.getBean(request.getQueryBu());

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            List<RcTrend> rcTrendList = genralConsumeBiz.getRcTrend(request.getBasecondition(),
                    request.getDateDimension(), request.getProductType());
            response.setRcTrendList(rcTrendList);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryReportRcTrend:", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportRcViewCountResponse queryReportRcViewCountData(OnlineReportRcViewRequest request) {
        OnlineReportRcViewCountResponse response = new OnlineReportRcViewCountResponse();
        RcAnalysis genralConsumeBiz = rcAnalyses.getBean(request.getQueryBu());

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            RcViewCount rcViewCount =
                    genralConsumeBiz.getRcPercent(request.getBasecondition(), request.getProductType());
            response.setRcViewCount(rcViewCount);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryReportRcViewCountData:", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportRcViewReasonResponse queryReportRcViewReasonData(OnlineReportRcViewRequest request) {
        OnlineReportRcViewReasonResponse response = new OnlineReportRcViewReasonResponse();
        RcAnalysis genralConsumeBiz = rcAnalyses.getBean(request.getQueryBu());
        String lang = request.getLang();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            if (request.getQueryBu() == QueryReportBuTypeEnum.overview) {
                response.setRcViewBu(genralConsumeBiz.getRcViewBu(request.getBasecondition(), request.getProductType()));
            } else {
                response.setRcViewReasonList(genralConsumeBiz.getRcViewReason(request.getBasecondition(), request.getProductType(), lang));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryReportRcViewReasonData", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTopRcAnalysisResponse queryReportTopRcAnalysis(OnlineReportTopRcAnalysisRequest request) {
        OnlineReportTopRcAnalysisResponse response = new OnlineReportTopRcAnalysisResponse();
        RcAnalysis genralConsumeBiz = rcAnalyses.getBean(request.getQueryBu());

        try {
            AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
            Pager pager = request.getPage();
            List listMap = genralConsumeBiz.deptDetail(request.getBasecondition(), analysisObjectEnum, pager,
                    request.getProductType(), request.getUser());
            listMap.forEach(map -> {
                if (map instanceof Map) {
                    // 删除ACCOUNT数据
                    Map map1 = (Map) map;
                    map1.remove("ACCOUNT");
                }
            });

            List listStr = (List) listMap.stream().map(i -> OrpGsonUtils.toJsonNullStr(i)).collect(Collectors.toList());
            response.setDeptDetail(listStr);
            List hearder = genralConsumeBiz.getHearder(request.getAnalysisObjectEnum(), request.getLang(), request.getQueryBu());


            response.setHeaderData(hearder);
            response.setTotalRecords(genralConsumeBiz.count(request.getBasecondition(), analysisObjectEnum, request.getProductType(), request.getUser()));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryReportTopRcAnalysis", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportSaveGeneralResponse querySaveGeneralInfo(OnlineReportSaveGeneralRequest request) {
        OnlineReportSaveGeneralResponse response = new OnlineReportSaveGeneralResponse();
        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            Map extData = request.getExtData();
            String index = (String) extData.get("index");
            if (StringUtils.equalsIgnoreCase("GENERAL_SAVE_BU", index)) {
                response.setBuSaveInfo(overviewSaveAndLossBiz.saveGeneral(request));
            } else if (StringUtils.equalsIgnoreCase("GENERAL_SAVE", index)) {
                log.info("querySaveGeneralInfo:{}", JsonUtils.toJsonString(request));
                if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                    response.setSaveInfo(flightSaveAndLossBiz.saveGeneral(request));
                } else if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                    response.setSaveInfo(hotelSaveAndLossBiz.saveGeneral(request));
                }
            } else if (StringUtils.equalsIgnoreCase("GENERAL_SAVE_DISTRIBUTION", index)) {
                if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                    response.setSaveDistributionInfo(flightSaveAndLossBiz.saveDistributionGeneral(request));
                } else if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                    response.setSaveDistributionInfo(hotelSaveAndLossBiz.saveDistributionGeneral(request));
                } else {
                    BuSaveDistributionInfo buSaveDistributionInfo = new BuSaveDistributionInfo();
                    buSaveDistributionInfo.setFlightSaveDis(flightSaveAndLossBiz.saveDistributionGeneral(request));
                    buSaveDistributionInfo.setHotelSaveDis(hotelSaveAndLossBiz.saveDistributionGeneral(request));
                    response.setBuSaveDisInfo(buSaveDistributionInfo);
                }
            } else if (StringUtils.equalsIgnoreCase("GENERAL_SAVE_POTENTIAL", index)) {
                if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                    response.setPotentialSaveInfo(flightSaveAndLossBiz.potentialSaveGeneral(request));
                } else if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                    response.setPotentialSaveInfo(hotelSaveAndLossBiz.potentialSaveGeneral(request));
                }
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("querySaveGeneralInfo", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportPromotionsSaveDetailResponse queryPromotionsSaveDetail(OnlineReportPromotionsSaveDetailRequest request) {
        OnlineReportPromotionsSaveDetailResponse response = new OnlineReportPromotionsSaveDetailResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setSavelist(hotelSaveAndLossBiz.promotionsSaveDetail(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryPromotionsSaveDetail", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportSaveProportionResponse querySaveProportion(OnlineReportSaveProportionRequest request) {
        OnlineReportSaveProportionResponse response = new OnlineReportSaveProportionResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            List<OnlineReportProportion> proportions = saveAnalysisDisBiz.getSaveDis(request);
            List<OnlineReportTrendLegend> legends = saveAnalysisDisBiz.getSaveDisLegend(request);
            if (proportions.size() != legends.size()) {
                legends.remove(1);
            }
            response.setLegends(legends);
            response.setProportions(proportions);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportSaveProportionDetailResponse querySaveProportionDetail(OnlineReportSaveProportionDetailRequest request) {
        OnlineReportSaveProportionDetailResponse response = new OnlineReportSaveProportionDetailResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setProportionDetails(saveAnalysisDisBiz.getSaveDisDetail(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTrendResponse queryReportSaveTrend(OnlineReportTrendRequest request) {
        OnlineReportTrendResponse response = new OnlineReportTrendResponse();
        OnlineReportTrendData data = new OnlineReportTrendData();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            List<OnlineReportTrendPoint> trendPoints = saveAnalysisTrendBiz.trendBody(request);
            List<OnlineReportTrendLegend> trendLegends = saveAnalysisTrendBiz.trendLegend(request);
            data.setLegends(trendLegends);
            data.setData(trendPoints);
            response.setData(data);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTrendResponse queryReportSaveDetail(OnlineReportTrendRequest request) {
        OnlineReportTrendResponse response = new OnlineReportTrendResponse();
        OnlineReportTrendData data = new OnlineReportTrendData();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            List<OnlineReportTrendPoint> trendPoints = saveAnalysisDetailBiz.trendBody(request);
            List<OnlineReportTrendLegend> trendLegends = saveAnalysisDetailBiz.trendLegend(request);
            data.setLegends(trendLegends);
            data.setData(trendPoints);
            response.setData(data);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryReportSaveDetail", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportPotentialSaveLowRcResponse queryPotentialSaveLowRcDetail(OnlineReportPotentialSaveLowRclRequest request) {
        OnlineReportPotentialSaveLowRcResponse response = new OnlineReportPotentialSaveLowRcResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                response.setLowRcList(flightSaveAndLossBiz.lowRcAnalysis(request));
            }
            if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                response.setLowRcList(hotelSaveAndLossBiz.lowRcAnalysis(request));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryPotentialSaveLowRcDetail", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTopPotentialSaveResponse queryTopPotentialSaveDetail(OnlineReportTopPotentialSaveRequest request) {
        OnlineReportTopPotentialSaveResponse response = new OnlineReportTopPotentialSaveResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
                Pager pager = request.getPage();
                List listMap = flightSaveAndLossBiz.deptDetail(request.getBasecondition(), analysisObjectEnum, pager, request.getProductType());
                List listStr = (List) listMap.stream().map(i -> OrpGsonUtils.toJsonNullStr(i)).collect(Collectors.toList());
                response.setDeptDetail(listStr);
                response.setHeaderData(flightSaveAndLossBiz.getHearder(request.getAnalysisObjectEnum(), request.getLang(), request.getQueryBu()));
                response.setTotalRecords(flightSaveAndLossBiz.count(request.getBasecondition(), analysisObjectEnum, request.getProductType()));
            }
            if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
                Pager pager = request.getPage();
                List listMap = hotelSaveAndLossBiz.deptDetail(request.getBasecondition(), analysisObjectEnum, pager, request.getProductType());
                List listStr = (List) listMap.stream().map(i -> OrpGsonUtils.toJsonNullStr(i)).collect(Collectors.toList());
                response.setDeptDetail(listStr);
                response.setHeaderData(hotelSaveAndLossBiz.getHearder(request.getAnalysisObjectEnum(), request.getLang(), request.getQueryBu()));
                response.setTotalRecords(hotelSaveAndLossBiz.count(request.getBasecondition(), analysisObjectEnum, request.getProductType()));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryTopPotentialSaveDetail", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportSaveProportionResponse queryPSaveProportion(OnlineReportSaveProportionRequest request) {
        OnlineReportSaveProportionResponse response = new OnlineReportSaveProportionResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setLegends(potentialSaveAnalysisDizBiz.getSaveDisLegend(request));
            response.setProportions(potentialSaveAnalysisDizBiz.getSaveDis(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryPSaveProportion", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportPSaveProportionDetailResponse queryPSaveProportionDetail(OnlineReportPSaveProportionDetailRequest request) {
        OnlineReportPSaveProportionDetailResponse response = new OnlineReportPSaveProportionDetailResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setProportionDetails(potentialSaveAnalysisDizBiz.getSaveDisDetail(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryPSaveProportionDetail", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTrendResponse queryReportPotentialSaveTrend(OnlineReportTrendRequest request) {
        OnlineReportTrendResponse response = new OnlineReportTrendResponse();
        OnlineReportTrendData data = new OnlineReportTrendData();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            List<OnlineReportTrendPoint> trendPoints = potentialSaveAnalysisTrendBiz.trendBody(request);
            List<OnlineReportTrendLegend> trendLegends = potentialSaveAnalysisTrendBiz.trendLegend(request);
            data.setLegends(trendLegends);
            data.setData(trendPoints);
            response.setData(data);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryReportPotentialSaveTrend", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTrendResponse queryReportPotentialSaveDetail(OnlineReportTrendRequest request) {
        OnlineReportTrendResponse response = new OnlineReportTrendResponse();
        OnlineReportTrendData data = new OnlineReportTrendData();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            List<OnlineReportTrendPoint> trendPoints = potentialSaveAnalysisDetailBiz.trendBody(request);
            List<OnlineReportTrendLegend> trendLegends = potentialSaveAnalysisDetailBiz.trendLegend(request);
            data.setLegends(trendLegends);
            data.setData(trendPoints);
            response.setData(data);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryReportPotentialSaveDetail", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public ReportSaveLossAnalyseResponse querySaveLossAnalyse(ReportSaveLossAnalyseRequest request) {
        ReportSaveLossAnalyseResponse response = new ReportSaveLossAnalyseResponse();
        List<String> notices = Lists.newArrayList();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            switch (request.getQueryBu()) {
                case flight:
                    /**
                     * 提前预订天数分布
                     */
                    FlightAdvanceBookDaysData bookDaysData = saveAndLossDetailBiz.queryFlightPreBookDays(request);
                    if (Objects.nonNull(bookDaysData) && org.apache.commons.lang.StringUtils.isNotEmpty(bookDaysData.getPreOrderNotice())) {
                        notices.add(bookDaysData.getPreOrderNotice());
                    }

                    /**
                     * 查询舱位分布
                     */
                    FlightPositionDist positionDist = saveAndLossDetailBiz.queryFlightPosition(request);
                    if (Objects.nonNull(positionDist) && org.apache.commons.lang.StringUtils.isNotEmpty(positionDist.getPositionDistNotice())) {
                        notices.add(positionDist.getPositionDistNotice());
                    }

                    /**
                     * 折扣分布
                     */
                    FlightDiscountDist discountDist = saveAndLossDetailBiz.queryFlightDiscount(request);
                    if (Objects.nonNull(discountDist) && org.apache.commons.lang.StringUtils.isNotEmpty(discountDist.getDiscountDistNotice())) {
                        notices.add(discountDist.getDiscountDistNotice());
                    }
                    /**
                     * 平均票价分布
                     */
                    FlightAvgTicketPriceDist priceAvgAvgDTO = saveAndLossDetailBiz.queryFlightPriceAvg(request);
                    if (Objects.nonNull(priceAvgAvgDTO) && org.apache.commons.lang.StringUtils.isNotEmpty(priceAvgAvgDTO.getAvgTicketPriceNotice())) {
                        notices.add(priceAvgAvgDTO.getAvgTicketPriceNotice());
                    }

                    /**
                     * 里程均价分布
                     */
                    FlightAvgMileageDist mileageDist = saveAndLossDetailBiz.queryFlightMileageAvg(request);
                    if (Objects.nonNull(mileageDist) && org.apache.commons.lang.StringUtils.isNotEmpty(mileageDist.getAvgMileageNotice())) {
                        notices.add(mileageDist.getAvgMileageNotice());
                    }
                    /**
                     * 贵司数据表现优异，暂无建议。
                     */
                    if (CollectionUtils.isEmpty(notices)) {
                        notices.add(SharkUtils.getHeaderVal(FlightIndustrySharkEnum.NONE_NOTICE.getSharkKey(), request.getLang()));
                    }
                    break;
                case hotel:
                    /**
                     * 酒店星级分布 sum(case when star=x then quantity else 0 end )/sum(quantity)
                     */
                    HotelStarDist hotelStarDist = saveAndLossDetailBiz.queryHotelStarDist(request);
                    if (Objects.nonNull(hotelStarDist) && org.apache.commons.lang.StringUtils.isNotEmpty(hotelStarDist.getStarDistNotice())) {
                        notices.add(hotelStarDist.getStarDistNotice());
                    }
                    /**
                     * 间夜均价分布 sum(room_price)/sum(quantity)
                     */
                    HotelRoomNightDist hotelRoomNightDist = saveAndLossDetailBiz.queryHotelNightDist(request);
                    if (Objects.nonNull(hotelRoomNightDist) && org.apache.commons.lang.StringUtils.isNotEmpty(hotelRoomNightDist.getRoomNightNotice())) {
                        notices.add(hotelRoomNightDist.getRoomNightNotice());
                    }
                    /**
                     * 贵司数据表现优异，暂无建议。
                     */
                    if (CollectionUtils.isEmpty(notices)) {
                        notices.add(SharkUtils.getHeaderVal(FlightIndustrySharkEnum.NONE_NOTICE.getSharkKey(), request.getLang()));
                    }
                    break;
                default:
                    break;
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        response.setNotices(notices);
        return response;
    }

    @Override
    public ReportSaveLossAnalyseResponse querySaveLossPreOrderDate(ReportSaveLossAnalyseRequest request) {
        ReportSaveLossAnalyseResponse response = new ReportSaveLossAnalyseResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            FlightAdvanceBookDaysData data = saveAndLossDetailBiz.queryFlightPreBookDays(request);
            response.setFlightBookDaysData(data);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public ReportSaveLossAnalyseResponse querySaveLossPositionDist(ReportSaveLossAnalyseRequest request) {
        ReportSaveLossAnalyseResponse response = new ReportSaveLossAnalyseResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            FlightPositionDist data = saveAndLossDetailBiz.queryFlightPosition(request);
            response.setFlightPositionDist(data);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public ReportSaveLossAnalyseResponse querySaveLossDiscountDist(ReportSaveLossAnalyseRequest request) {
        ReportSaveLossAnalyseResponse response = new ReportSaveLossAnalyseResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            FlightDiscountDist data = saveAndLossDetailBiz.queryFlightDiscount(request);
            response.setFlightDiscountDist(data);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public ReportSaveLossAnalyseResponse queryFlightAvgPriceDist(ReportSaveLossAnalyseRequest request) {
        ReportSaveLossAnalyseResponse response = new ReportSaveLossAnalyseResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            FlightAvgTicketPriceDist priceAvgAvgDTO = saveAndLossDetailBiz.queryFlightPriceAvg(request);
            response.setFlightAvgTicketPriceDist(priceAvgAvgDTO);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public ReportSaveLossAnalyseResponse queryFlightAvgMileageDist(ReportSaveLossAnalyseRequest request) {
        ReportSaveLossAnalyseResponse response = new ReportSaveLossAnalyseResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            FlightAvgMileageDist data = saveAndLossDetailBiz.queryFlightMileageAvg(request);
            response.setFlightAvgMileageDist(data);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public ReportSaveLossAnalyseResponse queryHotelStarDist(ReportSaveLossAnalyseRequest request) {
        ReportSaveLossAnalyseResponse response = new ReportSaveLossAnalyseResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            HotelStarDist hotelStarDist = saveAndLossDetailBiz.queryHotelStarDist(request);
            response.setHotelStarDist(hotelStarDist);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public ReportSaveLossAnalyseResponse queryHotelRoomNightDist(ReportSaveLossAnalyseRequest request) {
        ReportSaveLossAnalyseResponse response = new ReportSaveLossAnalyseResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            HotelRoomNightDist hotelRoomNightDist = saveAndLossDetailBiz.queryHotelNightDist(request);
            response.setHotelRoomNightDist(hotelRoomNightDist);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportOrderDetailResponse queryOrderDetail(OnlineReportOrderDetailRequest request) {
        OnlineReportOrderDetailResponse response = new OnlineReportOrderDetailResponse();
        OrderDetailBiz orderDetailBiz = factoryList.getBean(request.getQueryBu());
        try {
            OnlineReportOrderDetailInfo onlineReportOrderDetailInfo = orderDetailBiz.queryOrderDetail(request);
            Set<String> fltCorpIdSet = Optional.ofNullable(onlineReportOrderDetailInfo.getFltOrderList()).orElse(new ArrayList<>())
                    .stream().map(OnlineReportFlightOrderInfo::getCorpCorporation).collect(Collectors.toSet());
            Set<String> htlCorpIdSet = Optional.ofNullable(onlineReportOrderDetailInfo.getHtlOrderList()).orElse(new ArrayList<>())
                    .stream().map(OnlineReportHotelOrderInfo::getCorpCorporation).collect(Collectors.toSet());
            Set<String> trainCorpSet = Optional.ofNullable(onlineReportOrderDetailInfo.getTrainOrderList()).orElse(new ArrayList<>())
                    .stream().map(OnlineReportTrainOrderInfo::getCorpCorporation).collect(Collectors.toSet());
            Set<String> carCorpSet = Optional.ofNullable(onlineReportOrderDetailInfo.getCarOrderList()).orElse(new ArrayList<>())
                    .stream().map(OnlineReportCarOrderInfo::getCorpCorporation).collect(Collectors.toSet());
            fltCorpIdSet.addAll(htlCorpIdSet);
            fltCorpIdSet.addAll(trainCorpSet);
            fltCorpIdSet.addAll(carCorpSet);
            response.setOrderDetailInfo(onlineReportOrderDetailInfo);
            response.setTotalRecords(orderDetailBiz.queryOrderDetailCount(request));
            BaseQueryResponse baseQueryResponse = new BaseQueryResponse();
            baseQueryResponse.setUid(request.getBasecondition().getUid());
            baseQueryResponse.setCorpIds(new ArrayList<>(fltCorpIdSet));
            response.setBaseQueryResponse(baseQueryResponse);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryOrderDetail", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        } finally {
            ThreadPartitionContextUtils.clear();
        }
        return response;
    }

    @Override
    public OnlineReportConsumeProfileResponse queryConsumeProfile(OnlineReportConsumeProfileRequest request) {
        OnlineReportConsumeProfileResponse response = new OnlineReportConsumeProfileResponse();
        try {
            // 不区分uid,提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setConsumeProfileList(consumeProfileBiz.aggreationConsume(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryConsumeProfile", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportCityHotelResponse queryCityHotelInfo(OnlineReportCityHotelRequest request) {
        OnlineReportCityHotelResponse response = new OnlineReportCityHotelResponse();
        try {
            // 不区分uid,提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setCityHotelInfoList(cityHotelBiz.queryCityHotelInfo(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryCityHotelInfo", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTravelPositionResponse queryTravelPositionInfo(OnlineReportTravelPositionRequest request) {
        OnlineReportTravelPositionResponse response = new OnlineReportTravelPositionResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            Map extData = request.getExtData();
            String index = (String) extData.get("index");
            // 足迹
            if (StringUtils.equalsIgnoreCase("position", index) || StringUtils.equalsIgnoreCase("FRONT_PAGE", index)) {
                response.setPositionInfo(positionBiz.positionInfo(request));
            } else if (StringUtils.equalsIgnoreCase("position_track", index)) {// 交通
                response.setPositionTrackInfo(positionBiz.positionTrackInfo(request));
            } else if (StringUtils.equalsIgnoreCase("position_hotel", index)) { // 酒店
                request.setQueryBu(QueryReportBuTypeEnum.hotel);
                response.setPositionInfo(positionBiz.positionInfo(request));
            } else if (StringUtils.equalsIgnoreCase("position_general", index)) { // 概览
                response.setTravelGeneralInfo(positionBiz.positionGenralInfo(request));
            } else if (StringUtils.equalsIgnoreCase("event_general", index)) { // 概览
                response.setTravelGeneralInfo(positionBiz.eventGenralInfo(request));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTopCityResponse queryTopCity(OnlineReportTopCityRequest request) {
        OnlineReportTopCityResponse response = new OnlineReportTopCityResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setTopCityList(positionBiz.queryTopCity(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTopHotelResponse queryTopHotel(OnlineReportTopHotelRequest request) {
        OnlineReportTopHotelResponse response = new OnlineReportTopHotelResponse();

        try {

            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setTopHotelList(positionBiz.queryTopHotel(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    private List<Integer> getIdList(PageResult<List<TravelPositionDetailDTO>> pageResult) throws Exception {
        Set<Integer> cityList = new HashSet<>();
        List<TravelPositionDetailDTO> list = pageResult.getData();
        for (TravelPositionDetailDTO travelPositionDetailDTO : list) {
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(travelPositionDetailDTO.getOrderType(), TravelPositionBuTypeEnum.HTL.getBu())) {
                cityList.add(travelPositionDetailDTO.getStartCityId());
                cityList.add(travelPositionDetailDTO.getEndCityId());
            }
            cityList.add(travelPositionDetailDTO.getStartProvinceId());
            cityList.add(travelPositionDetailDTO.getEndProvinceId());
        }
        return cityList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<Integer> getFltCityIdList(PageResult<List<TravelPositionDetailDTO>> pageResult) throws Exception {
        Set<Integer> cityList = new HashSet<>();
        List<TravelPositionDetailDTO> list = pageResult.getData();
        for (TravelPositionDetailDTO travelPositionDetailDTO : list) {
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(travelPositionDetailDTO.getOrderType(), TravelPositionBuTypeEnum.FLT.getBu())) {
                cityList.add(travelPositionDetailDTO.getStartCityId());
                cityList.add(travelPositionDetailDTO.getEndCityId());
            }
        }
        return cityList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<Integer> getMasterHtlIdCityIdList(PageResult<List<TravelPositionDetailDTO>> pageResult) throws Exception {
        Set<Integer> htlIdList = new HashSet<>();
        List<TravelPositionDetailDTO> list = pageResult.getData();
        for (TravelPositionDetailDTO travelPositionDetailDTO : list) {
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(travelPositionDetailDTO.getOrderType(), TravelPositionBuTypeEnum.HTL.getBu())) {
                htlIdList.add(travelPositionDetailDTO.getMasterhotelid());
            }
        }
        return htlIdList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public TravelPositionDetailResponse queryTravelPositionDetailByPage(TravelPositionDetailRequest request) {
        TravelPositionDetailResponse response = new TravelPositionDetailResponse();

        try {

            response.setTitle(SharkUtils.getHeaderVal(TravelPositionStepTypeEnum.TITLE.getSharkKey(), request.getLang()));
            response.setQueryTitle(Arrays.stream(TravelPositionQueryKeyEnum.values()).collect(Collectors.toMap(TravelPositionQueryKeyEnum::getKey,
                    o -> SharkUtils.getHeaderVal(o.getSharkKey(), request.getLang()))));
            PageResult<List<TravelPositionDetailDTO>> pageResult = travelPositionDetailBiz.queryTravelPositionDetailByPage(request);
            Map cityMap = new HashMap();
            Map fltCityMap = new HashMap();
            Map provinceMap = new HashMap();
            Map htlMap = new HashMap();
            // Foreign才走多语言
            if (BlueSpaceUtils.isForeign(request.getBasecondition().getPos(), request.getBasecondition().getBlueSpace())) {
                List<Integer> idList = getIdList(pageResult);
                List<Integer> fltCityIdList = getFltCityIdList(pageResult);
                List<Integer> masterHtlIdCityIdList = getMasterHtlIdCityIdList(pageResult);
                Future<List<GeoLocationDTO>> future1 = executorService.submit(new Callable<List<GeoLocationDTO>>() {
                    @Override
                    public List<GeoLocationDTO> call() throws Exception {
                        return geoLocationLanguageBiz.queryGeoLocationEntity(idList, Arrays.asList(GeoCategoryEnum.CITY, GeoCategoryEnum.PROVINCE), request.lang);
                    }
                });
                Future<List<MultiLangDTO>> future2 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                    @Override
                    public List<MultiLangDTO> call() throws Exception {
                        return geoLocationLanguageBiz.queryFltGeoLocationEntity(fltCityIdList, request.lang);
                    }
                });

                Future<List<MultiLangDTO>> future3 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                    @Override
                    public List<MultiLangDTO> call() throws Exception {
                        return htlMultiLangBiz.queryHtlInfoMultiLang(masterHtlIdCityIdList, request.lang);
                    }
                });
                List<GeoLocationDTO> geoList = future1.get();
                List<MultiLangDTO> fltGeoList = future2.get();
                List<MultiLangDTO> htlList = future3.get();
                Optional.ofNullable(geoList).orElse(new ArrayList<>()).forEach(i -> {
                    if (i.getGeoCategoryId() == GeoCategoryEnum.CITY.getCode()) {
                        cityMap.put(i.getGeoId(), i.getGeoPlaceName());
                    }
                    if (i.getGeoCategoryId() == GeoCategoryEnum.PROVINCE.getCode()) {
                        provinceMap.put(i.getGeoId(), i.getGeoPlaceName());
                    }
                });
                Optional.ofNullable(fltGeoList).orElse(new ArrayList<>()).forEach(i -> {
                    fltCityMap.put(i.getId(), i.getMultiLangName());
                });
                Optional.ofNullable(htlList).orElse(new ArrayList<>()).forEach(i -> {
                    htlMap.put(i.getId(), i.getMultiLangName());
                });
            }
            BaseQueryCondition base = request.getBasecondition();
            if (TravelPositionStepEnum.TRAVEL_ING.equals(request.getTravelStep())) {
                response.setNotLeftData(mapperDetailData(pageResult, request.getLang(), request.getTravelStep(), cityMap, provinceMap, base.getPos(), base.getBlueSpace(),
                        fltCityMap, htlMap));
            } else if (TravelPositionStepEnum.GOING.equals(request.getTravelStep())) {
                response.setGoingData(mapperDetailData(pageResult, request.getLang(), request.getTravelStep(), cityMap, provinceMap, base.getPos(), base.getBlueSpace(),
                        fltCityMap, htlMap));
            } else {
                response.setLeftData(mapperDetailData(pageResult, request.getLang(), request.getTravelStep(), cityMap, provinceMap, base.getPos(), base.getBlueSpace(),
                        fltCityMap, htlMap));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryTravelPositionDetailByPage", ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }


    private String getHeader(TravelPositionLegendEnum travelPositionLegendEnum, String pos, String blueSpace, String lang) {
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            // 蓝色空间这里没有火车票 所以sharkkey要更换一下
            return travelPositionLegendEnum == TravelPositionLegendEnum.FLIGHTNO_TRAINNO_HOTELNAME
                    ? SharkUtils.getHeaderVal("Exceltopname.flightnohotelname", lang) : SharkUtils.getHeaderVal(travelPositionLegendEnum.getSharkKey(), lang);
        }
        return SharkUtils.getHeaderVal(travelPositionLegendEnum.getSharkKey(), lang);
    }

    /**
     * mapper result
     */
    private TravelPositionDetailInfo mapperDetailData(PageResult<List<TravelPositionDetailDTO>> pageResult, String lang, TravelPositionStepEnum stepEnum,
                                                      Map cityMap, Map provinceMap, String pos, String bluespace, Map fltCityMap, Map htlMap) {
        TravelPositionDetailInfo data = new TravelPositionDetailInfo();
        TravelPositionLegendEnum[] travelPositionLegendEnums = TravelPositionLegendEnum.values();
        if (BlueSpaceUtils.isForeign(pos, bluespace)) {
            // 顶大明细去掉卡号
            travelPositionLegendEnums = removeElement(travelPositionLegendEnums, TravelPositionLegendEnum.UID.ordinal());
        }
        data.setTotalCount(pageResult.getTotalCount());
        data.setLegends(Arrays.stream(travelPositionLegendEnums)
                .map(t -> new OnlineReportTrendLegend(getHeader(t, pos, bluespace, lang), t.getKey(), OrpConstants.EMPTY, OrpConstants.EMPTY, OrpConstants.EMPTY))
                .collect(Collectors.toList()));
        data.setSubject(SharkUtils.getHeaderVal(TravelPositionStepTypeEnum.getSharkKey(stepEnum.name()), lang));
        data.setData(convertLogList(pageResult.getData(), cityMap, provinceMap, lang, fltCityMap, htlMap, pos, bluespace));
        return data;
    }

    /**
     * list to list
     */
    private List<TravelPositionDetailInfoData> convertLogList(List<TravelPositionDetailDTO> input, Map cityMap, Map provinceMap, String lang, Map fltCityMap, Map htlMap,
                                                              String pos, String bluespace) {
        List<TravelPositionDetailInfoData> output = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(input)) {
            for (TravelPositionDetailDTO source : input) {
                TravelPositionDetailInfoData target = new TravelPositionDetailInfoData();
                BeanUtils.copyProperties(source, target);
                target.setOrderType(mapperOrderType(source.getOrderType(), lang));
                target.setEndSubTripDate(source.getEndSubTripDate());
                // isForeign需要翻译
                if (BlueSpaceUtils.isForeign(pos, bluespace)) {
                    target.setStartProvinceName(org.apache.commons.lang.StringUtils.trimToEmpty((String) provinceMap.get(source.getStartProvinceId())));
                    target.setEndProvinceName(org.apache.commons.lang.StringUtils.trimToEmpty((String) provinceMap.get(source.getEndProvinceId())));
                    if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(source.getOrderType(), TravelPositionBuTypeEnum.FLT.name())) {
                        target.setEndCityName(org.apache.commons.lang.StringUtils.trimToEmpty((String) fltCityMap.get(source.getEndCityId())));
                        target.setStartCityName(org.apache.commons.lang.StringUtils.trimToEmpty((String) fltCityMap.get(source.getStartCityId())));
                    } else {
                        target.setEndCityName((org.apache.commons.lang.StringUtils.trimToEmpty((String) cityMap.get(source.getEndCityId()))));
                        target.setStartCityName(org.apache.commons.lang.StringUtils.trimToEmpty((String) cityMap.get(source.getStartCityId())));
                        target.setFlightnoTrainnoHotelname(org.apache.commons.lang.StringUtils.trimToEmpty((String) htlMap.get(source.getMasterhotelid())));
                    }
                }
                output.add(target);
            }
        }
        return output;
    }

    /**
     * OnlineReportBuEnum.overview=概览
     * OnlineReportBuEnum.flight=机票
     * OnlineReportBuEnum.hotel=酒店
     * OnlineReportBuEnum.train=火车
     * OnlineReportBuEnum.car=用车
     */
    private String mapperOrderType(String orderType, String lang) {
        if (org.apache.commons.lang.StringUtils.isEmpty(orderType)) {
            return orderType;
        }
        if (TravelPositionBuTypeEnum.HTL.getBu().equalsIgnoreCase(orderType.trim()) || QueryReportBuTypeEnum.hotel.name().equalsIgnoreCase(orderType.trim())) {
            return SharkUtils.getHeaderVal("Index.hotel", lang);
        } else if (TravelPositionBuTypeEnum.FLT.getBu().equals(orderType) || QueryReportBuTypeEnum.flight.name().equalsIgnoreCase(orderType.trim())) {
            return SharkUtils.getHeaderVal("Index.air", lang);
        } else if (TravelPositionBuTypeEnum.TRAIN.getBu().equals(orderType) || QueryReportBuTypeEnum.train.name().equalsIgnoreCase(orderType.trim())) {
            return SharkUtils.getHeaderVal("Index.train", lang);
        }
        return orderType;
    }

    /**
     * 国家
     */
    private List<TravelPositionCountryInfo> mapperCountryList(List<GeoLocationDTO> provinceList) {
        return CollectionUtils.isEmpty(provinceList) ? Lists.newArrayList() :
                provinceList.stream().filter(Objects::nonNull)
                        .map(t -> new TravelPositionCountryInfo(t.getGeoId(), t.getGeoPlaceName()))
                        .collect(Collectors.toList());
    }

    private List<TravelPositionCountryInfo> mapperHotCountry(List<TravelPositionCityDTO> provinceList) {
        if (CollectionUtils.isEmpty(provinceList)) {
            return Lists.newArrayList();
        }
        return provinceList.stream().filter(Objects::nonNull)
                .map(t -> new TravelPositionCountryInfo(
                        t.getSubTripCityCountryid(), t.getSubTripCityCountryname()))
                .collect(Collectors.toList());
    }

    @Override
    public TravelPositionCountryResponse queryTravelPositionCountry(TravelPositionCountryRequest request) {
        TravelPositionCountryResponse response = new TravelPositionCountryResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            List<GeoLocationDTO> provinceList = geoLocationLanguageBiz.queryGeoLocationEntity(request.getCountryName(), GeoCategoryEnum.COUNTRY, request.lang);
            List<TravelPositionCityDTO> provinceHotList = travelPositionDetailBiz.queryTravelPositionHotCountry(request);
            TravelPositionCountryData countryData = new TravelPositionCountryData();
            countryData.setCountryList(mapperCountryList(provinceList));
            countryData.setHotList(mapperHotCountry(provinceHotList));
            response.setCountryData(countryData);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryTravelPositionCountry", ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    private List<TravelPositionCityInfo> mapperProvinceList(List<GeoLocationDTO> provinceList) {
        return CollectionUtils.isEmpty(provinceList) ? Lists.newArrayList() :
                provinceList.stream().filter(Objects::nonNull)
                        .map(t -> new TravelPositionCityInfo((Integer) NULL, (String) NULL,
                                t.getGeoId(), t.getGeoPlaceName()))
                        .collect(Collectors.toList());
    }

    private List<TravelPositionCityInfo> mapperHotProvince(List<TravelPositionCityDTO> provinceList) {
        if (CollectionUtils.isEmpty(provinceList)) {
            return Lists.newArrayList();
        }
        return provinceList.stream().filter(Objects::nonNull)
                .map(t -> new TravelPositionCityInfo((Integer) NULL, (String) NULL,
                        t.getSubTripProvinceId(), t.getSubTripProvinceName()))
                .collect(Collectors.toList());
    }

    @Override
    public TravelPositionProvinceResponse queryTravelPositionProvince(TravelPositionProvinceRequest request) {
        TravelPositionProvinceResponse response = new TravelPositionProvinceResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            List<GeoLocationDTO> provinceList = geoLocationLanguageBiz.queryGeoLocationEntity(request.getProvinceName(), GeoCategoryEnum.PROVINCE, request.lang);
            List<TravelPositionCityDTO> provinceHotList = travelPositionDetailBiz.queryTravelPositionHotProvince(request);
            TravelPositionProvinceData provinceData = new TravelPositionProvinceData();
            provinceData.setHotProvince(mapperHotProvince(provinceHotList));
            provinceData.setProvinceList(mapperProvinceList(provinceList));
            response.setProvinceData(provinceData);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryTravelPositionProvince {}", ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public TravelPositionCityResponse queryTravelPositionCity(TravelPositionCityRequest request) {
        TravelPositionCityResponse response = new TravelPositionCityResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            List<GeoLocationDTO> cityList = geoLocationLanguageBiz.queryGeoLocationEntity(request.getCityName(), GeoCategoryEnum.CITY, request.lang);
            List<TravelPositionCityDTO> hotCityList = travelPositionDetailBiz.queryTravelPositionHotCity(request);
            TravelPositionCityData cityData = new TravelPositionCityData();
            cityData.setHotCity(mapperHotCity(hotCityList));
            cityData.setCityList(mapperCityList(cityList));
            response.setCityData(cityData);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryTravelPositionCity {}", ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    private List<TravelPositionCityInfo> mapperCityList(List<GeoLocationDTO> cityList) {
        return CollectionUtils.isEmpty(cityList) ? Lists.newArrayList() :
                cityList.stream().filter(Objects::nonNull)
                        .map(t -> new TravelPositionCityInfo(t.getGeoId(), t.getGeoPlaceName(),
                                (Integer) NULL, (String) NULL))
                        .collect(Collectors.toList());
    }

    private List<TravelPositionCityInfo> mapperHotCity(List<TravelPositionCityDTO> cityList) {
        if (CollectionUtils.isEmpty(cityList)) {
            return Lists.newArrayList();
        }
        return cityList.stream().filter(Objects::nonNull)
                .map(t -> new TravelPositionCityInfo(t.getSubTripCityId(), t.getSubTripCityName(),
                        (Integer) NULL, (String) NULL))
                .collect(Collectors.toList());
    }


    @Override
    public OnlineReportBalanceTypeResponse queryHtlBalanceTypeAnalysis(OnlineReportBalanceTypeRequest request) {
        OnlineReportBalanceTypeResponse response = new OnlineReportBalanceTypeResponse();
        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setBalanceTypeList(behaviorAnalysisBiz.hotelBalanceTypeAnalysis(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryHtlBalanceTypeAnalysis>>>Resp", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportZoningOpenResponse queryHtlRoomShareOpenAnalysis(OnlineReportZoningOpenRequest request) {
        OnlineReportZoningOpenResponse response = new OnlineReportZoningOpenResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setIsOpen(behaviorAnalysisBiz.hotelRoomShareIsOpenAnalysis(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryHtlRoomShareOpenAnalysis>>>Resp", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportCostAllocationResponse queryHtlCostAllocationAnalysis(OnlineReportCostAllocationRequest request) {
        OnlineReportCostAllocationResponse response = new OnlineReportCostAllocationResponse();
        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setZoningInfoList(behaviorAnalysisBiz.hotelCostAllocationTypeAnalysis(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryHtlCostAllocationAnalysis>>>Resp", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportZoningPatternTrendResponse queryHtlRoomShareTrendAnalysis(OnlineReportZoningPatternTrendRequest request) {
        OnlineReportZoningPatternTrendResponse response = new OnlineReportZoningPatternTrendResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setZoningInfo(behaviorAnalysisBiz.hotelRoomShareTrendTypeAnalysis(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryHtlRoomShareTrendAnalysis>>>Resp", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportZoningPatternResponse queryHtlRoomShareAnalysis(OnlineReportZoningPatternRequest request) {
        OnlineReportZoningPatternResponse response = new OnlineReportZoningPatternResponse();

        try {
            response.setZoningInfoList(behaviorAnalysisBiz.hotelRoomShareTypeAnalysis(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryHtlRoomShareAnalysis>>>Resp", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportMixPaymentResponse queryMixPaymentInfo(OnlineReportMixPaymentRequest request) {
        OnlineReportMixPaymentResponse response = new OnlineReportMixPaymentResponse();

        try {
            // 不区分uid,提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setMixPayment(behaviorAnalysisBiz.mixpaymentInfos(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryMixPaymentInfo>>>Resp", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }


    @Override
    public OnlineReportAvgNightPriceResponse queryHtlAvgNightPriceAnalysis(OnlineReportAvgNightPriceRequest request) {
        OnlineReportAvgNightPriceResponse response = new OnlineReportAvgNightPriceResponse();
        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            String index = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()).get("index");
            if (StringUtils.equalsIgnoreCase("overview", index)) {
                response.setOverviewInfo(behaviorAnalysisBiz.hotelAvgRoomPriceOverview(request));
            } else {
                response.setRangeList(behaviorAnalysisBiz.hotelAvgRoomPriceAnalysis(request));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryHtlAvgNightPriceAnalysis error", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportTopDeptConsumeDetailResponse queryTopDeptConsumeDetail(OnlineReportTopDeptConsumeDetailRequest request) {
        OnlineReportTopDeptConsumeDetailResponse response = new OnlineReportTopDeptConsumeDetailResponse();

        try {
            /*if (BlueSpaceUtils.isForeign(request.getBasecondition().getPos(), request.getBasecondition().getBlueSpace())) {
                ThreadPartitionContextUtils.init(partitionBiz.getPartitionInfoByModule(OnlineReportModuleEnum.COMMON_BS.getModuleCode()));
            } else {
                ThreadPartitionContextUtils.init(partitionBiz.getPartitionInfoByModule(OnlineReportModuleEnum.COMMON_CN.getModuleCode()));
            }*/
            // 不区分uid,提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                response.setFltTopConsumeInfo(top5DeptFltConsumeBiz.topDeptConsume(request));
            } else if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                response.setHtlTopConsumeInfo(top5DeptHtlConsumeBiz.topDeptConsume(request));
            } else if (request.getQueryBu() == QueryReportBuTypeEnum.train) {
                response.setTrainTopConsumeInfo(top5DeptTrainConsumeBiz.topDeptConsume(request));
            } else if (request.getQueryBu() == QueryReportBuTypeEnum.car) {
                response.setCarTopConsumeInfo(top5DeptCarConsumeBiz.topDeptConsume(request));
            } else if (request.getQueryBu() == QueryReportBuTypeEnum.overview) {
                response.setOverviewTopConsumeInfo(top5DeptOverviewConsumeBiz.topDeptConsume1(request));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("topDeptConsumeDetail error:", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    public OnlineReportQueryHtlOrderAuditResponse queryHtlOrderAuditInfo(OnlineReportQueryHtlOrderAuditRequest request) {
        OnlineReportQueryHtlOrderAuditResponse response = new OnlineReportQueryHtlOrderAuditResponse();
        try {
            String uid = request.getBasecondition().getUid();
            response.setHtlAuditList(htlOrderAuditBiz.queryOrderDetail(request));
            response.setTotalRecords(htlOrderAuditBiz.queryOrderDetailCount(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        } finally {
            ThreadPartitionContextUtils.clear();
        }
        return response;
    }


    @Override
    public OnlineReportQueryUpdateTimeInfoResponse queryUpdateTimeInfo(OnlineReportQueryUpdateTimeInfoRequest request) {
        OnlineReportQueryUpdateTimeInfoResponse response = new OnlineReportQueryUpdateTimeInfoResponse();
        try {
            response.setDate(partitionBiz.getUpdateTimeInfo(request.getModuleCode()));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILURE.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILURE.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportHotelStarResponse queryHotelStarAnalysis(OnlineReportHotelStarRequest request) {
        OnlineReportHotelStarResponse response = new OnlineReportHotelStarResponse();
        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            response.setStarList(behaviorAnalysisBiz.hotelStarAnalysis(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryHotelStarAnalysis error", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportPreOrderDateResponse queryPreOrderDateAnalysis(OnlineReportPreOrderDateRequest request) {
        OnlineReportPreOrderDateResponse response = new OnlineReportPreOrderDateResponse();
        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            String from = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()).get("from");
            if (StringUtils.equalsIgnoreCase(from, "PC")) {
                response.setPreOrderDateList(flightBehaviorAnalysisBiz.flightPreorderdateAnalysis(request));
            } else {
                response.setPreOrderDateList(behaviorAnalysisBiz.preOrderDateAnalysis(request));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("queryPreOrderDateAnalysis error", e);
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportWelfareStockDataResponse queryWelfareStockData(OnlineReportWelfareStockDataRequest request) {

        OnlineReportWelfareStockDataResponse response = new OnlineReportWelfareStockDataResponse();
        try {
            WelfareStockData welfareStockData = settlementDataBiz.queryWelfareStockData(request);
            response.setWelfareStockData(welfareStockData);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILURE.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILURE.getDesc());
        }
        return response;
    }

    @Override
    public String queryBaseDataLastUpdateTime() {
        return onlineReportDao.queryBaseDataLastUpdateTime();
    }

    @Override
    public OnlineReportHotAanlysisResponse queryHotAnalysis(OnlineReportHotAanlysisRequest request) {

        OnlineReportHotAanlysisResponse response = new OnlineReportHotAanlysisResponse();
        OnlineReportTrendData data = new OnlineReportTrendData();
        HotAnalysis hotAnalysis = hotAnalysisBiz.getBean(request.getQueryBu());

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(StringUtils.EMPTY);
            List<OnlineReportTrendPoint> trendPoints = hotAnalysis.trendBody(request);
            List<OnlineReportTrendLegend> trendLegends = hotAnalysis.trendLegend(request);
            data.setLegends(trendLegends);
            data.setData(trendPoints);
            response.setData(data);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportAgreementConsumeResponse queryAgreementConsume(OnlineReportAgreementConsumeRequest request) {
        OnlineReportAgreementConsumeResponse response = new OnlineReportAgreementConsumeResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            Map extData = request.getExtData();
            String index = (String) extData.get("index");
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(index, "view")) {
                response.setAgreementViewInfo(supplierMonitorBiz.agreementView(request));
            }
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(index, "agg")) {
                response.setAgreementAggInfo(supplierMonitorBiz.agreementAgg(request));
            }
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(index, "briefing")) {
                response.setExtData(supplierMonitorBiz.jianbao(request));
            }
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(index, "appIndex")) {
                response.setExtData(supplierMonitorBiz.appIndex(request));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportAgreementDetailResponse queryAgreementDetail(OnlineReportAgreementDetailRequest request) {
        OnlineReportAgreementDetailResponse response = new OnlineReportAgreementDetailResponse();

        try {
            String index = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()).get("index");
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(index, "other")) {
                response.setDeptDetail(supplierMonitorBiz.agreementDetail(request));
                response.setDeptSum(supplierMonitorBiz.agreementDetailSum(request));
                response.setHeaderData(supplierMonitorBiz.getAgreementDetailHearder(request.getLang(), request.getQueryBu()));
            }
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(index, "dept")) {
                AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
                Pager pager = request.getPage();
                List listMap = supplierMonitorBiz.agreementDeptDetail(request, analysisObjectEnum, pager);
                List listStr = (List) listMap.stream().map(i -> OrpGsonUtils.toJsonNullStr(i)).collect(Collectors.toList());
                response.setDeptDetail(listStr);
                response.setHeaderData(supplierMonitorBiz.getAgreementDeptDetailHearder(request.getLang(), request.getAnalysisObjectEnum(), request.getQueryBu()));
                response.setTotalRecords(supplierMonitorBiz.count(request, analysisObjectEnum));
            }
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportSupplierTrendResponse querySupplierTrend(OnlineReportSupplierTrendRequest request) {
        OnlineReportSupplierTrendResponse response = new OnlineReportSupplierTrendResponse();

        try {
            // 不区分uid,使提高缓存的命中率
            request.getBasecondition().setUid(org.apache.commons.lang3.StringUtils.EMPTY);
            response.setTrends(supplierMonitorBiz.trend(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportCarbonsDeptDetailV2Response queryCarbonsDeptDetailV2Header(OnlineReportCarbonsDeptDetailV2Request request) {
        OnlineReportCarbonsDeptDetailV2Response response = new OnlineReportCarbonsDeptDetailV2Response();

        try {
            response.setHeaderData(carbonsBiz.getCarbonsDeptDetailHearder(request));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public OnlineReportDeptDetailAnalysisResponse queryDeptDetailHeader(OnlineReportDeptDetailAnalysisRequest request) {
        OnlineReportDeptDetailAnalysisResponse response = new OnlineReportDeptDetailAnalysisResponse();
        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum = SubQueryReportBuTypeEnum.getByOrdinal(request.getSubQueryBu());
        DeptDetailAnalysisBiz deptDetailAnalysisBiz = deptDetailAnalysisBizs.getBean(SubQueryReportBuTypeEnum.getByOrdinal(request.getSubQueryBu()));

        try {
            response.setHeaderData(deptDetailAnalysisBiz.getHearder(request.getAnalysisObjectEnum(), request.getLang(), subQueryReportBuTypeEnum));
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            response.setResponseCode(ResponseCodeEnum.FAILED.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILED.getDesc());
        }
        return response;
    }

    @Override
    public ReportLibTakeawayIndicatorConfigBo queryIndicatorConfig(String cardNo) {
        if (StringUtils.isBlank(cardNo)){
            return null;
        }
        CorpReportLibIndicatorConfigDo configDo = corpReportLibIndicatorConfigMapper.selectByReportCardNo(cardNo);
        if (configDo == null){
            return null;
        }
        ReportLibTakeawayIndicatorConfigBo result = new ReportLibTakeawayIndicatorConfigBo();
        result.setCardNo(cardNo);
        result.setExtCondition(configDo.getExtCondition());
        result.setExtFieldEntities(JsonUtils.convert(configDo.getExtFieldListJson(), new TypeReference<List<ExtFiledEntity>>() {}));
        return result;
    }

    /**
     * 根据各个渠道获取部门/成本中心/公司
     * @param requestType
     * @return
     */
    @Override
    public List<String> queryCostCenterOrDepartmentOrCorpId(CostCenterAndDepartmentSearchRequestType requestType) throws Exception {


        if (requestType == null){
            return Collections.emptyList();
        }


        return supplierMonitorBiz.queryCostCenterOrDepartmentOrCorpId(requestType.getSearchType());
    }


    @Override
    public OnlineReportSettlementBillingDataResponse querySettlementBillingData(OnlineReportSettlementBillingDataRequest request) {
        OnlineReportSettlementBillingDataResponse response = new OnlineReportSettlementBillingDataResponse();
        try {
            SettlementBillingData settlementBillingData = settlementDataBiz.querySettlementBillingData(request);
            response.setSettlementBillingData(settlementBillingData);
            response.setResponseCode(ResponseCodeEnum.SUCCESS.getCode());
            response.setResponseDesc(ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
            response.setResponseCode(ResponseCodeEnum.FAILURE.getCode());
            response.setResponseDesc(ResponseCodeEnum.FAILURE.getDesc());
        }
        return response;
    }


}
