package com.corpgovernment.resource.schedule.screen.dao;


import com.corpgovernment.resource.schedule.domain.screen.model.TotalAmountDTO;
import com.corpgovernment.resource.schedule.screen.mapper.ScreenMapper;
import com.corpgovernment.resource.schedule.screen.po.TotalAmountDO;
import com.ctrip.corp.obt.shard.annotation.ShardModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ScreenDAO {

    @Resource
    private ScreenMapper screenMapper;


    @ShardModel(targetDataSource ="corpbi_db")
    public TotalAmountDO totalAmount(TotalAmountDTO totalAmountDTO) {
        log.info("totalAmountDTO：{}", totalAmountDTO);


        TotalAmountDO totalAmountDO = screenMapper.totalAmount(totalAmountDTO);

        log.info("totalAmountDO：{}", totalAmountDO);
        return totalAmountDO;
    }
}
