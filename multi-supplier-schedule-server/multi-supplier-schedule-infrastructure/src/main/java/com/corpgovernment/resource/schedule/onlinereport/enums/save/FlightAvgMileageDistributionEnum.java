package com.corpgovernment.resource.schedule.onlinereport.enums.save;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.save
 * @description:机酒节省分析-机票里程分布
 * @author: md_wang
 * @create: 2022-08-02 20:00
 **/
public enum FlightAvgMileageDistributionEnum {
    /**
     * 里程均价分布
     */
    TITLE("title", "Save.AvgTmps"),

    /**
     * 里程均价高于{{avgPrice}}元票张数占比高于{{compare}}平均水平，请关注。
     */
    NOTICE("notice", "Save.FltAys7"),
    /**
     * 公司
     */
    COMPANY("company", "Save.Company"),
    /**
     * 商旅
     */
    CORP("corp", "Index.tmc"),
    /**
     * 行业
     */
    INDUSTRY("industry", "Index.industry"),
    ;

    private String name;
    private String sharkKey;

    FlightAvgMileageDistributionEnum(String name, String sharkKey) {
        this.name = name;
        this.sharkKey = sharkKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public void setSharkKey(String sharkKey) {
        this.sharkKey = sharkKey;
    }
}
