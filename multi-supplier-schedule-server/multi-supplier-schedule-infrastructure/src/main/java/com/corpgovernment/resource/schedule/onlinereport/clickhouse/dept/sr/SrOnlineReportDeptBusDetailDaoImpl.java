package com.corpgovernment.resource.schedule.onlinereport.clickhouse.dept.sr;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptDetailDaoService;
import com.corpgovernment.resource.schedule.onlinereport.enums.StarRocksTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/*
 * <AUTHOR>
 *
 * @date 2022/09/20 16:15
 *
 * @Desc
 */


@Repository
public class SrOnlineReportDeptBusDetailDaoImpl extends AbstractSrOnlineReportDeptDetailDao implements OnlineReportDeptDetailDaoService {

    @Override
    protected String getProcutTypeCondition(String productType) {
        return StringUtils.EMPTY;
    }

    @Override
    protected String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return StringUtils.EMPTY;
    }

    /**
     * 统计字段
     *
     * @return
     */
    @Override
    protected String statical() {
        List sql = new ArrayList();
        sql.add("sum(coalesce(amount_bus, 0)) as TOTAL_REAL_PAY");
        sql.add("sum(coalesce(quantity_bus,0)) as TOTAL_CAR_QUANTITY");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    @Override
    protected String orderByField() {
        return "TOTAL_REAL_PAY desc";
    }

    @Override
    protected TableAndTimeColBind getTargetTableAndTimeColumn(String statisticalCaliber, Boolean isForegin) {
        TableAndTimeColBind tableAndTimeColBind = new TableAndTimeColBind();
        tableAndTimeColBind.setDateColumn(REPORT_DATE);
        tableAndTimeColBind.setStarRocksTable(StarRocksTable.ADM_INDEX_PRICE_SUMMARRY);
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            tableAndTimeColBind.setStarRocksTable(StarRocksTable.ADM_INDEX_PRICE_SUMMARRY_ODT);
        }
        return tableAndTimeColBind;
    }

    @Override
    protected String totalField() {
        return "SUM(coalesce(amount_bus, 0)) AS TOTAL_REAL_PAY,sum(coalesce(quantity_bus,0)) as TOTAL_CAR_QUANTITY";
    }

    /**
     * 同环比，总计数据
     *
     * @return
     */
    @Override
    protected String momAndYoy() {
        return "sum(coalesce(amount_bus, 0))  as TOTAL_REAL_PAY";
    }

    /**
     * 返回字段
     *
     * @return
     */
    protected String baseQueryField() {
        List sql = new ArrayList();
        sql.add("round(current.TOTAL_REAL_PAY, 4) AS REAL_PAY");
        sql.add("round(case when coalesce(total.TOTAL_REAL_PAY, 0) !=0 then divide(CAST(coalesce(current.TOTAL_REAL_PAY, 0) AS DOUBLE), \n"
                + "  CAST(coalesce(total.TOTAL_REAL_PAY, 0) AS DOUBLE)) * 100 else 0 end, 4) as REAL_PAY_PERCENT");
        sql.add("round(case when coalesce(yoy.TOTAL_REAL_PAY, 0) !=0 then divide(CAST((coalesce(current.TOTAL_REAL_PAY, 0) -\n" +
                "   coalesce(yoy.TOTAL_REAL_PAY, 0)) AS DOUBLE) , CAST(abs(coalesce(yoy.TOTAL_REAL_PAY, 0)) AS DOUBLE)) * 100 else 0 end, 4) as YOY_LAST");
        sql.add("round(case when coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0) !=0 then divide(CAST((coalesce(current.TOTAL_REAL_PAY, 0) - \n"
                + "  coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)) AS DOUBLE) , CAST(abs(coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)) AS DOUBLE)) * 100 \n" +
                "   else 0 end, 4) as YOY_BEFORE_LAST");
        sql.add("round(case when coalesce(mom.TOTAL_REAL_PAY, 0) !=0 then divide(CAST((coalesce(current.TOTAL_REAL_PAY, 0) - \n" +
                "   coalesce(mom.TOTAL_REAL_PAY, 0)) AS DOUBLE) , CAST(abs(coalesce(mom.TOTAL_REAL_PAY, 0)) AS DOUBLE)) * 100 else 0 end, 4) as MOM");
        sql.add("current.TOTAL_CAR_QUANTITY AS BUS_QUANTITY ");
        sql.add("round(case when coalesce(total.TOTAL_CAR_QUANTITY, 0) != 0 then divide(coalesce(current.TOTAL_CAR_QUANTITY, 0), \n" +
                "  coalesce(total.TOTAL_CAR_QUANTITY, 0)) * 100 else 0 end, 4) as BUS_QUANTITY_PERCENT ");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

}
