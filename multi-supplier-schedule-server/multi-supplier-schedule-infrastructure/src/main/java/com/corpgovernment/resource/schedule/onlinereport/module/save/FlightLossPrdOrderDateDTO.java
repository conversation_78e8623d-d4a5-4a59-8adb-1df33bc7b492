package com.corpgovernment.resource.schedule.onlinereport.module.save;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.save
 * @description:机票节省分析-提前预定天数分布
 * @author: md_wang
 * @create: 2022-08-02 18:04
 **/
@Data
public class FlightLossPrdOrderDateDTO {

    /**
     * 提前预定天数
     */
    @Column(name = "preOrderDate")
    @Type(value = Types.INTEGER)
    private Integer preOrderDate;

    /**
     * 票张数
     */
    @Column(name = "tickets")
    @Type(value = Types.INTEGER)
    private Integer tickets;

    /**
     * 超标损失金额
     */
    @Column(name = "rcLostAmt")
    @Type(value = Types.DECIMAL)
    private BigDecimal rcLostAmt;
    /**
     * 退票损失金额=退票费+前后收退票服务费
     */
    @Column(name = "refundLostAmt")
    @Type(value = Types.DECIMAL)
    private BigDecimal refundLostAmt;
    /**
     * 改签损失金额 = 改签费+改签差价+前后收改签服务费
     */
    @Column(name = "rebookLostAmt")
    @Type(value = Types.DECIMAL)
    private BigDecimal rebookLostAmt;

    /**
     * 全价票张数
     */
    @Column(name = "fullFaretkt")
    @Type(value = Types.INTEGER)
    private Integer fullFaretkt;

    /**
     * 成交净价(不含改签价差)
     */
    @Column(name = "netFare")
    @Type(value = Types.DECIMAL)
    private BigDecimal netFare;

    /**
     * 折扣分子=折扣*张数（仅国内，仅经济舱）
     */
    @Column(name = "priceRateFz")
    @Type(value = Types.DECIMAL)
    private BigDecimal priceRateFz;

    /**
     * 折扣分母=张数（仅国内，仅经济舱）
     */
    @Column(name = "priceRateFm")
    @Type(value = Types.DECIMAL)
    private BigDecimal priceRateFm;

    /**
     * 平均折扣=折扣分子/折扣分母
     */
    @Column(name = "avgDiscount")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgDiscount;
}
