package com.corpgovernment.resource.schedule.onlinereport.enums.travelposition;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.travelposition
 * @description:
 * @author: md_wang
 * @create: 2022-08-22 16:35
 **/
public enum TravelPositionStepTypeEnum {
    /**
     * 订单明细
     */
    TITLE("TripMap.OrderDetails"),
    /**
     * 正在出差
     */
    TRAVEL_ING("TripMap.OnTrip"),
    /**
     * 将要去
     */
    GOING("TripMap.GoingToTravel"),
    /**
     * 曾经去过，已离开
     */
    LEFTED("TripMap.AlreadyLeft"),
    ;

    private String sharkKey;

    public static String getSharkKey(String stepName) {
        if (TRAVEL_ING.name().equals(stepName)) {
            return TRAVEL_ING.getSharkKey();
        }
        if (GOING.name().equals(stepName)) {
            return GOING.getSharkKey();
        }
        if (LEFTED.name().equals(stepName)) {
            return LEFTED.getSharkKey();
        }
        return OrpConstants.EMPTY;

    }

    TravelPositionStepTypeEnum(String sharkKey) {
        this.sharkKey = sharkKey;
    }


    public String getSharkKey() {
        return sharkKey;
    }

    public void setSharkKey(String sharkKey) {
        this.sharkKey = sharkKey;
    }
}
