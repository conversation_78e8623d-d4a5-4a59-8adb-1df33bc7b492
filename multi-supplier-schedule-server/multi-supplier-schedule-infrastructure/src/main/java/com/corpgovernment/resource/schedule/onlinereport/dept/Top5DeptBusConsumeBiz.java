package com.corpgovernment.resource.schedule.onlinereport.dept;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBusTopDeptConsume;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptUidConsumeDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptUidConsumeDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.BusDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.BusTopDeptDTO;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportDeptConsumeDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptConsumeDaoService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.dept.sr.SrOnlineReportDeptConsumeDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.convert.TopDeptMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.PaginationHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:22
 * @Desc 成本中心消费金额排名
 */
@Service
public class Top5DeptBusConsumeBiz {

    @Autowired
    private OnlineReportDeptConsumeDaoImpl reportConsumeDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private TopDeptMapper topDeptMapper;

    @Autowired
    private SrOnlineReportDeptConsumeDaoImpl srReportConsumeDao;

    /**
     * 获取部门消费dao
     * @return
     */
    private OnlineReportDeptConsumeDaoService getDeptConsumeDaoService(BaseQueryConditionDTO baseQueryCondition) {
        boolean useSr = baseQueryCondition != null && BooleanUtils.isTrue(baseQueryCondition.useStarRocks);
        return useSr ? srReportConsumeDao : reportConsumeDao;
    }

    public OnlineReportDeptUidConsumeDetailInfo topDeptUidConsume(OnlineReportDeptUidConsumeDetailRequest request) throws Exception {
        OnlineReportDeptUidConsumeDetailInfo result = new OnlineReportDeptUidConsumeDetailInfo();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        baseQueryConditionDto.setLang(request.getLang());
        AnalysisObjectEnum downObjectEnum = request.getDrillDownObjectEnum();
        // 国际数据直接返回
        if (StringUtils.equalsIgnoreCase(request.getProductType(), "inter")) {
            return result;
        }
        List<BusTopDeptDTO> deptConsumeList = getDeptConsumeDaoService(baseQueryConditionDto).topDeptUidAnalysis(baseQueryConditionDto, AnalysisObjectEnum.UID,
                BusTopDeptDTO.class, request.getQueryBu(), request.getAnalysisObjectOrgInfo(),
                downObjectEnum, request.getDrillDownVal(), true, request.getUser());
        List<OnlineReportBusTopDeptConsume> onlineReportDeptConsumes = new ArrayList<>();
        BusDeptDTO sumAll = new BusDeptDTO();
        if (CollectionUtils.isNotEmpty(deptConsumeList)) {
            List<BusDeptDTO> consumeList = convertToVasoTopDeptList(deptConsumeList);
            BigDecimal sumAmount = consumeList.stream().map(BusDeptDTO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (int i = 0; i < consumeList.size(); i++) {
                BusDeptDTO deptConsume = consumeList.get(i);
                selfAdd(sumAll, deptConsume);
                onlineReportDeptConsumes.add(convertToBusTopDeptBO(deptConsume, StringUtils.EMPTY, sumAmount));
            }
            onlineReportDeptConsumes.sort(Comparator.comparing(OnlineReportBusTopDeptConsume::getTotalAmount).reversed()
                    .thenComparing(OnlineReportBusTopDeptConsume::getDimId).thenComparing(OnlineReportBusTopDeptConsume::getDim));
            Pager pager = BizUtils.initPager(request.getPage());
            PaginationHelper<OnlineReportBusTopDeptConsume> paginationHelper = new PaginationHelper<>();
            result.setBusList(paginationHelper.paginateFromAero(onlineReportDeptConsumes, pager.getPageIndex(), pager.getPageSize()));
            result.setTotalRecords(deptConsumeList.size());
        }
        return result;
    }

    private void selfAdd(BusDeptDTO sumTop, BusDeptDTO deptConsume) {
        sumTop.setTotalAmount(deptConsume.getTotalAmount().add(Optional.ofNullable(sumTop.getTotalAmount()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalOrderCount(deptConsume.getTotalOrderCount() + Optional.ofNullable(sumTop.getTotalOrderCount()).orElse(OrpConstants.ZERO));
    }

    private List<BusDeptDTO> convertToVasoTopDeptList(List<BusTopDeptDTO> topDeptDTO) {
        List<BusDeptDTO> boList = new ArrayList<>();
        for (BusTopDeptDTO dto : topDeptDTO) {
            boList.add(topDeptMapper.convertBusTopDeptDTO(dto));
        }
        return boList;
    }

    private OnlineReportBusTopDeptConsume convertToBusTopDeptBO(BusDeptDTO topDeptDTO, String aggType, BigDecimal sumAmount) {
        OnlineReportBusTopDeptConsume bo = new OnlineReportBusTopDeptConsume();
        bo.setDimId(StringUtils.trimToEmpty(topDeptDTO.getAggId()));
        bo.setDim(StringUtils.isEmpty(aggType) ? topDeptDTO.getAggType() : aggType);
        // 消费金额
        bo.setTotalAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalAmount()));
        // 订单数
        bo.setTotalOrderCount(topDeptDTO.getTotalOrderCount());
        /*消费金额占比*/
        bo.setAmountPercent(OrpReportUtils.divideUp(topDeptDTO.getTotalAmount(), sumAmount, OrpConstants.FOUR));
        return bo;
    }
}
