package com.corpgovernment.resource.schedule.onlinereport.enums;

/*
 * <AUTHOR>
 *
 * @date 2022/4/6 16:10
 *
 * @Desc 酒店rc
 */
public enum HotelRcEnum {
    // 低价RC
    L("ComplianceMonitor.lowpricerc"),
    // 最低价RC
    N("ComplianceMonitor.lowestpricerc"),
    // 协议RC
    A("ComplianceMonitor.pactrc");

    private String sharkKey;

    HotelRcEnum(String s) {
        this.sharkKey = s;
    }

    public String getSharkKey() {
        return sharkKey;
    }
}
