package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DeptStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDER_DATE;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
public class OnlineReportDeptFlightDetailStarRocksDaoImpl extends AbstractOnlineReportDeptDetailDao {

    @Override
    protected String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        String condition = StringUtils.EMPTY;
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        switch (subQueryReportBuTypeEnum) {
            case FLIGHT:
                break;
            case FLIGHT_N:
                condition = " and  flight_class = 'N'";
                break;
            case FLIGHT_I:
                condition = " and  flight_class = 'I'";
                break;
            case FLIGHT_AGREEMENT:
                condition = " and  (agreement_type = 'TA' or agreement_type = 'B2G')";
                break;
            case FLIGHT_NON_AGREEMENT:
                condition = " and  agreement_type != 'TA' and agreement_type != 'B2G'";
                break;
            default:
        }
        // audited <> 'F'
        condition += " and audited <> 'F'";
        return condition;
    }

    @Override
    protected String getProcutTypeCondition(String productType) {
        return getFlightClassCondition(productType);
    }

    protected String getFlightClassCondition(String flightClass) {
        if (StringUtils.equalsIgnoreCase(flightClass, "dom")) {
            return " and flight_class = 'N' ";
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(flightClass, "inter")) {
            return " and flight_class = 'I' ";
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }

    /**
     * 统计字段
     *
     * @return
     * @throws Exception
     */
    @Override
    protected String statical() {
        String flightStatusCondition =
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus");
        List sql = new ArrayList();
        sql.add("sum(coalesce(real_pay, 0)) as TOTAL_REAL_PAY");
        sql.add("sum(coalesce(quantity, 0)) as TOTAL_FLT_QUANTITY");
        sql.add(
                "sum(case when class_type = 'Y' then coalesce(quantity,0) else 0 end) as TOTAL_FLT_QUANTITY_ECONOMY_CLASS");
        sql.add(
                "sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(quantity,0) else 0 end) as TOTAL_FLT_QUANTITY_ECONOMY_CLASS_DOM");
        sql.add(
                "sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(price_rate,0) * coalesce(quantity,0) else 0 end) as TOTAL_FLT_PRICE_RATE_ECONOMY_CLASS_DOM");
        sql.add(
                "sum(case when class_type = 'F' or class_type = 'C' then coalesce(quantity,0) else 0 end) as TOTAL_FLT_QUANTITY_BUSINESS_FIRST_CLASS");
        sql.add(
                "sum(case when  flight_class = 'N' then coalesce(fullfaretkt, 0) else 0 end) as TOTAL_FLT_QUANTITY_FULL_PRICE_DOM");
        sql.add("sum(case when  flight_class = 'N' then coalesce(quantity, 0) else 0 end) as TOTAL_FLT_QUANTITY_DOM");
        sql.add("sum(case when class_type = 'Y'"
                + "then coalesce(quantity, 0) else 0 end) as TOTAL_FLT_QUANTITY_ECONOMY_CLASS_DONE");
        sql.add("sum(case when class_type = 'Y' "
                + "then coalesce(netfare, 0) else 0 end) as TOTAL_FLT_NETFARE_ECONOMY_CLASS_DONE");
        sql.add("sum(case when class_type = 'Y' "
                + "then coalesce(rebook_price_differential, 0) else 0 end) as TOTAL_FLT_REBOOK_PRICE_DIFFERENTIAL");
        sql.add("sum(case when class_type = 'Y' "
                + "then coalesce(tpms, 0) else 0 end) as TOTAL_FLT_MILE_ECONOMY_CLASS_DONE");
        sql.add("sum( case when (flight_class = 'N' and class_type = 'Y') or flight_class = 'I' then coalesce(00000, 0) " +
                "+ coalesce(save_amount_premium, 0) else 0 end) as TOTAL_FLT_SAVE");
        sql.add("sum( case when (flight_class = 'N' and class_type = 'Y') or flight_class = 'I' then coalesce(0000, 0) " +
                "+ coalesce(netfare_premium, 0) else 0 end) as TOTAL_FLT_NETFARE_3C_PREMIUM");
        sql.add("sum(coalesce(pre_order_date, 0) * coalesce(quantity, 0)) as TOTAL_FLT_PRE_ORDER_DATE");
        sql.add("sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as TOTAL_FLT_LOSS");
        sql.add("sum(coalesce(refund_fee, 0)) as TOTAL_FLT_REFUND_FEE");
        sql.add("sum(case when is_refund = 'T' then coalesce(refundtkt, 0) else 0 end ) as TOTAL_REFUNDTKT");
        sql.add("sum(coalesce(ordertkt, 0)) as TOTAL_ORDERTKT");
        sql.add("sum(coalesce(change_fee, 0)) as TOTAL_FLT_REBOOK_FEE");
        sql.add("sum(case when  (is_rebook = 'T' and is_refund = 'F') then coalesce(quantity, 0) else 0 end) as TOTAL_FLT_REBOOKTKT");
        sql.add("sum(case when is_rebook = 'T' and is_refund = 'T' then abs(coalesce(quantity, 0)) else 0 end) as TOTAL_REFUND_REBOOKTKT");

        sql.add(
                "count(distinct case when (is_refund = 'F' AND is_rc = 'T') then order_id end ) AS TOTAL_FLT_RC_TIMES");
        sql.add(
                "count(distinct case when (is_refund = 'F' AND is_online = 'M') then order_id end ) - " +
                        "count(distinct case when (is_refund = 'T' AND is_online = 'M') then order_id end ) AS TOTAL_APP_QUANTITY");
        sql.add(
                "count(distinct case when (is_refund = 'F' AND is_online = 'F') then order_id end ) - " +
                        "count(distinct case when (is_refund = 'T' AND is_online = 'F') then order_id end ) AS TOTAL_OFFLINE_QUANTITY");
        sql.add(
                "count(distinct case when (is_refund = 'F' AND is_online = 'T') then order_id end ) - " +
                        "count(distinct case when (is_refund = 'T' AND is_online = 'T') then order_id end ) AS TOTAL_ONLINE_QUANTITY");
        sql.add(
                "count(distinct case when is_refund = 'F' then order_id end ) - count(distinct case when is_refund = 'T' then order_id end ) AS TOTAL_FLT_CNT_ORDER");
        sql.add(
                "count(distinct case when is_refund = 'F' then order_id end ) AS TOTAL_FLT_ISSUE_CNT_ORDER");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    /**
     * 统计字段
     *
     * @return
     * @throws Exception
     */
    @Override
    protected String staticalForeign() {
        List sql = new ArrayList();
        sql.add("sum(coalesce(real_pay, 0)) as TOTAL_REAL_PAY");
        sql.add("sum(coalesce(quantity, 0)) as TOTAL_FLT_QUANTITY");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    /**
     * 同环比，总计数据
     *
     * @return
     */
    @Override
    protected String momAndYoy() {
        return "sum(coalesce(real_pay, 0))  as TOTAL_REAL_PAY";
    }

    /**
     * 占比
     *
     * @return
     */
    @Override
    protected String totalField() {
        return "SUM(coalesce(real_pay,0)) AS TOTAL_REAL_PAY, SUM(coalesce(quantity,0)) AS TOTAL_FLT_QUANTITY";
    }

    /**
     * 返回字段
     *
     * @return
     */
    public String baseQueryField() {
        return StringUtils.EMPTY;
    }

    /**
     * 返回字段
     *
     * @return
     */
    @Override
    public String baseQueryField(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        List sql = new ArrayList();
        List<DeptStatisticalsEnum> list1 = DeptStatisticalsEnum.getStaticalsByBizType(QueryReportBuTypeEnum.overview.toString(), subQueryReportBuTypeEnum);
        List<DeptStatisticalsEnum> list2 = DeptStatisticalsEnum.getStaticalsBySubBuType(subQueryReportBuTypeEnum.ordinal());
        list1.addAll(list2);
        for (DeptStatisticalsEnum deptStatisticalsEnum : list1) {
            switch (deptStatisticalsEnum) {
                case REAL_PAY:
                    sql.add("round(current.TOTAL_REAL_PAY, 4) AS REAL_PAY");
                    break;
                case REAL_PAY_PERCENT:
                    sql.add("round(case when coalesce(total.TOTAL_REAL_PAY, 0) !=0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0))"
                            + ", toFloat64(coalesce(total.TOTAL_REAL_PAY, 0))) * 100 else 0 end, 4) as REAL_PAY_PERCENT");
                    break;
                case YOY_LAST:
                    sql.add("round(case when coalesce(yoy.TOTAL_REAL_PAY, 0) !=0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(yoy.TOTAL_REAL_PAY, 0)) "
                            + ", toFloat64(abs(coalesce(yoy.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_LAST");
                    break;
                case YOY_BEFORE_LAST:
                    sql.add("round(case when coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0) !=0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)) "
                            + ", toFloat64(abs(coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_BEFORE_LAST");
                    break;
                case MOM:
                    sql.add("round(case when coalesce(mom.TOTAL_REAL_PAY, 0) !=0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(mom.TOTAL_REAL_PAY, 0)) "
                            + ", toFloat64(abs(coalesce(mom.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as MOM");
                    break;
                case FLT_QUANTITY:
                    sql.add("current.TOTAL_FLT_QUANTITY as FLT_QUANTITY");
                    break;
                case FLT_QUANTITY_PERCENT:
                    sql.add("round(case when coalesce(total.TOTAL_FLT_QUANTITY, 0) !=0 "
                            + "then divide(coalesce(current.TOTAL_FLT_QUANTITY, 0)"
                            + ", coalesce(total.TOTAL_FLT_QUANTITY, 0)) * 100 else 0 end, 4) as FLT_QUANTITY_PERCENT");
                    break;
                case FLT_QUANTITY_ECONOMY_CLASS:
                    sql.add("current.TOTAL_FLT_QUANTITY_ECONOMY_CLASS as FLT_QUANTITY_ECONOMY_CLASS");
                    break;
                case FLT_QUANTITY_BUSINESS_FIRST_CLASS:
                    sql.add("current.TOTAL_FLT_QUANTITY_BUSINESS_FIRST_CLASS as FLT_QUANTITY_BUSINESS_FIRST_CLASS");
                    break;
                case FLT_QUANTITY_FULL_PRICE_DOM:
                    sql.add("current.TOTAL_FLT_QUANTITY_FULL_PRICE_DOM as FLT_QUANTITY_FULL_PRICE_DOM");
                    break;
                case FLT_QUANTITY_FULL_PRICE_DOM_PERCENT:
                    sql.add("round(case when coalesce(current.TOTAL_FLT_QUANTITY_DOM, 0) !=0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_FLT_QUANTITY_FULL_PRICE_DOM, 0))"
                            + ", toFloat64(coalesce(current.TOTAL_FLT_QUANTITY_DOM, 0))) * 100 else 0 end, 4) as FLT_QUANTITY_FULL_PRICE_DOM_PERCENT");
                    break;
                case FLT_AVG_PRICE_ECONOMY_CLASS:
                    sql.add("round(case when coalesce(current.TOTAL_FLT_QUANTITY_ECONOMY_CLASS_DONE, 0) !=0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_FLT_NETFARE_ECONOMY_CLASS_DONE, 0)) "
                            + "+ toFloat64(coalesce(current.TOTAL_FLT_REBOOK_PRICE_DIFFERENTIAL, 0))"
                            + ", toFloat64(coalesce(current.TOTAL_FLT_QUANTITY_ECONOMY_CLASS_DONE, 0))) else 0 end, 4) as FLT_AVG_PRICE_ECONOMY_CLASS");
                    break;
                case FLT_AVG_PRICE_RATE_ECONOMY_CLASS_DOM:
                    sql.add("round(case when coalesce(current.TOTAL_FLT_QUANTITY_ECONOMY_CLASS_DOM, 0) !=0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_FLT_PRICE_RATE_ECONOMY_CLASS_DOM, 0))"
                            + ", toFloat64(coalesce(current.TOTAL_FLT_QUANTITY_ECONOMY_CLASS_DOM, 0))) else 0 end, 4) as FLT_AVG_PRICE_RATE_ECONOMY_CLASS_DOM");
                    break;
                case FLT_AVG_MILE_PRICE_ECONOMY_CLASS:
                    sql.add("round(case when coalesce(current.TOTAL_FLT_MILE_ECONOMY_CLASS_DONE, 0) !=0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_FLT_NETFARE_ECONOMY_CLASS_DONE, 0)) "
                            + "+ toFloat64(coalesce(current.TOTAL_FLT_REBOOK_PRICE_DIFFERENTIAL, 0))"
                            + ", toFloat64(coalesce(current.TOTAL_FLT_MILE_ECONOMY_CLASS_DONE, 0))) else 0 end, 4) as FLT_AVG_MILE_PRICE_ECONOMY_CLASS");
                    break;
                case FLT_SAVE:
                    sql.add("current.TOTAL_FLT_SAVE as FLT_SAVE");
                    break;
                case FLT_SAVE_RATE:
                    sql.add("round(case when coalesce(current.TOTAL_FLT_NETFARE_3C_PREMIUM, 0) !=0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_FLT_SAVE, 0))"
                            + ", toFloat64(coalesce(current.TOTAL_FLT_NETFARE_3C_PREMIUM, 0))) * 100 else 0 end, 4) as FLT_SAVE_RATE");
                    break;
                case FLT_AVG_PRE_ORDER_DATE:
                    sql.add("round(case when coalesce(current.TOTAL_FLT_QUANTITY, 0) !=0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_FLT_PRE_ORDER_DATE, 0))"
                            + ", toFloat64(coalesce(current.TOTAL_FLT_QUANTITY, 0))) else 0 end, 2) as FLT_AVG_PRE_ORDER_DATE");
                    break;
                case FLT_LOSS:
                    sql.add("current.TOTAL_FLT_LOSS as FLT_LOSS");
                    break;
                case FLT_REFUND_FEE:
                    sql.add("current.TOTAL_FLT_REFUND_FEE as FLT_REFUND_FEE");
                    break;
                case FLT_REFUND_RATE:
                    sql.add("round(case when coalesce(current.TOTAL_ORDERTKT, 0) != 0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_REFUNDTKT, 0))"
                            + ", toFloat64(coalesce(current.TOTAL_ORDERTKT, 0))) * 100 else 0 end, 4) as FLT_REFUND_RATE");
                    break;
                case FLT_REBOOK_FEE:
                    sql.add("current.TOTAL_FLT_REBOOK_FEE as FLT_REBOOK_FEE");
                    break;
                case FLT_REBOOK_RATE:// 既退又改的算改签
                    sql.add("round(case when coalesce(current.TOTAL_ORDERTKT, 0) !=0 "
                            + "then divide(coalesce(current.TOTAL_FLT_REBOOKTKT, 0), coalesce(current.TOTAL_ORDERTKT, 0)) * 100 else 0 end, 4) "
                            + "as FLT_REBOOK_RATE");
                    break;
                case FLT_REFUND_REBOOK_FEE:
                    sql.add(
                            "(current.TOTAL_FLT_REBOOK_FEE + current.TOTAL_FLT_REFUND_FEE)  as FLT_REFUND_REBOOK_FEE");
                    break;
                case FLT_REFUND_REBOOK_RATE:
                    sql.add("round(case when coalesce(current.TOTAL_ORDERTKT, 0) != 0 "
                            + "then divide(toFloat64(coalesce(current.TOTAL_REFUNDTKT, 0) + "
                            + "coalesce(current.TOTAL_FLT_REBOOKTKT, 0) - coalesce(current.TOTAL_REFUND_REBOOKTKT, 0))"
                            + ", toFloat64(coalesce(current.TOTAL_ORDERTKT, 0))) * 100 else 0 end, 4) as FLT_REFUND_REBOOK_RATE");
                    break;
                case FLT_RC_TIMES:
                    sql.add("current.TOTAL_FLT_RC_TIMES as FLT_RC_TIMES");
                    break;
                case FLT_RC_PERCENT:
                    sql.add("round(case when coalesce(current.TOTAL_FLT_ISSUE_CNT_ORDER, 0) !=0 "
                            + "then divide(coalesce(current.TOTAL_FLT_RC_TIMES, 0)"
                            + ", coalesce(current.TOTAL_FLT_ISSUE_CNT_ORDER, 0)) * 100 else 0 end, 4) as FLT_RC_PERCENT");
                    break;
                case FLT_APP_PERCENT:
                    sql.add("round(case when coalesce(current.TOTAL_FLT_CNT_ORDER, 0) !=0 "
                            + "then divide(coalesce(current.TOTAL_APP_QUANTITY, 0)"
                            + ", coalesce(current.TOTAL_FLT_CNT_ORDER, 0)) * 100 else 0 end, 4) as FLT_APP_PERCENT");
                    break;
                case FLT_ONLINE_PERCENT:
                    sql.add("round(case when coalesce(current.TOTAL_FLT_CNT_ORDER, 0) !=0 "
                            + "then divide(coalesce(current.TOTAL_ONLINE_QUANTITY, 0)"
                            + ", coalesce(current.TOTAL_FLT_CNT_ORDER, 0)) * 100 else 0 end, 4) as FLT_ONLINE_PERCENT");
                    break;
                case FLT_OFFLINE_PERCENT:
                    sql.add("round(case when coalesce(current.TOTAL_FLT_CNT_ORDER, 0) !=0 "
                            + "then divide(coalesce(current.TOTAL_OFFLINE_QUANTITY, 0)"
                            + ", coalesce(current.TOTAL_FLT_CNT_ORDER, 0)) * 100 else 0 end, 4) as FLT_OFFLINE_PERCENT");
                    break;
                default:
                    break;
            }
        }
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    @Override
    protected String orderByField() {
        return "TOTAL_REAL_PAY desc";
    }

    @Override
    protected BaseConditionPrebuilder.TableAndTimeColBind getTargetTableAndTimeColumn(String statisticalCaliber, Boolean isForegin) {
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = new BaseConditionPrebuilder.TableAndTimeColBind();
        if (isForegin) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                tableAndTimeColBind.setClickHouseTable(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD_FOREIGN_MULTI_CURRENCY);
            } else {
                tableAndTimeColBind.setClickHouseTable(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD_FOREIGN);
            }
            tableAndTimeColBind.setDateColumn(ORDER_DATE);
        } else {
            tableAndTimeColBind.setClickHouseTable(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
            tableAndTimeColBind.setDateColumn(REPORT_DATE);
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                tableAndTimeColBind.setDateColumn(ORDER_DATE);
            }
        }
        return tableAndTimeColBind;
    }

}
