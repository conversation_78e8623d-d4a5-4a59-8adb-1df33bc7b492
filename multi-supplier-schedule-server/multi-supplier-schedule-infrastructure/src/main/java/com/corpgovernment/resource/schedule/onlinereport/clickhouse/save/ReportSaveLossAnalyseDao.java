package com.corpgovernment.resource.schedule.onlinereport.clickhouse.save;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.save.FlightClassDistributionDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.save.FlightHotCityBaseDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.save.FlightLossPrdOrderDateDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.save.FlightPriceAvgAndMileageAvgDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.save.HotelSaveLossNightDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.save.HotelSaveLossStarDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.SharkLocaleEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.dao.impl.save
 * @description:
 * @author: md_wang
 * @create: 2022-08-01 17:40
 **/
@Service
@Slf4j
@Repository
public class ReportSaveLossAnalyseDao extends AbstractClickhouseBaseDao {
    private static final String LOG_TITLE_1 = "ReportSaveLossAnalyseDao>>queryPreOrderDate";
    private static final String LOG_TITLE_2 = "ReportSaveLossAnalyseDao>>queryFlightClassDistribution";
    private static final String LOG_TITLE_3 = "ReportSaveLossAnalyseDao>>queryFlightPrice";
    private static final String LOG_TITLE_4 = "ReportSaveLossAnalyseDao>>queryFlightMileage";
    private static final String LOG_TITLE_5 = "ReportSaveLossAnalyseDao>>queryHotelStar";
    private static final String LOG_TITLE_6 = "ReportSaveLossAnalyseDao>>queryHotelNight";
    private static final String LOG_TITLE_7 = "ReportSaveLossAnalyseDao>>queryFlightHotCity";


    /**
     * 查询机票提前预定天数
     */
    public List<FlightLossPrdOrderDateDTO> queryFlightPreOrderDate(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select case ");
        sqlBuilder.append(" WHEN pre_order_date >= 0 AND pre_order_date < 1 THEN 0  ");
        sqlBuilder.append(" WHEN pre_order_date >= 1 AND pre_order_date < 2 THEN 1 ");
        sqlBuilder.append(" WHEN pre_order_date >= 2 AND pre_order_date < 3 THEN 2 ");
        sqlBuilder.append(" WHEN pre_order_date >= 3 AND pre_order_date < 4 THEN 3 ");
        sqlBuilder.append(" WHEN pre_order_date >= 4 AND pre_order_date < 5 THEN 4 ");
        sqlBuilder.append("  WHEN pre_order_date >= 5 THEN 6 ELSE pre_order_date END AS preOrderDate ");
        sqlBuilder.append(" ,COALESCE(sum(quantity),0) as tickets ");
        sqlBuilder.append(" ,COALESCE(sum(case when low_rid <>'' and low_rid is not NULL and abs(coalesce(netfare,0))>abs(coalesce(corp_price_adj,0)) and is_refund = 'F' " +
                "then coalesce(netfare,0)-coalesce(corp_price_adj,0)*quantity else 0 end),0) as rcLostAmt");
        sqlBuilder.append(" ,COALESCE(SUM(refund_service_fee+refund_behind_service_fee+refund_fee),0) as refundLostAmt ");
        sqlBuilder.append(" ,COALESCE(SUM(change_fee+rebook_price_differential+rebook_service_fee+rebook_behind_service_fee),0) as rebookLostAmt ");
        sqlBuilder.append(" ,SUM(fullfaretkt) as fullFaretkt ");
        sqlBuilder.append(" ,SUM(netfare) as netFare");
        sqlBuilder.append(" ,cast(sum(price_rate*quantity) as double) as priceRateFz ");
        sqlBuilder.append(" ,cast(sum(quantity) as double) as priceRateFm ");
        sqlBuilder.append(" ,divide(toFloat64(priceRateFz),toFloat64(priceRateFm)) as avgDiscount ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d = ? and class_type = 'Y' ");
        List<Object> paramList = Lists.newArrayList();
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, paramList));
        }
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        // 产品要求和行为分析保持一致“国内经济舱”
        sqlBuilder.append(" and flight_class = 'N' ");
        sqlBuilder.append(" and pre_order_date >=0 ");
        sqlBuilder.append(" and audited <> 'F' ");
        sqlBuilder.append(" group by preOrderDate  ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), paramList,
                (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE_1, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, FlightLossPrdOrderDateDTO.class, "queryFlightPreOrderDate");
    }

    /**
     * 查询机票仓等分布
     */
    public List<FlightClassDistributionDTO> queryFlightClassDist(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        /**
         * class_type
         * Y(经济舱(包含全价经济舱、折扣经济舱、前返经济舱)),S(超级经济舱),F(头等舱),C(公务舱)
         */
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select ");
        sqlBuilder.append("COALESCE(sum(case when class_type = 'Y' then quantity else 0 end ),0) as economyTicket ");
        sqlBuilder.append(",if(SUM(quantity)!=0,divide(sum(case when class_type = 'Y' then quantity else 0 end ),SUM(quantity)),toFloat64(null)) as economyTicketRate ");

        sqlBuilder.append(",COALESCE(sum(case when class_type = 'C' then quantity else 0 end ),0) as businessTicket ");
        sqlBuilder.append(",if(SUM(quantity)!=0,divide(sum(case when class_type = 'C' then quantity else 0 end ),SUM(quantity)),toFloat64(null)) as businessTicketRate ");
        sqlBuilder.append(",COALESCE(sum(case when class_type = 'F' then quantity else 0 end ),0) as firstTicket ");
        sqlBuilder.append(",if(SUM(quantity)!=0,divide(sum(case when class_type = 'F' then quantity else 0 end ),SUM(quantity)),toFloat64(null)) as firstTicketRate ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d = ?");
        List<Object> paramList = Lists.newArrayList();
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, paramList));
        }
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        sqlBuilder.append(" and audited <> 'F' ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), paramList,
                (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE_2, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, FlightClassDistributionDTO.class, "queryFlightClassDist");

    }

    /**
     * 查询机票仓等分布
     */
    
    public List<FlightClassDistributionDTO> queryFlightClassDistCorpAndIndustry(String startTime, String endTime, String statisticalCaliber,
                                                                                DataTypeEnum dataTypeEnum,
                                                                                String productType,
                                                                                List<String> industryList,
                                                                                String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        /**
         * class_type
         * Y(经济舱(包含全价经济舱、折扣经济舱、前返经济舱)),S(超级经济舱),F(头等舱),C(公务舱)
         */
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select ");
        sqlBuilder.append("COALESCE(sum(case when class_type = 'Y' then quantity else 0 end ),0) as economyTicket ");
        sqlBuilder.append(",if(SUM(quantity)!=0,divide(sum(case when class_type = 'Y' then quantity else 0 end ),SUM(quantity)),toFloat64(null)) as economyTicketRate ");

        sqlBuilder.append(",COALESCE(sum(case when class_type = 'C' then quantity else 0 end ),0) as businessTicket ");
        sqlBuilder.append(",if(SUM(quantity)!=0,divide(sum(case when class_type = 'C' then quantity else 0 end ),SUM(quantity)),toFloat64(null)) as businessTicketRate ");
        sqlBuilder.append(",COALESCE(sum(case when class_type = 'F' then quantity else 0 end ),0) as firstTicket ");
        sqlBuilder.append(",if(SUM(quantity)!=0,divide(sum(case when class_type = 'F' then quantity else 0 end ),SUM(quantity)),toFloat64(null)) as firstTicketRate ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d = ?");
        List<Object> paramList = Lists.newArrayList();
        if (DataTypeEnum.INDUSTRY.equals(dataTypeEnum)) {
            if (isBookCaliber) {
                sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, paramList, ORDERDT));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, paramList));
            }
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP.equals(dataTypeEnum)) {
            if (isBookCaliber) {
                sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, paramList, ORDERDT));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, paramList));
            }
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        sqlBuilder.append(" and audited <> 'F' ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), paramList,
                (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE_2, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, FlightClassDistributionDTO.class, "queryFlightClassDist");

    }

    /**
     * 查询机票折扣分布
     */
    public List<FlightClassDistributionDTO> queryFlightDiscount(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select ");
        /**
         * 票张数占比
         */
        sqlBuilder.append("COALESCE(cast(SUM(case when toFloat64(price_rate)>=0 and toFloat64(price_rate)<0.1 then quantity else 0 end) as double),0) AS ticket0");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(SUM(case when toFloat64(price_rate)>=0 " +
                "and toFloat64(price_rate)<0.1 then quantity else 0 end))/cast(SUM(quantity)as double),toFloat64(null)) AS ticketRate0");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.1 and toFloat64(price_rate)<0.2 then quantity else 0 end) as double),0) AS ticket1");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(toFloat64(SUM(case when toFloat64(price_rate)>=0.1 " +
                "and toFloat64(price_rate)<0.2 then quantity else 0 end))/cast(SUM(quantity)as double),0),toFloat64(null)) AS ticketRate1");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.2 and toFloat64(price_rate)<0.3 then quantity else 0 end) as double ),0) AS ticket2");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.2 " +
                "and toFloat64(price_rate)<0.3 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate2");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.3 and toFloat64(price_rate)<0.4 then quantity else 0 end) as double ),0) AS ticket3");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.3 " +
                "and toFloat64(price_rate)<0.4 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate3");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.4 and toFloat64(price_rate)<0.5 then quantity else 0 end) as double ),0) AS ticket4");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.4 " +
                "and toFloat64(price_rate)<0.5 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate4");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.5 and toFloat64(price_rate)<0.6 then quantity else 0 end) as double ),0) AS ticket5");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.5 " +
                "and toFloat64(price_rate)<0.6 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate5");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.6 and toFloat64(price_rate)<0.7 then quantity else 0 end) as double ),0) AS ticket6");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.6 " +
                "and toFloat64(price_rate)<0.7 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate6");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.7 and toFloat64(price_rate)<0.8 then quantity else 0 end) as double ),0) AS ticket7");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.7 " +
                "and toFloat64(price_rate)<0.8 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate7");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.8 and toFloat64(price_rate)<0.9 then quantity else 0 end) as double ),0) AS ticket8");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.8 " +
                "and toFloat64(price_rate)<0.9 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate8");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.9 and toFloat64(price_rate)<1 then quantity else 0 end) as double ),0) AS ticket9");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.9 " +
                "and toFloat64(price_rate)<1 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate9");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)=1 then quantity else 0 end) as double ),0) AS ticket10");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)=1 " +
                "then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate10");

        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d = ?");
        List<Object> paramList = Lists.newArrayList();
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, paramList));
        }
        // 产品要求和行为分析保持一致“国内经济舱”
        sqlBuilder.append(" and flight_class = 'N' ");
        sqlBuilder.append(" and class_type = 'Y' ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        sqlBuilder.append(" and audited <> 'F' ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), paramList,
                (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE_2, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, FlightClassDistributionDTO.class, "queryFlightDiscount");

    }

    /**
     * 查询机票折扣分布
     */
    
    public List<FlightClassDistributionDTO> queryFlightDiscountCorpAndIndustry(String startTime, String endTime, String statisticalCaliber,
                                                                               DataTypeEnum dataTypeEnum,
                                                                               String productType,
                                                                               List<String> industryList,
                                                                               String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select ");
        /**
         * 票张数占比
         */
        sqlBuilder.append("COALESCE(cast(SUM(case when toFloat64(price_rate)>=0 and toFloat64(price_rate)<0.1 then quantity else 0 end) as double),0) AS ticket0");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(SUM(case when toFloat64(price_rate)>=0 " +
                "and toFloat64(price_rate)<0.1 then quantity else 0 end))/cast(SUM(quantity)as double),toFloat64(null)) AS ticketRate0");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.1 and toFloat64(price_rate)<0.2 then quantity else 0 end) as double),0) AS ticket1");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(toFloat64(SUM(case when toFloat64(price_rate)>=0.1 " +
                "and toFloat64(price_rate)<0.2 then quantity else 0 end))/cast(SUM(quantity)as double),0),toFloat64(null)) AS ticketRate1");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.2 and toFloat64(price_rate)<0.3 then quantity else 0 end) as double ),0) AS ticket2");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.2 " +
                "and toFloat64(price_rate)<0.3 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate2");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.3 and toFloat64(price_rate)<0.4 then quantity else 0 end) as double ),0) AS ticket3");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.3 " +
                "and toFloat64(price_rate)<0.4 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate3");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.4 and toFloat64(price_rate)<0.5 then quantity else 0 end) as double ),0) AS ticket4");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.4 " +
                "and toFloat64(price_rate)<0.5 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate4");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.5 and toFloat64(price_rate)<0.6 then quantity else 0 end) as double ),0) AS ticket5");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.5 " +
                "and toFloat64(price_rate)<0.6 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate5");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.6 and toFloat64(price_rate)<0.7 then quantity else 0 end) as double ),0) AS ticket6");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.6 " +
                "and toFloat64(price_rate)<0.7 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate6");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.7 and toFloat64(price_rate)<0.8 then quantity else 0 end) as double ),0) AS ticket7");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.7 " +
                "and toFloat64(price_rate)<0.8 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate7");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.8 and toFloat64(price_rate)<0.9 then quantity else 0 end) as double ),0) AS ticket8");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.8 " +
                "and toFloat64(price_rate)<0.9 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate8");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.9 and toFloat64(price_rate)<1 then quantity else 0 end) as double ),0) AS ticket9");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)>=0.9 " +
                "and toFloat64(price_rate)<1 then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate9");

        sqlBuilder.append(",COALESCE(cast(SUM(case when toFloat64(price_rate)=1 then quantity else 0 end) as double ),0) AS ticket10");
        sqlBuilder.append(",if(SUM(quantity)!=0,COALESCE(cast(SUM(case when toFloat64(price_rate)=1 " +
                "then quantity else 0 end) as double )/cast(SUM(quantity) as double),0),toFloat64(null)) AS ticketRate10");

        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d = ?");
        List<Object> paramList = Lists.newArrayList();
        if (DataTypeEnum.INDUSTRY.equals(dataTypeEnum)) {
            if (isBookCaliber) {
                sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, paramList, ORDERDT));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, paramList));
            }
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP.equals(dataTypeEnum)) {
            if (isBookCaliber) {
                sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, paramList, ORDERDT));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, paramList));
            }
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        // 产品要求和行为分析保持一致“国内经济舱”
        sqlBuilder.append(" and flight_class = 'N' ");
        sqlBuilder.append(" and class_type = 'Y' ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        sqlBuilder.append(" and audited <> 'F' ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), paramList,
                (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE_2, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, FlightClassDistributionDTO.class, "queryFlightDiscountCorpAndIndustry");

    }

    /**
     * 机票均价分布
     */
    public List<FlightPriceAvgAndMileageAvgDTO> queryFlightPrice(BaseQueryConditionDTO requestDto,
                                                                 String productType,
                                                                 String lang) throws Exception {
        /**
         * class_type
         * Y(经济舱(包含全价经济舱、折扣经济舱、前返经济舱)),S(超级经济舱),F(头等舱),C(公务舱)
         */
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select case ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 0 and toFloat64(d.avg_price) < 300 then '0-300' ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 300 and toFloat64(d.avg_price) < 600 then '300-600' ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 600 and toFloat64(d.avg_price) < 900 then '600-900' ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 900 and toFloat64(d.avg_price) < 1200 then '900-1200' ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 1200 and toFloat64(d.avg_price) < 1500 then '1200-1500' ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 1500  then '>=1500' end as avgPrice ");
        sqlBuilder.append(",sum(d.totalQuantity) as totalQuantity ");
        sqlBuilder.append(",sum(d.totalPrice) as totalPrice ");
        sqlBuilder.append(OrpConstants.FROM).append("(");
        sqlBuilder.append("select order_id ");
        sqlBuilder.append(",COALESCE(sum(quantity),0) as totalQuantity ");
        sqlBuilder.append(",COALESCE(toFloat64(sum(netfare+rebook_price_differential)),0) as totalPrice");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(totalPrice,totalQuantity),toFloat64(null)) as avg_price ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ? ");
        sqlBuilder.append("and class_type = 'Y' AND fee_type = '因公' AND audited <> 'F' ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        /**
         * 时间参数
         */
        List<Object> paramList = Lists.newArrayList();
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildFlightAvgPriceSqlWithTime(
                    paramList, requestDto.getStartTime(), requestDto.getEndTime(), ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildFlightAvgPriceSqlWithTime(
                    paramList, requestDto.getStartTime(), requestDto.getEndTime(), REPORT_DATE));
        }
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, paramList));
        if (CollectionUtils.isNotEmpty(requestDto.getStartCityId())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(requestDto.getStartCityId(), "departure_city_id", paramList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getArriveCityId())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(requestDto.getArriveCityId(), "arrival_city_id", paramList));
        }
        if (StringUtils.isNotBlank(requestDto.getFlightCity())) {
            if (StringUtils.isEmpty(lang) || SharkLocaleEnum.ZH_CN.getLoacal().equals(lang)) {
                paramList.add(requestDto.getFlightCity());
                sqlBuilder.append(" and flight_city = ? ");
            } else {
                paramList.add(requestDto.getFlightCity());
                sqlBuilder.append(" and lower(flight_city_en) = lower(?) ");
            }
        }
        sqlBuilder.append(" group by order_id ");
        sqlBuilder.append(") d ");
        sqlBuilder.append("where d.avg_price >= 0 ");
        sqlBuilder.append("group by avgPrice ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), paramList,
                (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE_3, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, FlightPriceAvgAndMileageAvgDTO.class, "queryFlightPrice");

    }

    /**
     * 机票均价分布
     */
    
    public List<FlightPriceAvgAndMileageAvgDTO> queryFlightPriceCorpAndIndustry(String startTime, String endTime, String statisticalCaliber,
                                                                                DataTypeEnum dataTypeEnum,
                                                                                String productType,
                                                                                String industry, String lang,
                                                                                List<Integer> startCityId, List<Integer> arrivalCityId, String flightCity,
                                                                                String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        /**
         * class_type
         * Y(经济舱(包含全价经济舱、折扣经济舱、前返经济舱)),S(超级经济舱),F(头等舱),C(公务舱)
         */
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select case ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 0 and toFloat64(d.avg_price) < 300 then '0-300' ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 300 and toFloat64(d.avg_price) < 600 then '300-600' ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 600 and toFloat64(d.avg_price) < 900 then '600-900' ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 900 and toFloat64(d.avg_price) < 1200 then '900-1200' ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 1200 and toFloat64(d.avg_price) < 1500 then '1200-1500' ");
        sqlBuilder.append("when toFloat64(d.avg_price) >= 1500  then '>=1500' end as avgPrice ");
        sqlBuilder.append(",sum(d.totalQuantity) as totalQuantity ");
        sqlBuilder.append(",sum(d.totalPrice) as totalPrice ");
        sqlBuilder.append(OrpConstants.FROM).append("(");
        sqlBuilder.append("select order_id ");
        sqlBuilder.append(",COALESCE(sum(quantity),0) as totalQuantity ");
        sqlBuilder.append(",COALESCE(toFloat64(sum(netfare+rebook_price_differential)),0) as totalPrice");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(totalPrice,totalQuantity),toFloat64(null)) as avg_price ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ? ");
        sqlBuilder.append("and class_type = 'Y' AND fee_type = '" + SharkUtils.getChineseVal("FlightFeeType") + "' AND audited <> 'F' ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        /**
         * 时间参数
         */
        List<Object> paramList = Lists.newArrayList();
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildFlightAvgPriceSqlWithTime(
                    paramList, startTime, endTime, ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildFlightAvgPriceSqlWithTime(
                    paramList, startTime, endTime, REPORT_DATE));
        }
        if (DataTypeEnum.INDUSTRY.equals(dataTypeEnum)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlByOne(industry, paramList));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP.equals(dataTypeEnum)) {
            // ignore
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        if (CollectionUtils.isNotEmpty(startCityId)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(startCityId, "departure_city_id", paramList));
        }
        if (CollectionUtils.isNotEmpty(arrivalCityId)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(arrivalCityId, "arrival_city_id", paramList));
        }
        if (StringUtils.isNotBlank(flightCity)) {
            if (StringUtils.isEmpty(lang) || SharkLocaleEnum.ZH_CN.getLoacal().equals(lang)) {
                paramList.add(flightCity);
                sqlBuilder.append(" and flight_city = ? ");
            } else {
                paramList.add(flightCity);
                sqlBuilder.append(" and lower(flight_city_en) = lower(?) ");
            }
        }
        sqlBuilder.append(" group by order_id ");
        sqlBuilder.append(") d ");
        sqlBuilder.append("where d.avg_price >= 0 ");
        sqlBuilder.append("group by avgPrice ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), paramList,
                (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE_3, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, FlightPriceAvgAndMileageAvgDTO.class, "queryFlightPriceCorpAndIndustry");

    }

    /**
     * 公里数均价
     */
    public List<FlightPriceAvgAndMileageAvgDTO> queryFlightMileage(BaseQueryConditionDTO requestDto,
                                                                   String productType,
                                                                   String lang) throws Exception {
        /**
         * class_type
         * Y(经济舱(包含全价经济舱、折扣经济舱、前返经济舱)),S(超级经济舱),F(头等舱),C(公务舱)
         */
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select case ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 0 and toFloat64(d.avg_mileage) < 0.25 then '0-0.25' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 0.25 and toFloat64(d.avg_mileage) < 0.5 then '0.25-0.5' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 0.5 and toFloat64(d.avg_mileage) < 0.75 then '0.5-0.75' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 0.75 and toFloat64(d.avg_mileage) < 1 then '0.75-1' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 1 and toFloat64(d.avg_mileage) < 1.25 then '1-1.25' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 1.25 and toFloat64(d.avg_mileage) < 1.5 then '1.25-1.5' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 1.5  then '>=1.5' end as avgMileage ");
        sqlBuilder.append(",sum(d.totalQuantity) as totalQuantity ");
        sqlBuilder.append(OrpConstants.FROM).append("(");
        sqlBuilder.append("select order_id ");
        sqlBuilder.append(",COALESCE(sum(quantity),0) as totalQuantity ");
        sqlBuilder.append(",if(SUM(tpms)!=0,divide(toFloat64(sum(netfare+rebook_price_differential)),toFloat64(SUM(tpms))),toFloat64(null)) as avg_mileage ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ? ");
        sqlBuilder.append("and class_type = 'Y' AND fee_type = '因公' AND audited <> 'F' and tpms!=0 ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        /**
         * 时间参数
         */
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildFlightAvgPriceSqlWithTime(
                paramList, requestDto.getStartTime(), requestDto.getEndTime(), isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, paramList));
        if (CollectionUtils.isNotEmpty(requestDto.getStartCityId())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(requestDto.getStartCityId(), "departure_city_id", paramList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getArriveCityId())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(requestDto.getArriveCityId(), "arrival_city_id", paramList));
        }
        if (StringUtils.isNotBlank(requestDto.getFlightCity())) {
            if (StringUtils.isEmpty(lang) || SharkLocaleEnum.ZH_CN.getLoacal().equals(lang)) {
                paramList.add(requestDto.getFlightCity());
                sqlBuilder.append(" and flight_city = ? ");
            } else {
                paramList.add(requestDto.getFlightCity());
                sqlBuilder.append(" and lower(flight_city_en) = lower(?) ");
            }
        }
        sqlBuilder.append(" group by order_id ");
        sqlBuilder.append(") d ");
        sqlBuilder.append("where d.avg_mileage >= 0 ");
        sqlBuilder.append("group by ");
        sqlBuilder.append("case when toFloat64(d.avg_mileage) >= 0 and toFloat64(d.avg_mileage) < 0.25 then '0-0.25' ");
        sqlBuilder.append("when toFloat64(d.avg_mileage) >= 0.25 and toFloat64(d.avg_mileage) < 0.5 then '0.25-0.5' ");
        sqlBuilder.append("when toFloat64(d.avg_mileage) >= 0.5 and toFloat64(d.avg_mileage) < 0.75 then '0.5-0.75' ");
        sqlBuilder.append("when toFloat64(d.avg_mileage) >= 0.75 and toFloat64(d.avg_mileage) < 1 then '0.75-1' ");
        sqlBuilder.append("when toFloat64(d.avg_mileage) >= 1 and toFloat64(d.avg_mileage) < 1.25 then '1-1.25' ");
        sqlBuilder.append("when toFloat64(d.avg_mileage) >= 1.25 and toFloat64(d.avg_mileage) < 1.5 then '1.25-1.5' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 1.5  then '>=1.5' end ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_4, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, FlightPriceAvgAndMileageAvgDTO.class, "queryFlightMileage");

    }

    /**
     * 公里数均价
     */
    
    public List<FlightPriceAvgAndMileageAvgDTO> queryFlightMileageCorpAndIndustry(String startTime, String endTime, String statisticalCaliber,
                                                                                  DataTypeEnum dataTypeEnum,
                                                                                  String productType,
                                                                                  List<String> industryList, String lang,
                                                                                  List<Integer> startCityId, List<Integer> arrivalCityId, String flightCity,
                                                                                  String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        /**
         * class_type
         * Y(经济舱(包含全价经济舱、折扣经济舱、前返经济舱)),S(超级经济舱),F(头等舱),C(公务舱)
         */
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select case ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 0 and toFloat64(d.avg_mileage) < 0.25 then '0-0.25' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 0.25 and toFloat64(d.avg_mileage) < 0.5 then '0.25-0.5' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 0.5 and toFloat64(d.avg_mileage) < 0.75 then '0.5-0.75' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 0.75 and toFloat64(d.avg_mileage) < 1 then '0.75-1' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 1 and toFloat64(d.avg_mileage) < 1.25 then '1-1.25' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 1.25 and toFloat64(d.avg_mileage) < 1.5 then '1.25-1.5' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 1.5  then '>=1.5' end as avgMileage ");
        sqlBuilder.append(",sum(d.totalQuantity) as totalQuantity ");
        sqlBuilder.append(OrpConstants.FROM).append("(");
        sqlBuilder.append("select order_id ");
        sqlBuilder.append(",COALESCE(sum(quantity),0) as totalQuantity ");
        sqlBuilder.append(",if(SUM(tpms)!=0,divide(toFloat64(sum(netfare+rebook_price_differential)),toFloat64(SUM(tpms))),toFloat64(null)) as avg_mileage ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ? ");
        sqlBuilder.append("and class_type = 'Y' AND fee_type = '" + SharkUtils.getChineseVal("FlightFeeType") + "' AND audited <> 'F' and tpms!=0 ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        /**
         * 时间参数
         */
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildFlightAvgPriceSqlWithTime(paramList, startTime, endTime, isBookCaliber ? ORDERDT : REPORT_DATE));
        if (DataTypeEnum.INDUSTRY.equals(dataTypeEnum)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(industryList, paramList));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP.equals(dataTypeEnum)) {
            // ignore
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        if (CollectionUtils.isNotEmpty(startCityId)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(startCityId, "departure_city_id", paramList));
        }
        if (CollectionUtils.isNotEmpty(arrivalCityId)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(arrivalCityId, "arrival_city_id", paramList));
        }
        if (StringUtils.isNotBlank(flightCity)) {
            if (StringUtils.isEmpty(lang) || SharkLocaleEnum.ZH_CN.getLoacal().equals(lang)) {
                paramList.add(flightCity);
                sqlBuilder.append(" and flight_city = ? ");
            } else {
                paramList.add(flightCity);
                sqlBuilder.append(" and lower(flight_city_en) = lower(?) ");
            }
        }
        sqlBuilder.append(" group by order_id ");
        sqlBuilder.append(") d ");
        sqlBuilder.append("where d.avg_mileage >= 0 ");
        sqlBuilder.append("group by ");
        sqlBuilder.append("case when toFloat64(d.avg_mileage) >= 0 and toFloat64(d.avg_mileage) < 0.25 then '0-0.25' ");
        sqlBuilder.append("when toFloat64(d.avg_mileage) >= 0.25 and toFloat64(d.avg_mileage) < 0.5 then '0.25-0.5' ");
        sqlBuilder.append("when toFloat64(d.avg_mileage) >= 0.5 and toFloat64(d.avg_mileage) < 0.75 then '0.5-0.75' ");
        sqlBuilder.append("when toFloat64(d.avg_mileage) >= 0.75 and toFloat64(d.avg_mileage) < 1 then '0.75-1' ");
        sqlBuilder.append("when toFloat64(d.avg_mileage) >= 1 and toFloat64(d.avg_mileage) < 1.25 then '1-1.25' ");
        sqlBuilder.append("when toFloat64(d.avg_mileage) >= 1.25 and toFloat64(d.avg_mileage) < 1.5 then '1.25-1.5' ");
        sqlBuilder.append("when  toFloat64(d.avg_mileage) >= 1.5  then '>=1.5' end ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), paramList, (req, statement) -> mapCommonRequest(paramList, statement,
                ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_4, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, FlightPriceAvgAndMileageAvgDTO.class, "queryFlightMileage");

    }


    /**
     * 酒店-星级均价
     */
    public List<HotelSaveLossStarDTO> queryHotelStar(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append("sum(case when star=0 then quantity else 0 end) as starNoTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=0 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starNoRate ");
        sqlBuilder.append(",sum(case when star=1 then quantity else 0 end) as starOneTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=1 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starOneRate ");
        sqlBuilder.append(",sum(case when star=2 then quantity else 0 end) as starTwoTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=2 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starTwoRate ");
        sqlBuilder.append(",sum(case when star=3 then quantity else 0 end) as starThreeTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=3 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starThreeRate ");
        sqlBuilder.append(",sum(case when star=4 then quantity else 0 end) as starFourTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=4 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starFourRate ");
        sqlBuilder.append(",sum(case when star=5 then quantity else 0 end) as starFiveTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=5 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starFiveRate ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and is_oversea in ('O','F') ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and is_oversea = 'T' ");
        }
        sqlBuilder.append(" and  order_status = '已完成' ");
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, isBookCaliber ? ORDERDT : "deal_date"));
        if (CollectionUtils.isNotEmpty(requestDto.getCityIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(requestDto.getCityIds(), "city", paramList));
        }
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_5, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, HotelSaveLossStarDTO.class, "queryHotelStar");
    }

    /**
     * 酒店-星级均价
     */
    
    public List<HotelSaveLossStarDTO> queryHotelStarCorpAndIndustry(String startTime, String endTime, String statisticalCaliber,
                                                                    DataTypeEnum dataTypeEnum,
                                                                    String productType,
                                                                    List<String> industryList,
                                                                    List<Integer> cityIds, String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append("sum(case when star=0 then quantity else 0 end) as starNoTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=0 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starNoRate ");
        sqlBuilder.append(",sum(case when star=1 then quantity else 0 end) as starOneTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=1 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starOneRate ");
        sqlBuilder.append(",sum(case when star=2 then quantity else 0 end) as starTwoTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=2 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starTwoRate ");
        sqlBuilder.append(",sum(case when star=3 then quantity else 0 end) as starThreeTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=3 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starThreeRate ");
        sqlBuilder.append(",sum(case when star=4 then quantity else 0 end) as starFourTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=4 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starFourRate ");
        sqlBuilder.append(",sum(case when star=5 then quantity else 0 end) as starFiveTicket ");
        sqlBuilder.append(",if(sum(quantity)!=0,divide(sum(case when  star=5 then quantity else 0 end),sum(quantity)),toFloat64(null)) as starFiveRate ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and is_oversea in ('O','F') ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and is_oversea = 'T' ");
        }
        sqlBuilder.append(" and  order_status = '" + OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus") + "' ");
        List<Object> paramList = Lists.newArrayList();
        if (DataTypeEnum.INDUSTRY.equals(dataTypeEnum)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, paramList, isBookCaliber ? ORDERDT : "deal_date"));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP.equals(dataTypeEnum)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, paramList, isBookCaliber ? ORDERDT : "deal_date"));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        if (CollectionUtils.isNotEmpty(cityIds)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(cityIds, "city", paramList));
        }
        return queryBySql(sqlBuilder.toString(), paramList, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_5, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, HotelSaveLossStarDTO.class, "queryHotelStar");
    }

    /**
     * 酒店-间夜均价
     */
    public List<HotelSaveLossNightDTO> queryHotelNight(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select case ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 0 and toFloat64(d.avg_night) < 100 then '0-100' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 100 and toFloat64(d.avg_night) < 200 then '100-200' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 200 and toFloat64(d.avg_night) < 300 then '200-300' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 300 and toFloat64(d.avg_night) < 400 then '300-400' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 400 and toFloat64(d.avg_night) < 500 then '400-500' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 500 and toFloat64(d.avg_night) < 600 then '500-600' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 600 and toFloat64(d.avg_night) < 700 then '600-700' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 700 and toFloat64(d.avg_night) < 800 then '700-800' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 800 and toFloat64(d.avg_night) < 900 then '800-900' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 900 and toFloat64(d.avg_night) < 1000 then '900-1000' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 1000 and toFloat64(d.avg_night) < 1500 then '1000-1500' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 1500 and toFloat64(d.avg_night) < 2000 then '1500-2000' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 2000  then '>=2000' end as avgNight ");
        sqlBuilder.append(",sum(d.totalQuantity) as totalQuantity ");
        sqlBuilder.append(",sum(d.totalPrice) as totalPrice ");
        sqlBuilder.append(OrpConstants.FROM).append(" ( ");
        sqlBuilder.append("select order_id, ");
        sqlBuilder.append("   COALESCE(sum(quantity),0) as totalQuantity, ");
        sqlBuilder.append("   COALESCE(toFloat64(sum(room_price)),0) as totalPrice, ");
        sqlBuilder.append("   if(sum(quantity)!=0,divide(toFloat64(sum(room_price)),toFloat64(sum(quantity))),toFloat64(null)) as avg_night ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d=? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and is_oversea in ('O','F') ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and is_oversea = 'T' ");
        }
        sqlBuilder.append(" and  order_status = '已完成' ");
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, isBookCaliber ? ORDERDT : "deal_date"));
        if (CollectionUtils.isNotEmpty(requestDto.getCityIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(requestDto.getCityIds(), "city", paramList));
        }
        sqlBuilder.append(" group by order_id ");
        sqlBuilder.append(") d  ");
        sqlBuilder.append("where d.avg_night >= 0  ");
        sqlBuilder.append("group by  ");
        sqlBuilder.append("case when  toFloat64(d.avg_night) >= 0 and toFloat64(d.avg_night) < 100 then '0-100' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 100 and toFloat64(d.avg_night) < 200 then '100-200' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 200 and toFloat64(d.avg_night) < 300 then '200-300' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 300 and toFloat64(d.avg_night) < 400 then '300-400' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 400 and toFloat64(d.avg_night) < 500 then '400-500' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 500 and toFloat64(d.avg_night) < 600 then '500-600' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 600 and toFloat64(d.avg_night) < 700 then '600-700' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 700 and toFloat64(d.avg_night) < 800 then '700-800' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 800 and toFloat64(d.avg_night) < 900 then '800-900' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 900 and toFloat64(d.avg_night) < 1000 then '900-1000' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 1000 and toFloat64(d.avg_night) < 1500 then '1000-1500' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 1500 and toFloat64(d.avg_night) < 2000 then '1500-2000' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 2000  then '>=2000' end ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_6, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, HotelSaveLossNightDTO.class, "queryHotelNight");
    }

    /**
     * 酒店-间夜均价
     */
    
    public List<HotelSaveLossNightDTO> queryHotelNightCorpAndIndustry(String startTime, String endTime, String statisticalCaliber,
                                                                      DataTypeEnum dataTypeEnum,
                                                                      String productType,
                                                                      List<String> industryList, List<Integer> cityIds,
                                                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select case ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 0 and toFloat64(d.avg_night) < 100 then '0-100' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 100 and toFloat64(d.avg_night) < 200 then '100-200' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 200 and toFloat64(d.avg_night) < 300 then '200-300' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 300 and toFloat64(d.avg_night) < 400 then '300-400' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 400 and toFloat64(d.avg_night) < 500 then '400-500' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 500 and toFloat64(d.avg_night) < 600 then '500-600' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 600 and toFloat64(d.avg_night) < 700 then '600-700' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 700 and toFloat64(d.avg_night) < 800 then '700-800' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 800 and toFloat64(d.avg_night) < 900 then '800-900' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 900 and toFloat64(d.avg_night) < 1000 then '900-1000' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 1000 and toFloat64(d.avg_night) < 1500 then '1000-1500' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 1500 and toFloat64(d.avg_night) < 2000 then '1500-2000' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 2000  then '>=2000' end as avgNight ");
        sqlBuilder.append(",sum(d.totalQuantity) as totalQuantity ");
        sqlBuilder.append(",sum(d.totalPrice) as totalPrice ");
        sqlBuilder.append(OrpConstants.FROM).append(" ( ");
        sqlBuilder.append("select order_id, ");
        sqlBuilder.append("   COALESCE(sum(quantity),0) as totalQuantity, ");
        sqlBuilder.append("   COALESCE(toFloat64(sum(room_price)),0) as totalPrice, ");
        sqlBuilder.append("   if(sum(quantity)!=0,divide(toFloat64(sum(room_price)),toFloat64(sum(quantity))),toFloat64(null)) as avg_night ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d=? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and is_oversea in ('O','F') ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and is_oversea = 'T' ");
        }
        sqlBuilder.append(" and  order_status = '" + OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus") + "' ");
        List<Object> paramList = Lists.newArrayList();
        if (DataTypeEnum.INDUSTRY.equals(dataTypeEnum)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, paramList, isBookCaliber ? ORDERDT : "deal_date"));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP.equals(dataTypeEnum)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, paramList, isBookCaliber ? ORDERDT : "deal_date"));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        if (CollectionUtils.isNotEmpty(cityIds)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(cityIds, "city", paramList));
        }
        sqlBuilder.append(" group by order_id ");
        sqlBuilder.append(") d  ");
        sqlBuilder.append("where d.avg_night >= 0  ");
        sqlBuilder.append("group by  ");
        sqlBuilder.append("case when  toFloat64(d.avg_night) >= 0 and toFloat64(d.avg_night) < 100 then '0-100' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 100 and toFloat64(d.avg_night) < 200 then '100-200' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 200 and toFloat64(d.avg_night) < 300 then '200-300' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 300 and toFloat64(d.avg_night) < 400 then '300-400' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 400 and toFloat64(d.avg_night) < 500 then '400-500' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 500 and toFloat64(d.avg_night) < 600 then '500-600' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 600 and toFloat64(d.avg_night) < 700 then '600-700' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 700 and toFloat64(d.avg_night) < 800 then '700-800' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 800 and toFloat64(d.avg_night) < 900 then '800-900' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 900 and toFloat64(d.avg_night) < 1000 then '900-1000' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 1000 and toFloat64(d.avg_night) < 1500 then '1000-1500' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 1500 and toFloat64(d.avg_night) < 2000 then '1500-2000' ");
        sqlBuilder.append("when  toFloat64(d.avg_night) >= 2000  then '>=2000' end ");
        return queryBySql(sqlBuilder.toString(), paramList, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_6, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, HotelSaveLossNightDTO.class, "queryHotelNight");
    }

    /**
     * 机票热门航段 top30
     */
    public List<FlightHotCityBaseDTO> queryFlightHotCity(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select flight_city,flight_city_code,flight_city_en,sum(quantity) as totalQuantity ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d=? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        sqlBuilder.append(" and audited <> 'F' ");
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(" group by flight_city,flight_city_code,flight_city_en ");
        sqlBuilder.append(" order by totalQuantity desc limit 30 ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_7, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, FlightHotCityBaseDTO.class, "queryFlightHotCity");
    }

    /**
     * 酒店-热门城市
     */
    public List<FlightHotCityBaseDTO> queryHotelHotCity(BaseQueryConditionDTO requestDto, String productType, String lang) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select city as hot_city_id,city_name as hot_city_name,city_name_en as hot_city_name_en,'' as hot_city_code,sum(quantity) as totalQuantity ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d=? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and is_oversea = 'F' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and is_oversea in ('O','T') ");
        }
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, isBookCaliber ? ORDERDT : REPORT_DATE));
        if (CollectionUtils.isNotEmpty(requestDto.getCityNames())) {
            if (StringUtils.isEmpty(lang) || SharkLocaleEnum.ZH_CN.getLoacal().equals(lang)) {
                sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(requestDto.getCityNames(), "city_name", paramList));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(requestDto.getCityNames(), "city_name_en", paramList));
            }
        }
        sqlBuilder.append(" group by hot_city_id,hot_city_name,hot_city_name_en,hot_city_code ");
        sqlBuilder.append(" order by totalQuantity desc limit 50 ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_7, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, FlightHotCityBaseDTO.class, "queryFlightHotCity");
    }

    /**
     * 酒店-热门城市
     */
    public List<FlightHotCityBaseDTO> queryHotelHotCityTop10(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select city as hot_city_id,city_name as hot_city_name,city_name_en as hot_city_name_en,'' as hot_city_code,sum(quantity) as totalQuantity ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d=? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and is_oversea = 'F' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and is_oversea in ('O','T') ");
        }
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(" group by hot_city_id,hot_city_name,hot_city_name_en,hot_city_code ");
        sqlBuilder.append(" order by totalQuantity desc limit 10 ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_7, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, FlightHotCityBaseDTO.class, "queryFlightHotCity");
    }

    /**
     * 出发城市
     */
    public List<FlightHotCityBaseDTO> queryFlightStartCity(BaseQueryConditionDTO requestDto, String productType, String lang) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select departure_city_id,departure_city_name,departure_city_name_en,departure_city_code,sum(quantity) as totalQuantity");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d=? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        sqlBuilder.append(" and audited <> 'F' ");
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, isBookCaliber ? ORDERDT : REPORT_DATE));
        if (CollectionUtils.isNotEmpty(requestDto.getCityNames())) {
            if (StringUtils.isEmpty(lang) || SharkLocaleEnum.ZH_CN.getLoacal().equals(lang)) {
                sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(requestDto.getCityNames(), "departure_city_name", paramList));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(requestDto.getCityNames(), "departure_city_name_en", paramList));
            }
        }
        sqlBuilder.append(" group by departure_city_id,departure_city_name,departure_city_name_en,departure_city_code ");
        sqlBuilder.append(" order by totalQuantity desc limit 50 ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_7, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, FlightHotCityBaseDTO.class, "queryFlightStartCity");
    }

    /**
     * 出发城市 Top10
     */
    public List<FlightHotCityBaseDTO> queryFlightStartCityTop10(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select departure_city_id,departure_city_name,departure_city_name_en,departure_city_code,sum(quantity) as totalQuantity");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d=? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        sqlBuilder.append(" and audited <> 'F' ");
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(" group by departure_city_id,departure_city_name,departure_city_name_en,departure_city_code ");
        sqlBuilder.append(" order by totalQuantity desc limit 10 ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_7, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, FlightHotCityBaseDTO.class, "queryFlightStartCity");
    }

    /**
     * 到达城市
     */
    public List<FlightHotCityBaseDTO> queryFlightArrivalCity(BaseQueryConditionDTO requestDto, String productType, String lang) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select arrival_city_id,arrival_city_name,arrival_city_name_en,arrival_city_code,sum(quantity) as totalQuantity ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d=? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        sqlBuilder.append(" and audited <> 'F' ");
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, isBookCaliber ? ORDERDT : REPORT_DATE));
        if (CollectionUtils.isNotEmpty(requestDto.getCityNames())) {
            if (StringUtils.isEmpty(lang) || SharkLocaleEnum.ZH_CN.getLoacal().equals(lang)) {
                sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(requestDto.getCityNames(), "arrival_city_name", paramList));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(requestDto.getCityNames(), "arrival_city_name_en", paramList));
            }
        }
        sqlBuilder.append(" group by arrival_city_id,arrival_city_name,arrival_city_name_en,arrival_city_code ");
        sqlBuilder.append(" order by totalQuantity desc limit 50 ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_7, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, FlightHotCityBaseDTO.class, "queryFlightArrivalCity");
    }

    /**
     * 到达城市 top10
     */
    public List<FlightHotCityBaseDTO> queryFlightArrivalCityTop10(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select arrival_city_id,arrival_city_name,arrival_city_name_en,arrival_city_code,sum(quantity) as totalQuantity ");
        sqlBuilder.append(OrpConstants.FROM).append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d=? ");
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuilder.append(" and flight_class = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuilder.append(" and flight_class = 'I' ");
        }
        sqlBuilder.append(" and audited <> 'F' ");
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, paramList, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(" group by arrival_city_id,arrival_city_name,arrival_city_name_en,arrival_city_code ");
        sqlBuilder.append(" order by totalQuantity desc limit 10 ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE_7, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, FlightHotCityBaseDTO.class, "queryFlightArrivalCity");
    }

    private PreparedStatement mapCommonRequest(List<Object> paramList, PreparedStatement statement, ClickHouseTable clickHouseTable) {
        int index = OrpConstants.ONE;
        try {
            String x = queryPartition(clickHouseTable);
            statement.setString(index++, x);
            for (Object obj : paramList) {
                statement.setObject(index++, obj);
            }

            paramList.add(0, x);
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }
}
