package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.SearchDeptAndCostcneterDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
@Slf4j
public class OnlineReportConsumeDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "OnlineReportConsumeDao queryOnlineReportConsume";
    /**
     * 查询 在线报告概览
     *
     * @param requestDto
     * @param clazz
     * @param otherFlag  是否查询其他指标（平均票价、里程均价、商旅服务费、退票张数、 改签张数）
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationFlightWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz, boolean otherFlag)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = null;
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");

        sqlBuilder.append("select ");
        sqlBuilder.append(
                "flight_class as flightClass, case when (agreement_type_name='" + ta + "' or agreement_type_name = 'B2G') then 'C' else 'NC' end as contractType, " +
                        "SUM(real_pay) AS totalAmount, SUM(quantity) AS totalQuantity ");
        if (otherFlag) {
            sqlBuilder.append(", sum(coalesce(service_fee, 0)+coalesce(rebook_service_fee, 0)" +
                    "+coalesce(refund_service_fee, 0)-coalesce(refund_itinerary_fee, 0)" +
                    "+coalesce(ticket_behind_service_fee, 0)+coalesce(rebook_behind_service_fee, 0)+coalesce(refund_behind_service_fee, 0)) as totalCorpServiceFee ");
            sqlBuilder.append(", sum(case when class_type = 'Y' then coalesce(netfare, 0) + coalesce(rebook_price_differential, 0) else 0 end) as totalNetfareEconomy ");
            sqlBuilder.append(", sum(case when class_type = 'Y' then coalesce(tpms, 0) else 0 end) as totalTpmsEconomy ");
            sqlBuilder.append(", sum(case when class_type = 'Y' then coalesce(quantity, 0) else 0 end) as totalQuantityEconomy ");
            sqlBuilder.append(", sum(coalesce(refundtkt, 0)) as totalRefundQuantity ");
            sqlBuilder.append(", sum(coalesce(rebooktkt, 0)) as totalRebookQuantity ");
        }
        sqlBuilder.append("from  ");
        if (BlueSpaceUtils.isForeign(requestDto.getPos(), requestDto.getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                clickHouseTable = ClickHouseTable.ADM_INDEXFLIGHT_PRICE_DETAIL_FOREIGN_MULTI_CURRENCY;
            } else {
                clickHouseTable = ClickHouseTable.ADM_INDEXFLIGHT_PRICE_DETAIL_FOREIGN;
            }
        } else {
            if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL;
            }
        }
        sqlBuilder.append(clickHouseTable.getTable());

        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        if (BlueSpaceUtils.isForeign(requestDto.getPos(), requestDto.getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(requestDto.getCurrency())) {
                // 币种条件
                sqlBuilder.append(String.format(" and termcurrency = '%s'", requestDto.getCurrency()));
            }
        }
        sqlBuilder.append(" group by flightClass, contractType");

        // 查询clickhouse
        ClickHouseTable finalClickHouseTable = clickHouseTable;
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, finalClickHouseTable),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggFlightWithCondition");
    }

    /**
     * 查询 在线报告概览 查询机票订单数
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationFlightOrderNumWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }
        sqlBuilder.append("select count(distinct if(is_refund='F',order_id,null) ) - count(distinct if(is_refund='T' and order_status='RA',order_id,null) )" +
                " as totalCntOrder from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, String.format("substr(%s,1,10)", col)));
        sqlBuilder.append(" AND fee_type = '因公' and  audited <> 'F' and order_status in ('RA','TA','EP','EA','RP')");
        sqlBuilder.append(" ");

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("aggreationFlightOrderNumWithCondition", logSql);

        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationFlightOrderNumWithCondition");
        return list;
    }


    /**
     * 查询 在线报告概览
     *
     * @param requestDto
     * @param clazz
     * @param otherFlag  是否查询其他指标（间夜均价、商旅管理服务费、退订间夜数）
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationHotelWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz, boolean otherFlag) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = null;
        sqlBuilder.append(
                "select  is_oversea as isOversea,order_type as orderType,SUM(real_pay_with_servicefee) AS totalAmount,SUM(quantity) AS totalQuantity ");
        if (otherFlag) {
            sqlBuilder.append(", sum(coalesce(service_fee, 0)+coalesce(postservicefee, 0)) as totalCorpServiceFee  ");
            sqlBuilder.append(", sum(coalesce(room_price,0)) as totalRoomPrice ");
            sqlBuilder.append(", sum(coalesce(rfd_quantity,0)) as refundQuantity ");
        }
        sqlBuilder.append("from  ");
        if (BlueSpaceUtils.isForeign(requestDto.getPos(), requestDto.getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                clickHouseTable = ClickHouseTable.ADM_INDEXHOTEL_PRICE_DETAIL_FOREIGN_MULTI_CURRENCY;
            } else {
                clickHouseTable = ClickHouseTable.ADM_INDEXHOTEL_PRICE_DETAIL_FOREIGN;
            }
        } else {
            if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL;
            }
        }
        sqlBuilder.append(clickHouseTable.getTable());

        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        sqlBuilder.append(String.format("and order_status = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus")));
        List<Object> parmList = new ArrayList<>();
        if (BlueSpaceUtils.isForeign(requestDto.getPos(), requestDto.getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(requestDto.getCurrency())) {
                // 币种条件
                sqlBuilder.append(String.format(" and termcurrency = '%s'", requestDto.getCurrency()));
            }
        }
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        sqlBuilder.append(" group by isOversea, order_type");

        // 查询clickhouse
        ClickHouseTable finalClickHouseTable = clickHouseTable;
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, finalClickHouseTable),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationHotelWithCondition");
    }

    /**
     * 查询 在线报告概览  查询酒店订单数
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationHotelOrderNumWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "order_date";
        }
        sqlBuilder.append("select count(distinct if(is_refund='F',order_id,null) ) - count(distinct if(is_refund='T' and order_status='已取消',order_id,null) ) " +
                " as totalCntOrder from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, String.format("substr(%s,1,10)", col)));
        sqlBuilder.append(" AND order_status = '已完成' AND is_oversea IN ('F','O','T') ");

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("aggreationHotelOrderNumWithCondition", logSql);

        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationHotelOrderNumWithCondition");
        return list;
    }

    /**
     * 查询 在线报告概览
     *
     * @param requestDto
     * @param clazz
     * @param otherFlag  是否查询其他指标（平均票价、商旅管理服务费、退票张数、改签张数）
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationTrainWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz, boolean otherFlag) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = null;
        sqlBuilder.append("select  SUM(real_pay) AS totalAmount,SUM(quantity) AS totalQuantity ");
        if (otherFlag) {
            sqlBuilder.append(", sum(coalesce(service_fee, 0) + coalesce(deal_change_service_fee, 0) + coalesce(after_service_fee, 0) " +
                    "+ coalesce(afterchangeservicefee, 0) + coalesce(aftertaketicketfee, 0) + coalesce(grab_service_fee, 0) " +
                    "+ coalesce(paper_ticket_fee, 0) + coalesce(afteraftertaketicketfee, 0)) as totalCorpServiceFee  ");
            sqlBuilder.append(", sum(coalesce(refund_quantity, 0)) as totalRefundQuantity");
            sqlBuilder.append(", sum(coalesce(change_quantity, 0)) as totalRebookQuantity");
            sqlBuilder.append(", sum(coalesce(ticket_price, 0) + coalesce(changebalance, 0) + coalesce(refund_ticket_fee, 0)) as totalTicketPriceChangeRefundFee ");
        }
        sqlBuilder.append("from  ");
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL_ODT;// todo 预订口径
        } else {
            // 默认使用成交口径
            clickHouseTable = ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL;
        }
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        // 查询clickhouse
        ClickHouseTable finalClickHouseTable = clickHouseTable;
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, finalClickHouseTable),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationTrainWithCondition");
    }

    /**
     * 查询 在线报告概览 查询火车票订单数
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationTrainOrderNumWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }
        sqlBuilder.append("select count(distinct case when a.cnt>0 then a.order_id end)-count(distinct case when a.cnt< 0 then a.order_id end)" +
                " as totalCntOrder from ");
        sqlBuilder.append(" (select order_id, sum(quantity) cnt from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, col));
        sqlBuilder.append(" and order_status  in ('TA','RP','EP','EA') ");
        sqlBuilder.append(" group by order_id");
        sqlBuilder.append(" ) a");

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("aggreationTrainOrderNumWithCondition", logSql);

        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationTrainOrderNumWithCondition");
        return list;
    }

    /**
     * 查询 在线报告概览(国际火车票)
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationIntTrainWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select  SUM(coalesce(net_amount, 0)) AS totalAmount,SUM(coalesce(net_ticket, 0)) AS totalQuantity ");
        sqlBuilder.append("from  ");
        sqlBuilder.append(ClickHouseTable.ADM_INDEXTRAIN_INTL_DETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(buildIntTrainPreSql(requestDto, parmList));
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, ClickHouseTable.ADM_INDEXTRAIN_INTL_DETAIL),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationIntTrainWithCondition");
    }


    /**
     * 查询 在线报告概览
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationCarWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = null;
        sqlBuilder.append(
                "select  order_type as orderType, sub_product_line,SUM(real_pay) AS totalAmount,SUM(quantity) AS totalQuantity, sum(service_fee) as totalCorpServiceFee ");
        sqlBuilder.append(",sum(coalesce(cnt_order, 0)) as totalCntOrder ");
        sqlBuilder.append(" from  ");
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL_ODT;// todo 预订口径
        } else {
            // 默认使用成交口径
            clickHouseTable = ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL;
        }
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        sqlBuilder.append(" group by order_type, sub_product_line");
        // 查询clickhouse
        ClickHouseTable finalClickHouseTable = clickHouseTable;
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, finalClickHouseTable),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationCarWithCondition");
    }

    /**
     * 查询 在线报告概览
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationWithConditionOverview(BaseQueryConditionDTO requestDto, Class<T> clazz)
            throws Exception {

        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = null;
        sqlBuilder.append("select ");
        sqlBuilder.append("SUM(amount_total) AS totalAmount,");
        sqlBuilder.append("SUM(amount_flt) AS totalFltAmount,");
        sqlBuilder.append("SUM(amount_htl) AS totalHtlAmount,");
        sqlBuilder.append("SUM(amount_train) AS totalTrainAmount,");
        sqlBuilder.append("SUM(amount_car) AS totalCarAmount,");
        sqlBuilder.append("SUM(coalesce(amount_bus, 0)) AS totalBusAmount,");
        sqlBuilder.append("SUM(coalesce(amount_vas, 0)) AS totalAddAmount,");
        sqlBuilder.append("SUM(quantity_total) AS totalQuantity,");
        sqlBuilder.append("SUM(quantity_flt) AS totalFltQuantity,");
        sqlBuilder.append("SUM(quantity_htl) AS totalHtlQuantity,");
        sqlBuilder.append("SUM(quantity_train) AS totalTrainQuantity,");
        sqlBuilder.append("SUM(quantity_car) AS totalCarQuantity, ");
        sqlBuilder.append("SUM(coalesce(quantity_bus, 0)) AS totalBusQuantity,");
        sqlBuilder.append("SUM(coalesce(quantity_vas, 0)) AS totalAddQuantity, ");
        sqlBuilder.append("SUM(service_fee) AS totalCorpServiceFee ");

        sqlBuilder.append(" from  ");
        if (BlueSpaceUtils.isForeign(requestDto.getPos(), requestDto.getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN_MULTI_CURRENCY;
            } else {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN;
            }
        } else {
            if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
            }
        }
        sqlBuilder.append(clickHouseTable.getTable());

        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        if (BlueSpaceUtils.isForeign(requestDto.getPos(), requestDto.getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(requestDto.getCurrency())) {
                // 币种条件
                sqlBuilder.append(String.format(" and termcurrency = '%s'", requestDto.getCurrency()));
            }
        }
        // 查询clickhouse
        ClickHouseTable finalClickHouseTable = clickHouseTable;
        /*
        String log = getLogSql(parmList, sqlBuilder.toString());
        log.info("aggreationWithConditionOverview", log);
         */
        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, finalClickHouseTable),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationWithConditionOverview");
        return list;
    }

    /**
     * 查询 在线报告概览
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    @Deprecated
    public <T> List<T> aggreationWithConditionOverviewOrderNum(BaseQueryConditionDTO requestDto, Class<T> clazz)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();

        if (Objects.equals(requestDto.getQueryDataType(), "AMOUNT")) {
            queryDataTypeAmountSql(sqlBuilder, parmList, requestDto);
        } else if (Objects.equals(requestDto.getQueryDataType(), "ORDER_NUM")) {
            queryDataTypeOrderNumSql(sqlBuilder, parmList, requestDto);
        } else {
            queryDataTypeDefaultSql(sqlBuilder, parmList, requestDto);
        }

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("aggreationWithConditionOverviewNoBlue", logSql);

        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationWithConditionOverviewNoBlue");
        return list;
    }

    public void queryDataTypeAmountSql(StringBuilder sqlBuilder,
                                       List<Object> parmList,
                                       BaseQueryConditionDTO requestDto) {
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "order_date";
        }
        ClickHouseTable clickHouseTable = null;
        sqlBuilder.append("select t1.totalAmount as totalAmount, t1.totalFltAmount as totalFltAmount, " +
                "t1.totalHtlAmount as totalHtlAmount, t1.totalTrainAmount as totalTrainAmount, t1.totalCarAmount as totalCarAmount, " +
                "t1.totalBusAmount as totalBusAmount, t1.totalAddAmount as totalAddAmount, " +
                "t1.totalCorpServiceFee as totalCorpServiceFee ");
        sqlBuilder.append(" from (");
        sqlBuilder.append("select ");
        sqlBuilder.append("SUM(amount_total) AS totalAmount,");
        sqlBuilder.append("SUM(amount_flt) AS totalFltAmount,");
        sqlBuilder.append("SUM(amount_htl) AS totalHtlAmount,");
        sqlBuilder.append("SUM(amount_train) AS totalTrainAmount,");
        sqlBuilder.append("SUM(amount_car) AS totalCarAmount,");
        sqlBuilder.append("SUM(coalesce(amount_bus, 0)) AS totalBusAmount,");
        sqlBuilder.append("SUM(coalesce(amount_vas, 0)) AS totalAddAmount,");
        sqlBuilder.append("SUM(service_fee) AS totalCorpServiceFee ");
        sqlBuilder.append(" from  ");

        if (BlueSpaceUtils.isForeign(requestDto.getPos(), requestDto.getBlueSpace())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN;
        } else {
            if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
            }
        }
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(clickHouseTable));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        sqlBuilder.append(" ) t1 ");

    }

    public void queryDataTypeDefaultSql(StringBuilder sqlBuilder,
                                        List<Object> parmList,
                                        BaseQueryConditionDTO requestDto) {
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }
        ClickHouseTable clickHouseTable = null;
        sqlBuilder.append("select t1.totalAmount as totalAmount, t1.totalFltAmount as totalFltAmount, " +
                "t1.totalHtlAmount as totalHtlAmount, t1.totalTrainAmount as totalTrainAmount, t1.totalCarAmount as totalCarAmount, " +
                "t1.totalBusAmount as totalBusAmount, t1.totalAddAmount as totalAddAmount, " +
                "t2.totalFltQuantity as totalFltQuantity, t3.totalHtlQuantity as totalHtlQuantity, t4.totalTrainQuantity as totalTrainQuantity, " +
                "t7.totalCarQuantity as totalCarQuantity, t5.totalBusQuantity as totalBusQuantity, t6.totalAddQuantity as totalAddQuantity ," +
                "t1.totalCorpServiceFee as totalCorpServiceFee ");
        sqlBuilder.append(", t2.totalFltQuantity + t3.totalHtlQuantity + t4.totalTrainQuantity + t7.totalCarQuantity + t5.totalBusQuantity + t6.totalAddQuantity " +
                " as totalQuantity ");
        sqlBuilder.append(" from (");

        sqlBuilder.append("select ");
        sqlBuilder.append("SUM(amount_total) AS totalAmount,");
        sqlBuilder.append("SUM(amount_flt) AS totalFltAmount,");
        sqlBuilder.append("SUM(amount_htl) AS totalHtlAmount,");
        sqlBuilder.append("SUM(amount_train) AS totalTrainAmount,");
        sqlBuilder.append("SUM(amount_car) AS totalCarAmount,");
        sqlBuilder.append("SUM(coalesce(amount_bus, 0)) AS totalBusAmount,");
        sqlBuilder.append("SUM(coalesce(amount_vas, 0)) AS totalAddAmount,");
        sqlBuilder.append("SUM(service_fee) AS totalCorpServiceFee ");
        sqlBuilder.append(" from  ");

        if (BlueSpaceUtils.isForeign(requestDto.getPos(), requestDto.getBlueSpace())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN;
        } else {
            if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
            }
        }
        sqlBuilder.append(clickHouseTable.getTable());

        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");

        parmList.add(queryPartition(clickHouseTable));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        sqlBuilder.append(" ) t1, ");
        sqlBuilder.append("(select count(distinct if(is_refund='F',order_id,null) ) - count(distinct if(is_refund='T' and order_status='RA',order_id,null) )" +
                " as totalFltQuantity from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, col));
        sqlBuilder.append(" AND fee_type = '因公' and  audited <> 'F' and order_status in ('RA','TA','EP','EA','RP')");
        sqlBuilder.append(" ) t2, ");
        sqlBuilder.append("(select count(distinct if(is_refund='F',order_id,null) ) - count(distinct if(is_refund='T' and order_status='已取消',order_id,null) ) " +
                " as totalHtlQuantity from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, col));
        sqlBuilder.append(" AND order_status = '已完成' AND is_oversea IN ('F','O','T') ");
        sqlBuilder.append(" ) t3, ");
        sqlBuilder.append("(select count(distinct case when a.cnt>0 then a.order_id end)-count(distinct case when a.cnt< 0 then a.order_id end)" +
                " as totalTrainQuantity from ");
        sqlBuilder.append(" (select order_id, sum(quantity) cnt from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, col));
        sqlBuilder.append(" and order_status  in ('TA','RP','EP','EA') ");
        sqlBuilder.append(" group by order_id");
        sqlBuilder.append(" )a");
        sqlBuilder.append(" ) t4, ");
        sqlBuilder.append("(select count(DISTINCT case when flag= 0 then orderid end)-count(DISTINCT case when flag= 1 then orderid end ) " +
                "as totalBusQuantity from ");
        sqlBuilder.append(ClickHouseTable.BUS_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.BUS_ORDERDETAIL));
        sqlBuilder.append(" and  isself = '因公' and orderstatusdesc = '已购票' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corpid", requestDto.getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "querydate"));
        sqlBuilder.append(" ) t5, ");
        sqlBuilder.append("(select count(DISTINCT case when vd.flag= 0 then vd.vaso_id end)-count(DISTINCT case when vd.flag= 1 then vd.vaso_id end )" +
                " as totalAddQuantity from ");
        sqlBuilder.append(" (select vaso_id, flag from ");
        sqlBuilder.append(ClickHouseTable.VASO_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.VASO_ORDERDETAIL));
        sqlBuilder.append(" AND orderstatus in ('S','5')");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("company", requestDto.getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "query_refund_date"));
        sqlBuilder.append(" ) vd");

        sqlBuilder.append(" ) t6, ");
        sqlBuilder.append("(select sum(coalesce(cnt_order,0)) as totalCarQuantity from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD));
        sqlBuilder.append(" AND fee_type = '因公' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "orderdt"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corp_corporation", requestDto.getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildRolePermission());
        sqlBuilder.append(" ) t7 ");
    }

    public void queryDataTypeOrderNumSql(StringBuilder sqlBuilder,
                                         List<Object> parmList,
                                         BaseQueryConditionDTO requestDto) {
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }
        ClickHouseTable clickHouseTable = null;
        sqlBuilder.append("select " +
                "t2.totalFltQuantity as totalFltQuantity, t3.totalHtlQuantity as totalHtlQuantity, t4.totalTrainQuantity as totalTrainQuantity, " +
                "t7.totalCarQuantity as totalCarQuantity, t5.totalBusQuantity as totalBusQuantity, t6.totalAddQuantity as totalAddQuantity ");
        sqlBuilder.append(", t2.totalFltQuantity + t3.totalHtlQuantity + t4.totalTrainQuantity + t7.totalCarQuantity + t5.totalBusQuantity + t6.totalAddQuantity " +
                " as totalQuantity ");
        sqlBuilder.append(" from ");
        sqlBuilder.append("(select count(distinct if(is_refund='F',order_id,null) ) - count(distinct if(is_refund='T' and order_status='RA',order_id,null) )" +
                " as totalFltQuantity from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, col));
        sqlBuilder.append(" AND fee_type = '因公' and  audited <> 'F' and order_status in ('RA','TA','EP','EA','RP')");
        sqlBuilder.append(" ) t2, ");
        sqlBuilder.append("(select count(distinct if(is_refund='F',order_id,null) ) - count(distinct if(is_refund='T' and order_status='已取消',order_id,null) ) " +
                " as totalHtlQuantity from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, col));
        sqlBuilder.append(" AND order_status = '已完成' AND is_oversea IN ('F','O','T') ");
        sqlBuilder.append(" ) t3, ");
        sqlBuilder.append("(select count(distinct case when a.cnt>0 then a.order_id end)-count(distinct case when a.cnt< 0 then a.order_id end)" +
                " as totalTrainQuantity from ");
        sqlBuilder.append(" (select order_id, sum(quantity) cnt from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, col));
        sqlBuilder.append(" and order_status  in ('TA','RP','EP','EA') ");
        sqlBuilder.append(" group by order_id");
        sqlBuilder.append(" )a");
        sqlBuilder.append(" ) t4, ");
        sqlBuilder.append("(select count(DISTINCT case when flag= 0 then orderid end)-count(DISTINCT case when flag= 1 then orderid end ) " +
                "as totalBusQuantity from ");
        sqlBuilder.append(ClickHouseTable.BUS_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.BUS_ORDERDETAIL));
        sqlBuilder.append(" and  isself = '因公' and orderstatusdesc = '已购票' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corpid", requestDto.getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "querydate"));
        sqlBuilder.append(" ) t5, ");
        sqlBuilder.append("(select count(DISTINCT case when vd.flag= 0 then vd.vaso_id end)-count(DISTINCT case when vd.flag= 1 then vd.vaso_id end )" +
                " as totalAddQuantity from ");
        sqlBuilder.append(" (select vaso_id, flag from ");
        sqlBuilder.append(ClickHouseTable.VASO_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.VASO_ORDERDETAIL));
        sqlBuilder.append(" AND orderstatus in ('S','5')");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("company", requestDto.getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "query_refund_date"));
        sqlBuilder.append(" ) vd");

        sqlBuilder.append(" ) t6, ");
        sqlBuilder.append("(select sum(coalesce(cnt_order,0)) as totalCarQuantity from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD));
        sqlBuilder.append(" AND fee_type = '因公' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "orderdt"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corp_corporation", requestDto.getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildRolePermission());
        sqlBuilder.append(" ) t7 ");
    }

    /**
     * 查询 在线报告概览
     *
     * @return
     * @throws Exception
     */
    public Integer aggreationOverview(BaseQueryConditionDTO requestDto) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append("SUM(one_trip_number) AS oneTripNum from ");
        sqlBuilder.append(ClickHouseTable.ADM_INDEX_ONE_TRIP_CORP.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlOneTrip(requestDto, parmList));

        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, ClickHouseTable.ADM_INDEX_ONE_TRIP_CORP),
                (u, d) -> {
                    try {
                        return mapIntResult(u, "oneTripNum");
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return 0;
                }, Integer.class, "aggreationOverview");
    }

    private PreparedStatement mapCommonRequest(List<Object> parmList, PreparedStatement statement,
                                               ClickHouseTable clickHouseTable) {
        int index = OrpConstants.ONE;
        try {
            // 分区
            String x = queryPartition(clickHouseTable);
            statement.setString(index++, x);

            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }

            parmList.add(0, queryPartition(clickHouseTable));
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }

    /**
     * 国际火车票的查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public String buildIntTrainPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            sqlBuffer.append(BaseConditionPrebuilder.buildCorpAndAccount("corpid", baseQueryCondition.getCorpIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getAccountIds())) {
            sqlBuffer.append(BaseConditionPrebuilder.buildCorpAndAccount("accountid", baseQueryCondition.getAccountIds(), parmList));
        }
        if (StringUtils.isNotEmpty(baseQueryCondition.getStartTime())) {
            sqlBuffer.append(" and  printticketdt >= ? ");
            parmList.add(baseQueryCondition.getStartTime());
        }
        if (StringUtils.isNotEmpty(baseQueryCondition.getEndTime())) {
            sqlBuffer.append(" and  printticketdt <= ? ");
            parmList.add(baseQueryCondition.getEndTime());
        }
        return sqlBuffer.toString();
    }


    public <T> List<T> queryDataTypeFltOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        if (BlueSpaceUtils.isForeign(requestDto.getPos(), requestDto.getBlueSpace())) {
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        }
        //sqlBuilder.append("select count(distinct if(is_refund='F',order_id,null) ) - count(distinct if(is_refund='T' and order_status='RA',order_id,null) )" +
        sqlBuilder.append("select count(DISTINCT order_id)" +
                " as totalQuantity from ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(clickHouseTable));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, col));
        sqlBuilder.append(" AND fee_type = '因公' and  audited <> 'F' and order_status in ('RA','TA','EP','EA','RP')");

        // 根据 productType 添加国内国际查询条件
        if (StringUtils.isNotEmpty(requestDto.getProductType())) {
            if ("dom".equals(requestDto.getProductType())) {
                // 国内
                sqlBuilder.append(" AND flight_class = 'N' ");
            } else if ("inter".equals(requestDto.getProductType())) {
                // 国际
                sqlBuilder.append(" AND flight_class = 'I' ");
            }
        }

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryDataTypeFltOrderNumSql", logSql);

        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryDataTypeFltOrderNumSql");
        return list;
    }

    public <T> List<T> queryDataTypeHtlOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "order_date";
        }
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
        if (BlueSpaceUtils.isForeign(requestDto.getPos(), requestDto.getBlueSpace())) {
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD_FOREIGN;
        }
        //sqlBuilder.append("select count(distinct if(is_refund='F',order_id,null) ) - count(distinct if(is_refund='T' and order_status='已取消',order_id,null) ) " +
        sqlBuilder.append("select count(distinct order_id) " +
                " as totalQuantity from ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(clickHouseTable));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, String.format("substr(%s,1,10)", col)));
        sqlBuilder.append(" AND order_status = '已完成' AND is_oversea IN ('F','O','T') ");

        // 根据 productType 添加国内国际查询条件
        if (StringUtils.isNotEmpty(requestDto.getProductType())) {
            if ("dom".equals(requestDto.getProductType())) {
                // 国内
                sqlBuilder.append(" AND is_oversea = 'F' ");
            } else if ("inter".equals(requestDto.getProductType())) {
                // 国际
                sqlBuilder.append(" AND is_oversea = 'T' ");
            }
        }

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryDataTypeHtlOrderNumSql：{}", logSql);

        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryDataTypeHtlOrderNumSql");
        return list;
    }

    public <T> List<T> queryDataTypeTrainOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        // 根据 productType 添加国内国际查询条件
        if (StringUtils.isNotEmpty(requestDto.getProductType()) && "inter".equals(requestDto.getProductType())) {
            return Lists.newArrayList();
        }

        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }
        ClickHouseTable clickHouseTable = null;
        //sqlBuilder.append("select count(distinct case when a.cnt>0 then a.order_id end)-count(distinct case when a.cnt< 0 then a.order_id end)" +
        sqlBuilder.append("select count(distinct a.order_id)" +
                " as totalQuantity from ");
        sqlBuilder.append(" (select order_id, sum(quantity) cnt from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, col));
        sqlBuilder.append(" and order_status  in ('TA','RP','EP','EA','RA') ");
        sqlBuilder.append(" group by order_id");
        sqlBuilder.append(" )a");

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryDataTypeTrainOrderNumSql", logSql);

        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryDataTypeTrainOrderNumSql");
        return list;
    }

    public <T> List<T> queryDataTypeCarOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        if (StringUtils.isNotEmpty(requestDto.getProductType()) && "inter".equals(requestDto.getProductType())) {
            return Lists.newArrayList();
        }
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();

        sqlBuilder.append("select sum(coalesce(cnt_order,0)) as totalQuantity from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD));
        sqlBuilder.append(" AND fee_type = '因公' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "substr(order_date,1,10)"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corp_corporation", requestDto.getCorpIds(), parmList));
        sqlBuilder.append(buildOrgId(requestDto.getDeptList(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildRolePermission());

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryDataTypeCarOrderNumSql", logSql);

        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryDataTypeCarOrderNumSql");
        return list;
    }

    public <T> List<T> queryDataTypeBusOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
  /*      StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();

        sqlBuilder.append("select count(DISTINCT case when flag= 0 then orderid end)-count(DISTINCT case when flag= 1 then orderid end ) " +
                "as totalQuantity from ");
        sqlBuilder.append(ClickHouseTable.BUS_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.BUS_ORDERDETAIL));
        sqlBuilder.append(" and  isself = '因公' and orderstatusdesc = '已购票' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corpid", requestDto.getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "substr(querydate,1,10)"));

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryDataTypeBusOrderNumSql", logSql);

        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryDataTypeBusOrderNumSql");
        return list;*/

        return Lists.newArrayList();
    }

    public static String buildOrgId(List<SearchDeptAndCostcneterDTO> list, List<Object> paramList) {
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        for (SearchDeptAndCostcneterDTO searchDeptAndCostcneterDto : list) {
            Integer level = searchDeptAndCostcneterDto.getKey();
            if (Objects.isNull(level)) {
                continue;
            }
            String filedName = "org_id";
            if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                return "";
            }
            List<String> vals = searchDeptAndCostcneterDto.getVals();
            if (CollectionUtils.isEmpty(vals)) {
                continue;
            }
            String valsSql =
                    org.apache.commons.lang.StringUtils.join(vals.stream().map(val -> "?").collect(Collectors.toList()), ",");
            sqlBuffer.append(" and " + filedName + " in (" + valsSql + ")");
            paramList.addAll(vals);
            break;
        }
        return sqlBuffer.toString();
    }

    public <T> List<T> queryDataTypeAddOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        return Lists.newArrayList();
/*        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();

        sqlBuilder.append("select count(DISTINCT case when vd.flag= 0 then vd.vaso_id end)-count(DISTINCT case when vd.flag= 1 then vd.vaso_id end )" +
                " as totalQuantity from ");
        sqlBuilder.append(" (select vaso_id, flag from ");
        sqlBuilder.append(ClickHouseTable.VASO_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.VASO_ORDERDETAIL));
        sqlBuilder.append(" AND orderstatus in ('S','5')");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("company", requestDto.getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "substr(query_refund_date,1,10)"));
        sqlBuilder.append(" ) vd");

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("queryDataTypeAddOrderNumSql", logSql);

        List<T> list = queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryDataTypeAddOrderNumSql");
        return list;*/
    }

}
