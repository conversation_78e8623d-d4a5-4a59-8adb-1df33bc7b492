package com.corpgovernment.resource.schedule.onlinereport.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.detail.overview
 * @description: 概览ck查询结果
 * @author: md_wang
 * @create: 2021-11-08 18:00
 **/
@Data
public class OverviewQueryDto {

    /**
     * 查询日期
     **/
    @Column(name = "reportDate")
    @Type(value = Types.VARCHAR)
    private String reportDate;

    /**
     * 机票金额
     **/
    @Column(name = "amountFlt")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountFlt;
    /**
     * 机票金额同比
     **/
    @Column(name = "amountFltYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountFltYoy;
    /**
     * 机票金额环比
     **/
    @Column(name = "amountFltMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountFltMom;

    /**
     * 酒店金额
     **/
    @Column(name = "amountHtl")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountHtl;
    /**
     * 酒店金额-同比
     **/
    @Column(name = "amountHtlYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountHtlYoy;
    /**
     * 酒店金额-环比
     **/
    @Column(name = "amountHtlMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountHtlMom;

    /**
     * 火车金额
     **/
    @Column(name = "amountTrain")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountTrain;
    /**
     * 火车金额-同比
     **/
    @Column(name = "amountTrainYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountTrainYoy;
    /**
     * 火车金额-环比
     **/
    @Column(name = "amountTrainMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountTrainMom;

    /**
     * 用车金额
     **/
    @Column(name = "amountCar")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountCar;
    /**
     * 用车金额-同比
     **/
    @Column(name = "amountCarYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountCarYoy;
    /**
     * 用车金额-环比
     **/
    @Column(name = "amountCarMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountCarMom;

    /**
     * 汽车金额
     **/
    @Column(name = "amountBus")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountBus;
    /**
     * 汽车金额-同比
     **/
    @Column(name = "amountBusYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountBusYoy;
    /**
     * 汽车金额-环比
     **/
    @Column(name = "amountBusMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountBusMom;

    /**
     * 增值金额
     **/
    @Column(name = "amountAdd")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountAdd;
    /**
     * 增值金额-同比
     **/
    @Column(name = "amountAddYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountAddYoy;
    /**
     * 增值金额-环比
     **/
    @Column(name = "amountAddMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountAddMom;

    /**
     * 整体消费金额
     **/
    @Column(name = "amountTotal")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountTotal;
    /**
     * 整体消费金额-同比
     **/
    @Column(name = "amountTotalYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountTotalYoy;
    /**
     * 整体消费金额-环比
     **/
    @Column(name = "amountTotalMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountTotalMom;


    /**
     * 机票张数
     **/
    @Column(name = "quantityFlt")
    @Type(value = Types.BIGINT)
    private Long quantityFlt;
    /**
     * 机票张数-同比
     **/
    @Column(name = "quantityFltYoy")
    @Type(value = Types.BIGINT)
    private Long quantityFltYoy;
    /**
     * 机票张数-环比
     **/
    @Column(name = "quantityFltMom")
    @Type(value = Types.BIGINT)
    private Long quantityFltMom;

    /**
     * 酒店间夜数
     **/
    @Column(name = "quantityHtl")
    @Type(value = Types.BIGINT)
    private Long quantityHtl;
    /**
     * 酒店间夜数-同比
     **/
    @Column(name = "quantityHtlYoy")
    @Type(value = Types.BIGINT)
    private Long quantityHtlYoy;
    /**
     * 酒店间夜数-环比
     **/
    @Column(name = "quantityHtlMom")
    @Type(value = Types.BIGINT)
    private Long quantityHtlMom;

    /**
     * 火车张数
     **/
    @Column(name = "quantityTrain")
    @Type(value = Types.BIGINT)
    private Long quantityTrain;
    /**
     * 火车张数-同比
     **/
    @Column(name = "quantityTrainYoy")
    @Type(value = Types.BIGINT)
    private Long quantityTrainYoy;
    /**
     * 火车张数-环比
     **/
    @Column(name = "quantityTrainMom")
    @Type(value = Types.BIGINT)
    private Long quantityTrainMom;

    /**
     * 用车订单数
     **/
    @Column(name = "quantityCar")
    @Type(value = Types.BIGINT)
    private Long quantityCar;
    /**
     * 用车订单数-同比
     **/
    @Column(name = "quantityCarYoy")
    @Type(value = Types.BIGINT)
    private Long quantityCarYoy;
    /**
     * 用车订单数-环比
     **/
    @Column(name = "quantityCarMom")
    @Type(value = Types.BIGINT)
    private Long quantityCarMom;

    /**
     * 汽车票张数
     **/
    @Column(name = "quantityBus")
    @Type(value = Types.BIGINT)
    private Long quantityBus;
    /**
     * 汽车票张数-同比
     **/
    @Column(name = "quantityBusYoy")
    @Type(value = Types.BIGINT)
    private Long quantityBusYoy;
    /**
     * 汽车票张数-环比
     **/
    @Column(name = "quantityBusMom")
    @Type(value = Types.BIGINT)
    private Long quantityBusMom;

    /**
     * 增值订单数
     **/
    @Column(name = "quantityAdd")
    @Type(value = Types.BIGINT)
    private Long quantityAdd;
    /**
     * 增值订单数-同比
     **/
    @Column(name = "quantityAddYoy")
    @Type(value = Types.BIGINT)
    private Long quantityAddYoy;
    /**
     * 增值订单数-环比
     **/
    @Column(name = "quantityAddMom")
    @Type(value = Types.BIGINT)
    private Long quantityAddMom;

    /**
     * 整体-票张
     **/
    @Column(name = "quantityV")
    @Type(value = Types.BIGINT)
    private Long quantityTotal;
    /**
     * 整体-票张-同比
     **/
    @Column(name = "quantityYoy")
    @Type(value = Types.BIGINT)
    private Long quantityTotalYoy;
    /**
     * 整体-票张-环比
     **/
    @Column(name = "quantityMom")
    @Type(value = Types.BIGINT)
    private Long quantityTotalMom;
}
