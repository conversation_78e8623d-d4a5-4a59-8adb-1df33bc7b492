package com.corpgovernment.resource.schedule.config;

import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Configuration
@EnableFeignClients("com.corpgovernment")
@EnableDiscoveryClient
public class OpenFeignConfig {
}
