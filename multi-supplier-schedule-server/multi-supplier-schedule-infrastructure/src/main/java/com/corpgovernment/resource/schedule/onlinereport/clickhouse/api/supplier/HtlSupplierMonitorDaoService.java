package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.supplier;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.supplier.vo.HotelInfoVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/25
 */
public interface HtlSupplierMonitorDaoService {

    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> jianbao(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String dim) throws Exception;

    /**
     * 查询酒店集团
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<String> queryAgreementHotelGroupName(BaseQueryConditionDTO request, String dim, String productType) throws Exception;

    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> index(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String dim) throws Exception;

    /**
     * 查询酒店集团
     *
     * @param request
     * @return
     * @throws Exception
     */
    <T> List<T> queryAgreementHotelGroup(BaseQueryConditionDTO request, Class<T> clazz, String dim,
                                         String productType) throws Exception;

    /**
     * @param requestDto
     * @param clazz
     * @param productType
     * @param dim
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryTop(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType,
                         String dim, String destinationType, String contractType) throws Exception;

    /**
     * @param requestDto
     * @param clazz
     * @param dim
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryTop(BaseQueryConditionDTO requestDto, Class<T> clazz, String dim, String contractType,
                         String needCorp) throws Exception;


    /**
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryTop(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;

    /**
     * 通过dim，查询行业和商旅数据
     *
     * @param dimIdList
     * @param dimFiled
     * @return
     */
    <T> List<T> listCorpAndIndustryDataByDimList(BaseQueryConditionDTO request, String productType,
                                                 String destinationType, String contractType,
                                                 List<String> dimIdList, String dimFiled, Class<T> clazz) throws Exception;

    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> agreementView(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception;

    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> agreementMomAndYoy(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception;

    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> agreementAgg(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception;

    /**
     * 协议消费明细
     *
     * @param requestDto
     * @param productType
     * @return
     * @throws Exception
     */
    List<Map> agreementDetail(BaseQueryConditionDTO requestDto, String productType, String lang,
                              boolean needGroup) throws Exception;

    /**
     * 协议消费明细
     *
     * @param requestDto
     * @param lang
     * @param needGroup
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> agreementDetail(BaseQueryConditionDTO requestDto, String lang, boolean needGroup,
                                Class<T> clazz) throws Exception;

    /**
     * 协议消费明细
     *
     * @param requestDto
     * @param productType
     * @return
     * @throws Exception
     */
    List<Map> agreementDeptDetail(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO requestDto,
                                  Pager pager, String productType, String user) throws Exception;

    /**
     * 协议消费明细
     *
     * @param requestDto
     * @param productType
     * @return
     * @throws Exception
     */
    Integer agreementDeptDetailCount(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO requestDto,
                                     String productType, String user) throws Exception;

    List<HotelInfoVo> queryHotelInfoByHotelName(BaseQueryConditionDTO baseQueryConditionDTO, List<String> dimList,
                                                String dimField) throws Exception;

    List<HotelInfoVo> queryHotelGroupInfoByHotelGroupName(BaseQueryConditionDTO baseQueryConditionDTO,
                                                          List<String> dimList, String dimField) throws Exception;
}
