package com.corpgovernment.resource.schedule.onlinereport.enums.save;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;

import java.util.Arrays;
import java.util.List;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.save
 * @description:机酒节省分析-里程均价x轴
 * @author: md_wang
 * @create: 2022-08-02 20:00
 **/
public enum FlightAvgMileageXEnum {
    /**
     * [0-0.25)
     */
    ZERO("0-0.25"),
    /**
     * [0.25-0.5)
     */
    ONE("0.25-0.5"),
    /**
     * [0.5~0.75)
     */
    TWO("0.5-0.75"),
    /**
     * [0.75-1)
     */
    THREE("0.75-1"),
    /**
     * [1-1.25)
     */
    FOUR("1-1.25"),
    /**
     * [1.25-1.5)
     */
    FIVE("1.25-1.5"),
    /**
     * 1.5及以上
     */
    SIX(">=1.5"),
    ;

    private String x;

    FlightAvgMileageXEnum(String x) {
        this.x = x;
    }

    /**
     * >=1的里程均價
     */
    public static List<FlightAvgMileageXEnum> mileageG1() {
        return Arrays.asList(FlightAvgMileageXEnum.FOUR, FlightAvgMileageXEnum.FIVE, FlightAvgMileageXEnum.SIX);
    }

    public static List<FlightAvgMileageXEnum> mileageG125() {
        return Arrays.asList(FlightAvgMileageXEnum.FIVE, FlightAvgMileageXEnum.SIX);
    }

    public static List<FlightAvgMileageXEnum> mileageG15() {
        return Arrays.asList(FlightAvgMileageXEnum.SIX);
    }

    public static String mileageYuan(FlightAvgMileageXEnum mileageXEnum) {
        if (FlightAvgMileageXEnum.FOUR.equals(mileageXEnum)) {
            return "1";
        }
        if (FlightAvgMileageXEnum.FIVE.equals(mileageXEnum)) {
            return "1.25";
        }
        if (FlightAvgMileageXEnum.SIX.equals(mileageXEnum)) {
            return "1.5";
        }
        return OrpConstants.EMPTY;
    }

    public String getX() {
        return x;
    }

    public void setX(String x) {
        this.x = x;
    }
}
