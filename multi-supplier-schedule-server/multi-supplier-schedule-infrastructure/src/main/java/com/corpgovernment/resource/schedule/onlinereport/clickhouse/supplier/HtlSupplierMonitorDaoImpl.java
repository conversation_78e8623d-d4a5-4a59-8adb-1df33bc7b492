package com.corpgovernment.resource.schedule.onlinereport.clickhouse.supplier;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.CostCenterAndDepartSearchType;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SqlFieldValidator;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/*
 * <AUTHOR>
 * @date 2022/6/20 13:08
 * @Desc
 */
@Repository
@Slf4j
public class HtlSupplierMonitorDaoImpl extends AbstractSupplierMonitorDao {

    private static final String LOG_TITLE = "HtlSupplierMonitorDaoImpl";


    private static final String CITY_HOTEL_SQL = "SELECT company.dim as dim\n" +
            ",company.sumRealPay as sumAmount,company.sumQuantity as sumQuantity,company.sumRoomPrice as sumRoomPrice\n" +
            ",company.sumRealPayC as sumAmount_ta,company.sumQuantityC as sumQuantity_ta,company.sumRoomPriceC as sumRoomPrice_ta\n" +
            ",company.sumRealPayM as sumAmount_nta,company.sumQuantityM as sumQuantity_nta,company.sumRoomPriceM as sumRoomPrice_nta\n" +
            ",corp.sumQuantityC as corpSumQuantity_ta,corp.sumRoomPriceC as corpSumRoomPrice_ta\n" +
            ",corp.sumQuantityM as corpSumQuantity_nta,corp.sumRoomPriceM as corpSumRoomPrice_nta\n" +

            "from(SELECT %s AS dim\n" +
            "    , SUM(real_pay) as sumRealPay\n" +
            "    , SUM(quantity) as sumQuantity\n" +
            "    , SUM(room_price) as sumRoomPrice\n" +
            "    , SUM(case when order_type = 'C' THEN real_pay else 0 END) as sumRealPayC\n" +
            "    , SUM(case when order_type = 'C' THEN quantity else 0 END) as sumQuantityC\n" +
            "    , SUM(case when order_type = 'C' THEN room_price else 0 END) as sumRoomPriceC\n" +

            "    , SUM(case when order_type = 'M' THEN real_pay else 0 END) as sumRealPayM\n" +
            "    , SUM(case when order_type = 'M' THEN quantity else 0 END) as sumQuantityM\n" +
            "    , SUM(case when order_type = 'M' THEN room_price else 0 END) as sumRoomPriceM\n" +

            "FROM corpbi_onlinereport_ckchdb.olrpt_indexhoteldownload_all\n" +
            "WHERE %s %s\n" +
            "GROUP BY dim order by sumRealPay desc) company\n" +
            "left join(\n" +
            "SELECT %s AS dim\n" +
            "    , SUM(case when order_type = 'C' THEN quantity else 0 END) as sumQuantityC\n" +
            "    , SUM(case when order_type = 'C' THEN room_price else 0 END) as sumRoomPriceC\n" +
            "    , SUM(case when order_type = 'M' THEN quantity else 0 END) as sumQuantityM\n" +
            "    , SUM(case when order_type = 'M' THEN room_price else 0 END) as sumRoomPriceM\n" +
            "FROM corpbi_onlinereport_ckchdb.olrpt_indexhoteldownload_all\n" +
            "WHERE %s %s\n" +
            "GROUP BY dim) corp\n" +
            "on company.dim = corp.dim";

    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> jianbao(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String dim) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);

        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        // dim值 city_name_en,hotel_group_name_en,hotel_group_name_en,hotel_group_name,star

        if ("agreement_mgrgroup_name".equals(dim)) {
            return new ArrayList<>();
        }


        sqlBuilder.append(String.format("select %s as dim ", dim));
        sqlBuilder.append(", round(CASE when sum(coalesce(quantity, 0)) != 0 then " +
                "sum(CASE when producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 END)/ sum(coalesce(quantity, 0)) ELSE 0 END * 100, 2) " +
                "as taPercent \n");
        sqlBuilder.append(", SUM(coalesce(real_pay, 0)) AS sumAmount \n");
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) AS sumQuantity \n");
        sqlBuilder.append(", sum(CASE when producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) else 0 END) as sumAmountTa \n");

        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        if (!StringUtils.equalsIgnoreCase("pcitylevel", dim) && !StringUtils.equalsIgnoreCase("star", dim)) {
            sqlBuilder.append(String.format("and coalesce(%s, '') != '' ", dim));
        }
        sqlBuilder.append(" group by dim ");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 查询酒店集团
     *
     * @param request
     * @return
     * @throws Exception
     */
    public List<String> queryAgreementHotelGroupName(BaseQueryConditionDTO request, String dim, String productType) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);

        if ("agreement_mgrgroup_name".equals(dim)) {
            return new ArrayList<>();
        }

        StringBuilder sqlBuilder = new StringBuilder();
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(request.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        String threeAgreement = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        sqlBuilder.append(String.format("select %s as dim ", dim));
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        sqlBuilder.append(" AND producttype_all = '" + threeAgreement + "' ");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(request, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(request, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append(" group by dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return DbResultMapUtils.mapStrResultList(u, "dim");
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "queryAgreementHotelGroupName");
    }

    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */

    public <T> List<T> index(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String dim) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);

        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(String.format("select %s as dim ", dim));
        sqlBuilder.append(", sum(CASE when producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) else 0 END) as sumAmountTa \n");
        sqlBuilder.append(", SUM(coalesce(real_pay, 0)) AS sumAmount \n");

        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append(" group by dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "index");
    }

    /**
     * 查询酒店集团
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryAgreementHotelGroup(BaseQueryConditionDTO request, Class<T> clazz, String dim, String productType) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);

        StringBuilder sqlBuilder = new StringBuilder();
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                request.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        String threeAgreement = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        sqlBuilder.append(String.format("select hotel_group_name as dimId, %s as dim ", dim));
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        sqlBuilder.append(" AND producttype_all = '" + threeAgreement + "' ");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(request, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(request, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append("group by dimId,dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryAgreementHotelGroup");
    }

    /**
     * @param requestDto
     * @param clazz
     * @param productType
     * @param dim
     * @param <T>
     * @return
     * @throws Exception
     */

    public <T> List<T> queryTop(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType,
                                String dim, String destinationType, String contractType) throws Exception {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);

        if("agreement_mgrgroup_name".equals(dim)){
            return new ArrayList<>();
        }

        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        // dim值 city_name_en,city_name,hotel_name_en,hotel_name,hotel_group_name_en,hotel_group_name,hotel_brand_name,pcitylevel,tar
        sqlBuilder.append(String.format("select %s as dim ", dim));
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) as sumQuantity \n");
        sqlBuilder.append(", round(SUM(coalesce(real_pay, 0)),2) as sumAmount \n");
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(coalesce(quantity, 0))) != 0 " +
                "THEN toFloat64(SUM(coalesce(room_price, 0))) / toFloat64(SUM(coalesce(quantity, 0))) ELSE 0 END,2) AS avgPrice  \n");

        sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 end) AS sumQuantity_ta  \n");
        sqlBuilder.append(", round(CASE when toFloat64(SUM(coalesce(quantity, 0))) != 0 \n" +
                "    then toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 end))" +
                "/toFloat64(SUM(coalesce(quantity, 0))) else 0 end * 100,2) as sumQuantity_ta_percent  \n");
        sqlBuilder.append(", round(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) else 0 end),2) AS sumAmount_ta  \n");
        sqlBuilder.append(", round(CASE when toFloat64(SUM(coalesce(real_pay, 0))) != 0 \n" +
                "    then toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) else 0 end))" +
                "/ toFloat64(SUM(coalesce(real_pay, 0))) else 0 end * 100,2) AS sumAmount_ta_percent   \n");
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 end)) != 0 THEN " +
                "toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(room_price, 0) else 0 end)) " +
                "/ toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 end)) ELSE 0 END,2) AS avgPrice_ta  \n");

        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(quantity, 0) else 0 end) AS sumQuantity_nta  \n");
        sqlBuilder.append(", round(CASE when toFloat64(SUM(coalesce(quantity, 0))) != 0 \n" +
                "    then toFloat64(SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(quantity, 0) else 0 end))" +
                "/toFloat64(SUM(coalesce(quantity, 0))) else 0 end * 100,2) as sumQuantity_nta_percent  \n");
        sqlBuilder.append(", round(SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(real_pay, 0) else 0 end),2) AS sumAmount_nta \n");
        sqlBuilder.append(", round(CASE when toFloat64(SUM(coalesce(real_pay, 0))) != 0 \n" +
                "    then toFloat64(SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(real_pay, 0) else 0 end))" +
                "/ toFloat64(SUM(coalesce(real_pay, 0))) else 0 end * 100,2) AS sumAmount_nta_percent \n");

        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(quantity, 0) else 0 end)) != 0 " +
                "THEN toFloat64(SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(room_price, 0) else 0 end)) " +
                "/ toFloat64(SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(quantity, 0) else 0 end)) ELSE 0 END,2) as avgPrice_nta \n");
        if (StringUtils.equalsIgnoreCase(dim, "hotel_group_name_en") || StringUtils.equalsIgnoreCase(dim, "hotel_group_name") ||
                StringUtils.equalsIgnoreCase(dim, "hotel_name_en") || StringUtils.equalsIgnoreCase(dim, "hotel_name")){
            String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");
            // 三方协议的节省金额
            sqlBuilder.append(", SUM(coalesce(save_amount_3c, 0)) as saveAmount3c \n");
            // 两方尊享的节省金额
            sqlBuilder.append(", SUM(coalesce(save_amount_premium, 0)) as saveAmountPremium \n");
            // 促销优惠活动的节省金额
            sqlBuilder.append(", SUM(coalesce(save_amount_promotion, 0)) as saveAmountPromotion \n");
            // 三方房价（不含服务费，未扣除优惠券）
            sqlBuilder.append(",sum(case when (producttype_all ='" + ta + "' and save_amount_3c is not null) then coalesce(corp_real_pay, 0) else 0 end)  as roomPrice3c \n");
            // 参加优惠活动房价（不含服务费，未扣除优惠券）
            sqlBuilder.append(",sum(case when save_amount_promotion != 0 then coalesce(corp_real_pay, 0) else 0 end)  as roomPricePromotion \n");
            // 两房尊享房价（不含服务费，未扣除优惠券）
            sqlBuilder.append(",sum(case when (producttype_all ='" + premium + "' and save_amount_premium is not null) then coalesce(corp_real_pay, 0) else 0 end)  " +
                    "as roomPricePremium \n");
            // 房价
            sqlBuilder.append(", SUM(coalesce(room_price, 0)) as sumPrice \n");
            sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(room_price, 0) else 0 end) as sumPrice_ta \n");
            sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(room_price, 0) else 0 end) as sumPrice_nta \n");
        }

        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        if (!StringUtils.equalsIgnoreCase("pcitylevel", dim) && !StringUtils.equalsIgnoreCase("star", dim)) {
            sqlBuilder.append(String.format("and coalesce(%s, '') != '' ", dim));
        }
        if ((StringUtils.equalsIgnoreCase(dim, "city_name") || StringUtils.equalsIgnoreCase(dim, "city_name_en")) && StringUtils.isNotEmpty(destinationType)){
            sqlBuilder.append(getDestCondition(destinationType));
        }
        sqlBuilder.append(getHtlContractTypeCondition(contractType));
        sqlBuilder.append(" group by dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryTop");
    }

    /**
     * @param requestDto
     * @param clazz
     * @param dim
     * @param <T>
     * @return
     * @throws Exception
     */

    public <T> List<T> queryTop(BaseQueryConditionDTO requestDto, Class<T> clazz, String dim, String contractType, String needCorp) throws Exception {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String firstTier = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.firstTier");
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        // dim值 city_name_en,city_name,hotel_name_en,hotel_name,hotel_group_name_en,hotel_group_name,hotel_brand_name,pcitylevel,tar
        if (StringUtils.equalsIgnoreCase(needCorp, "T")){
            sqlBuilder.append(aggreation(requestDto, requestDto.getProductType(), dim, parmList));
        }else {
            sqlBuilder.append(String.format("select %s as dim ", dim));
            sqlBuilder.append(", SUM(coalesce(quantity, 0)) as sumQuantity \n");
            sqlBuilder.append(", round(SUM(coalesce(real_pay, 0)),2) as sumAmount \n");
            sqlBuilder.append(", round(SUM(coalesce(room_price, 0)),2) as sumRoomPrice \n");
            // 协议
            sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 end) AS sumQuantity_ta  \n");
            sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) else 0 end) AS sumAmount_ta  \n");
            sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(room_price, 0) else 0 end)AS sumRoomPrice_ta  \n");
            // 非协议
            sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(quantity, 0) else 0 end) AS sumQuantity_nta  \n");
            sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(real_pay, 0) else 0 end) AS sumAmount_nta \n");
            sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(room_price, 0) else 0 end)as sumRoomPrice_nta \n");
            // 一线
            sqlBuilder.append(", SUM(CASE WHEN (pcitylevel = '" + firstTier + "') THEN coalesce(quantity, 0) else 0 end) AS sumQuantity_firstTier  \n");
            sqlBuilder.append(", SUM(CASE WHEN (pcitylevel = '" + firstTier + "') THEN coalesce(real_pay, 0) else 0 end) AS sumAmount_firstTier  \n");
            sqlBuilder.append(", SUM(CASE WHEN (pcitylevel = '" + firstTier + "') THEN coalesce(room_price, 0) else 0 end)AS sumRoomPrice_firstTier  \n");
            // 非一线
            sqlBuilder.append(", SUM(CASE WHEN (pcitylevel != '" + firstTier + "') THEN coalesce(quantity, 0) else 0 end) AS sumQuantity_nonFirstTier  \n");
            sqlBuilder.append(", SUM(CASE WHEN (pcitylevel != '" + firstTier + "') THEN coalesce(real_pay, 0) else 0 end) AS sumAmount_nonFirstTier \n");
            sqlBuilder.append(", SUM(CASE WHEN (pcitylevel != '" + firstTier + "') THEN coalesce(room_price, 0) else 0 end)as sumRoomPrice_nonFirstTier \n");
            // 国内
            sqlBuilder.append(", SUM(CASE WHEN (is_oversea='F' or is_oversea='O') THEN coalesce(quantity, 0) else 0 end) AS sumQuantity_dom  \n");
            sqlBuilder.append(", SUM(CASE WHEN (is_oversea='F' or is_oversea='O') THEN coalesce(real_pay, 0) else 0 end) AS sumAmount_dom  \n");
            sqlBuilder.append(", SUM(CASE WHEN (is_oversea='F' or is_oversea='O') THEN coalesce(room_price, 0) else 0 end)AS sumRoomPrice_dom  \n");
            // 国际
            sqlBuilder.append(", SUM(CASE WHEN is_oversea='T' THEN coalesce(quantity, 0) else 0 end) AS sumQuantity_inter  \n");
            sqlBuilder.append(", SUM(CASE WHEN is_oversea='T' THEN coalesce(real_pay, 0) else 0 end) AS sumAmount_inter \n");
            sqlBuilder.append(", SUM(CASE WHEN is_oversea='T' THEN coalesce(room_price, 0) else 0 end)as sumRoomPrice_inter \n");
            sqlBuilder.append(" FROM ");
            sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
            sqlBuilder.append(OrpConstants.WHERE);
            sqlBuilder.append("d = ?");
            parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
            if (isBookCaliber) {
                sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
            }
            sqlBuilder.append(getHotelCondition(requestDto.getProductType()));
            sqlBuilder.append(getHtlContractTypeCondition(contractType));
            sqlBuilder.append(" group by dim ");
        }
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */

    public <T> List<T> queryTop(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select city_name as dim ");
        sqlBuilder.append(", round(SUM(coalesce(real_pay, 0)),2) as sumAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(" group by dim  ORDER BY sumAmount DESC");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    public <T> List<T> listCorpAndIndustryDataByDimList(BaseQueryConditionDTO request, String productType,
                                                        String destinationType, String contractType,
                                                        List<String> dimIdList, String dimFiled, Class<T> clazz) throws Exception {
        return null;
    }

    public String agreementViewSql(List<Object> parmList, BaseQueryConditionDTO baseQueryConditionDto, String productType, boolean isBookCaliber) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 是否协议（Y:三方协议、N:非三方协议）
        sqlBuilder.append(" CASE WHEN producttype_all = '" + ta + "' THEN 'TA' ELSE 'NTA' END AS isAgreement \n");
        // 消费金额
        sqlBuilder.append(", SUM(coalesce(real_pay, 0)) AS totalPrice \n");
        // 票张数
        sqlBuilder.append(", SUM(coalesce(quantity)) AS totalQuantity \n");
        // 房价（为了计算均价的时候和机票的sql保持一致）
        sqlBuilder.append(", SUM(coalesce(room_price, 0)) AS totalPriceEconomy\n");
        // 间夜数（为了计算均价的时候和机票的sql保持一致）
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) AS totalQuantiyEconomy\n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(baseQueryConditionDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append(" group by isAgreement");
        // 查询clickhouse
        return sqlBuilder.toString();
    }

    public String agreementMomAndYoySql(List<Object> parmList, BaseQueryConditionDTO baseQueryConditionDto, String productType, boolean isBookCaliber) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String partion = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select curr.totalPrice as currentTotalPrice, mom.totalPrice as momTotalPrice, yoy.totalPrice as yoyTotalPrice,totalQuantity,avgPrice \n");
        sqlBuilder.append(" FROM ( \n");
        sqlBuilder.append(" SELECT SUM(coalesce(real_pay, 0)) AS totalPrice,SUM(coalesce(quantity, 0)) AS totalQuantity \n");
        sqlBuilder.append(",CASE WHEN toFloat64(SUM(quantity)) != 0 THEN toFloat64(SUM(coalesce(room_price, 0))) / toFloat64(SUM(quantity)) ELSE 0 END AS avgPrice");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList,
                    baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(), partion, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList,
                    baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(), partion, OrpConstants.REPORT_DATE));
        }
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append(" and producttype_all = '" + ta + "'");
        sqlBuilder.append(" ) curr ");
        sqlBuilder.append(" cross join ( \n");
        sqlBuilder.append(" SELECT SUM(coalesce(real_pay, 0)) AS totalPrice \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<String> momTimes = OrpDateTimeUtils.momTime(baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime());
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList,
                    momTimes.get(0), momTimes.get(1), partion, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList,
                    momTimes.get(0), momTimes.get(1), partion, OrpConstants.REPORT_DATE));
        }
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append(" and producttype_all = '" + ta + "'");
        sqlBuilder.append(" ) mom ");
        sqlBuilder.append(" cross join ( \n");
        sqlBuilder.append(" SELECT SUM(coalesce(real_pay, 0)) AS totalPrice \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<String> yoyTimes = OrpDateTimeUtils.yoyTime(baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(), OrpConstants.ONE_YEAR_DAYS);
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList,
                    yoyTimes.get(0), yoyTimes.get(1), partion, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList,
                    yoyTimes.get(0), yoyTimes.get(1), partion, OrpConstants.REPORT_DATE));
        }
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append(" and producttype_all = '" + ta + "'");
        sqlBuilder.append(" ) yoy ");
        return sqlBuilder.toString();
    }

    public String agreementAggSql(List<Object> parmList, BaseQueryConditionDTO baseQueryConditionDto, String productType, boolean isBookCaliber) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 会员国内、港澳台消费金额
        sqlBuilder.append(" SUM(CASE WHEN producttype_all = '" + ta + "' AND is_oversea in('F', 'O') THEN coalesce(real_pay, 0) ELSE 0 end) AS totalPriceDomTa \n");
        // 会员海外消费金额
        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' AND is_oversea in('F', 'O') THEN coalesce(real_pay, 0) ELSE 0 end) AS totalPriceDomNta \n");
        // 协议国内、港澳台消费金额
        sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' AND is_oversea in('T') THEN coalesce(real_pay, 0) ELSE 0 end) AS totalPriceInterTa \n");
        // 协议海外消费金额
        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' AND is_oversea in('T') THEN coalesce(real_pay, 0) ELSE 0 end) AS totalPriceInterNta \n");
        // 会员国内、港澳台间夜数
        sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' AND is_oversea in('F', 'O') THEN coalesce(quantity, 0) ELSE 0 end) AS totalQuantityDomTa \n");
        // 会员海外间夜数
        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' AND is_oversea in('F', 'O') THEN coalesce(quantity, 0) ELSE 0 end) AS totalQuantityDomNta \n");
        // 协议国内、港澳台间夜数
        sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' AND is_oversea in('T') THEN coalesce(quantity, 0) ELSE 0 end) AS totalQuantityInterTa \n");
        // 协议海外间夜数
        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' AND is_oversea in('T') THEN coalesce(quantity, 0) ELSE 0 end) AS totalQuantityInterNta \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(baseQueryConditionDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        return sqlBuilder.toString();
    }

    public String agreementDetailSql(List<Object> parmList, BaseQueryConditionDTO requestDto, String productType, boolean isBookCaliber,
                                     String lang, boolean needGroup) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        StringBuilder sqlBuilder = new StringBuilder();
        if (needGroup) {
            sqlBuilder.append("select ").append(SharkUtils.isEN(lang) ? "hotel_group_name_en as hotelGroupName" : "hotel_group_name as hotelGroupName");
        } else {
            sqlBuilder.append("select '" + SharkUtils.get("Index.sum", lang) + "' as hotelGroupName");
        }
        // 总消费金额
        sqlBuilder.append(" , SUM(coalesce(real_pay, 0)) AS HTL_TOTAL_REAL_PAY \n");
        // 三方消费金额
        sqlBuilder.append(", COALESCE(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) END), 0) AS HTL_TOTAL_REAL_PAY_TA \n");
        // 三方协议消费金额占比
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(coalesce(real_pay, 0))) != 0 " +
                "then toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) else 0 END))/ toFloat64(SUM(coalesce(real_pay, 0))) else 0 end * 100, 4) " +
                "as HTL_REAL_PAY_TA_PERCENT\n");
        // 总间夜数
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) AS HTL_TOTAL_QUANTITY \n");
        // 三方间夜数
        sqlBuilder.append(", COALESCE(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) END), 0) AS HTL_TOTAL_QUANTITY_TA \n");
        // 三方协议间夜数占比
        sqlBuilder.append(", round(CASE WHEN SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 END) != 0 " +
                "then SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 END)/ SUM(quantity) else 0 end * 100, 4) " +
                "AS HTL_QUANTITY_TA_PERCENT \n");
        // 三方间夜均价
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 END)) != 0 " +
                "then toFloat64(SUM(CASE  WHEN producttype_all = '" + ta + "' THEN coalesce(room_price, 0) else 0 END)) " +
                "/ toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 END)) else 0 END, 2) AS HTL_AVG_PRICE_TA \n");
        // 非三方间夜均价
        sqlBuilder.append(" , round(CASE WHEN toFloat64(SUM(CASE WHEN producttype_all <> '" + ta + "' THEN coalesce(quantity, 0) else 0 END)) != 0 " +
                "then toFloat64(SUM(CASE WHEN producttype_all <> '" + ta + "' THEN coalesce(room_price, 0) else 0 END)) " +
                "/ toFloat64(SUM(CASE WHEN producttype_all <> '" + ta + "' THEN coalesce(quantity, 0) else 0 END)) else 0 END, 2) AS HTL_AVG_PRICE_NTA \n");
        // RC订单数占比
        sqlBuilder.append(" , round(CASE WHEN COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END) != 0 \n" +
                "    then COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END) " +
                "/ COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END) ELSE 0 END, 4) * 100 AS HTL_RC_PERCENT \n");
        // 三方协议节省金额(仅国内经济舱）
        sqlBuilder.append(" , round(SUM(coalesce(save_amount_3c, 0)),2) AS HTL_TOTAL_SAV_AMOUNT_3C \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(" and hotel_group_name in(").append(airlineSubQuery(parmList, requestDto, productType, isBookCaliber)).append(")");
        sqlBuilder.append(buildListConditionPreSql(requestDto.getHotelGroups(), parmList, "hotel_group_name"));
        sqlBuilder.append(getHotelCondition(productType));
        if (needGroup) {
            sqlBuilder.append(" group by ").append(SharkUtils.isEN(lang) ? "hotel_group_name_en" : "hotel_group_name");
        }
        sqlBuilder.append(" order by HTL_TOTAL_REAL_PAY desc");
        // 查询clickhouse
        return sqlBuilder.toString();
    }

    public String agreementDeptDetailSql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO requestDto, Pager pager,
                                         String productType, boolean isBookCaliber, String user) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select %s");
        // 总消费金额
        sqlBuilder.append(" , SUM(coalesce(real_pay, 0)) AS HTL_TOTAL_REAL_PAY \n");
        // 三方消费金额

        sqlBuilder.append(", COALESCE(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) END), 0) AS HTL_TOTAL_REAL_PAY_TA");
        // 三方协议消费金额占比
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(coalesce(real_pay, 0))) != 0 " +
                "then toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) else 0 END))/ toFloat64(SUM(coalesce(real_pay, 0))) else 0 end * 100, 2) " +
                "as HTL_REAL_PAY_TA_PERCENT\n");
        // 总间夜数
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) AS HTL_TOTAL_QUANTITY \n");
        // 三方间夜数
        sqlBuilder.append(", COALESCE(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) END), 0) AS HTL_TOTAL_QUANTITY_TA");
        // 三方协议间夜数占比
        sqlBuilder.append(", round(CASE WHEN SUM(coalesce(quantity, 0)) != 0 " +
                "then SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 END)/ SUM(coalesce(quantity, 0)) else 0 end * 100, 2) " +
                "AS HTL_QUANTITY_TA_PERCENT \n");
        // 三方间夜均价
        sqlBuilder.append(", round(CASE WHEN toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 END)) != 0 " +
                "then toFloat64(SUM(CASE  WHEN producttype_all = '" + ta + "' THEN coalesce(room_price, 0) else 0 END)) " +
                "/ toFloat64(SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 END)) else 0 END, 2) AS HTL_AVG_PRICE_TA \n");
        // 非三方间夜均价
        sqlBuilder.append(" , round(CASE WHEN toFloat64(SUM(CASE WHEN producttype_all <> '" + ta + "' THEN coalesce(quantity, 0) else 0 END)) != 0 " +
                "then toFloat64(SUM(CASE WHEN producttype_all <> '" + ta + "' THEN coalesce(room_price, 0) else 0 END)) " +
                "/ toFloat64(SUM(CASE WHEN producttype_all <> '" + ta + "' THEN coalesce(quantity, 0) else 0 END)) else 0 END, 2) AS HTL_AVG_PRICE_NTA \n");
        // RC订单数占比
        sqlBuilder.append(" , round(CASE WHEN COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END) != 0 \n" +
                "    then COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END) " +
                "/ COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END) ELSE 0 END, 4) * 100 AS HTL_RC_PERCENT \n");
        // 三方协议节省金额(仅国内经济舱）
        sqlBuilder.append(" , round(SUM(coalesce(save_amount_3c, 0)),2) AS HTL_TOTAL_SAV_AMOUNT_3C \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        JoinCondition biz = joinCondition(analysisObjectEnum);
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append(buildListConditionPreSql(requestDto.getAirLines(), parmList, "hotel_group_name"));
        sqlBuilder.append(buildPreSqlUser(user, parmList));
        sqlBuilder.append(" group by %s");
        sqlBuilder.append(String.format(" order by HTL_TOTAL_REAL_PAY desc, %s", biz.getGroupFields()));
        sqlBuilder.append(" limit ?, ? ");
        parmList.add((pager.pageIndex - 1) * pager.pageSize);
        parmList.add(pager.pageSize);
        // 查询clickhouse
        return String.format(sqlBuilder.toString(), biz.getResultFields(), biz.getGroupFields());
    }


    public String agreementDeptDetailCountSql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO requestDto,
                                              String productType, boolean isBookCaliber, String user) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select count(1) as countAll from( select %s");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append(buildListConditionPreSql(requestDto.getAirLines(), parmList, "hotel_group_name"));
        sqlBuilder.append(buildPreSqlUser(user, parmList));
        sqlBuilder.append(" group by %s)");
        JoinCondition biz = joinCondition(analysisObjectEnum);
        return String.format(sqlBuilder.toString(), biz.getResultFields(), biz.getGroupFields());
    }

    private String airlineSubQuery(List<Object> parmList, BaseQueryConditionDTO requestDto, String productType, boolean isBookCaliber) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select DISTINCT hotel_group_name");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, OrpConstants.ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append(" AND producttype_all = '" + ta + "' AND hotel_group_name <> '' ");
        return sqlBuilder.toString();
    }


    protected String getDestCondition(String destType) {
        StringBuilder stringBuilder = new StringBuilder();
        String china = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.country.china");
        // 国内
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(destType, "dom")) {
            stringBuilder.append(" and country_name = '" + china + "'  ");
        }
        // 国际
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(destType, "inter")) {
            stringBuilder.append(" and country_name != '" + china + "'  ");
        }
        return stringBuilder.toString();
    }

    /**
     * 查询 在线报告概览
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    public String aggreation(BaseQueryConditionDTO requestDto, String productType, String dim, List<Object> parmList) throws BusinessException {
        // Validate dim field against whitelist to prevent SQL injection
        dim = SqlFieldValidator.validateDimField(dim);

        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        // %s %s 分区公司公共条件条件，酒店类型
        return String.format(CITY_HOTEL_SQL
                , dim, BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partition), getHotelOrderTypeCondition(productType)
                , dim, BaseConditionPrebuilder.buildCorpPreSql(requestDto, parmList, partition), getHotelOrderTypeCondition(productType));
    }

    public List<String> queryCostCenterOrDepartmentOrCorpId(CostCenterAndDepartSearchType searchType) throws Exception {
        return super.queryCostCenterOrDepartmentOrCorpId(searchType,ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
    }
}
