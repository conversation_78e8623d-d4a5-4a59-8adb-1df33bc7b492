package com.corpgovernment.resource.schedule.onlinereport.enums;

/**
 * <AUTHOR>
 * @date 2022-08-05 14:18
 */
public enum StatiscalCaliberEnum {
    BOOKING,DEALING,TRAVELING;

    public static StatiscalCaliberEnum getByName(String name) {
        for (StatiscalCaliberEnum value : values()) {
            if (value.name().equalsIgnoreCase(name)) {
                return value;
            }
        }
        return null;
    }
}
