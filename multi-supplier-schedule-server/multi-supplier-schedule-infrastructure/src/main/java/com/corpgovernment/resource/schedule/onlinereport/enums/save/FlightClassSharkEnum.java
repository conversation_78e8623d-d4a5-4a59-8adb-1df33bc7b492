package com.corpgovernment.resource.schedule.onlinereport.enums.save;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.save
 * @description:机票仓等shark
 * @author: md_wang
 * @create: 2022-08-02 20:00
 **/
public enum FlightClassSharkEnum {
    /**
     * 经济舱
     */
    ECONOMY("economy","lbl_FlightRealClass_Economy"),
    /**
     * 头等舱
     */
    FIRST("first", "lbl_FlightRealClass_First"),

    /**
     * 商务舱
     */
    BUSINESS("business", "Save.FltClass.Business"),
    /**
     * 头等舱及商务舱
     */
    FIRST_BUSINESS("first_business", "Save.Choice1"),
    ;

    private String name;
    private String sharkKey;

    FlightClassSharkEnum(String name, String sharkKey) {
        this.name = name;
        this.sharkKey = sharkKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public void setSharkKey(String sharkKey) {
        this.sharkKey = sharkKey;
    }
}
