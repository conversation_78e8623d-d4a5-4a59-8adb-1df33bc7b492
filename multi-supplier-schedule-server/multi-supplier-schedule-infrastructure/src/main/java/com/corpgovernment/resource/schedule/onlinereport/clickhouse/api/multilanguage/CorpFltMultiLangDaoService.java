package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.multilanguage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
public interface CorpFltMultiLangDaoService {
    /**
     *
     * 机票
     * corpbi_onlinereport_ckchdb.dimcorp_flt_multi_language_airport  机场
     * corpbi_onlinereport_ckchdb.dimcorp_flt_multi_language_airline  航线
     * corpbi_onlinereport_ckchdb.dimcorp_flt_citymultilang_all       城市
     *
     * 酒店
     * corpbi_onlinereport_ckchdb.dimcorp_htl_brandmultilang     品牌
     * corpbi_onlinereport_ckchdb.dimcorp_htl_mgrgroupmultilang  集团
     * corpbi_onlinereport_ckchdb.dimcorp_htl_roommultilang      房型
     * corpbi_onlinereport_ckchdb.dimcorp_htl_htlinfomultilang    酒店名称表待开发
     */

    /**
     * 查询 城市 多语言
     *
     * @param ids
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryGeoLocation(List<Integer> ids, String lang, Class<T> clazz) throws Exception;

    /**
     * 查询 城市 多语言
     *
     * @param names
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryGeoLocationByNames(List<String> names, String lang, Class<T> clazz) throws Exception;

    /**
     * 查询 航司 多语言
     *
     * @param ids
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryFltAirlineMultiLang(List<String> ids, String lang, Class<T> clazz) throws Exception;

    /**
     * 查询 机场 多语言
     *
     * @param ids
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryFltAirportMultiLang(List<String> ids, String lang, Class<T> clazz) throws Exception;
}
