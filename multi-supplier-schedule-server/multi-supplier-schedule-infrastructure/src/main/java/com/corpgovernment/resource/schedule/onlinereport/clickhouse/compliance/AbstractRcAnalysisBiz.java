package com.corpgovernment.resource.schedule.onlinereport.clickhouse.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValDataType;
import com.corpgovernment.resource.schedule.onlinereport.enums.RcStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.STRING;


/*
 * <AUTHOR>
 *
 * @date 2021/12/15 14:22
 *
 * @Desc 各成本中心明细数据
 */
@Service
public abstract class AbstractRcAnalysisBiz implements RcAnalysis<BaseQueryCondition> {

    public abstract List<RcStatisticalsEnum> getStatisticalList(QueryReportBuTypeEnum queryReportBuTypeEnum);

    /**
     * 获取标题
     *
     * @return
     */
    @Override
    public List<HeaderKeyValDataType> getHearder(AnalysisObjectEnum analysisObjectEnum, String lang,
                                                 QueryReportBuTypeEnum queryReportBuTypeEnum) {
        List<HeaderKeyValDataType> mapDimensions = getDimesionsDesc(analysisObjectEnum, lang);
        List<HeaderKeyValDataType> mapStaticals = getStaticalsDes(getStatisticalList(queryReportBuTypeEnum), lang);
        mapDimensions.addAll(mapStaticals);
        if (analysisObjectEnum.equals(AnalysisObjectEnum.UID)) {
            List<HeaderKeyValDataType> mapDept = getUidDept(lang);
            mapDimensions.addAll(mapDept);
        }
        return mapDimensions;
    }

    /**
     * 获取统计项标题
     *
     * @param statisticalStringList
     * @return
     */
    private List<HeaderKeyValDataType> getStaticalsDes(List<RcStatisticalsEnum> statisticalStringList, String lang) {
        List<HeaderKeyValDataType> result = new ArrayList<>();
        for (RcStatisticalsEnum deptStatisticalsEnum : statisticalStringList) {
            HeaderKeyValDataType headerKeyValMap = new HeaderKeyValDataType();
            headerKeyValMap.setHeaderKey(deptStatisticalsEnum.toString());
            headerKeyValMap.setHeaderValue(SharkUtils.get(deptStatisticalsEnum.getSharkKey(), lang));
            headerKeyValMap.setDataType(deptStatisticalsEnum.getDataType());
            result.add(headerKeyValMap);
        }
        return result;
    }

    /**
     * 获得维度标题
     *
     * @param analysisObjectEnum
     * @param lang
     * @return
     */
    private List<HeaderKeyValDataType> getDimesionsDesc(AnalysisObjectEnum analysisObjectEnum, String lang) {
        List<HeaderKeyValDataType> result = new ArrayList<>();
        switch (analysisObjectEnum) {
            case CORP:
                result.add(getHeaderKeyValMap(AnalysisObjectEnum.CORP, "Index.public", lang, STRING));
                break;
            case ACCOUNT:
                result = defaultHeader(lang);
                result.add(getHeaderKeyValMap(AnalysisObjectEnum.ACCOUNT.toString(), "DeptAnalysis.AccountName", lang, STRING));
                break;
            case ACCOUNTCODE:
                result = defaultHeader(lang);
                result.add(getHeaderKeyValMap(AnalysisObjectEnum.ACCOUNTCODE.toString(), "DeptAnalysis.AccountCode", lang, STRING));
                break;
            case DEPT1:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depone", lang, STRING));
                break;
            case DEPT2:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.deptwo", lang, STRING));
                break;
            case DEPT3:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depthree", lang, STRING));
                break;
            case DEPT4:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depfour", lang, STRING));
                break;
            case DEPT5:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depfive", lang, STRING));
                break;
            case DEPT6:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depsix", lang, STRING));
                break;
            case DEPT7:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depseven", lang, STRING));
                break;
            case DEPT8:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depeight", lang, STRING));
                break;
            case DEPT9:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depnight", lang, STRING));
                break;
            case DEPT10:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depten", lang, STRING));
                break;
            case COSTCENTER1:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcenterone", lang, STRING));
                break;
            case COSTCENTER2:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcentertwo", lang, STRING));
                break;
            case COSTCENTER3:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcenterthree", lang, STRING));
                break;
            case COSTCENTER4:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcenterfour", lang, STRING));
                break;
            case COSTCENTER5:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcenterfive", lang, STRING));
                break;
            case COSTCENTER6:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcentersix", lang, STRING));
                break;
            case UID:
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.uidnumber", lang, STRING));
                result.add(getHeaderKeyValMap("NAME", "Index.idholder", lang, STRING));
                break;
            default:
                break;
        }
        return result;
    }

    private List defaultHeader(String lang) {
        List result = new ArrayList<>();
        result.add(getHeaderKeyValMap(AnalysisObjectEnum.CORP, "Index.public", lang, STRING));
        return result;
    }

    private HeaderKeyValDataType getHeaderKeyValMap(AnalysisObjectEnum analysisObjectEnum, String sharkKey,
                                                    String lang, Integer dataType) {
        HeaderKeyValDataType headerKeyValMap = new HeaderKeyValDataType();
        headerKeyValMap.setHeaderKey(analysisObjectEnum.toString());
        headerKeyValMap.setHeaderValue(SharkUtils.getHeaderVal(sharkKey, lang));
        headerKeyValMap.setDataType(dataType);
        return headerKeyValMap;
    }

    private HeaderKeyValDataType getHeaderKeyValMap(String key, String sharkKey, String lang, Integer dataType) {
        HeaderKeyValDataType headerKeyValMap = new HeaderKeyValDataType();
        headerKeyValMap.setHeaderKey(key);
        headerKeyValMap.setHeaderValue(SharkUtils.getHeaderVal(sharkKey, lang));
        headerKeyValMap.setDataType(dataType);
        return headerKeyValMap;
    }

    /**
     * 统一格式化结果数据
     *
     * @param list
     */
    protected void fomratResultData(List<Map> list) {
        int i = 0;
        for (Map map : list) {
            i++;
            for (Object key : map.keySet()) {
                RcStatisticalsEnum rcStatisticalsEnum = getStaticalByKey((String) key);
                if (rcStatisticalsEnum != null) {
                    BigDecimal target = OrpReportUtils.formatBigDecimal(
                            Objects.isNull(map.get(key)) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(map.get(key))),
                            rcStatisticalsEnum.getNum(), false);
                    map.put(key, rcStatisticalsEnum.isPercent() ? target.toString().concat("%") : target);
                }
            }
        }
    }

    protected RcStatisticalsEnum getStaticalByKey(String key) {
        try {
            return RcStatisticalsEnum.valueOf((String) key);
        } catch (Exception e) {

        }
        return null;
    }

    protected List getIndustryList(String industryType) {
        if (StringUtils.isNotEmpty(industryType)) {
            return Arrays.asList(industryType);
        }
        return null;
    }

    /**
     * 得到员工部门和成本中心sharkKey
     *
     * @param lang
     */
    private List<HeaderKeyValDataType> getUidDept(String lang) {
        List<HeaderKeyValDataType> result = new ArrayList<>();
        result.add(getHeaderKeyValMap("NAME", "Exceltopname.cardholder", lang, STRING));
        result.add(getHeaderKeyValMap("DEPT1", "Exceltopname.depone", lang, STRING));
        result.add(getHeaderKeyValMap("COST_CENTER1", "Exceltopname.costcenterone", lang, STRING));
        return result;
    }
}
