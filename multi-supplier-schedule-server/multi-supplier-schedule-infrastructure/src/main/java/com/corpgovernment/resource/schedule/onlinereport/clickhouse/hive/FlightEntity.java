package com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/18 11:32
 * @Desc
 */
@Data
public class FlightEntity {
    private Long order_id;//"订单号",
    private String order_status;//	"订单状态",
    private String order_date;//	"订单预定时间",
    private String corp_corporation;//	"公司ID",
    private String corp_name;//	"公司名称",
    private String companygroupid;//	"公司集团ID",
    private String companygroup;//	"公司集团",
    private Long account_id;//"主账户账号",
    private String account_code;//	"主账户代号",
    private String account_name;//	"主账户公司名称",
    private Long sub_account_id;//"子账户账号",
    private String sub_account_code;//	"子账户代号",
    private String sub_account_name;//	"子账户公司名称",
    private String industry_type;//	"行业类型",
    private String industry_type_name;//	"行业名称",
    private String uid;//	"持卡人卡号",
    private String user_name;//	"持卡人姓名",
    private String employe_id;//	"持卡人员工编号",
    private String work_city;//	"工作所在城市",
    private String rank_name;//	"持卡人职级CN",
    private String cost_center1;//	"成本中心1",
    private String cost_center2;//	"成本中心2",
    private String cost_center3;//	"成本中心3",
    private String cost_center4;//	"成本中心4",
    private String cost_center5;//	"成本中心5",
    private String cost_center6;//	"成本中心6",
    private String dept1;//	"部门1",
    private String dept2;//	"部门2",
    private String dept3;//	"部门3",
    private String dept4;//	"部门4",
    private String dept5;//	"部门5",
    private String dept6;//	"部门6",
    private String dept7;//	"部门7",
    private String dept8;//	"部门8",
    private String dept9;//	"部门9",
    private String dept10;//	"部门10",
    private String fee_type;//	"是否个人消费行为:因公,因私,",
    private String is_online;//	"预订方式",
    private String prepay_type;//	"支付方式",
    private String acb_prepay_type;//	"结算类型",
    private String bosstype;//	"是否BOSS(T/F)",
    private Long trip_id;//"所属行程订单号",
    private String journey_no;//	"关联行程单号",
    private String journey_reason;//	"出行目的",
    private String journey_reason_code;//	"出行目的编号",
    private String project_code;//	"项目编号",
    private String project;//	"项目名称",
    private String verbal_authorize;//	"是否口头授权T/F",
    private String confirm_person;//	"一次授权人姓名",
    private String confirm_type;//	"一次授权方式",
    private String confirm_person2;//	"二次授权人姓名",
    private String confirm_type2;//	"二次授权方式",
    private Integer group_month;//"年月",
    private Integer print_month;//"出票、退票审核月份",
    private Integer print_year;//"出票年份",
    private String print_ticket_time;//	"出票时间",
    private String provide_bill_type;//	"开票类型",
    private Integer quantity;//"票张数",
    private Integer fullfaretkt;//"全价票张数",
    private Integer ordertkt;//"出票张数",
    private Integer refundtkt;//"退票张数",
    private Long reportdate;//"报告日期",
    private Float real_pay;//"实收实付",
    private Float price;//"成交净价",
    private Float netfare;//"成交净价(不含改签价差)",
    private Float tax;//"民航基金/税",
    private Float oil_fee;//"燃油税",
    private Float service_fee;//"基础服务费",
    private Float insurance_fee;//"保险费",
    private Float bind_amount;//"绑酒优惠券金额",
    private Float change_fee;//"改签费",
    private Float rebook_price_differential;//"改签差价",
    private Float rebook_service_fee;//"改签服务费",
    private Float refund_fee;//"退票费",
    private Float refund_service_fee;//"退票服务费",
    private Float send_ticket_fee;//"送票费",
    private Float ticket_behind_service_fee;//"出票后收服务费",
    private Float rebook_behind_service_fee;//"改签后收服务费",
    private Float refund_behind_service_fee;//"退票后收服务费",
    private Float std_price;//"全价/标准",
    private Float print_price;//"票面价",
    private Float price_rate;//"折扣",
    private Float corp_price;//"最低价",
    private String is_refund;//	"是否退票:T(是)/F(否)",
    private String refund_reson_desc;//	"退票原因",



    private String refund_time;//	"退供应商审核日期",
    private String is_rebook;//	"是否改签:T(是)/F(否)",
    private String rebook_time;//	"改签时间",
    private String rebook_reson_desc;//	"改签原因",



    private Long original_order_id;//"国际机票改签前订单号",
    private String change_flight_no;//	"改签后航班号",
    private String change_takeoff_time;//	"改签后起飞时间",
    private String change_arrival_datetime;//	"改签后到达时间",
    private String productcategory;//	"产品类型分类",
    private String bf_return;//	"是否前返",
    private Float carbons;//"合并碳排量",
    private String agreement_type_name;//	"协议类型",
    private String agreement_type;//	"协议类型code",
    private String contract_type;//	"C（协议），NC（非协议）",
    private Float agreement_rate;//"协航扣率",
    private Integer pre_order_date;//"提前预订天数",
    private String flight_way;//	"航程类型(S:单程,D:往返,M:联程)",
    private String flight_way_desc;//	"航程类型描述",
    private String flight_class;//	"航班类型/产品类别国内国际I:国际，N：国内",
    private String flight_continent;//	"国际航班类型,航程洲际(N:国内,A:州内,I:洲际)",
    private String get_ticket_way_name;//	"配送方式名称",
    private String passenger_uid;//	"乘机人UID",
    private String passenger_name;//	"乘机人",
    private String passenger_name_py;//	"乘机人拼音",
    private String passenger_no;//	"乘机人员工编号",
    private String age_type;//	"机票类型",
    private Integer sequence;//"行程序号",
    private String flight_no;//	"航班号",
    private String low_airlines;//	"廉价航空航班标识(T/F)",
    private String sub_class;//	"子舱位",
    private String flight_status;//	"航段状态",
    private Float flight_time;//"飞行时长",
    private String original_ticket_no;//	"票号-航空公司票号(原始票号",
    private String airline;//	"航空公司二字码",
    private String airline_cn_name;//	"航空公司CN",
    private String ticket_no;//	"航空公司票号",
    private Integer ticket_status;//"机票票号状态",
    private String ticket_warning_level;//	"机票票号风险级别票号风险级别(0.未知1.提示2.预警3.高风险4.正常)",
    private String class_type;//	"Y(经济舱(包含全价经济舱、折扣经济舱、前返经济舱)),S(超级经济舱),F(头等舱),C(公务舱)",
    private String real_class;//	"物理舱位CN",
    private String flight_city_code;//	"航段(城市)三字码",
    private String flight_city;//	"航段",
    private String flight_city2;//	"合并航程/航程",
    private Float tpms;//"合并里程(公里)",
    private Float tpms_en;//"合并里程(英里)",
    private String departure_port_name;//	"起飞机场",
    private String dport_code;//	"起飞机场三字码",
    private String takeoff_time;//	"起飞时间",
    private Integer departure_city_id;//"出发城市ID",
    private String departure_city_name;//	"出发城市名称",
    private String departure_city_code;//	"出发城市三字码,",
    private String departure_continent;//	"出发城市大洲",
    private String departure_country;//	"起飞国家",


    private String departure_province;//	"出发省份",
    private Integer arrival_city_id;//"到达城市ID",
    private String arrival_city_name;//	"到达城市名称",
    private String arrival_city_code;//	"到达城市三字码",
    private String arrival_date_time;//	"到达时间",
    private String arrival_port_name;//	"到达机场",
    private String aport_code;//	"到达机场（三字码）",
    private String arrival_continent;//	"到达城市大洲",
    private String arrival_country;//	"到达国家",
    private String arrival_province;//	"到达省份",
    private String dest_province;//	"目的地省份",
    private String dest_country;//	"目的地国家",
    private String dest_continent;//	"目的地大洲",
    private Integer dest_city;//"目的地城市ID",
    private String dest_city_name;//	"目的地城市名称",
    private String dest_city_code;//	"目的地城市代码",
    private String is_rc;//	"是否有RC，T(是)/F(否)",
    private String class_rid;//	"舱等ReasonCode",
    private String class_rc;//	"舱等RC说明",
    private String agreement_rid;//	"协议RC",
    private String agreement_rc;//	"协议RC说明",
    private String low_rid;//	"低价ReasonCode,",
    private String low_rc;//	"低价RC说明",
    private String pre_rid;//	"未提前预订ReasonCode",
    private String pre_rc;//	"未提前预订RC说明",
    private String time_rid;//	"时间RC",
    private String time_rc;//	"时间RC说明",
    private String low_dtime;//	"最低价航班起飞时间",
    private String userdefined_rid;//	"用户自定义RCcode",
    private String userdefined_rc;//	"用户自定义RC说明",
    private String datachange_lasttime;//	"创建时间",
    private Float refund_itinerary_fee;//"退行程单服务费",
    private String guid;//"主键",
    private String ticketno_refund;//"票号",
    private String rbkid;//"改签id",
    private String audited;//"退票审核状态",
    private String low_flight;//"最低价航班",
    private String low_subclass;//"最低价航班子舱位",
    private Long report_etltime;//'同步job时间'

    private Float servicepackage_fee;
    private String o_currency;//原币种(客户支付币种)
    private Float o_exchangerate;//汇率(客户支付币种汇率)
    private Integer airline_isbudget;//是否廉航
    private String airline_en_name;//航空公司EN
    private String real_class_en;//物理舱位EN
    private String flight_city_en;//航段EN
    private String flight_city2_en;//合并航程/航程EN
    private String departure_port_name_en;//起飞机场EN
    private String departure_city_name_en;//出发城市名称EN
    private String departure_continent_en;//出发城市大洲EN
    private String departure_country_en;//起飞国家EN
    private String departure_province_en;//出发省份EN
    private String arrival_city_name_en;//到达城市名称EN
    private String arrival_port_name_en;//到达机场名称EN
    private String arrival_continent_en;//到达城市大洲EN
    private String arrival_country_en;//到达国家EN
    private String arrival_province_en;//到达省份EN
    private String dest_province_en;//目的地省份EN
    private String dest_country_en;//目的国家EN
    private String dest_continent_en;//目的大洲EN
    private String dest_city_name_en;//目的城市名称EN
    private String class_rc_en;//舱等RC说明EN
    private String agreement_rc_en;//协议RC说明EN
    private String low_rc_en;//低价RC说明EN
    private String pre_rc_en;//为提前预定RC说明EN
    private String time_rc_en;//时间RC说明EN

    private String approvalpasstime;//授权通过时间
    private String actionname;//授权结果
    private String defineflag;//自定义成本中心
    private String defineflag2;//自定义成本中心2
    private String confirmtimepoint;//一次授权时间
    private String confirmtimepoint2;//二次授权时间
    private Float corp_price_adj;//"最低价",
    private String isshared;//是否共享航班
    private String carrierflightno;//"承运航班号",
    private String refund_customer_status;//"退款状态字段（是否已退款完成）,

    private String low_reason_remarks;//"超过低价RC备注"
    private String noorderhtlreason;//"预订机票不预定酒店RC"
    private String noorderhtlreasondesc;//"预订机票不预定酒店RC说明"
    private String noorderhtlreason_en;//"预订机票不预定酒店RC说明英文"

    private Float notselect_insurance_fee;//"用户取消勾选赠险，赠险对应服务费"

    private String lowprice_rc_vv; //  '低价RC自定义备注	',
    private String agreement_rc_vv; // '协议RC自定义备注	',
    private String time_rc_vv; // '时间RC自定义备注	',
    private String distance_rc_vv; // '距离RC自定义备注	',

    private String refundtime; // "退票时间"
    private Float publishprice; // 公布运价
    private Integer rebooktimes; // 改签次数
    private String rebooksubclass; // 改签后子仓位

    private String customerid; // 大客户编号
    private String auditorid; // 一次授权人uid

    private String auditorid2; // 二次授权人uid

    private Float save_amount_3c; // 三方协议节省金额
    private Float save_amount_premium; // 两方协议节省金额

    private String flightrefundtype; // 退票类型
    private String flightrebooktype; // 改签类型

    private String custom_refund_reason_code;// 退票RC
    private String custom_refund_reason_code_desc;// 退票RC说明

    private Float oilfeedifferential; // 改签燃油差
    private Float tax_differential; // 税差

    private String ismixpayment; // 是否混付(混付/非混付)
    private Float settlementaccntamt; // 混付公司支付金额
    private Float settlementpersonamt; // 混付个人支付金额

    private Float saving_price ; // 管控节省

    private String discount_reason_code; // 折扣RC

    private String custom_discount_reason ; // 用户自定义折扣RC

    private Integer countofpassengerflight; // 航段量

    private Float companyfee;// 应收款调整费用

    private String rebook_prepaytypename; //改签支付方式

    private Integer carbon_emission; // 碳排放

    private Integer median_carbon_emission; // 碳排量中位数

    private String std_industry1;//	行业大类,

    private String std_industry2;//	行业小类,
}
