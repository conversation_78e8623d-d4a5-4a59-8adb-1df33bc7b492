package com.corpgovernment.resource.schedule.onlinereport.enums;

/*
 * <AUTHOR>
 *
 * @date 2022/1/11 10:26
 *
 * @Desc 不能改变枚举值的顺序
 */
public enum SubQueryReportBuTypeEnum {
    // 所有
    ALL,
    // 机票
    FLIGHT,
    // 国内机票
    FLIGHT_N,
    // 国际机票
    FLIGHT_I,
    // 协议机票
    FLIGHT_AGREEMENT,
    // 非协议机票
    FLIGHT_NON_AGREEMENT,
    // 酒店
    HOTEL,
    // 国内酒店
    HOTEL_N,
    // 海外酒店
    HOTEL_I,
    // 协议酒店
    HOTEL_AGREEMENT,
    // 非协议酒店
    HOTEL_NON_AGREEMENT,
    // 火车
    TRAIN,
    // 用车
    CAR,
    // 打车
    CAR_TAXI,
    // 国内接送机
    CAR_AIRPORTPICKUP_N,
    // 租车
    CAR_RENTAL,
    // 包车
    CAR_CHARTERED,
    // 国际接送机
    CAR_AIRPORTPICKUP_I,
    // 汽车票
    BUS_TICKETS,
    // 增值
    VASO_ORDER,
    // 国际打车
    CAR_TAXI_I,
    // 国际租车
    CAR_RENTAL_I,
    // 国内火车票
    TRAIN_N,
    // 国际火车票
    TRAIN_I,
    // 心程贝
    WELFARE,
    ;

    public static SubQueryReportBuTypeEnum getByOrdinal(int i) {
        SubQueryReportBuTypeEnum[] subQueryReportBuTypeEnums = SubQueryReportBuTypeEnum.values();
        for (SubQueryReportBuTypeEnum deptStatisticalsEnum : subQueryReportBuTypeEnums) {
            if (deptStatisticalsEnum.ordinal() == i) {
                return deptStatisticalsEnum;
            }
        }
        return null;
    }
}
