package com.corpgovernment.resource.schedule.onlinereport.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.detail
 * @description:
 * @author: md_wang
 * @create: 2022-09-20 13:58
 **/
@Data
public class FltCarBonEmissionTrendDTO {

    @Column(name = "date")
    @Type(value = Types.VARCHAR)
    private String date;
    /**
     * 碳排放
     */
    @Column(name = "carbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbons;
    /**
     * 同环比碳排放
     */
    @Column(name = "chainCarbonsNum")
    @Type(value = Types.DECIMAL)
    private BigDecimal chainCarbonsNum;
    @Column(name = "yoyCarbonsNum")
    @Type(value = Types.DECIMAL)
    private BigDecimal yoyCarbonsNum;

    /**
     * 占比
     */
    @Column(name = "carbonsChain")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbonsChain;
    /**
     * 占比
     */
    @Column(name = "carbonsYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbonsYoy;

    @Column(name = "quantity")
    @Type(value = Types.DECIMAL)
    private BigDecimal quantity;

    @Column(name = "quantityChain")
    @Type(value = Types.DECIMAL)
    private BigDecimal quantityChain;

    @Column(name = "quantityYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal quantityYoy;

    public FltCarBonEmissionTrendDTO() {
    }

    public FltCarBonEmissionTrendDTO(String date) {
        this.date = date;
    }
}
