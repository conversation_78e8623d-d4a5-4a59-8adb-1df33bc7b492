package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.dao.impl
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-10 09:58
 **/
@Repository
@Slf4j
public class OnlineReportTrainDao extends AbstractClickhouseBaseDao {


    private static final String LOG_TITLE = "OnlineReportTrainDao queryOnlineReportTrain";

    private BaseCommonTableQueryCondition queryCondition;

    public OnlineReportTrainDao(BaseCommonTableQueryCondition queryCondition) {
        this.queryCondition = queryCondition;
    }

    /**
     * 查询-火车产线数据
     */
    public <T> List<T> queryOnlineReportTrain(OnlineDetailRequestDto requestDto, Class<T> clazz) throws Exception {

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select report_date as reportDate,");
        // 火车-当期
        // sqlBuilder.append(
        //         "sum(coalesce(afteraftertaketicketfee,0)) as afteraftertaketicketfee,\n");
        sqlBuilder.append(
                "sum(coalesce(afterchangeservicefee,0)) as afterchangeservicefee,\n");
        // sqlBuilder.append(
        //         "sum(coalesce(aftertaketicketfee,0)) as aftertaketicketfee,\n");
        sqlBuilder.append(
                "sum(coalesce(after_service_fee,0)) as afterServiceFee,\n");
        sqlBuilder.append(
                "sum(coalesce(changebalance,0)) as changebalance,\n");
        sqlBuilder.append(
                "sum(coalesce(delay_reschedule_fee,0)) as delayRescheduleFee,\n");
        sqlBuilder.append(
                "sum(coalesce(est_fee_12306,0)) as estFee12306,\n");
        sqlBuilder.append(
                "sum(coalesce(deal_change_service_fee,0)) as dealChangeServiceFee,\n");
        // sqlBuilder.append(
        //         "sum(coalesce(insurance_fee,0)) as insuranceFee,\n");
        // sqlBuilder.append(
        //         "sum(coalesce(paper_ticket_fee,0)) as paperTicketFee,\n");
        sqlBuilder.append(
                "sum(coalesce(refund_ticket_fee,0)) as refundTicketFee,\n");
        sqlBuilder.append(
                "sum(coalesce(service_fee,0)) as serviceFee,\n");
        sqlBuilder.append(
                "sum(coalesce(ticket_price,0)) as ticketPrice,\n");

        // 金额 同比-环比
        sqlBuilder.append(
                " sum(coalesce(real_pay,0)) as realPay,\n");

        // 张数 同比环比
        sqlBuilder.append(
                " sum(coalesce(quantity,0)) as quantityV \n");

        sqlBuilder.append(OrpConstants.FROM);
        ClickHouseTable clickHouseTable = null;

        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            clickHouseTable = ClickHouseTable.ADM_INDEXTRAIN_PRICE_DETAIL_FOREIGN;
        } else {
            if (StringUtils.equalsIgnoreCase(requestDto.getBaseCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL;
            }
        }
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        sqlBuilder.append(OrpConstants.AND);
        sqlBuilder.append(" ( report_date>=? and report_date<=? ) ");
        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 币种查询条件
            }
        }
        // 其他条件
        queryCondition.buildPreSqlCondition(requestDto, sqlBuilder);
        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append("report_date");

        ClickHouseTable finalClickHouseTable = clickHouseTable;
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapRequest(req, statement, finalClickHouseTable), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, "queryOnlineReportTrain");
    }

    private PreparedStatement mapRequest(OnlineDetailRequestDto requestDto, PreparedStatement statement, ClickHouseTable clickHouseTable) {
        AtomicInteger index = new AtomicInteger(OrpConstants.ONE);
        try {

            // 分区
            statement.setString(index.getAndIncrement(), queryPartition(clickHouseTable));
            // 查询日期
            statement.setString(index.getAndIncrement(), requestDto.getDataStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());

            queryCondition.setPreSqlCondition(index, requestDto, statement);
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }
}
