package com.corpgovernment.resource.schedule.onlinereport.saveanalysis.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPSaveProportionDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPotentionSaveProportionDetail;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportProportion;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveProportionRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendLegend;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.saveanalysis.OnlineReportPotentialSaveDisDao;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.OnlineTrendFieldEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportPotentialSaveFlightDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportPotentialSaveHotelDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveProportionDetailRequestDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveProportionRequestDTO;
import com.corpgovernment.resource.schedule.onlinereport.saveanalysis.AbstractSaveAnalysisDis;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 11:02
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class PotentialSaveAnalysisDizBiz extends AbstractSaveAnalysisDis {

    @Autowired
    OnlineReportPotentialSaveDisDao onlineReportPotentialSaveDisDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    
    public List<OnlineReportPotentionSaveProportionDetail> getSaveDisDetail(OnlineReportPSaveProportionDetailRequest request) throws Exception {
        OnlineReportSaveProportionRequest request1 = new OnlineReportSaveProportionRequest();
        request1.setQueryBu(request.getQueryBu());
        request1.setBasecondition(request.getBasecondition());
        request1.setProductType(request.getProductType());
        HashMap<String, OnlineReportProportion> proportion = getSaveDisInner(request1);
        HashMap<String, OnlineReportPotentionSaveProportionDetail> part = getSaveDisDetailPart(request);
        List<OnlineTrendFieldEnum> legends = getSaveDisLegend(request.getQueryBu());
        List<OnlineReportPotentionSaveProportionDetail> results = new ArrayList<>();
        for (OnlineTrendFieldEnum legend : legends) {
            OnlineReportPotentionSaveProportionDetail detail = part.get(legend.getNameKey());
            detail.setTypeNum(proportion.get(legend.getNameKey()).getTypeNum());
            detail.setTimesRate(proportion.get(legend.getNameKey()).getSavePerQuantity());
            detail.setTypeTimes(proportion.get(legend.getNameKey()).getSaveRate().intValue());
            results.add(detail);
        }
        return results;
    }

    
    public List<OnlineReportProportion> getSaveDis(OnlineReportSaveProportionRequest request) throws Exception {
        HashMap<String, OnlineReportProportion> part = getSaveDisInner(request);
        List<OnlineTrendFieldEnum> legends = getSaveDisLegend(request.getQueryBu());
        List<OnlineReportProportion> proportions = new ArrayList<>();
        for (OnlineTrendFieldEnum header : legends) {
            proportions.add(part.get(header.getNameKey()));
        }
        return proportions;
    }

    public HashMap<String, OnlineReportProportion> getSaveDisInner(OnlineReportSaveProportionRequest request) throws Exception {
        OnlineReportSaveProportionRequestDTO requestDTO = map(request, baseQueryConditionMapper);
        HashMap<String, OnlineReportProportion> saveDis = new HashMap<>();
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
            List<OnlineReportPotentialSaveFlightDTO> flightDisDTOS = onlineReportPotentialSaveDisDao.queryFlightProportion(requestDTO);
            if (flightDisDTOS.size() == 1) {
                OnlineReportPotentialSaveFlightDTO dto = flightDisDTOS.get(0);
                BigDecimal sumNum = OrpReportUtils.nonNegative(dto.getFlightRcPotentialSave())
                        .add(OrpReportUtils.nonNegative(dto.getFlightRebookPotentialSave()))
                        .add(OrpReportUtils.nonNegative(dto.getFlightRefundPotentialSave()));
                saveDis.put(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_AMOUNT.getNameKey(),
                        new OnlineReportProportion(
                                OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_AMOUNT.getValueName(),
                                BizUtils.round2(OrpReportUtils.nonNegative(dto.getFlightRcPotentialSave())),
                                OrpReportUtils.divideWithPercent(OrpReportUtils.nonNegative(dto.getFlightRcPotentialSave()), sumNum),
                                dto.getFlightRcTimes(),
                                BizUtils.round2(dto.getFlightRcTimesRate())
                        )
                );
                saveDis.put(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_AMOUNT.getNameKey(),
                        new OnlineReportProportion(
                                OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_AMOUNT.getValueName(),
                                BizUtils.round2(OrpReportUtils.nonNegative(dto.getFlightRebookPotentialSave())),
                                OrpReportUtils.divideWithPercent(OrpReportUtils.nonNegative(dto.getFlightRebookPotentialSave()), sumNum),
                                dto.getFlightRebookTimes(),
                                BizUtils.round2(dto.getFlightRebookTimesRate())
                        )
                );
                saveDis.put(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_AMOUNT.getNameKey(),
                        new OnlineReportProportion(
                                OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_AMOUNT.getValueName(),
                                BizUtils.round2(OrpReportUtils.nonNegative(dto.getFlightRefundPotentialSave())),
                                OrpReportUtils.divideWithPercent(OrpReportUtils.nonNegative(dto.getFlightRefundPotentialSave()), sumNum),
                                dto.getFlightRefundTimes(),
                                BizUtils.round2(dto.getFlightRefundTimesRate())
                        )
                );
            }
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
            List<OnlineReportPotentialSaveHotelDTO> hotelDisDTOS = onlineReportPotentialSaveDisDao.queryHotelProportion(requestDTO);
            if (hotelDisDTOS.size() == 1) {
                OnlineReportPotentialSaveHotelDTO dto = hotelDisDTOS.get(0);
                BigDecimal sumNum = OrpReportUtils.nonNegative(dto.getHotelRcPotentialSave())
                        .add(OrpReportUtils.nonNegative(dto.getHotelCancelPotentialSave()));
                saveDis.put(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_AMOUNT.getNameKey(),
                        new OnlineReportProportion(
                                OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_AMOUNT.getValueName(),
                                BizUtils.round2(OrpReportUtils.nonNegative(dto.getHotelRcPotentialSave())),
                                OrpReportUtils.divideWithPercent(OrpReportUtils.nonNegative(dto.getHotelRcPotentialSave()), sumNum),
                                BizUtils.round2(dto.getHotelRcPotentialTimes()), BizUtils.round2(dto.getHotelRcPotentialTimesRate())
                        )
                );
                saveDis.put(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_AMOUNT.getNameKey(),
                        new OnlineReportProportion(
                                OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_AMOUNT.getValueName(),
                                BizUtils.round2(OrpReportUtils.nonNegative(dto.getHotelCancelPotentialSave())),
                                OrpReportUtils.divideWithPercent(compareZero(dto.getHotelCancelPotentialSave()), sumNum),
                                BizUtils.round2(dto.getHotelCancelPotentialTimes()), BizUtils.round2(dto.getHotelCancelPotentialTimesRate())
                        )
                );
            }
        }
        return saveDis;
    }

    public List<OnlineReportTrendLegend> getSaveDisLegend(OnlineReportSaveProportionRequest request) {
        String lang = request.getLang();
        List<OnlineReportTrendLegend> legends = new ArrayList<>();
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
        }
        return legends;
    }

    public List<OnlineTrendFieldEnum> getSaveDisLegend(QueryReportBuTypeEnum bu) {
        List<OnlineTrendFieldEnum> legends = new ArrayList<>();
        if (bu == QueryReportBuTypeEnum.flight) {
            legends.add(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_AMOUNT);
            legends.add(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_AMOUNT);
            legends.add(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_AMOUNT);
        }
        if (bu == QueryReportBuTypeEnum.hotel) {
            legends.add(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_AMOUNT);
            legends.add(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_AMOUNT);
        }
        return legends;
    }

    public HashMap<String, OnlineReportPotentionSaveProportionDetail> getSaveDisDetailPart(OnlineReportPSaveProportionDetailRequest request) throws Exception {
        String lang = request.getLang();
        OnlineReportSaveProportionDetailRequestDTO requestDTO = map(request, baseQueryConditionMapper);
        HashMap<String, OnlineReportPotentionSaveProportionDetail> saveDisDetail = new HashMap<>();
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
            BaseQueryConditionDTO dto1 = requestDTO.getBaseQueryCondition();
            // 行业
            List<OnlineReportPotentialSaveFlightDTO> flightDisIndustryDTOS = onlineReportPotentialSaveDisDao.queryFlightProportionDetail(dto1.getStartTime(), dto1.getEndTime(),
                    dto1.getStatisticalCaliber(), requestDTO.getProductType(), requestDTO.getIndustries(),
                    dto1.getCompareSameLevel(), dto1.getConsumptionLevel(), dto1.getCompareCorpSameLevel());
            // 全商旅
            requestDTO.setIndustries(null);
            List<OnlineReportPotentialSaveFlightDTO> flightDisDTOS = onlineReportPotentialSaveDisDao.queryFlightProportionDetail(dto1.getStartTime(), dto1.getEndTime(),
                    dto1.getStatisticalCaliber(), requestDTO.getProductType(), requestDTO.getIndustries(),
                    dto1.getCompareSameLevel(), dto1.getConsumptionLevel(), dto1.getCompareCorpSameLevel());

            OnlineReportPotentialSaveFlightDTO corpDto = flightDisDTOS.get(0);
            OnlineReportPotentialSaveFlightDTO dto = new OnlineReportPotentialSaveFlightDTO();
            if (flightDisIndustryDTOS.size() == 1) {
                dto = flightDisIndustryDTOS.get(0);
            }
            // RC
            OnlineReportPotentionSaveProportionDetail var1 = new OnlineReportPotentionSaveProportionDetail();
            var1.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_AMOUNT.getNameKey(), lang));
            var1.setIndustryTimesRate(BizUtils.round2(dto.getFlightRcTimesRate()));
            var1.setCorpTimesRate(BizUtils.round2(corpDto.getFlightRcTimesRate()));
            saveDisDetail.put(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_AMOUNT.getNameKey(), var1);
            // 改签
            OnlineReportPotentionSaveProportionDetail var2 = new OnlineReportPotentionSaveProportionDetail();
            var2.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_AMOUNT.getNameKey(), lang));
            var2.setIndustryTimesRate(BizUtils.round2(dto.getFlightRebookTimesRate()));
            var2.setCorpTimesRate(BizUtils.round2(corpDto.getFlightRebookTimesRate()));
            saveDisDetail.put(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_AMOUNT.getNameKey(), var2);
            // 退
            OnlineReportPotentionSaveProportionDetail var3 = new OnlineReportPotentionSaveProportionDetail();
            var3.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_AMOUNT.getNameKey(), lang));
            var3.setIndustryTimesRate(BizUtils.round2(dto.getFlightRefundTimesRate()));
            var3.setCorpTimesRate(BizUtils.round2(corpDto.getFlightRefundTimesRate()));
            saveDisDetail.put(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_AMOUNT.getNameKey(), var3);
        }

        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
            BaseQueryConditionDTO dto1 = requestDTO.getBaseQueryCondition();
            // 行业数据
            List<OnlineReportPotentialSaveHotelDTO> industryHotelDisDTOS = onlineReportPotentialSaveDisDao.queryHotelProportionDetail(dto1.getStartTime(), dto1.getEndTime(),
                    dto1.getStatisticalCaliber(), requestDTO.getProductType(), requestDTO.getIndustries(),
                    dto1.getCompareSameLevel(), dto1.getConsumptionLevel(), dto1.getCompareCorpSameLevel());
            // 全商旅
            requestDTO.setIndustries(null);
            List<OnlineReportPotentialSaveHotelDTO> hotelDisDTOS = onlineReportPotentialSaveDisDao.queryHotelProportionDetail(dto1.getStartTime(), dto1.getEndTime(),
                    dto1.getStatisticalCaliber(), requestDTO.getProductType(), requestDTO.getIndustries(),
                    dto1.getCompareSameLevel(), dto1.getConsumptionLevel(), dto1.getCompareCorpSameLevel());
            OnlineReportPotentialSaveHotelDTO corpDto = hotelDisDTOS.get(0);
            OnlineReportPotentialSaveHotelDTO dto = new OnlineReportPotentialSaveHotelDTO();
            if (industryHotelDisDTOS.size() == 1) {
                dto = industryHotelDisDTOS.get(0);
            }
            // RC
            OnlineReportPotentionSaveProportionDetail var1 = new OnlineReportPotentionSaveProportionDetail();
            var1.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_AMOUNT.getNameKey(), lang));
            var1.setCorpTimesRate(BizUtils.round2(corpDto.getHotelRcPotentialTimesRate()));
            var1.setIndustryTimesRate(BizUtils.round2(dto.getHotelRcPotentialTimesRate()));
            saveDisDetail.put(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_AMOUNT.getNameKey(), var1);
            // 退
            OnlineReportPotentionSaveProportionDetail var2 = new OnlineReportPotentionSaveProportionDetail();
            var2.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_AMOUNT.getNameKey(), lang));
            var2.setCorpTimesRate(BizUtils.round2(corpDto.getHotelCancelPotentialTimesRate()));
            var2.setIndustryTimesRate(BizUtils.round2(dto.getHotelCancelPotentialTimesRate()));
            saveDisDetail.put(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_AMOUNT.getNameKey(), var2);
        }
        return saveDisDetail;
    }

}
