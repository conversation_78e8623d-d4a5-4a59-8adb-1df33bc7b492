package com.corpgovernment.resource.schedule.onlinereport.enums;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-03 19:22
 **/
public enum DateFormatEnum {
    /**
     * data 格式
     */
    DATA_MONTH_FORMAT("yyyy-MM", OrpConstants.EMPTY),
    /**
     * data 格式
     */
    DATE_FORMAT("yyyy-MM-dd", OrpConstants.EMPTY),
    /**
     * data 格式
     */
    DATE_FORMAT_STR("yyyyMMdd", OrpConstants.EMPTY),
    /**
     * data 格式
     */
    DATE_FORMAT_SIMPLE("yyyy-MM-dd HH:mm:ss", OrpConstants.EMPTY),
    /**
     * DATE_FORMAT_SIMPLE_1S
     */
    DATE_FORMAT_SIMPLE_1S("yyyy-MM-dd HH:mm:ss.S", OrpConstants.EMPTY),
    /**
     * DATE_FORMAT_SIMPLE_2S
     */
    DATE_FORMAT_SIMPLE_2S("yyyy-MM-dd HH:mm:ss.SS", OrpConstants.EMPTY),
    /**
     * DATE_FORMAT_SIMPLE_3S
     */
    DATE_FORMAT_SIMPLE_3S("yyyy-MM-dd HH:mm:ss.SSS", OrpConstants.EMPTY),
    ;

    private String format;
    private String msg;

    public static DateFormatEnum getDateFormat(String date) {
        DateFormatEnum[] values = DateFormatEnum.values();
        for (DateFormatEnum dateFormatEnum : values) {
            if (dateFormatEnum.getFormat().equalsIgnoreCase(date)) {
                return dateFormatEnum;
            }
        }
        return DATE_FORMAT;
    }

    DateFormatEnum(String format, String msg) {
        this.format = format;
        this.msg = msg;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
