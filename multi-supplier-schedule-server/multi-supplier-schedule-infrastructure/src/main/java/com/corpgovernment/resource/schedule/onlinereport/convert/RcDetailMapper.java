package com.corpgovernment.resource.schedule.onlinereport.convert;

import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcDetailDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcViewReasonDetail;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2021/11/8 16:26
 * @Desc
 */
@Mapper(componentModel = "spring")
public interface RcDetailMapper {

    RcDetailMapper INSTANCE = Mappers.getMapper(RcDetailMapper.class);

    @Mapping(target = "rcPercent", expression = "java(calPercent(rcDetail.getRcTimes(),totalCount))")
    RcViewReasonDetail toBO(RcDetailDTO rcDetail, @Context Integer totalCount);

    List<RcViewReasonDetail> toBOs(List<RcDetailDTO> list, @Context Integer totalCount);

    @Named("calPercent")
    default double calPercent(Integer rcTimes, Integer totalCount) {
        return OrpReportUtils.divideWithPercent(new BigDecimal(rcTimes)
                , new BigDecimal(Optional.ofNullable(totalCount).orElse(0))).doubleValue();
    }
}
