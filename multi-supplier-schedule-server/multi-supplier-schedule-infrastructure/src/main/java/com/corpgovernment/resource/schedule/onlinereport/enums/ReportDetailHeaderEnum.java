package com.corpgovernment.resource.schedule.onlinereport.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-03 19:22
 **/
public enum ReportDetailHeaderEnum {
    /**
     * 概览
     */
    OVERVIEW_AMOUNT_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewHeader()),
    OVERVIEW_TICKET_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewHeader()),

    /**
     * flight header
     */
    FLIGHT_ALL_HEADER(OnlineReportBuEnum.FLIGHT, mapFlightHeader()),
    FLIGHT_DOMESTIC_HEADER(OnlineReportBuEnum.FLIGHT, mapFlightHeader()),
    FLIGHT_INTERNATIONAL_HEADER(OnlineReportBuEnum.FLIGHT, mapFlightHeader()),
    FLIGHT_AGREEMENT_HEADER(OnlineReportBuEnum.FLIGHT, mapFlightHeader()),
    FLIGHT_NOT_AGREEMENT_HEADER(OnlineReportBuEnum.FLIGHT, mapFlightHeader()),
    FLIGHT_INFO_TICKET_HEADER(OnlineReportBuEnum.FLIGHT, mapFlightHotelInfoHeader()),
    FLIGHT_INFO_AMOUNT_HEADER(OnlineReportBuEnum.FLIGHT, mapFlightHotelInfoHeader()),

    /**
     * hotel header
     */
    HOTEL_ALL_HEADER(OnlineReportBuEnum.HOTEL, mapHotelHeader()),
    HOTEL_DOMESTIC_HEADER(OnlineReportBuEnum.HOTEL, mapHotelHeader()),
    HOTEL_INTERNATIONAL_HEADER(OnlineReportBuEnum.HOTEL, mapHotelHeader()),
    HOTEL_AGREEMENT_HEADER(OnlineReportBuEnum.HOTEL, mapHotelHeader()),
    HOTEL_NOT_AGREEMENT_HEADER(OnlineReportBuEnum.HOTEL, mapHotelHeader()),
    HOTEL_INFO_TICKET_HEADER(OnlineReportBuEnum.HOTEL, mapFlightHotelInfoHeader()),
    HOTEL_INFO_AMOUNT_HEADER(OnlineReportBuEnum.HOTEL, mapFlightHotelInfoHeader()),

    /**
     * 火车 header
     */
    TRAIN_ALL_HEADER(OnlineReportBuEnum.TRAIN, mapTrainHeader()),
    TRAIN_DOMESTIC_HEADER(OnlineReportBuEnum.TRAIN, mapTrainHeader()),
    TRAIN_INTERNATIONAL_HEADER(OnlineReportBuEnum.TRAIN, mapTrainHeader()),
    TRAIN_INFO_TICKET_HEADER(OnlineReportBuEnum.TRAIN, mapTrainInfoHeader()),
    TRAIN_INFO_AMOUNT_HEADER(OnlineReportBuEnum.TRAIN, mapTrainInfoHeader()),

    /**
     * 用车 header
     */
    CAR_ALL_HEADER(OnlineReportBuEnum.CAR, mapCarHeader()),
    CAR_TAKE_TAXI_HEADER(OnlineReportBuEnum.CAR, mapCarHeader()),
    CAR_TAKE_TAXI_HEADER_INTER(OnlineReportBuEnum.CAR, mapCarHeader()),
    CAR_AIRPORT_PICK_UP_HEADER(OnlineReportBuEnum.CAR, mapCarHeader()),
    CAR_AIRPORT_PICK_UP_HEADER_INTER(OnlineReportBuEnum.CAR, mapCarHeader()),
    CAR_RENTAL_CAR_HEADER(OnlineReportBuEnum.CAR, mapCarHeader()),
    CAR_CHARTERED_CAR_HEADER(OnlineReportBuEnum.CAR, mapCarHeader()),
    CAR_INFO_AMOUNT_HEADER(OnlineReportBuEnum.CAR, mapCarInfoHeader()),
    CAR_INFO_TICKET_HEADER(OnlineReportBuEnum.CAR, mapCarInfoHeader()),
    CAR_RENTAL_CAR_HEADER_INTER(OnlineReportBuEnum.CAR, mapCarHeader());

    private OnlineReportBuEnum buEnum;

    private List<OnlineDetailFieldEnum> headerList;

    /**
     * 概览header
     */
    public static List<OnlineDetailFieldEnum> mapOverviewHeader() {
        List<OnlineDetailFieldEnum> list = Lists.newArrayList();
        // flight
        list.add(OnlineDetailFieldEnum.DIMENSION);
        list.add(OnlineDetailFieldEnum.FLIGHT_V);
        list.add(OnlineDetailFieldEnum.FLIGHT_YOY);
        list.add(OnlineDetailFieldEnum.FLIGHT_MOM);
        // hotel
        list.add(OnlineDetailFieldEnum.HOTEL_V);
        list.add(OnlineDetailFieldEnum.HOTEL_YOY);
        list.add(OnlineDetailFieldEnum.HOTEL_MOM);
        // train
        list.add(OnlineDetailFieldEnum.TRAIN_V);
        list.add(OnlineDetailFieldEnum.TRAIN_YOY);
        list.add(OnlineDetailFieldEnum.TRAIN_MOM);
        // car
        list.add(OnlineDetailFieldEnum.CAR_V);
        list.add(OnlineDetailFieldEnum.CAR_YOY);
        list.add(OnlineDetailFieldEnum.CAR_MOM);
        // bus
        list.add(OnlineDetailFieldEnum.BUS_V);
        list.add(OnlineDetailFieldEnum.BUS_YOY);
        list.add(OnlineDetailFieldEnum.BUS_MOM);
        // 增值
        list.add(OnlineDetailFieldEnum.ADD_V);
        list.add(OnlineDetailFieldEnum.ADD_YOY);
        list.add(OnlineDetailFieldEnum.ADD_MOM);
        // 总计
        list.add(OnlineDetailFieldEnum.TOTAL_V);
        list.add(OnlineDetailFieldEnum.TOTAL_YOY);
        list.add(OnlineDetailFieldEnum.TOTAL_MOM);
        return list;
    }


    /**
     * 概览header(日本站)
     */
    public static List<OnlineDetailFieldEnum> mapJPOverviewHeader() {
        List<OnlineDetailFieldEnum> list = Lists.newArrayList();
        // flight
        list.add(OnlineDetailFieldEnum.DIMENSION);
        list.add(OnlineDetailFieldEnum.FLIGHT_V);
        list.add(OnlineDetailFieldEnum.FLIGHT_YOY);
        list.add(OnlineDetailFieldEnum.FLIGHT_MOM);
        // hotel
        list.add(OnlineDetailFieldEnum.HOTEL_V);
        list.add(OnlineDetailFieldEnum.HOTEL_YOY);
        list.add(OnlineDetailFieldEnum.HOTEL_MOM);
        // 总计
        list.add(OnlineDetailFieldEnum.TOTAL_V);
        list.add(OnlineDetailFieldEnum.TOTAL_YOY);
        list.add(OnlineDetailFieldEnum.TOTAL_MOM);
        return list;
    }

    public static Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>>
    getHeaderByType(DetailReportTypeEnum reportType, OnlineReportBuEnum buEnum) {
        Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> returnMap = Maps.newHashMap();
        if (Objects.isNull(reportType) || Objects.isNull(buEnum)) {
            return returnMap;
        }
        if (DetailReportTypeEnum.DETAIL_AMOUNT_REPORT.equals(reportType)) {
            switch (buEnum) {
                case FLIGHT:
                    returnMap.put(FLIGHT_ALL_HEADER, FLIGHT_ALL_HEADER.getHeaderList());
                    returnMap.put(FLIGHT_DOMESTIC_HEADER, FLIGHT_DOMESTIC_HEADER.getHeaderList());
                    returnMap.put(FLIGHT_INTERNATIONAL_HEADER, FLIGHT_INTERNATIONAL_HEADER.getHeaderList());
                    returnMap.put(FLIGHT_AGREEMENT_HEADER, FLIGHT_AGREEMENT_HEADER.getHeaderList());
                    returnMap.put(FLIGHT_NOT_AGREEMENT_HEADER, FLIGHT_NOT_AGREEMENT_HEADER.getHeaderList());
                    returnMap.put(FLIGHT_INFO_AMOUNT_HEADER, FLIGHT_INFO_AMOUNT_HEADER.getHeaderList());
                    break;
                case HOTEL:
                    returnMap.put(HOTEL_ALL_HEADER, HOTEL_ALL_HEADER.getHeaderList());
                    returnMap.put(HOTEL_DOMESTIC_HEADER, HOTEL_INTERNATIONAL_HEADER.getHeaderList());
                    returnMap.put(HOTEL_INTERNATIONAL_HEADER, HOTEL_INTERNATIONAL_HEADER.getHeaderList());
                    returnMap.put(HOTEL_AGREEMENT_HEADER, HOTEL_AGREEMENT_HEADER.getHeaderList());
                    returnMap.put(HOTEL_NOT_AGREEMENT_HEADER, HOTEL_NOT_AGREEMENT_HEADER.getHeaderList());
                    returnMap.put(HOTEL_INFO_AMOUNT_HEADER, HOTEL_INFO_AMOUNT_HEADER.getHeaderList());
                    break;
                case TRAIN:
                    returnMap.put(TRAIN_ALL_HEADER, TRAIN_ALL_HEADER.getHeaderList());
                    returnMap.put(TRAIN_DOMESTIC_HEADER, TRAIN_DOMESTIC_HEADER.getHeaderList());
                    returnMap.put(TRAIN_INTERNATIONAL_HEADER, TRAIN_INTERNATIONAL_HEADER.getHeaderList());
                    returnMap.put(TRAIN_INFO_AMOUNT_HEADER, TRAIN_INFO_AMOUNT_HEADER.getHeaderList());
                    break;
                case CAR:
                    returnMap.put(CAR_ALL_HEADER, CAR_ALL_HEADER.getHeaderList());
                    returnMap.put(CAR_TAKE_TAXI_HEADER, CAR_TAKE_TAXI_HEADER.getHeaderList());
                    returnMap.put(CAR_TAKE_TAXI_HEADER_INTER, CAR_TAKE_TAXI_HEADER_INTER.getHeaderList());
                    returnMap.put(CAR_AIRPORT_PICK_UP_HEADER, CAR_AIRPORT_PICK_UP_HEADER.getHeaderList());
                    returnMap.put(CAR_AIRPORT_PICK_UP_HEADER_INTER, CAR_AIRPORT_PICK_UP_HEADER_INTER.getHeaderList());
                    returnMap.put(CAR_RENTAL_CAR_HEADER, CAR_RENTAL_CAR_HEADER.getHeaderList());
                    returnMap.put(CAR_RENTAL_CAR_HEADER_INTER, CAR_RENTAL_CAR_HEADER.getHeaderList());
                    returnMap.put(CAR_CHARTERED_CAR_HEADER, CAR_CHARTERED_CAR_HEADER.getHeaderList());
                    returnMap.put(CAR_INFO_AMOUNT_HEADER, CAR_INFO_AMOUNT_HEADER.getHeaderList());
                    break;
                default:
            }
        } else if (DetailReportTypeEnum.DETAIL_TICKET_REPORT.equals(reportType)) {
            switch (buEnum) {
                case FLIGHT:
                    returnMap.put(FLIGHT_INFO_TICKET_HEADER, FLIGHT_INFO_TICKET_HEADER.getHeaderList());
                    break;
                case HOTEL:
                    returnMap.put(HOTEL_INFO_TICKET_HEADER, HOTEL_INFO_TICKET_HEADER.getHeaderList());
                    break;
                case TRAIN:
                    returnMap.put(TRAIN_INFO_TICKET_HEADER, TRAIN_INFO_TICKET_HEADER.getHeaderList());
                    break;
                case CAR:
                    returnMap.put(CAR_INFO_TICKET_HEADER, CAR_INFO_TICKET_HEADER.getHeaderList());
                    break;
                default:
            }
        }
        return returnMap;

    }

    public static Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>>
    getJPHeaderByType(DetailReportTypeEnum reportType, OnlineReportBuEnum buEnum) {
        Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> returnMap = Maps.newHashMap();
        if (Objects.isNull(reportType) || Objects.isNull(buEnum)) {
            return returnMap;
        }
        if (DetailReportTypeEnum.DETAIL_AMOUNT_REPORT.equals(reportType)) {
            switch (buEnum) {
                case FLIGHT:
                    returnMap.put(FLIGHT_ALL_HEADER, ReportDetailHeaderEnum.mapJPFlightHeader());
                    returnMap.put(FLIGHT_AGREEMENT_HEADER, ReportDetailHeaderEnum.mapJPFlightHeader());
                    returnMap.put(FLIGHT_NOT_AGREEMENT_HEADER, ReportDetailHeaderEnum.mapJPFlightHeader());
                    returnMap.put(FLIGHT_INFO_AMOUNT_HEADER, ReportDetailHeaderEnum.mapJPFlightHotelInfoHeader());
                    break;
                case HOTEL:
                    returnMap.put(HOTEL_ALL_HEADER, HOTEL_ALL_HEADER.getHeaderList());
                    returnMap.put(HOTEL_AGREEMENT_HEADER, HOTEL_AGREEMENT_HEADER.getHeaderList());
                    returnMap.put(HOTEL_NOT_AGREEMENT_HEADER, HOTEL_NOT_AGREEMENT_HEADER.getHeaderList());
                    returnMap.put(HOTEL_INFO_AMOUNT_HEADER, ReportDetailHeaderEnum.mapJPFlightHotelInfoHeader());
                    break;
                default:
            }
        } else if (DetailReportTypeEnum.DETAIL_TICKET_REPORT.equals(reportType)) {
            switch (buEnum) {
                case FLIGHT:
                    returnMap.put(FLIGHT_INFO_TICKET_HEADER, ReportDetailHeaderEnum.mapJPFlightHotelInfoHeader());
                    break;
                case HOTEL:
                    returnMap.put(HOTEL_INFO_TICKET_HEADER, ReportDetailHeaderEnum.mapJPFlightHotelInfoHeader());
                    break;
                default:
            }
        }
        return returnMap;

    }

    /**
     * 机票-整体消费金额
     *
     * @return
     */
    private static List<OnlineDetailFieldEnum> mapFlightHeader() {
        List<OnlineDetailFieldEnum> flightHeader = Lists.newArrayList();
        flightHeader.add(OnlineDetailFieldEnum.DIMENSION);
        flightHeader.add(OnlineDetailFieldEnum.NETFARE);
        flightHeader.add(OnlineDetailFieldEnum.TAX);
        flightHeader.add(OnlineDetailFieldEnum.OIL_FEE);
        flightHeader.add(OnlineDetailFieldEnum.SERVICE_FEE);
//        flightHeader.add(OnlineDetailFieldEnum.INSURANCE_FEE);
//        flightHeader.add(OnlineDetailFieldEnum.SEND_TICKET_FEE);
//        flightHeader.add(OnlineDetailFieldEnum.SERVICEPACKAGE_FEE);
//        flightHeader.add(OnlineDetailFieldEnum.BIND_AMOUNT);
        flightHeader.add(OnlineDetailFieldEnum.CHANGE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.REBOOK_PRICE_DIFFERENTIAL);
        flightHeader.add(OnlineDetailFieldEnum.OILFEEDIFFERENTIAL);
        flightHeader.add(OnlineDetailFieldEnum.TAX_DIFFERENTIAL);
        flightHeader.add(OnlineDetailFieldEnum.REBOOK_SERVICE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.REFUND_FEE);
        flightHeader.add(OnlineDetailFieldEnum.REFUND_SERVICE_FEE);
//        flightHeader.add(OnlineDetailFieldEnum.REFUND_ITINERARY_FEE);
        flightHeader.add(OnlineDetailFieldEnum.TICKET_BEHIND_SERVICE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.REBOOK_BEHIND_SERVICE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.REFUND_BEHIND_SERVICE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.TOTAL_V);
        return flightHeader;
    }

    /**
     * 机票-整体消费金额(日本站)
     *
     * @return
     */
    private static List<OnlineDetailFieldEnum> mapJPFlightHeader() {
        List<OnlineDetailFieldEnum> flightHeader = Lists.newArrayList();
        flightHeader.add(OnlineDetailFieldEnum.DIMENSION);
        flightHeader.add(OnlineDetailFieldEnum.NETFARE);
        flightHeader.add(OnlineDetailFieldEnum.TAX);
        flightHeader.add(OnlineDetailFieldEnum.OIL_FEE);
        flightHeader.add(OnlineDetailFieldEnum.SERVICE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.INSURANCE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.SEND_TICKET_FEE);
        flightHeader.add(OnlineDetailFieldEnum.CHANGE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.REBOOK_PRICE_DIFFERENTIAL);
        flightHeader.add(OnlineDetailFieldEnum.REBOOK_SERVICE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.REFUND_FEE);
        flightHeader.add(OnlineDetailFieldEnum.REFUND_SERVICE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.TICKET_BEHIND_SERVICE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.REBOOK_BEHIND_SERVICE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.REFUND_BEHIND_SERVICE_FEE);
        flightHeader.add(OnlineDetailFieldEnum.TOTAL_V);
        return flightHeader;
    }

    /**
     * 酒店-整体header
     *
     * @return
     */
    private static List<OnlineDetailFieldEnum> mapHotelHeader() {
        List<OnlineDetailFieldEnum> hotelHeader = Lists.newArrayList();
        hotelHeader.add(OnlineDetailFieldEnum.DIMENSION);
        hotelHeader.add(OnlineDetailFieldEnum.ROOM_PRICE);
        hotelHeader.add(OnlineDetailFieldEnum.SERVICE_FEE);
        hotelHeader.add(OnlineDetailFieldEnum.HOTEL_POST_SERVICE_FEE);
        // hotelHeader.add(OnlineDetailFieldEnum.couponAmount);
        hotelHeader.add(OnlineDetailFieldEnum.CARD_PAY_SERVICE_FEE);
        hotelHeader.add(OnlineDetailFieldEnum.TAX_AMOUNT);
        hotelHeader.add(OnlineDetailFieldEnum.TOTAL_V);
        return hotelHeader;
    }

    /**
     * 火车-整体header
     *
     * @return
     */
    private static List<OnlineDetailFieldEnum> mapTrainHeader() {
        List<OnlineDetailFieldEnum> trainHeader = Lists.newArrayList();
        // 月份
        trainHeader.add(OnlineDetailFieldEnum.DIMENSION);
        // 原始出票金额(票价)
        trainHeader.add(OnlineDetailFieldEnum.TICKET_PRICE);
        // 基础服务费
        trainHeader.add(OnlineDetailFieldEnum.SERVICE_FEE);
        // 改签时票面差价
        trainHeader.add(OnlineDetailFieldEnum.CHANGEBALANCE);
        // 保险费
//        trainHeader.add(OnlineDetailFieldEnum.INSURANCE_FEE);
        // 配送费
//        trainHeader.add(OnlineDetailFieldEnum.SEND_TICKET_FEE);
        // 抢票费
//        trainHeader.add(OnlineDetailFieldEnum.GRAB_SERVICE_FEE);
        // 代购服务费
        trainHeader.add(OnlineDetailFieldEnum.PURCHASE_SERVICE_FEE);
        // 预估改签手续费12306(发生改签时才收取)
        trainHeader.add(OnlineDetailFieldEnum.EST_FEE_12306);
        // 纸质出票费
//        trainHeader.add(OnlineDetailFieldEnum.PAPER_TICKET_FEE);
        // 出票服务费
        trainHeader.add(OnlineDetailFieldEnum.ISSUE_TICKET_FEE);
        // 退票时退补给客户的金额
        trainHeader.add(OnlineDetailFieldEnum.REFUND_TICKET_FEE);
        // 退款(改签差额)
        trainHeader.add(OnlineDetailFieldEnum.CHANGE_BALANCE_REFUND_AMOUNT);
        // 代取票人工费
//        trainHeader.add(OnlineDetailFieldEnum.AFTERTAKETICKETFEE);
        // 改签商旅管理服务费
        trainHeader.add(OnlineDetailFieldEnum.DEAL_CHANGE_SERVICE_FEE);
        // 后收商旅管理服务费
        trainHeader.add(OnlineDetailFieldEnum.AFTER_SERVICE_FEE);
        // 后收改签商旅管理服务费
        trainHeader.add(OnlineDetailFieldEnum.AFTERCHANGESERVICEFEE);
        // 后收代取人工费
//        trainHeader.add(OnlineDetailFieldEnum.AFTERAFTERTAKETICKETFEE);
        // 总计
        trainHeader.add(OnlineDetailFieldEnum.TOTAL_V);
        return trainHeader;
    }

    /**
     * 用车-header
     *
     * @return
     */
    private static List<OnlineDetailFieldEnum> mapCarHeader() {
        List<OnlineDetailFieldEnum> hotelHeader = Lists.newArrayList();
        hotelHeader.add(OnlineDetailFieldEnum.DIMENSION);
        hotelHeader.add(OnlineDetailFieldEnum.BASIC_FEE);
        hotelHeader.add(OnlineDetailFieldEnum.SERVICE_FEE);
//        hotelHeader.add(OnlineDetailFieldEnum.REFUND_AMOUNT);
        hotelHeader.add(OnlineDetailFieldEnum.TOTAL_V);
        return hotelHeader;
    }

    /**
     * 机票/酒店-(金额/票张)明细header
     *
     * @return
     */
    private static List<OnlineDetailFieldEnum> mapFlightHotelInfoHeader() {
        List<OnlineDetailFieldEnum> hotelInfoHeader = Lists.newArrayList();
        // 月份
        hotelInfoHeader.add(OnlineDetailFieldEnum.DIMENSION);
        // 协议&非协议
        hotelInfoHeader.add(OnlineDetailFieldEnum.AGREEMENT_V);
        hotelInfoHeader.add(OnlineDetailFieldEnum.AGREEMENT_YOY);
        hotelInfoHeader.add(OnlineDetailFieldEnum.AGREEMENT_MOM);
        hotelInfoHeader.add(OnlineDetailFieldEnum.UN_AGREEMENT_V);
        hotelInfoHeader.add(OnlineDetailFieldEnum.UN_AGREEMENT_YOY);
        hotelInfoHeader.add(OnlineDetailFieldEnum.UN_AGREEMENT_MOM);
        // 国内&国际
        hotelInfoHeader.add(OnlineDetailFieldEnum.DOMESTIC_V);
        hotelInfoHeader.add(OnlineDetailFieldEnum.DOMESTIC_YOY);
        hotelInfoHeader.add(OnlineDetailFieldEnum.DOMESTIC_MOM);
        hotelInfoHeader.add(OnlineDetailFieldEnum.INTERNATIONAL_V);
        hotelInfoHeader.add(OnlineDetailFieldEnum.INTERNATIONAL_YOY);
        hotelInfoHeader.add(OnlineDetailFieldEnum.INTERNATIONAL_MOM);

        // 总计
        hotelInfoHeader.add(OnlineDetailFieldEnum.TOTAL_V);
        hotelInfoHeader.add(OnlineDetailFieldEnum.TOTAL_YOY);
        hotelInfoHeader.add(OnlineDetailFieldEnum.TOTAL_MOM);
        return hotelInfoHeader;
    }

    /**
     * 机票/酒店-(金额/票张)明细header(日本站)
     *
     * @return
     */
    private static List<OnlineDetailFieldEnum> mapJPFlightHotelInfoHeader() {
        List<OnlineDetailFieldEnum> hotelInfoHeader = Lists.newArrayList();
        // 月份
        hotelInfoHeader.add(OnlineDetailFieldEnum.DIMENSION);
        // 协议&非协议
        hotelInfoHeader.add(OnlineDetailFieldEnum.AGREEMENT_V);
        hotelInfoHeader.add(OnlineDetailFieldEnum.AGREEMENT_YOY);
        hotelInfoHeader.add(OnlineDetailFieldEnum.AGREEMENT_MOM);
        hotelInfoHeader.add(OnlineDetailFieldEnum.UN_AGREEMENT_V);
        hotelInfoHeader.add(OnlineDetailFieldEnum.UN_AGREEMENT_YOY);
        hotelInfoHeader.add(OnlineDetailFieldEnum.UN_AGREEMENT_MOM);
        // 总计
        hotelInfoHeader.add(OnlineDetailFieldEnum.TOTAL_V);
        hotelInfoHeader.add(OnlineDetailFieldEnum.TOTAL_YOY);
        hotelInfoHeader.add(OnlineDetailFieldEnum.TOTAL_MOM);
        return hotelInfoHeader;
    }

    /**
     * 火车-明细header
     */
    private static List<OnlineDetailFieldEnum> mapTrainInfoHeader() {
        List<OnlineDetailFieldEnum> trainInfoHeader = Lists.newArrayList();
        // 月份
        trainInfoHeader.add(OnlineDetailFieldEnum.DIMENSION);
        // 总计
        trainInfoHeader.add(OnlineDetailFieldEnum.TOTAL_V);
        trainInfoHeader.add(OnlineDetailFieldEnum.TOTAL_YOY);
        trainInfoHeader.add(OnlineDetailFieldEnum.TOTAL_MOM);
        // 国内&国际
        trainInfoHeader.add(OnlineDetailFieldEnum.DOMESTIC_V);
        trainInfoHeader.add(OnlineDetailFieldEnum.DOMESTIC_YOY);
        trainInfoHeader.add(OnlineDetailFieldEnum.DOMESTIC_MOM);
        trainInfoHeader.add(OnlineDetailFieldEnum.INTERNATIONAL_V);
        trainInfoHeader.add(OnlineDetailFieldEnum.INTERNATIONAL_YOY);
        trainInfoHeader.add(OnlineDetailFieldEnum.INTERNATIONAL_MOM);
        return trainInfoHeader;
    }

    /**
     * 用车-明细header
     *
     * @return
     */
    private static List<OnlineDetailFieldEnum> mapCarInfoHeader() {
        List<OnlineDetailFieldEnum> carInfoHeader = Lists.newArrayList();
        // 月份
        carInfoHeader.add(OnlineDetailFieldEnum.DIMENSION);
        // 打车
        carInfoHeader.add(OnlineDetailFieldEnum.TAKE_CAR_V);
        carInfoHeader.add(OnlineDetailFieldEnum.TAKE_CAR_YOY);
        carInfoHeader.add(OnlineDetailFieldEnum.TAKE_CAR_MOM);
        // 国际打车
        carInfoHeader.add(OnlineDetailFieldEnum.TAKE_CAR_V_INTER);
        carInfoHeader.add(OnlineDetailFieldEnum.TAKE_CAR_YOY_INTER);
        carInfoHeader.add(OnlineDetailFieldEnum.TAKE_CAR_MOM_INTER);
        // 接送机
        carInfoHeader.add(OnlineDetailFieldEnum.TRANSFER_V);
        carInfoHeader.add(OnlineDetailFieldEnum.TRANSFER_YOY);
        carInfoHeader.add(OnlineDetailFieldEnum.TRANSFER_MOM);
        // 国际接送机
        carInfoHeader.add(OnlineDetailFieldEnum.TRANSFER_V_INTER);
        carInfoHeader.add(OnlineDetailFieldEnum.TRANSFER_YOY_INTER);
        carInfoHeader.add(OnlineDetailFieldEnum.TRANSFER_MOM_INTER);
        // 租车
        carInfoHeader.add(OnlineDetailFieldEnum.RENTAL_CAR_V);
        carInfoHeader.add(OnlineDetailFieldEnum.RENTAL_CAR_YOY);
        carInfoHeader.add(OnlineDetailFieldEnum.RENTAL_CAR_MOM);
        // 国际租车
        carInfoHeader.add(OnlineDetailFieldEnum.RENTAL_CAR_V_INTER);
        carInfoHeader.add(OnlineDetailFieldEnum.RENTAL_CAR_YOY_INTER);
        carInfoHeader.add(OnlineDetailFieldEnum.RENTAL_CAR_MOM_INTER);
        // 包车
        carInfoHeader.add(OnlineDetailFieldEnum.CHARTER_CAR_V);
        carInfoHeader.add(OnlineDetailFieldEnum.CHARTER_CAR_YOY);
        carInfoHeader.add(OnlineDetailFieldEnum.CHARTER_CAR_MOM);
        // 总计
        carInfoHeader.add(OnlineDetailFieldEnum.TOTAL_V);
        carInfoHeader.add(OnlineDetailFieldEnum.TOTAL_YOY);
        carInfoHeader.add(OnlineDetailFieldEnum.TOTAL_MOM);
        return carInfoHeader;
    }

    ReportDetailHeaderEnum(OnlineReportBuEnum buEnum, List<OnlineDetailFieldEnum> headerList) {
        this.buEnum = buEnum;
        this.headerList = headerList;
    }

    public OnlineReportBuEnum getBuEnum() {
        return buEnum;
    }

    public void setBuEnum(OnlineReportBuEnum buEnum) {
        this.buEnum = buEnum;
    }

    public List<OnlineDetailFieldEnum> getHeaderList() {
        return headerList;
    }

    public void setHeaderList(List<OnlineDetailFieldEnum> headerList) {
        this.headerList = headerList;
    }
}
