package com.corpgovernment.resource.schedule.onlinereport.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TrendDimensionTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/8 19:40
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@NoArgsConstructor
public class OnlineTrendRequestDto {

    String startTime;
    String endTime;
    String chainStartTime;
    String chainEndTime;
    String yoyStartTime;
    String yoyEndTime;

    String bu;
    Map<String, String> extParams;

    // 月或季度或半年
    String dateDimension;
    // 同比去年 -12 | 同比前年 -24
    int yoyDuration;
    String lang;
    BaseQueryConditionDTO baseQueryCondition;

    /**
     * 累计，单程
     */
    TrendDimensionTypeEnum dimensionType;

    String queryType;

    String productType;
    String method;
}
