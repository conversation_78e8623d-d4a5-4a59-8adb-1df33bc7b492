package com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 11:02
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class OnlineReportSaveTrendV2DTO {

    @Column(name = "date")
    @Type(value = Types.VARCHAR)
    private String date;

    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;

    @Column(name = "cSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal cSave;

    @Column(name = "netfare3c")
    @Type(value = Types.DECIMAL)
    private BigDecimal netfare3c;

    @Column(name = "quantity3c")
    @Type(value = Types.INTEGER)
    private Integer quantity3c;

    @Column(name = "premiumSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal premiumSave;

    @Column(name = "netfarePremium")
    @Type(value = Types.DECIMAL)
    private BigDecimal netfarePremium;

    @Column(name = "quantityPremium")
    @Type(value = Types.INTEGER)
    private Integer quantityPremium;

    @Column(name = "controlSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal controlSave;

    @Column(name = "controlNetfare")
    @Type(value = Types.DECIMAL)
    private BigDecimal controlNetfare;

    @Column(name = "controlQuantity")
    @Type(value = Types.INTEGER)
    private Integer controlQuantity;

    @Column(name = "rcAndResponseCnt")
    @Type(value = Types.INTEGER)
    private Integer rcAndResponseCnt;

    @Column(name = "controlResponseCnt")
    @Type(value = Types.INTEGER)
    private Integer controlResponseCnt;
}
