package com.corpgovernment.resource.schedule.onlinereport.factory;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDetailRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.service.factory.test
 * @description:
 * @author: md_wang
 * @create: 2021-11-08 10:35
 **/
@Service
public class DetailFactory extends AbstractFactory {

    @Autowired
    @Qualifier("overviewDetail")
    private OnlineDetailExecute overviewDetail;

    @Autowired
    @Qualifier("flightDetail")
    private OnlineDetailExecute flightDetail;

    @Autowired
    @Qualifier("hotelDetail")
    private OnlineDetailExecute hotelDetail;

    @Autowired
    @Qualifier("trainDetail")
    private OnlineDetailExecute trainDetail;

    @Autowired
    @Qualifier("carDetail")
    private OnlineDetailExecute carDetail;

    @Override
    public OnlineDetailExecute execute(OnlineReportDetailRequest request) {
        QueryReportBuTypeEnum queryByBu = request.getQueryBu();
        switch (queryByBu) {
            case overview:
                return overviewDetail;
            case flight:
                return flightDetail;
            case hotel:
                return hotelDetail;
            case train:
                return trainDetail;
            case car:
                return carDetail;
            default:
                break;
        }
        return null;
    }
}

