package com.corpgovernment.resource.schedule.cleanup.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DataModificationLog {
    private Long id;

    /**
     * 表名称
     */
    private String tableName;

    /**
     * 查询数据使用的sql
     */
    private String selectSql;

    /**
     * 插入数据使用的sql
     */
    private String insertSql;

    /**
     * select_sql 字段查询出来的原始数据（java对象json序列化后的字符串）
     */
    private String oldData;

    /**
     * 更新后的数据（java对象json序列化后的字符串）
     */
    private String newData;

    /**
     * 数据清洗异常后，用于还原的sql（基于old_data字段的数据拼接）
     */
    private String restoreSql;

    private LocalDateTime datachangeCreatetime;

    private LocalDateTime datachangeLasttime;
}
