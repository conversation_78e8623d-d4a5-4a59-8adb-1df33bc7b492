package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className OnlineReportTopRcAnalysisDaoService
 * @date 2024/9/29
 */
public interface OnlineReportTopRcAnalysisDaoService {

    List<Map> topRcAnalysis(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto,
                            Pager pager, String orderType, String user) throws Exception;

    Integer topRcAnalysisCount(AnalysisObjectEnum analysisObjectEnum,
                               BaseQueryConditionDTO baseQueryConditionDto, String orderType, String user) throws Exception;

}
