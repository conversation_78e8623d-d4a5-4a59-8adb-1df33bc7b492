package com.corpgovernment.resource.schedule.onlinereport.consume;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportConsume;
import com.corpgovernment.resource.schedule.onlinereport.dept.MatchBean;

/*
 * <AUTHOR>
 * @date 2021/11/9 20:21
 * @Desc
 */
public interface GenralConsumeBiz<T> extends MatchBean<QueryReportBuTypeEnum> {
    OnlineReportConsume aggreationGenralConsume(T t) throws Exception;
}
