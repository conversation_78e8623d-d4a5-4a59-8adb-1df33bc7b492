package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportRebookTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportRefundTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RebookInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RebookTrendInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RefundInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RefundTrendInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TrainBehaviorInfo;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.behavior.TrainBehaviorAnalysisDao;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.behaivor.RefundRebookDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.behaivor.TrainBehaviorDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022-11-11 10:49
 * @desc
 */
@Service
public class TrainBehaviorAnalysisBiz {

    @Autowired
    private TrainBehaviorAnalysisDao trainBehaviorAnalysisDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    
    public List<TrainBehaviorInfo> behaviorInfos(OnlineReportBehaviorAnalysisRequest request) throws Exception {
        List<TrainBehaviorInfo> result = new ArrayList<>();
        String dim = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()).get("dim");
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        List<TrainBehaviorDTO> metricsDTOS = trainBehaviorAnalysisDao.behaviorAnaylsis(baseQueryConditionDto, TrainBehaviorDTO.class, dim, request.getLang());
        metricsDTOS = Optional.ofNullable(metricsDTOS).orElse(new ArrayList<>());
        Integer sumQuantity = metricsDTOS.stream().filter(i -> Objects.nonNull(i.getTotalQuantity())).mapToInt(TrainBehaviorDTO::getTotalQuantity).sum();
        for (TrainBehaviorDTO i : metricsDTOS) {
            TrainBehaviorInfo trainBehaviorInfo = new TrainBehaviorInfo();
            trainBehaviorInfo.setDim(i.getDim());
            trainBehaviorInfo.setTotalQuantity(i.getTotalQuantity());
            trainBehaviorInfo.setQuantityPercent(OrpReportUtils.divideWithPercent(i.getTotalQuantity(), sumQuantity).doubleValue());
            trainBehaviorInfo.setTotalRefundQuantity(i.getTotalRefundQuantity());
            trainBehaviorInfo.setTotalRefundLoss(OrpReportUtils.formatBigDecimal(i.getTotalRefundLoss()));
            trainBehaviorInfo.setTotalPotentialSaveAmount(OrpReportUtils.formatBigDecimal(i.getTotalPotentialSaveAmount()));
            result.add(trainBehaviorInfo);
        }
        return result;
    }

    
    public RefundInfo refund(OnlineReportRefundTrendRequest request) throws Exception {
        RefundInfo result = new RefundInfo();
        String index = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()).get("index");
        if (StringUtils.equalsIgnoreCase(index, "overview")) {
            result = refundOverview(request);
        } else if (StringUtils.equalsIgnoreCase(index, "trend")) {
            result = refundTrend(request);
        }
        return result;
    }

    private RefundInfo refundOverview(OnlineReportRefundTrendRequest request) throws Exception {
        RefundInfo result = new RefundInfo();
        BaseQueryConditionDTO dto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        dto.setProductType(request.getProductType());
        List<RefundRebookDTO> metricsDTOS = trainBehaviorAnalysisDao.refundOverview(dto, RefundRebookDTO.class,
                getIndustryList(dto.getIndustryType()), dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
        if (CollectionUtils.isNotEmpty(metricsDTOS)) {
            RefundRebookDTO refundRebookDTO = metricsDTOS.get(0);
            result.setTotalRefundQuantity(refundRebookDTO.getCompanyRfRbtkt());
            result.setRefundQuantityPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getCompanyRfRbtkt(), refundRebookDTO.getCompanytkt()).doubleValue());
            result.setTotalRefundLoss(OrpReportUtils.formatBigDecimal(OrpReportUtils.nonNegative(refundRebookDTO.getCompanyLoss())));
            result.setCorpRefundtktPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getCorpRfRbtkt(), refundRebookDTO.getCorptkt()).doubleValue());
            result.setIndustryRefundtktPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getIndustryRfRbtkt(), refundRebookDTO.getIndustrytkt()).doubleValue());
        }
        return result;
    }

    private RefundInfo refundTrend(OnlineReportRefundTrendRequest request) throws Exception {
        RefundInfo result = new RefundInfo();
        List<RefundTrendInfo> trendInfoList = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<RefundRebookDTO> metricsDTOS = trainBehaviorAnalysisDao.refundTrend(baseQueryConditionDto, RefundRebookDTO.class);
        metricsDTOS = Optional.ofNullable(metricsDTOS).orElse(new ArrayList<>());
        Set<String> points = BizUtils.getDatePoints(baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(), QueryReportAggDateDimensionEnum.month);
        for (String date : points) {
            RefundTrendInfo trendInfo = new RefundTrendInfo();
            trendInfo.setPoint(date);
            trendInfo.setTotalRefundQuantity(OrpConstants.ZERO);
            trendInfo.setCompanyRefundPercent(OrpReportUtils.formatBigDecimal(BigDecimal.ZERO).doubleValue());
            trendInfo.setTotalRefundLoss(OrpReportUtils.formatBigDecimal(BigDecimal.ZERO));
            Optional.ofNullable(metricsDTOS).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(BizUtils.dateFormat(i.getDim(), QueryReportAggDateDimensionEnum.month), date))
                    .findFirst().ifPresent(i -> {
                        trendInfo.setTotalQuantity(i.getCompanytkt());
                        trendInfo.setTotalRefundQuantity(i.getCompanyRfRbtkt());
                        trendInfo.setCompanyRefundPercent(OrpReportUtils.divideWithPercent(i.getCompanyRfRbtkt(), i.getCompanytkt()).doubleValue());
                    });
            trendInfoList.add(trendInfo);
        }
        trendInfoList.sort((obj1, obj2) -> {
            return obj1.getPoint().compareTo(obj2.getPoint());
        });
        result.setRefundTrendList(trendInfoList);
        return result;
    }

    
    public RebookInfo reBook(OnlineReportRebookTrendRequest request) throws Exception {
        RebookInfo result = new RebookInfo();
        String index = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()).get("index");
        if (StringUtils.equalsIgnoreCase(index, "overview")) {
            result = rebookOverview(request);
        } else if (StringUtils.equalsIgnoreCase(index, "trend")) {
            result = rebookTrend(request);
        }
        return result;
    }

    private RebookInfo rebookOverview(OnlineReportRebookTrendRequest request) throws Exception {
        RebookInfo result = new RebookInfo();
        BaseQueryConditionDTO dto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        dto.setProductType(request.getProductType());
        List<RefundRebookDTO> metricsDTOS = trainBehaviorAnalysisDao.rebookOverview(dto, RefundRebookDTO.class,
                getIndustryList(dto.getIndustryType()), dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
        if (CollectionUtils.isNotEmpty(metricsDTOS)) {
            RefundRebookDTO refundRebookDTO = metricsDTOS.get(0);
            result.setTotalRebookQuantity(refundRebookDTO.getCompanyRfRbtkt());
            result.setRebookQuantityPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getCompanyRfRbtkt(), refundRebookDTO.getCompanytkt()).doubleValue());
            result.setTotalRebookLoss(OrpReportUtils.formatBigDecimal(OrpReportUtils.nonNegative(refundRebookDTO.getCompanyLoss())));
            result.setCorpRebooktktPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getCorpRfRbtkt(), refundRebookDTO.getCorptkt()).doubleValue());
            result.setIndustryRebooktktPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getIndustryRfRbtkt(), refundRebookDTO.getIndustrytkt()).doubleValue());
        }
        return result;
    }

    private RebookInfo rebookTrend(OnlineReportRebookTrendRequest request) throws Exception {
        RebookInfo result = new RebookInfo();
        List<RebookTrendInfo> trendInfoList = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        List<RefundRebookDTO> metricsDTOS = trainBehaviorAnalysisDao.rebookTrend(baseQueryConditionDto, RefundRebookDTO.class);
        metricsDTOS = Optional.ofNullable(metricsDTOS).orElse(new ArrayList<>());
        Set<String> points = BizUtils.getDatePoints(baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(), QueryReportAggDateDimensionEnum.month);
        for (String date : points) {
            RebookTrendInfo trendInfo = new RebookTrendInfo();
            trendInfo.setPoint(date);
            trendInfo.setTotalRebookQuantity(OrpConstants.ZERO);
            trendInfo.setCompanyRebookPercent(OrpReportUtils.formatBigDecimal(BigDecimal.ZERO).doubleValue());
            trendInfo.setTotalRebookLoss(OrpReportUtils.formatBigDecimal(BigDecimal.ZERO));
            Optional.ofNullable(metricsDTOS).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(BizUtils.dateFormat(i.getDim(), QueryReportAggDateDimensionEnum.month), date))
                    .findFirst().ifPresent(i -> {
                        trendInfo.setTotalQuantity(i.getCompanytkt());
                        trendInfo.setTotalRebookQuantity(i.getCompanyRfRbtkt());
                        trendInfo.setCompanyRebookPercent(OrpReportUtils.divideWithPercent(i.getCompanyRfRbtkt(), i.getCompanytkt()).doubleValue());
                    });
            trendInfoList.add(trendInfo);
        }
        trendInfoList.sort((obj1, obj2) -> {
            return obj1.getPoint().compareTo(obj2.getPoint());
        });
        result.setRebookTrendList(trendInfoList);
        return result;
    }

    protected List getIndustryList(String industryType) {
        if (StringUtils.isNotEmpty(industryType)) {
            return Arrays.asList(industryType);
        }
        return null;
    }
}
