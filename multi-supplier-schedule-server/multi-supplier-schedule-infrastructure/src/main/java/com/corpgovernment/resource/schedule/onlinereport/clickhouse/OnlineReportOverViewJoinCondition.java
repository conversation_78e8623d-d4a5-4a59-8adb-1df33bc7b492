package com.corpgovernment.resource.schedule.onlinereport.clickhouse;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AnalysisObjectOrgInfo;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 *
 * @date 2022/4/10 20:49
 *
 * @Desc
 */
@Service
public class OnlineReportOverViewJoinCondition {

    private static final String RC_FUKK_JOIN_CORP_RESULT_FIELD =
            "CASE WHEN flight.corp_corporation != '' THEN flight.corp_corporation ELSE  CASE \n"
                    + "                WHEN hotel.corp_corporation != '' THEN hotel.corp_corporation\n"
                    + "                ELSE train.corp_corporation END END AS aggId, " +
                    "CASE WHEN flight.corp_name != '' THEN flight.corp_name ELSE  CASE \n"
                    + "                WHEN hotel.corp_name != '' THEN hotel.corp_name\n"
                    + "                ELSE train.corp_name END END AS aggType";

    private static final String RC_FUKK_JOIN_UID_RESULT_FIELD =
            "CASE WHEN flight.uid != '' THEN flight.uid ELSE  CASE \n"
                    + "                WHEN hotel.uid != '' THEN hotel.uid\n"
                    + "                ELSE train.uid END END AS aggId," +
                    "CASE WHEN flight.user_name != '' THEN flight.user_name ELSE  CASE \n"
                    + "                WHEN hotel.user_name != '' THEN hotel.user_name\n"
                    + "                ELSE train.user_name END END AS aggType";

    private static final String OVERAMOUNT_FUKK_JOIN_CORP_RESULT_FIELD =
            "CASE WHEN flight.corp_corporation != '' THEN flight.corp_corporation ELSE hotel.corp_corporation END AS aggId," +
                    "CASE WHEN flight.corp_name != '' THEN flight.corp_name ELSE hotel.corp_name END AS aggType";

    private static final String OVERAMOUNT_FUKK_JOIN_UID_RESULT_FIELD =
            "CASE WHEN flight.uid != '' THEN flight.uid ELSE hotel.uid END AS aggId," +
                    "CASE WHEN flight.user_name != '' THEN flight.user_name ELSE hotel.user_name END AS aggType";

    private static final String CARBONS_FUKK_JOIN_CORP_RESULT_FIELD =
            "CASE WHEN flight.corp_corporation != '' THEN flight.corp_corporation ELSE  CASE \n"
                    + "                WHEN train.corp_corporation != '' THEN train.corp_corporation\n"
                    + "                ELSE car.corp_corporation END END AS aggId, " +
                    "CASE WHEN flight.corp_name != '' THEN flight.corp_name ELSE  CASE \n"
                    + "                WHEN train.corp_name != '' THEN train.corp_name\n"
                    + "                ELSE car.corp_name END END AS aggType";

    private static final String CARBONS_FUKK_JOIN_UID_RESULT_FIELD =
            "CASE WHEN flight.uid != '' THEN flight.uid ELSE  CASE \n"
                    + "                WHEN train.uid != '' THEN train.uid\n"
                    + "                ELSE car.uid END END AS aggId," +
                    "CASE WHEN flight.user_name != '' THEN flight.user_name ELSE  CASE \n"
                    + "                WHEN train.user_name != '' THEN train.user_name\n"
                    + "                ELSE car.user_name END END AS aggType";

    private static final String RC_FUKK_JOIN_ACCOUNT_RESULT_FIELD =
            "CASE WHEN (flight.account_id is not null and flight.account_id != 0) THEN flight.account_id ELSE \n"
                    + "            CASE WHEN (hotel.account_id is not null and hotel.account_id != 0) THEN hotel.account_id\n"
                    + "                ELSE train.account_id  END END AS aggId," +
                    "CASE WHEN flight.ACCOUNT != '' THEN flight.ACCOUNT ELSE \n"
                    + "            CASE WHEN hotel.ACCOUNT != '' THEN hotel.ACCOUNT\n"
                    + "                ELSE train.ACCOUNT  END END AS aggType";

    private static final String OVERAMOUNT_FUKK_JOIN_ACCOUNT_RESULT_FIELD =
            "CASE WHEN (flight.account_id is not null and flight.account_id != 0) THEN flight.account_id ELSE hotel.account_id END AS aggId," +
                    "CASE WHEN flight.ACCOUNT != '' THEN flight.ACCOUNT ELSE hotel.ACCOUNT END AS aggType";

    private static final String CARBONS_FUKK_JOIN_ACCOUNT_RESULT_FIELD =
            "CASE WHEN (flight.account_id is not null and flight.account_id != 0) THEN flight.account_id ELSE \n"
                    + "            CASE WHEN (train.account_id is not null and train.account_id != 0) THEN train.account_id\n"
                    + "                ELSE car.account_id  END END AS aggId," +
                    "CASE WHEN flight.ACCOUNT != '' THEN flight.ACCOUNT ELSE \n"
                    + "            CASE WHEN train.ACCOUNT != '' THEN train.ACCOUNT\n"
                    + "                ELSE car.ACCOUNT  END END AS aggType";

    private static final String RC_FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD =
            "CASE WHEN (flight.account_id is not null and flight.account_id != 0) THEN flight.account_id ELSE \n"
                    + "            CASE WHEN (hotel.account_id is not null and hotel.account_id != 0) THEN hotel.account_id\n"
                    + "                ELSE train.account_id  END END AS aggId," +
                    "CASE WHEN flight.ACCOUNTCODE != '' THEN flight.ACCOUNTCODE ELSE \n"
                    + "            CASE WHEN hotel.ACCOUNTCODE != '' THEN hotel.ACCOUNTCODE\n"
                    + "                ELSE train.ACCOUNTCODE  END END AS aggType";

    private static final String OVERAMOUNT_FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD =
            "CASE WHEN (flight.account_id is not null and flight.account_id != 0) THEN flight.account_id ELSE hotel.account_id END AS aggId," +
                    "CASE WHEN flight.ACCOUNTCODE != '' THEN flight.ACCOUNTCODE ELSE hotel.ACCOUNTCODE END AS aggType";

    private static final String CARBONS_FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD =
            "CASE WHEN (flight.account_id is not null and flight.account_id != 0) THEN flight.account_id ELSE \n"
                    + "            CASE WHEN (train.account_id is not null and train.account_id != 0) THEN train.account_id\n"
                    + "                ELSE car.account_id  END END AS aggId," +
                    "CASE WHEN flight.ACCOUNTCODE != '' THEN flight.ACCOUNTCODE ELSE \n"
                    + "            CASE WHEN train.ACCOUNTCODE != '' THEN train.ACCOUNTCODE\n"
                    + "                ELSE car.ACCOUNTCODE  END END AS aggType";

    private static final String RC_FUKK_JOIN_ORG_RESULT_FIELD =
            "CASE WHEN  flight.ORG != '' THEN flight.ORG ELSE  CASE WHEN hotel.ORG != '' THEN hotel.ORG ELSE train.ORG END END AS aggId," +
                    "CASE WHEN  flight.ORG != '' THEN flight.ORG ELSE  CASE WHEN hotel.ORG != '' THEN hotel.ORG ELSE train.ORG END END AS aggType";

    private static final String OVERAMOUNT_FUKK_JOIN_ORG_RESULT_FIELD =
            "CASE WHEN flight.ORG != '' THEN flight.ORG ELSE hotel.ORG END AS aggId," +
                    "CASE WHEN flight.ORG != '' THEN flight.ORG ELSE hotel.ORG END AS aggType";

    private static final String CARBONS_FUKK_JOIN_ORG_RESULT_FIELD =
            "CASE WHEN flight.ORG != '' THEN flight.ORG ELSE  CASE \n"
                    + "                WHEN train.ORG != '' THEN train.ORG\n"
                    + "                ELSE car.ORG END END AS aggId," +
                    "CASE WHEN flight.ORG != '' THEN flight.ORG ELSE  CASE \n"
                    + "                WHEN train.ORG != '' THEN train.ORG\n"
                    + "                ELSE car.ORG END END AS aggType";

    private static final String RC_FUKK_JOIN_DEPT_RESULT_FIELD =
            "CASE WHEN  flight.%s != '%s' THEN flight.%s ELSE  CASE WHEN hotel.%s != '%s' THEN hotel.%s ELSE \n"
                    + "                    CASE WHEN train.%s != '%s' THEN train.%s ELSE ''\n"
                    + "                    END  END  END AS aggId , " +
                    "CASE WHEN  flight.%s != '' THEN flight.%s ELSE  CASE WHEN hotel.%s != '' THEN hotel.%s ELSE \n"
                    + "                    CASE WHEN train.%s != '' THEN train.%s ELSE '%s'\n"
                    + "                    END  END  END AS aggType";

    private static final String OVERAMOUNT_FUKK_JOIN_DEPT_RESULT_FIELD =
            "CASE WHEN  flight.%s != '%s' THEN flight.%s ELSE  CASE WHEN hotel.%s != '%s' THEN hotel.%s ELSE '' END  END AS aggId," +
                    "CASE WHEN  flight.%s != '' THEN flight.%s ELSE  CASE WHEN hotel.%s != '' THEN hotel.%s ELSE '%s' END  END AS aggType";

    private static final String CARBONS_FUKK_JOIN_DEPT_RESULT_FIELD =
            "CASE WHEN  flight.%s != '%s' THEN flight.%s ELSE  CASE WHEN train.%s != '%s' THEN train.%s ELSE \n"
                    + "                    CASE WHEN car.%s != '%s' THEN car.%s ELSE ''\n"
                    + "                    END  END  END AS aggId," +
                    "CASE WHEN  flight.%s != '' THEN flight.%s ELSE  CASE WHEN train.%s != '' THEN train.%s ELSE \n"
                    + "                    CASE WHEN car.%s != '' THEN car.%s ELSE '%s'\n"
                    + "                    END  END  END AS aggType";

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinCondition(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String lang, Boolean useSr) {
        if (ConfigUtils.getBoolean("custom_dim_open", false)) {
            return joinConditionCustom(analysisObjectEnum, analysisObjectOrgInfo, lang, useSr);
        } else {
            return joinConditionDefault(analysisObjectEnum, analysisObjectOrgInfo, lang, useSr);
        }
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionDefault(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String lang, Boolean useSr) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz.setResultFields("corp_corporation, corp_name ")
                        .setGroupFields(" corp_corporation, corp_name ")
                        .setRcFullJoinResultFields(RC_FUKK_JOIN_CORP_RESULT_FIELD)
                        .setOverAmountFullJoinResultFields(OVERAMOUNT_FUKK_JOIN_CORP_RESULT_FIELD)
                        .setCarbonsFullJoinResultFields(CARBONS_FUKK_JOIN_CORP_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name ")
                        .setHotelFullJointrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name ")
                        .setFlightFullJoinTrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name ")
                        .setTrainFullJoinCar(
                                "flight.corp_corporation = car.corp_corporation and flight.corp_name = car.corp_name ");
                break;
            case ACCOUNT:
                biz = biz.setResultFields("corp_corporation,corp_name ,account_id, account_name as ACCOUNT ")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_name ")
                        .setRcFullJoinResultFields(RC_FUKK_JOIN_ACCOUNT_RESULT_FIELD)
                        .setOverAmountFullJoinResultFields(OVERAMOUNT_FUKK_JOIN_ACCOUNT_RESULT_FIELD)
                        .setCarbonsFullJoinResultFields(CARBONS_FUKK_JOIN_ACCOUNT_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name "
                                        + "and flight.account_id = hotel.account_id and flight.ACCOUNT = hotel.ACCOUNT ")
                        .setHotelFullJointrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name "
                                        + "and flight.account_id = train.account_id and flight.ACCOUNT = train.ACCOUNT ")
                        .setFlightFullJoinTrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name "
                                        + "and flight.account_id = train.account_id and flight.ACCOUNT = train.ACCOUNT ")
                        .setTrainFullJoinCar(
                                "flight.corp_corporation = car.corp_corporation and flight.corp_name = car.corp_name "
                                        + "and flight.account_id = car.account_id and flight.ACCOUNT = car.ACCOUNT ");
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("corp_corporation,corp_name,account_id, account_code as ACCOUNTCODE")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_code")
                        .setRcFullJoinResultFields(RC_FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD)
                        .setOverAmountFullJoinResultFields(OVERAMOUNT_FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD)
                        .setCarbonsFullJoinResultFields(CARBONS_FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name "
                                        + "and flight.account_id = hotel.account_id and flight.ACCOUNTCODE = hotel.ACCOUNTCODE ")
                        .setHotelFullJointrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name "
                                        + "and flight.account_id = train.account_id and flight.ACCOUNTCODE = train.ACCOUNTCODE ")
                        .setFlightFullJoinTrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name "
                                        + "and flight.account_id = train.account_id and flight.ACCOUNTCODE = train.ACCOUNTCODE ")
                        .setTrainFullJoinCar(
                                "flight.corp_corporation = car.corp_corporation and flight.corp_name = car.corp_name "
                                        + "and flight.account_id = car.account_id and flight.ACCOUNTCODE = car.ACCOUNTCODE ");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase();
                break;
            case COSTCENTER1:
                analysisObject = "cost_center1";
                break;
            case COSTCENTER2:
                analysisObject = "cost_center2";
                break;
            case COSTCENTER3:
                analysisObject = "cost_center3";
                break;
            case COSTCENTER4:
                analysisObject = "cost_center4";
                break;
            case COSTCENTER5:
                analysisObject = "cost_center5";
                break;
            case COSTCENTER6:
                analysisObject = "cost_center6";
                break;
            case ORG:
                analysisObject = getAnalysisObjectOrgInfo(analysisObjectOrgInfo, useSr);
                break;
            case UID:
                biz.setResultFields("uid, user_name ")
                        .setGroupFields(" uid, user_name ")
                        .setRcFullJoinResultFields(RC_FUKK_JOIN_UID_RESULT_FIELD)
                        .setOverAmountFullJoinResultFields(OVERAMOUNT_FUKK_JOIN_UID_RESULT_FIELD)
                        .setCarbonsFullJoinResultFields(CARBONS_FUKK_JOIN_UID_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.uid = hotel.uid and flight.user_name = hotel.user_name ")
                        .setHotelFullJointrain(
                                "flight.uid = train.uid and flight.user_name = train.user_name ")
                        .setFlightFullJoinTrain(
                                "flight.uid = train.uid and flight.user_name = train.user_name ")
                        .setTrainFullJoinCar(
                                "flight.uid = car.uid and flight.user_name = car.user_name ");
                break;
            default:
                break;
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(analysisObject)) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            biz = biz
                    .setResultFields(
                            "corp_corporation, corp_name, case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                                    + "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(" corp_corporation, corp_name ," + " case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                            + "then '" + other + "' else " + analysisObject + " end ")
                    .setRcFullJoinResultFields(String.format(RC_FUKK_JOIN_DEPT_RESULT_FIELD, analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(),
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(), other))
                    .setOverAmountFullJoinResultFields(String.format(OVERAMOUNT_FUKK_JOIN_DEPT_RESULT_FIELD, analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(),
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other))
                    .setCarbonsFullJoinResultFields(String.format(CARBONS_FUKK_JOIN_DEPT_RESULT_FIELD, analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(),
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(), other))
                    .setFlightFullJoinHotel(
                            "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name and flight."
                                    + analysisObjectEnum.toString() + " = hotel." + analysisObjectEnum.toString())
                    .setHotelFullJointrain(
                            "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name and flight."
                                    + analysisObjectEnum.toString() + " = train." + analysisObjectEnum.toString())
                    .setFlightFullJoinTrain(
                            "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name and "
                                    + String.format("flight.%s = train.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString()))
                    .setTrainFullJoinCar(
                            "flight.corp_corporation = car.corp_corporation and flight.corp_name = car.corp_name and "
                                    + String.format("flight.%s = car.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString()));

        }
        if (analysisObjectEnum == AnalysisObjectEnum.ORG) {
            biz = biz
                    .setResultFields(" corp_corporation, corp_name, " + analysisObject + " as " + analysisObjectEnum.toString())
                    .setGroupFields(" corp_corporation, corp_name , " + analysisObject)
                    .setRcFullJoinResultFields(RC_FUKK_JOIN_ORG_RESULT_FIELD)
                    .setOverAmountFullJoinResultFields(OVERAMOUNT_FUKK_JOIN_ORG_RESULT_FIELD)
                    .setCarbonsFullJoinResultFields(CARBONS_FUKK_JOIN_ORG_RESULT_FIELD)
                    .setFlightFullJoinHotel("flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name and flight.ORG = hotel.ORG")
                    .setHotelFullJointrain("flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name and flight.ORG = train.ORG ")
                    .setFlightFullJoinTrain("flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name and flight.ORG = train.ORG ")
                    .setTrainFullJoinCar("flight.corp_corporation = car.corp_corporation and flight.corp_name = car.corp_name and flight.ORG = car.ORG ");
        }
        return biz;
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionCustom(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String lang, Boolean useSr) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz.setResultFields("corp_corporation, corp_name ")
                        .setGroupFields(" corp_corporation, corp_name ")
                        .setRcFullJoinResultFields(RC_FUKK_JOIN_CORP_RESULT_FIELD)
                        .setOverAmountFullJoinResultFields(OVERAMOUNT_FUKK_JOIN_CORP_RESULT_FIELD)
                        .setCarbonsFullJoinResultFields(CARBONS_FUKK_JOIN_CORP_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name ")
                        .setHotelFullJointrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name ")
                        .setFlightFullJoinTrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name ")
                        .setTrainFullJoinCar(
                                "flight.corp_corporation = car.corp_corporation and flight.corp_name = car.corp_name ");
                break;
            case ACCOUNT:
                biz = biz.setResultFields("corp_corporation,corp_name,account_id, account_name as ACCOUNT ")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_name ")
                        .setRcFullJoinResultFields(RC_FUKK_JOIN_ACCOUNT_RESULT_FIELD)
                        .setOverAmountFullJoinResultFields(OVERAMOUNT_FUKK_JOIN_ACCOUNT_RESULT_FIELD)
                        .setCarbonsFullJoinResultFields(CARBONS_FUKK_JOIN_ACCOUNT_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name "
                                        + "and flight.account_id = hotel.account_id and flight.ACCOUNT = hotel.ACCOUNT ")
                        .setHotelFullJointrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name "
                                        + "and flight.account_id = train.account_id and flight.ACCOUNT = train.ACCOUNT ")
                        .setFlightFullJoinTrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name "
                                        + "and flight.account_id = train.account_id and flight.ACCOUNT = train.ACCOUNT ")
                        .setTrainFullJoinCar(
                                "flight.corp_corporation = car.corp_corporation and flight.corp_name = car.corp_name "
                                        + "and flight.account_id = car.account_id and flight.ACCOUNT = car.ACCOUNT ");
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("corp_corporation, corp_name,account_id, account_code as ACCOUNTCODE")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_code")
                        .setRcFullJoinResultFields(RC_FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD)
                        .setOverAmountFullJoinResultFields(OVERAMOUNT_FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD)
                        .setCarbonsFullJoinResultFields(CARBONS_FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name "
                                        + "and flight.account_id = hotel.account_id and flight.ACCOUNTCODE = hotel.ACCOUNTCODE ")
                        .setHotelFullJointrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name "
                                        + "and flight.account_id = train.account_id and flight.ACCOUNTCODE = train.ACCOUNTCODE ")
                        .setFlightFullJoinTrain(
                                "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name "
                                        + "and flight.account_id = train.account_id and flight.ACCOUNTCODE = train.ACCOUNTCODE ")
                        .setTrainFullJoinCar(
                                "flight.corp_corporation = car.corp_corporation and flight.corp_name = car.corp_name "
                                        + "and flight.account_id = car.account_id and flight.ACCOUNTCODE = car.ACCOUNTCODE ");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase().concat("_custom");
                break;
            case COSTCENTER1:
                analysisObject = "costcenter1_custom";
                break;
            case COSTCENTER2:
                analysisObject = "costcenter2_custom";
                break;
            case COSTCENTER3:
                analysisObject = "costcenter3_custom";
                break;
            case COSTCENTER4:
                analysisObject = "costcenter4_custom";
                break;
            case COSTCENTER5:
                analysisObject = "costcenter5_custom";
                break;
            case COSTCENTER6:
                analysisObject = "costcenter6_custom";
                break;
            case ORG:
                analysisObject = getAnalysisObjectOrgInfo(analysisObjectOrgInfo, useSr);
                break;
            case UID:
                biz.setResultFields("uid, user_name ")
                        .setGroupFields(" uid, user_name ")
                        .setRcFullJoinResultFields(RC_FUKK_JOIN_UID_RESULT_FIELD)
                        .setOverAmountFullJoinResultFields(OVERAMOUNT_FUKK_JOIN_UID_RESULT_FIELD)
                        .setCarbonsFullJoinResultFields(CARBONS_FUKK_JOIN_UID_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.uid = hotel.uid and flight.user_name = hotel.user_name ")
                        .setHotelFullJointrain(
                                "flight.uid = train.uid and flight.user_name = train.user_name ")
                        .setFlightFullJoinTrain(
                                "flight.uid = train.uid and flight.user_name = train.user_name ")
                        .setTrainFullJoinCar(
                                "flight.uid = car.uid and flight.user_name = car.user_name ");
                break;
            default:
                break;
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(analysisObject)) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            biz = biz
                    .setResultFields(
                            "corp_corporation, corp_name, case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                                    + "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(" corp_corporation, corp_name ," + "case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                            + "then '" + other + "' else " + analysisObject + " end ")
                    .setRcFullJoinResultFields(String.format(RC_FUKK_JOIN_DEPT_RESULT_FIELD, analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(),
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(), other))
                    .setOverAmountFullJoinResultFields(String.format(OVERAMOUNT_FUKK_JOIN_DEPT_RESULT_FIELD, analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(),
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other))
                    .setCarbonsFullJoinResultFields(String.format(CARBONS_FUKK_JOIN_DEPT_RESULT_FIELD, analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), other,
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(),
                            analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(), analysisObjectEnum.toString(), other))
                    .setFlightFullJoinHotel(
                            "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name and flight."
                                    + analysisObjectEnum.toString() + " = hotel." + analysisObjectEnum.toString())
                    .setHotelFullJointrain(
                            "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name and flight."
                                    + analysisObjectEnum.toString() + " = train." + analysisObjectEnum.toString())
                    .setFlightFullJoinTrain(
                            "flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name and "
                                    + String.format("flight.%s = train.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString()))
                    .setTrainFullJoinCar(
                            "flight.corp_corporation = car.corp_corporation and flight.corp_name = car.corp_name and "
                                    + String.format("flight.%s = car.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString()));

        }
        if (analysisObjectEnum == AnalysisObjectEnum.ORG) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            biz = biz
                    .setResultFields(" corp_corporation, corp_name, case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                            + "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(" corp_corporation, corp_name ," + "case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                            + "then '" + other + "' else " + analysisObject + " end ")
                    .setRcFullJoinResultFields(RC_FUKK_JOIN_ORG_RESULT_FIELD)
                    .setOverAmountFullJoinResultFields(OVERAMOUNT_FUKK_JOIN_ORG_RESULT_FIELD)
                    .setCarbonsFullJoinResultFields(CARBONS_FUKK_JOIN_ORG_RESULT_FIELD)
                    .setFlightFullJoinHotel("flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name and flight.ORG = hotel.ORG")
                    .setHotelFullJointrain("flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name and flight.ORG = train.ORG ")
                    .setFlightFullJoinTrain("flight.corp_corporation = train.corp_corporation and flight.corp_name = train.corp_name and flight.ORG = train.ORG ")
                    .setTrainFullJoinCar("flight.corp_corporation = car.corp_corporation and flight.corp_name = car.corp_name and flight.ORG = car.ORG ");
        }
        return biz;
    }

    private String getAnalysisObjectOrgInfo(AnalysisObjectOrgInfo analysisObjectOrgInfo, Boolean useSr) {
        int level = analysisObjectOrgInfo.getLevel();
        switch (level) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
                return useSr ? String.format("get_json_object(orginfo, '$.org%d')", analysisObjectOrgInfo.getLevel() + 1)
                        : String.format("visitParamExtractString(orginfo, 'org%d')", analysisObjectOrgInfo.getLevel() + 1);
            default:
                return "";
        }
    }

    @Data
    @Accessors(chain = true)
    public class JoinCondition {
        private String resultFields;

        private String groupFields;

        private String flightFullJoinHotel;

        private String hotelFullJointrain;

        private String rcFullJoinResultFields;
        private String OverAmountFullJoinResultFields;
        private String carbonsFullJoinResultFields;

        private String trainFullJoinCar;

        private String flightFullJoinTrain;
    }
}
