package com.corpgovernment.resource.schedule.onlinereport.convert;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrainOrderInfo;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.TrainOrderDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/*
 * <AUTHOR>
 * @date 2021/11/8 16:26
 * @Desc
 */

@Mapper(componentModel = "spring")
public interface TrainOrderDetailMapper {

    TrainOrderDetailMapper INSTANCE = Mappers.getMapper(TrainOrderDetailMapper.class);

    @Mappings({
//            @Mapping(target = "tripId", expression = "java(convert(trainOrderDTO.getTripId()))"),
            @Mapping(target = "carbonSave", expression = "java(calCarbonSave(trainOrderDTO.getCarbonEmission(),trainOrderDTO.getMedianCarbons()))"),
            @Mapping(target = "medianCarbons", expression = "java(convertUnit(trainOrderDTO.getMedianCarbons()))"),
            @Mapping(target = "carbonEmission", expression = "java(convertUnit(trainOrderDTO.getCarbonEmission()))"),
    })
    OnlineReportTrainOrderInfo toDTO(TrainOrderDTO trainOrderDTO);

    List<OnlineReportTrainOrderInfo> toDTOs(List<TrainOrderDTO> trainOrderDTOs);

    @Named("convert")
    default Long convert(String num) {
        if (StringUtils.isEmpty(num)) {
            return 0L;
        }
        return Long.valueOf(StringUtils.trimToEmpty(num));
    }

    @Named("calCarbonSave")
    default Double calCarbonSave(Integer carbons, Integer medianCarbons) {
        if (carbons == null || medianCarbons == null || carbons == 0 || medianCarbons == 0) {
            return 0D;
        }
        return OrpReportUtils.divideUp(medianCarbons - carbons, OrpConstants.HANDRED * OrpConstants.TEN, OrpConstants.TWO).doubleValue();
    }

    @Named("convertUnit")
    default Double convertUnit(Integer medianCarbons) {
        if (medianCarbons == null || medianCarbons == 0) {
            return 0D;
        }
        return OrpReportUtils.divideUp(medianCarbons, OrpConstants.HANDRED * OrpConstants.TEN, OrpConstants.TWO).doubleValue();
    }
}
