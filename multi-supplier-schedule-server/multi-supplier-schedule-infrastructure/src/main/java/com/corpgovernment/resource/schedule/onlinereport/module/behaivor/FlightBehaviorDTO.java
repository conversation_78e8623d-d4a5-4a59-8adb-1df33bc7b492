package com.corpgovernment.resource.schedule.onlinereport.module.behaivor;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class FlightBehaviorDTO {

    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    public String dim;
    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    public Integer totalQuantity;
    @Column(name = "totalPrice")
    @Type(value = Types.DECIMAL)
    public BigDecimal totalPrice;
    @Column(name = "totalTpms")
    @Type(value = Types.DECIMAL)
    public BigDecimal totalTpms;
    @Column(name = "totalDiscount")
    @Type(value = Types.DECIMAL)
    public BigDecimal totalDiscount;
    @Column(name = "totalRefundQuantity")
    @Type(value = Types.INTEGER)
    public Integer totalRefundQuantity;
    @Column(name = "totalRebookQuantity")
    @Type(value = Types.INTEGER)
    public Integer totalRebookQuantity;
}
