package com.corpgovernment.resource.schedule.usertemplate.gateway;

import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.ChineseLanguageConfig;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.ConstDefine;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.CustomContentInfoBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.CustomTemplateBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.dto.orderdetail.usertemplate.QueryCustomTemplateDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.JsonUtils;
import com.corpgovernment.resource.schedule.domain.usertemplate.IReportUserTemplateGateway;
import com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateContentDetailDo;
import com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateDo;
import com.corpgovernment.resource.schedule.usertemplate.mysql.mapper.UserCustomTemplateContentDetailDTOMapper;
import com.corpgovernment.resource.schedule.usertemplate.mysql.mapper.UserCustomTemplateDTOMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/7 17:06
 * @description
 */
@Service
@Slf4j
public class ReportUserTemplateGateway implements IReportUserTemplateGateway {


    @Autowired
    private UserCustomTemplateBusiness userCustomTemplateBusiness;


    private static final int PAGE_SIZE = 50;


    @Override
    public JSONResult addOrUpdateCustomTemplate(CustomTemplateBo templateBo) {
        try {
            UserCustomTemplateDo userCustomTemplateDTO = convertToUserCustomTemplateDO(templateBo);
            List<UserCustomTemplateContentDetailDo> conditionInfoDTOS =
                    Optional.ofNullable(templateBo.getCustomConditionInfoBoList()).map(Collection::stream).map(stream ->
                            stream.map(infoBo -> convertToUserCustomTemplateContentInfoDO(templateBo.getCustomConditionNo(), infoBo))
                                    .collect(Collectors.toList())).orElse(Lists.newArrayList());
            List<UserCustomTemplateContentDetailDo> columnInfoDTOS =
                    Optional.ofNullable(templateBo.getCustomColumnInfoBoList()).map(Collection::stream).map(stream ->
                            stream.map(infoBo -> this.convertToUserCustomTemplateContentInfoDTO(templateBo.getCustomColumnNo(), infoBo))
                                    .collect(Collectors.toList())).orElse(Lists.newArrayList());

            if (userCustomTemplateBusiness.checkExistTemplate(templateBo.getTemplateNo())) {
                userCustomTemplateBusiness.updateUserCustomTemplate(userCustomTemplateDTO, conditionInfoDTOS, columnInfoDTOS);
            } else {
                userCustomTemplateBusiness.insertUserCustomTemplate(userCustomTemplateDTO, conditionInfoDTOS, columnInfoDTOS);
            }
        } catch (Exception e) {
            return JSONResult.errorMsg(ConstDefine.SYTEMERROR_STATUS_COE,ChineseLanguageConfig.get(ConstDefine.SYSTEM_ERROR));
        }

        return JSONResult.ok();
    }




    @Override
    public JSONResult deleteCustomTemplate(List<String> templateNos) {
        try {
            if (CollectionUtils.isNotEmpty(templateNos)) {
                userCustomTemplateBusiness.deleteUserCustomTemplate(templateNos);
            }
        } catch (Exception e) {
            log.error( String.format("delete error, requestType:%s", templateNos), e);
            return JSONResult.errorMsg(ConstDefine.SYTEMERROR_STATUS_COE,ChineseLanguageConfig.get(ConstDefine.SYSTEM_ERROR));
        }
        return JSONResult.ok();
    }

    @Override
    public List<CustomTemplateBo> queryCustomTemplate(QueryCustomTemplateDto request) {
        try {
            return userCustomTemplateBusiness.selectByUidAndTemplateNo(request);
        } catch (Exception e) {
            log.error("query error, requestType:{}, error:{}", JsonUtils.objectToString(request), e);
        }
        return Lists.newArrayList();
    }

    @Override
    public JSONResult<Boolean> checkExistingCustomTemplate(String uid, String templateName, String reportKey) {
        try {
            boolean exist = userCustomTemplateBusiness.checkTemplateNameIfDuplicated(templateName,
                    uid, reportKey);
            log.info("checkExistingCustomTemplate, templateName:{},reportKey:{}, exist:{}", templateName,reportKey, exist);
            return JSONResult.success(exist);
        } catch (Exception e) {
            log.error("check error, templateName:{},reportKey:{}, error:{}", templateName,reportKey, e);
        }
        return JSONResult.build(ConstDefine.SYTEMERROR_STATUS_COE,ChineseLanguageConfig.get(ConstDefine.SYSTEM_ERROR),false);
    }

    @Override
    public CustomTemplateBo queryCustomTemplateByNo(String templateNo) {
        QueryCustomTemplateDto request = new QueryCustomTemplateDto();
        request.setTemplateNo(templateNo);

        try {
            List<CustomTemplateBo> customTemplateBos = userCustomTemplateBusiness.selectByUidAndTemplateNo(request);
            if (CollectionUtils.isNotEmpty(customTemplateBos)){
                return customTemplateBos.get(0);
            }
        } catch (Exception e) {
            log.error("query error, requestType:{}, error:{}", JsonUtils.objectToString(request), e);
        }
        return null;
    }


    private UserCustomTemplateContentDetailDo convertToUserCustomTemplateContentInfoDO(String contentNo, CustomContentInfoBo contentInfoBo) {
        UserCustomTemplateContentDetailDo contentInfoDTO = new UserCustomTemplateContentDetailDo();
        contentInfoDTO.setContentNo(contentNo);
        contentInfoDTO.setFliedKey(contentInfoBo.getFliedKey());
        contentInfoDTO.setFliedName(contentInfoBo.getFliedName());
        contentInfoDTO.setFliedValue(contentInfoBo.getFliedValue());
        return contentInfoDTO;
    }

    private UserCustomTemplateDo convertToUserCustomTemplateDO(CustomTemplateBo templateBo) {
        UserCustomTemplateDo userCustomTemplateDTO = new UserCustomTemplateDo();
        userCustomTemplateDTO.setTemplateNo(templateBo.getTemplateNo());
        userCustomTemplateDTO.setTemplateName(templateBo.getTemplateName());
        userCustomTemplateDTO.setUid(templateBo.getUid());
        userCustomTemplateDTO.setReportKey(templateBo.getReportKey());
        userCustomTemplateDTO.setCustomConditionNo(templateBo.getCustomConditionNo());
        userCustomTemplateDTO.setCustomColumnNo(templateBo.getCustomColumnNo());
        return userCustomTemplateDTO;
    }

    private UserCustomTemplateContentDetailDo convertToUserCustomTemplateContentInfoDTO(String contentNo,
                                                                                         CustomContentInfoBo contentInfoBo) {
        UserCustomTemplateContentDetailDo contentInfoDTO = new UserCustomTemplateContentDetailDo();
        contentInfoDTO.setContentNo(contentNo);
        contentInfoDTO.setFliedKey(contentInfoBo.getFliedKey());
        contentInfoDTO.setFliedName(contentInfoBo.getFliedName());
        contentInfoDTO.setFliedValue(contentInfoBo.getFliedValue());
        return contentInfoDTO;
    }
}
