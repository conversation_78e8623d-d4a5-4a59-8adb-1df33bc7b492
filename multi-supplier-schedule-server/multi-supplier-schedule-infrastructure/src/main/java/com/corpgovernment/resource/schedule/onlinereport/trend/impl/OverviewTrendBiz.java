package com.corpgovernment.resource.schedule.onlinereport.trend.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportTypeionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendLegend;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendPoint;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendRequest;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportOverviewTrendDao;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportOverviewTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportOverviewTrendJPDTO;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.OnlineTrendFieldEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.ReportTrendHeaderEnum;
import com.corpgovernment.resource.schedule.onlinereport.trend.GeneralTrendBiz;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/10 13:36
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class OverviewTrendBiz extends OverviewTrendBaseBiz implements GeneralTrendBiz<OnlineReportTrendRequest> {

    @Autowired
    OnlineReportOverviewTrendDao onlineReportOverviewTrendDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    
    @Override
    public List<OnlineReportTrendPoint> trendBody(OnlineReportTrendRequest onlineReportTrendRequest) throws Exception {
        OnlineTrendRequestDto onlineTrendRequestDto = map(onlineReportTrendRequest, baseQueryConditionMapper);
        BaseQueryCondition baseQueryCondition = onlineReportTrendRequest.getBasecondition();
        if (BlueSpaceUtils.isForeign(baseQueryCondition.getPos(), baseQueryCondition.getBlueSpace())) {
            return trendBodyJP(onlineReportTrendRequest);
        }
        HashMap<String, OnlineReportTrendPoint> points = new HashMap<>();
        List<Field> fields = Arrays.stream(OnlineReportOverviewTrendDTO.class.getDeclaredFields())
                .filter(f -> !(f.getName().equals("date") || f.getName().equals("dim")))
                .collect(Collectors.toList());
        List<OnlineReportOverviewTrendDTO> bizPoints = new ArrayList<>();
        List<OnlineReportOverviewTrendDTO> blankBizPoints = blankResults(onlineReportTrendRequest);

        String reqDim = onlineReportTrendRequest.getExtData().getOrDefault("dim", "");

        if (onlineReportTrendRequest.aggType == QueryReportAggTypeEnum.accumulated) {
            // 概览 当选择行程数 且选择整体时, 取一次行程的数据
            if (onlineReportTrendRequest.getQueryType() == QueryReportTypeionEnum.numberOfTrips) {
                if (StringUtils.isBlank(reqDim)) {
                    // bizPoints = onlineReportOverviewTrendDao.queryAccOverviewTripTrend(onlineTrendRequestDto);
                    bizPoints = onlineReportOverviewTrendDao.queryAccOverviewTripTrendOrderNum(onlineTrendRequestDto);
                } else {
                    // bizPoints = onlineReportOverviewTrendDao.queryOnlineReportAccOverviewTrend(onlineTrendRequestDto);
                    bizPoints = onlineReportOverviewTrendDao.queryOnlineReportAccOverviewTrendOrderNum(onlineTrendRequestDto);
                }
//                bizPoints = onlineReportOverviewTrendDao.queryOnlineReportAccOverviewTrend(onlineTrendRequestDto);

            }
            if (onlineReportTrendRequest.getQueryType() == QueryReportTypeionEnum.amount) {

                bizPoints = onlineReportOverviewTrendDao.queryOnlineReportAccOverviewTrend(onlineTrendRequestDto);

            }

            bizPoints.addAll(blankBizPoints);

            bizPoints.sort((o1, o2) -> (int) OrpDateTimeUtils.betweenDay(o2.getDate(), o1.getDate()));
            OnlineReportOverviewTrendDTO tmpPoint = null;

            for (OnlineReportOverviewTrendDTO point : bizPoints) {
                fieldMerge(fields, tmpPoint, point);
                tmpPoint = point;
            }
        } else {
            if (onlineReportTrendRequest.getQueryType() == QueryReportTypeionEnum.numberOfTrips) {
                if (StringUtils.isBlank(reqDim)) {
                    // bizPoints = onlineReportOverviewTrendDao.queryCurrentOverviewTripTrend(onlineTrendRequestDto);
                    bizPoints = onlineReportOverviewTrendDao.queryCurrentOverviewTripTrendOrderNum(onlineTrendRequestDto);
                } else {
                    // bizPoints = onlineReportOverviewTrendDao.queryOnlineReportCurrentOverviewTrend(onlineTrendRequestDto);
                    bizPoints = onlineReportOverviewTrendDao.queryOnlineReportCurrentOverviewTrendOrderNum(onlineTrendRequestDto);
                }
//                bizPoints = onlineReportOverviewTrendDao.queryOnlineReportCurrentOverviewTrend(onlineTrendRequestDto);
            }
            if (onlineReportTrendRequest.getQueryType() == QueryReportTypeionEnum.amount) {
                bizPoints = onlineReportOverviewTrendDao.queryOnlineReportCurrentOverviewTrend(onlineTrendRequestDto);
            }
        }
        for (OnlineReportOverviewTrendDTO onlineReportTrendDTO : bizPoints) {
            String date = onlineReportTrendDTO.getDate();
            String dim = onlineReportTrendDTO.getDim();
            OnlineReportTrendPoint point = points.getOrDefault(date, new OnlineReportTrendPoint(date, new HashMap<>()));
            fields.forEach(f -> point.data.put(dim + f.getName(), field2Value(onlineReportTrendDTO, f)));
            points.put(date, point);
        }

        // // 非百分比 取整
        List<Field> intFields = Arrays.stream(OnlineReportOverviewTrendDTO.class.getDeclaredFields())
                .filter(f -> !(f.getName().equals("date") || f.getName().equals("dim")))
                .filter(f -> !(f.getName().toLowerCase().contains("yoy") || f.getName().toLowerCase().contains("chain")))
                .collect(Collectors.toList());
        for (OnlineReportOverviewTrendDTO onlineReportTrendDTO : bizPoints) {
            String date = onlineReportTrendDTO.getDate();
            String dim = onlineReportTrendDTO.getDim();
            OnlineReportTrendPoint point = points.getOrDefault(date, new OnlineReportTrendPoint(date, new HashMap<>()));
            intFields.forEach(f -> point.data.put(dim + f.getName(), round2(field2Value(onlineReportTrendDTO, f))));
            points.put(date, point);
        }

        // 填充空缺日期
        String dim = "";

        for (OnlineReportOverviewTrendDTO dto : blankBizPoints) {
            if (!points.containsKey(dto.getDate())) {
                HashMap<String, BigDecimal> pointData = new HashMap<>();
                fields.forEach(f -> pointData.put(dim + f.getName(), new BigDecimal("0.00")));
                points.put(dto.getDate(), new OnlineReportTrendPoint(dto.getDate(), pointData));
            }
        }

        // 按日期排序输出
        List<OnlineReportTrendPoint> resultPoints = new ArrayList<>(points.values());
        resultPoints.sort((o1, o2) -> (int) OrpDateTimeUtils.betweenDay(o2.axis, o1.axis));
        resultPoints.forEach(point ->
                point.axis = dateFormat(point.axis, onlineReportTrendRequest.getDateDimension()));


        // 去除首尾的同环比
        if (needStartRemoveYoyChain(onlineReportTrendRequest.getBasecondition().getStartTime(),
                OrpConstants.START, onlineReportTrendRequest.getDateDimension())) {
            OnlineReportTrendPoint tmp = resultPoints.remove(0);
            resultPoints.add(0, removeYoyChain(tmp));
        }

        if (needStartRemoveYoyChain(onlineReportTrendRequest.getBasecondition().getEndTime(),
                OrpConstants.END, onlineReportTrendRequest.getDateDimension())) {
            OnlineReportTrendPoint tmp = resultPoints.remove(resultPoints.size() - 1);
            resultPoints.add(removeYoyChain(tmp));
        }
        // 周转换成按时间区间显示的形式，如：2023-01-02-2023-01-08
        weekInterval(resultPoints, onlineReportTrendRequest.getBasecondition().getStartTime(),
                onlineReportTrendRequest.getBasecondition().getEndTime(), onlineReportTrendRequest.getDateDimension());

        return resultPoints;
    }

    public List<OnlineReportTrendPoint> trendBodyJP(OnlineReportTrendRequest onlineReportTrendRequest) throws Exception {
        OnlineTrendRequestDto onlineTrendRequestDto = map(onlineReportTrendRequest, baseQueryConditionMapper);
        HashMap<String, OnlineReportTrendPoint> points = new HashMap<>();
        boolean amountDecimalPlacesZero = OrpReportUtils.amountDecimalPlacesZero(onlineReportTrendRequest.getBasecondition().getCurrency(),
                onlineReportTrendRequest.getBasecondition().getPos());
        List<Field> fields = Arrays.stream(OnlineReportOverviewTrendJPDTO.class.getDeclaredFields())
                .filter(f -> !(f.getName().equals("date") || f.getName().equals("dim")))
                .collect(Collectors.toList());
        List<OnlineReportOverviewTrendJPDTO> bizPoints = new ArrayList<>();
        List<OnlineReportOverviewTrendJPDTO> blankBizPoints = blankJPResults(onlineReportTrendRequest);

        String reqDim = onlineReportTrendRequest.getExtData().getOrDefault("dim", "");

        if (onlineReportTrendRequest.aggType == QueryReportAggTypeEnum.accumulated) {
            // 概览 当选择行程数 且选择整体时, 取一次行程的数据
            if (onlineReportTrendRequest.getQueryType() == QueryReportTypeionEnum.numberOfTrips) {
                if (StringUtils.isBlank(reqDim)) {
//                    bizPoints = onlineReportOverviewTrendDao.queryAccJPOverviewTripTrend(onlineTrendRequestDto);
                    bizPoints = onlineReportOverviewTrendDao.queryAccJPOverviewTripTrendOrderNum(onlineTrendRequestDto);
                } else {
//                    bizPoints = onlineReportOverviewTrendDao.queryOnlineReportAccJPOverviewTrend(onlineTrendRequestDto);
                    bizPoints = onlineReportOverviewTrendDao.queryOnlineReportAccJPOverviewTrendOrderNum(onlineTrendRequestDto);
                }
//                bizPoints = onlineReportOverviewTrendDao.queryOnlineReportAccJPOverviewTrend(onlineTrendRequestDto);
            }
            if (onlineReportTrendRequest.getQueryType() == QueryReportTypeionEnum.amount) {
                bizPoints = onlineReportOverviewTrendDao.queryOnlineReportAccJPOverviewTrend(onlineTrendRequestDto);
            }
            bizPoints.addAll(blankBizPoints);
            bizPoints.sort((o1, o2) -> (int) OrpDateTimeUtils.betweenDay(o2.getDate(), o1.getDate()));
            OnlineReportOverviewTrendJPDTO tmpPoint = null;
            for (OnlineReportOverviewTrendJPDTO point : bizPoints) {
                fieldMerge(fields, tmpPoint, point);
                tmpPoint = point;
            }
        } else {
            if (onlineReportTrendRequest.getQueryType() == QueryReportTypeionEnum.numberOfTrips) {
                if (StringUtils.isBlank(reqDim)) {
//                    bizPoints = onlineReportOverviewTrendDao.queryCurrentJPOverviewTripTrend(onlineTrendRequestDto);
                    bizPoints = onlineReportOverviewTrendDao.queryCurrentJPOverviewTripTrendOrderNum(onlineTrendRequestDto);
                } else {
//                    bizPoints = onlineReportOverviewTrendDao.queryOnlineReportCurrentJPOverviewTrend(onlineTrendRequestDto);
                    bizPoints = onlineReportOverviewTrendDao.queryOnlineReportCurrentJPOverviewTrendOrderNum(onlineTrendRequestDto);
                }
//                bizPoints = onlineReportOverviewTrendDao.queryOnlineReportCurrentJPOverviewTrend(onlineTrendRequestDto);
            }
            if (onlineReportTrendRequest.getQueryType() == QueryReportTypeionEnum.amount) {
                bizPoints = onlineReportOverviewTrendDao.queryOnlineReportCurrentJPOverviewTrend(onlineTrendRequestDto);
            }
        }
        for (OnlineReportOverviewTrendJPDTO onlineReportTrendDTO : bizPoints) {
            String date = onlineReportTrendDTO.getDate();
            String dim = onlineReportTrendDTO.getDim();
            OnlineReportTrendPoint point = points.getOrDefault(date, new OnlineReportTrendPoint(date, new HashMap<>()));
            fields.forEach(f -> point.data.put(dim + f.getName(), field2Value(onlineReportTrendDTO, f)));
            points.put(date, point);
        }

        // // 非百分比 取整
        List<Field> intFields = Arrays.stream(OnlineReportOverviewTrendJPDTO.class.getDeclaredFields())
                .filter(f -> !(f.getName().equals("date") || f.getName().equals("dim")))
                .filter(f -> !(f.getName().toLowerCase().contains("yoy") || f.getName().toLowerCase().contains("chain")))
                .collect(Collectors.toList());
        for (OnlineReportOverviewTrendJPDTO onlineReportTrendDTO : bizPoints) {
            String date = onlineReportTrendDTO.getDate();
            String dim = onlineReportTrendDTO.getDim();
            OnlineReportTrendPoint point = points.getOrDefault(date, new OnlineReportTrendPoint(date, new HashMap<>()));
            if (amountDecimalPlacesZero) {
                intFields.forEach(f -> point.data.put(dim + f.getName(), round2JP(field2Value(onlineReportTrendDTO, f))));
            } else {
                intFields.forEach(f -> point.data.put(dim + f.getName(), round2(field2Value(onlineReportTrendDTO, f))));
            }
            points.put(date, point);
        }

        // 填充空缺日期
        String dim = "";

        for (OnlineReportOverviewTrendJPDTO dto : blankBizPoints) {
            if (!points.containsKey(dto.getDate())) {
                HashMap<String, BigDecimal> pointData = new HashMap<>();
                if (amountDecimalPlacesZero) {
                    fields.forEach(f -> pointData.put(dim + f.getName(), new BigDecimal("0")));
                } else {
                    fields.forEach(f -> pointData.put(dim + f.getName(), new BigDecimal("0.00")));
                }
                points.put(dto.getDate(), new OnlineReportTrendPoint(dto.getDate(), pointData));
            }
        }

        // 按日期排序输出
        List<OnlineReportTrendPoint> resultPoints = new ArrayList<>(points.values());
        resultPoints.sort((o1, o2) -> (int) OrpDateTimeUtils.betweenDay(o2.axis, o1.axis));
        resultPoints.forEach(point ->
                point.axis = dateFormat(point.axis, onlineReportTrendRequest.getDateDimension()));

        // 去除首尾的同环比
        if (needStartRemoveYoyChain(onlineReportTrendRequest.getBasecondition().getStartTime(),
                OrpConstants.START, onlineReportTrendRequest.getDateDimension())) {
            OnlineReportTrendPoint tmp = resultPoints.remove(0);
            resultPoints.add(0, removeYoyChain(tmp));
        }

        if (needStartRemoveYoyChain(onlineReportTrendRequest.getBasecondition().getEndTime(),
                OrpConstants.END, onlineReportTrendRequest.getDateDimension())) {
            OnlineReportTrendPoint tmp = resultPoints.remove(resultPoints.size() - 1);
            resultPoints.add(removeYoyChain(tmp));
        }

        // 周转换成按时间区间显示的形式，如：2023-01-02-2023-01-08
        weekInterval(resultPoints, onlineReportTrendRequest.getBasecondition().getStartTime(),
                onlineReportTrendRequest.getBasecondition().getEndTime(), onlineReportTrendRequest.getDateDimension());

        return resultPoints;
    }

    @Override
    public List<OnlineReportTrendLegend> trendLegend(OnlineReportTrendRequest onlineReportTrendRequest) throws Exception {
        List<OnlineTrendFieldEnum> bizLegends;
        String dim = onlineReportTrendRequest.getExtData().getOrDefault("dim", "");
        String lang = onlineReportTrendRequest.getExtData().getOrDefault("lang", "");
        BaseQueryCondition baseQueryCondition = onlineReportTrendRequest.getBasecondition();
        if (onlineReportTrendRequest.getQueryType() == QueryReportTypeionEnum.amount) {
            if (BlueSpaceUtils.isForeign(baseQueryCondition.getPos(), baseQueryCondition.getBlueSpace())) {
                bizLegends = ReportTrendHeaderEnum.OVERVIEW_BU_JP_AMOUNT_HEADER.getHeaderList();
            } else {
                bizLegends = ReportTrendHeaderEnum.OVERVIEW_BU_AMOUNT_HEADER.getHeaderList();
            }
            if (StringUtils.isBlank(dim)) {
                bizLegends = ReportTrendHeaderEnum.OVERVIEW_AMOUNT_HEADER.getHeaderList();
            }
        } else {
            if (BlueSpaceUtils.isForeign(baseQueryCondition.getPos(), baseQueryCondition.getBlueSpace())) {
                bizLegends = ReportTrendHeaderEnum.OVERVIEW_BU_JP_ORDER_NUM_HEADER.getHeaderList();
            } else {
                bizLegends = ReportTrendHeaderEnum.OVERVIEW_BU_ORDER_NUM_HEADER.getHeaderList();
            }
            if (StringUtils.isBlank(dim)) {
                bizLegends = ReportTrendHeaderEnum.OVERVIEW_ORDER_NUM_HEADER.getHeaderList();
            }
        }

        HashMap<String, OnlineReportTrendLegend> legends = new LinkedHashMap<>();
        for (OnlineTrendFieldEnum bizLegend : bizLegends) {
            OnlineReportTrendLegend legend = legends.getOrDefault(bizLegend.getName(), new OnlineReportTrendLegend());
            legend.setName(SharkUtils.getHeaderVal(bizLegend.getNameKey(), lang));
            if (BlueSpaceUtils.isForeign(baseQueryCondition.getPos(), baseQueryCondition.getBlueSpace())) {
                if (StringUtils.equalsIgnoreCase(bizLegend.getUnitKey(), "Unit.Yuan")) {
                    if (StringUtils.equalsIgnoreCase(onlineReportTrendRequest.getBasecondition().getPos(), OrpConstants.POS_JP)) {
                        legend.setUnit(SharkUtils.getHeaderVal("Unit.JPY", lang));
                    }
                    if (StringUtils.equalsIgnoreCase(onlineReportTrendRequest.getBasecondition().getPos(), OrpConstants.POS_HK)) {
                        legend.setUnit(SharkUtils.getHeaderVal("Unit.HKD", lang));
                    }
                } else {
                    legend.setUnit(SharkUtils.getHeaderVal(bizLegend.getUnitKey(), lang));
                }
            } else {
                legend.setUnit(SharkUtils.getHeaderVal(bizLegend.getUnitKey(), lang));
            }
            switch (bizLegend.getValueType()) {
                case "key":
                    legend.setKey(bizLegend.getValueName());
                    break;
                case "yoy":
                    legend.setYoy(bizLegend.getValueName());
                    break;
                case "chain":
                    legend.setChain(bizLegend.getValueName());
                    break;
                default:
                    break;
            }
            legends.put(bizLegend.getName(), legend);
        }
        return new ArrayList<>(legends.values());
    }

    private List<OnlineReportOverviewTrendDTO> blankResults(OnlineReportTrendRequest request) {
        List<String> allDate = getDateRangeFullItem(
                request.getBasecondition().startTime, request.getBasecondition().endTime, request.dateDimension
        );
        return allDate.stream().map(date -> {
            OnlineReportOverviewTrendDTO dto = new OnlineReportOverviewTrendDTO();
            dto.setDate(date);
            dto.setDim("");
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 日本站
     *
     * @param request
     * @return
     */
    private List<OnlineReportOverviewTrendJPDTO> blankJPResults(OnlineReportTrendRequest request) {
        List<String> allDate = getDateRangeFullItem(
                request.getBasecondition().startTime, request.getBasecondition().endTime, request.dateDimension
        );
        return allDate.stream().map(date -> {
            OnlineReportOverviewTrendJPDTO dto = new OnlineReportOverviewTrendJPDTO();
            dto.setDate(date);
            dto.setDim("");
            return dto;
        }).collect(Collectors.toList());
    }

    private void fieldMerge(List<Field> fields, Object pre, Object cur) {
        if (pre == null || cur == null) {
            return;
        }
        for (Field field : fields) {
            try {
                field.set(cur, field2Value(pre, field).add(field2Value(cur, field)));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum s) {
        return QueryReportBuTypeEnum.overview == s;
    }
}
