package com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 11:02
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@NoArgsConstructor
public class OnlineReportSaveProportionDetailRequestDTO {
    QueryReportBuTypeEnum bu;
    String lang;
    List<String> industries;
    BaseQueryConditionDTO baseQueryCondition;
    String productType;
}
