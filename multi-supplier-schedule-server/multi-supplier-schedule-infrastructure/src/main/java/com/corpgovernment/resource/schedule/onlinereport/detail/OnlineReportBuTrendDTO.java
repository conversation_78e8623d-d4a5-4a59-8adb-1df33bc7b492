package com.corpgovernment.resource.schedule.onlinereport.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/10 17:20
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class OnlineReportBuTrendDTO {

    @Column(name = "date")
    @Type(value = Types.VARCHAR)
    private String date;

    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;

    @Column(name = "amount")
    @Type(value = Types.DECIMAL)
    private BigDecimal amount;

    @Column(name = "amountChain")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountChain;

    @Column(name = "amountYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal amountYoy;

    @Column(name = "quantity")
    @Type(value = Types.DECIMAL)
    private BigDecimal quantity;

    @Column(name = "quantityChain")
    @Type(value = Types.DECIMAL)
    private BigDecimal quantityChain;

    @Column(name = "quantityYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal quantityYoy;
}
