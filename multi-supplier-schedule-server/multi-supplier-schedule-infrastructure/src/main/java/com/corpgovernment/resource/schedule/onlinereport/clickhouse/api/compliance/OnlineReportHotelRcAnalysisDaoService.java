package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTimesDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.HotelRcTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @className OnlineReportHotelRcAnalysisDaoService
 * @date 2024/9/27
 */
public interface OnlineReportHotelRcAnalysisDaoService extends OnlineReportTopRcAnalysisDaoService {

    List<RcTimesDTO> aggreationRcView(BaseQueryConditionDTO requestDto, String hotelType, String rcTypeFiled)
            throws Exception;

    List<RcTimesDTO> aggreationRcViewCorpAndIndustry(String startTime, String endTime, String hotelType, String rcTypeFiled
            , List<String> industryList, DataTypeEnum dataTypeEnum, String compareSameLevel, String consumptionLevel, String compareCorpSameLevel)
            throws Exception;

    <T> List<T> aggreationRcViewReason(BaseQueryConditionDTO requestDto, Class<T> clazz, String hotelType)
            throws Exception;

    <T> List<T> aggreationRcViewReasonDetail(BaseQueryConditionDTO requestDto, Class<T> clazz, String hotelType,
                                             HotelRcTypeEnum hotelRcEnum, String lang) throws Exception;

    List<RcTrendDTO> aggreationRcTrend(BaseQueryConditionDTO requestDto, String hotelType, QueryReportAggDateDimensionEnum dateDimensionEnum) throws Exception;

    List<RcTrendDTO> aggreationRcTrendCorpAndIndustry(String startTime, String endTime, String hotelType, QueryReportAggDateDimensionEnum dateDimensionEnum,
                                                      List<String> industryList, DataTypeEnum dataTypeEnum,
                                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

}
