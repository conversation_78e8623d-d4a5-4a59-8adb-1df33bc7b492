package com.corpgovernment.resource.schedule.onlinereport.consume.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.GenralConsume;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.OnlineReportConsumeBO;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportConsumeDao;
import com.corpgovernment.resource.schedule.onlinereport.consume.AbstractGenralConsume;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Service
public class OverViewGenralBiz extends AbstractGenralConsume {
    @Autowired
    private OnlineReportConsumeDao reportConsumeDao;

    @Override
    protected BigDecimal getTotalAmount1(OnlineReportConsumeBO genralConsumeCurrent) {
        return getTotalAmount3(genralConsumeCurrent);
    }

    @Override
    protected Integer getTotalQuantity1(OnlineReportConsumeBO genralConsumeCurrent) {
        return getTotalQuantity3(genralConsumeCurrent);
    }

    @Override
    protected BigDecimal getTotalAmount3(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneAmount())
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalTwoAmount()))
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalThreeAmount()))
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFourAmount()))
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFiveAmount()))
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalSixAmount()));
    }

    @Override
    protected Integer getTotalQuantity3(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneQuantity())
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalTwoQuantity()))
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalThreeQuantity()))
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFourQuantity()))
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFiveQuantity()))
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalSixQuantity()));
    }

    @Override
    protected OnlineReportConsumeBO aggreationGenralConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        List<GenralConsume> aggreationConsumeDtoList;
        aggreationConsumeDtoList = (List<GenralConsume>) reportConsumeDao.aggreationWithConditionOverview(baseQueryConditionDto, GenralConsume.class);
        //int currentTotalQuantity = reportConsumeDao.aggreationOverview(baseQueryConditionDto);

        //return convert(aggreationConsumeDtoList, currentTotalQuantity);
        return convert(aggreationConsumeDtoList, 0);
    }

    private void setTotalQuantity(GenralConsume genralConsume) {
        Integer totalQuantity = 0;
        totalQuantity += genralConsume.getTotalFlightTicketQuantity() == null ? 0 : genralConsume.getTotalFlightTicketQuantity();
        totalQuantity += genralConsume.getTotalHotelRoomNight() == null ? 0 : genralConsume.getTotalHotelRoomNight();
        totalQuantity += genralConsume.getTotalTrainTicketQuantity() == null ? 0 : genralConsume.getTotalTrainTicketQuantity();
        totalQuantity += genralConsume.getTotalCarOrderQuantity() == null ? 0 : genralConsume.getTotalCarOrderQuantity();
        totalQuantity += genralConsume.getTotalBusTicketQuantity() == null ? 0 : genralConsume.getTotalBusTicketQuantity();
        totalQuantity += genralConsume.getTotalAddOrderQuantity() == null ? 0 : genralConsume.getTotalAddOrderQuantity();
        genralConsume.setTotalQuantity(totalQuantity);
    }

    @Override
    protected OnlineReportConsumeBO aggreationCurrentConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        List<GenralConsume> aggreationConsumeDtoList;
        aggreationConsumeDtoList = (List<GenralConsume>) reportConsumeDao.aggreationWithConditionOverview(baseQueryConditionDto, GenralConsume.class);

        //int currentTotalQuantity = reportConsumeDao.aggreationOverview(baseQueryConditionDto);
        //OnlineReportConsumeBO genralConsume = convert(aggreationConsumeDtoList, currentTotalQuantity);
        OnlineReportConsumeBO genralConsume = convert(aggreationConsumeDtoList, 0);
        // 商旅服务费
        genralConsume.setTotalCorpServiceFee(OrpReportUtils.formatBigDecimal(new BigDecimal(aggreationConsumeDtoList.stream()
                .mapToDouble(i -> Optional.ofNullable(i.getTotalCorpServiceFee()).orElse(0d)).sum())));
        return genralConsume;
    }

    private OnlineReportConsumeBO convert(List<GenralConsume> aggreationConsumeDtoList, int currentTotalQuantity) {
        OnlineReportConsumeBO genralConsume = new OnlineReportConsumeBO();
        double currentTotalAmount = 0;
        if (CollectionUtils.isNotEmpty(aggreationConsumeDtoList)) {
            GenralConsume genralAggreationConsumeDto = aggreationConsumeDtoList.get(0);
            currentTotalAmount = handlerDouble(genralAggreationConsumeDto.getTotalAmount());
            // 机票
            genralConsume.setTotalOneAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(handlerDouble(genralAggreationConsumeDto.getTotalFlightAmount()))));
            genralConsume.setTotalOneQuantity(handlerInteger(genralAggreationConsumeDto.getTotalFlightTicketQuantity()));
            // 火车
            genralConsume.setTotalTwoAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(handlerDouble(genralAggreationConsumeDto.getTotalTrainAmount()))));
            genralConsume.setTotalTwoQuantity(handlerInteger(genralAggreationConsumeDto.getTotalTrainTicketQuantity()));
            // 酒店
            genralConsume.setTotalThreeAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(handlerDouble(genralAggreationConsumeDto.getTotalHotelAmount()))));
            genralConsume.setTotalThreeQuantity(handlerInteger(genralAggreationConsumeDto.getTotalHotelRoomNight()));
            // 用车
            genralConsume.setTotalFourAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(handlerDouble(genralAggreationConsumeDto.getTotalCarAmount()))));
            genralConsume.setTotalFourQuantity(handlerInteger(genralAggreationConsumeDto.getTotalCarOrderQuantity()));
            // 汽车
            genralConsume.setTotalFiveAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(handlerDouble(genralAggreationConsumeDto.getTotalBusAmount()))));
            genralConsume.setTotalFiveQuantity(handlerInteger(genralAggreationConsumeDto.getTotalBusTicketQuantity()));
            // 增值
            genralConsume.setTotalSixAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(handlerDouble(genralAggreationConsumeDto.getTotalAddAmount()))));
            genralConsume.setTotalSixQuantity(handlerInteger(genralAggreationConsumeDto.getTotalAddOrderQuantity()));

        }
        genralConsume.setTotalAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(currentTotalAmount)));
        genralConsume.setTotalQuantity(currentTotalQuantity);
        return genralConsume;
    }

    public Double handlerDouble(Double value) {
        return value == null ? 0 : value;
    }

    public Integer handlerInteger(Integer value) {
        return value == null ? 0 : value;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum s) {
        return QueryReportBuTypeEnum.overview == s || s == null;
    }
}
