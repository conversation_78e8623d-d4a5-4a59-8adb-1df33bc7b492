package com.corpgovernment.resource.schedule.onlinereport.module.repotlib;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-10-13 11:26
 * @desc
 */

@Data
public class HotelOrderAuditDTO {

    // 订单号
    @Column(name = "orderid")
    @Type(value = Types.BIGINT)
    private Long orderId;

    // 订单状态
    @Column(name = "order_status")
    @Type(value = Types.VARCHAR)
    private String orderStatus;

    // 公司ID
    @Column(name = "corp_corporation")
    @Type(value = Types.VARCHAR)
    private String corpCorporation;

    // 公司名称
    @Column(name = "corp_name")
    @Type(value = Types.VARCHAR)
    private String corpName;

    // 订单预定时间
    @Column(name = "order_date")
    @Type(value = Types.VARCHAR)
    private String orderDate;

    // 省份
    @Column(name = "province_name")
    @Type(value = Types.VARCHAR)
    private String provincename;

    // 省份
    @Column(name = "province_name_en")
    @Type(value = Types.VARCHAR)
    private String provincenameEn;

    // 城市
    @Column(name = "city_name")
    @Type(value = Types.VARCHAR)
    private String cityname;

    // 城市
    @Column(name = "city_name_en")
    @Type(value = Types.VARCHAR)
    private String citynameEn;

    // 酒店集团
    @Column(name = "hotel_group_name")
    @Type(value = Types.VARCHAR)
    private String groupname;

    // 酒店集团
    @Column(name = "hotel_group_name_en")
    @Type(value = Types.VARCHAR)
    private String groupnameEn;

    // 酒店名称
    @Column(name = "hotel_name")
    @Type(value = Types.VARCHAR)
    private String hotelname;

    // 酒店名称
    @Column(name = "hotel_name_en")
    @Type(value = Types.VARCHAR)
    private String hotelnameEn;

    // 产品类型
    @Column(name = "producttype_all")
    @Type(value = Types.VARCHAR)
    private String producttype;

    // 支付类型
    @Column(name = "balancetypename")
    @Type(value = Types.VARCHAR)
    private String balancetypename;

    // 预订入住时间，订单号粒度
    @Column(name = "arrival_date_time")
    @Type(value = Types.VARCHAR)
    private String arrival;

    // 预订离店时间，订单号粒度
    @Column(name = "departure_date_time")
    @Type(value = Types.VARCHAR)
    private String departure;

    // 预订间夜数，订单号粒度
    @Column(name = "order_quantity")
    @Type(value = Types.INTEGER)
    private Integer quantity;

    // 预订订单金额（包含前收商旅管理服务费），订单号粒度
    @Column(name = "order_amount")
    @Type(value = Types.DECIMAL)
    private BigDecimal amount;

    // 是否已审核，订单号+房间数粒度
    @Column(name = "is_audit")
    @Type(value = Types.VARCHAR)
    private String isAudit;

    // 房间类型（正常/钟点房），订单号+房间数粒度
    @Column(name = "room_type")
    @Type(value = Types.VARCHAR)
    private String roomType;

    // 房间号，订单号+房间数粒度
    @Column(name = "room_no")
    @Type(value = Types.VARCHAR)
    private String roomNo;

    // 审核状态，订单号+房间数粒度
    @Column(name = "audit_status")
    @Type(value = Types.VARCHAR)
    private String auditStatus;

    // 审核入住人姓名，订单号+房间数粒度
    @Column(name = "audit_client_name")
    @Type(value = Types.VARCHAR)
    private String auditClientName;

    // 审核入住日期，订单号+房间数粒度
    @Column(name = "audit_checkin_date")
    @Type(value = Types.VARCHAR)
    private String auditCheckinDate;

    // 审核离店日期，订单号+房间数粒度
    @Column(name = "audit_checkout_date")
    @Type(value = Types.VARCHAR)
    private String auditCheckoutDate;

    // 审核成交间夜，订单号+房间数粒度
    @Column(name = "audit_quantity")
    @Type(value = Types.INTEGER)
    private Integer auditQuantity;

    // 客户修改后的实际入住时间，订单号粒度
    @Column(name = "realeta")
    @Type(value = Types.VARCHAR)
    private String realeta;

    // 客户修改后的实际离店时间，订单号粒度
    @Column(name = "realetd")
    @Type(value = Types.VARCHAR)
    private String realetd;

    // 夜审退款间夜量
    @Column(name = "audit_refund_quantity")
    @Type(value = Types.INTEGER)
    private Integer auditRefundQuantity;

    // 夜审退款金额
    @Column(name = "audit_refund_amount")
    @Type(value = Types.DECIMAL)
    private BigDecimal auditRefundAmount;

    // 房间类型（正常/钟点房），订单号+房间数粒度
    @Column(name = "room_type_code")
    @Type(value = Types.INTEGER)
    private Integer roomTypeCode;
}
