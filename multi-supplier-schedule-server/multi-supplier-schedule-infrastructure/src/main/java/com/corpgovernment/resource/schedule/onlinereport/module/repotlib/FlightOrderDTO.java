package com.corpgovernment.resource.schedule.onlinereport.module.repotlib;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-10-13 11:26
 * @desc
 */

@Data
public class FlightOrderDTO {

    // 订单号
    @Column(name = "order_id")
    @Type(value = Types.BIGINT)
    private Long orderId;

    // 订单状态
    @Column(name = "order_status")
    @Type(value = Types.VARCHAR)
    private String orderStatus;

    // 订单预定时间
    @Column(name = "order_date")
    @Type(value = Types.VARCHAR)
    private String orderDate;

    // 公司ID
    @Column(name = "corp_corporation")
    @Type(value = Types.VARCHAR)
    private String corpCorporation;

    // 公司名称
    @Column(name = "corp_name")
    @Type(value = Types.VARCHAR)
    private String corpName;

    // 公司集团ID
    @Column(name = "companygroupid")
    @Type(value = Types.VARCHAR)
    private String companygroupid;

    // 公司集团
    @Column(name = "companygroup")
    @Type(value = Types.VARCHAR)
    private String companygroup;

    // 主账户账号
    @Column(name = "account_id")
    @Type(value = Types.BIGINT)
    private Long accountId;

    // 主账户代号
    @Column(name = "account_code")
    @Type(value = Types.VARCHAR)
    private String accountCode;

    // 主账户公司名称
    @Column(name = "account_name")
    @Type(value = Types.VARCHAR)
    private String accountName;

    // 子账户账号
    @Column(name = "sub_account_id")
    @Type(value = Types.BIGINT)
    private Long subAccountId;

    // 子账户代号
    @Column(name = "sub_account_code")
    @Type(value = Types.VARCHAR)
    private String subAccountCode;

    // 子账户公司名称
    @Column(name = "sub_account_name")
    @Type(value = Types.VARCHAR)
    private String subAccountName;

    // 行业类型
    @Column(name = "industry_type")
    @Type(value = Types.VARCHAR)
    private String industryType;

    // 行业名称
    @Column(name = "industry_type_name")
    @Type(value = Types.VARCHAR)
    private String industryTypeName;

    // 持卡人卡号
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    // 持卡人姓名
    @Column(name = "user_name")
    @Type(value = Types.VARCHAR)
    private String userName;

    // 持卡人员工编号
    @Column(name = "employe_id")
    @Type(value = Types.VARCHAR)
    private String employeId;

    // 工作所在城市
    @Column(name = "work_city")
    @Type(value = Types.VARCHAR)
    private String workCity;

    // 持卡人职级CN
    @Column(name = "rank_name")
    @Type(value = Types.VARCHAR)
    private String rankName;

    // 成本中心1
    @Column(name = "cost_center1")
    @Type(value = Types.VARCHAR)
    private String costCenter1;

    // 成本中心2
    @Column(name = "cost_center2")
    @Type(value = Types.VARCHAR)
    private String costCenter2;

    // 成本中心3
    @Column(name = "cost_center3")
    @Type(value = Types.VARCHAR)
    private String costCenter3;

    // 成本中心4
    @Column(name = "cost_center4")
    @Type(value = Types.VARCHAR)
    private String costCenter4;

    // 成本中心5
    @Column(name = "cost_center5")
    @Type(value = Types.VARCHAR)
    private String costCenter5;

    // 成本中心6
    @Column(name = "cost_center6")
    @Type(value = Types.VARCHAR)
    private String costCenter6;

    // 部门1
    @Column(name = "dept1")
    @Type(value = Types.VARCHAR)
    private String dept1;

    // 部门2
    @Column(name = "dept2")
    @Type(value = Types.VARCHAR)
    private String dept2;

    // 部门3
    @Column(name = "dept3")
    @Type(value = Types.VARCHAR)
    private String dept3;

    // 部门4
    @Column(name = "dept4")
    @Type(value = Types.VARCHAR)
    private String dept4;

    // 部门5
    @Column(name = "dept5")
    @Type(value = Types.VARCHAR)
    private String dept5;
    // 部门6
    @Column(name = "dept6")
    @Type(value = Types.VARCHAR)
    private String dept6;
    // 部门7
    @Column(name = "dept7")
    @Type(value = Types.VARCHAR)
    private String dept7;
    // 部门8
    @Column(name = "dept8")
    @Type(value = Types.VARCHAR)
    private String dept8;
    // 部门9
    @Column(name = "dept9")
    @Type(value = Types.VARCHAR)
    private String dept9;
    // 部门10
    @Column(name = "dept10")
    @Type(value = Types.VARCHAR)
    private String dept10;
    // 是否个人消费行为:因公,因私,
    @Column(name = "fee_type")
    @Type(value = Types.VARCHAR)
    private String feeType;
    // 预订方式
    @Column(name = "is_online")
    @Type(value = Types.VARCHAR)
    private String isOnline;
    // 支付方式
    @Column(name = "prepay_type")
    @Type(value = Types.VARCHAR)
    private String prepayType;
    // 结算类型
    @Column(name = "acb_prepay_type")
    @Type(value = Types.VARCHAR)
    private String acbPrepayType;
    // 是否BOSS(T/F)
    @Column(name = "bosstype")
    @Type(value = Types.VARCHAR)
    private String bosstype;
    // 所属行程订单号
    @Column(name = "trip_id")
    @Type(value = Types.BIGINT)
    private Long tripId;
    // 关联行程单号
    @Column(name = "journey_no")
    @Type(value = Types.VARCHAR)
    private String journeyNo;
    // 出行目的
    @Column(name = "journey_reason")
    @Type(value = Types.VARCHAR)
    private String journeyReason;
    // 出行目的编号
    @Column(name = "journey_reason_code")
    @Type(value = Types.VARCHAR)
    private String journeyReasonCode;
    // 项目编号
    @Column(name = "project_code")
    @Type(value = Types.VARCHAR)
    private String projectCode;
    // 项目名称
    @Column(name = "project")
    @Type(value = Types.VARCHAR)
    private String project;
    // 是否口头授权T/F
    @Column(name = "verbal_authorize")
    @Type(value = Types.VARCHAR)
    private String verbalAuthorize;
    // 一次授权人姓名
    @Column(name = "confirm_person")
    @Type(value = Types.VARCHAR)
    private String confirmPerson;
    // 一次授权方式
    @Column(name = "confirm_type")
    @Type(value = Types.VARCHAR)
    private String confirmType;
    // 二次授权人姓名
    @Column(name = "confirm_person2")
    @Type(value = Types.VARCHAR)
    private String confirmPerson2;
    // 二次授权方式
    @Column(name = "confirm_type2")
    @Type(value = Types.VARCHAR)
    private String confirmType2;
    // 授权通过时间
    @Column(name = "approvalpasstime")
    @Type(value = Types.VARCHAR)
    private String approvalpasstime;
    // 授权结果
    @Column(name = "actionname")
    @Type(value = Types.VARCHAR)
    private String actionname;
    // 自定义成本中心
    @Column(name = "defineflag")
    @Type(value = Types.VARCHAR)
    private String defineflag;
    // 自定义成本中心2
    @Column(name = "defineflag2")
    @Type(value = Types.VARCHAR)
    private String defineflag2;
    // 一次授权时间
    @Column(name = "confirmtimepoint")
    @Type(value = Types.VARCHAR)
    private String confirmtimepoint;
    // 二次授权时间
    @Column(name = "confirmtimepoint2")
    @Type(value = Types.VARCHAR)
    private String confirmtimepoint2;
    // 一次授权人uid
    @Column(name = "AuditorID")
    @Type(value = Types.VARCHAR)
    private String auditorid;
    // 二次授权人uid
    @Column(name = "AuditorID2")
    @Type(value = Types.VARCHAR)
    private String auditorid2;
    // 年月
    @Column(name = "group_month")
    @Type(value = Types.INTEGER)
    private Integer groupMonth;
    // 出票、退票审核月份
    @Column(name = "print_month")
    @Type(value = Types.INTEGER)
    private Integer printMonth;
    // 出票年份
    @Column(name = "print_year")
    @Type(value = Types.INTEGER)
    private Integer printYear;
    // 出票时间
    @Column(name = "print_ticket_time")
    @Type(value = Types.VARCHAR)
    private String printTicketTime;
    // 开票类型
    @Column(name = "provide_bill_type")
    @Type(value = Types.VARCHAR)
    private String provideBillType;
    // 票张数
    @Column(name = "quantity")
    @Type(value = Types.INTEGER)
    private Integer quantity;
    // 全价票张数
    @Column(name = "fullfaretkt")
    @Type(value = Types.INTEGER)
    private Integer fullfaretkt;
    // 出票张数
    @Column(name = "ordertkt")
    @Type(value = Types.INTEGER)
    private Integer ordertkt;
    // 退票张数
    @Column(name = "refundtkt")
    @Type(value = Types.INTEGER)
    private Integer refundtkt;
    // 实收实付
    @Column(name = "real_pay")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPay;
    // 成交净价
    @Column(name = "price")
    @Type(value = Types.DECIMAL)
    private BigDecimal price;
    // 成交净价(不含改签价差)
    @Column(name = "netfare")
    @Type(value = Types.DECIMAL)
    private BigDecimal netfare;
    // 民航基金/税
    @Column(name = "tax")
    @Type(value = Types.DECIMAL)
    private BigDecimal tax;
    // 燃油税
    @Column(name = "oil_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal oilFee;
    // 基础服务费
    @Column(name = "service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal serviceFee;
    // 保险费
    @Column(name = "insurance_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal insuranceFee;
    // 绑酒优惠券金额
    @Column(name = "bind_amount")
    @Type(value = Types.DECIMAL)
    private BigDecimal bindAmount;
    // 改签费
    @Column(name = "change_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal changeFee;
    // 改签差价
    @Column(name = "rebook_price_differential")
    @Type(value = Types.DECIMAL)
    private BigDecimal rebookPriceDifferential;
    // 改签服务费
    @Column(name = "rebook_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal rebookServiceFee;
    // 退票费
    @Column(name = "refund_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal refundFee;
    // 退票服务费
    @Column(name = "refund_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal refundServiceFee;
    // 送票费
    @Column(name = "send_ticket_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal sendTicketFee;
    // 出票后收服务费
    @Column(name = "ticket_behind_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal ticketBehindServiceFee;
    // 改签后收服务费
    @Column(name = "rebook_behind_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal rebookBehindServiceFee;
    // 退票后收服务费
    @Column(name = "refund_behind_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal refundBehindServiceFee;
    // 全价/标准
    @Column(name = "std_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal stdPrice;
    // 票面价
    @Column(name = "print_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal printPrice;
    // 折扣
    @Column(name = "price_rate")
    @Type(value = Types.DECIMAL)
    private BigDecimal priceRate;
    // 最低价
    @Column(name = "corp_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal corpPrice;
    // 是否退票:T(是)/F(否)
    @Column(name = "is_refund")
    @Type(value = Types.VARCHAR)
    private String isRefund;
    // 退票原因
    @Column(name = "refund_reson_desc")
    @Type(value = Types.VARCHAR)
    private String refundResonDesc;
    // 是否改签:T(是)/F(否)
    @Column(name = "is_rebook")
    @Type(value = Types.VARCHAR)
    private String isRebook;
    // 改签时间
    @Column(name = "rebook_time")
    @Type(value = Types.VARCHAR)
    private String rebookTime;
    // 改签原因
    @Column(name = "rebook_reson_desc")
    @Type(value = Types.VARCHAR)
    private String rebookResonDesc;
    // 国际机票改签前订单号
    @Column(name = "original_order_id")
    @Type(value = Types.BIGINT)
    private Long originalOrderId;
    // 改签后航班号
    @Column(name = "change_flight_no")
    @Type(value = Types.VARCHAR)
    private String changeFlightNo;
    // 改签后起飞时间
    @Column(name = "change_takeoff_time")
    @Type(value = Types.VARCHAR)
    private String changeTakeoffTime;
    // 改签后到达时间
    @Column(name = "change_arrival_datetime")
    @Type(value = Types.VARCHAR)
    private String changeArrivalDatetime;
    // 产品类型分类
    @Column(name = "productcategory")
    @Type(value = Types.VARCHAR)
    private String productcategory;
    // 是否前返
    @Column(name = "bf_return")
    @Type(value = Types.VARCHAR)
    private String bfReturn;
    // 合并碳排量
    @Column(name = "carbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbons;
    // 协议类型
    @Column(name = "agreement_type_name")
    @Type(value = Types.VARCHAR)
    private String agreementTypeName;
    // 协议类型code
    @Column(name = "agreement_type")
    @Type(value = Types.VARCHAR)
    private String agreementType;
    // C（协议），NC（非协议）
    @Column(name = "contract_type")
    @Type(value = Types.VARCHAR)
    private String contractType;
    // 协航扣率
    @Column(name = "agreement_rate")
    @Type(value = Types.DECIMAL)
    private BigDecimal agreementRate;
    // 提前预订天数
    @Column(name = "pre_order_date")
    @Type(value = Types.INTEGER)
    private Integer preOrderDate;
    // 航程类型(S:单程,D:往返,M:联程)
    @Column(name = "flight_way")
    @Type(value = Types.VARCHAR)
    private String flightWay;
    // 航程类型描述
    @Column(name = "flight_way_desc")
    @Type(value = Types.VARCHAR)
    private String flightWayDesc;
    // 航班类型/产品类别国内国际I:国际，N：国内
    @Column(name = "flight_class")
    @Type(value = Types.VARCHAR)
    private String flightClass;
    // 国际航班类型,航程洲际(N:国内,A:州内,I:洲际)
    @Column(name = "flight_continent")
    @Type(value = Types.VARCHAR)
    private String flightContinent;
    // 配送方式名称
    @Column(name = "get_ticket_way_name")
    @Type(value = Types.VARCHAR)
    private String getTicketWayName;
    // 乘机人UID
    @Column(name = "passenger_uid")
    @Type(value = Types.VARCHAR)
    private String passengerUid;
    // 乘机人
    @Column(name = "passenger_name")
    @Type(value = Types.VARCHAR)
    private String passengerName;
    // 乘机人拼音
    @Column(name = "passenger_name_py")
    @Type(value = Types.VARCHAR)
    private String passengerNamePy;
    // 乘机人员工编号
    @Column(name = "passenger_no")
    @Type(value = Types.VARCHAR)
    private String passengerNo;
    // 机票类型
    @Column(name = "age_type")
    @Type(value = Types.VARCHAR)
    private String ageType;
    // 行程序号
    @Column(name = "sequence")
    @Type(value = Types.INTEGER)
    private Integer sequence;
    // 航班号
    @Column(name = "flight_no")
    @Type(value = Types.VARCHAR)
    private String flightNo;
    // 廉价航空航班标识(T/F)
    @Column(name = "low_airlines")
    @Type(value = Types.VARCHAR)
    private String lowAirlines;
    // 子舱位
    @Column(name = "sub_class")
    @Type(value = Types.VARCHAR)
    private String subClass;
    // 航段状态
    @Column(name = "flight_status")
    @Type(value = Types.VARCHAR)
    private String flightStatus;
    // 飞行时长
    @Column(name = "flight_time")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightTime;
    // 票号-航空公司票号(原始票号
    @Column(name = "original_ticket_no")
    @Type(value = Types.VARCHAR)
    private String originalTicketNo;
    // 航空公司二字码
    @Column(name = "airline")
    @Type(value = Types.VARCHAR)
    private String airline;
    // 航空公司CN
    @Column(name = "airline_cn_name")
    @Type(value = Types.VARCHAR)
    private String airlineCnName;
    // 航空公司票号
    @Column(name = "ticket_no")
    @Type(value = Types.VARCHAR)
    private String ticketNo;
    // 机票票号状态
    @Column(name = "ticket_status")
    @Type(value = Types.INTEGER)
    private Integer ticketStatus;
    // 机票票号风险级别票号风险级别(0.未知1.提示2.预警3.高风险4.正常)
    @Column(name = "ticket_warning_level")
    @Type(value = Types.VARCHAR)
    private String ticketWarningLevel;
    // Y(经济舱(包含全价经济舱、折扣经济舱、前返经济舱)),S(超级经济舱),F(头等舱),C(公务舱)
    @Column(name = "class_type")
    @Type(value = Types.VARCHAR)
    private String classType;
    // 物理舱位CN
    @Column(name = "real_class")
    @Type(value = Types.VARCHAR)
    private String realClass;
    // 航段(城市)三字码
    @Column(name = "flight_city_code")
    @Type(value = Types.VARCHAR)
    private String flightCityCode;
    // 航段
    @Column(name = "flight_city")
    @Type(value = Types.VARCHAR)
    private String flightCity;
    // 合并航程/航程
    @Column(name = "flight_city2")
    @Type(value = Types.VARCHAR)
    private String flightCity2;
    // 合并里程(公里)
    @Column(name = "tpms")
    @Type(value = Types.DECIMAL)
    private BigDecimal tpms;
    // 合并里程(英里)
    @Column(name = "tpms_en")
    @Type(value = Types.DECIMAL)
    private BigDecimal tpmsEn;
    // 起飞机场
    @Column(name = "departure_port_name")
    @Type(value = Types.VARCHAR)
    private String departurePortName;
    // 起飞机场三字码
    @Column(name = "dport_code")
    @Type(value = Types.VARCHAR)
    private String dportCode;
    // 起飞时间
    @Column(name = "takeoff_time")
    @Type(value = Types.VARCHAR)
    private String takeoffTime;
    // 出发城市ID
    @Column(name = "departure_city_id")
    @Type(value = Types.INTEGER)
    private Integer departureCityId;
    // 出发城市名称
    @Column(name = "departure_city_name")
    @Type(value = Types.VARCHAR)
    private String departureCityName;
    // 出发城市三字码,
    @Column(name = "departure_city_code")
    @Type(value = Types.VARCHAR)
    private String departureCityCode;
    // 出发城市大洲
    @Column(name = "departure_continent")
    @Type(value = Types.VARCHAR)
    private String departureContinent;
    // 起飞国家
    @Column(name = "departure_country")
    @Type(value = Types.VARCHAR)
    private String departureCountry;
    // 出发省份
    @Column(name = "departure_province")
    @Type(value = Types.VARCHAR)
    private String departureProvince;
    // 到达城市ID
    @Column(name = "arrival_city_id")
    @Type(value = Types.INTEGER)
    private Integer arrivalCityId;
    // 到达城市名称
    @Column(name = "arrival_city_name")
    @Type(value = Types.VARCHAR)
    private String arrivalCityName;
    // 到达城市三字码
    @Column(name = "arrival_city_code")
    @Type(value = Types.VARCHAR)
    private String arrivalCityCode;
    // 到达时间
    @Column(name = "arrival_date_time")
    @Type(value = Types.VARCHAR)
    private String arrivalDateTime;
    // 到达机场
    @Column(name = "arrival_port_name")
    @Type(value = Types.VARCHAR)
    private String arrivalPortName;
    // 到达机场（三字码）
    @Column(name = "aport_code")
    @Type(value = Types.VARCHAR)
    private String aportCode;
    // 到达城市大洲
    @Column(name = "arrival_continent")
    @Type(value = Types.VARCHAR)
    private String arrivalContinent;
    // 到达国家
    @Column(name = "arrival_country")
    @Type(value = Types.VARCHAR)
    private String arrivalCountry;
    // 到达省份
    @Column(name = "arrival_province")
    @Type(value = Types.VARCHAR)
    private String arrivalProvince;
    // 目的地省份
    @Column(name = "dest_province")
    @Type(value = Types.VARCHAR)
    private String destProvince;
    // 目的地国家
    @Column(name = "dest_country")
    @Type(value = Types.VARCHAR)
    private String destCountry;
    // 目的地大洲
    @Column(name = "dest_continent")
    @Type(value = Types.VARCHAR)
    private String destContinent;
    // 目的地城市ID
    @Column(name = "dest_city")
    @Type(value = Types.INTEGER)
    private Integer destCity;
    // 目的地城市名称
    @Column(name = "dest_city_name")
    @Type(value = Types.VARCHAR)
    private String destCityName;
    // 目的地城市代码
    @Column(name = "dest_city_code")
    @Type(value = Types.VARCHAR)
    private String destCityCode;
    // 是否有RC，T(是)/F(否)
    @Column(name = "is_rc")
    @Type(value = Types.VARCHAR)
    private String isRc;
    // 舱等ReasonCode
    @Column(name = "class_rid")
    @Type(value = Types.VARCHAR)
    private String classRid;
    // 舱等RC说明
    @Column(name = "class_rc")
    @Type(value = Types.VARCHAR)
    private String classRc;
    // 协议RC
    @Column(name = "agreement_rid")
    @Type(value = Types.VARCHAR)
    private String agreementRid;
    // 协议RC说明
    @Column(name = "agreement_rc")
    @Type(value = Types.VARCHAR)
    private String agreementRc;
    // 低价ReasonCode,
    @Column(name = "low_rid")
    @Type(value = Types.VARCHAR)
    private String lowRid;
    // 低价RC说明
    @Column(name = "low_rc")
    @Type(value = Types.VARCHAR)
    private String lowRc;
    // 未提前预订ReasonCode
    @Column(name = "pre_rid")
    @Type(value = Types.VARCHAR)
    private String preRid;
    // 未提前预订RC说明
    @Column(name = "pre_rc")
    @Type(value = Types.VARCHAR)
    private String preRc;
    // 时间RC
    @Column(name = "time_rid")
    @Type(value = Types.VARCHAR)
    private String timeRid;// 时间RC
    // 时间RC说明
    @Column(name = "time_rc")
    @Type(value = Types.VARCHAR)
    private String timeRc;
    // 最低价航班起飞时间
    @Column(name = "low_dtime")
    @Type(value = Types.VARCHAR)
    private String lowDtime;
    // 用户自定义RCcode
    @Column(name = "userdefined_rid")
    @Type(value = Types.VARCHAR)
    private String userdefinedRid;
    // 用户自定义RC说明
    @Column(name = "userdefined_rc")
    @Type(value = Types.VARCHAR)
    private String userdefinedRc;
    // 退行程单服务费
    @Column(name = "refund_itinerary_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal refundItineraryFee;
    // 票号
    @Column(name = "ticketno_refund")
    @Type(value = Types.VARCHAR)
    private String ticketnoRefund;
    // 改签id
    @Column(name = "rbkid")
    @Type(value = Types.VARCHAR)
    private String rbkid;
    // 退票审核状态
    @Column(name = "audited")
    @Type(value = Types.VARCHAR)
    private String audited;
    // 最低价航班
    @Column(name = "low_flight")
    @Type(value = Types.VARCHAR)
    private String lowFlight;
    // 最低价航班子舱位
    @Column(name = "low_subclass")
    @Type(value = Types.VARCHAR)
    private String lowSubclass;

    @Column(name = "servicepackage_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal servicepackageFee;
    // 原币种(客户支付币种)
    @Column(name = "o_currency")
    @Type(value = Types.VARCHAR)
    private String oCurrency;
    // 汇率(客户支付币种汇率)
    @Column(name = "o_exchangerate")
    @Type(value = Types.DECIMAL)
    private BigDecimal oExchangerate;
    // 是否廉航
    @Column(name = "airline_isbudget")
    @Type(value = Types.INTEGER)
    private Integer airlineIsbudget;
    // 航空公司EN
    @Column(name = "airline_en_name")
    @Type(value = Types.VARCHAR)
    private String airlineEnName;
    // 物理舱位EN
    @Column(name = "real_class_en")
    @Type(value = Types.VARCHAR)
    private String realClassEn;
    // 航段EN
    @Column(name = "flight_city_en")
    @Type(value = Types.VARCHAR)
    private String flightCityEn;
    // 合并航程/航程EN
    @Column(name = "flight_city2_en")
    @Type(value = Types.VARCHAR)
    private String flightCity2En;
    // 起飞机场EN
    @Column(name = "departure_port_name_en")
    @Type(value = Types.VARCHAR)
    private String departurePortNameEn;
    // 出发城市名称EN
    @Column(name = "departure_city_name_en")
    @Type(value = Types.VARCHAR)
    private String departureCityNameEn;
    // 出发城市大洲EN
    @Column(name = "departure_continent_en")
    @Type(value = Types.VARCHAR)
    private String departureContinentEn;
    // 起飞国家EN
    @Column(name = "departure_country_en")
    @Type(value = Types.VARCHAR)
    private String departureCountryEn;
    // 出发省份EN
    @Column(name = "departure_province_en")
    @Type(value = Types.VARCHAR)
    private String departureProvinceEn;
    // 到达城市名称EN
    @Column(name = "arrival_city_name_en")
    @Type(value = Types.VARCHAR)
    private String arrivalCityNameEn;
    // 到达机场名称EN
    @Column(name = "arrival_port_name_en")
    @Type(value = Types.VARCHAR)
    private String arrivalPortNameEn;
    // 到达城市大洲EN
    @Column(name = "arrival_continent_en")
    @Type(value = Types.VARCHAR)
    private String arrivalContinentEn;
    // 到达国家EN
    @Column(name = "arrival_country_en")
    @Type(value = Types.VARCHAR)
    private String arrivalCountryEn;
    // 到达省份EN
    @Column(name = "arrival_province_en")
    @Type(value = Types.VARCHAR)
    private String arrivalProvinceEn;
    // 目的地省份EN
    @Column(name = "dest_province_en")
    @Type(value = Types.VARCHAR)
    private String destProvinceEn;
    // 目的国家EN
    @Column(name = "dest_country_en")
    @Type(value = Types.VARCHAR)
    private String destCountryEn;
    // 目的大洲EN
    @Column(name = "dest_continent_en")
    @Type(value = Types.VARCHAR)
    private String destContinentEn;
    // 目的城市名称EN
    @Column(name = "dest_city_name_en")
    @Type(value = Types.VARCHAR)
    private String destCityNameEn;
    // 舱等RC说明EN
    @Column(name = "class_rc_en")
    @Type(value = Types.VARCHAR)
    private String classRcEn;
    // 协议RC说明EN
    @Column(name = "agreement_rc_en")
    @Type(value = Types.VARCHAR)
    private String agreementRcEn;
    // 低价RC说明EN
    @Column(name = "low_rc_en")
    @Type(value = Types.VARCHAR)
    private String lowRcEn;
    // 为提前预定RC说明EN
    @Column(name = "pre_rc_en")
    @Type(value = Types.VARCHAR)
    private String preRcEn;
    // 时间RC说明EN
    @Column(name = "time_rc_en")
    @Type(value = Types.VARCHAR)
    private String timeRcEn;
    // 最低价
    @Column(name = "corp_price_adj")
    @Type(value = Types.DECIMAL)
    private BigDecimal corpPriceAdj;
    // 是否共享航班
    @Column(name = "isshared")
    @Type(value = Types.VARCHAR)
    private String isshared;
    // 承运航班号
    @Column(name = "carrierflightno")
    @Type(value = Types.VARCHAR)
    private String carrierflightno;
    // 退款状态字段（是否已退款完成）,
    @Column(name = "refund_customer_status")
    @Type(value = Types.VARCHAR)
    private String refundCustomerStatus;
    // 超过低价RC备注"
    @Column(name = "low_reason_remarks")
    @Type(value = Types.VARCHAR)
    private String lowReasonRemarks;
    // 预订机票不预定酒店RC"
    @Column(name = "noorderhtlreason")
    @Type(value = Types.VARCHAR)
    private String noorderhtlreason;
    // 预订机票不预定酒店RC说明"
    @Column(name = "noorderhtlreasondesc")
    @Type(value = Types.VARCHAR)
    private String noorderhtlreasondesc;
    // 预订机票不预定酒店RC说明英文"
    @Column(name = "noorderhtlreason_en")
    @Type(value = Types.VARCHAR)
    private String noorderhtlreasonEn;
    // 用户取消勾选赠险，赠险对应服务费"
    @Column(name = "notselect_insurance_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal notselectInsuranceFee;
    //  低价RC自定义备注
    @Column(name = "lowprice_rc_vv")
    @Type(value = Types.VARCHAR)
    private String lowpriceRcVv;
    // 协议RC自定义备注
    @Column(name = "agreement_rc_vv")
    @Type(value = Types.VARCHAR)
    private String agreementRcVv;
    // 时间RC自定义备注
    @Column(name = "time_rc_vv")
    @Type(value = Types.VARCHAR)
    private String timeRcVv;
    // 距离RC自定义备注
    @Column(name = "distance_rc_vv")
    @Type(value = Types.VARCHAR)
    private String distanceRcVv;
    // 退票时间
    @Column(name = "refundtime")
    @Type(value = Types.VARCHAR)
    private String refundtime;
    // 公布运价
    @Column(name = "publishPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal publishprice;
    // 改签次数
    @Column(name = "rebookTimes")
    @Type(value = Types.INTEGER)
    private Integer rebooktimes;
    // 改签后子仓位
    @Column(name = "rebooksubclass")
    @Type(value = Types.VARCHAR)
    private String rebookSubclass;
    // 大客户编号
    @Column(name = "customerid")
    @Type(value = Types.VARCHAR)
    private String customerid;
    // 三方协议节省金额
    @Column(name = "save_amount_3c")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveAmount3c;
    // 两方协议节省金额
    @Column(name = "save_amount_premium")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveAmountPremium;
    // 退票类型
    @Column(name = "flightrefundtype")
    @Type(value = Types.VARCHAR)
    private String flightRefundType;
    // 改签类型
    @Column(name = "FlightRebookType")
    @Type(value = Types.VARCHAR)
    private String flightRebookType;

    // 退票RC
    @Column(name = "custom_refund_reason_code")
    @Type(value = Types.VARCHAR)
    private String customRefundReasonCode;
    // 退票RC说明
    @Column(name = "custom_refund_reason_code_desc")
    @Type(value = Types.VARCHAR)
    private String customRefundReasonCodeDesc;

    // 改签燃油差
    @Column(name = "oilfeedifferential")
    @Type(value = Types.DECIMAL)
    private BigDecimal oilfeedifferential;
    // 税差
    @Column(name = "taxDifferential")
    @Type(value = Types.DECIMAL)
    private BigDecimal taxDifferential;

    // 起飞国家id
    @Column(name = "departure_country_id")
    @Type(value = Types.INTEGER)
    private Integer departureCountryId;

    // 到达国家id
    @Column(name = "arrival_country_id")
    @Type(value = Types.INTEGER)
    private Integer arrivalCountryId;

    // 目的地国家id
    @Column(name = "dest_country_id")
    @Type(value = Types.INTEGER)
    private Integer destCountryId;

    // 是否混付(混付/非混付)
    @Column(name = "ismixpayment")
    @Type(value = Types.VARCHAR)
    private String ismixpayment;

    // 混付公司支付金额
    @Column(name = "settlementaccntamt")
    @Type(value = Types.DECIMAL)
    private BigDecimal settlementaccntamt;
    // 混付个人支付金额
    @Column(name = "settlementpersonamt")
    @Type(value = Types.DECIMAL)
    private BigDecimal settlementpersonamt;

    // 管控节省金额
    @Column(name = "saving_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal controlSave;


    // 折扣rc
    @Column(name = "discount_reason_code")
    @Type(value = Types.VARCHAR)
    private String discountReasonCode;
    // 用户自定义折扣RC
    @Column(name = "custom_discount_reason")
    @Type(value = Types.VARCHAR)
    private String customDiscountReason;

    // 人程数
    @Column(name = "countofpassengerflight")
    @Type(value = Types.INTEGER)
    private Integer countofpassengerflight;

    // 应收款调整费用
    @Column(name = "companyfee")
    @Type(value = Types.DECIMAL)
    private BigDecimal companyfee;

    // 改签支付方式
    @Column(name = "rebook_prepaytypename")
    @Type(value = Types.VARCHAR)
    private String rebookPrepaytypename ;

    // 碳排放
    @Column(name = "carbon_emission")
    @Type(value = Types.INTEGER)
    private Integer carbonEmission;

    // 碳排量中位数
    @Column(name = "median_carbon_emission")
    @Type(value = Types.INTEGER)
    private Integer medianCarbons ;

    // 行业大类
    @Column(name = "std_industry1")
    @Type(value = Types.VARCHAR)
    private String stdIndustry1;

    // 行业小类
    @Column(name = "std_industry2")
    @Type(value = Types.VARCHAR)
    private String stdIndustry2;
    // 退供应商审核日期
    @Column(name = "refund_time")
    @Type(value = Types.VARCHAR)
    private String refundAuditedTime;
}
