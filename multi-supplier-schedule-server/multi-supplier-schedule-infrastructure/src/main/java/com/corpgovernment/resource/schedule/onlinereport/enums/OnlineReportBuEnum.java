package com.corpgovernment.resource.schedule.onlinereport.enums;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-03 19:22
 **/
public enum OnlineReportBuEnum {
    /**
     * 概览
     */
    OVERVIEW("overview", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "OnlineReportBuEnum.overview")),
    /**
     * 机票
     */
    FLIGHT("flight", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "OnlineReportBuEnum.flight")),
    /**
     * 酒店
     */
    HOTEL("hotel", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "OnlineReportBuEnum.hotel")),
    /**
     * 火车
     */
    TRAIN("train", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "OnlineReportBuEnum.train")),
    /**
     * 用车
     */
    CAR("car", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "OnlineReportBuEnum.car")),
    /**
     * 汽车
     */
    BUS("bus", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "OnlineReportBuEnum.bus")),
    /**
     * 增值
     */
    VASO("vaso", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "OnlineReportBuEnum.vaso")),
    ;

    private String name;
    private String msg;

    OnlineReportBuEnum(String name, String msg) {
        this.name = name;
        this.msg = msg;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
