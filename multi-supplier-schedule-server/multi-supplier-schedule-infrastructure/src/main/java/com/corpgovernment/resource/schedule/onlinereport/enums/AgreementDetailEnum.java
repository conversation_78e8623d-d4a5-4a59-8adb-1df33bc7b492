package com.corpgovernment.resource.schedule.onlinereport.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/*
 * <AUTHOR>
 *
 * @date 2021/12/22 15:31
 *
 * @Desc协议航司、协议酒店酒店集团消费详情
 */
public enum AgreementDetailEnum {

    /***************************************** 机票 *********************************************/
    // 总成交净价
    FLT_TOTAL_PRICE("flight", "SupplierMonitor.FltNetfareAll", 2, false, "0,1",1),
    // 三方协议成交净价
    FLT_TOTAL_PRICE_TA("flight", "SupplierMonitor.FltNetfareTP", 2, false, "0,1",1),
    // 三方协议成交净价占比
    FLT_PRICE_TA_PERCENT("flight", "SupplierMonitor.FltNetfareTPPer", 2, true, "0,1",3),
    // 三方协议前返成交净价
    //FLT_TOTAL_PRICE_TA_BF_RETURN("flight", "SupplierMonitor.FltNetfareTPBefore", 2, false, "0",1),
    // 总票张数
    FLT_TOTAL_QUANTITY("flight", "SupplierMonitor.FltTktAll", 0, false, "0,1",2),
    // 三方协议票张数
    FLT_TOTAL_QUANTITY_TA("flight", "SupplierMonitor.FltTkt", 0, false, "0,1",2),
    // 三方协议票张数占比
    FLT_QUANTITY_TA_PERCENT("flight", "SupplierMonitor.FltTktTP", 2, true, "0,1",3),
    // 三方协议前返票张数
    //FLT_TOTAL_QUANTITY_TA_BF_RETURN("flight", "SupplierMonitor.FltTktTPBefore", 0,false, "0",2),
    // 三方协议前返票张数占比
    //FLT_QUANTITY_TA_BF_RETURN_PERCENT("flight", "SupplierMonitor.FltTktTPBeforePer", 2, true, "0",3),
    // 三方协议机票均价（仅经济舱）
    FLT_AVG_PRICE_TA_ECONOMY("flight", "SupplierMonitor.FltAvg", 2, false, "0",1),
    // 三方协议前返机票均价（仅经济舱）
    FLT_AVG_PRICE_TA_BF_RETURN_ECONOMY("flight", "SupplierMonitor.FltAvgBefore", 2, false, "0",1),
    // 非三方协议机票均价（仅经济舱）
    FLT_AVG_PRICE_NTA_ECONOMY("flight", "SupplierMonitor.FltAvgNotTP", 2, false, "0",1),
    // RC订单数占比
    FLT_RC_PERCENT("flight", "Save.RCPercent", 2, true, "0",3),
    // 三方协议节省金额(仅国内经济舱）
    FLT_TOTAL_SAV_AMOUNT_3C("flight", "SupplierMonitor.FltSave", 2, false, "0,1",1),

    /***************************************** 酒店 *********************************************/
    // 总消费金额
    HTL_TOTAL_REAL_PAY("hotel", "Supplier.totalcostamount", 2, false, "0,1",1),
    // 三方消费金额
    HTL_TOTAL_REAL_PAY_TA("hotel", "Department.ThreePartyAmt", 2, false, "0,1",1),
    // 三方协议消费金额占比
    HTL_REAL_PAY_TA_PERCENT("hotel", "SupplierMonitor.HtlAmountPer", 2, true, "0,1",3),
    // 总间夜数
    HTL_TOTAL_QUANTITY("hotel", "SupplierMonitor.HtlRoomNight", 2, false, "0,1",2),
    // 三方间夜数
    HTL_TOTAL_QUANTITY_TA("hotel", "Department.ThreePartyQuantity", 0, false, "0,1",2),
    // 三方协议间夜数占比
    HTL_QUANTITY_TA_PERCENT("hotel", "Department.ThreePartyQuantityPer", 2, true, "0,1",3),
    // 三方间夜均价
    HTL_AVG_PRICE_TA("hotel", "SupplierMonitor.HtlTDAvg", 2, false, "0",1),
    // 非三方间夜均价
    HTL_AVG_PRICE_NTA("hotel", "SupplierMonitor.HtlNotTDAvg", 2,false, "0",1),
    // RC订单数占比
    HTL_RC_PERCENT("hotel", "Save.RCPercent", 2, true, "0",3),
    // 三方协议节省金额(仅国内经济舱）
    HTL_TOTAL_SAV_AMOUNT_3C("hotel", "SupplierMonitor.FltSave", 2, false, "0,1",1);

    private String bizType;

    private String sharkKey;

    private int num;

    private boolean isPercent;

    // 0:消费详情,1:是聚合维度是部门
    private String aggType;

    // 数据类型,1金额，2整数，3百分数
    private int dataType;

    public String getBizType() {
        return bizType;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public int getNum() {
        return num;
    }

    public boolean isPercent() {
        return isPercent;
    }

    public String getAggType() {
        return aggType;
    }

    public int getDataType() {
        return dataType;
    }

    AgreementDetailEnum(String s, String r, int a, boolean flag, String aggType, int i) {
        this.bizType = s;
        this.sharkKey = r;
        this.num = a;
        this.isPercent = flag;
        this.aggType = aggType;
        this.dataType = i;
    }


    public static List<AgreementDetailEnum> getStaticalsByBizType(String bizType) {
        List<AgreementDetailEnum> result = new ArrayList<>();
        AgreementDetailEnum[] agreementDetailEnums = AgreementDetailEnum.values();
        for (AgreementDetailEnum agreementDetailEnum : agreementDetailEnums) {
            if (StringUtils.equalsIgnoreCase(agreementDetailEnum.getBizType(), bizType)) {
                result.add(agreementDetailEnum);
            }
        }
        return result;
    }

    // 不同的aggType要同统计的指标不一样
    public static List<AgreementDetailEnum> getStaticalsByAggType(String bizType, int aggType) {
        List<AgreementDetailEnum> result = new ArrayList<>();
        AgreementDetailEnum[] agreementDetailEnums = AgreementDetailEnum.values();
        for (AgreementDetailEnum agreementDetailEnum : agreementDetailEnums) {
            if (StringUtils.isNotEmpty(agreementDetailEnum.getBizType())
                    && Arrays.stream(agreementDetailEnum.getAggType().split(","))
                    .anyMatch(i -> Integer.valueOf(i) == aggType && StringUtils.equalsIgnoreCase(agreementDetailEnum.getBizType(), bizType))) {
                result.add(agreementDetailEnum);
            }
        }
        return result;
    }
}
