package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.dao.impl
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-10 10:02
 **/
@Repository
@Slf4j
public class OnlineReportCarDao extends AbstractClickhouseBaseDao {


    private static final String LOG_TITLE = "OnlineReportCarDao queryOnlineReportCar";


    private BaseCommonTableQueryCondition queryCondition;

    public OnlineReportCarDao(BaseCommonTableQueryCondition queryCondition) {
        this.queryCondition = queryCondition;
    }

    /**
     * 查询-用车产线数据
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryOnlineReportCar(OnlineDetailRequestDto requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append("report_date as reportDate, order_type as orderType,sub_product_line as subProductLine, \n");

        /**
         * order_type: 1-国内接送机;2-国际接送机;3-包车;4-租车;6-打车
         * subProductLine:1-国内打车；CAR_TAXI_INTL:国际打车
         */
        // 基础费用
        sqlBuilder.append(" sum(coalesce(basic_fee,0)) as basicFee , \n");
        // 商旅服务费
        sqlBuilder.append(" sum(coalesce(service_fee,0)) as serviceFee , \n");
        // 退票金额
        sqlBuilder.append(" sum(coalesce(refund_amount,0)) as refundAmount , \n");

        /**
         * 金额明细-当期/环比/同比
         */
        // 总金额
        sqlBuilder.append(" sum(coalesce(real_pay,0)) as realPay , \n");

        // 国内打车-当期
        sqlBuilder.append(" sum(case when (order_type=6 and sub_product_line = '1') then coalesce(real_pay,0) else 0 end) as takeTaxiAmount , \n");

        // 国内接送机-当期
        sqlBuilder.append(" sum(case when order_type = 1 then coalesce(real_pay,0) else 0 end) as airportPickUpAmount , \n");

        // 租车-当期
        sqlBuilder.append(" sum(case when order_type=4 then coalesce(real_pay,0) else 0 end) as rentalCarAmount , \n");

        // 国际租车-当期
        sqlBuilder.append(" sum(case when order_type=18 then coalesce(real_pay,0) else 0 end) as rentalCarAmountInter , \n");

        // 包车-当期
        sqlBuilder.append(" sum(case when order_type=3 then coalesce(real_pay,0) else 0 end) as charteredCarAmount , \n");

        // 国际打车-当期
        sqlBuilder.append(" sum(case when (order_type=6 and sub_product_line = 'CAR_TAXI_INTL') then coalesce(real_pay,0) else 0 end) as takeTaxiAmountInter , \n");

        // 国际接送机-当期
        sqlBuilder.append(" sum(case when order_type = 2 then coalesce(real_pay,0) else 0 end) as airportPickUpAmountInter , \n");

        /**
         * 票张明细-当期/环比/同比
         */
        // 票张-当期
        sqlBuilder.append(" sum(coalesce(cnt_order,0)) as quantityV , \n");

        // 国内打车-当期
        sqlBuilder.append(" sum(case when (order_type=6 and sub_product_line = '1') then coalesce(cnt_order,0) else 0 end) as takeTaxiQuantity , \n");

        // 国内接送机-当期
        sqlBuilder.append(" sum(case when order_type= 1 then coalesce(cnt_order,0) else 0 end) as airportPickUpQuantity , \n");

        // 租车-当期
        sqlBuilder.append(" sum(case when order_type=4 then coalesce(cnt_order,0) else 0 end) as rentalCarQuantity , \n");

        // 国际租车-当期
        sqlBuilder.append(" sum(case when order_type=18 then coalesce(cnt_order,0) else 0 end) as rentalCarQuantityInter , \n");

        // 包车-当期
        sqlBuilder.append(" sum(case when order_type=3 then coalesce(cnt_order,0) else 0 end) as charteredCarQuantity, \n");

        // 国际打车-当期
        sqlBuilder.append(" sum(case when (order_type=6 and sub_product_line = 'CAR_TAXI_INTL') then coalesce(cnt_order,0) else 0 end) as takeTaxiQuantityInter , \n");

        // 国际接送机-当期
        sqlBuilder.append(" sum(case when order_type = 2 then coalesce(cnt_order,0) else 0 end) as airportPickUpQuantityInter \n");

        sqlBuilder.append(OrpConstants.FROM);
        ClickHouseTable clickHouseTable = null;
        if (StringUtils.equalsIgnoreCase(requestDto.getBaseCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL_ODT;// todo 预订口径
        } else {
            // 默认使用成交口径
            clickHouseTable = ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL;
        }
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        sqlBuilder.append(OrpConstants.AND);
        sqlBuilder.append(" ( report_date>=? and report_date<=? ) ");

        // 条件
        queryCondition.buildPreSqlCondition(requestDto, sqlBuilder);
        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append(" report_date,order_type,sub_product_line \n");

        ClickHouseTable finalClickHouseTable = clickHouseTable;
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapRequest(req, statement, finalClickHouseTable), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, "queryOnlineReportCar");
    }


    private PreparedStatement mapRequest(OnlineDetailRequestDto requestDto, PreparedStatement statement, ClickHouseTable clickHouseTable) {
        AtomicInteger index = new AtomicInteger(OrpConstants.ONE);
        try {

            // 分区
            statement.setString(index.getAndIncrement(), queryPartition(clickHouseTable));
            // 查询日期
            statement.setString(index.getAndIncrement(), requestDto.getDataStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());

            queryCondition.setPreSqlCondition(index, requestDto, statement);
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }
}
