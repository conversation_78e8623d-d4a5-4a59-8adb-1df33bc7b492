package com.corpgovernment.resource.schedule.onlinereport.utils.adapter;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;
import java.util.Objects;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.utils.adapter
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-03 19:01
 **/
public class StringNullAdapter extends TypeAdapter<String> {

    @Override
    public String read(JsonReader reader) throws IOException {
        if (reader.peek() == JsonToken.NULL) {
            reader.nextNull();
            return "";
        }
        if (reader.peek().equals(JsonToken.NULL)) {
            reader.nextNull();
            return "";
        }
        return reader.nextString();
    }

    @Override
    public void write(JsonWriter writer, String value) throws IOException {
        if (Objects.isNull(value)) {
            writer.value("");
            return;
        }
        writer.value(value);
    }
}
