package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.multilanguage.CorpFltMultiLangDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.multilanguage.CorpHtlMultiLangDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.AbstractDetaiDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.FltDetaiDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.FltRebookDetaiDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.FltRfeundDetaiDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.FltUnuseDetaiDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.FltOrderDetailMapper;
import com.corpgovernment.resource.schedule.onlinereport.enums.CcontientEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.FlightRealClassEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.GeoCategoryEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.reportlib.FlightOrderStatusEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.geo.MultiLangDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.FlightOrderDTO;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.AbstractOrderDetailBiz;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.DEFAULT_LANG;


/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Slf4j
@Service
public class FlightOrderDetailBiz extends AbstractOrderDetailBiz {


    private static Map continentIdMap = null;

    static {
        continentIdMap = ImmutableMap.builder()
                .put(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, CcontientEnum.ASIA.getKey()), CcontientEnum.ASIA.getId())
                .put(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, CcontientEnum.EUROPE.getKey()), CcontientEnum.EUROPE.getId())
                .put(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, CcontientEnum.OCEANIA.getKey()), CcontientEnum.OCEANIA.getId())
                .put(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, CcontientEnum.NORTHAMERICA.getKey()), CcontientEnum.NORTHAMERICA.getId())
                .put(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, CcontientEnum.SOUTHAMERICA.getKey()), CcontientEnum.SOUTHAMERICA.getId())
                .put(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, CcontientEnum.AFRICA.getKey()), CcontientEnum.AFRICA.getId())
                .put(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, CcontientEnum.ANTARCTICA.getKey()), CcontientEnum.ANTARCTICA.getId())
                .build();
    }

    @Qualifier("screenThreadPoolExecutor")
    @Resource
    ExecutorService executorService;
    @Resource
    private FltDetaiDaoImpl fltDetaiDaoImpl;
    @Resource
    private FltRebookDetaiDaoImpl fltRebookDetaiDaoImpl;
    @Resource
    private FltRfeundDetaiDaoImpl fltRfeundDetaiDaoImpl;
    @Resource
    private FltUnuseDetaiDaoImpl fltUnuseDetaiDaoImpl;
    @Resource
    private FltOrderDetailMapper detailMapper;
    @Autowired
    private CorpHtlMultiLangDao corpHtlMultiLangDao;
    @Autowired
    private CorpFltMultiLangDao corpFltMultiLangDao;

    @Override
    protected AbstractDetaiDao current(OnlineReportOrderDetailRequest request) {
        if (request.getExtData() == null) {
            return fltDetaiDaoImpl;
        }
        String reportType = request.getExtData().get("reportType");
        if (StringUtils.equalsIgnoreCase(reportType, FltReportTypeEnum.F_REFUND.name())) {
            return fltRfeundDetaiDaoImpl;
        } else if (StringUtils.equalsIgnoreCase(reportType, FltReportTypeEnum.F_REBOOK.name())) {
            return fltRebookDetaiDaoImpl;
        } else if (StringUtils.equalsIgnoreCase(reportType, FltReportTypeEnum.F_UNUSE.name())) {
            return fltUnuseDetaiDaoImpl;
        } else {
            return fltDetaiDaoImpl;
        }
    }

    @Override
    public OnlineReportOrderDetailInfo queryOrderDetail(OnlineReportOrderDetailRequest request) throws Exception {
        BaseQueryCondition baseQueryCondition = request.getBasecondition();
        OnlineReportOrderDetailInfo detailInfo = new OnlineReportOrderDetailInfo();
        long startTime = System.currentTimeMillis();
        List<FlightOrderDTO> list = queryOrderDetailEntity(request, FlightOrderDTO.class);
        log.info("queryDetail执行queryOrderDetail后innner_queryOrderDetail耗时：{}",System.currentTimeMillis()-startTime);
        if (BlueSpaceUtils.isBlueSpace(baseQueryCondition.getBlueSpace()) || BlueSpaceUtils.isForeign(baseQueryCondition.getPos(), baseQueryCondition.getBlueSpace())) {
            transLate(list, request.getLang());
        }
        if (CollectionUtils.isNotEmpty(list)) {
            detailInfo.setFltOrderList(detailMapper.toDTOs(list));
        }
        return detailInfo;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.flight;
    }

    public void transLate(List<FlightOrderDTO> list, String lang) throws Exception {
        Map orderStatusMap = initOrderStatus(lang);
        Map realclassMap = initRealClass(lang);
        Map yesOrNoMap = initYesOrNo(lang);
        if (CollectionUtils.isNotEmpty(list)) {
            Set<String> airportIds = new HashSet<>();
            Set<String> airlineIds = new HashSet<>();
//            Set<Integer> cityIds = new HashSet<>();
            Set<String> cityNames = new HashSet<>();
            Set<Integer> countryIds = new HashSet<>();
            // 大洲的id默认就是1,2,3,4,5,6,7
            List<Integer> continentIds = new ArrayList<Integer>() {{
                add(1);
                add(2);
                add(3);
                add(4);
                add(5);
                add(6);
                add(7);
            }};
            for (FlightOrderDTO flightOrderDTO : list) {
                countryIds.add(flightOrderDTO.getDepartureCountryId());
                countryIds.add(flightOrderDTO.getDestCountryId());
                countryIds.add(flightOrderDTO.getArrivalCountryId());
                if (StringUtils.isNotEmpty(flightOrderDTO.getDepartureCityName())) {
                    cityNames.add(flightOrderDTO.getDepartureCityName());
                }
                if (StringUtils.isNotEmpty(flightOrderDTO.getArrivalCityName())) {
                    cityNames.add(flightOrderDTO.getArrivalCityName());
                }
                if (StringUtils.isNotEmpty(flightOrderDTO.getDestCityName())) {
                    cityNames.add(flightOrderDTO.getDestCityName());
                }
                if (StringUtils.isNotEmpty(flightOrderDTO.getDportCode())) {
                    airportIds.add(flightOrderDTO.getDportCode());
                }
                if (StringUtils.isNotEmpty(flightOrderDTO.getAportCode())) {
                    airportIds.add(flightOrderDTO.getAportCode());
                }
                if (StringUtils.isNotEmpty(flightOrderDTO.getAirline())) {
                    airlineIds.add(flightOrderDTO.getAirline());
                }
                if (Objects.nonNull(flightOrderDTO.getFlightCity2())) {
                    cityNames.addAll(splitFlightCity2(flightOrderDTO.getFlightCity2()));
                }
            }
            Future<List<MultiLangDTO>> future1 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpHtlMultiLangDao.queryGeoLocation(continentIds, lang, MultiLangDTO.class, GeoCategoryEnum.CONTINENT);
                }
            });
            Future<List<MultiLangDTO>> future2 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpHtlMultiLangDao.queryGeoLocation(new ArrayList<>(countryIds), lang, MultiLangDTO.class, GeoCategoryEnum.COUNTRY);
                }
            });
            Future<List<MultiLangDTO>> future4 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpFltMultiLangDao.queryFltAirlineMultiLang(new ArrayList<>(airlineIds), lang, MultiLangDTO.class);
                }
            });
            Future<List<MultiLangDTO>> future5 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpFltMultiLangDao.queryFltAirportMultiLang(new ArrayList<>(airportIds), lang, MultiLangDTO.class);
                }
            });
            Future<List<MultiLangDTO>> future6 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpFltMultiLangDao.queryGeoLocationByNames(new ArrayList<>(cityNames), lang, MultiLangDTO.class);
                }
            });
            List<MultiLangDTO> continentMultiLangs = future1.get();
            List<MultiLangDTO> countryMultiLangs = future2.get();
            List<MultiLangDTO> airlineMultiLangs = future4.get();
            List<MultiLangDTO> airportMultiLangs = future5.get();
            List<MultiLangDTO> cityMultiLangs1 = future6.get();
            for (FlightOrderDTO flightOrderDTO : list) {
                flightOrderDTO.setDepartureCountry(matchMultiLangInfo(countryMultiLangs, flightOrderDTO.getDepartureCountryId(), flightOrderDTO.getDepartureCountryEn()));
                flightOrderDTO.setArrivalCountry(matchMultiLangInfo(countryMultiLangs, flightOrderDTO.getArrivalCountryId(), flightOrderDTO.getArrivalCountryEn()));
                flightOrderDTO.setDestCountry(matchMultiLangInfo(countryMultiLangs, flightOrderDTO.getDestCountryId(), flightOrderDTO.getDestCountryEn()));
                flightOrderDTO.setDepartureContinent(matchMultiLangInfo(continentMultiLangs, (Integer) continentIdMap.get(flightOrderDTO.getDepartureContinent()),
                        flightOrderDTO.getDepartureContinentEn()));
                flightOrderDTO.setDestContinent(matchMultiLangInfo(continentMultiLangs, (Integer) continentIdMap.get(flightOrderDTO.getDestContinent()),
                        flightOrderDTO.getDestContinentEn()));

                flightOrderDTO.setDepartureCityName(matchMultiLangInfo(cityMultiLangs1, flightOrderDTO.getDepartureCityName(), flightOrderDTO.getDepartureCityNameEn()));
                flightOrderDTO.setArrivalCityName(matchMultiLangInfo(cityMultiLangs1, flightOrderDTO.getArrivalCityName(), flightOrderDTO.getArrivalCityNameEn()));
                flightOrderDTO.setDestCityName(matchMultiLangInfo(cityMultiLangs1, flightOrderDTO.getDestCityName(), flightOrderDTO.getDestCityNameEn()));
                flightOrderDTO.setAirlineCnName(matchMultiLangInfo(airlineMultiLangs, flightOrderDTO.getAirline(), flightOrderDTO.getAirlineEnName()));
                flightOrderDTO.setDeparturePortName(matchMultiLangInfo(airportMultiLangs, flightOrderDTO.getDportCode(), flightOrderDTO.getDeparturePortNameEn()));
                flightOrderDTO.setArrivalPortName(matchMultiLangInfo(airportMultiLangs, flightOrderDTO.getAportCode(), flightOrderDTO.getArrivalPortNameEn()));
                flightOrderDTO.setOrderStatus(StringUtils.trimToEmpty((String) orderStatusMap.get(StringUtils.trimToEmpty(flightOrderDTO.getOrderStatus()))));
                flightOrderDTO.setRealClass(StringUtils.trimToEmpty((String) realclassMap.get(StringUtils.trimToEmpty(flightOrderDTO.getRealClass()))));
                flightOrderDTO.setIsRebook(StringUtils.trimToEmpty((String) yesOrNoMap.get(StringUtils.trimToEmpty(flightOrderDTO.getIsRebook()))));
                flightOrderDTO.setIsRefund(StringUtils.trimToEmpty((String) yesOrNoMap.get(StringUtils.trimToEmpty(flightOrderDTO.getIsRefund()))));
                flightOrderDTO.setFlightCity(transFlightCity(flightOrderDTO.getFlightCity(), cityMultiLangs1, flightOrderDTO.getFlightCityEn()));
                flightOrderDTO.setFlightCity2(transFlightCity(flightOrderDTO.getFlightCity2(), cityMultiLangs1, flightOrderDTO.getFlightCity2En()));
            }
        }
    }

    private Map initOrderStatus(String lang) {
        return ImmutableMap.builder()
                .put(FlightOrderStatusEnum.R.toString(), SharkUtils.get(FlightOrderStatusEnum.R.getName(), lang))
                .put(FlightOrderStatusEnum.S.toString(), SharkUtils.get(FlightOrderStatusEnum.S.getName(), lang))
                .put(FlightOrderStatusEnum.T.toString(), SharkUtils.get(FlightOrderStatusEnum.T.getName(), lang))
                .put(FlightOrderStatusEnum.C.toString(), SharkUtils.get(FlightOrderStatusEnum.C.getName(), lang))
                .build();
    }

    private Map initRealClass(String lang) {
        return ImmutableMap.builder()
                .put(SharkUtils.get(FlightRealClassEnum.ECONOMY.getName(), DEFAULT_LANG), SharkUtils.get(FlightRealClassEnum.ECONOMY.getName(), lang))
                .put(SharkUtils.get(FlightRealClassEnum.ECONOMY_FLEX.getName(), DEFAULT_LANG), SharkUtils.get(FlightRealClassEnum.ECONOMY_FLEX.getName(), lang))
                .put(SharkUtils.get(FlightRealClassEnum.ECONOMY_STANDARD.getName(), DEFAULT_LANG), SharkUtils.get(FlightRealClassEnum.ECONOMY_STANDARD.getName(), lang))
                .put(SharkUtils.get(FlightRealClassEnum.BUSINESS.getName(), DEFAULT_LANG), SharkUtils.get(FlightRealClassEnum.BUSINESS.getName(), lang))
                .put(SharkUtils.get(FlightRealClassEnum.FIRST.getName(), DEFAULT_LANG), SharkUtils.get(FlightRealClassEnum.FIRST.getName(), lang))
                .build();
    }

    private List splitFlightCity2(String flightCity2) {
        String[] flightCityNames = StringUtils.trimToEmpty(flightCity2).split("-");
        return ArrayUtils.isNotEmpty(flightCityNames) ? Arrays.asList(flightCityNames) : ListUtils.EMPTY_LIST;
    }

    private String transFlightCity(String flightCity, List<MultiLangDTO> cityMultiLangs, String flightCityEn) {
        String[] flightCityNames = StringUtils.trimToEmpty(flightCity).split("-");
        if (ArrayUtils.isNotEmpty(flightCityNames)) {
            for (int i = 0; i < flightCityNames.length; i++) {
                String multiCityname = matchMultiLangInfo(cityMultiLangs, flightCityNames[i], StringUtils.EMPTY);
                if (StringUtils.isNotEmpty(multiCityname)) {
                    flightCity = flightCity.replace(flightCityNames[i], multiCityname);
                    continue;
                } else {
                    return flightCityEn;
                }
            }
            return flightCity;
        }
        return StringUtils.trimToEmpty(flightCity);
    }

    enum FltReportTypeEnum {
        /*明细*/
        F_DETAIL,
        /*退*/
        F_REFUND,
        /*改*/
        F_REBOOK,
        /*未使用*/
        F_UNUSE
    }

}
