package com.corpgovernment.resource.schedule.hotel.dao.db.impl;

import com.corpgovernment.resource.schedule.domain.hotel.enums.TaskStatusEnum;
import com.corpgovernment.resource.schedule.hotel.dao.db.ITaskPoolDao;
import com.corpgovernment.resource.schedule.hotel.dao.mapper.TaskPoolMapper;
import com.corpgovernment.resource.schedule.hotel.dao.mysql.entity.TaskPoolDo;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.github.pagehelper.PageRowBounds;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/9
 */
@Repository
public class TaskPoolDao implements ITaskPoolDao {

    @Resource
    private TaskPoolMapper taskPoolMapper;

    @Override
    public Integer remove(String name, Integer batchSize) {
        if (StringUtils.isBlank(name)) {
            return 0;
        }
        return taskPoolMapper.remove(name, batchSize);
    }

    @Override
    public void batchCreate(List<TaskPoolDo> taskPoolDoList) {
        if (CollectionUtils.isEmpty(taskPoolDoList)) {
            return;
        }
        for (TaskPoolDo taskPoolDo : taskPoolDoList) {
            taskPoolDo.setDeleted(false);
            taskPoolDo.setCreateTime(new Date());
            taskPoolDo.setUpdateTime(new Date());
        }
        taskPoolMapper.insertList(taskPoolDoList);
    }

    @Override
    public List<TaskPoolDo> list(String name, String supplierCode, Integer pageSize) {
        if (StringUtils.isBlank(name) || StringUtils.isBlank(supplierCode) || pageSize == null) {
            return new ArrayList<>(0);
        }
        Example example = new Example(TaskPoolDo.class);
        example.createCriteria().andEqualTo("name", name).andEqualTo("supplierCode", supplierCode).andEqualTo("status", TaskStatusEnum.CREATED.name());
        return taskPoolMapper.selectByExampleAndRowBounds(example, new PageRowBounds(0, pageSize));
    }

    @Override
    public void update(List<Long> idList, Integer status) {
        if (CollectionUtils.isEmpty(idList) || status == null) {
            return;
        }
        Example example = new Example(TaskPoolDo.class);
        example.createCriteria().andIn("id", idList);
        TaskPoolDo taskPoolDo = new TaskPoolDo();
        taskPoolDo.setStatus(status);
        taskPoolMapper.updateByExampleSelective(taskPoolDo, example);
    }

    @Override
    public void remove(Long id) {
        if (id == null) {
            return;
        }
        taskPoolMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void retry(Integer maxRetryCount) {
        if (maxRetryCount == null) {
            return;
        }
        taskPoolMapper.retry(maxRetryCount);
    }

    @Override
    public void releaseUnFinishedTask() {
        Example example = new Example(TaskPoolDo.class);
        example.createCriteria().andIn("status", Arrays.asList(TaskStatusEnum.LOCKED.getCode(), TaskStatusEnum.FAILED.getCode()));
        TaskPoolDo taskPoolDo = new TaskPoolDo();
        taskPoolDo.setStatus(TaskStatusEnum.CREATED.getCode());
        taskPoolMapper.updateByExampleSelective(taskPoolDo, example);
    }

}
