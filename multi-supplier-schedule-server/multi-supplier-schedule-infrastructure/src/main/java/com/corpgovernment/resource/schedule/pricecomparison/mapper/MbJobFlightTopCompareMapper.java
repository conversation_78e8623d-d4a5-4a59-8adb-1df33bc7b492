package com.corpgovernment.resource.schedule.pricecomparison.mapper;

import com.corpgovernment.resource.schedule.config.TkMapper;
import com.corpgovernment.resource.schedule.pricecomparison.mysql.entity.MbJobFlightTopCompareDo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * top100机票航线表
 */
@Mapper
public interface MbJobFlightTopCompareMapper extends TkMapper<MbJobFlightTopCompareDo> {

    List<MbJobFlightTopCompareDo> selectAll();
}
