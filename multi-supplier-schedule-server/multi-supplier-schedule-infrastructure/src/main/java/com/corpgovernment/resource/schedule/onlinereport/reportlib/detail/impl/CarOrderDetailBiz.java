package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.impl;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailRequest;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.AbstractDetaiDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.CarDetaiDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.CarOrderDetailMapper;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.CarOrderDTO;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.AbstractOrderDetailBiz;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Service
public class CarOrderDetailBiz extends AbstractOrderDetailBiz {


    @Autowired
    private CarDetaiDaoImpl detaiDao;

    @Autowired
    private CarOrderDetailMapper detailMapper;


    @Override
    protected AbstractDetaiDao current(OnlineReportOrderDetailRequest request) {
        return detaiDao;
    }

    @Override
    public OnlineReportOrderDetailInfo queryOrderDetail(OnlineReportOrderDetailRequest request) throws Exception {
        OnlineReportOrderDetailInfo detailInfo = new OnlineReportOrderDetailInfo();
        // 用户订单要包含接送机站，接送机站的“完成”状态是“SA"
        if(CollectionUtils.isNotEmpty(request.getOrderstatusList()) && request.getOrderstatusList().contains("Completed")){
            request.getOrderstatusList().add("SA");
        }
        List<CarOrderDTO> list = queryOrderDetailEntity(request, CarOrderDTO.class);
        if (CollectionUtils.isNotEmpty(list)) {
            detailInfo.setCarOrderList(detailMapper.toDTOs(list));
        }
        return detailInfo;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.car;
    }
}
