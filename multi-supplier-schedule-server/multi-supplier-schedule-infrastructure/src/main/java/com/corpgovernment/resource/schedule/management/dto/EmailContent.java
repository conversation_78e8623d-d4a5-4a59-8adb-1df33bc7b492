package com.corpgovernment.resource.schedule.management.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class EmailContent {

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 触发次数
     */
    private Long triggerCount;

    /**
     * 成功次数
     */
    private Long successCount;

    /**
     * 失败次数
     */
    private Long failCount;

    /**
     * 失败占比
     */
    private String failRate;

    public String getFailRate() {
        return String.format("%.2f%%", failCount * 100.0 / triggerCount);
    }
}
