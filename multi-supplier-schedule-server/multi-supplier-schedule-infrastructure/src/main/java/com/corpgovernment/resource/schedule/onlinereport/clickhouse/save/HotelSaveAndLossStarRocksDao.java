package com.corpgovernment.resource.schedule.onlinereport.clickhouse.save;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.HotelSaveStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.save.HotelSaveDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/*
 * <AUTHOR>
 * @date 2022/6/20 13:08
 * @Desc
 */
@Service
@Slf4j
@Repository
public class HotelSaveAndLossStarRocksDao extends AbstractSaveAndLossDao {

    private static final String LOG_TITLE = "HotelSaveAndLossDao";

    private static final String TOP_DEPT_POTENTIAL_SAVE_SQL = "SELECT %s \n" +
            "    , round(if(CAST(coalesce(a.overAmount, 0) AS DOUBLE)>0,CAST(coalesce(a.overAmount, 0) AS DOUBLE),0) " +
            "+ if(CAST(coalesce(a.refundloss, 0) AS DOUBLE)>0,CAST(coalesce(a.refundloss, 0) AS DOUBLE),0), 2) AS "
            + HotelSaveStatisticalsEnum.TOTAL_SAVE_POTENTIAL.toString() + "\n" +
            "    , round(case when CAST(if(CAST(coalesce(b.overAmount, 0) AS DOUBLE)>0,CAST(coalesce(b.overAmount, 0) AS DOUBLE),0) " +
            "+ if(CAST(coalesce(b.refundloss, 0) AS DOUBLE)>0,CAST(coalesce(b.refundloss, 0) AS DOUBLE),0) AS DOUBLE) != 0 \n" +
            "      then CAST(if(CAST(coalesce(a.overAmount, 0) AS DOUBLE)>0,CAST(coalesce(a.overAmount, 0) AS DOUBLE),0)" +
            " + if(CAST(coalesce(a.refundloss, 0) AS DOUBLE)>0,CAST(coalesce(a.refundloss, 0) AS DOUBLE),0) AS DOUBLE) \n" +
            "    / CAST(if(CAST(coalesce(b.overAmount, 0) AS DOUBLE)>0,CAST(coalesce(b.overAmount, 0) AS DOUBLE),0) " +
            "+ if(CAST(coalesce(b.refundloss, 0) AS DOUBLE)>0,CAST(coalesce(b.refundloss, 0) AS DOUBLE),0) AS DOUBLE) * 100 else 0 end, 2) AS "
            + HotelSaveStatisticalsEnum.TOTAL_SAVE_POTENTIAL_PERCENT.toString() + "\n" +
            "    , round(case when CAST(coalesce(b.overAmount, 0) AS DOUBLE) > 0 and CAST(coalesce(a.overAmount, 0) AS DOUBLE) > 0 then CAST(coalesce(a.overAmount, 0) AS DOUBLE) " +
            "    / CAST(coalesce(b.overAmount, 0) AS DOUBLE) * 100 else 0 end, 2) AS "
            + HotelSaveStatisticalsEnum.OVER_AMOUNT_PERCENT.toString() + "\n" +
            "    , if(CAST(coalesce(a.overAmount, 0) AS DOUBLE)>0,CAST(coalesce(a.overAmount, 0) AS DOUBLE),0) as " + HotelSaveStatisticalsEnum.OVER_AMOUNT.toString() + "\n" +
            "    , if(CAST(coalesce(a.refundloss, 0) AS DOUBLE)>0,CAST(coalesce(a.refundloss, 0) AS DOUBLE),0) as " + HotelSaveStatisticalsEnum.REFUND_LOSS.toString() + "\n" +
            "    , round(case when CAST(coalesce(b.refundloss, 0) AS DOUBLE) > 0 and CAST(coalesce(a.refundloss, 0) AS DOUBLE) > 0 then  CAST(coalesce(a.refundloss, 0) AS DOUBLE) " +
            "    / CAST(coalesce(b.refundloss, 0) AS DOUBLE) * 100 ELSE 0 end, 2) AS "
            + HotelSaveStatisticalsEnum.REFUND_LOSS_PERCENT.toString() + "\n" +
            "FROM (\n" +
            "    SELECT %s \n" +
            "        , SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as overAmount\n" +
            "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND reason_code IS NOT NULL AND reason_code != '' THEN order_id  END) \n" +
            "        AS reasonRcTimes \n" +
            "        , SUM(coalesce(cancel_esti_save_amount , 0)) AS refundloss\n" +
            "        , SUM(coalesce(rfd_quantity,0))  AS refundtkt\n" +
            "    FROM olrpt_indexhoteldownload_all\n" +
            "    WHERE %s \n" +
            "        AND (coalesce(over_std_esti_save_amount , 0) != 0 OR coalesce(cancel_esti_save_amount , 0) != 0 )\n" +
            "    GROUP BY %s\n" +
            ") a\n" +
            "    CROSS JOIN ( \n" +
            "    SELECT\n" +
            "          SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as overAmount\n" +
            "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND reason_code IS NOT NULL AND reason_code != '' THEN order_id  END) \n" +
            "        AS reasonRcTimes \n" +
            "        , SUM(coalesce(cancel_esti_save_amount , 0)) AS refundloss\n" +
            "        , SUM(coalesce(rfd_quantity,0))  AS refundtkt\n" +
            "        FROM olrpt_indexhoteldownload_all\n" +
            "        WHERE %s \n" +
            "            AND (coalesce(over_std_esti_save_amount , 0) != 0 OR coalesce(cancel_esti_save_amount , 0) != 0 )\n" +
            "    ) b\n" +
            "    order by " + HotelSaveStatisticalsEnum.TOTAL_SAVE_POTENTIAL.toString() + " desc,%s ";

    private static final String TOP_DEPT_POTENTIAL_SAVE_SQL_COUNT = "SELECT COUNT(1) AS countAll  \n" +
            "    from ( select 1 FROM olrpt_indexhoteldownload_all\n" +
            "    WHERE %s \n" +
            "        AND (coalesce(over_std_esti_save_amount , 0) != 0 OR coalesce(cancel_esti_save_amount , 0) != 0 ) group by %s) t\n";

    /**
     * 酒店节省
     *
     * @param requestDto
     * @param productType
     * @return
     * @throws Exception
     */
    public List<HotelSaveDTO> agreementSave(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");
        sqlBuilder.append("select ");
        // 三方协议的节省金额
        sqlBuilder.append(" SUM(coalesce(save_amount_3c, 0)) as saveAmount3c \n");
        // 两方尊享的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_premium, 0)) as saveAmountPremium \n");
        // 促销优惠活动的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_promotion, 0)) as saveAmountPromotion \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as controlSave \n");
        sqlBuilder.append(",sum(case when (producttype_all ='" + ta + "' and save_amount_3c is not null) or " +
                "(producttype_all ='" + premium + "' and save_amount_premium is not null) or " +
                "save_amount_promotion != 0 or coalesce(saving_price, 0) != 0 then coalesce(corp_real_pay, 0) else 0 end)  " +
                "as sumCorpRealPay \n");
        // 每间夜节省
        sqlBuilder.append(String.format(", if(SUM(case when (producttype_all = '%s' and save_amount_3c is not null) or coalesce(save_amount_promotion, 0) <> 0 or " +
                " (producttype_all = '%s' and save_amount_premium is not null) or coalesce(saving_price, 0) != 0" +
                " then quantity else 0 end) != 0, " +
                " CAST(SUM(coalesce(save_amount_3c, 0)) + SUM(coalesce(save_amount_premium, 0)) + " +
                "SUM(coalesce(save_amount_promotion, 0)) + SUM(coalesce(saving_price, 0)) AS DOUBLE) / " +
                " CAST(SUM(case when (producttype_all = '%s' and save_amount_3c is not null) or coalesce(save_amount_promotion, 0) <> 0 or " +
                " (producttype_all = '%s' and save_amount_premium is not null) or coalesce(saving_price, 0) != 0 " +
                " then CAST(quantity AS DOUBLE) else 0 end) AS DOUBLE), " +
                " 0) as savePerQuantity ", ta, premium, ta, premium));
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.PRE_WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        // 查询clickhouse
        return commonList(HotelSaveDTO.class, sqlBuilder.toString(), parmList);
    }

    /**
     * 酒店节省
     *
     * @param starTime
     * @param endTime
     * @param statisticalCaliber
     * @param industryList
     * @param dataTypeEnum
     * @param productType
     * @param compareSameLevel   是否对比同级比较
     * @param consumptionLevel   消费等级
     * @return
     * @throws Exception
     */
    
    public List<HotelSaveDTO> agreementSaveCorpAndIndustry(String starTime, String endTime, String statisticalCaliber, List<String> industryList, DataTypeEnum dataTypeEnum,
                                                           String productType, String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");
        sqlBuilder.append("select ");
        // 三方协议的节省金额
        sqlBuilder.append(" SUM(coalesce(save_amount_3c, 0)) as saveAmount3c \n");
        // 两方尊享的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_premium, 0)) as saveAmountPremium \n");
        // 促销优惠活动的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_promotion, 0)) as saveAmountPromotion \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as controlSave \n");
        sqlBuilder.append(",sum(case when (producttype_all ='" + ta + "' and save_amount_3c is not null) or " +
                "(producttype_all ='" + premium + "' and save_amount_premium is not null) or " +
                "save_amount_promotion != 0 or coalesce(saving_price, 0) != 0 then coalesce(corp_real_pay, 0) else 0 end)  " +
                "as sumCorpRealPay \n");
        // 每间夜节省
        sqlBuilder.append(String.format(", if(SUM(case when (producttype_all = '%s' and save_amount_3c is not null) or coalesce(save_amount_promotion, 0) <> 0 or " +
                " (producttype_all = '%s' and save_amount_premium is not null) or coalesce(saving_price, 0) != 0" +
                " then quantity else 0 end) != 0, " +
                " CAST(SUM(coalesce(save_amount_3c, 0)) + SUM(coalesce(save_amount_premium, 0)) + " +
                "SUM(coalesce(save_amount_promotion, 0)) + SUM(coalesce(saving_price, 0)) AS DOUBLE) / " +
                " CAST(SUM(case when (producttype_all = '%s' and save_amount_3c is not null) or coalesce(save_amount_promotion, 0) <> 0 or " +
                " (producttype_all = '%s' and save_amount_premium is not null) or coalesce(saving_price, 0) != 0 " +
                " then CAST(quantity AS DOUBLE) else 0 end) AS DOUBLE), " +
                " 0) as savePerQuantity ", ta, premium, ta, premium));
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.PRE_WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        boolean ignoreTenantId = false;
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            if (isBookCaliber) {
                sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(starTime, endTime, industryList, parmList, ORDERDT));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(starTime, endTime, industryList, parmList));
            }
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            if (isBookCaliber) {
                sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(starTime, endTime, parmList, ORDERDT));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(starTime, endTime, parmList));
            }
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        }
        sqlBuilder.append(getHotelCondition(productType));
        // 查询clickhouse
        return commonList(HotelSaveDTO.class, sqlBuilder.toString(), parmList, ignoreTenantId);
    }

    /**
     * 酒店累计节省(从有消费记录开始)
     *
     * @param requestDto
     * @param productType
     * @return
     * @throws Exception
     */
    
    public List<HotelSaveDTO> agreementSaveTotal(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 最早开始消费时间
        if (isBookCaliber) {
            sqlBuilder.append(String.format(" min(%s) as report_date \n", ORDERDT));
        } else {
            sqlBuilder.append(String.format(" min(%s) as report_date \n", REPORT_DATE));
        }
        // 三方协议的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_3c, 0)) as saveAmount3c \n");
        // 促销优惠活动的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_promotion, 0)) as saveAmountPromotion \n");
        // 两房尊享节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_premium, 0)) as saveAmountPremium \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as controlSave \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.PRE_WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList));
        sqlBuilder.append(getHotelCondition(productType));
        // 查询clickhouse
        return commonList(HotelSaveDTO.class, sqlBuilder.toString(), parmList);
    }

    /**
     * 酒店潜在节省
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> agreementPotentialSave(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 超标潜在节省金额
        sqlBuilder.append(" SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as overAmount \n");
        // 超标次数
        sqlBuilder.append(", count(DISTINCT case when is_mix_payment='F' and dead_price_onenight >0 and reason_code <>''and is_refund = 'F' then order_id end) AS rcTimes \n");
        // 取消损失节省金额
        sqlBuilder.append(", sum(coalesce(cancel_esti_save_amount,0)) as refundloss\n");
        // 取消次数
        sqlBuilder.append(", sum(coalesce(rfd_quantity,0)) as refundtkt\n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.PRE_WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 酒店潜在节省(从有消费记录开始)
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> agreementPotentialSaveTotal(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 最早时间
        if (isBookCaliber) {
            sqlBuilder.append(String.format("min(%s) as report_date \n", ORDERDT));
        } else {
            sqlBuilder.append(String.format("min(%s) as report_date \n", REPORT_DATE));
        }
        // 超标潜在节省金额
        sqlBuilder.append(", SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as overAmount \n");
        // 取消损失节省金额
        sqlBuilder.append(", sum(coalesce(cancel_esti_save_amount,0)) as refundloss\n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.PRE_WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList));
        sqlBuilder.append(getHotelCondition(productType));
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 酒店携程优惠活动节省明细
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> promotionSaveDetail(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 活动名称
        sqlBuilder.append(" tagname \n");
        // 优惠金额
        sqlBuilder.append(", sum(coalesce(net_cny_deductamount, 0)) as saveAmountPromotion \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.ADM_HTL_ORD_PROMOTION.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.ADM_HTL_ORD_PROMOTION));
        if (isBookCaliber) {
            sqlBuilder.append(buildPromotionsPreSql(requestDto, parmList, productType, "substr(orderdate, 1, 10)"));
        } else {
            sqlBuilder.append(buildPromotionsPreSql(requestDto, parmList, productType, "deal_date"));
        }
        sqlBuilder.append(" group by tagname  order by saveAmountPromotion desc");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 酒店超标原因分析
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> hotelLowRcAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String lang) throws Exception {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        boolean isEn = SharkUtils.isEN(lang);
        sqlBuilder.append(" SELECT  reason_code," + (isEn ? " low_reasoninfo_en " : " low_reasoninfo ") + " as reason_desc \n");
        sqlBuilder.append(" , SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) AS overAmount \n");
        sqlBuilder.append(" , count(DISTINCT case when is_mix_payment='F' and dead_price_onenight >0 and reason_code <>''and is_refund = 'F' then order_id end) AS rcTimes \n");
        sqlBuilder.append("from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(" and (reason_code <> '') ");
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getHotelCondition(productType));
        sqlBuilder.append(" group by ");
        sqlBuilder.append(" reason_code," + (isEn ? "low_reasoninfo_en " : "low_reasoninfo"));
        sqlBuilder.append(" order by overAmount desc");
        sqlBuilder.append(" ,reason_code," + (isEn ? "low_reasoninfo_en " : "low_reasoninfo"));
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "hotelLowRcAnalysis");
    }

    @Override
    public String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto
            , Pager pager, String productType) {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                baseQueryConditionDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuffer baseSql = new StringBuffer();
        ClickHouseTable clickHouseTable = getTargetTable();
        String partion = queryPartition(clickHouseTable);
        JoinCondition biz = joinCondition(analysisObjectEnum);
        if (isBookCaliber) {
            baseSql.append(String.format(TOP_DEPT_POTENTIAL_SAVE_SQL, biz.getResultFields(), biz.getGroupFields()
                    , BaseConditionPrebuilder.buildPreSqlWithCol(baseQueryConditionDto, parmList, partion, ORDERDT).concat(getHotelCondition(productType))
                    , biz.getGroupFields(),
                    BaseConditionPrebuilder.buildPreSqlWithCol(baseQueryConditionDto, parmList, partion, ORDERDT).concat(getHotelCondition(productType))
                    , biz.getGroupFields()));
        } else {
            baseSql.append(String.format(TOP_DEPT_POTENTIAL_SAVE_SQL, biz.getResultFields(), biz.getGroupFields()
                    , BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, partion).concat(getHotelCondition(productType))
                    , biz.getGroupFields(),
                    BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, partion).concat(getHotelCondition(productType))
                    , biz.getGroupFields()));
        }
        baseSql.append(" limit ?, ? ");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        return baseSql.toString();
    }

    @Override
    protected ClickHouseTable getTargetTable() {
        return ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
    }

    @Override
    public String countSql(List<Object> parmList, ClickHouseTable clickHouseTable, AnalysisObjectEnum analysisObjectEnum
            , BaseQueryConditionDTO baseQueryConditionDto, String productType) {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                baseQueryConditionDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        String partion = queryPartition(clickHouseTable);
        JoinCondition biz = joinCondition(analysisObjectEnum);
        if (isBookCaliber) {
            return String.format(TOP_DEPT_POTENTIAL_SAVE_SQL_COUNT,
                    BaseConditionPrebuilder.buildPreSqlWithCol(baseQueryConditionDto, parmList, partion, ORDERDT).concat(getHotelCondition(productType)),
                    biz.getGroupFields());
        } else {
            return String.format(TOP_DEPT_POTENTIAL_SAVE_SQL_COUNT,
                    BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, partion).concat(getHotelCondition(productType)),
                    biz.getGroupFields());
        }
    }

    /**
     * 优惠活动的查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public String buildPromotionsPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList, String productType, String dateColumn) {
        StringBuilder sqlBuffer = new StringBuilder(String.format(" and orderstatusdetail = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus")));
        // 国内、港澳台
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            sqlBuffer.append(" and is_oversea in ('F','O') ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            sqlBuffer.append(" and is_oversea in ('T') ");
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            sqlBuffer.append(BaseConditionPrebuilder.buildCorpAndAccount("corp_corporation", baseQueryCondition.getCorpIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getAccountIds())) {
            sqlBuffer.append(BaseConditionPrebuilder.buildCorpAndAccount("accountid", baseQueryCondition.getAccountIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCostCenterList())) {
            sqlBuffer.append(BaseConditionPrebuilder.buildDeptAndCostcenter("costcenter", baseQueryCondition.getCostCenterList(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getDeptList())) {
            sqlBuffer.append(BaseConditionPrebuilder.buildOrgId(baseQueryCondition.getDeptList(), parmList));
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(baseQueryCondition.getStartTime())) {
            sqlBuffer.append(String.format(" and %s >= ? ", dateColumn));
            parmList.add(baseQueryCondition.getStartTime());
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(baseQueryCondition.getEndTime())) {
            sqlBuffer.append(String.format(" and %s <= ? ", dateColumn));
            parmList.add(baseQueryCondition.getEndTime());
        }
        return sqlBuffer.toString();
    }

}
