package com.corpgovernment.resource.schedule.onlinereport.supplier.dto;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-09-06 14:14
 * @desc
 */
@Data
public class AgreementAggDTO {

    // 国内三方成交净价
    @Column(name = "totalPriceDomTa")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalPriceDomTa;

    // 国内非三方成交净价
    @Column(name = "totalPriceDomNta")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalPriceDomNta;

    // 国际三方成交净价
    @Column(name = "totalPriceInterTa")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalPriceInterTa;

    // 国际非三方成交净价
    @Column(name = "totalPriceInterNta")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalPriceInterNta;

    // 国内三方票张数
    @Column(name = "totalQuantityDomTa")
    @Type(value = Types.INTEGER)
    private Integer totalQuantityDomTa;

    // 国内非三方票张数
    @Column(name = "totalQuantityDomNta")
    @Type(value = Types.INTEGER)
    private Integer totalQuantityDomNta;

    // 国际三方票张数
    @Column(name = "totalQuantityInterTa")
    @Type(value = Types.INTEGER)
    private Integer totalQuantityInterTa;

    // 国际非三方票张数
    @Column(name = "totalQuantityInterNta")
    @Type(value = Types.INTEGER)
    private Integer totalQuantityInterNta;

}
