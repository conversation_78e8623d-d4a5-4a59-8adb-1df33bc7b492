package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TrendDimensionTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.OnlineReportBuEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/9 16:01 @description：
 * @modified By：
 * @version: $
 */
@Repository
@Slf4j
public class OnlineReportOtherBuTrendStarRocksDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "OnlineReportOtherBuTrendDao";
    private static final String DIM = "dim";
    private static final String DIM_DEFAULT = "''";
    private static final String CURRENT = "crt";
    private static final String CHAIN = "chain";
    private static final String YOY = "yoy";

    private static final HashMap<OnlineReportBuEnum, ClickHouseTable> TABLES =
            new HashMap<OnlineReportBuEnum, ClickHouseTable>() {
                {
                    put(OnlineReportBuEnum.FLIGHT, ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL);
                    put(OnlineReportBuEnum.HOTEL, ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL);
                    put(OnlineReportBuEnum.TRAIN, ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL);
                    put(OnlineReportBuEnum.CAR, ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL);
                }
            };

    private static final HashMap<OnlineReportBuEnum, ClickHouseTable> TABLES_ODT =
            new HashMap<OnlineReportBuEnum, ClickHouseTable>() {
                {
                    put(OnlineReportBuEnum.FLIGHT, ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL_ODT);
                    put(OnlineReportBuEnum.HOTEL, ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL_ODT);
                    put(OnlineReportBuEnum.TRAIN, ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL_ODT);
                    put(OnlineReportBuEnum.CAR, ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL_ODT);
                }
            };

    private static final HashMap<String, String> CHAIN_DURATION = new HashMap<String, String>() {
        {
            put("month", "1");
            put("quarter", "3");
            put("half", "6");
        }
    };

    private static final String JOIN_DATE_CONDITION_TEMPLATE =
            "" + "subStr(CAST(add_Months(cast({type} as date), {duration}) AS STRING), 1, 10)";

    private static final String OVERVIEW_SQL_TEMPLATE = "SELECT \n"
            + "  if(crt.date != '', crt.date, if(chain.date != '', {join_date_chain}, {join_date_yoy})) as date, \n"
            + "  if(cast(crt.dim as varchar) != '', cast(crt.dim as varchar), if(cast(chain.dim as varchar) != '', "
            + "cast(chain.dim as varchar), cast(yoy.dim as varchar))) as dim,\n" + "  coalesce(round(CASE \n"
            + "        WHEN coalesce(chain.amount, 0) != 0 THEN 1.0 * (coalesce(crt.amount, 0) - coalesce(chain.amount, 0)) / abs(coalesce(chain.amount, 0))\n"
            + "    END * 100, 2), 0) AS amountChain,\n" + "  coalesce(round(CASE \n"
            + "        WHEN coalesce(chain.quantity, 0) != 0 THEN 1.0 * (coalesce(crt.quantity, 0) - coalesce(chain.quantity, 0)) / abs(coalesce(chain.quantity, 0))\n"
            + "    END * 100, 2), 0) AS quantityChain,\n" + "  coalesce(round(CASE \n"
            + "        WHEN coalesce(yoy.amount, 0) != 0 THEN 1.0 * (coalesce(crt.amount, 0) - coalesce(yoy.amount, 0)) / abs(coalesce(yoy.amount, 0))\n"
            + "    END * 100, 2), 0) AS amountYoy,\n" + "  coalesce(round(CASE \n"
            + "        WHEN coalesce(yoy.quantity, 0) != 0 THEN 1.0 * (coalesce(crt.quantity, 0) - coalesce(yoy.quantity, 0)) / abs(coalesce(yoy.quantity, 0))\n"
            + "  END * 100, 2), 0) AS quantityYoy, " + "  coalesce(crt.amount, 0) as amount,\n"
            + "  coalesce(crt.quantity, 0) as quantity\n" + "FROM ({current_sql}) crt\n"
            + "FULL JOIN ({chain_sql}) chain on crt.date = {join_date_chain} and crt.dim = chain.dim\n"
            + "FULL JOIN ({yoy_sql}) yoy on crt.date = {join_date_yoy} and crt.dim = yoy.dim";


    /**
     * 碳排放
     */
    private static final String CARBON_SQL_TEMPLATE = "SELECT"
            + "  if(crt.date != '', crt.date, if(chain.date != '', {join_date_chain}, {join_date_yoy})) as date,"
            + "  coalesce(round(CASE "
            + "        WHEN coalesce(chain.carbons, 0) != 0 THEN 1.0 * (coalesce(crt.carbons, 0) - coalesce(chain.carbons, 0)) / abs(coalesce(chain.carbons, 0)) "
            + "    END * 100, 2), 0) AS carbonsChain,"
            + "  coalesce(round(CASE "
            + "        WHEN coalesce(chain.quantitys, 0) != 0 THEN 1.0 * (coalesce(crt.quantitys, 0) - coalesce(chain.quantitys, 0)) / abs(coalesce(chain.quantitys, 0)) "
            + "    END * 100, 2), 0) AS quantityChain,"
            + "  coalesce(round(CASE "
            + "        WHEN coalesce(yoy.carbons, 0) != 0 THEN 1.0 * (coalesce(crt.carbons, 0) - coalesce(yoy.carbons, 0)) / abs(coalesce(yoy.carbons, 0)) "
            + "    END * 100, 2), 0) AS carbonsYoy,"
            + "  coalesce(round(CASE "
            + "        WHEN coalesce(yoy.quantitys, 0) != 0 THEN 1.0 * (coalesce(crt.quantitys, 0) - coalesce(yoy.quantitys, 0)) / abs(coalesce(yoy.quantitys, 0)) "
            + "  END * 100, 2), 0) AS quantityYoy,"
            + "  coalesce(crt.carbons, 0) as carbons,"
            + "  coalesce(crt.quantitys, 0) as quantity,"
            + " chain.carbons as chainCarbonsNum,"
            + " yoy.carbons as yoyCarbonsNum "
            + "FROM ({current_sql}) crt "
            + "FULL JOIN ({chain_sql}) chain on crt.date = {join_date_chain} "
            + "FULL JOIN ({yoy_sql}) yoy on if(coalesce(crt.date,'') = '',{join_date_chain}, crt.date) = {join_date_yoy} ";

    /**
     * 查询 在线报告概况-消费金额趋势数据 - 概览 （bu + 字段名 -> 数据聚合维度 日期聚合 -> 数据聚合粒度 同环比)
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryOnlineReportCurrentOtherBuTrend(OnlineTrendRequestDto request, Class clazz)
            throws Exception {
        String sql;
        if (request.getDateDimension().equals(QueryReportAggDateDimensionEnum.day.toString())
                || request.getDateDimension().equals(QueryReportAggDateDimensionEnum.week.toString())) {
            sql = buildAccOverViewSql(request);
        } else {
            sql = buildCurrentOverViewSql(request);
        }
        /**
         * 当期时 为join 语句
         */
        return queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, request.getMethod());
    }

    public <T> List<T> queryOnlineReportAccOtherBuTrend(OnlineTrendRequestDto request, Class clazz) throws Exception {
        /**
         * 累计时为当期子语句
         */
        String sql = buildAccOverViewSql(request);
        return queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, request.getMethod());
    }

    public String buildAccOverViewSql(OnlineTrendRequestDto requestDto) {
        Map<String, String> extParams = Optional.ofNullable(requestDto.getExtParams()).orElse(Maps.newHashMap());
        String dim = extParams.getOrDefault(DIM, DIM_DEFAULT);
        String bu = requestDto.getBu();
        String trickyDim = trickyDim(bu, dim);
        String sqlTemplate;
        BaseQueryConditionDTO dto = requestDto.getBaseQueryCondition();
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(dto.getStatisticalCaliber(),
                StatiscalCaliberEnum.BOOKING.name());
        if (requestDto.getQueryType().equals("carbonEmission")) {
            sqlTemplate = fltCarbonTrend(isBookCaliber, requestDto.getDimensionType(), requestDto.getProductType()).toString();
        } else {
            sqlTemplate = otherBuAccTrend(bu, dto.getPos(), dto.getStatisticalCaliber(), dto.getBlueSpace(), dto.getCurrency()).toString();
        }
        return conditionWrapAsSubSql(trickyDim, requestDto, sqlTemplate, CURRENT, isBookCaliber);
    }

    public String buildCurrentOverViewSql(OnlineTrendRequestDto requestDto) {
        Map<String, String> extParams = requestDto.getExtParams();
        String dim = extParams.getOrDefault(DIM, DIM_DEFAULT);
        String bu = requestDto.getBu();
        String trickyDim = trickyDim(bu, dim);
        String sqlTemplate;
        BaseQueryConditionDTO dto = requestDto.getBaseQueryCondition();
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(dto.getStatisticalCaliber(),
                StatiscalCaliberEnum.BOOKING.name());
        if (requestDto.getQueryType().equals("carbonEmission")) {
            sqlTemplate = fltCarbonTrend(isBookCaliber, requestDto.getDimensionType(), requestDto.getProductType()).toString();
        } else {
            sqlTemplate = otherBuTrend(bu, dto.getPos(), dto.getStatisticalCaliber(), dto.getBlueSpace(), dto.getCurrency()).toString();
        }
        /**
         * 当前值sql
         */
        String currentSubSql = conditionWrapAsSubSql(trickyDim, requestDto, sqlTemplate, CURRENT, isBookCaliber);
        /**
         * 环比sql
         */
        String chainSubSql = conditionWrapAsSubSql(trickyDim, requestDto, sqlTemplate, CHAIN, isBookCaliber);
        /**
         * 同比去年sql
         */
        String yoySubSql = conditionWrapAsSubSql(trickyDim, requestDto, sqlTemplate, YOY, isBookCaliber);
        String overviewSql;
        if (requestDto.getQueryType().equals("carbonEmission")) {
            overviewSql = StringUtils.replace(CARBON_SQL_TEMPLATE, "{current_sql}", currentSubSql);
        } else {
            overviewSql = StringUtils.replace(OVERVIEW_SQL_TEMPLATE, "{current_sql}", currentSubSql);
        }
        overviewSql = StringUtils.replace(overviewSql, "{chain_sql}", chainSubSql);
        overviewSql = StringUtils.replace(overviewSql, "{yoy_sql}", yoySubSql);

        String chainJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "chain.date");
        chainJoin = StringUtils.replace(chainJoin, "{duration}", CHAIN_DURATION.get(requestDto.getDateDimension()));
        String yoyJoin = StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "yoy.date");
        yoyJoin = StringUtils.replace(yoyJoin, "{duration}", String.valueOf(requestDto.getYoyDuration()));
        overviewSql = StringUtils.replace(overviewSql, "{join_date_chain}", chainJoin);
        overviewSql = StringUtils.replace(overviewSql, "{join_date_yoy}", yoyJoin);
        return overviewSql;
    }

    /**
     * 碳排放
     */
    public StringBuilder fltCarbonTrend(Boolean isBookCaliber, TrendDimensionTypeEnum dimensionType, String productType) {
        /**
         * 整体与分产线 - 月/季/半年 - 日期
         */
        ClickHouseTable table = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String partition = queryPartition(table);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {group_date} as date, ");
        stringBuilder.append("sum(quantity) quantitys, ");
        if (TrendDimensionTypeEnum.TOTAL.equals(dimensionType)) {
            stringBuilder.append("CAST(sum(carbon_emission)/1000 AS DOUBLE) as carbons ");
        } else {
            stringBuilder.append("if(SUM(quantity) != 0,CAST(SUM(carbon_emission)/1000 AS DOUBLE)/SUM(quantity),0) as carbons ");
        }
        stringBuilder.append("from ");
        stringBuilder.append(table.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        /**
         * 预定
         */
        if (isBookCaliber) {
            stringBuilder.append("substr(order_date, 1, 10) >= '{start}' and substr(order_date, 1, 10) <= '{end}' ");
            stringBuilder.append(" and coalesce(order_date,'')!='' ");
            stringBuilder.append(" and ({scope_condition}) ");
            stringBuilder.append(" and coalesce({group_date},'')!='' ");
        } else {
            /**
             * 成交
             */
            stringBuilder.append("report_date >= '{start}' and report_date <= '{end}' ");
            stringBuilder.append(" and coalesce(report_date,'')!='' ");
            stringBuilder.append(" and ({scope_condition}) ");
            stringBuilder.append(" and coalesce({group_date},'')!='' ");
        }
        String feeType = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.corpPayType");
        stringBuilder.append(" and fee_type = '").append(feeType).append("' ");
        stringBuilder.append(" and audited <> 'F' ");
        stringBuilder.append(getFlightClassCondition(productType));
        stringBuilder.append("group by {group_date}");
        return stringBuilder;
    }

    /**
     * 碳排放
     */
    public StringBuilder carbonTrend(Boolean isBookCaliber, TrendDimensionTypeEnum dimensionType) {
        /**
         * 整体与分产线 - 月/季/半年 - 日期
         */
        ClickHouseTable table = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String partition = queryPartition(table);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {group_date} as date, ");
        stringBuilder.append("sum(quantity) quantitys, ");
        stringBuilder.append("CAST(sum(carbon_emission)/1000 AS DOUBLE) as carbons ");
        stringBuilder.append("from ");
        stringBuilder.append(table.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        /**
         * 预定
         */
        if (isBookCaliber) {
            stringBuilder.append("substr(order_date, 1, 10) >= '{start}' and substr(order_date, 1, 10) <= '{end}' ");
            stringBuilder.append(" and coalesce(order_date,'')!='' ");
            stringBuilder.append(" and ({scope_condition}) ");
            stringBuilder.append(" and coalesce({group_date},'')!='' ");
        } else {
            /**
             * 成交
             */
            stringBuilder.append("report_date >= '{start}' and report_date <= '{end}' ");
            stringBuilder.append(" and coalesce(report_date,'')!='' ");
            stringBuilder.append(" and ({scope_condition}) ");
            stringBuilder.append(" and coalesce({group_date},'')!='' ");
        }
        String feeType = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.corpPayType");
        stringBuilder.append(" and fee_type = '").append(feeType).append("' ");
        stringBuilder.append(" and audited <> 'F' ");
        stringBuilder.append("group by {group_date}");
        return stringBuilder;
    }

    public StringBuilder otherBuTrend(String bu, String pos, String statisticalCaliber, String blueSpace, String currency) {
        /**
         * 整体与分产线 - 月/季/半年 - 日期
         */
        ClickHouseTable clickHouseTable;
        OnlineReportBuEnum buEnum = OnlineReportBuEnum.valueOf(bu.toUpperCase());
        /**
         * 日本站要切换表
         */
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            if (buEnum == OnlineReportBuEnum.FLIGHT) {
                if (ConfigUtils.isMustCurrencySwitch()) {
                    // 多币种表
                    clickHouseTable = ClickHouseTable.ADM_INDEXFLIGHT_PRICE_DETAIL_FOREIGN_MULTI_CURRENCY;
                } else {
                    clickHouseTable = ClickHouseTable.ADM_INDEXFLIGHT_PRICE_DETAIL_FOREIGN;
                }
            } else if (buEnum == OnlineReportBuEnum.HOTEL) {
                if (ConfigUtils.isMustCurrencySwitch()) {
                    // 多币种表
                    clickHouseTable = ClickHouseTable.ADM_INDEXHOTEL_PRICE_DETAIL_FOREIGN_MULTI_CURRENCY;
                } else {
                    clickHouseTable = ClickHouseTable.ADM_INDEXHOTEL_PRICE_DETAIL_FOREIGN;
                }
            } else if (buEnum == OnlineReportBuEnum.TRAIN) {
                clickHouseTable = ClickHouseTable.ADM_INDEXTRAIN_PRICE_DETAIL_FOREIGN;
            } else {
                clickHouseTable = TABLES.get(buEnum);
            }
        } else {
            if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = TABLES_ODT.get(buEnum);
            } else {
                // 默认使用成交口径
                clickHouseTable = TABLES.get(buEnum);
            }
        }
        String partition = queryPartition(clickHouseTable);

        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("select {group_date} as date, {group_dim} as dim, ");
        if (bu.equals(OnlineReportBuEnum.HOTEL.getName())) {
            stringBuilder.append("CAST(sum(real_pay_with_servicefee ) AS DOUBLE) amount, ");
        } else {
            stringBuilder.append("CAST(sum(real_pay) AS DOUBLE) amount, ");
        }
        if (bu.equals(OnlineReportBuEnum.CAR.getName())) {
            stringBuilder.append("sum(cnt_order) quantity ");
        } else {
            stringBuilder.append("sum(quantity) quantity ");
        }
        stringBuilder.append("from ");
        stringBuilder.append(clickHouseTable.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        stringBuilder.append("report_date >= '{start}' and report_date <= '{end}' and ({scope_condition})  ");
        // 酒店筛选已成交的数据
        if (bu.toUpperCase().equals(OnlineReportBuEnum.HOTEL.name())) {
            stringBuilder.append("and order_status = '" + OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus") + "' ");
        }
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(currency)) {
                // 币种条件
                stringBuilder.append(String.format(" and termcurrency = '%s'", currency));
            }
        }
        stringBuilder.append("group by {group_date}, {group_dim}");
        return stringBuilder;
    }

    public StringBuilder otherBuAccTrend(String bu, String pos, String statisticalCaliber, String blueSpace, String currency) {
        // 整体与分产线 - 月/季/半年 - 日期
        ClickHouseTable clickHouseTable;
        OnlineReportBuEnum buEnum = OnlineReportBuEnum.valueOf(bu.toUpperCase());
        // 日本站要切换表
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            if (buEnum == OnlineReportBuEnum.FLIGHT) {
                if (ConfigUtils.isMustCurrencySwitch()) {
                    // 多币种表
                    clickHouseTable = ClickHouseTable.ADM_INDEXFLIGHT_PRICE_DETAIL_FOREIGN_MULTI_CURRENCY;
                } else {
                    clickHouseTable = ClickHouseTable.ADM_INDEXFLIGHT_PRICE_DETAIL_FOREIGN;
                }
            } else if (buEnum == OnlineReportBuEnum.HOTEL) {
                if (ConfigUtils.isMustCurrencySwitch()) {
                    // 多币种表
                    clickHouseTable = ClickHouseTable.ADM_INDEXHOTEL_PRICE_DETAIL_FOREIGN_MULTI_CURRENCY;
                } else {
                    clickHouseTable = ClickHouseTable.ADM_INDEXHOTEL_PRICE_DETAIL_FOREIGN;
                }
            } else if (buEnum == OnlineReportBuEnum.TRAIN) {
                clickHouseTable = ClickHouseTable.ADM_INDEXTRAIN_PRICE_DETAIL_FOREIGN;
            } else {
                clickHouseTable = TABLES.get(buEnum);
            }
        } else {
            if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = TABLES_ODT.get(buEnum);
            } else {
                // 默认使用成交口径
                clickHouseTable = TABLES.get(buEnum);
            }
        }
        String partition = queryPartition(clickHouseTable);

        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("select {group_date} as date, {group_dim} as dim, ");
        if (bu.equals(OnlineReportBuEnum.HOTEL.getName())) {
            stringBuilder.append("sum(real_pay_with_servicefee) amount, ");
        } else {
            stringBuilder.append("sum(real_pay) amount, ");
        }
        if (bu.equals(OnlineReportBuEnum.CAR.getName())) {
            stringBuilder.append("sum(cnt_order) quantity ");
        } else {
            stringBuilder.append("sum(quantity) quantity ");
        }
        stringBuilder.append("from ");
        stringBuilder.append(clickHouseTable.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        stringBuilder.append("report_date >= '{start}' and report_date <= '{end}' and ({scope_condition})  ");
        // 酒店筛选已成交的数据
        if (bu.toUpperCase().equals(OnlineReportBuEnum.HOTEL.name())) {
            stringBuilder.append("and order_status = '" + OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus") + "' ");
        }
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(currency)) {
                // 币种条件
                stringBuilder.append(String.format(" and termcurrency = '%s'", currency));
            }
        }
        stringBuilder.append("group by {group_date}, {group_dim}");
        return stringBuilder;
    }

    private String conditionWrapAsSubSql(String dim, OnlineTrendRequestDto requestDto, String sqlTemplate, String dimType, boolean isBookCaliber) {
        String group_date = OrpConstants.EMPTY;
        if (requestDto.getQueryType().equals("carbonEmission")) {
            switch (requestDto.getDateDimension()) {
                case "month":
                    group_date = isBookCaliber ? "firstday_of_month_odt" : "firstday_of_month";
                    break;
                case "half":
                    group_date = isBookCaliber ? "firstday_of_halfyear_odt" : "firstday_of_halfyear";
                    break;
                case "quarter":
                    group_date = isBookCaliber ? "firstday_of_quarter_odt" : "firstday_of_quarter";
                    break;
                default:
                    break;
            }
        } else {
            switch (requestDto.getDateDimension()) {
                case "month":
                    group_date = "firstday_of_month";
                    break;
                case "half":
                    group_date = "firstday_of_halfyear";
                    break;
                case "quarter":
                    group_date = "firstday_of_quarter";
                    break;
                case "day":
                    group_date = "substr(report_date, 1, 10)";
                    break;
                case "week":
                    // 获得report_date所在周的第一天
                    group_date = "dateTrunc('week',CAST(report_date AS DATETIME))";
                    break;
                default:
                    break;
            }
        }

        String start = requestDto.getStartTime();
        String end = requestDto.getEndTime();
        if (CURRENT.equals(dimType)) {
            start = requestDto.getStartTime();
            end = requestDto.getEndTime();
        } else if (CHAIN.equals(dimType)) {
            start = requestDto.getChainStartTime();
            end = requestDto.getChainEndTime();
        } else if (YOY.equals(dimType)) {
            start = requestDto.getYoyStartTime();
            end = requestDto.getYoyEndTime();
        }
        String scope_condition = OnlineReportOverviewTrendDao.buildScopeFilter(requestDto.getBaseQueryCondition());
        return sqlTemplate.replace("{group_date}", group_date).replace("{group_dim}", dim)
                .replace("{scope_condition}", scope_condition).replace("{start}", start).replace("{end}", end);
    }

    private PreparedStatement mapRequest(OnlineTrendRequestDto requestDto, PreparedStatement statement) {
        return statement;
    }

    private String trickyDim(String bu, String dim) {
        HashSet<String> validDims = new HashSet<String>() {
            {
                add("''");
                add("flight_class");
                add("contract_type");
                add("is_oversea");
                add("order_type");
            }
        };
        if (!validDims.contains(dim)) {
            return "''";
        }
        String trickedDim = dim;
        if (bu.equals("hotel") && dim.equals("is_oversea")) {
            trickedDim = "case when is_oversea in ('F', 'O') then 'O/F' else is_oversea end";
        }
        if (bu.equals("hotel") && dim.equals("order_type")) {
            String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
            // C: 三方协议， M:非三方协议
            trickedDim = "case when producttype_all = '" + ta + "' then 'C' else 'M' end";
        }
        if (bu.equals("flight") && dim.equals("contract_type")) {
            String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
            // C: 协议， NC:非协议
            trickedDim = "case when (agreement_type_name='" + ta + "' or agreement_type_name = 'B2G') then 'C' else 'NC' end";
        }
        if (bu.equals("car") && dim.equals("order_type")) {
            trickedDim = "case when (order_type = 6 and sub_product_line = '1') then '6/1' " +
                    "when (order_type = 6 and sub_product_line = 'CAR_TAXI_INTL') then '6/CAR_TAXI_INTL' else cast(order_type as varchar) end";
        }
        return trickedDim;
    }

    /**
     * 机票类型查询条件
     *
     * @param flightClass
     * @return
     */
    protected String getFlightClassCondition(String flightClass) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(flightClass, "dom")) {
            stringBuilder.append(" and flight_class = 'N'  ");
        }
        // 海外
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(flightClass, "inter")) {
            stringBuilder.append(" and flight_class = 'I'  ");
        }
        return stringBuilder.toString();
    }
}
