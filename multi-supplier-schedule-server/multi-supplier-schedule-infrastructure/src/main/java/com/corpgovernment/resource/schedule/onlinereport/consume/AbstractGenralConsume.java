package com.corpgovernment.resource.schedule.onlinereport.consume;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.OnlineReportConsumeBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportConsume;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportConsumeRequest;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;

import java.math.BigDecimal;
import java.util.List;

/*
 * <AUTHOR>
 * @date 2021/12/13 20:32
 * @Desc
 */
public abstract class AbstractGenralConsume implements GenralConsumeBiz<OnlineReportConsumeRequest> {

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    
    public OnlineReportConsume aggreationGenralConsume(OnlineReportConsumeRequest request) throws Exception {
        OnlineReportConsume genralConsume = new OnlineReportConsume();
        BaseQueryCondition baseQueryCondition = request.getBasecondition();
        // 金额日本站点取整
        int precision = OrpReportUtils.amountDecimalPlacesZero(baseQueryCondition.getCurrency(), baseQueryCondition.getPos()) ? OrpConstants.ZERO : OrpConstants.TWO;
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(baseQueryCondition);
        int intervalMonths = OrpDateTimeUtils.getMonthNum(baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime());
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        // 当前
        OnlineReportConsumeBO genralConsumeCurrent = aggreationCurrentConsume(baseQueryConditionDto);

        // 环比
        List<String> momTimes = OrpDateTimeUtils.momTime(startTime, endTime);
        baseQueryConditionDto.setStartTime(momTimes.get(0));
        baseQueryConditionDto.setEndTime(momTimes.get(1));
        OnlineReportConsumeBO genralConsumeMom = aggreationGenralConsume(baseQueryConditionDto);

        // 同比去年
        List<String> yoyTimes = OrpDateTimeUtils.yoyTime(startTime, endTime, OrpConstants.ONE_YEAR_DAYS);

        baseQueryConditionDto.setStartTime(yoyTimes.get(0));
        baseQueryConditionDto.setEndTime(yoyTimes.get(1));
        OnlineReportConsumeBO genralConsumeYoyLast = aggreationGenralConsume(baseQueryConditionDto);

        // 同比前年
        List<String> yoyBeforeTimes = OrpDateTimeUtils.yoyTime(startTime, endTime, OrpConstants.TWO_YEAR_DAYS);
        baseQueryConditionDto.setStartTime(yoyBeforeTimes.get(0));
        baseQueryConditionDto.setEndTime(yoyBeforeTimes.get(1));
        OnlineReportConsumeBO genralConsumeYoyBeforeLast = aggreationGenralConsume(baseQueryConditionDto);

        genralConsume = cal(genralConsumeCurrent, genralConsumeMom, genralConsumeYoyLast, genralConsumeYoyBeforeLast, intervalMonths, precision);
        return genralConsume;
    }

    private BaseQueryConditionDTO getDto(OnlineReportConsumeRequest request,
                                         String startTime,
                                         String endTime) {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        if (request.getExtData() != null) {
            baseQueryConditionDto.setQueryDataType(request.getExtData().get("queryDataType"));
        }
        baseQueryConditionDto.setStartTime(startTime);
        baseQueryConditionDto.setEndTime(endTime);
        return baseQueryConditionDto;
    }

    protected abstract BigDecimal getTotalAmount1(OnlineReportConsumeBO genralConsumeCurrent);

    protected abstract Integer getTotalQuantity1(OnlineReportConsumeBO genralConsumeCurrent);

    protected abstract BigDecimal getTotalAmount3(OnlineReportConsumeBO genralConsumeCurrent);

    protected abstract Integer getTotalQuantity3(OnlineReportConsumeBO genralConsumeCurrent);

    // aggreationGenralConsume 和aggreationCurrentConsume 统计指标不一样

    /**
     * 统计消费金额、票张、间夜、订单
     *
     * @param baseQueryConditionDto
     * @return
     * @throws Exception
     */
    protected abstract OnlineReportConsumeBO aggreationGenralConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception;

    /**
     * 统计消费金额、票张、间夜、订单、均价、退改
     *
     * @param baseQueryConditionDto
     * @return
     * @throws Exception
     */
    protected abstract OnlineReportConsumeBO aggreationCurrentConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception;

    /**
     * 转化并计算
     *
     * @param genralConsumeCurrent       当前
     * @param genralConsumeMom           环比
     * @param genralConsumeYoyLast       同比去年
     * @param genralConsumeYoyBeforeLast 同比前年
     * @param intervalMonths             间隔月数
     * @param precision                  小数位数
     * @return
     */
    protected OnlineReportConsume cal(OnlineReportConsumeBO genralConsumeCurrent, OnlineReportConsumeBO genralConsumeMom, OnlineReportConsumeBO genralConsumeYoyLast
            , OnlineReportConsumeBO genralConsumeYoyBeforeLast, int intervalMonths, int precision) {
        OnlineReportConsume genralConsume = new OnlineReportConsume();
        genralConsume.setTotalAmount(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getTotalAmount(), precision));
        genralConsume.setTotalOneAmount(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getTotalOneAmount(), precision));
        genralConsume.setTotalTwoAmount(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getTotalTwoAmount(), precision));
        genralConsume.setTotalThreeAmount(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getTotalThreeAmount(), precision));
        genralConsume.setTotalFourAmount(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getTotalFourAmount(), precision));
        genralConsume.setTotalFiveAmount(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getTotalFiveAmount(), precision));
        genralConsume.setTotalSixAmount(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getTotalSixAmount(), precision));
        genralConsume.setTotalSevenAmount(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getTotalSevenAmount(), precision));
        genralConsume.setTotalCntOrder(genralConsumeCurrent.getTotalCntOrder());

        // 占比分母金额，需剔除负数数据
        BigDecimal percentCalTotalAmount1 = getTotalAmount1(genralConsumeCurrent);
        BigDecimal percentCalTotalAmount3 = getTotalAmount3(genralConsumeCurrent);

        genralConsume.setTotalQuantity(genralConsumeCurrent.getTotalQuantity());
        genralConsume.setTotalOneQuantity(genralConsumeCurrent.getTotalOneQuantity());
        genralConsume.setTotalTwoQuantity(genralConsumeCurrent.getTotalTwoQuantity());
        genralConsume.setTotalThreeQuantity(genralConsumeCurrent.getTotalThreeQuantity());
        genralConsume.setTotalFourQuantity(genralConsumeCurrent.getTotalFourQuantity());
        genralConsume.setTotalFiveQuantity(genralConsumeCurrent.getTotalFiveQuantity());
        genralConsume.setTotalSixQuantity(genralConsumeCurrent.getTotalSixQuantity());
        genralConsume.setTotalSevenQuantity(genralConsumeCurrent.getTotalSevenQuantity());


        int percentCalTotalQuantity1 = getTotalQuantity1(genralConsumeCurrent);
        int percentCalTotalQuantity3 = getTotalQuantity3(genralConsumeCurrent);

        genralConsume.setTotalCorpServiceFee(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getTotalCorpServiceFee(), precision));
        genralConsume.setAvgPrice(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getAvgPrice(), precision));
        genralConsume.setAvgOtherPrice(OrpReportUtils.formatBigDecimal(genralConsumeCurrent.getAvgOtherPrice(), precision));
        genralConsume.setRefundQuantity(genralConsumeCurrent.getTotalRefundQuantity());
        genralConsume.setReBookQuantity(genralConsumeCurrent.getTotalRebookQuantity());

        // total 同比
        genralConsume.setYoyAmountLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalAmount().subtract(genralConsumeYoyLast.getTotalAmount())
                , genralConsumeYoyLast.getTotalAmount().abs()));
        genralConsume.setYoyQuantityLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalQuantity() - genralConsumeYoyLast.getTotalQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyLast.getTotalQuantity()))));
        genralConsume.setYoyAmountBeforeLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalAmount().subtract(genralConsumeYoyBeforeLast.getTotalAmount())
                , genralConsumeYoyBeforeLast.getTotalAmount().abs()));
        genralConsume.setYoyQuantityBeforeLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalQuantity()
                        - genralConsumeYoyBeforeLast.getTotalQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyBeforeLast.getTotalQuantity()))));
        genralConsume.setMomAmount(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalAmount().subtract(genralConsumeMom.getTotalAmount())
                , genralConsumeMom.getTotalAmount().abs()));
        genralConsume.setMomQuantity(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalQuantity() - genralConsumeMom.getTotalQuantity())
                , new BigDecimal(Math.abs(genralConsumeMom.getTotalQuantity()))));

        // one 同环比
        genralConsume.setYoyOneAmountLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalOneAmount().subtract(genralConsumeYoyLast.getTotalOneAmount())
                , genralConsumeYoyLast.getTotalOneAmount().abs()));
        genralConsume.setYoyOneQuantityLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalOneQuantity() - genralConsumeYoyLast.getTotalOneQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyLast.getTotalOneQuantity()))));
        genralConsume.setYoyOneAmountBeforeLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalOneAmount()
                        .subtract(genralConsumeYoyBeforeLast.getTotalOneAmount())
                , genralConsumeYoyBeforeLast.getTotalOneAmount().abs()));
        genralConsume.setYoyOneQuantityBeforeLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalOneQuantity()
                        - genralConsumeYoyBeforeLast.getTotalOneQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyBeforeLast.getTotalOneQuantity()))));
        genralConsume.setMomOneAmount(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalOneAmount().subtract(genralConsumeMom.getTotalOneAmount())
                , genralConsumeMom.getTotalOneAmount().abs()));
        genralConsume.setMomOneQuantity(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalOneQuantity() - genralConsumeMom.getTotalOneQuantity())
                , new BigDecimal(Math.abs(genralConsumeMom.getTotalOneQuantity()))));
        // one 占比
        genralConsume.setOneAmountPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneAmount())
                , percentCalTotalAmount1));
        genralConsume.setOneQuantityPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(new BigDecimal(genralConsumeCurrent.getTotalOneQuantity()))
                , new BigDecimal(percentCalTotalQuantity1)));

        // two 同环比
        genralConsume.setYoyTwoAmountLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalTwoAmount().subtract(genralConsumeYoyLast.getTotalTwoAmount())
                , genralConsumeYoyLast.getTotalTwoAmount().abs()));
        genralConsume.setYoyTwoQuantityLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalTwoQuantity()
                        - genralConsumeYoyLast.getTotalTwoQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyLast.getTotalTwoQuantity()))));
        genralConsume.setYoyTwoAmountBeforeLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalTwoAmount().subtract(genralConsumeYoyBeforeLast.getTotalTwoAmount())
                , genralConsumeYoyBeforeLast.getTotalTwoAmount().abs()));
        genralConsume.setYoyTwoQuantityBeforeLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalTwoQuantity()
                        - genralConsumeYoyBeforeLast.getTotalTwoQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyBeforeLast.getTotalTwoQuantity()))));
        genralConsume.setMomTwoAmount(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalTwoAmount().subtract(genralConsumeMom.getTotalTwoAmount())
                , genralConsumeMom.getTotalTwoAmount().abs()));
        genralConsume.setMomTwoQuantity(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalTwoQuantity() - genralConsumeMom.getTotalTwoQuantity())
                , new BigDecimal(Math.abs(genralConsumeMom.getTotalTwoQuantity()))));
        // two 占比
        genralConsume.setTwoAmountPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalTwoAmount())
                , percentCalTotalAmount1));
        genralConsume.setTwoQuantityPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(new BigDecimal(genralConsumeCurrent.getTotalTwoQuantity()))
                , new BigDecimal(percentCalTotalQuantity1)));

        // three 同环比
        genralConsume.setYoyThreeAmountLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalThreeAmount().subtract(genralConsumeYoyLast.getTotalThreeAmount())
                , genralConsumeYoyLast.getTotalThreeAmount().abs()));
        genralConsume.setYoyThreeQuantityLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalThreeQuantity()
                        - genralConsumeYoyLast.getTotalThreeQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyLast.getTotalThreeQuantity()))));
        genralConsume.setYoyThreeAmountBeforeLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalThreeAmount()
                        .subtract(genralConsumeYoyBeforeLast.getTotalThreeAmount())
                , genralConsumeYoyBeforeLast.getTotalThreeAmount().abs()));
        genralConsume.setYoyThreeQuantityBeforeLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalThreeQuantity()
                        - genralConsumeYoyBeforeLast.getTotalThreeQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyBeforeLast.getTotalQuantity()))));
        genralConsume.setMomThreeAmount(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalThreeAmount().subtract(genralConsumeMom.getTotalThreeAmount())
                , genralConsumeMom.getTotalThreeAmount().abs()));
        genralConsume.setMomThreeQuantity(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalThreeQuantity()
                        - genralConsumeMom.getTotalThreeQuantity())
                , new BigDecimal(Math.abs(genralConsumeMom.getTotalThreeQuantity()))));
        // three 占比
        genralConsume.setThreeAmountPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalThreeAmount())
                , percentCalTotalAmount3));
        genralConsume.setThreeQuantityPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(new BigDecimal(genralConsumeCurrent.getTotalThreeQuantity()))
                , new BigDecimal(percentCalTotalQuantity3)));

        // four 同环比
        genralConsume.setYoyFourAmountLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalFourAmount().subtract(genralConsumeYoyLast.getTotalFourAmount())
                , genralConsumeYoyLast.getTotalFourAmount().abs()));
        genralConsume.setYoyFourQuantityLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalFourQuantity()
                        - genralConsumeYoyLast.getTotalFourQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyLast.getTotalFourQuantity()))));
        genralConsume.setYoyFourAmountBeforeLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalFourAmount()
                        .subtract(genralConsumeYoyBeforeLast.getTotalFourAmount())
                , genralConsumeYoyBeforeLast.getTotalFourAmount().abs()));
        genralConsume.setYoyFourQuantityBeforeLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalFourQuantity()
                        - genralConsumeYoyBeforeLast.getTotalFourQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyBeforeLast.getTotalFourQuantity()))));
        genralConsume.setMomFourAmount(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalFourAmount().subtract(genralConsumeMom.getTotalFourAmount())
                , genralConsumeMom.getTotalFourAmount().abs()));
        genralConsume.setMomFourQuantity(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalFourQuantity()
                        - genralConsumeMom.getTotalFourQuantity())
                , new BigDecimal(Math.abs(genralConsumeMom.getTotalFourQuantity()))));
        // four 占比
        genralConsume.setFourAmountPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFourAmount())
                , percentCalTotalAmount3));
        genralConsume.setFourQuantityPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(new BigDecimal(genralConsumeCurrent.getTotalFourQuantity()))
                , new BigDecimal(percentCalTotalQuantity3)));

        // five 同环比
        genralConsume.setYoyFiveAmountLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalFiveAmount().subtract(genralConsumeYoyLast.getTotalFiveAmount())
                , genralConsumeYoyLast.getTotalFiveAmount().abs()));
        genralConsume.setYoyFiveQuantityLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalFiveQuantity()
                        - genralConsumeYoyLast.getTotalFiveQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyLast.getTotalFiveQuantity()))));
        genralConsume.setYoyFiveAmountBeforeLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalFiveAmount()
                        .subtract(genralConsumeYoyBeforeLast.getTotalFiveAmount())
                , genralConsumeYoyBeforeLast.getTotalFiveAmount().abs()));
        genralConsume.setYoyFiveQuantityBeforeLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalFiveQuantity()
                        - genralConsumeYoyBeforeLast.getTotalFiveQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyBeforeLast.getTotalFiveQuantity()))));
        genralConsume.setMomFiveAmount(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalFiveAmount().subtract(genralConsumeMom.getTotalFiveAmount())
                , genralConsumeMom.getTotalFiveAmount().abs()));
        genralConsume.setMomFiveQuantity(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalFiveQuantity()
                        - genralConsumeMom.getTotalFiveQuantity())
                , new BigDecimal(Math.abs(genralConsumeMom.getTotalFiveQuantity()))));
        // five 占比
        genralConsume.setFiveAmountPercentage(OrpReportUtils.divideWithPercent(
                genralConsumeCurrent.getTotalFiveAmount()
                , percentCalTotalAmount3));
        genralConsume.setFiveQuantityPercentage(OrpReportUtils.divideWithPercent(
                new BigDecimal(genralConsumeCurrent.getTotalFiveQuantity())
                , new BigDecimal(percentCalTotalQuantity3)));

        // six 同环比
        genralConsume.setYoySixAmountLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalSixAmount().subtract(genralConsumeYoyLast.getTotalSixAmount())
                , genralConsumeYoyLast.getTotalSixAmount().abs()));
        genralConsume.setYoySixQuantityLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalSixQuantity()
                        - genralConsumeYoyLast.getTotalSixQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyLast.getTotalSixQuantity()))));
        genralConsume.setYoySixAmountBeforeLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalSixAmount()
                        .subtract(genralConsumeYoyBeforeLast.getTotalSixAmount())
                , genralConsumeYoyBeforeLast.getTotalSixAmount().abs()));
        genralConsume.setYoySixQuantityBeforeLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalSixQuantity()
                        - genralConsumeYoyBeforeLast.getTotalSixQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyBeforeLast.getTotalSixQuantity()))));
        genralConsume.setMomSixAmount(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalSixAmount().subtract(genralConsumeMom.getTotalSixAmount())
                , genralConsumeMom.getTotalSixAmount().abs()));
        genralConsume.setMomSixQuantity(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalSixQuantity()
                        - genralConsumeMom.getTotalSixQuantity())
                , new BigDecimal(Math.abs(genralConsumeMom.getTotalSixQuantity()))));
        // six 占比
        genralConsume.setSixAmountPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalSixAmount())
                , percentCalTotalAmount3));
        genralConsume.setSixQuantityPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(new BigDecimal(genralConsumeCurrent.getTotalSixQuantity()))
                , new BigDecimal(percentCalTotalQuantity3)));

        // seven 同环比
        genralConsume.setYoySevenAmountLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalSevenAmount().subtract(genralConsumeYoyLast.getTotalSevenAmount())
                , genralConsumeYoyLast.getTotalSevenAmount().abs()));
        genralConsume.setYoySevenQuantityLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalSevenQuantity()
                        - genralConsumeYoyLast.getTotalSevenQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyLast.getTotalSevenQuantity()))));
        genralConsume.setYoySevenAmountBeforeLast(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalSevenAmount()
                        .subtract(genralConsumeYoyBeforeLast.getTotalSevenAmount())
                , genralConsumeYoyBeforeLast.getTotalSevenAmount().abs()));
        genralConsume.setYoySevenQuantityBeforeLast(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalSevenQuantity()
                        - genralConsumeYoyBeforeLast.getTotalSevenQuantity())
                , new BigDecimal(Math.abs(genralConsumeYoyBeforeLast.getTotalSevenQuantity()))));
        genralConsume.setMomSevenAmount(OrpReportUtils.divideWithPercent(genralConsumeCurrent.getTotalSevenAmount().subtract(genralConsumeMom.getTotalSevenAmount())
                , genralConsumeMom.getTotalSevenAmount().abs()));
        genralConsume.setMomSevenQuantity(OrpReportUtils.divideWithPercent(new BigDecimal(genralConsumeCurrent.getTotalSevenQuantity()
                        - genralConsumeMom.getTotalSevenQuantity())
                , new BigDecimal(Math.abs(genralConsumeMom.getTotalSevenQuantity()))));
        // seven 占比
        genralConsume.setSevenAmountPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalSevenAmount())
                , percentCalTotalAmount3));
        genralConsume.setSevenQuantityPercentage(OrpReportUtils.divideWithPercent(
                OrpReportUtils.nonNegative(new BigDecimal(genralConsumeCurrent.getTotalSevenQuantity()))
                , new BigDecimal(percentCalTotalQuantity3)));
        // 平均
        genralConsume.setAvgMonthAmount(OrpReportUtils.divide(genralConsumeCurrent.getTotalAmount()
                , new BigDecimal(intervalMonths + OrpConstants.ONE), precision));
        genralConsume.setAvgMonthQuantity(OrpReportUtils.divide(new BigDecimal(genralConsumeCurrent.getTotalQuantity())
                , new BigDecimal(intervalMonths + OrpConstants.ONE), OrpConstants.ZERO));
        return genralConsume;

    }
}
