package com.corpgovernment.resource.schedule.onlinereport.supplier.dto;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * 协议详情
 *
 * <AUTHOR>
 * @date 2024/7/7
 */
@Data
public class AgreementDetailDto {
    // 维度id
    @Column(name = "dim_id")
    @Type(value = Types.VARCHAR)
    private String dimId;
    // 维度
    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;
    // 协议id
    @Column(name = "agreement_id")
    @Type(value = Types.BIGINT)
    private Long agreementId;
    // 协议名称
    @Column(name = "agreement_name")
    @Type(value = Types.VARCHAR)
    private String agreementName;
    private String agreementNameCn;
    // 协议状态
    @Column(name = "agreement_status_label")
    @Type(value = Types.VARCHAR)
    private String agreementStatusLabel;
    // 协议创建日期
    @Column(name = "agreement_create_date")
    @Type(value = Types.VARCHAR)
    private String agreementCreateDate;
    // 协议开始日期
    @Column(name = "agreement_start_date")
    @Type(value = Types.VARCHAR)
    private String agreementStartDate;
    // 协议结束日期
    @Column(name = "agreement_end_date")
    @Type(value = Types.VARCHAR)
    private String agreementEndDate;

}
