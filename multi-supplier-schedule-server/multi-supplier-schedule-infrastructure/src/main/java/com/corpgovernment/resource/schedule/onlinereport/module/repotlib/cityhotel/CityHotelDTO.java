package com.corpgovernment.resource.schedule.onlinereport.module.repotlib.cityhotel;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-10-13 11:26
 * @desc
 */
@Data
public class CityHotelDTO {

    // 订单号
    @Column(name = "city")
    @Type(value = Types.BIGINT)
    private Long city;

    // 订单号
    @Column(name = "cityName")
    @Type(value = Types.VARCHAR)
    private String cityName;

    // 订单状态
    @Column(name = "sumRealPay")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumRealPay;

    // 订单预定时间
    @Column(name = "sumQuantity")
    @Type(value = Types.INTEGER)
    private Integer sumQuantity;

    // 公司ID
    @Column(name = "avgPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgPrice;

    // 公司集团ID
    @Column(name = "sumRealPayC")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumRealPayC;

    // 公司集团
    @Column(name = "sumQuantityC")
    @Type(value = Types.INTEGER)
    private Integer sumQuantityC;

    // 主账户账号
    @Column(name = "avgPriceC")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgPriceC;

    // 主账户代号
    @Column(name = "avgPriceCCorp")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgPriceCCorp;

    // 主账户公司名称
    @Column(name = "avgPriceCIndustry")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgPriceCIndustry;

    // 子账户账号
    @Column(name = "sumRealPayM")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumRealPayM;

    // 子账户代号
    @Column(name = "sumQuantityM")
    @Type(value = Types.INTEGER)
    private Integer sumQuantityM;

    // 子账户公司名称
    @Column(name = "avgPriceM")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgPriceM;

    // 行业类型
    @Column(name = "avgPriceMCorp")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgPriceMCorp;

    // 行业名称
    @Column(name = "avgPriceMIndustry")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgPriceMIndustry;
}
