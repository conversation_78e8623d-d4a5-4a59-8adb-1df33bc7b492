package com.corpgovernment.resource.schedule.hotelaudit.mysql.mapper;

import com.corpgovernment.resource.schedule.hotelaudit.mysql.entity.HotelInfoDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025-01-23 11:25
 */
@Mapper
public interface IHotelInfoMapper {
    
    List<HotelInfoDo> selectHotelInfoByOrderIdList(@Param("orderIdList") List<String> orderIdList);
    
    List<HotelInfoDo> selectHotelIntlInfoByOrderIdList(@Param("orderIdList") List<String> orderIdList);
    
}
