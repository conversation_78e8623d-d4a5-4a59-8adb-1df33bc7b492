package com.corpgovernment.resource.schedule.onlinereport.clickhouse.base;

import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.resource.schedule.domain.ResponseCodeEnum;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.BinaryExpression;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.util.TablesNamesFinder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.Stack;
import java.util.stream.Collectors;

/**
 * ClickHouse SQL解析器<br>
 * 目前用于两点：<br>
 * 1. 为SQL语句添加租户条件<br>
 * 2. 加密查询条件值<br>
 *
 * <AUTHOR> Smith
 * <AUTHOR>
 */
@Slf4j
public class CKSqlParser {

    /**
     * 用于跟踪表别名和表名的栈
     */
    private final Stack<String> aliasStack = new Stack<>();
    /**
     * 用于记录已处理的SelectBody，避免重复处理
     */
    private final Set<SelectBody> processedSelects = new HashSet<>();

    /**
     * 给传入的SQL语句添加租户条件
     */
    public static String sqlAddTenantCondition(String sql) {
        try {
            CKSqlParser CKSqlParser = new CKSqlParser();
            return CKSqlParser.addTenantCondition(sql);
        } catch (JSQLParserException e) {
            log.error("Failed to add tenant condition to SQL using JSQLParser, trying manual approach", e);
            // 尝试手动添加租户条件
            return manuallyAddTenantCondition(sql);
        }
    }

    /**
     * 给传入的SQL语句添加租户条件
     */
    public static String ignoreTenantIdCondition(String sql) {
        try {
            CKSqlParser CKSqlParser = new CKSqlParser();
            return CKSqlParser.ignoreTenantId(sql);
        } catch (JSQLParserException e) {
            log.error("Failed to ignore tenant condition in SQL", e);
            // 对于忽略租户ID的情况，直接返回原始SQL
            return sql;
        }
    }

    /**
     * 主方法：添加租户条件到SQL语句
     * @param sql 原始SQL语句
     * @return 添加了租户条件的SQL语句
     */
    private String addTenantCondition(String sql) throws JSQLParserException {
        // 解析SQL语句
        Select select;
        try {
            select = (Select) CCJSqlParserUtil.parse(sql);
        } catch (JSQLParserException e) {
            log.warn("Failed to parse SQL");

            log.info("The Original SQL is {}", sql);
            // 尝试降级，手动添加租户条件
            String manuallySql;
            try {
                manuallySql = manuallyAddTenantCondition(sql);
            } catch (RuntimeException ex) {
                log.error("Failed to manually add tenant condition to SQL. we'll return the original SQL", ex);
                return sql;
            }

            log.info("The manually SQL is {}", manuallySql);

            // if didn't parse, return original SQL
            return manuallySql;
        }

        // 判断是否是需要处理tableList中的表。必须所有表都在TENANT_TABLES中才处理
        boolean b = TableInfo.tableName().containsAll(getTableName(select));
        // FIXME 如果不是需要处理的表，直接返回原始SQL（目前理论上来说，所有的SQL查询都是单表查询。并且都是上面定义的业务表）
        if (!b) {
            log.info("Table not in tenant tables, skip adding tenant condition");
            return sql;
        }

        Set<String> tableName = getTableName(select);

        // 加密查询条件值，需要加密后再查询的表字段值
        encryptQueryCondition(select, tableName.stream().findFirst().orElse(""));

        // 处理select语句的主体部分
        processSelectBody(select.getSelectBody());
        // 返回修改后的SQL语句
        return select.toString();
    }

    /**
     * 主方法：添加租户条件到SQL语句
     * @param sql 原始SQL语句
     * @return 添加了租户条件的SQL语句
     */
    private String ignoreTenantId(String sql) throws JSQLParserException {
        // 解析SQL语句
        Select select = (Select) CCJSqlParserUtil.parse(sql);

        // 判断是否是需要处理tableList中的表。必须所有表都在TENANT_TABLES中才处理
        boolean b = TableInfo.tableName().containsAll(getTableName(select));
        // FIXME 如果不是需要处理的表，直接返回原始SQL（目前理论上来说，所有的SQL查询都是单表查询。并且都是上面定义的业务表）
        if (!b) {
            log.info("Table not in tenant tables, skip adding tenant condition");
            return sql;
        }

        Set<String> tableName = getTableName(select);

        // 加密查询条件值，需要加密后再查询的表字段值
        encryptQueryCondition(select, tableName.stream().findFirst().orElse(""));

        log.info("ignoreTenantId select {} ", TenantContext.getTenantId());

//        // 处理select语句的主体部分
//        processSelectBody(select.getSelectBody(), true);
        log.info("ignoreTenantId sql {} ", select.toString());
        // 返回修改后的SQL语句
        return select.toString();
    }

    /**
     * 获取SQL语句中的表名。如果表名包含库名，去掉库名。（DB.table）
     *
     */
    private static @NotNull Set<String> getTableName(Select select) {
        // 使用 TablesNamesFinder 获取表名
        return new TablesNamesFinder().getTableList(select).stream()
                // 如果表名字符串包含库名，去掉库名。（DB.table）
                .map(m -> m.contains(".") ? m.split("\\.")[1] : m)
                // 如果表名是 `table` 这种带反引号的，去掉反引号
                .map(m -> m.startsWith("`") ? m.substring(1, m.length() - 1) : m)
                .collect(Collectors.toSet());
    }

    /**
     * 加密查询条件值
     * @param select SQL语句
     * @param tableName SQL语句中的表名。（基于业务。理论上来说，一个SQL语句只会有一个表名）
     */
    private void encryptQueryCondition(Select select, String tableName) throws JSQLParserException {
        SelectBody selectBody = select.getSelectBody();

        if (!(selectBody instanceof PlainSelect)) {
            log.info("Only support PlainSelect, skip encrypting query condition");
            return;
        }

        PlainSelect plainSelect = (PlainSelect) selectBody;
        Expression where = plainSelect.getWhere();
        if (where == null) {
            log.info("No where condition, skip encrypting query condition");
            return;
        }


        ConditionVisitor conditionVisitor = new ConditionVisitor(tableName);
        where.accept(conditionVisitor);
    }


    /**
     * 处理SelectBody，可能是PlainSelect或SetOperationList
     */
    private void processSelectBody(SelectBody selectBody) {
        // 如果该SelectBody已经处理过，直接返回
        if (processedSelects.contains(selectBody)) {
            return;
        }

        if (selectBody instanceof PlainSelect) {
            // 处理普通的SELECT语句
            processPlainSelect((PlainSelect) selectBody);
        } else if (selectBody instanceof SetOperationList) {
            // 处理UNION、INTERSECT等复合SELECT语句
            SetOperationList setOperationList = (SetOperationList) selectBody;
            for (SelectBody sb : setOperationList.getSelects()) {
                processSelectBody(sb);
            }
        }

        // 标记该SelectBody已处理
        processedSelects.add(selectBody);
    }

    private void processSelectBody(SelectBody selectBody, boolean flag) {
        // 如果该SelectBody已经处理过，直接返回
        if (processedSelects.contains(selectBody)) {
            return;
        }

        if (selectBody instanceof PlainSelect) {
            // 处理普通的SELECT语句
            processPlainSelect((PlainSelect) selectBody, flag);
        } else if (selectBody instanceof SetOperationList) {
            // 处理UNION、INTERSECT等复合SELECT语句
            SetOperationList setOperationList = (SetOperationList) selectBody;
            for (SelectBody sb : setOperationList.getSelects()) {
                processSelectBody(sb, flag);
            }
        }

        // 标记该SelectBody已处理
        processedSelects.add(selectBody);
    }

    /**
     * 处理PlainSelect，即普通的SELECT语句
     */
    private void processPlainSelect(PlainSelect plainSelect) {
        // 处理FROM子句
        processFromItem(plainSelect.getFromItem());
        // 处理JOIN子句
        processJoins(plainSelect.getJoins());

        // 处理WHERE子句
        if (plainSelect.getWhere() != null) {
            plainSelect.setWhere(processExpression(plainSelect.getWhere()));
        }

        // 如果存在子查询，不再给外层查询添加租户条件
        if (hasSubquery(plainSelect)) {
            return;
        }

        // FIXME 测试数据不够，暂时不放开
        // 添加租户条件到WHERE子句
         //addTenantConditionToWhere(plainSelect);
    }


    private void processPlainSelect(PlainSelect plainSelect, boolean addTenant) {
        // 处理FROM子句
        processFromItem(plainSelect.getFromItem());
        // 处理JOIN子句
        processJoins(plainSelect.getJoins());

        // 处理WHERE子句
        if (plainSelect.getWhere() != null) {
            plainSelect.setWhere(processExpression(plainSelect.getWhere()));
        }

//        if (addTenant){
//            addTenantConditionToWhere(plainSelect);
//            return;
//        }

        // 如果存在子查询，不再给外层查询添加租户条件
        if (hasSubquery(plainSelect)) {
            return;
        }

        // FIXME 测试数据不够，暂时不放开
        // 添加租户条件到WHERE子句
         //addTenantConditionToWhere(plainSelect);
    }

    /**
     * 处理JOIN子句
     */
    private void processJoins(List<Join> joins) {
        if (CollectionUtils.isEmpty(joins)) {
            return;
        }

        for (Join join : joins) {
            processFromItem(join.getRightItem());
        }
    }

    /**
     * 检查PlainSelect是否包含子查询
     */
    private boolean hasSubquery(PlainSelect plainSelect) {
        // 检查FROM子句是否为子查询
        if (plainSelect.getFromItem() instanceof SubSelect) {
            return true;
        }

        // 检查WHERE子句是否包含子查询
        if (plainSelect.getWhere() != null && containsSubSelect(plainSelect.getWhere())) {
            return true;
        }

        // 检查JOIN子句是否包含子查询
        if (plainSelect.getJoins() != null) {
            for (Join join : plainSelect.getJoins()) {
                if (join.getRightItem() instanceof SubSelect) {
                    return true;
                }
            }
        }

        boolean b = plainSelect.getSelectItems().stream()
                .filter(selectItem -> selectItem instanceof SelectExpressionItem)
                .map(selectItem -> ((SelectExpressionItem) selectItem).getExpression())
                .anyMatch(this::containsSubSelect);
        // 检查SELECT项是否包含子查询
        if (b) {
            return true;
        }

        // 检查HAVING子句是否包含子查询
        if (plainSelect.getHaving() != null && containsSubSelect(plainSelect.getHaving())) {
            return true;
        }

        // 检查ORDER BY子句是否包含子查询
        if (plainSelect.getOrderByElements() != null) {
            return plainSelect.getOrderByElements().stream()
                    .anyMatch(orderByElement -> containsSubSelect(orderByElement.getExpression()));
        }

        return false;
    }

    private void addWhereToSubSelect(SubSelect subSelect) {

        if (subSelect == null){
            return;
        }

        SelectBody selectBody = subSelect.getSelectBody();
        String tableIdentifier = subSelect.getAlias() != null ? subSelect.getAlias().getName() : null;
        String tenantColumnName = getTenantColumnName(tableIdentifier);

        // 创建租户条件表达式
        Column tenantColumn = new Column(tenantColumnName);
        Expression tenantCondition = new EqualsTo(tenantColumn, new StringValue(getTenantId()));

        if (selectBody instanceof PlainSelect) {
            PlainSelect subPlainSelect = (PlainSelect) selectBody;
            if (subPlainSelect.getWhere() == null) {
                subPlainSelect.setWhere(tenantCondition);
            } else {
                // 如果已经有WHERE条件，可以使用AND连接新的条件
                EqualsTo newCondition = new EqualsTo();
                newCondition.setLeftExpression(subPlainSelect.getWhere());
                newCondition.setRightExpression(tenantCondition);
                subPlainSelect.setWhere(newCondition);
            }
        }
    }


    /**
     * 递归检查表达式是否包含子查询
     */
    private boolean containsSubSelect(Expression expression) {
        if (expression instanceof SubSelect) {
            return true;
        }
        if (expression instanceof BinaryExpression) {
            BinaryExpression binaryExpression = (BinaryExpression) expression;
            return containsSubSelect(binaryExpression.getLeftExpression()) ||
                    containsSubSelect(binaryExpression.getRightExpression());
        }
        // 可以根据需要添加其他类型的表达式检查
        return false;
    }

    /**
     * 处理FROM项，可能是子查询或表
     */
    private void processFromItem(FromItem fromItem) {
        if (fromItem instanceof SubSelect) {
            // 处理子查询
            SubSelect subSelect = (SubSelect) fromItem;
            String alias = subSelect.getAlias() != null ? subSelect.getAlias().getName() : null;
            if (alias != null) {
                aliasStack.push(alias);
            }
            processSelectBody(subSelect.getSelectBody());
            if (alias != null) {
                aliasStack.pop();
            }
        } else if (fromItem instanceof Table) {
            // 处理表
            Table table = (Table) fromItem;
            String tableIdentifier = getTableIdentifier(table);
            aliasStack.push(tableIdentifier);
        }
    }

    /**
     * 获取表的标识符（别名或完整表名）
     */
    private String getTableIdentifier(Table table) {
        if (table.getAlias() != null) {
            return table.getAlias().getName();
        }
        // 返回完整的表名，包括可能的库名
        return table.getFullyQualifiedName();
    }

    /**
     * 处理表达式，主要用于处理WHERE和HAVING子句
     */
    private Expression processExpression(Expression expression) {
        if (expression instanceof SubSelect) {
            processSelectBody(((SubSelect) expression).getSelectBody());
        } else if (expression instanceof BinaryExpression) {
            BinaryExpression binaryExpression = (BinaryExpression) expression;
            binaryExpression.setLeftExpression(processExpression(binaryExpression.getLeftExpression()));
            binaryExpression.setRightExpression(processExpression(binaryExpression.getRightExpression()));
        }
        return expression;
    }

    /**
     * 向WHERE子句添加租户条件
     */
    private void addTenantConditionToWhere(PlainSelect plainSelect) {

        // 如果存在子查询，不再给外层查询添加租户条件
//        if (hasSubquery(plainSelect)) {
//            // 给子语句添加租户id
//            addTenantConditionToSubSelect(plainSelect);
//            return;
//        }


        String tableIdentifier = aliasStack.isEmpty() ? null : aliasStack.peek();
        String tenantColumnName = getTenantColumnName(tableIdentifier);

        // 创建租户条件表达式
        Column tenantColumn = new Column(tenantColumnName);
        Expression tenantCondition = new EqualsTo(tenantColumn, new StringValue(getTenantId()));

        // 将租户条件添加到WHERE子句
        if (plainSelect.getWhere() == null) {
            plainSelect.setWhere(tenantCondition);
        } else {
            plainSelect.setWhere(new AndExpression(plainSelect.getWhere(), tenantCondition));
        }
    }

    private void addTenantConditionToSubSelect(PlainSelect plainSelect) {
        // 检查FROM子句是否为子查询
        if (plainSelect.getFromItem() instanceof SubSelect) {
            addWhereToSubSelect((SubSelect) plainSelect.getFromItem());
        }

        // 检查JOIN子句是否包含子查询
        if (plainSelect.getJoins() != null) {
            for (Join join : plainSelect.getJoins()) {
                if (join.getRightItem() instanceof SubSelect) {
                    addWhereToSubSelect((SubSelect) plainSelect.getFromItem());
                }
            }
        }

    }


    private void processExpressionForSubSelects(Expression expression) {
        if (expression instanceof SubSelect) {
            addWhereToSubSelect((SubSelect) expression);
        } else if (expression instanceof BinaryExpression) {
            BinaryExpression binaryExpression = (BinaryExpression) expression;
            processExpressionForSubSelects(binaryExpression.getLeftExpression());
            processExpressionForSubSelects(binaryExpression.getRightExpression());
        }
    }

    /**
     * 获取租户列名
     */
    private String getTenantColumnName(String tableIdentifier) {
        return tableIdentifier != null ? tableIdentifier + ".orderdbtype" : "orderdbtype";
    }


    private String getTenantId() {
        String tenantId = TenantContext.getTenantId();

        // 如果租户ID为空，则拒绝访问
        if (tenantId == null) {
            log.error("Tenant ID is empty, access denied");
            throw new CorpBusinessException(ResponseCodeEnum.GET_TENANT_INFO_ERROR);
        }

        return tenantId;
    }


    /**
     * 手动添加租户条件到SQL语句
     * 当JSQLParser无法解析ClickHouse特定语法时使用此方法
     *
     * @param sql 原始SQL语句
     * @return 添加了租户条件的SQL语句
     */
    private static String manuallyAddTenantCondition(String sql) {
        Config appConfig = ConfigService.getAppConfig();
        // 是否开启手动添加租户条件。默认开启。一般来说，测试环境需要手动关闭该功能。因为测试环境数据量太少，需要去除租户条件
        boolean open = appConfig.getBooleanProperty("open_manually_add_tenant_condition", true);
        if (!open) {
            log.info("Manually add tenant condition is closed");
            return sql;
        }

        String tenantId = TenantContext.getTenantId();
        if (StringUtils.isBlank(sql)) {
            log.error("Tenant ID is empty, access denied");
            throw new CorpBusinessException(ResponseCodeEnum.GET_TENANT_INFO_ERROR);
        }

        // 检查SQL是否包含需要处理的表
        boolean containsTargetTable = false;
        for (String tableName : TableInfo.tableName()) {
            if (sql.contains(tableName)) {
                containsTargetTable = true;
                break;
            }
        }

        if (!containsTargetTable) {
            log.info("SQL does not contain any tenant tables, skip adding tenant condition");
            return sql;
        }

        // 预处理SQL，将多个空白字符（包括换行符、制表符等）替换为单个空格
        String normalizedSql = sql.replaceAll("\\s+", " ").toLowerCase();
        String originalSql = sql; // 保留原始SQL用于后续处理

        // 使用正则表达式查找关键词位置
        java.util.regex.Pattern wherePattern = java.util.regex.Pattern.compile("\\bwhere\\b", java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Pattern groupByPattern = java.util.regex.Pattern.compile("\\bgroup\\s+by\\b", java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Pattern orderByPattern = java.util.regex.Pattern.compile("\\border\\s+by\\b", java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Pattern andPattern = java.util.regex.Pattern.compile("\\band\\b", java.util.regex.Pattern.CASE_INSENSITIVE);

        java.util.regex.Matcher whereMatcher = wherePattern.matcher(normalizedSql);
        boolean hasWhere = whereMatcher.find();

        if (!hasWhere) {
            // 如果没有WHERE子句，在FROM和GROUP BY/ORDER BY之间添加WHERE子句
            java.util.regex.Matcher groupByMatcher = groupByPattern.matcher(normalizedSql);
            java.util.regex.Matcher orderByMatcher = orderByPattern.matcher(normalizedSql);

            int insertPosition = -1;
            String keyword = null;

            if (groupByMatcher.find()) {
                insertPosition = findActualPosition(originalSql, "group by", groupByMatcher.start());
                keyword = "GROUP BY";
            } else if (orderByMatcher.find()) {
                insertPosition = findActualPosition(originalSql, "order by", orderByMatcher.start());
                keyword = "ORDER BY";
            }

            if (insertPosition != -1) {
                log.info("Adding WHERE clause before {} at position {}", keyword, insertPosition);
                return originalSql.substring(0, insertPosition) + " WHERE orderdbtype = '" + tenantId + "' " + originalSql.substring(insertPosition);
            } else {
                // 如果没有找到GROUP BY或ORDER BY，在SQL末尾添加
                log.info("Adding WHERE clause at the end of SQL");
                return originalSql + " WHERE orderdbtype = '" + tenantId + "' ";
            }
        } else {
            // 如果有WHERE子句，在第一个AND前添加租户条件
            int wherePosition = findActualPosition(originalSql, "where", whereMatcher.start());
            log.info("Found WHERE clause at position {}", wherePosition);

            // 查找WHERE之后的第一个AND
            java.util.regex.Matcher andMatcher = andPattern.matcher(normalizedSql);

            // 先尝试在WHERE之后找到第一个AND
            while (andMatcher.find()) {
                if (andMatcher.start() > whereMatcher.start()) {
                    int andPosition = findActualPosition(originalSql, "and", andMatcher.start());
                    log.info("Adding tenant condition before AND at position {}", andPosition);
                    return originalSql.substring(0, andPosition) + " AND orderdbtype = '" + tenantId + "' " + originalSql.substring(andPosition);
                }
            }

            // 如果没有找到AND，在WHERE子句后面的GROUP BY或ORDER BY前添加AND
            java.util.regex.Matcher groupByMatcher = groupByPattern.matcher(normalizedSql);
            java.util.regex.Matcher orderByMatcher = orderByPattern.matcher(normalizedSql);

            int insertPosition = -1;
            String keyword = null;

            if (groupByMatcher.find() && groupByMatcher.start() > whereMatcher.start()) {
                insertPosition = findActualPosition(originalSql, "group by", groupByMatcher.start());
                keyword = "GROUP BY";
            } else if (orderByMatcher.find() && orderByMatcher.start() > whereMatcher.start()) {
                insertPosition = findActualPosition(originalSql, "order by", orderByMatcher.start());
                keyword = "ORDER BY";
            }

            if (insertPosition != -1) {
                log.info("Adding AND condition before {} at position {}", keyword, insertPosition);
                return originalSql.substring(0, insertPosition) + " AND orderdbtype = '" + tenantId + "' " + originalSql.substring(insertPosition);
            } else {
                // 如果WHERE后没有GROUP BY或ORDER BY，在SQL末尾添加
                log.info("Adding AND condition at the end of SQL");
                return originalSql + " AND orderdbtype = '" + tenantId + "' ";
            }
        }
    }

    /**
     * 在原始SQL中找到关键词的实际位置
     * 由于我们在标准化的SQL中找到了关键词的位置，需要将其映射回原始SQL中
     *
     * @param originalSql 原始SQL
     * @param keyword 要查找的关键词（小写）
     * @param approximatePosition 在标准化SQL中的大致位置
     * @return 在原始SQL中的实际位置
     */
    private static int findActualPosition(String originalSql, String keyword, int approximatePosition) {
        // 将原始SQL转换为小写以进行不区分大小写的搜索
        String lowerOriginalSql = originalSql.toLowerCase();

        // 在原始SQL中查找关键词
        // 使用\b表示单词边界，确保匹配完整关键词
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\b" + keyword + "\\b", java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Matcher matcher = pattern.matcher(lowerOriginalSql);

        // 找到所有匹配项
        java.util.List<Integer> positions = new java.util.ArrayList<>();
        while (matcher.find()) {
            positions.add(matcher.start());
        }

        if (positions.isEmpty()) {
            log.warn("Could not find keyword '{}' in original SQL", keyword);
            return -1;
        }

        // 如果只有一个匹配项，直接返回
        if (positions.size() == 1) {
            return positions.get(0);
        }

        // 如果有多个匹配项，找到最接近approximatePosition的那个
        int closestPosition = -1;
        int minDistance = Integer.MAX_VALUE;

        for (int pos : positions) {
            int distance = Math.abs(pos - approximatePosition);
            if (distance < minDistance) {
                minDistance = distance;
                closestPosition = pos;
            }
        }

        return closestPosition;
    }

    public static void main(String[] args) throws Exception {
        // 模拟租户ID
        TenantContext.setTenantId("test_tenant");

        // 测试用例1: 原始SQL示例，包含换行符和缩进
        String sql1 = "SELECT current.dept1                                                 as aggId\n" +
                "     , case when (current.dept1 = '') then '' else current.dept1 end as aggType\n" +
                "     , SUM(real_pay)                                                 as totalAmount\n" +
                "     , SUM(if(order_type = 1, real_pay, 0))                          as totalAmountAirportpickDom\n" +
                "     , SUM(if(order_type = 2, real_pay, 0))                          as totalAmountAirportpickInter\n" +
                "     , SUM(if(order_type = 3, real_pay, 0))                          as totalAmountCharter\n" +
                "     , SUM(if(order_type = 4, real_pay, 0))                          as totalAmountRent\n" +
                "     , SUM(if(order_type = 6, real_pay, 0))                          as totalAmountTax\n" +
                "     , SUM(cnt_order)                                                as totalOrderCount\n" +
                "     , SUM(normal_distance)                                          as totalNormalDistance\n" +
                "     , SUM(if(carbon_emission != 0, normal_distance, 0))             as totalCarbonsTpms\n" +
                "     , SUM(carbon_emission)                                          AS totalCarbons\n" +
                "FROM olrpt_indexcardownload_all as current\n" +
                "where d = '20250309'\n" +
                "  and orderdt >= '2025-01-01'\n" +
                "  and orderdt <= '2025-04-13'\n" +
                "  and (order_type in (1, 3, 4) or (order_type = 6 and sub_product_line = '1'))\n" +
                "group by aggId, aggType\n" +
                "order by totalAmount desc, aggId, aggType";

        // 测试用例2: 没有WHERE子句的SQL
        String sql2 = "SELECT dept1, SUM(real_pay) as total\n" +
                "FROM olrpt_indexcardownload_all\n" +
                "GROUP BY dept1";

        // 测试用例3: 没有AND的WHERE子句
        String sql3 = "SELECT dept1, SUM(real_pay) as total\n" +
                "FROM olrpt_indexcardownload_all\n" +
                "WHERE d = '20250309'\n" +
                "GROUP BY dept1";

        // 测试用例4: 包含复杂函数和子查询的SQL
        String sql4 = "SELECT\n" +
                "  coalesce(current.dept1,'') as aggId,\n" +
                "  case when (coalesce(current.dept1,'')='') then '' else current.dept1 end as aggType,\n" +
                "  SUM(coalesce(real_pay, 0)) as totalAmount,\n" +
                "  SUM(if(order_type = 1,coalesce(real_pay, 0),0)) as totalAmountAirportpickDom\n" +
                "FROM olrpt_indexcardownload_all as current\n" +
                "where d = '20250309'\n" +
                "  and orderdt >= '2025-01-01'\n" +
                "  and orderdt <= '2025-04-13'\n" +
                "group by aggId, aggType\n" +
                "order by totalAmount desc";

        testSql("Test Case 1 - Original SQL", sql1);
        testSql("Test Case 2 - SQL without WHERE clause", sql2);
        testSql("Test Case 3 - WHERE without AND", sql3);
        testSql("Test Case 4 - Complex functions and subqueries", sql4);
    }

    private static void testSql(String testName, String sql) {
        System.out.println("\n===== " + testName + " =====");
        System.out.println("Original SQL:\n" + sql);

        try {
            String result = CKSqlParser.sqlAddTenantCondition(sql);
            System.out.println("\nResult:\n" + result);
        } catch (Exception e) {
            System.out.println("\nError: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
