package com.corpgovernment.resource.schedule.onlinereport.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2023/12/15
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class OnlineReportFootprintKeywordDTO {
    /**
     * 关键词
     */
    @Column(name = "footprint_keyword")
    @Type(value = Types.VARCHAR)
    private String footprintKeyword;

}
