package com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-09-21 11:24
 * @desc
 */
@Data
public class CarbonsTypeDTO {
    // 1：短途，2：中途，3：长途
    @Column(name = "carbonType")
    @Type(value = Types.INTEGER)
    private Integer carbonType;
    // 碳排放量
    @Column(name = "sumCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumCarbons;
    // 里程数
    @Column(name = "sumTpms")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumTpms;
}
