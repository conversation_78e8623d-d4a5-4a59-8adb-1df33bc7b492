package com.corpgovernment.resource.schedule.onlinereport.clickhouse.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PreOrderdateRange;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SqlFieldValidator;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-11-10 11:00
 * @desc
 */
@Repository
public class FlightBehaviorAnlysisDao extends OnlineReportBehaviorAnalysisDao {

    public <T> List<T> behaviorAnaylsis(BaseQueryConditionDTO requestDto, Class<T> clazz, Map<String, String> extParams) throws Exception {
        String sql = null;
        List<Object> parmList = new ArrayList<>();
        String dim = extParams.get("dim");

        // Note: We don't need to validate dim here because it's used as an enum value, not directly in SQL
        if (StringUtils.equalsIgnoreCase(BehaviorDimEnum.CLASS_TYPE.name(), dim)) {
            sql = classTypeAnaylsisSql(requestDto, parmList);
        } else if (StringUtils.equalsIgnoreCase(BehaviorDimEnum.TAKEOFF_TIME.name(), dim)) {
            sql = domEconomyTakeOffTimeRange(requestDto, parmList);
        } else if (StringUtils.equalsIgnoreCase(BehaviorDimEnum.REFUND_TYPE_DIS.name(), dim)) {
            sql = refundReasonDist(requestDto, parmList);
        } else if (StringUtils.equalsIgnoreCase(BehaviorDimEnum.REBOOK_TYPE_DIS.name(), dim)) {
            sql = rebookReasonDist(requestDto, parmList);
        } else if (StringUtils.equalsIgnoreCase(BehaviorDimEnum.LOW_RC_BD_DATE_DIFF.name(), dim)) {
            sql = dateDiffBD(requestDto, parmList);
        } else if (StringUtils.equalsIgnoreCase(BehaviorDimEnum.REAL_CLASS.name(), dim)) {
            sql = realClassAnaylsisSql(requestDto, parmList);
        }
        return commonList(clazz, sql.toString(), parmList);
    }

    /**
     * BD-时间差张数比
     *
     * @param
     * @return
     * @throws Exception
     */
    public String dateDiffBD(BaseQueryConditionDTO request, List<Object> parmList) {
        String partion = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuffer sqlBuilder = new StringBuffer();
        sqlBuilder.append("SELECT CASE ");
        sqlBuilder.append("WHEN dateDiff('minute', toDateTime(takeoff_time), toDateTime(low_dtime)) > 60 THEN '>60min' ");
        sqlBuilder.append("WHEN dateDiff('minute', toDateTime(takeoff_time), toDateTime(low_dtime)) >= 30\n" +
                "        AND dateDiff('minute', toDateTime(takeoff_time), toDateTime(low_dtime)) <= 60 THEN '30-60min' ");
        sqlBuilder.append("WHEN dateDiff('minute', toDateTime(takeoff_time), toDateTime(low_dtime)) < 30 THEN '<30min' ");
        sqlBuilder.append("END AS dim");
        sqlBuilder.append(", SUM(quantity) AS totalQuantity ");
        sqlBuilder.append("from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(request, parmList, partion, getDateFieldByCaliber(request.getStatisticalCaliber())));
        sqlBuilder.append(getFlightClassConditionWithAudited(request.getProductType()));
        sqlBuilder.append(" AND low_rid = 'BD' ");
        sqlBuilder.append(" AND (coalesce(takeoff_time, '') != '' and coalesce(low_dtime, '') != '') ");
        sqlBuilder.append("GROUP BY dim ");
        // 查询clickhouse
        return sqlBuilder.toString();
    }

    /**
     * 舱位分析
     *
     * @return
     * @throws Exception
     */
    public String classTypeAnaylsisSql(BaseQueryConditionDTO requestDto, List<Object> parmList) throws Exception {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT class_type as dim");
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) totalQuantity");
        sqlBuilder.append(", SUM(coalesce(netfare, 0) + coalesce(rebook_price_differential, 0)) totalPrice");
        sqlBuilder.append(", SUM(coalesce(price_rate, 0) * coalesce(quantity, 0)) totalDiscount");
        sqlBuilder.append(", SUM(coalesce(tpms, 0)) totalTpms");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" GROUP BY dim ");
        return sqlBuilder.toString();
    }

    /**
     * 舱位分析
     *
     * @return
     * @throws Exception
     */
    public String realClassAnaylsisSql(BaseQueryConditionDTO requestDto, List<Object> parmList) throws Exception {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT real_class as dim");
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) totalQuantity");
        sqlBuilder.append(", SUM(coalesce(netfare, 0) + coalesce(rebook_price_differential, 0)) totalPrice");
        sqlBuilder.append(", SUM(coalesce(price_rate, 0) * coalesce(quantity, 0)) totalDiscount");
        sqlBuilder.append(", SUM(coalesce(tpms, 0)) totalTpms");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" GROUP BY dim ");
        return sqlBuilder.toString();
    }

    /**
     * 退票原因分布
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    private String refundReasonDist(BaseQueryConditionDTO requestDto, List<Object> paramList) {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        // 特殊事件
        String specialVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Special");
        // 自愿
        String voluntaryVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Voluntary");
        // 航变
        String involuntaryVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Involuntary");
        // 其他
        String otherVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Other");
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(String.format("SELECT case flightrefundtype when '%s' then 'Voluntary' when '%s' then 'Involuntary' when '%s' then 'Special' " +
                " else 'Other' END as dim", voluntaryVal, involuntaryVal, specialVal));
        sqlBuilder.append(",SUM(coalesce(refundtkt, 0)) as totalRefundQuantity");
        sqlBuilder.append(",SUM(coalesce(refundtkt, 0)) as totalQuantity");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(" AND is_refund = 'T' ");
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" GROUP BY dim  order by totalRefundQuantity desc");
        return sqlBuilder.toString();
    }

    /**
     * @param requestDto
     * @param clazz
     * @param industryList
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> refundOverView(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> industryList,
                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        String dateField = getDateFieldByCaliber(requestDto.getStatisticalCaliber());
        List<Object> paramList = new ArrayList<>();
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select company.totalRfRbtkt as companyRfRbtkt, corp.totalRfRbtkt as corpRfRbtkt, industry.totalRfRbtkt as industryRfRbtkt");
        sqlBuilder.append(", company.totalOrdertkt as companytkt, corp.totalOrdertkt as corptkt, industry.totalOrdertkt as industrytkt ");
        sqlBuilder.append(", company.totalLoss as companyLoss ");
        sqlBuilder.append(" from (");
        sqlBuilder.append("SELECT SUM(coalesce(refundtkt, 0)) as totalRfRbtkt, SUM(coalesce(ordertkt, 0)) as totalOrdertkt");
        sqlBuilder.append(",SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0)) + SUM(coalesce(refund_behind_service_fee, 0)) as totalLoss");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, dateField));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(BaseConditionPrebuilder.buildTenantId(paramList));
        sqlBuilder.append(") company cross join ( ");

        sqlBuilder.append("SELECT SUM(coalesce(refundtkt, 0)) as totalRfRbtkt, SUM(coalesce(ordertkt, 0)) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(requestDto, paramList, partition, dateField));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(" ) corp cross join (");
        sqlBuilder.append("SELECT SUM(coalesce(refundtkt, 0)) as totalRfRbtkt, SUM(coalesce(ordertkt, 0)) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(requestDto, industryList, partition, paramList, dateField));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(" ) industry");
        return commonList(clazz, sqlBuilder.toString(), paramList,true);
    }

    public <T> List<T> refundTrend(BaseQueryConditionDTO requestDto, Class<T> clazz, Map<String, String> extParams) throws Exception {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT firstday_of_month AS dim ");
        sqlBuilder.append(",SUM(coalesce(ordertkt, 0)) AS companytkt");
        sqlBuilder.append(",SUM(coalesce(refundtkt, 0)) AS companyRfRbtkt");
        sqlBuilder.append(",SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0)) + SUM(coalesce(refund_behind_service_fee, 0)) as companyLoss");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getFlightRefundTypeCondition(extParams.get("rebookType")));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" group by dim ");
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 改签原因分布
     *
     * @param requestDto
     * @return
     */
    public String rebookReasonDist(BaseQueryConditionDTO requestDto, List<Object> paramList) {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        StringBuilder sqlBuilder = new StringBuilder();
        // 特殊事件
        String specialVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Special");
        // 自愿
        String voluntaryVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Voluntary");
        // 航变
        String involuntaryVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Involuntary");
        // 其他
        String otherVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Other");
        sqlBuilder.append(String.format("SELECT case rebook_reson_desc when '%s' then 'Voluntary' when '%s' then 'Involuntary' when '%s' then 'Special' " +
                " else 'Other' END as dim", voluntaryVal, involuntaryVal, specialVal));
        sqlBuilder.append(",SUM(coalesce(quantity, 0)) as totalRebookQuantity");
        sqlBuilder.append(",SUM(coalesce(quantity, 0)) as totalQuantity");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(" and is_rebook = 'T'");
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" GROUP BY dim order by totalRebookQuantity desc");
        return sqlBuilder.toString();
    }

    public <T> List<T> rebookTrend(BaseQueryConditionDTO requestDto, Class<T> clazz, Map<String, String> extParams) throws Exception {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT firstday_of_month AS dim ");
        sqlBuilder.append(",SUM(coalesce(ordertkt, 0)) AS companytkt");
        sqlBuilder.append(",sum(case when is_rebook = 'T' then coalesce(quantity,0) else 0 end) AS companyRfRbtkt");
        sqlBuilder.append(",SUM(coalesce(change_fee,0))+SUM(coalesce(rebook_price_differential,0))+SUM(coalesce(rebook_service_fee,0))" +
                "+SUM(if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0))) +SUM(coalesce(rebook_behind_service_fee, 0)) as companyLoss");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getFlightRebookTypeCondition(extParams.get("refundType")));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" group by dim ");
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * @param requestDto
     * @param clazz
     * @param industryList
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> rebookOverview(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> industryList,
                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        String dateField = getDateFieldByCaliber(requestDto.getStatisticalCaliber());
        List<Object> paramList = new ArrayList<>();
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select company.totalRfRbtkt as companyRfRbtkt, corp.totalRfRbtkt as corpRfRbtkt, industry.totalRfRbtkt as industryRfRbtkt");
        sqlBuilder.append(", company.totalOrdertkt as companytkt, corp.totalOrdertkt as corptkt, industry.totalOrdertkt as industrytkt ");
        sqlBuilder.append(", company.totalLoss as companyLoss ");
        sqlBuilder.append(" from (");
        sqlBuilder.append("SELECT sum(case when is_rebook = 'T' then coalesce(quantity,0) else 0 end) as totalRfRbtkt, SUM(coalesce(ordertkt, 0)) as totalOrdertkt");
        sqlBuilder.append(",SUM(coalesce(change_fee,0))+SUM(coalesce(rebook_price_differential,0))+SUM(coalesce(rebook_service_fee,0))" +
                " + SUM(if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0))) + SUM(coalesce(rebook_behind_service_fee, 0)) as totalLoss");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, dateField));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        // 查询公司信息这里手动拼接租户id
        sqlBuilder.append(BaseConditionPrebuilder.buildTenantId(paramList));
        sqlBuilder.append(") company cross join ( ");

        sqlBuilder.append("SELECT sum(case when is_rebook = 'T' then coalesce(quantity,0) else 0 end) as totalRfRbtkt, SUM(coalesce(ordertkt, 0)) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(requestDto, paramList, partition, dateField));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(" ) corp cross join (");
        sqlBuilder.append("SELECT sum(case when is_rebook = 'T' then coalesce(quantity,0) else 0 end) as totalRfRbtkt, SUM(coalesce(ordertkt, 0)) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(requestDto, industryList, partition, paramList, dateField));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(" ) industry");
        return commonList(clazz, sqlBuilder.toString(), paramList,true);
    }

    /**
     * 机票国内经济舱起飞时间段
     *
     * @return
     * @throws Exception
     */
    public String domEconomyTakeOffTimeRange(BaseQueryConditionDTO requestDto, List<Object> parmList) {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT case when subString(takeoff_time, 12, 2) >= '00' and subString(takeoff_time, 12, 2) < '06' then '00:00-06:00'\n" +
                "             when subString(takeoff_time, 12, 2) >= '06' and subString(takeoff_time, 12, 2) < '08' then '06:00-08:00' \n" +
                "             when subString(takeoff_time, 12, 2) >= '08' and subString(takeoff_time, 12, 2) < '10' then '08:00-10:00' \n" +
                "             when subString(takeoff_time, 12, 2) >= '10' and subString(takeoff_time, 12, 2) < '12' then '10:00-12:00' \n" +
                "             when subString(takeoff_time, 12, 2) >= '12' and subString(takeoff_time, 12, 2) < '14' then '12:00-14:00' \n" +
                "             when subString(takeoff_time, 12, 2) >= '14' and subString(takeoff_time, 12, 2) < '16' then '14:00-16:00' \n" +
                "             when subString(takeoff_time, 12, 2) >= '16' and subString(takeoff_time, 12, 2) < '18' then '16:00-18:00' \n" +
                "             when subString(takeoff_time, 12, 2) >= '18' and subString(takeoff_time, 12, 2) < '20' then '18:00-20:00' \n" +
                "             when subString(takeoff_time, 12, 2) >= '20' and subString(takeoff_time, 12, 2) < '22' then '20:00-22:00'\n" +
                "             when subString(takeoff_time, 12, 2) >= '22' and subString(takeoff_time, 12, 2) < '24' then '22:00-24:00' else '' end as dim");
        sqlBuilder.append(", SUM(coalesce(quantity, 0)) totalQuantity");
        sqlBuilder.append(", SUM(coalesce(netfare, 0) + coalesce(rebook_price_differential, 0)) totalPrice");
        sqlBuilder.append(", SUM(coalesce(price_rate, 0) * coalesce(quantity, 0)) totalDiscount");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        // 仅计算国内经济舱
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" and flight_class = 'N'  ");
        sqlBuilder.append(" AND class_type = 'Y'");
        sqlBuilder.append(" GROUP BY dim ");
        return sqlBuilder.toString();
    }

    public <T> List<T> flightPreorderdateAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz, List<PreOrderdateRange> rangeList) throws Exception {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select  ");
        sqlBuilder.append(getPreOrderdateRangeDimSql(rangeList));
        sqlBuilder.append(" , SUM(quantity) AS totalQuantity ");
        sqlBuilder.append(" , SUM(ordertkt) AS totalOrdertkt ");
        // 产品要求和节省分析中提前预订天数保持一致用不含改签差价的成交净价
        sqlBuilder.append(" , SUM(coalesce(netfare, 0)) AS totalPrice ");
        sqlBuilder.append(" , SUM(coalesce(price_rate,0) * coalesce(quantity,0)) AS totalDiscount ");
        sqlBuilder.append(" , SUM(refundtkt) AS totalRefundtkt ");
        sqlBuilder.append(" , SUM(case when is_rebook = 'T' then coalesce(quantity,0) else 0 end) AS totalRebooktkt ");
        sqlBuilder.append(" , SUM(fullfaretkt) AS totalFullfaretkt ");
        // 不含全价票的票张
        sqlBuilder.append(" , SUM(case when price_rate>= 0 and price_rate < 1 then coalesce(quantity,0) else 0 end) AS totalWithoutFullQuantity ");
        // 不含全价票的折扣
        sqlBuilder.append(" , SUM(case when price_rate>= 0 and price_rate < 1 then coalesce(price_rate,0) * coalesce(quantity,0) else 0 end) AS totalWithoutFullDiscount ");
        sqlBuilder.append(" , SUM(case when pre_order_date > 4 then coalesce(price_rate,0) * coalesce(quantity,0) else 0 end) AS totalPre4Discount ");
        sqlBuilder.append(" , SUM(case when pre_order_date > 4 then coalesce(quantity,0) else 0 end) AS totalPre4Quantiy ");
        sqlBuilder.append("from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        // 如果查国际的数据就不限制flight_class
        if (!StringUtils.equalsIgnoreCase(requestDto.getProductType(), "inter")) {
            // 国内
            sqlBuilder.append(" and flight_class = 'N'  ");
        }
        // 经济仓
        sqlBuilder.append(" and class_type = 'Y' ");
        sqlBuilder.append(" and pre_order_date >=0 ");
        sqlBuilder.append(" group by preOrderDateRange  ");
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    public <T> List<T> flightPreorderdateAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder sqlBuilder = new StringBuilder();

        sqlBuilder.append(" select  ");
        sqlBuilder.append("  SUM(case when pre_order_date > 4 then coalesce(price_rate,0) * coalesce(quantity,0) else 0 end) AS totalPre4Discount ");
        sqlBuilder.append(" , SUM(case when pre_order_date > 4 then coalesce(quantity,0) else 0 end) AS totalPre4Quantiy ");
        sqlBuilder.append("from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        // 国内经济仓
        sqlBuilder.append(" and flight_class = 'N'  ");
        sqlBuilder.append(" and class_type = 'Y' ");
        sqlBuilder.append(" and pre_order_date >=0 ");
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    private String getPreOrderdateRangeDimSql(List<PreOrderdateRange> preOrderdateRanges) {
        StringBuilder sqlBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(preOrderdateRanges)) {
            return StringUtils.EMPTY;
        }
        sqlBuilder.append(" case ");
        for (PreOrderdateRange preOrderdateRange : preOrderdateRanges) {
            Integer start = preOrderdateRange.getStart();
            Integer end = preOrderdateRange.getEnd();
            if (Objects.nonNull(start) && Objects.nonNull(end)) {
                sqlBuilder.append(" WHEN pre_order_date >= ").append(start).append(" and ").append(" pre_order_date <= ").append(end)
                        .append(" then ").append("'").append(getPreorderdateDim(preOrderdateRange)).append("'");
            } else if (Objects.isNull(start) && Objects.nonNull(end)) {
                sqlBuilder.append(" WHEN pre_order_date <= ").append(end)
                        .append(" then ").append("'").append(getPreorderdateDim(preOrderdateRange)).append("'");
            } else if (Objects.nonNull(start) && Objects.isNull(end)) {
                sqlBuilder.append(" WHEN pre_order_date >= ").append(start)
                        .append(" then ").append("'").append(getPreorderdateDim(preOrderdateRange)).append("'");
            }
        }
        sqlBuilder.append(" ELSE toString(pre_order_date) ");
        sqlBuilder.append(" END AS preOrderDateRange ");
        return sqlBuilder.toString();
    }

    public static String getPreorderdateDim(PreOrderdateRange preOrderdateRange) {
        StringBuilder sqlBuilder = new StringBuilder();
        Integer start = preOrderdateRange.getStart();
        Integer end = preOrderdateRange.getEnd();
        if (Objects.nonNull(start) && Objects.nonNull(end)) {
            sqlBuilder.append(String.format("%d-%d", start, end));
        } else if (Objects.isNull(start) && Objects.nonNull(end)) {
            sqlBuilder.append(String.format("%d", end));
        } else if (Objects.nonNull(start) && Objects.isNull(end)) {
            sqlBuilder.append(String.format(">%d", start));
        }
        return sqlBuilder.toString();
    }


    protected String getFlightRebookTypeCondition(String rebookType) {
        // 特殊事件
        String specialVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Special");
        // 自愿
        String voluntaryVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Voluntary");
        //  航变
        String involuntaryVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Involuntary");
        if (StringUtils.equalsIgnoreCase(rebookType, "Special")) {
            return String.format(" and rebook_reson_desc = '%s'  ", specialVal);
        } else if (StringUtils.equalsIgnoreCase(rebookType, "Voluntary")) {
            return String.format(" and rebook_reson_desc = '%s'  ", voluntaryVal);
        } else if (StringUtils.equalsIgnoreCase(rebookType, "Involuntary")) {
            return String.format(" and rebook_reson_desc = '%s'  ", rebookType);
        } else if (StringUtils.equalsIgnoreCase(rebookType, "other")) {
            return String.format(" and rebook_reson_desc not in ('%s','%s','%s')", specialVal, voluntaryVal, involuntaryVal);
        } else if (StringUtils.equalsIgnoreCase(rebookType, "offline")) {
            return " and FlightRebookType = 'offline'  ";
        } else {
            return StringUtils.EMPTY;
        }
    }

    protected String getFlightRefundTypeCondition(String refundType) {
        // 特殊事件
        String specialVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Special");
        // 自愿
        String voluntaryVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Voluntary");
        //  航变
        String involuntaryVal = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.FlightRefundtype.Involuntary");
        if (StringUtils.equalsIgnoreCase(refundType, "Special")) {
            return String.format(" and FlightRefundType = '%s'  ", specialVal);
        } else if (StringUtils.equalsIgnoreCase(refundType, "Voluntary")) {
            return String.format(" and FlightRefundType = '%s'  ", voluntaryVal);
        } else if (StringUtils.equalsIgnoreCase(refundType, "Involuntary")) {
            return String.format(" and FlightRefundType = '%s'  ", involuntaryVal);
        } else if (StringUtils.equalsIgnoreCase(refundType, "other")) {
            return String.format(" and FlightRefundType not in ('%s','%s','%s')", specialVal, voluntaryVal, involuntaryVal);
        } else {
            return StringUtils.EMPTY;
        }
    }

    // CLASS_TYPE：仓位, REAL_CLASS:物理舱位，TAKEOFF_TIME：起飞时间段
    enum BehaviorDimEnum {
        CLASS_TYPE, REAL_CLASS, TAKEOFF_TIME, REBOOK_TYPE_DIS, REFUND_TYPE_DIS, LOW_RC_BD_DATE_DIFF
    }
}
