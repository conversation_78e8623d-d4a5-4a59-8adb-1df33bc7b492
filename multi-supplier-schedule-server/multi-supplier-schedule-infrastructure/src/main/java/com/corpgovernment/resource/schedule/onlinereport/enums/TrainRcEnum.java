package com.corpgovernment.resource.schedule.onlinereport.enums;

/*
 * <AUTHOR>
 *
 * @date 2022/4/6 16:10
 *
 * @Desc 酒店rc
 */
public enum TrainRcEnum {
    // 坐席RC
    S("ComplianceMonitor.seatrc"),
    // 票张RC
    T("ComplianceMonitor.ticketrc"),
    // 退票RC
    R("index.refundrc");

    private String sharkKey;

    TrainRcEnum(String s) {
        this.sharkKey = s;
    }

    public String getSharkKey() {
        return sharkKey;
    }
}
