package com.corpgovernment.resource.schedule.onlinereport.module.behaivor;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/11 19:25
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class HotelCostAllocationTypeDTO {

    @Column(name = "not_allocation")
    @Type(value = Types.INTEGER)
    private Integer notAllocation;

    @Column(name = "by_order")
    @Type(value = Types.INTEGER)
    private Integer byOrder;

    @Column(name = "by_room")
    @Type(value = Types.INTEGER)
    private Integer byRoom;

    @Column(name = "by_control")
    @Type(value = Types.INTEGER)
    private Integer byControl;

}
