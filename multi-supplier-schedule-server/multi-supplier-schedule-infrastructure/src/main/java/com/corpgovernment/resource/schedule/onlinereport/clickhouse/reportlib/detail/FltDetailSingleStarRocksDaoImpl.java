package com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail;

import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-21 13:39
 * @desc
 */
@Service
@Repository
public class FltDetailSingleStarRocksDaoImpl extends AbstractCommonDao {

    public final <T> List<T> queryDetail(Class<T> clazz, Long orderId) throws Exception {
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        sqlBuilder.append("select * ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        parmList.add(queryPartition(clickHouseTable));
        sqlBuilder.append(" and order_id = ? ");
        parmList.add(orderId);
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }
}
