package com.corpgovernment.resource.schedule.onlinereport.utils;


import cn.hutool.core.util.CharsetUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.common.SmConstant;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.Column;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.corpgovernment.common.utils.EncryptUtils.isEncryption;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.utils
 * @description:
 * @author: md_wang
 * @create: 2022-10-20 19:50
 **/
@Slf4j
public class DbResultMapUtils {

    private final static String KEY = "1234567890123456";

    /**
     * map ck result to response
     */
    public static <T> List<T> mapResultList(ResultSet resultSet, Class clazz) throws Exception {
        List<T> respList = Lists.newArrayList();
        Map<String, Field> fieldMap = getAllFieldMap(clazz);
        if (MapUtils.isEmpty(fieldMap)) {
            return Lists.newArrayList();
        }
        while (resultSet.next()) {
            ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
            int size = getColumnCount(resultSetMetaData);
            T entity = (T) clazz.newInstance();
            for (int i = OrpConstants.ONE; i <= size; i++) {
                String columnName = OrpReportUtils.lineToHump(resultSetMetaData.getColumnName(i));
                if (fieldMap.containsKey(columnName)) {
                    setMethodSetResult(entity, fieldMap.get(columnName), resultSet.getObject(i));
                }
            }
            respList.add(entity);
        }
        return respList;
    }

    /**
     * 获取--写缓存 key-->column_name, value-->field_name
     */
    public static Map<String, Field> getAllFieldMap(Class clazz) {
        Map<String, Field> allFieldMap = Maps.newHashMap();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            Column col = field.getAnnotation(Column.class);
            Type type = field.getAnnotation(Type.class);
            if (Objects.isNull(col) || Objects.isNull(type)) {
                continue;
            }
            allFieldMap.put(OrpReportUtils.lineToHump(col.name()), field);
        }
        return allFieldMap;
    }

    /**
     * map ck result int value
     *
     * @param resultSetMetaData
     * @return
     */
    public static int getColumnCount(ResultSetMetaData resultSetMetaData) {
        return Optional.ofNullable(resultSetMetaData).map(data -> {
            try {
                return data.getColumnCount();
            } catch (SQLException sqlException) {
                log.error("getColumnCount", ExceptionUtils.getFullStackTrace(sqlException));
            }
            return OrpConstants.ONE;
        }).orElse(OrpConstants.ONE);
    }

    /**
     * 通过反射设置值 仅对Column注解的字段生效 写缓存
     */
    public static void setMethodSetResult(Object object, Field field, Object value) {
        try {
            String fieldName = field.getName();
            Class clazz = object.getClass();
            Method method = null;
            String setName = OrpConstants.SET + OrpReportUtils.capitalize(fieldName);
            typeCompatible(object, clazz, setName, method, field, value);
        } catch (Exception e) {
            if (OrpReportUtils.isFatEvn()) {
                System.err.println("setMethodSetResult>>>>>>>>>>>>>>>>>fieldName:" + field.getName() +
                        ">>>valueType:" + field.getType() + ">>>>value:" + value + "\t" + ExceptionUtils.getFullStackTrace(e));
            }
            try {
                Map<String, String> map = Maps.newHashMap();
                map.put("fieldName", field.getName());
                map.put("valueType", String.valueOf(field.getType()));
                map.put("value", Objects.isNull(value) ? OrpConstants.EMPTY : String.valueOf(value));
                log.error("setMethodSetResult map {}", map);
                log.error("setMethodSetResult", e);
            } catch (Exception e1) {
                if (OrpReportUtils.isFatEvn()) {
                    e1.printStackTrace();
                }
                log.error("setMethodSetResult catch", e1);
            }
        }
    }


    /**
     * map ck result
     */
    protected List<Integer> mapResultCount(ResultSet resultSet) throws Exception {
        List<Integer> respList = Lists.newArrayList();
        while (resultSet.next()) {
            ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
            int size = getColumnCount(resultSetMetaData);
            if (size > OrpConstants.ZERO) {
                BigInteger totalCount = (BigInteger) resultSet.getObject(OrpConstants.ONE);
                respList.add(Optional.ofNullable(totalCount).map(t -> t.intValue()).orElse(OrpConstants.ZERO));
            }
        }
        return respList;
    }

    /**
     * object set value through reflect and map ck db field type to java field type
     */
    public static void typeCompatible(Object object, Class clazz, String setName, Method method, Field field, Object value) throws Exception {
        Class<?> valueType = field.getType();

        if (value == null) {
            method = getMethodCached(clazz, setName, valueType);
            method.invoke(object, (Object) null);
            return;
        }

        int dbType = field.getAnnotation(Type.class).value();
        switch (dbType) {
            case Types.VARCHAR:
            case Types.CHAR:
                value = String.valueOf(value);
                method = getMethodCached(clazz, setName, String.class);
                break;
            case Types.BIGINT:
                try {
                    value = new Long(value.toString());
                    method = getMethodCached(clazz, setName, Long.class);
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid BIGINT value: " + value, e);
                }
                break;
            case Types.INTEGER:
            case Types.TINYINT:
            case Types.SMALLINT:
                if (StringUtils.isBlank(value.toString())) {
                    value = null;
                } else {
                    try {
                        value = new Integer(value.toString());
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("Invalid INTEGER value: " + value, e);
                    }
                }
                method = getMethodCached(clazz, setName, Integer.class);
                break;
            case Types.FLOAT:
                try {
                    value = new Float(value.toString());
                    method = getMethodCached(clazz, setName, Float.class);
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid FLOAT value: " + value, e);
                }
                break;
            case Types.DOUBLE:
                try {
                    value = new Double(value.toString());
                    method = getMethodCached(clazz, setName, Double.class);
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid DOUBLE value: " + value, e);
                }
                break;
            case Types.DECIMAL:
                try {
                    value = new BigDecimal(value.toString());
                    method = getMethodCached(clazz, setName, BigDecimal.class);
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid DECIMAL value: " + value, e);
                }
                break;
            case Types.BIT:
            case Types.BOOLEAN:
                value = Boolean.valueOf(value.toString());
                method = getMethodCached(clazz, setName, Boolean.class);
                break;
            case Types.BLOB:
                // BLOB should be byte[]
                if (value instanceof byte[]) {
                    method = getMethodCached(clazz, setName, byte[].class);
                } else {
                    throw new IllegalArgumentException("Unsupported BLOB type: " + value.getClass());
                }
                break;
            case Types.DATE:
                String dateStr = String.valueOf(value);
                String dateFormat = OrpDateTimeUtils.getDateFormat(dateStr).getFormat();
                value = OrpDateTimeUtils.dateTimeStrToDate(dateStr, dateFormat);
                method = getMethodCached(clazz, setName, Date.class);
                break;
            case Types.TIME:
                String timeStr = String.valueOf(value);
                String timeFormat = OrpDateTimeUtils.getDateFormat(timeStr).getFormat();
                value = OrpDateTimeUtils.dateTimeStrToDate(timeStr, timeFormat);
                method = getMethodCached(clazz, setName, Time.class);
                break;
            case Types.TIMESTAMP:
                String timestampStr = String.valueOf(value);
                String timestampFormat = OrpDateTimeUtils.getDateFormat(timestampStr).getFormat();
                Date date = OrpDateTimeUtils.dateTimeStrToDate(timestampStr, timestampFormat);
                value = new Timestamp(date.getTime());
                method = getMethodCached(clazz, setName, Timestamp.class);
                break;
            case Types.ARRAY:
                value = convertStrToArrayToList(field, value);
                method = getMethodCached(clazz, setName, List.class);
                break;
            default:
                method = getMethodCached(clazz, setName, valueType);
                break;
        }
        if(method!=null) {
            try {
                method.invoke(object, valueType.cast(value));
            } catch (Exception e){
                log.error("typeCompatible error:",e);
            }
        }
    }
    /**
     * object set value through reflect and map ck db field type to java field type
     */
    public static Object typeCompatible(Field field, Object value)  {

        if (value == null) {
            return null;
        }

        int dbType = field.getAnnotation(Type.class).value();
        switch (dbType) {
            case Types.VARCHAR:
            case Types.CHAR:
                value = String.valueOf(value);
                break;
            case Types.BIGINT:
                try {
                    value = new Long(value.toString());
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid BIGINT value: " + value, e);
                }
                break;
            case Types.INTEGER:
            case Types.TINYINT:
            case Types.SMALLINT:
                if (StringUtils.isBlank(value.toString())) {
                    value = null;
                } else {
                    try {
                        value = new Integer(value.toString());
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("Invalid INTEGER value: " + value, e);
                    }
                }
                break;
            case Types.FLOAT:
                try {
                    value = new Float(value.toString());
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid FLOAT value: " + value, e);
                }
                break;
            case Types.DOUBLE:
                try {
                    value = new Double(value.toString());
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid DOUBLE value: " + value, e);
                }
                break;
            case Types.DECIMAL:
                try {
                    value = new BigDecimal(value.toString());
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid DECIMAL value: " + value, e);
                }
                break;
            case Types.BIT:
            case Types.BOOLEAN:
                value = Boolean.valueOf(value.toString());
                break;
            case Types.BLOB:
                break;
            case Types.DATE:
            case Types.TIME:
                String dateStr = String.valueOf(value);
                String dateFormat = OrpDateTimeUtils.getDateFormat(dateStr).getFormat();
                value = OrpDateTimeUtils.dateTimeStrToDate(dateStr, dateFormat);
                break;
            case Types.TIMESTAMP:
                String timestampStr = String.valueOf(value);
                String timestampFormat = OrpDateTimeUtils.getDateFormat(timestampStr).getFormat();
                Date date = OrpDateTimeUtils.dateTimeStrToDate(timestampStr, timestampFormat);
                value = new Timestamp(date.getTime());
                break;
            case Types.ARRAY:
                value = convertStrToArrayToList(field, value);
                break;
            default:
                break;
        }
        return value;
    }

    // 缓存反射方法
    private static final ConcurrentHashMap<Class<?>, Map<String, Method>> methodCache = new ConcurrentHashMap<>();

    private static Method getMethodCached(Class<?> clazz, String methodName, Class<?> paramType) throws NoSuchMethodException {
        // 处理paramType为null的情况
        String key = methodName + "(" + (paramType != null ? paramType.getName() : "null") + ")";

        Map<String, Method> classCache = methodCache.computeIfAbsent(clazz, k -> new ConcurrentHashMap<>());

        Method method = classCache.get(key);
        if (method != null) {
            return method;
        }

        try {
            method = clazz.getMethod(methodName, paramType);
            classCache.put(key, method);
            return method;
        } catch (NoSuchMethodException e) {
            log.error("NoSuchMethodException: methodName={}, paramType={}", methodName, paramType.getName());
        }
        return null;
    }


    public static Object convertStrToArrayToList(Field field, Object value) {
        String s = ((String)value);
        String[] arrayDataArray = s.substring(1, s.length() - 1).split(",");
        java.lang.reflect.Type fieldType = field.getGenericType();
        if (fieldType instanceof ParameterizedType){
            java.lang.reflect.Type[] types = ((ParameterizedType) fieldType).getActualTypeArguments();
            if (ArrayUtils.isNotEmpty(types)){
                if (types[0].equals(Long.class)){
                    return Arrays.stream(arrayDataArray).map(i->Long.valueOf(StringUtils.trimToEmpty(i))).collect(Collectors.toList());
                }else if (types[0].equals(String.class)){
                    return Arrays.stream(arrayDataArray).map(i->StringUtils.trimToEmpty(i)).collect(Collectors.toList());
                }
            }
        }
        return value;
    }

    /**
     * calculate pageIndex
     */
    protected Integer calculatePage(Integer pageIndex, Integer pageSize) {
        return (Optional.ofNullable(pageIndex).orElse(OrpConstants.ZERO) - OrpConstants.ONE) * Optional.ofNullable(pageSize).orElse(OrpConstants.ZERO);
    }

    /**
     * calculate pageSize
     */
    protected Integer calculatePageSize(Integer pageSize) {
        return Optional.ofNullable(pageSize).orElse(OrpConstants.ZERO);
    }

    /**
     * whether the paging
     *
     * @return true:need paging false:don`t paging
     */
    protected boolean pageIsTrue(Integer pageIndex, Integer pageSize) {
        return Objects.nonNull(pageIndex) && Objects.nonNull(pageSize) && pageIndex > OrpConstants.ZERO && pageSize > OrpConstants.ZERO;
    }


    /**
     * map ck result to response
     */
    public static List<Map> mapResultList(ResultSet resultSet) throws Exception {
        List<Map> respList = Lists.newArrayList();
        ResultSetMetaData metaData = resultSet.getMetaData();
        while (resultSet.next()) {
            Map<String, Object> map = new HashMap<>();
            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                map.put(metaData.getColumnName(i), decrypt(resultSet.getObject(i)));
            }
            respList.add(map);
        }
        return respList;
    }

    /**
     * 解密
     */
    public static Object decrypt(Object value) {
        if (Objects.isNull(value)) {
            return OrpConstants.EMPTY;
        }

        // 类型不是String直接返回
        if (!(value instanceof String)) {
            return value.toString();
        }


        try {
            return SmConstant.sm4.decryptStr((String) value, CharsetUtil.CHARSET_UTF_8);
        } catch (Exception e) {
            return value;
        }
    }

    public static String sm4Encrypt(String content) {
        if (isEncryption(content)) {
            return content;
        } else {
            try {
                return SmConstant.sm4.encryptBase64(content);
            } catch (Exception var3) {
                log.error("加密失败,content:" + content, var3);
                return content;
            }
        }
    }

    /**
     * map ck result to response
     */
    public static List<String> mapStrResultList(ResultSet resultSet, String colName) throws Exception {
        List<String> respList = Lists.newArrayList();
        while (resultSet.next()) {
            respList.add(resultSet.getString(colName));
        }
        return respList;
    }

    /**
     * map ck string type result
     */
    public static String mapStringResult(ResultSet resultSet, String colName) throws Exception {
        List<String> respList = Lists.newArrayList();
        while (resultSet.next()) {
            respList.add(resultSet.getString(colName));
        }
        return CollectionUtils.isEmpty(respList) ? OrpConstants.EMPTY : respList.get(OrpConstants.ZERO);
    }

    /**
     * map ck string type result
     */
    public static BigDecimal mapBigDecimalResult(ResultSet resultSet, String colName) throws Exception {
        List<BigDecimal> respList = Lists.newArrayList();
        while (resultSet.next()) {
            respList.add(resultSet.getBigDecimal(colName));
        }
        return CollectionUtils.isEmpty(respList) ? BigDecimal.ZERO : respList.get(OrpConstants.ZERO);
    }

    /**
     * map ck string type result
     */
    public static Integer mapIntResult(ResultSet resultSet, String colName) throws Exception {
        List<Integer> respList = Lists.newArrayList();
        while (resultSet.next()) {
            respList.add(resultSet.getInt(colName));
        }
        return CollectionUtils.isEmpty(respList) ? OrpConstants.ZERO : respList.get(OrpConstants.ZERO);
    }

    /**
     * 打印sql
     */
    public static <Req> String formatSql(Req req, String sql, String totalTime, String sqlType) {
        try {
            if (req instanceof List) {
                List paramList = (List) req;
                if (CollectionUtils.isEmpty(paramList)) {
                    return sql;
                }
                String s = "[?]";
                AtomicReference<String> sqlStr = new AtomicReference<>(sql);
                paramList.stream().filter(Objects::nonNull)
                        .map(t -> {
                            if (t instanceof String) {
                                sqlStr.getAndSet(sqlStr.get().replaceFirst(s,
                                        String.format("%s%s%s", OrpConstants.SINGLE_COMMA, t, OrpConstants.SINGLE_COMMA)));
                            } else {
                                sqlStr.getAndSet(sqlStr.get().replaceFirst(s, String.format("%s%s", t, OrpConstants.EMPTY)));
                            }
                            return sqlStr;
                        })
                        .collect(Collectors.toList());
                if (OrpReportUtils.isPrintLog()) {
                    //log.info("sql>>>>>>{}", sqlStr.get());
                    //log.info((String.format("totalTime %s>>>>>%s", sqlType, totalTime)));
                }
                return sqlStr.get();
            } else {
                if (OrpReportUtils.isPrintLog()) {
                    /*log.info("sql>>>>>>{}", sql);
                    log.info(String.format("totalTime %s>>>>>%s", sqlType, totalTime));*/
                }
                return sql;
            }
        } catch (Exception e) {
            if (OrpReportUtils.isPrintLog()) {
                log.error("sql>>>>>>{}", sql);
                log.error(String.format("totalTime %s>>>>>%s", sqlType, totalTime));
            }
            return sql;
        }
    }
}
