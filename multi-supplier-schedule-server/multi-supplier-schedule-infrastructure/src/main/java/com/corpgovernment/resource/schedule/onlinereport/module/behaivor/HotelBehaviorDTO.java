package com.corpgovernment.resource.schedule.onlinereport.module.behaivor;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class HotelBehaviorDTO {

    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    public String dim;
    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    public Integer totalQuantity;
    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    public BigDecimal totalAmount;
    @Column(name = "totalAmountM")
    @Type(value = Types.DECIMAL)
    public BigDecimal totalAmountM;
    @Column(name = "totalAmountC")
    @Type(value = Types.DECIMAL)
    public BigDecimal totalAmountC;
    @Column(name = "totalRoomPriceM")
    @Type(value = Types.DECIMAL)
    public BigDecimal totalRoomPriceM;
    @Column(name = "totalRoomPriceC")
    @Type(value = Types.DECIMAL)
    public BigDecimal totalRoomPriceC;
    @Column(name = "totalQuantityM")
    @Type(value = Types.INTEGER)
    public Integer totalQuantityM;
    @Column(name = "totalQuantityC")
    @Type(value = Types.INTEGER)
    public Integer totalQuantityC;
}
