package com.corpgovernment.resource.schedule.pricecomparison.mysql.entity;

import java.util.Date;

/**
 * 供应商比价任务表
 */
public class MbJobSupplierCompareTask {
    /**
    * 主键
    */
    private Long id;

    /**
    * 任务ID
    */
    private String taskId;

    /**
    * 任务类型:FLIGHT-指定机票查询，FLIGHT_TOP-机票热门,HOTEL-指定酒店查询
    */
    private String taskType;

    /**
    * 任务状态 1:待执行 2:执行成功 3:执行失败
    */
    private Integer taskStatus;

    /**
    * 创建人工号
    */
    private String createEid;

    /**
    * 创建人姓名
    */
    private String createName;

    /**
    * 比价核心数据JSON
    */
    private String compareData;

    /**
    * 日志查询
    */
    private String requestid;

    /**
    * 创建时间
    */
    private Date datachangeCreatetime;

    /**
    * 最后修改时间
    */
    private Date datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getCreateEid() {
        return createEid;
    }

    public void setCreateEid(String createEid) {
        this.createEid = createEid;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCompareData() {
        return compareData;
    }

    public void setCompareData(String compareData) {
        this.compareData = compareData;
    }

    public String getRequestid() {
        return requestid;
    }

    public void setRequestid(String requestid) {
        this.requestid = requestid;
    }

    public Date getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Date datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Date getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Date datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }
}