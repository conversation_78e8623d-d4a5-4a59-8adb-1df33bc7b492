package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable.CORP_WELFARE_STOCK;
import static com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable.STL_PLTF_ACCCHECK_AMOUNT;
import static com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils.DEFAULT_DATE;
import static com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils.DEFAULT_DATE_1;


/*
 * <AUTHOR>
 * @date 2024/1/22 20:49
 * @Desc
 */
@Service
@Slf4j
@Repository
public class SettlementBillingDataDaoImpl extends AbstractCommonDao {

    private static final String LOG_TITLE = "SettlementBillingDataDaoImpl";

    /**
     * 账单数据
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> querySettlementAmount(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select sum(coalesce(realtimeacccheckamount, 0)) as checkAmount, ");
        sqlBuilder.append("max(month) as maxMonth, min(month) as minMonth ");
        sqlBuilder.append("from ").append(STL_PLTF_ACCCHECK_AMOUNT.getTable());
        sqlBuilder.append(" where d = ? ");
        sqlBuilder.append(" and startdate >= ? and enddate <= ? ");

        parmList.add(queryPartition(STL_PLTF_ACCCHECK_AMOUNT));
        parmList.add(
                OrpDateTimeUtils.formatDateTimeToStr(
                        OrpDateTimeUtils.dateTimeStrToDate(requestDto.getStartTime(), DEFAULT_DATE),
                        DEFAULT_DATE_1
                )
        );
        parmList.add(
                OrpDateTimeUtils.formatDateTimeToStr(
                        OrpDateTimeUtils.dateTimeStrToDate(requestDto.getEndTime(), DEFAULT_DATE),
                        DEFAULT_DATE_1
                )
        );

        // 公司id
        if (CollectionUtils.isNotEmpty(requestDto.getCorpIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corpid", requestDto.getCorpIds(), parmList));
        }

        // 主账户id
        if (CollectionUtils.isNotEmpty(requestDto.getAccountIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("accountid", requestDto.getAccountIds(), parmList));
        }
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    public <T> List<T> queryWelfareStock(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        /*List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select sum(coalesce(stock, 0)) as allStock, sum(coalesce(available_stock, 0)) availableStock ");
        sqlBuilder.append("from ").append(CORP_WELFARE_STOCK.getTable());
        sqlBuilder.append(" where d = ? ");

        parmList.add(queryPartition(CORP_WELFARE_STOCK));

        // 公司id
        if (CollectionUtils.isNotEmpty(requestDto.getCorpIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corpid", requestDto.getCorpIds(), parmList));
        }

        // 主账户id
        if (CollectionUtils.isNotEmpty(requestDto.getAccountIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("account_id", requestDto.getAccountIds(), parmList));
        }
        return commonList(clazz, sqlBuilder.toString(), parmList);*/

        return Lists.newArrayList();
    }

}
