package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
public class OnlineReportDeptCarDetailStarRocksDaoImpl extends AbstractOnlineReportDeptDetailDao {

    /**
     * 统计字段
     *
     * @return
     */
    public String statical() {
        List sql = new ArrayList();
        sql.add("sum(coalesce(real_pay, 0)) as TOTAL_REAL_PAY");
        sql.add("sum(coalesce(cnt_order, 0)) as TOTAL_QUANTITY");
        sql.add("sum(coalesce(service_fee, 0)) as TOTAL_SERVICE_FEE");
        sql.add("sum(coalesce(normal_distance, 0)) as TOTAL_NORMAL_DISTANCE");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    /**
     * 同环比，总计数据
     *
     * @return
     */
    @Override
    protected String momAndYoy() {
        return "sum(coalesce(real_pay, 0))  as TOTAL_REAL_PAY";
    }

    /**
     * 返回字段
     *
     * @return
     */
    public String baseQueryField() {
        List sql = new ArrayList();
        sql.add("round(current.TOTAL_REAL_PAY, 4) AS REAL_PAY");
        sql.add("round(case when coalesce(total.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0))"
                + ", toFloat64(coalesce(total.TOTAL_REAL_PAY, 0))) * 100 else 0 end, 4) as REAL_PAY_PERCENT");
        sql.add("round(case when coalesce(yoy.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(yoy.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(yoy.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_LAST");
        sql.add("round(case when coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_BEFORE_LAST");
        sql.add("round(case when coalesce(mom.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(mom.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(mom.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as MOM");
        sql.add("current.TOTAL_QUANTITY as CAR_ORDER_QUANTITY");
        sql.add("round(case when coalesce(total.TOTAL_CAR_ORDER_QUANTITY, 0) != 0 "
                + "then divide(coalesce(current.TOTAL_QUANTITY, 0)"
                + ", coalesce(total.TOTAL_CAR_ORDER_QUANTITY, 0)) * 100 else 0 end, 4) as CAR_ORDER_QUANTITY_PERCENT");
        sql.add("round(case when coalesce(current.TOTAL_NORMAL_DISTANCE, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(current.TOTAL_SERVICE_FEE, 0))"
                + ", toFloat64(coalesce(current.TOTAL_NORMAL_DISTANCE, 0))) else 0 end, 4) as CAR_AVG_MILE_PRICE");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    @Override
    protected String totalField() {
        return "SUM(coalesce(real_pay, 0)) AS TOTAL_REAL_PAY, SUM(coalesce(cnt_order, 0)) AS TOTAL_CAR_ORDER_QUANTITY";
    }

    @Override
    protected String orderByField() {
        return "TOTAL_REAL_PAY desc";
    }

    @Override
    protected BaseConditionPrebuilder.TableAndTimeColBind getTargetTableAndTimeColumn(String statisticalCaliber, Boolean isForegin) {
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = new BaseConditionPrebuilder.TableAndTimeColBind();
        tableAndTimeColBind.setClickHouseTable(ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL);
        tableAndTimeColBind.setDateColumn(REPORT_DATE);
        return tableAndTimeColBind;
    }

    protected String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        String condition = StringUtils.EMPTY;
        switch (subQueryReportBuTypeEnum) {
            case CAR:
                break;
            case CAR_TAXI:
                condition = " and  order_type = 6 and sub_product_line = '1'";
                break;
            case CAR_TAXI_I:
                condition = " and  order_type = 6 and sub_product_line = 'CAR_TAXI_INTL'";
                break;
            case CAR_AIRPORTPICKUP_N:
                condition = " and  order_type = 1";
                break;
            case CAR_AIRPORTPICKUP_I:
                condition = " and  order_type = 2";
                break;
            case CAR_RENTAL:
                condition = " and  order_type = 4";
                break;
            case CAR_CHARTERED:
                condition = " and  order_type = 3";
                break;
            case CAR_RENTAL_I:
                condition = " and  order_type = 18";
                break;
            default:
                break;
        }
        return condition;
    }

    @Override
    protected String getProcutTypeCondition(String productType) {
        return getCarProductTypeCondition(productType);
    }

    /**
     * 用车类型查询条件
     *
     * @param productType
     * @return
     */
    protected String getCarProductTypeCondition(String productType) {
        StringBuilder stringBuilder = new StringBuilder();
        // 国内
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            stringBuilder.append(" and (order_type in(1,3,4) or (order_type = 6 and sub_product_line = '1')) ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            stringBuilder.append(" and (order_type in(2,18) or (order_type = 6 and sub_product_line = 'CAR_TAXI_INTL')) ");
        }
        return stringBuilder.toString();
    }
}
