package com.corpgovernment.resource.schedule.geography.mysql.mapper;

import com.corpgovernment.resource.schedule.config.TkMapper;
import com.corpgovernment.resource.schedule.geography.mysql.entity.CtripCityInfoDo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.dao.DataAccessException;

import java.util.List;

/**
 * 携程商旅
 * <AUTHOR> zhang
 * @date 2023/10/23 13:53
 */
@Mapper
public interface MsBaseCityMapper extends TkMapper<CtripCityInfoDo> {

    /**
     * 获取城市CtripID
     */
    List<String> getCtripCityIdList();

}
