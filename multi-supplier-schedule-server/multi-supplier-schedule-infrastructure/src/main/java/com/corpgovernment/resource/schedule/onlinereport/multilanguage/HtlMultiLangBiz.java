package com.corpgovernment.resource.schedule.onlinereport.multilanguage;


import com.corpgovernment.resource.schedule.onlinereport.clickhouse.multilanguage.CorpHtlMultiLangDao;
import com.corpgovernment.resource.schedule.onlinereport.module.geo.MultiLangDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-01-16 11:08
 * @desc
 */
@Service
public class HtlMultiLangBiz {

    @Autowired
    private CorpHtlMultiLangDao corpHtlMultiLangDao;

    public List<MultiLangDTO> queryHtlInfoMultiLang(List<Integer> idList, String lang) throws Exception {
        lang = StringUtils.equalsIgnoreCase(lang, "zh-hk") ? "zh-CHT" : (StringUtils.equalsIgnoreCase(lang, "zh-cn") ? "zh-CHS" : lang);
        if (CollectionUtils.isEmpty(idList)) {
            return null;
        }
        return corpHtlMultiLangDao.queryHtlInfoMultiLang(idList, lang, MultiLangDTO.class);
    }
}
