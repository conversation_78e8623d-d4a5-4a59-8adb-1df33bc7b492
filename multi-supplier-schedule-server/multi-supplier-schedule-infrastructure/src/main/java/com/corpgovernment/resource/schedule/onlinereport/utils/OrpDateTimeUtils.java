package com.corpgovernment.resource.schedule.onlinereport.utils;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.enums.DateFormatEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.TimeCycleEnum;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Period;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ONE;


/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.utils
 * @description:
 * @author: Chris Yu
 * @create: 2021-11-03 19:20
 **/
@Slf4j
public class OrpDateTimeUtils {

    private static final String REGEX_1 = "\\d{4}-\\d{2}-\\d{2}\\s{1}\\d{2}:\\d{2}:\\d{2}$";
    private static final String REGEX_2 = "\\d{4}-\\d{2}-\\d{2}$";
    private static final String REGEX_1S = "\\d{4}-\\d{2}-\\d{2}\\s{1}\\d{2}:\\d{2}:\\d{2}.\\d{1}$";
    private static final String REGEX_2S = "\\d{4}-\\d{2}-\\d{2}\\s{1}\\d{2}:\\d{2}:\\d{2}.\\d{2}$";
    private static final String REGEX_3S = "\\d{4}-\\d{2}-\\d{2}\\s{1}\\d{2}:\\d{2}:\\d{2}.\\d{3}$";
    private static final String REGEX_4 = "\\d{4}\\d{2}\\d{2}$";
    private static final String REGEX_M = "\\d{4}-\\d{2}$";
    /**
     * 默认日期格式化字串
     */
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DEFAULT_DATA_FORMAT_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String DEFAULT_DATA_FORMAT_S = "yyyy-MM-dd HH:mm:ss.S";
    public static final String DEFAULT_DATE = "yyyy-MM-dd";

    public static final String DEFAULT_DATE_1 = "yyyyMMdd";

    public static final String DEFAULT_MONTH = "yyyy-MM";
    public static final int SUNDAY = 7;

    private static final SimpleDateFormat MONTH_FORMAT = new SimpleDateFormat(DEFAULT_MONTH);

    /**
     * 长日期-获取本地当前时间
     * 格式为：yyyy-MM-dd HH:mm:ss
     *
     * @param dateFormatStr
     * @return
     */
    public static String getCurrentDateTime(String dateFormatStr) {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(dateFormatStr);
        return now.format(dateTimeFormatter);
    }

    /**
     * 比较当前日期与参数日期天数差值
     *
     * @param dateTime      格式：2020-12-28 10:10:10
     * @param differenceVal
     * @return
     */
    public static boolean comparePlusDiffValDay(String dateTime, String differenceVal) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
        LocalDateTime paramDate = LocalDateTime.parse(dateTime, dateTimeFormatter);
        int plusVal = NumberUtils.toInt(differenceVal);
        LocalDateTime plusDate = paramDate.plusDays(plusVal);
        LocalDateTime nowDate = LocalDateTime.now();
        return nowDate.compareTo(plusDate) <= OrpConstants.ZERO;
    }

    /**
     * 指定日期与日期字串 转换成日期字符串
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String formatDateTimeToStr(Date date, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        return localDateTime.format(dateTimeFormatter);
    }

    /**
     * 取当前分区
     *
     * @return
     */
    public static String getCurrentDate() {
        return formatDateTimeToStr(new Date(), DEFAULT_DATE);
    }

    /**
     * long 转换成dateStr
     *
     * @param longDate
     * @return
     */
    public static String getLongToDateStr(Long longDate) {
        Instant instant = Instant.ofEpochMilli(longDate);
        LocalDateTime longLocalDate =
                LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
        return dateTimeFormatter.format(longLocalDate);
    }

    public static Date dateTimeStrToDate(String dateTimeStr) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
        LocalDateTime paramDate = LocalDateTime.parse(dateTimeStr, dateTimeFormatter);
        return localDateTimeToDate(paramDate);
    }

    public static Date dateTimeStrToDate(String dateTimeStr, String format) {
        LocalDate localDate = LocalDate.parse(dateTimeStr, DateTimeFormatter.ofPattern(format));
        return localDateToDate(localDate);
    }


    public static LocalDateTime parseStringToDateTime(String date, String format) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format).withLocale(Locale.CHINESE);
        return LocalDateTime.parse(date, df);
    }

    /**
     * LocalDateTime to Date
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    public static String localDateTimeToStr(LocalDateTime localDateTime, String format) {
        return localDateTime.format(DateTimeFormatter.ofPattern(format));
    }

    /**
     * LocalDate to Date
     */
    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * 计算两个日期相距多少天
     *
     * @return
     */
    public static int getDayNum(String startTime, String endTime) {
        LocalDate start = getLocalDateStartTimeByString(startTime).toLocalDate();
        LocalDate end = getLocalDateEndTimeByString(endTime).toLocalDate();
        // 开始&结束-时间-比较
        Period diffDay = Period.between(start, end);
        return diffDay.getDays();
    }

    /**
     * 判断日期的格式：
     */
    public static DateFormatEnum getDateFormat(String timeStr) {
        Matcher matcher1 = Pattern.compile(REGEX_1, Pattern.CASE_INSENSITIVE).matcher(timeStr);
        Matcher matcher2 = Pattern.compile(REGEX_2, Pattern.CASE_INSENSITIVE).matcher(timeStr);
        Matcher matcher1S = Pattern.compile(REGEX_1S, Pattern.CASE_INSENSITIVE).matcher(timeStr);
        Matcher matcher2S = Pattern.compile(REGEX_2S, Pattern.CASE_INSENSITIVE).matcher(timeStr);
        Matcher matcher3S = Pattern.compile(REGEX_3S, Pattern.CASE_INSENSITIVE).matcher(timeStr);
        Matcher matcherM = Pattern.compile(REGEX_M, Pattern.CASE_INSENSITIVE).matcher(timeStr);
        Matcher matcher4 = Pattern.compile(REGEX_4, Pattern.CASE_INSENSITIVE).matcher(timeStr);

        if (matcher1.matches()) {
            return DateFormatEnum.DATE_FORMAT_SIMPLE;
        }
        if (matcher2.matches()) {
            return DateFormatEnum.DATE_FORMAT;
        }
        if (matcher1S.matches()) {
            return DateFormatEnum.DATE_FORMAT_SIMPLE_1S;
        }
        if (matcher2S.matches()) {
            return DateFormatEnum.DATE_FORMAT_SIMPLE_2S;
        }
        if (matcher3S.matches()) {
            return DateFormatEnum.DATE_FORMAT_SIMPLE_3S;
        }
        if (matcherM.matches()) {
            return DateFormatEnum.DATA_MONTH_FORMAT;
        }
        if (matcher4.matches()) {
            return DateFormatEnum.DATE_FORMAT_STR;
        }
        return null;
    }


    /**
     * 通过起始日期、日期偏移量，将日期段转化为字符串的格式
     */
    public static String getDateCycleStr(LocalDateTime startDate, int startOffsetDay, int endOffsetDay,
                                         int timeCycle, int cycleTimes) {
        if (timeCycle == TimeCycleEnum.MONTH.getCode()) {
            return startDate.toLocalDate().plusMonths(cycleTimes).toString() + "~" + startDate.toLocalDate()
                    .plusMonths(cycleTimes + ONE).minusDays(ONE).toString();
        } else if (timeCycle == TimeCycleEnum.WEEK.getCode()) {
            return startDate.toLocalDate().plusDays(startOffsetDay).toString() + "~" + startDate.toLocalDate()
                    .plusDays(endOffsetDay);
        } else {
            return startDate.toLocalDate().plusDays(startOffsetDay).toString();
        }
    }

    /**
     * 计算差距的天数
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static long betweenDay(String startDate, String endDate) {
        return getLocalDateStartTimeByString(endDate).toLocalDate().toEpochDay()
                - getLocalDateStartTimeByString(startDate).toLocalDate().toEpochDay();
    }

    public static boolean isWholeMonth(LocalDateTime startTime, LocalDateTime endTime) {
        return startTime.getDayOfMonth() == ONE && endTime.getDayOfMonth() == endTime.with(TemporalAdjusters.lastDayOfMonth())
                .toLocalDate().getDayOfMonth();
    }

    /**
     * 取上月日期
     */
    public static String getLastMonthDayStr(Date currentDay) {
        SimpleDateFormat formatter = new SimpleDateFormat(DEFAULT_DATE);
        Calendar c = Calendar.getInstance();
        c.setTime(currentDay);
        c.add(Calendar.MONTH, OrpConstants.MINUS_ONE);
        Date y = c.getTime();
        return formatter.format(y);
    }

    /**
     * 将日期的字符串转化为LocalDateTime类型，如果日期大于等于当前日期则返回此时此刻的LocalDateTime，否则返回当天的最晚时间
     *
     * @param date
     * @return
     */
    public static LocalDateTime getLocalDateEndTimeByString(String date) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
        LocalDateTime nowTime = LocalDateTime.now();

        LocalDateTime dateTime = LocalDateTime.parse(date + " 00:00:00", df);
        return LocalDateTime.of(dateTime.toLocalDate(), LocalTime.MAX);
    }

    public static LocalDateTime getLocalDateStartTimeByString(String date) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
        LocalDateTime dateTime = LocalDateTime.parse(date + " 00:00:00", df);
        return LocalDateTime.of(dateTime.toLocalDate(), LocalTime.MIN);
    }

    public static LocalDateTime getLocalDateTimeByString(String date) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
        return LocalDateTime.parse(date, df);
    }

    public static Timestamp getTimeStampEndTimeByString(String date) {
        return Timestamp.valueOf(getLocalDateEndTimeByString(date));
    }

    public static Timestamp getTimeStampStartTimeByString(String date) {
        return Timestamp.valueOf(getLocalDateStartTimeByString(date));
    }

    public static String timeStampToString(Timestamp timestamp) {
        if (timestamp != null) {
            return timestamp.toLocalDateTime().toLocalDate().toString();
        }
        return OrpConstants.EMPTY;
    }

    /**
     * 根据月份取第一天日期
     *
     * @param year
     * @param month
     * @return
     */
    public static String getBeginTimeOfMonth(int year, int month) {
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate localDate = yearMonth.atDay(ONE);
        LocalDateTime startOfDay = localDate.atStartOfDay();
        ZonedDateTime zonedDateTime = startOfDay.atZone(ZoneId.of("Asia/Shanghai"));
        return formatDateTimeToStr(Date.from(zonedDateTime.toInstant()), DEFAULT_DATE);
    }

    /**
     * 根据月份取最后一天日期
     *
     * @param year
     * @param month
     * @return
     */
    public static String getEndTimeOfMonth(int year, int month) {
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate endOfMonth = yearMonth.atEndOfMonth();
        LocalDateTime localDateTime = endOfMonth.atTime(23, 59, 59, 999);
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
        return formatDateTimeToStr(Date.from(zonedDateTime.toInstant()), DEFAULT_DATE);
    }

    public static String dayAddToStr(Date date, int day) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, day);
        return new SimpleDateFormat(DEFAULT_DATE).format(cal.getTime());
    }

    public static Date dayAddToDate(Date date, int day) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, day);
        return cal.getTime();
    }

    public static String timestampFormatString(Timestamp timestamp) {
        if (Objects.isNull(timestamp)) {
            return StringUtils.EMPTY;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(timestamp.toInstant(), zoneId);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
        return localDateTime.format(dateTimeFormatter);
    }

    /**
     * 获取传入年月的第一天
     *
     * @param dateStr
     * @return
     */
    public static String firstDayOfMonth(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            dateStr = LocalDate.now().toString();
        }
        LocalDate dateTime = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(DEFAULT_DATE));
        return dateTime.withDayOfMonth(ONE).toString();
    }

    /**
     * 获取某一个月份的第一天
     *
     * @param offset 相对于当前月的偏移量 0表示本月，1表示下个月，-1表示上个月，以此类推
     * @return
     */
    public static String getFirstDayOfMonth(int offset) {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfNextMonth = today.plusMonths(offset).with(TemporalAdjusters.firstDayOfMonth());
        return firstDayOfNextMonth.toString();
    }

    /**
     * 获取某一个月份的第一天
     *
     * @param offset 相对于当前月的偏移量 0表示本月，1表示下个月，-1表示上个月，以此类推
     * @return
     */
    public static String getLastDayOfMonth(int offset) {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfNextMonth = today.plusMonths(offset).with(TemporalAdjusters.lastDayOfMonth());
        return firstDayOfNextMonth.toString();
    }

    /**
     * 是否是月的第一天
     *
     * @param dateStr
     * @return
     */
    public static boolean isFirstDayOfMonth(String dateStr) {
        String day = dateStr.substring(OrpConstants.NINE, OrpConstants.TEN);
        return Integer.parseInt(day) == ONE;
    }

    /**
     * 获取传入年月的最后一天
     *
     * @param dateStr
     * @return
     */
    public static String lastDayOfMonth(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            dateStr = LocalDate.now().toString();
        }
        LocalDate dateTime = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(DEFAULT_DATE));
        return dateTime.withDayOfMonth(dateTime.lengthOfMonth()).toString();
    }

    /**
     * 日期同比计算(年减1)
     *
     * @param startTime YYYY-MM
     * @return
     */
    public static String startTimeYoy(String startTime) {
        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(startTime))) {
            startTime = String.format("%s%s", startTime, "-01");
        }
        Date date = dateTimeStrToDate(startTime, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.YEAR, OrpConstants.MINUS_ONE);
        cal.set(Calendar.DAY_OF_MONTH, ONE);
        return formatDateTimeToStr(cal.getTime(), DEFAULT_DATE);
    }

    /**
     * 日期同比计算(年减1)
     *
     * @param endTime YYYY-MM
     * @return
     */
    public static String endTimeYoy(String endTime) {
        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(endTime))) {
            endTime = String.format("%s%s", endTime, "-31");
        }
        Date date = dateTimeStrToDate(endTime, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.YEAR, OrpConstants.MINUS_ONE);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return formatDateTimeToStr(cal.getTime(), DEFAULT_DATE);
    }

    /**
     * 日期同比计算(前年，年减2)
     *
     * @param startTime YYYY-MM
     * @return
     */
    public static String startTimeYoyBefore(String startTime) {
        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(startTime))) {
            startTime = String.format("%s%s", startTime, "-01");
        }
        System.err.println(startTime);
        Date date = dateTimeStrToDate(startTime, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.YEAR, OrpConstants.MINUS_ONE * 2);
        cal.set(Calendar.DAY_OF_MONTH, ONE);
        return formatDateTimeToStr(cal.getTime(), DEFAULT_DATE);
    }

    /**
     * 日期同比计算(前年，年减2)
     *
     * @param endTime YYYY-MM
     * @return
     */
    public static String endTimeYoyBefore(String endTime) {
        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(endTime))) {
            endTime = String.format("%s%s", endTime, "-31");
        }
        Date date = dateTimeStrToDate(endTime, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.YEAR, OrpConstants.MINUS_ONE * 2);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return formatDateTimeToStr(cal.getTime(), DEFAULT_DATE);
    }

    /**
     * 日期环比(月减1)
     *
     * @param startTime YYYY-MM
     * @return
     */
    public static String startTimeMom(String startTime) {
        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(startTime))) {
            startTime = String.format("%s%s", startTime, "-01");
        }
        Date date = dateTimeStrToDate(startTime, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, OrpConstants.MINUS_ONE);
        return formatDateTimeToStr(cal.getTime(), DEFAULT_DATE);
    }


    /**
     * 返回环比日期
     *
     * @param dateTime
     * @return yyyy-MM
     */
    public static String momDate(String dateTime) {
        LocalDate localDate = null;
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateFormatEnum.DATE_FORMAT.getFormat());
        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(dateTime))) {
            dateTime = String.format("%s%s", dateTime, "-01");
            localDate = LocalDate.parse(dateTime, dateTimeFormatter);
        } else {
            localDate = LocalDate.parse(dateTime, dateTimeFormatter);
        }
        LocalDate minusLocalDate = localDate.minusMonths(ONE);
        // 格式化成-yyyy-MM
        DateTimeFormatter datFormatter = DateTimeFormatter.ofPattern(DateFormatEnum.DATA_MONTH_FORMAT.getFormat());
        return minusLocalDate.format(datFormatter);
    }


    public static String searchTimeMom(String startTime, String endTime, int type) {
        LocalDate startLocalDate = null;
        LocalDate endLocalDate = null;
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateFormatEnum.DATE_FORMAT.getFormat());
        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(startTime))) {
            startTime = String.format("%s%s", startTime, "-01");
            startLocalDate = LocalDate.parse(startTime, dateTimeFormatter);
        } else {
            startLocalDate = LocalDate.parse(startTime, dateTimeFormatter);
        }

        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(endTime))) {
            endTime = String.format("%s%s", endTime, "-01");
            endLocalDate = LocalDate.parse(endTime, dateTimeFormatter);
        } else {
            endLocalDate = LocalDate.parse(endTime, dateTimeFormatter);
        }
        // 计算月份差值
        Period diffPeriod = Period.between(startLocalDate, endLocalDate);
        // 月份差值+1
        int minusMonthNum = diffPeriod.getMonths() + ONE;
        if (type == OrpConstants.ZERO) {
            LocalDate calcStartMom = startLocalDate.minusMonths(minusMonthNum);
            LocalDate fristDayOfMonth = calcStartMom.withDayOfMonth(ONE);
            return fristDayOfMonth.toString();
        } else {
            LocalDate calcEndMom = endLocalDate.minusMonths(minusMonthNum);
            LocalDate lastDayOfMonth = calcEndMom.withDayOfMonth(calcEndMom.lengthOfMonth());
            return lastDayOfMonth.toString();
        }
    }

    /**
     * 日期环比(月减1)
     *
     * @param endTime YYYY-MM
     * @return
     */
    public static String endTimeMom(String endTime) {
        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(endTime))) {
            endTime = String.format("%s%s", endTime, "-31");
        }
        Date date = dateTimeStrToDate(endTime, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, OrpConstants.MINUS_ONE);
        return formatDateTimeToStr(cal.getTime(), DEFAULT_DATE);
    }


    /**
     * @param startTime 最小时间 2015-01-01
     * @param endTime   最大时间 2015-10-01
     * @return 日期集合 格式为 年-月-日
     * @throws Exception
     */
    public static List<String> getdayBetween(String startTime, String endTime) {
        SimpleDateFormat dayFormat = new SimpleDateFormat(DEFAULT_DATE);
        List<String> result = Lists.newArrayList();
        try {
            Calendar min = Calendar.getInstance();
            Calendar max = Calendar.getInstance();
            min.setTime(dayFormat.parse(startTime));
            min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), min.get(Calendar.DATE));
            max.setTime(dayFormat.parse(endTime));
            max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), max.get(Calendar.DATE));
            Calendar curr = min;
            // 两种情况1.curr小于max，2.curr等于max
            while (curr.before(max) || curr.compareTo(max) == 0) {
                result.add(dayFormat.format(curr.getTime()));
                curr.add(Calendar.DATE, ONE);
            }
        } catch (Exception e) {
            log.error("getDateBetween>>>>", ExceptionUtils.getFullStackTrace(e));
        }
        return result;
    }

    /**
     * @param startTime 最小时间 2015-01
     * @param endTime   最大时间 2015-10
     * @return 日期集合 格式为 年-月
     * @throws Exception
     */
    public static List<String> getMonthBetween(String startTime, String endTime) {
        SimpleDateFormat monthFormat = new SimpleDateFormat(DEFAULT_MONTH);
        List<String> result = Lists.newArrayList();
        try {
            Calendar min = Calendar.getInstance();
            Calendar max = Calendar.getInstance();
            min.setTime(monthFormat.parse(startTime));
            min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), ONE);
            max.setTime(monthFormat.parse(endTime));
            max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), OrpConstants.TWO);
            Calendar curr = min;
            while (curr.before(max)) {
                result.add(monthFormat.format(curr.getTime()));
                curr.add(Calendar.MONTH, ONE);
            }
        } catch (Exception e) {
            log.error("getMonthBetween>>>>", ExceptionUtils.getFullStackTrace(e));
        }
        return result;
    }

    /**
     * 校验日期格式，startTime>=endTime
     * TRUE:格式OK，FALSE:格式异常
     *
     * @param startTime 最小时间  2015-01
     * @param endTime   最大时间 2015-10
     * @return 日期集合 格式为 年-月
     * @throws Exception
     */
    public static Boolean dataFormatValid(String startTime, String endTime) {
        SimpleDateFormat monthFormat = new SimpleDateFormat(DEFAULT_MONTH);
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return Boolean.FALSE;
        }
        try {
            Calendar min = Calendar.getInstance();
            Calendar max = Calendar.getInstance();
            min.setTime(monthFormat.parse(startTime));
            min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), ONE);
            max.setTime(monthFormat.parse(endTime));
            max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), OrpConstants.TWO);
            return min.before(max);
        } catch (Exception e) {
            log.error("getMonthBetween>>>>", ExceptionUtils.getFullStackTrace(e));
            return Boolean.FALSE;
        }
    }

    /**
     * 日期同比
     *
     * @param startTime YYYY-MM
     * @param offset    需要向前推的偏移量
     * @return
     */
    public static String startTimeMom(String startTime, int offset) {
        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(startTime))) {
            startTime = String.format("%s%s", startTime, "-01");
        }
        Date date = dateTimeStrToDate(startTime, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.MONTH, OrpConstants.MINUS_ONE * offset);
        cal.set(Calendar.DAY_OF_MONTH, ONE);
        return formatDateTimeToStr(cal.getTime(), DEFAULT_DATE);
    }

    /**
     * 实时数据compare
     *
     * @param startTime yyyy-MM-dd HH:mm:ss
     * @param offset    需要向前推的偏移量
     * @return
     */
    public static String realTime(String startTime, int offset) {
        LocalDateTime start = getLocalDateTimeByString(startTime);
        start = start.plus(OrpConstants.MINUS_ONE * offset, ChronoUnit.DAYS);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
        return start.format(dateTimeFormatter);
    }

    /**
     * 最近时间(单位小时)
     *
     * @param date   yyyy-MM-dd HH:mm:ss
     * @param offset 需要向前推的偏移量
     * @return
     */
    public static String realTimePreHour(Date date, int offset) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.HOUR_OF_DAY, OrpConstants.MINUS_ONE * offset);
        return formatDateTimeToStr(cal.getTime(), DEFAULT_DATE_FORMAT);
    }

    /**
     * 日期环比
     *
     * @param endTime YYYY-MM
     * @param offset  需要向前推的偏移量
     * @return
     */
    public static String endTimeMom(String endTime, int offset) {
        if (DateFormatEnum.DATA_MONTH_FORMAT.equals(getDateFormat(endTime))) {
            endTime = String.format("%s%s", endTime, "-31");
        }
        Date date = dateTimeStrToDate(endTime, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.MONTH, OrpConstants.MINUS_ONE * offset);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return formatDateTimeToStr(cal.getTime(), DEFAULT_DATE);
    }

    /**
     * 环比日期
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List momTime(String startTime, String endTime) {
        boolean isWholeMonth = isWholeMonth(startTime, endTime);
        int offset = 0;
        Date dateStart = dateTimeStrToDate(startTime, DEFAULT_DATE);
        Calendar calStart = Calendar.getInstance();
        calStart.setTimeInMillis(dateStart.getTime());

        Date dateEnd = dateTimeStrToDate(endTime, DEFAULT_DATE);
        Calendar calEnd = Calendar.getInstance();
        calEnd.setTimeInMillis(dateEnd.getTime());

        if (isWholeMonth) {
            offset = getMonthNum(startTime, endTime) + 1;
            calStart.add(Calendar.MONTH, OrpConstants.MINUS_ONE * offset);
            calStart.set(Calendar.DAY_OF_MONTH, ONE);
            calEnd.add(Calendar.MONTH, OrpConstants.MINUS_ONE * offset);
            calEnd.set(Calendar.DAY_OF_MONTH, calEnd.getActualMaximum(Calendar.DAY_OF_MONTH));
        } else {
            offset = Integer.valueOf(String.valueOf(betweenDay(startTime, endTime) + ONE));
            calStart.add(Calendar.DAY_OF_YEAR, OrpConstants.MINUS_ONE * offset);
            calEnd.add(Calendar.DAY_OF_YEAR, OrpConstants.MINUS_ONE * offset);
        }
        String startMom = formatDateTimeToStr(calStart.getTime(), DEFAULT_DATE);
        String endMom = formatDateTimeToStr(calEnd.getTime(), DEFAULT_DATE);
        return Arrays.asList(startMom, endMom);
    }


    /**
     * 同
     *
     * @param startTime
     * @param endTime
     * @param offset
     * @return
     */
    public static List<String> yoyTime(String startTime, String endTime, int offset) {

        boolean isWholeMonth = isWholeMonth(startTime, endTime);
        if (isWholeMonth) {
            String yoyStart = (Integer.parseInt(startTime.substring(0, 4)) - offset / OrpConstants.ONE_YEAR_DAYS) + startTime.substring(4, 10);
            String yoyEndFirst = String.format("%s-01",
                    (Integer.parseInt(endTime.substring(0, 4)) - offset / OrpConstants.ONE_YEAR_DAYS) + endTime.substring(4, 7)
            );
            LocalDateTime yoyEndFirstDate = getLocalDateStartTimeByString(yoyEndFirst);
            String yoyEnd = yoyEndFirstDate.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate().toString();
            return Arrays.asList(yoyStart, yoyEnd);
        }
        Date dateStart = dateTimeStrToDate(startTime, DEFAULT_DATE);
        Calendar calStart = Calendar.getInstance();
        calStart.setTimeInMillis(dateStart.getTime());
        calStart.add(Calendar.DAY_OF_YEAR, OrpConstants.MINUS_ONE * offset);
        String startYoy = formatDateTimeToStr(calStart.getTime(), DEFAULT_DATE);

        Date dateEnd = dateTimeStrToDate(endTime, DEFAULT_DATE);
        Calendar calEnd = Calendar.getInstance();
        calEnd.setTimeInMillis(dateEnd.getTime());
        calEnd.add(Calendar.DAY_OF_YEAR, OrpConstants.MINUS_ONE * offset);
        String endYoy = formatDateTimeToStr(calEnd.getTime(), DEFAULT_DATE);
        return Arrays.asList(startYoy, endYoy);
    }

    /**
     * 计算两个日期相距多少月
     *
     * @return
     */
    public static int getMonthNum(String startTime, String endTime) {
        LocalDate start = getLocalDateStartTimeByString(startTime).toLocalDate();
        LocalDate end = getLocalDateEndTimeByString(endTime).toLocalDate();
        int year = end.getYear() - start.getYear();
        int month = end.getMonthValue() + year * 12 - start.getMonthValue();
        return month;
    }

    public static boolean isWholeMonth(String startTime, String endTime) {
        LocalDateTime start = getLocalDateStartTimeByString(startTime);
        LocalDateTime end = getLocalDateEndTimeByString(endTime);
        return isWholeMonth(start, end);
    }


    /**
     * 计算一个日期是否在两个日期中间
     *
     * @return
     */
    public static boolean dateInRange(String start, String end, String judgeDate) {
        return betweenDay(start, judgeDate) + ONE > 0 && betweenDay(judgeDate, end) + ONE > 0;
    }

    public static Map<String, MonthSplitPair> momSplit(String startDate, String endDate) {
        HashMap<String, MonthSplitPair> splitResults = new HashMap<>();
        // 起始日期的时间段, 始末跨月与否
        String startDateMonthEnd = lastDayOfMonth(startDate);
        Date startDateMonthEndDate = dateTimeStrToDate(startDateMonthEnd, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(startDateMonthEndDate.getTime());
        cal.add(Calendar.DATE, 1);
        Date wholeMonthStartDate = cal.getTime();

        MonthSplitPair pair = betweenDay(startDateMonthEnd, endDate) + ONE > 0 ?
                MonthSplitPair.create(startDate, startDateMonthEnd) : MonthSplitPair.create(startDate, endDate);

        splitResults.put(startDate.substring(OrpConstants.ZERO, OrpConstants.SEVEN), pair);

        // 终止日期的时间段
        String endDateMonthStart = firstDayOfMonth(endDate);
        Date wholeMonthEndDate = dateTimeStrToDate(endDateMonthStart, DEFAULT_DATE);

        MonthSplitPair pair1 = betweenDay(startDate, endDateMonthStart) + ONE < 0 ?
                MonthSplitPair.create(startDate, endDate) : MonthSplitPair.create(endDateMonthStart, endDate);

        splitResults.put(endDate.substring(OrpConstants.ZERO, OrpConstants.SEVEN), pair1);

        // 中间整月时间段
        while (wholeMonthStartDate.before(wholeMonthEndDate)) {
            String var1 = formatDateTimeToStr(wholeMonthStartDate, DEFAULT_DATE);
            String var2 = lastDayOfMonth(var1);
            splitResults.put(
                    var1.substring(OrpConstants.ZERO, OrpConstants.SEVEN),
                    MonthSplitPair.create(var1, var2)
            );
            cal.clear();
            cal.setTimeInMillis(dateTimeStrToDate(var2, DEFAULT_DATE).getTime());
            cal.add(Calendar.DATE, 1);
            wholeMonthStartDate = cal.getTime();
        }

        return splitResults;
    }

    /**
     * 时间偏移
     *
     * @param timeScale     时间跨度
     * @param timeScaleUnit 时间跨度单位（0-年；1-月；2-日）
     * @return
     */
    public static Date currentDateOffset(Integer timeScale, Integer timeScaleUnit) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        switch (timeScaleUnit) {
            case 0:
                cal.add(Calendar.YEAR, timeScale);
                break;
            case 1:
                cal.add(Calendar.MONTH, timeScale);
                break;
            case 2:
                cal.add(Calendar.DAY_OF_MONTH, timeScale);
                break;
            default:
                break;
        }
        return cal.getTime();
    }

    @Data
    public static class MonthSplitPair {

        boolean isWholeMonth;
        String startDate;
        String endDate;
        String yoyStartDate;
        String yoyEndDate;
        String momStartDate;
        String momEndDate;

        public static MonthSplitPair create(String startDate, String endDate) {
            MonthSplitPair pair = new MonthSplitPair();
            pair.setStartDate(startDate);
            pair.setEndDate(endDate);
            pair.setWholeMonth(OrpDateTimeUtils.isWholeMonth(startDate, endDate));
            List<String> yoyTimes = OrpDateTimeUtils.yoyTime(startDate, endDate, OrpConstants.ONE_YEAR_DAYS);
            pair.setYoyStartDate(yoyTimes.get(0));
            pair.setYoyEndDate(yoyTimes.get(1));
            List<String> momTimes = OrpDateTimeUtils.momTime(startDate, endDate);
            pair.setMomStartDate(momTimes.get(0));
            pair.setMomEndDate(momTimes.get(1));
            return pair;
        }
    }

    /**
     * 获得当前时间是当前年的第几周
     *
     * @param dateStr （格式yyyy-MM-dd）
     * @return
     */
    public static int getWeekOfYear(String dateStr) {
        Date date = dateTimeStrToDate(dateStr, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        return cal.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * 获得当前周的第一天
     *
     * @param dateStr （格式yyyy-MM-dd）
     * @return
     */
    public static String getFirstDayOfWeek(String dateStr) {
        LocalDate startWeek = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(DEFAULT_DATE));
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE);
        return startWeek.with(DayOfWeek.MONDAY).format(dateTimeFormatter);
    }

    /**
     * 获得当前周的最后一天
     *
     * @param dateStr （格式yyyy-MM-dd）
     * @return
     */
    public static String getLastDayOfWeek(String dateStr) {
        LocalDate startWeek = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(DEFAULT_DATE));
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE);
        return startWeek.with(DayOfWeek.SUNDAY).format(dateTimeFormatter);
    }

    /**
     * 时间偏移
     *
     * @param dateStr （格式yyyy-MM-dd）
     * @param offset  需要向前推的偏移量（单位：天）
     * @return
     */
    public static String dateOffset(String dateStr, int offset) {
        Date date = dateTimeStrToDate(dateStr, DEFAULT_DATE);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.DAY_OF_MONTH, offset);
        return formatDateTimeToStr(cal.getTime(), DEFAULT_DATE);
    }
    /**
     * 获取日期
     *
     * @param dateStr
     * @return
     */
    public static Date getOrDefaultDate(String dateStr, Date defaultDate) {
        DateFormatEnum dateFormatEnum = OrpDateTimeUtils.getDateFormat(dateStr);
        return null != dateFormatEnum ? OrpDateTimeUtils.dateTimeStrToDate(dateStr, dateFormatEnum.getFormat()) : defaultDate;
    }
    /**
     * 判断两个Date的大小
     * true: date1 > date2
     * false: date1 <= date2
     */
    public static boolean compareDatePair(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        return date1.compareTo(date2) > 0;
    }
    public static void main(String[] args) {
        System.out.println(yoyTime("2022-05-01", "2022-05-31", 1));
    }

}
