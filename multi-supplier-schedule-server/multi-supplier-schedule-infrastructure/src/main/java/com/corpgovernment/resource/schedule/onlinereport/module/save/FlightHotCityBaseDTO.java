package com.corpgovernment.resource.schedule.onlinereport.module.save;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.save
 * @description:机票节省分析-机票热门航段、出发、达到城市
 * @author: md_wang
 * @create: 2022-08-02 18:04
 **/
@Data
public class FlightHotCityBaseDTO {
    /**
     * 热门航段
     */
    @Column(name = "flight_city")
    @Type(value = Types.VARCHAR)
    private String flightCity;

    /**
     * 航段Code
     */
    @Column(name = "flight_city_code")
    @Type(value = Types.VARCHAR)
    private String flightCityCode;
    /**
     * 航段EN
     */
    @Column(name = "flight_city_en")
    @Type(value = Types.VARCHAR)
    private String flightCityEn;

    /**
     * 出发城市
     */
    @Column(name = "departure_city_id")
    @Type(value = Types.INTEGER)
    private Integer departureCityId;
    @Column(name = "departure_city_name")
    @Type(value = Types.VARCHAR)
    private String departureCityName;
    @Column(name = "departure_city_name_en")
    @Type(value = Types.VARCHAR)
    private String departureCityCameEn;
    @Column(name = "departure_city_code")
    @Type(value = Types.VARCHAR)
    private String departureCityCode;

    /**
     * 到达城市
     */
    @Column(name = "arrival_city_id")
    @Type(value = Types.INTEGER)
    private Integer arrivalCityId;
    @Column(name = "arrival_city_name")
    @Type(value = Types.VARCHAR)
    private String arrivalCityName;
    @Column(name = "arrival_city_name_en")
    @Type(value = Types.VARCHAR)
    private String arrivalCityCameEn;
    @Column(name = "arrival_city_code")
    @Type(value = Types.VARCHAR)
    private String arrivalCityCode;

    /**
     * 热点城市
     */
    @Column(name = "hot_city_id")
    @Type(value = Types.INTEGER)
    private Integer hotCityId;
    @Column(name = "hot_city_name")
    @Type(value = Types.VARCHAR)
    private String hotCityName;
    @Column(name = "hot_city_name_en")
    @Type(value = Types.VARCHAR)
    private String hotCityCameEn;
    @Column(name = "hot_city_code")
    @Type(value = Types.VARCHAR)
    private String hotCityCode;
    /**
     * 票张
     */
    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;
}
