package com.corpgovernment.resource.schedule.onlinereport.clickhouse.position;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TravelPositionDetailQueryColnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TravelPositionStepEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionDetail;
import com.corpgovernment.resource.schedule.domain.onlinereport.travelposition.TravelPositionCityDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.travelposition.TravelPositionDetailDTO;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.FlightClassEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.ProductTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.travelposition.TravelPositionBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.dao.impl.travelposition
 * @description:
 * @author: md_wang
 * @create: 2022-08-19 16:10
 **/
@Repository
@Slf4j
public class TravelPositionDao extends AbstractClickhouseBaseDao {

    private static final ClickHouseTable TABLE = ClickHouseTable.ADM_INDEX_ONE_TRIP_FULL_TRIP_ID_GENERATE_ALL;


    /**
     * 查询-差旅足迹 城市
     */
    public List<TravelPositionCityDTO> queryTravelPositionCity(BaseQueryConditionDTO requestDto) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select sub_trip_city_id,sub_trip_city_name,count(distinct sub_one_trip_id) as subTripTripCount");
        sqlBuilder.append(OrpConstants.FROM).append(TABLE.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d='").append(querySingleTablePartition(TABLE)).append("'");
        List<Object> paramList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(requestDto.getCityNames())) {
            sqlBuilder.append(" and sub_trip_city_name like ? ");
            paramList.add("%".concat(requestDto.getCityNames().get(OrpConstants.ZERO)).concat("%"));
        }
        if (FlightClassEnum.N.getClassType().equals(requestDto.getProductType())) {
            sqlBuilder.append(" and sub_trip_city_countryid = 1 ");
        } else if (FlightClassEnum.I.getClassType().equals(requestDto.getProductType())) {
            sqlBuilder.append(" and sub_trip_city_countryid <> 1 ");
        }
        sqlBuilder.append(" and COALESCE(sub_trip_city_id,-1) !=-1 ");
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, paramList));
        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append("sub_trip_city_id,sub_trip_city_name");
        sqlBuilder.append(OrpConstants.ORDER_BY).append("subTripTripCount desc ");
        sqlBuilder.append(" limit 50 ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error("queryTravelPositionCity", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, TravelPositionCityDTO.class, "queryTravelPositionCity");
    }

    /**
     * 查询-差旅足迹 城市
     */
    public List<TravelPositionCityDTO> queryTravelPositionHotCity(BaseQueryConditionDTO requestDto, String lang) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String dimName = SharkUtils.isZH(lang) ? " sub_trip_city_name" : " sub_trip_city_name_en ";
        sqlBuilder.append("select sub_trip_city_id," + dimName + " as subTripCityName,count(distinct sub_one_trip_id) as subTripTripCount");
        sqlBuilder.append(OrpConstants.FROM).append(TABLE.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d='").append(querySingleTablePartition(TABLE)).append("'");
        if (FlightClassEnum.N.getClassType().equals(requestDto.getProductType())) {
            sqlBuilder.append(" and sub_trip_city_countryid = 1 ");
        } else if (FlightClassEnum.I.getClassType().equals(requestDto.getProductType())) {
            sqlBuilder.append(" and sub_trip_city_countryid <> 1 ");
        }
        sqlBuilder.append(" and COALESCE(sub_trip_city_id,-1) !=-1 ");
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, paramList));
        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append("sub_trip_city_id,").append(dimName);
        sqlBuilder.append(OrpConstants.ORDER_BY).append("subTripTripCount desc ");
        sqlBuilder.append(" limit 10 ");
        return queryBySql(sqlBuilder.toString(), paramList, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error("queryTravelPositionHotCity", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, TravelPositionCityDTO.class, "queryTravelPositionHotCity");
    }

    /**
     * 查询-差旅足迹 省份
     */
    public List<TravelPositionCityDTO> queryTravelPositionProvince(BaseQueryConditionDTO requestDto) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select sub_trip_province_name,sub_trip_province_id,count(distinct sub_one_trip_id) as subTripTripCount");
        sqlBuilder.append(OrpConstants.FROM).append(TABLE.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d='").append(querySingleTablePartition(TABLE)).append("'");
        List<Object> paramList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(requestDto.getProvinceNames())) {
            sqlBuilder.append(" and sub_trip_province_name like ? ");
            paramList.add("%".concat(requestDto.getProvinceNames().get(OrpConstants.ZERO)).concat("%"));
        }
        sqlBuilder.append(" and sub_trip_city_countryid = 1 ");
        sqlBuilder.append(" and COALESCE(sub_trip_province_id,-1) !=-1 ");
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, paramList));
        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append("sub_trip_province_name,sub_trip_province_id");
        sqlBuilder.append(OrpConstants.ORDER_BY).append("subTripTripCount desc ");
        sqlBuilder.append(" limit 50 ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error("queryTravelPositionProvince", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, TravelPositionCityDTO.class, "queryTravelPositionProvince");
    }

    /**
     * 查询-差旅足迹 省份
     */
    public List<TravelPositionCityDTO> queryTravelPositionHotProvince(BaseQueryConditionDTO requestDto, String lang) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String dimName = SharkUtils.isZH(lang) ? " sub_trip_province_name" : " sub_trip_province_name_en ";
        sqlBuilder.append("select " + dimName + " as subTripProvinceName,sub_trip_province_id,count(distinct sub_one_trip_id) as subTripTripCount");
        sqlBuilder.append(OrpConstants.FROM).append(TABLE.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d='").append(querySingleTablePartition(TABLE)).append("'");
        sqlBuilder.append(" and sub_trip_city_countryid = 1 ");
        sqlBuilder.append(" and COALESCE(sub_trip_province_id,-1) !=-1 ");
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        List<Object> paramList = Lists.newArrayList();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, paramList));
        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append(dimName).append(",sub_trip_province_id");
        sqlBuilder.append(OrpConstants.ORDER_BY).append("subTripTripCount desc ");
        sqlBuilder.append(" limit 10 ");
        return queryBySql(sqlBuilder.toString(), paramList, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error("queryTravelPositionHotProvince", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, TravelPositionCityDTO.class, "queryTravelPositionHotProvince");
    }

    /**
     * 查询-差旅足迹 国家
     */
    public List<TravelPositionCityDTO> queryTravelPositionCountry(BaseQueryConditionDTO requestDto) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select sub_trip_city_countryname,sub_trip_city_countryid,count(distinct sub_one_trip_id) as subTripTripCount");
        sqlBuilder.append(OrpConstants.FROM).append(TABLE.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d='").append(querySingleTablePartition(TABLE)).append("'");
        List<Object> paramList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(requestDto.getProvinceNames())) {
            sqlBuilder.append(" and sub_trip_city_countryname like ? ");
            paramList.add("%".concat(requestDto.getCountryNames().get(OrpConstants.ZERO)).concat("%"));
        }
        sqlBuilder.append(" and sub_trip_city_countryid <> 1 ");
        sqlBuilder.append(" and COALESCE(sub_trip_city_countryid,-1) !=-1 ");
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append("sub_trip_city_countryname,sub_trip_city_countryid");
        sqlBuilder.append(OrpConstants.ORDER_BY).append("subTripTripCount desc ");
        sqlBuilder.append(" limit 50 ");
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error("queryTravelPositionCountry", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, TravelPositionCityDTO.class, "queryTravelPositionCountry");
    }

    /**
     * 查询-差旅足迹 国家
     */
    public List<TravelPositionCityDTO> queryTravelPositionHotCountry(String lang) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String dimName = SharkUtils.isZH(lang) ? " sub_trip_city_countryname" : " sub_trip_city_countryname_en ";

        sqlBuilder.append("select " + dimName + " as subTripCityCountryname,sub_trip_city_countryid,count(distinct sub_one_trip_id) as subTripTripCount");
        sqlBuilder.append(OrpConstants.FROM).append(TABLE.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d='").append(querySingleTablePartition(TABLE)).append("'");
        sqlBuilder.append(" and sub_trip_city_countryid <> 1 ");
        sqlBuilder.append(" and COALESCE(sub_trip_city_countryid,-1) !=-1 ");
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append(dimName).append(",sub_trip_city_countryid");
        sqlBuilder.append(OrpConstants.ORDER_BY).append("subTripTripCount desc ");
        sqlBuilder.append(" limit 10 ");
        return queryBySql(sqlBuilder.toString(), OrpConstants.EMPTY, (req, statement) -> mapCommonRequest(Lists.newArrayList(), statement), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error("queryTravelPositionHotCountry", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, TravelPositionCityDTO.class, "queryTravelPositionHotCountry");
    }

    /**
     * 查询-差旅足迹明细 分页查询
     */
    public List<TravelPositionDetailDTO> queryTravelPositionDetailByPage(BaseQueryConditionDTO requestDto, String lang) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String dimName1 = SharkUtils.isZH(lang) ? " start_province_name" : " start_province_name_en ";
        String dimName2 = SharkUtils.isZH(lang) ? " start_city_name" : " start_city_name_en ";
        String dimName3 = SharkUtils.isZH(lang) ? " end_province_name" : " end_province_name_en ";
        String dimName4 = SharkUtils.isZH(lang) ? " end_city_name" : " end_city_name_en ";

        sqlBuilder.append("select order_id,order_type,isself as isSelf,uid,uidname as uidName,user_name as passengerName," +
                "flightno_trainno_hotelname,start_province_id," + dimName1 + " as startProvinceName,start_city_id," + dimName2 + " AS startCityName," +
                "end_province_id," + dimName3 + " AS endProvinceName,end_city_id," + dimName4 + " AS endCityName," +
                "start_sub_trip_date," +
                "end_sub_trip_date," +
                "start_time," +
                "end_time," +
                "dept1,dept2,dept3,dept4,dept5,dept6,dept7,dept8,dept9,dept10," +
                "cost_center1 as costcenter1,cost_center2 as costcenter2,cost_center3 as costcenter3," +
                "cost_center4 as costcenter4,cost_center5 as costcenter5,cost_center6 as costcenter6,masterhotelid ");
        sqlBuilder.append(OrpConstants.FROM).append(TABLE.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d='").append(querySingleTablePartition(TABLE)).append("'");
        List<Object> paramList = Lists.newArrayList();
        List<QueryReportBuTypeEnum> buTypes = Optional.ofNullable(requestDto.getBuType()).orElse(Lists.newArrayList()).stream()
                .filter(t -> Objects.nonNull(t))
                .filter(t -> QueryReportBuTypeEnum.hotel.equals(t) ||
                        QueryReportBuTypeEnum.flight.equals(t) ||
                        QueryReportBuTypeEnum.train.equals(t)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(buTypes)) {
            sqlBuilder.append(buildBuNameSql(buTypes, "order_type", paramList));
        }
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, paramList));
        List<TravelPositionDetail> queryColList = Optional.ofNullable(requestDto.getQueryColumn()).orElse(Lists.newArrayList()).stream()
                .filter(t -> Objects.nonNull(t))
                .filter(t -> Objects.nonNull(t.getQueryKey()) && Objects.nonNull(t.getQueryValue()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(queryColList)) {
            sqlBuilder.append(buildQueryColSql(queryColList, paramList));
        }
        boolean isExist = Boolean.FALSE;
        sqlBuilder.append(" and ( 1=1 ");
        List<Integer> cityList = Optional.ofNullable(requestDto.getCityIds()).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cityList)) {
            sqlBuilder.append(" and ").append(buildCityIdsSql(cityList, "sub_trip_city_id", paramList));
            isExist = Boolean.TRUE;
        }
        List<Integer> provinceList = Optional.ofNullable(requestDto.getProvinceIds()).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(provinceList)) {
            if (BooleanUtils.isTrue(isExist)) {
                sqlBuilder.append(" or ").append(buildCityIdsSql(provinceList, "sub_trip_province_id", paramList));
            } else {
                sqlBuilder.append(" and ").append(buildCityIdsSql(provinceList, "sub_trip_province_id", paramList));
            }
            isExist = Boolean.TRUE;
        }
        List<Integer> countryList = Optional.ofNullable(requestDto.getCountryIds()).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(countryList)) {
            if (BooleanUtils.isTrue(isExist)) {
                sqlBuilder.append(" or ").append(buildCityIdsSql(countryList, "sub_trip_city_countryid", paramList));
            } else {
                sqlBuilder.append(" and ").append(buildCityIdsSql(countryList, "sub_trip_city_countryid", paramList));
            }
        }
        sqlBuilder.append(" ) ");
        if (StringUtils.isNotEmpty(requestDto.getStartTime()) && StringUtils.isNotEmpty(requestDto.getEndTime())) {
            sqlBuilder.append(buildTravelStepTime(requestDto, paramList));
        }
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        appendProductType(sqlBuilder, requestDto.getProductType());
        appendOrderBy(sqlBuilder, buTypes);
        sqlBuilder.append(" limit ?,? ");
        paramList.add((limitStr(requestDto.getPager())));
        paramList.add(limitEnd(requestDto.getPager()));
        return queryBySql(sqlBuilder.toString(), paramList, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error("queryTravelPositionDetailByPage", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, TravelPositionDetailDTO.class, "queryTravelPositionDetailByPage");
    }

    /**
     * 查询-差旅足迹明细 分页查询
     */
    public Integer queryTravelPositionDetailCount(BaseQueryConditionDTO requestDto) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select count(1) as totalCount ");
        sqlBuilder.append(OrpConstants.FROM).append(TABLE.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d='").append(querySingleTablePartition(TABLE)).append("'");
        List<Object> paramList = Lists.newArrayList();
        List<QueryReportBuTypeEnum> buTypes = Optional.ofNullable(requestDto.getBuType()).orElse(Lists.newArrayList()).stream()
                .filter(t -> Objects.nonNull(t))
                .filter(t -> QueryReportBuTypeEnum.hotel.equals(t) ||
                        QueryReportBuTypeEnum.flight.equals(t) ||
                        QueryReportBuTypeEnum.train.equals(t)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(buTypes)) {
            sqlBuilder.append(buildBuNameSql(buTypes, "order_type", paramList));
        }
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, paramList));
        List<TravelPositionDetail> queryColList = Optional.ofNullable(requestDto.getQueryColumn()).orElse(Lists.newArrayList()).stream()
                .filter(t -> Objects.nonNull(t))
                .filter(t -> Objects.nonNull(t.getQueryKey()) && Objects.nonNull(t.getQueryValue()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(queryColList)) {
            sqlBuilder.append(buildQueryColSql(queryColList, paramList));
        }
        boolean isExist = Boolean.FALSE;
        sqlBuilder.append(" and ( 1=1 ");
        List<Integer> cityList = Optional.ofNullable(requestDto.getCityIds()).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cityList)) {
            sqlBuilder.append(" and ").append(buildCityIdsSql(cityList, "sub_trip_city_id", paramList));
            isExist = Boolean.TRUE;
        }
        List<Integer> provinceList = Optional.ofNullable(requestDto.getProvinceIds()).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(provinceList)) {
            if (BooleanUtils.isTrue(isExist)) {
                sqlBuilder.append(" or ").append(buildCityIdsSql(provinceList, "sub_trip_province_id", paramList));
            } else {
                sqlBuilder.append(" and ").append(buildCityIdsSql(provinceList, "sub_trip_province_id", paramList));
            }
            isExist = Boolean.TRUE;
        }
        List<Integer> countryList = Optional.ofNullable(requestDto.getCountryIds()).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(countryList)) {
            if (BooleanUtils.isTrue(isExist)) {
                sqlBuilder.append(" or ").append(buildCityIdsSql(countryList, "sub_trip_city_countryid", paramList));
            } else {
                sqlBuilder.append(" and ").append(buildCityIdsSql(countryList, "sub_trip_city_countryid", paramList));
            }
        }
        sqlBuilder.append(" ) ");

        if (StringUtils.isNotEmpty(requestDto.getStartTime()) && StringUtils.isNotEmpty(requestDto.getEndTime())) {
            sqlBuilder.append(buildTravelStepTime(requestDto, paramList));
        }
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        appendProductType(sqlBuilder, requestDto.getProductType());
        return queryBySql(sqlBuilder.toString(), paramList, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return mapIntResult(u, "totalCount");
            } catch (Exception e) {
                log.error("queryTravelPositionDetailCount", ExceptionUtils.getFullStackTrace(e));
            }
            return OrpConstants.ZERO;
        }, Integer.class, "queryTravelPositionDetailCount");
    }

    /**
     * city in (?,?)
     */
    public static String buildCityIdsSql(List<Integer> cityIds, String key, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(key + " in (");
        for (int i = OrpConstants.ZERO; i < cityIds.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != cityIds.size() - OrpConstants.ONE) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            paramList.add(cityIds.get(i));
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    private void appendOrderBy(StringBuilder sqlBuilder, List<QueryReportBuTypeEnum> buTypes) {
        if (CollectionUtils.isNotEmpty(buTypes) && buTypes.size() == OrpConstants.ONE) {
            sqlBuilder.append(" order by start_time desc ");
            return;
        }
        /**
         * order by
         * case when order_type = 'flt' then 1
         *      when order_type = 'train' then 2
         *      when order_type = 'htl' then 3
         * else 0 end
         */
        sqlBuilder.append(" order by ");
        sqlBuilder.append(" case when order_type = 'flt' then 1 ");
        sqlBuilder.append(" when order_type = 'train' then 2 ");
        sqlBuilder.append(" when order_type = 'htl' then 3 ");
        sqlBuilder.append(" else 0 end ");
        sqlBuilder.append(",start_time desc ");
    }

    private void appendProductType(StringBuilder sqlBuilder, String productType) {
        if (StringUtils.equalsIgnoreCase(productType, ProductTypeEnum.DOM.getType())) {
            sqlBuilder.append(" and sub_trip_city_countryid=1 ");
        }
        if (StringUtils.equalsIgnoreCase(productType, ProductTypeEnum.INTER.getType())) {
            sqlBuilder.append(" and sub_trip_city_countryid<>1 ");
        }
    }

    private String buildTravelStepTime(BaseQueryConditionDTO requestDto, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (TravelPositionStepEnum.TRAVEL_ING.equals(requestDto.getTravelStep())) {
            /**
             * 正在出差
             * 1. start_sub_trip_date子行程开始时间 <= 筛选器结束时间& end_sub_trip_date子行程结束时间 > 筛选器结束时间
             * 2. and end_time订单产品的结束时间 >= 筛选器开始时间
             * 3. and start_time订单产品的开始时间 <= 筛选器结束时间
             */
            sqlBuffer.append(" and substring(start_sub_trip_date,1,10)<=? and substring(end_sub_trip_date,1,10)>=? ");
            sqlBuffer.append(" and substring(end_time,1,10)>=? and substring(start_time,1,10) <= ? ");
            paramList.add(requestDto.getEndTime());
            paramList.add(requestDto.getEndTime());
            paramList.add(requestDto.getStartTime());
            paramList.add(requestDto.getEndTime());

        } else if (TravelPositionStepEnum.GOING.equals(requestDto.getTravelStep())) {
            /**
             * 将要去 start_sub_trip_date子行程开始时间>筛选器结束时间
             */
            sqlBuffer.append(" and substring(start_sub_trip_date,1,10)>? ");
            paramList.add(requestDto.getEndTime());
        } else if (TravelPositionStepEnum.LEFTED.equals(requestDto.getTravelStep())) {
            /**
             * 已离开
             *
             * 1. 筛选器开始时间 <= end_sub_trip_date子行程结束时间 <= 筛选器结束时间
             * 2.  end_time订单产品的结束时间 >= 筛选器开始时间
             */
            sqlBuffer.append(" and substring(end_sub_trip_date,1,10)>=? and substring(end_sub_trip_date,1,10)<? ");
            sqlBuffer.append(" and substring(end_time,1,10)>=? ");
            paramList.add(requestDto.getStartTime());
            paramList.add(requestDto.getEndTime());
            paramList.add(requestDto.getStartTime());
        }
        return sqlBuffer.toString();
    }

    /**
     * htl,train flt
     */
    public static String buildBuNameSql(List<QueryReportBuTypeEnum> buTypes, String key, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(OrpConstants.AND).append(key).append(OrpConstants.IN).append(OrpConstants.LEFT_PARENTHESES);
        for (int i = OrpConstants.ZERO; i < buTypes.size(); i++) {
            QueryReportBuTypeEnum buTypeEnum = buTypes.get(i);
            sqlBuffer.append("?");
            if (i != buTypes.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            if (QueryReportBuTypeEnum.hotel.equals(buTypeEnum)) {
                paramList.add(TravelPositionBuTypeEnum.HTL.getBu());
            } else if (QueryReportBuTypeEnum.flight.equals(buTypeEnum)) {
                paramList.add(TravelPositionBuTypeEnum.FLT.getBu());
            } else if (QueryReportBuTypeEnum.train.equals(buTypeEnum)) {
                paramList.add(TravelPositionBuTypeEnum.TRAIN.getBu());
            }
        }
        sqlBuffer.append(OrpConstants.RIGHT_PARENTHESES);
        return sqlBuffer.toString();
    }

    /**
     * htl,train flt
     */
    public static String buildQueryColSql(List<TravelPositionDetail> queryColList, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        for (TravelPositionDetail queryCol : queryColList) {
            /**
             * 预订人 uid or uidname
             */
            if (TravelPositionDetailQueryColnum.BOOKER.equals(queryCol.getQueryKey())) {
                sqlBuffer.append(OrpConstants.AND).append(" (").append("uid = ? or uidname like ? ").append(") ");
                paramList.add(queryCol.getQueryValue());
                paramList.add("%".concat(queryCol.getQueryValue()).concat("%"));
            }
            /**
             * 出行人 passengeruid or user_name
             */
            if (TravelPositionDetailQueryColnum.TRAVELER.equals(queryCol.getQueryKey())) {
                sqlBuffer.append(OrpConstants.AND).append(" (").append("passengeruid = ? or user_name like ? ").append(") ");
                paramList.add(queryCol.getQueryValue());
                paramList.add("%".concat(queryCol.getQueryValue()).concat("%"));
            }
            /**
             * 航班号,火车班次
             */
            if (TravelPositionDetailQueryColnum.FLIGHT_NO.equals(queryCol.getQueryKey()) ||
                    TravelPositionDetailQueryColnum.TRAIN_NO.equals(queryCol.getQueryKey())) {
                sqlBuffer.append(OrpConstants.AND).append(" (").append("flightno_trainno_hotelname = ? ").append(") ");
                paramList.add(queryCol.getQueryValue());
            }
            /**
             * 酒店名称
             */
            if (TravelPositionDetailQueryColnum.HOTEL_NAME.equals(queryCol.getQueryKey())) {
                sqlBuffer.append(OrpConstants.AND).append(" ( ").append("flightno_trainno_hotelname like ? ").append(" ) ");
                paramList.add("%".concat(queryCol.getQueryValue()).concat("%"));
            }
        }
        return sqlBuffer.toString();
    }

    /*
    private PreparedStatement mapCommonRequest(List<Object> paramList, PreparedStatement statement) {
        int index = OrpConstants.ONE;
        try {
            for (Object obj : paramList) {
                statement.setObject(index++, obj);
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }
     */
}
