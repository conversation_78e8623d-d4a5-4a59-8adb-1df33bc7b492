package com.corpgovernment.resource.schedule.onlinereport.deadprice;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2023/12/20 16:31
 * @Desc
 */
@Data
public class HotelDeadPricePointDTO {

    @Column(name = "year_month")
    @Type(value = Types.VARCHAR)
    private String yearMonth;

    @Column(name = "price_rmb")
    @Type(value = Types.DECIMAL)
    private BigDecimal rmbPrice;

    @Column(name = "order_mean_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal orderMeanPrice180d;

    @Column(name = "c_city_mean_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal cityMeanPrice180d;

    @Column(name = "competitive_htl_mean_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal competitiveHtlMeanPrice;

}
