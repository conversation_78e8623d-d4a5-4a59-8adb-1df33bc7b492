package com.corpgovernment.resource.schedule.onlinereport.clickhouse;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.SearchDeptAndCostcneterDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: md_wang
 * @create: 2022-10-19 22:55
 **/
public class BaseConditionSrBuilder {
    /**
     * 构建基本查询条件
     */
    public static String buildPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTime(paramList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, paramList));
        return sqlBuffer.toString();
    }

    public static String buildPreSqlWithCol(BaseQueryConditionDTO baseQueryCondition, List<Object> paramList, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), col));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, paramList));
        return sqlBuffer.toString();
    }

    /**
     * 构建商旅基本查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildCorpPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTime(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        return sqlBuffer.toString();
    }

    public static String buildCorpPreSqlWithCol(BaseQueryConditionDTO baseQueryCondition, List<Object> paramList, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), col));
        return sqlBuffer.toString();
    }

    /**
     * 构建商旅基本查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildCorpPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                         String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTime(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        return sqlBuffer.toString();
    }

    /**
     * 构建行业基本查询条件
     */
    public static String buildIndustryPreSql(BaseQueryConditionDTO baseQueryCondition, List<String> industryList,
                                             List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTime(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        sqlBuffer.append(buildIndustryPreSql(industryList, parmList));
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSqlWithCol(BaseQueryConditionDTO baseQueryCondition, List<String> industryList,
                                                    List<Object> paramList, String queryCol) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlWithTimeWithCol(paramList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), queryCol));
        sqlBuffer.append(buildIndustryPreSql(industryList, paramList));
        return sqlBuffer.toString();
    }

    /**
     * 构建行业基本查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildIndustryPreSql(BaseQueryConditionDTO baseQueryCondition, List<String> industryList,
                                             List<Object> parmList, String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer
                .append(buildPreSqlWithTime(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        sqlBuffer.append(buildIndustryPreSql(industryList, parmList));
        return sqlBuffer.toString();
    }

    /**
     * 构建基本查询条件(不使用baseQueryCondition里面的startTime、endTime)
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList, String startTime,
                                     String endTime, String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTime(parmList, startTime, endTime));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));
        return sqlBuffer.toString();
    }


    /**
     * 构建机票基本查询条件(不使用baseQueryCondition里面的startTime、endTime)
     *
     * @param parmList
     * @return
     */
    public static String buildFlightSaveAmount(List<Object> parmList, String startTime, String endTime) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(startTime)) {
            sqlBuffer.append(" substr(print_ticket_time, 1, 10) >=  ? ");
            parmList.add(startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sqlBuffer.append(" AND substr(print_ticket_time, 1, 10) <= ?");
            parmList.add(endTime);
        }
        return sqlBuffer.toString();
    }


    /**
     * 构建酒店基本查询条件(不使用baseQueryCondition里面的startTime、endTime)
     *
     * @param parmList
     * @return
     */
    public static String buildHotelSaveAmount(List<Object> parmList, String startTime, String endTime) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(startTime)) {
            sqlBuffer.append(" substr(deal_date, 1, 10) >=  ? ");
            parmList.add(startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sqlBuffer.append(" AND substr(deal_date, 1, 10) <= ?");
            parmList.add(endTime);
        }
        return sqlBuffer.toString();
    }


    /**
     * 构建基本查询条件(不使用baseQueryCondition里面的startTime、endTime、col)
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSqlWithTimeWithCol(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                                    String startTime, String endTime, String partition,
                                                    String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(parmList, startTime, endTime, col));
        // sqlBuffer.append(buildPreSqlWithTime(parmList, startTime, endTime));

        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));


        return sqlBuffer.toString();
    }

    /**
     * 构建基本查询条件(使用baseQueryCondition里面的startTime、endTime)
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSql(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                     String partition) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer
                .append(buildPreSqlWithTime(parmList, baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime()));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));
        return sqlBuffer.toString();
    }

    /**
     * 构建基本查询条件(使用baseQueryCondition里面的startTime、endTime、col)
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSqlWithCol(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList,
                                            String partition, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(buildPreSqlPartition(parmList, partition));
        sqlBuffer.append(buildPreSqlWithTimeWithCol(parmList,
                baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), col));
        sqlBuffer.append(buildPreSqlNoTime(baseQueryCondition, parmList));
        return sqlBuffer.toString();
    }

    public static String buildPreSqlPartition(List<Object> parmList, String partition) {
        parmList.add(partition);
        return " d = ? ";
    }

    public static String buildPreSqlNoTime(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(baseQueryCondition.getGroupId())) {
            sqlBuffer.append(" and  companygroupid = ? ");
            parmList.add(baseQueryCondition.getGroupId());
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            sqlBuffer.append(buildCorpAndAccount("corp_corporation", baseQueryCondition.getCorpIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getAccountIds())) {
            sqlBuffer.append(buildCorpAndAccount("account_id", baseQueryCondition.getAccountIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCostCenterList())) {
            sqlBuffer.append(buildDeptAndCostcenter("cost_center", baseQueryCondition.getCostCenterList(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getDeptList())) {
            sqlBuffer.append(buildDeptAndCostcenter("dept", baseQueryCondition.getDeptList(), parmList));
        }
        return sqlBuffer.toString();
    }

    public static String buildPreBusSqlNoTime(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(baseQueryCondition.getGroupId())) {
            sqlBuffer.append(" and  companygroupid = ? ");
            parmList.add(baseQueryCondition.getGroupId());
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            sqlBuffer.append(buildCorpAndAccount("corpid", baseQueryCondition.getCorpIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getAccountIds())) {
            sqlBuffer.append(buildCorpAndAccount("account_id", baseQueryCondition.getAccountIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCostCenterList())) {
            sqlBuffer.append(buildDeptAndCostcenter("cost_center", baseQueryCondition.getCostCenterList(), parmList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getDeptList())) {
            sqlBuffer.append(buildDeptAndCostcenter("dept", baseQueryCondition.getDeptList(), parmList));
        }
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSql(List<String> industryList, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(industryList)) {
            sqlBuffer.append(buildCorpAndAccount("industry_type", industryList, parmList));
        }
        return sqlBuffer.toString();
    }

    public static String buildIndustryPreSqlByOne(String industry, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(industry)) {
            paramList.add(industry.toUpperCase());
            sqlBuffer.append(" and industry_type = ? ");
        }
        return sqlBuffer.toString();
    }

    public static String buildPreSqlWithTime(List<Object> parmList, String startTime, String endTime) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(startTime)) {
            sqlBuffer.append(" and  report_date >= ? ");
            parmList.add(startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sqlBuffer.append(" and  report_date <= ? ");
            parmList.add(endTime);
        }
        return sqlBuffer.toString();
    }

    public static String buildPreSqlWithTimeWithCol(List<Object> paramList, String startTime, String endTime, String col) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(startTime)) {
            sqlBuffer.append(" and ").append(col).append(" >= ? ");
            paramList.add(startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sqlBuffer.append(" and ").append(col).append(" <= ? ");
            paramList.add(endTime);
        }
        return sqlBuffer.toString();
    }

    public static String buildFlightAvgPriceSqlWithTime(List<Object> paramList, String startTime, String endTime, String dateColumn) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(startTime)) {
            sqlBuffer.append(String.format(" and %s >= ? ", dateColumn));
            paramList.add(startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            sqlBuffer.append(String.format(" and %s <= ? ", dateColumn));
            paramList.add(endTime);
        }
        return sqlBuffer.toString();
    }

    /**
     * 构建基本查询条件
     *
     * @param baseQueryCondition
     * @param parmList
     * @return
     */
    public static String buildPreSqlOneTrip(BaseQueryConditionDTO baseQueryCondition, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(baseQueryCondition.getStartTime())) {
            sqlBuffer.append(" and  report_date >= ? ");
            parmList.add(baseQueryCondition.getStartTime());
        }
        if (StringUtils.isNotEmpty(baseQueryCondition.getEndTime())) {
            sqlBuffer.append(" and  report_date <= ? ");
            parmList.add(baseQueryCondition.getEndTime());
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            sqlBuffer.append(buildCorpAndAccount("corp_corporation", baseQueryCondition.getCorpIds(), parmList));
        }
        return sqlBuffer.toString();
    }

    /**
     * 构建公司和成本中心的查询条件
     */
    public static String buildCorpAndAccount(String key, List<String> list, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = 0; i < list.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != list.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            parmList.add(list.get(i).toUpperCase());
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * 构建list查询条件
     */
    public static String buildListConditionNoAnd(String key, List<String> list, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(key + " in (");
        for (int i = 0; i < list.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != list.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            parmList.add(list.get(i).toUpperCase());
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * 构建list查询条件
     */
    public static String buildListConditionWtihAnd(String key, List<String> list, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = 0; i < list.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != list.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            parmList.add(list.get(i).toUpperCase());
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * city in (?,?)
     */
    public static String buildCityIdsSql(List<Integer> cityIds, String key, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = OrpConstants.ZERO; i < cityIds.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != cityIds.size() - OrpConstants.ONE) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            paramList.add(cityIds.get(i));
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * cityName in (?,?)
     */
    public static String buildCityNameSql(List<String> cityNames, String key, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" and " + key + " in (");
        for (int i = 0; i < cityNames.size(); i++) {
            sqlBuffer.append(" ? ");
            if (i != cityNames.size() - 1) {
                sqlBuffer.append(OrpConstants.COMMA);
            }
            paramList.add(cityNames.get(i));
        }
        sqlBuffer.append(")");
        return sqlBuffer.toString();
    }

    /**
     * 排除空值
     */
    public static String excludeEmpty(String key) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isEmpty(key)) {
            return sqlBuffer.toString();
        }
        sqlBuffer.append(" and " + key + " is not null");
        sqlBuffer.append(" and " + key + " <> ''");
        return sqlBuffer.toString();
    }

    /**
     * 构建成本中心和部门的查询条件
     */
    public static String buildDeptAndCostcenter(String key, List<SearchDeptAndCostcneterDTO> list,
                                                List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        for (SearchDeptAndCostcneterDTO searchDeptAndCostcneterDto : list) {
            Integer level = searchDeptAndCostcneterDto.getKey();
            if (Objects.isNull(level)) {
                continue;
            }
            String filedName = key + level;
            if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                sqlBuffer.append(" and " + filedName + " is not null");
                sqlBuffer.append(" and " + filedName + " <> ''");
            }
            List<String> vals = searchDeptAndCostcneterDto.getVals();
            if (CollectionUtils.isEmpty(vals)) {
                continue;
            }
            sqlBuffer.append(" and " + filedName + " in (");
            for (int i = 0; i < vals.size(); i++) {
                sqlBuffer.append(" ? ");
                if (i != vals.size() - 1) {
                    sqlBuffer.append(OrpConstants.COMMA);
                }
                parmList.add(vals.get(i));
            }
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }
}
