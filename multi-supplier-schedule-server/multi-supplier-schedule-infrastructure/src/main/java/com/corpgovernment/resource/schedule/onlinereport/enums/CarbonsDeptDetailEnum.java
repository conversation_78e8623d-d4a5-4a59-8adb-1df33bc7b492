package com.corpgovernment.resource.schedule.onlinereport.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/*
 * <AUTHOR>
 *
 * @date 2021/12/22 15:31
 *
 * @Desc协议航司、协议酒店酒店集团消费详情
 */
public enum CarbonsDeptDetailEnum {

    /***************************************** 机票 *********************************************/
    // 月度碳排放
    MONTH_TOTAL_CARBONS("flight", "Carbon.DetailMonth", 2, false, "0", 1),
    // 月度碳排放占比
    MONTH_CARBONS_PERCENT("flight", "Carbon.DetailMonthPer", 2, true, "0", 3),
    // 月度单程碳排放
    MONTH_AVG_CARBONS("flight", "Carbon.DetailMonthAverage", 2, false, "1",1),
    // 年度碳排放
    YEAR_TOTAL_CARBONS("flight", "Carbon.DetailYear", 2, false, "0", 1),
    // 年度碳排放占比
    YEAR_CARBONS_PERCENT("flight", "Carbon.DetailYearPer", 2, true, "0", 2),
    // 年度单程碳排放
    YEAR_AVG_CARBONS("flight", "Carbon.DetailYearAverage", 2, false, "1",1),
    ;

    private String bizType;

    private String sharkKey;

    private int num;

    private boolean isPercent;

    // 0:总计,1:单程
    private String aggType;

    // 数据类型,1金额，2整数，3百分数
    private int dataType;

    public String getBizType() {
        return bizType;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public int getNum() {
        return num;
    }

    public boolean isPercent() {
        return isPercent;
    }

    public String getAggType() {
        return aggType;
    }

    public int getDataType() {
        return dataType;
    }

    CarbonsDeptDetailEnum(String s, String r, int a, boolean flag, String aggType, int i) {
        this.bizType = s;
        this.sharkKey = r;
        this.num = a;
        this.isPercent = flag;
        this.aggType = aggType;
        this.dataType = i;
    }

    // 不同的aggType要同统计的指标不一样
    public static List<CarbonsDeptDetailEnum> getStaticalsByAggType(int aggType) {
        List<CarbonsDeptDetailEnum> result = new ArrayList<>();
        CarbonsDeptDetailEnum[] agreementDetailEnums = CarbonsDeptDetailEnum.values();
        for (CarbonsDeptDetailEnum agreementDetailEnum : agreementDetailEnums) {
            if (StringUtils.isNotEmpty(agreementDetailEnum.getBizType())
                    && Arrays.stream(agreementDetailEnum.getAggType().split(","))
                    .anyMatch(i -> Integer.valueOf(i) == aggType)) {
                result.add(agreementDetailEnum);
            }
        }
        return result;
    }
}
