package com.corpgovernment.resource.schedule.onlinereport.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.DECIMAL;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.INTEGER;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.PERCENT_F;


/*
 * <AUTHOR>
 *
 * @date 2021/12/24 15:31
 *
 * @Desc部门分析指标
 */
public enum RcStatisticalsEnum {

    /***************************************** 公共 *********************************************/
    // rc次数
    RC_TIMES_OVERVIEW("overview", "RCAnalysis.RCNum1", 0, false, INTEGER),

    // RC订单损失金额
    RC_SAVEAMOUNT_OVERVIEW("overview", "RCAnalysis.OutRCPriceAll", 0, false, DECIMAL),

    // rc次数占比
    RC_PERCENT_OVERVIEW("overview", "RCAnalysis.RCNumPct", 2, true, PERCENT_F),
    // 机票rc次数
    FLT_RC_TIMES_OVERVIEW("overview", "RCAnalysis.FlightRCNum", 0, false, INTEGER),
    // 机票rc次数占比
    FLT_RC_PERCENT_OVERVIEW("overview", "RCAnalysis.FlightRCPercentage", 2, true, PERCENT_F),

    // 机票RC订单损失金额
    FLT_RC_SAVEAMOUNT_OVERVIEW("overview", "RCAnalysis.FltOutRCPrice", 0, false, DECIMAL),

    // 酒店rc次数
    HTL_RC_TIMES_OVERVIEW("overview", "RCAnalysis.HotelRCNum", 0, false, INTEGER),
    // 酒店rc次数占比
    HTL_RC_PERCENT_OVERVIEW("overview", "RCAnalysis.HotelRCPercentage", 2, true, PERCENT_F),

    // 酒店RC订单损失金额
    HTL_RC_SAVEAMOUNT_OVERVIEW("overview", "RCAnalysis.OutRCPrice", 0, false, DECIMAL),


    // 火车rc次数
    TRAIN_RC_TIMES_OVERVIEW("overview", "RCAnalysis.TrainRCNum", 0, false, INTEGER),
    // 火车rc次数占比
    TRAIN_RC_PERCENT_OVERVIEW("overview", "RCAnalysis.TrainRCPercentage", 2, true, PERCENT_F),

    /***************************************** 机票 *********************************************/
    // rc总次数
    FLT_RC_TIMES("flight", "RCAnalysis.RCNum1", 0, false, INTEGER),
    // rc总次数占比
    FLT_RC_PERCENT("flight", "RCAnalysis.RCNumPct", 2, true, PERCENT_F),
    // 低价rc次数
    FLT_LOW_RC_TIMES("flight", "RCAnalysis.LowCostRCNum", 0, false, INTEGER),

    // 低价RC订单损失金额
    FLT_RC_SAVEAMOUNT("flight", "RCAnalysis.LowCostRCAmount", 0, false, DECIMAL),

    // 舱等rc次数
    FLT_CLASS_RC_TIMES("flight", "RCAnalysis.ClassRCNum", 0, false, INTEGER),
    // 机票协议rc次数
    FLT_AGREEMENT_RC_TIMES("flight", "RCAnalysis.CorporateRCNum", 0, false, INTEGER),
    // 未提前预定rc次数
    FLT_PRE_RC_TIMES("flight", "RCAnalysis.AdvancedRCNum", 0, false, INTEGER),
    // 时间rc次数
    FLT_TIME_RC_TIMES("flight", "RCAnalysis.TimeRCNum", 0, false, INTEGER),

    // 折扣rc次数
    FLT_DISCOUNT_RC_TIMES("flight", "ComplianceMonitor.discountrcNum", 0, false, INTEGER),

    /***************************************** 酒店 *********************************************/
    // rc总次数
    HTL_RC_TIMES("hotel", "RCAnalysis.RCNum1", 0, false, INTEGER),
    // rc总次数占比
    HTL_RC_PERCENT("hotel", "RCAnalysis.RCNumPct", 2, true, PERCENT_F),
    // 低价rc次数
    HTL_REASON_RC_TIMES("hotel", "RCAnalysis.LowCostRCNum", 0, false, INTEGER),

    // 低价RC订单损失金额
    HTL_RC_SAVEAMOUNT("hotel", "RCAnalysis.LowCostRCAmount", 0, false, DECIMAL),


    // 最低价rc次数
    HTL_MIN_RC_TIMES("hotel", "RCAnalysis.LowestPriceRC", 0, false, INTEGER),
    // 协议rc次数
    HTL_AGREEMENT_RC_TIMES("hotel", "RCAnalysis.CorporateRCNum", 0, false, INTEGER),

    /***************************************** 火车 *********************************************/
    // rc总次数
    TRAIN_RC_TIMES("train", "RCAnalysis.RCNum1", 0, false, INTEGER),
    // rc总次数占比
    TRAIN_RC_PERCENT("train", "RCAnalysis.RCNumPct", 2, true, PERCENT_F),
    // 坐席rc次数
    TRAIN_SEATTYPE_RC_TIMES("train", "RCAnalysis.SeatRCNum", 0, false, INTEGER),
    // 票张rc次数
    TRAIN_TICKET_RC_TIMES("train", "RCAnalysis.TicketRCNum", 0, false, INTEGER),
    // 退票rc次数
    //TRAIN_REFUND_RC_TIMES("train", "index.refundrccount", 0, false, INTEGER);
    ;

    private String bizType;

    private String sharkKey;

    private int num;

    private boolean isPercent;

    private int dataType;

    public String getBizType() {
        return bizType;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public int getNum() {
        return num;
    }

    public boolean isPercent() {
        return isPercent;
    }

    public int getDataType() {
        return dataType;
    }

    RcStatisticalsEnum(String s, String r, int a, boolean flag, int dataType) {
        this.bizType = s;
        this.sharkKey = r;
        this.num = a;
        this.isPercent = flag;
        this.dataType = dataType;
    }

    public static List<RcStatisticalsEnum> getStaticalsByBizType(String bizType) {
        List<RcStatisticalsEnum> result = new ArrayList<>();
        RcStatisticalsEnum[] deptStatisticalsEnums = RcStatisticalsEnum.values();
        for (RcStatisticalsEnum deptStatisticalsEnum : deptStatisticalsEnums) {
            if (StringUtils.equalsIgnoreCase(deptStatisticalsEnum.getBizType(), bizType)) {
                result.add(deptStatisticalsEnum);
            }
        }
        return result;
    }
}
