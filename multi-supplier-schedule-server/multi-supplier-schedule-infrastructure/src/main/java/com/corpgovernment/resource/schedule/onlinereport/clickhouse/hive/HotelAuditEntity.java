package com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/9/18 11:32
 * @Desc
 */
@Data
public class HotelAuditEntity {
    // 订单号
    private Long orderid;

    // 订单状态
    private String order_status;

    // 公司ID
    private String corp_corporation;

    // 公司名称
    private String corp_name;

    // 订单预定时间
    private String order_date;

    // 省份
    private String province_name;

    // 省份
    private String province_name_en;

    // 城市
    private String city_name;

    // 城市
    private String city_name_en;

    // 酒店集团
    private String hotel_group_name;

    // 酒店集团
    private String hotel_group_name_en;

    // 酒店名称
    private String hotel_name;

    // 酒店名称
    private String hotel_name_en;

    // 产品类型
    private String producttype_all;

    // 支付类型
    private String balancetypename;

    // 预订入住时间，订单号粒度
    private String arrival_date_time;

    // 预订离店时间，订单号粒度
    private String departure_date_time;

    // 预订间夜数，订单号粒度
    private Integer order_quantity;

    // 预订订单金额（包含前收商旅管理服务费），订单号粒度
    private BigDecimal order_amount;

    // 是否已审核，订单号+房间数粒度
    private String is_audit;

    // 房间类型（正常/钟点房），订单号+房间数粒度
    private String room_type;

    // 房间号，订单号+房间数粒度
    private String room_no;

    // 审核状态，订单号+房间数粒度
    private String audit_status;

    // 审核入住人姓名，订单号+房间数粒度
    private String audit_client_name;

    // 审核入住日期，订单号+房间数粒度
    private String audit_checkin_date;

    // 审核离店日期，订单号+房间数粒度
    private String audit_checkout_date;

    // 审核成交间夜，订单号+房间数粒度
    private Integer audit_quantity;

    // 客户修改后的实际入住时间，订单号粒度
    private String realeta;

    // 客户修改后的实际离店时间，订单号粒度
    private String realetd;

    // 夜审退款间夜量
    private Integer audit_refund_quantity;

    // 夜审退款金额
    private BigDecimal auditRefundAmount;

    // 房间类型（正常/钟点房），订单号+房间数粒度
    private Integer room_type_code;
}

