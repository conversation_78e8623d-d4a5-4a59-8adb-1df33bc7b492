package com.corpgovernment.resource.schedule.onlinereport.clickhouse.base;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.enums.StarRocksTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.ThreadPartitionContextUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;

/**
 * <AUTHOR>
 * @className AbstractCommonSrOnlineReportDao
 * @date 2024/5/30
 */
@Slf4j
public class AbstractCommonSrOnlineReportDao extends AbstractStarRocksOnlineReportBaseDao {

    private static final String log_TITLE = "AbstractCommonSrDao";

    private final static String COUNT_ALIAS = "countAll";

    /**
     * @param clazz
     * @param sql
     * @param paramList
     * @param <T>
     * @return
     * @throws Exception
     */
    protected <T> List<T> commonList(Class<T> clazz, String sql, List<Object> paramList, String methodName) throws Exception {
        return querySrBySql(sql, paramList, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return DbResultMapUtils.mapResultList(u, d);
            } catch (Exception e) {
                log.error("AbstractCommonSrOnlineReportDao commonList", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, methodName);
    }

    /**
     * @param sql
     * @param parmList
     * @return
     * @throws Exception
     */
    protected List<Map> commonList(String sql, List<Object> parmList, String methodName) throws Exception {
        return querySrBySql(sql, parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return DbResultMapUtils.mapResultList(u);
                    } catch (Exception e) {
                        log.error(log_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, methodName);
    }

    /**
     * @param sql
     * @param parmList
     * @return
     * @throws Exception
     */
    protected List<String> commonListStr(String sql, List<Object> parmList, String methodName) throws Exception {
        return querySrBySql(sql, parmList,
                (req, statement) -> mapCommonRequest(parmList, statement), (u, d) -> {
                    try {
                        return DbResultMapUtils.mapStrResultList(u, "dim");
                    } catch (Exception e) {
                        log.error(log_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, String.class, methodName);
    }

    /**
     * @param sql
     * @param paramList
     * @return
     * @throws Exception
     */
    protected Integer commonCount(String sql, List<Object> paramList) throws Exception {
        return querySrBySql(sql, paramList, (req, statement) -> mapCommonRequest(paramList, statement),
                (u, d) -> {
                    try {
                        return mapIntResult(u, COUNT_ALIAS);
                    } catch (Exception e) {
                        log.error(log_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return OrpConstants.ZERO;
                }, Integer.class, "commonCount");
    }

    // region [ Mysql - Sum 处理 ]

    /**
     *
     * @param clazz
     * @param sql
     * @param paramList
     * @return
     * @param <T>
     * @throws Exception
     * @desc 如果Sum沒有之，那麽返回的都是Null。 這裏要做Default處理，如果為Null的數值類型，集中進行處理
     */
    protected <T> List<T> commonSumList(Class<T> clazz, String sql, List<Object> paramList, String methodName) throws Exception {
        List<T> resultList = querySrBySql(sql, paramList, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return DbResultMapUtils.mapResultList(u, d);
            } catch (Exception e) {
                log.error("AbstractCommonSrOnlineReportDao commonList", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, methodName);

        // 通過反射，進行處理
        if (!CollectionUtils.isEmpty(resultList) && resultList.size() == 1) {
            processSumResultDefaultZero(resultList.get(0));
        }
        return resultList;
    }

    private <T extends Object> void processSumResultDefaultZero(T result) {
        if (result == null) {
            return;
        }
        Field[] fields = result.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                if (field.getType() == Integer.class && field.get(result) == null) {
                    field.set(result, 0);
                } else if (field.getType() == Double.class && field.get(result) == null) {
                    field.set(result, 0.0);
                } else if (field.getType() == Float.class && field.get(result) == null) {
                    field.set(result, 0.0f);
                } else if (field.getType() == Long.class && field.get(result) == null) {
                    field.set(result, 0L);
                } else if (field.getType() == Short.class && field.get(result) == null) {
                    field.set(result, (short) 0);
                } else if (field.getType() == Byte.class && field.get(result) == null) {
                    field.set(result, (byte) 0);
                } else if (field.getType() == BigDecimal.class && field.get(result) == null) {
                    field.set(result, BigDecimal.ZERO);
                }
            } catch (IllegalAccessException e) {
                log.error("processSumResultDefaultZero", e);
            }
        }
    }
    // endregion

    protected PreparedStatement mapCommonRequest(List<Object> parmList, PreparedStatement statement) {
        if (CollectionUtils.isEmpty(parmList)) {
            return statement;
        }
        int index = OrpConstants.ONE;
        try {
            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }


    /**
     * map ck result to response
     */
    protected <T> List<T> mapResultList(ResultSet resultSet, Class clazz) throws Exception {
        List<T> respList = Lists.newArrayList();
        // 需要做预处理，反射回来的数据-不存在需要跑异常
        Map<String, Field> fieldMap = DbResultMapUtils.getAllFieldMap(clazz);
        if (MapUtils.isEmpty(fieldMap)) {
            return Lists.newArrayList();
        }
        while (resultSet.next()) {
            ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
            int size = DbResultMapUtils.getColumnCount(resultSetMetaData);
            T entity = (T) clazz.newInstance();
            for (int i = OrpConstants.ONE; i <= size; i++) {
                String columnName = OrpReportUtils.lineToHump(resultSetMetaData.getColumnName(i));
                if (fieldMap.containsKey(columnName)) {
                    DbResultMapUtils.setMethodSetResult(entity, fieldMap.get(columnName), resultSet.getObject(i));
                }
            }
            respList.add(entity);
        }
        return respList;
    }

    protected Integer mapIntResult(ResultSet resultSet, String colName) throws Exception {
        List<Integer> respList = Lists.newArrayList();
        while (resultSet.next()) {
            respList.add(resultSet.getInt(colName));
        }
        return CollectionUtils.isEmpty(respList) ? OrpConstants.ZERO : respList.get(OrpConstants.ZERO);
    }

    protected String queryPartition() {
        if (ConfigUtils.getModulePartitionSwitch(ThreadPartitionContextUtils.getModule())) {
            String modulePartition = ThreadPartitionContextUtils.getPartition();
            log.info("getModulePartition>>>>>>>", String.format("%s%s%s", ThreadPartitionContextUtils.getModule(), ">>>>>>>",
                    StringUtils.trimToEmpty(modulePartition)));
            if (StringUtils.isNotEmpty(modulePartition)) {
                return modulePartition;
            }
        }
        return LocalDate.now().minusDays(1L).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    protected String getlogSql(List<Object> paramList, String sqlBuild) {
        try {
            char[] chars = sqlBuild.toCharArray();
            int index = 0;
            StringBuffer stringBuffer = new StringBuffer();
            char b = '?';
            for (int i = 0; i < chars.length; i++) {
                if (Objects.equals(b, chars[i])) {
                    if (paramList.get(index) instanceof String) {
                        stringBuffer.append("'").append(paramList.get(index)).append("'");
                    } else {
                        stringBuffer.append(paramList.get(index));
                    }
                    index++;
                } else {
                    stringBuffer.append(chars[i]);
                }
            }
            return stringBuffer.toString();
        } catch (Exception e) {
            return sqlBuild;
        }
    }

    /**
     * 机票类型查询条件
     *
     * @param flightClass
     * @return
     */
    protected String getFlightClassConditionWithAudited(String flightClass) {
        StringBuilder stringBuilder = new StringBuilder(" and audited <> 'F' ");
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(flightClass, "dom")) {
            stringBuilder.append(" and flight_class = 'N'  ");
        }
        // 海外
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(flightClass, "inter")) {
            stringBuilder.append(" and flight_class = 'I'  ");
        }
        return stringBuilder.toString();
    }

    /**
     * 机票类型查询条件
     *
     * @param flightClass
     * @return
     */
    protected String getFlightClassConditionNoAudited(String flightClass) {
        StringBuilder stringBuilder = new StringBuilder();
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(flightClass, "dom")) {
            stringBuilder.append(" and flight_class = 'N'  ");
        }
        // 海外
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(flightClass, "inter")) {
            stringBuilder.append(" and flight_class = 'I'  ");
        }
        return stringBuilder.toString();
    }

    /**
     * 酒店类型查询条件
     *
     * @param hotelType
     * @return
     */
    protected String getHotelOrderTypeCondition(String hotelType) {
        StringBuilder stringBuilder = new StringBuilder(String.format(" and order_status = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus")));
        // 国内
        if (StringUtils.equalsIgnoreCase(hotelType, "domf")) {
            stringBuilder.append(" and is_oversea in ('F') ");
        }
        // 国内、港澳台
        if (StringUtils.equalsIgnoreCase(hotelType, "dom")) {
            stringBuilder.append(" and is_oversea in ('F','O') ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(hotelType, "inter")) {
            stringBuilder.append(" and is_oversea in ('T') ");
        }
        return stringBuilder.toString();
    }

    /**
     * 火车状态限制
     * @return
     */
    protected String getTrainOrderStatusCondition() {
        return " and order_status in ('TA', 'RP', 'EP', 'EA', 'RA')";
    }

    /**
     * 火车类型查询条件
     *
     * @param trainType
     * @return
     */
    protected String getTrainTypeCondition(String trainType) {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.equalsIgnoreCase(trainType, "dom")) {
            stringBuilder.append(" and traintype = 'N' ");
        }
        if (StringUtils.equalsIgnoreCase(trainType, "inter")) {
            stringBuilder.append(" and traintype = 'I' ");
        }
        return stringBuilder.toString();
    }

    /**
     * 酒店协议会员
     * @param orderType
     * @return
     */
    protected String getHtlTypeCondition(String orderType) {
        // 会员
        if (StringUtils.equalsIgnoreCase(orderType, "M")) {
            return " and order_type in 'M' ";
        }
        // 协议
        if (StringUtils.equalsIgnoreCase(orderType, "C")) {
            return " and order_type in 'C' ";
        }
        return StringUtils.EMPTY;
    }

    /**
     * 构建酒店协议/非协议条件（C：协议；NC：非协议）
     *
     * @param contractType
     * @return
     */
    protected String getHtlContractTypeCondition(String contractType) {
        if (StringUtils.isEmpty(contractType)) {
            return StringUtils.EMPTY;
        }
        StringBuilder stringBuilder = new StringBuilder();
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        // 三方协议
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(contractType, "C")) {
            stringBuilder.append(" and producttype_all = '" + ta + "'  ");
        }
        // 非三方协议
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(contractType, "NC")) {
            stringBuilder.append(" and producttype_all != '" + ta + "'  ");
        }
        return stringBuilder.toString();
    }

    /**
     * 构建机票协议/非协议条件（C：协议；NC：非协议）
     *
     * @param contractType
     * @return
     */
    protected String getFlightContractTypeCondition(String contractType) {
        if (StringUtils.isEmpty(contractType)) {
            return StringUtils.EMPTY;
        }
        StringBuilder stringBuilder = new StringBuilder();
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String taListStr = Lists.newArrayList(ta, "B2G").stream().map(item -> String.format("'%s'", item))
                .collect(Collectors.joining(", ", "(", ")"));

        if (StringUtils.equalsIgnoreCase(contractType, "C")) {
            stringBuilder.append(" and agreement_type_name in " + taListStr);
        }
        if (StringUtils.equalsIgnoreCase(contractType, "NC")) {
            stringBuilder.append(" and agreement_type_name not in " + taListStr);
        }
        return stringBuilder.toString();
    }

    /**
     * 酒店类型查询条件
     *
     * @param hotelType
     * @return
     */
    protected String getHotelOrderTypeConditionNoOrderStatus(String hotelType) {
        StringBuilder stringBuilder = new StringBuilder();
        // 国内
        if (StringUtils.equalsIgnoreCase(hotelType, "domf")) {
            stringBuilder.append(" and is_oversea in ('F') ");
        }
        // 国内、港澳台
        if (StringUtils.equalsIgnoreCase(hotelType, "dom")) {
            stringBuilder.append(" and is_oversea in ('F','O') ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(hotelType, "inter")) {
            stringBuilder.append(" and is_oversea in ('T') ");
        }
        return stringBuilder.toString();
    }

    public String getCarOrderTypeCondition(String orderType) {
        // airportpick接送机,airportpickDom接送机,airportpickInter接送机,Charter包车,rent租车,tax打车,taxDom国内打车,taxInter国际打车,rentInter国际租车
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "airportpick")) {
            return " and order_type IN (1,2) ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "airportpickDom")) {
            return " and order_type = 1 ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "airportpickInter")) {
            return " and order_type = 2 ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "charter")) {
            return " and order_type = 3 ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "rent")) {
            return " and order_type = 4 ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "tax")) {
            return " and order_type = 6 ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "taxDom")) {
            return " and order_type = 6 and sub_product_line = '1' ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "taxInter")) {
            return " and order_type = 6 and sub_product_line = 'CAR_TAXI_INTL'";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "rentInter")) {
            return " and order_type = 18 ";
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }

    /**
     * 用车类型查询条件
     *
     * @param productType
     * @return
     */
    protected String getCarProductTypeCondition(String productType) {
        StringBuilder stringBuilder = new StringBuilder();
        // 国内
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            stringBuilder.append(" and (order_type in(1,3,4) or (order_type = 6 and sub_product_line = '1')) ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            stringBuilder.append(" and (order_type in(2,18) or (order_type = 6 and sub_product_line = 'CAR_TAXI_INTL')) ");
        }
        return stringBuilder.toString();
    }


    /**
     * @param paramList
     * @param pager
     * @return
     */
    protected String buildPage(List<Object> paramList, Pager pager) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" limit ?, ? ");
        paramList.add((pager.pageIndex - 1) * pager.pageSize);
        paramList.add(pager.pageSize);
        return stringBuilder.toString();
    }

    /**
     * 构建in条件
     *
     * @param column
     * @param values
     * @param parmList
     * @return
     */
    protected String buildInCondition(String column, List<?> values, List<Object> parmList) {
        if (CollectionUtils.isEmpty(values)) {
            return StringUtils.EMPTY;
        }
        parmList.addAll(values);
        return String.format(" and %s in %s ", column, values.stream().map(item -> "?")
                .collect(Collectors.joining(", ", "(", ")")));
    }

    /**
     * 根据统计口径获得查询的的时间字段
     * @param statisticalCaliber
     * @return
     */
    public String getDateFieldByCaliber(String statisticalCaliber) {
        return StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name()) ? ORDERDT : REPORT_DATE;
    }

    @Data
    public static class TableAndTimeColBind {

        private StarRocksTable starRocksTable;

        private String dateColumn;

    }

}
