package com.corpgovernment.resource.schedule.onlinereport.module.repotlib;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-10-13 11:26
 * @desc
 */

@Data
public class TrainOrderDTO {

    // 订单号
    @Column(name = "order_id")
    @Type(value = Types.BIGINT)
    private Long orderId;

    // 订单状态
    @Column(name = "order_status")
    @Type(value = Types.VARCHAR)
    private String orderStatus;

    // 订单预定时间
    @Column(name = "order_date")
    @Type(value = Types.VARCHAR)
    private String orderDate;

    // 公司ID
    @Column(name = "corp_corporation")
    @Type(value = Types.VARCHAR)
    private String corpCorporation;

    // 公司名称
    @Column(name = "corp_name")
    @Type(value = Types.VARCHAR)
    private String corpName;

    // 公司集团ID
    @Column(name = "companygroupid")
    @Type(value = Types.VARCHAR)
    private String companygroupid;

    // 公司集团
    @Column(name = "companygroup")
    @Type(value = Types.VARCHAR)
    private String companygroup;

    // 主账户账号
    @Column(name = "account_id")
    @Type(value = Types.BIGINT)
    private Long accountId;

    // 主账户代号
    @Column(name = "account_code")
    @Type(value = Types.VARCHAR)
    private String accountCode;

    // 主账户公司名称
    @Column(name = "account_name")
    @Type(value = Types.VARCHAR)
    private String accountName;

    // 子账户账号
    @Column(name = "sub_account_id")
    @Type(value = Types.BIGINT)
    private Long subAccountId;

    // 子账户代号
    @Column(name = "sub_account_code")
    @Type(value = Types.VARCHAR)
    private String subAccountCode;

    // 子账户公司名称
    @Column(name = "sub_account_name")
    @Type(value = Types.VARCHAR)
    private String subAccountName;

    // 行业类型
    @Column(name = "industry_type")
    @Type(value = Types.VARCHAR)
    private String industryType;

    // 行业名称
    @Column(name = "industry_type_name")
    @Type(value = Types.VARCHAR)
    private String industryTypeName;

    // 持卡人卡号
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    // 持卡人姓名
    @Column(name = "user_name")
    @Type(value = Types.VARCHAR)
    private String userName;

    // 持卡人员工编号
    @Column(name = "employe_id")
    @Type(value = Types.VARCHAR)
    private String employeId;

    // 工作所在城市
    @Column(name = "work_city")
    @Type(value = Types.VARCHAR)
    private String workCity;

    // 持卡人职级CN
    @Column(name = "rank_name")
    @Type(value = Types.VARCHAR)
    private String rankName;

    // 成本中心1
    @Column(name = "cost_center1")
    @Type(value = Types.VARCHAR)
    private String costCenter1;

    // 成本中心2
    @Column(name = "cost_center2")
    @Type(value = Types.VARCHAR)
    private String costCenter2;

    // 成本中心3
    @Column(name = "cost_center3")
    @Type(value = Types.VARCHAR)
    private String costCenter3;

    // 成本中心4
    @Column(name = "cost_center4")
    @Type(value = Types.VARCHAR)
    private String costCenter4;

    // 成本中心5
    @Column(name = "cost_center5")
    @Type(value = Types.VARCHAR)
    private String costCenter5;

    // 成本中心6
    @Column(name = "cost_center6")
    @Type(value = Types.VARCHAR)
    private String costCenter6;

    // 部门1
    @Column(name = "dept1")
    @Type(value = Types.VARCHAR)
    private String dept1;

    // 部门2
    @Column(name = "dept2")
    @Type(value = Types.VARCHAR)
    private String dept2;

    // 部门3
    @Column(name = "dept3")
    @Type(value = Types.VARCHAR)
    private String dept3;

    // 部门4
    @Column(name = "dept4")
    @Type(value = Types.VARCHAR)
    private String dept4;

    // 部门5
    @Column(name = "dept5")
    @Type(value = Types.VARCHAR)
    private String dept5;
    // 部门6
    @Column(name = "dept6")
    @Type(value = Types.VARCHAR)
    private String dept6;
    // 部门7
    @Column(name = "dept7")
    @Type(value = Types.VARCHAR)
    private String dept7;
    // 部门8
    @Column(name = "dept8")
    @Type(value = Types.VARCHAR)
    private String dept8;
    // 部门9
    @Column(name = "dept9")
    @Type(value = Types.VARCHAR)
    private String dept9;
    // 部门10
    @Column(name = "dept10")
    @Type(value = Types.VARCHAR)
    private String dept10;
    // 是否个人消费行为:因公,因私,
    @Column(name = "fee_type")
    @Type(value = Types.VARCHAR)
    private String feeType;
    // 预订方式
    @Column(name = "is_online")
    @Type(value = Types.VARCHAR)
    private String isOnline;
    // 支付方式
    @Column(name = "prepay_type")
    @Type(value = Types.VARCHAR)
    private String prepayType;
    // 结算类型
    @Column(name = "acb_prepay_type")
    @Type(value = Types.VARCHAR)
    private String acbPrepayType;
    // 是否BOSS(T/F)
    @Column(name = "bosstype")
    @Type(value = Types.VARCHAR)
    private String bosstype;
    // 所属行程订单号
    @Column(name = "trip_id")
    @Type(value = Types.VARCHAR)
    private String tripId;
    // 关联行程单号
    @Column(name = "journey_no")
    @Type(value = Types.VARCHAR)
    private String journeyNo;
    // 出行目的
    @Column(name = "journey_reason")
    @Type(value = Types.VARCHAR)
    private String journeyReason;
    // 出行目的编号
    @Column(name = "journey_reason_code")
    @Type(value = Types.VARCHAR)
    private String journeyReasonCode;
    // 项目编号
    @Column(name = "project_code")
    @Type(value = Types.VARCHAR)
    private String projectCode;
    // 项目名称
    @Column(name = "project")
    @Type(value = Types.VARCHAR)
    private String project;
    // 是否口头授权T/F
    @Column(name = "verbal_authorize")
    @Type(value = Types.VARCHAR)
    private String verbalAuthorize;
    // 一次授权人姓名
    @Column(name = "confirm_person")
    @Type(value = Types.VARCHAR)
    private String confirmPerson;
    // 一次授权方式
    @Column(name = "confirm_type")
    @Type(value = Types.VARCHAR)
    private String confirmType;
    // 二次授权人姓名
    @Column(name = "confirm_person2")
    @Type(value = Types.VARCHAR)
    private String confirmPerson2;
    // 二次授权方式
    @Column(name = "confirm_type2")
    @Type(value = Types.VARCHAR)
    private String confirmType2;
    // 授权通过时间
    @Column(name = "approvalpasstime")
    @Type(value = Types.VARCHAR)
    private String approvalpasstime;
    // 授权结果
    @Column(name = "actionname")
    @Type(value = Types.VARCHAR)
    private String actionname;
    // 自定义成本中心
    @Column(name = "defineflag")
    @Type(value = Types.VARCHAR)
    private String defineflag;
    // 自定义成本中心2
    @Column(name = "defineflag2")
    @Type(value = Types.VARCHAR)
    private String defineflag2;
    // 一次授权时间
    @Column(name = "confirmtimepoint")
    @Type(value = Types.VARCHAR)
    private String confirmtimepoint;
    // 二次授权时间
    @Column(name = "confirmtimepoint2")
    @Type(value = Types.VARCHAR)
    private String confirmtimepoint2;
    // 一次授权人uid
    @Column(name = "AuditorID")
    @Type(value = Types.VARCHAR)
    private String auditorid;
    // 二次授权人uid
    @Column(name = "AuditorID2")
    @Type(value = Types.VARCHAR)
    private String auditorid2;
    // 年月
    @Column(name = "group_month")
    @Type(value = Types.INTEGER)
    private Integer groupMonth;
    // 出票、退票审核月份
    @Column(name = "print_month")
    @Type(value = Types.INTEGER)
    private Integer printMonth;
    // 出票日期-年
    @Column(name = "print_year")
    @Type(value = Types.INTEGER)
    private Integer printYear;
    // 出票时间
    @Column(name = "print_time")
    @Type(value = Types.VARCHAR)
    private String printTime;
    // 乘客id
    @Column(name = "passenger_id")
    @Type(value = Types.VARCHAR)
    private String passengerId;
    // 乘客姓名
    @Column(name = "passenger_name")
    @Type(value = Types.VARCHAR)
    private String passengerName;
    // 乘客姓名-拼音
    @Column(name = "passenger_name_py")
    @Type(value = Types.VARCHAR)
    private String passengerNamePy;
    // 乘客人数
    @Column(name = "persons")
    @Type(value = Types.INTEGER)
    private Integer persons;
    // P(纸质票)；E(电子票)
    @Column(name = "print_ticket_type")
    @Type(value = Types.VARCHAR)
    private String printTicketType;
    // 火车票张数
    @Column(name = "quantity")
    @Type(value = Types.INTEGER)
    private Integer quantity;
    // 火车票改签张数
    @Column(name = "change_quantity")
    @Type(value = Types.INTEGER)
    private Integer changeQuantity;
    // 改签状态
    @Column(name = "change_status")
    @Type(value = Types.VARCHAR)
    private String changeStatus;
    // 退票状态
    @Column(name = "refund_status")
    @Type(value = Types.VARCHAR)
    private String refundStatus;
    // 车票类型（D原车次车票；C改签车次车票）
    @Column(name = "ticket_type")
    @Type(value = Types.VARCHAR)
    private String ticketType;
    // 车次
    @Column(name = "train_name")
    @Type(value = Types.VARCHAR)
    private String trainName;
    // 席别
    @Column(name = "first_seat_type_name")
    @Type(value = Types.VARCHAR)
    private String firstSeatTypeName;
    // 坐席代号
    @Column(name = "seat_type")
    @Type(value = Types.VARCHAR)
    private String seatType;
    // 原始出票金额/出票总金额/订单总金额
    @Column(name = "real_pay")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPay;
    // 票价
    @Column(name = "ticket_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal ticketPrice;
    // 保险费
    @Column(name = "insurance_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal insuranceFee;
    // 基础服务费
    @Column(name = "service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal serviceFee;
    // 退票金额
    @Column(name = "refund_ticket_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal refundTicketFee;
    // 配送费
    @Column(name = "deliver_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal deliverFee;
    // 纸质出票费
    @Column(name = "paper_ticket_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal paperTicketFee;
    // 改签服务费
    @Column(name = "deal_change_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal dealChangeServiceFee;
    // 抢票服务费
    @Column(name = "grab_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal grabServiceFee;
    // 后收服务费
    @Column(name = "after_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal afterServiceFee;
    // 后收改签服务费
    @Column(name = "afterchangeservicefee")
    @Type(value = Types.DECIMAL)
    private BigDecimal afterchangeservicefee;
    // 后取票服务费
    @Column(name = "aftertaketicketfee")
    @Type(value = Types.DECIMAL)
    private BigDecimal aftertaketicketfee;
    // 后收后取票服务费
    @Column(name = "afteraftertaketicketfee")
    @Type(value = Types.DECIMAL)
    private BigDecimal afteraftertaketicketfee;
    // 改签差价
    @Column(name = "changebalance")
    @Type(value = Types.DECIMAL)
    private BigDecimal changebalance;
    // 出发日期
    @Column(name = "departure_date_time")
    @Type(value = Types.VARCHAR)
    private String departureDateTime;
    // 出发站(中)
    @Column(name = "departure_station_name")
    @Type(value = Types.VARCHAR)
    private String departureStationName;
    // 出发城市ID
    @Column(name = "departure_city_id")
    @Type(value = Types.VARCHAR)
    private String departureCityId;
    // 出发城市
    @Column(name = "departure_city_name")
    @Type(value = Types.VARCHAR)
    private String departureCityName;
    // 出发省份
    @Column(name = "departure_province_name")
    @Type(value = Types.VARCHAR)
    private String departureProvinceName;
    // 到达日期
    @Column(name = "arrival_date_time")
    @Type(value = Types.VARCHAR)
    private String arrivalDateTime;
    // 到达站(中)
    @Column(name = "arrival_station_name")
    @Type(value = Types.VARCHAR)
    private String arrivalStationName;
    // 到达城市ID
    @Column(name = "arrival_city_id")
    @Type(value = Types.VARCHAR)
    private String arrivalCityId;
    // 到达城市
    @Column(name = "arrival_city_name")
    @Type(value = Types.VARCHAR)
    private String arrivalCityName;
    // 到达省份
    @Column(name = "arrival_province_name")
    @Type(value = Types.VARCHAR)
    private String arrivalProvinceName;
    // 出发城市名称-到达城市名称
    @Column(name = "line_city")
    @Type(value = Types.VARCHAR)
    private String lineCity;
    // 是否有RC，T(是)/F(否)
    @Column(name = "is_rc")
    @Type(value = Types.VARCHAR)
    private String isRc;
    // 坐席RC
    @Column(name = "seattype_rccodeid")
    @Type(value = Types.VARCHAR)
    private String seattypeRccodeid;
    // 坐席RC说明
    @Column(name = "seattype_rccodename")
    @Type(value = Types.VARCHAR)
    private String seattypeRccodename;
    // 票张RC
    @Column(name = "ticket_rccodeid")
    @Type(value = Types.VARCHAR)
    private String ticketRccodeid;
    // 票张RC说明
    @Column(name = "ticket_rccodename")
    @Type(value = Types.VARCHAR)
    private String ticketRccodename;
    // 用户自定义RCcode,
    @Column(name = "userdefined_rid")
    @Type(value = Types.VARCHAR)
    private String userdefinedRid;
    // 用户自定义RC说明,
    @Column(name = "userdefined_rc")
    @Type(value = Types.VARCHAR)
    private String userdefinedRc;
    // 原币种(客户支付币种)
    @Column(name = "o_currency")
    @Type(value = Types.VARCHAR)
    private String oCurrency;
    // 汇率(客户支付币种汇率)
    @Column(name = "o_exchangerate")
    @Type(value = Types.DECIMAL)
    private BigDecimal oExchangerate;
    // 席别英文
    @Column(name = "first_seat_type_name_en")
    @Type(value = Types.VARCHAR)
    private String firstSeatTypeNameEn;
    // 出发站英文
    @Column(name = "departure_station_name_en")
    @Type(value = Types.VARCHAR)
    private String departureStationNameEn;
    // 出发城市英文
    @Column(name = "departure_city_name_en")
    @Type(value = Types.VARCHAR)
    private String departureCityNameEn;
    // 出发省份英文
    @Column(name = "departure_province_name_en")
    @Type(value = Types.VARCHAR)
    private String departureProvinceNameEn;
    // 到达站英文
    @Column(name = "arrival_station_name_en")
    @Type(value = Types.VARCHAR)
    private String arrivalStationNameEn;
    // 到城市英文
    @Column(name = "arrival_city_name_en")
    @Type(value = Types.VARCHAR)
    private String arrivalCityNameEn;
    // 到达省份英文
    @Column(name = "arrival_province_name_en")
    @Type(value = Types.VARCHAR)
    private String arrivalProvinceNameEn;
    // 出发城市名称-到达城市名称
    @Column(name = "line_city_en")
    @Type(value = Types.VARCHAR)
    private String lineCityEn;

    // 退票rc code
    @Column(name = "refund_rc")
    @Type(value = Types.VARCHAR)
    private String refundRc;
    // 退票rc code说明
    @Column(name = "refund_rc_desc")
    @Type(value = Types.VARCHAR)
    private String refundRcDesc;
    // 预估手续费12306
    @Column(name = "est_fee_12306")
    @Type(value = Types.DECIMAL)
    private BigDecimal estFee12306;

    // 后取票状态
    @Column(name = "taketicketstatus")
    @Type(value = Types.VARCHAR)
    private String taketicketstatus;

    // 退票张数
    @Column(name = "refund_quantity")
    @Type(value = Types.VARCHAR)
    private String refundQuantity;

    // 碳排放
    @Column(name = "carbon_emission")
    @Type(value = Types.INTEGER)
    private Integer carbonEmission;

    // 碳排量中位数
    @Column(name = "median_carbon_emission")
    @Type(value = Types.INTEGER)
    private Integer medianCarbons ;

    // 行业大类
    @Column(name = "std_industry1")
    @Type(value = Types.VARCHAR)
    private String stdIndustry1;

    // 行业小类
    @Column(name = "std_industry2")
    @Type(value = Types.VARCHAR)
    private String stdIndustry2;

    // 出行人员工编号
    @Column(name = "passenger_employeeid")
    @Type(value = Types.VARCHAR)
    private String passengerEid;
}
