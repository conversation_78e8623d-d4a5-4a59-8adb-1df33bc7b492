package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.travelmark;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
public interface OnlineReportTravelMarkTrendDaoService {
    /**
     * 差旅评分指标趋势
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> travelMarkMetricTrend(BaseQueryConditionDTO requestDto, List<String> industryList, Map extMap,
                                      Class<T> clazz, QueryReportAggDateDimensionEnum dateDimensionEnum) throws Exception;

    /**
     * 差旅评分指标趋势
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> travelMarkMetric(BaseQueryConditionDTO requestDto, List<String> industryList, Map extMap, Class<T> clazz) throws Exception;
}
