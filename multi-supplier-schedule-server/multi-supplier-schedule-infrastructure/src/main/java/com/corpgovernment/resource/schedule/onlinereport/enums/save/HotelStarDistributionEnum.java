package com.corpgovernment.resource.schedule.onlinereport.enums.save;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.save
 * @description:机酒节省分析-酒店星级分布
 * @author: md_wang
 * @create: 2022-08-02 20:00
 **/
public enum HotelStarDistributionEnum {
    /**
     * 酒店星级分布
     */
    TITLE("title", "Save.HtlStar"),

    /**
     * 五星级酒店间夜数占比高于{{compare}}平均水平，请关注。
     */
    NOTICE("notice", "Save.HltAys1"),
    /**
     * 一星  APP.Behavior.Star1
     * 二星  APP.Behavior.Star2
     * 三星  APP.Behavior.Star3
     * 四星 APP.Behavior.Star4
     * 五星 APP.Behavior.Star5
     * 无星 APP.Behavior.StarNone
     */
    STAR_ONE("star_one", "APP.Behavior.Star1"),
    STAR_TWO("star_two", "APP.Behavior.Star2"),
    STAR_THREE("star_three", "APP.Behavior.Star3"),
    STAR_FOUR("star_four", "APP.Behavior.Star4"),
    STAR_FIVE("star_five", "APP.Behavior.Star5"),
    NONE("none", "APP.Behavior.StarNone"),
    /**
     * 公司
     */
    COMPANY("company", "Save.Company"),
    /**
     * 商旅
     */
    CORP("corp", "Index.tmc"),
    /**
     * 行业
     */
    INDUSTRY("industry", "Index.industry"),
    ;

    private String name;
    private String sharkKey;

    HotelStarDistributionEnum(String name, String sharkKey) {
        this.name = name;
        this.sharkKey = sharkKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public void setSharkKey(String sharkKey) {
        this.sharkKey = sharkKey;
    }
}
