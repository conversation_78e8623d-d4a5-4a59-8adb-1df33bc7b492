package com.corpgovernment.resource.schedule.screen.convert;


import com.corpgovernment.resource.schedule.domain.screen.model.ConsumeRankBO;
import com.corpgovernment.resource.schedule.domain.screen.model.HotCityBO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapBO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDestinationInfoBO;
import com.corpgovernment.resource.schedule.domain.screen.model.TotalCountBO;
import com.corpgovernment.resource.schedule.domain.screen.model.TotalOrderBO;
import com.corpgovernment.resource.schedule.domain.screen.model.TripRankBO;
import com.corpgovernment.resource.schedule.screen.po.ConsumeRankDO;
import com.corpgovernment.resource.schedule.screen.po.HotCityDO;
import com.corpgovernment.resource.schedule.screen.po.MapDO;
import com.corpgovernment.resource.schedule.screen.po.MapDestinationInfoDO;
import com.corpgovernment.resource.schedule.screen.po.TotalAmountDO;
import com.corpgovernment.resource.schedule.screen.po.TotalOrderDO;
import com.corpgovernment.resource.schedule.screen.po.TripRankDO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 */
@Mapper(componentModel = "spring")
public interface ScreenConvertInfrastructure {


    TotalCountBO toTotalAmountBO(TotalAmountDO totalAmountDO);

    TotalOrderBO toTotalOrderBO(TotalOrderDO totalOrderDO);

    List<ConsumeRankBO> toConsumeRanksBO(List<ConsumeRankDO> consumeRanksDO);

    List<HotCityBO> toHotCitiesBO(List<HotCityDO> hotCitiesDO);

    List<TripRankBO> toTripRanksBO(List<TripRankDO> tripRanksDO);

    List<MapBO> toMapBO(List<MapDO> mapsDO);

    List<MapDestinationInfoBO> toMapDestinationInfoBO(List<MapDestinationInfoDO> mapDestinationInfosDO);
}


