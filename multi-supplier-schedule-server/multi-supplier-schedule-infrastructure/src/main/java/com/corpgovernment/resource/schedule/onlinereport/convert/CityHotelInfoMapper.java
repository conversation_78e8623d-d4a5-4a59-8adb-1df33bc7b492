package com.corpgovernment.resource.schedule.onlinereport.convert;


import com.corpgovernment.resource.schedule.domain.onlinereport.entity.CityHotelInfo;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.cityhotel.CityHotelDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2021/11/8 16:26
 * @Desc
 */

@Mapper(componentModel = "spring")
public interface CityHotelInfoMapper {

    CityHotelInfoMapper INSTANCE = Mappers.getMapper(CityHotelInfoMapper.class);

    @Mappings({
            @Mapping(target = "sumRealPay", expression = "java(format2(dto.getSumRealPay()))"),
            @Mapping(target = "sumQuantity", expression = "java(format1(dto.getSumQuantity()))"),
            @Mapping(target = "avgPrice", expression = "java(format2(dto.getAvgPrice()))"),
            @Mapping(target = "sumRealPayC", expression = "java(format2(dto.getSumRealPayC()))"),
            @Mapping(target = "sumQuantityC", expression = "java(format1(dto.getSumQuantityC()))"),
            @Mapping(target = "avgPriceC", expression = "java(format2(dto.getAvgPriceC()))"),
            @Mapping(target = "avgPriceCCorp", expression = "java(format2(dto.getAvgPriceCCorp()))"),
            @Mapping(target = "avgPriceCIndustry", expression = "java(format2(dto.getAvgPriceCIndustry()))"),
            @Mapping(target = "sumRealPayM", expression = "java(format2(dto.getSumRealPayM()))"),
            @Mapping(target = "sumQuantityM", expression = "java(format1(dto.getSumQuantityM()))"),
            @Mapping(target = "avgPriceM", expression = "java(format2(dto.getAvgPriceM()))"),
            @Mapping(target = "avgPriceMCorp", expression = "java(format2(dto.getAvgPriceMCorp()))"),
            @Mapping(target = "avgPriceMIndustry", expression = "java(format2(dto.getAvgPriceMIndustry()))")
    })
    CityHotelInfo toDTO(CityHotelDTO dto);

    List<CityHotelInfo> toDTOs(List<CityHotelDTO> list);

    @Named("format1")
    default Integer format1(Integer num) {
        return Optional.ofNullable(num).orElse(0);
    }

    @Named("format2")
    default BigDecimal format2(BigDecimal num) {
        return OrpReportUtils.formatBigDecimal(Optional.ofNullable(num).orElse(BigDecimal.ZERO));
    }
}
