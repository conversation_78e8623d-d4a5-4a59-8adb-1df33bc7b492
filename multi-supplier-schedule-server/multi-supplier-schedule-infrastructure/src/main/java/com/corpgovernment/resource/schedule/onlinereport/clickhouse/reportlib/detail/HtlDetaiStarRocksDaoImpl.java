package com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.dto.TimeFilterTypeInfoDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DbTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.ThreadPartitionContextUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-09-21 13:39
 * @desc
 */
@Service
@Repository
public class HtlDetaiStarRocksDaoImpl extends AbstractDetaiDao {

    @Override
    protected ClickHouseTable getClickHouseTable(boolean isBluespace) {
        if (isBluespace) {
            return ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD_FOREIGN;
        }
        return ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
    }

    @Override
    protected String buildPreSqlTime(List<TimeFilterTypeInfoDTO> timeFilterTypeInfoList, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isEmpty(timeFilterTypeInfoList)) {
            return sqlBuffer.toString();
        }
        for (TimeFilterTypeInfoDTO timeFilterTypeInfo : timeFilterTypeInfoList) {
            // 预订时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate") || StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
                sqlBuffer.append(buildTimeFilter(timeFilterTypeInfo, parmList));
            }
            // 入住日期
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "usedate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    sqlBuffer.append(" and ").append("subString(arrival_date_time, 1, 10)").append(" >= ? ");
                    parmList.add(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    sqlBuffer.append(" and ").append("subString(arrival_date_time, 1, 10)").append(" <= ? ");
                    parmList.add(timeFilterTypeInfo.getEndTime());
                }
            }
        }
        return sqlBuffer.toString();
    }

    // hotel client_name（拼接）
    @Override
    protected String buildPreSqlPassenger(List<String> passengers, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(passengers)) {
            if (StringUtils.equalsIgnoreCase(DbTypeEnum.STARROCKS.name(), ThreadPartitionContextUtils.getDbType())) {
                if (passengers.stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                        .anyMatch(i -> StringUtils.isNotEmpty(i))) {
                    sqlBuffer.append(" AND array_length(array_intersect(split(regexp_replace(upper(IFNULL(client_name, '')), ' ', ''), ','), split(%s,','))) > 0 ");
                    String arr = passengers.stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                            .filter(i -> StringUtils.isNotEmpty(i)).map(DbResultMapUtils::sm4Encrypt).collect(Collectors.joining(","));
                    if (StringUtils.isNotEmpty(arr)) {
                        return String.format(sqlBuffer.toString(), arr);
                    }
                }
            } else {
                String arrayCondition = " and client_name in (%s)";
                for (int i = 0; i < passengers.size(); i++) {
                    sqlBuffer.append(" ? ");
                    if (i != passengers.size() - 1) {
                        sqlBuffer.append(OrpConstants.COMMA);
                    }
                    parmList.add(DbResultMapUtils.sm4Encrypt(StringUtils.trimToEmpty(passengers.get(i))));
                }
                return String.format(arrayCondition, sqlBuffer.toString());
            }
        }
        return sqlBuffer.toString();
    }

    @Override
    protected String buildProductTypeCondition(String productType) {
        return getHotelOrderTypeConditionNoOrderStatus(productType);
    }

    @Override
    protected String buildOrderBySql(String pos, String blueSpace) {
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            return " order by order_id, client_name, is_refund desc";
        } else {
            return StringUtils.EMPTY;
        }
    }

    @Override
    protected String buildPreSqlExceedStandard(List<String> exceedStandard, List<Object> parmList) {
        if (CollectionUtils.isEmpty(exceedStandard) || exceedStandard.size() == 2) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.equalsIgnoreCase(exceedStandard.get(0), "T")) {
            return " AND is_rc = 'T'";
        }
        if (StringUtils.equalsIgnoreCase(exceedStandard.get(0), "F")) {
            return " AND is_rc = 'F'";
        }
        return StringUtils.EMPTY;
    }

}
