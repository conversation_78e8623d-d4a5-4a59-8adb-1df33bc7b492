package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTimesDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @className OnlineReportOverViewRcAnalysisDaoService
 * @date 2024/9/29
 */
public interface OnlineReportOverViewRcAnalysisDaoService extends OnlineReportTopRcAnalysisDaoService {

    List<RcTimesDTO> aggreationRcView(BaseQueryConditionDTO requestDto, String orderType)
            throws Exception;

    List<RcTimesDTO> aggreationRcViewCorpAndIndustry(String startTime, String endTime, String orderType,
                                                     List<String> industryList, DataTypeEnum dataTypeEnum,
                                                     String compareSameLevel, String consumptionLevel, String compareCorpSameLevel)
            throws Exception;

    <T> List<T> aggreationRcViewBu(BaseQueryConditionDTO requestDto, Class<T> clazz, String orderType)
            throws Exception;

    List<RcTrendDTO> aggreationRcTrend(BaseQueryConditionDTO requestDto, String orderType, QueryReportAggDateDimensionEnum dateDimensionEnum)
            throws Exception;

    List<RcTrendDTO> aggreationRcTrendCorpAndIndustry(String startTime, String endTime, String orderType, QueryReportAggDateDimensionEnum dateDimensionEnum,
                                                      List<String> industryList, DataTypeEnum dataTypeEnum,
                                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

}
