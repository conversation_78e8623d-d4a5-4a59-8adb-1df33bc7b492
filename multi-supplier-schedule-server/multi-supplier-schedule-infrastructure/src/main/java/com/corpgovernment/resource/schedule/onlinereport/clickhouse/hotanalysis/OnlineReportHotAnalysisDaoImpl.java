package com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SqlFieldValidator;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.dto.OnlineReportHotAnalysisDto;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.dto.OnlineReportHotAnalysisRequestBo;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.SearchDeptAndCostcneterDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/24 17:58
 * @description：
 * @modified By：
 * @version: $
 */
@Repository
@Slf4j
public class OnlineReportHotAnalysisDaoImpl extends AbstractCommonDao {


    private static final String LOG_TITLE = "OnlineReportHotAnalysisDaoImpl";


    public List<OnlineReportHotAnalysisDto> query(OnlineReportHotAnalysisRequestBo requestBo) throws Exception {

        if (requestBo.getBu().equals(QueryReportBuTypeEnum.flight.name())) {
            return queryFlight(requestBo);
        }

        if (requestBo.getBu().equals(QueryReportBuTypeEnum.hotel.name())) {
            return queryHotel(requestBo);
        }

        if (requestBo.getBu().equals(QueryReportBuTypeEnum.train.name())) {
            return queryTrain(requestBo);
        }

        if (requestBo.getBu().equals(QueryReportBuTypeEnum.car.name())) {
            return queryCar(requestBo);
        }
        return Collections.emptyList();
    }

    /**
     * 查询机票热门分析
     *
     * @param request
     * @return
     * @throws Exception
     */

    public List<OnlineReportHotAnalysisDto> queryFlight(OnlineReportHotAnalysisRequestBo request)
            throws Exception {
        List<Object> paramList = new ArrayList<>();
        String sql = flightSql(request, paramList);
        return commonList(OnlineReportHotAnalysisDto.class, sql, paramList);
    }

    /**
     * 查询协议航司
     *
     * @param request
     * @return
     * @throws Exception
     */

    public List<String> queryAgreementAirlines(OnlineReportHotAnalysisRequestBo request)
            throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(request.getBaseQueryCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        // Validate dim field against whitelist to prevent SQL injection
        String dim = SqlFieldValidator.validateDimField(request.getExtParams().get("dim"));
        if (StringUtils.equalsIgnoreCase(dim, "airline_cn_name") || StringUtils.equalsIgnoreCase(dim, "airline_en_name")) {
            sqlBuilder.append(String.format("select %s as dim ", dim));
        }
        sqlBuilder.append("from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        sqlBuilder.append(" and agreement_type in ('B2G','TA') ");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(request.getBaseQueryCondition(), parmList, isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionNoAudited(request.getProductType()));
        sqlBuilder.append(String.format("and coalesce(%s, '') != '' ", dim));
        sqlBuilder.append("group by dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), OrpConstants.EMPTY,
                (req, statement) -> mapCommonRequest(parmList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
                    try {
                        return DbResultMapUtils.mapStrResultList(u, "dim");
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "queryAgreementAirlines");
    }

    /**
     * 查询酒店热门分析
     *
     * @param request
     * @return
     * @throws Exception
     */

    public List<OnlineReportHotAnalysisDto> queryHotel(OnlineReportHotAnalysisRequestBo request)
            throws Exception {
        List<Object> paramList = new ArrayList<>();
        String sql = hotelSql(request, paramList);
        return commonList(OnlineReportHotAnalysisDto.class, sql, paramList);
    }

    /**
     * 查询酒店集团
     *
     * @param request
     * @return
     * @throws Exception
     */

    public List<String> queryAgreementHotelGroupName(OnlineReportHotAnalysisRequestBo request)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        // Validate dim field against whitelist to prevent SQL injection
        String dim = SqlFieldValidator.validateDimField(request.getExtParams().get("dim"));
        String threeAgreement = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        if (StringUtils.equalsIgnoreCase(dim, "hotel_group_name_en") || StringUtils.equalsIgnoreCase(dim, "hotel_group_name")) {
            sqlBuilder.append(String.format("select %s as dim ", dim));
        }
        sqlBuilder.append("from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        sqlBuilder.append(" AND producttype_all = '" + threeAgreement + "' ");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(request.getBaseQueryCondition(), parmList));
        sqlBuilder.append("group by dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), OrpConstants.EMPTY,
                (req, statement) -> mapCommonRequest(parmList, statement, ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD), (u, d) -> {
                    try {
                        return DbResultMapUtils.mapStrResultList(u, "dim");
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "queryAirlines");
    }

    /**
     * 查询火车热门分析
     *
     * @param request
     * @return
     * @throws Exception
     */

    public List<OnlineReportHotAnalysisDto> queryTrain(OnlineReportHotAnalysisRequestBo request)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        // Validate dim field against whitelist to prevent SQL injection
        String dim = SqlFieldValidator.validateDimField(request.getExtParams().get("dim"));
        List<String> dims = Arrays.asList("line_city", "line_city_en", "departure_city_name", "departure_city_name_en", "arrival_city_name", "arrival_city_name_en");
        if (dims.stream().anyMatch(i -> StringUtils.endsWithIgnoreCase(i, dim))) {
            sqlBuilder.append(String.format("select %s as dim, ", dim));
        }
        sqlBuilder.append("SUM(real_pay) as sumAmount, SUM(quantity) as sumQuantity ");
        sqlBuilder.append("from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(" WHERE d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(request.getBaseQueryCondition(), parmList
                , isBooking(request.getBaseQueryCondition().getStatisticalCaliber()) ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(getTrainTypeCondition(request.getProductType()));
        sqlBuilder.append("group by dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), OrpConstants.EMPTY,
                (req, statement) -> mapCommonRequest(parmList, statement, ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportHotAnalysisDto.class, "queryTrain");
    }

    /**
     * 查询用车热门分析
     *
     * @param request
     * @return
     * @throws Exception
     */

    public List<OnlineReportHotAnalysisDto> queryCar(OnlineReportHotAnalysisRequestBo request)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        // Validate dim field against whitelist to prevent SQL injection
        String dim = SqlFieldValidator.validateDimField(request.getExtParams().get("dim"));
        if (StringUtils.equalsIgnoreCase(dim, "departure_city_name") || StringUtils.equalsIgnoreCase(dim, "vendor_name") || StringUtils.equalsIgnoreCase(dim, "vehicle_name")) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
            sqlBuilder.append(String.format("select case when coalesce(%s, '') = '' then '" + other + "' else %s end as dim, ", dim, dim));
        }
        sqlBuilder.append("sum(coalesce(basic_fee,0))+ sum(coalesce(service_fee,0)) - sum(coalesce(refund_amount,0))  as sumAmount, ");
        sqlBuilder.append("SUM(cnt_order) as sumQuantity, ");
        sqlBuilder.append("round(case when coalesce(sum(coalesce(normal_distance, 0)), 0) != 0 " +
                "                   then divide(" +
                "                           CAST((coalesce(sum(case when coalesce(normal_distance, 0) != 0 then coalesce(real_pay, 0) else 0 end), 0) -" +
                "                               coalesce(sum(case when coalesce(normal_distance, 0) != 0 then coalesce(service_fee, 0) else 0 end), 0)) AS DOUBLE)," +
                "                           CAST(coalesce(sum(coalesce(normal_distance, 0)), 0) AS DOUBLE))" +
                "                   end, 4)  as avgTpms ");
        sqlBuilder.append("from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable());
        sqlBuilder.append(" WHERE d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(request.getBaseQueryCondition(), parmList));
        if (StringUtils.isNotEmpty(request.getBaseQueryCondition().getStartTime())) {
            sqlBuilder.append(" and  substring(order_date,1,10) >= ? ");
            parmList.add(request.getBaseQueryCondition().getStartTime());
        }
        if (StringUtils.isNotEmpty(request.getBaseQueryCondition().getEndTime())) {
            sqlBuilder.append(" and  substring(order_date,1,10) <= ? ");
            parmList.add(request.getBaseQueryCondition().getEndTime());
        }
        sqlBuilder.append("and fee_type = '" + SharkUtils.getChineseVal("FlightFeeType") + "' ");
        sqlBuilder.append(getCarOrderTypeCondition(request.getExtParams().get("carOrderType")));
        // 国内、国际
        sqlBuilder.append(getCarProductTypeCondition(request.getProductType()));
        sqlBuilder.append(" and order_status = 'SA' ");
        sqlBuilder.append("group by dim ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), OrpConstants.EMPTY,
                (req, statement) -> mapCommonRequest(parmList, statement, ClickHouseTable.OLRPT_INDEXCARDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportHotAnalysisDto.class, "queryCar");
    }

    public String flightSql(OnlineReportHotAnalysisRequestBo requestBo, List<Object> paramList) throws BusinessException {
        // Validate dim field against whitelist to prevent SQL injection
        String dim = SqlFieldValidator.validateDimField(requestBo.getExtParams().get("dim"));
        if (!dimCheck(QueryReportBuTypeEnum.flight.name(), dim)) {
            dim = "''";
        }
        String sqlTemplate =
                buildFlightSqlTemplate(isBooking(requestBo.getBaseQueryCondition().getStatisticalCaliber()), paramList).toString();
        StringBuilder conditionBuilder = new StringBuilder();
        conditionBuilder.append(buildScopeFilter(requestBo.getBaseQueryCondition(), paramList));
        // 国内、国际
        conditionBuilder.append(getFlightClassConditionNoAudited(requestBo.getProductType()));
        /**
         * getFlightClassCondition(productType)先和filterCondition(flight_class)同时存在，
         * 后面统一使用getFlightClassCondition(productType)要和前端解耦
         */
        String filterCondition = filterCondition(requestBo.getExtParams(), paramList);
        String sql = sqlTemplate.replace("{dim}", dim)
                .replace("{start}", "?")
                .replace("{end}", "?")
                .replace("{scope_condition}", conditionBuilder.toString())
                .replace("{filter_condition}", filterCondition);
        paramList.add(requestBo.getStartTime());
        paramList.add(requestBo.getEndTime());
        return sql;
    }

    public String filterCondition(Map<String, String> extParams, List<Object> paramList) {
        String filterCondition = "1 = 1";
        if (!StringUtils.isEmpty(extParams.get("flight_class"))) {
            filterCondition += " and flight_class in (?) ";
            paramList.add(extParams.get("flight_class"));
        }
        return filterCondition;
    }

    public String hotelSql(OnlineReportHotAnalysisRequestBo requestBo, List<Object> paramList) throws BusinessException {
        String sqlTemplate =
                buildHotelSqlTemplate(isBooking(requestBo.getBaseQueryCondition().getStatisticalCaliber()), paramList).toString();
        // Validate dim field against whitelist to prevent SQL injection
        String dim = SqlFieldValidator.validateDimField(requestBo.getExtParams().get("dim"));
        if (!dimCheck(QueryReportBuTypeEnum.hotel.name(), dim)) {
            dim = "''";
        }
        if (dim.equalsIgnoreCase("pcitylevel")) {
            sqlTemplate =
                    buildHotelCityLevelSqlTemplate(isBooking(requestBo.getBaseQueryCondition().getStatisticalCaliber()), paramList).toString();
        }
        if (dim.equalsIgnoreCase("star")) {
            sqlTemplate =
                    buildHotelStarSqlTemplate(isBooking(requestBo.getBaseQueryCondition().getStatisticalCaliber()), paramList).toString();
        }
        StringBuilder conditionBuilder = new StringBuilder();
        conditionBuilder.append(buildScopeFilter(requestBo.getBaseQueryCondition(), paramList));
        // 协议、非协议
        conditionBuilder.append(getHotelAgreementConditionSql(requestBo.getExtParams().get("agreementType")));
        // 国内、国际
        conditionBuilder.append(getHotelOrderTypeConditionNoOrderStatus(requestBo.getProductType()));

        String sql = sqlTemplate.replace("{dim}", dim)
                .replace("{start}", "?")
                .replace("{end}", "?")
                .replace("{scope_condition}", conditionBuilder.toString());
        paramList.add(requestBo.getStartTime());
        paramList.add(requestBo.getEndTime());
        return sql;
    }

    /**
     * @param isBooking 是否预订口径
     * @return
     */
    public StringBuilder buildFlightSqlTemplate(boolean isBooking, List<Object> paramList) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {dim} as dim, ");
        stringBuilder.append("SUM(real_pay) as sumAmount,  SUM(quantity) as sumQuantity, ");
        stringBuilder.append("SUM(tpms) as sumTpms, ");
        stringBuilder.append("SUM(price) as sumPrice, ");
        stringBuilder.append("CASE WHEN SUM(CASE WHEN class_type = 'Y' THEN quantity ELSE 0 END) = 0 THEN 0 " +
                "ELSE coalesce(toFloat64(SUM(CASE WHEN class_type = 'Y' THEN price ELSE 0 END)) / " +
                "       SUM(CASE WHEN class_type = 'Y' THEN quantity ELSE 0 END), 0" +
                ") END as avgPrice, ");
        stringBuilder.append("CASE WHEN SUM(CASE WHEN class_type = 'Y' THEN tpms ELSE 0 END) = 0 THEN 0 ELSE " +
                "coalesce(" +
                "   toFloat64(SUM(CASE WHEN class_type = 'Y' THEN price ELSE 0 END)) / " +
                "   toFloat64(SUM(CASE WHEN class_type = 'Y' THEN tpms * quantity ELSE 0 END)), 0" +
                ") END avgTpms ");

        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        stringBuilder.append(" where d = ?").append(" and ");
        paramList.add(partition);
        if (isBooking) {
            stringBuilder.append("orderdt >= {start} and orderdt <= {end} and ({scope_condition}) ");
        } else {
            stringBuilder.append("report_date >= {start} and report_date <= {end} and ({scope_condition}) ");
        }
        stringBuilder.append("and ({filter_condition}) ");
        // 因公
        stringBuilder.append("and fee_type = '" + SharkUtils.getChineseVal("FlightFeeType") + "' ");
        stringBuilder.append("and audited <> 'F' ");
        stringBuilder.append("group by dim ");
        return stringBuilder;
    }

    public StringBuilder buildHotelSqlTemplate(boolean isBooking, List<Object> paramList) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {dim} as dim, ");
        stringBuilder.append("SUM(real_pay) as sumAmount, SUM(quantity) as sumQuantity, ");
        stringBuilder.append("CASE WHEN sumQuantity = 0 THEN 0 ELSE toFloat64(sumAmount) / sumQuantity END as avgPrice ");
        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        stringBuilder.append(" where d = ?").append(" and ");
        paramList.add(partition);
        if (isBooking) {
            stringBuilder.append("orderdt >= {start} and orderdt <= {end} and ({scope_condition}) ");
        } else {
            stringBuilder.append("report_date >= {start} and report_date <= {end} and ({scope_condition}) ");
        }
        stringBuilder.append("and is_oversea IN ('F','O','T') ");
        stringBuilder.append("and order_status = '" + OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus") + "' ");
        stringBuilder.append("and coalesce({dim}, '') != '' ");
        stringBuilder.append("group by dim ");
        return stringBuilder;
    }

    public StringBuilder buildHotelStarSqlTemplate(boolean isBooking, List<Object> paramList) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {dim} as dim, ");
        stringBuilder.append("SUM(real_pay) as sumAmount, SUM(quantity) as sumQuantity, ");
        stringBuilder.append("CASE WHEN sumQuantity = 0 THEN 0 ELSE toFloat64(sumAmount) / sumQuantity END as avgPrice ");
        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        stringBuilder.append(" where d = ?").append(" and ");
        paramList.add(partition);
        if (isBooking) {
            stringBuilder.append("orderdt >= {start} and orderdt <= {end} and ({scope_condition}) ");
        } else {
            stringBuilder.append("report_date >= {start} and report_date <= {end} and ({scope_condition}) ");
        }
        stringBuilder.append("and is_oversea IN ('F','O','T') ");
        stringBuilder.append("and order_status = '" + OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus") + "' ");
        stringBuilder.append("group by dim ");
        return stringBuilder;
    }

    /**
     * 查询酒店城市等级
     *
     * @param
     * @return
     * @throws Exception
     */
    public StringBuilder buildHotelCityLevelSqlTemplate(boolean isBooking, List<Object> paramList) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select case when coalesce({dim}, '') = '' then 'other' else {dim} end as dim, ");
        stringBuilder.append(" SUM(real_pay) as sumAmount, SUM(quantity) as sumQuantity, ");
        stringBuilder.append("CASE WHEN sumQuantity = 0 THEN 0 ELSE toFloat64(sumAmount) / sumQuantity END as avgPrice ");
        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        stringBuilder.append(" where d = ?").append(" and ");
        paramList.add(partition);
        if (isBooking) {
            stringBuilder.append("orderdt >= {start} and orderdt <= {end} and ({scope_condition}) ");
        } else {
            stringBuilder.append("report_date >= {start} and report_date <= {end} and ({scope_condition}) ");
        }
        stringBuilder.append("and order_status = '" + OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus") + "' ");
        stringBuilder.append("and is_oversea IN ('F','O','T') ");
        stringBuilder.append("group by dim ");
        return stringBuilder;
    }

    public static String buildScopeFilter(BaseQueryConditionDTO baseQueryCondition, List<Object> paramList) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" 1 = 1");
        if (StringUtils.isNotEmpty(baseQueryCondition.getGroupId())) {
            stringBuilder.append(" and companygroupid = ?");
            paramList.add(baseQueryCondition.getGroupId());
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            stringBuilder.append(bulidCorpAndAccount("corp_corporation", baseQueryCondition.getCorpIds(), paramList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getAccountIds())) {
            stringBuilder.append(bulidCorpAndAccount("account_id", baseQueryCondition.getAccountIds(), paramList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCostCenterList())) {
            stringBuilder.append(bulidDeptAndCostcenter("cost_center", baseQueryCondition.getCostCenterList(), paramList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getDeptList())) {
            stringBuilder.append(buildOrgId(baseQueryCondition.getDeptList(), paramList));
        }
        return stringBuilder.toString();
    }

    /**
     * 构建公司和成本中心的查询条件
     *
     * @param key
     * @param list
     * @param
     */
    public static String bulidCorpAndAccount(String key, List<String> list, List<Object> paramList) {
        StringBuilder sqlBuffer = new StringBuilder();
        String sqlList =
                StringUtils.join(list.stream().map(corp -> "?").collect(Collectors.toList()), ",");
        sqlBuffer.append(" and ").append(key).append(" in (").append(sqlList).append(")");
        paramList.addAll(list);
        return sqlBuffer.toString();
    }

    /**
     * 构建成本中心和部门的查询条件
     *
     * @param key
     * @param list
     * @param
     */
    public static String bulidDeptAndCostcenter(String key, List<SearchDeptAndCostcneterDTO> list, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        for (SearchDeptAndCostcneterDTO searchDeptAndCostcneterDto : list) {
            Integer level = searchDeptAndCostcneterDto.getKey();
            if (Objects.isNull(level)) {
                continue;
            }
            String filedName = key + level;
            if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                sqlBuffer.append(" and " + filedName + " is not null");
                sqlBuffer.append(" and " + filedName + " <> ''");
            }
            List<String> vals = searchDeptAndCostcneterDto.getVals();
            if (CollectionUtils.isEmpty(vals)) {
                continue;
            }
            String valsSql =
                    StringUtils.join(vals.stream().map(val -> "?").collect(Collectors.toList()), ",");
            sqlBuffer.append(" and " + filedName + " in (" + valsSql + ")");
            paramList.addAll(vals);
        }
        return sqlBuffer.toString();
    }

    public static String buildOrgId(List<SearchDeptAndCostcneterDTO> list, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        for (SearchDeptAndCostcneterDTO searchDeptAndCostcneterDto : list) {
            Integer level = searchDeptAndCostcneterDto.getKey();
            if (Objects.isNull(level)) {
                continue;
            }
            String filedName = "org_id";
            if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                return "";
            }
            List<String> vals = searchDeptAndCostcneterDto.getVals();
            if (CollectionUtils.isEmpty(vals)) {
                continue;
            }
            String valsSql =
                    StringUtils.join(vals.stream().map(val -> "?").collect(Collectors.toList()), ",");
            sqlBuffer.append(" and " + filedName + " in (" + valsSql + ")");
            paramList.addAll(vals);
            break;
        }
        return sqlBuffer.toString();
    }

    private PreparedStatement mapRequest(Object requestDto, PreparedStatement statement) {
        return statement;
    }

    private boolean dimCheck(String bu, String dim) {
        HashSet<String> flightDims = new HashSet<String>() {
            {
                add("airline_en_name");
                add("airline_cn_name");
                add("flight_city_en");
                add("flight_city");
                add("departure_city_name_en");
                add("departure_city_name");
                add("arrival_city_name_en");
                add("arrival_city_name");
            }
        };
        HashSet<String> hotelDims = new HashSet<String>() {
            {
                add("city_name_en");
                add("city_name");
                add("hotel_name_en");
                add("hotel_name");
                add("agreement_mgrgroup_name_en");
                add("agreement_mgrgroup_name");
                add("hotel_brand_name");
                add("pcitylevel");
                add("star");
            }
        };

        HashSet<String> trainDims = new HashSet<String>() {
            {
                add("line_city");
                add("line_city_en");
                add("departure_city_name");
                add("departure_city_name_en");
                add("arrival_city_name");
                add("arrival_city_name_en");
            }
        };

        HashSet<String> carDims = new HashSet<String>() {
            {
                add("departure_city_name");
                add("vendor_name");
                add("vehicle_name");
            }
        };

        if (bu.equals(QueryReportBuTypeEnum.flight.name())) {
            return flightDims.contains(dim.toLowerCase());
        }

        if (bu.equals(QueryReportBuTypeEnum.hotel.name())) {
            return hotelDims.contains(dim.toLowerCase());
        }
        if (bu.equals(QueryReportBuTypeEnum.train.name())) {
            return hotelDims.contains(dim.toLowerCase());
        }
        if (bu.equals(QueryReportBuTypeEnum.car.name())) {
            return hotelDims.contains(dim.toLowerCase());
        }
        return false;
    }

    /**
     * 协议、非协议
     *
     * @param agreementType
     * @return
     */
    protected String getHotelAgreementConditionSql(String agreementType) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");

        // 三方协议
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(agreementType, "TA")) {
            return " and producttype_all = '" + ta + "'  ";
        }
        // 非三方协议
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(agreementType, "NTA")) {
            return " and producttype_all != '" + ta + "'  ";
        }
        return StringUtils.EMPTY;
    }

    // 统计口径是否是预定口径
    private boolean isBooking(String statisticalCaliber) {
        return StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
    }

}
