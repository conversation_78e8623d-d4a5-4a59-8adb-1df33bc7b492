package com.corpgovernment.resource.schedule.onlinereport.module.behaivor;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class TravelMarkMetricDTO {

    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;

    @Column(name = "companyMetric")
    @Type(value = Types.DECIMAL)
    private BigDecimal companyMetric;

    @Column(name = "corpMetric")
    @Type(value = Types.DECIMAL)
    private BigDecimal corpMetric;

    @Column(name = "industryMetric")
    @Type(value = Types.DECIMAL)
    private BigDecimal industryMetric;


    @Column(name = "companyFZ")
    @Type(value = Types.DECIMAL)
    private BigDecimal companyFZ;

    @Column(name = "companyFM")
    @Type(value = Types.DECIMAL)
    private BigDecimal companyFM;

    @Column(name = "corpFZ")
    @Type(value = Types.DECIMAL)
    private BigDecimal corpFZ;

    @Column(name = "corpFM")
    @Type(value = Types.DECIMAL)
    private BigDecimal corpFM;

    @Column(name = "industryFZ")
    @Type(value = Types.DECIMAL)
    private BigDecimal industryFZ;

    @Column(name = "industryFM")
    @Type(value = Types.DECIMAL)
    private BigDecimal industryFM;
}
