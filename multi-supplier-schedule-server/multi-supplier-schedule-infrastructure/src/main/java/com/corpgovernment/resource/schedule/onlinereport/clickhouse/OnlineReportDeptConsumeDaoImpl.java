package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import cn.hutool.core.lang.func.Func;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum1;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AnalysisObjectOrgInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.OverviewTopDeptDTO;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptConsumeDaoService;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;
import static com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
import static com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;


/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
@Slf4j
public class OnlineReportDeptConsumeDaoImpl extends AbstractCommonDao implements OnlineReportDeptConsumeDaoService {

    private static final String CORP_PAY = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.corpPayType");
    private static final String DEAL = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Deal");
    private static final String OVERVIEW_RC_SQL = "SELECT %s  \n"
            + ",coalesce(flight.rcTimes, 0) + coalesce(hotel.rcTimes, 0) + coalesce(train.rcTimes, 0) AS totalRcTimes\n"
            + ",coalesce(flight.orderCount, 0) + coalesce(hotel.orderCount, 0) + coalesce(train.orderCount, 0) AS totalOrderCount "
            + " FROM ( SELECT %s \n"
            + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s  group by %s ) \n"
            + "         flight full JOIN (\n"
            + "        SELECT %s \n"
            + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
            + "        FROM olrpt_indexhoteldownload_all WHERE %s %s group by %s )\n"
            + "         hotel on %s full JOIN (\n"
            + "        SELECT %s \n"
            + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes\n"
            + "        FROM olrpt_indextraindownload_all WHERE %s AND order_status in ('TA', 'RP', 'EP', 'EA') group by %s)\n"
            + "         train on %s";
    private static final String OVERVIEW_RC_CORP_INDUSTRY_SQL = "SELECT \n"
            + "coalesce(flight.rcTimes, 0) + coalesce(hotel.rcTimes, 0) + coalesce(train.rcTimes, 0) AS totalRcTimes\n"
            + ",coalesce(flight.orderCount, 0) + coalesce(hotel.orderCount, 0) + coalesce(train.orderCount, 0) AS totalOrderCount "
            + " FROM ( SELECT  \n"
            + "        COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s \n"
            + "        having (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0) \n"
            + "         flight CROSS JOIN (\n"
            + "        SELECT \n"
            + "        COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
            + "        FROM olrpt_indexhoteldownload_all WHERE %s %s \n"
            + "          having (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0)\n"
            + "         hotel CROSS JOIN (\n"
            + "        SELECT \n"
            + "        COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes\n"
            + "        FROM olrpt_indextraindownload_all WHERE %s AND order_status in ('TA', 'RP', 'EP', 'EA')\n"
            + "            having (COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END)) != 0)\n"
            + "         train ";
    private static final String OVERVIEW_OVER_AMOUNT_SQL = "SELECT %s  \n"
            + ",coalesce(flight.lossPrice, 0) + coalesce(hotel.lossPrice, 0) AS totalOverAmount\n"
            + " FROM ( SELECT %s \n"
            + "        , round(sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end), 2) as lossPrice"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s  group by %s \n"
            + "        having (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0) \n"
            + "         flight full JOIN (\n"
            + "        SELECT %s \n"
            + "        ,  round(SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end),2) as lossPrice \n"
            + "        FROM olrpt_indexhoteldownload_all WHERE %s %s group by %s \n"
            + "          having (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0)\n"
            + "         hotel on %s";
    private static final String OVERVIEW_CARBON_SQL = "SELECT %s  \n"
            + ",coalesce(flight.totalCarbonEmission, 0) + coalesce(car.totalCarbonEmission, 0) + coalesce(train.totalCarbonEmission, 0) AS totalCarbonEmission\n"
            + " FROM ( SELECT %s \n"
            + "        , SUM(coalesce(carbon_emission, 0)) AS totalCarbonEmission"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s  group by %s ) \n"
            + "         flight full JOIN (\n"
            + "        SELECT %s \n"
            + "        , SUM(coalesce(carbon_emission, 0)) AS totalCarbonEmission"
            + "        FROM olrpt_indextraindownload_all WHERE %s AND order_status in ('TA', 'RP', 'EP', 'EA') group by %s )\n"
            + "         train on %s full JOIN (\n"
            + "        SELECT %s \n"
            + "        , SUM(coalesce(carbon_emission, 0)) AS totalCarbonEmission"
            + "        FROM olrpt_indexcardownload_all WHERE %s  group by %s)\n"
            + "         car on %s";
    @Autowired
    private OnlineReportOverViewJoinCondition onlineReportOverViewJoinCondition;

    public String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum,
                           BaseQueryConditionDTO baseQueryConditionDto, Pager pager, QueryReportBuTypeEnum queryBu, String analysisObjectVal) throws Exception {

        JoinCondition biz = joinCondition(analysisObjectEnum, null, baseQueryConditionDto.getLang());
        String base = String.format("SELECT %s FROM", biz.getResultFields() + staticalsSql());
        StringBuilder baseSql = new StringBuilder(base);
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String statisticalCaliber = baseQueryConditionDto.getStatisticalCaliber();
        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partion = queryPartition(clickHouseTable);
        String queryCondition = queryConditionWithSpecialCol(analysisObjectEnum, analysisObjectVal, parmList);
        String currentCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion);
        int intervalMonths =
                OrpDateTimeUtils.getMonthNum(baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime());
        baseSql.append(String.format(
                "( SELECT %s  FROM %s\n"
                        + "    WHERE %s GROUP BY %s) %s",
                biz.getGroupFields() + ", " + getStaticalField(queryBu) + ", " + getStaticalQuantity(queryBu)
                , clickHouseTable.getTable()
                , currentCondition + queryCondition
                , biz.getGroupFields()
                , "current")
        );
        // 同比去年
        List<String> yoyTimes = OrpDateTimeUtils.yoyTime(startTime, endTime, OrpConstants.ONE_YEAR_DAYS);

        String yoyCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, yoyTimes.get(0), yoyTimes.get(1), partion);
        baseSql.append(String.format(
                " LEFT JOIN (SELECT %s\n"
                        + "        FROM %s  WHERE %s\n"
                        + "        GROUP BY %s) %s  ON  %s",
                biz.getGroupFields() + ", " + getStaticalField(queryBu)
                , clickHouseTable.getTable()
                , yoyCondition + queryCondition, biz.getGroupFields()
                , "yoy", biz.getCurrentJoinYoy()));
        // 同比前年
        List<String> yoyBeforeTimes = OrpDateTimeUtils.yoyTime(startTime, endTime, OrpConstants.TWO_YEAR_DAYS);
        String yoyBeforeLastCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, yoyBeforeTimes.get(0), yoyBeforeTimes.get(1), partion);
        baseSql.append(String.format(
                " LEFT JOIN (SELECT %s\n"
                        + "        FROM %s  WHERE %s\n"
                        + "        GROUP BY %s) %s  ON  %s",
                biz.getGroupFields() + ", " + getStaticalField(queryBu)
                , clickHouseTable.getTable()
                , yoyBeforeLastCondition + queryCondition
                , biz.getGroupFields(), "yoyBeforeLast", biz.getCurrentJoinYoyBeforeLast()));
        // 环比
        List<String> momTimes = OrpDateTimeUtils.momTime(startTime, endTime);
        String momCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, momTimes.get(0), momTimes.get(1), partion);
        baseSql.append(String.format(
                " LEFT JOIN (SELECT %s\n"
                        + "        FROM %s  WHERE %s\n"
                        + "        GROUP BY %s) %s  ON  %s",
                biz.getGroupFields() + ", " + getStaticalField(queryBu)
                , clickHouseTable.getTable()
                , momCondition + queryCondition, biz.getGroupFields()
                , "mom", biz.getCurrentJoinMom()));
        currentCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion);
        baseSql.append(String.format(
                " CROSS JOIN (SELECT %s FROM %s  WHERE %s ) total",
                getStaticalField(queryBu), clickHouseTable.getTable(), currentCondition + queryCondition));
        baseSql.append(" order by current.real_pay desc");
        baseSql.append(" limit ?, ? ");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        return baseSql.toString();
    }

    private String staticalsSql() {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(" , round(current.real_pay, 2) AS realPay");
        stringBuffer.append(" , current.totalQuantity AS totalQuantity");
        stringBuffer.append(" , round(case when toFloat64(abs(coalesce(yoy.real_pay, 0))) !=0 "
                + "then divide(toFloat64(coalesce(current.real_pay, 0) - coalesce(yoy.real_pay, 0))"
                + ", toFloat64(abs(coalesce(yoy.real_pay, 0)))) * 100 else 0 end, 2) AS yoy");
        stringBuffer.append(" , round(case when toFloat64(abs(coalesce(yoyBeforeLast.real_pay, 0))) !=0 "
                + "then divide(toFloat64(coalesce(current.real_pay, 0) - coalesce(yoyBeforeLast.real_pay, 0))"
                + ", toFloat64(abs(coalesce(yoyBeforeLast.real_pay, 0)))) * 100 else 0 end, 2) AS yoyBeforeLast");
        stringBuffer.append(" , round(case when toFloat64(abs(coalesce(mom.real_pay, 0))) !=0 "
                + "then divide(toFloat64(coalesce(current.real_pay, 0) - coalesce(mom.real_pay, 0))"
                + ", toFloat64(abs(coalesce(mom.real_pay, 0)))) * 100 else 0 end, 2) AS mom");
        stringBuffer.append(" , round(case when toFloat64(abs(coalesce(current.real_pay, 0))) !=0 "
                + "then divide(toFloat64(coalesce(current.real_pay, 0)), toFloat64(abs(coalesce(total.real_pay, 0))))* 100 else 0 end, 2) AS realPayPercent");
        return stringBuffer.toString();
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinCondition(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String lang) {
        if (ConfigUtils.getBoolean("custom_dim_open", false)) {
            return joinConditionCustom(analysisObjectEnum, analysisObjectOrgInfo, lang);
        } else {
            return joinConditionDefault(analysisObjectEnum, analysisObjectOrgInfo, lang);
        }
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionDefault(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String lang) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        // 用别名group by是因为原字段在表中可能的值可能为空或null，导至group by时会出现分组不准确的情况，因此进行特殊处理特殊处理
        biz.setSpecialGroupFields(" aggId, aggType");
        switch (analysisObjectEnum) {
            case CORP:
                biz = biz.setSpecialResultFields("current.corp_corporation as aggId, current.corp_name as aggType")
                        .setResultFields("current.corp_corporation as aggId,current.corp_name as aggType")
                        .setOrderByFields("current.corp_corporation, current.corp_name")
                        .setGroupFields("corp_corporation, corp_name")
                        .setCurrentJoinMom(
                                "current.corp_corporation = mom.corp_corporation and current.corp_name = mom.corp_name")
                        .setCurrentJoinYoy(
                                "current.corp_corporation = yoy.corp_corporation and current.corp_name = yoy.corp_name")
                        .setCurrentJoinYoyBeforeLast(
                                "current.corp_corporation = yoyBeforeLast.corp_corporation and current.corp_name = yoyBeforeLast.corp_name");
                break;
            case ACCOUNT:
                biz = biz.setSpecialResultFields("current.account_id as aggId, current.account_name as aggType")
                        .setResultFields("current.account_id as aggId, current.account_name as aggType")
                        .setOrderByFields("current.account_id, current.account_name")
                        .setGroupFields("account_id, account_name")
                        .setCurrentJoinMom(
                                "current.account_id = mom.account_id and current.account_name = mom.account_name")
                        .setCurrentJoinYoy(
                                "current.account_id = yoy.account_id and current.account_name = yoy.account_name")
                        .setCurrentJoinYoyBeforeLast(
                                "current.account_id = yoyBeforeLast.account_id and current.account_name = yoyBeforeLast.account_name");
                break;
            case ACCOUNTCODE:
                biz = biz.setSpecialResultFields("current.account_id as aggId, current.account_code as aggType")
                        .setResultFields("current.account_id as aggId, current.account_code as aggType")
                        .setOrderByFields("current.account_id, current.account_code")
                        .setGroupFields("account_id, account_code")
                        .setCurrentJoinMom(
                                "current.account_id = mom.account_id and current.account_code = mom.account_code")
                        .setCurrentJoinYoy(
                                "current.account_id = yoy.account_id and current.account_code = yoy.account_code")
                        .setCurrentJoinYoyBeforeLast(
                                "current.account_id = yoyBeforeLast.account_id and current.account_code = yoyBeforeLast.account_code");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase();
                break;
            case COSTCENTER1:
                analysisObject = "cost_center1";
                break;
            case COSTCENTER2:
                analysisObject = "cost_center2";
                break;
            case COSTCENTER3:
                analysisObject = "cost_center3";
                break;
            case COSTCENTER4:
                analysisObject = "cost_center4";
                break;
            case COSTCENTER5:
                analysisObject = "cost_center5";
                break;
            case COSTCENTER6:
                analysisObject = "cost_center6";
                break;
            case ORG:
                analysisObject = getAnalysisObjectOrgInfo(analysisObjectOrgInfo);
                break;
            case UID:
                biz = biz.setSpecialResultFields("current.uid as aggId, current.user_name as aggType")
                        .setResultFields("current.uid as aggId, current.user_name as aggType")
                        .setOrderByFields("current.uid, current.user_name")
                        .setGroupFields("uid, user_name")
                        .setCurrentJoinMom(
                                "current.uid = mom.uid and current.user_name = mom.user_name")
                        .setCurrentJoinYoy(
                                "current.uid = yoy.uid and current.user_name = yoy.user_name")
                        .setCurrentJoinYoyBeforeLast(
                                "current.uid = yoyBeforeLast.uid and current.user_name = yoyBeforeLast.user_name");
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject)) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            biz = biz.setResultFields(" coalesce(current." + analysisObject + ",'') as aggId" +
                            ", case when (coalesce(current." + analysisObject + ",'')='') then '" + other + "' else current." + analysisObject + " end as " + "aggType")
                    .setSpecialResultFields(" coalesce(current." + analysisObject + ",'') as aggId " +
                            ", case when (coalesce(current." + analysisObject + ",'')='') then '" + other + "' else current." + analysisObject + " end as " + "aggType")
                    .setOrderByFields("current." + analysisObject).setGroupFields(analysisObject)
                    .setCurrentJoinMom(String.format("current.%s = mom.%s", analysisObject, analysisObject))
                    .setCurrentJoinYoy(String.format("current.%s = yoy.%s", analysisObject, analysisObject))
                    .setCurrentJoinYoyBeforeLast(
                            String.format("current.%s = yoyBeforeLast.%s", analysisObject, analysisObject));
        }
        if (analysisObjectEnum == AnalysisObjectEnum.ORG) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            String temp = " coalesce(" + analysisObject + ",'') as aggId, " +
                    " case when (coalesce(" + analysisObject + ",'')='') then '" + other + "' else " + analysisObject + " end as " + "aggType";
            biz = biz.setResultFields(temp)
                    .setSpecialResultFields(temp)
                    .setOrderByFields(" aggId, aggType").setGroupFields(" aggId, aggType");
        }
        return biz;
    }


    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionCustom(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String lang) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        // 用别名group by是因为原字段在表中可能的值可能为空或null，导至group by时会出现分组不准确的情况，因此进行特殊处理特殊处理
        biz.setSpecialGroupFields(" aggId, aggType");
        switch (analysisObjectEnum) {
            case CORP:
                biz = biz.setSpecialResultFields("current.corp_corporation as aggId, current.corp_name as aggType")
                        .setResultFields("current.corp_corporation as aggId, current.corp_name as aggType")
                        .setOrderByFields("current.corp_corporation, current.corp_name")
                        .setGroupFields("corp_corporation, corp_name")
                        .setCurrentJoinMom(
                                "current.corp_corporation = mom.corp_corporation and current.corp_name = mom.corp_name")
                        .setCurrentJoinYoy(
                                "current.corp_corporation = yoy.corp_corporation and current.corp_name = yoy.corp_name")
                        .setCurrentJoinYoyBeforeLast(
                                "current.corp_corporation = yoyBeforeLast.corp_corporation and current.corp_name = yoyBeforeLast.corp_name");
                break;
            case ACCOUNT:
                biz = biz.setSpecialResultFields("current.account_id as aggId, current.account_name as aggType")
                        .setResultFields("current.account_id as aggId, current.account_name as aggType")
                        .setOrderByFields("current.account_id, current.account_name")
                        .setGroupFields("account_id, account_name")
                        .setCurrentJoinMom(
                                "current.account_id = mom.account_id and current.account_name = mom.account_name")
                        .setCurrentJoinYoy(
                                "current.account_id = yoy.account_id and current.account_name = yoy.account_name")
                        .setCurrentJoinYoyBeforeLast(
                                "current.account_id = yoyBeforeLast.account_id and current.account_name = yoyBeforeLast.account_name");
                break;
            case ACCOUNTCODE:
                biz = biz.setSpecialResultFields("current.account_id as aggId, current.account_code as aggType")
                        .setResultFields("current.account_id as aggId, current.account_code as aggType")
                        .setOrderByFields("current.account_id, current.account_code")
                        .setGroupFields("account_id, account_code")
                        .setCurrentJoinMom(
                                "current.account_id = mom.account_id and current.account_code = mom.account_code")
                        .setCurrentJoinYoy(
                                "current.account_id = yoy.account_id and current.account_code = yoy.account_code")
                        .setCurrentJoinYoyBeforeLast(
                                "current.account_id = yoyBeforeLast.account_id and current.account_code = yoyBeforeLast.account_code");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase().concat("_custom");
                break;
            case COSTCENTER1:
                analysisObject = "costcenter1_custom";
                break;
            case COSTCENTER2:
                analysisObject = "costcenter2_custom";
                break;
            case COSTCENTER3:
                analysisObject = "costcenter3_custom";
                break;
            case COSTCENTER4:
                analysisObject = "costcenter4_custom";
                break;
            case COSTCENTER5:
                analysisObject = "costcenter5_custom";
                break;
            case COSTCENTER6:
                analysisObject = "costcenter6_custom";
                break;
            case ORG:
                analysisObject = getAnalysisObjectOrgInfo(analysisObjectOrgInfo);
                break;
            case UID:
                biz = biz.setSpecialResultFields("current.uid as aggId, current.user_name as aggType")
                        .setResultFields("current.uid as aggId, current.user_name as aggType")
                        .setOrderByFields("current.uid, current.user_name")
                        .setGroupFields("uid, user_name")
                        .setCurrentJoinMom(
                                "current.uid = mom.uid and current.user_name = mom.user_name")
                        .setCurrentJoinYoy(
                                "current.uid = yoy.uid and current.user_name = yoy.user_name")
                        .setCurrentJoinYoyBeforeLast(
                                "current.uid = yoyBeforeLast.uid and current.user_name = yoyBeforeLast.user_name");
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject) && analysisObjectEnum != AnalysisObjectEnum.ORG) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            biz = biz.setResultFields(" coalesce(current." + analysisObject + ",'') as aggId, " +
                            " case when (coalesce(current." + analysisObject + ",'')='') then '" + other + "' else current." + analysisObject + " end as " + "aggType")
                    .setSpecialResultFields(" coalesce(current." + analysisObject + ",'') as aggId, " +
                            "case when (coalesce(current." + analysisObject + ",'')='') then '" + other + "' else current." + analysisObject + " end as " + "aggType")
                    .setOrderByFields("current." + analysisObject).setGroupFields(analysisObject)
                    .setCurrentJoinMom(String.format("current.%s = mom.%s", analysisObject, analysisObject))
                    .setCurrentJoinYoy(String.format("current.%s = yoy.%s", analysisObject, analysisObject))
                    .setCurrentJoinYoyBeforeLast(
                            String.format("current.%s = yoyBeforeLast.%s", analysisObject, analysisObject));
        }
        if (analysisObjectEnum == AnalysisObjectEnum.ORG) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            String temp = " coalesce(" + analysisObject + ",'') as aggId, " +
                    " case when (coalesce(" + analysisObject + ",'')='') then '" + other + "' else " + analysisObject + " end as " + "aggType";
            biz = biz.setResultFields(temp)
                    .setSpecialResultFields(temp)
                    .setOrderByFields(" aggId, aggType").setGroupFields(" aggId, aggType");
        }
        return biz;
    }

    private String getAnalysisObjectOrgInfo(AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        return String.format("visitParamExtractString(current.orginfo, 'org%d')", analysisObjectOrgInfo.getLevel() + 1);
    }

    private String getAnalysisObjectOrgInfoCondition(AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum analysisObjectEnum,
                                                     List<Object> parmList) {
        StringBuilder stringBuilder = new StringBuilder();
        if (analysisObjectOrgInfo == null || StringUtils.isEmpty(analysisObjectOrgInfo.getOrgName()) || analysisObjectEnum != AnalysisObjectEnum.ORG) {
            return stringBuilder.toString();
        }
        stringBuilder.append(" AND coalesce(orginfo, '') != '' ");
        if (analysisObjectOrgInfo.getLevel() > 0) {
            stringBuilder.append(String.format(" AND visitParamExtractString(orginfo, 'org%d') = ? ", analysisObjectOrgInfo.getLevel()));
            parmList.add(analysisObjectOrgInfo.getOrgName());
        }
        return stringBuilder.toString();
    }

    private String getDownAnalysisObjectOrgInfoCondition(AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        StringBuilder stringBuilder = new StringBuilder();
        if (analysisObjectOrgInfo == null || StringUtils.isEmpty(analysisObjectOrgInfo.getOrgName())) {
            return stringBuilder.toString();
        }
        stringBuilder.append(" AND coalesce(orginfo, '') != '' ");
        if (analysisObjectOrgInfo.getLevel() > 0) {
            stringBuilder.append(String.format(" AND visitParamExtractString(orginfo, 'org%d') != '' ", analysisObjectOrgInfo.getLevel()));
        }
        return stringBuilder.toString();
    }

    private String getAnalysisObjectOrgInfoCondition(AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum analysisObjectEnum,
                                                     String val, List<Object> parmList) {
        StringBuilder stringBuilder = new StringBuilder();
        if (analysisObjectOrgInfo == null || StringUtils.isEmpty(analysisObjectOrgInfo.getOrgName()) || analysisObjectEnum != AnalysisObjectEnum.ORG) {
            return stringBuilder.toString();
        }
        stringBuilder.append(" AND coalesce(orginfo, '') != '' ");
        if (analysisObjectOrgInfo.getLevel() > 0) {
            stringBuilder.append(String.format(" AND visitParamExtractString(orginfo, 'org%d') = ? ", analysisObjectOrgInfo.getLevel()));
            parmList.add(val);
        }
        return stringBuilder.toString();
    }

    /**
     * 具体查询条件
     *
     * @param downObjectEnum
     * @param downObjectVal
     * @return
     */
    public String downCondition(AnalysisObjectEnum downObjectEnum, String downObjectVal, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                AnalysisObjectEnum analysisObjectEnum, List<Object> parmList) {
        String conditon = StringUtils.EMPTY;
        if (downObjectEnum == null || StringUtils.isEmpty(downObjectVal)) {
            return conditon;
        }
        switch (downObjectEnum) {
            case CORP:
                conditon = " and corp_corporation = ? ";
                parmList.add(downObjectVal);
                break;
            case ACCOUNT:
            case ACCOUNTCODE:
                conditon = " and account_id = '%s' ";
                conditon = String.format(conditon, downObjectVal);
                break;
            case DEPT1:
                conditon = " and dept1 like '%"+ DbResultMapUtils.sm4Encrypt(downObjectVal) +"%' ";
                break;
            case DEPT2:
                conditon = " and dept2 like '% "+ DbResultMapUtils.sm4Encrypt(downObjectVal) +" %' ";
                break;
            case DEPT3:
                conditon = " and dept3 like '% "+ DbResultMapUtils.sm4Encrypt(downObjectVal) +" %' ";

                break;
            case DEPT4:
                conditon = " and dept4 like '% "+ DbResultMapUtils.sm4Encrypt(downObjectVal) +" %' ";
                break;
            case DEPT5:
                conditon = " and dept5 like '% "+ DbResultMapUtils.sm4Encrypt(downObjectVal) +" %' ";
                break;
            case DEPT6:
                conditon = " and dept6 like '% "+ DbResultMapUtils.sm4Encrypt(downObjectVal) +" %' ";
                conditon = String.format(conditon, DbResultMapUtils.sm4Encrypt(downObjectVal));
                break;
            case DEPT7:
                conditon = " and dept7 like '% "+ DbResultMapUtils.sm4Encrypt(downObjectVal) +" %' ";
                break;
            case DEPT8:
                conditon = " and dept8 like '% "+ DbResultMapUtils.sm4Encrypt(downObjectVal) +" %' ";
                break;
            case DEPT9:
                conditon = " and dept9 like '% "+ DbResultMapUtils.sm4Encrypt(downObjectVal) +" %' ";
                break;
            case DEPT10:
                conditon = " and dept10 like '% "+ DbResultMapUtils.sm4Encrypt(downObjectVal) +" %' ";
                break;
            case COSTCENTER1:
                conditon = " and cost_center1 like '% "+DbResultMapUtils.sm4Encrypt(downObjectVal)+" %' ";
                break;
            case COSTCENTER2:
                conditon = " and cost_center2 like '% "+DbResultMapUtils.sm4Encrypt(downObjectVal)+" %' ";
                break;
            case COSTCENTER3:
                conditon = " and cost_center3 like '% "+DbResultMapUtils.sm4Encrypt(downObjectVal)+" %' ";
                break;
            case COSTCENTER4:
                conditon = " and cost_center4 like '% "+DbResultMapUtils.sm4Encrypt(downObjectVal)+" %' ";
                break;
            case COSTCENTER5:
                conditon = " and cost_center5 like '% "+DbResultMapUtils.sm4Encrypt(downObjectVal)+" %' ";
                break;
            case COSTCENTER6:
                if (ConfigUtils.getBoolean("custom_dim_open", false)) {
                    conditon = String.format(" and %s_custom = ? ", downObjectEnum.toString().toLowerCase());
                    conditon = String.format(conditon, downObjectVal);
                } else {
                    String fieldName = AnalysisObjectEnum1.getAnalysisObjectEnum(downObjectEnum).getName();
                    conditon = String.format(" and %s = ? ", fieldName);
                    conditon = String.format(conditon, downObjectVal);
                }
                break;
            case ORG:
                conditon = getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, downObjectVal, parmList);
                break;
            case UID:
                conditon = " and uid = '%s' ";
                conditon = String.format(conditon, downObjectVal);
                break;
            default:
                break;
        }
        return conditon;
    }

    /**
     * 具体查询条件
     *
     * @param downObjectEnum
     * @param downObjectVal
     * @return
     */
    public String downConditionWithSpecialCol(AnalysisObjectEnum downObjectEnum, String downObjectVal, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                              AnalysisObjectEnum analysisObjectEnum, QueryReportBuTypeEnum queryBu, List<Object> paramList) {
        String conditon = StringUtils.EMPTY;
        if (downObjectEnum == null || StringUtils.isEmpty(downObjectVal)) {
            return conditon;
        }
        switch (downObjectEnum) {
            case CORP:
                if (queryBu == QueryReportBuTypeEnum.bus) {
                    conditon = " and corpid = ? ";
                    paramList.add(downObjectVal);
                } else if (queryBu == QueryReportBuTypeEnum.vaso) {
                    conditon = " and company = ? ";
                    paramList.add(downObjectVal);
                } else {
                    conditon = " and corp_corporation = ? ";
                    paramList.add(downObjectVal);
                }
                break;
            case ACCOUNT:
            case ACCOUNTCODE:
                if (queryBu == QueryReportBuTypeEnum.bus || queryBu == QueryReportBuTypeEnum.vaso) {
                    conditon = " and accountid = ? ";
                    paramList.add(Integer.valueOf(downObjectVal));
                } else {
                    conditon = " and account_id = ? ";
                    paramList.add(Integer.valueOf(downObjectVal));
                }
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
            case COSTCENTER1:
            case COSTCENTER2:
            case COSTCENTER3:
            case COSTCENTER4:
            case COSTCENTER5:
            case COSTCENTER6:
                if (ConfigUtils.getBoolean("custom_dim_open", false)) {
                    conditon = String.format(" and %s_custom = ? ", downObjectEnum.toString().toLowerCase());
                    paramList.add(downObjectVal);
                } else {
                    String fieldName = AnalysisObjectEnum1.getAnalysisObjectEnum(downObjectEnum).getName();
                    conditon = String.format(" and %s = ? ", fieldName);
                    paramList.add(downObjectVal);
                }
                break;
            case ORG:
                conditon = getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum,
                        downObjectVal, paramList);
                break;
            case UID:
                conditon = " and uid = ? ";
                paramList.add(downObjectVal);
                break;
            default:
                break;
        }
        return conditon;
    }

    /**
     * 无具体查询条件
     *
     * @param downObjectEnum
     * @param analysisObjectOrgInfo
     * @return
     */
    public String downCondition(AnalysisObjectEnum downObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        String conditon = StringUtils.EMPTY;
        if (downObjectEnum == null) {
            return conditon;
        }
        switch (downObjectEnum) {
            case CORP:
                conditon = " and corp_corporation != '' ";
                break;
            case ACCOUNT:
            case ACCOUNTCODE:
                conditon = " and account_id != null and account_id != 0  ";
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
            case COSTCENTER1:
            case COSTCENTER2:
            case COSTCENTER3:
            case COSTCENTER4:
            case COSTCENTER5:
            case COSTCENTER6:
                if (ConfigUtils.getBoolean("custom_dim_open", false)) {
                    conditon = String.format(" and %s_custom != '' ", downObjectEnum.toString().toLowerCase());
                } else {
                    conditon = String.format(" and %s != '' ", downObjectEnum.toString().toLowerCase());
                }
                break;
            case ORG:
                conditon = getDownAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo);
                break;
            case UID:
                conditon = String.format(" and uid != '' ");
                break;
            default:
                break;
        }
        return conditon;
    }


    /**
     * 前5部门消费
     *
     * @param
     * @return
     * @throws Exception
     */
    public <T> List<T> deptConsume(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto,
                                   Pager pager, Class<T> clazz, QueryReportBuTypeEnum queryBu, String analysisObjectVal) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String querySql = querySql(parmList, analysisObjectEnum, baseQueryConditionDto, pager, queryBu, analysisObjectVal);
        // 查询clickhouse
        return commonList(clazz, querySql, parmList);
    }

    /**
     * 前5部门消费
     *
     * @param
     * @return
     * @throws Exception
     */
    public Integer count(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, String analysisObjectVal)
            throws Exception {
        List<Object> parmList = new ArrayList<>();

        String statisticalCaliber = baseQueryConditionDto.getStatisticalCaliber();
        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String countSql =
                countSql(parmList, clickHouseTable, analysisObjectEnum, baseQueryConditionDto, analysisObjectVal);
        // 查询clickhouse
        return commonCount(countSql, parmList);
    }

    public String countSql(List<Object> parmList, ClickHouseTable clickHouseTable,
                           AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, String analysisObjectVal) throws Exception {
        JoinCondition biz = joinCondition(analysisObjectEnum, null, baseQueryConditionDto.getLang());
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String partion = queryPartition(clickHouseTable);
        String queryCondition = queryConditionWithSpecialCol(analysisObjectEnum, analysisObjectVal, parmList);

        String currentCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion);
        return String.format(
                "select count(1) as countAll from(SELECT 1  FROM  %s  WHERE %s  GROUP by %s ) tmp",
                clickHouseTable.getTable(), currentCondition + queryCondition, biz.groupFields);
    }

    private String getStaticalField(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        String staticalField = " SUM(coalesce(amount_total,0)) AS real_pay ";
        if (queryReportBuTypeEnum == null) {
            return staticalField;
        }
        switch (queryReportBuTypeEnum) {
            case overview:
                staticalField = " SUM(coalesce(amount_total,0)) AS real_pay ";
                break;
            case flight:
                staticalField = " SUM(coalesce(amount_flt,0)) AS real_pay ";
                break;
            case hotel:
                staticalField = " SUM(coalesce(amount_htl,0)) AS real_pay ";
                break;
            case train:
                staticalField = " SUM(coalesce(amount_train,0)) AS real_pay ";
                break;
            case car:
                staticalField = " SUM(coalesce(amount_car,0)) AS real_pay ";
                break;
            case bus:
                staticalField = " SUM(coalesce(amount_bus,0)) AS real_pay ";
                break;
            case vaso:
                staticalField = " SUM(coalesce(amount_vas,0)) AS real_pay ";
                break;
            default:
                staticalField = " SUM(coalesce(amount_total,0)) AS real_pay ";
                break;
        }
        return staticalField;
    }

    private String getStaticalQuantity(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        String staticalField = " SUM(coalesce(quantity_total,0)) AS totalQuantity ";
        if (queryReportBuTypeEnum == null) {
            return staticalField;
        }
        switch (queryReportBuTypeEnum) {
            case overview:
                staticalField = " SUM(coalesce(quantity_total,0)) AS totalQuantity ";
                break;
            case flight:
                staticalField = " SUM(coalesce(quantity_flt,0)) AS totalQuantity ";
                break;
            case hotel:
                staticalField = " SUM(coalesce(quantity_htl,0)) AS totalQuantity ";
                break;
            case train:
                staticalField = " SUM(coalesce(quantity_train,0)) AS totalQuantity ";
                break;
            case car:
                staticalField = " SUM(coalesce(quantity_car,0)) AS totalQuantity ";
                break;
            case bus:
                staticalField = " SUM(coalesce(quantity_bus,0)) AS totalQuantity ";
                break;
            case vaso:
                staticalField = " SUM(coalesce(quantity_vas,0)) AS totalQuantity ";
                break;
            default:
                staticalField = " SUM(coalesce(quantity_total,0)) AS totalQuantity ";
                break;
        }
        return staticalField;
    }

    public <T> List<T> topDeptMetricAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum,
                                             Class<T> clazz, QueryReportBuTypeEnum queryBu, String metric, Pager pager) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        JoinCondition biz = joinCondition(analysisObjectEnum, null, requestDto.getLang());

        List<Object> parmList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        switch (queryBu) {
            case flight:
                sql.append(fltMetricSql(requestDto, metric, biz, parmList, isBookCaliber));
                break;
            case hotel:
                sql.append(htlMetricSql(requestDto, metric, biz, parmList, isBookCaliber));
                break;
            case train:
                sql.append(trainMetricSql(requestDto, metric, biz, parmList, isBookCaliber));
                break;
            default:
                break;
        }
        sql.append(" limit ?, ? ");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        // 查询clickhouse
        return commonList(clazz, sql.toString(), parmList);
    }

    private String fltMetricSql(BaseQueryConditionDTO requestDto,
                                String metric, JoinCondition biz, List<Object> parmList, boolean isBookCaliber) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ").append(biz.getResultFields());
        if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.REFUND_QUANTITY.toString())) {
            sqlBuilder.append(" , SUM(coalesce(refundtkt, 0)) AS metric ");
            sqlBuilder.append(",SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0)) + SUM(coalesce(refund_behind_service_fee, 0)) as loss");
        } else if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.REBOOK_QUANTITY.toString())) {
            sqlBuilder.append(" , SUM(CASE WHEN is_rebook = 'T' THEN quantity ELSE 0 END) AS metric ");
            sqlBuilder.append(",SUM(coalesce(change_fee,0))+SUM(coalesce(rebook_price_differential,0))+SUM(coalesce(rebook_service_fee,0))" +
                    " + SUM(if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0)))+SUM(coalesce(rebook_behind_service_fee, 0)) as loss");
        } else if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.LOW_RC_COUNT.toString())) {
            sqlBuilder.append(" , count(distinct CASE WHEN is_refund = 'F' and low_rid IS NOT NULL AND low_rid != '' THEN order_id END) AS metric ");
            sqlBuilder.append(" , SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as loss ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXFLIGHTDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by metric desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    private String htlMetricSql(BaseQueryConditionDTO requestDto, String metric,
                                JoinCondition biz, List<Object> parmList, boolean isBookCaliber) {
        StringBuilder sqlBuilder = new StringBuilder();

        sqlBuilder.append(" SELECT ").append(biz.getResultFields());
        if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.REFUND_QUANTITY.toString())) {
            sqlBuilder.append(" , SUM(CASE WHEN is_refund = 'T' THEN abs(coalesce(quantity,0)) ELSE 0 END) AS metric ");
            sqlBuilder.append(" , sum(coalesce(cancel_esti_save_amount,0)) AS loss ");
        } else if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.LOW_RC_COUNT.toString())) {
            sqlBuilder.append(" , count(distinct CASE WHEN reason_code IS NOT NULL AND reason_code != '' THEN order_id END) AS metric ");
            sqlBuilder.append(" , SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) AS loss ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXHOTELDOWNLOAD.getTable()).append(" as current");
        ;
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by metric desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    private String trainMetricSql(BaseQueryConditionDTO requestDto, String metric,
                                  JoinCondition biz, List<Object> parmList, boolean isBookCaliber) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ").append(biz.getResultFields());
        if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.REFUND_QUANTITY.toString())) {
            sqlBuilder.append(" , sum(case when  coalesce(quantity, 0) < 0 then abs(coalesce(quantity,0)) else 0 end) AS metric ");
            sqlBuilder.append(" , sum(case when refund_status = 'S' THEN coalesce(ticket_price,0) + coalesce(refund_ticket_fee,0) else 0 end) AS loss ");
        } else if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.REBOOK_QUANTITY.toString())) {
            sqlBuilder.append(" , SUM(coalesce(change_quantity, 0)) AS metric ");
            sqlBuilder.append(" ,SUM(coalesce(changebalance, 0)) + SUM(coalesce(deal_change_service_fee, 0)) + SUM(coalesce(afterchangeservicefee, 0)) AS loss ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable()).append(" as current");
        ;
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by metric desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    public <T> List<T> topDeptAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum, Class<T> clazz,
                                       QueryReportBuTypeEnum queryBu, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                       AnalysisObjectEnum downObjectEnum, String downObjectVal) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        JoinCondition biz = joinCondition(analysisObjectEnum, analysisObjectOrgInfo, requestDto.getLang());

        List<Object> parmList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        switch (queryBu) {
            case flight:
                sql.append(fltSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, false, null));
                break;
            case hotel:
                sql.append(htlSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, false, null));
                break;
            case train:
                sql.append(trainSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, false, null));
                break;
            case car:
                sql.append(carSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, null));
                break;
            case overview:
                sql.append(overviewSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, null));
                break;
            case bus:
                sql.append(busSql1(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, null));
                break;
            case vaso:
                sql.append(vasoSql1(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, null));
                break;
            default:
                break;
        }
        // 查询clickhouse
        return commonList(clazz, sql.toString(), parmList);
    }

    public <T> List<T> topDeptUidAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum, Class<T> clazz,
                                          QueryReportBuTypeEnum queryBu, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                          AnalysisObjectEnum downObjectEnum, String downObjectVal, boolean needHotAnaylsis, String user) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        JoinCondition biz = joinCondition(analysisObjectEnum, analysisObjectOrgInfo, requestDto.getLang());

        List<Object> parmList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        switch (queryBu) {
            case flight:
                sql.append(fltSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, needHotAnaylsis, user));
                break;
            case hotel:
                sql.append(htlSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, needHotAnaylsis, user));
                break;
            case train:
                sql.append(trainSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, needHotAnaylsis, user));
                break;
            case car:
                sql.append(carSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, user));
                break;
            case overview:
                sql.append(overviewSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, user));
                break;
            case bus:
                sql.append(busSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, user));
                break;
            case vaso:
                sql.append(vasoSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, downObjectEnum, downObjectVal, user));
                break;
            default:
                break;
        }
        // 查询clickhouse
        return commonList(clazz, sql.toString(), parmList);
    }

    public <T> List<T> topDeptRcPercentAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum, Class<T> clazz,
                                                AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum downObjectEnum, String downObjectVal) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        OnlineReportOverViewJoinCondition.JoinCondition biz = onlineReportOverViewJoinCondition.joinCondition(
                analysisObjectEnum, analysisObjectOrgInfo, requestDto.getLang(), false);
        List<Object> parmList = new ArrayList<>();
        StringBuffer baseSql = new StringBuffer();
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        String orgCondition = getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList);

        Supplier<String> downCondition = () -> downCondition(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum, parmList);

        if (StringUtils.equalsIgnoreCase(requestDto.getProductType(), "inter")) {
            baseSql.append(String.format(OVERVIEW_RC_SQL, biz.getRcFullJoinResultFields(),
                    biz.getResultFields(),
                    BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition + downCondition.get(),
                    getFlightClassConditionWithAudited(requestDto.getProductType()), biz.getGroupFields(),
                    biz.getResultFields(),
                    BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition + downCondition.get(),
                    getHotelOrderTypeCondition(requestDto.getProductType()), biz.getGroupFields(), biz.getFlightFullJoinHotel(),
                    biz.getResultFields(),
                    " account_id = ********",
                    biz.getGroupFields(), biz.getHotelFullJointrain()));
        } else {
            baseSql.append(String.format(OVERVIEW_RC_SQL, biz.getRcFullJoinResultFields(),
                    biz.getResultFields(),
                    BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition + downCondition.get(),
                    getFlightClassConditionWithAudited(requestDto.getProductType()), biz.getGroupFields(),
                    biz.getResultFields(),
                    BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition + downCondition.get(),
                    getHotelOrderTypeCondition(requestDto.getProductType()), biz.getGroupFields(), biz.getFlightFullJoinHotel(),
                    biz.getResultFields(),
                    BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition + downCondition.get(),
                    biz.getGroupFields(), biz.getHotelFullJointrain()));
        }
        // 查询clickhouse
        return commonList(clazz, baseSql.toString(), parmList);
    }

    public List<OverviewTopDeptDTO> topDeptRcPercentAnalysisCorpAndIndustry(String statisticalCaliber, String startTime, String endTime, String productType,
                                                                            List<String> industryList, DataTypeEnum dataTypeEnum,
                                                                            String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuffer baseSql = new StringBuffer();
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        String consumptionlevelSql = "";
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                consumptionlevelSql = " and consumptionlevel = '" + consumptionLevel + "' ";
            }
            if (StringUtils.equalsIgnoreCase(productType, "inter")) {
                baseSql.append(String.format(OVERVIEW_RC_CORP_INDUSTRY_SQL,
                        BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE) +
                                consumptionlevelSql,
                        getFlightClassConditionWithAudited(productType),
                        BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE) +
                                consumptionlevelSql,
                        getHotelOrderTypeCondition(productType),
                        "account_id = ********"));
            } else {
                baseSql.append(String.format(OVERVIEW_RC_CORP_INDUSTRY_SQL,
                        BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE) +
                                consumptionlevelSql,
                        getFlightClassConditionWithAudited(productType),
                        BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE) +
                                consumptionlevelSql,
                        getHotelOrderTypeCondition(productType),
                        BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE) +
                                consumptionlevelSql));
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                consumptionlevelSql = " and consumptionlevel = '" + consumptionLevel + "' ";
            }
            if (StringUtils.equalsIgnoreCase(productType, "inter")) {
                baseSql.append(String.format(OVERVIEW_RC_CORP_INDUSTRY_SQL,
                        BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + consumptionlevelSql,
                        getFlightClassConditionWithAudited(productType),
                        BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + consumptionlevelSql,
                        getHotelOrderTypeCondition(productType),
                        "account_id = ********"));
            } else {
                baseSql.append(String.format(OVERVIEW_RC_CORP_INDUSTRY_SQL,
                        BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + consumptionlevelSql,
                        getFlightClassConditionWithAudited(productType),
                        BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + consumptionlevelSql,
                        getHotelOrderTypeCondition(productType),
                        BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + consumptionlevelSql));
            }
        }
        // 查询clickhouse
        return commonList(OverviewTopDeptDTO.class, baseSql.toString(), parmList);
    }

    public List<OverviewTopDeptDTO> topDeptRcPercentAnalysisDown(BaseQueryConditionDTO requestDto, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                                                 AnalysisObjectEnum downObjectEnum) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuffer baseSql = new StringBuffer();
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        String downCondition = StringUtils.EMPTY;
        if (downObjectEnum != null) {
            downCondition = downCondition(downObjectEnum, analysisObjectOrgInfo);
        }
        baseSql.append(String.format(OVERVIEW_RC_CORP_INDUSTRY_SQL,
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + downCondition,
                getFlightClassConditionWithAudited(requestDto.getProductType()),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + downCondition,
                getHotelOrderTypeCondition(requestDto.getProductType()),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + downCondition));
        // 查询clickhouse
        return commonList(OverviewTopDeptDTO.class, baseSql.toString(), parmList);
    }

    public <T> List<T> topDeptOverAmountAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum, Class<T> clazz,
                                                 AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum downObjectEnum, String downObjectVal) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        OnlineReportOverViewJoinCondition.JoinCondition biz = onlineReportOverViewJoinCondition.joinCondition(
                analysisObjectEnum, analysisObjectOrgInfo, requestDto.getLang(), false);
        List<Object> parmList = new ArrayList<>();
        StringBuffer baseSql = new StringBuffer();

        String orgCondition = getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList);
        String downCondition = downCondition(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum, parmList);


        baseSql.append(String.format(OVERVIEW_OVER_AMOUNT_SQL, biz.getOverAmountFullJoinResultFields(),
                biz.getResultFields(),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, queryPartition(OLRPT_INDEXFLIGHTDOWNLOAD), isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition + downCondition ,
                getFlightClassConditionWithAudited(requestDto.getProductType()), biz.getGroupFields(),
                biz.getResultFields(),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, queryPartition(OLRPT_INDEXHOTELDOWNLOAD), isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition + downCondition,
                getHotelOrderTypeCondition(requestDto.getProductType()), biz.getGroupFields(), biz.getFlightFullJoinHotel()));
        // 查询clickhouse
        return commonList(clazz, baseSql.toString(), parmList);
    }

    public <T> List<T> topDeptCarbonsAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum, Class<T> clazz,
                                              AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum downObjectEnum, String downObjectVal) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        OnlineReportOverViewJoinCondition.JoinCondition biz = onlineReportOverViewJoinCondition.joinCondition(
                analysisObjectEnum, analysisObjectOrgInfo, requestDto.getLang(), false);
        List<Object> parmList = new ArrayList<>();
        StringBuffer baseSql = new StringBuffer();
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String orgCondition = getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList);
        String downCondition = downCondition(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum, parmList);


        String  flightDownload = queryPartition(OLRPT_INDEXFLIGHTDOWNLOAD);
        String  trainDownload = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        String  carDownload = queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD);



        if (StringUtils.equalsIgnoreCase(requestDto.getProductType(), "inter")) {
            baseSql.append(String.format(OVERVIEW_CARBON_SQL, biz.getCarbonsFullJoinResultFields(),
                    biz.getResultFields(),
                    BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, flightDownload, isBookCaliber ? ORDERDT : REPORT_DATE) +orgCondition + downCondition ,
                    getFlightClassConditionWithAudited(requestDto.getProductType()), biz.getGroupFields(),
                    biz.getResultFields(),
                    " account_id = ********9",
                    biz.getGroupFields(), biz.getFlightFullJoinTrain(),
                    biz.getResultFields(),
                    BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, trainDownload, ORDERDT) + orgCondition + downCondition,
                    biz.getGroupFields(), biz.getTrainFullJoinCar()));
        } else {
            baseSql.append(String.format(OVERVIEW_CARBON_SQL, biz.getCarbonsFullJoinResultFields(),
                    biz.getResultFields(),
                    BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, flightDownload, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition + downCondition,
                    getFlightClassConditionWithAudited(requestDto.getProductType()), biz.getGroupFields(),
                    biz.getResultFields(),
                    BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, trainDownload, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition + downCondition,
                    biz.getGroupFields(), biz.getFlightFullJoinTrain(),
                    biz.getResultFields(),
                    BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, carDownload, ORDERDT) + orgCondition + downCondition,
                    biz.getGroupFields(), biz.getTrainFullJoinCar()));
        }
        // 查询clickhouse
        return commonList(clazz, baseSql.toString(), parmList);
    }

    public <T> List<T> topDeptAnalysisCorpAndIndustry(String statisticalCaliber, Class<T> clazz, QueryReportBuTypeEnum queryBu, String startTime, String endTime,
                                                      String productType, List<String> industryList, DataTypeEnum dataTypeEnum,
                                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        switch (queryBu) {
            case flight:
                sql.append(fltSqlCorpAndIndustry(parmList, isBookCaliber, startTime, endTime, productType,
                        industryList, dataTypeEnum, compareSameLevel, consumptionLevel, compareCorpSameLevel));
                break;
            case hotel:
                sql.append(htlSqlCorpAndIndustry(parmList, isBookCaliber, startTime, endTime, productType,
                        industryList, dataTypeEnum, compareSameLevel, consumptionLevel, compareCorpSameLevel));
                break;
            case train:
                sql.append(trainSqlCorpAndIndustry(parmList, isBookCaliber, startTime, endTime, productType,
                        industryList, dataTypeEnum, compareSameLevel, consumptionLevel, compareCorpSameLevel));
                break;
            case car:
                sql.append(carSqlCorpAndIndustry(parmList, isBookCaliber, startTime, endTime, productType,
                        industryList, dataTypeEnum, compareSameLevel, consumptionLevel, compareCorpSameLevel));
                break;
            case overview:
                sql.append(overviewSqlCorpAndIndustry(parmList, isBookCaliber, startTime, endTime, productType,
                        industryList, dataTypeEnum, compareSameLevel, consumptionLevel, compareCorpSameLevel));
                break;
            default:
                break;
        }
        // 查询clickhouse
        return commonList(clazz, sql.toString(), parmList);
    }

    public <T> List<T> topDeptAnalysisDown(BaseQueryConditionDTO requestDto, Class<T> clazz,
                                           QueryReportBuTypeEnum queryBu, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                           AnalysisObjectEnum downObjectEnum) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        switch (queryBu) {
            case flight:
                sql.append(fltSqlDown(requestDto, parmList, isBookCaliber, analysisObjectOrgInfo, downObjectEnum));
                break;
            case hotel:
                sql.append(htlSqlDown(requestDto, parmList, isBookCaliber, analysisObjectOrgInfo, downObjectEnum));
                break;
            case train:
                sql.append(trainSqlDown(requestDto, parmList, isBookCaliber, analysisObjectOrgInfo, downObjectEnum));
                break;
            case car:
                sql.append(carSqlDown(requestDto, parmList, isBookCaliber, analysisObjectOrgInfo, downObjectEnum));
                break;
            case overview:
                sql.append(overviewSqlDown(requestDto, parmList, isBookCaliber, analysisObjectOrgInfo, downObjectEnum));
                break;
            default:
                break;
        }
        // 查询clickhouse
        return commonList(clazz, sql.toString(), parmList);
    }

    private String fltSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber,
                          AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum analysisObjectEnum,
                          AnalysisObjectEnum downObjectEnum, String downObjectVal, boolean needHotAnaylsis, String user) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(biz.getSpecialResultFields()).append(",");
        // 消费金额
        sqlBuilder.append("sum(coalesce(real_pay, 0)) as totalAmount,");
        // 票张数
        sqlBuilder.append("sum(coalesce(quantity, 0)) as totalQuantity,");
        // 成交净价（经济舱）
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(price, 0), 0)) as totalEconmyPrice,");
        // 总里程（经济舱）计算里程均价
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(tpms, 0), 0)) as totalEconmyTpms,");
        // 总票张（经济舱）计算平均票价
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(quantity, 0), 0)) as totalEconmyQuantity,");
        // 总折扣（国内、经济舱）计算平均折扣
        sqlBuilder.append("SUM(if(class_type = 'Y' and flight_class = 'N', coalesce(price_rate, 0) * coalesce(quantity, 0), 0)) as totalDomEconmyDiscount,");
        // 总票张（国内、经济舱）计算平均折扣
        sqlBuilder.append("SUM(if(class_type = 'Y' and flight_class = 'N', coalesce(quantity, 0), 0)) as totalDomEconmyQuantity,");
        // 全价票张
        sqlBuilder.append("sum(coalesce(fullfaretkt, 0)) as totalFullfaretkt,");
        // 全价票张（国内）计算全价票张占比
        sqlBuilder.append("SUM(if( flight_class = 'N', coalesce(fullfaretkt, 0), 0)) as totalDomEconmyFullfaretkt,");
        // 出票张数（国内）计算全价票张占比
        sqlBuilder.append("SUM(if( flight_class = 'N', coalesce(ordertkt, 0), 0)) as totalDomEconmyOrdertkt,");
        // 退票费
        sqlBuilder.append("sum(coalesce(refund_fee, 0)) as totalRefundFee,");
        // 退票张数
        sqlBuilder.append("sum(case when is_refund = 'T' then coalesce(refundtkt, 0) else 0 end ) as totalRefundtkt,");
        // 改签费
        sqlBuilder.append("sum(coalesce(change_fee, 0)) as totalRebookFee,");
        // 改签张数
        sqlBuilder.append("sum(case when is_rebook = 'T' then coalesce(quantity, 0) else 0 end) as totalRebooktkt,");
        // 出票张数
        sqlBuilder.append("sum(coalesce(ordertkt, 0)) as totalOrdertkt,");
        // 超标损失
        sqlBuilder.append("sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as totalOverAmount,");
        // rc次数
        sqlBuilder.append("COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS totalRcTimes,");
        // 总订单数(出票)
        sqlBuilder.append("COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS totalOrderCount,");
        // 三方节省金额
        sqlBuilder.append("sum(CASE WHEN (flight_class = 'N' and class_type = 'Y') or flight_class = 'I' THEN coalesce(00000, 0) ELSE 0 END) AS totalSaveAmount3c, ");
        // 三方成交净价
        sqlBuilder.append("sum(case when (class_type = 'Y' and flight_class = 'N') or (flight_class = 'I') then coalesce(0000, 0) else 0 end) AS totalNetfare3c, ");
        // 两方节省金额
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) AS totalSaveAmountPremium, ");
        // 两方成交净价
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_premium, 0) else 0 end) AS totalNetfarePremium, ");
        // 管控节省金额
        sqlBuilder.append("SUM(coalesce(saving_price, 0)) AS totalControlSave, ");
        // 管控成交净价
        sqlBuilder.append("sum(case when coalesce(saving_price, 0) != 0 then coalesce(netfare, 0) else 0 end) AS totalControlNetfare, ");
        // 碳排放
        sqlBuilder.append("SUM(coalesce(carbon_emission, 0)) AS totalCarbons, ");
        // 碳排放中位数
        sqlBuilder.append("SUM(coalesce(median_carbon_emission, 0)) AS totalMedianCarbons, ");
        sqlBuilder.append("sum(if(median_carbon_emission <> 0 AND carbon_emission <> 0, median_carbon_emission - carbon_emission, 0)) as totalCarbonSave");

        if (needHotAnaylsis) {
            // 退票损失
            sqlBuilder.append(", SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0)) + SUM(coalesce(refund_behind_service_fee, 0)) " +
                    "as totalRefundloss \n");
            // 改签损失
            sqlBuilder.append(", SUM(coalesce(change_fee,0)) + SUM(coalesce(rebook_price_differential,0)) " +
                    "+ SUM(if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0))) " +
                    "+ SUM(coalesce(rebook_service_fee,0)) + SUM(coalesce(rebook_behind_service_fee,0)) as totalRebookloss \n");
            // 如果查国际的数据就不限制flight_class
            if (!StringUtils.equalsIgnoreCase(requestDto.getProductType(), "inter")) {
                // 国内
                sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(pre_order_date, 0) * coalesce(quantity, 0) else 0 end) " +
                        "as totalPreOrderDate");
                sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(quantity, 0) else 0 end) as totalPreOrderDateQuantity");
            } else {
                sqlBuilder.append(", sum(case when class_type = 'Y' then coalesce(pre_order_date, 0) * coalesce(quantity, 0) else 0 end)  as totalPreOrderDate");
                sqlBuilder.append(", sum(case when class_type = 'Y' then coalesce(quantity, 0) else 0 end) as totalPreOrderDateQuantity");
            }
            // 总里程计算里程碳排
            sqlBuilder.append(", SUM(if(coalesce(carbon_emission, 0) != 0, coalesce(tpms, 0), 0)) as totalCarbonsTpms ");
            // 总订单数
            sqlBuilder.append(", COUNT(DISTINCT order_id) AS totalAllOrderCount \n");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXFLIGHTDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(downCondition(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(buildPreSqlUser(user, parmList));
        Map map = Optional.ofNullable(requestDto.getOtherParams()).orElse(new HashMap<>());
        String uidStr = (String) map.get("uids");
        if (StringUtils.isNotEmpty(uidStr)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("uid", Arrays.asList(uidStr.split(",")), parmList));
        }
        sqlBuilder.append(" group by ").append(biz.getSpecialGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    public <T> List<T> getHotFlightCitySql(BaseQueryConditionDTO requestDto, List<String> uids, Class<T> clazz, String lang) throws Exception {
        List<Object> paramList = new ArrayList<>();
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        // flight_city_en, flight_city
        String dimName = SharkUtils.isZH(lang) ? " flight_city" : " flight_city_en ";
        sqlBuilder.append("select uid, " + dimName + " as topTarget , count(DISTINCT order_id) as countSort ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("uid", uids, paramList));
        sqlBuilder.append(" GROUP BY uid," + dimName);
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    private String fltSqlCorpAndIndustry(List<Object> parmList, boolean isBookCaliber, String startTime, String endTime, String productType,
                                         List<String> industryList, DataTypeEnum dataTypeEnum,
                                         String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        // 成交净价（经济舱）
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(price, 0), 0)) as totalEconmyPrice,");
        // 总里程（经济舱）计算里程均价
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(tpms, 0), 0)) as totalEconmyTpms,");
        // 总票张（经济舱）计算平均票价
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(quantity, 0), 0)) as totalEconmyQuantity,");
        // 总折扣（国内、经济舱）计算平均折扣
        sqlBuilder.append("SUM(if(class_type = 'Y' and flight_class = 'N', coalesce(price_rate, 0) * coalesce(quantity, 0), 0)) as totalDomEconmyDiscount,");
        // 总票张（国内、经济舱）计算平均折扣
        sqlBuilder.append("SUM(if(class_type = 'Y' and flight_class = 'N', coalesce(quantity, 0), 0)) as totalDomEconmyQuantity,");
        // 全价票张
        sqlBuilder.append("sum(coalesce(fullfaretkt, 0)) as totalFullfaretkt,");
        // 全价票张（国内）计算全价票张占比
        sqlBuilder.append("SUM(if( flight_class = 'N', coalesce(fullfaretkt, 0), 0)) as totalDomEconmyFullfaretkt,");
        // 出票张数（国内）计算全价票张占比
        sqlBuilder.append("SUM(if( flight_class = 'N', coalesce(ordertkt, 0), 0)) as totalDomEconmyOrdertkt,");
        // 退票张数
        sqlBuilder.append("sum(case when is_refund = 'T' then coalesce(refundtkt, 0) else 0 end ) as totalRefundtkt,");
        // 改签张数
        sqlBuilder.append("sum(case when is_rebook = 'T' then coalesce(quantity, 0) else 0 end) as totalRebooktkt,");
        // 出票张数
        sqlBuilder.append("sum(coalesce(ordertkt, 0)) as totalOrdertkt,");
        // 超标损失
        sqlBuilder.append("sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as totalOverAmount,");
        // rc次数
        sqlBuilder.append("COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS totalRcTimes,");
        // 总订单数
        sqlBuilder.append("COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS totalOrderCount,");
        // 三方节省金额
        sqlBuilder.append("sum(CASE WHEN (flight_class = 'N' and class_type = 'Y') or flight_class = 'I' THEN coalesce(00000, 0) ELSE 0 END) AS totalSaveAmount3c, ");
        // 三方成交净价
        sqlBuilder.append("sum(case when (class_type = 'Y' and flight_class = 'N') or (flight_class = 'I') then coalesce(0000, 0) else 0 end) AS totalNetfare3c, ");
        // 两方节省金额
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) AS totalSaveAmountPremium, ");
        // 两方成交净价
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_premium, 0) else 0 end) AS totalNetfarePremium, ");
        // 管控节省金额
        sqlBuilder.append("SUM(coalesce(saving_price, 0)) AS totalControlSave, ");
        // 管控成交净价
        sqlBuilder.append("sum(case when coalesce(saving_price, 0) != 0 then coalesce(netfare, 0) else 0 end) AS totalControlNetfare, ");
        // 碳排放
        sqlBuilder.append("SUM(coalesce(carbon_emission, 0)) AS totalCarbons, ");
        // 碳排放中位数
        sqlBuilder.append("SUM(coalesce(median_carbon_emission, 0)) AS totalMedianCarbons, ");
        sqlBuilder.append("sum(if(median_carbon_emission <> 0 AND carbon_emission <> 0, median_carbon_emission - carbon_emission, 0)) as totalCarbonSave");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXFLIGHTDOWNLOAD);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (org.apache.commons.lang.StringUtils.isNotEmpty(compareSameLevel) && org.apache.commons.lang.StringUtils.equalsIgnoreCase(compareSameLevel, "T")
                    && org.apache.commons.lang.StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (org.apache.commons.lang.StringUtils.isNotEmpty(compareCorpSameLevel) && org.apache.commons.lang.StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T")
                    && org.apache.commons.lang.StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        return sqlBuilder.toString();
    }

    private String fltSqlDown(BaseQueryConditionDTO requestDto, List<Object> parmList, boolean isBookCaliber,
                              AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum downObjectEnum) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        // 消费金额
        sqlBuilder.append("sum(coalesce(real_pay, 0)) as totalAmount,");
        // 票张数
        sqlBuilder.append("sum(coalesce(quantity, 0)) as totalQuantity,");
        // 成交净价（经济舱）
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(price, 0), 0)) as totalEconmyPrice,");
        // 总里程（经济舱）计算里程均价
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(tpms, 0), 0)) as totalEconmyTpms,");
        // 总票张（经济舱）计算平均票价
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(quantity, 0), 0)) as totalEconmyQuantity,");
        // 总折扣（国内、经济舱）计算平均折扣
        sqlBuilder.append("SUM(if(class_type = 'Y' and flight_class = 'N', coalesce(price_rate, 0) * coalesce(quantity, 0), 0)) as totalDomEconmyDiscount,");
        // 总票张（国内、经济舱）计算平均折扣
        sqlBuilder.append("SUM(if(class_type = 'Y' and flight_class = 'N', coalesce(quantity, 0), 0)) as totalDomEconmyQuantity,");
        // 全价票张
        sqlBuilder.append("sum(coalesce(fullfaretkt, 0)) as totalFullfaretkt,");
        // 全价票张（国内）计算全价票张占比
        sqlBuilder.append("SUM(if( flight_class = 'N', coalesce(fullfaretkt, 0), 0)) as totalDomEconmyFullfaretkt,");
        // 出票张数（国内）计算全价票张占比
        sqlBuilder.append("SUM(if( flight_class = 'N', coalesce(ordertkt, 0), 0)) as totalDomEconmyOrdertkt,");
        // 退票费
        sqlBuilder.append("sum(coalesce(refund_fee, 0)) as totalRefundFee,");
        // 退票张数
        sqlBuilder.append("sum(case when is_refund = 'T' then coalesce(refundtkt, 0) else 0 end ) as totalRefundtkt,");
        // 改签费
        sqlBuilder.append("sum(coalesce(change_fee, 0)) as totalRebookFee,");
        // 改签张数
        sqlBuilder.append("sum(case when is_rebook = 'T' then coalesce(quantity, 0) else 0 end) as totalRebooktkt,");
        // 出票张数
        sqlBuilder.append("sum(coalesce(ordertkt, 0)) as totalOrdertkt,");
        // 超标损失
        sqlBuilder.append("sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as totalOverAmount,");
        // rc次数
        sqlBuilder.append("COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS totalRcTimes,");
        // 总订单数
        sqlBuilder.append("COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS totalOrderCount,");
        // 三方节省金额
        sqlBuilder.append("sum(CASE WHEN (flight_class = 'N' and class_type = 'Y') or flight_class = 'I' THEN coalesce(00000, 0) ELSE 0 END) AS totalSaveAmount3c, ");
        // 三方成交净价
        sqlBuilder.append("sum(case when (class_type = 'Y' and flight_class = 'N') or (flight_class = 'I') then coalesce(0000, 0) else 0 end) AS totalNetfare3c, ");
        // 两方节省金额
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) AS totalSaveAmountPremium, ");
        // 两方成交净价
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_premium, 0) else 0 end) AS totalNetfarePremium, ");
        // 管控节省金额
        sqlBuilder.append("SUM(coalesce(saving_price, 0)) AS totalControlSave, ");
        // 管控成交净价
        sqlBuilder.append("sum(case when coalesce(saving_price, 0) != 0 then coalesce(netfare, 0) else 0 end) AS totalControlNetfare, ");
        // 碳排放
        sqlBuilder.append("SUM(coalesce(carbon_emission, 0)) AS totalCarbons, ");
        // 碳排放中位数
        sqlBuilder.append("SUM(coalesce(median_carbon_emission, 0)) AS totalMedianCarbons, ");
        sqlBuilder.append("sum(if(median_carbon_emission <> 0 AND carbon_emission <> 0, median_carbon_emission - carbon_emission, 0)) as totalCarbonSave");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXFLIGHTDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        if (downObjectEnum != null) {
            sqlBuilder.append(downCondition(downObjectEnum, analysisObjectOrgInfo));
        }
        return sqlBuilder.toString();
    }

    private String htlSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                          AnalysisObjectEnum analysisObjectEnum, AnalysisObjectEnum downObjectEnum, String downObjectVal, boolean neeDhotAnaylsis, String user) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ").append(biz.getSpecialResultFields());
        // 消费金额
        sqlBuilder.append(",sum(coalesce(real_pay_with_servicefee, 0)) as totalAmount");
        // 票张数
        sqlBuilder.append(",sum(coalesce(quantity, 0)) as totalQuantity");
        // 房价
        sqlBuilder.append(",sum(coalesce(room_price, 0)) as totalRoomPrice");

        // 协议
        sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) else 0 end) AS totalAmount_ta  \n");
        sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 end) AS totalQuantity_ta  \n");
        sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(room_price, 0) else 0 end) AS totalRoomPrice_ta  \n");
        // 非协议
        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(real_pay, 0) else 0 end) AS totalAmount_nta  \n");
        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(quantity, 0) else 0 end) AS totalQuantity_nta  \n");
        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(room_price, 0) else 0 end) AS totalRoomPrice_nta  \n");

        // 国内
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'F' or is_oversea = 'O') THEN coalesce(real_pay, 0) else 0 end) AS totalAmount_dom  \n");
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'F' or is_oversea = 'O') THEN coalesce(quantity, 0) else 0 end) AS totalQuantity_dom  \n");
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'F' or is_oversea = 'O') THEN coalesce(room_price, 0) else 0 end) AS totalRoomPrice_dom  \n");
        // 海外
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'T' ) THEN coalesce(real_pay, 0) else 0 end) AS totalAmount_inter  \n");
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'T' ) THEN coalesce(quantity, 0) else 0 end) AS totalQuantity_inter  \n");
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'T') THEN coalesce(room_price, 0) else 0 end) AS totalRoomPrice_inter  \n");
        // 超标损失
        sqlBuilder.append(", SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as totalOverAmount");
        // rc次数
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS totalRcTimes \n");
        // 总订单数（出）
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS totalOrderCount \n");
        // 三方协议的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_3c, 0)) as totalSaveAmount3c \n");
        sqlBuilder.append(", SUM(CASE WHEN (producttype_all = '" + ta + "' and save_amount_3c is not null) then corp_real_pay else 0 end) as totalCorpRealPay3c \n");
        // 两方尊享的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_premium, 0)) as totalSaveAmountPremium \n");
        sqlBuilder.append(", SUM(CASE WHEN (producttype_all = '" + premium + "' and save_amount_premium is not null) then corp_real_pay else 0 end) as totalCorpRealPayPremium\n");

        // 促销优惠活动的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_promotion, 0)) as totalSaveAmountPromotion \n");
        sqlBuilder.append(", SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then corp_real_pay else 0 end) as totalCorpRealPayPromotion  \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as totalControlSave \n");
        sqlBuilder.append(", sum(case when coalesce(saving_price, 0) != 0 then coalesce(corp_real_pay, 0) else 0 end) as totalControlCorpRealPay  \n");

        if (neeDhotAnaylsis) {
            // 取消损失节省金额
            sqlBuilder.append(", sum(coalesce(cancel_esti_save_amount,0)) as totalRefundloss\n");
            /*Top酒店城市*/
            /*最常入住星级*/
            String shareRoom = String.format(" houseshare_mode_type in ('%s', '%s', '%s') ",
                    OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.room"),
                    OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.travel"),
                    OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.all")
            );
            /*差标使用率*/
            sqlBuilder.append(", SUM(coalesce(dead_price_onenight, 0)*coalesce(quantity, 0)) AS totalDeadPriceOnenightQantiy\n");
            sqlBuilder.append(", SUM(coalesce(quantity, 0) * CASE WHEN " + shareRoom + " THEN persons ELSE 1 END) AS totalPersonsQuantity\n");
            /*取消率*/
            sqlBuilder.append(", SUM(CASE WHEN coalesce(quantity, 0) < 0 then -1 * quantity  else 0 end) as totalCancelQuaity  \n");
            sqlBuilder.append(", SUM(CASE WHEN coalesce(quantity, 0) > 0 then  quantity  else 0 end) as totalAllQuaity  \n");
            // 总订单数
            sqlBuilder.append(", COUNT(DISTINCT order_id) AS totalAllOrderCount \n");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXHOTELDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(downCondition(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(buildPreSqlUser(user, parmList));
        Map map = Optional.ofNullable(requestDto.getOtherParams()).orElse(new HashMap<>());
        String uidStr = (String) map.get("uids");
        if (StringUtils.isNotEmpty(uidStr)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("uid", Arrays.asList(uidStr.split(",")), parmList));
        }
        sqlBuilder.append(" group by ").append(biz.getSpecialGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    public <T> List<T> getHotHotelCitySql(BaseQueryConditionDTO requestDto, List<String> uids, Class<T> clazz, String lang) throws Exception {
        List<Object> paramList = new ArrayList<>();
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        String dimName = SharkUtils.isZH(lang) ? " city_name" : " city_name_en ";
        sqlBuilder.append("select uid, " + dimName + " as topTarget , count(DISTINCT order_id) as countSort ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("uid", uids, paramList));
        sqlBuilder.append(" GROUP BY uid," + dimName);
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    public <T> List<T> getHotHotelStarSql(BaseQueryConditionDTO requestDto, List<String> uids, Class<T> clazz) throws Exception {
        List<Object> paramList = new ArrayList<>();
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT uid, star as topTarget, count(DISTINCT order_id) AS countSort");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("uid", uids, paramList));
        sqlBuilder.append(" group by uid,star");
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    private String htlSqlCorpAndIndustry(List<Object> parmList, boolean isBookCaliber, String startTime, String endTime, String productType,
                                         List<String> industryList, DataTypeEnum dataTypeEnum,
                                         String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT sum(coalesce(real_pay_with_servicefee, 0)) as totalAmount");
        // 票张数
        sqlBuilder.append(",sum(coalesce(quantity, 0)) as totalQuantity");
        // 房价
        sqlBuilder.append(",sum(coalesce(room_price, 0)) as totalRoomPrice");

        // 超标损失
        sqlBuilder.append(", SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as totalOverAmount");
        // rc次数
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS totalRcTimes \n");
        // 总订单数
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS totalOrderCount \n");
        // 三方协议的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_3c, 0)) as totalSaveAmount3c \n");
        sqlBuilder.append(", SUM(CASE WHEN (producttype_all = '" + ta + "' and save_amount_3c is not null) then corp_real_pay else 0 end) as totalCorpRealPay3c \n");
        // 两方尊享的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_premium, 0)) as totalSaveAmountPremium \n");
        sqlBuilder.append(", SUM(CASE WHEN (producttype_all = '" + premium + "' and save_amount_premium is not null) then corp_real_pay else 0 end) as totalCorpRealPayPremium \n");

        // 促销优惠活动的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_promotion, 0)) as totalSaveAmountPromotion \n");
        sqlBuilder.append(", SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then corp_real_pay else 0 end) as totalCorpRealPayPromotion  \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as totalControlSave \n");
        sqlBuilder.append(", sum(case when coalesce(saving_price, 0) != 0 then coalesce(corp_real_pay, 0) else 0 end) as totalControlCorpRealPay  \n");

        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXHOTELDOWNLOAD);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (org.apache.commons.lang.StringUtils.isNotEmpty(compareSameLevel) && org.apache.commons.lang.StringUtils.equalsIgnoreCase(compareSameLevel, "T")
                    && org.apache.commons.lang.StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (org.apache.commons.lang.StringUtils.isNotEmpty(compareCorpSameLevel) && org.apache.commons.lang.StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T")
                    && org.apache.commons.lang.StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        sqlBuilder.append(getHotelOrderTypeCondition(productType));
        return sqlBuilder.toString();
    }

    private String htlSqlDown(BaseQueryConditionDTO requestDto, List<Object> parmList, boolean isBookCaliber,
                              AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum downObjectEnum) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT sum(coalesce(real_pay_with_servicefee, 0)) as totalAmount");
        // 票张数
        sqlBuilder.append(",sum(coalesce(quantity, 0)) as totalQuantity");
        // 房价
        sqlBuilder.append(",sum(coalesce(room_price, 0)) as totalRoomPrice");

        // 超标损失
        sqlBuilder.append(", SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as totalOverAmount");
        // rc次数
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS totalRcTimes \n");
        // 总订单数
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS totalOrderCount \n");
        // 三方协议的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_3c, 0)) as totalSaveAmount3c \n");
        sqlBuilder.append(", SUM(CASE WHEN (producttype_all = '" + ta + "' and save_amount_3c is not null) then corp_real_pay else 0 end) as totalCorpRealPay3c \n");
        // 两方尊享的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_premium, 0)) as totalSaveAmountPremium \n");
        sqlBuilder.append(", SUM(CASE WHEN (producttype_all = '" + premium + "' and save_amount_premium is not null) then corp_real_pay else 0 end) as totalCorpRealPayPremium \n");

        // 促销优惠活动的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_promotion, 0)) as totalSaveAmountPromotion \n");
        sqlBuilder.append(", SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then corp_real_pay else 0 end) as totalCorpRealPayPromotion  \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as totalControlSave \n");
        sqlBuilder.append(", sum(case when coalesce(saving_price, 0) != 0 then coalesce(corp_real_pay, 0) else 0 end) as totalControlCorpRealPay  \n");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXHOTELDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        if (downObjectEnum != null) {
            sqlBuilder.append(downCondition(downObjectEnum, analysisObjectOrgInfo));
        }
        return sqlBuilder.toString();
    }

    private String trainSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                            AnalysisObjectEnum analysisObjectEnum, AnalysisObjectEnum downObjectEnum, String downObjectVal, boolean needHotAnaylsis, String user) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(biz.getSpecialResultFields());
        // 消费金额
        sqlBuilder.append(",sum(coalesce(real_pay, 0)) as totalAmount");
        sqlBuilder.append(",SUM(coalesce(ticket_price,0)+coalesce(changebalance,0)+coalesce(refund_ticket_fee,0)) as totalPrice");
        // 票张数
        sqlBuilder.append(",sum(coalesce(quantity, 0)) as totalQuantity");
        // 退票张数
        sqlBuilder.append(", sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as totalRefundtkt ");
        // 改签张数
        sqlBuilder.append(", sum(coalesce(change_quantity,0)) as totalRebooktkt");
        // 出票张数
        sqlBuilder.append(", sum(case when  quantity > 0 then abs(coalesce(quantity,0)) else 0 end) as totalOrdertkt ");
        if (needHotAnaylsis) {
            // 总订单数
            sqlBuilder.append(", COUNT(DISTINCT order_id) AS totalAllOrderCount \n");
            // 碳排放
            sqlBuilder.append(", SUM(coalesce(carbon_emission, 0)) AS totalCarbons ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(downCondition(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(buildPreSqlUser(user, parmList));
        Map map = Optional.ofNullable(requestDto.getOtherParams()).orElse(new HashMap<>());
        String uidStr = (String) map.get("uids");
        if (StringUtils.isNotEmpty(uidStr)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("uid", Arrays.asList(uidStr.split(",")), parmList));
        }
        sqlBuilder.append(" group by ").append(biz.getSpecialGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    public <T> List<T> getHotTrainLineCitySql(BaseQueryConditionDTO requestDto, List<String> uids, Class<T> clazz, String lang) throws Exception {
        List<Object> paramList = new ArrayList<>();
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        String dimName = SharkUtils.isZH(lang) ? " line_city" : " line_city_en ";
        sqlBuilder.append("select uid, " + dimName + " as topTarget , count(DISTINCT order_id) as countSort ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("uid", uids, paramList));
        sqlBuilder.append(" GROUP BY uid," + dimName);
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    public <T> List<T> getHotTrainSeatSql(BaseQueryConditionDTO requestDto, List<String> uids, Class<T> clazz, String lang) throws Exception {
        List<Object> paramList = new ArrayList<>();
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        String dimName = SharkUtils.isZH(lang) ? " first_seat_type_name" : " first_seat_type_name_en ";
        sqlBuilder.append("select uid, " + dimName + " as topTarget , count(DISTINCT order_id) as countSort ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("uid", uids, paramList));
        sqlBuilder.append(" GROUP BY uid," + dimName);
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    private String trainSqlCorpAndIndustry(List<Object> parmList, boolean isBookCaliber, String startTime, String endTime, String productType,
                                           List<String> industryList, DataTypeEnum dataTypeEnum,
                                           String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT sum(coalesce(real_pay, 0)) as totalAmount");
        sqlBuilder.append(",SUM(coalesce(ticket_price,0)+coalesce(changebalance,0)+coalesce(refund_ticket_fee,0)) as totalPrice");
        // 票张数
        sqlBuilder.append(",sum(coalesce(quantity, 0)) as totalQuantity");
        // 退票张数
        sqlBuilder.append(", sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as totalRefundtkt ");
        // 改签张数
        sqlBuilder.append(", sum(coalesce(change_quantity,0)) as totalRebooktkt");
        // 出票张数
        sqlBuilder.append(", sum(case when  quantity > 0 then abs(coalesce(quantity,0)) else 0 end) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        sqlBuilder.append(getTrainOrderStatusCondition());
        return sqlBuilder.toString();
    }

    private String trainSqlDown(BaseQueryConditionDTO requestDto, List<Object> parmList, boolean isBookCaliber,
                                AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum downObjectEnum) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT sum(coalesce(real_pay, 0)) as totalAmount");
        sqlBuilder.append(",SUM(coalesce(ticket_price,0)+coalesce(changebalance,0)+coalesce(refund_ticket_fee,0)) as totalPrice");
        // 票张数
        sqlBuilder.append(",sum(coalesce(quantity, 0)) as totalQuantity");
        // 退票张数
        sqlBuilder.append(", sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as totalRefundtkt ");
        // 改签张数
        sqlBuilder.append(", sum(coalesce(change_quantity,0)) as totalRebooktkt");
        // 出票张数
        sqlBuilder.append(", sum(case when  quantity > 0 then abs(coalesce(quantity,0)) else 0 end) as totalOrdertkt ");
        // rc次数
        sqlBuilder.append(",COUNT(DISTINCT CASE  WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id END) AS totalRcTimes");
        // 总订单数
        sqlBuilder.append(",COUNT(DISTINCT CASE WHEN refund_status <> 'S' OR quantity = 1 THEN order_id END) AS totalOrderCount");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getTrainOrderStatusCondition());
        if (downObjectEnum != null) {
            sqlBuilder.append(downCondition(downObjectEnum, analysisObjectOrgInfo));
        }
        return sqlBuilder.toString();
    }

    private String carSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> paramList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                          AnalysisObjectEnum analysisObjectEnum, AnalysisObjectEnum downObjectEnum, String downObjectVal, String user) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(biz.getSpecialResultFields());
        // 用车类型, airportpick接送机(order_type IN (1,2)),Charter包车(order_type = 3),rent租车(order_type = 4),tax打车(order_type = 6)
        sqlBuilder.append(", SUM(coalesce(real_pay, 0)) as totalAmount \n");
        sqlBuilder.append(", SUM(if(order_type = 1,coalesce(real_pay, 0),0)) as totalAmountAirportpickDom \n");
        sqlBuilder.append(", SUM(if(order_type = 2,coalesce(real_pay, 0),0)) as totalAmountAirportpickInter \n");
        sqlBuilder.append(", SUM(if(order_type = 3,coalesce(real_pay, 0),0)) as totalAmountCharter \n");
        sqlBuilder.append(", SUM(if(order_type = 4,coalesce(real_pay, 0),0)) as totalAmountRent \n");
        sqlBuilder.append(", SUM(if(order_type = 6,coalesce(real_pay, 0),0)) as totalAmountTax \n");
        sqlBuilder.append(", SUM(cnt_order) as totalOrderCount \n");
        sqlBuilder.append(", SUM(normal_distance) as totalNormalDistance \n");
        // 总里程计算里程碳排
        sqlBuilder.append(", SUM(if(coalesce(carbon_emission, 0) != 0, coalesce(normal_distance, 0), 0)) as totalCarbonsTpms ");
        // 碳排放
        sqlBuilder.append(", SUM(coalesce(carbon_emission, 0)) AS totalCarbons ");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList,
                queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD), ORDERDT));
        sqlBuilder.append(getCarProductTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, paramList));
        sqlBuilder.append(downCondition(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum, paramList));
        sqlBuilder.append(buildPreSqlUser(user, paramList));
        Map map = Optional.ofNullable(requestDto.getOtherParams()).orElse(new HashMap<>());
        String uidStr = (String) map.get("uids");
        if (StringUtils.isNotEmpty(uidStr)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("uid", Arrays.asList(uidStr.split(",")), paramList));
        }
        sqlBuilder.append(" group by ").append(biz.getSpecialGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    /**
     * 用车类型查询条件
     *
     * @param productType
     * @return
     */
    protected String getCarProductTypeCondition(String productType) {
        StringBuilder stringBuilder = new StringBuilder();
        // 国内
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            stringBuilder.append(" and (order_type in(1,3,4) or (order_type = 6 and sub_product_line = '1')) ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            stringBuilder.append(" and (order_type in(2,18) or (order_type = 6 and sub_product_line = 'CAR_TAXI_INTL')) ");
        }
        return stringBuilder.toString();
    }

    private String busSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> paramList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                          AnalysisObjectEnum analysisObjectEnum, AnalysisObjectEnum downObjectEnum, String downObjectVal, String user) {
        // 因公
        String corpPayType = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.corpPayType");
        // 已购票
        String orderstatusdesc = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Bus.orderstatusdesc");
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(" current.uid as aggId, current.username as aggType");
        sqlBuilder.append(", SUM(coalesce(realpay, 0)) as totalAmount \n");
        sqlBuilder.append(", count(distinct orderid) as totalOrderCount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.BUS_ORDERDETAIL.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        paramList.add(queryPartition(ClickHouseTable.BUS_ORDERDETAIL));
        sqlBuilder.append(" and isself = '" + corpPayType + "' and orderstatusdesc = '" + orderstatusdesc + "' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corpid", requestDto.getCorpIds(), paramList));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("accountid", requestDto.getAccountIds(), paramList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(paramList, requestDto.getStartTime(), requestDto.getEndTime(), "orderdate_dt"));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, paramList));
        sqlBuilder.append(downConditionWithSpecialCol(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum,
                QueryReportBuTypeEnum.bus, paramList));
        sqlBuilder.append(buildBusPreSqlUser(user, paramList));
        Map map = Optional.ofNullable(requestDto.getOtherParams()).orElse(new HashMap<>());
        String uidStr = (String) map.get("uids");
        if (StringUtils.isNotEmpty(uidStr)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("uid", Arrays.asList(uidStr.split(",")), paramList));
        }
        sqlBuilder.append(" group by ").append("uid, username ");
        sqlBuilder.append(" order by totalAmount desc").append(",").append("uid, username ");
        return sqlBuilder.toString();
    }

    private String vasoSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> paramList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                           AnalysisObjectEnum analysisObjectEnum, AnalysisObjectEnum downObjectEnum, String downObjectVal, String user) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(" current.uid as aggId, current.corpcust_custname as aggType ");
        sqlBuilder.append(", SUM(coalesce(price, 0)) as totalAmount \n");
        sqlBuilder.append(", count(distinct vaso_id) as totalOrderCount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.VASO_ORDERDETAIL.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        paramList.add(queryPartition(ClickHouseTable.VASO_ORDERDETAIL));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("company", requestDto.getCorpIds(), paramList));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("accountid", requestDto.getAccountIds(), paramList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(paramList, requestDto.getStartTime(), requestDto.getEndTime(), "orderdate_dt"));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, paramList));
        sqlBuilder.append(downConditionWithSpecialCol(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum,
                QueryReportBuTypeEnum.vaso, paramList));
        sqlBuilder.append(buildVasoPreSqlUser(user, paramList));
        Map map = Optional.ofNullable(requestDto.getOtherParams()).orElse(new HashMap<>());
        String uidStr = (String) map.get("uids");
        if (StringUtils.isNotEmpty(uidStr)) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("uid", Arrays.asList(uidStr.split(",")), paramList));
        }
        sqlBuilder.append(" group by ").append(" uid, corpcust_custname ");
        sqlBuilder.append(" order by totalAmount desc").append(",").append("uid, corpcust_custname ");
        return sqlBuilder.toString();
    }

    private String carSqlCorpAndIndustry(List<Object> parmList, boolean isBookCaliber, String startTime, String endTime, String productType,
                                         List<String> industryList, DataTypeEnum dataTypeEnum,
                                         String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        // 用车类型, airportpick接送机(order_type IN (1,2)),Charter包车(order_type = 3),rent租车(order_type = 4),tax打车(order_type = 6)
        sqlBuilder.append("  SUM(coalesce(real_pay, 0)) as totalAmount \n");
        sqlBuilder.append(", SUM(if(order_type = 1,coalesce(real_pay, 0),0)) as totalAmountAirportpickDom \n");
        sqlBuilder.append(", SUM(if(order_type = 2,coalesce(real_pay, 0),0)) as totalAmountAirportpickInter \n");
        sqlBuilder.append(", SUM(if(order_type = 3,coalesce(real_pay, 0),0)) as totalAmountCharter \n");
        sqlBuilder.append(", SUM(if(order_type = 4,coalesce(real_pay, 0),0)) as totalAmountRent \n");
        sqlBuilder.append(", SUM(if(order_type = 6,coalesce(real_pay, 0),0)) as totalAmountTax \n");
        sqlBuilder.append(", count(distinct order_id) as totalOrderCount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        return sqlBuilder.toString();
    }

    private String carSqlDown(BaseQueryConditionDTO requestDto, List<Object> paramList, boolean isBookCaliber,
                              AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum downObjectEnum) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        // 用车类型, airportpick接送机(order_type IN (1,2)),Charter包车(order_type = 3),rent租车(order_type = 4),tax打车(order_type = 6)
        sqlBuilder.append("  SUM(coalesce(real_pay, 0)) as totalAmount \n");
        sqlBuilder.append(", SUM(if(order_type = 1,coalesce(real_pay, 0),0)) as totalAmountAirportpickDom \n");
        sqlBuilder.append(", SUM(if(order_type = 2,coalesce(real_pay, 0),0)) as totalAmountAirportpickInter \n");
        sqlBuilder.append(", SUM(if(order_type = 3,coalesce(real_pay, 0),0)) as totalAmountCharter \n");
        sqlBuilder.append(", SUM(if(order_type = 4,coalesce(real_pay, 0),0)) as totalAmountRent \n");
        sqlBuilder.append(", SUM(if(order_type = 6,coalesce(real_pay, 0),0)) as totalAmountTax \n");
        sqlBuilder.append(", count(distinct order_id) as totalOrderCount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList,
                queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD), ORDERDT));
        sqlBuilder.append(getCarProductTypeCondition(requestDto.getProductType()));
        if (downObjectEnum != null) {
            sqlBuilder.append(downCondition(downObjectEnum, analysisObjectOrgInfo));
        }
        return sqlBuilder.toString();
    }

    private String overviewSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> paramList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                               AnalysisObjectEnum analysisObjectEnum, AnalysisObjectEnum downObjectEnum, String downObjectVal, String user) {
        StringBuilder sqlBuilder = new StringBuilder();

        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (isBookCaliber) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partion = queryPartition(clickHouseTable);

        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(biz.getSpecialResultFields());
        sqlBuilder.append(", SUM(coalesce(amount_total, 0)) as totalAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(clickHouseTable.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, paramList, requestDto.getStartTime(), requestDto.getEndTime(), partion));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, paramList));
        sqlBuilder.append(downCondition(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum, paramList));
        sqlBuilder.append(" group by ").append(biz.getSpecialGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    private String busSql1(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> paramList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                           AnalysisObjectEnum analysisObjectEnum, AnalysisObjectEnum downObjectEnum, String downObjectVal, String user) {
        StringBuilder sqlBuilder = new StringBuilder();

        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (isBookCaliber) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partion = queryPartition(clickHouseTable);

        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(biz.getSpecialResultFields());
        sqlBuilder.append(", SUM(coalesce(amount_bus, 0)) as totalAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(clickHouseTable.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, paramList, requestDto.getStartTime(), requestDto.getEndTime(), partion));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, paramList));
        sqlBuilder.append(downCondition(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum, paramList));
        sqlBuilder.append(" group by ").append(biz.getSpecialGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    private String vasoSql1(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> paramList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                            AnalysisObjectEnum analysisObjectEnum, AnalysisObjectEnum downObjectEnum, String downObjectVal, String user) {
        StringBuilder sqlBuilder = new StringBuilder();

        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (isBookCaliber) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partion = queryPartition(clickHouseTable);

        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(biz.getSpecialResultFields());
        sqlBuilder.append(", SUM(coalesce(amount_vas, 0)) as totalAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(clickHouseTable.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, paramList, requestDto.getStartTime(), requestDto.getEndTime(), partion));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, paramList));
        sqlBuilder.append(downCondition(downObjectEnum, downObjectVal, analysisObjectOrgInfo, analysisObjectEnum, paramList));
        sqlBuilder.append(" group by ").append(biz.getSpecialGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    /**
     * 查询足迹
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> getHotDestSql(BaseQueryConditionDTO requestDto, List<String> uids, Class<T> clazz, String lang) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> paramList = new ArrayList<>();
        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_ONE_TRIP_FULL_TRIP_ID_GENERATE_ALL;
        String dimName = SharkUtils.isZH(lang) ? " sub_trip_city_name" : " sub_trip_city_name_en ";
        sqlBuilder.append("select uid, " + dimName + " as topTarget , count(DISTINCT order_id) as countSort ");
        sqlBuilder.append(" from  ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, requestDto.getStartTime(),
                requestDto.getEndTime(), queryPartition(clickHouseTable), "substring(order_date, 1, 10)"));



        sqlBuilder.append(BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("uid", uids, paramList));
        sqlBuilder.append(" and corp_city != sub_trip_city_name");
        sqlBuilder.append(" GROUP BY uid," + dimName);
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    private String overviewSqlCorpAndIndustry(List<Object> parmList, boolean isBookCaliber, String startTime, String endTime, String productType,
                                              List<String> industryList, DataTypeEnum dataTypeEnum,
                                              String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        StringBuilder sqlBuilder = new StringBuilder();

        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (isBookCaliber) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partition = queryPartition(clickHouseTable);

        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(" SUM(coalesce(amount_total, 0)) as totalAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, REPORT_DATE));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, REPORT_DATE));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        return sqlBuilder.toString();
    }

    private String overviewSqlDown(BaseQueryConditionDTO requestDto, List<Object> paramList, boolean isBookCaliber,
                                   AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum downObjectEnum) {
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (isBookCaliber) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(" SUM(coalesce(amount_total, 0)) as totalAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(clickHouseTable.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, paramList, requestDto.getStartTime(), requestDto.getEndTime(), partition));
        if (downObjectEnum != null) {
            sqlBuilder.append(downCondition(downObjectEnum, analysisObjectOrgInfo));
        }
        return sqlBuilder.toString();
    }

    /**
     * 预订人
     *
     * @param user     （uid, user_name）
     * @param parmList
     * @return
     */
    protected String buildPreSqlUser(String user, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        String val = StringUtils.trimToEmpty(user);
        if (StringUtils.isNotEmpty(val)) {
            String uid = val;
            String encryptedVal = DbResultMapUtils.sm4Encrypt(val);
            log.info("Building user query condition for user: {}, encrypted: {}", uid, encryptedVal);
            sqlBuffer.append(" and ( uid like ? or user_name like ? )");
            parmList.add("%" + uid + "%");
            parmList.add("%" + encryptedVal + "%");
        }
        return sqlBuffer.toString();
    }

    /**
     * 预订人
     *
     * @param user     （uid, user_name）
     * @param parmList
     * @return
     */
    protected String buildBusPreSqlUser(String user, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        String val = StringUtils.trimToEmpty(user);
        if (StringUtils.isNotEmpty(val)) {
            sqlBuffer.append(" and ( uid like ? or username like ? )");
            parmList.add("%" + val + "%");
            parmList.add("%" + val + "%");
        }
        return sqlBuffer.toString();
    }

    /**
     * 预订人
     *
     * @param user     （uid, user_name）
     * @param parmList
     * @return
     */
    protected String buildVasoPreSqlUser(String user, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        String val = StringUtils.trimToEmpty(user);
        if (StringUtils.isNotEmpty(val)) {
            sqlBuffer.append(" and ( uid like ? or corpcust_custname like ? )");
            parmList.add("%" + val + "%");
            parmList.add("%" + val + "%");
        }
        return sqlBuffer.toString();
    }

    public <T> List<T> topDeptAnalysisSimpleDetail(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum, Class<T> clazz,
                                                   QueryReportBuTypeEnum queryBu, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                                   String startTime, String endTime, String analysisObjectVal) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        JoinCondition biz = joinCondition(analysisObjectEnum, analysisObjectOrgInfo, requestDto.getLang());

        List<Object> parmList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        switch (queryBu) {
            case flight:
                sql.append(fltSqlSimple(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, startTime, endTime, analysisObjectVal));
                break;
            case hotel:
                sql.append(htlSqlSimple(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, startTime, endTime, analysisObjectVal));
                break;
            case train:
                sql.append(trainSqlSimple(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, startTime, endTime, analysisObjectVal));
                break;
            case car:
                sql.append(carSqlSimple(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, startTime, endTime, analysisObjectVal));
                break;
            case bus:
                sql.append(busSqlSimple(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, startTime, endTime, analysisObjectVal));
                break;
            case vaso:
                sql.append(vasoSqlSimple(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum, startTime, endTime, analysisObjectVal));
                break;
            default:
                break;
        }
        // 查询clickhouse
        return commonList(clazz, sql.toString(), parmList);
    }

    private String fltSqlSimple(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber,
                                AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum analysisObjectEnum, String startTime, String endTime
            , String analysisObjectVal) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT corp_name, ").append(biz.getSpecialResultFields());
        // 消费金额
        sqlBuilder.append(", sum(coalesce(real_pay, 0)) as totalAmount");
        // 总订单数
        sqlBuilder.append(", COUNT(DISTINCT order_id) AS totalOrderCount \n");
        // app订单数
        sqlBuilder.append(", COUNT(DISTINCT case when is_online = 'M' then order_id end) AS totalAppOrderCount \n");
        // online订单数
        sqlBuilder.append(", COUNT(DISTINCT case when is_online = 'T' then order_id end) AS totalOnlineOrderCount \n");
        // offline订单数
        sqlBuilder.append(", COUNT(DISTINCT case when is_online = 'F' then order_id end) AS totalOfflineOrderCount \n");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXFLIGHTDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                startTime, endTime, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(downConditionWithSpecialCol(analysisObjectEnum, analysisObjectVal, analysisObjectOrgInfo, parmList));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(" group by corp_name,").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    private String htlSqlSimple(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber,
                                AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum analysisObjectEnum, String startTime, String endTime
            , String analysisObjectVal) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT corp_name,").append(biz.getSpecialResultFields());
        // 消费金额
        sqlBuilder.append(", sum(coalesce(real_pay_with_servicefee, 0)) as totalAmount");
        // 总订单数
        sqlBuilder.append(", COUNT(DISTINCT order_id) AS totalOrderCount \n");
        // app订单数
        sqlBuilder.append(", COUNT(DISTINCT case when is_online = 'M' then order_id end) AS totalAppOrderCount \n");
        // online订单数
        sqlBuilder.append(", COUNT(DISTINCT case when is_online = 'T' then order_id end) AS totalOnlineOrderCount \n");
        // offline订单数
        sqlBuilder.append(", COUNT(DISTINCT case when is_online = 'F' then order_id end) AS totalOfflineOrderCount \n");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(OLRPT_INDEXHOTELDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                startTime, endTime, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(downConditionWithSpecialCol(analysisObjectEnum, analysisObjectVal, analysisObjectOrgInfo, parmList));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(" group by corp_name,").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    private String trainSqlSimple(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber,
                                  AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum analysisObjectEnum, String startTime, String endTime
            , String analysisObjectVal) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT corp_name,").append(biz.getSpecialResultFields());
        // 消费金额
        sqlBuilder.append(", sum(coalesce(real_pay, 0)) as totalAmount");
        // 总订单数
        sqlBuilder.append(", COUNT(DISTINCT order_id) AS totalOrderCount \n");
        // app订单数
        sqlBuilder.append(", COUNT(DISTINCT case when is_online = 'M' then order_id end) AS totalAppOrderCount \n");
        // online订单数
        sqlBuilder.append(", COUNT(DISTINCT case when is_online = 'T' then order_id end) AS totalOnlineOrderCount \n");
        // offline订单数
        sqlBuilder.append(", COUNT(DISTINCT case when is_online = 'F' then order_id end) AS totalOfflineOrderCount \n");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                startTime, endTime, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(downConditionWithSpecialCol(analysisObjectEnum, analysisObjectVal, analysisObjectOrgInfo, parmList));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(" group by corp_name,").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    private String carSqlSimple(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber,
                                AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum analysisObjectEnum, String startTime, String endTime
            , String analysisObjectVal) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT corp_name,").append(biz.getSpecialResultFields());
        sqlBuilder.append(", SUM(coalesce(real_pay, 0)) as totalAmount \n");
        sqlBuilder.append(", SUM(cnt_order) as totalOrderCount \n");
        // app订单数
        sqlBuilder.append(", SUM(case when is_online = 'M' then cnt_order end) AS totalAppOrderCount \n");
        // online订单数
        sqlBuilder.append(", SUM(case when is_online = 'T' then cnt_order end) AS totalOnlineOrderCount \n");
        // offline订单数
        sqlBuilder.append(", SUM(case when is_online = 'F' then cnt_order end) AS totalOfflineOrderCount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                startTime, endTime, partition, ORDERDT));
        sqlBuilder.append(downConditionWithSpecialCol(analysisObjectEnum, analysisObjectVal, analysisObjectOrgInfo, parmList));
        sqlBuilder.append(getCarProductTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(" group by corp_name,").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    private String busSqlSimple(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber,
                                AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum analysisObjectEnum, String startTime, String endTime
            , String analysisObjectVal) {
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (isBookCaliber) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(" SELECT corp_name,").append(biz.getSpecialResultFields());
        sqlBuilder.append(", SUM(coalesce(amount_bus, 0)) as totalAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(clickHouseTable.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                startTime, endTime, partition, REPORT_DATE));
        sqlBuilder.append(downConditionWithSpecialCol(analysisObjectEnum, analysisObjectVal, analysisObjectOrgInfo, parmList));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(" group by corp_name,").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    private String vasoSqlSimple(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber,
                                 AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum analysisObjectEnum, String startTime, String endTime
            , String analysisObjectVal) {
        StringBuilder sqlBuilder = new StringBuilder();

        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (isBookCaliber) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(" SELECT corp_name,").append(biz.getSpecialResultFields());
        sqlBuilder.append(", SUM(coalesce(amount_vas, 0)) as totalAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(clickHouseTable.getTable()).append(" as current");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList,
                startTime, endTime, partition, REPORT_DATE));
        sqlBuilder.append(downConditionWithSpecialCol(analysisObjectEnum, analysisObjectVal, analysisObjectOrgInfo, parmList));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList));
        sqlBuilder.append(" group by corp_name,").append(biz.getSpecialGroupFields());
        return sqlBuilder.toString();
    }

    /**
     * 具体查询条件
     *
     * @param analysisObjectEnum
     * @param analysisObjectVal
     * @param analysisObjectOrgInfo
     * @return
     */
    public String downConditionWithSpecialCol(AnalysisObjectEnum analysisObjectEnum, String analysisObjectVal,
                                              AnalysisObjectOrgInfo analysisObjectOrgInfo, List<Object> parmList) {
        String conditon = StringUtils.EMPTY;
        if (analysisObjectEnum == null || StringUtils.isEmpty(analysisObjectVal)) {
            return conditon;
        }
        switch (analysisObjectEnum) {
            case CORP:
                conditon = " and corp_corporation = ? ";
                parmList.add(analysisObjectVal);
                break;
            case ACCOUNT:
            case ACCOUNTCODE:
                conditon = " and account_id = ? ";
                parmList.add(Integer.valueOf(analysisObjectVal));
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
            case COSTCENTER1:
            case COSTCENTER2:
            case COSTCENTER3:
            case COSTCENTER4:
            case COSTCENTER5:
            case COSTCENTER6:
                if (ConfigUtils.getBoolean("custom_dim_open", false)) {
                    conditon = String.format(" and %s_custom = ? ", analysisObjectEnum.toString().toLowerCase());
                    parmList.add(analysisObjectVal);
                } else {
                    conditon = String.format(" and %s = ? ", analysisObjectEnum.toString().toLowerCase());
                    parmList.add(analysisObjectVal);
                }
                break;
            case ORG:
                conditon = getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum,
                        analysisObjectVal, parmList);
                break;
            case UID:
                conditon = " and uid = ? ";
                parmList.add(analysisObjectVal);
                break;
            default:
                break;
        }
        return conditon;
    }

    /**
     * 具体查询条件
     *
     * @param analysisObjectEnum
     * @param analysisObjectVal
     * @return
     */
    public String queryConditionWithSpecialCol(AnalysisObjectEnum analysisObjectEnum, String analysisObjectVal,
                                               List<Object> paramList) {
        String conditon = StringUtils.EMPTY;
        if (analysisObjectEnum == null || StringUtils.isEmpty(analysisObjectVal)) {
            return conditon;
        }
        switch (analysisObjectEnum) {
            case CORP:
                conditon = " and corp_name like ?";
                paramList.add("%" + analysisObjectVal + "%");
                break;
            case ACCOUNT:
                conditon = " and account_name like ? ";
                paramList.add("%" + analysisObjectVal + "%");
                break;
            case ACCOUNTCODE:
                conditon = " and account_code like ? ";
                paramList.add("%" + analysisObjectVal + "%");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
            case COSTCENTER1:
            case COSTCENTER2:
            case COSTCENTER3:
            case COSTCENTER4:
            case COSTCENTER5:
            case COSTCENTER6:
                if (ConfigUtils.getBoolean("custom_dim_open", false)) {
                    conditon = String.format(" and %s_custom like ? ", analysisObjectEnum.toString().toLowerCase());
                    paramList.add("%" + analysisObjectVal + "%");
                } else {
                    conditon = String.format(" and %s like ? ", analysisObjectEnum.toString().toLowerCase());
                    paramList.add("%" + analysisObjectVal + "%");
                }
                break;
            case UID:
                conditon = " and uid = ? ";
                paramList.add("%" + analysisObjectVal + "%");
                break;
            default:
                break;
        }
        return conditon;
    }

    enum DeptMetricEnum {
        REFUND_QUANTITY, REBOOK_QUANTITY, LOW_RC_COUNT
    }

    @Data
    @Accessors(chain = true)
    class JoinCondition {
        private String resultFields;
        private String specialResultFields;
        private String specialGroupFields;
        private String groupFields;

        private String currentJoinMom;

        private String currentJoinYoy;

        private String currentJoinYoyBeforeLast;

        private String orderByFields;
    }
}
