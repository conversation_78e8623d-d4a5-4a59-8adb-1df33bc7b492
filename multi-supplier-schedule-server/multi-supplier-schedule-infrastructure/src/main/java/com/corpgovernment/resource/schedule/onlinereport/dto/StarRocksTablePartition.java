package com.corpgovernment.resource.schedule.onlinereport.dto;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.dto
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-03 19:18
 **/
@Data
public class StarRocksTablePartition {

    @Column(name = "table")
    @Type(value = Types.VARCHAR)
    private String table;

    @Column(name = "partition")
    @Type(value = Types.VARCHAR)
    private String partition;

}
