package com.corpgovernment.resource.schedule.onlinereport.module.behaivor;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class BalanceTypeDTO {

    @Column(name = "balancetype")
    @Type(value = Types.VARCHAR)
    private String balanceType;

    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    @Column(name = "totalRoomPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRoomPrice;

    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;
}
