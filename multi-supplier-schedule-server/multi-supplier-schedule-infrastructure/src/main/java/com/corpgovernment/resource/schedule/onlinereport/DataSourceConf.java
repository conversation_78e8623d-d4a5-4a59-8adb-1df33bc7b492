package com.corpgovernment.resource.schedule.onlinereport;

import com.alibaba.druid.pool.DruidDataSource;
import com.ctrip.corp.obt.shard.configuration.DruidDatabaseProperties;
import com.ctrip.corp.obt.shard.core.ShardDataSource;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Configurable
public class DataSourceConf {

    public static final String CORPGOVERNMENT_MSS_DEFAULT = "corpgovernment.mss.default";
    @Resource
    private DruidDatabaseProperties dbProperties;


    /**
     * 目前该bean是为了解决框架中的动态数据源，在多个数据源的情况下，没有指定其需要使用的数据源，导致存在多个数据源的情况下，spring无法确定使用哪个数据源
     */
    @Primary
    @Bean
    public DataSource defaultDataSource() {
        System.setProperty("druid.mysql.usePingMethod", "false");
        return ShardDataSource.buildDefaultDatasource(dbProperties);
    }

    @Bean(name = "onlinereportDataSource")
    // 加载数据源标识。 true-加载数据源， false-不加载数据源。默认不加载
    @ConditionalOnProperty(value = "dataSources.onlinereport.loadDataSource", havingValue = "true")
    public DataSource onlinereportDataSource() {
        // 获取当前租户的数据源
        String tenantNamespace = ConfigService.getAppConfig().getProperty("dataSources.onlinereport.tenantNamespace", CORPGOVERNMENT_MSS_DEFAULT);
        log.info("current tenantNamespace:{}", tenantNamespace);

        // 当前租户的数据源配置
        Config config = tenantNamespace.equals(CORPGOVERNMENT_MSS_DEFAULT) ? ConfigService.getAppConfig() : ConfigService.getConfig(tenantNamespace);
        if (config == null) {
            log.error("get datasource error, tenantNamespace:{}", tenantNamespace);
            throw new RuntimeException("get datasource error, tenantNamespace:" + tenantNamespace);
        }


        // 获取tenantNamespace最后一个.的租户名称
        String tenant = tenantNamespace.substring(tenantNamespace.lastIndexOf(".") + 1);

        String driver = config.getProperty("dataSources." + tenant + ".onlinereport.driverClass", null);
        String url = config.getProperty("dataSources." + tenant + ".onlinereport.url", null);
        String username = config.getProperty("dataSources." + tenant + ".onlinereport.username", null);
        String password = config.getProperty("dataSources." + tenant + ".onlinereport.password", null);
        // 替换url中敏感的ip、域名、端口信息
        log.info("current datasource info: driver:{}, url:{}", driver, Optional.ofNullable(url).map(m -> m.replaceAll("//(.*):(.*)/", "//***:***/")).orElse(null));

        if (StringUtils.isAnyBlank(driver, url, username, password)) {
            log.error("get datasource error, driver:{}, url:{}, username:{}, password:{}", driver, url, username, password);
            throw new RuntimeException("get datasource error, driver:" + driver + ", url:" + url + ", username:" + username + ", password:" + password);
        }


        return getDruidDataSource(driver, url, username, password);
    }

    /**
     * 前端埋点使用的数据源
     */
    @Bean(name = "frontDataSource")
    // 加载数据源标识。 true-加载数据源， false-不加载数据源。默认不加载。（目前只有程曦平台使用该数据源，其他租户不需要加载）
    @ConditionalOnProperty(value = "dataSources.front.loadDataSource", havingValue = "true")
    public DataSource frontDataSource() {
        log.info("load frontDataSource");

        Config appConfig = ConfigService.getAppConfig();
        String driver = appConfig.getProperty("dataSources.front.driverClass", null);
        String url = appConfig.getProperty("dataSources.front.url", null);
        String username = appConfig.getProperty("dataSources.front.username", null);
        String password = appConfig.getProperty("dataSources.front.password", null);

        if (StringUtils.isAnyBlank(driver, url, username, password)) {
            log.error("get frontDataSource error, driver:{}, url:{}, username:{}, password:{}", driver, url, username, password);
            throw new RuntimeException("get datasource error, driver:" + driver + ", url:" + url + ", username:" + username + ", password:" + password);
        }

        log.info("current frontDataSource info: driver:{}, url:{}", driver, Optional.ofNullable(url).map(m -> m.replaceAll("//(.*):(.*)/", "//***:***/")).orElse(null));
        return getDruidDataSource(driver, url, username, password);
    }

    /**
     * 管控日报、周报使用的数据源
     */
    @Bean(name = "managementDataSource")
    // 加载数据源标识。 true-加载数据源， false-不加载数据源。默认不加载。（目前只有程曦平台使用该数据源，其他租户不需要加载）
    @ConditionalOnProperty(value = "dataSources.management.loadDataSource", havingValue = "true")
    public DataSource managementDataSource() {
        log.info("load managementDataSource");

        Config appConfig = ConfigService.getAppConfig();
        String driver = appConfig.getProperty("dataSources.management.driverClass", null);
        String url = appConfig.getProperty("dataSources.management.url", null);
        String username = appConfig.getProperty("dataSources.management.username", null);
        String password = appConfig.getProperty("dataSources.management.password", null);

        if (StringUtils.isAnyBlank(driver, url, username, password)) {
            log.error("get frontDataSource error, driver:{}, url:{}, username:{}, password:{}", driver, url, username, password);
            throw new RuntimeException("get datasource error, driver:" + driver + ", url:" + url + ", username:" + username + ", password:" + password);
        }

        log.info("current frontDataSource info: driver:{}, url:{}", driver, Optional.ofNullable(url).map(m -> m.replaceAll("//(.*):(.*)/", "//***:***/")).orElse(null));

        return getDruidDataSource(driver, url, username, password);
    }

    private static DruidDataSource getDruidDataSource(String driver, String url, String username, String password) {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName(driver);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);

        // 基础配置
        // 初始化时创建3个连接
        dataSource.setInitialSize(20);
        // 最小保持5个空闲连接
        dataSource.setMinIdle(20);
        // 最大活跃连接数为15
        dataSource.setMaxActive(100);
        // 获取连接的最大等待时间为60秒
        dataSource.setMaxWait(60000);

        // 连接池检查与回收配置
        // 每60秒运行一次空闲连接检测
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        // 连接在池中保持最小空闲时间为10分钟
        dataSource.setMinEvictableIdleTimeMillis(600000);
        // 空闲时检查连接有效性
        dataSource.setTestWhileIdle(true);
        // 借用连接时检查有效性
        dataSource.setTestOnBorrow(true);
        // 归还连接时不检查有效性
        dataSource.setTestOnReturn(false);

        // SQL执行监控
        // 禁用预编译SQL缓存
        dataSource.setPoolPreparedStatements(false);

        // 连接错误重试与超时设置
        // 连接错误重试次数
        dataSource.setConnectionErrorRetryAttempts(3);
        // 获取连接失败后中断
        dataSource.setBreakAfterAcquireFailure(true);
        // 保持活跃连接
        dataSource.setKeepAlive(true);
        // 3分钟无活动后进行心跳检测
        dataSource.setKeepAliveBetweenTimeMillis(180000);

        // SQL查询超时设置
        // 设置查询超时为30秒
        dataSource.setQueryTimeout(30);

        // 防止长时间未关闭连接
        // 启用连接回收
        dataSource.setRemoveAbandoned(true);
        // 设置10分钟为连接回收超时
        dataSource.setRemoveAbandonedTimeoutMillis(600000);

        // 日志与监控
        // 启用日志记录被回收的连接
        dataSource.setLogAbandoned(true);
        // 每5分钟记录一次统计信息
        dataSource.setTimeBetweenLogStatsMillis(300000);
        return dataSource;
    }
}
