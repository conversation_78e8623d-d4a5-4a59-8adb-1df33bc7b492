package com.corpgovernment.resource.schedule.onlinereport.module.save;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class PromotionSaveDTO {

    @Column(name = "tagname")
    @Type(value = Types.VARCHAR)
    private String tagName;

    @Column(name = "saveAmountPromotion")
    @Type(value = Types.DOUBLE)
    private Double saveAmount;


}
