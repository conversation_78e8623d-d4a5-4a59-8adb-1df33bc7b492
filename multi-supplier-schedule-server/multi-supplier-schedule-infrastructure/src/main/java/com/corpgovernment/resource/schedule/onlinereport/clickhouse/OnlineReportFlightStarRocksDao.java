package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.dao.impl
 * @description:
 * @author: Chris Yu
 * @create: 2021-11-03 19:49
 **/
@Repository
@Slf4j
public class OnlineReportFlightStarRocksDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "OnlineReportOverviewDao queryOnlineReportFlight";

    private BaseCommonTableQueryCondition queryCondition;

    public OnlineReportFlightStarRocksDao(BaseCommonTableQueryCondition queryCondition) {
        this.queryCondition = queryCondition;
    }

    /**
     * 查询 在线报告概 机票
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryOnlineReportDetailFlight(OnlineDetailRequestDto requestDto, Class<T> clazz)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");

        if (!BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            if (StringUtils.equalsIgnoreCase(requestDto.getBaseCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                sqlBuilder.append("select orderdt as reportDate, flight_class as flightClass " +
                        ", (case when (agreement_type_name='" + ta + "' or agreement_type_name = 'B2G') then 'C' else 'NC' end) as contractType , \n");
            } else {
                // 默认使用成交口径
                sqlBuilder.append("select report_date as reportDate, flight_class as flightClass " +
                        ", (case when (agreement_type_name='" + ta + "' or agreement_type_name = 'B2G') then 'C' else 'NC' end) as contractType , \n");
            }
        } else {
            sqlBuilder.append(
                    "select report_date as reportDate, flight_class as flightClass " +
                            ", (case when (agreement_type_name='" + ta + "' or agreement_type_name = 'B2G') then 'C' else 'NC' end) as contractType , \n");
        }
        // 优惠券
        sqlBuilder.append(
                "sum(coalesce(bind_amount,0)) as bindAmount,\n");
        // 改签费
        sqlBuilder.append(
                "sum(coalesce(change_fee,0)) as changeFee,\n");
        // 保险费
        sqlBuilder.append(
                "sum(coalesce(insurance_fee,0)) as insuranceFee,\n");
        // 成交净价
        sqlBuilder.append(
                "sum(coalesce(netfare,0)) as netfare,\n");
        // 燃油费
        sqlBuilder.append(
                "sum(coalesce(oil_fee,0)) as oilFee,\n");
        // 改签差价
        sqlBuilder.append(
                "sum(coalesce(rebook_price_differential,0)) as rebookPriceDifferential,\n");
        if (!BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            // 改签燃油差
            sqlBuilder.append(
                    "sum(coalesce(oilfeedifferential,0)) as oilfeedifferential,\n");
            // 税差
            sqlBuilder.append(
                    "sum(coalesce(tax_differential,0)) as taxDifferential,\n");
        }
        // 改签商旅管理服务费
        sqlBuilder.append(
                "sum(coalesce(rebook_service_fee,0)) as rebookServiceFee,\n");
        // 退票费
        sqlBuilder.append(
                "sum(coalesce(refund_fee,0)) as refundFee,\n");
        // 退票行程单商旅管理服务方
        sqlBuilder.append(
                "sum(coalesce(refund_itinerary_fee,0)) as refundItineraryFee,\n");
        // 退票商旅管理服务方
        sqlBuilder.append(
                "sum(coalesce(refund_service_fee,0)) as refundServiceFee,\n");
        // 配送费
        sqlBuilder.append(
                "sum(coalesce(send_ticket_fee,0)) as sendTicketFee,\n");
        // 后收商旅管理服务费
        sqlBuilder.append(
                "sum(coalesce(ticket_behind_service_fee,0)) as ticketBehindServicefee,\n");
        // 后收改签商旅管理服务费
        sqlBuilder.append(
                "sum(coalesce(rebook_behind_service_fee,0)) as rebookBehindServiceFee,\n");
        // 后收退票商旅管理服务费
        sqlBuilder.append(
                "sum(coalesce(refund_behind_service_fee,0)) as refundBehindServiceFee,\n");
        // 基础服务费
        sqlBuilder.append(
                "sum(coalesce(service_fee,0)) as serviceFee,\n");
        // 基建费
        sqlBuilder.append("sum(coalesce(tax,0)) as tax,\n");
        // 增值服务包
        sqlBuilder.append(
                "sum(coalesce(servicepackage_fee,0)) as servicepackageFee,\n");

        /**
         * 机票-金额-明细
         */
        // 协议金额 real_pay（总金额）
        sqlBuilder.append(
                " sum(case when agreement_type_name='" + ta + "' then coalesce(real_pay,0) else 0 end) as agreementAmount,\n");
        // 非协议金额
        sqlBuilder.append(
                " sum(case when agreement_type_name!='" + ta + "' then coalesce(real_pay,0) else 0 end) as notAgreementAmount,\n");

        // 国内金额
        sqlBuilder.append(
                " sum(case when flight_class='N' then coalesce(real_pay,0) else 0 end) as domesticAmount,\n");
        // 国际金额
        sqlBuilder.append(
                " sum(case when flight_class='I' then coalesce(real_pay,0) else 0 end) as internationalAmount,\n");

        // 总金额
        sqlBuilder.append(
                "sum(coalesce(real_pay,0)) as realPay,\n");

        /**
         * 机票-票张-明细
         */
        // 票张-协议-当期
        sqlBuilder.append(
                " sum(case when agreement_type_name='" + ta + "' then coalesce(quantity,0) else 0 end) as agreementQuantity,\n");
        // 票张-非协议-当期
        sqlBuilder.append(
                " sum(case when agreement_type_name!='" + ta + "' then coalesce(quantity,0) else 0 end) as notAgreementQuantity,\n");

        // 票张-国内-当期
        sqlBuilder.append(
                " sum(case when flight_class='N' then coalesce(quantity,0) else 0 end) as domesticQuantity,\n");
        // 票张-国际-当期
        sqlBuilder.append(
                " sum(case when flight_class='I' then coalesce(quantity,0) else 0 end) as internationalQuantity,\n");

        // 机票张数-当期
        sqlBuilder.append(
                "sum(coalesce(quantity,0)) as quantityV\n");

        sqlBuilder.append(OrpConstants.FROM);
        ClickHouseTable clickHouseTable = null;
        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                clickHouseTable = ClickHouseTable.ADM_INDEXFLIGHT_PRICE_DETAIL_FOREIGN_MULTI_CURRENCY;
            } else {
                clickHouseTable = ClickHouseTable.ADM_INDEXFLIGHT_PRICE_DETAIL_FOREIGN;
            }
        } else {
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        }
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d=? ");
        sqlBuilder.append(OrpConstants.AND);
        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            sqlBuilder.append(" ( report_date>=? and report_date<=? )");
        } else {
            if (StringUtils.equalsIgnoreCase(requestDto.getBaseCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                sqlBuilder.append(" ( orderdt>=? and orderdt<=? )");
            } else {
                // 默认使用成交口径
                sqlBuilder.append(" ( report_date>=? and report_date<=? )");
            }
            Map extParams = Optional.ofNullable(requestDto.getExtData()).orElse(new HashMap<>());
            String isRefund = (String) extParams.get("isRefund");
            if (StringUtils.equalsIgnoreCase("T", isRefund)) {
                sqlBuilder.append(" and is_refund = 'T' ");
            }
            if (StringUtils.equalsIgnoreCase("F", isRefund)) {
                sqlBuilder.append(" and is_refund = 'F' ");
            }
            // OLRPT_INDEXFLIGHTDOWNLOAD表要加这个过滤条件
            sqlBuilder.append(" and audited <> 'F' ");
        }
        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(requestDto.getBaseCondition().getCurrency())) {
                // 币种条件
                sqlBuilder.append(String.format(" and termcurrency = '%s'", requestDto.getBaseCondition().getCurrency()));
            }
        }
        // 其他条件
        queryCondition.buildPreSqlCondition(requestDto, sqlBuilder);
        sqlBuilder.append(" group by report_date,flight_class,contractType");
        // 查询clickhouse
        ClickHouseTable finalClickHouseTable = clickHouseTable;
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapRequest(req, statement, finalClickHouseTable), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, "queryOnlineReportFlight");
    }

    private PreparedStatement mapRequest(OnlineDetailRequestDto requestDto, PreparedStatement statement, ClickHouseTable clickHouseTable) {
        AtomicInteger index = new AtomicInteger(OrpConstants.ONE);
        try {

            statement.setString(index.getAndIncrement(), queryPartition(clickHouseTable));
            // 查询日期
            statement.setString(index.getAndIncrement(), requestDto.getDataStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            queryCondition.setPreSqlCondition(index, requestDto, statement);
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }
}
