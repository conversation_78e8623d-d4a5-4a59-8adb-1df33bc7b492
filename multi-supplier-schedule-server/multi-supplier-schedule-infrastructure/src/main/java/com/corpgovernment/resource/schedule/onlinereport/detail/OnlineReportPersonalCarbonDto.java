package com.corpgovernment.resource.schedule.onlinereport.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2023/10/24
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class OnlineReportPersonalCarbonDto {

    /**
     * 总碳排
     */
    @Column(name = "allTotalCarbon")
    @Type(value = Types.DECIMAL)
    private BigDecimal allTotalCarbon;

    /**
     * 总节省碳排
     */
    @Column(name = "allSaveCarbon")
    @Type(value = Types.DECIMAL)
    private BigDecimal allSaveCarbon;
    /**
     * 碳排-机票
     */
    @Column(name = "carbonEmissionFlt")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbonEmissionFtl;

    /**
     * 平均碳排-机票
     */
    @Column(name = "avgCarbonFlt")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgCarbonFlt;

    /**
     * 节省碳排-机票
     */
    @Column(name = "saveCarbonFlt")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveCarbonFlt;

    /**
     * 绿色占比-机票
     */
    @Column(name = "greenRateFlt")
    @Type(value = Types.DECIMAL)
    private BigDecimal greenRateFlt;

    /**
     * 碳排-酒店
     */
    @Column(name = "carbonEmissionHtl")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbonEmissionHtl;

    /**
     * 平均碳排-酒店
     */
    @Column(name = "avgCarbonHtl")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgCarbonHtl;

    /**
     * 节省碳排-酒店
     */
    @Column(name = "saveCarbonHtl")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveCarbonHtl;

    /**
     * 绿色占比-酒店
     */
    @Column(name = "greenRateHtl")
    @Type(value = Types.DECIMAL)
    private BigDecimal greenRateHtl;

    /**
     * 碳排-火车
     */
    @Column(name = "carbonEmissionTrain")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbonEmissionTrain;

    /**
     * 平均碳排-火车
     */
    @Column(name = "avgCarbonTrain")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgCarbonTrain;

    /**
     * 节省碳排-火车
     */
    @Column(name = "saveCarbonTrain")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveCarbonTrain;

    /**
     * 绿色占比-火车
     */
    @Column(name = "greenRateTrain")
    @Type(value = Types.DECIMAL)
    private BigDecimal greenRateTrain;


    /**
     * 碳排-用车
     */
    @Column(name = "carbonEmissionCar")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbonEmissionCar;

    /**
     * 平均碳排-用车
     */
    @Column(name = "avgCarbonCar")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgCarbonCar;

    /**
     * 节省碳排-用车
     */
    @Column(name = "saveCarbonCar")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveCarbonCar;

    /**
     * 绿色占比-用车
     */
    @Column(name = "greenRateCar")
    @Type(value = Types.DECIMAL)
    private BigDecimal greenRateCar;
}
