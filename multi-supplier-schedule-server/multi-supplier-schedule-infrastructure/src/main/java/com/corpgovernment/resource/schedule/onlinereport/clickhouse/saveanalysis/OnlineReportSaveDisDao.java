package com.corpgovernment.resource.schedule.onlinereport.clickhouse.saveanalysis;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportOverviewTrendDao;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveFlightDisDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveFlightDisV2DTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveHotelDisDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveProportionDetailRequestDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveProportionRequestDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/**
 * <AUTHOR>
 * @date ：Created in 2021/11/10 10:26
 * @description：
 * @modified By：
 * @version: $
 */
@Component
@Slf4j
@Repository
public class OnlineReportSaveDisDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "OnlineReportSaveDisDao";

    /**
     * 查询 在线报告概况-节省分布机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryFlightProportion(OnlineReportSaveProportionRequestDTO request) throws Exception {
        String sql = buildFlightProportionSql(request);
        return queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveFlightDisDTO.class, "queryOnlineReportFlightSaveProportion");
    }

    /**
     * 查询 在线报告概况-节省分布机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryFlightProportionV2(OnlineReportSaveProportionRequestDTO request) throws Exception {
        String sql = buildFlightProportionSql(request);
        return queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveFlightDisV2DTO.class, "queryOnlineReportFlightSaveProportion");
    }

    /**
     * 查询 在线报告概况-节省分布详情机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryFlightProportionDetail(OnlineReportSaveProportionDetailRequestDTO request) throws Exception {
        String sql = buildFlightProportionDetailSql(request);
        return queryBySql(sql, request, this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveFlightDisDTO.class, "queryOnlineReportFlightSaveProportionDetail");
    }

    /**
     * 查询 在线报告概况-节省分布详情机票
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param productType
     * @param industries
     * @return
     * @throws Exception
     */
    
    public List<OnlineReportSaveFlightDisV2DTO> queryFlightProportionDetailV2(String startTime, String endTime, String statisticalCaliber,
                                                                              String productType, List<String> industries,
                                                                              String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        String sql = conditionWrapDetailSql(startTime,
                endTime,
                flightProportion(productType, statisticalCaliber).toString(),
                industries, compareSameLevel, consumptionLevel, compareCorpSameLevel);
        return queryBySql(sql, Lists.newArrayList(), this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveFlightDisV2DTO.class, "queryOnlineReportFlightSaveProportionDetail");
    }

    /**
     * 查询 在线报告概况-节省分布详情机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryFlightProportionDetailV2(OnlineReportSaveProportionDetailRequestDTO request) throws Exception {
        String sql = buildFlightProportionDetailSql(request);
        return queryBySql(sql, request, this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveFlightDisV2DTO.class, "queryOnlineReportFlightSaveProportionDetail");
    }

    /**
     * 查询 在线报告概况-节省分布 累计节省
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryFlightAccProportionDetail(OnlineReportSaveProportionDetailRequestDTO request) throws Exception {
        String sql = buildFlightAccProportionSql(request);
        return queryBySql(sql, request, this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveFlightDisDTO.class, "queryOnlineReportFlightSaveAccProportionDetail");
    }


    /**
     * 查询 在线报告概况-节省分布酒店
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryHotelProportion(OnlineReportSaveProportionRequestDTO request) throws Exception {
        String sql = buildHotelProportionSql(request);
        return queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveHotelDisDTO.class, "queryOnlineReportHotelSaveProportion");
    }

    /**
     * 查询 在线报告概况-节省分布详情酒店
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryHotelProportionDetail(OnlineReportSaveProportionDetailRequestDTO request) throws Exception {
        String sql = buildHotelProportionDetailSql(request);
        return queryBySql(sql, request, this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveHotelDisDTO.class, "queryOnlineReportHotelSaveProportionDetail");
    }

    /**
     * 查询 在线报告概况-节省分布详情酒店
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param productType
     * @param industries
     * @return
     * @throws Exception
     */
    
    public List<OnlineReportSaveHotelDisDTO> queryHotelProportionDetail(String startTime, String endTime, String statisticalCaliber, String productType, List<String> industries,
                                                                        String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        String sql = conditionWrapDetailSql(startTime,
                endTime,
                hotelProportionDetail(productType, statisticalCaliber).toString(),
                industries, compareSameLevel, consumptionLevel, compareCorpSameLevel);
        return queryBySql(sql, Lists.newArrayList(), this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveHotelDisDTO.class, "queryOnlineReportHotelSaveProportionDetail");
    }

    /**
     * 查询 在线报告概况-节省分布 累计
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryHotelAccProportionDetail(OnlineReportSaveProportionDetailRequestDTO request) throws Exception {
        String sql = buildHotelAccProportionSql(request);
        return queryBySql(sql, request, this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveHotelDisDTO.class, "queryOnlineReportHotelAccSaveProportionDetail");
    }

    public String buildFlightProportionSql(OnlineReportSaveProportionRequestDTO request) {
        return conditionWrapSql(request.getBaseQueryCondition().getStartTime(),
                request.getBaseQueryCondition().getEndTime(),
                flightProportion(request.getProductType(), request.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                request.getBaseQueryCondition());
    }

    public String buildFlightAccProportionSql(OnlineReportSaveProportionDetailRequestDTO request) {
        return conditionWrapSql(request.getBaseQueryCondition().getStartTime(),
                request.getBaseQueryCondition().getEndTime(),
                flightAccProportion(request.getProductType(), request.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                request.getBaseQueryCondition());
    }

    public String buildFlightProportionDetailSql(OnlineReportSaveProportionDetailRequestDTO request) {
        BaseQueryConditionDTO dto = request.getBaseQueryCondition();
        return conditionWrapDetailSql(request.getBaseQueryCondition().getStartTime(),
                request.getBaseQueryCondition().getEndTime(),
                flightProportion(request.getProductType(), request.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                request.getIndustries(), dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
    }

    public String buildHotelProportionSql(OnlineReportSaveProportionRequestDTO request) {
        return conditionWrapSql(request.getBaseQueryCondition().getStartTime(),
                request.getBaseQueryCondition().getEndTime(),
                hotelProportion(request.getProductType(), request.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                request.getBaseQueryCondition());
    }

    public String buildHotelAccProportionSql(OnlineReportSaveProportionDetailRequestDTO request) {
        return conditionWrapSql(request.getBaseQueryCondition().getStartTime(),
                request.getBaseQueryCondition().getEndTime(),
                hotelAccProportion(request.getProductType(), request.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                request.getBaseQueryCondition());
    }

    public String buildHotelProportionDetailSql(OnlineReportSaveProportionDetailRequestDTO request) {
        BaseQueryConditionDTO dto = request.getBaseQueryCondition();
        return conditionWrapDetailSql(request.getBaseQueryCondition().getStartTime(),
                request.getBaseQueryCondition().getEndTime(),
                hotelProportionDetail(request.getProductType(), dto.getStatisticalCaliber()).toString(),
                request.getIndustries(), dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
    }

    public StringBuilder flightProportion(String productType, String statisticalCaliber) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_3c, 0) else 0 end) AS cSave, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_3c, 0) else 0 end) AS netfare3c, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(quantity_3c, 0) else 0 end) AS quantity3c, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) AS premiumSave, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_premium, 0) else 0 end) AS netfarePremium, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(quantity_premium, 0) else 0 end) AS quantityPremium, ");
        stringBuilder.append("SUM(coalesce(saving_price, 0)) as controlSave,");
        stringBuilder.append("sum(case when coalesce(saving_price, 0) != 0 then coalesce(netfare, 0) else 0 end) AS controlNetfare, ");
        stringBuilder.append("sum(case when coalesce(saving_price, 0) != 0 then coalesce(quantity, 0) else 0 end) AS controlQuantity ");
        stringBuilder.append("from  ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        stringBuilder.append("  where d = '").append(partition).append("' and ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        // 国内 经济舱 两方+三方
        stringBuilder.append(" and audited <> 'F' ");
        stringBuilder.append(SaveCondition.getFlightClassCondition(productType));
        return stringBuilder;
    }

    public StringBuilder flightAccProportion(String productType, String statisticalCaliber) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("min(%s) as minReportDate,",
                    ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("min(%s) as minReportDate,",
                    REPORT_DATE)
            );
        }

        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_3c, 0) else 0 end) AS cSave, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) AS premiumSave, ");
        stringBuilder.append("SUM(coalesce(saving_price, 0)) AS controlSave ");
        stringBuilder.append("from  ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        stringBuilder.append("  where d = '").append(partition).append("' and ");
        stringBuilder.append(" ({scope_condition})  ");
        // 国内 经济舱 两方+三方
        stringBuilder.append(" and audited <> 'F' ");
        stringBuilder.append(SaveCondition.getFlightClassCondition(productType));
        return stringBuilder;
    }

    public StringBuilder hotelProportion(String productType, String statisticalCaliber) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select ");
        stringBuilder.append("SUM(coalesce(save_amount_3c, 0)) as cSave, ");
        stringBuilder.append(String.format("if(SUM(CASE WHEN (producttype_all = '%s' and save_amount_3c is not null) then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_3c, 0))) * 100 / SUM(CASE WHEN (producttype_all = '%s' and save_amount_3c is not null) " +
                " then toFloat64(corp_real_pay) else 0 end), " +
                " 0) as cSaveRate, ", ta, ta));
        stringBuilder.append(String.format("if(SUM(CASE WHEN (producttype_all = '%s' and save_amount_3c is not null) then quantity else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_3c, 0))) / SUM(CASE WHEN (producttype_all = '%s' and save_amount_3c is not null) then toFloat64(quantity) else 0 end), " +
                " 0) as cSavePerQuantity, ", ta, ta));

        stringBuilder.append("SUM(coalesce(save_amount_premium, 0)) as premiumSave, ");
        stringBuilder.append(String.format("if(SUM(CASE WHEN (producttype_all = '%s' and save_amount_premium is not null) then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_premium, 0))) * 100 / " +
                "    SUM(CASE WHEN (producttype_all = '%s' and save_amount_premium is not null) then toFloat64(corp_real_pay) else 0 end), " +
                " 0) as premiumSaveRate, ", premium, premium));
        stringBuilder.append(String.format("if(SUM(CASE WHEN (producttype_all = '%s' and save_amount_premium is not null) then quantity else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_premium, 0))) / " +
                "    SUM(CASE WHEN (producttype_all = '%s' and save_amount_premium is not null) then toFloat64(quantity) else 0 end), " +
                " 0) as premiumSavePerQuantity, ", premium, premium));


        stringBuilder.append("SUM(coalesce(save_amount_promotion, 0)) as promotionSave, ");
        stringBuilder.append("if(SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(save_amount_promotion)) * 100 / SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then toFloat64(corp_real_pay) else 0 end)," +
                " 0) as promotionSaveRate, ");
        stringBuilder.append("if(SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then quantity else 0 end) != 0, " +
                " toFloat64(SUM(save_amount_promotion)) / SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then toFloat64(quantity) else 0 end)," +
                " 0) as promotionSavePerQuantity, ");

        stringBuilder.append("SUM(coalesce(saving_price, 0)) as controlSave,");
        stringBuilder.append("if(sum(case when coalesce(saving_price, 0) != 0 then coalesce(corp_real_pay, 0) else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(saving_price, 0))) * 100 / toFloat64(sum(case when coalesce(saving_price, 0) != 0 then coalesce(corp_real_pay, 0) else 0 end)), " +
                " 0) AS controlSaveRate, ");
        stringBuilder.append("if(sum(case when coalesce(saving_price, 0) != 0 then coalesce(quantity, 0) else 0 end) != 0, " +
                " toFloat64(sum(coalesce(saving_price, 0))) / toFloat64(sum(case when coalesce(saving_price, 0) != 0 then coalesce(quantity, 0) else 0 end)), " +
                " 0) AS controlSavePerQuantity ");

        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        stringBuilder.append(SaveCondition.getHotelCondition(productType));
        return stringBuilder;
    }

    public StringBuilder hotelAccProportion(String productType, String statisticalCaliber) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("min(%s) as minReportDate,",
                    ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("min(%s) as minReportDate,",
                    REPORT_DATE)
            );
        }
        stringBuilder.append("SUM(coalesce(save_amount_3c, 0)) as cSave, ");
        stringBuilder.append("SUM(coalesce(save_amount_premium, 0)) as premiumSave, ");
        stringBuilder.append("SUM(coalesce(save_amount_promotion, 0)) as promotionSave, ");
        stringBuilder.append("SUM(coalesce(saving_price, 0)) as controlSave ");
        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        stringBuilder.append("({scope_condition})  ");
        stringBuilder.append(SaveCondition.getHotelCondition(productType));
        return stringBuilder;
    }

    public StringBuilder hotelProportionDetail(String productType, String statisticalCaliber) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select ");
        stringBuilder.append(String.format("if(SUM(CASE WHEN (producttype_all = '%s' and save_amount_3c is not null) then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_3c, 0))) * 100 / SUM(CASE WHEN (producttype_all = '%s' and save_amount_3c is not null) " +
                " then toFloat64(corp_real_pay) else 0 end), " +
                " 0) as cSaveRate, ", ta, ta));
        stringBuilder.append(String.format("if(SUM(CASE WHEN (producttype_all = '%s' and save_amount_3c is not null) then quantity else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_3c, 0))) / SUM(CASE WHEN (producttype_all = '%s' and save_amount_3c is not null) " +
                " then toFloat64(quantity) else 0 end), " +
                " 0) as cSavePerQuantity, ", ta, ta));

        stringBuilder.append("SUM(coalesce(save_amount_premium, 0)) as premiumSave, ");
        stringBuilder.append(String.format("if(SUM(CASE WHEN (producttype_all = '%s' and save_amount_premium is not null) then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_premium, 0))) * 100 / " +
                "     SUM(CASE WHEN (producttype_all = '%s' and save_amount_premium is not null) then toFloat64(corp_real_pay) else 0 end), " +
                " 0) as premiumSaveRate, ", premium, premium));


        stringBuilder.append(String.format("if(SUM(CASE WHEN (producttype_all = '%s' and save_amount_premium is not null) then quantity else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_premium, 0))) / " +
                "     SUM(CASE WHEN (producttype_all = '%s' and save_amount_premium is not null) then toFloat64(quantity) else 0 end), " +
                " 0) as premiumSavePerQuantity, ", premium, premium));

        stringBuilder.append("if(SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(save_amount_promotion)) * 100 / SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then toFloat64(corp_real_pay) else 0 end)," +
                " 0) as promotionSaveRate, ");
        stringBuilder.append("if(SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then quantity else 0 end) != 0, " +
                " toFloat64(SUM(save_amount_promotion)) / SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then toFloat64(quantity) else 0 end)," +
                " 0) as promotionSavePerQuantity, ");

        stringBuilder.append("SUM(coalesce(saving_price, 0)) as controlSave, ");
        stringBuilder.append("if(SUM(CASE WHEN coalesce(saving_price, 0) != 0 then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(saving_price)) * 100 / SUM(CASE WHEN coalesce(saving_price, 0) != 0 then toFloat64(corp_real_pay) else 0 end)," +
                " 0) as controlSaveRate, ");
        stringBuilder.append("if(sum(case when coalesce(saving_price, 0) != 0 then coalesce(quantity, 0) else null end) != 0, " +
                " toFloat64(SUM(coalesce(saving_price, 0))) / toFloat64(sum(case when coalesce(saving_price, 0) != 0 then coalesce(quantity, 0) else null end)), " +
                " 0) AS controlSavePerQuantity ");

        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        stringBuilder.append(SaveCondition.getHotelCondition(productType));
        return stringBuilder;
    }

    private String conditionWrapSql(String start, String end, String sqlTemplate,
                                    BaseQueryConditionDTO baseQueryConditionDTO) {
        String scope_condition = OnlineReportOverviewTrendDao.buildScopeFilter(baseQueryConditionDTO);
        sqlTemplate = sqlTemplate.replace("{scope_condition}", scope_condition).replace("{start}", start).replace("{end}", end);
        return sqlTemplate;
    }

    private String conditionWrapDetailSql(String start, String end, String sqlTemplate,
                                          List<String> industries, String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        String industryScopeCondition = buildIndustryScope(industries);
        // 行业
        if (CollectionUtils.isNotEmpty(industries) && StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T")
                && StringUtils.isNotEmpty(consumptionLevel)) {
            industryScopeCondition += " and consumptionlevel = '" + consumptionLevel + "' ";
        }
        // 商旅
        if (CollectionUtils.isEmpty(industries) && StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T")
                && StringUtils.isNotEmpty(consumptionLevel)) {
            industryScopeCondition += " and consumptionlevel = '" + consumptionLevel + "' ";
        }
        sqlTemplate = sqlTemplate.replace("{scope_condition}", industryScopeCondition)
                .replace("{start}", start).replace("{end}", end);
        return sqlTemplate;
    }

    public static String buildIndustryScope(List<String> industries) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" 1 = 1");
        if (CollectionUtils.isNotEmpty(industries) && StringUtils.isNotEmpty(industries.get(0))) {
            // industry是由#连接的两个值，前面是大类，后面是小类
            String[] strs = industries.get(0).split("#");
            // strs[0]行业d大类，strs[1]行业小类
            if (ArrayUtils.isNotEmpty(strs) && strs.length == OrpConstants.TWO && StringUtils.isNotEmpty(strs[1]) && StringUtils.isNotEmpty(strs[1])) {
                stringBuilder.append(String.format(" and std_industry2 =  '%s'", strs[1]));
            } else if (ArrayUtils.isNotEmpty(strs) && StringUtils.isNotEmpty(strs[0]) && StringUtils.isNotEmpty(strs[0])) {
                stringBuilder.append(String.format(" and std_industry1 =  '%s'", strs[0]));
            }
        }
        return stringBuilder.toString();
    }

    private PreparedStatement mapRequest(OnlineReportSaveProportionRequestDTO requestDto, PreparedStatement statement) {
        return statement;
    }

    private PreparedStatement mapRequest2(OnlineReportSaveProportionDetailRequestDTO requestDto, PreparedStatement statement) {
        return statement;
    }

    private PreparedStatement mapRequest2(List<Object> parmList, PreparedStatement statement) {
        return statement;
    }

}
