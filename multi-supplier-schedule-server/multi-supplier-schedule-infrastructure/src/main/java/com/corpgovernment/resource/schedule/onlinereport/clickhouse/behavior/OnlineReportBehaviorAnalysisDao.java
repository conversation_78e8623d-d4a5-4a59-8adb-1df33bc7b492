package com.corpgovernment.resource.schedule.onlinereport.clickhouse.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.behaivor.HotelAvgNightPriceDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.behaivor.RefundRebookTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
public class OnlineReportBehaviorAnalysisDao extends AbstractCommonDao {

    /**
     * 预订方式
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> bookTypeAnalysis(BaseQueryConditionDTO requestDto, QueryReportBuTypeEnum queryReportBuTypeEnum,
                                        String productType, Class<T> clazz, Map<String, String> extMap) throws Exception {
        String dateField = getDateFieldByCaliber(requestDto.getStatisticalCaliber());
        ClickHouseTable clickHouseTable = anaylsisDownloadTable(queryReportBuTypeEnum);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select is_online");
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            sqlBuilder.append(",sum(coalesce(real_pay_with_servicefee)) as totalAmount");
        } else {
            sqlBuilder.append(",sum(coalesce(real_pay)) as totalAmount");
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            sqlBuilder.append(", count(distinct case when (refund_status <> 'S' OR quantity = 1) then order_id end ) " +
                    "- count(distinct case when quantity = -1 then order_id end ) as totalOrderCount");
        } else if (queryReportBuTypeEnum == QueryReportBuTypeEnum.car) {
            dateField = OrpConstants.ORDERDT;
            sqlBuilder.append(", sum(coalesce(cnt_order,0)) as totalOrderCount");
        } else {
            sqlBuilder.append(", count(distinct case when is_refund = 'F' then order_id end ) " +
                    "- count(distinct case when is_refund = 'T' then order_id end ) as totalOrderCount");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(clickHouseTable);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, dateField));
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            String contractType = Optional.ofNullable(extMap).orElse(new HashMap<>()).get("contractType");
            sqlBuilder.append(getHotelOrderTypeCondition(productType));
            sqlBuilder.append(getHtlContractTypeCondition(contractType));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            sqlBuilder.append(getTrainOrderStatusCondition());
        }
        sqlBuilder.append(" group by is_online  ");

        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 酒店支付方式
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> hotelBalanceTypeAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable;
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL_ODT;// todo 预订口径
        } else {
            // 默认使用成交口径
            clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL;
        }
        String partition = queryPartition(clickHouseTable);

        sqlBuilder.append("select balancetype");
        sqlBuilder.append(", sum(coalesce(quantity,0)) as totalQuantity, sum(coalesce(room_price,0)) as totalRoomPrice, " +
                "sum(coalesce(real_pay,0)) as totalAmount ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partition));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(" group by balancetype  ");

        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 机票提前预定天数
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> flightPreOrderDateAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(" select case ");
        sqlBuilder.append(" WHEN pre_order_date <= 0 THEN '0' ");
        sqlBuilder.append(" WHEN pre_order_date >= 1 AND pre_order_date <= 3 THEN '1-3' ");
        sqlBuilder.append(" WHEN pre_order_date >= 4 AND pre_order_date <= 7 THEN '4-7' ");
        sqlBuilder.append(" WHEN pre_order_date >= 8 AND pre_order_date <= 15 THEN '8-15' ");
        sqlBuilder.append(" WHEN pre_order_date >= 16 THEN '16' ELSE toString(pre_order_date) END AS preOrderDateRange ");
        sqlBuilder.append(" , SUM(quantity) AS totalQuantity ");
        sqlBuilder.append(" , SUM(case when class_type = 'Y' then coalesce(netfare, 0) + coalesce(rebook_price_differential, 0) else 0 end) AS totalEconomyPrice ");
        sqlBuilder.append(" , SUM(case when class_type = 'Y' then coalesce(price_rate, 0)*coalesce(quantity,0) else 0 end) AS totalEconomyPriceRate ");
        sqlBuilder.append(" , SUM(case when class_type = 'Y' then coalesce(quantity,0) else 0 end) AS totalEconomyQuantity ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" and pre_order_date >=0 ");
        sqlBuilder.append(" and flight_class = 'N' ");
        sqlBuilder.append(" group by preOrderDateRange  ");

        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 机票折扣分布
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> flightDiscountAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select case ");
        sqlBuilder.append(" WHEN (toFloat64(price_rate) >= 0 and toFloat64(price_rate) < 0.1) THEN '0-0.09' ");
        sqlBuilder.append(" WHEN (toFloat64(price_rate) >= 0.1 and toFloat64(price_rate) < 0.2) THEN '0.1-0.19' ");
        sqlBuilder.append(" WHEN (toFloat64(price_rate) >= 0.2 and toFloat64(price_rate) < 0.3) THEN '0.2-0.29' ");
        sqlBuilder.append(" WHEN (toFloat64(price_rate) >= 0.3 and toFloat64(price_rate) < 0.4) THEN '0.3-0.39' ");
        sqlBuilder.append(" WHEN (toFloat64(price_rate) >= 0.4 and toFloat64(price_rate) < 0.5) THEN '0.4-0.49' ");
        sqlBuilder.append(" WHEN (toFloat64(price_rate) >= 0.5 and toFloat64(price_rate) < 0.6) THEN '0.5-0.59' ");
        sqlBuilder.append(" WHEN (toFloat64(price_rate) >= 0.6 and toFloat64(price_rate) < 0.7) THEN '0.6-0.69' ");
        sqlBuilder.append(" WHEN (toFloat64(price_rate) >= 0.7 and toFloat64(price_rate) < 0.8) THEN '0.7-0.79' ");
        sqlBuilder.append(" WHEN (toFloat64(price_rate) >= 0.8 and toFloat64(price_rate) < 0.9) THEN '0.8-0.89' ");
        sqlBuilder.append(" WHEN (toFloat64(price_rate) >= 0.9 and toFloat64(price_rate) < 1) THEN '0.9-0.99' ");
        sqlBuilder.append(" WHEN toFloat64(price_rate) >= 1 THEN '1' else 'other' END AS priceRateRange ");
        sqlBuilder.append(" , SUM(quantity) AS totalQuantity ");
        sqlBuilder.append(" , SUM(coalesce(real_pay, 0)) AS totalAmount ");
        sqlBuilder.append(" , SUM(coalesce(netfare, 0) + coalesce(rebook_price_differential, 0)) AS totalPrice ");
        sqlBuilder.append(" , SUM(pre_order_date*quantity) AS totalPreOrderDate ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        // 国内经济仓
        sqlBuilder.append(" and flight_class = 'N' ");
        sqlBuilder.append(" and class_type = 'Y' ");
        sqlBuilder.append(" and price_rate >= 0 ");
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" group by priceRateRange  ");
        sqlBuilder.append(" having priceRateRange is not null  ");

        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 机票提前预定天数
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> hotelPreOrderDateAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(" select case ");
        sqlBuilder.append(" WHEN dateDiff('day', toDateTime(order_date), toDateTime(arrival_date_time)) = 0 THEN '0' ");
        sqlBuilder.append(" WHEN dateDiff('day', toDateTime(order_date), toDateTime(arrival_date_time)) >= 1 AND dateDiff('day', toDateTime(order_date)" +
                ", toDateTime(arrival_date_time)) <= 3 THEN '1-3' ");
        sqlBuilder.append(" WHEN dateDiff('day', toDateTime(order_date), toDateTime(arrival_date_time)) >= 4 AND dateDiff('day', toDateTime(order_date)" +
                ", toDateTime(arrival_date_time)) <= 7 THEN '4-7' ");
        sqlBuilder.append(" WHEN dateDiff('day', toDateTime(order_date), toDateTime(arrival_date_time)) >= 8 AND dateDiff('day', toDateTime(order_date)" +
                ", toDateTime(arrival_date_time)) <= 15 THEN '8-15' ");
        sqlBuilder.append(" WHEN dateDiff('day', toDateTime(order_date), toDateTime(arrival_date_time)) >= 16 THEN '16' ELSE 'other' END AS preOrderDateRange ");
        sqlBuilder.append(" , SUM(quantity) AS totalQuantity ");
        sqlBuilder.append(" , SUM(coalesce(room_price, 0)) AS totalRoomPrice ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(" group by preOrderDateRange  ");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    private ClickHouseTable anaylsisDetailTable(QueryReportBuTypeEnum buTypeEnum, String statisticalCaliber) {
        ClickHouseTable clickHouseTable = null;
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            switch (buTypeEnum) {
                case flight:
                    clickHouseTable = ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL_ODT;
                    break;
                case hotel:
                    clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL_ODT;
                    break;
                case train:
                    clickHouseTable = ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL_ODT;
                    break;
                default:
            }
        } else {
            // 默认使用成交口径
            switch (buTypeEnum) {
                case flight:
                    clickHouseTable = ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL;
                    break;
                case hotel:
                    clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL;
                    break;
                case train:
                    clickHouseTable = ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL;
                    break;
                default:
            }
        }
        return clickHouseTable;
    }

    private ClickHouseTable anaylsisDownloadTable(QueryReportBuTypeEnum buTypeEnum) {
        ClickHouseTable clickHouseTable = null;
        switch (buTypeEnum) {
            case flight:
                clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
                break;
            case hotel:
                clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
                break;
            case train:
                clickHouseTable = ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;
                break;
            case car:
                clickHouseTable = ClickHouseTable.OLRPT_INDEXCARDOWNLOAD;
                break;
            default:
        }
        return clickHouseTable;
    }

    /**
     * 退票分析
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> refundAnalysis(BaseQueryConditionDTO requestDto, QueryReportBuTypeEnum queryReportBuTypeEnum, String productType, Class<T> clazz) throws Exception {
        ClickHouseTable clickHouseTable = anaylsisDetailTable(queryReportBuTypeEnum, requestDto.getStatisticalCaliber());
        String partition = queryPartition(clickHouseTable);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(" sum(coalesce(refundtkt,0) ) as total_refundtkt ");
            sqlBuilder.append(", sum(coalesce(ordertkt,0) ) as total_ordertkt ");
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            sqlBuilder.append(" abs(sum(coalesce(rfd_quantity,0))) as total_refundtkt ");
            sqlBuilder.append(", sum(coalesce(quantity,0) ) as total_ordertkt ");
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            sqlBuilder.append(" abs(sum(coalesce(refund_quantity,0))) as total_refundtkt ");
            sqlBuilder.append(", sum(coalesce(ordertkt,0)) as total_ordertkt ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partition));
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            sqlBuilder.append(getHotelOrderTypeCondition(productType));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(getFlightClassConditionNoAudited(productType));
        }
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }


    /**
     * 退票趋势分析
     *
     * @param requestDto
     * @param queryReportBuTypeEnum
     * @param productType
     * @return
     * @throws Exception
     */
    public List<RefundRebookTrendDTO> refundAnalysis(BaseQueryConditionDTO requestDto, QueryReportBuTypeEnum queryReportBuTypeEnum, String productType) throws Exception {
        ClickHouseTable clickHouseTable;
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            clickHouseTable = anaylsisDownloadTable(queryReportBuTypeEnum);
        } else {
            clickHouseTable = anaylsisDetailTable(queryReportBuTypeEnum, requestDto.getStatisticalCaliber());
        }
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select firstday_of_month as date");
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(", sum(coalesce(refundtkt,0) ) as total_refundtkt ");
            sqlBuilder.append(", sum(coalesce(ordertkt,0) ) as total_ordertkt ");
            sqlBuilder.append(", SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0)) + " +
                    "SUM(coalesce(refund_behind_service_fee, 0)) as total_refundloss");
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            sqlBuilder.append(", sum(abs(coalesce(rfd_quantity,0))) as total_refundtkt ");
            sqlBuilder.append(", sum(coalesce(quantity,0) ) as total_ordertkt ");
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            sqlBuilder.append(", sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as total_refundtkt ");
            sqlBuilder.append(", sum(case when  quantity > 0 then abs(coalesce(quantity,0)) else 0 end) as total_ordertkt ");
            sqlBuilder.append(", sum(case when refund_status = 'S' THEN coalesce(ticket_price,0) + coalesce(refund_ticket_fee,0) else 0 end) as total_refundloss ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(clickHouseTable);
        String dateField = getDateFieldByCaliber(requestDto.getStatisticalCaliber());
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            // download 表
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, dateField));
            sqlBuilder.append(getTrainOrderStatusCondition());
        } else {
            // detail 表
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partition));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            sqlBuilder.append(getHotelOrderTypeCondition(productType));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(getFlightClassConditionNoAudited(productType));
        }
        sqlBuilder.append(" group by firstday_of_month  ");
        // 查询clickhouse
        return commonList(RefundRebookTrendDTO.class, sqlBuilder.toString(), parmList);
    }

    /**
     * 退票趋势分析
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param queryReportBuTypeEnum
     * @param productType
     * @param dataTypeEnum
     * @param industryList
     * @param compareSameLevel      是否对比同级比较
     * @param consumptionLevel      消费等级
     * @return
     * @throws Exception
     */
    
    public List<RefundRebookTrendDTO> refundAnalysisCorpAndIndustry(String startTime, String endTime, String statisticalCaliber, QueryReportBuTypeEnum queryReportBuTypeEnum,
                                                                    String productType, DataTypeEnum dataTypeEnum, List<String> industryList,
                                                                    String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        ClickHouseTable clickHouseTable;
        if (dataTypeEnum == DataTypeEnum.INDUSTRY) {
            clickHouseTable = anaylsisDownloadTable(queryReportBuTypeEnum);
        } else {
            clickHouseTable = anaylsisDetailTable(queryReportBuTypeEnum, statisticalCaliber);
        }
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select firstday_of_month as date");
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(", sum(coalesce(refundtkt,0) ) as total_refundtkt ");
            sqlBuilder.append(", sum(coalesce(ordertkt,0) ) as total_ordertkt ");
            sqlBuilder.append(", SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0)) + " +
                    "SUM(coalesce(refund_behind_service_fee, 0)) as total_refundloss");
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            if (dataTypeEnum == DataTypeEnum.INDUSTRY) {
                sqlBuilder.append(", sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as total_refundtkt ");
            } else {
                sqlBuilder.append(", sum(abs(coalesce(rfd_quantity,0))) as total_refundtkt ");
            }
            sqlBuilder.append(", sum(coalesce(quantity,0) ) as total_ordertkt ");
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            if (dataTypeEnum == DataTypeEnum.CORP) {
                sqlBuilder.append(", sum(abs(coalesce(refund_quantity,0))) as total_refundtkt ");
                sqlBuilder.append(", sum(coalesce(ordertkt,0)) as total_ordertkt ");
            } else {
                sqlBuilder.append(", sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as total_refundtkt ");
                sqlBuilder.append(", sum(case when  quantity > 0 then abs(coalesce(quantity,0)) else 0 end) as total_ordertkt ");
            }
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(clickHouseTable);
        String dateField = getDateFieldByCaliber(statisticalCaliber);
        boolean ignoreTenantId = false;
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            // download 表
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, dateField));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            // detail 表
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList, partition));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            sqlBuilder.append(getHotelOrderTypeCondition(productType));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            if (dataTypeEnum == DataTypeEnum.INDUSTRY) {
                sqlBuilder.append(getFlightClassConditionWithAudited(productType));
            } else {
                sqlBuilder.append(getFlightClassConditionNoAudited(productType));
            }
        }
        sqlBuilder.append(" group by firstday_of_month  ");

        // 查询clickhouse
        return commonList(RefundRebookTrendDTO.class, sqlBuilder.toString(), parmList, ignoreTenantId);
    }

    /**
     * 改签趋势分析
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> rebookAnalysis(BaseQueryConditionDTO requestDto, QueryReportBuTypeEnum queryReportBuTypeEnum
            , String productType, Class<T> clazz) throws Exception {
        ClickHouseTable clickHouseTable = anaylsisDownloadTable(queryReportBuTypeEnum);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(" sum(case when is_rebook = 'T' then coalesce(quantity,0) else 0 end) as total_rebooktkt, sum(coalesce(ordertkt,0)) as total_ordertkt ");
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            sqlBuilder.append(" sum(coalesce(change_quantity,0)) as total_rebooktkt, sum(case when  quantity > 0 then abs(coalesce(quantity,0)) else 0 end) as total_ordertkt ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            sqlBuilder.append(getTrainOrderStatusCondition());
        }
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 改签趋势分析
     *
     * @param requestDto
     * @param queryReportBuTypeEnum
     * @param productType
     * @return
     * @throws Exception
     */
    public List<RefundRebookTrendDTO> rebookAnalysis(BaseQueryConditionDTO requestDto, QueryReportBuTypeEnum queryReportBuTypeEnum, String productType) throws Exception {
        ClickHouseTable clickHouseTable = anaylsisDownloadTable(queryReportBuTypeEnum);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select firstday_of_month as date");
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(", sum(case when  is_rebook = 'T' then coalesce(quantity,0) else 0 end) as total_rebooktkt ");
            sqlBuilder.append(", sum(coalesce(ordertkt,0)) as total_ordertkt ");
            sqlBuilder.append(", SUM(coalesce(change_fee,0))+SUM(coalesce(rebook_price_differential,0))+SUM(coalesce(rebook_service_fee,0))" +
                    "+ SUM(if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0)))+SUM(coalesce(rebook_behind_service_fee, 0)) as total_rebookloss");
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            sqlBuilder.append(", sum(coalesce(change_quantity,0)) as total_rebooktkt ");
            sqlBuilder.append(", sum(case when  quantity > 0 then abs(coalesce(quantity,0)) else 0 end) as total_ordertkt ");
            sqlBuilder.append(", sum(coalesce(changebalance,0))+sum(coalesce(deal_change_service_fee,0))+sum(coalesce(afterchangeservicefee,0)) as total_rebookloss ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(clickHouseTable);
        String dateField = getDateFieldByCaliber(requestDto.getStatisticalCaliber());
        // download 表
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partition));
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(getFlightClassConditionNoAudited(productType));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            sqlBuilder.append(getTrainOrderStatusCondition());
        }
        sqlBuilder.append(" group by firstday_of_month  ");

        // 查询clickhouse
        return commonList(RefundRebookTrendDTO.class, sqlBuilder.toString(), parmList);
    }

    /**
     * 改签趋势分析
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param queryReportBuTypeEnum
     * @param productType
     * @param dataTypeEnum
     * @param industryList
     * @param compareSameLevel      是否对比同级公司比较
     * @param consumptionLevel      公司消费等级
     * @return
     * @throws Exception
     */
    
    public List<RefundRebookTrendDTO> rebookAnalysisCorpAndUndustry(String startTime, String endTime, String statisticalCaliber, QueryReportBuTypeEnum queryReportBuTypeEnum,
                                                                    String productType, DataTypeEnum dataTypeEnum, List<String> industryList,
                                                                    String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        ClickHouseTable clickHouseTable = anaylsisDownloadTable(queryReportBuTypeEnum);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select firstday_of_month as date");
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            sqlBuilder.append(", sum(case when  is_rebook = 'T' then coalesce(quantity,0) else 0 end) as total_rebooktkt ");
            sqlBuilder.append(", sum(coalesce(ordertkt,0)) as total_ordertkt ");
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            sqlBuilder.append(", sum(coalesce(change_quantity,0)) as total_rebooktkt ");
            sqlBuilder.append(", sum(case when  quantity > 0 then abs(coalesce(quantity,0)) else 0 end) as total_ordertkt ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(clickHouseTable);
        String dateField = getDateFieldByCaliber(statisticalCaliber);
        boolean ignoreTenantId = false;
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            // download 表
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, dateField));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            // download 表
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList, partition));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
            if (dataTypeEnum == DataTypeEnum.INDUSTRY) {
                sqlBuilder.append(getFlightClassConditionWithAudited(productType));
            } else {
                sqlBuilder.append(getFlightClassConditionNoAudited(productType));
            }
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
            sqlBuilder.append(getTrainOrderStatusCondition());
        }
        sqlBuilder.append(" group by firstday_of_month  ");

        // 查询clickhouse
        return commonList(RefundRebookTrendDTO.class, sqlBuilder.toString(), parmList, ignoreTenantId);
    }

    /**
     * 酒店星级分析
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> hotelStarAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select star ");
        sqlBuilder.append(" , SUM(quantity) AS totalQuantity ");
        sqlBuilder.append(" , CASE WHEN toFloat64(SUM(quantity)) != 0 THEN toFloat64(SUM(coalesce(room_price, 0))) / toFloat64(SUM(quantity)) ELSE 0 END AS avgNightPrice ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(" group by star  ");

        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * @param requestDto
     * @param clazz
     * @param industryList
     * @param extParams
     * @param compareSameLevel 是否对比同级公司比较
     * @param consumptionLevel 公司消费等级
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> hotelStarAnalysisWithIndustry(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> industryList, Map<String, String> extParams,
                                                     String compareSameLevel, String consumptionLevel) throws Exception {
        String dateField = getDateFieldByCaliber(requestDto.getStatisticalCaliber());
        List<Object> parmList = new ArrayList<>();
        String orderType = extParams.get("htlOrderType");
        String contractType = extParams.get("contractType");
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select if(company.star != null, company.star, if(corp.star != null, corp.star, industry.star)) as star ");
        sqlBuilder.append(" ,company.totalQuantity as totalQuantity");
        sqlBuilder.append(" ,company.avgNightPrice as avgNightPrice");
        sqlBuilder.append(" ,corp.totalQuantity as corpTotalQuantity");
        sqlBuilder.append(" ,industry.totalQuantity as industryTotalQuantity ");
        sqlBuilder.append(" from(select star ");
        sqlBuilder.append(" , SUM(quantity) AS totalQuantity ");
        sqlBuilder.append(" , CASE WHEN toFloat64(SUM(quantity)) != 0 THEN toFloat64(SUM(coalesce(room_price, 0))) / toFloat64(SUM(quantity)) ELSE 0 END AS avgNightPrice ");
        sqlBuilder.append(OrpConstants.FROM).append(clickHouseTable.getTable()).append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, dateField));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(getHtlTypeCondition(orderType));
        sqlBuilder.append(getHtlContractTypeCondition(contractType));
        sqlBuilder.append(" group by star) company  full join ");
        sqlBuilder.append(" (select star ");
        sqlBuilder.append(" , SUM(quantity) AS totalQuantity ");
        sqlBuilder.append(OrpConstants.FROM).append(clickHouseTable.getTable()).append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(requestDto, parmList, partition, dateField));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(getHtlTypeCondition(orderType));
        sqlBuilder.append(getHtlContractTypeCondition(contractType));
        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(" group by star) corp on company.star = corp.star full join ");
        sqlBuilder.append(" (select star ");
        sqlBuilder.append(" , SUM(quantity) AS totalQuantity ");
        sqlBuilder.append(OrpConstants.FROM).append(clickHouseTable.getTable()).append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(requestDto, industryList, partition, parmList, dateField));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(getHtlTypeCondition(orderType));
        sqlBuilder.append(getHtlContractTypeCondition(contractType));
        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(" group by star) industry on if(company.star != null, company.star, corp.star) = industry.star  ");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 酒店间夜均价分布
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    public List<HotelAvgNightPriceDTO> hotelAvgRoomPriceAnalysis(BaseQueryConditionDTO requestDto) throws Exception {
        String dateField = getDateFieldByCaliber(requestDto.getStatisticalCaliber());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT CASE ");
        sqlBuilder.append(" WHEN avgNightPrice >= 0 AND avgNightPrice < 100 THEN '0-100' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 100 AND avgNightPrice < 200 THEN '100-200' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 200 AND avgNightPrice < 300 THEN '200-300' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 300 AND avgNightPrice < 400 THEN '300-400' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 400 AND avgNightPrice < 500 THEN '400-500' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 500 AND avgNightPrice < 600 THEN '500-600' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 600 AND avgNightPrice < 700 THEN '600-700' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 700 AND avgNightPrice < 800 THEN '700-800' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 800 AND avgNightPrice < 900 THEN '800-900' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 900 AND avgNightPrice < 1000 THEN '900-1000' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 1000 AND avgNightPrice < 1500 THEN '1000-1500' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 1500 AND avgNightPrice < 2000 THEN '1500-2000' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 2000 THEN '2000' ");
        sqlBuilder.append(" ELSE 'other' END AS avgNightPriceRange ");
        sqlBuilder.append(" , SUM(quantityTemp) AS totalQuantity ");
        sqlBuilder.append(" , SUM(amountTemp) AS totalAmount ");
        sqlBuilder.append(" FROM ( ");
        sqlBuilder.append(" SELECT order_id ");
        sqlBuilder.append(" , CASE WHEN toFloat64(SUM(quantity)) != 0 THEN toFloat64(SUM(coalesce(room_price, 0))) / toFloat64(SUM(quantity)) ELSE 0 END AS avgNightPrice ");
        sqlBuilder.append(" , SUM(coalesce(quantity, 0)) AS quantityTemp ");
        sqlBuilder.append(" , SUM(coalesce(real_pay_with_servicefee, 0)) AS amountTemp ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, dateField));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(" group by order_id ) t ");
        sqlBuilder.append(" GROUP BY avgNightPriceRange  ");
        // 查询clickhouse
        return commonList(HotelAvgNightPriceDTO.class, sqlBuilder.toString(), parmList);
    }

    /**
     * 酒店间夜均价分布
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param dataTypeEnum
     * @param industryList
     * @param productType
     * @param compareSameLevel   是否对比同级公司比较
     * @param consumptionLevel   公司消费等级
     * @return
     * @throws Exception
     */
    
    public List<HotelAvgNightPriceDTO> hotelAvgRoomPriceAnalysisCorpAndIndustry(String startTime, String endTime, String statisticalCaliber, DataTypeEnum dataTypeEnum,
                                                                                List<String> industryList, String productType,
                                                                                String compareSameLevel, String consumptionLevel,
                                                                                String compareCorpSameLevel) throws Exception {
        String dateField = getDateFieldByCaliber(statisticalCaliber);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT CASE ");
        sqlBuilder.append(" WHEN avgNightPrice >= 0 AND avgNightPrice < 100 THEN '0-100' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 100 AND avgNightPrice < 200 THEN '100-200' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 200 AND avgNightPrice < 300 THEN '200-300' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 300 AND avgNightPrice < 400 THEN '300-400' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 400 AND avgNightPrice < 500 THEN '400-500' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 500 AND avgNightPrice < 600 THEN '500-600' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 600 AND avgNightPrice < 700 THEN '600-700' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 700 AND avgNightPrice < 800 THEN '700-800' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 800 AND avgNightPrice < 900 THEN '800-900' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 900 AND avgNightPrice < 1000 THEN '900-1000' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 1000 AND avgNightPrice < 1500 THEN '1000-1500' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 1500 AND avgNightPrice < 2000 THEN '1500-2000' ");
        sqlBuilder.append(" WHEN avgNightPrice >= 2000 THEN '2000' ");
        sqlBuilder.append(" ELSE 'other' END AS avgNightPriceRange ");
        sqlBuilder.append(" , SUM(quantityTemp) AS totalQuantity ");
        sqlBuilder.append(" , SUM(amountTemp) AS totalAmount ");
        sqlBuilder.append(" FROM ( ");
        sqlBuilder.append(" SELECT order_id ");
        sqlBuilder.append(" , CASE WHEN toFloat64(SUM(quantity)) != 0 THEN toFloat64(SUM(coalesce(room_price, 0))) / toFloat64(SUM(quantity)) ELSE 0 END AS avgNightPrice ");
        sqlBuilder.append(" , SUM(coalesce(quantity, 0)) AS quantityTemp ");
        sqlBuilder.append(" , SUM(coalesce(real_pay_with_servicefee, 0)) AS amountTemp ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        if (dataTypeEnum == DataTypeEnum.CORP) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, dateField));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (dataTypeEnum == DataTypeEnum.INDUSTRY) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, dateField));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        sqlBuilder.append(getHotelOrderTypeCondition(productType));
        sqlBuilder.append(" group by order_id ) t ");
        sqlBuilder.append(" GROUP BY avgNightPriceRange  ");
        // 查询clickhouse
        return commonList(HotelAvgNightPriceDTO.class, sqlBuilder.toString(), parmList);
    }

    /**
     * @param requestDto
     * @param clazz
     * @param industryList
     * @param compareSameLevel 是否对比同级公司比较
     * @param consumptionLevel 公司消费等级
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> hotelAvgRoomPriceOverview(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> industryList,
                                                 String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        String dateField = getDateFieldByCaliber(requestDto.getStatisticalCaliber());
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT  company.avgNightPrice as companyMetric, corp.avgNightPrice as corpMetric, industry.avgNightPrice as industryMetric");
        sqlBuilder.append(" FROM ( ");
        sqlBuilder.append(" SELECT CASE WHEN toFloat64(SUM(quantity)) != 0 THEN toFloat64(SUM(coalesce(room_price, 0))) / toFloat64(SUM(quantity)) ELSE 0 END AS avgNightPrice ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, dateField));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType())).append(") company cross join (");
        sqlBuilder.append(" SELECT CASE WHEN toFloat64(SUM(quantity)) != 0 THEN toFloat64(SUM(coalesce(room_price, 0))) / toFloat64(SUM(quantity)) ELSE 0 END AS avgNightPrice ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(requestDto, parmList, partition, dateField));
        if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType())).append(") corp cross join (");
        sqlBuilder.append(" SELECT CASE WHEN toFloat64(SUM(quantity)) != 0 THEN toFloat64(SUM(coalesce(room_price, 0))) / toFloat64(SUM(quantity)) ELSE 0 END AS avgNightPrice ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(requestDto, industryList, partition, parmList, dateField));
        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType())).append(") industry");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 拼房模式分布
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> zoningPatternAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append(String.format("coalesce(sum(case when houseshare_mode_type = '%s' " +
                        "then quantity else 0 end), 0) as room_together, ",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.room")));
        sqlBuilder.append(String.format("coalesce(sum(case when houseshare_mode_type = '%s' " +
                        "then quantity else 0 end), 0) as travel_together, ",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.travel")));
        sqlBuilder.append(String.format("coalesce(sum(case when houseshare_mode_type = '%s' " +
                        "then quantity else 0 end), 0) as all_together ",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.all")));
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));

        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 费用分摊模式分布
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> costAllocationPatternAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append("coalesce(sum(case when allocation_mode = 'NONE' then quantity else 0 end), 0) as not_allocation, ");
        sqlBuilder.append("coalesce(sum(case when allocation_mode = 'ORDER' then quantity else 0 end), 0) as by_order, ");
        sqlBuilder.append("coalesce(sum(case when allocation_mode = 'ROOM' then quantity else 0 end), 0) as by_room, ");
        sqlBuilder.append("coalesce(sum(case when allocation_mode = 'TRAVEL_CONTROL' then quantity else 0 end), 0) as by_control ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));

        sqlBuilder.append(String.format(" and houseshare_mode_type in ('%s', '%s', '%s') ",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.room"),
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.travel"),
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.all")
        ));
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 拼房间夜数趋势
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> roomShareTrendPatternAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select firstday_of_month as date, ");
        sqlBuilder.append(String.format("coalesce(sum(case when houseshare_mode_type in ('%s', '%s', '%s') " +
                                "then quantity else 0 end), 0) as room_share_quantity, ",
                        OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.room"),
                        OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.travel"),
                        OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.all")
                )
        );
        sqlBuilder.append(String.format("coalesce(sum(case when houseshare_mode_type in ('%s', '%s', '%s') " +
                                "then coalesce(real_pay_with_servicefee, 0) else 0 end), 0) as room_share_amount, ",
                        OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.room"),
                        OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.travel"),
                        OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.all")
                )
        );
        sqlBuilder.append(String.format("coalesce(sum(case when coalesce(houseshare_mode_type, '') in ('', '%s') " +
                                "then quantity else 0 end), 0) as no_room_share_quantity ",
                        OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.single")
                )
        );

        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));

        sqlBuilder.append(" group by firstday_of_month  ");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 是否有拼房数据
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> roomShareOpenPatternAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select 1 as placeholder ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList, partition));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(" and firstday_of_month >= '2022-01' ");
        sqlBuilder.append(String.format(" and houseshare_mode_type in ('%s', '%s', '%s') ",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.room"),
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.travel"),
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.roomshare.all")
        ));
        sqlBuilder.append(" limit 2 ");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }
}
