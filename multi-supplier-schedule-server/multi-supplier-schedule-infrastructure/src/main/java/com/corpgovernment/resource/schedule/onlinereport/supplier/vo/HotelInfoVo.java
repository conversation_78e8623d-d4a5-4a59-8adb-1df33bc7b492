package com.corpgovernment.resource.schedule.onlinereport.supplier.vo;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2025/2/10
 */
@Data
public class HotelInfoVo {
    /**
     * 酒店ID
     */
    @Column(name = "hotel")
    @Type(value = Types.VARCHAR)
    private String hotelId;
    /**
     * 酒店名称
     */
    @Column(name = "htlName")
    @Type(value = Types.VARCHAR)
    private String hotelName;
    /**
     * 酒店所属城市ID
     */
    @Column(name = "city")
    @Type(value = Types.VARCHAR)
    private String htlCityId;
    /**
     * 酒店所属城市名称
     */
    @Column(name = "cyName")
    @Type(value = Types.VARCHAR)
    private String htlCityName;
    /**
     * 酒店所属集团ID
     */
    @Column(name = "mgrgroupid")
    @Type(value = Types.VARCHAR)
    private String htlGroupId;
    /**
     * 酒店所属集团名称
     */
    @Column(name = "agreementHotelGroupName")
    @Type(value = Types.VARCHAR)
    private String agreementMgrGroupName;
    /**
     * 是否直连酒店集团
     */
    @Column(name = "directGroupTag")
    @Type(value = Types.INTEGER)
    private Integer isDirectGroup;
}
