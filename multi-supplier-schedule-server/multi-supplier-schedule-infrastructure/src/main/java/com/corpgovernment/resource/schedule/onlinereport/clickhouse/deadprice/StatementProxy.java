package com.corpgovernment.resource.schedule.onlinereport.clickhouse.deadprice;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.sql.Statement;

/**
 * <AUTHOR>
 */
public class StatementProxy implements InvocationHandler {
    private final Statement target;


    public StatementProxy(Statement target) {
        this.target = target;
    }

    public static Statement newInstance(Statement stmt) {
        return (Statement) Proxy.newProxyInstance(
                stmt.getClass().getClassLoader(),
                new Class<?>[]{Statement.class},
                new StatementProxy(stmt)
        );
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        // 拦截executeQuery、executeUpdate、execute等方法
        if ("executeQuery".equals(method.getName()) || "executeUpdate".equals(method.getName()) || "execute".equals(method.getName())) {
            System.out.println("Executing SQL: " + args);
        }
        return method.invoke(target, args);
    }
}
