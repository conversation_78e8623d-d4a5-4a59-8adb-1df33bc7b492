package com.corpgovernment.resource.schedule.screen.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TotalAmountDO {


    /**
     * 国内机票总额
     */
    private BigDecimal domesticAirfareTotal;

    /**
     * 国际机票总额
     */
    private BigDecimal internationalAirfareTotal;

    /**
     * 国内酒店总额
     */
    private BigDecimal domesticHotelTotal;

    /**
     * 国际酒店总额
     */
    private BigDecimal internationalHotelTotal;

    /**
     * 火车票总额
     */
    private BigDecimal trainTicketTotal;

    /**
     * 用车总额
     */
    private BigDecimal carRentalTotal;
}
