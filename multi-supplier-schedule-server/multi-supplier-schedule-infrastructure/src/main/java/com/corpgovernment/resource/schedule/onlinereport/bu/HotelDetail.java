package com.corpgovernment.resource.schedule.onlinereport.bu;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailAmountDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailTicketDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportDataDetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReprotBodyData;
import com.corpgovernment.resource.schedule.onlinereport.abs.AbstractOnlineDetailService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportHotelDao;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportHotelDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.DetailReportTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.HotelReportEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.ReportDetailHeaderEnum;
import com.corpgovernment.resource.schedule.onlinereport.factory.OnlineDetailExecute;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCollectionUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.AtomicDouble;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.service.factory.test
 * @description: 酒店产线
 * @author: md_wang
 * @create: 2021-11-08 10:34
 **/
@Service
@Slf4j
public class HotelDetail extends AbstractOnlineDetailService implements OnlineDetailExecute {

    private OnlineReportHotelDao hotelDao;

    public HotelDetail(OnlineReportHotelDao hotelDao) {
        this.hotelDao = hotelDao;
    }

    @Override
    
    public OnlineReportData queryDetailRecord(OnlineReportDetailRequest request) {
        // 金额明细
        Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> amountHeader =
                mapperHeader(DetailReportTypeEnum.DETAIL_AMOUNT_REPORT, request.queryBu, request.lang);
        // 票张明细
        Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> ticketHeader =
                mapperHeader(DetailReportTypeEnum.DETAIL_TICKET_REPORT, request.queryBu, request.lang);
        if (BlueSpaceUtils.isForeign(request.getBasecondition().getPos(), request.getBasecondition().getBlueSpace())) {
            boolean isBlueSpace = BlueSpaceUtils.isBlueSpace(request.getBasecondition().getBlueSpace());
            amountHeader = mapperJPHeader(DetailReportTypeEnum.DETAIL_AMOUNT_REPORT, request.queryBu, request.lang, isBlueSpace);
            ticketHeader = mapperJPHeader(DetailReportTypeEnum.DETAIL_TICKET_REPORT, request.queryBu, request.lang, isBlueSpace);
        }
        try {
            OnlineDetailRequestDto requestDto = mapOnlineDetailRequestDto(request);
            List<OnlineReportHotelDto> hotelList =
                    hotelDao.queryOnlineReportHotel(requestDto, OnlineReportHotelDto.class);
            if (CollectionUtils.isEmpty(hotelList)) {
                return mapHotelAmountTicketEmptyData(amountHeader, ticketHeader);
            }
            return mapHotelAmountTickData(requestDto, hotelList, amountHeader, ticketHeader);
        } catch (Exception e) {
            log.error("queryHotelDetailRecord", ExceptionUtils.getFullStackTrace(e));
            return mapHotelAmountTicketEmptyData(amountHeader, ticketHeader);
        }
    }

    @Override
    public Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> mapperHeader(DetailReportTypeEnum reportTypeEnum,
                                                                           QueryReportBuTypeEnum bu, String lang) {
        return mapperHeaderByBu(reportTypeEnum, bu, lang);
    }

    @Override
    public Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> mapperJPHeader(DetailReportTypeEnum reportTypeEnum,
                                                                             QueryReportBuTypeEnum bu, String lang, boolean isBlueSpace) {
        return mapperJPHeaderByBu(reportTypeEnum, bu, lang, isBlueSpace);
    }

    private OnlineReportData mapHotelAmountTickData(OnlineDetailRequestDto request,
                                                    List<OnlineReportHotelDto> hotelList, Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> amountHeader,
                                                    Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> ticketHeader) {
        DetailAmountDto detailAmountData = new DetailAmountDto();
        DetailTicketDto detailTicketData = new DetailTicketDto();

        // 整体
        DetailData hotelAllResult = mapHotelAllResultData(request, hotelList,
                amountHeader.get(ReportDetailHeaderEnum.HOTEL_ALL_HEADER), OrpConstants.ANY);
        // 协议
        DetailData hotelAgreementResult = mapHotelAllResultData(request,
                hotelList.stream().filter(Objects::nonNull).filter(t -> Objects.nonNull(t.getOrderType()))
                        .filter(t -> StringUtils.equalsIgnoreCase(t.getOrderType(), HotelReportEnum.AGREEMENT.getName()))
                        .collect(Collectors.toList()),
                amountHeader.get(ReportDetailHeaderEnum.HOTEL_AGREEMENT_HEADER), OrpConstants.EMPTY);
        // 非协议
        DetailData hotelNonAgreementResult = mapHotelAllResultData(request,
                hotelList.stream().filter(Objects::nonNull).filter(t -> Objects.nonNull(t.getOrderType()))
                        .filter(t -> StringUtils.equalsIgnoreCase(t.getOrderType(), HotelReportEnum.NOT_AGREEMENT.getName()))
                        .collect(Collectors.toList()),
                amountHeader.get(ReportDetailHeaderEnum.HOTEL_NOT_AGREEMENT_HEADER), OrpConstants.EMPTY);
        // 国内
        DetailData hotelDomesticResult = mapHotelAllResultData(request,
                hotelList.stream().filter(Objects::nonNull).filter(t -> Objects.nonNull(t.getOversea()))
                        .filter(t -> checkHotelOversea(t.getOversea())).collect(Collectors.toList()),
                amountHeader.get(ReportDetailHeaderEnum.HOTEL_DOMESTIC_HEADER), OrpConstants.EMPTY);
        // 国际
        DetailData hotelInternationalResult = mapHotelAllResultData(request,
                hotelList.stream().filter(Objects::nonNull).filter(t -> Objects.nonNull(t.getOversea()))
                        .filter(t -> StringUtils.equalsIgnoreCase(t.getOversea(), HotelReportEnum.INTERNATIONAL.getName()))
                        .collect(Collectors.toList()),
                amountHeader.get(ReportDetailHeaderEnum.HOTEL_INTERNATIONAL_HEADER), OrpConstants.EMPTY);

        detailAmountData.setAllDetail(hotelAllResult);
        detailAmountData.setAgreementtDetail(hotelAgreementResult);
        detailAmountData.setNotAgreementDetail(hotelNonAgreementResult);
        detailAmountData.setDomesticDetail(hotelDomesticResult);
        detailAmountData.setInternationalDetail(hotelInternationalResult);

        // 金额明细
        Map<String, OrpDateTimeUtils.MonthSplitPair> dateYoyMomRange = OrpDateTimeUtils.momSplit(request.getStartTime(), request.getEndTime());
        Map<String, CurrentYoyMomData<OnlineReportHotelDto>> unionData = dateYoyMomRange.entrySet().stream()
                .map(entry -> {
                    CurrentYoyMomData<OnlineReportHotelDto> currentYoyMomData = getCurrentYoyMomHotelData(entry.getValue(), hotelList);
                    currentYoyMomData.setMonth(entry.getKey());
                    return currentYoyMomData;
                })
                .collect(Collectors.toMap(CurrentYoyMomData::getMonth, (p) -> p));

        OrpDateTimeUtils.MonthSplitPair totalPair = OrpDateTimeUtils.MonthSplitPair.create(request.getStartTime(), request.getEndTime());
        CurrentYoyMomData<OnlineReportHotelDto> totalCurrentYoyMomData = getCurrentYoyMomHotelData(totalPair, hotelList);

        DetailData hotelAmountResult = mapHotelInfoResultData(request, unionData, totalCurrentYoyMomData,
                amountHeader.get(ReportDetailHeaderEnum.HOTEL_INFO_AMOUNT_HEADER), DetailReportTypeEnum.DETAIL_AMOUNT_REPORT);
        detailAmountData.setInfoDetail(hotelAmountResult);
        // 间夜量明细
        DetailData hotelQuantityResult = mapHotelInfoResultData(request, unionData, totalCurrentYoyMomData,
                ticketHeader.get(ReportDetailHeaderEnum.HOTEL_INFO_TICKET_HEADER), DetailReportTypeEnum.DETAIL_TICKET_REPORT);
        detailTicketData.setInfoDetail(hotelQuantityResult);
        // 全量返回数据
        return new OnlineReportData(detailAmountData, detailTicketData);
    }

    /**
     * @param request
     * @param hotelList
     * @param hotelHeader
     * @return
     */
    private DetailData mapHotelAllResultData(OnlineDetailRequestDto request, List<OnlineReportHotelDto> hotelList,
                                             List<HeaderKeyValMap> hotelHeader, String type) {
        // POS_JP 保留0位小数
        boolean amountDecimalPlacesZero = OrpReportUtils.amountDecimalPlacesZero(request.getBaseCondition().getCurrency(), request.getBaseCondition().getPos());

        // 金额报表
        Map<String, OrpDateTimeUtils.MonthSplitPair> dateYoyMomRange = OrpDateTimeUtils.momSplit(request.getStartTime(), request.getEndTime());
        Map<String, ReprotBodyData> amountReportMap = dateYoyMomRange.entrySet().stream()
                .map(entry -> {
                    CurrentYoyMomData<OnlineReportHotelDto> currentYoyMomData = getCurrentYoyMomHotelData(entry.getValue(), hotelList);
                    currentYoyMomData.setMonth(entry.getKey());
                    return currentYoyMomData;
                })
                .map(data -> {
                    return mapHotelReportBodyData(StringUtils.EMPTY, data, amountDecimalPlacesZero);
                })
                .collect(Collectors.toMap(ReprotBodyData::getDimension, (p) -> p));

        OrpDateTimeUtils.MonthSplitPair totalPair = OrpDateTimeUtils.MonthSplitPair.create(request.getStartTime(), request.getEndTime());
        CurrentYoyMomData<OnlineReportHotelDto> totalCurrentYoyMomData = getCurrentYoyMomHotelData(totalPair, hotelList);

        // hotel整体-金额明细
        List<ReprotBodyData> amountList = Lists.newArrayList();

        // dimension
        OrpDateTimeUtils.getMonthBetween(request.getStartTime(), request.getEndTime()).forEach(month -> {
            // 空时补数据
            if (Objects.isNull(amountReportMap.get(month))) {
                amountList.add(new ReprotBodyData(month, amountDecimalPlacesZero ? mapJPHotelEmptyData() : mapHotelEmptyData()));
            } else {
                amountList.add(amountReportMap.get(month));
            }
        });
        OrpCollectionUtils.sortAsc(amountList, ReprotBodyData::getDimension);
        String hotelTitle = StringUtils.EMPTY;
        if (StringUtils.isBlank(type)) {
            // 小计
            hotelTitle = SharkUtils.getHeaderVal(
                    OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.subtotal"),
                    request.getLang());
        } else {
            // 总计
            hotelTitle = SharkUtils.getHeaderVal(
                    OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.totalV"),
                    request.getLang());
        }
        amountList.add(mapHotelReportBodyData(hotelTitle, totalCurrentYoyMomData, amountDecimalPlacesZero));

        return new DetailData(hotelHeader, amountList);
    }

    private DetailData mapHotelInfoResultData(OnlineDetailRequestDto request,
                                              Map<String, CurrentYoyMomData<OnlineReportHotelDto>> unionData,
                                              CurrentYoyMomData<OnlineReportHotelDto> totalCurrentYoyMomData,
                                              List<HeaderKeyValMap> ticketHeader, DetailReportTypeEnum reportType) {
        // POS_JP 保留0位小数
        boolean amountDecimalPlacesZero = OrpReportUtils.amountDecimalPlacesZero(request.getBaseCondition().getCurrency(), request.getBaseCondition().getPos());

        List<ReprotBodyData> hotelBodyDataList = mapHotelAmountTicketData(reportType, unionData, amountDecimalPlacesZero);

        OrpCollectionUtils.sortAsc(hotelBodyDataList, ReprotBodyData::getDimension);
        // 总计
        String totalTitle = SharkUtils.getHeaderVal(
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.totalV"),
                request.getLang());
        hotelBodyDataList.add(mapHotelTotalAmountTicketData(totalTitle, reportType, totalCurrentYoyMomData, amountDecimalPlacesZero));
        return new DetailData(ticketHeader, hotelBodyDataList);
    }

    private List<ReprotBodyData> mapHotelAmountTicketData(DetailReportTypeEnum typeEnum,
                                                          Map<String, CurrentYoyMomData<OnlineReportHotelDto>> data,
                                                          boolean isJP) {
        List<ReprotBodyData> reportBodyDataList = Lists.newArrayList();

        data.forEach((k, v) -> {
            AtomicDouble agreementV = new AtomicDouble();
            AtomicDouble agreementYoy = new AtomicDouble();
            AtomicDouble agreementMom = new AtomicDouble();

            AtomicDouble unAgreementV = new AtomicDouble();
            AtomicDouble unAgreementYoy = new AtomicDouble();
            AtomicDouble unAgreementMom = new AtomicDouble();

            AtomicDouble domesticV = new AtomicDouble();
            AtomicDouble domesticYoy = new AtomicDouble();
            AtomicDouble domesticMom = new AtomicDouble();

            AtomicDouble internationalV = new AtomicDouble();
            AtomicDouble internationalYoy = new AtomicDouble();
            AtomicDouble internationalMom = new AtomicDouble();

            AtomicDouble totalV = new AtomicDouble();
            AtomicDouble totalYoy = new AtomicDouble();
            AtomicDouble totalMom = new AtomicDouble();

            List<OnlineReportHotelDto> currentHotelList = v.getCurrent();
            Optional.ofNullable(currentHotelList).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        // 协议
                        agreementV
                                .set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()), t.getAgreementAmount()));
                        // 非协议
                        unAgreementV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()), t.getNotAgreementAmount()));
                        // 国内
                        domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()), t.getDomesticAmount()));
                        // 国际
                        internationalV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()), t.getInternationalAmount()));
                        // 总计
                        totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), t.getRealPay()));

                        break;
                    case DETAIL_TICKET_REPORT:
                        // 协议
                        agreementV.set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()),
                                new BigDecimal(t.getAgreementQuantity())));
                        // 非协议
                        unAgreementV.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()),
                                new BigDecimal(t.getMemQuantity())));
                        // 国内
                        domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()),
                                new BigDecimal(t.getDomesticQuantity())));
                        // 国际
                        internationalV.set(OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()),
                                new BigDecimal(t.getInternationalQuantity())));
                        // 总计
                        totalV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), new BigDecimal(t.getQuantityV())));
                        break;
                    default:
                }

            });

            List<OnlineReportHotelDto> yoyHotelList = v.getYoy();
            Optional.ofNullable(yoyHotelList).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        agreementYoy.set(
                                OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()), t.getAgreementAmount()));
                        unAgreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()),
                                t.getNotAgreementAmount()));
                        domesticYoy
                                .set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()), t.getDomesticAmount()));
                        internationalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()),
                                t.getInternationalAmount()));
                        totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), t.getRealPay()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        agreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()),
                                new BigDecimal(t.getAgreementQuantity())));
                        unAgreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()),
                                new BigDecimal(t.getMemQuantity())));
                        domesticYoy.set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()),
                                new BigDecimal(t.getDomesticQuantity())));
                        internationalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()),
                                new BigDecimal(t.getInternationalQuantity())));
                        totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()),
                                new BigDecimal(t.getQuantityV())));
                        break;
                    default:
                }
            });

            List<OnlineReportHotelDto> momHotelList = v.getMom();
            Optional.ofNullable(momHotelList).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        agreementMom.set(
                                OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()), t.getAgreementAmount()));
                        unAgreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()),
                                t.getNotAgreementAmount()));
                        domesticMom.set(
                                OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()), t.getDomesticAmount()));
                        internationalMom.set(OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()),
                                t.getInternationalAmount()));
                        totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getRealPay()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        agreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()),
                                new BigDecimal(t.getAgreementQuantity())));
                        unAgreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()),
                                new BigDecimal(t.getMemQuantity())));
                        domesticMom.set(OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()),
                                new BigDecimal(t.getDomesticQuantity())));
                        internationalMom.set(OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()),
                                new BigDecimal(t.getInternationalQuantity())));
                        totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()),
                                new BigDecimal(t.getQuantityV())));
                        break;
                    default:
                }
            });

            // 精度,日本站金额取整
            int precision = typeEnum == DetailReportTypeEnum.DETAIL_AMOUNT_REPORT ? (isJP ? OrpConstants.ZERO : OrpConstants.TWO) : OrpConstants.ZERO;
            ReportDataDetailData detailData = new ReportDataDetailData();
            // 协议
            detailData.setAgreementV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(agreementV.get()), precision).toString());
            detailData.setAgreementYoy(OrpCommonUtils.yoy(agreementV.get(), agreementYoy.get()));
            detailData.setAgreementMom(OrpCommonUtils.mom(agreementV.get(), agreementMom.get()));
            // 非协议
            detailData.setUnAgreementV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(unAgreementV.get()), precision).toString());
            detailData.setUnAgreementYoy(OrpCommonUtils.yoy(unAgreementV.get(), unAgreementYoy.get()));
            detailData.setUnAgreementMom(OrpCommonUtils.mom(unAgreementV.get(), unAgreementMom.get()));
            // 国内
            detailData.setDomesticV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(domesticV.get()), precision).toString());
            detailData.setDomesticYoy(OrpCommonUtils.yoy(domesticV.get(), domesticYoy.get()));
            detailData.setDomesticMom(OrpCommonUtils.mom(domesticV.get(), domesticMom.get()));
            // 国际
            detailData.setInternationalV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(internationalV.get()), precision).toString());
            detailData.setInternationalYoy(OrpCommonUtils.yoy(internationalV.get(), internationalYoy.get()));
            detailData.setInternationalMom(OrpCommonUtils.mom(internationalV.get(), internationalMom.get()));

            // 总计
            detailData.setTotalV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());
            detailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
            detailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));

            reportBodyDataList.add(new ReprotBodyData(k, detailData));
        });
        return reportBodyDataList;
    }

    /**
     * 酒店汇总-金额/间夜量-总计
     *
     * @param totalTitle
     * @param typeEnum
     * @param data
     * @return
     */
    private ReprotBodyData mapHotelTotalAmountTicketData(String totalTitle, DetailReportTypeEnum typeEnum,
                                                         CurrentYoyMomData<OnlineReportHotelDto> data, boolean isJP) {
        AtomicDouble agreementV = new AtomicDouble();
        AtomicDouble agreementYoy = new AtomicDouble();
        AtomicDouble agreementMom = new AtomicDouble();

        AtomicDouble unAgreementV = new AtomicDouble();
        AtomicDouble unAgreementYoy = new AtomicDouble();
        AtomicDouble unAgreementMom = new AtomicDouble();

        AtomicDouble domesticV = new AtomicDouble();
        AtomicDouble domesticYoy = new AtomicDouble();
        AtomicDouble domesticMom = new AtomicDouble();

        AtomicDouble internationalV = new AtomicDouble();
        AtomicDouble internationalYoy = new AtomicDouble();
        AtomicDouble internationalMom = new AtomicDouble();

        AtomicDouble totalV = new AtomicDouble();
        AtomicDouble totalYoy = new AtomicDouble();
        AtomicDouble totalMom = new AtomicDouble();

        List<OnlineReportHotelDto> currentHotelList = data.getCurrent();
        Optional.ofNullable(currentHotelList).orElse(Lists.newArrayList()).forEach(t -> {
            switch (typeEnum) {
                case DETAIL_AMOUNT_REPORT:
                    // 协议
                    agreementV
                            .set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()), t.getAgreementAmount()));
                    // 非协议
                    unAgreementV.set(
                            OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()), t.getNotAgreementAmount()));
                    // 国内
                    domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()), t.getDomesticAmount()));
                    // 国际
                    internationalV.set(
                            OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()), t.getInternationalAmount()));
                    // 总计
                    totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), t.getRealPay()));

                    break;
                case DETAIL_TICKET_REPORT:
                    // 协议
                    agreementV.set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()),
                            new BigDecimal(t.getAgreementQuantity())));
                    // 非协议
                    unAgreementV.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()),
                            new BigDecimal(t.getMemQuantity())));
                    // 国内
                    domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()),
                            new BigDecimal(t.getDomesticQuantity())));
                    // 国际
                    internationalV.set(OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()),
                            new BigDecimal(t.getInternationalQuantity())));
                    // 总计
                    totalV.set(
                            OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), new BigDecimal(t.getQuantityV())));
                    break;
                default:
            }
        });

        List<OnlineReportHotelDto> yoyHotelList = data.getYoy();
        Optional.ofNullable(yoyHotelList).orElse(Lists.newArrayList()).forEach(t -> {
            switch (typeEnum) {
                case DETAIL_AMOUNT_REPORT:
                    agreementYoy.set(
                            OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()), t.getAgreementAmount()));
                    unAgreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()),
                            t.getNotAgreementAmount()));
                    domesticYoy
                            .set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()), t.getDomesticAmount()));
                    internationalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()),
                            t.getInternationalAmount()));
                    totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), t.getRealPay()));
                    break;
                case DETAIL_TICKET_REPORT:
                    agreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()),
                            new BigDecimal(t.getAgreementQuantity())));
                    unAgreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()),
                            new BigDecimal(t.getMemQuantity())));
                    domesticYoy.set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()),
                            new BigDecimal(t.getDomesticQuantity())));
                    internationalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()),
                            new BigDecimal(t.getInternationalQuantity())));
                    totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()),
                            new BigDecimal(t.getQuantityV())));
                    break;
                default:
            }
        });

        List<OnlineReportHotelDto> momHotelList = data.getMom();
        Optional.ofNullable(momHotelList).orElse(Lists.newArrayList()).forEach(t -> {
            switch (typeEnum) {
                case DETAIL_AMOUNT_REPORT:
                    agreementMom.set(
                            OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()), t.getAgreementAmount()));
                    unAgreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()),
                            t.getNotAgreementAmount()));
                    domesticMom
                            .set(OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()), t.getDomesticAmount()));
                    internationalMom.set(OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()),
                            t.getInternationalAmount()));
                    totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getRealPay()));
                    break;
                case DETAIL_TICKET_REPORT:
                    agreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()),
                            new BigDecimal(t.getAgreementQuantity())));
                    unAgreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()),
                            new BigDecimal(t.getMemQuantity())));
                    domesticMom.set(OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()),
                            new BigDecimal(t.getDomesticQuantity())));
                    internationalMom.set(OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()),
                            new BigDecimal(t.getInternationalQuantity())));
                    totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()),
                            new BigDecimal(t.getQuantityV())));
                    break;
                default:
            }
        });

        // 精度,日本站金额取整
        int precision = typeEnum == DetailReportTypeEnum.DETAIL_AMOUNT_REPORT ? (isJP ? OrpConstants.ZERO : OrpConstants.TWO) : OrpConstants.ZERO;
        ReportDataDetailData detailData = new ReportDataDetailData();
        // 协议
        detailData.setAgreementV(
                OrpCommonUtils.precisionConversion(new BigDecimal(agreementV.get()), precision).toString());
        detailData.setAgreementYoy(OrpCommonUtils.yoy(agreementV.get(), agreementYoy.get()));
        detailData.setAgreementMom(OrpCommonUtils.mom(agreementV.get(), agreementMom.get()));
        // 非协议
        detailData.setUnAgreementV(
                OrpCommonUtils.precisionConversion(new BigDecimal(unAgreementV.get()), precision).toString());
        detailData.setUnAgreementYoy(OrpCommonUtils.yoy(unAgreementV.get(), unAgreementYoy.get()));
        detailData.setUnAgreementMom(OrpCommonUtils.mom(unAgreementV.get(), unAgreementMom.get()));
        // 国内
        detailData.setDomesticV(
                OrpCommonUtils.precisionConversion(new BigDecimal(domesticV.get()), precision).toString());
        detailData.setDomesticYoy(OrpCommonUtils.yoy(domesticV.get(), domesticYoy.get()));
        detailData.setDomesticMom(OrpCommonUtils.mom(domesticV.get(), domesticMom.get()));
        // 国际
        detailData.setInternationalV(
                OrpCommonUtils.precisionConversion(new BigDecimal(internationalV.get()), precision).toString());
        detailData.setInternationalYoy(OrpCommonUtils.yoy(internationalV.get(), internationalYoy.get()));
        detailData.setInternationalMom(OrpCommonUtils.mom(internationalV.get(), internationalMom.get()));
        // 总计
        detailData
                .setTotalV(OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());
        detailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
        detailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));
        return new ReprotBodyData(totalTitle, detailData);
    }

    protected ReprotBodyData mapHotelReportBodyData(String type, CurrentYoyMomData<OnlineReportHotelDto> hotelList, boolean isJP) {
        ReportDataDetailData detailData = new ReportDataDetailData();
        AtomicDouble roomPrice = new AtomicDouble();
        // AtomicDouble couponAmount = new AtomicDouble();
        AtomicDouble serviceFee = new AtomicDouble();
        AtomicDouble totalV = new AtomicDouble();
        AtomicDouble postServiceFee = new AtomicDouble();


        if (StringUtils.isBlank(type)) {
            type = hotelList.getMonth();
        }
        // 空集合补数,日本站数据取整
        if (isJP) {
            if (CollectionUtils.isEmpty(hotelList.getCurrent())) {
                detailData.setRoomPrice(OrpConstants.ZERO_CHAR);
                detailData.setCouponAmount(OrpConstants.ZERO_CHAR);
                detailData.setServiceFee(OrpConstants.ZERO_CHAR);
                detailData.setTotalV(OrpConstants.ZERO_CHAR_2);
                detailData.setHotelPostServiceFee(OrpConstants.ZERO_CHAR);
                return new ReprotBodyData(type, detailData);
            }
        } else {
            if (CollectionUtils.isEmpty(hotelList.getCurrent())) {
                detailData.setRoomPrice(OrpConstants.ZERO_CHAR_2);
                detailData.setCouponAmount(OrpConstants.ZERO_CHAR_2);
                detailData.setServiceFee(OrpConstants.ZERO_CHAR_2);
                detailData.setTotalV(OrpConstants.ZERO_CHAR_2);
                detailData.setHotelPostServiceFee(OrpConstants.ZERO_CHAR_2);
                return new ReprotBodyData(type, detailData);
            }
        }

        hotelList.getCurrent().forEach(data -> {
            roomPrice.set(OrpCommonUtils.addDouble(new BigDecimal(roomPrice.get()), data.getRoomPrice()));
            // couponAmount.set(OrpCommonUtils.addDouble(new BigDecimal(couponAmount.get()), data.getCouponAmount()));
            serviceFee.set(OrpCommonUtils.addDouble(new BigDecimal(serviceFee.get()), data.getServiceFee()));
            totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), data.getRealPay()));
            postServiceFee.set(OrpCommonUtils.addDouble(new BigDecimal(postServiceFee.get()), data.getPostServiceFee()));
        });
        // 精度,日本站金额取整
        int precision = isJP ? OrpConstants.ZERO : OrpConstants.TWO;
        detailData.setRoomPrice(
                OrpCommonUtils.precisionConversion(new BigDecimal(roomPrice.get()), precision).toString());
        // detailData.setCouponAmount(OrpCommonUtils.showMinusVal(OrpCommonUtils.precisionConversion(new
        // BigDecimal(couponAmount.get()), OrpConstants.ZERO)).toString());
        detailData.setServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(serviceFee.get()), precision).toString());
        detailData.setHotelPostServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(postServiceFee.get()), precision).toString());
        detailData
                .setTotalV(OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());

        return new ReprotBodyData(type, detailData);
    }

    /**
     * 酒店明细
     */
    private ReprotBodyData mapHotelInfoReportBodyData(String type, DetailReportTypeEnum reportType,
                                                      List<OnlineReportHotelDto> hotelList) {
        if (reportType.getName().equals(DetailReportTypeEnum.DETAIL_AMOUNT_REPORT.getName())) {
            return mapInfoAmountHotelReport(type, hotelList);
        } else {
            return mapInfoTicketHotelReport(type, hotelList);
        }
    }

    /**
     * 酒店补充-空数据
     *
     * @param amountHeader
     * @param ticketHeader
     * @return
     */
    private OnlineReportData mapHotelAmountTicketEmptyData(
            Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> amountHeader,
            Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> ticketHeader) {
        DetailAmountDto amountData = new DetailAmountDto();
        DetailTicketDto ticketData = new DetailTicketDto();

        // header
        amountData.setAllDetail(
                new DetailData(amountHeader.get(ReportDetailHeaderEnum.HOTEL_ALL_HEADER), Lists.newArrayList()));
        amountData.setAgreementtDetail(
                new DetailData(amountHeader.get(ReportDetailHeaderEnum.HOTEL_AGREEMENT_HEADER), Lists.newArrayList()));
        amountData.setNotAgreementDetail(
                new DetailData(amountHeader.get(ReportDetailHeaderEnum.HOTEL_NOT_AGREEMENT_HEADER), Lists.newArrayList()));
        amountData.setDomesticDetail(
                new DetailData(amountHeader.get(ReportDetailHeaderEnum.HOTEL_DOMESTIC_HEADER), Lists.newArrayList()));
        amountData.setInternationalDetail(
                new DetailData(amountHeader.get(ReportDetailHeaderEnum.HOTEL_INTERNATIONAL_HEADER), Lists.newArrayList()));
        amountData.setInfoDetail(
                new DetailData(amountHeader.get(ReportDetailHeaderEnum.HOTEL_INFO_AMOUNT_HEADER), Lists.newArrayList()));
        ticketData.setInfoDetail(
                new DetailData(ticketHeader.get(ReportDetailHeaderEnum.HOTEL_INFO_TICKET_HEADER), Lists.newArrayList()));

        return new OnlineReportData(amountData, ticketData);
    }

    public boolean checkHotelOversea(String oversea) {
        List<String> hotelOversea = Stream.of(StringUtils.split(HotelReportEnum.DOMESTIC.getName(), OrpConstants.COMMA))
                .collect(Collectors.toList());
        return hotelOversea.contains(oversea);
    }

    private Map<String, List<OnlineReportHotelDto>> getCurrentYoyMomHotelData(List<String> trainKeyList,
                                                                              Map<String, List<OnlineReportHotelDto>> hotelListMap) {
        Map<String, List<OnlineReportHotelDto>> currentHotelListMap = Maps.newHashMap();
        trainKeyList.forEach(t -> {
            List<OnlineReportHotelDto> currentOverviewList = hotelListMap.get(t);
            currentHotelListMap.put(t, currentOverviewList);
        });
        return currentHotelListMap;
    }

    /**
     * map 当期/同比/环比-数据
     *
     * @param splitPair
     * @param overViewList
     * @return
     */
    private CurrentYoyMomData<OnlineReportHotelDto> getCurrentYoyMomHotelData(OrpDateTimeUtils.MonthSplitPair splitPair,
                                                                              List<OnlineReportHotelDto> overViewList) {
        CurrentYoyMomData<OnlineReportHotelDto> currentYoyMomData = new CurrentYoyMomData<>();
        List<OnlineReportHotelDto> current = new ArrayList<>();
        List<OnlineReportHotelDto> yoy = new ArrayList<>();
        List<OnlineReportHotelDto> mom = new ArrayList<>();

        overViewList.forEach(t -> {
            if (OrpDateTimeUtils.dateInRange(splitPair.getStartDate(), splitPair.getEndDate(), t.getReportDate())) {
                current.add(t);
            }

            if (OrpDateTimeUtils.dateInRange(splitPair.getYoyStartDate(), splitPair.getYoyEndDate(), t.getReportDate())) {
                yoy.add(t);
            }

            if (OrpDateTimeUtils.dateInRange(splitPair.getMomStartDate(), splitPair.getMomEndDate(), t.getReportDate())) {
                mom.add(t);
            }
        });
        currentYoyMomData.setCurrent(current);
        currentYoyMomData.setYoy(yoy);
        currentYoyMomData.setMom(mom);
        return currentYoyMomData;
    }
}
