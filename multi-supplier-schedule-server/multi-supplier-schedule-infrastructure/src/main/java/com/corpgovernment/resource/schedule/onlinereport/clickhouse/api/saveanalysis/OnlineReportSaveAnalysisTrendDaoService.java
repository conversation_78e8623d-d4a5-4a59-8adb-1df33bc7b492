package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.saveanalysis;


import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveTrendV2DTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29
 */
public interface OnlineReportSaveAnalysisTrendDaoService {
    /**
     * 查询 在线报告概况-节省趋势机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveTrendDTO> queryOnlineReportFlightTrend(OnlineTrendRequestDto request) throws Exception;

    /**
     * 查询 在线报告概况-节省趋势机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveTrendV2DTO> queryOnlineReportFlightTrendV2(OnlineTrendRequestDto request) throws Exception;

    /**
     * 查询 在线报告概况-节省趋势酒店
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveTrendDTO> queryOnlineReportHotelTrend(OnlineTrendRequestDto request) throws Exception;
}
