package com.corpgovernment.resource.schedule.onlinereport.enums.save;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.save
 * @description:机酒节省分析-机票折扣分布
 * @author: md_wang
 * @create: 2022-08-02 20:00
 **/
public enum FlightDiscountDistEnum {
    /**
     * 无折扣
     */
    NONE("Save.Choice3"),

    /**
     * 7折及以上
     */
    DISCOUNT_7("Save.Choice4"),
    /**
     * 8折及以上
     */
    DISCOUNT_8("Save.Choice5"),
    /**
     * 9折及以上
     */
    DISCOUNT_9("Save.Choice6"),
    ;

    private String sharkKey;

    FlightDiscountDistEnum(String sharkKey) {
        this.sharkKey = sharkKey;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public void setSharkKey(String sharkKey) {
        this.sharkKey = sharkKey;
    }
}
