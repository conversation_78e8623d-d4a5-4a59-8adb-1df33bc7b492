package com.corpgovernment.resource.schedule.onlinereport.bu;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailAmountDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailTicketDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailTrainAmountData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailTrainData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportDataDetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReprotBodyData;
import com.corpgovernment.resource.schedule.onlinereport.abs.AbstractOnlineDetailService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportFlightDao;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportFlightDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.DetailReportTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.FlightReportEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.ReportDetailHeaderEnum;
import com.corpgovernment.resource.schedule.onlinereport.factory.OnlineDetailExecute;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCollectionUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.AtomicDouble;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.service.factory.test
 * @description: 机票产线
 * @author: md_wang
 * @create: 2021-11-08 10:33
 **/
@Service
@Slf4j
public class FlightDetail extends AbstractOnlineDetailService implements OnlineDetailExecute {

    @Autowired
    private OnlineReportFlightDao reportFlightDao;

    @Override
    
    public OnlineReportData queryDetailRecord(OnlineReportDetailRequest request) {
        Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerAmount =
                mapperHeader(DetailReportTypeEnum.DETAIL_AMOUNT_REPORT, request.queryBu, request.lang);
        Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerTicket =
                mapperHeader(DetailReportTypeEnum.DETAIL_TICKET_REPORT, request.queryBu, request.lang);
        if (BlueSpaceUtils.isForeign(request.getBasecondition().getPos(), request.getBasecondition().getBlueSpace())) {
            boolean isBlueSpace = BlueSpaceUtils.isBlueSpace(request.getBasecondition().getBlueSpace());
            headerAmount = mapperJPHeader(DetailReportTypeEnum.DETAIL_AMOUNT_REPORT, request.queryBu, request.lang, isBlueSpace);
            headerTicket = mapperJPHeader(DetailReportTypeEnum.DETAIL_TICKET_REPORT, request.queryBu, request.lang, isBlueSpace);
        }
        try {
            OnlineDetailRequestDto requestDto = mapOnlineDetailRequestDto(request);
            List<OnlineReportFlightDto> flightList = reportFlightDao.queryOnlineReportDetailFlight(requestDto, OnlineReportFlightDto.class);
            if (CollectionUtils.isEmpty(flightList)) {
                return mapFlightEmptyData(headerAmount, headerTicket);
            }
            return mapFlightResultData(requestDto, flightList, headerAmount, headerTicket);
        } catch (Exception e) {
            log.error("queryFlightDetailRecord", ExceptionUtils.getFullStackTrace(e));
            return mapFlightEmptyData(headerAmount, headerTicket);
        }
    }

    /**
     * 返回结果映射
     */
    private OnlineReportData mapFlightResultData(OnlineDetailRequestDto request, List<OnlineReportFlightDto> flightList,
                                                 Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerAmount,
                                                 Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerTicket) {
        // 整体
        DetailData flightAllResult = mapFlightAllData(request, flightList,
                headerAmount.get(ReportDetailHeaderEnum.FLIGHT_ALL_HEADER), OrpConstants.ANY);
        // 协议
        DetailData agreementResult = mapFlightAllData(request,
                flightList.stream().filter(Objects::nonNull).filter(t -> Objects.nonNull(t.getContractType()))
                        .filter(t -> StringUtils.equalsIgnoreCase(t.getContractType(), FlightReportEnum.AGREEMENT.getName()))
                        .collect(Collectors.toList()),
                headerAmount.get(ReportDetailHeaderEnum.FLIGHT_AGREEMENT_HEADER), OrpConstants.EMPTY);
        // 非协议
        DetailData unAgreementResult = mapFlightAllData(request,
                flightList.stream().filter(Objects::nonNull).filter(t -> Objects.nonNull(t.getContractType()))
                        .filter(
                                t -> StringUtils.equalsIgnoreCase(t.getContractType(), FlightReportEnum.NOT_AGREEMENT.getName()))
                        .collect(Collectors.toList()),
                headerAmount.get(ReportDetailHeaderEnum.FLIGHT_NOT_AGREEMENT_HEADER), OrpConstants.EMPTY);
        // 国内
        DetailData domesticResult = mapFlightAllData(request,
                flightList.stream().filter(Objects::nonNull).filter(t -> Objects.nonNull(t.getFlightClass()))
                        .filter(t -> StringUtils.equalsIgnoreCase(t.getFlightClass(), FlightReportEnum.DOMESTIC.getName()))
                        .collect(Collectors.toList()),
                headerAmount.get(ReportDetailHeaderEnum.FLIGHT_DOMESTIC_HEADER), OrpConstants.EMPTY);
        // 国际
        DetailData internationalResult = mapFlightAllData(request,
                flightList.stream().filter(Objects::nonNull).filter(t -> Objects.nonNull(t.getFlightClass()))
                        .filter(t -> StringUtils.equalsIgnoreCase(t.getFlightClass(), FlightReportEnum.INTERNATIONAL.getName()))
                        .collect(Collectors.toList()),
                headerAmount.get(ReportDetailHeaderEnum.FLIGHT_INTERNATIONAL_HEADER), OrpConstants.EMPTY);

        // amountInfoResult 及 quantityInfoResult
        Map<String, OrpDateTimeUtils.MonthSplitPair> dateYoyMomRange = OrpDateTimeUtils.momSplit(request.getStartTime(), request.getEndTime());
        Map<String, CurrentYoyMomData<OnlineReportFlightDto>> unionData = dateYoyMomRange.entrySet().stream()
                .map(entry -> {
                    CurrentYoyMomData<OnlineReportFlightDto> currentYoyMomData = getCurrentYoyMomFlightData(entry.getValue(), flightList);
                    currentYoyMomData.setMonth(entry.getKey());
                    return currentYoyMomData;
                })
                .collect(Collectors.toMap(CurrentYoyMomData::getMonth, (p) -> p));

        OrpDateTimeUtils.MonthSplitPair totalPair = OrpDateTimeUtils.MonthSplitPair.create(request.getStartTime(), request.getEndTime());
        CurrentYoyMomData<OnlineReportFlightDto> totalCurrentYoyMomData = getCurrentYoyMomFlightData(totalPair, flightList);

        // 金额明细
        DetailData amountInfoResult = mapAmountTicketInfoData(request, unionData, totalCurrentYoyMomData,
                headerAmount.get(ReportDetailHeaderEnum.FLIGHT_INFO_AMOUNT_HEADER),
                DetailReportTypeEnum.DETAIL_AMOUNT_REPORT);

        // 票张明细
        DetailData quantityInfoResult = mapAmountTicketInfoData(request, unionData, totalCurrentYoyMomData,
                headerTicket.get(ReportDetailHeaderEnum.FLIGHT_INFO_TICKET_HEADER),
                DetailReportTypeEnum.DETAIL_TICKET_REPORT);

        DetailData empty = (DetailData) OrpConstants.NULL;
        DetailTrainData train_empty = (DetailTrainData) OrpConstants.NULL;
        DetailTrainAmountData trainAmount_empty = (DetailTrainAmountData) OrpConstants.NULL;
        return new OnlineReportData(
                new DetailAmountDto(flightAllResult, domesticResult, internationalResult, agreementResult,
                        unAgreementResult, amountInfoResult, empty, empty, empty, empty, empty, empty, empty, train_empty, trainAmount_empty),
                new DetailTicketDto(quantityInfoResult));
    }

    /**
     * 机票整体报表
     */
    private DetailData mapFlightAllData(OnlineDetailRequestDto request, List<OnlineReportFlightDto> flightList,
                                        List<HeaderKeyValMap> headerKeyValMaps, String type) {
        // POS_JP 保留0位小数
        boolean amountDecimalPlacesZero = OrpReportUtils.amountDecimalPlacesZero(request.getBaseCondition().getCurrency(), request.getBaseCondition().getPos());
        // 金额报表
        Map<String, OrpDateTimeUtils.MonthSplitPair> dateYoyMomRange = OrpDateTimeUtils.momSplit(request.getStartTime(), request.getEndTime());
        Map<String, ReprotBodyData> amountReportMap = dateYoyMomRange.entrySet().stream()
                .map(entry -> {
                    CurrentYoyMomData<OnlineReportFlightDto> currentYoyMomData = getCurrentYoyMomFlightData(entry.getValue(), flightList);
                    currentYoyMomData.setMonth(entry.getKey());
                    return currentYoyMomData;
                })
                .map(data -> {
                    return mapFlightAllBodyData(StringUtils.EMPTY, data, amountDecimalPlacesZero);
                })
                .collect(Collectors.toMap(ReprotBodyData::getDimension, (p) -> p));

        OrpDateTimeUtils.MonthSplitPair totalPair = OrpDateTimeUtils.MonthSplitPair.create(request.getStartTime(), request.getEndTime());
        CurrentYoyMomData<OnlineReportFlightDto> totalCurrentYoyMomData = getCurrentYoyMomFlightData(totalPair, flightList);

        // hotel整体-金额明细
        List<ReprotBodyData> amountList = Lists.newArrayList();
        // dimension
        OrpDateTimeUtils.getMonthBetween(request.getStartTime(), request.getEndTime()).forEach(month -> {
            if (Objects.isNull(amountReportMap.get(month))) {
                amountList.add(new ReprotBodyData(month, amountDecimalPlacesZero ? mapJPFlightEmptyData() : mapFlightEmptyData()));
            } else {
                amountList.add(amountReportMap.get(month));
            }
        });
        OrpCollectionUtils.sortAsc(amountList, ReprotBodyData::getDimension);
        String flightTitle = StringUtils.EMPTY;
        if (StringUtils.isBlank(type)) {
            flightTitle = SharkUtils.getHeaderVal(
                    OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.subtotal"),
                    request.getLang());
        } else {
            flightTitle = SharkUtils.getHeaderVal(
                    OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.totalV"),
                    request.getLang());
        }
        // 总计
        amountList.add(mapFlightAllBodyData(flightTitle, totalCurrentYoyMomData, amountDecimalPlacesZero));
        return new DetailData(headerKeyValMaps, amountList);
    }

    /**
     * 金额明细报表
     */
    private DetailData mapAmountTicketInfoData(OnlineDetailRequestDto request,
                                               Map<String, CurrentYoyMomData<OnlineReportFlightDto>> unionData,
                                               CurrentYoyMomData<OnlineReportFlightDto> totalCurrentYoyMomData,
                                               List<HeaderKeyValMap> headerKeyValMaps,
                                               DetailReportTypeEnum reportType) {
        // POS_JP 保留0位小数
        boolean amountDecimalPlacesZero = OrpReportUtils.amountDecimalPlacesZero(request.getBaseCondition().getCurrency(), request.getBaseCondition().getPos());
        // 金额报表

        // 计算-机票-金额/订单量-当期/同比/环比-数据
        List<ReprotBodyData> flightBodyDataList = mapFlightAmountTicketData(reportType, unionData, amountDecimalPlacesZero);
        // 排序
        OrpCollectionUtils.sortAsc(flightBodyDataList, ReprotBodyData::getDimension);
        // 总计
        String totalTitle = SharkUtils.getHeaderVal(
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.totalV"),
                request.getLang());
        flightBodyDataList.add(mapFlightTotalAmountTicketData(totalTitle, reportType, totalCurrentYoyMomData, amountDecimalPlacesZero));
        return new DetailData(headerKeyValMaps, flightBodyDataList);
    }

    private List<ReprotBodyData> mapFlightAmountTicketData(DetailReportTypeEnum typeEnum,
                                                           Map<String, CurrentYoyMomData<OnlineReportFlightDto>> data,
                                                           boolean isJP) {
        List<ReprotBodyData> reportBodyDataList = Lists.newArrayList();

        data.forEach((k, v) -> {
            AtomicDouble agreementV = new AtomicDouble();
            AtomicDouble agreementYoy = new AtomicDouble();
            AtomicDouble agreementMom = new AtomicDouble();

            AtomicDouble unAgreementV = new AtomicDouble();
            AtomicDouble unAgreementYoy = new AtomicDouble();
            AtomicDouble unAgreementMom = new AtomicDouble();

            AtomicDouble domesticV = new AtomicDouble();
            AtomicDouble domesticYoy = new AtomicDouble();
            AtomicDouble domesticMom = new AtomicDouble();

            AtomicDouble internationalV = new AtomicDouble();
            AtomicDouble internationalYoy = new AtomicDouble();
            AtomicDouble internationalMom = new AtomicDouble();

            AtomicDouble totalV = new AtomicDouble();
            AtomicDouble totalYoy = new AtomicDouble();
            AtomicDouble totalMom = new AtomicDouble();

            List<OnlineReportFlightDto> currentFlightList = v.getCurrent();
            Optional.ofNullable(currentFlightList).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        // 协议
                        agreementV
                                .set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()), t.getAgreementAmount()));
                        // 非协议
                        unAgreementV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()), t.getNotAgreementAmount()));
                        // 国内
                        domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()), t.getDomesticAmount()));
                        // 国际
                        internationalV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()), t.getInternationalAmount()));
                        // 总计
                        totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), t.getRealPay()));

                        break;
                    case DETAIL_TICKET_REPORT:
                        // 协议
                        agreementV.set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()),
                                new BigDecimal(t.getAgreementQuantity())));
                        // 非协议
                        unAgreementV.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()),
                                new BigDecimal(t.getNotAgreementQuantity())));
                        // 国内
                        domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()),
                                new BigDecimal(t.getDomesticQuantity())));
                        // 国际
                        internationalV.set(OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()),
                                new BigDecimal(t.getInternationalQuantity())));
                        // 总计
                        totalV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), new BigDecimal(t.getQuantityV())));
                        break;
                    default:
                }

            });

            List<OnlineReportFlightDto> yoyFlightlList = v.getYoy();
            Optional.ofNullable(yoyFlightlList).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        agreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()), t.getAgreementAmount()));
                        unAgreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()), t.getNotAgreementAmount()));
                        domesticYoy.set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()), t.getDomesticAmount()));
                        internationalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()), t.getInternationalAmount()));
                        totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), t.getRealPay()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        agreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()), new BigDecimal(t.getAgreementQuantity())));
                        unAgreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()), new BigDecimal(t.getNotAgreementQuantity())));
                        domesticYoy.set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()), new BigDecimal(t.getDomesticQuantity())));
                        internationalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()), new BigDecimal(t.getInternationalQuantity())));
                        totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()),
                                new BigDecimal(t.getQuantityV())));
                        break;
                    default:
                }
            });

            List<OnlineReportFlightDto> momFlightList = v.getMom();
            Optional.ofNullable(momFlightList).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        agreementMom.set(
                                OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()), t.getAgreementAmount()));
                        unAgreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()),
                                t.getNotAgreementAmount()));
                        domesticMom.set(
                                OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()), t.getDomesticAmount()));
                        internationalMom.set(OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()),
                                t.getInternationalAmount()));
                        totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getRealPay()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        agreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()),
                                new BigDecimal(t.getAgreementQuantity())));
                        unAgreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()),
                                new BigDecimal(t.getNotAgreementQuantity())));
                        domesticMom.set(OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()),
                                new BigDecimal(t.getDomesticQuantity())));
                        internationalMom.set(OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()),
                                new BigDecimal(t.getInternationalQuantity())));
                        totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()),
                                new BigDecimal(t.getQuantityV())));
                        break;
                    default:
                }
            });

            // 精度,日本站金额取整
            int precision = typeEnum == DetailReportTypeEnum.DETAIL_AMOUNT_REPORT ? (isJP ? OrpConstants.ZERO : OrpConstants.TWO) : OrpConstants.ZERO;
            ReportDataDetailData detailData = new ReportDataDetailData();
            // 协议
            detailData.setAgreementV(OrpCommonUtils.precisionConversion(new BigDecimal(agreementV.get()), precision).toString());
            detailData.setAgreementYoy(OrpCommonUtils.yoy(agreementV.get(), agreementYoy.get()));
            detailData.setAgreementMom(OrpCommonUtils.mom(agreementV.get(), agreementMom.get()));
            // 非协议
            detailData.setUnAgreementV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(unAgreementV.get()), precision).toString());
            detailData.setUnAgreementYoy(OrpCommonUtils.yoy(unAgreementV.get(), unAgreementYoy.get()));
            detailData.setUnAgreementMom(OrpCommonUtils.mom(unAgreementV.get(), unAgreementMom.get()));
            // 国内
            detailData.setDomesticV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(domesticV.get()), precision).toString());
            detailData.setDomesticYoy(OrpCommonUtils.yoy(domesticV.get(), domesticYoy.get()));
            detailData.setDomesticMom(OrpCommonUtils.mom(domesticV.get(), domesticMom.get()));
            // 国际
            detailData.setInternationalV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(internationalV.get()), precision).toString());
            detailData.setInternationalYoy(OrpCommonUtils.yoy(internationalV.get(), internationalYoy.get()));
            detailData.setInternationalMom(OrpCommonUtils.mom(internationalV.get(), internationalMom.get()));

            // 总计
            detailData.setTotalV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());
            detailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
            detailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));

            reportBodyDataList.add(new ReprotBodyData(k, detailData));
        });
        return reportBodyDataList;
    }

    private ReprotBodyData mapFlightTotalAmountTicketData(String totalTitle, DetailReportTypeEnum typeEnum,
                                                          CurrentYoyMomData<OnlineReportFlightDto> data,
                                                          boolean isJP) {
        AtomicDouble agreementV = new AtomicDouble();
        AtomicDouble agreementYoy = new AtomicDouble();
        AtomicDouble agreementMom = new AtomicDouble();

        AtomicDouble unAgreementV = new AtomicDouble();
        AtomicDouble unAgreementYoy = new AtomicDouble();
        AtomicDouble unAgreementMom = new AtomicDouble();

        AtomicDouble domesticV = new AtomicDouble();
        AtomicDouble domesticYoy = new AtomicDouble();
        AtomicDouble domesticMom = new AtomicDouble();

        AtomicDouble internationalV = new AtomicDouble();
        AtomicDouble internationalYoy = new AtomicDouble();
        AtomicDouble internationalMom = new AtomicDouble();

        AtomicDouble totalV = new AtomicDouble();
        AtomicDouble totalYoy = new AtomicDouble();
        AtomicDouble totalMom = new AtomicDouble();

        List<OnlineReportFlightDto> currentFlightList = data.getCurrent();
        Optional.ofNullable(currentFlightList).orElse(Lists.newArrayList()).forEach(t -> {
            switch (typeEnum) {
                case DETAIL_AMOUNT_REPORT:
                    // 协议
                    agreementV
                            .set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()), t.getAgreementAmount()));
                    // 非协议
                    unAgreementV.set(
                            OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()), t.getNotAgreementAmount()));
                    // 国内
                    domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()), t.getDomesticAmount()));
                    // 国际
                    internationalV.set(
                            OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()), t.getInternationalAmount()));
                    // 总计
                    totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), t.getRealPay()));

                    break;
                case DETAIL_TICKET_REPORT:
                    // 协议
                    agreementV.set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()),
                            new BigDecimal(t.getAgreementQuantity())));
                    // 非协议
                    unAgreementV.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()),
                            new BigDecimal(t.getNotAgreementQuantity())));
                    // 国内
                    domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()),
                            new BigDecimal(t.getDomesticQuantity())));
                    // 国际
                    internationalV.set(OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()),
                            new BigDecimal(t.getInternationalQuantity())));
                    // 总计
                    totalV.set(
                            OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), new BigDecimal(t.getQuantityV())));
                    break;
                default:
            }
        });

        List<OnlineReportFlightDto> yoyFlightList = data.getYoy();
        Optional.ofNullable(yoyFlightList).orElse(Lists.newArrayList()).forEach(t -> {
            switch (typeEnum) {
                case DETAIL_AMOUNT_REPORT:
                    agreementYoy.set(
                            OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()), t.getAgreementAmount()));
                    unAgreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()),
                            t.getNotAgreementAmount()));
                    domesticYoy
                            .set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()), t.getDomesticAmount()));
                    internationalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()),
                            t.getInternationalAmount()));
                    totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), t.getRealPay()));
                    break;
                case DETAIL_TICKET_REPORT:
                    agreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()),
                            new BigDecimal(t.getAgreementQuantity())));
                    unAgreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()),
                            new BigDecimal(t.getNotAgreementQuantity())));
                    domesticYoy.set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()),
                            new BigDecimal(t.getDomesticQuantity())));
                    internationalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()),
                            new BigDecimal(t.getInternationalQuantity())));
                    totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()),
                            new BigDecimal(t.getQuantityV())));
                    break;
                default:
            }
        });

        List<OnlineReportFlightDto> momFlightList = data.getMom();
        Optional.ofNullable(momFlightList).orElse(Lists.newArrayList()).forEach(t -> {
            switch (typeEnum) {
                case DETAIL_AMOUNT_REPORT:
                    agreementMom.set(
                            OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()), t.getAgreementAmount()));
                    unAgreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()),
                            t.getNotAgreementAmount()));
                    domesticMom
                            .set(OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()), t.getDomesticAmount()));
                    internationalMom.set(OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()),
                            t.getInternationalAmount()));
                    totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getRealPay()));
                    break;
                case DETAIL_TICKET_REPORT:
                    agreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()),
                            new BigDecimal(t.getAgreementQuantity())));
                    unAgreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()),
                            new BigDecimal(t.getNotAgreementQuantity())));
                    domesticMom.set(OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()),
                            new BigDecimal(t.getDomesticQuantity())));
                    internationalMom.set(OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()),
                            new BigDecimal(t.getInternationalQuantity())));
                    totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()),
                            new BigDecimal(t.getQuantityV())));
                    break;
                default:
            }
        });

        // 精度,日本站金额取整
        int precision = typeEnum == DetailReportTypeEnum.DETAIL_AMOUNT_REPORT ? (isJP ? OrpConstants.ZERO : OrpConstants.TWO) : OrpConstants.ZERO;
        ReportDataDetailData detailData = new ReportDataDetailData();
        // 协议
        detailData.setAgreementV(
                OrpCommonUtils.precisionConversion(new BigDecimal(agreementV.get()), precision).toString());
        detailData.setAgreementYoy(OrpCommonUtils.yoy(agreementV.get(), agreementYoy.get()));
        detailData.setAgreementMom(OrpCommonUtils.mom(agreementV.get(), agreementMom.get()));
        // 非协议
        detailData.setUnAgreementV(
                OrpCommonUtils.precisionConversion(new BigDecimal(unAgreementV.get()), precision).toString());
        detailData.setUnAgreementYoy(OrpCommonUtils.yoy(unAgreementV.get(), unAgreementYoy.get()));
        detailData.setUnAgreementMom(OrpCommonUtils.mom(unAgreementV.get(), unAgreementMom.get()));
        // 国内
        detailData.setDomesticV(
                OrpCommonUtils.precisionConversion(new BigDecimal(domesticV.get()), precision).toString());
        detailData.setDomesticYoy(OrpCommonUtils.yoy(domesticV.get(), domesticYoy.get()));
        detailData.setDomesticMom(OrpCommonUtils.mom(domesticV.get(), domesticMom.get()));
        // 国际
        detailData.setInternationalV(
                OrpCommonUtils.precisionConversion(new BigDecimal(internationalV.get()), precision).toString());
        detailData.setInternationalYoy(OrpCommonUtils.yoy(internationalV.get(), internationalYoy.get()));
        detailData.setInternationalMom(OrpCommonUtils.mom(internationalV.get(), internationalMom.get()));

        // 总计
        detailData
                .setTotalV(OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());
        detailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
        detailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));
        return new ReprotBodyData(totalTitle, detailData);
    }

    /**
     * 票张报表
     */
    private DetailData mapTicketInfoData(OnlineReportDetailRequest request, List<OnlineReportFlightDto> flightList,
                                         List<HeaderKeyValMap> headerKeyValMaps, String lang) {
        // 金额报表
        Map<String,
                ReprotBodyData> amountReportMap = Optional.ofNullable(flightList).orElse(Lists.newArrayList()).stream()
                .filter(Objects::nonNull).filter(t -> Objects.nonNull(t.getReportDate()))
                .collect(Collectors.groupingBy(t -> groupByMonth(t.getReportDate()))).entrySet().stream()
                .filter(t -> Objects.nonNull(t.getKey())).filter(t -> CollectionUtils.isNotEmpty(t.getValue()))
                .collect(Collectors.toMap((entity) -> entity.getKey(),
                        (entity) -> mapInfoTicketFlightReport(StringUtils.EMPTY, entity.getValue())));
        // hotel整体-金额明细
        List<ReprotBodyData> amountList = Lists.newArrayList();
        // dimension
        OrpDateTimeUtils.getMonthBetween(request.getBasecondition().startTime, request.getBasecondition().endTime)
                .forEach(month -> {
                    if (Objects.isNull(amountReportMap.get(month))) {
                        amountList.add(mapEmptyInfoReport(month));
                    } else {
                        amountList.add(amountReportMap.get(month));
                    }
                });
        OrpCollectionUtils.sortAsc(amountList, ReprotBodyData::getDimension);
        // 总计
        amountList.add(mapInfoTicketFlightReport(
                SharkUtils.getHeaderVal(
                        OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.totalV"), lang),
                flightList));
        return new DetailData(headerKeyValMaps, amountList);
    }

    /**
     * 空数据
     */
    private OnlineReportData mapFlightEmptyData(Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerAmount,
                                                Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerTicket) {
        DetailAmountDto amountDto = new DetailAmountDto();
        DetailTicketDto ticketDto = new DetailTicketDto();

        amountDto.setAllDetail(
                new DetailData(headerAmount.get(ReportDetailHeaderEnum.FLIGHT_ALL_HEADER), Lists.newArrayList()));
        amountDto.setAgreementtDetail(
                new DetailData(headerAmount.get(ReportDetailHeaderEnum.FLIGHT_AGREEMENT_HEADER), Lists.newArrayList()));
        amountDto.setNotAgreementDetail(
                new DetailData(headerAmount.get(ReportDetailHeaderEnum.FLIGHT_NOT_AGREEMENT_HEADER), Lists.newArrayList()));
        amountDto.setDomesticDetail(
                new DetailData(headerAmount.get(ReportDetailHeaderEnum.FLIGHT_DOMESTIC_HEADER), Lists.newArrayList()));
        amountDto.setInternationalDetail(
                new DetailData(headerAmount.get(ReportDetailHeaderEnum.FLIGHT_INTERNATIONAL_HEADER), Lists.newArrayList()));
        amountDto.setInfoDetail(
                new DetailData(headerAmount.get(ReportDetailHeaderEnum.FLIGHT_INFO_AMOUNT_HEADER), Lists.newArrayList()));
        ticketDto.setInfoDetail(
                new DetailData(headerTicket.get(ReportDetailHeaderEnum.FLIGHT_INFO_TICKET_HEADER), Lists.newArrayList()));
        return new OnlineReportData(amountDto, ticketDto);
    }

    @Override
    public Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> mapperHeader(DetailReportTypeEnum reportTypeEnum,
                                                                           QueryReportBuTypeEnum bu, String lang) {
        return mapperHeaderByBu(reportTypeEnum, bu, lang);
    }

    @Override
    public Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> mapperJPHeader(DetailReportTypeEnum reportTypeEnum,
                                                                             QueryReportBuTypeEnum bu, String lang, boolean isBlueSpace) {
        return mapperJPHeaderByBu(reportTypeEnum, bu, lang, isBlueSpace);
    }

    /**
     * map 当期/同比/环比-数据
     *
     * @param splitPair
     * @param overViewList
     * @return
     */
    private CurrentYoyMomData<OnlineReportFlightDto> getCurrentYoyMomFlightData(OrpDateTimeUtils.MonthSplitPair splitPair,
                                                                                List<OnlineReportFlightDto> overViewList) {
        CurrentYoyMomData<OnlineReportFlightDto> currentYoyMomData = new CurrentYoyMomData<>();
        List<OnlineReportFlightDto> current = new ArrayList<>();
        List<OnlineReportFlightDto> yoy = new ArrayList<>();
        List<OnlineReportFlightDto> mom = new ArrayList<>();

        overViewList.forEach(t -> {
            if (OrpDateTimeUtils.dateInRange(splitPair.getStartDate(), splitPair.getEndDate(), t.getReportDate())) {
                current.add(t);
            }

            if (OrpDateTimeUtils.dateInRange(splitPair.getYoyStartDate(), splitPair.getYoyEndDate(), t.getReportDate())) {
                yoy.add(t);
            }

            if (OrpDateTimeUtils.dateInRange(splitPair.getMomStartDate(), splitPair.getMomEndDate(), t.getReportDate())) {
                mom.add(t);
            }
        });
        currentYoyMomData.setCurrent(current);
        currentYoyMomData.setYoy(yoy);
        currentYoyMomData.setMom(mom);
        return currentYoyMomData;
    }

}
