package com.corpgovernment.resource.schedule.onlinereport.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.detail
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-10 16:06
 **/
@Entity
//@Database(name = "ck_corpbi_htlpftdb")
@Table(name = "adm_indexcar_price_detail")
@Data
public class OnlineReportCarDto {

    /**
     * 查询时间
     */
    @Column(name = "reportDate")
    @Type(value = Types.VARCHAR)
    private String reportDate;

    /**
     * 订单类型
     * <p>
     * 1-国内接送机;2-国际接送机;;3-包车;4-租车;6-打车
     */
    @Column(name = "orderType")
    @Type(value = Types.INTEGER)
    private Integer orderType;

    /**
     * 订单子类型
     * <p>
     * 1-国内打车;CAR_TAXI_INTL-国际打车
     */
    @Column(name = "subProductLine")
    @Type(value = Types.VARCHAR)
    private String subProductLine;

    /**
     * 基础费用
     */
    @Column(name = "basicFee")
    @Type(value = Types.DECIMAL)
    private BigDecimal basicFee;

    /**
     * 商旅服务费
     */
    @Column(name = "serviceFee")
    @Type(value = Types.DECIMAL)
    private BigDecimal serviceFee;

    /**
     * 退款金额
     */
    @Column(name = "refundAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal refundAmount;

    /**
     * 消费金额
     */
    @Column(name = "realPay")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPay;
    /**
     * 消费金额
     */
    @Column(name = "realPayYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPayYoy;
    /**
     * 消费金额
     */
    @Column(name = "realPayMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPayMom;


    /**
     * 金额明细-国内打车-当期
     */
    @Column(name = "takeTaxiAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal takeTaxiAmount;
    /**
     * 金额明细-国内打车-同比
     */
    @Column(name = "takeTaxiAmountYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal takeTaxiAmountYoy;
    /**
     * 金额明细-国内打车-环比
     */
    @Column(name = "takeTaxiAmountMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal takeTaxiAmountMom;

    /**
     * 金额明细-国际打车-当期
     */
    @Column(name = "takeTaxiAmountInter")
    @Type(value = Types.DECIMAL)
    private BigDecimal takeTaxiAmountInter;
    /**
     * 金额明细-国际打车-同比
     */
    @Column(name = "takeTaxiAmountYoyInter")
    @Type(value = Types.DECIMAL)
    private BigDecimal takeTaxiAmountYoyInter;
    /**
     * 金额明细-国际打车-环比
     */
    @Column(name = "takeTaxiAmountMomInter")
    @Type(value = Types.DECIMAL)
    private BigDecimal takeTaxiAmountMomInter;


    /**
     * 金额明细-国内接送机-当期
     */
    @Column(name = "airportPickUpAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal airportPickUpAmount;
    /**
     * 金额明细-国内接送机-同比
     */
    @Column(name = "airportPickUpAmountYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal airportPickUpAmountYoy;
    /**
     * 金额明细-国内接送机-环比
     */
    @Column(name = "airportPickUpAmountMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal airportPickUpAmountMom;

    /**
     * 金额明细-国际接送机-当期
     */
    @Column(name = "airportPickUpAmountInter")
    @Type(value = Types.DECIMAL)
    private BigDecimal airportPickUpAmountInter;
    /**
     * 金额明细-国际接送机-同比
     */
    @Column(name = "airportPickUpAmountYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal airportPickUpAmountYoyInter;
    /**
     * 金额明细-国际接送机-环比
     */
    @Column(name = "airportPickUpAmountMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal airportPickUpAmountMomInter;

    /**
     * 金额明细-租车-当期
     */
    @Column(name = "rentalCarAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal rentalCarAmount;
    /**
     * 金额明细-租车-同比
     */
    @Column(name = "rentalCarAmountYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal rentalCarAmountYoy;
    /**
     * 金额明细-租车-环比
     */
    @Column(name = "rentalCarAmountMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal rentalCarAmountMom;

    /**
     * 金额明细-国际租车-当期
     */
    @Column(name = "rentalCarAmountInter")
    @Type(value = Types.DECIMAL)
    private BigDecimal rentalCarAmountInter;
    /**
     * 金额明细-国际租车-同比
     */
    @Column(name = "rentalCarAmountYoyInter")
    @Type(value = Types.DECIMAL)
    private BigDecimal rentalCarAmountYoyInter;
    /**
     * 金额明细-国际租车-环比
     */
    @Column(name = "rentalCarAmountMomInter")
    @Type(value = Types.DECIMAL)
    private BigDecimal rentalCarAmountMomInter;

    /**
     * 金额明细-包车-当期
     */
    @Column(name = "charteredCarAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal charteredCarAmount;
    /**
     * 金额明细-包车-同比
     */
    @Column(name = "charteredCarAmountYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal charteredCarAmountYoy;
    /**
     * 金额明细-包车-环比
     */
    @Column(name = "charteredCarAmountMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal charteredCarAmountMom;

    /**
     * 票张明细-总-当期
     */
    @Column(name = "quantityV")
    @Type(value = Types.BIGINT)
    private Long quantityV;
    /**
     * 票张明细-总-同比
     */
    @Column(name = "quantityYoy")
    @Type(value = Types.BIGINT)
    private Long quantityYoy;
    /**
     * 票张明细-总-环比
     */
    @Column(name = "quantityMom")
    @Type(value = Types.BIGINT)
    private Long quantityMom;
    /**
     * 票张明细-国内打车-当期
     */
    @Column(name = "takeTaxiQuantity")
    @Type(value = Types.BIGINT)
    private Long takeTaxiQuantity;
    /**
     * 票张明细-国内打车-同比
     */
    @Column(name = "takeTaxiQuantityYoy")
    @Type(value = Types.BIGINT)
    private Long takeTaxiQuantityYoy;
    /**
     * 票张明细-国内打车-环比
     */
    @Column(name = "takeTaxiQuantityMom")
    @Type(value = Types.BIGINT)
    private Long takeTaxiQuantityMom;

    /**
     * 票张明细-国际打车-当期
     */
    @Column(name = "takeTaxiQuantityInter")
    @Type(value = Types.BIGINT)
    private Long takeTaxiQuantityInter;
    /**
     * 票张明细-国际打车-同比
     */
    @Column(name = "takeTaxiQuantityYoyInter")
    @Type(value = Types.BIGINT)
    private Long takeTaxiQuantityYoyInter;
    /**
     * 票张明细-国际打车-环比
     */
    @Column(name = "takeTaxiQuantityMomInter")
    @Type(value = Types.BIGINT)
    private Long takeTaxiQuantityMomInter;

    /**
     * 票张明细-国内接送机-当期
     */
    @Column(name = "airportPickUpQuantity")
    @Type(value = Types.BIGINT)
    private Long airportPickUpQuantity;
    /**
     * 票张明细-国内接送机-同比
     */
    @Column(name = "airportPickUpQuantityYoy")
    @Type(value = Types.BIGINT)
    private Long airportPickUpQuantityYoy;
    /**
     * 票张明细-国内接送机-环比
     */
    @Column(name = "airportPickUpQuantityMom")
    @Type(value = Types.BIGINT)
    private Long airportPickUpQuantityMom;

    /**
     * 票张明细-国际接送机-当期
     */
    @Column(name = "airportPickUpQuantityInter")
    @Type(value = Types.BIGINT)
    private Long airportPickUpQuantityInter;
    /**
     * 票张明细-国际接送机-同比
     */
    @Column(name = "airportPickUpQuantityYoyInter")
    @Type(value = Types.BIGINT)
    private Long airportPickUpQuantityYoyInter;
    /**
     * 票张明细-国际接送机-环比
     */
    @Column(name = "airportPickUpQuantityMomInter")
    @Type(value = Types.BIGINT)
    private Long airportPickUpQuantityMomInter;

    /**
     * 票张明细-租车-当期
     */
    @Column(name = "rentalCarQuantity")
    @Type(value = Types.BIGINT)
    private Long rentalCarQuantity;
    /**
     * 票张明细-租车-同比
     */
    @Column(name = "rentalCarQuantityYoy")
    @Type(value = Types.BIGINT)
    private Long rentalCarQuantityYoy;
    /**
     * 票张明细-租车-环比
     */
    @Column(name = "rentalCarQuantityMom")
    @Type(value = Types.BIGINT)
    private Long rentalCarQuantityMom;

    /**
     * 票张明细-国际租车-当期
     */
    @Column(name = "rentalCarQuantityInter")
    @Type(value = Types.BIGINT)
    private Long rentalCarQuantityInter;
    /**
     * 票张明细-国际租车-同比
     */
    @Column(name = "rentalCarQuantityYoyInter")
    @Type(value = Types.BIGINT)
    private Long rentalCarQuantityYoyInter;
    /**
     * 票张明细-国际租车-环比
     */
    @Column(name = "rentalCarQuantityMomInter")
    @Type(value = Types.BIGINT)
    private Long rentalCarQuantityMomInter;

    /**
     * 票张明细-包车-当期
     */
    @Column(name = "charteredCarQuantity")
    @Type(value = Types.BIGINT)
    private Long charteredCarQuantity;
    /**
     * 票张明细-包车-同比
     */
    @Column(name = "charteredCarQuantityYoy")
    @Type(value = Types.BIGINT)
    private Long charteredCarQuantityYoy;
    /**
     * 票张明细-包车-环比
     */
    @Column(name = "charteredCarQuantityMom")
    @Type(value = Types.BIGINT)
    private Long charteredCarQuantityMom;

}
