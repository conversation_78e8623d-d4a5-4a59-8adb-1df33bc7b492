package com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 11:02
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class OnlineReportSaveFlightDisDTO {

    @Column(name = "minReportDate")
    @Type(value = Types.VARCHAR)
    private String minReportDate;

    @Column(name = "cSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal cSave;

    @Column(name = "cSaveRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal cSaveRate;

    @Column(name = "cSavePerQuantity")
    @Type(value = Types.DECIMAL)
    private BigDecimal cSavePerQuantity;

    @Column(name = "premiumSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal premiumSave;

    @Column(name = "premiumSaveRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal premiumSaveRate;

    @Column(name = "premiumSavePerQuantity")
    @Type(value = Types.DECIMAL)
    private BigDecimal premiumSavePerQuantity;

    @Column(name = "controlSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal controlSave;

    @Column(name = "controlSaveRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal controlSaveRate;

    @Column(name = "controlSavePerQuantity")
    @Type(value = Types.DECIMAL)
    private BigDecimal controlSavePerQuantity;

    @Column(name = "controlResponseRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal controlResponseRate;

}
