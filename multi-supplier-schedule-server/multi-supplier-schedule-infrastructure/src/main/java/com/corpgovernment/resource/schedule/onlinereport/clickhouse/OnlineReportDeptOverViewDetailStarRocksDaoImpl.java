package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptDetailDaoService;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
public class OnlineReportDeptOverViewDetailStarRocksDaoImpl  extends AbstractOnlineReportDeptDetailStarRocksDao implements OnlineReportDeptDetailDaoService {

    @Override
    protected String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return StringUtils.EMPTY;
    }

    @Override
    protected String getProcutTypeCondition(String productType) {
        return StringUtils.EMPTY;
    }

    /**
     * 统计字段
     *
     * @return
     */
    @Override
    protected String statical() {
        List sql = new ArrayList();
        sql.add("sum(coalesce(amount_total, 0)) as TOTAL_REAL_PAY");
        sql.add("sum(case when is_online = 'M' then quantity_total else 0 end ) as TOTAL_APP_QUANTITY");
        sql.add("sum(case when is_online = 'F' then quantity_total else 0 end ) as TOTAL_OFFLINE_QUANTITY");
        sql.add("sum(case when is_online = 'T' then quantity_total else 0 end ) as TOTAL_ONLINE_QUANTITY");
        sql.add("sum(quantity_total) as TOTAL_QUANTITY");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    /**
     * 统计字段
     *
     * @return
     */
    @Override
    protected String staticalForeign() {
        List sql = new ArrayList();
        sql.add("sum(coalesce(amount_total, 0)) as TOTAL_REAL_PAY");
        sql.add("sum(quantity_total) as TOTAL_QUANTITY");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    @Override
    protected String orderByField() {
        return "TOTAL_REAL_PAY desc";
    }

    @Override
    protected BaseConditionPrebuilder.TableAndTimeColBind getTargetTableAndTimeColumn(String statisticalCaliber, Boolean isForegin) {
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = new BaseConditionPrebuilder.TableAndTimeColBind();
        if (isForegin) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                tableAndTimeColBind.setClickHouseTable(ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN_MULTI_CURRENCY);
            } else {
                tableAndTimeColBind.setClickHouseTable(ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN);
            }
            tableAndTimeColBind.setDateColumn(OrpConstants.REPORT_DATE);
        } else {
            tableAndTimeColBind.setClickHouseTable(ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY);
            tableAndTimeColBind.setDateColumn(OrpConstants.REPORT_DATE);
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                tableAndTimeColBind.setClickHouseTable(ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT);
            }
        }
        return tableAndTimeColBind;
    }

    @Override
    protected String totalField() {
        return "SUM(coalesce(amount_total, 0)) AS TOTAL_REAL_PAY";
    }

    /**
     * 同环比，总计数据
     *
     * @return
     */
    @Override
    protected String momAndYoy() {
        return "sum(coalesce(amount_total, 0))  as TOTAL_REAL_PAY";
    }

    /**
     * 返回字段
     *
     * @return
     */
    protected String baseQueryField() {
        List sql = new ArrayList();
        sql.add("round(current.TOTAL_REAL_PAY, 4) AS REAL_PAY");
        sql.add("round(case when coalesce(total.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0))"
                + ", toFloat64(coalesce(total.TOTAL_REAL_PAY, 0))) * 100 else 0 end, 4) as REAL_PAY_PERCENT");
        sql.add("round(case when coalesce(yoy.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(yoy.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(yoy.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_LAST");
        sql.add("round(case when coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_BEFORE_LAST");
        sql.add("round(case when coalesce(mom.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(current.TOTAL_REAL_PAY, 0) - coalesce(mom.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(mom.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as MOM");
        sql.add("round(case when coalesce(current.TOTAL_QUANTITY, 0) !=0 "
                + "then divide(coalesce(current.TOTAL_APP_QUANTITY, 0), coalesce(current.TOTAL_QUANTITY, 0)) * 100 else 0 end, 4) as APP_PERCENT");
        sql.add("round(case when coalesce(current.TOTAL_QUANTITY, 0) !=0 "
                + "then divide(coalesce(current.TOTAL_ONLINE_QUANTITY, 0), coalesce(current.TOTAL_QUANTITY, 0)) * 100 else 0 end , 4) as ONLINE_PERCENT");
        sql.add("round(case when coalesce(current.TOTAL_QUANTITY, 0) !=0 "
                + "then divide(coalesce(current.TOTAL_OFFLINE_QUANTITY, 0), coalesce(current.TOTAL_QUANTITY, 0)) * 100 else 0 end, 4) as OFFLINE_PERCENT");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

}
