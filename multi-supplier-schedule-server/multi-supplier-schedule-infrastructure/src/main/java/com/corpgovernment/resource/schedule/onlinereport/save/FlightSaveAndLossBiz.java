package com.corpgovernment.resource.schedule.onlinereport.save;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.GeneralPotentialSaveInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.GeneralSaveDistributionInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.GeneralSaveInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPotentialSaveLowRclRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveGeneralRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PotentialSaveLowRcInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.save.FlightSaveAndLossDao;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.FlightSaveStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatisticalEnumerable;
import com.corpgovernment.resource.schedule.onlinereport.module.save.FlightLowRcPotentialSaveDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.save.FlightSaveDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.save.PotentialSaveDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.google.common.util.concurrent.AtomicDouble;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/*
 * <AUTHOR>
 * @date 2022/6/20 13:08
 * @Desc
 */
@Service
public class FlightSaveAndLossBiz extends AbstractSaveAndLossBiz {


    private static final Logger log = LoggerFactory.getLogger(FlightSaveAndLossBiz.class);
    @Autowired
    private FlightSaveAndLossDao flightSaveAndLossDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Qualifier("screenThreadPoolExecutor")
    @Resource
    private ThreadPoolExecutor executor;


    /**
     * 节省概览
     *
     * @param request
     * @throws Exception
     */
    
    public GeneralSaveInfo saveGeneral(OnlineReportSaveGeneralRequest request) throws Exception {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        BaseQueryConditionDTO dto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        GeneralSaveInfo saveBO = new GeneralSaveInfo();
        saveBO.setTotalSaveAmount(BigDecimal.ZERO);
        ExecutorService executorService = executor;

        Future<GeneralSaveInfo> future1 = executorService.submit(new Callable<GeneralSaveInfo>() {
            @Override
            public GeneralSaveInfo call() throws Exception {
                log.info("the dto is:{} , the productType is:{}", JsonUtils.toJsonString(dto), request.getProductType());
                List<FlightSaveDTO> total = flightSaveAndLossDao.agreementSaveTotal(dto, request.getProductType());
                log.info("the total is:{}", JsonUtils.toJsonString(total));
                if (CollectionUtils.isNotEmpty(total)) {
                    FlightSaveDTO flightSaveDTO = total.get(0);
                    double saveAmount = flightSaveDTO.getSaveAmount3c() + flightSaveDTO.getSaveAmountPremium() +
                            Optional.ofNullable(flightSaveDTO.getControlSaveAmount()).orElse(0d);
                    saveBO.setTotalSaveAmount(OrpReportUtils.formatDouble(saveAmount));
                    saveBO.setReportDate(flightSaveDTO.getReportDate());
                }
                return saveBO;
            }
        });
        /*Future<GeneralSaveInfo> future2 = executorService.submit(new Callable<GeneralSaveInfo>() {
            @Override
            public GeneralSaveInfo call() throws Exception {
                List<FlightSaveDTO> list = flightSaveAndLossDao.agreementSave(dto, request.getProductType());
                List<FlightSaveDTO> corpList = flightSaveAndLossDao.agreementSaveCorpAndIndustry(dto.getStartTime(), dto.getEndTime(), dto.getStatisticalCaliber(),
                        null, DataTypeEnum.CORP, request.getProductType(),
                        dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
                List<FlightSaveDTO> industryList = flightSaveAndLossDao.agreementSaveCorpAndIndustry(dto.getStartTime(), dto.getEndTime(), dto.getStatisticalCaliber(),
                        Arrays.asList(request.getBasecondition().getIndustryType()), DataTypeEnum.INDUSTRY, request.getProductType(),
                        dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
                if (CollectionUtils.isNotEmpty(list)) {
                    FlightSaveDTO flightSaveDTO = list.get(0);
                    double saveAmount = flightSaveDTO.getSaveAmount3c() + compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getSaveAmountPremium()) +
                            Optional.ofNullable(flightSaveDTO.getControlSaveAmount()).orElse(0d);
                    saveBO.setCompanySaveAmount(OrpReportUtils.formatDouble(saveAmount));
                    saveBO.setCompanySaveRate(OrpReportUtils.divideWithPercent(saveAmount
                            , flightSaveDTO.getNetfare3c() +
                                    compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getNetfarePremium()) +
                                    Optional.ofNullable(flightSaveDTO.getControlSaveAmount()).orElse(0d)));
                    saveBO.setCompanyPerQtySave(OrpReportUtils.divideUp(saveAmount
                            , new Double(flightSaveDTO.getQty3c() + compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getQtyPremium()) +
                                    Optional.ofNullable(flightSaveDTO.getQtyControl()).orElse(0))
                            , OrpConstants.TWO));
                }
                if (CollectionUtils.isNotEmpty(corpList)) {
                    FlightSaveDTO flightSaveDTO = corpList.get(0);
                    double saveAmount = flightSaveDTO.getSaveAmount3c() + compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getSaveAmountPremium());
                    saveBO.setCorpSaveRate(OrpReportUtils.divideWithPercent(saveAmount
                            , flightSaveDTO.getNetfare3c() + compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getNetfarePremium()) +
                                    Optional.ofNullable(flightSaveDTO.getControlSaveAmount()).orElse(0d)));
                    saveBO.setCorpPerQtySave(OrpReportUtils.divideUp(saveAmount
                            , new Double(flightSaveDTO.getQty3c() + compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getQtyPremium()) +
                                    Optional.ofNullable(flightSaveDTO.getQtyControl()).orElse(0))
                            , OrpConstants.TWO));
                }
                if (CollectionUtils.isNotEmpty(industryList)) {
                    FlightSaveDTO flightSaveDTO = industryList.get(0);
                    double saveAmount = flightSaveDTO.getSaveAmount3c() + compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getSaveAmountPremium());
                    saveBO.setIndustrySaveRate(OrpReportUtils.divideWithPercent(saveAmount
                            , flightSaveDTO.getNetfare3c() + compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getNetfarePremium()) +
                                    Optional.ofNullable(flightSaveDTO.getControlSaveAmount()).orElse(0d)));
                    saveBO.setIndustryPerQtySave(OrpReportUtils.divideUp(saveAmount
                            , new Double(flightSaveDTO.getQty3c() + compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getQtyPremium()) +
                                    Optional.ofNullable(flightSaveDTO.getQtyControl()).orElse(0))
                            , OrpConstants.TWO));
                }
                return saveBO;
            }
        });*/

        Future<GeneralSaveInfo> future2 = executorService.submit(new Callable<GeneralSaveInfo>() {
            @Override
            public GeneralSaveInfo call() throws Exception {

                List<FlightSaveDTO> list = flightSaveAndLossDao.agreementSave(dto, request.getProductType());
                List<FlightSaveDTO> corpList = flightSaveAndLossDao.agreementSaveCorpAndIndustry(dto.getStartTime(), dto.getEndTime(), dto.getStatisticalCaliber(),
                        null, DataTypeEnum.CORP, request.getProductType(),
                        dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
                List<FlightSaveDTO> industryList = flightSaveAndLossDao.agreementSaveCorpAndIndustry(dto.getStartTime(), dto.getEndTime(), dto.getStatisticalCaliber(),
                        Arrays.asList(request.getBasecondition().getIndustryType()), DataTypeEnum.INDUSTRY, request.getProductType(),
                        dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());

                // Process company data
                Optional.ofNullable(list)
                        .filter(CollectionUtils::isNotEmpty)
                        .map(l -> l.get(0))
                        .ifPresent(flightSaveDTO -> {
                            double saveAmount = flightSaveDTO.getSaveAmount3c() +
                                    compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getSaveAmountPremium()) +
                                    Optional.ofNullable(flightSaveDTO.getControlSaveAmount()).orElse(0d);
                            double totalNetfare = flightSaveDTO.getNetfare3c() +
                                    compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getNetfarePremium()) +
                                    Optional.ofNullable(flightSaveDTO.getControlSaveAmount()).orElse(0d);
                            double totalQty = flightSaveDTO.getQty3c() +
                                    compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getQtyPremium()) +
                                    Optional.ofNullable(flightSaveDTO.getQtyControl()).orElse(0);

                            saveBO.setCompanySaveAmount(OrpReportUtils.formatDouble(saveAmount));
                            saveBO.setCompanySaveRate(OrpReportUtils.divideWithPercent(saveAmount, totalNetfare));
                            saveBO.setCompanyPerQtySave(OrpReportUtils.divideUp(saveAmount, totalQty, OrpConstants.TWO));
                        });

                // Process corporate data
                Optional.ofNullable(corpList)
                        .filter(CollectionUtils::isNotEmpty)
                        .map(l -> l.get(0))
                        .ifPresent(flightSaveDTO -> {
                            double saveAmount = flightSaveDTO.getSaveAmount3c() +
                                    compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getSaveAmountPremium());
                            double totalNetfare = flightSaveDTO.getNetfare3c() +
                                    compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getNetfarePremium()) +
                                    Optional.ofNullable(flightSaveDTO.getControlSaveAmount()).orElse(0d);
                            double totalQty = flightSaveDTO.getQty3c() +
                                    compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getQtyPremium()) +
                                    Optional.ofNullable(flightSaveDTO.getQtyControl()).orElse(0);
                            saveBO.setCorpSaveRate(OrpReportUtils.divideWithPercent(saveAmount, totalNetfare));
                            saveBO.setCorpPerQtySave(OrpReportUtils.divideUp(saveAmount, totalQty, OrpConstants.TWO));
                        });

                // Process industry data
                Optional.ofNullable(industryList)
                        .filter(CollectionUtils::isNotEmpty)
                        .map(l -> l.get(0))
                        .ifPresent(flightSaveDTO -> {
                            double saveAmount = Optional.ofNullable(flightSaveDTO.getSaveAmount3c()).orElse(0d) +
                                    compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getSaveAmountPremium());
                            double totalNetfare = Optional.ofNullable(flightSaveDTO.getNetfare3c()).orElse(0d) +
                                    compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getNetfarePremium()) +
                                    Optional.ofNullable(flightSaveDTO.getControlSaveAmount()).orElse(0d);
                            double totalQty = Optional.ofNullable(flightSaveDTO.getQty3c()).orElse(0) +
                                    compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getQtyPremium()) +
                                    Optional.ofNullable(flightSaveDTO.getQtyControl()).orElse(0);

                            saveBO.setIndustrySaveRate(OrpReportUtils.divideWithPercent(saveAmount, totalNetfare));
                            saveBO.setIndustryPerQtySave(OrpReportUtils.divideUp(saveAmount, totalQty, OrpConstants.TWO));
                        });

                return saveBO;
            }
        });

        log.info("saveGeneral cost:{}", stopWatch.getTime());

        future1.get();
        future2.get();
        return saveBO;
    }

    /**
     * 节省分布概览
     *
     * @param request
     * @throws Exception
     */
    
    public GeneralSaveDistributionInfo saveDistributionGeneral(OnlineReportSaveGeneralRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<FlightSaveDTO> list = flightSaveAndLossDao.agreementSave(baseQueryConditionDto, request.getProductType());
        GeneralSaveDistributionInfo saveDistributionBO = new GeneralSaveDistributionInfo();
        if (CollectionUtils.isNotEmpty(list)) {
            FlightSaveDTO flightSaveDTO = list.get(0);
            double saveAmount = flightSaveDTO.getSaveAmount3c() + compareZeroAndGetDefault(flightSaveDTO.getSaveAmountPremium()) +
                    Optional.ofNullable(flightSaveDTO.getControlSaveAmount()).orElse(0d);
            // 三方节省金额
            saveDistributionBO.setSaveAmount3c(OrpReportUtils.formatDouble(flightSaveDTO.getSaveAmount3c()));
            saveDistributionBO.setSaveAmount3cPercent(OrpReportUtils.divideWithPercent(flightSaveDTO.getSaveAmount3c(), saveAmount));

            // 两方节省金额
            saveDistributionBO.setSaveAmount2c(OrpReportUtils.formatDouble(flightSaveDTO.getSaveAmountPremium()));
            saveDistributionBO.setSaveAmount2cPercent(OrpReportUtils.divideWithPercent(compareZeroAndGetDefault(flightSaveDTO.getSaveAmountPremium()), saveAmount));

            // 管控节省金额
            saveDistributionBO.setControlSave(OrpReportUtils.formatDouble(flightSaveDTO.getControlSaveAmount()));
            saveDistributionBO.setControlSavePercent(OrpReportUtils.divideWithPercent(compareZeroAndGetDefault(flightSaveDTO.getControlSaveAmount()), saveAmount));

            saveDistributionBO.setSaveAmount(OrpReportUtils.formatDouble(saveAmount));
        }
        if (Objects.isNull(request.getExtData()) || !StringUtils.equalsIgnoreCase(request.getExtData().get("needTotal"), "F")) {
            List<FlightSaveDTO> total = flightSaveAndLossDao.agreementSaveTotal(baseQueryConditionDto, request.getProductType());
            if (CollectionUtils.isNotEmpty(total)) {
                FlightSaveDTO flightSaveDTO = total.get(0);
                saveDistributionBO.setReportDate(flightSaveDTO.getReportDate());
                saveDistributionBO.setTotalSaveAmount3c(OrpReportUtils.formatDouble(flightSaveDTO.getSaveAmount3c()));
                saveDistributionBO.setTotalSaveAmount2c(OrpReportUtils.formatDouble(flightSaveDTO.getSaveAmountPremium()));
                // 管控节省金额 todo
                saveDistributionBO.setTotalControlSave(OrpReportUtils.formatDouble(flightSaveDTO.getControlSaveAmount()));
            }
        }
        return saveDistributionBO;
    }

    /**
     * 潜在节省概览
     *
     * @param request
     * @throws Exception
     */
    
    public GeneralPotentialSaveInfo potentialSaveGeneral(OnlineReportSaveGeneralRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<PotentialSaveDTO> list = flightSaveAndLossDao.agreementPotentialSave(baseQueryConditionDto, PotentialSaveDTO.class, request.getProductType());
        List<PotentialSaveDTO> totalList = flightSaveAndLossDao.agreementPotentialSaveTotal(baseQueryConditionDto, PotentialSaveDTO.class, request.getProductType());
        GeneralPotentialSaveInfo potentialSaveBO = new GeneralPotentialSaveInfo();
        if (CollectionUtils.isNotEmpty(list)) {
            PotentialSaveDTO potentialSaveDTO = list.get(0);
            potentialSaveBO.setOverAmount(compareZeroAndGetDefault(OrpReportUtils.formatDouble(potentialSaveDTO.getOverAmount())));
            potentialSaveBO.setRcTimes(potentialSaveDTO.getRcTimes());
            potentialSaveBO.setRefundloss(OrpReportUtils.formatDouble(compareZeroAndGetDefault(potentialSaveDTO.getRefundloss())));
            potentialSaveBO.setRefundtkt(potentialSaveDTO.getRefundtkt());
            potentialSaveBO.setRebookloss(compareZeroAndGetDefault(OrpReportUtils.formatDouble(potentialSaveDTO.getRebookloss())));
            potentialSaveBO.setRebooktkt(potentialSaveDTO.getRebooktkt());
            potentialSaveBO.setPotentialSaveAmount(OrpReportUtils.formatBigDecimal(potentialSaveBO.getOverAmount()
                    .add(potentialSaveBO.getRefundloss()).add(potentialSaveBO.getRebookloss())));
        }
        if (CollectionUtils.isNotEmpty(totalList)) {
            PotentialSaveDTO potentialSaveDTO = totalList.get(0);
            potentialSaveBO.setReportDate(potentialSaveDTO.getReportDate());
            potentialSaveBO.setTotalOverAmount(compareZeroAndGetDefault(OrpReportUtils.formatDouble(potentialSaveDTO.getOverAmount())));
            potentialSaveBO.setTotalRefundloss(compareZeroAndGetDefault(OrpReportUtils.formatDouble(potentialSaveDTO.getRefundloss())));
            potentialSaveBO.setTotalRebookloss(compareZeroAndGetDefault(OrpReportUtils.formatDouble(potentialSaveDTO.getRebookloss())));
            potentialSaveBO.setTotalPotentialSaveAmount(OrpReportUtils.formatBigDecimal(potentialSaveBO.getTotalOverAmount()
                    .add(potentialSaveBO.getTotalRefundloss()).add(potentialSaveBO.getTotalRebookloss())));
        }
        return potentialSaveBO;
    }


    /**
     * 机票超标原因分析
     *
     * @param request
     * @throws Exception
     */
    
    public List<PotentialSaveLowRcInfo> lowRcAnalysis(OnlineReportPotentialSaveLowRclRequest request) throws Exception {
        List<PotentialSaveLowRcInfo> result = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<FlightLowRcPotentialSaveDTO> list = flightSaveAndLossDao.flightLowRcAnalysis(baseQueryConditionDto, FlightLowRcPotentialSaveDTO.class
                , request.getProductType(), request.getLang());
        // 总超标次数
        AtomicInteger sumRcTimes = new AtomicInteger();
        // 总潜在节省
        AtomicDouble sumOverAmount = new AtomicDouble();
        // 总成交净价
        AtomicDouble sumAmount = new AtomicDouble();
        // 总差标内最低价
        AtomicDouble sumCorpPriceAdj = new AtomicDouble();
        // 总票面价
        AtomicDouble sumPrintPrice = new AtomicDouble();
        list.forEach(i -> {
            sumRcTimes.set(sumRcTimes.get() + Optional.ofNullable(i.getRcTimes()).orElse(0));
            sumOverAmount.set(sumOverAmount.get() + Optional.ofNullable(OrpReportUtils.nonNegative(i.getOverAmount())).orElse(0d));
            sumAmount.set(sumAmount.get() + Optional.ofNullable(i.getAmount()).orElse(0d));
            sumCorpPriceAdj.set(sumCorpPriceAdj.get() + Optional.ofNullable(i.getCorpPriceAdj()).orElse(0d));
            sumPrintPrice.set(sumPrintPrice.get() + Optional.ofNullable(i.getPrintPrice()).orElse(0d));
        });
        if (CollectionUtils.isNotEmpty(list)) {
            for (FlightLowRcPotentialSaveDTO lowRcPotentialSaveDTO : list) {
                PotentialSaveLowRcInfo lowRcPotentialSaveBO = new PotentialSaveLowRcInfo();
                lowRcPotentialSaveBO.setLowRcCode(lowRcPotentialSaveDTO.getLowRcCode());
                lowRcPotentialSaveBO.setLowRcDesc(lowRcPotentialSaveDTO.getLowRcDesc());
                lowRcPotentialSaveBO.setRcTimes(lowRcPotentialSaveDTO.getRcTimes());
                lowRcPotentialSaveBO.setRcPercent(OrpReportUtils.divideWithPercent(lowRcPotentialSaveDTO.getRcTimes(), sumRcTimes.get()));
                lowRcPotentialSaveBO.setAmount(OrpReportUtils.formatDouble(lowRcPotentialSaveDTO.getAmount()));
                lowRcPotentialSaveBO.setCorpPriceAdj(OrpReportUtils.formatDouble(lowRcPotentialSaveDTO.getCorpPriceAdj()));
                lowRcPotentialSaveBO.setOverAmount(OrpReportUtils.formatDouble(OrpReportUtils.nonNegative(lowRcPotentialSaveDTO.getOverAmount())));
                lowRcPotentialSaveBO.setOverAmountPercent(OrpReportUtils.divideWithPercent(lowRcPotentialSaveBO.getOverAmount().doubleValue(), sumOverAmount.get()));
                lowRcPotentialSaveBO.setPrintPrice(OrpReportUtils.formatDouble(lowRcPotentialSaveDTO.getPrintPrice()));
                result.add(lowRcPotentialSaveBO);
            }
            // 汇总
            PotentialSaveLowRcInfo sumBO = new PotentialSaveLowRcInfo();
            sumBO.setLowRcCode(SharkUtils.get("Index.sum", request.getLang()));
            sumBO.setLowRcDesc(StringUtils.EMPTY);
            sumBO.setRcTimes(sumRcTimes.get());
            sumBO.setRcPercent(OrpReportUtils.divideWithPercent(OrpConstants.ONE, OrpConstants.ONE));
            sumBO.setAmount(OrpReportUtils.formatDouble(sumAmount.get()));
            sumBO.setCorpPriceAdj(OrpReportUtils.formatDouble(sumCorpPriceAdj.get()));
            sumBO.setOverAmount(OrpReportUtils.formatDouble(sumOverAmount.get()));
            sumBO.setOverAmountPercent(OrpReportUtils.divideWithPercent(OrpConstants.ONE, OrpConstants.ONE));
            sumBO.setPrintPrice(OrpReportUtils.formatDouble(sumPrintPrice.get()));
            result.add(sumBO);
        }
        return result;
    }

    public List<Map> deptDetail(BaseQueryCondition request, AnalysisObjectEnum analysisObjectEnum, Pager pager,
                                String orderType) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request);
        List result = flightSaveAndLossDao.topAnalysis(analysisObjectEnum, baseQueryConditionDto, BizUtils.initPager(pager), orderType);
        fomratResultData(result);
        return result;
    }

    
    public int count(BaseQueryCondition request, AnalysisObjectEnum analysisObjectEnum, String orderType)
            throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request);
        return flightSaveAndLossDao.topRcAnalysisCount(analysisObjectEnum, baseQueryConditionDto, orderType);
    }

    /**
     * 统一格式化结果数据
     *
     * @param list
     */
    protected void fomratResultData(List<Map> list) {
        int i = 0;
        for (Map map : list) {
            i++;
            for (Object key : map.keySet()) {
                FlightSaveStatisticalsEnum rcStatisticalsEnum = getStaticalByKey((String) key);
                if (rcStatisticalsEnum != null) {
                    BigDecimal target = OrpReportUtils.formatBigDecimal(
                            Objects.isNull(map.get(key)) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(map.get(key))),
                            rcStatisticalsEnum.getNum(), true);
                    map.put(key, rcStatisticalsEnum.isPercent() ? target.toString().concat("%") : target);
                }
            }
        }
    }

    protected FlightSaveStatisticalsEnum getStaticalByKey(String key) {
        try {
            return FlightSaveStatisticalsEnum.valueOf((String) key);
        } catch (Exception e) {

        }
        return null;
    }

    @Override
    public List<StatisticalEnumerable> getStatisticalList(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return Arrays.stream(FlightSaveStatisticalsEnum.values()).collect(Collectors.toList());
    }
}
