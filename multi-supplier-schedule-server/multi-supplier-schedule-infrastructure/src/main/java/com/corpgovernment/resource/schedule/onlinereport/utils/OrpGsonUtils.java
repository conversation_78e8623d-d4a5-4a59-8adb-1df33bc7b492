package com.corpgovernment.resource.schedule.onlinereport.utils;


import com.corpgovernment.resource.schedule.onlinereport.utils.adapter.NullStringToEmptyAdapterFactory;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.util.Objects;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.utils
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-03 18:58
 **/
public class OrpGsonUtils {

    protected static final Gson GSON = new Gson();
    protected static final Gson GSON_WITH_ADAPTER = new GsonBuilder().serializeNulls().registerTypeAdapterFactory(new NullStringToEmptyAdapterFactory()).create();
    /**
     * 创建取出转译"\"的Gson对象
     */
    protected static final Gson GSON_TO_FORMAT = new GsonBuilder()
            .setPrettyPrinting()
            .disableHtmlEscaping()
            .create();

    /**
     * json 转对象
     *
     * @param json
     * @param typeOfT
     * @param <T>
     * @return
     */
    public static <T> T jsonNullToType(String json, Type typeOfT) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return getGson().fromJson(json, typeOfT);
        } catch (JsonSyntaxException e) {
            throw new RuntimeException("30000, Request failed");
        }
    }

    /**
     * json 转对象
     *
     * @param json
     * @param typeOfT
     * @param <T>
     * @return
     */
    public static <T> T fromToJsonType(String json, Type typeOfT) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return GSON.fromJson(json, typeOfT);
        } catch (JsonSyntaxException e) {
            throw new RuntimeException("30000, Request failed");
        }
    }

    public static Gson getGson() {
        return new GsonBuilder().serializeNulls()
                .registerTypeAdapterFactory(
                        new NullStringToEmptyAdapterFactory<Object>()).create();
    }

    /**
     * 格式化json
     *
     * @param json
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> T jsonStrFormat(R json) {
        if (Objects.isNull(json)) {
            return null;
        }
        return (T) GSON_TO_FORMAT.toJson(json);
    }

    /**
     * 转换成json 字串
     *
     * @param json
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> T toJsonStr(R json) {
        if (Objects.isNull(json)) {
            return null;
        }
        return (T) GSON.toJson(json);
    }

    public static <T, R> T toJsonNullStr(R json) {
        if (Objects.isNull(json)) {
            return null;
        }
        return (T) GSON_WITH_ADAPTER.toJson(json);
    }
}
