package com.corpgovernment.resource.schedule.hotelaudit.openfeign;

import com.corpgovernment.client.CoreServiceClient;
import com.corpgovernment.client.ManagementClientUtil;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.dto.config.request.GetBookingConfigByTokenRequest;
import com.corpgovernment.dto.config.response.GetBookingConfigByTokenResponse;
import com.corpgovernment.dto.snapshot.SnapshotQtyCmd;
import com.corpgovernment.dto.snapshot.dto.QuerySnapshotResponseDTO;
import com.corpgovernment.dto.snapshot.dto.hotel.request.GetHotelProductSnapshotRequest;
import com.corpgovernment.dto.snapshot.dto.hotel.response.GetHotelProductSnapshotResponse;
import com.corpgovernment.dto.token.request.HotelOrderTravelStandardGetReqVo;
import com.corpgovernment.dto.token.response.HotelOrderTravelStandardGetRespVo;
import com.corpgovernment.dto.travelstandard.request.GetTravelStandardByTokenRequest;
import com.corpgovernment.dto.travelstandard.response.TravelStandardResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-23 21:25
 */
@Repository
@Slf4j
public class HotelAuditOpenfeignDao implements IHotelAuditOpenfeignDao {
    
    @Resource
    private CoreServiceClient coreServiceClient;
    
    @Resource
    private ManagementClientUtil managementClientUtil;
    
    @Override
    @BusinessBehaviorMonitor
    public JSONResult<QuerySnapshotResponseDTO> getSnapshot(SnapshotQtyCmd snapshotQtyCmd) {
        return coreServiceClient.getSnapshot(snapshotQtyCmd);
    }
    
    @Override
    @BusinessBehaviorMonitor
    public JSONResult<GetBookingConfigByTokenResponse> getBookingConfigByToken(GetBookingConfigByTokenRequest getBookingConfigByTokenRequest) {
        return coreServiceClient.getBookingConfigByToken(getBookingConfigByTokenRequest);
    }
    
    @Override
    @BusinessBehaviorMonitor
    public List<TravelStandardResponse> getTravelStandardByToken(GetTravelStandardByTokenRequest getTravelStandardByTokenRequest) {
        return managementClientUtil.getTravelStandardByToken(getTravelStandardByTokenRequest);
    }
    
    @Override
    @BusinessBehaviorMonitor
    public JSONResult<GetHotelProductSnapshotResponse> getHotelProductSnapshot(GetHotelProductSnapshotRequest getHotelProductSnapshotRequest) {
        return coreServiceClient.getHotelProductSnapshot(getHotelProductSnapshotRequest);
    }

    @Override
    @BusinessBehaviorMonitor
    public JSONResult<HotelOrderTravelStandardGetRespVo> getHotelOrderTravelStandard(HotelOrderTravelStandardGetReqVo request) {
        return coreServiceClient.getHotelOrderTravelStandard(request);
    }

}
