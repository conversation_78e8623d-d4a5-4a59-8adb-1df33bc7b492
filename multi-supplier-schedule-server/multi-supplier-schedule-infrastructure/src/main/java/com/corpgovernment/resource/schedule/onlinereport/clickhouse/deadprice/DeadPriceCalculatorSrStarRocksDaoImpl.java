package com.corpgovernment.resource.schedule.onlinereport.clickhouse.deadprice;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.base.AbstractCommonSrOnlineReportDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.deadprice.HotelDeadPriceBaseDataDTO;
import com.corpgovernment.resource.schedule.onlinereport.deadprice.HotelDeadPriceDataDTO;
import com.corpgovernment.resource.schedule.onlinereport.deadprice.HotelDeadPricePointDTO;
import com.corpgovernment.resource.schedule.onlinereport.deadprice.HotelDeadPriceSuggestOverviewDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.StarRocksTable;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.corpgovernment.resource.schedule.onlinereport.enums.StarRocksTable.ADM_HTL_BIZ_STD_ORDER_DATA;


/*
 * <AUTHOR>
 *
 * @date 2024/5/15 20:49
 *
 * @Desc 差标使用-酒店
 */
@Service
@Repository
public class DeadPriceCalculatorSrStarRocksDaoImpl extends AbstractCommonSrOnlineReportDao {
    private static final StarRocksTable RISK_AREA = StarRocksTable.ADM_CORP_RISK_AREA_DATA;

    private static final String DETAIL = "WITH biz_order AS (\n" +
            "        SELECT corp_corporation, corporation_name, corporation_flag, consumption_level, std_industry1\n" +
            "            , std_industry2, area_id, area_name, country_id, country_name, city_id, city_name\n" +
            "            , price, currency, exchangerate, price_rmb\n" +
            "            , sum(order_quantity) AS order_quantity\n" +
            "            , sum(order_amount) / sum(order_quantity) AS order_price\n" +
            "            , array_join(array_agg(rank_id_list), '$') as rank_id_list\n" +
            "            , array_join(array_agg(rank_name_list), '$') as rank_name_list\n" +
            "        FROM adm_htl_biz_std_order_data\n" +
            "        WHERE d = {partition}  {corpCondition}  {dataCondition}\n" +
            "            AND price > 0\n" +
            "            AND price_rmb <= 10000\n" +
            "        GROUP BY corp_corporation, corporation_name, corporation_flag, consumption_level, \n" +
            "            std_industry1, std_industry2, area_id, area_name, country_id, country_name, city_id, city_name, price, currency, exchangerate, price_rmb\n" +
            "    ), \n" +
            "    market_price AS (\n" +
            "        SELECT city_id, sum(market_amount) / sum(market_quantity) AS market_price\n" +
            "        FROM adm_htl_market_price_data\n" +
            "        WHERE d = {partition} {dataCondition}\n" +
            "        GROUP BY city_id\n" +
            "    ), \n" +
            "    biz_order_and_market_price AS (\n" +
            "        SELECT biz_order.*, market_price.market_price, max(market_price) OVER (PARTITION BY corp_corporation, area_id ) AS area_max_market_price\n" +
            "            , min(price_rmb) OVER (PARTITION BY corp_corporation, area_id ) AS area_min_price_rmb\n" +
            "        FROM biz_order\n" +
            "            LEFT JOIN market_price ON biz_order.city_id = market_price.city_id\n" +
            "    ), \n" +
            "    htl_min_price AS (\n" +
            "        SELECT DISTINCT city_id, masterhotelid AS coverage_htl, all_room_min_price AS coverage_htl_price\n" +
            "        FROM adm_htl_min_price_coverage_data\n" +
            "        WHERE d = {partition} {dataCondition}\n" +
            "    ), \n" +
            "    htl_coverage AS (\n" +
            "        SELECT city_id, price_rmb, market_price\n" +
            "            , sum(price_rmb_over_tag) / count(*) AS price_rmb_coverage\n" +
            "            , sum(market_price_over_tag) / count(*) AS market_price_coverage\n" +
            "        FROM (\n" +
            "            SELECT DISTINCT biz_order_and_market_price.city_id, price_rmb, market_price, coverage_htl, coverage_htl_price\n" +
            "                , CASE \n" +
            "                    WHEN price_rmb > coverage_htl_price THEN 1\n" +
            "                    ELSE 0\n" +
            "                END AS price_rmb_over_tag\n" +
            "                , CASE \n" +
            "                    WHEN market_price > coverage_htl_price THEN 1\n" +
            "                    ELSE 0\n" +
            "                END AS market_price_over_tag\n" +
            "            FROM biz_order_and_market_price\n" +
            "                LEFT JOIN htl_min_price ON biz_order_and_market_price.city_id = htl_min_price.city_id\n" +
            "            WHERE price_rmb > 0\n" +
            "                AND market_price > 0\n" +
            "        ) x \n" +
            "        GROUP BY city_id, price_rmb, market_price\n" +
            "    ), \n" +
            "    industry_level_price AS (\n" +
            "        SELECT city_id\n" +
            "            , sum(CASE \n" +
            "                WHEN {industryCondition} THEN sum_htl_std_price\n" +
            "                ELSE 0\n" +
            "            END) / sum(CASE \n" +
            "                WHEN {industryCondition} THEN sum_order_nums\n" +
            "                ELSE 0\n" +
            "            END) AS industry_avg_price\n" +
            "            , {bizAvgPrice} AS biz_avg_price\n" +
            "        FROM adm_corp_industry_htl_std_data\n" +
            "        where d = {partition}\n" +
            "        GROUP BY city_id\n" +
            "    )\n" +
            "SELECT a.*\n" +
            "    , CASE \n" +
            "            WHEN suggest_code IN (0, 1) THEN NULL\n" +
            "            WHEN exchangerate > 2 THEN CAST(a.market_price / (exchangerate * 5) AS int) * 5\n" +
            "            ELSE CAST(a.market_price / (exchangerate * 10) AS int) * 10\n" +
            "        END AS suggest_price\n" +
            "        , exchangerate * CASE \n" +
            "            WHEN suggest_code IN (0, 1) THEN NULL\n" +
            "            WHEN exchangerate > 2 THEN CAST(a.market_price / (exchangerate * 5) AS int) * 5\n" +
            "            ELSE CAST(a.market_price / (exchangerate * 10) AS int) * 10\n" +
            "        END AS suggest_price_rmb \n" +
            "    , industry_avg_price, biz_avg_price, a.order_price / a.price_rmb AS htl_std_use_rio, price_rmb_coverage\n" +
            "    , market_price_coverage\n" +
            "FROM (\n" +
            "    SELECT *\n" +
            "        , CASE \n" +
            "            WHEN market_price < area_max_market_price THEN 0\n" +
            "            WHEN price_rmb <= market_price - 20 THEN 3\n" +
            "            WHEN price_rmb > market_price - 20\n" +
            "                AND price_rmb <= market_price + 20\n" +
            "            THEN 1\n" +
            "            WHEN price_rmb = area_min_price_rmb THEN 2\n" +
            "            ELSE 1\n" +
            "        END AS suggest_code\n" +
            "    FROM biz_order_and_market_price\n" +
            ") a\n" +
            "    LEFT JOIN industry_level_price b ON a.city_id = b.city_id\n" +
            "    LEFT JOIN htl_coverage c\n" +
            "    ON a.city_id = c.city_id\n" +
            "        AND a.price_rmb = c.price_rmb\n" +
            "        AND a.market_price = c.market_price";

    private static final String TREND = "WITH biz_order_by_month AS (\n" +
            "    SELECT year_month, corp_corporation, area_id, city_id, price\n" +
            "        , currency, price_rmb\n" +
            "        , sum(order_amount) / sum(order_quantity) AS order_price_by_month\n" +
            "    FROM adm_htl_biz_std_order_data\n" +
            "    WHERE \n" +
            "        d = {partition} \n" +
            "        {corpCondition} {dataCondition} \n" +
            "        AND city_id = {cityId}\n" +
            "        AND price = {price}\n" +
            "        AND currency = {currency}\n" +
            "        AND price > 0\n" +
            "        AND price_rmb <= 10000\n" +
            "    GROUP BY year_month, corp_corporation, area_id, city_id, price, currency, price_rmb\n" +
            "), \n" +
            "market_price_by_month AS (\n" +
            "    SELECT city_id, year_month\n" +
            "        , sum(market_amount) / sum(market_quantity) AS market_price_by_month\n" +
            "    FROM adm_htl_market_price_data\n" +
            "    WHERE d = {partition} {dataCondition} \n" +
            "      AND city_id = {cityId}\n" +
            "    GROUP BY city_id, year_month\n" +
            ")\n" +
            "SELECT corp_corporation, area_id, a.city_id, price, currency\n" +
            "    , a.year_month, price_rmb\n" +
            "    , max(CASE \n" +
            "        WHEN a.year_month = b.year_month THEN order_price_by_month\n" +
            "        ELSE NULL\n" +
            "    END) AS order_price_by_month\n" +
            "    , max(market_price_by_month) AS market_price_by_month\n" +
            "FROM market_price_by_month a\n" +
            "    INNER JOIN biz_order_by_month b ON a.city_id = b.city_id\n" +
            "GROUP BY corp_corporation, area_id, a.city_id, price, currency, a.year_month, price_rmb";

    /**
     * 基础数据 查询 城市等级 城市 星级 酒店品牌 区域 筛选项
     *
     * @param searchScope
     * @param corpIds
     * @return
     * @throws Exception
     */
    public List<HotelDeadPriceBaseDataDTO> queryBaseDataCityInfo(int searchScope, List<String> corpIds,
                                                                 String searchKey, String partition) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        switch (searchScope) {
            case 0:
                sqlBuilder.append(" select  distinct area_id , area_name ");
                break;
            case 1:
                sqlBuilder.append(" select  distinct city_id, city_name ");
                break;
            case 2:
                sqlBuilder.append(" select distinct star ");
                break;
            case 3:
                sqlBuilder.append(" select brand_id, brand_name, sum(order_quantity) as quantity ");
                break;
            case 4:
                sqlBuilder.append(" select distinct concat(city_id, '$', zone_id) as zone_id, concat(city_name, '$', zone_name) as zone_name ");
                break;
            case 5:
                sqlBuilder.append(" select distinct country_id, country_name ");
                break;
            default:
                break;
        }

        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ADM_HTL_BIZ_STD_ORDER_DATA.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> paramList = new ArrayList<>();

        paramList.add(partition);
        // 公司
        sqlBuilder.append(BaseConditionPrebuilder.buildListConditionWtihAnd("corp_corporation", corpIds, paramList));
        switch (searchScope) {
            case 0:
                sqlBuilder.append(" order by area_id asc ");
                break;
            case 1:
                sqlBuilder.append(" order by city_id asc ");
                break;
            case 2:
                sqlBuilder.append(" order by star asc ");
                break;
            case 3:
                if (StringUtils.isNotEmpty(searchKey)) {
                    sqlBuilder.append(" and brand_name like ? ");
                    paramList.add("%" + searchKey + "%");
                }
                sqlBuilder.append(" group by brand_id, brand_name ");
                sqlBuilder.append(" order by sum(order_quantity) desc ");
                break;
            case 4:
                sqlBuilder.append(" and city_id in (14,3,12,28,4,7,451,32,223,17,559,251,1,30,206,477,2,278,10) and zone_id > 0 ");
                if (StringUtils.isNotEmpty(searchKey)) {
                    sqlBuilder.append(" and zone_name like ? ");
                    paramList.add("%" + searchKey + "%");
                }
                sqlBuilder.append(" order by zone_id asc ");
                break;
            case 5:
                sqlBuilder.append(" order by country_id asc ");
                break;
            default:
                break;
        }
        // 查询sr
        return commonSumList(HotelDeadPriceBaseDataDTO.class, sqlBuilder.toString(), paramList, "queryBaseDataCityInfo");
    }


    /**
     * 基础数据 查询 职级 筛选项
     * @param corpIds
     * @
     * @return
     * @throws Exception
     */
    public List<HotelDeadPriceBaseDataDTO> queryBaseDataRank(List<String> corpIds, String partition) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select rank_id_list AS rankIdList, rank_name_list AS rankNameList");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ADM_HTL_BIZ_STD_ORDER_DATA.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> paramList = new ArrayList<>();
        paramList.add(partition);
        // 公司
        sqlBuilder.append(BaseConditionPrebuilder.buildListConditionWtihAnd("corp_corporation", corpIds, paramList));
        // 查询clickhouse
        return commonSumList(HotelDeadPriceBaseDataDTO.class, sqlBuilder.toString(), paramList, "queryBaseDataRank");
    }

    /**
     * 酒店差标建议
     * @param corpIds
     * @param stars
     * @param brandIds
     * @param zoneIds
     * @param partition
     * @return
     * @throws Exception
     */
    public List<HotelDeadPriceSuggestOverviewDTO> queryHtlDeadPriceSuggestOverview(
            List<String> corpIds, List<Integer> stars, List<Integer> brandIds, List<Integer> zoneIds, String industryType,
            String partition) throws Exception {
        List<Object> paramList = new ArrayList<>();
        String innerDetailSql = buildInnerDetailSql(corpIds, stars, brandIds, zoneIds, industryType, partition,
                "", "", "", paramList);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select ");
        sqlBuilder.append(" area_id,");
        sqlBuilder.append(" area_name,");
        sqlBuilder.append(" group_concat(distinct suggest_code) as area_codes, ");
        sqlBuilder.append(" sum(order_quantity) as quantity, ");
        sqlBuilder.append(" sum(order_price) as sum_order_price, ");
        sqlBuilder.append(" sum(price * exchangerate) as sum_price ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append("( ");
        sqlBuilder.append(innerDetailSql);
        sqlBuilder.append(") as o ");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" suggest_code != 0 ");
        sqlBuilder.append(" group by area_id, area_name ");
        return commonSumList(HotelDeadPriceSuggestOverviewDTO.class, sqlBuilder.toString(), paramList, "queryHtlDeadPriceSuggestOverview");
    }

    private String buildInnerDetailSql(List<String> corpIds, List<Integer> stars, List<Integer> brandIds, List<Integer> zoneIds,
                                       String industryType, String partition,
                                       String consumptionLevel, String compareSameLevel, String compareCorpSameLevel,
                                       List<Object> paramList) {
        /*
        {partition} and {corpCondition} and {dataCondition}
        {partition} and {dataCondition}
        {partition} and {dataCondition}
        {industryCondition}
        {industryCondition}
        {partition}
        */
        paramList.add(partition);
        String corpCondition = BaseConditionPrebuilder.buildListConditionWtihAnd("corp_corporation", corpIds, paramList);
        String dataCondition = buildDataFilterCondition(stars, brandIds, zoneIds, paramList);
        String innerDetailSql = DETAIL.replace("{partition}", "?")
                .replace("{corpCondition}", corpCondition)
                .replace("{dataCondition}", dataCondition);
        paramList.add(partition);
        buildDataFilterCondition(stars, brandIds, zoneIds, paramList);
        paramList.add(partition);
        buildDataFilterCondition(stars, brandIds, zoneIds, paramList);
        String industryCondition = buildIndustryCondition(industryType, consumptionLevel, compareCorpSameLevel, paramList);
        innerDetailSql = innerDetailSql.replace("{industryCondition}", industryCondition);
        buildIndustryCondition(industryType, consumptionLevel, compareCorpSameLevel, paramList);

        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") &&
                StringUtils.isNotEmpty(consumptionLevel)) {
            innerDetailSql = innerDetailSql.replace("{bizAvgPrice}",
                    " sum(case when consumption_level =  ? then sum_htl_std_price else 0 end) / sum(case when consumption_level =  ? then sum_order_nums else 0 end) ");
            paramList.add(consumptionLevel);
            paramList.add(consumptionLevel);
        } else {
            innerDetailSql = innerDetailSql.replace("{bizAvgPrice}", " sum(sum_htl_std_price) / sum(sum_order_nums) ");
        }

        paramList.add(partition);
        return innerDetailSql;
    }

    /**
     * 行业为空，返回字符串"true"。默认走商旅的条件（biz_avg_price）
     *
     * @param industryType
     * @param consumptionLevel
     * @param compareSameLevel
     * @param paramList
     * @return
     */
    private String buildIndustryCondition(String industryType, String consumptionLevel, String compareSameLevel, List<Object> paramList) {
        StringBuilder conditionBuilder = new StringBuilder();
        if (StringUtils.isEmpty(industryType)) {
            return "true";
        }
        String[] industryTypes = StringUtils.splitByWholeSeparatorPreserveAllTokens(industryType, "#");
        if (industryTypes.length < 2 || (StringUtils.isEmpty(industryTypes[0]) && StringUtils.isEmpty(industryTypes[1]))) {
            return "true";
        }
        if (StringUtils.isNotEmpty(industryTypes[0])) {
            conditionBuilder.append(" std_industry1 = ? ");
            paramList.add(industryTypes[0]);
        }

        if (StringUtils.isNotEmpty(industryTypes[1])) {
            if (StringUtils.isNotEmpty(industryTypes[0])) {
                conditionBuilder.append("and");
            }
            conditionBuilder.append(" std_industry2 = ? ");
            paramList.add(industryTypes[1]);
        }

        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T")
                && StringUtils.isNotEmpty(consumptionLevel)) {
            conditionBuilder.append(" and consumption_level =  ? ");
            paramList.add(consumptionLevel);
        }

        return conditionBuilder.toString();
    }

    private String buildDataFilterCondition(List<Integer> stars, List<Integer> brandIds, List<Integer> zoneIds, List<Object> paramList) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(stars)) {
            builder.append(BaseConditionPrebuilder.buildCityIdsSql(stars, "star", paramList));
        }
        if (CollectionUtils.isNotEmpty(brandIds)) {
            builder.append(BaseConditionPrebuilder.buildCityIdsSql(brandIds, "brand_id", paramList));
        }
        if (CollectionUtils.isNotEmpty(zoneIds)) {
            builder.append(BaseConditionPrebuilder.buildCityIdsSql(zoneIds, "zone_id", paramList));
        }
        return builder.toString();
    }

    /**
     * 酒店差标建议
     * @param areaIds
     * @param cityIds
     * @param rankIds
     * @param corpIds
     * @param adviceCodes
     * @param stars
     * @param brandIds
     * @param zoneIds
     * @param partition
     * @return
     * @throws Exception
     */
    public List<HotelDeadPriceDataDTO> queryHtlRecommendData(List<Integer> areaIds, List<Integer> countryIds,
                                                             List<Integer> cityIds, List<Integer> rankIds,
                                                             List<String> corpIds, List<Integer> adviceCodes,
                                                             List<Integer> stars, List<Integer> brandIds,
                                                             List<Integer> zoneIds,
                                                             String industryType,
                                                             String partition,
                                                             String consumptionLevel, String compareSameLevel, String compareCorpSameLevel
    ) throws Exception {

        List<Object> paramList = new ArrayList<>();
        String innerDetailSql = buildInnerDetailSql(corpIds, stars, brandIds, zoneIds, industryType, partition,
                consumptionLevel, compareSameLevel, compareCorpSameLevel,
                paramList);

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select ");
        sqlBuilder.append(" area_id,");
        sqlBuilder.append(" area_name,");
        sqlBuilder.append(" country_id,");
        sqlBuilder.append(" country_name,");
        sqlBuilder.append(" city_id,");
        sqlBuilder.append(" city_name,");
        sqlBuilder.append(" rank_name_list as rankNameList,");
        sqlBuilder.append(" rank_id_list as rankIdList,");
        sqlBuilder.append(" price,");
        sqlBuilder.append(" currency,");
        sqlBuilder.append(" exchangerate,");
        sqlBuilder.append(" price_rmb,");
        sqlBuilder.append(" order_price as order_mean_price_180d,");
        sqlBuilder.append(" market_price as c_city_mean_price_180d,");
        sqlBuilder.append(" 0 as competitive_htl_mean_price_180d,");
        sqlBuilder.append(" order_quantity as quantity_180d,");
        sqlBuilder.append(" suggest_code,");
        sqlBuilder.append(" suggest_price,");
        sqlBuilder.append(" suggest_price_rmb,");

        // 差标可订酒店覆盖率
        sqlBuilder.append(" price_rmb_coverage,");
        // 市场价可订酒店覆盖率
        sqlBuilder.append(" market_price_coverage,");
        // 同行业客户平均差标
        sqlBuilder.append(" industry_avg_price,");
        // 携程商旅客户平均差标
        sqlBuilder.append(" biz_avg_price,");
        // 差标使用率
        sqlBuilder.append(" htl_std_use_rio");

        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append("(").append(innerDetailSql).append(") as o");
        sqlBuilder.append(OrpConstants.WHERE).append(" 1 = 1 ");

        // 地区
        sqlBuilder.append(BaseConditionPrebuilder.buildCityPreSql(mapToLong(areaIds), paramList, "area_id"));
        // 城市
        sqlBuilder.append(BaseConditionPrebuilder.buildCityPreSql(mapToLong(cityIds), paramList, "city_id"));
        // 国家
        sqlBuilder.append(BaseConditionPrebuilder.buildCityPreSql(mapToLong(countryIds), paramList, "country_id"));
        // 职级
        sqlBuilder.append(buildPreSqlRank(rankIds, paramList));
        // 调整建议筛选
        sqlBuilder.append(BaseConditionPrebuilder.buildCityPreSql(mapToLong(adviceCodes), paramList, "suggest_code"));
        return commonSumList(HotelDeadPriceDataDTO.class, sqlBuilder.toString(), paramList, "queryHtlRecommendData");
    }

    private String buildInnerTrendSql(List<String> corpIds, Integer cityId, BigDecimal price, String currency,
                                      List<Integer> stars, List<Integer> brandIds, List<Integer> zoneIds,
                                      String partition, List<Object> paramList) {
        paramList.add(partition);
        String corpCondition = BaseConditionPrebuilder.buildListConditionWtihAnd("corp_corporation", corpIds, paramList);
        String dataCondition = buildDataFilterCondition(stars, brandIds, zoneIds, paramList);

        String innerTrendSql = TREND.replace("{partition}", "?")
                .replace("{corpCondition}", corpCondition)
                .replace("{cityId}", "?")
                .replace("{price}", "?")
                .replace("{currency}", "?")
                .replace("{dataCondition}", dataCondition);
        paramList.add(cityId);
        paramList.add(price);
        paramList.add(currency);
        paramList.add(partition);
        buildDataFilterCondition(stars, brandIds, zoneIds, paramList);
        paramList.add(cityId);
        return innerTrendSql;
    }


    /**
     * 酒店差标建议(趋势图)
     * @param corpIds
     * @param cityId
     * @param price
     * @param currency
     * @param partition
     * @return
     * @throws Exception
     */
    public List<HotelDeadPricePointDTO> queryHtlRecommendTrendData(List<String> corpIds, Integer cityId, BigDecimal price, String currency,
                                                                   List<Integer> stars, List<Integer> brandIds, List<Integer> zoneIds,
                                                                   String partition) throws Exception {
        List<Object> paramList = new ArrayList<>();
        String innerTrendSql = buildInnerTrendSql(
                corpIds, cityId, price, currency, stars, brandIds, zoneIds, partition, paramList
        );

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select ");
        sqlBuilder.append(" year_month,");
        sqlBuilder.append(" market_price_by_month as c_city_mean_price,");
        sqlBuilder.append(" order_price_by_month as order_mean_price,");
        sqlBuilder.append(" 0 as competitive_htl_mean_price,");
        sqlBuilder.append(" price_rmb ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append("( ").append(innerTrendSql).append(" ) as o ");
        sqlBuilder.append(" order by year_month asc ");
        return commonSumList(HotelDeadPricePointDTO.class, sqlBuilder.toString(), paramList, "queryHtlRecommendTrendData");
    }

    protected String buildPreSqlRank(List<Integer> rankIds, List<Object> parmList) {
        if (CollectionUtils.isNotEmpty(rankIds)) {
            StringBuilder sqlBuffer = new StringBuilder();
            String arrayCondition = " and array_length(array_intersect(split(rank_id_list, '$'), [ ";
            sqlBuffer.append(arrayCondition);
            for (int i = 0; i < rankIds.size(); i++) {
                sqlBuffer.append(" ? ");
                if (i != rankIds.size() - 1) {
                    sqlBuffer.append(OrpConstants.COMMA);
                }
                parmList.add(rankIds.get(i));
            }
            sqlBuffer.append("])) > 0 ");
            return sqlBuffer.toString();
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }

    protected List<Long> mapToLong(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(Long::valueOf).collect(Collectors.toList());
    }

    /**
     * 查询风险地区信息
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> searchRiskArea(Class<T> clazz, String pt) throws Exception {
        List<Object> parmList = new ArrayList<>();
        parmList.add(pt);
        String sqlSb = String.format("select country_id, country_name, city_id, city_name, risk_type from %s where d=?",
                RISK_AREA.getTable());
        return commonSumList(clazz, sqlSb, parmList, "searchRiskArea");
    }

}
