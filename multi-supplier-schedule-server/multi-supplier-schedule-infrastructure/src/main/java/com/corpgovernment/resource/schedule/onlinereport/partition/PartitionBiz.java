package com.corpgovernment.resource.schedule.onlinereport.partition;


import com.corpgovernment.resource.schedule.onlinereport.common.StarRocksSwitchConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-01-04 15:42
 * @desc
 */
@Service
public class PartitionBiz {

    @Autowired
    private StarRocksSwitchConfig starRocksSwitchConfig;




    /**
     * 获取最新的数据更新时间
     *
     * @param module
     * @return
     * @throws Exception
     */
    public String getUpdateTimeInfo(String module) throws Exception {
/*        String partition = null;
        if (ConfigUtils.getModulePartitionSwitch(module)) {
            partition = biCorpDataService.getPartitionByModule(module);
        } else {
            partition = RedisCacheUtils.get(OrpConstants.READ_CLICKHOUSE_PARTITION);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        long timestamp = sdf.parse(partition).getTime();
        DateTime point = new DateTime(timestamp).plusDays(-1);
        return sdf.format(point.toDate());*/
        return null;
    }


}
