package com.corpgovernment.resource.schedule.onlinereport.deadprice;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2022/7/20 16:31
 * @Desc
 */
@Data
public class HotelDeadPriceDataDTO {

    @Column(name = "area_id")
    @Type(value = Types.INTEGER)
    private Integer areaId;

    @Column(name = "area_name")
    @Type(value = Types.VARCHAR)
    private String areaName;

    @Column(name = "city_id")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    @Column(name = "city_name")
    @Type(value = Types.VARCHAR)
    private String cityName;


    @Column(name = "rankIdList")
    @Type(value = Types.VARCHAR)
    private String rankIdList;

    @Column(name = "rankNameList")
    @Type(value = Types.VARCHAR)
    private String rankNameList;

    @Column(name = "price")
    @Type(value = Types.DECIMAL)
    private BigDecimal price;

    @Column(name = "exchangerate")
    @Type(value = Types.DECIMAL)
    private BigDecimal exchangeRate;

    @Column(name = "price_rmb")
    @Type(value = Types.DECIMAL)
    private BigDecimal rmbPrice;

    @Column(name = "quantity_180d")
    @Type(value = Types.BIGINT)
    private Long quantity180d;

    @Column(name = "currency")
    @Type(value = Types.VARCHAR)
    private String currency;

    @Column(name = "order_mean_price_180d")
    @Type(value = Types.DECIMAL)
    private BigDecimal orderMeanPrice180d;

    @Column(name = "c_city_mean_price_180d")
    @Type(value = Types.DECIMAL)
    private BigDecimal cityMeanPrice180d;

    @Column(name = "competitive_htl_mean_price_180d")
    @Type(value = Types.DECIMAL)
    private BigDecimal circleMeanPrice180d;

    @Column(name = "suggest_code")
    @Type(value = Types.INTEGER)
    private Integer suggestCode;

    @Column(name = "uuid")
    @Type(value = Types.BIGINT)
    private Long uuid;

    @Column(name = "suggest_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal suggestPrice;

    @Column(name = "suggest_price_rmb")
    @Type(value = Types.DECIMAL)
    private BigDecimal suggestPriceRMB;

    @Column(name = "price_rmb_coverage")
    @Type(value = Types.DECIMAL)
    private BigDecimal priceRmbCoverage;

    @Column(name = "market_price_coverage")
    @Type(value = Types.DECIMAL)
    private BigDecimal marketPriceCoverage;

    @Column(name = "industry_avg_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal industryAvgPrice;

    @Column(name = "biz_avg_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal bizAvgPrice;

    @Column(name = "htl_std_use_rio")
    @Type(value = Types.DECIMAL)
    private BigDecimal htlStdUseRio;

}
