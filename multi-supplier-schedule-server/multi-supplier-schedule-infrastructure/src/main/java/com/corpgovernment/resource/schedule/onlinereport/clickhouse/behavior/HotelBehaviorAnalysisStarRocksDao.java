package com.corpgovernment.resource.schedule.onlinereport.clickhouse.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-11-10 11:00
 * @desc
 */
@Repository
public class HotelBehaviorAnalysisStarRocksDao extends OnlineReportBehaviorAnalysisDao {

    public <T> List<T> behaviorAnaylsis(BaseQueryConditionDTO requestDto, Class<T> clazz, Map<String, String> extParams) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String sql = compareMc(requestDto, parmList);
        return commonList(clazz, sql, parmList);
    }

    /**
     * 对比三方协议、非三方协议
     *
     * @param requestDto
     * @param paramList
     * @return
     * @throws Exception
     */
    public String compareMc(BaseQueryConditionDTO requestDto, List<Object> paramList) {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        StringBuilder sqlBuilder = new StringBuilder();
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");

        sqlBuilder.append("SELECT firstday_of_month AS dim ");
        sqlBuilder.append(", sum(coalesce(real_pay_with_servicefee, 0)) AS totalAmount");
        sqlBuilder.append(", sum(if( producttype_all != '" + ta + "', coalesce(real_pay_with_servicefee,0), 0)) AS totalAmountM");
        sqlBuilder.append(", sum(if( producttype_all = '" + ta + "', coalesce(real_pay_with_servicefee,0), 0)) AS totalAmountC");
        sqlBuilder.append(", sum(if( producttype_all != '" + ta + "', coalesce(room_price,0), 0)) AS totalRoomPriceM");
        sqlBuilder.append(", sum(if( producttype_all = '" + ta + "', coalesce(room_price,0), 0)) AS totalRoomPriceC");
        sqlBuilder.append(", sum(if( producttype_all != '" + ta + "', coalesce(quantity,0), 0)) AS totalQuantityM");
        sqlBuilder.append(", sum(if( producttype_all = '" + ta + "', coalesce(quantity,0), 0)) AS totalQuantityC");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(" group by dim  order by dim asc");
        return sqlBuilder.toString();
    }

    /**
     * 随心订
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> mixPaymentAnaylsis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT firstday_of_month AS dim ");
        sqlBuilder.append(",SUM(coalesce(quantity, 0)) AS sumTotalQuantity");
        sqlBuilder.append(",SUM(coalesce(real_pay_with_servicefee, 0)) AS sumTotalAmount");
        sqlBuilder.append(",SUM(if(is_mix_payment = 'T',coalesce(quantity, 0), 0)) AS totalQuantity");
        sqlBuilder.append(",SUM(if(is_mix_payment = 'T',coalesce(settlement_accnt_amt, 0), 0)) AS settlementAccntamt");
        sqlBuilder.append(",SUM(if(is_mix_payment = 'T',coalesce(settlement_person_amt, 0), 0)) AS settlementPersonamt");
        sqlBuilder.append(",SUM(if(is_mix_payment = 'T',coalesce(real_pay_with_servicefee, 0), 0)) AS totalAmount");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, getDateFieldByCaliber(requestDto.getStatisticalCaliber())));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(" group by dim ");
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }
}
