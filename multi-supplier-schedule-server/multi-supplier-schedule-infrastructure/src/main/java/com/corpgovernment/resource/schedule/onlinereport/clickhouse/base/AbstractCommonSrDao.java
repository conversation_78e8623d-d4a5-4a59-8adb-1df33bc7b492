package com.corpgovernment.resource.schedule.onlinereport.clickhouse.base;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.logging.log4j.LogManager;

import java.sql.PreparedStatement;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-21 13:39
 * @desc
 */
@Slf4j
public abstract class AbstractCommonSrDao extends AbstractStarRocksBaseDao {

    private static final String LOG_TITLE = "AbstractCommonSrDao";


    private final static String COUNT_ALIAS = "countAll";

    /**
     * @param clazz
     * @param sql
     * @param paramList
     * @param <T>
     * @return
     * @throws Exception
     */
    protected <T> List<T> commonList(Class<T> clazz, String sql, List<Object> paramList) throws Exception {
        return querySrBySql(sql, paramList, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return DbResultMapUtils.mapResultList(u, d);
            } catch (Exception e) {
                log.error("AbstractCommonSrDao commonList", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, "commonList");
    }

    protected PreparedStatement mapCommonRequest(List<Object> parmList, PreparedStatement statement) {
        int index = OrpConstants.ONE;
        try {
            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }
}
