package com.corpgovernment.resource.schedule.onlinereport.clickhouse.base;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.ExpressionVisitorAdapter;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.LikeExpression;
import net.sf.jsqlparser.schema.Column;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * SQL条件访问器。目前只处理 = 和 LIKE 表达式<br>
 * <br>
 * 例如该SQL: SELECT * FROM users WHERE (age = 25 AND (uid = '李云' OR uidname LIKE '%李云%'))<br>
 * 我们可以通过该访问器，递归遍历 where 子句中的所有条件，找到所有的 = 和 LIKE 表达式，然后对字段值进行加密处理<br>
 *
 * <br>
 * 解析流程如下：<br>
 * <pre>
 * 最外层：expression = Parenthesis = (age = 25 AND (uid = '李云' OR uidname LIKE '%李云%'))
 * 子层：expression = BinaryExpression = age = 25 AND (uid = '李云' OR uidname LIKE '%李云%')
 *  - leftExpression = BinaryExpression = age = 25
 *   - leftExpression = Column = age
 *   - rightExpression = LongValue = 25
 *  - rightExpression = Parenthesis = (uid = '李云' OR uidname LIKE '%李云%')
 *   - expression = BinaryExpression/OrExpression = uid = '李云' OR uidname LIKE '%李云%'
 *    - leftExpression = BinaryExpression = uid = '李云'
 *     - leftExpression = Column = uid
 *     - rightExpression = StringValue = '李云'
 *   - rightExpression = BinaryExpression = uidname LIKE '%李云%'
 *    - leftExpression = Column = uidname
 *    - rightExpression = StringValue = '%李云%'
 *
 * <AUTHOR> Smith
 */
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class ConditionVisitor extends ExpressionVisitorAdapter {

    /**
     * 表名
     */
    private String tableNameInSql;


    /**
     * = 表达式：uid = '李云'
     * @param equalsTo = 表达式
     */
    @Override
    public void visit(EqualsTo equalsTo) {
        // 获取字段名
        Expression leftExpression = equalsTo.getLeftExpression();
        // 获取字段值
        Expression rightExpression = equalsTo.getRightExpression();

        if (!(leftExpression instanceof Column)) {
            log.info("Only support Column, skip encrypting query condition");
            return;
        }

        if (!(rightExpression instanceof StringValue)) {
            log.info("Only support StringValue, skip encrypting query condition");
            return;
        }


        encrypted((Column) leftExpression, (StringValue) rightExpression)
                // 替换条件值
                .ifPresent(e -> equalsTo.setRightExpression(new StringValue(e)));


        super.visit(equalsTo);
    }


    /**
     * like表达式：uidname LIKE '%李云%'
     * @param likeExpression like表达式
     */
    @Override
    public void visit(LikeExpression likeExpression) {
        // 获取字段名
        Expression leftExpression = likeExpression.getLeftExpression();
        // 获取字段值
        Expression rightExpression = likeExpression.getRightExpression();

        if (!(leftExpression instanceof Column)) {
            log.info("Only support Column, skip encrypting query condition");
            return;
        }

        if (!(rightExpression instanceof StringValue)) {
            log.info("Only support StringValue, skip encrypting query condition");
            return;
        }


        encrypted((Column) leftExpression, (StringValue) rightExpression)
                // 替换条件值
                .ifPresent(e -> likeExpression.setRightExpression(new StringValue(e)));

        super.visit(likeExpression);
    }

    /**
     * in表达式：uid IN ('李云', '张三')
     */
    @Override
    public void visit(InExpression inExpression) {
        // 获取字段名
        Expression leftExpression = inExpression.getLeftExpression();

        if (!(leftExpression instanceof Column)) {
            log.info("Only support Column, skip encrypting query condition");
            return;
        }

        if (!(inExpression.getRightItemsList() instanceof ExpressionList)) {
            log.info("Only support ExpressionList, skip encrypting query condition");
            return;
        }

        // 获取字段名字符串
        String columnName = getColumString((Column) leftExpression);

        // 如果该字段不需要加密，直接跳过
        if (noEncrypt(columnName)) {
            return;
        }
        // 获取右侧的ItemsList
        ExpressionList expressionList = (ExpressionList) inExpression.getRightItemsList();

        // 提取每个表达式的字符串值，然后加密
        List<Expression> valueStrings = expressionList.getExpressions()
                .stream()
                // 过滤掉不是StringValue的表达式
                .filter(StringValue.class::isInstance)
                .map(Expression::toString)
                // 去除前后的单引号
                .map(s -> s.substring(1, s.length() - 1))
                // 加密每个字段值
                .map(TableInfo::encryptColumnValue)
                .map(StringValue::new)
                .collect(Collectors.toList());

        // 替换条件值
        inExpression.setRightItemsList(new ExpressionList(valueStrings));

        super.visit(inExpression);
    }


    private Optional<String> encrypted(Column column, StringValue stringValue) {
        String columnName = getColumString(column);

        // 获取SQL语句中需要加密的字段值（此处理论上只会处理字符串类型的字段值）
        String columnValue = stringValue.getValue();

        if (noEncrypt(columnName)) {
            return Optional.empty();
        }

        // 加密字段值
        String pattern = extractLikePattern(columnValue);
        String valueToEncrypt = removeLikeWildcards(columnValue);

        return Optional.ofNullable(applyLikePattern(TableInfo.encryptColumnValue(valueToEncrypt), pattern, columnValue));
    }


    /**
     * 判断该字段是否需要被加密
     * @param columnName 字段名
     * @return true：不需要加密；false：需要加密
     */

    private boolean noEncrypt(String columnName) {
        // 获取所有需要被CKSqlParser处理的表信息
        Map<String, TableInfo> tableInfoMap = TableInfo.tableInfoMap();

        // 获取需要处理的表信息
        TableInfo tableInfo = tableInfoMap.get(tableNameInSql);
        // 如果表信息为空，则说明该表不需要加密，直接跳过
        if (tableInfo == null) {
            return true;
        }

        // 从配置中获取需要加密的字段名称
        List<String> encryptFields = tableInfo.getEncryptFields();

        // 如果SQL语句中的字段名不在需要加密的字段中，直接跳过
        return !encryptFields.contains(columnName);
    }

    /**
     * 获取当前SQL语句中的字段名
     */
    private String getColumString(Column column) {
        // 去除可能 `field` 这种带反引号的字段名
        return Optional.ofNullable(column.getColumnName())
                // 去除可能 `field` 这种带反引号的字段名
                .map(m -> m.startsWith("`") ? m.substring(1, m.length() - 1) : m)
                .orElse("");
    }


    /**
     * 提取 LIKE 表达式的通配符
     * @param likePattern LIKE 表达式
     * @return 通配符
     */
    private String extractLikePattern(String likePattern) {
        StringBuilder pattern = new StringBuilder();
        if (likePattern.startsWith("%")) {
            pattern.append("%");
        }
        if (likePattern.endsWith("%")) {
            pattern.append("%");
        }
        return pattern.toString();
    }

    /**
     * 移除 LIKE 表达式的通配符
     * @param likePattern LIKE 表达式
     * @return 处理后的值
     */
    private String removeLikeWildcards(String likePattern) {
        // 移除开头和结尾的 % 通配符
        String trimmed = likePattern.replaceAll("^%|%$", "");

        // 处理转义的通配符和其他特殊字符
        // 移除非转义的 %
        return trimmed.replaceAll("(?<!\\\\)%", "")
                // 移除非转义的 _
                .replaceAll("(?<!\\\\)_", "")
                // 还原转义的 %
                .replace("\\%", "%")
                // 还原转义的 _
                .replace("\\_", "_")
                // 还原双反斜杠
                .replace("\\\\", "\\");
    }


    /**
     * 还原 LIKE 表达式的通配符
     * @param value 要应用通配符的值
     * @param pattern 通配符
     * @param originalValue 原始值
     * @return 处理后的值
     */
    private String applyLikePattern(String value, String pattern, String originalValue) {
        if ("%".equals(pattern)) {
            // 如果模式只是一个 '%'，我们需要判断它是在开头还是结尾
            if (originalValue.startsWith("%")) {
                return "%" + value;
            } else if (originalValue.endsWith("%")) {
                return value + "%";
            }
        } else {
            // 处理可能同时有开头和结尾 '%' 的情况
            if (pattern.startsWith("%")) {
                value = "%" + value;
            }
            if (pattern.endsWith("%")) {
                value += "%";
            }
        }
        return value;
    }

}
