package com.corpgovernment.resource.schedule.onlinereport.module.save;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class PotentialSaveDTO {

    @Column(name = "report_date")
    @Type(value = Types.VARCHAR)
    private String reportDate;

    @Column(name = "overAmount")
    @Type(value = Types.DOUBLE)
    private Double overAmount;

    @Column(name = "rcTimes")
    @Type(value = Types.INTEGER)
    private Integer rcTimes;

    @Column(name = "refundloss")
    @Type(value = Types.DOUBLE)
    private Double refundloss;

    @Column(name = "refundtkt")
    @Type(value = Types.INTEGER)
    private Integer refundtkt;

    @Column(name = "rebookloss")
    @Type(value = Types.DOUBLE)
    private Double rebookloss;

    @Column(name = "rebooktkt")
    @Type(value = Types.INTEGER)
    private Integer rebooktkt;
}
