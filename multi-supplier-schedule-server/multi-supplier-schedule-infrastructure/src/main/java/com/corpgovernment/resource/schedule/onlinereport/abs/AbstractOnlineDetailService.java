package com.corpgovernment.resource.schedule.onlinereport.abs;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryYoyTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportDataDetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReprotBodyData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReprotTrainBodyData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TrainDataDetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TrainDataInfoDetailData;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportFlightDto;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportHotelDto;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportTrainDto;
import com.corpgovernment.resource.schedule.onlinereport.detail.OverviewQueryDto;
import com.corpgovernment.resource.schedule.onlinereport.dto.ReportCurrentYoyMomMapping;
import com.corpgovernment.resource.schedule.onlinereport.enums.DetailReportTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.OnlineDetailFieldEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.OnlineReportBuEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.ReportDetailHeaderEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.AtomicDouble;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.service.factory
 * @description:
 * @author: md_wang
 * @create: 2021-11-08 12:34
 **/
@Slf4j
public abstract class AbstractOnlineDetailService {

    /**
     * 返回各个产线header
     *
     * @param reportTypeEnum
     * @param buTypeEnum
     * @return
     */
    protected Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> mapperHeaderByBu(DetailReportTypeEnum reportTypeEnum,
                                                                                  QueryReportBuTypeEnum buTypeEnum, String lang) {
        Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> returnMap = Maps.newHashMap();
        switch (buTypeEnum) {
            case overview:
                List<OnlineDetailFieldEnum> list = ReportDetailHeaderEnum.mapOverviewHeader();
                returnMap.put(ReportDetailHeaderEnum.OVERVIEW_AMOUNT_HEADER,
                        list.stream().map(t -> new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang)))
                                .collect(Collectors.toList()));
                returnMap.put(ReportDetailHeaderEnum.OVERVIEW_TICKET_HEADER,
                        list.stream().map(t -> new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang)))
                                .collect(Collectors.toList()));
                break;
            case flight:
                Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> flightMap =
                        ReportDetailHeaderEnum.getHeaderByType(reportTypeEnum, OnlineReportBuEnum.FLIGHT);
                returnMap = flightMap.entrySet().stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap((entity) -> entity.getKey(), (entity) -> entity.getValue().stream()
                                .map(t -> getFlightHeaderKeyValMap(entity, t, lang)).collect(Collectors.toList())));
                break;
            case hotel:
                Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> hotelMap =
                        ReportDetailHeaderEnum.getHeaderByType(reportTypeEnum, OnlineReportBuEnum.HOTEL);
                returnMap = hotelMap.entrySet().stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                                .map(v -> getHotelHeaderKeyValMap(entry, v, lang)).collect(Collectors.toList())));
                break;
            case train:
                Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> map =
                        ReportDetailHeaderEnum.getHeaderByType(reportTypeEnum, OnlineReportBuEnum.TRAIN);
                returnMap = map.entrySet().stream().filter(Objects::nonNull).collect(
                        Collectors.toMap((entity) -> entity.getKey(), (entity) -> entity.getValue().stream().map(t -> {
                            HeaderKeyValMap headerKeyValMap = null;
                            if (entity.getKey().equals(ReportDetailHeaderEnum.TRAIN_ALL_HEADER)
                                    && t.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                        SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                                "ReportDetailHeaderEnum.trainOverallConsumerDetails"), lang));
                            } else if (entity.getKey().equals(ReportDetailHeaderEnum.TRAIN_ALL_HEADER)
                                    && t.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                        SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                                "ReportDetailHeaderEnum.trainServiceFee"), lang));
                            } else if (entity.getKey().equals(ReportDetailHeaderEnum.TRAIN_ALL_HEADER)
                                    && t.getHeaderKey().equals(OnlineDetailFieldEnum.INSURANCE_FEE.getHeaderKey())) {
                                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                        SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                                "ReportDetailHeaderEnum.trainInsuranceFee"), lang));
                            } else if (entity.getKey().equals(ReportDetailHeaderEnum.TRAIN_ALL_HEADER)
                                    && t.getHeaderKey().equals(OnlineDetailFieldEnum.SEND_TICKET_FEE.getHeaderKey())) {
                                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                        SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                                "ReportDetailHeaderEnum.trainDeliverFee"), lang));
                            } else {
                                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
                            }
                            return headerKeyValMap;
                        }).collect(Collectors.toList())));
                break;
            case car:
                Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> carMap =
                        ReportDetailHeaderEnum.getHeaderByType(reportTypeEnum, OnlineReportBuEnum.CAR);
                returnMap = carMap.entrySet().stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                                .map(v -> getCarHeaderKeyValMap(entry, v, lang)).collect(Collectors.toList())));
                break;
            default:
                break;
        }
        return returnMap;
    }

    /**
     * 返回各个产线header(日本站)
     *
     * @param reportTypeEnum
     * @param buTypeEnum
     * @return
     */
    protected Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> mapperJPHeaderByBu(DetailReportTypeEnum reportTypeEnum,
                                                                                    QueryReportBuTypeEnum buTypeEnum, String lang, boolean isBlueSpace) {
        Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> returnMap = Maps.newHashMap();
        switch (buTypeEnum) {
            case overview:
                List<OnlineDetailFieldEnum> list = ReportDetailHeaderEnum.mapJPOverviewHeader();
                returnMap.put(ReportDetailHeaderEnum.OVERVIEW_AMOUNT_HEADER,
                        list.stream().map(t -> new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang)))
                                .collect(Collectors.toList()));
                returnMap.put(ReportDetailHeaderEnum.OVERVIEW_TICKET_HEADER,
                        list.stream().map(t -> new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang)))
                                .collect(Collectors.toList()));
                break;
            case flight:
                Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> flightMap =
                        ReportDetailHeaderEnum.getJPHeaderByType(reportTypeEnum, OnlineReportBuEnum.FLIGHT);
                returnMap = flightMap.entrySet().stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap((entity) -> entity.getKey(), (entity) -> entity.getValue().stream()
                                .map(t -> isBlueSpace ? getFlightHeaderKeyValMap(entity, t, lang) : getJPFlightHeaderKeyValMap(entity, t, lang))
                                .collect(Collectors.toList())));
                break;
            case hotel:
                Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> hotelMap =
                        ReportDetailHeaderEnum.getJPHeaderByType(reportTypeEnum, OnlineReportBuEnum.HOTEL);
                returnMap = hotelMap.entrySet().stream().filter(Objects::nonNull)
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                                .map(v -> isBlueSpace ? getBlueSpaceHotelHeaderKeyValMap(entry, v, lang) : getJPHotelHeaderKeyValMap(entry, v, lang))
                                .collect(Collectors.toList())));
                break;
            case train:
                Map<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> map =
                        ReportDetailHeaderEnum.getHeaderByType(reportTypeEnum, OnlineReportBuEnum.TRAIN);
                returnMap = map.entrySet().stream().filter(Objects::nonNull).collect(
                        Collectors.toMap((entity) -> entity.getKey(), (entity) -> entity.getValue().stream().map(t -> {
                            HeaderKeyValMap headerKeyValMap = null;
                            if (entity.getKey().equals(ReportDetailHeaderEnum.TRAIN_ALL_HEADER)
                                    && t.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                        SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                                "ReportDetailHeaderEnum.trainOverallConsumerDetails"), lang));
                            } else if (entity.getKey().equals(ReportDetailHeaderEnum.TRAIN_ALL_HEADER)
                                    && t.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                        SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                                "ReportDetailHeaderEnum.trainServiceFee"), lang));
                            } else if (entity.getKey().equals(ReportDetailHeaderEnum.TRAIN_ALL_HEADER)
                                    && t.getHeaderKey().equals(OnlineDetailFieldEnum.INSURANCE_FEE.getHeaderKey())) {
                                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                        SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                                "ReportDetailHeaderEnum.trainInsuranceFee"), lang));
                            } else if (entity.getKey().equals(ReportDetailHeaderEnum.TRAIN_ALL_HEADER)
                                    && t.getHeaderKey().equals(OnlineDetailFieldEnum.SEND_TICKET_FEE.getHeaderKey())) {
                                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                        SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                                "ReportDetailHeaderEnum.trainDeliverFee"), lang));
                            } else {
                                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
                            }
                            return headerKeyValMap;
                        }).collect(Collectors.toList())));
                break;
            default:
                break;
        }
        return returnMap;
    }

    /**
     * hotel-header
     *
     * @param entry
     * @param v
     * @param lang
     * @return
     */
    private HeaderKeyValMap getBlueSpaceHotelHeaderKeyValMap(
            Map.Entry<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> entry, OnlineDetailFieldEnum v, String lang) {
        HeaderKeyValMap headerKeyValMap = null;
        switch (entry.getKey()) {
            case HOTEL_ALL_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.hotelOverallConsumerDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                            .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelServiceFee"), lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case HOTEL_AGREEMENT_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.agreementHotelConsumptionDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                            .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelServiceFee"), lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case HOTEL_NOT_AGREEMENT_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.nonAgreementHotelConsumptionDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                            .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelServiceFee"), lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case HOTEL_INFO_AMOUNT_HEADER:
            case HOTEL_INFO_TICKET_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.UN_AGREEMENT_V.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal("Index.HotelNonAgreement", lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.AGREEMENT_V.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal("Index.HotelAgreement", lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            default:
                headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
        }
        return headerKeyValMap;
    }

    /**
     * hotel-header
     *
     * @param entry
     * @param v
     * @param lang
     * @return
     */
    private HeaderKeyValMap getHotelHeaderKeyValMap(
            Map.Entry<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> entry, OnlineDetailFieldEnum v, String lang) {
        HeaderKeyValMap headerKeyValMap = null;
        switch (entry.getKey()) {
            case HOTEL_ALL_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.hotelOverallConsumerDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                            .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelServiceFee"), lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case HOTEL_AGREEMENT_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.agreementHotelConsumptionDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                            .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelServiceFee"), lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case HOTEL_NOT_AGREEMENT_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.nonAgreementHotelConsumptionDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                            .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelServiceFee"), lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case HOTEL_DOMESTIC_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.domesticHotelConsumptionDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                            .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelServiceFee"), lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case HOTEL_INTERNATIONAL_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.internationalHotelConsumptionDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                            .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelServiceFee"), lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            default:
                headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
        }
        return headerKeyValMap;
    }

    /**
     * hotel-header,日本站
     *
     * @param entry
     * @param v
     * @param lang
     * @return
     */
    private HeaderKeyValMap getJPHotelHeaderKeyValMap(
            Map.Entry<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> entry, OnlineDetailFieldEnum v, String lang) {
        HeaderKeyValMap headerKeyValMap = null;
        switch (entry.getKey()) {
            case HOTEL_ALL_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.hotelOverallConsumerDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP5", lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.HOTEL_POST_SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP6", lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case HOTEL_AGREEMENT_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.agreementHotelConsumptionDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP5", lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.HOTEL_POST_SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP6", lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case HOTEL_NOT_AGREEMENT_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.DIMENSION.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                    "ReportDetailHeaderEnum.nonAgreementHotelConsumptionDetails"), lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP5", lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.HOTEL_POST_SERVICE_FEE.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP6", lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case HOTEL_INFO_AMOUNT_HEADER:
            case HOTEL_INFO_TICKET_HEADER:
                if (v.getHeaderKey().equals(OnlineDetailFieldEnum.UN_AGREEMENT_V.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal("Index.HotelNonAgreement", lang));
                } else if (v.getHeaderKey().equals(OnlineDetailFieldEnum.AGREEMENT_V.getHeaderKey())) {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                            SharkUtils.getHeaderVal("Index.HotelAgreement", lang));
                } else {
                    headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            default:
                headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
        }
        return headerKeyValMap;
    }

    /**
     * 用车-header
     */
    private HeaderKeyValMap getCarHeaderKeyValMap(Map.Entry<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> entry,
                                                  OnlineDetailFieldEnum v, String lang) {
        HeaderKeyValMap headerKeyValMap = null;
        switch (entry.getKey()) {
            case CAR_ALL_HEADER:
                switch (v) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.carConsumptionDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.carServiceFee"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case CAR_TAKE_TAXI_HEADER:
                switch (v) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal("index.domtaxidetail", lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.carServiceFee"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case CAR_TAKE_TAXI_HEADER_INTER:
                switch (v) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal("index.inttaxidetail", lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.carServiceFee"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case CAR_AIRPORT_PICK_UP_HEADER:
                switch (v) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal("index.domcarservicedetail", lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.carServiceFee"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case CAR_AIRPORT_PICK_UP_HEADER_INTER:
                switch (v) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal("index.intcarservicedetail", lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.carServiceFee"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case CAR_RENTAL_CAR_HEADER:
                switch (v) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.rentalCarConsumptionDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.carServiceFee"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            case CAR_CHARTERED_CAR_HEADER:
                switch (v) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.charterCarConsumptionDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.carServiceFee"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
                }
                break;
            default:
                headerKeyValMap = new HeaderKeyValMap(v.getHeaderKey(), SharkUtils.getHeaderVal(v.getName(), lang));
        }
        return headerKeyValMap;
    }

    /**
     * flight-header
     */
    private HeaderKeyValMap getFlightHeaderKeyValMap(
            Map.Entry<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> entity, OnlineDetailFieldEnum t, String lang) {
        HeaderKeyValMap headerKeyValMap = null;
        switch (entity.getKey()) {
            case FLIGHT_ALL_HEADER:
                switch (t) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightOverallConsumerDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightServiceFee"), lang));
                        break;
                    case INSURANCE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightInsurance"), lang));
                        break;
                    case SEND_TICKET_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightSendTicketFee"), lang));
                        break;
                    case REBOOK_PRICE_DIFFERENTIAL:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightRebookPriceDifferential"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
                }
                break;
            case FLIGHT_AGREEMENT_HEADER:
                switch (t) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.agreementFlightConsumptionDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightServiceFee"), lang));
                        break;
                    case INSURANCE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightInsurance"), lang));
                        break;
                    case SEND_TICKET_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightSendTicketFee"), lang));
                        break;
                    case REBOOK_PRICE_DIFFERENTIAL:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightRebookPriceDifferential"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
                }
                break;
            case FLIGHT_NOT_AGREEMENT_HEADER:
                switch (t) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.nonAgreementFlightConsumptionDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightServiceFee"), lang));
                        break;
                    case INSURANCE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightInsurance"), lang));
                        break;
                    case SEND_TICKET_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightSendTicketFee"), lang));
                        break;
                    case REBOOK_PRICE_DIFFERENTIAL:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightRebookPriceDifferential"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
                }
                break;
            case FLIGHT_DOMESTIC_HEADER:
                switch (t) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.domesticFlightConsumptionDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightServiceFee"), lang));
                        break;
                    case INSURANCE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightInsurance"), lang));
                        break;
                    case SEND_TICKET_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightSendTicketFee"), lang));
                        break;
                    case REBOOK_PRICE_DIFFERENTIAL:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightRebookPriceDifferential"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
                }
                break;
            case FLIGHT_INTERNATIONAL_HEADER:
                switch (t) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.internationalFlightConsumptionDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightServiceFee"), lang));
                        break;
                    case INSURANCE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightInsurance"), lang));
                        break;
                    case SEND_TICKET_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightSendTicketFee"), lang));
                        break;
                    case REBOOK_PRICE_DIFFERENTIAL:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightRebookPriceDifferential"), lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
                }
                break;
            default:
                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
        }
        return headerKeyValMap;
    }

    /**
     * flight-header 日本站
     */
    private HeaderKeyValMap getJPFlightHeaderKeyValMap(
            Map.Entry<ReportDetailHeaderEnum, List<OnlineDetailFieldEnum>> entity, OnlineDetailFieldEnum t, String lang) {
        HeaderKeyValMap headerKeyValMap = null;
        switch (entity.getKey()) {
            case FLIGHT_ALL_HEADER:
                switch (t) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightOverallConsumerDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightServiceFee"), lang));
                        break;
                    case INSURANCE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightInsurance"), lang));
                        break;
                    case SEND_TICKET_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightSendTicketFee"), lang));
                        break;
                    case REBOOK_PRICE_DIFFERENTIAL:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightRebookPriceDifferential"), lang));
                        break;
                    case REBOOK_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeforJP2", lang));
                        break;
                    case REFUND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeforJP3", lang));
                        break;
                    case TICKET_BEHIND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP6", lang));
                        break;
                    case REBOOK_BEHIND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP7", lang));
                        break;
                    case REFUND_BEHIND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP8", lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
                }
                break;
            case FLIGHT_AGREEMENT_HEADER:
                switch (t) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.agreementFlightConsumptionDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightServiceFee"), lang));
                        break;
                    case INSURANCE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightInsurance"), lang));
                        break;
                    case SEND_TICKET_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightSendTicketFee"), lang));
                        break;
                    case REBOOK_PRICE_DIFFERENTIAL:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightRebookPriceDifferential"), lang));
                        break;
                    case REBOOK_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeforJP2", lang));
                        break;
                    case REFUND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeforJP3", lang));
                        break;
                    case TICKET_BEHIND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP6", lang));
                        break;
                    case REBOOK_BEHIND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP7", lang));
                        break;
                    case REFUND_BEHIND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP8", lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
                }
                break;
            case FLIGHT_NOT_AGREEMENT_HEADER:
                switch (t) {
                    case DIMENSION:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.nonAgreementFlightConsumptionDetails"), lang));
                        break;
                    case SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightServiceFee"), lang));
                        break;
                    case INSURANCE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(OrpCommonUtils
                                .getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightInsurance"), lang));
                        break;
                    case SEND_TICKET_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightSendTicketFee"), lang));
                        break;
                    case REBOOK_PRICE_DIFFERENTIAL:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(),
                                SharkUtils.getHeaderVal(OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE,
                                        "ReportDetailHeaderEnum.flightRebookPriceDifferential"), lang));
                        break;
                    case REBOOK_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeforJP2", lang));
                        break;
                    case REFUND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeforJP3", lang));
                        break;
                    case TICKET_BEHIND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP6", lang));
                        break;
                    case REBOOK_BEHIND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP7", lang));
                        break;
                    case REFUND_BEHIND_SERVICE_FEE:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal("ServiceFeeForJP8", lang));
                        break;
                    default:
                        headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
                }
                break;
            default:
                headerKeyValMap = new HeaderKeyValMap(t.getHeaderKey(), SharkUtils.getHeaderVal(t.getName(), lang));
        }
        return headerKeyValMap;
    }

    /**
     * 映射-当期/同比/环比-查询时间
     *
     * @param request
     * @return
     */
    protected OnlineDetailRequestDto mapOnlineDetailRequestDtoOld(OnlineReportDetailRequest request) {
        OnlineDetailRequestDto requestDto = OnlineDetailRequestDto.newBuild();
        String queryStartTime = request.basecondition.getStartTime();
        String queryEndTime = request.basecondition.getEndTime();
        // 当期-查询时间
        requestDto.setStartTime(queryStartTime);
        requestDto.setEndTime(queryEndTime);
        String yoyStartTime = StringUtils.EMPTY;
        String yoyEndTime = StringUtils.EMPTY;
        // 空时-默认yoyType 时 去年
        QueryYoyTypeEnum yoyType = Optional.ofNullable(request.getYoyType()).orElse(QueryYoyTypeEnum.yoy);
        switch (yoyType) {
            case yoy:
                // 同比-去年-查询时间
                List<String> yoyTimes = OrpDateTimeUtils.yoyTime(queryStartTime, queryEndTime, OrpConstants.ONE_YEAR_DAYS);
                yoyStartTime = yoyTimes.get(0);
                yoyEndTime = yoyTimes.get(1);
                // yoyStartTime = OrpDateTimeUtils.startTimeYoy(queryStartTime);
                // yoyEndTime = OrpDateTimeUtils.endTimeYoy(queryEndTime);
                break;
            case yoy_pre:
                // 同比-前年-查询时间
                List<String> yoyBeforeTimes = OrpDateTimeUtils.yoyTime(queryStartTime, queryEndTime, OrpConstants.TWO_YEAR_DAYS);
                yoyStartTime = yoyBeforeTimes.get(0);
                yoyEndTime = yoyBeforeTimes.get(1);
                // yoyStartTime = OrpDateTimeUtils.startTimeYoyBefore(queryStartTime);
                // yoyEndTime = OrpDateTimeUtils.endTimeYoyBefore(queryEndTime);
            default:
        }
        // 同比-查询时间
        requestDto.setYoyStartTime(yoyStartTime);
        requestDto.setYoyEndTime(yoyEndTime);
        // 环比-查询时间
        List<String> momTimes = OrpDateTimeUtils.momTime(queryStartTime, queryEndTime);
        requestDto.setMomStartTime(momTimes.get(0));
        requestDto.setMomEndTime(momTimes.get(1));
        // requestDto.setMomStartTime(OrpDateTimeUtils.searchTimeMom(queryStartTime, queryEndTime, OrpConstants.ZERO));
        // requestDto.setMomEndTime(OrpDateTimeUtils.searchTimeMom(queryStartTime, queryEndTime, OrpConstants.ONE));
        requestDto.setBaseCondition(request.basecondition);
        requestDto.setLang(request.getLang());
        return requestDto;
    }

    /**
     * 映射-当期/同比/环比-查询时间
     *
     * @param request
     * @return
     */
    protected OnlineDetailRequestDto mapOnlineDetailRequestDto(OnlineReportDetailRequest request) {
        OnlineDetailRequestDto requestDto = OnlineDetailRequestDto.newBuild();
        String queryStartTime = request.basecondition.getStartTime();
        String queryEndTime = request.basecondition.getEndTime();
        // 当期-查询时间
        requestDto.setStartTime(queryStartTime);
        requestDto.setEndTime(queryEndTime);
        String yoyStartTime = StringUtils.EMPTY;
        String yoyEndTime = StringUtils.EMPTY;
        // 空时-默认yoyType 时 去年
        QueryYoyTypeEnum yoyType = Optional.ofNullable(request.getYoyType()).orElse(QueryYoyTypeEnum.yoy);
        switch (yoyType) {
            case yoy:
                // 同比-去年-查询时间
                List<String> yoyTimes = OrpDateTimeUtils.yoyTime(queryStartTime, queryEndTime, OrpConstants.ONE_YEAR_DAYS);
                yoyStartTime = yoyTimes.get(0);
                yoyEndTime = yoyTimes.get(1);
                break;
            case yoy_pre:
                // 同比-前年-查询时间
                List<String> yoyBeforeTimes = OrpDateTimeUtils.yoyTime(queryStartTime, queryEndTime, OrpConstants.TWO_YEAR_DAYS);
                yoyStartTime = yoyBeforeTimes.get(0);
                yoyEndTime = yoyBeforeTimes.get(1);
            default:
        }
        requestDto.setDataStartTime(
                OrpDateTimeUtils.dayAddToStr(
                        OrpDateTimeUtils.dateTimeStrToDate(yoyStartTime, OrpDateTimeUtils.DEFAULT_DATE),
                        OrpConstants.MINUS_TWENTY
                )
        );
        // 同比-查询时间
        requestDto.setYoyStartTime(yoyStartTime);
        requestDto.setYoyEndTime(yoyEndTime);
        // 环比-查询时间
        List<String> momTimes = OrpDateTimeUtils.momTime(queryStartTime, queryEndTime);
        requestDto.setMomStartTime(momTimes.get(0));
        requestDto.setMomEndTime(momTimes.get(1));
        requestDto.setBaseCondition(request.basecondition);
        requestDto.setLang(request.getLang());
        requestDto.setExtData(request.getExtData());
        return requestDto;
    }

    /**
     * 根据月 group
     */
    public String groupByMonth(String reportDate) {
        return OrpDateTimeUtils.formatDateTimeToStr(
                OrpDateTimeUtils.dateTimeStrToDate(reportDate, OrpDateTimeUtils.DEFAULT_DATE),
                OrpDateTimeUtils.DEFAULT_MONTH);
    }

    /**
     * 概览-金额/票张-明细
     *
     * @param mapping
     * @param currentOverViewListMap
     * @param yoyOverViewListMap
     * @param momOverViewListMap
     * @return
     */
    protected List<ReprotBodyData> mapOverviewAmountTicketData(DetailReportTypeEnum typeEnum,
                                                               Map<String, ReportCurrentYoyMomMapping> mapping, Map<String, List<OverviewQueryDto>> currentOverViewListMap,
                                                               Map<String, List<OverviewQueryDto>> yoyOverViewListMap,
                                                               Map<String, List<OverviewQueryDto>> momOverViewListMap, boolean isJP) {
        List<ReprotBodyData> reportBodyDataList = Lists.newArrayList();
        // 迭代映射关系值
        mapping.forEach((k, v) -> {
            AtomicDouble flightV = new AtomicDouble();
            AtomicDouble flightYoy = new AtomicDouble();
            AtomicDouble flightMom = new AtomicDouble();

            AtomicDouble hotelV = new AtomicDouble();
            AtomicDouble hotelYoy = new AtomicDouble();
            AtomicDouble hotelMom = new AtomicDouble();

            AtomicDouble trainV = new AtomicDouble();
            AtomicDouble trainYoy = new AtomicDouble();
            AtomicDouble trainMom = new AtomicDouble();

            AtomicDouble carV = new AtomicDouble();
            AtomicDouble carYoy = new AtomicDouble();
            AtomicDouble carMom = new AtomicDouble();

            AtomicDouble busV = new AtomicDouble();
            AtomicDouble busYoy = new AtomicDouble();
            AtomicDouble busMom = new AtomicDouble();

            AtomicDouble addV = new AtomicDouble();
            AtomicDouble addYoy = new AtomicDouble();
            AtomicDouble addMom = new AtomicDouble();

            AtomicDouble totalV = new AtomicDouble();
            AtomicDouble totalYoy = new AtomicDouble();
            AtomicDouble totalMom = new AtomicDouble();
            // 当期
            List<OverviewQueryDto> currentReportData = currentOverViewListMap.get(k);
            Optional.ofNullable(currentReportData).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        flightV.set(OrpCommonUtils.addDouble(new BigDecimal(flightV.get()), t.getAmountFlt()));
                        hotelV.set(OrpCommonUtils.addDouble(new BigDecimal(hotelV.get()), t.getAmountHtl()));
                        trainV.set(OrpCommonUtils.addDouble(new BigDecimal(trainV.get()), t.getAmountTrain()));
                        carV.set(OrpCommonUtils.addDouble(new BigDecimal(carV.get()), t.getAmountCar()));
                        busV.set(OrpCommonUtils.addDouble(new BigDecimal(busV.get()), t.getAmountBus()));
                        addV.set(OrpCommonUtils.addDouble(new BigDecimal(addV.get()), t.getAmountAdd()));
                        totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), t.getAmountTotal()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        flightV.set(OrpCommonUtils.addDouble(new BigDecimal(flightV.get()),
                                new BigDecimal(t.getQuantityFlt())));
                        hotelV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(hotelV.get()), new BigDecimal(t.getQuantityHtl())));
                        trainV.set(OrpCommonUtils.addDouble(new BigDecimal(trainV.get()),
                                new BigDecimal(t.getQuantityTrain())));
                        carV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(carV.get()), new BigDecimal(t.getQuantityCar())));
                        busV.set(OrpCommonUtils.addDouble(new BigDecimal(busV.get()), new BigDecimal(t.getQuantityBus())));
                        addV.set(OrpCommonUtils.addDouble(new BigDecimal(addV.get()), new BigDecimal(t.getQuantityAdd())));
                        totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()),
                                new BigDecimal(t.getQuantityTotal())));
                        break;
                    default:
                }
            });

            // 同比
            List<OverviewQueryDto> yoyReportData = yoyOverViewListMap.get(v.getYoyDate());
            Optional.ofNullable(yoyReportData).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        flightYoy.set(OrpCommonUtils.addDouble(new BigDecimal(flightYoy.get()), t.getAmountFltYoy()));
                        hotelYoy.set(OrpCommonUtils.addDouble(new BigDecimal(hotelYoy.get()), t.getAmountHtlYoy()));
                        trainYoy.set(OrpCommonUtils.addDouble(new BigDecimal(trainYoy.get()), t.getAmountTrainYoy()));
                        carYoy.set(OrpCommonUtils.addDouble(new BigDecimal(carYoy.get()), t.getAmountCarYoy()));
                        busYoy.set(OrpCommonUtils.addDouble(new BigDecimal(busYoy.get()), t.getAmountBusYoy()));
                        addYoy.set(OrpCommonUtils.addDouble(new BigDecimal(addYoy.get()), t.getAmountAddYoy()));
                        totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), t.getAmountTotalYoy()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        flightYoy.set(OrpCommonUtils.addDouble(new BigDecimal(flightYoy.get()),
                                new BigDecimal(t.getQuantityFltYoy())));
                        hotelYoy.set(OrpCommonUtils.addDouble(new BigDecimal(hotelYoy.get()),
                                new BigDecimal(t.getQuantityHtlYoy())));
                        trainYoy.set(OrpCommonUtils.addDouble(new BigDecimal(trainYoy.get()),
                                new BigDecimal(t.getQuantityTrainYoy())));
                        carYoy.set(OrpCommonUtils.addDouble(new BigDecimal(carYoy.get()),
                                new BigDecimal(t.getQuantityCarYoy())));
                        busYoy.set(OrpCommonUtils.addDouble(new BigDecimal(busYoy.get()),
                                new BigDecimal(t.getQuantityBusYoy())));
                        addYoy.set(OrpCommonUtils.addDouble(new BigDecimal(addYoy.get()),
                                new BigDecimal(t.getQuantityAddYoy())));
                        totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()),
                                new BigDecimal(t.getQuantityTotalYoy())));
                        break;
                    default:
                }
            });

            // 环比
            String momDate = OrpDateTimeUtils.momDate(k);
            List<OverviewQueryDto> momReportData = currentOverViewListMap.get(momDate);
            if (CollectionUtils.isEmpty(momReportData)) {
                momReportData = momOverViewListMap.get(momDate);
                Optional.ofNullable(momReportData).orElse(Lists.newArrayList()).forEach(t -> {
                    switch (typeEnum) {
                        case DETAIL_AMOUNT_REPORT:
                            flightMom
                                    .set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()), t.getAmountFltMom()));
                            hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()), t.getAmountHtlMom()));
                            trainMom
                                    .set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()), t.getAmountTrainMom()));
                            carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()), t.getAmountCarMom()));
                            busMom.set(OrpCommonUtils.addDouble(new BigDecimal(busMom.get()), t.getAmountBusMom()));
                            addMom.set(OrpCommonUtils.addDouble(new BigDecimal(addMom.get()), t.getAmountAddMom()));
                            totalMom
                                    .set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getAmountTotalMom()));
                            break;
                        case DETAIL_TICKET_REPORT:
                            flightMom.set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()),
                                    new BigDecimal(t.getQuantityFltMom())));
                            hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()),
                                    new BigDecimal(t.getQuantityHtlMom())));
                            trainMom.set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()),
                                    new BigDecimal(t.getQuantityTrainMom())));
                            carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()),
                                    new BigDecimal(t.getQuantityCarMom())));
                            busMom.set(OrpCommonUtils.addDouble(new BigDecimal(busMom.get()),
                                    new BigDecimal(t.getQuantityBusMom())));
                            addMom.set(OrpCommonUtils.addDouble(new BigDecimal(addMom.get()),
                                    new BigDecimal(t.getQuantityAddMom())));
                            totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()),
                                    new BigDecimal(t.getQuantityTotalMom())));
                            break;
                        default:
                    }
                });
            } else {
                Optional.ofNullable(momReportData).orElse(Lists.newArrayList()).forEach(t -> {
                    switch (typeEnum) {
                        case DETAIL_AMOUNT_REPORT:
                            flightMom.set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()), t.getAmountFlt()));
                            hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()), t.getAmountHtl()));
                            trainMom.set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()), t.getAmountTrain()));
                            carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()), t.getAmountCar()));
                            busMom.set(OrpCommonUtils.addDouble(new BigDecimal(busMom.get()), t.getAmountBus()));
                            addMom.set(OrpCommonUtils.addDouble(new BigDecimal(addMom.get()), t.getAmountAdd()));
                            totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getAmountTotal()));
                            break;
                        case DETAIL_TICKET_REPORT:
                            flightMom.set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()),
                                    new BigDecimal(t.getQuantityFlt())));
                            hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()),
                                    new BigDecimal(t.getQuantityHtl())));
                            trainMom.set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()),
                                    new BigDecimal(t.getQuantityTrain())));
                            carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()),
                                    new BigDecimal(t.getQuantityCar())));
                            busMom.set(OrpCommonUtils.addDouble(new BigDecimal(busMom.get()), new BigDecimal(t.getQuantityBus())));
                            addMom.set(OrpCommonUtils.addDouble(new BigDecimal(addMom.get()), new BigDecimal(t.getQuantityAdd())));
                            totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()),
                                    new BigDecimal(t.getQuantityTotal())));
                            break;
                        default:
                    }
                });
            }
            int precision = typeEnum == DetailReportTypeEnum.DETAIL_AMOUNT_REPORT ? (isJP ? OrpConstants.ZERO : OrpConstants.TWO) : OrpConstants.ZERO;

            ReportDataDetailData reportDetailData = new ReportDataDetailData();
            reportDetailData.setFlightV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(flightV.get()), precision).toString());
            reportDetailData.setFlightYoy(OrpCommonUtils.yoy(flightV.get(), flightYoy.get()));
            reportDetailData.setFlightMom(OrpCommonUtils.mom(flightV.get(), flightMom.get()));

            reportDetailData.setHotelV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(hotelV.get()), precision).toString());
            reportDetailData.setHotelYoy(OrpCommonUtils.yoy(hotelV.get(), hotelYoy.get()));
            reportDetailData.setHotelMom(OrpCommonUtils.mom(hotelV.get(), hotelMom.get()));

            reportDetailData.setTrainV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(trainV.get()), precision).toString());
            reportDetailData.setTrainYoy(OrpCommonUtils.yoy(trainV.get(), trainYoy.get()));
            reportDetailData.setTrainMom(OrpCommonUtils.mom(trainV.get(), trainMom.get()));

            reportDetailData
                    .setCarV(OrpCommonUtils.precisionConversion(new BigDecimal(carV.get()), precision).toString());
            reportDetailData.setCarYoy(OrpCommonUtils.yoy(carV.get(), carYoy.get()));
            reportDetailData.setCarMom(OrpCommonUtils.mom(carV.get(), carMom.get()));

            reportDetailData.setBusV(OrpCommonUtils.precisionConversion(new BigDecimal(busV.get()), precision).toString());
            reportDetailData.setBusYoy(OrpCommonUtils.yoy(busV.get(), busYoy.get()));
            reportDetailData.setBusMom(OrpCommonUtils.mom(busV.get(), busMom.get()));

            reportDetailData.setAddV(OrpCommonUtils.precisionConversion(new BigDecimal(addV.get()), precision).toString());
            reportDetailData.setAddYoy(OrpCommonUtils.yoy(addV.get(), addYoy.get()));
            reportDetailData.setAddMom(OrpCommonUtils.mom(addV.get(), addMom.get()));

            reportDetailData.setTotalV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());
            reportDetailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
            reportDetailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));

            reportBodyDataList.add(new ReprotBodyData(k, reportDetailData));
        });
        return reportBodyDataList;
    }

    /**
     * 概览-金额/票张-明细
     *
     * @param typeEnum
     * @param unionData
     * @param isJP
     * @return
     */
    protected List<ReprotBodyData> mapOverviewAmountTicketData(DetailReportTypeEnum typeEnum,
                                                               Map<String, CurrentYoyMomData<OverviewQueryDto>> unionData,
                                                               boolean isJP) {
        List<ReprotBodyData> reportBodyDataList = Lists.newArrayList();

        // 迭代映射关系值
        unionData.forEach((k, v) -> {
            AtomicDouble flightV = new AtomicDouble();
            AtomicDouble flightYoy = new AtomicDouble();
            AtomicDouble flightMom = new AtomicDouble();

            AtomicDouble hotelV = new AtomicDouble();
            AtomicDouble hotelYoy = new AtomicDouble();
            AtomicDouble hotelMom = new AtomicDouble();

            AtomicDouble trainV = new AtomicDouble();
            AtomicDouble trainYoy = new AtomicDouble();
            AtomicDouble trainMom = new AtomicDouble();

            AtomicDouble carV = new AtomicDouble();
            AtomicDouble carYoy = new AtomicDouble();
            AtomicDouble carMom = new AtomicDouble();

            AtomicDouble busV = new AtomicDouble();
            AtomicDouble busYoy = new AtomicDouble();
            AtomicDouble busMom = new AtomicDouble();

            AtomicDouble addV = new AtomicDouble();
            AtomicDouble addYoy = new AtomicDouble();
            AtomicDouble addMom = new AtomicDouble();

            AtomicDouble totalV = new AtomicDouble();
            AtomicDouble totalYoy = new AtomicDouble();
            AtomicDouble totalMom = new AtomicDouble();
            // 当期
            List<OverviewQueryDto> currentReportData = v.getCurrent();
            Optional.ofNullable(currentReportData).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        flightV.set(OrpCommonUtils.addDouble(new BigDecimal(flightV.get()), t.getAmountFlt()));
                        hotelV.set(OrpCommonUtils.addDouble(new BigDecimal(hotelV.get()), t.getAmountHtl()));
                        trainV.set(OrpCommonUtils.addDouble(new BigDecimal(trainV.get()), t.getAmountTrain()));
                        carV.set(OrpCommonUtils.addDouble(new BigDecimal(carV.get()), t.getAmountCar()));
                        busV.set(OrpCommonUtils.addDouble(new BigDecimal(busV.get()), t.getAmountBus()));
                        addV.set(OrpCommonUtils.addDouble(new BigDecimal(addV.get()), t.getAmountAdd()));
                        totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), t.getAmountTotal()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        flightV.set(OrpCommonUtils.addDouble(new BigDecimal(flightV.get()),
                                new BigDecimal(t.getQuantityFlt())));
                        hotelV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(hotelV.get()), new BigDecimal(t.getQuantityHtl())));
                        trainV.set(OrpCommonUtils.addDouble(new BigDecimal(trainV.get()),
                                new BigDecimal(t.getQuantityTrain())));
                        carV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(carV.get()), new BigDecimal(t.getQuantityCar())));
                        busV.set(OrpCommonUtils.addDouble(new BigDecimal(busV.get()), new BigDecimal(t.getQuantityBus())));
                        addV.set(OrpCommonUtils.addDouble(new BigDecimal(addV.get()), new BigDecimal(t.getQuantityAdd())));
                        totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()),
                                new BigDecimal(t.getQuantityTotal())));
                        break;
                    default:
                }
            });

            // 同比
            List<OverviewQueryDto> yoyReportData = v.getYoy();
            Optional.ofNullable(yoyReportData).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        flightYoy.set(OrpCommonUtils.addDouble(new BigDecimal(flightYoy.get()), t.getAmountFlt()));
                        hotelYoy.set(OrpCommonUtils.addDouble(new BigDecimal(hotelYoy.get()), t.getAmountHtl()));
                        trainYoy.set(OrpCommonUtils.addDouble(new BigDecimal(trainYoy.get()), t.getAmountTrain()));
                        carYoy.set(OrpCommonUtils.addDouble(new BigDecimal(carYoy.get()), t.getAmountCar()));
                        busYoy.set(OrpCommonUtils.addDouble(new BigDecimal(busYoy.get()), t.getAmountBus()));
                        addYoy.set(OrpCommonUtils.addDouble(new BigDecimal(addYoy.get()), t.getAmountAdd()));
                        totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), t.getAmountTotal()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        flightYoy.set(OrpCommonUtils.addDouble(new BigDecimal(flightYoy.get()),
                                new BigDecimal(t.getQuantityFlt())));
                        hotelYoy.set(OrpCommonUtils.addDouble(new BigDecimal(hotelYoy.get()),
                                new BigDecimal(t.getQuantityHtl())));
                        trainYoy.set(OrpCommonUtils.addDouble(new BigDecimal(trainYoy.get()),
                                new BigDecimal(t.getQuantityTrain())));
                        carYoy.set(OrpCommonUtils.addDouble(new BigDecimal(carYoy.get()),
                                new BigDecimal(t.getQuantityCar())));
                        busYoy.set(OrpCommonUtils.addDouble(new BigDecimal(busYoy.get()),
                                new BigDecimal(t.getQuantityBus())));
                        addYoy.set(OrpCommonUtils.addDouble(new BigDecimal(addYoy.get()),
                                new BigDecimal(t.getQuantityAdd())));
                        totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()),
                                new BigDecimal(t.getQuantityTotal())));
                        break;
                    default:
                }
            });

            // 环比
            List<OverviewQueryDto> momReportData = v.getMom();
            Optional.ofNullable(momReportData).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        flightMom.set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()), t.getAmountFlt()));
                        hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()), t.getAmountHtl()));
                        trainMom.set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()), t.getAmountTrain()));
                        carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()), t.getAmountCar()));
                        busMom.set(OrpCommonUtils.addDouble(new BigDecimal(busMom.get()), t.getAmountBus()));
                        addMom.set(OrpCommonUtils.addDouble(new BigDecimal(addMom.get()), t.getAmountAdd()));
                        totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getAmountTotal()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        flightMom.set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()),
                                new BigDecimal(t.getQuantityFlt())));
                        hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()),
                                new BigDecimal(t.getQuantityHtl())));
                        trainMom.set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()),
                                new BigDecimal(t.getQuantityTrain())));
                        carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()),
                                new BigDecimal(t.getQuantityCar())));
                        busMom.set(OrpCommonUtils.addDouble(new BigDecimal(busMom.get()), new BigDecimal(t.getQuantityBus())));
                        addMom.set(OrpCommonUtils.addDouble(new BigDecimal(addMom.get()), new BigDecimal(t.getQuantityAdd())));
                        totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()),
                                new BigDecimal(t.getQuantityTotal())));
                        break;
                    default:
                }
            });

            int precision = typeEnum == DetailReportTypeEnum.DETAIL_AMOUNT_REPORT ? (isJP ? OrpConstants.ZERO : OrpConstants.TWO) : OrpConstants.ZERO;

            ReportDataDetailData reportDetailData = new ReportDataDetailData();
            reportDetailData.setFlightV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(flightV.get()), precision).toString());
            reportDetailData.setFlightYoy(OrpCommonUtils.yoy(flightV.get(), flightYoy.get()));
            reportDetailData.setFlightMom(OrpCommonUtils.mom(flightV.get(), flightMom.get()));

            reportDetailData.setHotelV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(hotelV.get()), precision).toString());
            reportDetailData.setHotelYoy(OrpCommonUtils.yoy(hotelV.get(), hotelYoy.get()));
            reportDetailData.setHotelMom(OrpCommonUtils.mom(hotelV.get(), hotelMom.get()));

            reportDetailData.setTrainV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(trainV.get()), precision).toString());
            reportDetailData.setTrainYoy(OrpCommonUtils.yoy(trainV.get(), trainYoy.get()));
            reportDetailData.setTrainMom(OrpCommonUtils.mom(trainV.get(), trainMom.get()));

            reportDetailData
                    .setCarV(OrpCommonUtils.precisionConversion(new BigDecimal(carV.get()), precision).toString());
            reportDetailData.setCarYoy(OrpCommonUtils.yoy(carV.get(), carYoy.get()));
            reportDetailData.setCarMom(OrpCommonUtils.mom(carV.get(), carMom.get()));

            reportDetailData.setBusV(OrpCommonUtils.precisionConversion(new BigDecimal(busV.get()), precision).toString());
            reportDetailData.setBusYoy(OrpCommonUtils.yoy(busV.get(), busYoy.get()));
            reportDetailData.setBusMom(OrpCommonUtils.mom(busV.get(), busMom.get()));

            reportDetailData.setAddV(OrpCommonUtils.precisionConversion(new BigDecimal(addV.get()), precision).toString());
            reportDetailData.setAddYoy(OrpCommonUtils.yoy(addV.get(), addYoy.get()));
            reportDetailData.setAddMom(OrpCommonUtils.mom(addV.get(), addMom.get()));

            reportDetailData.setTotalV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());
            reportDetailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
            reportDetailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));

            reportBodyDataList.add(new ReprotBodyData(k, reportDetailData));
        });
        return reportBodyDataList;
    }

    /**
     * 概览-汇总数据-金额/票张
     *
     * @param totalTitle
     * @param typeEnum
     * @param totalCurrentYoyMomData
     * @param isJP
     * @return
     */
    protected ReprotBodyData mapOverviewTotalAmountTicketData(String totalTitle, DetailReportTypeEnum typeEnum,
                                                              CurrentYoyMomData<OverviewQueryDto> totalCurrentYoyMomData,
                                                              boolean isJP) {
        AtomicDouble flightV = new AtomicDouble();
        AtomicDouble flightYoy = new AtomicDouble();
        AtomicDouble flightMom = new AtomicDouble();

        AtomicDouble hotelV = new AtomicDouble();
        AtomicDouble hotelYoy = new AtomicDouble();
        AtomicDouble hotelMom = new AtomicDouble();

        AtomicDouble trainV = new AtomicDouble();
        AtomicDouble trainYoy = new AtomicDouble();
        AtomicDouble trainMom = new AtomicDouble();

        AtomicDouble carV = new AtomicDouble();
        AtomicDouble carYoy = new AtomicDouble();
        AtomicDouble carMom = new AtomicDouble();

        AtomicDouble busV = new AtomicDouble();
        AtomicDouble busYoy = new AtomicDouble();
        AtomicDouble busMom = new AtomicDouble();

        AtomicDouble addV = new AtomicDouble();
        AtomicDouble addYoy = new AtomicDouble();
        AtomicDouble addMom = new AtomicDouble();

        AtomicDouble totalV = new AtomicDouble();
        AtomicDouble totalYoy = new AtomicDouble();
        AtomicDouble totalMom = new AtomicDouble();

        // 当期 同比 环比
        // 当期
        List<OverviewQueryDto> currentReportData = totalCurrentYoyMomData.getCurrent();
        Optional.ofNullable(currentReportData).orElse(Lists.newArrayList()).forEach(t -> {
            switch (typeEnum) {
                case DETAIL_AMOUNT_REPORT:
                    flightV.set(OrpCommonUtils.addDouble(new BigDecimal(flightV.get()), t.getAmountFlt()));
                    hotelV.set(OrpCommonUtils.addDouble(new BigDecimal(hotelV.get()), t.getAmountHtl()));
                    trainV.set(OrpCommonUtils.addDouble(new BigDecimal(trainV.get()), t.getAmountTrain()));
                    carV.set(OrpCommonUtils.addDouble(new BigDecimal(carV.get()), t.getAmountCar()));
                    busV.set(OrpCommonUtils.addDouble(new BigDecimal(busV.get()), t.getAmountBus()));
                    addV.set(OrpCommonUtils.addDouble(new BigDecimal(addV.get()), t.getAmountAdd()));
                    totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), t.getAmountTotal()));
                    break;
                case DETAIL_TICKET_REPORT:
                    flightV.set(OrpCommonUtils.addDouble(new BigDecimal(flightV.get()),
                            new BigDecimal(t.getQuantityFlt())));
                    hotelV.set(
                            OrpCommonUtils.addDouble(new BigDecimal(hotelV.get()), new BigDecimal(t.getQuantityHtl())));
                    trainV.set(OrpCommonUtils.addDouble(new BigDecimal(trainV.get()),
                            new BigDecimal(t.getQuantityTrain())));
                    carV.set(
                            OrpCommonUtils.addDouble(new BigDecimal(carV.get()), new BigDecimal(t.getQuantityCar())));
                    busV.set(OrpCommonUtils.addDouble(new BigDecimal(busV.get()), new BigDecimal(t.getQuantityBus())));
                    addV.set(OrpCommonUtils.addDouble(new BigDecimal(addV.get()), new BigDecimal(t.getQuantityAdd())));
                    totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()),
                            new BigDecimal(t.getQuantityTotal())));
                    break;
                default:
            }
        });

        // 同比
        List<OverviewQueryDto> yoyReportData = totalCurrentYoyMomData.getYoy();
        Optional.ofNullable(yoyReportData).orElse(Lists.newArrayList()).forEach(t -> {
            switch (typeEnum) {
                case DETAIL_AMOUNT_REPORT:
                    flightYoy.set(OrpCommonUtils.addDouble(new BigDecimal(flightYoy.get()), t.getAmountFlt()));
                    hotelYoy.set(OrpCommonUtils.addDouble(new BigDecimal(hotelYoy.get()), t.getAmountHtl()));
                    trainYoy.set(OrpCommonUtils.addDouble(new BigDecimal(trainYoy.get()), t.getAmountTrain()));
                    carYoy.set(OrpCommonUtils.addDouble(new BigDecimal(carYoy.get()), t.getAmountCar()));
                    busYoy.set(OrpCommonUtils.addDouble(new BigDecimal(busYoy.get()), t.getAmountBus()));
                    addYoy.set(OrpCommonUtils.addDouble(new BigDecimal(addYoy.get()), t.getAmountAdd()));
                    totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), t.getAmountTotal()));
                    break;
                case DETAIL_TICKET_REPORT:
                    flightYoy.set(OrpCommonUtils.addDouble(new BigDecimal(flightYoy.get()),
                            new BigDecimal(t.getQuantityFlt())));
                    hotelYoy.set(OrpCommonUtils.addDouble(new BigDecimal(hotelYoy.get()),
                            new BigDecimal(t.getQuantityHtl())));
                    trainYoy.set(OrpCommonUtils.addDouble(new BigDecimal(trainYoy.get()),
                            new BigDecimal(t.getQuantityTrain())));
                    carYoy.set(OrpCommonUtils.addDouble(new BigDecimal(carYoy.get()),
                            new BigDecimal(t.getQuantityCar())));
                    busYoy.set(OrpCommonUtils.addDouble(new BigDecimal(busYoy.get()),
                            new BigDecimal(t.getQuantityBus())));
                    addYoy.set(OrpCommonUtils.addDouble(new BigDecimal(addYoy.get()),
                            new BigDecimal(t.getQuantityAdd())));
                    totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()),
                            new BigDecimal(t.getQuantityTotal())));
                    break;
                default:
            }
        });

        // 环比
        List<OverviewQueryDto> momReportData = totalCurrentYoyMomData.getMom();
        Optional.ofNullable(momReportData).orElse(Lists.newArrayList()).forEach(t -> {
            switch (typeEnum) {
                case DETAIL_AMOUNT_REPORT:
                    flightMom.set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()), t.getAmountFlt()));
                    hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()), t.getAmountHtl()));
                    trainMom.set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()), t.getAmountTrain()));
                    carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()), t.getAmountCar()));
                    busMom.set(OrpCommonUtils.addDouble(new BigDecimal(busMom.get()), t.getAmountBus()));
                    addMom.set(OrpCommonUtils.addDouble(new BigDecimal(addMom.get()), t.getAmountAdd()));
                    totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getAmountTotal()));
                    break;
                case DETAIL_TICKET_REPORT:
                    flightMom.set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()),
                            new BigDecimal(t.getQuantityFlt())));
                    hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()),
                            new BigDecimal(t.getQuantityHtl())));
                    trainMom.set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()),
                            new BigDecimal(t.getQuantityTrain())));
                    carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()),
                            new BigDecimal(t.getQuantityCar())));
                    busMom.set(OrpCommonUtils.addDouble(new BigDecimal(busMom.get()), new BigDecimal(t.getQuantityBus())));
                    addMom.set(OrpCommonUtils.addDouble(new BigDecimal(addMom.get()), new BigDecimal(t.getQuantityAdd())));
                    totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()),
                            new BigDecimal(t.getQuantityTotal())));
                    break;
                default:
            }
        });

        // 日本站金额取整
        int precision = typeEnum == DetailReportTypeEnum.DETAIL_AMOUNT_REPORT ? (isJP ? OrpConstants.ZERO : OrpConstants.TWO) : OrpConstants.ZERO;
        ReportDataDetailData reportDetailData = new ReportDataDetailData();
        reportDetailData.setFlightV(
                OrpCommonUtils.precisionConversion(new BigDecimal(flightV.get()), precision).toString());
        reportDetailData.setFlightYoy(OrpCommonUtils.yoy(flightV.get(), flightYoy.get()));
        reportDetailData.setFlightMom(OrpCommonUtils.mom(flightV.get(), flightMom.get()));

        reportDetailData
                .setHotelV(OrpCommonUtils.precisionConversion(new BigDecimal(hotelV.get()), precision).toString());
        reportDetailData.setHotelYoy(OrpCommonUtils.yoy(hotelV.get(), hotelYoy.get()));
        reportDetailData.setHotelMom(OrpCommonUtils.mom(hotelV.get(), hotelMom.get()));

        reportDetailData
                .setTrainV(OrpCommonUtils.precisionConversion(new BigDecimal(trainV.get()), precision).toString());
        reportDetailData.setTrainYoy(OrpCommonUtils.yoy(trainV.get(), trainYoy.get()));
        reportDetailData.setTrainMom(OrpCommonUtils.mom(trainV.get(), trainMom.get()));

        reportDetailData
                .setCarV(OrpCommonUtils.precisionConversion(new BigDecimal(carV.get()), precision).toString());
        reportDetailData.setCarYoy(OrpCommonUtils.yoy(carV.get(), carYoy.get()));
        reportDetailData.setCarMom(OrpCommonUtils.mom(carV.get(), carMom.get()));

        reportDetailData.setBusV(OrpCommonUtils.precisionConversion(new BigDecimal(busV.get()), precision).toString());
        reportDetailData.setBusYoy(OrpCommonUtils.yoy(busV.get(), busYoy.get()));
        reportDetailData.setBusMom(OrpCommonUtils.mom(busV.get(), busMom.get()));

        reportDetailData.setAddV(OrpCommonUtils.precisionConversion(new BigDecimal(addV.get()), precision).toString());
        reportDetailData.setAddYoy(OrpCommonUtils.yoy(addV.get(), addYoy.get()));
        reportDetailData.setAddMom(OrpCommonUtils.mom(addV.get(), addMom.get()));

        reportDetailData
                .setTotalV(OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());
        reportDetailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
        reportDetailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));
        return new ReprotBodyData(totalTitle, reportDetailData);
    }

    /**
     * 概览-汇总数据-金额/票张
     *
     * @param totalTitle
     * @param typeEnum
     * @param mapping
     * @param currentOverViewListMap
     * @param yoyOverViewListMap
     * @param momOverViewListMap
     * @return
     */
    protected ReprotBodyData mapOverviewTotalAmountTicketData(String totalTitle, DetailReportTypeEnum typeEnum,
                                                              Map<String, ReportCurrentYoyMomMapping> mapping, Map<String, List<OverviewQueryDto>> currentOverViewListMap,
                                                              Map<String, List<OverviewQueryDto>> yoyOverViewListMap,
                                                              Map<String, List<OverviewQueryDto>> momOverViewListMap, boolean isJP) {
        AtomicDouble flightV = new AtomicDouble();
        AtomicDouble flightYoy = new AtomicDouble();
        AtomicDouble flightMom = new AtomicDouble();

        AtomicDouble hotelV = new AtomicDouble();
        AtomicDouble hotelYoy = new AtomicDouble();
        AtomicDouble hotelMom = new AtomicDouble();

        AtomicDouble trainV = new AtomicDouble();
        AtomicDouble trainYoy = new AtomicDouble();
        AtomicDouble trainMom = new AtomicDouble();

        AtomicDouble carV = new AtomicDouble();
        AtomicDouble carYoy = new AtomicDouble();
        AtomicDouble carMom = new AtomicDouble();

        AtomicDouble busV = new AtomicDouble();
        AtomicDouble busYoy = new AtomicDouble();
        AtomicDouble busMom = new AtomicDouble();

        AtomicDouble addV = new AtomicDouble();
        AtomicDouble addYoy = new AtomicDouble();
        AtomicDouble addMom = new AtomicDouble();

        AtomicDouble totalV = new AtomicDouble();
        AtomicDouble totalYoy = new AtomicDouble();
        AtomicDouble totalMom = new AtomicDouble();
        // 迭代映射关系值
        mapping.forEach((k, v) -> {
            // 当期
            List<OverviewQueryDto> currentReportData = currentOverViewListMap.get(k);
            Optional.ofNullable(currentReportData).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        flightV.set(OrpCommonUtils.addDouble(new BigDecimal(flightV.get()), t.getAmountFlt()));
                        hotelV.set(OrpCommonUtils.addDouble(new BigDecimal(hotelV.get()), t.getAmountHtl()));
                        trainV.set(OrpCommonUtils.addDouble(new BigDecimal(trainV.get()), t.getAmountTrain()));
                        carV.set(OrpCommonUtils.addDouble(new BigDecimal(carV.get()), t.getAmountCar()));
                        busV.set(OrpCommonUtils.addDouble(new BigDecimal(busV.get()), t.getAmountBus()));
                        addV.set(OrpCommonUtils.addDouble(new BigDecimal(addV.get()), t.getAmountAdd()));
                        totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), t.getAmountTotal()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        flightV.set(OrpCommonUtils.addDouble(new BigDecimal(flightV.get()),
                                new BigDecimal(t.getQuantityFlt())));
                        hotelV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(hotelV.get()), new BigDecimal(t.getQuantityHtl())));
                        trainV.set(OrpCommonUtils.addDouble(new BigDecimal(trainV.get()),
                                new BigDecimal(t.getQuantityTrain())));
                        carV.set(
                                OrpCommonUtils.addDouble(new BigDecimal(carV.get()), new BigDecimal(t.getQuantityCar())));
                        busV.set(OrpCommonUtils.addDouble(new BigDecimal(busV.get()), new BigDecimal(t.getQuantityBus())));
                        addV.set(OrpCommonUtils.addDouble(new BigDecimal(addV.get()), new BigDecimal(t.getQuantityAdd())));
                        totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()),
                                new BigDecimal(t.getQuantityTotal())));
                        break;
                    default:
                }
            });
            // 同比
            List<OverviewQueryDto> yoyReportData = yoyOverViewListMap.get(v.getYoyDate());
            Optional.ofNullable(yoyReportData).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        flightYoy.set(OrpCommonUtils.addDouble(new BigDecimal(flightYoy.get()), t.getAmountFltYoy()));
                        hotelYoy.set(OrpCommonUtils.addDouble(new BigDecimal(hotelYoy.get()), t.getAmountHtlYoy()));
                        trainYoy.set(OrpCommonUtils.addDouble(new BigDecimal(trainYoy.get()), t.getAmountTrainYoy()));
                        carYoy.set(OrpCommonUtils.addDouble(new BigDecimal(carYoy.get()), t.getAmountCarYoy()));
                        busYoy.set(OrpCommonUtils.addDouble(new BigDecimal(busYoy.get()), t.getAmountBusYoy()));
                        addYoy.set(OrpCommonUtils.addDouble(new BigDecimal(addYoy.get()), t.getAmountAddYoy()));
                        totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), t.getAmountTotalYoy()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        flightYoy.set(OrpCommonUtils.addDouble(new BigDecimal(flightYoy.get()),
                                new BigDecimal(t.getQuantityFltYoy())));
                        hotelYoy.set(OrpCommonUtils.addDouble(new BigDecimal(hotelYoy.get()),
                                new BigDecimal(t.getQuantityHtlYoy())));
                        trainYoy.set(OrpCommonUtils.addDouble(new BigDecimal(trainYoy.get()),
                                new BigDecimal(t.getQuantityTrainYoy())));
                        carYoy.set(OrpCommonUtils.addDouble(new BigDecimal(carYoy.get()),
                                new BigDecimal(t.getQuantityCarYoy())));
                        busYoy.set(OrpCommonUtils.addDouble(new BigDecimal(busYoy.get()), new BigDecimal(t.getQuantityBusYoy())));
                        addYoy.set(OrpCommonUtils.addDouble(new BigDecimal(addYoy.get()), new BigDecimal(t.getQuantityAddYoy())));
                        totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()),
                                new BigDecimal(t.getQuantityTotalYoy())));
                        break;
                    default:
                }
            });
            // 环比
            List<OverviewQueryDto> momReportData = momOverViewListMap.get(v.getMomDate());
            Optional.ofNullable(momReportData).orElse(Lists.newArrayList()).forEach(t -> {
                switch (typeEnum) {
                    case DETAIL_AMOUNT_REPORT:
                        flightMom.set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()), t.getAmountFltMom()));
                        hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()), t.getAmountHtlMom()));
                        trainMom.set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()), t.getAmountTrainMom()));
                        carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()), t.getAmountCarMom()));
                        busMom.set(OrpCommonUtils.addDouble(new BigDecimal(busMom.get()), t.getAmountBusMom()));
                        addMom.set(OrpCommonUtils.addDouble(new BigDecimal(addMom.get()), t.getAmountAddMom()));
                        totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getAmountTotalMom()));
                        break;
                    case DETAIL_TICKET_REPORT:
                        flightMom.set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()),
                                new BigDecimal(t.getQuantityFltMom())));
                        hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()),
                                new BigDecimal(t.getQuantityHtlMom())));
                        trainMom.set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()),
                                new BigDecimal(t.getQuantityTrainMom())));
                        carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()),
                                new BigDecimal(t.getQuantityCarMom())));
                        busMom.set(OrpCommonUtils.addDouble(new BigDecimal(busMom.get()), new BigDecimal(t.getQuantityBusMom())));
                        addMom.set(OrpCommonUtils.addDouble(new BigDecimal(addMom.get()), new BigDecimal(t.getQuantityAddMom())));
                        totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()),
                                new BigDecimal(t.getQuantityTotalMom())));
                        break;
                    default:
                }
            });
        });
        // 日本站金额取整
        int precision = typeEnum == DetailReportTypeEnum.DETAIL_AMOUNT_REPORT ? (isJP ? OrpConstants.ZERO : OrpConstants.TWO) : OrpConstants.ZERO;
        ReportDataDetailData reportDetailData = new ReportDataDetailData();
        reportDetailData.setFlightV(
                OrpCommonUtils.precisionConversion(new BigDecimal(flightV.get()), precision).toString());
        reportDetailData.setFlightYoy(OrpCommonUtils.yoy(flightV.get(), flightYoy.get()));
        reportDetailData.setFlightMom(OrpCommonUtils.mom(flightV.get(), flightMom.get()));

        reportDetailData
                .setHotelV(OrpCommonUtils.precisionConversion(new BigDecimal(hotelV.get()), precision).toString());
        reportDetailData.setHotelYoy(OrpCommonUtils.yoy(hotelV.get(), hotelYoy.get()));
        reportDetailData.setHotelMom(OrpCommonUtils.mom(hotelV.get(), hotelMom.get()));

        reportDetailData
                .setTrainV(OrpCommonUtils.precisionConversion(new BigDecimal(trainV.get()), precision).toString());
        reportDetailData.setTrainYoy(OrpCommonUtils.yoy(trainV.get(), trainYoy.get()));
        reportDetailData.setTrainMom(OrpCommonUtils.mom(trainV.get(), trainMom.get()));

        reportDetailData
                .setCarV(OrpCommonUtils.precisionConversion(new BigDecimal(carV.get()), precision).toString());
        reportDetailData.setCarYoy(OrpCommonUtils.yoy(carV.get(), carYoy.get()));
        reportDetailData.setCarMom(OrpCommonUtils.mom(carV.get(), carMom.get()));

        reportDetailData.setBusV(OrpCommonUtils.precisionConversion(new BigDecimal(busV.get()), precision).toString());
        reportDetailData.setBusYoy(OrpCommonUtils.yoy(busV.get(), busYoy.get()));
        reportDetailData.setBusMom(OrpCommonUtils.mom(busV.get(), busMom.get()));

        reportDetailData.setAddV(OrpCommonUtils.precisionConversion(new BigDecimal(addV.get()), precision).toString());
        reportDetailData.setAddYoy(OrpCommonUtils.yoy(addV.get(), addYoy.get()));
        reportDetailData.setAddMom(OrpCommonUtils.mom(addV.get(), addMom.get()));

        reportDetailData
                .setTotalV(OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());
        reportDetailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
        reportDetailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));
        return new ReprotBodyData(totalTitle, reportDetailData);
    }

    /**
     * 计算 map 概览 ck entity -> body
     */
    protected ReprotBodyData mapOverviewTotalReportBodyData(String type, DetailReportTypeEnum reportTypeEnum,
                                                            List<OverviewQueryDto> overviewList) {
        ReportDataDetailData detailData = new ReportDataDetailData();
        AtomicDouble flightV = new AtomicDouble();
        AtomicDouble flightYoy = new AtomicDouble();
        AtomicDouble flightMom = new AtomicDouble();

        AtomicDouble hotelV = new AtomicDouble();
        AtomicDouble hotelYoy = new AtomicDouble();
        AtomicDouble hotelMom = new AtomicDouble();

        AtomicDouble trainV = new AtomicDouble();
        AtomicDouble trainYoy = new AtomicDouble();
        AtomicDouble trainMom = new AtomicDouble();

        AtomicDouble carV = new AtomicDouble();
        AtomicDouble carYoy = new AtomicDouble();
        AtomicDouble carMom = new AtomicDouble();

        AtomicDouble totalV = new AtomicDouble();
        AtomicDouble totalYoy = new AtomicDouble();
        AtomicDouble totalMom = new AtomicDouble();

        if (StringUtils.isBlank(type)) {
            type = groupByMonth(overviewList.get(OrpConstants.ZERO).getReportDate());
        }
        // 空集合补数
        if (CollectionUtils.isEmpty(overviewList)) {
            return new ReprotBodyData(type, mapOverviewInfoReport());
        }

        overviewList.forEach(v -> {
            if (reportTypeEnum.getName().equals(DetailReportTypeEnum.DETAIL_AMOUNT_REPORT.getName())) {
                flightV.set(OrpCommonUtils.addDouble(new BigDecimal(flightV.get()), v.getAmountFlt()));
                flightYoy.set(OrpCommonUtils.addDouble(new BigDecimal(flightYoy.get()), v.getAmountFltYoy()));
                flightMom.set(OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()), v.getAmountFltMom()));

                hotelV.set(OrpCommonUtils.addDouble(new BigDecimal(hotelV.get()), v.getAmountHtl()));
                hotelYoy.set(OrpCommonUtils.addDouble(new BigDecimal(hotelYoy.get()), v.getAmountHtlYoy()));
                hotelMom.set(OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()), v.getAmountHtlMom()));

                trainV.set(OrpCommonUtils.addDouble(new BigDecimal(trainV.get()), v.getAmountTrain()));
                trainYoy.set(OrpCommonUtils.addDouble(new BigDecimal(trainYoy.get()), v.getAmountTrainYoy()));
                trainMom.set(OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()), v.getAmountTrainMom()));

                carV.set(OrpCommonUtils.addDouble(new BigDecimal(carV.get()), v.getAmountCar()));
                carYoy.set(OrpCommonUtils.addDouble(new BigDecimal(carYoy.get()), v.getAmountCarYoy()));
                carMom.set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()), v.getAmountCarMom()));

                totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), v.getAmountTotal()));
                totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), v.getAmountTotalYoy()));
                totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), v.getAmountTotalMom()));
            } else {
                flightV
                        .set(OrpCommonUtils.addDouble(new BigDecimal(flightV.get()), new BigDecimal(v.getQuantityFlt())));
                flightYoy.set(
                        OrpCommonUtils.addDouble(new BigDecimal(flightYoy.get()), new BigDecimal(v.getQuantityFltYoy())));
                flightMom.set(
                        OrpCommonUtils.addDouble(new BigDecimal(flightMom.get()), new BigDecimal(v.getQuantityFltMom())));

                hotelV.set(OrpCommonUtils.addDouble(new BigDecimal(hotelV.get()), new BigDecimal(v.getQuantityHtl())));
                hotelYoy.set(
                        OrpCommonUtils.addDouble(new BigDecimal(hotelYoy.get()), new BigDecimal(v.getQuantityHtlYoy())));
                hotelMom.set(
                        OrpCommonUtils.addDouble(new BigDecimal(hotelMom.get()), new BigDecimal(v.getQuantityHtlMom())));

                trainV
                        .set(OrpCommonUtils.addDouble(new BigDecimal(trainV.get()), new BigDecimal(v.getQuantityTrain())));
                trainYoy.set(
                        OrpCommonUtils.addDouble(new BigDecimal(trainYoy.get()), new BigDecimal(v.getQuantityTrainYoy())));
                trainMom.set(
                        OrpCommonUtils.addDouble(new BigDecimal(trainMom.get()), new BigDecimal(v.getQuantityTrainMom())));

                carV.set(OrpCommonUtils.addDouble(new BigDecimal(carV.get()), new BigDecimal(v.getQuantityCar())));
                carYoy
                        .set(OrpCommonUtils.addDouble(new BigDecimal(carYoy.get()), new BigDecimal(v.getQuantityCarYoy())));
                carMom
                        .set(OrpCommonUtils.addDouble(new BigDecimal(carMom.get()), new BigDecimal(v.getQuantityCarMom())));

                totalV
                        .set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), new BigDecimal(v.getQuantityTotal())));
                totalYoy.set(
                        OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), new BigDecimal(v.getQuantityTotalYoy())));
                totalMom.set(
                        OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), new BigDecimal(v.getQuantityTotalMom())));
            }
        });

        detailData.setFlightV(
                OrpCommonUtils.precisionConversion(new BigDecimal(flightV.get()), OrpConstants.ZERO).toString());
        detailData.setFlightYoy(OrpCommonUtils.yoy(flightV.get(), flightYoy.get()));
        detailData.setFlightMom(OrpCommonUtils.mom(flightV.get(), flightMom.get()));

        detailData
                .setHotelV(OrpCommonUtils.precisionConversion(new BigDecimal(hotelV.get()), OrpConstants.ZERO).toString());
        detailData.setHotelYoy(OrpCommonUtils.yoy(hotelV.get(), hotelYoy.get()));
        detailData.setHotelMom(OrpCommonUtils.mom(hotelV.get(), hotelMom.get()));

        detailData
                .setTrainV(OrpCommonUtils.precisionConversion(new BigDecimal(trainV.get()), OrpConstants.ZERO).toString());
        detailData.setTrainYoy(OrpCommonUtils.yoy(trainV.get(), trainYoy.get()));
        detailData.setTrainMom(OrpCommonUtils.mom(trainV.get(), trainMom.get()));

        detailData
                .setCarV(OrpCommonUtils.precisionConversion(new BigDecimal(carV.get()), OrpConstants.ZERO).toString());
        detailData.setCarYoy(OrpCommonUtils.yoy(carV.get(), carYoy.get()));
        detailData.setCarMom(OrpCommonUtils.mom(carV.get(), carMom.get()));

        detailData
                .setTotalV(OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), OrpConstants.ZERO).toString());
        detailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
        detailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));
        return new ReprotBodyData(type, detailData);
    }

    /**
     * 机票 整体、协议非协议、国内国际报表
     */
    protected ReprotBodyData mapFlightAllBodyData(String type, CurrentYoyMomData<OnlineReportFlightDto> flightList, boolean isJP) {
        ReportDataDetailData detailData = new ReportDataDetailData();
        if (StringUtils.isBlank(type)) {
            type = flightList.getMonth();
        }
        if (CollectionUtils.isEmpty(flightList.getCurrent())) {
            return new ReprotBodyData(type, isJP ? mapJPFlightEmptyData() : mapFlightEmptyData());
        }
        AtomicDouble bindAmount = new AtomicDouble();
        AtomicDouble changeFee = new AtomicDouble();
        AtomicDouble insuranceFee = new AtomicDouble();
        AtomicDouble netfare = new AtomicDouble();
        AtomicDouble oilFee = new AtomicDouble();
        AtomicDouble rebookPriceDifferential = new AtomicDouble();
        AtomicDouble oilfeedifferential = new AtomicDouble();
        AtomicDouble taxDifferential = new AtomicDouble();
        AtomicDouble rebookServiceFee = new AtomicDouble();
        AtomicDouble refundFee = new AtomicDouble();
        AtomicDouble refundItineraryFee = new AtomicDouble();
        AtomicDouble refundServiceFee = new AtomicDouble();
        AtomicDouble sendTicketFee = new AtomicDouble();
        AtomicDouble servicepackageFee = new AtomicDouble();
        AtomicDouble serviceFee = new AtomicDouble();
        AtomicDouble tax = new AtomicDouble();
        AtomicDouble ticketBehindServicefee = new AtomicDouble();
        AtomicDouble rebookBehindServiceFee = new AtomicDouble();
        AtomicDouble refundBehindServiceFee = new AtomicDouble();
        // 小计
        AtomicDouble subtotal = new AtomicDouble();
        flightList.getCurrent().forEach(data -> {
            bindAmount.set(OrpCommonUtils.addDouble(new BigDecimal(bindAmount.get()), data.getBindAmount()));
            changeFee.set(OrpCommonUtils.addDouble(new BigDecimal(changeFee.get()), data.getChangeFee()));
            insuranceFee.set(OrpCommonUtils.addDouble(new BigDecimal(insuranceFee.get()), data.getInsuranceFee()));
            netfare.set(OrpCommonUtils.addDouble(new BigDecimal(netfare.get()), data.getNetFare()));
            oilFee.set(OrpCommonUtils.addDouble(new BigDecimal(oilFee.get()), data.getOilFee()));
            rebookPriceDifferential.set(OrpCommonUtils.addDouble(new BigDecimal(rebookPriceDifferential.get()),
                    data.getRebookPriceDifferential()));
            oilfeedifferential.set(OrpCommonUtils.addDouble(new BigDecimal(oilfeedifferential.get()), data.getOilfeedifferential()));
            taxDifferential.set(OrpCommonUtils.addDouble(new BigDecimal(taxDifferential.get()), data.getTaxDifferential()));
            rebookServiceFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(rebookServiceFee.get()), data.getRebookServiceFee()));
            refundFee.set(OrpCommonUtils.addDouble(new BigDecimal(refundFee.get()), data.getRefundFee()));
            refundItineraryFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(refundItineraryFee.get()), data.getRefundItineraryFee()));
            refundServiceFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(refundServiceFee.get()), data.getRefundServiceFee()));
            sendTicketFee.set(OrpCommonUtils.addDouble(new BigDecimal(sendTicketFee.get()), data.getSendTicketFee()));
            servicepackageFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(servicepackageFee.get()), data.getServicepackageFee()));
            serviceFee.set(OrpCommonUtils.addDouble(new BigDecimal(serviceFee.get()), data.getServiceFee()));
            tax.set(OrpCommonUtils.addDouble(new BigDecimal(tax.get()), data.getTax()));
            ticketBehindServicefee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(ticketBehindServicefee.get()), data.getTicketBehindServicefee()));
            rebookBehindServiceFee.set(OrpCommonUtils.addDouble(new BigDecimal(rebookBehindServiceFee.get()), data.getRebookBehindServiceFee()));
            refundBehindServiceFee.set(OrpCommonUtils.addDouble(new BigDecimal(refundBehindServiceFee.get()), data.getRefundBehindServiceFee()));
            // 小计
            subtotal.set(OrpCommonUtils.addDouble(new BigDecimal(subtotal.get()), data.getRealPay()));
        });
        // 日本站金额取整
        int precision = isJP ? OrpConstants.ZERO : OrpConstants.TWO;
        detailData.setBindAmount(
                OrpCommonUtils.precisionConversion(new BigDecimal(bindAmount.get()), precision).toString());
        detailData.setChangeFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(changeFee.get()), precision).toString());
        detailData.setInsuranceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(insuranceFee.get()), precision).toString());
        detailData.setNetfare(
                OrpCommonUtils.precisionConversion(new BigDecimal(netfare.get()), precision).toString());
        detailData
                .setOilFee(OrpCommonUtils.precisionConversion(new BigDecimal(oilFee.get()), precision).toString());
        detailData.setRebookPriceDifferential(OrpCommonUtils
                .precisionConversion(new BigDecimal(rebookPriceDifferential.get()), precision).toString());
        detailData.setOilfeedifferential(OrpCommonUtils
                .precisionConversion(new BigDecimal(oilfeedifferential.get()), precision).toString());
        detailData.setTaxDifferential(OrpCommonUtils
                .precisionConversion(new BigDecimal(taxDifferential.get()), precision).toString());
        detailData.setRebookServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(rebookServiceFee.get()), precision).toString());
        detailData.setRefundFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(refundFee.get()), precision).toString());
        detailData.setRefundItineraryFee(OrpCommonUtils
                .precisionConversion(
                        OrpCommonUtils.showMinusVal(new BigDecimal(refundItineraryFee.get())), precision)
                .toString());
        detailData.setRefundServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(refundServiceFee.get()), precision).toString());
        detailData.setSendTicketFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(sendTicketFee.get()), precision).toString());
        detailData.setServicepackageFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(servicepackageFee.get()), precision).toString());
        detailData.setServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(serviceFee.get()), precision).toString());
        detailData.setTax(OrpCommonUtils.precisionConversion(new BigDecimal(tax.get()), precision).toString());
        detailData.setTicketBehindServicefee(
                OrpCommonUtils.precisionConversion(new BigDecimal(ticketBehindServicefee.get()), precision).toString());
        detailData.setRebookBehindServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(rebookBehindServiceFee.get()), precision).toString());
        detailData.setRefundBehindServiceFee(OrpCommonUtils.precisionConversion(new BigDecimal(refundBehindServiceFee.get()), precision).toString());
        detailData.setTotalV(
                OrpCommonUtils.precisionConversion(new BigDecimal(subtotal.get()), precision).toString());
        return new ReprotBodyData(type, detailData);
    }

    /**
     * 机票总计
     */
    protected ReprotBodyData mapFlightAllTotalBodyData(String type, List<OnlineReportFlightDto> gList) {
        ReportDataDetailData detailData = new ReportDataDetailData();
        if (StringUtils.isBlank(type)) {
            type = groupByMonth(gList.get(OrpConstants.ZERO).getReportDate());
        }
        if (CollectionUtils.isEmpty(gList)) {
            return new ReprotBodyData(type, mapFlightEmptyData());
        }
        AtomicDouble bindAmount = new AtomicDouble();
        AtomicDouble changeFee = new AtomicDouble();
        AtomicDouble insuranceFee = new AtomicDouble();
        AtomicDouble netfare = new AtomicDouble();
        AtomicDouble oilFee = new AtomicDouble();
        AtomicDouble rebookPriceDifferential = new AtomicDouble();
        AtomicDouble rebookServiceFee = new AtomicDouble();
        AtomicDouble refundFee = new AtomicDouble();
        AtomicDouble refundItineraryFee = new AtomicDouble();
        AtomicDouble refundServiceFee = new AtomicDouble();
        AtomicDouble sendTicketFee = new AtomicDouble();
        AtomicDouble servicePackageFee = new AtomicDouble();
        AtomicDouble serviceFee = new AtomicDouble();
        AtomicDouble tax = new AtomicDouble();
        // 小计
        AtomicDouble subtotal = new AtomicDouble();
        gList.forEach(data -> {
            bindAmount.set(OrpCommonUtils.addDouble(new BigDecimal(bindAmount.get()), data.getBindAmount()));
            changeFee.set(OrpCommonUtils.addDouble(new BigDecimal(changeFee.get()), data.getChangeFee()));
            insuranceFee.set(OrpCommonUtils.addDouble(new BigDecimal(insuranceFee.get()), data.getInsuranceFee()));
            netfare.set(OrpCommonUtils.addDouble(new BigDecimal(netfare.get()), data.getNetFare()));
            oilFee.set(OrpCommonUtils.addDouble(new BigDecimal(oilFee.get()), data.getOilFee()));
            rebookPriceDifferential.set(OrpCommonUtils.addDouble(new BigDecimal(rebookPriceDifferential.get()),
                    data.getRebookPriceDifferential()));
            rebookServiceFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(rebookServiceFee.get()), data.getRebookServiceFee()));
            refundFee.set(OrpCommonUtils.addDouble(new BigDecimal(refundFee.get()), data.getRefundFee()));
            refundItineraryFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(refundItineraryFee.get()), data.getRefundItineraryFee()));
            refundServiceFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(refundServiceFee.get()), data.getRefundServiceFee()));
            sendTicketFee.set(OrpCommonUtils.addDouble(new BigDecimal(sendTicketFee.get()), data.getSendTicketFee()));
            servicePackageFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(servicePackageFee.get()), data.getServicepackageFee()));
            serviceFee.set(OrpCommonUtils.addDouble(new BigDecimal(serviceFee.get()), data.getServiceFee()));
            tax.set(OrpCommonUtils.addDouble(new BigDecimal(tax.get()), data.getTax()));
            // 小计
            subtotal.set(OrpCommonUtils.addDouble(new BigDecimal(subtotal.get()), data.getRealPay()));
        });
        detailData.setBindAmount(
                OrpCommonUtils.precisionConversion(new BigDecimal(bindAmount.get()), OrpConstants.ZERO).toString());
        detailData.setChangeFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(changeFee.get()), OrpConstants.ZERO).toString());
        detailData.setInsuranceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(insuranceFee.get()), OrpConstants.ZERO).toString());
        detailData.setNetfare(
                OrpCommonUtils.precisionConversion(new BigDecimal(netfare.get()), OrpConstants.ZERO).toString());
        detailData
                .setOilFee(OrpCommonUtils.precisionConversion(new BigDecimal(oilFee.get()), OrpConstants.ZERO).toString());
        detailData.setRebookPriceDifferential(OrpCommonUtils
                .precisionConversion(new BigDecimal(rebookPriceDifferential.get()), OrpConstants.ZERO).toString());
        detailData.setRebookServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(rebookServiceFee.get()), OrpConstants.ZERO).toString());
        detailData.setRefundFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(refundFee.get()), OrpConstants.ZERO).toString());
        detailData.setRefundItineraryFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(refundItineraryFee.get()), OrpConstants.ZERO).toString());
        detailData.setRefundServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(refundServiceFee.get()), OrpConstants.ZERO).toString());
        detailData.setSendTicketFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(sendTicketFee.get()), OrpConstants.ZERO).toString());
        detailData.setServicepackageFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(servicePackageFee.get()), OrpConstants.ZERO).toString());
        detailData.setServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(serviceFee.get()), OrpConstants.ZERO).toString());
        detailData.setTax(OrpCommonUtils.precisionConversion(new BigDecimal(tax.get()), OrpConstants.ZERO).toString());
        detailData.setTotalV(
                OrpCommonUtils.precisionConversion(new BigDecimal(subtotal.get()), OrpConstants.ZERO).toString());
        return new ReprotBodyData(type, detailData);
    }

    /**
     * 酒店 - 金额明细报表
     */
    protected ReprotBodyData mapInfoAmountHotelReport(String type, List<OnlineReportHotelDto> hList) {
        return mapInfoReport(type, hList, (List) OrpConstants.NULL);
    }

    /**
     * 机票 - 金额明细报表
     */
    protected ReprotBodyData mapInfoAmountFlightReport(String type, List<OnlineReportFlightDto> fList) {
        return mapInfoReport(type, (List) OrpConstants.NULL, fList);
    }

    /**
     * 酒店 - 间夜量明细报表
     */
    protected ReprotBodyData mapInfoTicketHotelReport(String type, List<OnlineReportHotelDto> hList) {
        return mapTicketReport(type, hList, (List) OrpConstants.NULL);
    }

    /**
     * 机票 - 间夜量明细报表
     */
    protected ReprotBodyData mapInfoTicketFlightReport(String type, List<OnlineReportFlightDto> fList) {
        return mapTicketReport(type, (List) OrpConstants.NULL, fList);
    }

    /**
     * 机票/酒店-票张/间夜量-共用
     *
     * @param dimension
     * @param hotelList
     * @param flightList
     * @return
     */
    private ReprotBodyData mapTicketReport(String dimension, List<OnlineReportHotelDto> hotelList,
                                           List<OnlineReportFlightDto> flightList) {
        ReportDataDetailData detailData = new ReportDataDetailData();
        AtomicDouble agreementV = new AtomicDouble();
        AtomicDouble agreementYoy = new AtomicDouble();
        AtomicDouble agreementMom = new AtomicDouble();

        AtomicDouble unAgreementV = new AtomicDouble();
        AtomicDouble unAgreementYoy = new AtomicDouble();
        AtomicDouble unAgreementMom = new AtomicDouble();

        AtomicDouble domesticV = new AtomicDouble();
        AtomicDouble domesticYoy = new AtomicDouble();
        AtomicDouble domesticMom = new AtomicDouble();

        AtomicDouble internationalV = new AtomicDouble();
        AtomicDouble internationalYoy = new AtomicDouble();
        AtomicDouble internationalMom = new AtomicDouble();

        AtomicDouble totalV = new AtomicDouble();
        AtomicDouble totalYoy = new AtomicDouble();
        AtomicDouble totalMom = new AtomicDouble();

        if (CollectionUtils.isNotEmpty(hotelList)) {
            hotelList.forEach(data -> {
                // 协议
                agreementV.set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()),
                        new BigDecimal(data.getAgreementQuantity())));
                agreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()),
                        new BigDecimal(data.getAgreementQuantityYoy())));
                agreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()),
                        new BigDecimal(data.getAgreementQuantityMom())));
                // 非协议
                unAgreementV.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()),
                        new BigDecimal(data.getMemQuantity())));
                unAgreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()),
                        new BigDecimal(data.getMemQuantityYoy())));
                unAgreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()),
                        new BigDecimal(data.getMemQuantityMom())));
                // 国内
                domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()),
                        new BigDecimal(data.getDomesticQuantity())));
                domesticYoy.set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()),
                        new BigDecimal(data.getDomesticQuantityYoy())));
                domesticMom.set(OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()),
                        new BigDecimal(data.getDomesticQuantityMom())));
                // 国际
                internationalV.set(OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()),
                        new BigDecimal(data.getInternationalQuantity())));
                internationalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()),
                        new BigDecimal(data.getInternationalQuantityYoy())));
                internationalMom.set(OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()),
                        new BigDecimal(data.getInternationalQuantityMom())));

                // 总票张
                totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), new BigDecimal(data.getQuantityV())));
                totalYoy.set(
                        OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), new BigDecimal(data.getQuantityYoy())));
                totalMom.set(
                        OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), new BigDecimal(data.getQuantityMom())));
            });
        }
        if (CollectionUtils.isNotEmpty(flightList)) {
            flightList.forEach(data -> {
                // 协议
                agreementV.set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()),
                        new BigDecimal(data.getAgreementQuantity())));
                agreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()),
                        new BigDecimal(data.getAgreementQuantityYoy())));
                agreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()),
                        new BigDecimal(data.getAgreementQuantityMom())));
                // 非协议
                unAgreementV.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()),
                        new BigDecimal(data.getNotAgreementQuantity())));
                unAgreementYoy.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()),
                        new BigDecimal(data.getNotAgreementQuantityYoy())));
                unAgreementMom.set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()),
                        new BigDecimal(data.getNotAgreementQuantityMom())));
                // 国内
                domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()),
                        new BigDecimal(data.getDomesticQuantity())));
                domesticYoy.set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()),
                        new BigDecimal(data.getDomesticQuantityYoy())));
                domesticMom.set(OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()),
                        new BigDecimal(data.getDomesticQuantityMom())));
                // 国际
                internationalV.set(OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()),
                        new BigDecimal(data.getInternationalQuantity())));
                internationalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()),
                        new BigDecimal(data.getInternationalQuantityYoy())));
                internationalMom.set(OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()),
                        new BigDecimal(data.getInternationalQuantityMom())));

                // 总票张
                totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), new BigDecimal(data.getQuantityV())));
                totalYoy.set(
                        OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), new BigDecimal(data.getQuantityYoy())));
                totalMom.set(
                        OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), new BigDecimal(data.getQuantityMom())));
            });
        }
        // 协议
        detailData.setAgreementV(
                OrpCommonUtils.precisionConversion(new BigDecimal(agreementV.get()), OrpConstants.ZERO).toString());
        detailData.setAgreementYoy(OrpCommonUtils.yoy(agreementV.get(), agreementYoy.get()));
        detailData.setAgreementMom(OrpCommonUtils.mom(agreementV.get(), agreementMom.get()));
        // 非协议
        detailData.setUnAgreementV(
                OrpCommonUtils.precisionConversion(new BigDecimal(unAgreementV.get()), OrpConstants.ZERO).toString());
        detailData.setUnAgreementYoy(OrpCommonUtils.yoy(unAgreementV.get(), unAgreementYoy.get()));
        detailData.setUnAgreementMom(OrpCommonUtils.mom(unAgreementV.get(), unAgreementMom.get()));
        // 国内
        detailData.setDomesticV(
                OrpCommonUtils.precisionConversion(new BigDecimal(domesticV.get()), OrpConstants.ZERO).toString());
        detailData.setDomesticYoy(OrpCommonUtils.yoy(domesticV.get(), domesticYoy.get()));
        detailData.setDomesticMom(OrpCommonUtils.mom(domesticV.get(), domesticMom.get()));
        // 国际
        detailData.setInternationalV(
                OrpCommonUtils.precisionConversion(new BigDecimal(internationalV.get()), OrpConstants.ZERO).toString());
        detailData.setInternationalYoy(OrpCommonUtils.yoy(internationalV.get(), internationalYoy.get()));
        detailData.setInternationalMom(OrpCommonUtils.mom(internationalV.get(), internationalMom.get()));

        // 总计
        detailData
                .setTotalV(OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), OrpConstants.ZERO).toString());
        detailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
        detailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));
        return new ReprotBodyData(getDimension(dimension, hotelList, flightList), detailData);
    }

    /**
     * 机票/酒店-票张/间夜量-共用
     */
    private ReprotBodyData mapInfoReport(String dimension, List<OnlineReportHotelDto> hotelList,
                                         List<OnlineReportFlightDto> flightList) {
        ReportDataDetailData detailData = new ReportDataDetailData();
        AtomicDouble agreementV = new AtomicDouble();
        AtomicDouble agreementYoy = new AtomicDouble();
        AtomicDouble agreementMom = new AtomicDouble();

        AtomicDouble unAgreementV = new AtomicDouble();
        AtomicDouble unAgreementYoy = new AtomicDouble();
        AtomicDouble unAgreementMom = new AtomicDouble();

        AtomicDouble domesticV = new AtomicDouble();
        AtomicDouble domesticYoy = new AtomicDouble();
        AtomicDouble domesticMom = new AtomicDouble();

        AtomicDouble internationalV = new AtomicDouble();
        AtomicDouble internationalYoy = new AtomicDouble();
        AtomicDouble internationalMom = new AtomicDouble();

        AtomicDouble totalV = new AtomicDouble();
        AtomicDouble totalYoy = new AtomicDouble();
        AtomicDouble totalMom = new AtomicDouble();

        if (CollectionUtils.isNotEmpty(hotelList)) {
            hotelList.forEach(data -> {
                // 协议
                agreementV.set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()), data.getAgreementAmount()));
                agreementYoy
                        .set(OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()), data.getAgreementAmountYoy()));
                agreementMom
                        .set(OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()), data.getAgreementAmountMom()));
                // 非协议
                unAgreementV
                        .set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()), data.getNotAgreementAmount()));
                unAgreementYoy.set(
                        OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()), data.getNotAgreementAmountYoy()));
                unAgreementMom.set(
                        OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()), data.getNotAgreementAmountMom()));
                // 国内
                domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()), data.getDomesticAmount()));
                domesticYoy
                        .set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()), data.getDomesticAmountYoy()));
                domesticMom
                        .set(OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()), data.getDomesticAmountMom()));
                // 国际
                internationalV
                        .set(OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()), data.getInternationalAmount()));
                internationalYoy.set(
                        OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()), data.getInternationalAmountYoy()));
                internationalMom.set(
                        OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()), data.getInternationalAmountMom()));
                // 总计
                totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), data.getRealPay()));
                totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), data.getRealPayYoy()));
                totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), data.getRealPayMom()));
            });
        }
        if (CollectionUtils.isNotEmpty(flightList)) {
            flightList.forEach(data -> {
                // 协议
                agreementV.set(OrpCommonUtils.addDouble(new BigDecimal(agreementV.get()), data.getAgreementAmount()));
                agreementYoy
                        .set(OrpCommonUtils.addDouble(new BigDecimal(agreementYoy.get()), data.getAgreementAmountYoy()));
                agreementMom
                        .set(OrpCommonUtils.addDouble(new BigDecimal(agreementMom.get()), data.getAgreementAmountMom()));
                // 非协议
                unAgreementV
                        .set(OrpCommonUtils.addDouble(new BigDecimal(unAgreementV.get()), data.getNotAgreementAmount()));
                unAgreementYoy.set(
                        OrpCommonUtils.addDouble(new BigDecimal(unAgreementYoy.get()), data.getNotAgreementAmountYoy()));
                unAgreementMom.set(
                        OrpCommonUtils.addDouble(new BigDecimal(unAgreementMom.get()), data.getNotAgreementAmountMom()));
                // 国内
                domesticV.set(OrpCommonUtils.addDouble(new BigDecimal(domesticV.get()), data.getDomesticAmount()));
                domesticYoy
                        .set(OrpCommonUtils.addDouble(new BigDecimal(domesticYoy.get()), data.getDomesticAmountYoy()));
                domesticMom
                        .set(OrpCommonUtils.addDouble(new BigDecimal(domesticMom.get()), data.getDomesticAmountMom()));
                // 国际
                internationalV
                        .set(OrpCommonUtils.addDouble(new BigDecimal(internationalV.get()), data.getInternationalAmount()));
                internationalYoy.set(
                        OrpCommonUtils.addDouble(new BigDecimal(internationalYoy.get()), data.getInternationalAmountYoy()));
                internationalMom.set(
                        OrpCommonUtils.addDouble(new BigDecimal(internationalMom.get()), data.getInternationalAmountMom()));
                // 总计
                totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), data.getRealPay()));
                totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), data.getRealPayYoy()));
                totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), data.getRealPayMom()));
            });
        }
        // 协议
        detailData.setAgreementV(
                OrpCommonUtils.precisionConversion(new BigDecimal(agreementV.get()), OrpConstants.TWO).toString());
        detailData.setAgreementYoy(OrpCommonUtils.yoy(agreementV.get(), agreementYoy.get()));
        detailData.setAgreementMom(OrpCommonUtils.mom(agreementV.get(), agreementMom.get()));
        // 非协议
        detailData.setUnAgreementV(
                OrpCommonUtils.precisionConversion(new BigDecimal(unAgreementV.get()), OrpConstants.TWO).toString());
        detailData.setUnAgreementYoy(OrpCommonUtils.yoy(unAgreementV.get(), unAgreementYoy.get()));
        detailData.setUnAgreementMom(OrpCommonUtils.mom(unAgreementV.get(), unAgreementMom.get()));
        // 国内
        detailData.setDomesticV(
                OrpCommonUtils.precisionConversion(new BigDecimal(unAgreementV.get()), OrpConstants.TWO).toString());
        detailData.setDomesticYoy(OrpCommonUtils.yoy(domesticV.get(), domesticYoy.get()));
        detailData.setDomesticMom(OrpCommonUtils.mom(domesticV.get(), domesticMom.get()));
        // 国际
        detailData.setInternationalV(
                OrpCommonUtils.precisionConversion(new BigDecimal(unAgreementV.get()), OrpConstants.TWO).toString());
        detailData.setInternationalYoy(OrpCommonUtils.yoy(internationalV.get(), internationalYoy.get()));
        detailData.setInternationalMom(OrpCommonUtils.mom(internationalV.get(), internationalMom.get()));

        // 总计
        detailData
                .setTotalV(OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), OrpConstants.TWO).toString());
        detailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
        detailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));
        return new ReprotBodyData(getDimension(dimension, hotelList, flightList), detailData);
    }

    protected String getDimension(String dimension, List<OnlineReportHotelDto> hList,
                                  List<OnlineReportFlightDto> fList) {
        if (StringUtils.isBlank(dimension)) {
            if (Objects.nonNull(hList)) {
                dimension = groupByMonth(hList.get(OrpConstants.ZERO).getReportDate());
            } else {
                dimension = groupByMonth(fList.get(OrpConstants.ZERO).getReportDate());
            }
        }
        return dimension;
    }

    /**
     * empty 概览 info 报表
     */
    protected ReportDataDetailData mapOverviewInfoReport() {
        ReportDataDetailData dataDetailData = new ReportDataDetailData();
        dataDetailData.setFlightV(OrpConstants.ZERO_CHAR);
        dataDetailData.setFlightYoy(OrpConstants.DEFAULT_P);
        dataDetailData.setFlightMom(OrpConstants.DEFAULT_P);

        dataDetailData.setHotelV(OrpConstants.ZERO_CHAR);
        dataDetailData.setHotelYoy(OrpConstants.DEFAULT_P);
        dataDetailData.setHotelMom(OrpConstants.DEFAULT_P);

        dataDetailData.setTrainV(OrpConstants.ZERO_CHAR);
        dataDetailData.setTrainYoy(OrpConstants.DEFAULT_P);
        dataDetailData.setTrainMom(OrpConstants.DEFAULT_P);

        dataDetailData.setCarV(OrpConstants.ZERO_CHAR);
        dataDetailData.setCarYoy(OrpConstants.DEFAULT_P);
        dataDetailData.setCarMom(OrpConstants.DEFAULT_P);

        dataDetailData.setTotalV(OrpConstants.ZERO_CHAR);
        dataDetailData.setTotalYoy(OrpConstants.DEFAULT_P);
        dataDetailData.setTotalMom(OrpConstants.DEFAULT_P);
        return dataDetailData;
    }

    /**
     * empty 机票-整体、协议非协议、国内国际
     */
    protected ReportDataDetailData mapFlightEmptyData() {
        ReportDataDetailData dataDetailData = new ReportDataDetailData();
        dataDetailData.setBindAmount(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setChangeFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setInsuranceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setNetfare(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setOilFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setQuantity(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setRebookPriceDifferential(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setOilfeedifferential(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setTaxDifferential(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setRebookServiceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setRefundFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setRefundItineraryFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setRefundServiceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setSendTicketFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setServicepackageFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setServiceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setTax(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setTicketBehindServicefee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setRebookBehindServiceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setRefundBehindServiceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setTotalV(OrpConstants.ZERO_CHAR_2);
        return dataDetailData;
    }

    /**
     * empty 机票-整体、协议非协议、国内国际, 日本站数据取整
     */
    protected ReportDataDetailData mapJPFlightEmptyData() {
        ReportDataDetailData dataDetailData = new ReportDataDetailData();
        dataDetailData.setBindAmount(OrpConstants.ZERO_CHAR);
        dataDetailData.setChangeFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setInsuranceFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setNetfare(OrpConstants.ZERO_CHAR);
        dataDetailData.setOilFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setQuantity(OrpConstants.ZERO_CHAR);
        dataDetailData.setRebookPriceDifferential(OrpConstants.ZERO_CHAR);
        dataDetailData.setOilfeedifferential(OrpConstants.ZERO_CHAR);
        dataDetailData.setTaxDifferential(OrpConstants.ZERO_CHAR);
        dataDetailData.setRebookServiceFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setRefundFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setRefundItineraryFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setRefundServiceFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setSendTicketFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setServicepackageFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setServiceFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setTax(OrpConstants.ZERO_CHAR);
        dataDetailData.setTicketBehindServicefee(OrpConstants.ZERO_CHAR);
        dataDetailData.setRebookBehindServiceFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setRefundBehindServiceFee(OrpConstants.ZERO_CHAR);
        dataDetailData.setTotalV(OrpConstants.ZERO_CHAR);
        return dataDetailData;
    }

    /**
     * empty 酒店
     */
    protected ReportDataDetailData mapHotelEmptyData() {
        ReportDataDetailData detailData = new ReportDataDetailData();
        detailData.setRoomPrice(OrpConstants.ZERO_CHAR_2);
        detailData.setCouponAmount(OrpConstants.ZERO_CHAR_2);
        detailData.setServiceFee(OrpConstants.ZERO_CHAR_2);
        detailData.setHotelPostServiceFee(OrpConstants.ZERO_CHAR_2);
        detailData.setTotalV(OrpConstants.ZERO_CHAR_2);
        return detailData;
    }

    /**
     * empty 酒店, 日本站数据取整
     */
    protected ReportDataDetailData mapJPHotelEmptyData() {
        ReportDataDetailData detailData = new ReportDataDetailData();
        detailData.setRoomPrice(OrpConstants.ZERO_CHAR);
        detailData.setCouponAmount(OrpConstants.ZERO_CHAR);
        detailData.setServiceFee(OrpConstants.ZERO_CHAR);
        detailData.setHotelPostServiceFee(OrpConstants.ZERO_CHAR);
        detailData.setTotalV(OrpConstants.ZERO_CHAR);
        return detailData;
    }

    /**
     * 明细- 机票/酒店-空集合-补数据
     */
    protected ReprotBodyData mapEmptyInfoReport(String dimension) {
        ReportDataDetailData detailData = new ReportDataDetailData();
        // 协议
        detailData.setAgreementV(OrpConstants.ZERO_CHAR);
        detailData.setAgreementYoy(OrpConstants.ZERO_CHAR);
        detailData.setAgreementMom(OrpConstants.ZERO_CHAR);
        // 非协议
        detailData.setUnAgreementV(OrpConstants.ZERO_CHAR);
        detailData.setUnAgreementYoy(OrpConstants.ZERO_CHAR);
        detailData.setUnAgreementMom(OrpConstants.ZERO_CHAR);
        // 国内
        detailData.setDomesticV(OrpConstants.ZERO_CHAR);
        detailData.setDomesticYoy(OrpConstants.ZERO_CHAR);
        detailData.setDomesticMom(OrpConstants.ZERO_CHAR);
        // 国际
        detailData.setInternationalV(OrpConstants.ZERO_CHAR);
        detailData.setInternationalYoy(OrpConstants.ZERO_CHAR);
        detailData.setInternationalMom(OrpConstants.ZERO_CHAR);
        // 总计
        detailData.setTotalV(OrpConstants.ZERO_CHAR);
        detailData.setTotalYoy(OrpConstants.ZERO_CHAR);
        detailData.setTotalMom(OrpConstants.ZERO_CHAR);
        return new ReprotBodyData(dimension, detailData);
    }

    /**
     * 用车-金额/票张
     *
     * @param dimension
     * @return
     */
    protected ReprotBodyData mapCarAmountTicketEmptyData(String dimension) {
        ReportDataDetailData detailData = new ReportDataDetailData();
        detailData.setTakeCarV(OrpConstants.ZERO_CHAR);
        detailData.setTakeCarYoy(OrpConstants.ZERO_CHAR);
        detailData.setTakeCarMom(OrpConstants.ZERO_CHAR);
        detailData.setTransferV(OrpConstants.ZERO_CHAR);
        detailData.setTransferYoy(OrpConstants.ZERO_CHAR);
        detailData.setTransferMom(OrpConstants.ZERO_CHAR);
        detailData.setRentalCarV(OrpConstants.ZERO_CHAR);
        detailData.setRentalCarYoy(OrpConstants.ZERO_CHAR);
        detailData.setRentalCarMom(OrpConstants.ZERO_CHAR);
        detailData.setCharterCarV(OrpConstants.ZERO_CHAR);
        detailData.setCharterCarYoy(OrpConstants.ZERO_CHAR);
        detailData.setCharterCarMom(OrpConstants.ZERO_CHAR);
        detailData.setTotalV(OrpConstants.ZERO_CHAR);
        detailData.setTotalYoy(OrpConstants.ZERO_CHAR);
        detailData.setTotalMom(OrpConstants.ZERO_CHAR);
        return new ReprotBodyData(dimension, detailData);
    }

    /**
     * 用车-详情
     *
     * @param dimension
     * @return
     */
    protected ReprotBodyData mapCarEmptyData(String dimension) {
        ReportDataDetailData detailData = new ReportDataDetailData();
        detailData.setBasicFee(OrpConstants.ZERO_CHAR_2);
        detailData.setServiceFee(OrpConstants.ZERO_CHAR_2);
        detailData.setRefundAmount(OrpConstants.ZERO_CHAR_2);
        detailData.setTotalV(OrpConstants.ZERO_CHAR_2);
        return new ReprotBodyData(dimension, detailData);
    }

    /**
     * 火车票-费用维度空数据
     */
    protected TrainDataDetailData mapTrainEmptyData() {
        TrainDataDetailData dataDetailData = new TrainDataDetailData();
        dataDetailData.setAfteraftertaketicketfee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setAfterchangeservicefee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setAfterServiceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setAftertaketicketfee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setChangebalance(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setDealChangeServiceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setSendTicketFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setGrabServiceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setInsuranceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setPaperTicketFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setRefundTicketFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setServiceFee(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setTicketPrice(OrpConstants.ZERO_CHAR_2);
        dataDetailData.setTotalV(OrpConstants.ZERO_CHAR_2);
        return dataDetailData;
    }

    /**
     * 火车票-明细空数据
     */
    protected ReportDataDetailData mapTrainInfoEmptyData() {
        ReportDataDetailData dataDetailData = new ReportDataDetailData();
        dataDetailData.setTotalV(OrpConstants.ZERO_CHAR);
        dataDetailData.setTotalYoy(OrpConstants.ZERO_CHAR);
        dataDetailData.setTotalMom(OrpConstants.ZERO_CHAR);
        return dataDetailData;
    }

    protected TrainDataInfoDetailData mapTrainTicketInfoEmptyData() {
        TrainDataInfoDetailData dataDetailData = new TrainDataInfoDetailData();
        dataDetailData.setTotalV(OrpConstants.ZERO_CHAR);
        dataDetailData.setTotalYoy(OrpConstants.ZERO_CHAR);
        dataDetailData.setTotalMom(OrpConstants.ZERO_CHAR);
        return dataDetailData;
    }

    /**
     * 火车票-各个费用维度
     */
    protected ReprotTrainBodyData mapTrainAllBodyData(String dimension, CurrentYoyMomData<OnlineReportTrainDto> gList, boolean isJP) {
        TrainDataDetailData detailData = new TrainDataDetailData();
        AtomicDouble afteraftertaketicketfee = new AtomicDouble();
        AtomicDouble afterchangeservicefee = new AtomicDouble();
        AtomicDouble afterServiceFee = new AtomicDouble();
        AtomicDouble aftertaketicketfee = new AtomicDouble();
        AtomicDouble changebalance = new AtomicDouble();
        AtomicDouble dealChangeServiceFee = new AtomicDouble();
        AtomicDouble deliverFee = new AtomicDouble();
        AtomicDouble grabServiceFee = new AtomicDouble();
        AtomicDouble insuranceFee = new AtomicDouble();
        AtomicDouble paperTicketFee = new AtomicDouble();
        AtomicDouble refundTicketFee = new AtomicDouble();
        AtomicDouble serviceFee = new AtomicDouble();
        AtomicDouble ticketPrice = new AtomicDouble();
        AtomicDouble totalPrice = new AtomicDouble();
        gList.getCurrent().forEach(data -> {
            afteraftertaketicketfee.set(OrpCommonUtils.addDouble(new BigDecimal(afteraftertaketicketfee.get()),
                    data.getAfteraftertaketicketfee()));
            afterchangeservicefee.set(
                    OrpCommonUtils.addDouble(new BigDecimal(afterchangeservicefee.get()), data.getAfterchangeservicefee()));
            afterServiceFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(afterServiceFee.get()), data.getAfterServiceFee()));
            aftertaketicketfee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(aftertaketicketfee.get()), data.getAftertaketicketfee()));
            changebalance.set(OrpCommonUtils.addDouble(new BigDecimal(changebalance.get()), data.getChangebalance()));
            dealChangeServiceFee.set(
                    OrpCommonUtils.addDouble(new BigDecimal(dealChangeServiceFee.get()), data.getDealChangeServiceFee()));
            deliverFee.set(OrpCommonUtils.addDouble(new BigDecimal(deliverFee.get()), data.getDeliverFee()));
            grabServiceFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(grabServiceFee.get()), data.getGrabServiceFee()));
            insuranceFee.set(OrpCommonUtils.addDouble(new BigDecimal(insuranceFee.get()), data.getInsuranceFee()));
            paperTicketFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(paperTicketFee.get()), data.getPaperTicketFee()));
            refundTicketFee
                    .set(OrpCommonUtils.addDouble(new BigDecimal(refundTicketFee.get()), data.getRefundTicketFee()));
            serviceFee.set(OrpCommonUtils.addDouble(new BigDecimal(serviceFee.get()), data.getServiceFee()));
            ticketPrice.set(OrpCommonUtils.addDouble(new BigDecimal(ticketPrice.get()), data.getTicketPrice()));
            totalPrice.set(OrpCommonUtils.addDouble(new BigDecimal(totalPrice.get()), data.getRealPay()));
        });
        // 精度,日本站金额取整
        int precision = isJP ? OrpConstants.ZERO : OrpConstants.TWO;
        detailData.setAfteraftertaketicketfee(OrpCommonUtils
                .precisionConversion(new BigDecimal(afteraftertaketicketfee.get()), precision).toString());
        detailData.setAfterchangeservicefee(OrpCommonUtils
                .precisionConversion(new BigDecimal(afterchangeservicefee.get()), precision).toString());
        detailData.setAfterServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(afterServiceFee.get()), precision).toString());
        detailData.setAftertaketicketfee(
                OrpCommonUtils.precisionConversion(new BigDecimal(aftertaketicketfee.get()), precision).toString());
        detailData.setChangebalance(
                OrpCommonUtils.precisionConversion(new BigDecimal(changebalance.get()), precision).toString());
        detailData.setDealChangeServiceFee(OrpCommonUtils
                .precisionConversion(new BigDecimal(dealChangeServiceFee.get()), precision).toString());
        detailData.setSendTicketFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(deliverFee.get()), precision).toString());
        detailData.setGrabServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(grabServiceFee.get()), precision).toString());
        detailData.setInsuranceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(insuranceFee.get()), precision).toString());
        detailData.setPaperTicketFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(paperTicketFee.get()), precision).toString());
        detailData.setRefundTicketFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(refundTicketFee.get()), precision).toString());
        detailData.setServiceFee(
                OrpCommonUtils.precisionConversion(new BigDecimal(serviceFee.get()), precision).toString());
        detailData.setTicketPrice(
                OrpCommonUtils.precisionConversion(new BigDecimal(ticketPrice.get()), precision).toString());
        detailData.setTotalV(
                OrpCommonUtils.precisionConversion(new BigDecimal(totalPrice.get()), precision).toString());
        if (StringUtils.isBlank(dimension)) {
            dimension = gList.getMonth();
        }
        return new ReprotTrainBodyData(dimension, detailData);
    }

    @Data
    public static class CurrentYoyMomData<T> {

        String month;

        List<T> current;

        List<T> yoy;

        List<T> mom;

    }

}
