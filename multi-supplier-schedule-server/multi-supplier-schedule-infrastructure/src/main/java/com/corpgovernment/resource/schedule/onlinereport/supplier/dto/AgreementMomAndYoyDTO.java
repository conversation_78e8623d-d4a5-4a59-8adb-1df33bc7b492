package com.corpgovernment.resource.schedule.onlinereport.supplier.dto;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-09-06 14:14
 * @desc
 */
@Data
public class AgreementMomAndYoyDTO {

    // 当前成交净价
    @Column(name = "currentTotalPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal currentTotalPrice;

    // 环比成交净价
    @Column(name = "momTotalPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal momTotalPrice;

    // 同比成交净价
    @Column(name = "yoyTotalPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal yoyTotalPrice;

    // 票张数
    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    // 机票均价
    @Column(name = "avgPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgPrice;

}
