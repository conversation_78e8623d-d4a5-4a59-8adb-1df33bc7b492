package com.corpgovernment.resource.schedule.usertemplate.mysql.entity;

import java.util.Date;

public class CorpReportLibIndicatorConfigDo {
    private Long id;

    private String reportCardNo;

    private String extCondition;

    private String extConditionDesc;

    private String extFieldListJson;

    private String createUser;

    private String changeUser;

    private Boolean isDel;

    private Date datachangeCreatetime;

    private Date datachangeLasttime;

    public CorpReportLibIndicatorConfigDo(String reportCardNo, String extCondition, String extConditionDesc, String extFieldListJson, String createUser, String changeUser) {
        this.reportCardNo = reportCardNo;
        this.extCondition = extCondition;
        this.extConditionDesc = extConditionDesc;
        this.extFieldListJson = extFieldListJson;
        this.createUser = createUser;
        this.changeUser = changeUser;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReportCardNo() {
        return reportCardNo;
    }

    public void setReportCardNo(String reportCardNo) {
        this.reportCardNo = reportCardNo == null ? null : reportCardNo.trim();
    }

    public String getExtCondition() {
        return extCondition;
    }

    public void setExtCondition(String extCondition) {
        this.extCondition = extCondition == null ? null : extCondition.trim();
    }

    public String getExtConditionDesc() {
        return extConditionDesc;
    }

    public void setExtConditionDesc(String extConditionDesc) {
        this.extConditionDesc = extConditionDesc == null ? null : extConditionDesc.trim();
    }

    public String getExtFieldListJson() {
        return extFieldListJson;
    }

    public void setExtFieldListJson(String extFieldListJson) {
        this.extFieldListJson = extFieldListJson == null ? null : extFieldListJson.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public String getChangeUser() {
        return changeUser;
    }

    public void setChangeUser(String changeUser) {
        this.changeUser = changeUser == null ? null : changeUser.trim();
    }

    public Boolean getIsDel() {
        return isDel;
    }

    public void setIsDel(Boolean isDel) {
        this.isDel = isDel;
    }

    public Date getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Date datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Date getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Date datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }
}