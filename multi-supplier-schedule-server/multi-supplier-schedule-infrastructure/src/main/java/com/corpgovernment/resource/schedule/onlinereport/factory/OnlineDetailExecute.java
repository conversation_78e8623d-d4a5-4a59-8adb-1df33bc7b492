package com.corpgovernment.resource.schedule.onlinereport.factory;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDetailRequest;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.dto.ReportCurrentYoyMomMapping;
import com.corpgovernment.resource.schedule.onlinereport.enums.DetailReportTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.ReportDetailHeaderEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.service.factory.test
 * @description:
 * @author: md_wang
 * @create: 2021-11-08 10:42
 **/
public interface OnlineDetailExecute {

    /**
     * query db record
     *
     * @param request
     * @return
     * @throws Exception
     */
    OnlineReportData queryDetailRecord(OnlineReportDetailRequest request) throws Exception;

    /**
     * init report header
     *
     * @param reportTypeEnum
     * @param bu
     * @param lang           语言环境
     * @return
     */
    default Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> mapperHeader(DetailReportTypeEnum reportTypeEnum, QueryReportBuTypeEnum bu, String lang) {
        return Maps.newHashMap();
    }

    /**
     * init report header (日本站)
     *
     * @param reportTypeEnum
     * @param bu
     * @param lang           语言环境
     * @return
     */
    default Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> mapperJPHeader(DetailReportTypeEnum reportTypeEnum, QueryReportBuTypeEnum bu, String lang, boolean isBlueSpace) {
        return Maps.newHashMap();
    }

    /**
     * 当期/同比/环比-时间关系
     *
     * @param request
     * @return
     */
    default Map<String, ReportCurrentYoyMomMapping> currentYoyMomMapping(OnlineDetailRequestDto request) {
        List<String> currentMonth = currentDateList(request);
        List<String> yoyMonth = yoyDateList(request);
        List<String> momMonth = momDateList(request);
        Map<String, ReportCurrentYoyMomMapping> currentYoyMomMap = Maps.newHashMap();
        currentMonth.forEach(t -> {
            int index = currentMonth.indexOf(t);
            currentYoyMomMap.put(t, new ReportCurrentYoyMomMapping(yoyMonth.get(index), momMonth.get(index)));
        });
        return currentYoyMomMap;
    }

    /**
     * 当期时间List
     *
     * @param request
     * @return
     */
    default List<String> currentDateList(OnlineDetailRequestDto request) {
        return OrpDateTimeUtils.getMonthBetween(request.getStartTime(), request.getEndTime());
    }

    /**
     * 同比时间List
     *
     * @param request
     * @return
     */
    default List<String> yoyDateList(OnlineDetailRequestDto request) {
        return OrpDateTimeUtils.getMonthBetween(request.getYoyStartTime(), request.getYoyEndTime());
    }

    /**
     * 环比时间List
     *
     * @param request
     * @return
     */
    default List<String> momDateList(OnlineDetailRequestDto request) {
        return OrpDateTimeUtils.getMonthBetween(request.getMomStartTime(), request.getMomEndTime());
    }
}
