package com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.*;
import com.corpgovernment.resource.schedule.onlinereport.AbstractDeptDetailCommonBiz;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.carbons.CarbonsDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.CarbonsDeptDetailEnum;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto.CarbonsAggDTO;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto.CarbonsTypeDTO;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto.CarbonsViewDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-09-21 11:23
 * @desc 机票碳排放
 */
@Service
public class FltCarbonsBiz extends AbstractDeptDetailCommonBiz {

    @Autowired
    private CarbonsDaoImpl carbonsDaoImpl;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;


    
    public CarbonsViewInfo queryCarbons(OnlineReportCarbonsViewRequest request) throws Exception {

        CarbonsViewInfo carbonsViewBO = new CarbonsViewInfo();
        QueryReportAggDateDimensionEnum dateDimensionEnum = request.getDateDimension();
        BaseQueryCondition baseQueryCondition = request.getBasecondition();
        Map extMap = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        // supportCustomTimeCondition为T的时候支持自定义时间查询，即根据request参数传进来的时间条件查询
        if (!StringUtils.equalsIgnoreCase((String) extMap.get("supportCustomTimeCondition"),"T")) {
            List<String> timeInterval = getTimeInterval();
            baseQueryCondition.setStartTime(timeInterval.get(0));
            if (dateDimensionEnum == QueryReportAggDateDimensionEnum.month){
                baseQueryCondition.setStartTime(timeInterval.get(1));
            }
            baseQueryCondition.setEndTime(timeInterval.get(2));
        }
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(baseQueryCondition);
        List<CarbonsViewDTO> carbonsViewDTOList = (List<CarbonsViewDTO>) carbonsDaoImpl.queryCarbons(baseQueryConditionDto, CarbonsViewDTO.class, request.getProductType());
        if (CollectionUtils.isNotEmpty(carbonsViewDTOList)) {
            CarbonsViewDTO carbonsViewDTO = carbonsViewDTOList.get(0);
            carbonsViewBO.setTotalCarbons(OrpReportUtils.formatBigDecimal(carbonsViewDTO.getSumCarbons()));
            carbonsViewBO.setAvgCarbons(OrpReportUtils.formatBigDecimal(carbonsViewDTO.getAvgCarbons()));
            carbonsViewBO.setMom(OrpReportUtils.divideWithPercent(carbonsViewDTO.getSumCarbons().subtract(carbonsViewDTO.getMomSumCarbons()), carbonsViewDTO.getMomSumCarbons()));
            carbonsViewBO.setYoy(OrpReportUtils.divideWithPercent(carbonsViewDTO.getSumCarbons().subtract(carbonsViewDTO.getYoySumCarbons()), carbonsViewDTO.getYoySumCarbons()));
        }
        return carbonsViewBO;
    }

    
    public List<CarbonsTypeInfo> queryCarbonType(OnlineReportCarbonsViewRequest request) throws Exception {

        List result = new ArrayList();
        BaseQueryCondition baseQueryCondition = request.getBasecondition();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(baseQueryCondition);
        List<CarbonsTypeDTO> list = (List<CarbonsTypeDTO>) carbonsDaoImpl.queryCarbonType(baseQueryConditionDto, CarbonsTypeDTO.class, request.getProductType());
        if (CollectionUtils.isNotEmpty(list)) {
            for (CarbonsTypeDTO carbonsTypeDTO : list){
                CarbonsTypeInfo carbonsTypeInfo = new CarbonsTypeInfo();
                carbonsTypeInfo.setTotalTpms(OrpReportUtils.formatBigDecimal(carbonsTypeDTO.getSumTpms()));
                carbonsTypeInfo.setTotalCarbons(OrpReportUtils.formatBigDecimal(carbonsTypeDTO.getSumCarbons()));
                carbonsTypeInfo.setCarbonType(carbonsTypeDTO.getCarbonType());
                result.add(carbonsTypeInfo);
            }
        }
        return result;
    }

    
    public List<CarbonsTopInfo> queryTopCarbonsDept(OnlineReportCarbonsTopRequest request) throws Exception {

        List<CarbonsTopInfo> carbonsAggBOList = new ArrayList<>();
        QueryReportAggDateDimensionEnum dateDimensionEnum = request.getDateDimension();
        BaseQueryCondition baseQueryCondition = request.getBasecondition();
        Map extMap = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        // supportCustomTimeCondition为T的时候支持自定义时间查询，即根据request参数传进来的时间条件查询
        if (!StringUtils.equalsIgnoreCase((String) extMap.get("supportCustomTimeCondition"),"T")){
            List<String> timeInterval = getTimeInterval();
            baseQueryCondition.setStartTime(timeInterval.get(0));
            if (dateDimensionEnum == QueryReportAggDateDimensionEnum.month){
                baseQueryCondition.setStartTime(timeInterval.get(1));
            }
            baseQueryCondition.setEndTime(timeInterval.get(2));
        }
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<CarbonsAggDTO> carbonsAggDTOList = (List<CarbonsAggDTO>) carbonsDaoImpl.queryCarbonsTop5Dept(baseQueryConditionDto, CarbonsAggDTO.class, request.getProductType(),
                request.getDimensionType(), request.getAnalysisObjectEnum(), BizUtils.initPager(request.getPage()));
        Optional.ofNullable(carbonsAggDTOList).orElse(new ArrayList<>()).forEach(i->{
            CarbonsTopInfo carbonsAggBO = new CarbonsTopInfo();
            carbonsAggBO.setDim(i.getDim());
            carbonsAggBO.setTotalCarbons(OrpReportUtils.formatBigDecimal(i.getSumCarbons()));
            carbonsAggBO.setAvgCarbons(OrpReportUtils.formatBigDecimal(i.getAvgCarbons()));
            carbonsAggBO.setCarbonsPercent(OrpReportUtils.formatBigDecimal(i.getCarbonsPercent()));
            carbonsAggBOList.add(carbonsAggBO);
        });
        return carbonsAggBOList;
    }

    public List<Map> queryCarbonsDeptDetail(OnlineReportCarbonsDeptDetailRequest request) throws Exception {

        BaseQueryCondition baseQueryCondition = request.getBasecondition();
        List<String> timeInterval = getTimeInterval();
        baseQueryCondition.setStartTime(timeInterval.get(0));
        baseQueryCondition.setEndTime(timeInterval.get(2));
        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(baseQueryCondition);
        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        Pager pager = request.getPage();
        List<Map> result = carbonsDaoImpl.queryCarbonsDeptDetail(baseQueryConditionDTO, request.getProductType(), BizUtils.initPagerStartOne(pager),
                analysisObjectEnum, request.getDimensionType(), timeInterval.get(1));
        fomratResultData(result);
        return result;
    }
    /**
     * 统一格式化结果数据
     *
     * @param list
     */
    protected void fomratResultData(List<Map> list) {
        int i = 0;
        for (Map map : list) {
            i++;
            for (Object key : map.keySet()) {
                CarbonsDeptDetailEnum rcStatisticalsEnum = getStaticalByKey((String) key);
                if (rcStatisticalsEnum != null) {
                    BigDecimal target = OrpReportUtils.formatBigDecimal(
                            Objects.isNull(map.get(key)) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(map.get(key))),
                            rcStatisticalsEnum.getNum(), true);
                    map.put(key, rcStatisticalsEnum.isPercent() ? target.toString().concat("%") : target);
                }
            }
        }
    }

    protected CarbonsDeptDetailEnum getStaticalByKey(String key) {
        try {
            return CarbonsDeptDetailEnum.valueOf((String) key);
        } catch (Exception e) {

        }
        return null;
    }

    /**
     * 获取标题
     *
     * @return
     */
    public List<HeaderKeyValDataType> getCarbonsDeptDetailHearder(OnlineReportCarbonsDeptDetailRequest request) {
        String lang = request.getLang();
        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        int aggType = request.getDimensionType().getValue();
        List<HeaderKeyValDataType> mapDimensions = getDimesionsDesc(analysisObjectEnum, lang);
        List<HeaderKeyValDataType> mapStaticals = getStaticalsDes(getStatisticalList(aggType), lang);
        mapDimensions.addAll(mapStaticals);
        return mapDimensions;
    }

    /**
     * 获取统计项标题
     *
     * @param statisticalStringList
     * @return
     */
    private List<HeaderKeyValDataType> getStaticalsDes(List<CarbonsDeptDetailEnum> statisticalStringList, String lang) {
        List<HeaderKeyValDataType> result = new ArrayList<>();
        for (CarbonsDeptDetailEnum statisticalEnum : statisticalStringList) {
            HeaderKeyValDataType headerKeyValMap = new HeaderKeyValDataType();
            headerKeyValMap.setHeaderKey(statisticalEnum.toString());
            headerKeyValMap.setHeaderValue(SharkUtils.get(statisticalEnum.getSharkKey(), lang));
            headerKeyValMap.setDataType(statisticalEnum.getDataType());
            result.add(headerKeyValMap);
        }
        return result;
    }

    public List<CarbonsDeptDetailEnum> getStatisticalList(int aggType) {
        return CarbonsDeptDetailEnum.getStaticalsByAggType(aggType);
    }


    
    public int count(OnlineReportCarbonsDeptDetailRequest request) throws Exception {

        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        BaseQueryCondition baseQueryCondition = request.getBasecondition();
        List<String> timeInterval = getTimeInterval();
        baseQueryCondition.setStartTime(timeInterval.get(0));
        baseQueryCondition.setEndTime(timeInterval.get(2));
        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(baseQueryCondition);
        return carbonsDaoImpl.countCarbonsDeptDetail(baseQueryConditionDTO, request.getProductType(), analysisObjectEnum);
    }

    /**
     * 时间区间
     * @return
     */
    private List<String> getTimeInterval(){
        List<String> timeInterval = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        String time = OrpDateTimeUtils.formatDateTimeToStr(calendar.getTime(), OrpDateTimeUtils.DEFAULT_DATE);
        // 当年开始日期
        timeInterval.add(String.format("%d-01-01", calendar.get(Calendar.YEAR)));
        // 当月开始日期
        timeInterval.add(OrpDateTimeUtils.firstDayOfMonth(time));
        // 当月/年结束日期
        timeInterval.add(time);
        return timeInterval;
    }
}
