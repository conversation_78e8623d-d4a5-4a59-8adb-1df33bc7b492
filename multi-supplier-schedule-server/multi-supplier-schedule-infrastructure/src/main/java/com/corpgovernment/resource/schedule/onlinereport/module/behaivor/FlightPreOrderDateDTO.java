package com.corpgovernment.resource.schedule.onlinereport.module.behaivor;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class FlightPreOrderDateDTO {

    @Column(name = "preOrderDateRange")
    @Type(value = Types.VARCHAR)
    private String orderDateRange;

    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    @Column(name = "totalEconomyPrice")
    @Type(value = Types.DOUBLE)
    private Double totalEconomyPrice;

    @Column(name = "totalEconomyPriceRate")
    @Type(value = Types.DOUBLE)
    private Double totalEconomyPriceRate;

    @Column(name = "totalEconomyQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalEconomyQuantity;

    @Column(name = "totalPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalPrice;

    @Column(name = "totalDiscount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalDiscount;

    @Column(name = "totalRefundtkt")
    @Type(value = Types.INTEGER)
    private Integer totalRefundtkt;

    @Column(name = "totalRebooktkt")
    @Type(value = Types.INTEGER)
    private Integer totalRebooktkt;

    @Column(name = "totalFullfaretkt")
    @Type(value = Types.INTEGER)
    private Integer totalFullfaretkt;

    @Column(name = "totalWithoutFullQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalWithoutFullQuantity;

    @Column(name = "totalWithoutFullDiscount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalWithoutFullDiscount;

    @Column(name = "totalPre4Quantiy")
    @Type(value = Types.INTEGER)
    private Integer totalPre4Quantiy;

    @Column(name = "totalPre4Discount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalPre4Discount;

    @Column(name = "totalOrdertkt")
    @Type(value = Types.INTEGER)
    private Integer totalOrdertkt;
}
