package com.corpgovernment.resource.schedule.usertemplate.gateway;

import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.CustomContentInfoBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.CustomTemplateBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.dto.orderdetail.usertemplate.QueryCustomTemplateDto;
import com.corpgovernment.resource.schedule.onlinereport.utils.PageOperateUtils;
import com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateContentDetailDo;
import com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateDo;
import com.corpgovernment.resource.schedule.usertemplate.mysql.mapper.UserCustomTemplateContentDetailDTOMapper;
import com.corpgovernment.resource.schedule.usertemplate.mysql.mapper.UserCustomTemplateDTOMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@Service
@Slf4j
public class UserCustomTemplateBusiness {
    @Resource
    private UserCustomTemplateContentDetailDTOMapper userCustomTemplateContentDetailDTOMapper;
    @Resource
    private UserCustomTemplateDTOMapper userCustomTemplateDTOMapper;

    private static final int PAGE_SIZE = 50;

    @Transactional(rollbackFor = Exception.class)
    public void insertUserCustomTemplate(UserCustomTemplateDo templateDTO,
                                         List<UserCustomTemplateContentDetailDo> templateConditionInfoDTOS,
                                         List<UserCustomTemplateContentDetailDo> templateColumnInfoDTOS) {
        userCustomTemplateDTOMapper.insertSelective(templateDTO);
        // 插入自定义列
        if (StringUtils.isNotEmpty(templateDTO.getCustomColumnNo()) && CollectionUtils.isNotEmpty(templateColumnInfoDTOS)) {
            PageOperateUtils.pageInsert(templateColumnInfoDTOS, PAGE_SIZE, subList ->
                    userCustomTemplateContentDetailDTOMapper.insertBatchSelective(subList));
        }
        // 插入自定义条件
        if (StringUtils.isNotEmpty(templateDTO.getCustomConditionNo()) && CollectionUtils.isNotEmpty(templateConditionInfoDTOS)) {
            PageOperateUtils.pageInsert(templateConditionInfoDTOS, PAGE_SIZE, subList ->
                    userCustomTemplateContentDetailDTOMapper.insertBatchSelective(subList));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUserCustomTemplate(UserCustomTemplateDo templateDTO,
                                         List<UserCustomTemplateContentDetailDo> templateConditionInfoDTOS,
                                         List<UserCustomTemplateContentDetailDo> templateColumnInfoDTOS) {
        userCustomTemplateDTOMapper.updateByTemplateNoSelective(templateDTO);
        // 更新自定义列
        if (StringUtils.isNotEmpty(templateDTO.getCustomColumnNo()) && CollectionUtils.isNotEmpty(templateColumnInfoDTOS)) {
            userCustomTemplateContentDetailDTOMapper.deleteByContentNo(templateDTO.getCustomColumnNo());
            PageOperateUtils.pageInsert(templateColumnInfoDTOS, PAGE_SIZE, subList ->
                    userCustomTemplateContentDetailDTOMapper.insertBatchSelective(subList));
        }
        // 更新自定义条件
        if (StringUtils.isNotEmpty(templateDTO.getCustomConditionNo()) && CollectionUtils.isNotEmpty(templateConditionInfoDTOS)) {
            userCustomTemplateContentDetailDTOMapper.deleteByContentNo(templateDTO.getCustomConditionNo());
            PageOperateUtils.pageInsert(templateConditionInfoDTOS, PAGE_SIZE, subList ->
                    userCustomTemplateContentDetailDTOMapper.insertBatchSelective(subList));
        }
    }

    public void deleteUserCustomTemplate(List<String> templateNos) {
        userCustomTemplateDTOMapper.deleteBatchTemplateNos(templateNos);
    }

    public List<CustomTemplateBo> selectByUidAndTemplateNo(QueryCustomTemplateDto requestType) {
        List<UserCustomTemplateDo> userCustomTemplateDTOS = userCustomTemplateDTOMapper.selectByUidAndTemplateNo(
                requestType.getUid(), requestType.getTemplateNo(), requestType.getReportKey());
        List<CustomTemplateBo> res = Lists.newArrayListWithCapacity(userCustomTemplateDTOS.size());
        if (CollectionUtils.isNotEmpty(userCustomTemplateDTOS)) {
            for (UserCustomTemplateDo userCustomTemplateDTO : userCustomTemplateDTOS) {
                String customColumnNo = userCustomTemplateDTO.getCustomColumnNo();
                String customConditionNo = userCustomTemplateDTO.getCustomConditionNo();
                List<UserCustomTemplateContentDetailDo> userCustomTemplateContentDetailDTOS =
                        userCustomTemplateContentDetailDTOMapper.selectByContentNolist(Lists.newArrayList(customColumnNo, customConditionNo));
                Map<String, List<UserCustomTemplateContentDetailDo>> contentNoDetailMap =
                        Optional.ofNullable(userCustomTemplateContentDetailDTOS).map(Collection::stream).map(stream ->
                                        stream.collect(Collectors.groupingBy(UserCustomTemplateContentDetailDo::getContentNo)))
                                .orElse(Maps.newHashMap());
                List<UserCustomTemplateContentDetailDo> columnInfoDTOS = contentNoDetailMap.getOrDefault(customColumnNo, Lists.newArrayList());
                List<UserCustomTemplateContentDetailDo> conditionInfoDTOS = contentNoDetailMap.getOrDefault(customConditionNo, Lists.newArrayList());
                CustomTemplateBo templateBo = convertBo(userCustomTemplateDTO, columnInfoDTOS, conditionInfoDTOS);
                res.add(templateBo);
            }
        }
        return res;
    }

    private CustomTemplateBo convertBo(UserCustomTemplateDo userCustomTemplateDTO, List<UserCustomTemplateContentDetailDo> columnInfoDTOS, List<UserCustomTemplateContentDetailDo> conditionInfoDTOS) {
            CustomTemplateBo customTemplateBo = new CustomTemplateBo();
        customTemplateBo.setTemplateNo(userCustomTemplateDTO.getTemplateNo());
        customTemplateBo.setTemplateName(userCustomTemplateDTO.getTemplateName());
        customTemplateBo.setUid(userCustomTemplateDTO.getUid());
        customTemplateBo.setReportKey(userCustomTemplateDTO.getReportKey());
        customTemplateBo.setCustomColumnNo(userCustomTemplateDTO.getCustomColumnNo());
        customTemplateBo.setCustomConditionNo(userCustomTemplateDTO.getCustomConditionNo());
        customTemplateBo.setCustomColumnInfoBoList(columnInfoDTOS.stream().map(this::convertColumnInfo).collect(Collectors.toList()));
        customTemplateBo.setCustomConditionInfoBoList(conditionInfoDTOS.stream().map(this::convertConditionInfo).collect(Collectors.toList()));
        return customTemplateBo;
    }

    private CustomContentInfoBo convertConditionInfo(UserCustomTemplateContentDetailDo userCustomTemplateContentDetailDo) {
        CustomContentInfoBo customContentInfoBo = new CustomContentInfoBo();
        customContentInfoBo.setFliedKey(userCustomTemplateContentDetailDo.getFliedKey());
        customContentInfoBo.setFliedName(userCustomTemplateContentDetailDo.getFliedName());
        customContentInfoBo.setFliedValue(userCustomTemplateContentDetailDo.getFliedValue());
        customContentInfoBo.setSort(userCustomTemplateContentDetailDo.getSort());
        return customContentInfoBo;
    }

    private CustomContentInfoBo convertColumnInfo(UserCustomTemplateContentDetailDo userCustomTemplateContentDetailDo) {
        CustomContentInfoBo customContentInfoBo = new CustomContentInfoBo();
        customContentInfoBo.setFliedKey(userCustomTemplateContentDetailDo.getFliedKey());
        customContentInfoBo.setFliedName(userCustomTemplateContentDetailDo.getFliedName());
        customContentInfoBo.setFliedValue(userCustomTemplateContentDetailDo.getFliedValue());
        customContentInfoBo.setSort(userCustomTemplateContentDetailDo.getSort());
        return customContentInfoBo;
    }

    public boolean checkTemplateNameIfDuplicated(String templateName, String uid, String reportKey) {
        return userCustomTemplateDTOMapper.countByTemplateNameAndUid(templateName, uid, reportKey) > 0;
    }

    public boolean checkExistTemplate(String templateNo) {
        return userCustomTemplateDTOMapper.countByTemplateNo(templateNo) > 0;
    }
}
