package com.corpgovernment.resource.schedule.onlinereport.utils;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.utils
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-03 19:26
 **/
public class OrpCollectionUtils {

    static final Set<Collector.Characteristics> CH_NOID = Collections.emptySet();

    /**
     * list to map
     */
    public static <T, R> Map<R, T> listToMap(List<T> list, Function<T, R> function) {
        return Optional.ofNullable(list).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).collect(Collectors.toMap(function, Function.identity(), (x1, x2) -> x1));
    }

    /**
     * list<Entity> 转map<RK,Entity2>
     *
     * @param list
     * @param functionKey key
     * @param functionV   v
     * @param <T>
     * @param <RK>
     * @param <RV>
     * @return
     */
    public static <T, RK, RV> Map<RK, RV> listToNewItemMap(List<T> list, Function<T, RK> functionKey, Function<T, RV> functionV) {
        return list.stream().filter(Objects::nonNull).collect(Collectors.toMap(functionKey, (entity) -> functionV.apply(entity)));
    }

    /**
     * list to list
     */
    public static <T, R> List<R> listToItemList(List<T> list, Function<T, R> function) {
        return Optional.ofNullable(list).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).map(function).collect(Collectors.toList());
    }

    /**
     * 倒叙
     */
    public static <T, U extends Comparable<? super U>> void sortDesc(List<T> list, Function<T, U> sortColName) {
        Optional.ofNullable(list).orElse(Lists.newArrayList()).sort(Comparator.comparing(sortColName).reversed());
    }

    /**
     * 正序
     */
    public static <T, U extends Comparable<? super U>> void sortAsc(List<T> list, Function<T, U> sortColName) {
        Optional.ofNullable(list).orElse(Lists.newArrayList()).sort(Comparator.comparing(sortColName));
    }

    /**
     * 排序，将排序字段为空的数据放在最后面
     */
    public static <T> void sortDescWithStrColNull(List<T> list, Function<? super T, String> sortColName) {
        Optional.ofNullable(list).orElse(Lists.newArrayList())
                .sort(Comparator.comparing(sortColName, Comparator.nullsFirst(String::compareTo)).reversed());
    }

    /**
     * 排序，将排序字段为空的数据放在最后面
     */
    public static <T> void sortDescWithIntColNull(List<T> list, Function<? super T, Integer> sortColName) {
        Optional.ofNullable(list).orElse(Lists.newArrayList())
                .sort(Comparator.comparing(sortColName, Comparator.nullsFirst(Integer::compareTo)).reversed());
    }

    /**
     * list group by to map
     */
    public static <T, R> Map<R, List<T>> listToGroupMap(List<T> list, Function<T, R> function) {
        if (CollectionUtils.isEmpty(list) || Objects.isNull(function)) {
            return Maps.newHashMap();
        }
        return list.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(function));
    }

    /**
     * 求和方法
     */
    public static <T> Collector<T, ?, BigDecimal> summingBigDecimal(OrpCollectorSelfInnovate.ToBigDecimalFunction<? super T> mapper) {
        return new OrpCollectorSelfInnovate<>(
                () -> new BigDecimal[]{BigDecimal.ZERO},
                (a, t) -> {
                    a[OrpConstants.ZERO] = a[OrpConstants.ZERO].add(Optional.ofNullable(mapper.applyAsBigDecimal(t)).orElse(BigDecimal.ZERO));
                },
                (a, b) -> {
                    a[OrpConstants.ZERO] = a[OrpConstants.ZERO].add(b[OrpConstants.ZERO]);
                    return a;
                },
                a -> a[OrpConstants.ZERO], CH_NOID);
    }

    /**
     * 求平均值
     */
    public static <T> Collector<T, ?, BigDecimal> averagingBigDecimal(OrpCollectorSelfInnovate.ToBigDecimalFunction<? super T> mapper, int newScale, int roundingMode) {
        return new OrpCollectorSelfInnovate<>(
                () -> new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO},
                (a, t) -> {
                    a[OrpConstants.ZERO] = a[OrpConstants.ZERO].add(Optional.ofNullable(mapper.applyAsBigDecimal(t)).orElse(BigDecimal.ZERO));
                    a[OrpConstants.ONE] = a[OrpConstants.ONE].add(BigDecimal.ONE);
                },
                (a, b) -> {
                    a[OrpConstants.ZERO] = a[OrpConstants.ZERO].add(b[OrpConstants.ZERO]);
                    return a;
                },
                a -> a[OrpConstants.ZERO].divide(a[1], BigDecimal.ROUND_HALF_UP).setScale(newScale, roundingMode), CH_NOID);
    }
}
