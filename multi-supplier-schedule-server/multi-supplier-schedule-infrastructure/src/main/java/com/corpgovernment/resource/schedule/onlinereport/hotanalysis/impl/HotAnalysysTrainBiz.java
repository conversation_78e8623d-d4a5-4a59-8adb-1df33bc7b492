package com.corpgovernment.resource.schedule.onlinereport.hotanalysis.impl;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportTypeionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportHotAanlysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendLegend;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendPoint;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.OnlineReportHotAnalysisDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.dto.OnlineReportHotAnalysisDto;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.dto.OnlineReportHotAnalysisRequestBo;
import com.corpgovernment.resource.schedule.onlinereport.enums.HotAnalysisEnum;
import com.corpgovernment.resource.schedule.onlinereport.hotanalysis.AbstractHotAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/25 15:17
 * @description：
 * @modified By：
 * @version: $
 */
@Component
public class HotAnalysysTrainBiz extends AbstractHotAnalysisBiz {

    @Autowired
    OnlineReportHotAnalysisDaoImpl onlineReportHotAnalysisDaoImpl;

    @Override
    
    public List<OnlineReportTrendPoint> trendBody(OnlineReportHotAanlysisRequest request) throws Exception {
        OnlineReportHotAnalysisRequestBo requestBo = mapper(request);
        List<OnlineReportHotAnalysisDto> queryData = onlineReportHotAnalysisDaoImpl.query(requestBo);
        QueryReportTypeionEnum queryReportTypeionEnum = request.getQueryType();
        if (queryReportTypeionEnum == QueryReportTypeionEnum.amount) {
            queryData.sort((o1, o2) -> o2.getSumAmount().subtract(o1.getSumAmount()).intValue());
        } else {
            queryData.sort((o1, o2) -> o2.getSumQuantity().subtract(o1.getSumQuantity()).intValue());
        }

        // 增加占比使用
        BigDecimal totalAmount = new BigDecimal("0");
        BigDecimal totalQuantity = new BigDecimal("0");
        for (OnlineReportHotAnalysisDto dto: queryData) {
            totalAmount = totalAmount.add(dto.getSumAmount());
            totalQuantity = totalQuantity.add(dto.getSumQuantity());
        }

        if (!isDownload(request)){
            // 非下载的时候如果传了topLimit则按topLimit取，否则取前20
            int topLimit = Optional.ofNullable(request.getTopLimit()).orElse(OrpConstants.TWENTY);
            queryData = queryData.size() > topLimit ? queryData.subList(OrpConstants.ZERO, topLimit) : queryData;
        }else {
            // 下载的时候如果传了topLimit则按topLimit取，否则取全部
            if (Objects.nonNull(request.getTopLimit()) && request.getTopLimit() > 0){
                queryData = queryData.size() > request.getTopLimit() ? queryData.subList(OrpConstants.ZERO, request.getTopLimit()) : queryData;
            }
        }

        List<Field> fields = Arrays.stream(OnlineReportHotAnalysisDto.class.getDeclaredFields())
                .filter(f -> !(f.getName().equals("dim")))
                .collect(Collectors.toList());
        BigDecimal finalTotalAmount = totalAmount;
        BigDecimal finalTotalQuantity = totalQuantity;
        return queryData.stream().map(o -> mapPoint(o, fields, finalTotalAmount, finalTotalQuantity)).collect(Collectors.toList());
    }

    @Override
    public List<OnlineReportTrendLegend> trendLegend(OnlineReportHotAanlysisRequest request) {
        List<OnlineReportTrendLegend> legends = new ArrayList<>();
        String lang = request.getLang();
        HotAnalysisEnum[] enums = {
                HotAnalysisEnum.TRAIN_AMOUNT, HotAnalysisEnum.TRAIN_QUANTITY,
                HotAnalysisEnum.TRAIN_AMOUNT_RATE, HotAnalysisEnum.TRAIN_QUANTITY_RATE
        };
        for (HotAnalysisEnum en : enums) {
            OnlineReportTrendLegend legend = new OnlineReportTrendLegend();
            legend.setName(SharkUtils.getHeaderVal(en.getNameKey(), lang));
            legend.setUnit(SharkUtils.getHeaderVal(en.getUnitKey(), lang));
            legend.setKey(en.getValueName());
            legends.add(legend);
        }
        return legends;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.train;
    }
}
