package com.corpgovernment.resource.schedule.onlinereport.clickhouse.base;

import com.corpgovernment.resource.schedule.onlinereport.common.SmConstant;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class TableInfo {

    /**
     * 需要被CKSqlParser处理的表信息
     */
    private static List<TableInfo> TABLEINFOS = Lists.newArrayList(
            new TableInfo("adm_index_one_trip_full_trip_id_generate_all", Lists.newArrayList("uidname", "user_name", "cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5")),
            new TableInfo("adm_index_one_trip_full_trip_id_generate_new", Lists.newArrayList("cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5")),
            new TableInfo("adm_index_price_summarry_all", Lists.newArrayList("cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5")),
            new TableInfo("adm_indexcar_price_detail_all", Lists.newArrayList("cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5")),
            new TableInfo("adm_indexflight_price_detail_all", Lists.newArrayList("cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5")),
            new TableInfo("adm_indexhotel_price_detail_all", Lists.newArrayList("cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5")),
            new TableInfo("adm_indextrain_price_detail_all", Lists.newArrayList("cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5")),
            new TableInfo("olrpt_indexcardownload_all", Lists.newArrayList("cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5")),
            new TableInfo("olrpt_indexflightdownload_all", Lists.newArrayList("cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5")),
            new TableInfo("olrpt_indexhoteldownload_all", Lists.newArrayList("cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5")),
            new TableInfo("olrpt_indextraindownload_all", Lists.newArrayList("cost_center1", "dept1", "dept2", "dept3", "dept4", "dept5"))
    );


    /**
     * 需要被CKSqlParser处理的表
     */
    public String name;

    /**
     * 值需要被加密的查询条件字段
     */
    public List<String> encryptFields;


    /**
     * 获取所有需要被CKSqlParser处理的表名称
     */
    public static Set<String> tableName() {
        return TABLEINFOS.stream().map(TableInfo::getName).collect(Collectors.toSet());
    }

    /**
     * 获取所有需要被CKSqlParser处理的表信息
     */
    public static List<TableInfo> tableInfo() {
        // TODO 还需要从Apollo中获取动态配置的表信息。直接覆盖TABLEINFOS中已经存在的表信息

        return TABLEINFOS;
    }

    /**
     * 所有需要被CKSqlParser处理的表信息
     */
    public static Map<String, TableInfo> tableInfoMap() {
        return tableInfo().stream().collect(Collectors.toMap(TableInfo::getName, tableInfo -> tableInfo));
    }

    /**
     * 加密传入的字段值
     */
    public static String encryptColumnValue(String columnValue) {

        try {
            return SmConstant.sm4.encryptBase64(columnValue);
        } catch (RuntimeException e) {

            log.info("encryptColumnValueIfNeed error, columnValue:{}", columnValue);
            return columnValue;
        }
    }


}
