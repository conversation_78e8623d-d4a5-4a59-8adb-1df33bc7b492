package com.corpgovernment.resource.schedule.onlinereport.module.behaivor;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class FlightDiscountDTO {

    @Column(name = "priceRateRange")
    @Type(value = Types.VARCHAR)
    private String rateRange;

    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    @Column(name = "totalAmount")
    @Type(value = Types.DOUBLE)
    private Double totalAmount;

    @Column(name = "totalPrice")
    @Type(value = Types.DOUBLE)
    private Double totalPrice;

    @Column(name = "totalPreOrderDate")
    @Type(value = Types.INTEGER)
    private Integer totalPreOrderDate;

}
