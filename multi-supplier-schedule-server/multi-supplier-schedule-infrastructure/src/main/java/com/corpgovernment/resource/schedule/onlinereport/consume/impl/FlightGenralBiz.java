package com.corpgovernment.resource.schedule.onlinereport.consume.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.FlightConsume;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.OnlineReportConsumeBO;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportConsumeDao;
import com.corpgovernment.resource.schedule.onlinereport.consume.AbstractGenralConsume;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Service
public class FlightGenralBiz extends AbstractGenralConsume {

    @Autowired
    private OnlineReportConsumeDao reportConsumeDao;

    @Override
    protected BigDecimal getTotalAmount1(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneAmount())
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalTwoAmount()));
    }

    @Override
    protected Integer getTotalQuantity1(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneQuantity())
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalTwoQuantity()));
    }

    @Override
    protected BigDecimal getTotalAmount3(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalThreeAmount())
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFourAmount()));
    }

    @Override
    protected Integer getTotalQuantity3(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalThreeQuantity())
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFourQuantity()));
    }

    @Override
    protected OnlineReportConsumeBO aggreationGenralConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        List<FlightConsume> flihgtClassList = (List<FlightConsume>) reportConsumeDao.aggreationFlightWithCondition(baseQueryConditionDto, FlightConsume.class, false);
        return convert(flihgtClassList);
    }

    @Override
    protected OnlineReportConsumeBO aggreationCurrentConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        List<FlightConsume> flihgtClassList = (List<FlightConsume>) reportConsumeDao.aggreationFlightWithCondition(baseQueryConditionDto, FlightConsume.class, true);
        OnlineReportConsumeBO genralConsume = convert(flihgtClassList);
        // 商旅服务费
        genralConsume.setTotalCorpServiceFee(OrpReportUtils.formatBigDecimal(new BigDecimal(flihgtClassList.stream()
                .mapToDouble(i -> Optional.ofNullable(i.getTotalCorpServiceFee()).orElse(0d)).sum())));
        Double totalPriceEconomy = flihgtClassList.stream()
                .mapToDouble(i -> Optional.ofNullable(i.getTotalNetfareEconomy()).orElse(0d)).sum();
        int totalQuantityEconomy = flihgtClassList.stream()
                .mapToInt(i -> Optional.ofNullable(i.getTotalQuantityEconomy()).orElse(0)).sum();
        Double totalTpmsEconomy = flihgtClassList.stream()
                .mapToDouble(i -> Optional.ofNullable(i.getTotalTpmsEconomy()).orElse(0d)).sum();
        // 平均票价
        genralConsume.setAvgPrice(OrpReportUtils.divide(new BigDecimal(totalPriceEconomy)
                , new BigDecimal(totalQuantityEconomy), OrpConstants.TWO));
        // 里程均价
        genralConsume.setAvgOtherPrice(OrpReportUtils.divide(new BigDecimal(totalPriceEconomy)
                , new BigDecimal(totalTpmsEconomy), OrpConstants.TWO));
        // 改签张数
        genralConsume.setTotalRebookQuantity(flihgtClassList.stream().mapToInt(i -> Optional.ofNullable(i.getTotalRebookQuantity()).orElse(0)).sum());
        // 退票张数
        genralConsume.setTotalRefundQuantity(flihgtClassList.stream().mapToInt(i -> Optional.ofNullable(i.getTotalRefundQuantity()).orElse(0)).sum());
        /* 订单数
        List<FlightConsume> flihgtClassOrderList = reportConsumeDao.aggreationFlightOrderNumWithCondition(baseQueryConditionDto, FlightConsume.class);
        genralConsume.setTotalCntOrder(flihgtClassOrderList.stream().mapToInt(i-> Optional.ofNullable(i.getTotalCntOrder()).orElse(0)).sum());
         */
        return genralConsume;
    }

    private OnlineReportConsumeBO convert(List<FlightConsume> flihgtClassList) {
        OnlineReportConsumeBO genralConsume = new OnlineReportConsumeBO();
        Double currentTotalAmount = 0d;
        Integer currentTotalQuantity = 0;
        if (CollectionUtils.isNotEmpty(flihgtClassList)) {
            currentTotalAmount = flihgtClassList.stream().mapToDouble(FlightConsume::getTotalAmount).filter(Objects::nonNull).sum();

            currentTotalQuantity = flihgtClassList.stream().mapToInt(FlightConsume::getTotalQuantity).filter(Objects::nonNull).sum();
            // 国内
            List<FlightConsume> nList = flihgtClassList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFlightClass(), "N")).collect(Collectors.toList());
            genralConsume.setTotalOneAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(nList.stream().mapToDouble(FlightConsume::getTotalAmount).sum())));
            genralConsume.setTotalOneQuantity(nList.stream().mapToInt(FlightConsume::getTotalQuantity).sum());
            // 国际
            List<FlightConsume> iList = flihgtClassList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFlightClass(), "I")).collect(Collectors.toList());
            genralConsume.setTotalTwoAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(iList.stream().mapToDouble(FlightConsume::getTotalAmount).sum())));
            genralConsume.setTotalTwoQuantity(iList.stream().mapToInt(FlightConsume::getTotalQuantity).sum());
            // 协议
            List<FlightConsume> isAgreementList = flihgtClassList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getContractType(), "C")).collect(Collectors.toList());
            genralConsume.setTotalThreeAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(isAgreementList.stream().mapToDouble(FlightConsume::getTotalAmount).sum())));
            genralConsume.setTotalThreeQuantity(isAgreementList.stream().mapToInt(FlightConsume::getTotalQuantity).sum());
            // 非协议
            List<FlightConsume> noAgreementList = flihgtClassList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getContractType(), "NC")).collect(Collectors.toList());
            genralConsume.setTotalFourAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(noAgreementList.stream().mapToDouble(FlightConsume::getTotalAmount).sum())));
            genralConsume.setTotalFourQuantity(noAgreementList.stream().mapToInt(FlightConsume::getTotalQuantity).sum());
        }
        genralConsume.setTotalAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(currentTotalAmount)));
        genralConsume.setTotalQuantity(currentTotalQuantity);
        return genralConsume;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum s) {
        return QueryReportBuTypeEnum.flight == s;
    }
}
