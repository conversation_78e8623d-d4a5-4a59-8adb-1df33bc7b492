package com.corpgovernment.resource.schedule.onlinereport.utils;


import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.apache.commons.lang3.StringUtils;

/**
 * 杨鑫：目前改为直接调用apollo配置中心
 * @program: com.ctrip.corp.bi.online.report.platform.common.utils
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-17 14:37
 **/
public class SharkUtils {

    /**
     * 获取apollo corpgovernment.field-dictionary 配置
     */
    private static final Config CONFIG = ConfigService.getConfig("corpgovernment.field-dictionary");

    /**
     * 获取shark值）
     *
     * @param headerKey
     * @param lang
     * @return
     */
    public static String getHeaderVal(String headerKey, String lang) {
        if (StringUtils.isBlank(headerKey)) {
            return "";
        }
        return CONFIG.getProperty(headerKey, "");
    }

    /**
     * 获取shark值
     *
     * @param sharkKey
     * @param lang
     * @return
     */
    public static String get(String sharkKey, String lang) {
        return CONFIG.getProperty(sharkKey, "");
    }

    /**
     * 获取shark中文值
     *
     * @param headerKey
     * @return
     */
    public static String getChineseVal(String headerKey) {
        return CONFIG.getProperty(headerKey, "");
    }

    /**
     * 是否是英文
     *
     * @param lang
     * @return
     */
    public static boolean isEN(String lang) {
        return StringUtils.equalsIgnoreCase("en_us", lang) || StringUtils.equalsIgnoreCase("en", lang) || StringUtils.equalsIgnoreCase("en-us", lang);
    }

    public static boolean isZH(String lang) {
        return true;
        //return StringUtils.equalsIgnoreCase("zh", lang) || StringUtils.equalsIgnoreCase("zh-cn", lang);
    }
}
