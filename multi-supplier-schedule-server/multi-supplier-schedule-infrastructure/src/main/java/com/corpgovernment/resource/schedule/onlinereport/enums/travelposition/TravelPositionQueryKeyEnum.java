package com.corpgovernment.resource.schedule.onlinereport.enums.travelposition;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.travelposition
 * @description:
 * @author: md_wang
 * @create: 2022-08-22 16:35
 **/
public enum TravelPositionQueryKeyEnum {
    /**
     * 预定人
     */
    BOOKER("BOOKER", "UserSurvey.Q1.Opt5"),

    /**
     * 出行人
     */
    TRAVELER("TRAVELER", "TravelPosition.passenger"),
    /**
     * 产品线
     */
    PRODUCT_TYPE("PRODUCT_TYPE", "Exceltopname.producttype"),
    /**
     * 航班号
     */
    FLIGHT_NO("FLIGHT_NO", "Index.flightno"),

    /**
     * 火车班次
     */
    TRAIN_NO("TRAIN_NO", "TripMap.TrainNo"),
    /**
     * 酒店名称
     */
    HOTEL_NAME("HOTEL_NAME", "Index.hotelname"),
    ;

    private String key;
    private String sharkKey;

    TravelPositionQueryKeyEnum(String key, String sharkKey) {
        this.key = key;
        this.sharkKey = sharkKey;
    }


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public void setSharkKey(String sharkKey) {
        this.sharkKey = sharkKey;
    }
}
