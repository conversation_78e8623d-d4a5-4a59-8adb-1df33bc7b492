package com.corpgovernment.resource.schedule.onlinereport.clickhouse.saveanalysis;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportOverviewTrendDao;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportPotentialSaveFlightDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportPotentialSaveHotelDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveProportionDetailRequestDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveProportionRequestDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/**
 * <AUTHOR>
 * @date ：Created in 2021/11/10 10:26
 * @description：
 * @modified By：
 * @version: $
 */
@Component
@Slf4j
@Repository
public class OnlineReportPotentialSaveDisDao extends AbstractClickhouseBaseDao {


    private static final String LOG_TITLE = "OnlineReportPotentialSaveDisDao";

    /**
     * 查询 在线报告概况-潜在节省分布机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryFlightProportion(OnlineReportSaveProportionRequestDTO request) throws Exception {
        String sql = buildFlightProportionSql(request);
        return queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportPotentialSaveFlightDTO.class, "queryOnlineReportFlightPSaveProportion");
    }

    /**
     * 查询 在线报告概况-潜在节省分布详情机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryFlightProportionDetail(OnlineReportSaveProportionDetailRequestDTO request) throws Exception {
        String sql = buildFlightProportionDetailSql(request);
        return queryBySql(sql, request, this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportPotentialSaveFlightDTO.class, "queryOnlineReportFlightPSaveProportionDetail");
    }

    /**
     * 查询 在线报告概况-潜在节省分布详情机票
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param productType
     * @param industries
     * @return
     * @throws Exception
     */
    
    public List<OnlineReportPotentialSaveFlightDTO> queryFlightProportionDetail(String startTime, String endTime, String statisticalCaliber, String productType,
                                                                                List<String> industries,
                                                                                String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        String sql = conditionWrapDetailSql(startTime, endTime, flightProportionDetail(productType, statisticalCaliber).toString(), industries,
                compareSameLevel, consumptionLevel, compareCorpSameLevel);
        return queryBySql(sql, Lists.newArrayList(), this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportPotentialSaveFlightDTO.class, "queryOnlineReportFlightPSaveProportionDetail");
    }

    /**
     * 查询 在线报告概况-潜在节省分布酒店
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryHotelProportion(OnlineReportSaveProportionRequestDTO request) throws Exception {
        String sql = buildHotelProportionSql(request);
        return queryBySql(sql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportPotentialSaveHotelDTO.class, "queryOnlineReportHotelPSaveProportion");
    }

    /**
     * 查询 在线报告概况-潜在节省分布详情酒店
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryHotelProportionDetail(OnlineReportSaveProportionDetailRequestDTO request) throws Exception {
        String sql = buildHotelProportionDetailSql(request);
        return queryBySql(sql, request, this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportPotentialSaveHotelDTO.class, "queryOnlineReportHotelPSaveProportionDetail");
    }

    /**
     * 查询 在线报告概况-潜在节省分布详情酒店
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param productType
     * @param industries
     * @return
     * @throws Exception
     */
    
    public List<OnlineReportPotentialSaveHotelDTO> queryHotelProportionDetail(String startTime, String endTime, String statisticalCaliber, String productType,
                                                                              List<String> industries,
                                                                              String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        String sql = conditionWrapDetailSql(startTime, endTime, hotelProportionDetail(productType, statisticalCaliber).toString(), industries,
                compareSameLevel, consumptionLevel, compareCorpSameLevel);
        return queryBySql(sql, Lists.newArrayList(), this::mapRequest2, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportPotentialSaveHotelDTO.class, "queryOnlineReportHotelPSaveProportionDetail");
    }

    public String buildFlightProportionSql(OnlineReportSaveProportionRequestDTO request) {
        return conditionWrapSql(request.getBaseQueryCondition().getStartTime(),
                request.getBaseQueryCondition().getEndTime(),
                flightProportion(request.getProductType(), request.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                request.getBaseQueryCondition());
    }

    public String buildFlightProportionDetailSql(OnlineReportSaveProportionDetailRequestDTO request) {
        BaseQueryConditionDTO dto = request.getBaseQueryCondition();
        return conditionWrapDetailSql(request.getBaseQueryCondition().getStartTime(),
                request.getBaseQueryCondition().getEndTime(),
                flightProportionDetail(request.getProductType(), dto.getStatisticalCaliber()).toString(),
                request.getIndustries(), dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
    }

    public String buildHotelProportionSql(OnlineReportSaveProportionRequestDTO request) {
        return conditionWrapSql(request.getBaseQueryCondition().getStartTime(),
                request.getBaseQueryCondition().getEndTime(),
                hotelProportion(request.getProductType(), request.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                request.getBaseQueryCondition());
    }

    public String buildHotelProportionDetailSql(OnlineReportSaveProportionDetailRequestDTO request) {
        BaseQueryConditionDTO dto = request.getBaseQueryCondition();
        return conditionWrapDetailSql(request.getBaseQueryCondition().getStartTime(),
                request.getBaseQueryCondition().getEndTime(),
                hotelProportionDetail(request.getProductType(), dto.getStatisticalCaliber()).toString(),
                request.getIndustries(), dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
    }

    public StringBuilder flightProportion(String productType, String statisticalCaliber) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select ");
        stringBuilder.append("SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as flightRcPotentialSave, ");
        stringBuilder.append("SUM(coalesce(change_fee, 0) + coalesce(rebook_price_differential, 0) + coalesce(rebook_service_fee, 0) " +
                "+ if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0))" +
                "+ coalesce(rebook_behind_service_fee, 0)) as flightRebookPotentialSave, ");
        stringBuilder.append("SUM(coalesce(refund_fee, 0) + coalesce(refund_service_fee, 0) + coalesce(refund_behind_service_fee, 0)) as flightRefundPotentialSave, ");

        // 退改张数 及 超标次数
        stringBuilder.append("COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END) as flightRcTimes, ");
        stringBuilder.append("if((COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END)) != 0, " +
                " (COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END)) * 100 / (COUNT(DISTINCT CASE WHEN is_refund = 'F' " +
                " THEN order_id END)), " +
                " 0) as flightRcTimesRate, ");

        stringBuilder.append("SUM(case when is_rebook='T' then quantity else 0 end) as flightRebookTimes, ");
        stringBuilder.append("if(SUM(ordertkt) != 0, " +
                " SUM(case when is_rebook='T' then quantity else 0 end) * 100 / SUM(ordertkt), " +
                " 0) as flightRebookTimesRate, ");

        stringBuilder.append("SUM(refundtkt) as flightRefundTimes, ");
        stringBuilder.append("if(SUM(ordertkt) != 0, " +
                " SUM(refundtkt) * 100 / SUM(ordertkt), " +
                " 0) as flightRefundTimesRate ");

        stringBuilder.append("from  ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        stringBuilder.append("  where d = '").append(partition).append("' and ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        stringBuilder.append(SaveCondition.getFlightClassCondition(productType));
        stringBuilder.append(" and audited <> 'F' ");
        return stringBuilder;
    }

    public StringBuilder flightProportionDetail(String productType, String statisticalCaliber) {
        // 商旅 行业
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select  ");
        stringBuilder.append("if((COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END)) != 0, " +
                " (COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END)) * 100 / (COUNT(DISTINCT CASE WHEN is_refund = 'F' " +
                " THEN order_id END)), " +
                " 0) as flightRcTimesRate, ");
        stringBuilder.append("if(SUM(ordertkt) != 0, " +
                " SUM(case when is_rebook='T' then quantity else 0 end) * 100 / SUM(ordertkt), " +
                " 0) as flightRebookTimesRate, ");
        stringBuilder.append("if(SUM(ordertkt) != 0, " +
                " SUM(refundtkt) * 100 / SUM(ordertkt), " +
                " 0) as flightRefundTimesRate ");
        stringBuilder.append("from  ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        stringBuilder.append(SaveCondition.getFlightClassCondition(productType));
        stringBuilder.append(" and audited <> 'F' ");
        return stringBuilder;
    }

    public StringBuilder hotelProportion(String productType, String statisticalCaliber) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select ");
        stringBuilder.append("SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) hotelRcPotentialSave, ");
        stringBuilder.append("SUM(coalesce(cancel_esti_save_amount, 0)) hotelCancelPotentialSave, ");

        stringBuilder.append("(COUNT(DISTINCT CASE " +
                "        WHEN is_mix_payment = 'F' " +
                "        AND dead_price_onenight > 0 " +
                "        AND (coalesce(reason_code, '') <> '' " +
                "            OR coalesce(min_price_rc, '') <> '') " +
                "        AND is_refund = 'F' THEN order_id " +
                "    END)) as hotelRcPotentialTimes, ");
        stringBuilder.append("if( (COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END)) != 0, " +
                " (COUNT(DISTINCT CASE " +
                "        WHEN is_mix_payment = 'F'" +
                "        AND dead_price_onenight > 0" +
                "        AND (coalesce(reason_code, '') <> ''" +
                "            OR coalesce(min_price_rc, '') <> '')" +
                "        AND is_refund = 'F' THEN order_id" +
                "    END)) * 100 / (COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END)), " +
                " 0) as hotelRcPotentialTimesRate, ");

        stringBuilder.append("SUM(coalesce(rfd_quantity, 0)) as hotelCancelPotentialTimes, ");
        stringBuilder.append("if( SUM(coalesce(quantity, 0)) != 0, " +
                " SUM(coalesce(rfd_quantity, 0)) * 100 / SUM(coalesce(quantity, 0)), " +
                " 0) as hotelCancelPotentialTimesRate ");
        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        stringBuilder.append(SaveCondition.getHotelCondition(productType));
        return stringBuilder;
    }

    public StringBuilder hotelProportionDetail(String productType, String statisticalCaliber) {
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select ");
        stringBuilder.append("if( (COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END)) != 0, " +
                " (COUNT(DISTINCT CASE " +
                "        WHEN is_mix_payment = 'F'" +
                "        AND dead_price_onenight > 0" +
                "        AND (coalesce(reason_code, '') <> ''" +
                "            OR coalesce(min_price_rc, '') <> '')" +
                "        AND is_refund = 'F' THEN order_id" +
                "    END)) * 100 / (COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END)), " +
                " 0) as hotelRcPotentialTimesRate, ");
        stringBuilder.append("if( SUM(coalesce(quantity, 0)) != 0, " +
                " SUM(coalesce(rfd_quantity, 0)) * 100 / SUM(coalesce(quantity, 0)), " +
                " 0) as hotelCancelPotentialTimesRate ");

        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        stringBuilder.append(SaveCondition.getHotelCondition(productType));
        return stringBuilder;
    }

    private String conditionWrapSql(String start, String end, String sqlTemplate,
                                    BaseQueryConditionDTO baseQueryConditionDTO) {
        String scope_condition = OnlineReportOverviewTrendDao.buildScopeFilter(baseQueryConditionDTO);
        sqlTemplate = sqlTemplate.replace("{scope_condition}", scope_condition).replace("{start}", start).replace("{end}", end);
        return sqlTemplate;
    }

    private String conditionWrapDetailSql(String start, String end, String sqlTemplate,
                                          List<String> industries, String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        String industryScopeCondition = buildIndustryScope(industries);
        // 行业
        if (CollectionUtils.isNotEmpty(industries) && StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T")
                && StringUtils.isNotEmpty(consumptionLevel)) {
            industryScopeCondition += " and consumptionlevel = '" + consumptionLevel + "' ";
        }
        // 商旅
        if (CollectionUtils.isEmpty(industries) && StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T")
                && StringUtils.isNotEmpty(consumptionLevel)) {
            industryScopeCondition += " and consumptionlevel = '" + consumptionLevel + "' ";
        }
        sqlTemplate = sqlTemplate.replace("{scope_condition}", industryScopeCondition)
                .replace("{start}", start).replace("{end}", end);
        return sqlTemplate;
    }

    public static String buildIndustryScope(List<String> industries) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" 1 = 1");
        if (CollectionUtils.isNotEmpty(industries) && StringUtils.isNotEmpty(industries.get(0))) {
            // industry是由#连接的两个值，前面是大类，后面是小类
            String[] strs = industries.get(0).split("#");
            // strs[0]行业d大类，strs[1]行业小类
            if (ArrayUtils.isNotEmpty(strs) && strs.length == OrpConstants.TWO && StringUtils.isNotEmpty(strs[1]) && StringUtils.isNotEmpty(strs[1])) {
                stringBuilder.append(String.format(" and std_industry2 =  '%S'", strs[1]));
            } else if (ArrayUtils.isNotEmpty(strs) && StringUtils.isNotEmpty(strs[0]) && StringUtils.isNotEmpty(strs[0])) {
                stringBuilder.append(String.format(" and std_industry1 =  '%S'", strs[0]));
            }
        }
        return stringBuilder.toString();
    }

    private PreparedStatement mapRequest(OnlineReportSaveProportionRequestDTO requestDto, PreparedStatement statement) {
        return statement;
    }

    private PreparedStatement mapRequest2(OnlineReportSaveProportionDetailRequestDTO requestDto, PreparedStatement statement) {
        return statement;
    }

    private PreparedStatement mapRequest2(List<Object> parmList, PreparedStatement statement) {
        return statement;
    }

}
