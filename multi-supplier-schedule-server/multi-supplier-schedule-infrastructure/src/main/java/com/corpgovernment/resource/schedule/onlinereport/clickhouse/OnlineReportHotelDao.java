package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.dao.impl
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-08 15:18
 **/
@Repository
@Slf4j
public class OnlineReportHotelDao extends AbstractClickhouseBaseDao {


    private static final String LOG_TITLE = "OnlineReportHotelDao queryOnlineReportHotel";

    private BaseCommonTableQueryCondition queryCondition;

    public OnlineReportHotelDao(BaseCommonTableQueryCondition queryCondition) {
        this.queryCondition = queryCondition;
    }

    /**
     * 查询-酒店产线数据
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryOnlineReportHotel(OnlineDetailRequestDto requestDto, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        sqlBuilder.append("select ");
        sqlBuilder.append(" report_date as reportDate, is_oversea as oversea , order_type as orderType, \n");

        /**
         * 整体-报表
         */
        // 房费
        sqlBuilder.append(
                " sum(coalesce(room_price,0)) as roomPrice ,\n");
        // 前收商旅管理服务费
        sqlBuilder.append(
                " sum(coalesce(service_fee,0)) as serviceFee ,\n");
        // 后收商旅管理服务费
        sqlBuilder.append(
                " sum(coalesce(postservicefee,0)) as postServiceFee ,\n");
        // 优惠券
        sqlBuilder.append(
                " sum(coalesce(coupon_amount,0)) as couponAmount ,\n");

        /**
         * 同环比-消费金额
         */
        // 当前-消费金额（总金额）
        sqlBuilder.append(
                " sum(coalesce(real_pay_with_servicefee,0)) as realPay ,\n");

        // 当前-三方协议-消费金额（总金额）
        sqlBuilder.append(
                " sum(case when producttype_all='" + ta + "' then coalesce(real_pay_with_servicefee,0) else 0 end) as agreementAmountPrice ,\n");

        // 当前-非三方协议-消费金额
        sqlBuilder.append(
                " sum(case when producttype_all!='" + ta + "' then coalesce(real_pay_with_servicefee,0) else 0 end) as amountMemPrice ,\n");

        // 当期-国内-消费金额
        sqlBuilder.append(
                " sum(case when is_oversea in ('F','O') then coalesce(real_pay_with_servicefee,0) else 0 end) as domesticPrice , \n");

        // 当期-国际-消费金额
        sqlBuilder.append(
                " sum(case when is_oversea='T' then coalesce(real_pay_with_servicefee,0) else 0 end) as internationalPrice ,\n");

        /**
         * 同环比-间夜量
         */
        // 当期-酒店间夜数
        sqlBuilder.append(
                " sum(coalesce(quantity,0)) as quantityV ,\n");

        // 当期-三方协议-酒店间夜数
        sqlBuilder.append(
                " sum(case when producttype_all='" + ta + "' then coalesce(quantity,0) else 0 end) as agreementQuantity ,\n");

        // 当期-非三方协议-酒店间夜数
        sqlBuilder.append(
                " sum(case when producttype_all!='" + ta + "' then coalesce(quantity,0) else 0 end) as memQuantity ,\n");

        // 当期-国内-酒店间夜数
        sqlBuilder.append(
                " sum(case when is_oversea in ('F','O') then coalesce(quantity,0) else 0 end) as domesticQuantity ,\n");

        // 当期-国际-酒店间夜数
        sqlBuilder.append(
                " sum(case when is_oversea='T' then coalesce(quantity,0) else 0 end) as internationalQuantity \n");

        sqlBuilder.append(OrpConstants.FROM);
        ClickHouseTable clickHouseTable = null;
        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                clickHouseTable = ClickHouseTable.ADM_INDEXHOTEL_PRICE_DETAIL_FOREIGN_MULTI_CURRENCY;
            } else {
                clickHouseTable = ClickHouseTable.ADM_INDEXHOTEL_PRICE_DETAIL_FOREIGN;
            }
        } else {
            if (StringUtils.equalsIgnoreCase(requestDto.getBaseCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL;
            }
        }
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        sqlBuilder.append(" and order_status = '" + OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus") + "' ");
        sqlBuilder.append(OrpConstants.AND);
        sqlBuilder.append(" ( report_date>=? and report_date<=? ) ");
        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(requestDto.getBaseCondition().getCurrency())) {
                // 币种条件
                sqlBuilder.append(String.format(" and termcurrency = '%s'", requestDto.getBaseCondition().getCurrency()));
            }
        }
        // 其他条件
        queryCondition.buildPreSqlCondition(requestDto, sqlBuilder);

        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append(" report_date,is_oversea,order_type ");

        ClickHouseTable finalClickHouseTable = clickHouseTable;
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapRequest(req, statement, finalClickHouseTable), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, "queryHotelDetail");
    }

    private PreparedStatement mapRequest(OnlineDetailRequestDto requestDto, PreparedStatement statement, ClickHouseTable clickHouseTable) {
        AtomicInteger index = new AtomicInteger(OrpConstants.ONE);
        try {

            // 分区
            statement.setString(index.getAndIncrement(), queryPartition(clickHouseTable));
            // 查询日期
            statement.setString(index.getAndIncrement(), requestDto.getDataStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            queryCondition.setPreSqlCondition(index, requestDto, statement);
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }

        return statement;
    }

}
