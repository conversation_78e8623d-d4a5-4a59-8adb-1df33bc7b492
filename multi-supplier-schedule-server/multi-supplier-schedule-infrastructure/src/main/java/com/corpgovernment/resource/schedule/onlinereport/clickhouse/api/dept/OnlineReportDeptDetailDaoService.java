package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AnalysisObjectOrgInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className OnlineReportDeptFlightDetailDaoService
 * @date 2024/9/10
 */
public interface OnlineReportDeptDetailDaoService {

    List<Map> deptDetailAnalysis(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, Pager pager,
                                 SubQueryReportBuTypeEnum subQueryReportBuTypeEnum, String startTime, String endTime, boolean needOrigianlCurrency,
                                 AnalysisObjectOrgInfo analysisObjectOrgInfo) throws Exception;

    List<Map> deptDetailAnalysis(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, Pager pager,
                                 SubQueryReportBuTypeEnum subQueryReportBuTypeEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String analysisObjectVal,
                                 String productType) throws Exception;

    Integer count(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, SubQueryReportBuTypeEnum subQueryReportBuTypeEnum,
                  AnalysisObjectOrgInfo analysisObjectOrgInfo, String analysisObjectVal, String productType) throws Exception;

}
