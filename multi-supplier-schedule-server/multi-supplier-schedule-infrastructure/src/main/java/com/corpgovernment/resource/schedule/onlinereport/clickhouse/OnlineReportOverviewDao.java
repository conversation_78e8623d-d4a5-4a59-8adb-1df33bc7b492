package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.dao.impl
 * @description:
 * @author: Chris Yu
 * @create: 2021-11-03 19:49
 **/
@Repository
@Slf4j
public class OnlineReportOverviewDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "OnlineReportOverviewDao queryOnlineReportOverview";

    private BaseCommonTableQueryCondition queryCondition;

    public OnlineReportOverviewDao(BaseCommonTableQueryCondition queryCondition) {
        this.queryCondition = queryCondition;
    }

    /**
     * 查询 在线报告概览
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    @Deprecated
    public <T> List<T> queryOnlineReportDetailOverviewOld(OnlineDetailRequestDto requestDto, Class<T> clazz)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select report_date as reportDate,");

        /**
         * 金额
         */
        // 机票-金额
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_flt,0) else 0 end) as amountFlt,\n");
        // 机票金额-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_flt,0) else 0 end) as amountFltYoy,\n");
        // 机票金额-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_flt,0) else 0 end) as amountFltMom,\n");

        // 酒店-金额
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_htl,0) else 0 end) as amountHtl,\n");
        // 酒店金额-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_htl,0) else 0 end) as amountHtlYoy,\n");
        // 酒店金额-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_htl,0) else 0 end) as amountHtlMom,\n");

        // 火车-金额
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_train,0) else 0 end) as amountTrain,\n");
        // 火车金额-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_train,0) else 0 end) as amountTrainYoy,\n");
        // 火车金额-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_train,0) else 0 end) as amountTrainMom,\n");

        // 用车-金额
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_car,0) else 0 end) as amountCar,\n");
        // 用车金额-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_car,0) else 0 end) as amountCarYoy,\n");
        // 用车金额-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_car,0) else 0 end) as amountCarMom,\n");

        // 汽车-金额
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_bus,0) else 0 end) as amountBus,\n");
        // 汽车金额-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_bus,0) else 0 end) as amountBusYoy,\n");
        // 汽车金额-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_bus,0) else 0 end) as amountBusMom,\n");

        // 增值-金额
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_vas,0) else 0 end) as amountAdd,\n");
        // 增值金额-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_vas,0) else 0 end) as amountAddYoy,\n");
        // 增值金额-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_vas,0) else 0 end) as amountAddMom,\n");

        // 整体消费金额
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_total,0) else 0 end) as amountTotal,\n");
        // 整体消费金额-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_total,0) else 0 end) as amountTotalYoy,\n");
        // 整体消费金额-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(amount_total,0) else 0 end) as amountTotalMom,\n");

        /**
         * 票张
         */
        // 机票张数
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_flt,0) else 0 end) as quantityFlt,\n");
        // 机票张数-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_flt,0) else 0 end) as quantityFltYoy,\n");
        // 机票张数-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_flt,0) else 0 end) as quantityFltMom,\n");

        // 酒店间夜数
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_htl,0) else 0 end) as quantityHtl,\n");
        // 酒店间夜数-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_htl,0) else 0 end) as quantityHtlYoy,\n");
        // 酒店间夜数-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_htl,0) else 0 end) as quantityHtlMom,\n");

        // 火车张数
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_train,0) else 0 end) as quantityTrain,\n");
        // 火车张数-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_train,0) else 0 end) as quantityTrainYoy,\n");
        // 火车张数-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_train,0) else 0 end) as quantityTrainMom, \n");

        // 用车订单数
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_car,0) else 0 end) as quantityCar,\n");
        // 用车订单数-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_car,0) else 0 end) as quantityCarYoy,\n");
        // 用车订单数环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_car,0) else 0 end) as quantityCarMom,\n");

        // 汽车票张数
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_bus,0) else 0 end) as quantityBus,\n");
        // 汽车票张数-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_bus,0) else 0 end) as quantityBusYoy,\n");
        // 汽车票张数-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_bus,0) else 0 end) as quantityBusMom,\n");

        // 增值订单数
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_vas,0) else 0 end) as quantityAdd,\n");
        // 增值订单数-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_vas,0) else 0 end) as quantityAddYoy,\n");
        // 增值订单数-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_vas,0) else 0 end) as quantityAddMom,\n");

        // 张数
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_total,0) else 0 end) as quantityV,\n");
        // 张数-同比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_total,0) else 0 end) as quantityYoy,\n");
        // 张数-环比
        sqlBuilder.append(
                "sum(case when report_date>=? and report_date<=? then coalesce(quantity_total,0) else 0 end) as quantityMom \n");

        sqlBuilder.append(OrpConstants.FROM);
        ClickHouseTable clickHouseTable = null;
        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN_MULTI_CURRENCY;
            } else {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN;
            }
        } else {
            if (StringUtils.equalsIgnoreCase(requestDto.getBaseCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
            }
        }
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d=? ");
        sqlBuilder.append(OrpConstants.AND);
        sqlBuilder.append(OrpConstants.LEFT_PARENTHESES);
        sqlBuilder.append(" ( report_date>=? and report_date<=? ) or");
        sqlBuilder.append(" ( report_date>=? and report_date<=? ) or ");
        sqlBuilder.append(" ( report_date>=? and report_date<=? ) ");
        sqlBuilder.append(OrpConstants.RIGHT_PARENTHESES);
        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(requestDto.getBaseCondition().getCurrency())) {
                // 币种条件
                sqlBuilder.append(String.format(" and termcurrency = '%s'", requestDto.getBaseCondition().getCurrency()));
            }
        }
        // 其他条件
        queryCondition.buildPreSqlCondition(requestDto, sqlBuilder);
        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append(" report_date ");

        // 查询clickhouse
        ClickHouseTable finalClickHouseTable = clickHouseTable;
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapRequest(req, statement, finalClickHouseTable), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, "queryOnlineReportOverview");
    }


    /**
     * 查询 在线报告概览
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryOnlineReportDetailOverview(OnlineDetailRequestDto requestDto, Class<T> clazz)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select report_date as reportDate,");

        /**
         * 金额
         */
        // 机票-金额
        sqlBuilder.append(
                "sum(coalesce(amount_flt,0)) as amountFlt,\n");

        // 酒店-金额
        sqlBuilder.append(
                "sum(coalesce(amount_htl,0)) as amountHtl,\n");

        // 火车-金额
        sqlBuilder.append(
                "sum(coalesce(amount_train,0)) as amountTrain,\n");

        // 用车-金额
        sqlBuilder.append(
                "sum(coalesce(amount_car,0)) as amountCar,\n");

        // 汽车-金额
        sqlBuilder.append(
                "sum(coalesce(amount_bus,0)) as amountBus,\n");

        // 增值-金额
        sqlBuilder.append(
                "sum(coalesce(amount_vas,0)) as amountAdd,\n");

        // 整体消费金额
        sqlBuilder.append(
                "sum(coalesce(amount_total,0)) as amountTotal,\n");

        // 机票张数
        sqlBuilder.append(
                "sum(coalesce(quantity_flt,0)) as quantityFlt,\n");

        // 酒店间夜数
        sqlBuilder.append(
                "sum(coalesce(quantity_htl,0)) as quantityHtl,\n");

        // 火车张数
        sqlBuilder.append(
                "sum(coalesce(quantity_train,0)) as quantityTrain,\n");

        // 用车订单数
        sqlBuilder.append(
                "sum(coalesce(quantity_car,0)) as quantityCar,\n");

        // 汽车票张数
        sqlBuilder.append(
                "sum(coalesce(quantity_bus,0)) as quantityBus,\n");

        // 增值订单数
        sqlBuilder.append(
                "sum(coalesce(quantity_vas,0)) as quantityAdd,\n");

        // 张数
        sqlBuilder.append(
                "sum(coalesce(quantity_total,0)) as quantityV \n");

        sqlBuilder.append(OrpConstants.FROM);
        ClickHouseTable clickHouseTable = null;
        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch()) {
                // 多币种表
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN_MULTI_CURRENCY;
            } else {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_FOREIGN;
            }
        } else {
            if (StringUtils.equalsIgnoreCase(requestDto.getBaseCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;// todo 预订口径
            } else {
                // 默认使用成交口径
                clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
            }
        }
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d=? ");
        sqlBuilder.append(OrpConstants.AND);
        sqlBuilder.append(" ( report_date>=? and report_date<=? ) ");
        if (BlueSpaceUtils.isForeign(requestDto.getBaseCondition().getPos(), requestDto.getBaseCondition().getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch() && StringUtils.isNotBlank(requestDto.getBaseCondition().getCurrency())) {
                // 币种条件
                sqlBuilder.append(String.format(" and termcurrency = '%s'", requestDto.getBaseCondition().getCurrency()));
            }
        }
        // 其他条件
        queryCondition.buildPreSqlCondition(requestDto, sqlBuilder);
        sqlBuilder.append(OrpConstants.GROUP_BY);
        sqlBuilder.append(" report_date ");

        // 查询clickhouse
        ClickHouseTable finalClickHouseTable = clickHouseTable;
        return queryBySql(sqlBuilder.toString(), requestDto, (req, statement) -> mapRequest(req, statement, finalClickHouseTable), (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, clazz, "queryOnlineReportOverview");
    }

    private PreparedStatement mapRequest(OnlineDetailRequestDto requestDto, PreparedStatement statement, ClickHouseTable clickHouseTable) {
        AtomicInteger index = new AtomicInteger(OrpConstants.ONE);
        try {

            statement.setString(index.getAndIncrement(), queryPartition(clickHouseTable));
            // 查询日期
            statement.setString(index.getAndIncrement(), requestDto.getDataStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());

            queryCondition.setPreSqlCondition(index, requestDto, statement);
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }


    /**
     * 查询 在线报告概览
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryOnlineReportDetailOverviewOrderNum(OnlineDetailRequestDto requestDto, Class<T> clazz)
            throws Exception {

        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dim = "''";
        String col = "report_date";
        if (StringUtils.equalsIgnoreCase(requestDto.getBaseCondition().getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            col = "orderdt";
        }
        sqlBuilder.append(String.format(" select sum(order_num) as quantityV , %s as dim, dt as report_date ", dim));
        sqlBuilder.append(", sum(if(order_type='机票', order_num, 0)) as quantityFlt");
        sqlBuilder.append(", sum(if(order_type='酒店', order_num, 0)) as quantityHtl");
        sqlBuilder.append(", sum(if(order_type='火车', order_num, 0)) as quantityTrain");
        sqlBuilder.append(", sum(if(order_type='用车', order_num, 0)) as quantityCar");
        sqlBuilder.append(", sum(if(order_type='汽车', order_num, 0)) as quantityBus");
        sqlBuilder.append(", sum(if(order_type='增值', order_num, 0)) as quantityAdd");
        sqlBuilder.append(" from ");
        sqlBuilder.append(" (");
        sqlBuilder.append("select count(distinct if(is_refund='F',order_id,null) ) - count(distinct if(is_refund='T' and order_status='R',order_id,null) )" +
                " as order_num, '机票' as order_type, ");
        sqlBuilder.append(String.format("concat(substr(%s,1,7), '-01')", col));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), col));
        if (StringUtils.isNotEmpty(requestDto.getBaseCondition().getGroupId())) {
            sqlBuilder.append(" and  companygroupid = ? ");
            parmList.add(requestDto.getBaseCondition().getGroupId());
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getCorpIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corp_corporation", requestDto.getBaseCondition().getCorpIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getAccountIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("account_id", requestDto.getBaseCondition().getAccountIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getCostCenterList())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildDetailSqlDeptAndCostcenter("cost_center", requestDto.getBaseCondition().getCostCenterList(), parmList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getDeptList())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildDetailSqlDeptAndCostcenter("dept", requestDto.getBaseCondition().getDeptList(), parmList));
        }
        sqlBuilder.append(" AND fee_type = '因公' and  audited <> 'F' and order_status in ('RA','TA','EP','EA','RP')");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(distinct if(is_refund='F',order_id,null) ) - count(distinct if(is_refund='T' and order_status='已取消',order_id,null) ) " +
                " as order_num, '酒店' as order_type, ");
        sqlBuilder.append(String.format("concat(substr(%s,1,7), '-01')", col));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), col));
        if (StringUtils.isNotEmpty(requestDto.getBaseCondition().getGroupId())) {
            sqlBuilder.append(" and  companygroupid = ? ");
            parmList.add(requestDto.getBaseCondition().getGroupId());
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getCorpIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corp_corporation", requestDto.getBaseCondition().getCorpIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getAccountIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("account_id", requestDto.getBaseCondition().getAccountIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getCostCenterList())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildDetailSqlDeptAndCostcenter("cost_center", requestDto.getBaseCondition().getCostCenterList(), parmList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getDeptList())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildDetailSqlDeptAndCostcenter("dept", requestDto.getBaseCondition().getDeptList(), parmList));
        }
        sqlBuilder.append(" AND order_status = '已完成' AND is_oversea IN ('F','O','T') ");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(distinct case when a.cnt>0 then a.order_id end)-count(distinct case when a.cnt< 0 then a.order_id end)" +
                " as order_num, '火车' as order_type,  a.dt as dt from ");
        sqlBuilder.append(" (select order_id, sum(quantity) cnt , ");
        sqlBuilder.append(String.format("concat(substr(%s,1,7), '-01')", col));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), col));
        if (StringUtils.isNotEmpty(requestDto.getBaseCondition().getGroupId())) {
            sqlBuilder.append(" and  companygroupid = ? ");
            parmList.add(requestDto.getBaseCondition().getGroupId());
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getCorpIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corp_corporation", requestDto.getBaseCondition().getCorpIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getAccountIds())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("account_id", requestDto.getBaseCondition().getAccountIds(), parmList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getCostCenterList())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildDetailSqlDeptAndCostcenter("cost_center", requestDto.getBaseCondition().getCostCenterList(), parmList));
        }
        if (CollectionUtils.isNotEmpty(requestDto.getBaseCondition().getDeptList())) {
            sqlBuilder.append(BaseConditionPrebuilder.buildDetailSqlDeptAndCostcenter("dept", requestDto.getBaseCondition().getDeptList(), parmList));
        }
        sqlBuilder.append(" and order_status  in ('TA','RP','EP','EA') ");
        sqlBuilder.append(" group by order_id, dt");
        sqlBuilder.append(" )a");
        sqlBuilder.append(" group by dt , order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(DISTINCT case when flag= 0 then orderid end)-count(DISTINCT case when flag= 1 then orderid end ) " +
                "as order_num, '汽车' as order_type, ");
        sqlBuilder.append(String.format("concat(substr(%s,1,7), '-01')", "querydate"));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.BUS_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.BUS_ORDERDETAIL));
        sqlBuilder.append(" and  isself = '因公' and orderstatusdesc = '已购票' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corpid", requestDto.getBaseCondition().getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "querydate"));
        sqlBuilder.append(" group by dt , order_type ");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append("select count(DISTINCT case when vd.flag= 0 then vd.vaso_id end)-count(DISTINCT case when vd.flag= 1 then vd.vaso_id end )" +
                " as order_num, '增值' as order_type, ");
        sqlBuilder.append(String.format("concat(substr(%s,1,7), '-01')", "query_refund_date"));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(" (select vaso_id, flag, query_refund_date from ");
        sqlBuilder.append(ClickHouseTable.VASO_ORDERDETAIL.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.VASO_ORDERDETAIL));
        sqlBuilder.append(" AND orderstatus in ('S','5')");
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("company", requestDto.getBaseCondition().getCorpIds(), parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "query_refund_date"));
        sqlBuilder.append(" ) vd");
        sqlBuilder.append(" group by dt, order_type");

        sqlBuilder.append(" \n union all \n");

        sqlBuilder.append(" select sum(coalesce(cnt_order,0)) as order_num, '用车' as order_type, ");
        sqlBuilder.append(String.format("concat(substr(%s,1,7), '-01')", "orderdt"));
        sqlBuilder.append(" as dt ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE).append(" d = ?");
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD));
        sqlBuilder.append(" AND fee_type = '因公' ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(parmList, requestDto.getStartTime(), requestDto.getEndTime(), "orderdt"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpAndAccount("corp_corporation", requestDto.getBaseCondition().getCorpIds(), parmList));
        sqlBuilder.append(" group by dt, order_type ");
        sqlBuilder.append(" \n )  group by report_date, dim ");

        log.info("queryOnlineReportDetailOverviewOrderNum", getLogSql(parmList, sqlBuilder.toString()));

        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "queryOnlineReportDetailOverviewOrderNum");
    }

    @Deprecated
    private PreparedStatement mapRequestOld(OnlineDetailRequestDto requestDto, PreparedStatement statement, ClickHouseTable clickHouseTable) {
        AtomicInteger index = new AtomicInteger(OrpConstants.ONE);
        try {
            /**
             * 金额
             */
            // 金额-机票-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 金额-酒店-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 金额-火车-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 金额-用车-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 金额-汽车-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 金额-增值-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 金额-整体-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            /**
             * 票张
             */
            // 票张-机票-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 票张-酒店-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 票张-火车-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 票张-用车-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 票张-汽车-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 票张-增值-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            // 票张-整体-当期/同比/环比
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());

            statement.setString(index.getAndIncrement(), queryPartition(clickHouseTable));
            // 查询日期
            statement.setString(index.getAndIncrement(), requestDto.getStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getEndTime());
            // 查询同比日期
            statement.setString(index.getAndIncrement(), requestDto.getYoyStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getYoyEndTime());
            // 查询环比日期
            statement.setString(index.getAndIncrement(), requestDto.getMomStartTime());
            statement.setString(index.getAndIncrement(), requestDto.getMomEndTime());
            queryCondition.setPreSqlCondition(index, requestDto, statement);
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }
}
