package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.saveanalysis;


import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveFlightDisDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveFlightDisV2DTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveHotelDisDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveProportionDetailRequestDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveProportionRequestDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27
 */
public interface OnlineReportSaveDisDaoService {
    /**
     * 查询 在线报告概况-节省分布机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveFlightDisDTO> queryFlightProportion(OnlineReportSaveProportionRequestDTO request) throws Exception;

    /**
     * 查询 在线报告概况-节省分布机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveFlightDisV2DTO> queryFlightProportionV2(OnlineReportSaveProportionRequestDTO request) throws Exception;

    /**
     * 查询 在线报告概况-节省分布详情机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveFlightDisDTO> queryFlightProportionDetail(OnlineReportSaveProportionDetailRequestDTO request) throws Exception;

    /**
     * 查询 在线报告概况-节省分布详情机票
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param productType
     * @param industries
     * @return
     * @throws Exception
     */

    List<OnlineReportSaveFlightDisV2DTO> queryFlightProportionDetailV2(
            String startTime, String endTime, String statisticalCaliber, String productType, List<String> industries,
            String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

    /**
     * 查询 在线报告概况-节省分布详情机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveFlightDisV2DTO> queryFlightProportionDetailV2(OnlineReportSaveProportionDetailRequestDTO request) throws Exception;

    /**
     * 查询 在线报告概况-节省分布 累计节省
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveFlightDisDTO> queryFlightAccProportionDetail(OnlineReportSaveProportionDetailRequestDTO request) throws Exception;


    /**
     * 查询 在线报告概况-节省分布酒店
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveHotelDisDTO> queryHotelProportion(OnlineReportSaveProportionRequestDTO request) throws Exception;

    /**
     * 查询 在线报告概况-节省分布详情酒店
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveHotelDisDTO> queryHotelProportionDetail(OnlineReportSaveProportionDetailRequestDTO request) throws Exception;

    /**
     * 查询 在线报告概况-节省分布详情酒店
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param productType
     * @param industries
     * @return
     * @throws Exception
     */

    List<OnlineReportSaveHotelDisDTO> queryHotelProportionDetail(
            String startTime, String endTime, String statisticalCaliber, String productType, List<String> industries,
            String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

    /**
     * 查询 在线报告概况-节省分布 累计
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportSaveHotelDisDTO> queryHotelAccProportionDetail(OnlineReportSaveProportionDetailRequestDTO request) throws Exception;

}
