package com.corpgovernment.resource.schedule.onlinereport.clickhouse.multilanguage;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.GeoCategoryEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-01-12 21:00
 * @desc
 */
@Repository
public class CorpHtlMultiLangDao extends AbstractCommonDao {

    /**
     * 机票
     * corpbi_onlinereport.dimcorp_flt_multi_language_airport  机场
     * corpbi_onlinereport.dimcorp_flt_multi_language_airline  航线
     * corpbi_onlinereport.corp_geo_location_language_all       geo_category_id可以区分：10000=大洲 1=国家/地区 2=省 3=城市
     * <p>
     * 酒店
     * corpbi_onlinereport.dimcorp_htl_brandmultilang     品牌
     * corpbi_onlinereport.dimcorp_htl_mgrgroupmultilang  集团
     * corpbi_onlinereport.dimcorp_htl_roommultilang      房型
     * corpbi_onlinereport.dimcorp_htl_htlinfomultilang    酒店名称表待开发
     * corpbi_onlinereport.corp_geo_location_language_all       geo_category_id可以区分：10000=大洲 1=国家/地区 2=省 3=城市
     */

    public <T> List<T> queryGeoLocation(List<Integer> ids, String lang, Class<T> clazz, GeoCategoryEnum categoryEnum) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }

        sqlBuilder.append("select  " + categoryEnum.getGeoIdFieldName() + " as id, " + categoryEnum.getGeoNameFieldName() + " as multiLangName ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(categoryEnum.getTableName());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" 1 = 1 ");
        if (categoryEnum == GeoCategoryEnum.PROVINCE) {
            sqlBuilder.append(" and country_id = 1 ");
        }

        List<Object> paramList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(ids, categoryEnum.getGeoIdFieldName(), paramList));
        sqlBuilder.append(" and is_deleted = 0");
        // 查询clickhouse
        return commonListMySql(clazz, sqlBuilder.toString(), paramList);
    }

    /**
     * 查询 酒店品牌
     *
     * @param ids
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryHtlBrandMultiLang(List<Integer> ids, String lang, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ClickHouseTable clickHouseTable = ClickHouseTable.DIMCORP_HTL_BRANDMULTILANG;
        sqlBuilder.append("select brand as strId, name as multiLangName");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> paramList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlPartition(paramList, querySingleTablePartition(clickHouseTable)));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlSingleVal(paramList, lang, "locale"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(ids.stream().map(i -> String.valueOf(i)).collect(Collectors.toList()), "brand", paramList));
        sqlBuilder.append(" and is_deleted = '0'");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    /**
     * 查询 酒店集团
     *
     * @param ids
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryHtlGroupMultiLang(List<Integer> ids, String lang, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ClickHouseTable clickHouseTable = ClickHouseTable.DIMCORP_HTL_MGRGROUPMULTILANG;
        sqlBuilder.append("select mgrgroup as strId, name as multiLangName");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> paramList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlPartition(paramList, querySingleTablePartition(clickHouseTable)));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlSingleVal(paramList, lang, "locale"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(ids.stream().map(i -> String.valueOf(i)).collect(Collectors.toList()), "mgrgroup", paramList));
        sqlBuilder.append(" and is_deleted = '0'");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    /**
     * 查询 酒店房型
     *
     * @param ids
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryHtlRoomMultiLang(List<String> ids, String lang, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ClickHouseTable clickHouseTable = ClickHouseTable.DIMCORP_HTL_ROOMMULTILANG;
        sqlBuilder.append("select  id as strId, translate as multiLangName");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> paramList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlPartition(paramList, querySingleTablePartition(clickHouseTable)));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlSingleVal(paramList, lang, "locale"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCityNameSql(ids.stream().map(String::valueOf).collect(Collectors.toList()), "id", paramList));
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    /**
     * 查询 酒店信息
     *
     * @param ids
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryHtlInfoMultiLang(List<Integer> ids, String lang, Class<T> clazz) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ClickHouseTable clickHouseTable = ClickHouseTable.DIMCORP_HTL_HTLINFOMULTILANG;
        sqlBuilder.append("select  hotelid as id, hotelname as multiLangName");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> paramList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlPartition(paramList, querySingleTablePartition(clickHouseTable)));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlSingleVal(paramList, lang, "language"));
        sqlBuilder.append(BaseConditionPrebuilder.buildCityIdsSql(ids, "hotelid", paramList));
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }
}
