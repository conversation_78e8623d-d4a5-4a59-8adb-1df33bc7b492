package com.corpgovernment.resource.schedule.onlinereport.reportlib.consumeprofile;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ConsumeProfileInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportConsumeProfileRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.OrderAggBO;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.OrderConsumeBO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Service
public class ConsumeProfileBiz {

    @Autowired
    private FlightConsumeProfileBiz flightConsumeProfileBiz;

    @Autowired
    private HotelConsumeProfileBiz hotelConsumeProfileBiz;

    @Autowired
    private TrainConsumeProfileBiz trainConsumeProfileBiz;

    @Autowired
    private CarConsumeProfileBiz carConsumeProfileBiz;

    @Autowired
    private BusConsumeProfileBiz busConsumeProfileBiz;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

  /*  @Autowired
    private ConsumeProfileMapper consumeProfileMapper;*/

    
    public List<ConsumeProfileInfo> aggreationConsume(OnlineReportConsumeProfileRequest request) throws Exception {
       /* BaseQueryCondition baseQueryCondition = request.getBasecondition();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(baseQueryCondition);
        String startTime = baseQueryCondition.getStartTime();
        String endTime = baseQueryCondition.getEndTime();
        String lang = request.getLang();
        QueryReportAggDateDimensionEnum dimensionEnum = request.getDateDimension();
        // 当前
        Set<String> points = BizUtils.getDatePoints(baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime(), dimensionEnum);
        // 环比时间
        int offset = getOffset(dimensionEnum);
        List<String> momTimes = Arrays.asList(OrpDateTimeUtils.startTimeMom(startTime, offset), OrpDateTimeUtils.endTimeMom(endTime, offset));
        // 同比时间
        List<String> yoyTimes = Arrays.asList(OrpDateTimeUtils.startTimeMom(startTime, OrpConstants.TWELVE), OrpDateTimeUtils.endTimeMom(endTime, OrpConstants.TWELVE));
        List<QueryReportBuTypeEnum> queryReportBuTypeEnumList = request.getQueryBus();
        if (CollectionUtils.isEmpty(queryReportBuTypeEnumList)) {
            return null;
        }
        List<OrderAggBO> flightList = null;
        List<OrderAggBO> hotelList = null;
        List<OrderAggBO> trainList = null;
        List<OrderAggBO> carList = null;
        List<OrderAggBO> busList = null;
        List<OrderAggBO> vasList = null;
        for (QueryReportBuTypeEnum queryReportBuTypeEnum : queryReportBuTypeEnumList) {
            if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
                flightList = flightConsumeProfileBiz.aggreationConsume(baseQueryConditionDto, momTimes, yoyTimes, points, dimensionEnum, lang);
            } else if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
                hotelList = hotelConsumeProfileBiz.aggreationConsume(baseQueryConditionDto, momTimes, yoyTimes, points, dimensionEnum, lang);
            } else if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
                trainList = trainConsumeProfileBiz.aggreationConsume(baseQueryConditionDto, momTimes, yoyTimes, points, dimensionEnum, lang);
            } else if (queryReportBuTypeEnum == QueryReportBuTypeEnum.car) {
                carList = carConsumeProfileBiz.aggreationConsume(baseQueryConditionDto, momTimes, yoyTimes, points, dimensionEnum, lang);
            } else if (queryReportBuTypeEnum == QueryReportBuTypeEnum.bus) {
                busList = busConsumeProfileBiz.aggreationConsume(baseQueryConditionDto, momTimes, yoyTimes, points, dimensionEnum, lang, queryReportBuTypeEnum);
            } else if (queryReportBuTypeEnum == QueryReportBuTypeEnum.vaso) {
                vasList = busConsumeProfileBiz.aggreationConsume(baseQueryConditionDto, momTimes, yoyTimes, points, dimensionEnum, lang, queryReportBuTypeEnum);
            }
        }

        List<OrderAggBO> list = mergerData(flightList, hotelList, carList, trainList, busList, vasList);
        summary(list, lang);
        List<ConsumeProfileInfo> result = consumeProfileMapper.toProfileDTOs(list);
        result.sort((obj1, obj2) -> {
            return obj1.getAggDate().compareTo(obj2.getAggDate());
        });
        return result;*/
        return null;
    }


    private List<OrderAggBO> mergerData(List<OrderAggBO>... array) {
        List<OrderAggBO> result = new ArrayList<>();
        for (List<OrderAggBO> list : array) {
            Optional.ofNullable(list).orElse(new ArrayList<>()).forEach(i -> {
                if (result.stream().anyMatch(j -> StringUtils.equals(i.getAggDate(), j.getAggDate()))) {
                    result.stream().filter(j -> StringUtils.equals(i.getAggDate(), j.getAggDate()))
                            .findFirst()
                            .ifPresent(k -> {
                                k.getList().addAll(i.getList());
                            });
                } else {
                    result.add(i);
                }
            });
        }
        return result;
    }

    /**
     * 数据汇总
     *
     * @param list
     */
    private void summary(List<OrderAggBO> list, String lang) {
        // 排除机、酒、用车的sum，避免重复计算
        String excludeFlt = SharkUtils.get("Exceltopname.subtotalflight", lang);
        String excludeHtl = SharkUtils.get("Exceltopname.subtotalhotel", lang);
        String excludeCar = SharkUtils.get("Exceltopname.subtotalcar", lang);
        for (OrderAggBO item : list) {
            OrderConsumeBO sum = new OrderConsumeBO();
            sum.setAggType(SharkUtils.get("Index.sum", lang));
            sum.setTotalCurrentAmount(Optional.ofNullable(item.getList()).orElse(new ArrayList<>())
                    .stream()
                    .filter(i -> !StringUtils.equalsIgnoreCase(i.getAggType(), excludeFlt) &&
                            !StringUtils.equalsIgnoreCase(i.getAggType(), excludeHtl) &&
                            !StringUtils.equalsIgnoreCase(i.getAggType(), excludeCar))
                    .map(OrderConsumeBO::getTotalCurrentAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            sum.setTotalMomAmount(Optional.ofNullable(item.getList()).orElse(new ArrayList<>())
                    .stream()
                    .filter(i -> !StringUtils.equalsIgnoreCase(i.getAggType(), excludeFlt) &&
                            !StringUtils.equalsIgnoreCase(i.getAggType(), excludeHtl) &&
                            !StringUtils.equalsIgnoreCase(i.getAggType(), excludeCar))
                    .map(OrderConsumeBO::getTotalMomAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            sum.setTotalYoyAmount(Optional.ofNullable(item.getList()).orElse(new ArrayList<>())
                    .stream()
                    .filter(i -> !StringUtils.equalsIgnoreCase(i.getAggType(), excludeFlt) &&
                            !StringUtils.equalsIgnoreCase(i.getAggType(), excludeHtl) &&
                            !StringUtils.equalsIgnoreCase(i.getAggType(), excludeCar))
                    .map(OrderConsumeBO::getTotalYoyAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            sum.setMom(OrpReportUtils.divideWithPercent(sum.getTotalCurrentAmount().subtract(sum.getTotalMomAmount())
                    , sum.getTotalMomAmount().abs()));
            sum.setYoy(OrpReportUtils.divideWithPercent(sum.getTotalCurrentAmount().subtract(sum.getTotalYoyAmount())
                    , sum.getTotalYoyAmount().abs()));
            item.getList().add(sum);
        }
    }

    private int getOffset(QueryReportAggDateDimensionEnum dimensionEnum) {
        int offset = 1;
        switch (dimensionEnum) {
            case month:
                offset = 1;
                break;
            case quarter:
                offset = 3;
                break;
            case half:
                offset = 6;
                break;
            case year:
                offset = 12;
                break;
            default:
                break;
        }
        return offset;
    }
}
