package com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.TimeFilterTypeInfoDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.FlightReportEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder.buildRolePermission;

/**
 * <AUTHOR>
 * @date 2022-09-21 13:39
 * @desc
 */
@Service
@Repository
public abstract class AbstractDetaiDao extends AbstractCommonDao {


    public String queryDetailSql(BaseQueryConditionDTO request, List<Object> parmList) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = getClickHouseTable(BlueSpaceUtils.isForeign(request.getPos(), request.getBlueSpace()));
        sqlBuilder.append("select * ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        parmList.add(queryPartition(clickHouseTable));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(request, parmList));
        sqlBuilder.append(this.buildPreSqlTime(request.getTimeFilterList(), parmList));
        sqlBuilder.append(this.buildPreSqlOrderstatus(request.getOrderstatusList(), parmList));
        sqlBuilder.append(buildPreSqlOrderIds(request.getOrderIds(), parmList));
        sqlBuilder.append(buildPreSqlUser(request.getUsers(), parmList));
        sqlBuilder.append(buildPreSqlPassenger(request.getPassengers(), parmList));
        sqlBuilder.append(buildProductTypeCondition(request.getProductType()));
        sqlBuilder.append(buildPreSqlExceedStandard(request.getExceedStandard(), parmList));
        sqlBuilder.append(buildPreSqlEmployeId(request.getEmployeIds(), parmList));
        sqlBuilder.append(this.buildPreSqlSpecial());
        sqlBuilder.append(buildContractTypeCondition(request.getContractType()));
        sqlBuilder.append(buildClassTypeCondition(request.getClassType()));
        sqlBuilder.append(buildFlightAirlineCondition(request.getAirLines(), parmList));
        sqlBuilder.append(buildFlighNoCondition(request.getFlightNos(), parmList));
        sqlBuilder.append(buildHotelGroupCondition(request.getHotelGroups(), parmList));
        sqlBuilder.append(buildHotelCondition(request.getHotelIds(), parmList));
        sqlBuilder.append(buildRolePermission());
        // 添加卡片携带条件
        sqlBuilder.append(this.buildCardTakeawayCondition(request.getCardTakeawayCondition()));
        sqlBuilder.append(this.buildOrderBySql(request.getPos(), request.getBlueSpace()));
        if (request.getPager() != null){
            sqlBuilder.append(buildPage(parmList, request.getPager()));
        }
        // 查询clickhouse
        return sqlBuilder.toString();
    }

    protected String buildCardTakeawayCondition(String condition) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotEmpty(condition)) {
            sqlBuffer.append(" and (");
            sqlBuffer.append(condition);
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }

    /**
     * 酒店
     *
     * @param hotelIds
     * @return
     */
    protected String buildHotelCondition(List<String> hotelIds, List<Object> parmList) {
        return StringUtils.EMPTY;
    }

    protected String buildFlighNoCondition(List<String> flightNos, List<Object> parmList){
        return StringUtils.EMPTY;
    };

    protected String buildFlightAirlineCondition(List<String> airLines, List<Object> parmList){
        return StringUtils.EMPTY;
    };

    /**
     * 构建协议类型条件
     *
     * @param contractType
     * @return
     */
    protected String buildContractTypeCondition(String contractType) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (FlightReportEnum.AGREEMENT.getName().equals(contractType)) {
            sqlBuffer.append(" and order_type = '").append(FlightReportEnum.AGREEMENT.getName()).append("'");
        } else if (FlightReportEnum.NOT_AGREEMENT.getName().equals(contractType)) {
            sqlBuffer.append(" and order_type = 'M'");
        }
        return sqlBuffer.toString();
    }

    /**
     * 舱位类型
     *
     * @param classType
     * @return
     */
    protected String buildClassTypeCondition(String classType) {
        return StringUtils.EMPTY;
    }

    /**
     * 酒店集团
     *
     * @param hotelGroupIds
     * @return
     */
    protected String buildHotelGroupCondition(List<String> hotelGroupIds, List<Object> parmList) {
        return StringUtils.EMPTY;
    }

    public String queryDetailRecentOne(String corpId, String uid, String eid, List<Object> parmList) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = getClickHouseTable(false);
        sqlBuilder.append("select * ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        parmList.add(queryPartition(clickHouseTable));
        sqlBuilder.append(" and corp_corporation = ? ");
        parmList.add(corpId);
        List list = new ArrayList();
        if (StringUtils.isNotEmpty(uid)) {
            list.add("  uid = ? ");
            parmList.add(uid);
        }
        if (StringUtils.isNotEmpty(eid)) {
            list.add(" employe_id = ? ");
            parmList.add(eid);
        }
        sqlBuilder.append(" and (").append(org.apache.commons.lang3.StringUtils.join(list, OrpConstants.OR)).append(")");
        sqlBuilder.append(" order by order_date  desc limit 1");
        // 查询clickhouse
        return sqlBuilder.toString();
    }

    public List<Map> queryDetail(BaseQueryConditionDTO request) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String sql = this.queryDetailSql(request, parmList);
        // 查询clickhouse
        return commonList(sql, parmList);
    }

    public final <T> T queryDetailRecentOne(Class<T> clazz, BaseQueryConditionDTO request) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String sql = this.queryDetailRecentOne(request.getCorpIds().get(0), request.getUid(), request.getEid(), parmList);
        List<T> result = commonList(clazz, sql, parmList);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        // 查询clickhouse
        return result.get(0);
    }

    public final <T> List<T> queryDetail(Class<T> clazz, BaseQueryConditionDTO request) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String sql = this.queryDetailSql(request, parmList);
        // 查询clickhouse
        return commonList(clazz, sql, parmList);
    }

    public String detailCountSql(BaseQueryConditionDTO request, List<Object> parmList) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = this.getClickHouseTable(BlueSpaceUtils.isForeign(request.getPos(), request.getBlueSpace()));
        sqlBuilder.append("select count(1) as countAll");
        sqlBuilder.append(" from ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        parmList.add(queryPartition(clickHouseTable));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(request, parmList));
        sqlBuilder.append(this.buildPreSqlTime(request.getTimeFilterList(), parmList));
        sqlBuilder.append(this.buildPreSqlOrderstatus(request.getOrderstatusList(), parmList));
        sqlBuilder.append(buildPreSqlOrderIds(request.getOrderIds(), parmList));
        sqlBuilder.append(buildPreSqlUser(request.getUsers(), parmList));
        sqlBuilder.append(buildPreSqlPassenger(request.getPassengers(), parmList));
        sqlBuilder.append(buildProductTypeCondition(request.getProductType()));
        sqlBuilder.append(buildContractTypeCondition(request.getContractType()));
        sqlBuilder.append(buildClassTypeCondition(request.getClassType()));
        sqlBuilder.append(buildFlightAirlineCondition(request.getAirLines(), parmList));
        sqlBuilder.append(buildFlighNoCondition(request.getFlightNos(), parmList));
        sqlBuilder.append(buildHotelGroupCondition(request.getHotelGroups(), parmList));
        // 主酒店id
        sqlBuilder.append(buildHotelCondition(request.getHotelIds(), parmList));
        sqlBuilder.append(this.buildPreSqlSpecial());
        sqlBuilder.append(buildPreSqlExceedStandard(request.getExceedStandard(), parmList));
        sqlBuilder.append(buildPreSqlEmployeId(request.getEmployeIds(), parmList));
        // 添加卡片携带条件
        sqlBuilder.append(this.buildCardTakeawayCondition(request.getCardTakeawayCondition()));
        // 查询clickhouse
        return sqlBuilder.toString();
    }

    public Integer detailCount(BaseQueryConditionDTO request) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String sql = detailCountSql(request, parmList);
        // 查询clickhouse
        return commonCount(sql, parmList);
    }

    protected String buildTimeFilter(TimeFilterTypeInfoDTO timeFilterTypeInfo, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        // 预订时间
        if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate")) {
            if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                sqlBuffer.append(" and ").append("orderdt").append(" >= ? ");
                paramList.add(timeFilterTypeInfo.getStartTime());
            }
            if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                sqlBuffer.append(" and ").append("orderdt").append(" <= ? ");
                paramList.add(timeFilterTypeInfo.getEndTime());
            }
        }
        // 成交时间
        if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
            if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                sqlBuffer.append(" and ").append("report_date").append(" >= ? ");
                paramList.add(timeFilterTypeInfo.getStartTime());
            }
            if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                sqlBuffer.append(" and ").append("report_date").append(" <= ? ");
                paramList.add(timeFilterTypeInfo.getEndTime());
            }
        }
        return sqlBuffer.toString();
    }

    /**
     * 订单状态
     *
     * @param orderstatusList
     * @param parmList
     * @return
     */
    protected String buildPreSqlOrderstatus(List<String> orderstatusList, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(orderstatusList)) {
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionWtihAnd("order_status", orderstatusList, parmList));
        }
        return sqlBuffer.toString();
    }

    /**
     * 订单号
     *
     * @param orderIds
     * @param parmList
     * @return
     */
    protected String buildPreSqlOrderIds(List<String> orderIds, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(orderIds)) {
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionWtihAnd("order_id", orderIds, parmList));
        }
        return sqlBuffer.toString();
    }

    protected abstract ClickHouseTable getClickHouseTable(boolean isBluespace);

    /**
     * 时间条件
     *
     * @param timeFilterTypeInfoList
     * @param parmList
     * @return
     */
    protected abstract String buildPreSqlTime(List<TimeFilterTypeInfoDTO> timeFilterTypeInfoList, List<Object> parmList);

    /**
     * 出行人
     *
     * @param users    （uid, user_name）
     * @param parmList
     * @return
     */
    protected String buildPreSqlUser(List<String> users, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(users)) {
            sqlBuffer.append(" and (");
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionNoAnd("uid", users, parmList));
            sqlBuffer.append(" or ");
            // 对user_name进行加密处理
            List<String> encryptedUsers = users.stream()
                    .map(DbResultMapUtils::sm4Encrypt)
                    .collect(Collectors.toList());
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionNoAnd("upper(user_name)", encryptedUsers, parmList));
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }

    /**
     * 出行人
     *
     * @param passengers
     * @param parmList
     * @return
     */
    protected abstract String buildPreSqlPassenger(List<String> passengers, List<Object> parmList);

    /**
     * 产品类型
     *
     * @param productType
     * @return
     */
    protected abstract String buildProductTypeCondition(String productType);

    /**
     * 排序
     *
     * @return
     */
    protected abstract String buildOrderBySql(String pos, String blueSpace);

    /**
     * 超标
     *
     * @param exceedStandard
     * @param parmList
     * @return
     */
    protected abstract String buildPreSqlExceedStandard(List<String> exceedStandard, List<Object> parmList);


    /**
     * @return
     */
    protected String buildPreSqlSpecial() {
        return StringUtils.EMPTY;
    }

    /**
     * 持卡人员工编号
     *
     * @param employeIds
     * @param parmList
     * @return
     */
    protected String buildPreSqlEmployeId(List<String> employeIds, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(employeIds)) {
            sqlBuffer.append(" and (");
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionNoAnd("upper(employe_id)", employeIds, parmList));
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }
}
