package com.corpgovernment.resource.schedule.onlinereport.settlement;

import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.settlement
 * @description:
 * @author: p_yin
 * @create: 2024-01-11 18:04
 **/
@Data
public class SettlementAmountDTO {

    @Column(name = "checkAmount")
    BigDecimal checkAmount;

    @Column(name = "maxMonth")
    String maxMonth;

    @Column(name = "minMonth")
    String minMonth;

}
