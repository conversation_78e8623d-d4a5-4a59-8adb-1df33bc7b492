package com.corpgovernment.resource.schedule.onlinereport.enums.reportlib;

/**
 * <AUTHOR>
 * @date 2022-09-07 20:58
 * @desc
 */
public enum FlightOrderStatusEnum {
    // 全部退票
    R("RiskOrder.FltStatus7"),
    // 取消
    C("RiskOrder.FltStatus1"),
    // 部分退票
    T("RiskOrder.FltStatus3"),
    // 成交
    S("RiskOrder.FltStatus4");


    private String name;

    FlightOrderStatusEnum(String name){
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
