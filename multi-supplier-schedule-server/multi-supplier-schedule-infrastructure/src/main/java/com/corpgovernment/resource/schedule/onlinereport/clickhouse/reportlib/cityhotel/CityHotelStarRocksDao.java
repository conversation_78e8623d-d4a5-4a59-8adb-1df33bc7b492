package com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.cityhotel;


import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.TimeFilterTypeInfoDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022-10-31 14:52
 * @desc
 */
@Repository
public class CityHotelStarRocksDao extends AbstractCommonDao {

    private static final String CITY_HOTEL_SQL = "SELECT company.city as city, company.cityName as cityName\n" +
            ",company.sumRealPay as sumRealPay,company.sumQuantity as sumQuantity\n" +
            ",case WHEN CAST(coalesce(company.sumQuantity,0) AS DOUBLE) !=0 then CAST(coalesce(company.sumRealPay,0) AS DOUBLE)/ CAST(coalesce(company.sumQuantity,0) AS DOUBLE) else 0 end \n" +
            "as avgPrice\n" +
            ",company.sumRealPayC as sumRealPayC,company.sumQuantityC as sumQuantityC\n" +
            ",case WHEN CAST(coalesce(company.sumQuantityC,0) AS DOUBLE) !=0 then CAST(coalesce(company.sumRealPayC,0) AS DOUBLE)/ CAST(coalesce(company.sumQuantityC,0) AS DOUBLE) else 0 " +
            " end as avgPriceC\n" +
            ",case WHEN CAST(coalesce(corp.sumQuantityC,0) AS DOUBLE) !=0 then CAST(coalesce(corp.sumRealPayC,0) AS DOUBLE)/ CAST(coalesce(corp.sumQuantityC,0) AS DOUBLE) else 0 " +
            " end as avgPriceCCorp\n" +
            ",case WHEN CAST(coalesce(industry.sumQuantityC,0) AS DOUBLE) !=0 then CAST(coalesce(industry.sumRealPayC,0) AS DOUBLE)/ CAST(coalesce(industry.sumQuantityC,0) AS DOUBLE) else 0 " +
            " end as avgPriceCIndustry\n" +
            "\n" +
            ",company.sumRealPayM as sumRealPayM,company.sumQuantityM as sumQuantityM\n" +
            "\n" +
            ",case WHEN CAST(coalesce(company.sumQuantityM,0) AS DOUBLE) !=0 then CAST(coalesce(company.sumRealPayM,0) AS DOUBLE)/ CAST(coalesce(company.sumQuantityM,0) AS DOUBLE) else 0 " +
            " end as avgPriceM\n" +
            ",case WHEN CAST(coalesce(corp.sumQuantityM,0) AS DOUBLE) !=0 then CAST(coalesce(corp.sumRealPayM,0) AS DOUBLE)/ CAST(coalesce(corp.sumQuantityM,0) AS DOUBLE) else 0 " +
            " end as avgPriceMCorp\n" +
            ",case WHEN CAST(coalesce(industry.sumQuantityM,0) AS DOUBLE) !=0 then CAST(coalesce(industry.sumRealPayM,0) AS DOUBLE)/ CAST(coalesce(industry.sumQuantityM,0) AS DOUBLE) else 0 " +
            " end as avgPriceMIndustry\n" +
            "\n" +
            "from(SELECT city, %s AS cityName\n" +
            "    , SUM(real_pay) as sumRealPay\n" +
            "    , SUM(quantity) as sumQuantity\n" +
            "    , SUM(case when order_type = 'C' THEN real_pay else 0 END) as sumRealPayC\n" +
            "    , SUM(case when order_type = 'C' THEN quantity else 0 END) as sumQuantityC\n" +
            "    , SUM(case when order_type = 'M' THEN real_pay else 0 END) as sumRealPayM\n" +
            "    , SUM(case when order_type = 'M' THEN quantity else 0 END) as sumQuantityM\n" +
            "FROM olrpt_indexhoteldownload_all\n" +
            /*"FROM corpbi_onlinereport.olrpt_indexhoteldownload_all\n" +*/
            "WHERE %s %s %s %s %s\n" +
            "GROUP BY city,cityName order by sumRealPay desc) company\n" +
            "left join (\n" +
            "SELECT city\n" +
            "    , SUM(case when order_type = 'C' THEN real_pay else 0 END) as sumRealPayC\n" +
            "    , SUM(case when order_type = 'C' THEN quantity else 0 END) as sumQuantityC\n" +
            "    , SUM(case when order_type = 'M' THEN real_pay else 0 END) as sumRealPayM\n" +
            "    , SUM(case when order_type = 'M' THEN quantity else 0 END) as sumQuantityM\n" +
            "FROM olrpt_indexhoteldownload_all\n" +
            /*"FROM corpbi_onlinereport.olrpt_indexhoteldownload_all\n" +*/
            "WHERE %s %s %s %s\n" +
            "GROUP BY city ) corp\n" +
            " on company.city = corp.city\n" +
            "left join(\n" +
            "SELECT city\n" +
            "    , SUM(case when order_type = 'C' THEN real_pay else 0 END) as sumRealPayC\n" +
            "    , SUM(case when order_type = 'C' THEN quantity else 0 END) as sumQuantityC\n" +
            "    , SUM(case when order_type = 'M' THEN real_pay else 0 END) as sumRealPayM\n" +
            "    , SUM(case when order_type = 'M' THEN quantity else 0 END) as sumQuantityM\n" +
            "FROM olrpt_indexhoteldownload_all\n" +
            /*"FROM corpbi_onlinereport.olrpt_indexhoteldownload_all\n" +*/
            "WHERE %s %s %s %s\n" +
            "GROUP BY city) industry\n" +
            "on company.city = industry.city";

    /**
     * 查询 在线报告概览
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreation(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String lang, Integer star)
            throws Exception {
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
        List<Object> parmList = new ArrayList<>();
        String partition = queryPartition(clickHouseTable);
        String cityName = SharkUtils.isEN(lang) ? "city_name_en" : "city_name";
        List<String> industryList = StringUtils.isNotEmpty(requestDto.getIndustryType()) ? Arrays.asList(requestDto.getIndustryType()) : null;
        // %s %s %s %s 分区公司公共条件条件，时间，酒店类型，星级，订单号
        String sql = String.format(CITY_HOTEL_SQL
                , cityName, BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList, partition), getHotelOrderTypeCondition(productType),
                getStarCondition(star), buildPreSqlOrderIds(requestDto.getOrderIds(), parmList)
                , buildTimeFilter(requestDto.getTimeFilterList(), parmList)
                , BaseConditionPrebuilder.buildCorpPreSqlNoTime(parmList, partition), getHotelOrderTypeCondition(productType),
                getStarCondition(star)
                , buildTimeFilter(requestDto.getTimeFilterList(), parmList)
                , BaseConditionPrebuilder.buildIndustryPreSqlNoTime(industryList, partition, parmList), getHotelOrderTypeCondition(productType),
                getStarCondition(star)
                , buildTimeFilter(requestDto.getTimeFilterList(), parmList));
        return commonList(clazz, sql, parmList);
    }

    public String getStarCondition(Integer star) {
        if (Objects.isNull(star) || star == 0) {
            return StringUtils.EMPTY;
        } else if (star <= 2) {
            return " and star <= 2";
        } else {
            return String.format(" and star = %d", star);
        }
    }

    protected String buildTimeFilter(List<TimeFilterTypeInfoDTO> timeFilterTypeInfoList, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        for (TimeFilterTypeInfoDTO timeFilterTypeInfo : timeFilterTypeInfoList) {
            // 预订时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    sqlBuffer.append(" and ").append("orderdt").append(" >= ? ");
                    paramList.add(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    sqlBuffer.append(" and ").append("orderdt").append(" <= ? ");
                    paramList.add(timeFilterTypeInfo.getEndTime());
                }
            }
            // 成交时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    sqlBuffer.append(" and ").append("report_date").append(" >= ? ");
                    paramList.add(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    sqlBuffer.append(" and ").append("report_date").append(" <= ? ");
                    paramList.add(timeFilterTypeInfo.getEndTime());
                }
            }
        }
        return sqlBuffer.toString();
    }

    /**
     * 订单号
     *
     * @param orderIds
     * @param parmList
     * @return
     */
    protected String buildPreSqlOrderIds(List<String> orderIds, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(orderIds)) {
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionWtihAnd("order_id", orderIds, parmList));
        }
        return sqlBuffer.toString();
    }
}
