package com.corpgovernment.resource.schedule.pricecomparison.mysql.entity;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/11/22
 */
@Data
@Table(name = "mb_job_flight_top_compare")
public class MbJobFlightTopCompareDo {
    @Id
    private Long id;
    // 航线类型:I-国际,N-国内
    private String flightType;
    // 出发城市三字码
    private String departCityCode;
    // 到达城市三字码
    private String arriveCityCode;
}
