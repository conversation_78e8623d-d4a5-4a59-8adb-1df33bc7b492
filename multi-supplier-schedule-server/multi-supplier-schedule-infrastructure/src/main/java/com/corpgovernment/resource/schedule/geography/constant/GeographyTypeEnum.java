package com.corpgovernment.resource.schedule.geography.constant;

import com.ctrip.corp.obt.generic.utils.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR> zhang
 * @date 2024/4/10 16:51
 */
public enum GeographyTypeEnum {

    GEOGRAPHY_CTRIP_COUNTRY("COUNTRY"),
    GEOGRAPHY_CTRIP_PROVINCE("PROVINCE"),
    GEOGRAPHY_CTRIP_CITY("CITY"),
    GEOGRAPHY_CTRIP_AIRPORT("AIRPORT"),
    GEOGRAPHY_CTRIP_TRAIN_STATION("TRAINSTATION"),
    GEOGRAPHY_CTRIP_BUS_STATION("BUSSTATION"),
    <PERSON>OGRAPHY_CTRIP_CORP_BRAND("CORPBRAND"),
    GEOGRAPHY_CTRIP_DOMESTIC_ZONE("DOMESTICZONE"),
    GEOGRAPHY_CTRIP_TIME_ZONE("TIMEZONE"),
    GEOGRAPHY_CTRIP_METRO_LINE("METROLINE"),
    GEOGRAPHY_CTRIP_CITYMARK("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>RK"),
    GEOGRAPHY_CTRIP_HOTEL_CITY("HOTELCITY"),
    GEOGRAPHY_MEIYA_CITY("MYCITY")
    ;

    private final String geographyType;

    GeographyTypeEnum(String geographyType) {
        this.geographyType = geographyType;
    }

    public String getGeographyType() {
        return this.geographyType;
    }

    public static GeographyTypeEnum getGeographyEnumByType(String type){
        if (StringUtils.isBlank(type)){
            return null;
        }
        GeographyTypeEnum[] values = GeographyTypeEnum.values();
        for (GeographyTypeEnum value : values) {
            if (Objects.equals(value.getGeographyType(),type)){
                return value;
            }
        }
        return null;
    }

}
