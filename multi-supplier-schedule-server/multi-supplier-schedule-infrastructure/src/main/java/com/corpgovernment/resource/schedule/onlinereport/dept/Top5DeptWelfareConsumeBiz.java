package com.corpgovernment.resource.schedule.onlinereport.dept;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.WelfareDeptDisplayIndicatorConstant;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptUidConsumeDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptUidConsumeDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopDeptConsumeDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportWelfareTopDeptConsume;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.WelfareTopDeptConsumeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.WelfareDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.WelfareDeptDetailDTO;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.dept.sr.SrOnlineReportDeptConsumeDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.convert.TopDeptMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.PaginationHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Service
@Slf4j
public class Top5DeptWelfareConsumeBiz {

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private TopDeptMapper topDeptMapper;

    @Autowired
    private SrOnlineReportDeptConsumeDaoImpl srReportConsumeDao;

    public WelfareTopDeptConsumeInfo topDeptConsume(OnlineReportTopDeptConsumeDetailRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        baseQueryConditionDto.setLang(request.getLang());
        int topLimit = Optional.ofNullable(request.getTopLimit()).orElse(OrpConstants.HUNDRED_1);
        AnalysisObjectEnum downObjectEnum = request.getDrillDownObjectEnum();
        List<WelfareDeptDetailDTO> deptConsumeList = srReportConsumeDao.topDeptAnalysis(baseQueryConditionDto, request.getAnalysisObjectEnum(),
                WelfareDeptDetailDTO.class, request.getQueryBu(), request.getAnalysisObjectOrgInfo(), downObjectEnum, request.getDrillDownVal());
        WelfareTopDeptConsumeInfo result = new WelfareTopDeptConsumeInfo();
        WelfareDeptDTO sumAll = new WelfareDeptDTO();
        List<OnlineReportWelfareTopDeptConsume> topList = new ArrayList<>();
        WelfareDeptDTO sumOther = new WelfareDeptDTO();
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        if (CollectionUtils.isNotEmpty(deptConsumeList)) {
            List<WelfareDeptDTO> consumeList = this.convertToWelfareDeptList(deptConsumeList);
            // 获得排序指标
            String sort = (String) map.get("sortStatistics");
            if (StringUtils.equalsIgnoreCase(sort, WelfareDeptDisplayIndicatorConstant.WELFARE_ALLOT_AMOUNT)) {
                consumeList.sort(Comparator.comparing(WelfareDeptDTO::getAllotAmount).reversed());
            } else if (StringUtils.equalsIgnoreCase(sort, WelfareDeptDisplayIndicatorConstant.WELFARE_RECYCLE_AMOUNT)) {
                consumeList.sort(Comparator.comparing(WelfareDeptDTO::getReturnAmount).reversed());
            } else if (StringUtils.equalsIgnoreCase(sort, WelfareDeptDisplayIndicatorConstant.WELFARE_RETURN_AMOUNT)) {
                consumeList.sort(Comparator.comparing(WelfareDeptDTO::getRefundAmount).reversed());
            } else if (StringUtils.equalsIgnoreCase(sort, WelfareDeptDisplayIndicatorConstant.WELFARE_DEDUCT_AMOUNT)) {
                consumeList.sort(Comparator.comparing(WelfareDeptDTO::getDeductAmount).reversed());
            }
            for (int i = 0; i < consumeList.size(); i++) {
                WelfareDeptDTO deptConsume = consumeList.get(i);
                selfAdd(sumAll, deptConsume);
                if (i < topLimit) {
                    topList.add(convertToWelfareTopDeptBO(deptConsume, StringUtils.EMPTY));
                } else {
                    // 超过topLimit以外的数据都聚合成“other”
                    selfAdd(sumOther, deptConsume);
                    result.setOtherConsume(convertToWelfareTopDeptBO(sumOther, "other"));
                }
            }
            result.setSumConsume(convertToWelfareTopDeptBO(sumAll, "all"));
        }
        result.setTopList(topList);
        return result;
    }


    public OnlineReportDeptUidConsumeDetailInfo deptUidConsumeDetailInfo(OnlineReportDeptUidConsumeDetailRequest request) throws Exception {
        OnlineReportDeptUidConsumeDetailInfo result = new OnlineReportDeptUidConsumeDetailInfo();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        baseQueryConditionDto.setLang(request.getLang());
        AnalysisObjectEnum downObjectEnum = request.getDrillDownObjectEnum();
        List<WelfareDeptDetailDTO> deptConsumeList = srReportConsumeDao.topDeptUidAnalysis(baseQueryConditionDto, AnalysisObjectEnum.UID,
                WelfareDeptDetailDTO.class, request.getQueryBu(), request.getAnalysisObjectOrgInfo(),
                downObjectEnum, request.getDrillDownVal(), true, request.getUser());
        List<OnlineReportWelfareTopDeptConsume> onlineReportDeptConsumes = new ArrayList<>();
        WelfareDeptDTO sumAll = new WelfareDeptDTO();
        if (CollectionUtils.isNotEmpty(deptConsumeList)) {
            List<WelfareDeptDTO> consumeList = this.convertToWelfareDeptList(deptConsumeList);
            BigDecimal sumAllotAmount = consumeList.stream().map(WelfareDeptDTO::getAllotAmount).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            BigDecimal sumRecycleAmount = consumeList.stream().map(WelfareDeptDTO::getReturnAmount).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            BigDecimal sumReturnAmount = consumeList.stream().map(WelfareDeptDTO::getRefundAmount).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            BigDecimal sumDeductAmount = consumeList.stream().map(WelfareDeptDTO::getDeductAmount).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            for (int i = 0; i < consumeList.size(); i++) {
                WelfareDeptDTO deptConsume = consumeList.get(i);
                selfAdd(sumAll, deptConsume);
                onlineReportDeptConsumes.add(convertToWelfareDeptDetailBO(deptConsume, StringUtils.EMPTY,
                        sumAllotAmount, sumRecycleAmount, sumReturnAmount, sumDeductAmount));
            }
            onlineReportDeptConsumes.sort(
                    Comparator.comparing(OnlineReportWelfareTopDeptConsume::getAllotAmount).reversed()
                            .thenComparing(OnlineReportWelfareTopDeptConsume::getDimId)
                            .thenComparing(OnlineReportWelfareTopDeptConsume::getDim));
            Pager pager = BizUtils.initPager(request.getPage());
            PaginationHelper<OnlineReportWelfareTopDeptConsume> paginationHelper = new PaginationHelper<>();
            result.setWelfareList(paginationHelper.paginateFromAero(onlineReportDeptConsumes, pager.getPageIndex(),
                    pager.getPageSize()));
            result.setTotalRecords(deptConsumeList.size());
        }
        return result;
    }

    private List<WelfareDeptDTO> convertToWelfareDeptList(List<WelfareDeptDetailDTO> deptConsumeList) {
        List<WelfareDeptDTO> result = Lists.newArrayListWithCapacity(deptConsumeList.size());
        for (WelfareDeptDetailDTO welfareDeptDetailDTO : deptConsumeList) {
            WelfareDeptDTO welfareDeptDTO = new WelfareDeptDTO();
            welfareDeptDTO.setAggId(welfareDeptDetailDTO.getAggId());
            welfareDeptDTO.setAggType(welfareDeptDetailDTO.getAggType());
            welfareDeptDTO.setAllotAmount(welfareDeptDetailDTO.getAllotAmount());
            welfareDeptDTO.setReturnAmount(welfareDeptDetailDTO.getReturnAmount());
            welfareDeptDTO.setRefundAmount(welfareDeptDetailDTO.getRefundAmount());
            welfareDeptDTO.setDeductAmount(welfareDeptDetailDTO.getDeductAmount());
            welfareDeptDTO.setTotalBalance(welfareDeptDetailDTO.getTotalBalance());
            result.add(welfareDeptDTO);
        }
        return result;
    }

    private void selfAdd(WelfareDeptDTO sumTop, WelfareDeptDTO deptConsume) {
        sumTop.setAllotAmount(OrpCommonUtils.addBigDecimal(deptConsume.getAllotAmount(), sumTop.getAllotAmount()));
        sumTop.setReturnAmount(OrpCommonUtils.addBigDecimal(deptConsume.getReturnAmount(), sumTop.getReturnAmount()));
        sumTop.setRefundAmount(OrpCommonUtils.addBigDecimal(deptConsume.getRefundAmount(), sumTop.getRefundAmount()));
        sumTop.setDeductAmount(OrpCommonUtils.addBigDecimal(deptConsume.getDeductAmount(), sumTop.getDeductAmount()));
    }


    /**
     * 转换->心程贝消费金额明细
     *
     * @param topDeptDTO
     * @param aggType
     * @return
     */
    private OnlineReportWelfareTopDeptConsume convertToWelfareDeptDetailBO(WelfareDeptDTO topDeptDTO, String aggType,
                                                                           BigDecimal sumAllotAmount, BigDecimal sumRecycleAmount,
                                                                           BigDecimal sumReturnAmount, BigDecimal sumDeductAmount) {
        OnlineReportWelfareTopDeptConsume bo = convertToWelfareTopDeptBO(topDeptDTO, aggType);
        // 发放金额占比
        bo.setAllotAmountPercent(OrpReportUtils.divideUp(topDeptDTO.getAllotAmount(), sumAllotAmount, OrpConstants.FOUR));
        // 回收金额占比
        bo.setReturnAmountPercent(OrpReportUtils.divideUp(topDeptDTO.getReturnAmount(), sumRecycleAmount, OrpConstants.FOUR));
        // 退还金额占比
        bo.setRefundAmountPercent(OrpReportUtils.divideUp(topDeptDTO.getRefundAmount(), sumReturnAmount, OrpConstants.FOUR));
        // 扣减金额占比
        bo.setDeductAmountPercent(OrpReportUtils.divideUp(topDeptDTO.getDeductAmount(), sumDeductAmount, OrpConstants.FOUR));
        // 余额
        bo.setTotalBalance(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalBalance()));
        return bo;
    }

    /**
     * 转换->心程贝top5消费金额
     *
     * @param topDeptDTO
     * @param aggType
     * @return
     */
    private OnlineReportWelfareTopDeptConsume convertToWelfareTopDeptBO(WelfareDeptDTO topDeptDTO, String aggType) {
        OnlineReportWelfareTopDeptConsume bo = new OnlineReportWelfareTopDeptConsume();
        bo.setDimId(StringUtils.trimToEmpty(topDeptDTO.getAggId()));
        bo.setDim(StringUtils.isEmpty(aggType) ? topDeptDTO.getAggType() : aggType);
        // 心程贝方法金额
        bo.setAllotAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getAllotAmount()));
        // 心程贝回收金额
        bo.setReturnAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getReturnAmount()));
        // 心程贝退还金额
        bo.setRefundAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getRefundAmount()));
        // 心程贝扣减金额
        bo.setDeductAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getDeductAmount()));
        return bo;
    }

}
