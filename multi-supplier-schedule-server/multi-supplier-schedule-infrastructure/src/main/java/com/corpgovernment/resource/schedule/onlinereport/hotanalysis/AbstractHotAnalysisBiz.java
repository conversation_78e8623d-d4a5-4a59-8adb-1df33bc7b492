package com.corpgovernment.resource.schedule.onlinereport.hotanalysis;

import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportHotAanlysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendPoint;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.dto.OnlineReportHotAnalysisDto;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.dto.OnlineReportHotAnalysisRequestBo;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/25 15:10
 * @description：
 * @modified By：
 * @version: $
 */
@Component
public abstract class AbstractHotAnalysisBiz implements HotAnalysis<OnlineReportHotAanlysisRequest> {

    @Autowired
    BaseQueryConditionMapper baseQueryConditionMapper;

    public OnlineReportHotAnalysisRequestBo mapper(OnlineReportHotAanlysisRequest request) {
        OnlineReportHotAnalysisRequestBo requestBo = new OnlineReportHotAnalysisRequestBo();
        requestBo.setBu(request.getQueryBu().name());
        requestBo.setBaseQueryCondition(baseQueryConditionMapper.toDTO(request.getBasecondition()));
        requestBo.setExtParams(request.getExtData());
        requestBo.setStartTime(request.getBasecondition().getStartTime());
        requestBo.setEndTime(request.getBasecondition().getEndTime());
        requestBo.setProductType(request.getProductType());
        return requestBo;
    }

    public OnlineReportTrendPoint mapPoint(OnlineReportHotAnalysisDto dto, List<Field> fields,
                                           BigDecimal totalAmount, BigDecimal totalQuantity) {
        OnlineReportTrendPoint point = new OnlineReportTrendPoint();
        Map<String, BigDecimal> data = new HashMap<>();
        point.setData(data);
        point.setAxis(dto.getDim());
        fields.forEach(f -> point.data.put(f.getName(), round2(field2Value(dto, f))));
        // 增加占比
        point.data.put("quantityRate", OrpReportUtils.divideWithPercent(dto.getSumQuantity(), totalQuantity));
        point.data.put("amountRate", OrpReportUtils.divideWithPercent(dto.getSumAmount(), totalAmount));
        return point;
    }

    public OnlineReportTrendPoint mapPoint(OnlineReportHotAnalysisDto dto, List<Field> fields,
                                           BigDecimal totalAmount, BigDecimal totalQuantity, BigDecimal totalPrice) {
        OnlineReportTrendPoint point = new OnlineReportTrendPoint();
        Map<String, BigDecimal> data = new HashMap<>();
        point.setData(data);
        point.setAxis(dto.getDim());
        fields.forEach(f -> point.data.put(f.getName(), round2(field2Value(dto, f))));
        // 增加占比
        point.data.put("quantityRate", OrpReportUtils.divideWithPercent(dto.getSumQuantity(), totalQuantity));
        point.data.put("amountRate", OrpReportUtils.divideWithPercent(dto.getSumAmount(), totalAmount));
        point.data.put("priceRate", OrpReportUtils.divideWithPercent(dto.getSumPrice(), totalPrice));
        return point;
    }

    public BigDecimal field2Value(Object target, Field filed) {
        BigDecimal value = new BigDecimal("0.0");
        try {
            filed.setAccessible(true);
            value = (BigDecimal) filed.get(target);
            if (value == null) {
                value = new BigDecimal("0.0");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    public BigDecimal round2(BigDecimal d) {
        return d.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public boolean isDownload(OnlineReportHotAanlysisRequest request){
        Map extMap = request.getExtData();
        return Objects.nonNull(extMap) && extMap.containsKey("download") && StringUtils.equalsIgnoreCase((String)extMap.get("download"), "T");
    }

}
