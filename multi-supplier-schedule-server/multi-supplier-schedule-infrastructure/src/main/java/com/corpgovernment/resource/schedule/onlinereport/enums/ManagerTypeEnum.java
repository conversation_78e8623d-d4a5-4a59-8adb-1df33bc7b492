package com.corpgovernment.resource.schedule.onlinereport.enums;

/**
 * <AUTHOR>
 * @date 2025/4/15 11:23
 * @description
 */

/**
 * 管理员类型
 */
public enum ManagerTypeEnum {

    /**
     * 超级管理员
     */
    SUPERADMIN(0),

    /**
     * 携程客户经理
     */
    CUSTOMERMANAGER(1),

    /**
     * 集团管理员
     */
    GROUPMANAGER(2),

    /**
     * 公司管理员
     */
    CORPMANAGER(3),

    /**
     * 普通用户
     */
    COMMONUSER(4),

    /**
     * 携程客户助理
     */
    CUSTOMERASSISTANT(5),

    /**
     * 管理人员
     */
    MANAGEMENT(6),

    /**
     * 运营人员
     */
    OPERATOR(7),

    /**
     * 实施经理
     */
    IMPLEMENTMANGER(8),

    /**
     * 销售经理
     */
    SALESMANGER(9);

    private final int value;

    ManagerTypeEnum(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public int getValue() {
        return value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public static ManagerTypeEnum findByValue(int value) {
        switch (value) {
            case 0:
                return SUPERADMIN;
            case 1:
                return CUSTOMERMANAGER;
            case 2:
                return GROUPMANAGER;
            case 3:
                return CORPMANAGER;
            case 4:
                return COMMONUSER;
            case 5:
                return CUSTOMERASSISTANT;
            case 6:
                return MANAGEMENT;
            case 7:
                return OPERATOR;
            case 8:
                return IMPLEMENTMANGER;
            case 9:
                return SALESMANGER;
            default:
                return null;
        }
    }
}