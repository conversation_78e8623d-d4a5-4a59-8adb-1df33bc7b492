package com.corpgovernment.resource.schedule.onlinereport.clickhouse.risk;

import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpRiskDetailCustomColumnPO;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.module.behaivor.RefundRebookDTO;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 * @date 2025/4/1 11:07
 * @description
 */
@Service
@Slf4j
public class CorpRiskOrderDetailCustomColumnDaoImpl extends AbstractCommonDao {

    // Insert method
    public int insert(CorpRiskDetailCustomColumnPO detail) throws SQLException {
        StringBuilder insertSql = new StringBuilder("INSERT INTO "+ ClickHouseTable.RISK_ORDER_DETAIL_COLUMN.getTable() +" (id, uid, riskSence, customColumns, datachange_createtime, datachange_lasttime) VALUES (?, ?, ?, ?, now(), now())");

        BiFunction<CorpRiskDetailCustomColumnPO, PreparedStatement, PreparedStatement> mapParameterFun = (req, stmt) -> {
            try {
                stmt.setLong(1, getNextId(ClickHouseTable.RISK_ORDER_DETAIL_COLUMN.getTable()));
                stmt.setString(2, req.getUid());
                stmt.setString(3, req.getRiskSence());
                stmt.setString(4, req.getCustomColumns());
            } catch (SQLException e) {
                log.error("insert build param error table:"+ClickHouseTable.RISK_ORDER_DETAIL_COLUMN.getTable(), e);
                return null;
            }
            return stmt;
        };

        // Assuming singleInsert is a method in the same class or accessible context
        return singleInsert(insertSql, detail, mapParameterFun);
    }

    // Update method
    public int udpateBySence(CorpRiskDetailCustomColumnPO detail) throws Exception {
        List<CorpRiskDetailCustomColumnPO> riskSence = queryByRiskSence(detail.getRiskSence(),detail.getUid());
        if (CollectionUtils.isNotEmpty(riskSence)){
            CorpRiskDetailCustomColumnPO corpRiskDetailCustomColumnPO = riskSence.get(0);
            corpRiskDetailCustomColumnPO.setCustomColumns(detail.getCustomColumns());
            return update(corpRiskDetailCustomColumnPO);
        }
        return insert(detail);
    }

    public int update(CorpRiskDetailCustomColumnPO detail) throws SQLException {
        StringBuilder updateSql = new StringBuilder("ALTER TABLE "+ ClickHouseTable.RISK_ORDER_DETAIL_COLUMN.getTable() +" UPDATE customColumns = ?, datachange_lasttime = now() WHERE id = ?");
        BiFunction<CorpRiskDetailCustomColumnPO, PreparedStatement, PreparedStatement> mapUpdateParameter = (req, stmt) -> {
            try {
                stmt.setString(1, req.getCustomColumns());
                stmt.setLong(2, req.getId());
            } catch (SQLException e) {
                log.error("update error table:"+ClickHouseTable.RISK_ORDER_DETAIL_COLUMN.getTable(), e);
                return null;
            }
            return stmt;
        };

        // Assuming updateBySql is a method in the same class or accessible context
        return updateBySql(updateSql, detail, mapUpdateParameter);
    }


    // Query method by riskSence
    public List<CorpRiskDetailCustomColumnPO> queryByRiskSence(String riskSence, String uid) throws Exception {
        String sql = "SELECT * FROM "+ ClickHouseTable.RISK_ORDER_DETAIL_COLUMN.getTable() +" WHERE riskSence = ? and uid = ?";
        List<Object> paramList = Lists.newArrayList(riskSence,uid);
        return commonList(CorpRiskDetailCustomColumnPO.class,sql, paramList);
    }

    public int update(String uid, String riskSence, String join) {
        try {
            CorpRiskDetailCustomColumnPO columnPO = new CorpRiskDetailCustomColumnPO();
            columnPO.setCustomColumns(join);
            columnPO.setRiskSence(riskSence);
            columnPO.setUid(uid);
            return udpateBySence(columnPO);
        }catch (Exception e){
            log.error("update error", e);
        }
        return 0;
    }

    public List<CorpRiskDetailCustomColumnPO> queryByUIDAndScene(String uid, String riskScene) {
        try {
            return queryByRiskSence(riskScene,uid);
        }catch (Exception e){
            log.error("queryByUIDAndScene error", e);
        }
        return Collections.emptyList();
    }
}
