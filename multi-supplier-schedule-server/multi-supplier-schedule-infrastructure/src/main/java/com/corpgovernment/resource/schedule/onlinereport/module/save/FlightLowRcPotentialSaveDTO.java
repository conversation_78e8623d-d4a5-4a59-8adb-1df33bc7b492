package com.corpgovernment.resource.schedule.onlinereport.module.save;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class FlightLowRcPotentialSaveDTO {

    @Column(name = "low_rid")
    @Type(value = Types.VARCHAR)
    private String lowRcCode;

    @Column(name = "low_rc_desc")
    @Type(value = Types.VARCHAR)
    private String lowRcDesc;

    @Column(name = "rcTimes")
    @Type(value = Types.INTEGER)
    private Integer rcTimes;

    @Column(name = "amount")
    @Type(value = Types.DOUBLE)
    private Double amount;

    @Column(name = "corpPriceAdj")
    @Type(value = Types.DOUBLE)
    private Double corpPriceAdj;

    @Column(name = "overAmount")
    @Type(value = Types.DOUBLE)
    private Double overAmount;

    @Column(name = "printPrice")
    @Type(value = Types.DOUBLE)
    private Double printPrice;
}
