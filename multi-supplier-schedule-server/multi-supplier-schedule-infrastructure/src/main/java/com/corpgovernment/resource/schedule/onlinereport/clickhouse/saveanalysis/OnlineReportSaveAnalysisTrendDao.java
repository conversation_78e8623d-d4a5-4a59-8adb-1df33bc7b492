package com.corpgovernment.resource.schedule.onlinereport.clickhouse.saveanalysis;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportOverviewTrendDao;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveTrendV2DTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/**
 * <AUTHOR>
 * @date ：Created in 2021/11/10 10:26
 * @description：
 * @modified By：
 * @version: $
 */
@Repository
@Slf4j
public class OnlineReportSaveAnalysisTrendDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "OnlineReportSaveAnalysisTrendDao";

    /**
     * 查询 在线报告概况-节省趋势机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryOnlineReportFlightTrend(OnlineTrendRequestDto request) throws Exception {
        String trendSql = buildFlightTrendSql(request);
        return queryBySql(trendSql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveTrendDTO.class, "queryOnlineReportFlightSaveTrend");
    }

    /**
     * 查询 在线报告概况-节省趋势机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryOnlineReportFlightTrendV2(OnlineTrendRequestDto request) throws Exception {
        String trendSql = buildFlightTrendSql(request);
        return queryBySql(trendSql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveTrendV2DTO.class, "queryOnlineReportFlightSaveTrend");
    }

    /**
     * 查询 在线报告概况-节省趋势酒店
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryOnlineReportHotelTrend(OnlineTrendRequestDto request) throws Exception {
        String trendSql = buildHotelTrendSql(request);
        return queryBySql(trendSql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportSaveTrendDTO.class, "queryOnlineReportHotelSaveTrend");
    }

    public String buildFlightTrendSql(OnlineTrendRequestDto requestDto) {
        return conditionWrapAsSubSql(requestDto.getDateDimension(), requestDto.getStartTime(),
                requestDto.getEndTime(),
                flightTrend(requestDto.getProductType(), requestDto.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                requestDto.getBaseQueryCondition());
    }

    public String buildHotelTrendSql(OnlineTrendRequestDto requestDto) {
        return conditionWrapAsSubSql(requestDto.getDateDimension(), requestDto.getStartTime(),
                requestDto.getEndTime(),
                hotelTrend(requestDto.getProductType(), requestDto.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                requestDto.getBaseQueryCondition());
    }

    public StringBuilder flightTrend(String productType, String statisticalCaliber) {
        // 产线 - 月/季/半年 - 日期
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {group_date} as date, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_3c, 0) else 0 end) AS cSave, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_3c, 0) else 0 end) AS netfare3c, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(quantity_3c, 0) else 0 end) AS quantity3c, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) AS premiumSave, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_premium, 0) else 0 end) AS netfarePremium, ");
        stringBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(quantity_premium, 0) else 0 end) AS quantityPremium, ");
        stringBuilder.append("SUM(coalesce(saving_price, 0)) AS controlSave, ");
        stringBuilder.append("sum(case when coalesce(saving_price, 0) != 0 then coalesce(netfare, 0) else 0 end) AS controlNetfare, ");
        stringBuilder.append("sum(case when coalesce(saving_price, 0) != 0 then coalesce(quantity, 0) else 0 end) AS controlQuantity ");
        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        // 国内 经济舱 两方+三方
        stringBuilder.append(" and audited <> 'F' ");
        stringBuilder.append(SaveCondition.getFlightClassCondition(productType));
        stringBuilder.append(" group by {group_date} ");
        return stringBuilder;
    }

    public StringBuilder hotelTrend(String productType, String statisticalCaliber) {
        // 产线 - 月/季/半年 - 日期
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {group_date} as date, ");
        stringBuilder.append("SUM(coalesce(save_amount_3c, 0)) as cSave, ");
        stringBuilder.append(String.format("if(SUM(CASE WHEN (producttype_all = '%s' and save_amount_3c is not null) then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_3c, 0))) * 100 / SUM(CASE WHEN (producttype_all = '%s' and save_amount_3c is not null) " +
                " then toFloat64(corp_real_pay) else 0 end), " +
                " 0) as cSaveRate, ", ta, ta));

        // 两房尊享节省及节省率
        stringBuilder.append("SUM(coalesce(save_amount_premium, 0)) as premiumSave, ");
        stringBuilder.append(String.format("if(SUM(CASE WHEN (producttype_all = '%s' and save_amount_premium is not null) then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_premium, 0))) * 100 / SUM(CASE WHEN (producttype_all = '%s' and save_amount_premium is not null) " +
                " then toFloat64(corp_real_pay) else 0 end), " +
                " 0) as premiumSaveRate, ", premium, premium));

        stringBuilder.append("SUM(coalesce(save_amount_promotion, 0)) as promotionSave, ");
        stringBuilder.append("if(SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(save_amount_promotion)) * 100 / SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then toFloat64(corp_real_pay) else 0 end)," +
                " 0) as promotionSaveRate, ");
        // 管控节省及节省率
        stringBuilder.append("SUM(coalesce(saving_price, 0)) as controlSave, ");
        stringBuilder.append("if(SUM(CASE WHEN coalesce(saving_price, 0) != 0 then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(saving_price)) * 100 / SUM(CASE WHEN coalesce(saving_price, 0) != 0 then toFloat64(corp_real_pay) else 0 end)," +
                " 0) as controlSaveRate, ");

        // 总节省率修改
        stringBuilder.append("SUM(coalesce(save_amount_3c, 0)) + SUM(coalesce(save_amount_premium, 0)) + SUM(coalesce(save_amount_promotion, 0)) + " +
                "SUM(coalesce(saving_price, 0)) save, ");
        stringBuilder.append(String.format("if(SUM(case when (producttype_all = '%s' and save_amount_3c is not null) or coalesce(save_amount_promotion, 0) <> 0 or " +
                " (producttype_all = '%s' and save_amount_premium is not null) or coalesce(saving_price, 0) != 0 " +
                " then corp_real_pay else 0 end) != 0, " +
                " toFloat64(SUM(coalesce(save_amount_3c, 0)) + SUM(coalesce(save_amount_premium, 0)) + SUM(coalesce(save_amount_promotion, 0)) + " +
                "SUM(coalesce(saving_price, 0))) * 100 / " +
                " toFloat64(SUM(case when (producttype_all = '%s' and save_amount_3c is not null) or coalesce(save_amount_promotion, 0) <> 0 or " +
                " (producttype_all = '%s' and save_amount_premium is not null) or coalesce(saving_price, 0) != 0 then toFloat64(corp_real_pay) else 0 end)), " +
                " 0) as saveRate ", ta, premium, ta, premium)
        );

        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        stringBuilder.append(SaveCondition.getHotelCondition(productType));
        stringBuilder.append(" group by {group_date} ");
        return stringBuilder;
    }


    private String conditionWrapAsSubSql(String dateDimension, String start, String end, String sqlTemplate,
                                         BaseQueryConditionDTO baseQueryConditionDTO) {
        String group_date = "";
        String statisticalCaliber = baseQueryConditionDTO.getStatisticalCaliber();
        boolean isBookingCaliber = StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        switch (dateDimension) {
            case "month":
                group_date = isBookingCaliber ? "firstday_of_month_odt" : "firstday_of_month";
                break;
            case "half":
                group_date = isBookingCaliber ? "firstday_of_halfyear_odt" : "firstday_of_halfyear";
                break;
            case "quarter":
                group_date = isBookingCaliber ? "firstday_of_quarter_odt" : "firstday_of_quarter";
                break;
            case "day":
                group_date = isBookingCaliber ? ORDERDT : REPORT_DATE;
                break;
            default:
                break;
        }
        String scope_condition = OnlineReportOverviewTrendDao.buildScopeFilter(baseQueryConditionDTO);
        sqlTemplate = sqlTemplate.replace("{group_date}", group_date)
                .replace("{scope_condition}", scope_condition)
                .replace("{start}", start)
                .replace("{end}", end);
        return sqlTemplate;
    }

    private PreparedStatement mapRequest(OnlineTrendRequestDto requestDto, PreparedStatement statement) {
        return statement;
    }
}
