package com.corpgovernment.resource.schedule.onlinereport.saveanalysis.impl;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendLegend;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendPoint;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendRequest;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.saveanalysis.OnlineReportSaveDetailDao;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.convert.SaveMapper;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.OnlineTrendFieldEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveTrendV2DTO;
import com.corpgovernment.resource.schedule.onlinereport.saveanalysis.AbstractSaveAnalysisTrend;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.corpgovernment.resource.schedule.onlinereport.enums.OnlineTrendFieldEnum.SAVE_AMOUNT;
import static com.corpgovernment.resource.schedule.onlinereport.enums.ReportTrendHeaderEnum.SAVE_FLIGHT_DETAIL_HEADER;
import static com.corpgovernment.resource.schedule.onlinereport.enums.ReportTrendHeaderEnum.SAVE_HOTEL_DETAIL_HEADER;


/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 11:02
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class SaveAnalysisDetailBiz extends AbstractSaveAnalysisTrend {

    @Autowired
    OnlineReportSaveDetailDao onlineReportSaveDetailDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private SaveMapper saveMapper;

    @Override
    
    public List<OnlineReportTrendPoint> trendBody(OnlineReportTrendRequest onlineReportTrendRequest) throws Exception {
        OnlineTrendRequestDto onlineTrendRequestDto = map(onlineReportTrendRequest, baseQueryConditionMapper);
        HashMap<String, OnlineReportTrendPoint> points = new HashMap<>();
        List<Field> fields = Arrays.stream(OnlineReportSaveTrendDTO.class.getDeclaredFields())
                .filter(f -> !(f.getName().equals("date") || f.getName().equals("dim")))
                .collect(Collectors.toList());
        List<OnlineReportSaveTrendDTO> bizPoints;
        List<OnlineReportSaveTrendDTO> totalBizPoints;
        List<OnlineReportSaveTrendDTO> blankBizPoints = blankResults(onlineReportTrendRequest);
        if (onlineReportTrendRequest.getQueryBu() == QueryReportBuTypeEnum.flight) {
            List<OnlineReportSaveTrendV2DTO> v2DTOS = onlineReportSaveDetailDao.queryOnlineReportFlightDetailV2(onlineTrendRequestDto);
            bizPoints = saveMapper.toTrendDTOS(v2DTOS);
            onlineTrendRequestDto.setDateDimension("");
            List<OnlineReportSaveTrendV2DTO> v2TotalDTOS = onlineReportSaveDetailDao.queryOnlineReportFlightDetailV2(onlineTrendRequestDto);
            totalBizPoints = saveMapper.toTrendDTOS(v2TotalDTOS);
        } else {
            bizPoints = onlineReportSaveDetailDao.queryOnlineReportHotelDetail(onlineTrendRequestDto);
            onlineTrendRequestDto.setDateDimension("");
            totalBizPoints = onlineReportSaveDetailDao.queryOnlineReportHotelDetail(onlineTrendRequestDto);
        }
        for (OnlineReportSaveTrendDTO onlineReportBuTrendDTO : bizPoints) {
            String date = onlineReportBuTrendDTO.getDate();
            OnlineReportTrendPoint point = points.getOrDefault(date, new OnlineReportTrendPoint(date, new HashMap<>()));
            fields.forEach(f -> point.data.put(f.getName(), BizUtils.round2(
                    BizUtils.field2Value(onlineReportBuTrendDTO, f))));
            points.put(date, point);
        }

        // 填充空缺日期
        for (OnlineReportSaveTrendDTO dto : blankBizPoints) {
            String blankDate = dto.getDate();
            OnlineReportTrendPoint existPoint = points.getOrDefault(blankDate, new OnlineReportTrendPoint(blankDate, new HashMap<>()));
            fields.forEach(f -> existPoint.data.putIfAbsent(f.getName(), new BigDecimal("0.00")));
            points.put(blankDate, existPoint);
        }

        // 按日期排序输出
        List<OnlineReportTrendPoint> resultPoints = new ArrayList<>(points.values());
        resultPoints.sort((o1, o2) -> (int) OrpDateTimeUtils.betweenDay(o2.axis, o1.axis));
        resultPoints.forEach(point ->
                point.axis = BizUtils.dateFormat(point.axis, onlineReportTrendRequest.getDateDimension()));

        // 加入总计数据
        String lang = onlineReportTrendRequest.getExtData().getOrDefault("lang", "");
        OnlineReportSaveTrendDTO totalBizPoint = new OnlineReportSaveTrendDTO();
        if (totalBizPoints.size() == 1) {
            totalBizPoint = totalBizPoints.get(0);
        }
        OnlineReportTrendPoint totalPoint = new OnlineReportTrendPoint(
                SharkUtils.getHeaderVal(SAVE_AMOUNT.getNameKey(), lang),
                new HashMap<>());
        OnlineReportSaveTrendDTO finalTotalBizPoint = totalBizPoint;
        fields.forEach(f -> totalPoint.data.put(f.getName(),
                BizUtils.round2(BizUtils.field2Value(finalTotalBizPoint, f)))
        );
        resultPoints.add(totalPoint);

        return resultPoints;
    }

    @Override
    public List<OnlineReportTrendLegend> trendLegend(OnlineReportTrendRequest request) throws Exception {
        List<OnlineTrendFieldEnum> bizLegends;
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
            bizLegends = SAVE_FLIGHT_DETAIL_HEADER.getHeaderList();
        } else {
            bizLegends = SAVE_HOTEL_DETAIL_HEADER.getHeaderList();
        }
        String lang = request.getExtData().getOrDefault("lang", "");
        HashMap<String, OnlineReportTrendLegend> legends = new LinkedHashMap<>();
        for (OnlineTrendFieldEnum bizLegend : bizLegends) {
            OnlineReportTrendLegend legend = legends.getOrDefault(bizLegend.getValueName(), new OnlineReportTrendLegend());
            legend.setName(SharkUtils.getHeaderVal(bizLegend.getNameKey(), lang));
            legend.setUnit(SharkUtils.getHeaderVal(bizLegend.getUnitKey(), lang));
            legend.setKey(bizLegend.getValueName());
            legends.put(bizLegend.getValueName(), legend);
        }
        return new ArrayList<>(legends.values());
    }

    private List<OnlineReportSaveTrendDTO> blankResults(OnlineReportTrendRequest request) {
        List<String> allDate = BizUtils.getDateRangeFullItem(
                request.getBasecondition().startTime, request.getBasecondition().endTime,
                request.getDateDimension()
        );
        List<OnlineReportSaveTrendDTO> blankData = new ArrayList<>();
        for (String date : allDate) {
            OnlineReportSaveTrendDTO dto = new OnlineReportSaveTrendDTO();
            dto.setDate(date);
            dto.setDim("");
            blankData.add(dto);
        }
        return blankData;
    }
}
