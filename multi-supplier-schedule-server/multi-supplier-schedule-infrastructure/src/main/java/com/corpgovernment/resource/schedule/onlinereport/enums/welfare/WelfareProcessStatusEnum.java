package com.corpgovernment.resource.schedule.onlinereport.enums.welfare;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;

/**
 * <AUTHOR>
 * @date 2025/1/9
 */
public enum WelfareProcessStatusEnum {
    /**
     * 待处理
     */
    PENDING("pending", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.welfare.processStatus.pending")),
    /**
     * 成功
     */
    SUCCESS("success", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.welfare.processStatus.success")),
    /**
     * 失败
     */
    FAIL("fail", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.welfare.processStatus.fail")),
    ;
    private String code;
    private String value;

    WelfareProcessStatusEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
