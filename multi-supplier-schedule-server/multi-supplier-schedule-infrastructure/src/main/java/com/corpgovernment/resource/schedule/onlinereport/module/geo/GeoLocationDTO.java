package com.corpgovernment.resource.schedule.onlinereport.module.geo;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-01-13 11:34
 * @desc
 */
@Data
public class GeoLocationDTO {

    // 地理数据ID
    @Column(name = "geo_id")
    @Type(value = Types.INTEGER)
    private Integer geoId;

    // ISO 639-2 Language Code
    @Column(name = "locale")
    @Type(value = Types.VARCHAR)
    private String locale;

    // 多语言全名
    @Column(name = "geo_place_name")
    @Type(value = Types.VARCHAR)
    private String geoPlaceName;

    // 10000=大洲 1=国家/地区 2=省 3=城市 4=区 5=景区(非行政划分类) 6=商圈(非行政划分类) 7=镇/乡/街道 9=洲下辖地区
    @Column(name = "geo_category_id")
    @Type(value = Types.INTEGER)
    private Integer geoCategoryId;
}
