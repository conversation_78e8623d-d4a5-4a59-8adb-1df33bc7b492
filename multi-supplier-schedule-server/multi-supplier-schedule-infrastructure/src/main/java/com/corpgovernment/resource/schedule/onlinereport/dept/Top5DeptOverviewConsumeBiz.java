package com.corpgovernment.resource.schedule.onlinereport.dept;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AnalysisObjectOrgInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptUidConsumeDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptUidConsumeDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOverviewTopDeptConsume;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopDeptConsumeDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OverviewTopDeptConsumeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.BusTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.CarTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.FltTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.HotTargetDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.HtlTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.OverviewDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.OverviewTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.TrainTopDeptDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.VasoTopDeptDTO;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportDeptConsumeDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptConsumeDaoService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.dept.sr.SrOnlineReportDeptConsumeDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.convert.TopDeptMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.partition.PartitionBiz;
import com.corpgovernment.resource.schedule.onlinereport.utils.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.PaginationHelper;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:22
 * @Desc 成本中心消费金额排名
 */
@Service
public class Top5DeptOverviewConsumeBiz {

    @Autowired
    private OnlineReportDeptConsumeDaoImpl reportConsumeDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private TopDeptMapper topDeptMapper;


    @Autowired
    private SrOnlineReportDeptConsumeDaoImpl srReportConsumeDao;


    @Autowired
    private PartitionBiz partitionBiz;

    @Qualifier("screenThreadPoolExecutor")
    @Resource
    private ThreadPoolExecutor executorService;

    /**
     * 获取部门消费dao
     *
     * @return
     */
    private OnlineReportDeptConsumeDaoService getDeptConsumeDaoService(BaseQueryConditionDTO baseQueryCondition) {
        boolean useSr = baseQueryCondition != null && BooleanUtils.isTrue(baseQueryCondition.useStarRocks);
        return useSr ? srReportConsumeDao : reportConsumeDao;
    }


    public OverviewTopDeptConsumeInfo topDeptConsume1(OnlineReportTopDeptConsumeDetailRequest request) throws Exception {
        OverviewTopDeptConsumeInfo result = new OverviewTopDeptConsumeInfo();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        baseQueryConditionDto.setLang(request.getLang());
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        // 获得排序指标
        String sort = (String) map.get("sortStatistics");
        int topLimit = Optional.ofNullable(request.getTopLimit()).orElse(OrpConstants.HUNDRED_1);
        if (StringUtils.equalsIgnoreCase(sort, "overviewAmount") || StringUtils.isEmpty(sort)) {
            AnalysisObjectEnum downObjectEnum = request.getDrillDownObjectEnum();
            List<OnlineReportOverviewTopDeptConsume> onlineReportDeptConsumes = new ArrayList<>();
            OverviewDeptDTO sumTop = new OverviewDeptDTO();
            OverviewDeptDTO sumOther = new OverviewDeptDTO();
            OverviewDeptDTO sumAll = new OverviewDeptDTO();
            List<OverviewDeptDTO> consumeList = new ArrayList<>();
            String drillDownVal = request.getDrillDownVal();
            AnalysisObjectOrgInfo analysisObjectOrgInfo = request.getAnalysisObjectOrgInfo();
            if (downObjectEnum != null) {
                consumeList = overviewDrillDown(downObjectEnum, baseQueryConditionDto, drillDownVal, StringUtils.EMPTY, analysisObjectOrgInfo, false);
            } else {
                consumeList = overviewAmount(request.getAnalysisObjectEnum(), baseQueryConditionDto, drillDownVal, StringUtils.EMPTY, analysisObjectOrgInfo, false);
            }
            if (CollectionUtils.isNotEmpty(consumeList)) {
                consumeList.sort(Comparator.comparing(OverviewDeptDTO::getTotalAmount).reversed().thenComparing(OverviewDeptDTO::getAggId));

                for (int i = 0; i < consumeList.size(); i++) {
                    OverviewDeptDTO deptConsume = consumeList.get(i);
                    selfAdd(sumAll, deptConsume);
                    if (i < topLimit) {
                        selfAdd(sumTop, deptConsume);
                        onlineReportDeptConsumes.add(convertToTopDeptBO(deptConsume, StringUtils.EMPTY));
                    } else {
                        // 超过topLimit以外的数据都聚合成“other”
                        selfAdd(sumOther, deptConsume);
                        result.setOtherConsume(convertToTopDeptBO(sumOther, "other"));
                    }
                }
                result.setSumConsume(convertToTopDeptBO(sumAll, "all"));
            }
            result.setTopList(onlineReportDeptConsumes);
            return result;
        } else if (StringUtils.equalsIgnoreCase(sort, "overviewCarbons") || StringUtils.equalsIgnoreCase(sort, "overviewOverAmount")
                || StringUtils.equalsIgnoreCase(sort, "overviewRcPercent")) {
            result = topDeptOtherConsume(request);
        }
        return result;
    }


    public OverviewTopDeptConsumeInfo topDeptOtherConsume(OnlineReportTopDeptConsumeDetailRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        baseQueryConditionDto.setLang(request.getLang());
        int topLimit = Optional.ofNullable(request.getTopLimit()).orElse(OrpConstants.HUNDRED_1);
        List<OverviewTopDeptDTO> deptConsumeList = null;
        OverviewTopDeptConsumeInfo result = new OverviewTopDeptConsumeInfo();
        List<OnlineReportOverviewTopDeptConsume> onlineReportDeptConsumes = new ArrayList<>();
        OverviewDeptDTO sumTop = new OverviewDeptDTO();
        OverviewDeptDTO sumOther = new OverviewDeptDTO();
        OverviewDeptDTO sumAll = new OverviewDeptDTO();
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        String sort = (String) map.get("sortStatistics");
        AnalysisObjectEnum downObjectEnum = request.getDrillDownObjectEnum();
        String drillDownVal = request.getDrillDownVal();

        List<OverviewDeptDTO> consumeList = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(sort, "overviewCarbons")) {
            // 机票、火车、用车有碳排
            deptConsumeList = getDeptConsumeDaoService(baseQueryConditionDto).topDeptCarbonsAnalysis(baseQueryConditionDto, request.getAnalysisObjectEnum(),
                    OverviewTopDeptDTO.class, request.getAnalysisObjectOrgInfo(), downObjectEnum, drillDownVal);
            consumeList = convertToOverviewDeptList(deptConsumeList);
            consumeList.sort(Comparator.comparing(OverviewDeptDTO::getTotalCarbonEmission).reversed().thenComparing(OverviewDeptDTO::getAggId));
        } else if (StringUtils.equalsIgnoreCase(sort, "overviewOverAmount")) {
            // 机票、酒店有RC损失
            deptConsumeList = getDeptConsumeDaoService(baseQueryConditionDto).topDeptOverAmountAnalysis(baseQueryConditionDto, request.getAnalysisObjectEnum(),
                    OverviewTopDeptDTO.class, request.getAnalysisObjectOrgInfo(), downObjectEnum, drillDownVal);
            consumeList = convertToOverviewDeptList(deptConsumeList);
            if (CollectionUtils.isNotEmpty(consumeList)) {
                consumeList.sort(Comparator.comparing(OverviewDeptDTO::getTotalOverAmount).reversed().thenComparing(OverviewDeptDTO::getAggId));
            }
        } else if (StringUtils.equalsIgnoreCase(sort, "overviewRcPercent")) {
            // 机票、酒店，火有RC占比
            deptConsumeList = getDeptConsumeDaoService(baseQueryConditionDto).topDeptRcPercentAnalysis(baseQueryConditionDto, request.getAnalysisObjectEnum(),
                    OverviewTopDeptDTO.class, request.getAnalysisObjectOrgInfo(), downObjectEnum, drillDownVal);
            consumeList = convertToOverviewDeptList(deptConsumeList);
            if (CollectionUtils.isNotEmpty(consumeList)) {
                consumeList.sort(Comparator.comparing(OverviewDeptDTO::getTotalRcPercent).reversed().thenComparing(OverviewDeptDTO::getAggId));
            }
        }
        if (CollectionUtils.isNotEmpty(consumeList)) {
            for (int i = 0; i < consumeList.size(); i++) {
                OverviewDeptDTO deptConsume = consumeList.get(i);
                selfAdd(sumAll, deptConsume);
                if (i < topLimit) {
                    selfAdd(sumTop, deptConsume);
                    OnlineReportOverviewTopDeptConsume temp = convertToTopDeptBO(deptConsume, StringUtils.EMPTY);
                    if (!needFilterZero(temp, sort)) {
                        onlineReportDeptConsumes.add(temp);
                    }
                } else {
                    // 超过topLimit以外的数据都聚合成“other”
                    selfAdd(sumOther, deptConsume);
                    result.setOtherConsume(convertToTopDeptBO(sumOther, "other"));
                }
            }
            result.setSumConsume(convertToTopDeptBO(sumAll, "all"));
        }
        // 是否需要查询行业和商旅
        String needIndustry = (String) map.get("needIndustry");
        // 下钻的时候，needIndustry为T的时候，需要查询公司和下钻对象的数据
        if (StringUtils.equalsIgnoreCase(needIndustry, "T") && StringUtils.equalsIgnoreCase(sort, "overviewRcPercent")) {
            if (downObjectEnum == null) {
                List<OverviewTopDeptDTO> corp = getDeptConsumeDaoService(baseQueryConditionDto)
                        .topDeptRcPercentAnalysisCorpAndIndustry(baseQueryConditionDto.getStatisticalCaliber(),
                                baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(),
                                request.getProductType(), getIndustryList(baseQueryConditionDto.getIndustryType()), DataTypeEnum.CORP, baseQueryConditionDto.getCompareSameLevel(),
                                baseQueryConditionDto.getConsumptionLevel(),
                                baseQueryConditionDto.getCompareCorpSameLevel());
                result.setCorpConsume(convertToTopDeptBO(CollectionUtils.isEmpty(corp) ? new OverviewDeptDTO() : convertToOverviewDeptList(corp).get(0), "corp"));
                List<OverviewTopDeptDTO> industry = getDeptConsumeDaoService(baseQueryConditionDto)
                        .topDeptRcPercentAnalysisCorpAndIndustry(baseQueryConditionDto.getStatisticalCaliber(),
                                baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(),
                                request.getProductType(), getIndustryList(baseQueryConditionDto.getIndustryType()), DataTypeEnum.INDUSTRY, baseQueryConditionDto.getCompareSameLevel(),
                                baseQueryConditionDto.getConsumptionLevel(),
                                baseQueryConditionDto.getCompareCorpSameLevel());
                result.setIndustryConsume(convertToTopDeptBO(CollectionUtils.isEmpty(industry) ? new OverviewDeptDTO() : convertToOverviewDeptList(industry).get(0)
                        , "industry"));
            } else {
                // 下钻的时候CorpConsume存的是公司的数据
                List<OverviewTopDeptDTO> corp = new ArrayList<>();
                OverviewTopDeptDTO overviewTopDeptDTO = new OverviewTopDeptDTO();
                Future<List<FltTopDeptDTO>> future1 = executorService.submit(new Callable<List<FltTopDeptDTO>>() {
                    @Override
                    public List<FltTopDeptDTO> call() throws Exception {
                        return getDeptConsumeDaoService(baseQueryConditionDto).topDeptAnalysisDown(baseQueryConditionDto,
                                FltTopDeptDTO.class, QueryReportBuTypeEnum.flight, request.getAnalysisObjectOrgInfo(), null);
                    }
                });
                Future<List<HtlTopDeptDTO>> future2 = executorService.submit(new Callable<List<HtlTopDeptDTO>>() {
                    @Override
                    public List<HtlTopDeptDTO> call() throws Exception {
                        return getDeptConsumeDaoService(baseQueryConditionDto).topDeptAnalysisDown(baseQueryConditionDto,
                                HtlTopDeptDTO.class, QueryReportBuTypeEnum.hotel, request.getAnalysisObjectOrgInfo(), null);
                    }
                });
                Future<List<TrainTopDeptDTO>> future3 = executorService.submit(new Callable<List<TrainTopDeptDTO>>() {
                    @Override
                    public List<TrainTopDeptDTO> call() throws Exception {
                        return getDeptConsumeDaoService(baseQueryConditionDto).topDeptAnalysisDown(baseQueryConditionDto,
                                TrainTopDeptDTO.class, QueryReportBuTypeEnum.train, request.getAnalysisObjectOrgInfo(), null);
                    }
                });
                List<FltTopDeptDTO> flight = future1.get();
                if (CollectionUtils.isNotEmpty(flight)) {
                    FltTopDeptDTO fltTopDeptDTO = flight.get(0);
                    overviewTopDeptDTO.setTotalRcTimes(OrpCommonUtils.getValue(overviewTopDeptDTO.getTotalRcTimes()) + OrpCommonUtils.getValue(fltTopDeptDTO.getTotalRcTimes()));
                    overviewTopDeptDTO.setTotalOrderCount(OrpCommonUtils.getValue(overviewTopDeptDTO.getTotalOrderCount()) +
                            OrpCommonUtils.getValue(fltTopDeptDTO.getTotalOrderCount()));
                }
                List<HtlTopDeptDTO> hotel = future2.get();
                if (CollectionUtils.isNotEmpty(hotel)) {
                    HtlTopDeptDTO htlTopDeptDTO = hotel.get(0);
                    overviewTopDeptDTO.setTotalRcTimes(OrpCommonUtils.getValue(overviewTopDeptDTO.getTotalRcTimes()) + OrpCommonUtils.getValue(htlTopDeptDTO.getTotalRcTimes()));
                    overviewTopDeptDTO.setTotalOrderCount(OrpCommonUtils.getValue(overviewTopDeptDTO.getTotalOrderCount()) +
                            OrpCommonUtils.getValue(htlTopDeptDTO.getTotalOrderCount()));
                }
                List<TrainTopDeptDTO> train = future3.get();
                if (CollectionUtils.isNotEmpty(train)) {
                    TrainTopDeptDTO trainTopDeptDTO = train.get(0);
                    overviewTopDeptDTO.setTotalRcTimes(OrpCommonUtils.getValue(overviewTopDeptDTO.getTotalRcTimes()) + OrpCommonUtils.getValue(trainTopDeptDTO.getTotalRcTimes()));
                    overviewTopDeptDTO.setTotalOrderCount(OrpCommonUtils.getValue(overviewTopDeptDTO.getTotalOrderCount()) +
                            OrpCommonUtils.getValue(trainTopDeptDTO.getTotalOrderCount()));
                }
                corp.add(overviewTopDeptDTO);
                result.setCorpConsume(convertToTopDeptBO(convertToOverviewDeptList(corp).get(0), "corp"));
            }
        }
        result.setTopList(onlineReportDeptConsumes);
        return result;
    }

    /**
     * 需要过滤 0值
     *
     * @param t
     * @param sort
     * @return
     */
    private boolean needFilterZero(OnlineReportOverviewTopDeptConsume t, String sort) {
        if (StringUtils.equalsIgnoreCase(sort, "overviewOverAmount")) {
            // 机票、酒店有RC损失
            return t.getTotalOverAmount() == null || BigDecimal.ZERO.compareTo(t.getTotalOverAmount()) == 0;
        } else if (StringUtils.equalsIgnoreCase(sort, "overviewRcPercent")) {
            // 机票、酒店，火有RC占比
            return t.getTotalRcPercent() == null || BigDecimal.ZERO.compareTo(t.getTotalRcPercent()) == 0;
        }
        return false;
    }

    private List<OverviewDeptDTO> convertToOverviewDeptList(List<OverviewTopDeptDTO> topDeptDTO) {
        List<OverviewDeptDTO> boList = new ArrayList<>();
        for (OverviewTopDeptDTO dto : topDeptDTO) {
            OverviewDeptDTO trainDeptDTO = topDeptMapper.convertOverviewTopDeptDTO(dto);
            trainDeptDTO.setTotalRcPercent(OrpReportUtils.divideUp(trainDeptDTO.getTotalRcTimes(), trainDeptDTO.getTotalOrderCount(), OrpConstants.FOUR));
            boList.add(trainDeptDTO);
        }
        return boList;
    }

    private void selfAdd(OverviewDeptDTO sumTop, OverviewDeptDTO deptConsume) {
        sumTop.setTotalAmount(Optional.ofNullable(deptConsume.getTotalAmount()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(sumTop.getTotalAmount()).orElse(BigDecimal.ZERO)));
        sumTop.setTotalOverAmount(Optional.ofNullable(deptConsume.getTotalOverAmount()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(sumTop.getTotalOverAmount())
                .orElse(BigDecimal.ZERO)));
        sumTop.setTotalRcTimes(Optional.ofNullable(deptConsume.getTotalRcTimes()).orElse(OrpConstants.ZERO) + Optional.ofNullable(sumTop.getTotalRcTimes())
                .orElse(OrpConstants.ZERO));
        sumTop.setTotalOrderCount(Optional.ofNullable(deptConsume.getTotalOrderCount()).orElse(OrpConstants.ZERO) + Optional.ofNullable(sumTop.getTotalOrderCount())
                .orElse(OrpConstants.ZERO));
        sumTop.setTotalCarbonEmission(Optional.ofNullable(deptConsume.getTotalCarbonEmission()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(sumTop.getTotalCarbonEmission())
                .orElse(BigDecimal.ZERO)));
    }

    private OnlineReportOverviewTopDeptConsume convertToTopDeptBO(OverviewDeptDTO topDeptDTO, String aggType) {
        OnlineReportOverviewTopDeptConsume bo = new OnlineReportOverviewTopDeptConsume();
        bo.setDimId(StringUtils.trimToEmpty(topDeptDTO.getAggId()));
        bo.setDim(StringUtils.isEmpty(aggType) ? topDeptDTO.getAggType() : aggType);
        bo.setTotalAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalAmount()));
        bo.setTotalOverAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalOverAmount()));
        bo.setTotalCarbons(OrpReportUtils.divideUp(topDeptDTO.getTotalCarbonEmission(), new BigDecimal(OrpConstants.THOUSAND), OrpConstants.TWO));
        bo.setTotalRcPercent(OrpReportUtils.divideUp(topDeptDTO.getTotalRcTimes(), topDeptDTO.getTotalOrderCount(), OrpConstants.FOUR));
        return bo;
    }

    protected List getIndustryList(String industryType) {
        if (StringUtils.isNotEmpty(industryType)) {
            return Arrays.asList(industryType);
        }
        return null;
    }


    public OnlineReportDeptUidConsumeDetailInfo topDeptUidConsume(OnlineReportDeptUidConsumeDetailRequest request) throws Exception {
        OnlineReportDeptUidConsumeDetailInfo result = new OnlineReportDeptUidConsumeDetailInfo();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        baseQueryConditionDto.setLang(request.getLang());
        baseQueryConditionDto.setOtherParams(request.getExtData());
        AnalysisObjectEnum downObjectEnum = request.getDrillDownObjectEnum();
        String lang = request.getLang();
        List<OverviewDeptDTO> sortedStudents = overviewDrillDown(downObjectEnum, baseQueryConditionDto, request.getDrillDownVal(), request.getUser(),
                request.getAnalysisObjectOrgInfo(), true);
        List<OnlineReportOverviewTopDeptConsume> onlineReportDeptConsumes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sortedStudents)) {
            BigDecimal sumAmount = sortedStudents.stream().map(OverviewDeptDTO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (int i = 0; i < sortedStudents.size(); i++) {
                OverviewDeptDTO deptConsume = sortedStudents.get(i);
                onlineReportDeptConsumes.add(convertToTopDeptBO(deptConsume, StringUtils.EMPTY, sumAmount));
            }
            Pager pager = BizUtils.initPager(request.getPage());
            PaginationHelper<OnlineReportOverviewTopDeptConsume> paginationHelper = new PaginationHelper<>();
            Map extMap = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
            // 是否需要分页
            List<OnlineReportOverviewTopDeptConsume> overViewList = paginationHelper.paginateFromAero(onlineReportDeptConsumes, pager.getPageIndex(), pager.getPageSize());
            String needTopDestination = (String) extMap.get("needTopDestination");
            // 兼容老逻辑needTopDestination默认值是空
            if (!StringUtils.equalsIgnoreCase(needTopDestination, "F")) {
                List<String> uids = overViewList.stream().map(OnlineReportOverviewTopDeptConsume::getDimId).collect(Collectors.toList());
                List<HotTargetDTO> hotFlightCityDTOList = getHotDest(uids, baseQueryConditionDto, lang);
                Map<String, List<HotTargetDTO>> group = hotFlightCityDTOList.stream().collect(Collectors.groupingBy(HotTargetDTO::getUid));
                Map map = sortMap(group);
                for (OnlineReportOverviewTopDeptConsume bo : overViewList) {
                    bo.setTopDestination(StringUtils.trimToEmpty((String) map.get(bo.getDimId())));
                }
            }
            result.setOverViewList(overViewList);
            result.setTotalRecords(onlineReportDeptConsumes.size());
        }
        return result;
    }

    private List<HotTargetDTO> getHotDest(List<String> uids, BaseQueryConditionDTO dto, String lang) throws ExecutionException, InterruptedException {
        int batchSize = ConfigUtils.getUidBatchSize();
        List<List<String>> list = OrpCommonUtils.splitList(uids, batchSize);
        List<HotTargetDTO> result = new ArrayList<>();
        List<Future<List<HotTargetDTO>>> futureList = new ArrayList<>();
        for (List<String> subList : list) {
            futureList.add(executorService.submit(new Callable<List<HotTargetDTO>>() {
                @Override
                public List<HotTargetDTO> call() throws Exception {
                    return getDeptConsumeDaoService(dto).getHotDestSql(dto, subList, HotTargetDTO.class, lang);
                }
            }));
        }
        for (Future<List<HotTargetDTO>> future : futureList) {
            result.addAll(future.get());
        }
        return result;
    }

    private List<OverviewDeptDTO> overviewDrillDown(AnalysisObjectEnum downObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, String drillDownVal,
                                                    String user, AnalysisObjectOrgInfo analysisObjectOrgInfo, boolean needHotAnalysis) throws Exception {
        Future<List<FltTopDeptDTO>> future1 = executorService.submit(new Callable<List<FltTopDeptDTO>>() {
            @Override
            public List<FltTopDeptDTO> call() throws Exception {
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptUidAnalysis(baseQueryConditionDto, AnalysisObjectEnum.UID,
                        FltTopDeptDTO.class, QueryReportBuTypeEnum.flight, analysisObjectOrgInfo,
                        downObjectEnum, drillDownVal, needHotAnalysis, user);
            }
        });
        Future<List<HtlTopDeptDTO>> future2 = executorService.submit(new Callable<List<HtlTopDeptDTO>>() {
            @Override
            public List<HtlTopDeptDTO> call() throws Exception {
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptUidAnalysis(baseQueryConditionDto, AnalysisObjectEnum.UID,
                        HtlTopDeptDTO.class, QueryReportBuTypeEnum.hotel, analysisObjectOrgInfo,
                        downObjectEnum, drillDownVal, needHotAnalysis, user);
            }
        });

        Future<List<TrainTopDeptDTO>> future3 = executorService.submit(new Callable<List<TrainTopDeptDTO>>() {
            @Override
            public List<TrainTopDeptDTO> call() throws Exception {
                if (StringUtils.equalsIgnoreCase(baseQueryConditionDto.getProductType(), "inter")) {
                    return Lists.newArrayList();
                }
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptUidAnalysis(baseQueryConditionDto, AnalysisObjectEnum.UID,
                        TrainTopDeptDTO.class, QueryReportBuTypeEnum.train, analysisObjectOrgInfo,
                        downObjectEnum, drillDownVal, needHotAnalysis, user);
            }
        });

        Future<List<CarTopDeptDTO>> future4 = executorService.submit(new Callable<List<CarTopDeptDTO>>() {
            @Override
            public List<CarTopDeptDTO> call() throws Exception {
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptUidAnalysis(baseQueryConditionDto, AnalysisObjectEnum.UID,
                        CarTopDeptDTO.class, QueryReportBuTypeEnum.car, analysisObjectOrgInfo,
                        downObjectEnum, drillDownVal, needHotAnalysis, user);
            }
        });

        Future<List<BusTopDeptDTO>> future5 = executorService.submit(new Callable<List<BusTopDeptDTO>>() {
            @Override
            public List<BusTopDeptDTO> call() throws Exception {

                return Lists.newArrayList();
/*                if (StringUtils.equalsIgnoreCase(baseQueryConditionDto.getProductType(), "inter")) {
                    return Lists.newArrayList();
                }
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptUidAnalysis(baseQueryConditionDto, AnalysisObjectEnum.UID,
                        BusTopDeptDTO.class, QueryReportBuTypeEnum.bus, analysisObjectOrgInfo,
                        downObjectEnum, drillDownVal, needHotAnalysis, user);*/
            }
        });
        Future<List<VasoTopDeptDTO>> future6 = executorService.submit(new Callable<List<VasoTopDeptDTO>>() {
            @Override
            public List<VasoTopDeptDTO> call() throws Exception {
                return Lists.newArrayList();
    /*            if (StringUtils.equalsIgnoreCase(baseQueryConditionDto.getProductType(), "inter")) {
                    return Lists.newArrayList();
                }
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptUidAnalysis(baseQueryConditionDto, AnalysisObjectEnum.UID,
                        VasoTopDeptDTO.class, QueryReportBuTypeEnum.vaso, analysisObjectOrgInfo,
                        downObjectEnum, drillDownVal, needHotAnalysis, user);*/
            }
        });
        List<FltTopDeptDTO> flghtDeptConsumeList = future1.get();
        List<HtlTopDeptDTO> hotelDeptConsumeList = future2.get();
        List<TrainTopDeptDTO> trainDeptConsumeList = future3.get();
        List<CarTopDeptDTO> carDeptConsumeList = future4.get();
        List<BusTopDeptDTO> busDeptConsumeList = future5.get();
        List<VasoTopDeptDTO> vasoDeptConsumeList = future6.get();
        List<OverviewDeptDTO> overviewDeptDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(flghtDeptConsumeList)) {
            for (FltTopDeptDTO dto : flghtDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                deptConsume.setTotalOverAmount(dto.getTotalOverAmount());
                deptConsume.setTotalRcTimes(dto.getTotalRcTimes());
                deptConsume.setTotalSaveAmount(OrpReportUtils.formatBigDecimal(dto.getTotalSaveAmount3c()).add(OrpReportUtils.formatBigDecimal(dto.getTotalSaveAmountPremium()))
                        .add(OrpReportUtils.formatBigDecimal(dto.getTotalControlSave())));
                deptConsume.setTotalSavePotential(OrpReportUtils.formatBigDecimal(dto.getTotalOverAmount().add(OrpReportUtils.formatBigDecimal(dto.getTotalRefundloss()))
                        .add(OrpReportUtils.formatBigDecimal(dto.getTotalRebookloss()))));
                deptConsume.setTotalOrderCount(dto.getTotalAllOrderCount());
                // 碳排放(转为千克)
                deptConsume.setTotalCarbonEmission(OrpReportUtils.divideUp(dto.getTotalCarbons(), new BigDecimal(OrpConstants.THOUSAND), OrpConstants.TWO));
                overviewDeptDTOList.add(deptConsume);
            }
        }

        if (CollectionUtils.isNotEmpty(hotelDeptConsumeList)) {
            for (HtlTopDeptDTO dto : hotelDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                deptConsume.setTotalRcTimes(dto.getTotalRcTimes());
                deptConsume.setTotalSaveAmount(OrpReportUtils.formatBigDecimal(dto.getTotalSaveAmount3c()).add(OrpReportUtils.formatBigDecimal(dto.getTotalSaveAmountPremium()))
                        .add(OrpReportUtils.formatBigDecimal(dto.getTotalControlSave())));
                deptConsume.setTotalSavePotential(OrpReportUtils.formatBigDecimal(dto.getTotalOverAmount()).add(OrpReportUtils.formatBigDecimal(dto.getTotalRefundloss())));
                deptConsume.setTotalOrderCount(dto.getTotalAllOrderCount());
                overviewDeptDTOList.add(deptConsume);
            }
        }

        if (CollectionUtils.isNotEmpty(trainDeptConsumeList)) {
            for (TrainTopDeptDTO dto : trainDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                deptConsume.setTotalOrderCount(dto.getTotalAllOrderCount());
                // 碳排放(转为千克)
                deptConsume.setTotalCarbonEmission(OrpReportUtils.divideUp(dto.getTotalCarbons(), new BigDecimal(OrpConstants.THOUSAND), OrpConstants.TWO));
                overviewDeptDTOList.add(deptConsume);
            }
        }

        if (CollectionUtils.isNotEmpty(carDeptConsumeList)) {
            for (CarTopDeptDTO dto : carDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                deptConsume.setTotalOrderCount(dto.getTotalOrderCount());
                // 碳排放(转为千克)
                deptConsume.setTotalCarbonEmission(OrpReportUtils.divideUp(dto.getTotalCarbons(), new BigDecimal(OrpConstants.THOUSAND), OrpConstants.TWO));
                overviewDeptDTOList.add(deptConsume);
            }
        }

        if (CollectionUtils.isNotEmpty(busDeptConsumeList)) {
            for (BusTopDeptDTO dto : busDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                deptConsume.setTotalOrderCount(dto.getTotalOrderCount());
                overviewDeptDTOList.add(deptConsume);
            }
        }

        if (CollectionUtils.isNotEmpty(vasoDeptConsumeList)) {
            for (VasoTopDeptDTO dto : vasoDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                deptConsume.setTotalOrderCount(dto.getTotalOrderCount());
                overviewDeptDTOList.add(deptConsume);
            }
        }
        Map<String, OverviewDeptDTO> groupedStudents = overviewDeptDTOList.stream()
                .collect(Collectors.toMap(
                        s -> s.getAggId() + "|" + s.getAggType(),
                        Function.identity(),
                        (s1, s2) -> {
                            s1.setTotalAmount(OrpReportUtils.formatBigDecimal(s1.getTotalAmount()).add(OrpReportUtils.formatBigDecimal(s2.getTotalAmount())));
                            s1.setTotalOrderCount(Optional.ofNullable(s1.getTotalOrderCount()).orElse(OrpConstants.ZERO) +
                                    Optional.ofNullable(s2.getTotalOrderCount()).orElse(OrpConstants.ZERO));
                            s1.setTotalSaveAmount(OrpReportUtils.formatBigDecimal(s1.getTotalSaveAmount()).add(OrpReportUtils.formatBigDecimal(s2.getTotalSaveAmount())));
                            s1.setTotalSavePotential(OrpReportUtils.formatBigDecimal(s1.getTotalSavePotential()).add(OrpReportUtils.formatBigDecimal(s2.getTotalSavePotential())));
                            return s1;
                        }
                ));
        List<OverviewDeptDTO> sortedStudents = new ArrayList<>(groupedStudents.values());
        sortedStudents.sort(Comparator.comparing(OverviewDeptDTO::getTotalAmount).reversed()
                .thenComparing(OverviewDeptDTO::getAggId).thenComparing(OverviewDeptDTO::getAggType));
        return sortedStudents;
    }

    /**
     * 要区分国内国际
     *
     * @param analysisObjectEnum
     * @param baseQueryConditionDto
     * @param drillDownVal
     * @param user
     * @param analysisObjectOrgInfo
     * @param needHotAnalysis
     * @return
     * @throws Exception
     */
    private List<OverviewDeptDTO> overviewAmount(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, String drillDownVal,
                                                 String user, AnalysisObjectOrgInfo analysisObjectOrgInfo, boolean needHotAnalysis) throws Exception {
        Future<List<FltTopDeptDTO>> future1 = executorService.submit(new Callable<List<FltTopDeptDTO>>() {
            @Override
            public List<FltTopDeptDTO> call() throws Exception {
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptAnalysis(baseQueryConditionDto, analysisObjectEnum,
                        FltTopDeptDTO.class, QueryReportBuTypeEnum.flight, analysisObjectOrgInfo,
                        null, drillDownVal);
            }
        });
        Future<List<HtlTopDeptDTO>> future2 = executorService.submit(new Callable<List<HtlTopDeptDTO>>() {
            @Override
            public List<HtlTopDeptDTO> call() throws Exception {
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptAnalysis(baseQueryConditionDto, analysisObjectEnum,
                        HtlTopDeptDTO.class, QueryReportBuTypeEnum.hotel, analysisObjectOrgInfo,
                        null, drillDownVal);
            }
        });

        Future<List<TrainTopDeptDTO>> future3 = executorService.submit(new Callable<List<TrainTopDeptDTO>>() {
            @Override
            public List<TrainTopDeptDTO> call() throws Exception {
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptAnalysis(baseQueryConditionDto, analysisObjectEnum,
                        TrainTopDeptDTO.class, QueryReportBuTypeEnum.train, analysisObjectOrgInfo,
                        null, drillDownVal);
            }
        });

        Future<List<CarTopDeptDTO>> future4 = executorService.submit(new Callable<List<CarTopDeptDTO>>() {
            @Override
            public List<CarTopDeptDTO> call() throws Exception {
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptAnalysis(baseQueryConditionDto, analysisObjectEnum,
                        CarTopDeptDTO.class, QueryReportBuTypeEnum.car, analysisObjectOrgInfo,
                        null, drillDownVal);
            }
        });

        Future<List<BusTopDeptDTO>> future5 = executorService.submit(new Callable<List<BusTopDeptDTO>>() {
            @Override
            public List<BusTopDeptDTO> call() throws Exception {
                if (StringUtils.equalsIgnoreCase(baseQueryConditionDto.getProductType(), "inter")) {
                    return Lists.newArrayList();
                }
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptAnalysis(baseQueryConditionDto, analysisObjectEnum,
                        BusTopDeptDTO.class, QueryReportBuTypeEnum.bus, analysisObjectOrgInfo,
                        null, drillDownVal);
            }
        });
        Future<List<VasoTopDeptDTO>> future6 = executorService.submit(new Callable<List<VasoTopDeptDTO>>() {
            @Override
            public List<VasoTopDeptDTO> call() throws Exception {
                if (StringUtils.equalsIgnoreCase(baseQueryConditionDto.getProductType(), "inter")) {
                    return Lists.newArrayList();
                }
                return getDeptConsumeDaoService(baseQueryConditionDto).topDeptAnalysis(baseQueryConditionDto, analysisObjectEnum,
                        VasoTopDeptDTO.class, QueryReportBuTypeEnum.vaso, analysisObjectOrgInfo,
                        null, drillDownVal);
            }
        });
        List<FltTopDeptDTO> flghtDeptConsumeList = future1.get();
        List<HtlTopDeptDTO> hotelDeptConsumeList = future2.get();
        List<TrainTopDeptDTO> trainDeptConsumeList = future3.get();
        List<CarTopDeptDTO> carDeptConsumeList = future4.get();
        List<BusTopDeptDTO> busDeptConsumeList = future5.get();
        List<VasoTopDeptDTO> vasoDeptConsumeList = future6.get();
        List<OverviewDeptDTO> overviewDeptDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(flghtDeptConsumeList)) {
            for (FltTopDeptDTO dto : flghtDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                deptConsume.setTotalOverAmount(dto.getTotalOverAmount());
                overviewDeptDTOList.add(deptConsume);
            }
        }

        if (CollectionUtils.isNotEmpty(hotelDeptConsumeList)) {
            for (HtlTopDeptDTO dto : hotelDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                overviewDeptDTOList.add(deptConsume);
            }
        }

        if (CollectionUtils.isNotEmpty(trainDeptConsumeList)) {
            for (TrainTopDeptDTO dto : trainDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                overviewDeptDTOList.add(deptConsume);
            }
        }

        if (CollectionUtils.isNotEmpty(carDeptConsumeList)) {
            for (CarTopDeptDTO dto : carDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                overviewDeptDTOList.add(deptConsume);
            }
        }

        if (CollectionUtils.isNotEmpty(busDeptConsumeList)) {
            for (BusTopDeptDTO dto : busDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                overviewDeptDTOList.add(deptConsume);
            }
        }

        if (CollectionUtils.isNotEmpty(vasoDeptConsumeList)) {
            for (VasoTopDeptDTO dto : vasoDeptConsumeList) {
                OverviewDeptDTO deptConsume = new OverviewDeptDTO();
                deptConsume.setAggId(dto.getAggId());
                deptConsume.setAggType(dto.getAggType());
                deptConsume.setTotalAmount(dto.getTotalAmount());
                overviewDeptDTOList.add(deptConsume);
            }
        }
        Map<String, OverviewDeptDTO> groupedStudents = overviewDeptDTOList.stream()
                .collect(Collectors.toMap(
                        s -> s.getAggId() + "|" + s.getAggType(),
                        Function.identity(),
                        (s1, s2) -> {
                            s1.setTotalAmount(OrpReportUtils.formatBigDecimal(s1.getTotalAmount()).add(OrpReportUtils.formatBigDecimal(s2.getTotalAmount())));
                            return s1;
                        }
                ));
        List<OverviewDeptDTO> sortedStudents = new ArrayList<>(groupedStudents.values());
        sortedStudents.sort(Comparator.comparing(OverviewDeptDTO::getTotalAmount).reversed()
                .thenComparing(OverviewDeptDTO::getAggId).thenComparing(OverviewDeptDTO::getAggType));
        return sortedStudents;
    }

    private Map sortMap(Map<String, List<HotTargetDTO>> mapOfEntities) {
        // 创建一个Map来存储每个Key对应的最大Entity
        Map<String, String> maxEntities = new HashMap<>();
        Comparator<HotTargetDTO> comparator = Comparator
                .comparingInt(HotTargetDTO::getCountSort)
                .thenComparing(HotTargetDTO::getTopTarget);
        for (Map.Entry<String, List<HotTargetDTO>> entry : mapOfEntities.entrySet()) {
            String key = entry.getKey();
            List<HotTargetDTO> entities = entry.getValue();
            if (entities != null && !entities.isEmpty()) {
                HotTargetDTO maxEntity = Collections.max(entities, comparator);
                maxEntities.put(key, maxEntity.getTopTarget());
            }
        }
        return maxEntities;
    }

    private OnlineReportOverviewTopDeptConsume convertToTopDeptBO(OverviewDeptDTO topDeptDTO, String aggType, BigDecimal sumAmount) {
        OnlineReportOverviewTopDeptConsume bo = new OnlineReportOverviewTopDeptConsume();
        bo.setDimId(StringUtils.trimToEmpty(topDeptDTO.getAggId()));
        bo.setDim(StringUtils.isEmpty(aggType) ? topDeptDTO.getAggType() : aggType);
        bo.setTotalSavePotential(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalSavePotential()));
        bo.setTotalSaveAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalSaveAmount()));
        bo.setTotalCarbons(OrpReportUtils.divideUp(topDeptDTO.getTotalCarbonEmission(), new BigDecimal(OrpConstants.THOUSAND), OrpConstants.TWO));
        bo.setTotalRcPercent(OrpReportUtils.divideUp(topDeptDTO.getTotalRcTimes(), topDeptDTO.getTotalOrderCount(), OrpConstants.FOUR));
        bo.setTotalAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getTotalAmount()));
        bo.setTotalAllOrderCount(topDeptDTO.getTotalOrderCount());
        // 消费金额占比
        bo.setAmountPercent(OrpReportUtils.divideUp(topDeptDTO.getTotalAmount(), sumAmount, OrpConstants.FOUR));
        return bo;
    }


    /**
     * 初始化分页参数
     *
     * @param pager
     * @return
     */
    protected Pager initPager(Pager pager) {
        pager = Objects.isNull(pager) ? new Pager(0L, OrpConstants.FIVE, OrpConstants.ZERO, OrpConstants.ZERO) : pager;
        pager.setPageSize((Objects.isNull(pager.getPageSize()) || pager.getPageSize() < OrpConstants.ZERO) ? OrpConstants.FIVE : pager.getPageSize());
        pager.setPageIndex((Objects.isNull(pager.getPageIndex()) || pager.getPageIndex() < OrpConstants.ZERO) ? OrpConstants.ZERO : pager.getPageIndex());
        return pager;
    }
}
