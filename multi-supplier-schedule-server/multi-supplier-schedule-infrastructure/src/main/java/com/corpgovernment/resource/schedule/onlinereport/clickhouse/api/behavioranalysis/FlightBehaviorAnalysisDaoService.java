package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.behavioranalysis;


import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PreOrderdateRange;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
public interface FlightBehaviorAnalysisDaoService {
    <T> List<T> behaviorAnaylsis(BaseQueryConditionDTO requestDto, Class<T> clazz, Map<String, String> extParams) throws Exception;

    /**
     * @param requestDto
     * @param clazz
     * @param industryList
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> refundOverView(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> industryList,
                               String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

    <T> List<T> refundTrend(BaseQueryConditionDTO requestDto, Class<T> clazz, Map<String, String> extParams) throws Exception;

    <T> List<T> rebookTrend(BaseQueryConditionDTO requestDto, Class<T> clazz, Map<String, String> extParams) throws Exception;

    /**
     * @param requestDto
     * @param clazz
     * @param industryList
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> rebookOverview(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> industryList,
                               String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

    <T> List<T> flightPreorderdateAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz, List<PreOrderdateRange> rangeList) throws Exception;

    <T> List<T> flightPreorderdateAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;

    default String getPreorderdateDim(PreOrderdateRange preOrderdateRange) {
        StringBuilder sqlBuilder = new StringBuilder();
        Integer start = preOrderdateRange.getStart();
        Integer end = preOrderdateRange.getEnd();
        if (Objects.nonNull(start) && Objects.nonNull(end)) {
            sqlBuilder.append(String.format("%d-%d", start, end));
        } else if (Objects.isNull(start) && Objects.nonNull(end)) {
            sqlBuilder.append(String.format("%d", end));
        } else if (Objects.nonNull(start) && Objects.isNull(end)) {
            sqlBuilder.append(String.format(">%d", start));
        }
        return sqlBuilder.toString();
    }
}
