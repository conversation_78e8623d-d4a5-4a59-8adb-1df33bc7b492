package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.geo;


import com.corpgovernment.resource.schedule.onlinereport.enums.GeoCategoryEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
public interface CorpGeoLocationDaoService {
    <T> List<T> queryGeoLocation(List<Integer> geoIds, String lang, Class<T> clazz,
                                 GeoCategoryEnum categoryEnum) throws Exception;

    <T> List<T> queryGeoLocation(String name, String lang, Class<T> clazz, GeoCategoryEnum categoryEnum) throws Exception;

    /**
     * 查询 在线报告概览
     *
     * @param geoIds
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryGeoLocation(List<Integer> geoIds, String lang, Class<T> clazz,
                                 List<GeoCategoryEnum> categoryEnums) throws Exception;
}
