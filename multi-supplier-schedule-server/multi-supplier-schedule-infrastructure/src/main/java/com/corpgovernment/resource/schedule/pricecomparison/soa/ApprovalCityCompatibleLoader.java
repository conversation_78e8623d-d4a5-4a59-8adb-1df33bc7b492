package com.corpgovernment.resource.schedule.pricecomparison.soa;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.corpgovernment.resource.schedule.domain.pricecomparison.model.QueryXrayComparePriceUploadDateRequestType;
import com.corpgovernment.resource.schedule.domain.pricecomparison.model.QueryXrayComparePriceUploadDateResponseType;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ApprovalCityCompatibleLoader {
    @Value("${flight.hotel.price.comparison.url:http://lis-outboud-gateway:8080/lis/gateway/queryXrayComparePriceUploadDate}")
    private String flightHotelPriceComparisonUrl;

    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    @Value("${flight.hotel.price.comparison.corp_id:SLCXCS}")
    private String CORP_ID;
    private final OkHttpClient httpClient;
    private static final int RESPONSECODE_SUCCESS = 2000; // 成功返回标志
    public ApprovalCityCompatibleLoader() {
        // Configure OkHttpClient with reasonable timeouts
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 获取机票和酒店比价任务
     * @param productType
     * @return
     */
    public QueryXrayComparePriceUploadDateResponseType queryXrayComparePriceUploadDate(Integer productType) {
        try {
            if(ObjectUtil.isNull(productType)) {
                log.error("产品类型为空");
                return null;
            }
            // 参数校验
            if (StrUtil.isBlank(flightHotelPriceComparisonUrl)) {
                log.error("获取机票和酒店比价任务URL未配置");
                return null;
            }

            QueryXrayComparePriceUploadDateRequestType request = new QueryXrayComparePriceUploadDateRequestType();
            request.setStartDate((int) Instant.now().minus(Duration.ofDays(1)).toEpochMilli());
            request.setEndDate((int) Instant.now().toEpochMilli());
            request.setProductType(productType);
            request.setCorpID(CORP_ID);
            log.info("获取机票和酒店比价任务QueryXrayComparePriceUploadDateRequestType request: {}", request);
            String requestBody = JsonUtils.toJsonString(request);
            // Create request body
            RequestBody body = RequestBody.create(JSON_MEDIA_TYPE, requestBody);

            // Build request
            Request httpRequest = new Request.Builder()
                    .url(flightHotelPriceComparisonUrl)
                    .post(body)
                    .header("Content-Type", "application/json")
                    .header("Accept", "application/json")
                    .build();

            // Execute request
            try (Response response = httpClient.newCall(httpRequest).execute()) {
                if(response == null || response.body() == null) {
                    throw new IOException("queryXrayComparePriceUploadDate Response is null");
                }

                if (!response.isSuccessful()) {
                    throw new IOException("queryXrayComparePriceUploadDate Unexpected response code: " + response.code());
                }
                QueryXrayComparePriceUploadDateResponseType responseObj = JsonUtils.parse(response.body().string(), QueryXrayComparePriceUploadDateResponseType.class);
                log.info("queryXrayComparePriceUploadDate responseObj:{}", JsonUtils.toJsonString(responseObj));
                if (responseObj == null ||
                        RESPONSECODE_SUCCESS != responseObj.getResponseCode() ||
                        CollectionUtils.isEmpty(responseObj.getRecords())) {
                    log.warn("响应数据异常: responseType={}", JsonUtils.toJsonString(responseObj));
                    return null;
                }
                return responseObj;
            }
        } catch (Exception e) {
            log.error("Failed to query QueryXrayComparePriceUploadDateRequestType API", e);
        }
        return null;
    }

}
