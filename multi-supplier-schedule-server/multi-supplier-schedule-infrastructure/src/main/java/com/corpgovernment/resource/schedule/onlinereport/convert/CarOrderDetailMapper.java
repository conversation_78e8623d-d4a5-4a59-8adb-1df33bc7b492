package com.corpgovernment.resource.schedule.onlinereport.convert;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportCarOrderInfo;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.CarOrderDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/*
 * <AUTHOR>
 * @date 2021/11/8 16:26
 * @Desc
 */

@Mapper(componentModel = "spring")
public interface CarOrderDetailMapper {

    CarOrderDetailMapper INSTANCE = Mappers.getMapper(CarOrderDetailMapper.class);

    @Mappings({
            @Mapping(target = "carbonEmission", expression = "java(convertUnit(carOrderDTO.getCarbonEmission()))"),
    })
    OnlineReportCarOrderInfo toDTO(CarOrderDTO carOrderDTO);

    List<OnlineReportCarOrderInfo> toDTOs(List<CarOrderDTO> carOrderDTOs);

    @Named("convertUnit")
    default Double convertUnit(Integer carbonEmission) {
        if (carbonEmission == null || carbonEmission == 0) {
            return 0D;
        }
        return OrpReportUtils.divideUp(carbonEmission, OrpConstants.HANDRED * OrpConstants.TEN, OrpConstants.TWO).doubleValue();
    }
}
