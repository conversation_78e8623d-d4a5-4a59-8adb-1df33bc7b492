package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailRequest;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.AbstractDetaiDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.TrainDetaiDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.TrainOrderDetailMapper;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.TrainOrderDTO;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.AbstractOrderDetailBiz;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Service
public class TrainOrderDetailBiz extends AbstractOrderDetailBiz {


    @Autowired
    private TrainDetaiDaoImpl detaiDao;

    @Autowired
    private TrainOrderDetailMapper detailMapper;

    @Override
    protected AbstractDetaiDao current(OnlineReportOrderDetailRequest request) {
        return detaiDao;
    }

    @Override
    public OnlineReportOrderDetailInfo queryOrderDetail(OnlineReportOrderDetailRequest request) throws Exception {
        OnlineReportOrderDetailInfo detailInfo = new OnlineReportOrderDetailInfo();
        List<TrainOrderDTO> list = queryOrderDetailEntity(request, TrainOrderDTO.class);
        if (CollectionUtils.isNotEmpty(list)) {
            detailInfo.setTrainOrderList(detailMapper.toDTOs(list));
        }
        return detailInfo;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.train;
    }
}
