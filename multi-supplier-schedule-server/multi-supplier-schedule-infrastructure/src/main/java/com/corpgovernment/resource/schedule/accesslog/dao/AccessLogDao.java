package com.corpgovernment.resource.schedule.accesslog.dao;

import com.corpgovernment.resource.schedule.domain.accesslog.model.BookingFaq;
import com.corpgovernment.resource.schedule.domain.accesslog.model.Top5InfoTraffic;
import com.corpgovernment.resource.schedule.domain.accesslog.model.req.ReportQueryReq;
import com.corpgovernment.resource.schedule.domain.accesslog.model.resp.Top5InfoResp;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.IntStream;

/**
 * <AUTHOR> Smith
 */
@Component
@Slf4j
public class AccessLogDao extends AbstractClickhouseBaseDao {

    public List<Top5InfoTraffic> queryBYD(ReportQueryReq reportQueryReq, String trafficType) throws Exception {
        List<Object> parmList = new ArrayList<>();

        StringBuilder sql = new StringBuilder("SELECT a.question_id, COUNT(*) AS pv, " +
                "COUNT(IF(a.source = 'WEB', 1, NULL)) AS web_pv, " +
                "COUNT(IF(a.source = 'APP', 1, NULL)) AS app_pv " +
                "FROM ods_faq_access_log a " +
                "LEFT JOIN ods_corpgovernment_mb_booking_faq b ON a.question_id = b.id " +
                "WHERE 1=1 ");

        if (StringUtils.isNotBlank(trafficType)) {
            sql.append(" AND a.type = ?");
            parmList.add(trafficType);
        }

        if (reportQueryReq.getStartTime() != null) {
            sql.append(" AND a.access_time >= ?");
            parmList.add(reportQueryReq.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (reportQueryReq.getEndTime() != null) {
            sql.append(" AND a.access_time <= ?");
            parmList.add(reportQueryReq.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }

        // 终端类型 WEB:PC APP:移动端 不传则查询全部
        if (StringUtils.isNotBlank(reportQueryReq.getSource())) {
            sql.append(" AND source = ?");
            parmList.add(reportQueryReq.getSource());
        }
        sql.append(" GROUP BY a.question_id ORDER BY pv DESC ");


        if (reportQueryReq.getTopNum() != null) {
            sql.append(" LIMIT ?");
            parmList.add(reportQueryReq.getTopNum());
        }

        return queryBySql(sql.toString(),
                parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (rs, clazz) -> {
                    try {
                        return mapResultList(rs, clazz); // 需要根据新的 Top5InfoTraffic 结构修改 mapResultList 方法
                    } catch (Exception e) {
                        log.error("query error:{}", ExceptionUtils.getStackTrace(e));
                    }
                    return Lists.newArrayList();
                },
                Top5InfoTraffic.class,
                "query");
    }

    public List<BookingFaq> queryQuestion(Collection<Integer> questionIds) throws Exception {
        if (CollectionUtils.isEmpty(questionIds)) {
            return Lists.newArrayList();
        }

        List<Object> parmList = new ArrayList<>(questionIds);

        StringBuilder query = new StringBuilder("SELECT * FROM ods_corpgovernment_mb_booking_faq WHERE id in (");
        IntStream.range(0, questionIds.size()).forEach(i -> query.append(i == 0 ? "?" : ",?"));
        query.append(")");


        return queryBySql(query.toString(),
                parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (rs, clazz) -> {
                    try {
                        return mapResultList(rs, clazz);
                    } catch (Exception e) {
                        log.error("query error:{}", ExceptionUtils.getStackTrace(e));
                    }
                    return Lists.newArrayList();
                },
                BookingFaq.class,
                "queryQuestion");
    }


    /**
     * <pre>
     *     select a.questionId,
     *        b.question,
     *        a.pv,
     *        a.web_pv,
     *        a.app_pv
     * from (select JSONExtractInt(data, 'questionId')                                             as questionId,
     *              count(1)                                                                       as pv,
     *              count(if(cast(JSONExtractString(data, 'source') as varchar) = 'WEB', 1, null)) as web_pv,
     *              count(if(cast(JSONExtractString(data, 'source') as varchar) = 'APP', 1, null)) as app_pv
     *       from wells_log_v1
     *       where createTime >= '2024-11-21'
     *         and createTime < '2024-11-29'
     *         -- and tenantID='cetc'
     *         and customKey = 'pc_h5_common_problem_expand'
     *       -- and JSONExtractString(data,'business')='flight'   -- flight/train/hotel/common
     *       group by JSONExtractInt(data, 'questionId')
     *          ) a
     *          left join (select cast(id as int) as id, question
     *                     from dim_mb_booking_faq
     *                     where d = formatDateTime(yesterday(), '%Y%m%d')) b on a.questionId = b.id;
     *                     </pre>
     */
    public List<Top5InfoResp> query(ReportQueryReq reportQueryReq, String trafficType) throws RuntimeException {
        log.info("query top5 info, req:{}，trafficType:{}", reportQueryReq, trafficType);
        if (reportQueryReq == null) {
            return Lists.newArrayList();
        }

        List<Object> parmList = new ArrayList<>();

        StringBuilder sql = new StringBuilder("select a.questionId, b.question as description,b.answer as answer, a.pv, a.web_pv, a.app_pv\n" +
                "from (select JSONExtractInt(data, 'questionId')             as questionId,\n" +
                "             count(1)                                             as pv,\n" +
                "             count(if(cast(JSONExtractString(data, 'source') as varchar) = 'WEB', 1, null)) as web_pv,\n" +
                "             count(if(cast(JSONExtractString(data, 'source') as varchar) = 'APP', 1, null)) as app_pv\n" +
                "      from wells_log_v1 where customKey = 'pc_h5_common_problem_expand'");

        // flight/train/hotel/common
        sql.append(" and JSONExtractString(data, 'business') = ?");
        parmList.add(trafficType);

        // tenantId（此处前端坚持使用 xClientId 字段为租户条件），所以需要通过apollo租户字典配置来获取租户ID
        if (StringUtils.isNotBlank(TenantContext.getTenantId())) {
            Config config = ConfigService.getConfig("corpgovernment.tenant.dictionary");
            String tenantId = TenantContext.getTenantId();
            log.info("the query tenantId is :{}", tenantId);
            String clientId = TenantContext.getClientId();
            log.info("the query clientId is :{}", clientId);

            String xClientId = config.getProperty(tenantId, clientId);
            log.info("the query xClientId is :{}", xClientId);
            if (StringUtils.isBlank(xClientId)) {
                throw new RuntimeException("the xClientId is null");
            }

            sql.append(" and xClientId = ?");
            parmList.add(xClientId);
        }

        if (reportQueryReq.getStartTime() != null) {
            //  sql.append(" AND JSONExtractString(data, 'accessTime') >= ?"); // accessTime字段的处理逻辑保持不变，如有需要，请根据实际情况修改
            //  parmList.add(reportQueryReq.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")));

            // 过滤createTime  使用createTime
            sql.append(" AND createTime >= ?");
            parmList.add(reportQueryReq.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (reportQueryReq.getEndTime() != null) {
            //   sql.append(" AND JSONExtractString(data, 'accessTime') <= ?"); // accessTime字段的处理逻辑保持不变，如有需要，请根据实际情况修改
            //   parmList.add(reportQueryReq.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")));

            // 过滤createTime  使用createTime
            sql.append(" AND createTime <= ?");
            parmList.add(reportQueryReq.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }


        // 终端类型 WEB:PC APP:移动端 不传则查询全部
        if (StringUtils.isNotBlank(reportQueryReq.getSource())) {
            sql.append(" AND visitParamExtractString(data, 'source') = ?");
            parmList.add(reportQueryReq.getSource());
        }

        sql.append(" group by JSONExtractInt(data, 'questionId')\n" +
                "         ) a\n" +
                "         left join (select cast(id as int) as id, question,answer\n" +
                "                    from dim_mb_booking_faq\n" +
                "                    where d = formatDateTime(yesterday(), '%Y%m%d')) b on a.questionId = b.id\n" +
                "order by a.pv desc\n");


        if (reportQueryReq.getTopNum() != null) {
            sql.append(" limit ?");
            parmList.add(reportQueryReq.getTopNum());
        }

        try {
            return queryBySqlFront(sql.toString(),
                    parmList,
                    (req, statement) -> mapCommonRequest(parmList, statement),
                    (rs, clazz) -> {
                        try {
                            return mapResultList(rs, clazz); // 这里需要根据新的 Top5InfoResp 结构修改 mapResultList 方法
                        } catch (Exception e) {
                            log.error("query error:{}", ExceptionUtils.getStackTrace(e));
                        }
                        return Lists.newArrayList();
                    },
                    Top5InfoResp.class,
                    "query");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
