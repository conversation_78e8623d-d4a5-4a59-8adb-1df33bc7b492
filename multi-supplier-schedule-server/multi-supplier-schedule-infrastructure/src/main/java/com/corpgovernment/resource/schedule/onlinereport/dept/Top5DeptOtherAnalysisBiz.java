package com.corpgovernment.resource.schedule.onlinereport.dept;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopFiveAnalysis;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopFiveDeptAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.DeptAnalysis;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportDeptOtherDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptOtherDaoService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.dept.sr.SrOnlineReportDeptOtherDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/*
 * <AUTHOR>
 *
 * @date 2021/12/15 14:22
 *
 * @Desc 前5部门其他指标分析
 */
@Service
public class Top5DeptOtherAnalysisBiz {

    @Autowired
    private OnlineReportDeptOtherDaoImpl onlineReportDeptOtherDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private SrOnlineReportDeptOtherDaoImpl srOnlineReportDeptOtherDao;

    /**
     * 获取部门消费dao
     * @return
     */
    private OnlineReportDeptOtherDaoService getDeptOtherDaoService(BaseQueryConditionDTO baseQueryCondition) {
        boolean useSr = baseQueryCondition != null && BooleanUtils.isTrue(baseQueryCondition.useStarRocks);
        return useSr ? srOnlineReportDeptOtherDao : onlineReportDeptOtherDao;
    }

    /**
     * 前五部门指标分析
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Cacheable
    public List<OnlineReportTopFiveAnalysis> deptOtherAnalysis(OnlineReportTopFiveDeptAnalysisRequest request)
            throws Exception {
        List<OnlineReportTopFiveAnalysis> list = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        AnalysisTypeEnum analysisTypeEnum = request.getAnalysisTypeEnum();
        Pager pager = request.getPage();
        pager = Objects.isNull(pager) ? new Pager(0L, OrpConstants.FIVE, OrpConstants.ZERO, OrpConstants.ZERO) : pager;
        pager.setPageSize((Objects.isNull(pager.getPageSize()) || pager.getPageSize() < OrpConstants.ZERO) ? OrpConstants.FIVE : pager.getPageSize());
        pager.setPageIndex((Objects.isNull(pager.getPageIndex()) || pager.getPageIndex() < OrpConstants.ZERO) ? OrpConstants.ZERO : pager.getPageIndex());
        List<DeptAnalysis> deptAnalysisList = getDeptOtherDaoService(baseQueryConditionDto).deptAnalysis(baseQueryConditionDto,
                analysisTypeEnum, analysisObjectEnum, DeptAnalysis.class, pager);
        if (CollectionUtils.isNotEmpty(deptAnalysisList)) {
            for (DeptAnalysis deptAnalysis : deptAnalysisList) {
                OnlineReportTopFiveAnalysis reportTopFiveAnalysis = new OnlineReportTopFiveAnalysis();
                reportTopFiveAnalysis.setDept(deptAnalysis.getAggType());
                reportTopFiveAnalysis.setResult(OrpReportUtils.formatBigDecimal(deptAnalysis.getResult()));
                list.add(reportTopFiveAnalysis);
            }
        }
        return list;
    }

    /**
     * 商旅指标
     *
     * @param request
     * @return
     * @throws Exception
     */
    public BigDecimal deptCorpAnalysis(OnlineReportTopFiveDeptAnalysisRequest request) throws Exception {
        BaseQueryConditionDTO dto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        return OrpReportUtils.formatBigDecimal(getDeptOtherDaoService(dto).deptCorpAnalysis(dto.getStartTime(), dto.getEndTime(),
                dto.getStatisticalCaliber(), request.getAnalysisTypeEnum(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel()));
    }

    /**
     * 数据条数
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Cacheable
    public int count(OnlineReportTopFiveDeptAnalysisRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        AnalysisTypeEnum analysisTypeEnum = request.getAnalysisTypeEnum();
        if (Objects.isNull(request.getPage())) {
            return 0;
        } else {
            return getDeptOtherDaoService(baseQueryConditionDto).count(analysisObjectEnum, analysisTypeEnum, baseQueryConditionDto);
        }
    }
}
