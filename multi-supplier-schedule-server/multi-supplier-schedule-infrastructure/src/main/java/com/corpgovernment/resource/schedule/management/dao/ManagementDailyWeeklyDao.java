package com.corpgovernment.resource.schedule.management.dao;

import com.corpgovernment.dto.management.dto.ManagementRuleDTO;
import com.corpgovernment.management.ManagementReq;
import com.corpgovernment.resource.schedule.domain.management.model.ManagementRule;
import com.corpgovernment.resource.schedule.management.dto.ManagementDailyDataSchema;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ManagementDailyWeeklyDao extends AbstractClickhouseBaseDao {

    /**
     * 计算分页起始和结束索引
     *
     * @param currentPage 当前页码，从 1 开始
     * @param pageSize 每页显示的记录数
     * @return 分页范围数组，包含起始索引和结束索引
     * @throws IllegalArgumentException 如果输入参数无效
     */
    public static int[] calculatePagination(int currentPage, int pageSize) {
        if (currentPage < 1 || pageSize < 1) {
            throw new IllegalArgumentException("当前页和每页大小必须为正数");
        }

        int startIndex = (currentPage - 1) * pageSize;
        // 结束索引是闭区间，适用于一些场景
        int endIndex = startIndex + pageSize - 1;

        return new int[]{startIndex, endIndex};
    }

    public static void main(String[] args) {
        // 测试用例
        int currentPage = 6;
        int pageSize = 100;

        int[] pagination = calculatePagination(currentPage, pageSize);

        System.out.println("起始索引: " + pagination[0]);
        System.out.println("结束索引: " + pagination[1]);
    }


    public ManagementDailyDataSchema getManagementData(ManagementReq parse) {

        StringBuilder sql = new StringBuilder("SELECT extra_data as managementRuleList\n" +
                "FROM audit_print_expression_logger\n" +
                "where method_name = 'verifyTravelStandard'\n" +
                "  and JSONExtractArrayRaw(extra_data, 'managementRuleList') != []\n");

        List<Object> parmList = new ArrayList<>();
        sql.append("  AND timestamp >= ?");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        parmList.add(parse.getStartTime().format(formatter) + " 00:00:00");
        sql.append("  AND timestamp <= ?");
        parmList.add(parse.getEndTime().format(formatter) + " 23:59:59");

        try {
            List<ManagementRule> list = queryBySqlManagement(sql.toString(),
                    parmList,
                    (req, statement) -> mapCommonRequest(parmList, statement),
                    (rs, clazz) -> {
                        try {
                            return mapResultList(rs, clazz);
                        } catch (Exception e) {
                            log.error("query error:{}", ExceptionUtils.getStackTrace(e));
                        }
                        return Lists.newArrayList();
                    },
                    ManagementRule.class,
                    "query");

            ManagementDailyDataSchema managementDailyDataSchema = new ManagementDailyDataSchema();
            managementDailyDataSchema.setVerifyTravelStandard(list.stream()
                    .map(m -> {
                        JsonNode managementRuleList = JsonUtils.getJsonNode(m.getManagementRuleList()).get("managementRuleList");
                        return JsonUtils.parseArray(managementRuleList.toString(), ManagementRuleDTO.class);
                    })
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList())
            );
            return managementDailyDataSchema;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }

}
