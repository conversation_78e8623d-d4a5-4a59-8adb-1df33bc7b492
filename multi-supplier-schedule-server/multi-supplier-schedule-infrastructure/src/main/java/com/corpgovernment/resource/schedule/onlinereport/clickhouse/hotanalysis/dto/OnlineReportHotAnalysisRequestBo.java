package com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.dto;

import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/24 19:19
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@NoArgsConstructor
public class OnlineReportHotAnalysisRequestBo {

    String startTime;
    String endTime;
    // 机票 flight / 酒店 hotel
    String bu;
    /*
    "flight_class": "国内"
    "dim": ""
    dim字段:
    机票：
     航司 airline_cn_name或airline_en_name
     航线 flight_city或 flight_city_en
     出发城市 departure_city_name 或 departure_city_name_en
     到达城市 arrival_city_name 或 arrival_city_name_en
    酒店:
     城市：city_name 或 city_name_en
     酒店：hotel_name 或 hotel_name_en
     火车：
     线路：line_city或line_city_en
     出发城市：departure_city_name或departure_city_name_en
     到达城市：arrival_city_name或arrival_city_name_en
     用车
     出发城市：departure_city_name
     供应商名称：vendor_name
     车型：vehicle_name
    */
    // agreementType:协议类型， TA协议，NTA非协议
    // carOrderType:用车类型, airportpick接送机,Charter包车,rent租车,tax打车
    Map<String, String> extParams;
    String lang;
    BaseQueryConditionDTO baseQueryCondition;
    // dom国内，inter国际
    String productType;

}
