package com.corpgovernment.resource.schedule.accesslog.gatewayimpl;

import com.corpgovernment.resource.schedule.accesslog.dao.AccessLogDao;
import com.corpgovernment.resource.schedule.domain.accesslog.gateway.AccessLogGateway;
import com.corpgovernment.resource.schedule.domain.accesslog.model.BookingFaq;
import com.corpgovernment.resource.schedule.domain.accesslog.model.Top5Info;
import com.corpgovernment.resource.schedule.domain.accesslog.model.Top5InfoTraffic;
import com.corpgovernment.resource.schedule.domain.accesslog.model.req.ReportQueryReq;
import com.corpgovernment.resource.schedule.domain.accesslog.model.resp.Top5InfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AccessLogGatewayImpl implements AccessLogGateway {

    @Resource
    private AccessLogDao accessLogDao;

    @Override
    public Top5Info queryBYD(ReportQueryReq reportQueryReq) {
        log.info("query top5 info, req:{}", reportQueryReq);
        try {
            List<Top5InfoTraffic> flightTop5 = accessLogDao.queryBYD(reportQueryReq, "flight");
            List<Top5InfoTraffic> trainTop5 = accessLogDao.queryBYD(reportQueryReq, "train");
            List<Top5InfoTraffic> hotelTop5 = accessLogDao.queryBYD(reportQueryReq, "hotel");
            List<Top5InfoTraffic> commonTop5 = accessLogDao.queryBYD(reportQueryReq, "common");

            return new Top5Info()
                    .setFlightTop5(flightTop5)
                    .setTrainTop5(trainTop5)
                    .setHotelTop5(hotelTop5)
                    .setCommonTop5(commonTop5);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<BookingFaq> queryQuestion(Collection<Integer> questionIds) {
        try {
            return accessLogDao.queryQuestion(questionIds);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<Top5InfoResp> query(ReportQueryReq reportQueryReq, String trafficType) {
        List<Top5InfoResp> top5 = accessLogDao.query(reportQueryReq, trafficType);

        return IntStream.range(0, top5.size())
                .mapToObj(i -> {
                    Top5InfoResp top5InfoResp = top5.get(i);
                    top5InfoResp.setRank(i + 1);
                    return top5InfoResp;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Top5InfoTraffic> queryExcelBYD(ReportQueryReq reportQueryReq) {
        try {
            return accessLogDao.queryBYD(reportQueryReq, null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
