package com.corpgovernment.resource.schedule.onlinereport.dept.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptDetailAnalysisRequest;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportDeptTrainDetailDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptDetailDaoService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.dept.sr.SrOnlineReportDeptTrainDetailDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dept.AbstractDeptDetailAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DeptStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2021/12/31 11:16
 * @Desc overview部门明细分析
 */
@Service
public class DeptDetailTrainBizImpl extends AbstractDeptDetailAnalysisBiz {

    @Autowired
    private OnlineReportDeptTrainDetailDaoImpl onlineReportDeptDetailDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private SrOnlineReportDeptTrainDetailDaoImpl srOnlineReportDeptTrainDetailDao;

    /**
     * 获取查询的dao
     */
    public OnlineReportDeptDetailDaoService getOnlineReportDeptDetailDao(BaseQueryConditionDTO baseQueryConditionDTO) {
        boolean useSr = baseQueryConditionDTO != null && BooleanUtils.isTrue(baseQueryConditionDTO.useStarRocks);
        return useSr ? srOnlineReportDeptTrainDetailDao : onlineReportDeptDetailDao;
    }

    public List<Map> deptDetail(OnlineReportDeptDetailAnalysisRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        baseQueryConditionDto.setLang(request.getLang());
        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum = SubQueryReportBuTypeEnum.getByOrdinal(request.getSubQueryBu());
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        List<Map> list = getOnlineReportDeptDetailDao(baseQueryConditionDto).deptDetailAnalysis(analysisObjectEnum,
                baseQueryConditionDto, initPager(request.getPage()), subQueryReportBuTypeEnum, request.getAnalysisObjectOrgInfo(), (String) map.get("analysisObjectVal")
                , request.getProductType());
        fomratResultData(list);
        return list;
    }

    @Override
    public int count(OnlineReportDeptDetailAnalysisRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        baseQueryConditionDto.setLang(request.getLang());
        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum = SubQueryReportBuTypeEnum.getByOrdinal(request.getSubQueryBu());
        return getOnlineReportDeptDetailDao(baseQueryConditionDto).count(analysisObjectEnum, baseQueryConditionDto, subQueryReportBuTypeEnum, request.getAnalysisObjectOrgInfo(),
                (String) map.get("analysisObjectVal"), request.getProductType());
    }

    @Override
    public List<DeptStatisticalsEnum> getStatisticalList(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        List list1 = DeptStatisticalsEnum.getStaticalsByBizType(QueryReportBuTypeEnum.overview.toString(), subQueryReportBuTypeEnum);
        List list2 = DeptStatisticalsEnum.getStaticalByBizTypeAndSubBuType(QueryReportBuTypeEnum.train.toString(),
                subQueryReportBuTypeEnum.ordinal());
        list1.addAll(list2);
        return list1;
    }

    @Override
    public boolean matching(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return subQueryReportBuTypeEnum == SubQueryReportBuTypeEnum.TRAIN ||
                subQueryReportBuTypeEnum == SubQueryReportBuTypeEnum.TRAIN_N ||
                subQueryReportBuTypeEnum == SubQueryReportBuTypeEnum.TRAIN_I;
    }

}
