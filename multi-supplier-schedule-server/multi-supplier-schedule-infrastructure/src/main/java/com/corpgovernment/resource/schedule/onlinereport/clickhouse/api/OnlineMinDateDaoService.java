package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api;


import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineMinDateRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
public interface OnlineMinDateDaoService {
    <T> List<T> onlineMinDateQuery(OnlineMinDateRequest onlineMinDateRequest) throws Exception;

    String getDefaultMinDate();
}
