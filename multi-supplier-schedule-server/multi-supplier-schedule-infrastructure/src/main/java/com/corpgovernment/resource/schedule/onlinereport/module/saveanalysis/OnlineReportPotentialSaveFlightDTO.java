package com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 11:02
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class OnlineReportPotentialSaveFlightDTO {

    @Column(name = "flightRcPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRcPotentialSave;

    @Column(name = "flightRebookPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRebookPotentialSave;

    @Column(name = "flightRefundPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRefundPotentialSave;

    @Column(name = "flightRcTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRcTimes;

    @Column(name = "flightRcTimesRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRcTimesRate;

    @Column(name = "flightRebookTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRebookTimes;

    @Column(name = "flightRebookTimesRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRebookTimesRate;

    @Column(name = "flightRefundTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRefundTimes;

    @Column(name = "flightRefundTimesRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRefundTimesRate;

}
