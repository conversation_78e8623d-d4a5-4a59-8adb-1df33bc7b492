package com.corpgovernment.resource.schedule.onlinereport.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/10 10:16
 * @description：
 * @modified By：
 * @version: $
 */
public enum ReportTrendHeaderEnum {

    /**
     * 概览 - 总计
     */
    OVERVIEW_AMOUNT_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewAmountHeader()),
    OVERVIEW_TICKET_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewQuantityHeader()),

    OVERVIEW_ORDER_NUM_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewOrderHeader()),

    /**
     * 概览 - 产线
     */
    OVERVIEW_BU_AMOUNT_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewBuAmountHeader()),
    OVERVIEW_BU_TICKET_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewBuQuantityHeader()),

    OVERVIEW_BU_ORDER_NUM_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewBuOrderNumHeader()),


    /**
     * 概览 - 产线-日本
     */
    OVERVIEW_BU_JP_AMOUNT_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewBuJPAmountHeader()),
    OVERVIEW_BU_JP_TICKET_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewBuJPQuantityHeader()),

    OVERVIEW_BU_JP_ORDER_NUM_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewBuJPOrderNumHeader()),

    /**
     * 节省 - 趋势 - 分类别
     */
    SAVE_HOTEL_TREND_HEADER(OnlineReportBuEnum.HOTEL, mapSaveHotelTrendHeader()),

    SAVE_FLIGHT_TREND_HEADER(OnlineReportBuEnum.FLIGHT, mapSaveFlightTrendHeader()),

    /**
     * 节省 - 趋势 - 总计
     */
    SAVE_TREND_TOTAL_HEADER(OnlineReportBuEnum.OVERVIEW, mapSaveTrendTotalHeader()),

    /**
     * 潜在节省 - 趋势 - 酒店 - 节省金额 - 分类别
     */
    POTENTIAL_SAVE_HOTEL_TREND_HEADER(OnlineReportBuEnum.HOTEL, mapPotentialSaveHotelTrendHeader()),

    /**
     * 潜在节省 - 趋势 - 机票 - 节省金额 - 分类别
     */
    POTENTIAL_SAVE_FLIGHT_TREND_HEADER(OnlineReportBuEnum.FLIGHT, mapPotentialSaveFlightTrendHeader()),

    /**
     * 潜在节省 - 趋势 - 酒店 - 节省金额 - 总计
     */
    POTENTIAL_SAVE_TREND_TOTAL_HEADER(OnlineReportBuEnum.HOTEL, mapPotentialSaveTrendTotalHeader()),

    /**
     * 潜在节省 - 趋势 - 酒店 - 次数 - 分类别
     */
    POTENTIAL_SAVE_TIMES_HOTEL_TREND_HEADER(OnlineReportBuEnum.HOTEL, mapPotentialSaveTimesHotelTrendHeader()),

    /**
     * 潜在节省 - 趋势 - 机票 - 次数 - 分类别
     */
    POTENTIAL_SAVE_TIMES_FLIGHT_TREND_HEADER(OnlineReportBuEnum.FLIGHT, mapPotentialSaveTimesFlightTrendHeader()),

    /**
     * 潜在节省 - 趋势 - 酒店 - 次数 - 总计
     */
    POTENTIAL_SAVE_TIMES_TREND_TOTAL_HEADER(OnlineReportBuEnum.HOTEL, mapPotentialSaveTimesTrendTotalHeader()),

    /**
     * 节省 - 趋势明细 - 分类别
     */
    SAVE_HOTEL_DETAIL_HEADER(OnlineReportBuEnum.HOTEL, mapSaveHotelDetailHeader()),

    SAVE_FLIGHT_DETAIL_HEADER(OnlineReportBuEnum.FLIGHT, mapSaveFlightDetailHeader()),

    /**
     * 潜在节省 - 趋势明细 - 分类别
     */
    POTENTIAL_SAVE_HOTEL_DETAIL_HEADER(OnlineReportBuEnum.HOTEL, mapPotentialSaveHotelDetailHeader()),

    POTENTIAL_SAVE_FLIGHT_DETAIL_HEADER(OnlineReportBuEnum.FLIGHT, mapPotentialSaveFlightDetailHeader()),


    /**
     * 部门碳排趋势-总体
     */
    OVERVIEW_CARBONS_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewCarbonsHeader()),

    /**
     * 部门碳排趋势-产线
     */
    OVERVIEW_BU_CARBONS_HEADER(OnlineReportBuEnum.OVERVIEW, mapOverviewBuCarbonsHeader()),
    ;

    private OnlineReportBuEnum buEnum;

    private List<OnlineTrendFieldEnum> headerList;

    ReportTrendHeaderEnum(OnlineReportBuEnum buEnum, List<OnlineTrendFieldEnum> headerList) {
        this.buEnum = buEnum;
        this.headerList = headerList;
    }

    public OnlineReportBuEnum getBuEnum() {
        return buEnum;
    }

    public List<OnlineTrendFieldEnum> getHeaderList() {
        return headerList;
    }

    public static List<OnlineTrendFieldEnum> mapBuAmountAccHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.AMOUNT);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapBuQuantityAccHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        OnlineTrendFieldEnum.QUANTITY.setUnitKey("Unit.Num");
        lists.add(OnlineTrendFieldEnum.QUANTITY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapBuAmountHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.AMOUNT);
        lists.add(OnlineTrendFieldEnum.AMOUNT_CHAIN);
        lists.add(OnlineTrendFieldEnum.AMOUNT_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapBuQuantityHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        OnlineTrendFieldEnum.QUANTITY.setUnitKey("Unit.Num");
        OnlineTrendFieldEnum.QUANTITY_CHAIN.setUnitKey("Unit.Num");
        OnlineTrendFieldEnum.QUANTITY_YOY.setUnitKey("Unit.Num");
        lists.add(OnlineTrendFieldEnum.QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewAmountHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.TOTAL_AMOUNT);
        lists.add(OnlineTrendFieldEnum.AMOUNT_TOTAL_CHAIN);
        lists.add(OnlineTrendFieldEnum.AMOUNT_TOTAL_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewBuAmountHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.FLT_AMOUNT);
        lists.add(OnlineTrendFieldEnum.AMOUNT_FLT_CHAIN);
        lists.add(OnlineTrendFieldEnum.AMOUNT_FLT_YOY);
        lists.add(OnlineTrendFieldEnum.HTL_AMOUNT);
        lists.add(OnlineTrendFieldEnum.AMOUNT_HTL_CHAIN);
        lists.add(OnlineTrendFieldEnum.AMOUNT_HTL_YOY);
        lists.add(OnlineTrendFieldEnum.TRAIN_AMOUNT);
        lists.add(OnlineTrendFieldEnum.AMOUNT_TRAIN_CHAIN);
        lists.add(OnlineTrendFieldEnum.AMOUNT_TRAIN_YOY);
        lists.add(OnlineTrendFieldEnum.CAR_AMOUNT);
        lists.add(OnlineTrendFieldEnum.AMOUNT_CAR_CHAIN);
        lists.add(OnlineTrendFieldEnum.AMOUNT_CAR_YOY);
        /*lists.add(OnlineTrendFieldEnum.BUS_AMOUNT);
        lists.add(OnlineTrendFieldEnum.AMOUNT_BUS_CHAIN);
        lists.add(OnlineTrendFieldEnum.AMOUNT_BUS_YOY);
        lists.add(OnlineTrendFieldEnum.ADD_AMOUNT);
        lists.add(OnlineTrendFieldEnum.AMOUNT_ADD_CHAIN);
        lists.add(OnlineTrendFieldEnum.AMOUNT_ADD_YOY);*/
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewBuJPAmountHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.FLT_AMOUNT);
        lists.add(OnlineTrendFieldEnum.AMOUNT_FLT_CHAIN);
        lists.add(OnlineTrendFieldEnum.AMOUNT_FLT_YOY);
        lists.add(OnlineTrendFieldEnum.HTL_AMOUNT);
        lists.add(OnlineTrendFieldEnum.AMOUNT_HTL_CHAIN);
        lists.add(OnlineTrendFieldEnum.AMOUNT_HTL_YOY);
//        lists.add(OnlineTrendFieldEnum.TRAIN_AMOUNT);
//        lists.add(OnlineTrendFieldEnum.AMOUNT_TRAIN_CHAIN);
//        lists.add(OnlineTrendFieldEnum.AMOUNT_TRAIN_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewQuantityHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.TOTAL_QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_TOTAL_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_TOTAL_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewOrderHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.TOTAL_ORDER_NUM);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_TOTAL_CHAIN);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_TOTAL_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewBuQuantityHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.FLT_QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_FLT_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_FLT_YOY);
        lists.add(OnlineTrendFieldEnum.HTL_QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_HTL_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_HTL_YOY);
        lists.add(OnlineTrendFieldEnum.TRAIN_QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_TRAIN_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_TRAIN_YOY);
        lists.add(OnlineTrendFieldEnum.CAR_QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_CAR_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_CAR_YOY);
        lists.add(OnlineTrendFieldEnum.BUS_QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_BUS_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_BUS_YOY);
        lists.add(OnlineTrendFieldEnum.ADD_QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_ADD_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_ADD_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewBuOrderNumHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.FLT_ORDER_NUM);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_FLT_CHAIN);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_FLT_YOY);
        lists.add(OnlineTrendFieldEnum.HTL_ORDER_NUM);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_HTL_CHAIN);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_HTL_YOY);
        lists.add(OnlineTrendFieldEnum.TRAIN_ORDER_NUM);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_TRAIN_CHAIN);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_TRAIN_YOY);
        lists.add(OnlineTrendFieldEnum.CAR_QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_CAR_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_CAR_YOY);
//        lists.add(OnlineTrendFieldEnum.BUS_ORDER_NUM);
//        lists.add(OnlineTrendFieldEnum.ORDER_NUM_BUS_CHAIN);
//        lists.add(OnlineTrendFieldEnum.ORDER_NUM_BUS_YOY);
//        lists.add(OnlineTrendFieldEnum.ADD_QUANTITY);
//        lists.add(OnlineTrendFieldEnum.QUANTITY_ADD_CHAIN);
//        lists.add(OnlineTrendFieldEnum.QUANTITY_ADD_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewBuJPQuantityHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.FLT_QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_FLT_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_FLT_YOY);
        lists.add(OnlineTrendFieldEnum.HTL_QUANTITY);
        lists.add(OnlineTrendFieldEnum.QUANTITY_HTL_CHAIN);
        lists.add(OnlineTrendFieldEnum.QUANTITY_HTL_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewBuJPOrderNumHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.FLT_ORDER_NUM);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_FLT_CHAIN);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_FLT_YOY);
        lists.add(OnlineTrendFieldEnum.HTL_ORDER_NUM);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_HTL_CHAIN);
        lists.add(OnlineTrendFieldEnum.ORDER_NUM_HTL_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapSaveHotelTrendHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.HOTEL_3C_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.HOTEL_3C_SAVE_RATE);
        // Removed HOTEL_PREMIUM_SAVE_AMOUNT and HOTEL_PREMIUM_SAVE_RATE
        /*lists.add(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_RATE);
        lists.add(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.CONTROL_SAVE_RATE);*/
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapSaveHotelDetailHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.MONTH);
        lists.add(OnlineTrendFieldEnum.HOTEL_3C_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.HOTEL_3C_SAVE_PER_QUANTITY);
        lists.add(OnlineTrendFieldEnum.HOTEL_3C_SAVE_RATE);
        // Removed HOTEL_PREMIUM_SAVE_AMOUNT, HOTEL_PREMIUM_SAVE_PER_QUANTITY, and HOTEL_PREMIUM_SAVE_RATE
        lists.add(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_PER_QUANTITY);
        lists.add(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_RATE);
        lists.add(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.CONTROL_SAVE_PER_QUANTITY);
        lists.add(OnlineTrendFieldEnum.CONTROL_SAVE_RATE);
        lists.add(OnlineTrendFieldEnum.CONTROL_RESPONSE_RATE);
        lists.add(OnlineTrendFieldEnum.SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.HOTEL_SAVE_PER_QUANTITY);
        lists.add(OnlineTrendFieldEnum.SAVE_RATE);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapSaveTrendTotalHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.SAVE_RATE);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapSaveFlightTrendHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_RATE);
        // Removed FLIGHT_PREMIUM_SAVE_AMOUNT and FLIGHT_PREMIUM_SAVE_RATE
        //lists.add(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT);
        //lists.add(OnlineTrendFieldEnum.CONTROL_SAVE_RATE);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapSaveFlightDetailHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.MONTH);
        lists.add(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_PER_QUANTITY);
        lists.add(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_RATE);
        // Removed FLIGHT_PREMIUM_SAVE_AMOUNT, FLIGHT_PREMIUM_SAVE_PER_QUANTITY, and FLIGHT_PREMIUM_SAVE_RATE
        lists.add(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.CONTROL_SAVE_PER_QUANTITY);
        lists.add(OnlineTrendFieldEnum.CONTROL_SAVE_RATE);
        lists.add(OnlineTrendFieldEnum.CONTROL_RESPONSE_RATE);
        lists.add(OnlineTrendFieldEnum.SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.FLIGHT_SAVE_PER_QUANTITY);
        lists.add(OnlineTrendFieldEnum.SAVE_RATE);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapPotentialSaveHotelTrendHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_AMOUNT);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapPotentialSaveHotelDetailHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.MONTH);
        lists.add(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_AMOUNT_DETAIL);
        lists.add(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_TIMES_RATE);
        lists.add(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_TIMES_RATE);
        lists.add(OnlineTrendFieldEnum.POTENTIAL_SAVE);
        lists.add(OnlineTrendFieldEnum.POTENTIAL_SAVE_TIMES);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapPotentialSaveFlightTrendHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_AMOUNT);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapPotentialSaveFlightDetailHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.MONTH);
        lists.add(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_AMOUNT_DETAIL);
        lists.add(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_TIMES_RATE);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_TIMES_RATE);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_AMOUNT);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_TIMES_RATE);
        lists.add(OnlineTrendFieldEnum.POTENTIAL_SAVE);
        lists.add(OnlineTrendFieldEnum.POTENTIAL_SAVE_TIMES);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapPotentialSaveTrendTotalHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.POTENTIAL_SAVE);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapPotentialSaveTimesHotelTrendHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.HOTEL_RC_POTENTIAL_SAVE_TIMES_RATE);
        lists.add(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.HOTEL_CANCEL_POTENTIAL_SAVE_TIMES_RATE);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapPotentialSaveTimesFlightTrendHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.FLIGHT_RC_POTENTIAL_SAVE_TIMES_RATE);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REBOOK_POTENTIAL_SAVE_TIMES_RATE);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.FLIGHT_REFUND_POTENTIAL_SAVE_TIMES_RATE);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapPotentialSaveTimesTrendTotalHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.POTENTIAL_SAVE_TIMES);
        lists.add(OnlineTrendFieldEnum.POTENTIAL_SAVE_TIMES_RATE);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewCarbonsHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.CARBONS);
        lists.add(OnlineTrendFieldEnum.CARBON_CHAIN);
        lists.add(OnlineTrendFieldEnum.CARBON_YOY);
        return lists;
    }

    public static List<OnlineTrendFieldEnum> mapOverviewBuCarbonsHeader() {
        List<OnlineTrendFieldEnum> lists = new ArrayList<>();
        lists.add(OnlineTrendFieldEnum.FLT_CARBONS);
        lists.add(OnlineTrendFieldEnum.FLT_CARBON_CHAIN);
        lists.add(OnlineTrendFieldEnum.FLT_CARBON_YOY);
        lists.add(OnlineTrendFieldEnum.TRAIN_CARBONS);
        lists.add(OnlineTrendFieldEnum.TRAIN_CARBON_CHAIN);
        lists.add(OnlineTrendFieldEnum.TRAIN_CARBON_YOY);
        lists.add(OnlineTrendFieldEnum.CAR_CARBONS);
        lists.add(OnlineTrendFieldEnum.CAR_CARBON_CHAIN);
        lists.add(OnlineTrendFieldEnum.CAR_CARBON_YOY);
        return lists;
    }
}
