package com.corpgovernment.resource.schedule.onlinereport.enums.reportlib;

/**
 * <AUTHOR>
 * @date 2022-09-07 20:58
 * @desc
 */
public enum HtlOrderStatusEnum {

    // 处理中
    P("RiskOrder.HtlOrderStatus6"),
    // 成交
    S("RiskOrder.HtlOrderStatus1"),
    // 取消
    C("RiskOrder.HtlOrderStatus2"),
    // 修改
    U("RiskOrder.HtlOrderStatus4"),
    // 已提交，待处理
    SW("RiskOrder.HtlOrderStatus5"),
    // 已提交，处理中
    SP("RiskOrder.HtlOrderStatus7"),
    // 其他
    O("RiskOrder.HtlOrderStatus3");
    private String name;

    HtlOrderStatusEnum(String name){
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
