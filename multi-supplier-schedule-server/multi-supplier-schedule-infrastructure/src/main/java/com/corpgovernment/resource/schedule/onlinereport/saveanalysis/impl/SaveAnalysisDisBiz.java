package com.corpgovernment.resource.schedule.onlinereport.saveanalysis.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportProportion;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveProportionDetail;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveProportionDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveProportionRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendLegend;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.saveanalysis.OnlineReportSaveDisDao;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.convert.SaveMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.OnlineTrendFieldEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveFlightDisDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveFlightDisV2DTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveHotelDisDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveProportionDetailRequestDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportSaveProportionRequestDTO;
import com.corpgovernment.resource.schedule.onlinereport.saveanalysis.AbstractSaveAnalysisDis;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.DEFAULT_SAVE_MIN_DATE;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.HOTEL_PREMIUM_EARLIEST_DAY;
import static com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils.betweenDay;


/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 11:02
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class SaveAnalysisDisBiz extends AbstractSaveAnalysisDis {

    @Autowired
    OnlineReportSaveDisDao onlineReportSaveDisDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private SaveMapper saveMapper;

    
    public List<OnlineReportSaveProportionDetail> getSaveDisDetail(OnlineReportSaveProportionDetailRequest request) throws Exception {
        OnlineReportSaveProportionRequest request1 = new OnlineReportSaveProportionRequest();
        request1.setQueryBu(request.getQueryBu());
        request1.setBasecondition(request.getBasecondition());
        request1.setProductType(request.getProductType());
        HashMap<String, OnlineReportProportion> proportion = getSaveDisInner(request1);
        HashMap<String, OnlineReportSaveProportionDetail> part = getSaveDisDetailPart(request);
        List<OnlineTrendFieldEnum> headers = getSaveDisLegend(request.getQueryBu());
        List<OnlineReportSaveProportionDetail> proportionDetails = new ArrayList<>();
        for (OnlineTrendFieldEnum header : headers) {
            // 机酒两方过滤负值
            if ((header == OnlineTrendFieldEnum.HOTEL_PREMIUM_SAVE_AMOUNT || header == OnlineTrendFieldEnum.FLIGHT_PREMIUM_SAVE_AMOUNT)
                    && !proportion.containsKey(header.getNameKey())) {
                continue;
            }
            OnlineReportSaveProportionDetail detail = part.get(header.getNameKey());

            if (detail == null) {
                continue;
            }

            detail.setSaveRate(
                    BizUtils.round2(proportion.get(header.getNameKey()).getSaveRate()));
            detail.setSavePerQuantity(
                    BizUtils.round2(proportion.get(header.getNameKey()).getSavePerQuantity()));
            detail.setTypeNum(BizUtils.round2(
                    proportion.get(header.getNameKey()).getTypeNum()));
            detail.setSavePercent(proportion.get(header.getNameKey()).getTypeNumPercent());
            proportionDetails.add(detail);
        }
        return proportionDetails;
    }

    
    public List<OnlineReportProportion> getSaveDis(OnlineReportSaveProportionRequest request) throws Exception {
        HashMap<String, OnlineReportProportion> part = getSaveDisInner(request);
        List<OnlineTrendFieldEnum> headers = getSaveDisLegend(request.getQueryBu());
        List<OnlineReportProportion> proportions = new ArrayList<>();
        for (OnlineTrendFieldEnum header : headers) {
            if (!part.containsKey(header.getNameKey())) {
                continue;
            }
            proportions.add(part.get(header.getNameKey()));
        }
        return proportions;
    }

    public HashMap<String, OnlineReportProportion> getSaveDisInner(OnlineReportSaveProportionRequest request) throws Exception {
        OnlineReportSaveProportionRequestDTO requestDTO = map(request, baseQueryConditionMapper);
        HashMap<String, OnlineReportProportion> saveDis = new HashMap<>();
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
            List<OnlineReportSaveFlightDisV2DTO> flightDisV2DTOS = onlineReportSaveDisDao.queryFlightProportionV2(requestDTO);
            List<OnlineReportSaveFlightDisDTO> flightDisDTOS = saveMapper.toDisDTOS(flightDisV2DTOS);
            if (flightDisDTOS.size() == 1) {
                OnlineReportSaveFlightDisDTO dto = flightDisDTOS.get(0);
                BigDecimal sumNum = dto.getCSave().add(compareZero(dto.getPremiumSave())).add(Optional.ofNullable(dto.getControlSave()).orElse(BigDecimal.ZERO));
                saveDis.put(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_AMOUNT.getNameKey(),
                        new OnlineReportProportion(
                                OnlineTrendFieldEnum.FLIGHT_3C_SAVE_AMOUNT.getValueName(),
                                BizUtils.round2(dto.getCSave()),
                                OrpReportUtils.divideWithPercent(dto.getCSave(), sumNum),
                                BizUtils.round2(dto.getCSaveRate()),
                                BizUtils.round2(dto.getCSavePerQuantity())
                        )
                );
                if (compareZeroT(dto.getPremiumSave())) {
                    saveDis.put(OnlineTrendFieldEnum.FLIGHT_PREMIUM_SAVE_AMOUNT.getNameKey(),
                            new OnlineReportProportion(
                                    OnlineTrendFieldEnum.FLIGHT_PREMIUM_SAVE_AMOUNT.getValueName(),
                                    BizUtils.round2(dto.getPremiumSave()),
                                    OrpReportUtils.divideWithPercent(dto.getPremiumSave(), sumNum),
                                    BizUtils.round2(dto.getPremiumSaveRate()),
                                    BizUtils.round2(dto.getPremiumSavePerQuantity())
                            )
                    );
                }
                saveDis.put(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getNameKey(),
                        new OnlineReportProportion(
                                OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getValueName(),
                                BizUtils.round2(dto.getControlSave()),
                                OrpReportUtils.divideWithPercent(dto.getControlSave(), sumNum),
                                BizUtils.round2(dto.getControlSaveRate()),
                                BizUtils.round2(dto.getControlSavePerQuantity())
                        )
                );
            }
        }

        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
            List<OnlineReportSaveHotelDisDTO> hotelDisDTOS = onlineReportSaveDisDao.queryHotelProportion(requestDTO);
            if (hotelDisDTOS.size() == 1) {
                OnlineReportSaveHotelDisDTO dto = hotelDisDTOS.get(0);
                BigDecimal sumNum = dto.getCSave()
                        .add(compareZero(dto.getPremiumSave()))
                        .add(dto.getPromotionSave())
                        .add(Optional.ofNullable(dto.getControlSave()).orElse(BigDecimal.ZERO));
                saveDis.put(OnlineTrendFieldEnum.HOTEL_3C_SAVE_AMOUNT.getNameKey(),
                        new OnlineReportProportion(
                                OnlineTrendFieldEnum.HOTEL_3C_SAVE_AMOUNT.getValueName(),
                                BizUtils.round2(dto.getCSave()),
                                OrpReportUtils.divideWithPercent(dto.getCSave(), sumNum),
                                BizUtils.round2(dto.getCSaveRate()), BizUtils.round2(dto.getCSavePerQuantity())
                        )
                );
                if (compareZeroT(dto.getPremiumSave())) {
                    saveDis.put(OnlineTrendFieldEnum.HOTEL_PREMIUM_SAVE_AMOUNT.getNameKey(),
                            new OnlineReportProportion(
                                    OnlineTrendFieldEnum.HOTEL_PREMIUM_SAVE_AMOUNT.getValueName(),
                                    BizUtils.round2(compareZero(dto.getPremiumSave())),
                                    OrpReportUtils.divideWithPercent(compareZero(dto.getPremiumSave()), sumNum),
                                    BizUtils.round2(dto.getPremiumSaveRate()), BizUtils.round2(dto.getPremiumSavePerQuantity())
                            )
                    );
                }
                saveDis.put(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_AMOUNT.getNameKey(),
                        new OnlineReportProportion(
                                OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_AMOUNT.getValueName(),
                                BizUtils.round2(dto.getPromotionSave()),
                                OrpReportUtils.divideWithPercent(compareZero(dto.getPromotionSave()), sumNum),
                                BizUtils.round2(dto.getPromotionSaveRate()), BizUtils.round2(dto.getPromotionSavePerQuantity())
                        )
                );
                saveDis.put(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getNameKey(),
                        new OnlineReportProportion(
                                OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getValueName(),
                                BizUtils.round2(dto.getControlSave()),
                                OrpReportUtils.divideWithPercent(dto.getControlSave(), sumNum),
                                BizUtils.round2(dto.getControlSaveRate()),
                                BizUtils.round2(dto.getControlSavePerQuantity())
                        )
                );
            }
        }
        return saveDis;
    }

    public List<OnlineReportTrendLegend> getSaveDisLegend(OnlineReportSaveProportionRequest request) {
        String lang = request.getLang();
        List<OnlineReportTrendLegend> legends = new ArrayList<>();
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.FLIGHT_3C_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_PREMIUM_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.FLIGHT_PREMIUM_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_PREMIUM_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_3C_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.HOTEL_3C_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_3C_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_PREMIUM_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.HOTEL_PREMIUM_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_PREMIUM_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
            legends.add(
                    new OnlineReportTrendLegend(
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getNameKey(), lang),
                            OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getValueName(),
                            SharkUtils.getHeaderVal(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getUnitKey(), lang),
                            "", ""
                    )
            );
        }
        return legends;
    }

    public List<OnlineTrendFieldEnum> getSaveDisLegend(QueryReportBuTypeEnum bu) {
        List<OnlineTrendFieldEnum> legends = new ArrayList<>();
        if (bu == QueryReportBuTypeEnum.flight) {
            legends.add(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_AMOUNT);
            legends.add(OnlineTrendFieldEnum.FLIGHT_PREMIUM_SAVE_AMOUNT);
            legends.add(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT);
        }
        if (bu == QueryReportBuTypeEnum.hotel) {
            legends.add(OnlineTrendFieldEnum.HOTEL_3C_SAVE_AMOUNT);
            legends.add(OnlineTrendFieldEnum.HOTEL_PREMIUM_SAVE_AMOUNT);
            legends.add(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_AMOUNT);
            legends.add(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT);
        }
        return legends;
    }

    public HashMap<String, OnlineReportSaveProportionDetail> getSaveDisDetailPart(OnlineReportSaveProportionDetailRequest request) throws Exception {
        String lang = request.getLang();
        OnlineReportSaveProportionDetailRequestDTO requestDTO = map(request, baseQueryConditionMapper);
        HashMap<String, OnlineReportSaveProportionDetail> saveDisDetail = new HashMap<>();
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
            // 累计
            List<OnlineReportSaveFlightDisDTO> flightAccDTOS = onlineReportSaveDisDao.queryFlightAccProportionDetail(requestDTO);
            OnlineReportSaveFlightDisDTO flightAccDTO = flightAccDTOS.size() == 1 ? flightAccDTOS.get(0) : new OnlineReportSaveFlightDisDTO();
            // 行业
            BaseQueryConditionDTO dto1 = requestDTO.getBaseQueryCondition();
            List<OnlineReportSaveFlightDisV2DTO> flightDisIndustryV2DTOS = onlineReportSaveDisDao.queryFlightProportionDetailV2(dto1.getStartTime(), dto1.getEndTime(),
                    dto1.getStatisticalCaliber(), requestDTO.getProductType(), requestDTO.getIndustries(),
                    dto1.getCompareSameLevel(), dto1.getConsumptionLevel(), dto1.getCompareCorpSameLevel());
            List<OnlineReportSaveFlightDisDTO> flightDisIndustryDTOS = saveMapper.toDisDTOS(flightDisIndustryV2DTOS);
            requestDTO.setIndustries(null);
            // 商旅数据
            List<OnlineReportSaveFlightDisV2DTO> flightDisV2DTOS = onlineReportSaveDisDao.queryFlightProportionDetailV2(dto1.getStartTime(), dto1.getEndTime(),
                    dto1.getStatisticalCaliber(), requestDTO.getProductType(), null,
                    dto1.getCompareSameLevel(), dto1.getConsumptionLevel(), dto1.getCompareCorpSameLevel());
            List<OnlineReportSaveFlightDisDTO> flightDisDTOS = saveMapper.toDisDTOS(flightDisV2DTOS);
            OnlineReportSaveFlightDisDTO corpDto = flightDisDTOS.get(0);
            OnlineReportSaveFlightDisDTO dto = new OnlineReportSaveFlightDisDTO();
            if (flightDisIndustryDTOS.size() == 1) {
                dto = flightDisIndustryDTOS.get(0);
            }
            // 三方
            OnlineReportSaveProportionDetail var1 = new OnlineReportSaveProportionDetail();
            var1.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_AMOUNT.getUnit(), lang));
            var1.setIndustrySavePerQuantity(BizUtils.round2(dto.getCSavePerQuantity()));
            var1.setIndustrySaveRate(BizUtils.round2(dto.getCSaveRate()));
            var1.setCorpSaveRate(BizUtils.round2(corpDto.getCSaveRate()));
            var1.setCorpSavePerQuantity(BizUtils.round2(corpDto.getCSavePerQuantity()));
            var1.setMinReportDate(StringUtils.isEmpty(flightAccDTO.getMinReportDate()) ? DEFAULT_SAVE_MIN_DATE : flightAccDTO.getMinReportDate());
            var1.setAccTypeNum(BizUtils.round2(flightAccDTO.getCSave()));
            saveDisDetail.put(OnlineTrendFieldEnum.FLIGHT_3C_SAVE_AMOUNT.getNameKey(), var1);
            // 两方
            // 去除 商旅尊享节省金额
/*            OnlineReportSaveProportionDetail var2 = new OnlineReportSaveProportionDetail();
            var2.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.FLIGHT_PREMIUM_SAVE_AMOUNT.getUnit(), lang));
            var2.setIndustrySavePerQuantity(BizUtils.round2(dto.getPremiumSavePerQuantity()));
            var2.setIndustrySaveRate(BizUtils.round2(dto.getPremiumSaveRate()));
            var2.setCorpSaveRate(BizUtils.round2(corpDto.getPremiumSaveRate()));
            var2.setCorpSavePerQuantity(BizUtils.round2(corpDto.getPremiumSavePerQuantity()));
            var2.setMinReportDate(StringUtils.isEmpty(flightAccDTO.getMinReportDate()) ? DEFAULT_SAVE_MIN_DATE : flightAccDTO.getMinReportDate());
            var2.setAccTypeNum(BizUtils.round2(flightAccDTO.getPremiumSave()));
            saveDisDetail.put(OnlineTrendFieldEnum.FLIGHT_PREMIUM_SAVE_AMOUNT.getNameKey(), var2);*/

            // 管控
            OnlineReportSaveProportionDetail var3 = new OnlineReportSaveProportionDetail();
            var3.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getUnit(), lang));
            var3.setIndustrySavePerQuantity(BizUtils.round2(dto.getControlSavePerQuantity()));
            var3.setIndustrySaveRate(BizUtils.round2(dto.getControlSaveRate()));
            var3.setCorpSaveRate(BizUtils.round2(corpDto.getControlSaveRate()));
            var3.setCorpSavePerQuantity(BizUtils.round2(corpDto.getControlSavePerQuantity()));
            var3.setMinReportDate(StringUtils.isEmpty(flightAccDTO.getMinReportDate()) ? DEFAULT_SAVE_MIN_DATE : flightAccDTO.getMinReportDate());
            var3.setAccTypeNum(BizUtils.round2(flightAccDTO.getControlSave()));
            saveDisDetail.put(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getNameKey(), var3);

        }

        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
            List<OnlineReportSaveHotelDisDTO> hotelAccDTOS = onlineReportSaveDisDao.queryHotelAccProportionDetail(requestDTO);
            OnlineReportSaveHotelDisDTO hotelAccDTO = hotelAccDTOS.size() == 1 ? hotelAccDTOS.get(0) : new OnlineReportSaveHotelDisDTO();
            BaseQueryConditionDTO dto1 = requestDTO.getBaseQueryCondition();
            // 商旅
            List<OnlineReportSaveHotelDisDTO> industryHotelDisDTOS = onlineReportSaveDisDao.queryHotelProportionDetail(dto1.getStartTime(), dto1.getEndTime(),
                    dto1.getStatisticalCaliber(), requestDTO.getProductType(), requestDTO.getIndustries(),
                    dto1.getCompareSameLevel(), dto1.getConsumptionLevel(), dto1.getCompareCorpSameLevel());
            requestDTO.setIndustries(null);
            // 行业
            List<OnlineReportSaveHotelDisDTO> hotelDisDTOS = onlineReportSaveDisDao.queryHotelProportionDetail(dto1.getStartTime(), dto1.getEndTime(),
                    dto1.getStatisticalCaliber(), requestDTO.getProductType(), requestDTO.getIndustries(),
                    dto1.getCompareSameLevel(), dto1.getConsumptionLevel(), dto1.getCompareCorpSameLevel());
            OnlineReportSaveHotelDisDTO corpDto = hotelDisDTOS.get(0);
            OnlineReportSaveHotelDisDTO dto = new OnlineReportSaveHotelDisDTO();
            if (industryHotelDisDTOS.size() == 1) {
                dto = industryHotelDisDTOS.get(0);
            }
            // 三方
            OnlineReportSaveProportionDetail var1 = new OnlineReportSaveProportionDetail();
            var1.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_3C_SAVE_AMOUNT.getUnit(), lang));
            var1.setIndustrySavePerQuantity(BizUtils.round2(dto.getCSavePerQuantity()));
            var1.setIndustrySaveRate(BizUtils.round2(dto.getCSaveRate()));
            var1.setCorpSaveRate(BizUtils.round2(corpDto.getCSaveRate()));
            var1.setCorpSavePerQuantity(BizUtils.round2(corpDto.getCSavePerQuantity()));
            var1.setMinReportDate(StringUtils.isEmpty(hotelAccDTO.getMinReportDate()) ? DEFAULT_SAVE_MIN_DATE : hotelAccDTO.getMinReportDate());
            var1.setAccTypeNum(BizUtils.round2(hotelAccDTO.getCSave()));
            saveDisDetail.put(OnlineTrendFieldEnum.HOTEL_3C_SAVE_AMOUNT.getNameKey(), var1);

            // 两方, 最早日期需要和2022-07-24比较,取大的
/*            OnlineReportSaveProportionDetail var2 = new OnlineReportSaveProportionDetail();
            var2.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_PREMIUM_SAVE_AMOUNT.getUnit(), lang));
            var2.setIndustrySavePerQuantity(BizUtils.round2(dto.getPremiumSavePerQuantity()));
            var2.setIndustrySaveRate(BizUtils.round2(dto.getPremiumSaveRate()));
            var2.setCorpSaveRate(BizUtils.round2(corpDto.getPremiumSaveRate()));
            var2.setCorpSavePerQuantity(BizUtils.round2(corpDto.getPremiumSavePerQuantity()));
            var2.setMinReportDate(StringUtils.isEmpty(hotelAccDTO.getMinReportDate()) ? DEFAULT_SAVE_MIN_DATE : hotelAccDTO.getMinReportDate());
            var2.setMinReportDate(
                    betweenDay(var2.getMinReportDate(), HOTEL_PREMIUM_EARLIEST_DAY) > 0 ? HOTEL_PREMIUM_EARLIEST_DAY : var2.getMinReportDate()
            );
            var2.setAccTypeNum(BizUtils.round2(hotelAccDTO.getPremiumSave()));
            saveDisDetail.put(OnlineTrendFieldEnum.HOTEL_PREMIUM_SAVE_AMOUNT.getNameKey(), var2);*/

            // 去除 商旅优惠活动节省金额
/*            // 优惠活动
            OnlineReportSaveProportionDetail var3 = new OnlineReportSaveProportionDetail();
            var3.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_AMOUNT.getUnit(), lang));
            var3.setIndustrySavePerQuantity(BizUtils.round2(dto.getPromotionSavePerQuantity()));
            var3.setIndustrySaveRate(BizUtils.round2(dto.getPromotionSaveRate()));
            var3.setCorpSaveRate(BizUtils.round2(corpDto.getPromotionSaveRate()));
            var3.setCorpSavePerQuantity(BizUtils.round2(corpDto.getPromotionSavePerQuantity()));
            var3.setMinReportDate(StringUtils.isEmpty(hotelAccDTO.getMinReportDate()) ? DEFAULT_SAVE_MIN_DATE : hotelAccDTO.getMinReportDate());
            var3.setAccTypeNum(BizUtils.round2(hotelAccDTO.getPromotionSave()));
            saveDisDetail.put(OnlineTrendFieldEnum.HOTEL_PROMOTION_SAVE_AMOUNT.getNameKey(), var3);*/

            // 管控
            OnlineReportSaveProportionDetail var4 = new OnlineReportSaveProportionDetail();
            var4.setType(SharkUtils.getHeaderVal(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getUnit(), lang));
            var4.setIndustrySavePerQuantity(BizUtils.round2(dto.getControlSavePerQuantity()));
            var4.setIndustrySaveRate(BizUtils.round2(dto.getControlSaveRate()));
            var4.setCorpSaveRate(BizUtils.round2(corpDto.getControlSaveRate()));
            var4.setCorpSavePerQuantity(BizUtils.round2(corpDto.getControlSavePerQuantity()));
            var4.setMinReportDate(StringUtils.isEmpty(hotelAccDTO.getMinReportDate()) ? DEFAULT_SAVE_MIN_DATE : hotelAccDTO.getMinReportDate());
            var4.setAccTypeNum(BizUtils.round2(hotelAccDTO.getControlSave()));
            saveDisDetail.put(OnlineTrendFieldEnum.CONTROL_SAVE_AMOUNT.getNameKey(), var4);
        }
        return saveDisDetail;
    }

}
