package com.corpgovernment.resource.schedule.onlinereport.save;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatisticalEnumerable;
import com.corpgovernment.resource.schedule.onlinereport.enums.save.FlightPreOrderHeaderEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/*
 * <AUTHOR>
 * @date 2022/6/23 23:55
 * @Desc
 */
public abstract class AbstractSaveAndLossBiz {
    public abstract List<StatisticalEnumerable> getStatisticalList(QueryReportBuTypeEnum queryReportBuTypeEnum);

    /**
     * 获取标题
     *
     * @return
     */
    public List<HeaderKeyValMap> getHearder(AnalysisObjectEnum analysisObjectEnum, String lang,
                                            QueryReportBuTypeEnum queryReportBuTypeEnum) {
        List<HeaderKeyValMap> mapDimensions = getDimesionsDesc(analysisObjectEnum, lang);
        List<HeaderKeyValMap> mapStaticals = getStaticalsDes(getStatisticalList(queryReportBuTypeEnum), lang);
        mapDimensions.addAll(mapStaticals);
        if (analysisObjectEnum == AnalysisObjectEnum.UID) {
            mapDimensions.add(getHeaderKeyValMap("dept1", "Exceltopname.depone", lang));
            mapDimensions.add(getHeaderKeyValMap("cost_center1", "Exceltopname.costcenterone", lang));
            /*mapDimensions.add(getHeaderKeyValMap("dept2", "Exceltopname.deptwo", lang));
            mapDimensions.add(getHeaderKeyValMap("dept3", "Exceltopname.depthree", lang));
            mapDimensions.add(getHeaderKeyValMap("dept4", "Exceltopname.depfour", lang));
            mapDimensions.add(getHeaderKeyValMap("dept5", "Exceltopname.depfive", lang));
            mapDimensions.add(getHeaderKeyValMap("dept6", "Exceltopname.depsix", lang));
            mapDimensions.add(getHeaderKeyValMap("dept7", "Exceltopname.depseven", lang));
            mapDimensions.add(getHeaderKeyValMap("dept8", "Exceltopname.depeight", lang));
            mapDimensions.add(getHeaderKeyValMap("dept9", "Exceltopname.depnight", lang));
            mapDimensions.add(getHeaderKeyValMap("dept10", "Exceltopname.depten", lang));
            mapDimensions.add(getHeaderKeyValMap("cost_center2", "Exceltopname.costcentertwo", lang));
            mapDimensions.add(getHeaderKeyValMap("cost_center3", "Exceltopname.costcenterthree", lang));
            mapDimensions.add(getHeaderKeyValMap("cost_center4", "Exceltopname.costcenterfour", lang));
            mapDimensions.add(getHeaderKeyValMap("cost_center5", "Exceltopname.costcenterfive", lang));
            mapDimensions.add(getHeaderKeyValMap("cost_center6", "Exceltopname.costcentersix", lang));*/
        }
        return mapDimensions;
    }

    /**
     * 机票提前预定天数列表header
     */
    protected List<HeaderKeyValMap> getPreOrderDateHeader(String lang) {
        List<HeaderKeyValMap> result = Lists.newArrayList();
        result.add(new HeaderKeyValMap(FlightPreOrderHeaderEnum.COL_1.getName(),
                SharkUtils.getHeaderVal(FlightPreOrderHeaderEnum.COL_1.getSharkKey(), lang)));

        result.add(new HeaderKeyValMap(FlightPreOrderHeaderEnum.COL_2.getName(),
                SharkUtils.getHeaderVal(FlightPreOrderHeaderEnum.COL_2.getSharkKey(), lang)));

        result.add(new HeaderKeyValMap(FlightPreOrderHeaderEnum.COL_3.getName(),
                SharkUtils.getHeaderVal(FlightPreOrderHeaderEnum.COL_3.getSharkKey(), lang)));

        result.add(new HeaderKeyValMap(FlightPreOrderHeaderEnum.COL_4.getName(),
                SharkUtils.getHeaderVal(FlightPreOrderHeaderEnum.COL_4.getSharkKey(), lang)));

        result.add(new HeaderKeyValMap(FlightPreOrderHeaderEnum.COL_5.getName(),
                SharkUtils.getHeaderVal(FlightPreOrderHeaderEnum.COL_5.getSharkKey(), lang)));

        result.add(new HeaderKeyValMap(FlightPreOrderHeaderEnum.COL_6.getName(),
                SharkUtils.getHeaderVal(FlightPreOrderHeaderEnum.COL_6.getSharkKey(), lang)));

        result.add(new HeaderKeyValMap(FlightPreOrderHeaderEnum.COL_7.getName(),
                SharkUtils.getHeaderVal(FlightPreOrderHeaderEnum.COL_7.getSharkKey(), lang)));
        return result;
    }

    /**
     * 获取统计项标题
     *
     * @param statisticalStringList
     * @return
     */
    private List<HeaderKeyValMap> getStaticalsDes(List<StatisticalEnumerable> statisticalStringList, String lang) {
        List<HeaderKeyValMap> result = new ArrayList<>();
        for (StatisticalEnumerable statisticalEnum : statisticalStringList) {
            HeaderKeyValMap headerKeyValMap = new HeaderKeyValMap();
            headerKeyValMap.setHeaderKey(statisticalEnum.toString());
            headerKeyValMap.setHeaderValue(SharkUtils.get(statisticalEnum.getSharkKey(), lang));
            result.add(headerKeyValMap);
        }
        return result;
    }

    /**
     * 获得维度标题
     *
     * @param analysisObjectEnum
     * @param lang
     * @return
     */
    private List<HeaderKeyValMap> getDimesionsDesc(AnalysisObjectEnum analysisObjectEnum, String lang) {
        List<HeaderKeyValMap> result = new ArrayList<>();
        switch (analysisObjectEnum) {
            case CORP:
                result.add(getHeaderKeyValMap(AnalysisObjectEnum.CORP, "Index.public", lang));
                break;
            case ACCOUNT:
                // 去除主账户
                /*result = defaultHeader(lang);
                result.add(getHeaderKeyValMap(AnalysisObjectEnum.ACCOUNT, "DeptAnalysis.AccountName", lang));*/
                break;
            case ACCOUNTCODE:
                result = defaultHeader(lang);
                result.add(getHeaderKeyValMap(AnalysisObjectEnum.ACCOUNTCODE, "DeptAnalysis.AccountCode", lang));
                break;
            case DEPT1:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depone", lang));
                break;
            case DEPT2:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.deptwo", lang));
                break;
            case DEPT3:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depthree", lang));
                break;
            case DEPT4:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depfour", lang));
                break;
            case DEPT5:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depfive", lang));
                break;
            case DEPT6:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depsix", lang));
                break;
            case DEPT7:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depseven", lang));
                break;
            case DEPT8:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depeight", lang));
                break;
            case DEPT9:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depnight", lang));
                break;
            case DEPT10:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.depten", lang));
                break;
            case COSTCENTER1:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcenterone", lang));
                break;
            case COSTCENTER2:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcentertwo", lang));
                break;
            case COSTCENTER3:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcenterthree", lang));
                break;
            case COSTCENTER4:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcenterfour", lang));
                break;
            case COSTCENTER5:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcenterfive", lang));
                break;
            case COSTCENTER6:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.costcentersix", lang));
                break;
            case UID:
                result.add(getHeaderKeyValMap(analysisObjectEnum, "Exceltopname.uidnumber", lang));
                result.add(getHeaderKeyValMap("NAME", "Index.idholder", lang));
                break;
            default:
                break;
        }
        return result;
    }

    private List defaultHeader(String lang) {
        List result = new ArrayList<>();
        result.add(getHeaderKeyValMap(AnalysisObjectEnum.CORP, "Index.public", lang));
        return result;
    }

    private HeaderKeyValMap getHeaderKeyValMap(AnalysisObjectEnum analysisObjectEnum, String sharkKey, String lang) {
        HeaderKeyValMap headerKeyValMap = new HeaderKeyValMap();
        headerKeyValMap.setHeaderKey(analysisObjectEnum.toString());
        headerKeyValMap.setHeaderValue(SharkUtils.getHeaderVal(sharkKey, lang));
        return headerKeyValMap;
    }

    private HeaderKeyValMap getHeaderKeyValMap(String key, String sharkKey, String lang) {
        HeaderKeyValMap headerKeyValMap = new HeaderKeyValMap();
        headerKeyValMap.setHeaderKey(key);
        headerKeyValMap.setHeaderValue(SharkUtils.getHeaderVal(sharkKey, lang));
        return headerKeyValMap;
    }

    protected Integer compareValAndGetDefault(Double saveAmountPremium, Integer defaultVal) {
        if (saveAmountPremium == null || saveAmountPremium < 0) {
            return 0;
        }
        return defaultVal;
    }

    protected Double compareValAndGetDefault(Double saveAmountPremium, Double defaultVal) {
        if (saveAmountPremium == null || saveAmountPremium < 0) {
            return 0d;
        }
        return defaultVal;
    }

    protected Double compareZeroAndGetDefault(Double saveAmountPremium) {
        if (saveAmountPremium == null || saveAmountPremium < 0) {
            return 0d;
        }
        return saveAmountPremium;
    }

    protected BigDecimal compareZeroAndGetDefault(BigDecimal saveAmountPremium) {
        if (saveAmountPremium == null || saveAmountPremium.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        return saveAmountPremium;
    }
}
