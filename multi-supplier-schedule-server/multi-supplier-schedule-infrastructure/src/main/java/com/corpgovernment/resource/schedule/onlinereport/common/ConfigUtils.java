package com.corpgovernment.resource.schedule.onlinereport.common;

import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/*
 * <AUTHOR>
 * @date 2021/11/16 14:08
 * @Desc
 */
public class ConfigUtils {


    public static String getString(String key, String defaultVal) {
        return ConfigService.getConfig("app.properties").getProperty(key, defaultVal);
    }

    public static boolean getBoolean(String key, boolean defaultValue) {
        String result = getString(key, String.valueOf(defaultValue));
        if (StringUtils.equalsIgnoreCase(Boolean.FALSE.toString(), result)) {
            return false;
        } else if (StringUtils.equalsIgnoreCase(Boolean.TRUE.toString(), result)) {
            return true;
        } else {
            return false;
        }
    }

    public static int getNullDefaultInterValue(String key, int defaultVal) {
        return org.apache.commons.lang.StringUtils.isNotEmpty(ConfigService.getConfig("app.properties").getProperty(key, StringUtils.EMPTY)) ? Integer.parseInt(ConfigService.getConfig("app.properties").getProperty(key, StringUtils.EMPTY)) : defaultVal;
    }

    /**
     * 获取开通了合规系统的公司id
     *
     * @param key
     * @return
     */
    public static String[] getOperatorCorpIds(String key) {
        String result = getString(key, StringUtils.EMPTY);
        if (StringUtils.isNotEmpty(result)) {
            return result.split(";");
        } else {
            return null;
        }
    }

    /**
     * 获取开通了合规系统的公司id
     *
     * @param
     * @return
     */
    public static boolean getSrChangeToNewSwitch() {
        String result = getString("sr_change_to_new_switch", Boolean.FALSE.toString());
        if (StringUtils.equalsIgnoreCase(Boolean.FALSE.toString(), result)) {
            return false;
        } else if (StringUtils.equalsIgnoreCase(Boolean.TRUE.toString(), result)) {
            return true;
        } else {
            return false;
        }
    }

    public static List<String> getOperationCloseRiskScene() {
        String blackList = getString("operation_close_risk_scene", StringUtils.EMPTY);
        if (org.apache.commons.lang.StringUtils.isEmpty(blackList)) {
            return null;
        }
        return Arrays.asList(blackList.split(";"));
    }

    /**
     * 蓝色空间部门分析多线程开关
     *
     * @return
     */
    public static Boolean blueSpaceDeptAnalysisMulitThread() {
        String result = getString("blue_space_dept_analysis_mulit_thread", StringUtils.EMPTY);
        if (StringUtils.equalsIgnoreCase(Boolean.FALSE.toString(), result)) {
            return false;
        } else if (StringUtils.equalsIgnoreCase(Boolean.TRUE.toString(), result)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 分区开关
     *
     * @return
     */
    public static Boolean getModulePartitionSwitch(String module) {
        String result = getString(module + "_partition_switch", StringUtils.EMPTY);
        if (StringUtils.equalsIgnoreCase(Boolean.FALSE.toString(), result)) {
            return false;
        } else if (StringUtils.equalsIgnoreCase(Boolean.TRUE.toString(), result)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 币种必传开关
     *
     * @return
     */
    public static Boolean isMustCurrencySwitch() {
        String result = getString("must_currency_switch", StringUtils.EMPTY);
        if (StringUtils.equalsIgnoreCase(Boolean.FALSE.toString(), result)) {
            return false;
        } else if (StringUtils.equalsIgnoreCase(Boolean.TRUE.toString(), result)) {
            return true;
        } else {
            return false;
        }
    }

    public static List<String> getSrGreyUids() {
        String uidList = getString("sr_grey_uid", StringUtils.EMPTY);
        if (org.apache.commons.lang.StringUtils.isEmpty(uidList)) {
            return Lists.newArrayList();
        }
        return Arrays.asList(uidList.split(";"));
    }

    /**
     * 获取公账支付方式
     *
     * @param defaultList
     * @return
     */
    public static List<String> getAccntType(List<String> defaultList) {
        String accnt = getString("prepayType.accnt", StringUtils.EMPTY);
        if (org.apache.commons.lang.StringUtils.isEmpty(accnt)) {
            return defaultList;
        }
        List<String> accntType = Arrays.asList(accnt.split(";"));
        if (CollectionUtils.isEmpty(accntType)) {
            return defaultList;
        }
        return accntType;
    }

/*    private void printMapConfig() {
        for (Map.Entry<String, String> entry : configs.entrySet()) {
            System.out.println(entry.getKey() + "=" + entry.getValue());
        }
    }*/

}
