package com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail;


import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.TimeFilterTypeInfoDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-21 13:39
 * @desc
 */
@Service
@Repository
public class HtlOrderAudidDetailDaoImpl extends AbstractCommonDao {

    public final <T> List<T> queryDetail(BaseQueryConditionDTO request, Class<T> clazz) throws Exception {
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEXHOTEL_ORDER_AUDIT;
        sqlBuilder.append("select * ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        parmList.add(queryPartition(clickHouseTable));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(request, parmList));
        sqlBuilder.append(buildPreSqlTime(request.getTimeFilterList(), parmList));
        sqlBuilder.append(buildPreSqlOrderIds(request.getOrderIds(), parmList));
        sqlBuilder.append(buildPreSqlUser(request.getUsers(), parmList));
        sqlBuilder.append(" and is_audit = '" + ConfigUtils.getString("HOTEL_ISAUDIT", "") + "'");
        sqlBuilder.append(" order by order_date,orderid,order_status,audit_client_name desc");
        sqlBuilder.append(buildPage(parmList, request.getPager()));

        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    public Integer detailCount(BaseQueryConditionDTO request) throws Exception {
        List<Object> paramList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEXHOTEL_ORDER_AUDIT;
        sqlBuilder.append("select count(1) as countAll");
        sqlBuilder.append(" from ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(" WHERE d = ? ");
        paramList.add(queryPartition(clickHouseTable));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(request, paramList));
        sqlBuilder.append(this.buildPreSqlTime(request.getTimeFilterList(), paramList));
        sqlBuilder.append(buildPreSqlOrderIds(request.getOrderIds(), paramList));
        sqlBuilder.append(buildPreSqlUser(request.getUsers(), paramList));
        sqlBuilder.append(" and is_audit = '" + ConfigUtils.getString("HOTEL_ISAUDIT", "") + "'");
        // 查询clickhouse
        return commonCount(sqlBuilder.toString(), paramList);
    }

    protected String buildPreSqlTime(List<TimeFilterTypeInfoDTO> timeFilterTypeInfoList, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isEmpty(timeFilterTypeInfoList)) {
            return sqlBuffer.toString();
        }
        for (TimeFilterTypeInfoDTO timeFilterTypeInfo : timeFilterTypeInfoList) {
            // 预订时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate")) {
                sqlBuffer.append(buildTimeFilter(timeFilterTypeInfo, parmList));
            }
            // 入住日期
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "usedate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    sqlBuffer.append(" and ").append("subString(arrival_date_time, 1, 10)").append(" >= ? ");
                    parmList.add(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    sqlBuffer.append(" and ").append("subString(arrival_date_time, 1, 10)").append(" <= ? ");
                    parmList.add(timeFilterTypeInfo.getEndTime());
                }
            }
        }
        return sqlBuffer.toString();
    }

    protected String buildTimeFilter(TimeFilterTypeInfoDTO timeFilterTypeInfo, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        // 预订时间
        if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate")) {
            if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                sqlBuffer.append(" and ").append("subString(order_date, 1, 10)").append(" >= ? ");
                paramList.add(timeFilterTypeInfo.getStartTime());
            }
            if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                sqlBuffer.append(" and ").append("subString(order_date, 1, 10)").append(" <= ? ");
                paramList.add(timeFilterTypeInfo.getEndTime());
            }
        }
        return sqlBuffer.toString();
    }

    /**
     * 出行人
     *
     * @param users    （uid, user_name）
     * @param parmList
     * @return
     */
    protected String buildPreSqlUser(List<String> users, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(users)) {
            sqlBuffer.append(" and (");
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionNoAnd("audit_client_name", users, parmList));
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }

    /**
     * 订单号
     *
     * @param orderIds
     * @param parmList
     * @return
     */
    protected String buildPreSqlOrderIds(List<String> orderIds, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(orderIds)) {
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionWtihAnd("orderid", orderIds, parmList));
        }
        return sqlBuffer.toString();
    }
}
