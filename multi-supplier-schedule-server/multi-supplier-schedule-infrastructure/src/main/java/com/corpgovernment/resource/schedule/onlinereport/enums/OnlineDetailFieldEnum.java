package com.corpgovernment.resource.schedule.onlinereport.enums;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums
 * @description:
 * @author: md_wang
 * @create: 2021-11-08 12:50
 **/
public enum OnlineDetailFieldEnum {
    /**
     * 概览
     */
    DIMENSION("dimension", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.dimension")),
    FLIGHT_V("flightV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightV")),
    FLIGHT_YOY("flightYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    FLIGHT_MOM("flightMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    HOTEL_V("hotelV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelV")),
    HOTEL_YOY("hotelYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    HOTEL_MOM("hotelMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    TRAIN_V("trainV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainV")),
    TRAIN_YOY("trainYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    TRAIN_MOM("trainMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    CAR_V("carV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.carV")),
    CAR_YOY("carYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    CAR_MOM("carMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    BUS_V("busV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.busV")),
    BUS_YOY("busYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    BUS_MOM("busMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    ADD_V("addV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.addV")),
    ADD_YOY("addYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    ADD_MOM("addMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    TOTAL_V("totalV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.totalV")),
    TOTAL_YOY("totalYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    TOTAL_MOM("totalMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),


    /**
     * 机票-净价
     */
    NETFARE("netfare", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightNetPrice")),
    /**
     * 机票-机建税
     */
    TAX("tax", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightMachineConstructionTax")),
    /**
     * 机票-燃油费
     */
    OIL_FEE("oilFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightFuelCosts")),
    /**
     * 机票-增值服务包费
     */
    SERVICEPACKAGE_FEE("servicepackageFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightServicePackageFee")),
    /**
     * 机票-绑定酒店优惠券
     */
    BIND_AMOUNT("bindAmount", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightBindHotelCoupons")),
    /**
     * 机票-改签费
     */
    CHANGE_FEE("changeFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightChangeFee")),
    /**
     * 机票-改签燃油差
     */
    OILFEEDIFFERENTIAL("oilfeedifferential", "index.oilfeediffernetial"),
    /**
     * 机票-税差
     */
    TAX_DIFFERENTIAL("taxDifferential", "index.taxdifferential"),
    /**
     * 机票-改签商旅管理服务费
     */
    REBOOK_SERVICE_FEE("rebookServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightChangeTravelManagementServiceFee")),
    /**
     * 机票-退票费
     */
    REFUND_FEE("refundFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightRefundFee")),
    /**
     * 机票-退票商旅管理服务费
     */
    REFUND_SERVICE_FEE("refundServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightRefundManagementServiceFee")),
    /**
     * 机票-退票行程单商旅管理服务费
     */
    REFUND_ITINERARY_FEE("refundItineraryFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.flightRefundTravelManagementServiceFee")),
    /**
     * 机票-后收商旅管理服务费
     */
    TICKET_BEHIND_SERVICE_FEE("ticketBehindServicefee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.ticketBehindServicefee")),
    /**
     * 机票-后收改签商旅管理服务费
     */
    REBOOK_BEHIND_SERVICE_FEE("rebookBehindServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.rebookBehindServiceFee")),
    /**
     * 机票-后收退票商旅管理服务费
     */
    REFUND_BEHIND_SERVICE_FEE("refundBehindServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.refundBehindServiceFee")),


    /**
     * hotel-房费
     */
    ROOM_PRICE("roomPrice", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelRomPrice")),
    /**
     * hotel-前收商旅服务费
     */
    HOTEL_SERVICE_FEE("hotelServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelServiceFee")),
    /**
     * hotel-优惠券
     */
    COUPON_AMOUNT("couponAmount", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelCoupon")),

    /**
     * hotel-后收商旅管理服务费
     */
    HOTEL_POST_SERVICE_FEE("hotelPostServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelPostServiceFee")),

    /**
     * hotel-外卡服务费
     */
    CARD_PAY_SERVICE_FEE("hotelCardPayServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.cardPayServiceFee")),
    TAX_AMOUNT("hotelTaxAmount", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.hotelTaxAmount")),
    /**
     * 火车-后收后取票服务费
     */
    AFTERAFTERTAKETICKETFEE("afteraftertaketicketfee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainAfteraftertaketicketfee")),
    /**
     * 后收改签服务费
     */
    AFTERCHANGESERVICEFEE("afterchangeservicefee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainAfterchangeservicefee")),
    /**
     * 代取票人工费
     */
    AFTERTAKETICKETFEE("aftertaketicketfee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainReplacementOfLaborCosts")),
    /**
     * 后收服务费
     */
    AFTER_SERVICE_FEE("afterServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainAfterServiceFee")),
    /**
     * 改签时票面差价
     */
    CHANGEBALANCE("changebalance", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainChangebalance")),
    /**
     * 改签时商旅收取的管理服务费
     */
    DEAL_CHANGE_SERVICE_FEE("dealChangeServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainDealChangeServiceFee")),
    /**
     * 抢票费
     */
    GRAB_SERVICE_FEE("grabServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainGrabServiceFee")),
    /**
     * 代购服务费
     */
    PURCHASE_SERVICE_FEE("purchaseServiceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainPurchaseServiceFee")),
    /**
     * 退款(改签差额)
     */
    CHANGE_BALANCE_REFUND_AMOUNT("changeBalanceRefundAmount", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainChangeBalanceRefundAmount")),
    /**
     * 预估改签手续费12306(发生改签时才收取)
     */
    EST_FEE_12306("estFee12306", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainEstFee12306")),
    /**
     * 纸质出票费
     */
    PAPER_TICKET_FEE("paperTicketFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainPaperTicketFee")),
    /**
     * 出票服务费(国际)
     */
    ISSUE_TICKET_FEE("issueTicketFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainIssueTicketFee")),
    /**
     * 退票时退补给客户的金额
     */
    REFUND_TICKET_FEE("refundTicketFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainRefundTicketFee")),
    /**
     * 原始出票金额(票价)
     */
    TICKET_PRICE("ticketPrice", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.trainTicketPrice")),


    /**
     * 用车-基础费用
     */
    BASIC_FEE("basicFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.basicFee")),

    /**
     * 用车-退款金额
     */
    REFUND_AMOUNT("refundAmount", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.refundAmount")),
    TAKE_CAR_V("takeCarV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.takeCarV")),
    TAKE_CAR_YOY("takeCarYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    TAKE_CAR_MOM("takeCarMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    TAKE_CAR_V_INTER("takeCarVInter", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.takeCarVInter")),
    TAKE_CAR_YOY_INTER("takeCarYoyInter", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    TAKE_CAR_MOM_INTER("takeCarMomInter", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    TRANSFER_V("transferV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.transferV")),
    TRANSFER_YOY("transferYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    TRANSFER_MOM("transferMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    TRANSFER_V_INTER("transferVInter", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.transferVInter")),
    TRANSFER_YOY_INTER("transferYoyInter", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    TRANSFER_MOM_INTER("transferMomInter", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    RENTAL_CAR_V("rentalCarV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.rentalCarV")),
    RENTAL_CAR_YOY("rentalCarYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    RENTAL_CAR_MOM("rentalCarMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    RENTAL_CAR_V_INTER("rentalCarVInter", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.rentalCarVInter")),
    RENTAL_CAR_YOY_INTER("rentalCarYoyInter", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    RENTAL_CAR_MOM_INTER("rentalCarMomInter", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    CHARTER_CAR_V("charterCarV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.charterCarV")),
    CHARTER_CAR_YOY("charterCarYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    CHARTER_CAR_MOM("charterCarMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),

    /**
     * 协议
     */
    AGREEMENT_V("agreementV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.agreement")),
    AGREEMENT_YOY("agreementYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    AGREEMENT_MOM("agreementMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    /**
     * 非协议
     */
    UN_AGREEMENT_V("unAgreementV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.nonAgreement")),
    UN_AGREEMENT_YOY("unAgreementYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    UN_AGREEMENT_MOM("unAgreementMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    /**
     * 国内
     */
    DOMESTIC_V("domesticV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.domestic")),
    DOMESTIC_YOY("domesticYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    DOMESTIC_MOM("domesticMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),
    /**
     * 国际
     */
    INTERNATIONAL_V("internationalV", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.international")),
    INTERNATIONAL_YOY("internationalYoy", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.yoy")),
    INTERNATIONAL_MOM("internationalMom", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.mom")),


    /**
     * 小计
     */
    SUBTOTAL("subtotal", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.subtotal")),

    /**
     * 保险费
     */
    INSURANCE_FEE("insuranceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.insurance")),
    /**
     * 配送费
     */
    SEND_TICKET_FEE("sendTicketFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.deliveryFee")),
    /**
     * 改签差价
     */
    REBOOK_PRICE_DIFFERENTIAL("rebookPriceDifferential", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.common.changePriceDiff")),

    /**
     * 商旅管理服务费
     */
    SERVICE_FEE("serviceFee", OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.train.serviceFee"));


    private String name;

    private String headerKey;

    OnlineDetailFieldEnum(String headerKey, String name) {
        this.headerKey = headerKey;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHeaderKey() {
        return headerKey;
    }

    public void setHeaderKey(String headerKey) {
        this.headerKey = headerKey;
    }
}
