package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.behavioranalysis;


import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
public interface CarBehaviorAnalysisDaoService {
    /**
     * 用车行为
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> behavior(BaseQueryConditionDTO requestDto, Class<T> clazz, Map<String, String> extParams) throws Exception;

    /**
     * 用车随心订（混付
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> mixPaymentAnaylsis(BaseQueryConditionDTO requestDto, Class<T> clazz, Map<String, String> extParams) throws Exception;
}
