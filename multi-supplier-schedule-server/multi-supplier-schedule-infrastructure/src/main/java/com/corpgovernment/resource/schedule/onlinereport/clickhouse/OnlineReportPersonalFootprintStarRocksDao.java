package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportFootprintKeywordDTO;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportPersonalCarbonDto;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportPersonalFootprintDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;

/*
 * <AUTHOR>
 *
 * @date 2022-07-05
 *
 * @Desc
 */
@Repository
@Slf4j
public class OnlineReportPersonalFootprintStarRocksDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "OnlineReportPersonalFootprintDao";

    /**
     * 查询 在线报告 个人足迹
     * 差率足迹 我的选择 我的榜单 等相关数据
     */
    public List<OnlineReportPersonalFootprintDTO> queryFootprint(BaseQueryConditionDTO requestDto)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        // 差旅足迹相关字段
        sqlBuilder.append("select countrycnt,citycnt,citylist,tripcnt,tripdays,tripcnt_rank,tripcnt_rate,workcity,tpms,tpms_to_pacific_cnt,triplist,");
        sqlBuilder.append("citylist_lat,citylist_lon,triplist_lat,triplist_lon,countrylist,countrylist_en,citylist_lat_intl,citylist_lon_intl,");
        sqlBuilder.append("citylist_intl,triplist_intl,triplist_lat_intl,triplist_lon_intl,provincelist,");
        // 我的榜单相关字段
        sqlBuilder.append("tripdays_weekend,mintime,maxtime,longtrip_days,longtrip_tpm,most_visit_city,carbons,");
        sqlBuilder.append("mintime_city,mintime_date,maxtime_city,maxtime_date," +
                "longtrip_dcity,longtrip_acity,longtrip_ddate,longtrip_adate,");
        // 我的选择-机票相关
        sqlBuilder.append("flt_qty,flt_amount,flt_most_airline,flt_most_dcityacity,flt_most_timerange,flt_qty_most_airline,flt_qty_rate,");
        // 我的选择酒店相关
        sqlBuilder.append("htl_qty,htl_amount,htl_most_hotelgroupname,htl_most_city,htl_star,htl_qty_most_hotelgroupname,htl_qty_rate,");
        // 我的选择火车相关
        sqlBuilder.append("train_qty,train_amount,train_most_timerange,train_most_dciyacity,train_qty_rate,");
        // 我的选择用车相关
        sqlBuilder.append("car_qty,car_amount,car_maxtime,car_qty_rate ");

        sqlBuilder.append("from  ");
        sqlBuilder.append(ClickHouseTable.ADM_INDEX_UID.getTable());

        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append("d = ?");
        parmList.add(requestDto.getPartition());
        sqlBuilder.append(" and date = ?");
        parmList.add(requestDto.getStartTime());
        sqlBuilder.append(" and passenger_uid = ?");
        parmList.add(requestDto.getUid());

        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequestV2(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportPersonalFootprintDTO.class, "queryFootprint");
    }

    /**
     * 查询 在线报告 个人足迹
     * 概览
     */
    public List<OnlineReportPersonalFootprintDTO> queryFootprintOverview(BaseQueryConditionDTO requestDto)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        // 差旅足迹相关字段
        sqlBuilder.append("select citycnt,tripcnt,tpms,flt_qty ");
        sqlBuilder.append("from  ");
        sqlBuilder.append(ClickHouseTable.ADM_INDEX_UID.getTable());

        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append("d = ?");
        parmList.add(requestDto.getPartition());
        sqlBuilder.append(" and date = ?");
        parmList.add(requestDto.getStartTime());
        sqlBuilder.append(" and passenger_uid = ?");
        parmList.add(requestDto.getUid());

        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequestV2(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportPersonalFootprintDTO.class, "queryFootprintOverview");
    }

    /**
     * 查询 在线报告 个人足迹年度分享报告
     * 概览
     */
    public List<OnlineReportPersonalFootprintDTO> queryFootprintYearReport(BaseQueryConditionDTO requestDto)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 三屏字段
        sqlBuilder.append(" first_trip_date, first_trip_cites ");
        // 四屏相关数据
        sqlBuilder.append(", citycnt,tripcnt,tripdays,tpms, tripcnt_rate");
        // 五屏相关数据
        sqlBuilder.append(",most_visit_city, most_visit_city_times,  longtrip_acity, longtrip_adate ");
        // 六屏相关数据
        sqlBuilder.append(",mintime,mintime_date, mintime_city, maxtime,maxtime_date,maxtime_city, tripcnt_deepnight ,tripcnt_earlymorning");
        // 七屏相关数据
        sqlBuilder.append(", flt_qty, flt_most_airline, train_qty, train_most_dciyacity,htl_most_hotelgroupname");
        // 八屏相关数据
        sqlBuilder.append(", carbons, green_order_rate, carbon_save");
        // 九屏相关数据
        sqlBuilder.append(", tpms_to_pacific_cnt, longtrip_days, citycnt_over_colleague_rio");
        sqlBuilder.append(", tripcnt_over_colleague_rio, tpms_over_colleague_rio,footprint_keyword ");
        sqlBuilder.append(", corpid, gender ,furthest_city_id, furthest_city_name, furthest_city_distance ");
        sqlBuilder.append(", deepnight_deperttime, deepnight_arrivetime, deepnight_dcityname, deepnight_acityname ");
        sqlBuilder.append(", earlymorning_deperttime, earlymorning_arrivetime, earlymorning_dcityname, earlymorning_acityname, beento_citycnt ");
        sqlBuilder.append(", furthest_city_min_arrival_date ");

        sqlBuilder.append(" from  ");
        sqlBuilder.append(ClickHouseTable.ADM_INDEX_UID_FOOTPRINT_KEYWORD.getTable());

        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append("d = ?");
        parmList.add(requestDto.getPartition());
        sqlBuilder.append(" and date = ?");
        parmList.add(requestDto.getStartTime());
        sqlBuilder.append(" and passenger_uid = ?");
        parmList.add(requestDto.getUid());

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("[[title=queryFootprintYearReport]] sql = {}", logSql);

        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequestV2(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportPersonalFootprintDTO.class, "queryFootprintYearReport");
    }

    /**
     * 查询 在线报告 个人足迹 关键词
     */
    public List<OnlineReportFootprintKeywordDTO> queryFootprintKeyword(BaseQueryConditionDTO requestDto) throws Exception {

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select footprint_keyword");
        // 三屏字段
        sqlBuilder.append(" from  ");
        sqlBuilder.append(ClickHouseTable.ADM_INDEX_UID_FOOTPRINT_KEYWORD.getTable());

        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append("d = ?");
        parmList.add(requestDto.getPartition());
        sqlBuilder.append(" and year = ?");
        parmList.add(requestDto.getStartTime());
        sqlBuilder.append(" and passenger_uid = ?");
        parmList.add(requestDto.getUid());
        sqlBuilder.append(" limit 1");

        String logSql = getLogSql(parmList, sqlBuilder.toString());
        log.info("[[title=queryFootprintKeyword]] sql = {}", logSql);

        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequestV2(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportFootprintKeywordDTO.class, "queryFootprintYearReport");

    }


    /**
     * 查询 在线报告 个人足迹
     * 碳排
     */
    public List<OnlineReportPersonalCarbonDto> queryFootprintFltCarbon(BaseQueryConditionDTO requestDto)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        // 机票碳排
        sqlBuilder.append("select sum(carbon_emission_flt) as carbonEmissionFlt" +
                ",if(sum(flightno_count) = 0, 0, CAST(sum(carbon_emission_flt) AS DOUBLE)/CAST(sum(flightno_count) AS DOUBLE)) as avgCarbonFlt" +
                ",sum(carbon_save_flt) as saveCarbonFlt" +
                ",if(sum(flightno_count) = 0, 0, CAST(sum(green_flightno_count) AS DOUBLE)/ CAST(sum(flightno_count) AS DOUBLE) ) as greenRateFlt");

        // 酒店碳排
        sqlBuilder.append(",sum(carbon_emission_htl) as carbonEmissionHtl" +
                ",avg(if(quantity_htl = 0, 0, CAST(carbon_emission_htl AS DOUBLE)/CAST(quantity_htl AS DOUBLE))) as avgCarbonHtl" +
                ",sum(carbon_save_htl) as saveCarbonHtl" +
                ",if(sum(quantity_htl) = 0, 0, " +
                "CAST(sum(if(carbon_emission_htl < median_carbon_emission_htl, quantity_htl, null) ) AS DOUBLE) / CAST(sum(quantity_htl) AS DOUBLE)) as greenRateHtl");

        // 火车碳排
        sqlBuilder.append(",sum(carbon_emission_trn) as carbonEmissionTrain" +
                ",if(sum(quantity_train) = 0, 0, CAST(sum(carbon_emission_trn) AS DOUBLE)/CAST(sum(quantity_train) AS DOUBLE)) as avgCarbonTrain" +
                ",sum(carbon_save_trn) as saveCarbonTrain" +
                ",if(sum(quantity_train) = 0, 0, " +
                "CAST(sum(green_train_count) AS DOUBLE) / CAST(sum(quantity_train) AS DOUBLE))" +
                " as greenRateTrain");

        // 用车碳排
        sqlBuilder.append(",sum(carbon_emission_car) as carbonEmissionCar" +
                ",if(sum(normal_distance) = 0, 0, CAST(sum(carbon_emission_car) AS DOUBLE)/CAST(sum(normal_distance) AS DOUBLE)) as avgCarbonCar" +
                ",sum(carbon_save_car) as saveCarbonCar" +
                ",if(sum(quantity_car) = 0, 0, CAST(sum(green_car_quantity) AS DOUBLE)/ CAST(sum(quantity_car) AS DOUBLE)) as greenRateCar");

        sqlBuilder.append(", sum(carbon_emission_flt) + sum(carbon_emission_htl) + sum(carbon_emission_trn) + sum(carbon_emission_car) as allTotalCarbon");
        sqlBuilder.append(", sum(carbon_save_flt) + sum(carbon_save_htl) + sum(carbon_save_trn) + sum(carbon_save_car) as allSaveCarbon ");

        sqlBuilder.append(" from  ");
        sqlBuilder.append(ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_CARBON_DTIME.getTable());

        sqlBuilder.append(OrpConstants.WHERE);
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append("d = ?");
        parmList.add(requestDto.getPartition());

        if (StringUtils.isNotEmpty(requestDto.getStartTime())) {
            sqlBuilder.append(" and report_date >= ?");
            parmList.add(requestDto.getStartTime());
        }

        if (StringUtils.isNotEmpty(requestDto.getEndTime())) {
            sqlBuilder.append(" and report_date <= ?");
            parmList.add(requestDto.getEndTime());
        }
        sqlBuilder.append(" and uid = ?");
        parmList.add(requestDto.getUid());

        log.info("[[title=queryFootprintCarbon]] sql = {}", getLogSql(parmList, sqlBuilder.toString()));
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequestV2(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, OnlineReportPersonalCarbonDto.class, "queryFootprintCarbon flt");
    }

    private PreparedStatement mapCommonRequest(List<Object> parmList, PreparedStatement statement,
                                               ClickHouseTable clickHouseTable) {
        int index = OrpConstants.ONE;
        try {
            // 分区
            statement.setString(index++, querySingleTablePartition(clickHouseTable));

            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }

    private PreparedStatement mapCommonRequestV2(List<Object> parmList, PreparedStatement statement) {
        int index = OrpConstants.ONE;
        try {
            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }

}
