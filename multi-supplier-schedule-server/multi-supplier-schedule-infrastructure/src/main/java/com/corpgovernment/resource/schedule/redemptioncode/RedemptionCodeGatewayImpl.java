package com.corpgovernment.resource.schedule.redemptioncode;

import com.corpgovernment.api.organization.soa.RedemptionCodeClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.domain.redemptioncode.gateway.RedemptionCodeGateway;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedemptionCodeGatewayImpl implements RedemptionCodeGateway {

    @Autowired
    private RedemptionCodeClient redemptionCodeClient;


    @Override
    public void bindAllRedemptionCode() {
        JSONResult<Void> result = redemptionCodeClient.bindAllRedemptionCode();
        log.info("bindAllRedemptionCode result: {}", JsonUtils.toJsonString(result));
        XxlJobLogger.log("bindAllRedemptionCode result: {}", JsonUtils.toJsonString(result));
    }
}
