package com.corpgovernment.resource.schedule.onlinereport.bu;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailAmountDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailTicketDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailTrainAmountData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailTrainData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportDataDetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReprotBodyData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReprotTrainBodyData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReprotTrainInfoBodyData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TrainDataInfoDetailData;
import com.corpgovernment.resource.schedule.onlinereport.abs.AbstractOnlineDetailService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportTrainDao;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineDetailRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportTrainDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.DetailReportTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.ReportDetailHeaderEnum;
import com.corpgovernment.resource.schedule.onlinereport.factory.OnlineDetailExecute;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCollectionUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.AtomicDouble;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.service.factory
 * @description: 火车产线
 * @author: Chris Yu
 * @create: 2021-11-08 11:16
 **/
@Service
@Slf4j
public class TrainDetail extends AbstractOnlineDetailService implements OnlineDetailExecute {

    private OnlineReportTrainDao reportTrainDao;

    public TrainDetail(OnlineReportTrainDao reportTrainDao) {
        this.reportTrainDao = reportTrainDao;
    }

    /**
     * query db record
     */
    @Override
    
    public OnlineReportData queryDetailRecord(OnlineReportDetailRequest request) {
        Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerAmount =
                mapperHeader(DetailReportTypeEnum.DETAIL_AMOUNT_REPORT, request.queryBu, request.lang);
        Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerTicket =
                mapperHeader(DetailReportTypeEnum.DETAIL_TICKET_REPORT, request.queryBu, request.lang);
        if (BlueSpaceUtils.isForeign(request.getBasecondition().getPos(), request.getBasecondition().getBlueSpace())) {
            boolean isBlueSpace = BlueSpaceUtils.isBlueSpace(request.getBasecondition().getBlueSpace());
            headerAmount = mapperJPHeader(DetailReportTypeEnum.DETAIL_AMOUNT_REPORT, request.queryBu, request.lang, isBlueSpace);
            headerTicket = mapperJPHeader(DetailReportTypeEnum.DETAIL_TICKET_REPORT, request.queryBu, request.lang, isBlueSpace);
        }
        try {
            OnlineDetailRequestDto requestDto = mapOnlineDetailRequestDto(request);
            List<OnlineReportTrainDto> trainList =
                    reportTrainDao.queryOnlineReportTrain(requestDto, OnlineReportTrainDto.class);
            if (CollectionUtils.isEmpty(trainList)) {
                return mapEmptyAmountTicketData(headerAmount, headerTicket);
            }
            // 获取火车全集数据
            return mapTrainAmountTicketData(requestDto, trainList, headerAmount, headerTicket, request.lang);
        } catch (Exception e) {
            log.error("queryTrainDetailRecord", ExceptionUtils.getFullStackTrace(e));
            return mapEmptyAmountTicketData(headerAmount, headerTicket);
        }
    }

    /**
     * init report header
     */
    @Override
    public Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> mapperHeader(DetailReportTypeEnum reportTypeEnum,
                                                                           QueryReportBuTypeEnum bu, String lang) {
        return mapperHeaderByBu(reportTypeEnum, bu, lang);
    }

    @Override
    public Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> mapperJPHeader(DetailReportTypeEnum reportTypeEnum,
                                                                             QueryReportBuTypeEnum bu, String lang, boolean isBlueSpace) {
        return mapperJPHeaderByBu(reportTypeEnum, bu, lang, isBlueSpace);
    }

    /**
     * 返回报表数据
     *
     * @param request
     * @param trainList
     * @param headerAmount
     * @param headerTicket
     * @param lang         语言环境
     * @return
     */
    private OnlineReportData mapTrainAmountTicketData(OnlineDetailRequestDto request,
                                                      List<OnlineReportTrainDto> trainList, Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerAmount,
                                                      Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerTicket, String lang) {
        DetailAmountDto detailAmount = new DetailAmountDto();
        DetailTicketDto detailTicket = new DetailTicketDto();

        // 整体
        DetailTrainData trainAllDetail =
                mapTrainAllData(request, trainList, headerAmount.get(ReportDetailHeaderEnum.TRAIN_ALL_HEADER), lang);

        // 金额明细

        Map<String, OrpDateTimeUtils.MonthSplitPair> dateYoyMomRange = OrpDateTimeUtils.momSplit(request.getStartTime(), request.getEndTime());
        Map<String, CurrentYoyMomData<OnlineReportTrainDto>> unionData = dateYoyMomRange.entrySet().stream()
                .map(entry -> {
                    CurrentYoyMomData<OnlineReportTrainDto> currentYoyMomData = getCurrentYoyMomTrainData(entry.getValue(), trainList);
                    currentYoyMomData.setMonth(entry.getKey());
                    return currentYoyMomData;
                })
                .collect(Collectors.toMap(CurrentYoyMomData::getMonth, (p) -> p));

        OrpDateTimeUtils.MonthSplitPair totalPair = OrpDateTimeUtils.MonthSplitPair.create(request.getStartTime(), request.getEndTime());
        CurrentYoyMomData<OnlineReportTrainDto> totalCurrentYoyMomData = getCurrentYoyMomTrainData(totalPair, trainList);

        DetailTrainAmountData trainInfoAmountResult =
                mapTrainAmountData(request, unionData, totalCurrentYoyMomData, headerAmount.get(ReportDetailHeaderEnum.TRAIN_INFO_AMOUNT_HEADER));
        // 票张明细
        DetailData trainInfoTicketResult =
                mapTrainTicketData(request, unionData, totalCurrentYoyMomData, headerTicket.get(ReportDetailHeaderEnum.TRAIN_INFO_TICKET_HEADER));

        detailAmount.setTrainAllDetail(trainAllDetail);
        detailAmount.setTrainAmountDetail(trainInfoAmountResult);
        detailTicket.setInfoDetail(trainInfoTicketResult);
        return new OnlineReportData(detailAmount, detailTicket);
    }

    /**
     * 火车整体报表
     */
    private DetailTrainData mapTrainAllData(OnlineDetailRequestDto request, List<OnlineReportTrainDto> list,
                                            List<HeaderKeyValMap> headerKeyValMaps, String lang) {
        // POS_JP 保留0位小数
        boolean isJP = StringUtils.equalsIgnoreCase(request.getBaseCondition().getPos(), OrpConstants.POS_JP);
        Map<String, OrpDateTimeUtils.MonthSplitPair> dateYoyMomRange = OrpDateTimeUtils.momSplit(request.getStartTime(), request.getEndTime());
        Map<String, ReprotTrainBodyData> amountReportMap = dateYoyMomRange.entrySet().stream()
                .map(entry -> {
                    CurrentYoyMomData<OnlineReportTrainDto> currentYoyMomData = getCurrentYoyMomTrainData(entry.getValue(), list);
                    currentYoyMomData.setMonth(entry.getKey());
                    return currentYoyMomData;
                })
                .map(data -> {
                    return mapTrainAllBodyData(StringUtils.EMPTY, data, isJP);
                })
                .collect(Collectors.toMap(ReprotTrainBodyData::getDimension, (p) -> p));

        OrpDateTimeUtils.MonthSplitPair totalPair = OrpDateTimeUtils.MonthSplitPair.create(request.getStartTime(), request.getEndTime());
        CurrentYoyMomData<OnlineReportTrainDto> totalCurrentYoyMomData = getCurrentYoyMomTrainData(totalPair, list);

        List<ReprotTrainBodyData> amountList = Lists.newArrayList();
        // dimension
        OrpDateTimeUtils.getMonthBetween(request.getStartTime(), request.getEndTime()).forEach(month -> {
            if (Objects.isNull(amountReportMap.get(month))) {
                amountList.add(new ReprotTrainBodyData(month, mapTrainEmptyData()));
            } else {
                amountList.add(amountReportMap.get(month));
            }
        });
        OrpCollectionUtils.sortAsc(amountList, ReprotTrainBodyData::getDimension);
        // 总计
        amountList
                .add(mapTrainAllBodyData(
                        SharkUtils.getHeaderVal(
                                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.totalV"), lang),
                        totalCurrentYoyMomData, isJP));
        return new DetailTrainData(headerKeyValMaps, amountList);
    }

    /**
     * 火车票-金额明细
     *
     * @param request
     * @param unionData
     * @param totalData
     * @return
     */
    private DetailTrainAmountData mapTrainAmountData(OnlineDetailRequestDto request,
                                                     Map<String, CurrentYoyMomData<OnlineReportTrainDto>> unionData,
                                                     CurrentYoyMomData<OnlineReportTrainDto> totalData,
                                                     List<HeaderKeyValMap> headerKeyValMaps) {
        // POS_JP 保留0位小数
        boolean isJP = StringUtils.equalsIgnoreCase(request.getBaseCondition().getPos(), OrpConstants.POS_JP);
        // 火车票-金额明细
        List<ReprotTrainInfoBodyData> trainAmountBodyDataList =
                mapTrainAmountBodyData(unionData, isJP);
        // 排序
        OrpCollectionUtils.sortAsc(trainAmountBodyDataList, ReprotTrainInfoBodyData::getDimension);
        // 总计
        String totalTitle = SharkUtils.getHeaderVal(
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.totalV"),
                request.getLang());
        trainAmountBodyDataList.add(mapTrainAmountTotalDetailData(totalTitle, totalData, isJP));
        return new DetailTrainAmountData(headerKeyValMaps, trainAmountBodyDataList);
    }

    /**
     * 计算汇总数据
     *
     * @param data
     * @return
     */
    private ReprotTrainInfoBodyData mapTrainAmountTotalDetailData(String totalTitle, CurrentYoyMomData<OnlineReportTrainDto> data, boolean isJP) {
        AtomicDouble totalV = new AtomicDouble();
        AtomicDouble totalYoy = new AtomicDouble();
        AtomicDouble totalMom = new AtomicDouble();
        // 迭代映射关系值

        // 当期
        List<OnlineReportTrainDto> currentTrainDataList = data.getCurrent();
        Optional.ofNullable(currentTrainDataList).orElse(Lists.newArrayList()).forEach(t -> {
            totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), t.getRealPay()));
        });
        // 同比
        List<OnlineReportTrainDto> yoyTrainDataList = data.getYoy();
        Optional.ofNullable(yoyTrainDataList).orElse(Lists.newArrayList()).forEach(t -> {
            totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), t.getRealPay()));
        });
        // 环比
        List<OnlineReportTrainDto> momTrainDataList = data.getMom();
        Optional.ofNullable(momTrainDataList).orElse(Lists.newArrayList()).forEach(t -> {
            totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getRealPay()));
        });
        // 精度,日本站金额取整
        int precision = isJP ? OrpConstants.ZERO : OrpConstants.TWO;
        TrainDataInfoDetailData reportDetailData = new TrainDataInfoDetailData();
        reportDetailData
                .setTotalV(OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());
        reportDetailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
        reportDetailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));
        return new ReprotTrainInfoBodyData(totalTitle, reportDetailData);
    }

    private DetailData mapTrainTicketData(OnlineDetailRequestDto request,
                                          Map<String, CurrentYoyMomData<OnlineReportTrainDto>> unionData,
                                          CurrentYoyMomData<OnlineReportTrainDto> totalData,
                                          List<HeaderKeyValMap> headerKeyValMaps) {
        // POS_JP 保留0位小数
        boolean isJP = StringUtils.equalsIgnoreCase(request.getBaseCondition().getPos(), OrpConstants.POS_JP);
        // 火车-票张明细/同比环比-数据
        List<ReprotBodyData> trainTicketBodyData =
                mapTrainTotalTicketData(unionData, isJP);

        OrpCollectionUtils.sortAsc(trainTicketBodyData, ReprotBodyData::getDimension);
        // 总计
        String totalTitle = SharkUtils.getHeaderVal(
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "ReportDetailHeaderEnum.totalV"),
                request.getLang());
        trainTicketBodyData.add(mapTrainTotalTicketData(totalTitle, totalData, isJP));
        return new DetailData(headerKeyValMaps, trainTicketBodyData);
    }

    private ReprotBodyData mapTrainTotalTicketData(String totalTitle, CurrentYoyMomData<OnlineReportTrainDto> totalData, boolean isJP) {
        AtomicDouble totalV = new AtomicDouble();
        AtomicDouble totalYoy = new AtomicDouble();
        AtomicDouble totalMom = new AtomicDouble();
        // 迭代映射关系值
        List<OnlineReportTrainDto> currentTrainList = totalData.getCurrent();
        Optional.ofNullable(currentTrainList).orElse(Lists.newArrayList()).forEach(t -> {
            totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), new BigDecimal(t.getQuantityV())));
        });

        List<OnlineReportTrainDto> yoyTrainList = totalData.getYoy();
        Optional.ofNullable(yoyTrainList).orElse(Lists.newArrayList()).forEach(t -> {
            totalYoy
                    .set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), new BigDecimal(t.getQuantityV())));
        });

        List<OnlineReportTrainDto> momTrainList = totalData.getMom();
        Optional.ofNullable(momTrainList).orElse(Lists.newArrayList()).forEach(t -> {
            totalMom
                    .set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), new BigDecimal(t.getQuantityV())));
        });

        ReportDataDetailData reportDetailData = new ReportDataDetailData();
        reportDetailData
                .setTotalV(OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), OrpConstants.ZERO).toString());
        reportDetailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
        reportDetailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));
        return new ReprotBodyData(totalTitle, reportDetailData);
    }

    /**
     * 补充金额明细/票张明细空数据
     *
     * @param headerAmount
     * @param headerTicket
     * @return
     */
    private OnlineReportData mapEmptyAmountTicketData(Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerAmount,
                                                      Map<ReportDetailHeaderEnum, List<HeaderKeyValMap>> headerTicket) {
        DetailAmountDto amountDto = new DetailAmountDto();
        DetailTicketDto ticketDto = new DetailTicketDto();
        // set header
        amountDto.setTrainAllDetail(
                new DetailTrainData(headerAmount.get(ReportDetailHeaderEnum.TRAIN_ALL_HEADER), Lists.newArrayList()));
        amountDto.setTrainAmountDetail(new DetailTrainAmountData(
                headerAmount.get(ReportDetailHeaderEnum.TRAIN_INFO_AMOUNT_HEADER), Lists.newArrayList()));
        ticketDto.setInfoDetail(
                new DetailData(headerTicket.get(ReportDetailHeaderEnum.TRAIN_INFO_TICKET_HEADER), Lists.newArrayList()));
        return new OnlineReportData(amountDto, ticketDto);
    }

    /**
     * 火车票-同环比
     *
     * @param data
     * @return
     */
    protected List<ReprotTrainInfoBodyData> mapTrainAmountBodyData(Map<String, CurrentYoyMomData<OnlineReportTrainDto>> data, boolean isJP) {
        List<ReprotTrainInfoBodyData> reportBodyDataList = Lists.newArrayList();
        // 精度,日本站金额取整
        int precision = isJP ? OrpConstants.ZERO : OrpConstants.TWO;
        // 迭代映射关系值
        data.forEach((k, v) -> {
            AtomicDouble totalV = new AtomicDouble();
            AtomicDouble totalYoy = new AtomicDouble();
            AtomicDouble totalMom = new AtomicDouble();
            // 当期
            List<OnlineReportTrainDto> currentTrainDataList = v.getCurrent();
            Optional.ofNullable(currentTrainDataList).orElse(Lists.newArrayList()).forEach(t -> {
                totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()), t.getRealPay()));
            });
            // 同比
            List<OnlineReportTrainDto> yoyTrainDataList = v.getYoy();
            Optional.ofNullable(yoyTrainDataList).orElse(Lists.newArrayList()).forEach(t -> {
                totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()), t.getRealPay()));
            });
            // 环比
            List<OnlineReportTrainDto> momTrainDataList = v.getMom();
            Optional.ofNullable(momTrainDataList).orElse(Lists.newArrayList()).forEach(t -> {
                totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()), t.getRealPay()));
            });

            TrainDataInfoDetailData reportDetailData = new TrainDataInfoDetailData();
            reportDetailData.setTotalV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), precision).toString());
            reportDetailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
            reportDetailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));

            reportBodyDataList.add(new ReprotTrainInfoBodyData(k, reportDetailData));
        });
        return reportBodyDataList;
    }

    /**
     * 火车票-票张明细
     *
     * @param unionData
     * @return
     */
    private List<ReprotBodyData> mapTrainTotalTicketData(Map<String, CurrentYoyMomData<OnlineReportTrainDto>> unionData, boolean isJP) {
        List<ReprotBodyData> reportBodyDataList = Lists.newArrayList();
        // 迭代映射关系值
        unionData.forEach((k, v) -> {
            AtomicDouble totalV = new AtomicDouble();
            AtomicDouble totalYoy = new AtomicDouble();
            AtomicDouble totalMom = new AtomicDouble();
            // 当期
            List<OnlineReportTrainDto> currentTrainList = v.getCurrent();
            Optional.ofNullable(currentTrainList).orElse(Lists.newArrayList()).forEach(t -> {
                totalV.set(OrpCommonUtils.addDouble(new BigDecimal(totalV.get()),
                        new BigDecimal(Optional.ofNullable(t.getQuantityV()).orElse(NumberUtils.LONG_ZERO))));
            });

            // 同比
            List<OnlineReportTrainDto> yoyTrainList = v.getYoy();
            Optional.ofNullable(yoyTrainList).orElse(Lists.newArrayList()).forEach(t -> {
                totalYoy.set(OrpCommonUtils.addDouble(new BigDecimal(totalYoy.get()),
                        new BigDecimal(Optional.ofNullable(t.getQuantityV()).orElse(NumberUtils.LONG_ZERO))));
            });
            // 环比
            List<OnlineReportTrainDto> momTrainList = v.getMom();
            Optional.ofNullable(momTrainList).orElse(Lists.newArrayList()).forEach(t -> {
                totalMom.set(OrpCommonUtils.addDouble(new BigDecimal(totalMom.get()),
                        new BigDecimal(Optional.ofNullable(t.getQuantityV()).orElse(NumberUtils.LONG_ZERO))));
            });

            ReportDataDetailData reportDetailData = new ReportDataDetailData();
            reportDetailData.setTotalV(
                    OrpCommonUtils.precisionConversion(new BigDecimal(totalV.get()), OrpConstants.ZERO).toString());
            reportDetailData.setTotalYoy(OrpCommonUtils.yoy(totalV.get(), totalYoy.get()));
            reportDetailData.setTotalMom(OrpCommonUtils.mom(totalV.get(), totalMom.get()));

            ReprotBodyData trainBodyData = new ReprotBodyData();
            trainBodyData.setDimension(k);
            trainBodyData.setDetailData(reportDetailData);
            reportBodyDataList.add(trainBodyData);
        });
        return reportBodyDataList;
    }

    /**
     * map 当期/同比/环比-数据
     *
     * @param splitPair
     * @param overViewList
     * @return
     */
    private CurrentYoyMomData<OnlineReportTrainDto> getCurrentYoyMomTrainData(OrpDateTimeUtils.MonthSplitPair splitPair,
                                                                              List<OnlineReportTrainDto> overViewList) {
        CurrentYoyMomData<OnlineReportTrainDto> currentYoyMomData = new CurrentYoyMomData<>();
        List<OnlineReportTrainDto> current = new ArrayList<>();
        List<OnlineReportTrainDto> yoy = new ArrayList<>();
        List<OnlineReportTrainDto> mom = new ArrayList<>();

        overViewList.forEach(t -> {
            if (OrpDateTimeUtils.dateInRange(splitPair.getStartDate(), splitPair.getEndDate(), t.getReportDate())) {
                current.add(t);
            }

            if (OrpDateTimeUtils.dateInRange(splitPair.getYoyStartDate(), splitPair.getYoyEndDate(), t.getReportDate())) {
                yoy.add(t);
            }

            if (OrpDateTimeUtils.dateInRange(splitPair.getMomStartDate(), splitPair.getMomEndDate(), t.getReportDate())) {
                mom.add(t);
            }
        });
        currentYoyMomData.setCurrent(current);
        currentYoyMomData.setYoy(yoy);
        currentYoyMomData.setMom(mom);
        return currentYoyMomData;
    }
}
