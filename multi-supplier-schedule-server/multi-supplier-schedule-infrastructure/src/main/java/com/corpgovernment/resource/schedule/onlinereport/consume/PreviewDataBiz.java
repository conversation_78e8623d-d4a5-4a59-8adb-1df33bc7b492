package com.corpgovernment.resource.schedule.onlinereport.consume;


import com.corpgovernment.resource.schedule.domain.onlinereport.consume.GenralConsume;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPreviewData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPreviewDataRequest;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportConsumeDao;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc 预览
 */
@Service
public class PreviewDataBiz {
    @Autowired
    private OnlineReportConsumeDao reportConsumeDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    
    public OnlineReportPreviewData aggreationGenralConsume(OnlineReportPreviewDataRequest request) throws Exception {
        OnlineReportPreviewData genralConsume = new OnlineReportPreviewData();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<GenralConsume> aggreationConsumeDtoList = (List<GenralConsume>) reportConsumeDao.aggreationWithConditionOverview(baseQueryConditionDto, GenralConsume.class);
        if (CollectionUtils.isNotEmpty(aggreationConsumeDtoList)) {
            GenralConsume genralAggreationConsumeDto = aggreationConsumeDtoList.get(0);
            // 机票
            genralConsume.setTotalFlightAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(genralAggreationConsumeDto.getTotalFlightAmount())));
            // 火车
            genralConsume.setTotalTrainAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(genralAggreationConsumeDto.getTotalTrainAmount())));
            // 酒店
            genralConsume.setTotalHotelAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(genralAggreationConsumeDto.getTotalHotelAmount())));
            // 用车
            genralConsume.setTotalCarAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(genralAggreationConsumeDto.getTotalCarAmount())));
            // 汽车
            genralConsume.setTotalBusAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(genralAggreationConsumeDto.getTotalBusAmount())));
            // 增值
            genralConsume.setTotalVasAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(genralAggreationConsumeDto.getTotalAddAmount())));
            // 总金额
            genralConsume.setTotalAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(genralAggreationConsumeDto.getTotalAmount())));
        }
        return genralConsume;
    }
}
