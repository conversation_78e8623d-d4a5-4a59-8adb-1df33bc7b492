package com.corpgovernment.resource.schedule.onlinereport.clickhouse.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
@Slf4j
public abstract class AbstractOnlineReportTopRcAnaylsisStarRocksDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "AbstractOnlineReportTopRcAnaylsisDao";

    private final static String COUNT_ALIAS = "countAll";

    protected final static String TREND_DIM_KEY = "sql";

    protected final static String TREND_GROUP_KEY = "groupField";

    private final static String[] RC_DEPT = {"dept1", "dept2", "dept3", "dept4", "dept5", "dept6", "dept7", "dept8", "dept9", "dept10",
            "cost_center1", "cost_center2", "cost_center3", "cost_center4", "cost_center5", "cost_center6"};


    private final static String[] UPPER_RC_DEPT = {"DEPT1", "DEPT2", "DEPT3", "DEPT4", "DEPT5", "DEPT6", "DEPT7", "DEPT8", "DEPT9", "DEPT10",
            "COST_CENTER1", "COST_CENTER2", "COST_CENTER3", "COST_CENTER4", "COST_CENTER5", "COST_CENTER6"};

    private static final String FUKK_JOIN_CORP_RESULT_FIELD =
            "CASE WHEN flight.corp_name != '' THEN flight.corp_name ELSE  CASE \n"
                    + "                WHEN hotel.corp_name != '' THEN hotel.corp_name\n"
                    + "                ELSE train.corp_name END END AS CORP";
    private static final String FUKK_JOIN_ACCOUNT_RESULT_FIELD =
            "CASE WHEN flight.account_name != '' THEN flight.account_name ELSE \n"
                    + "            CASE WHEN hotel.account_name != '' THEN hotel.account_name\n"
                    + "                ELSE train.account_name  END END AS ACCOUNT";

    private static final String FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD =
            "CASE WHEN flight.account_code != '' THEN flight.account_code ELSE \n"
                    + "            CASE WHEN hotel.account_code != '' THEN hotel.account_code\n"
                    + "                ELSE train.account_code  END END AS ACCOUNTCODE";
    private static final String FUKK_JOIN_UID_RESULT_FIELD =
            "CASE  WHEN flight.uid != '' THEN flight.uid  ELSE  CASE \n"
                    + "                WHEN  hotel.uid != '' THEN hotel.uid\n"
                    + "                ELSE train.uid END END AS UID";
    private static final String FUKK_JOIN_USER_NAME_RESULT_FIELD =
            "CASE WHEN flight.user_name != '' THEN flight.user_name ELSE  CASE \n"
                    + "                WHEN hotel.user_name != '' THEN hotel.user_name\n"
                    + "                ELSE train.user_name  END END AS NAME";
    private static final String FUKK_JOIN_DEPT_RESULT_FIELD =
            "CASE WHEN  flight.%s != '' THEN flight.%s ELSE  CASE WHEN hotel.%s != '' THEN hotel.%s ELSE \n"
                    + "                    CASE WHEN train.%s != '' THEN train.%s ELSE '%s'\n"
                    + "                    END  END  END AS %s";

    public abstract String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum,
                                    BaseQueryConditionDTO baseQueryConditionDto, Pager pager, String orderType, String user);

    protected abstract ClickHouseTable getTargetTable();

    public abstract String countSql(List<Object> parmList, ClickHouseTable clickHouseTable,
                                    AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, String orderType, String user);

    /**
     * 前5部门消费
     *
     * @param
     * @param user
     * @return
     * @throws Exception
     */
    public List<Map> topRcAnalysis(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto,
                                   Pager pager, String orderType, String user) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String querySql = querySql(parmList, analysisObjectEnum, baseQueryConditionDto, pager, orderType,user);

        // 查询clickhouse
        return queryBySql(querySql, parmList, (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return DbResultMapUtils.mapResultList(u);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "deptDetailAnalysis");
    }

    /**
     * 出行人
     * @param user （uid, user_name）
     * @param parmList
     * @return
     */
    protected String buildPreSqlUser(String user, List<Object> parmList){
        StringBuffer sqlBuffer = new StringBuffer();
        String val = StringUtils.trimToEmpty(user);
        if (StringUtils.isNotEmpty(val)) {
            String uid = val;
            String encryptedVal = DbResultMapUtils.sm4Encrypt(val);
            log.info("Building user query condition for user: {}, encrypted: {}", uid, encryptedVal);
            sqlBuffer.append(" and ( uid like ? or user_name like ? )");
            parmList.add("%" + uid + "%");
            parmList.add("%" + encryptedVal + "%");
        }
        return sqlBuffer.toString();
    }

    /**
     * 前5部门消费
     *
     * @param
     * @param user
     * @return
     * @throws Exception
     */
    public Integer topRcAnalysisCount(AnalysisObjectEnum analysisObjectEnum,
                                      BaseQueryConditionDTO baseQueryConditionDto, String orderType, String user) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String countSql = countSql(parmList, getTargetTable(), analysisObjectEnum, baseQueryConditionDto, orderType, user);
        // 查询clickhouse
        return queryBySql(countSql, OrpConstants.EMPTY, (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapIntResult(u, COUNT_ALIAS);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return OrpConstants.ZERO;
                }, Integer.class, "count");
    }

    protected PreparedStatement mapCommonRequest(List<Object> parmList, PreparedStatement statement) {
        int index = OrpConstants.ONE;
        try {
            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }

    protected abstract String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum);

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinCondition(AnalysisObjectEnum analysisObjectEnum) {
        if (ConfigUtils.getBoolean("custom_dim_open", false)) {
            return joinConditionCustom(analysisObjectEnum);
        } else {
            return joinConditionDefault(analysisObjectEnum);
        }
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionDefault(AnalysisObjectEnum analysisObjectEnum) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz.setResultFields("corp_name as CORP").setGroupFields(" corp_corporation, corp_name")
                        .setFullJoinResultFields(FUKK_JOIN_CORP_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name")
                        .setHotelFullJointrain(
                                "hotel.corp_corporation = train.corp_corporation and hotel.corp_name = train.corp_name");
                break;
            case ACCOUNT:
                biz = biz.setResultFields("corp_name as CORP, account_name as ACCOUNT")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_name")
                        .setFullJoinResultFields(FUKK_JOIN_CORP_RESULT_FIELD + ", " + FUKK_JOIN_ACCOUNT_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name "
                                        + "and flight.account_id = hotel.account_id and flight.account_name = hotel.account_name")
                        .setHotelFullJointrain(
                                "hotel.corp_corporation = train.corp_corporation and hotel.corp_name = train.corp_name "
                                        + "and hotel.account_id = train.account_id and hotel.account_name = train.account_name");
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("corp_name as CORP, account_code as ACCOUNTCODE")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_code")
                        .setFullJoinResultFields(FUKK_JOIN_CORP_RESULT_FIELD + ", " + FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name "
                                        + "and flight.account_id = hotel.account_id and flight.account_code = hotel.account_code")
                        .setHotelFullJointrain(
                                "hotel.corp_corporation = train.corp_corporation and hotel.corp_name = train.corp_name "
                                        + "and hotel.account_id = train.account_id and hotel.account_code = train.account_code");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase();
                break;
            case COSTCENTER1:
                analysisObject = "cost_center1";
                break;
            case COSTCENTER2:
                analysisObject = "cost_center2";
                break;
            case COSTCENTER3:
                analysisObject = "cost_center3";
                break;
            case COSTCENTER4:
                analysisObject = "cost_center4";
                break;
            case COSTCENTER5:
                analysisObject = "cost_center5";
                break;
            case COSTCENTER6:
                analysisObject = "cost_center6";
                break;
            case UID:
                biz = biz.setResultFields("uid as UID, user_name as NAME " + rcdeptAsDept()).setGroupFields(" uid, user_name" + rcUiddept())
                        .setFullJoinResultFields(FUKK_JOIN_UID_RESULT_FIELD + ", " + FUKK_JOIN_USER_NAME_RESULT_FIELD + rcSqlquerydept())
                        .setFlightFullJoinHotel("flight.uid = hotel.uid and flight.user_name = hotel.user_name " + rcFliandHtldept("flight", "hotel"))
                        .setHotelFullJointrain("hotel.uid = train.uid and hotel.user_name = train.user_name" + rcFliandHtldept("hotel", "train"));
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject)) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
            biz = biz
                    .setResultFields(
                            "corp_name as CORP, case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                                    + "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(" corp_corporation, corp_name ," + analysisObject)
                    .setFullJoinResultFields(FUKK_JOIN_CORP_RESULT_FIELD + ", "
                            + String.format(FUKK_JOIN_DEPT_RESULT_FIELD, analysisObject, analysisObject, analysisObject,
                            analysisObject, analysisObject, analysisObject, other, analysisObjectEnum))
                    .setFlightFullJoinHotel(
                            "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name and flight."
                                    + analysisObject + " = hotel." + analysisObject)
                    .setHotelFullJointrain(
                            "hotel.corp_corporation = train.corp_corporation and hotel.corp_name = train.corp_name and hotel."
                                    + analysisObject + " = train." + analysisObject);

        }
        return biz;
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionCustom(AnalysisObjectEnum analysisObjectEnum) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        String analysisObjectAlias = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz.setResultFields("corp_name as CORP").setGroupFields(" corp_corporation, corp_name")
                        .setFullJoinResultFields(FUKK_JOIN_CORP_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name")
                        .setHotelFullJointrain(
                                "hotel.corp_corporation = train.corp_corporation and hotel.corp_name = train.corp_name");
                break;
            case ACCOUNT:
                biz = biz.setResultFields("corp_name as CORP, account_name as ACCOUNT")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_name")
                        .setFullJoinResultFields(FUKK_JOIN_CORP_RESULT_FIELD + ", " + FUKK_JOIN_ACCOUNT_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name "
                                        + "and flight.account_id = hotel.account_id and flight.account_name = hotel.account_name")
                        .setHotelFullJointrain(
                                "hotel.corp_corporation = train.corp_corporation and hotel.corp_name = train.corp_name "
                                        + "and hotel.account_id = train.account_id and hotel.account_name = train.account_name");
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("corp_name as CORP, account_code as ACCOUNTCODE")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_code")
                        .setFullJoinResultFields(FUKK_JOIN_CORP_RESULT_FIELD + ", " + FUKK_JOIN_ACCOUNTCODE_RESULT_FIELD)
                        .setFlightFullJoinHotel(
                                "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name "
                                        + "and flight.account_id = hotel.account_id and flight.account_code = hotel.account_code")
                        .setHotelFullJointrain(
                                "hotel.corp_corporation = train.corp_corporation and hotel.corp_name = train.corp_name "
                                        + "and hotel.account_id = train.account_id and hotel.account_code = train.account_code");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase().concat("_custom");
                break;
            case COSTCENTER1:
                analysisObject = "costcenter1_custom";
                break;
            case COSTCENTER2:
                analysisObject = "costcenter2_custom";
                break;
            case COSTCENTER3:
                analysisObject = "costcenter3_custom";
                break;
            case COSTCENTER4:
                analysisObject = "costcenter4_custom";
                break;
            case COSTCENTER5:
                analysisObject = "costcenter5_custom";
                break;
            case COSTCENTER6:
                analysisObject = "costcenter6_custom";
                break;
            case UID:
                biz = biz.setResultFields("uid as UID, user_name as NAME " + rcdeptAsDept()).setGroupFields(" uid, user_name" + rcUiddept())
                        .setFullJoinResultFields(FUKK_JOIN_UID_RESULT_FIELD + ", " + FUKK_JOIN_USER_NAME_RESULT_FIELD + rcSqlquerydept())
                        .setFlightFullJoinHotel("flight.uid = hotel.uid and flight.user_name = hotel.user_name " + rcFliandHtldept("flight", "hotel"))
                        .setHotelFullJointrain("hotel.uid = train.uid and hotel.user_name = train.user_name" + rcFliandHtldept("hotel", "train"));
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject)) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
            biz = biz
                    .setResultFields(
                            "corp_name as CORP, case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                                    + "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(" corp_corporation, corp_name ," + analysisObject)
                    .setFullJoinResultFields(FUKK_JOIN_CORP_RESULT_FIELD + ", "
                            + String.format(FUKK_JOIN_DEPT_RESULT_FIELD, analysisObject, analysisObject, analysisObject,
                            analysisObject, analysisObject, analysisObject, other, analysisObjectEnum))
                    .setFlightFullJoinHotel(
                            "flight.corp_corporation = hotel.corp_corporation and flight.corp_name = hotel.corp_name and flight."
                                    + analysisObject + " = hotel." + analysisObject)
                    .setHotelFullJointrain(
                            "hotel.corp_corporation = train.corp_corporation and hotel.corp_name = train.corp_name and hotel."
                                    + analysisObject + " = train." + analysisObject);

        }
        return biz;
    }

    @Data
    @Accessors(chain = true)
    class JoinCondition {
        private String resultFields;

        private String groupFields;

        private String flightFullJoinHotel;

        private String hotelFullJointrain;

        private String fullJoinResultFields;
    }

    protected PreparedStatement mapCommonRequest(List<Object> parmList, PreparedStatement statement,
                                                 ClickHouseTable clickHouseTable) {
        int index = OrpConstants.ONE;
        try {
            // 分区
            String x = queryPartition(clickHouseTable);
            statement.setString(index++, x);

            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }

            parmList.add(0, x);
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }

    /**
     * 酒店类型查询条件
     *
     * @param hotelType
     * @return
     */
    protected String getHotelOrderTypeCondition(String hotelType) {
        StringBuilder stringBuilder = new StringBuilder(String.format(" and order_status = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus")));
        // 国内、港澳台
        if (StringUtils.equalsIgnoreCase(hotelType, "dom")) {
            stringBuilder.append(" and is_oversea in ('F','O') ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(hotelType, "inter")) {
            stringBuilder.append(" and is_oversea in ('T') ");
        }
        return stringBuilder.toString();
    }

    /**
     * 机票类型查询条件
     *
     * @param flightClass
     * @return
     */
    protected String getFlightClassCondition(String flightClass) {
        if (StringUtils.equalsIgnoreCase(flightClass, "dom")) {
            return " and flight_class = 'N' ";
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(flightClass, "inter")) {
            return " and flight_class = 'I' ";
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }

    /**
     * 获得趋势聚合字段
     *
     * @param dateDimensionEnum
     * @return
     */
    protected Map getGroupFieldBy(QueryReportAggDateDimensionEnum dateDimensionEnum) {
        Map result = new HashMap();
        switch (dateDimensionEnum) {
            case month:
                result.put(TREND_DIM_KEY, " firstday_of_month as date");
                result.put(TREND_GROUP_KEY, " firstday_of_month ");
                break;
            case quarter:
                result.put(TREND_DIM_KEY, " firstday_of_quarter as date");
                result.put(TREND_GROUP_KEY, " firstday_of_quarter ");
                break;
            case half:
                result.put(TREND_DIM_KEY, " firstday_of_halfyear as date");
                result.put(TREND_GROUP_KEY, " firstday_of_halfyear ");
                break;
            default:
                break;
        }
        return result;
    }

    public String rcUiddept() {
        StringBuilder stringBuilder = new StringBuilder();
        for (String str : RC_DEPT) {
            stringBuilder.append(", " + str);
        }
        return stringBuilder.toString();
    }

    public String rcSqlquerydept() {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < RC_DEPT.length; i++) {
            stringBuilder.append("," + "CASE  WHEN train." + RC_DEPT[i] + "!= '' THEN train." + RC_DEPT[i] + " ELSE  CASE WHEN ");
            stringBuilder.append(" flight." + RC_DEPT[i] + " !='' THEN flight." + RC_DEPT[i] + " ELSE hotel." + RC_DEPT[i]);
            stringBuilder.append(" END END AS " + UPPER_RC_DEPT[i]);
        }
        return stringBuilder.toString();
    }

    public String rcFliandHtldept(String s1, String s2) {
        StringBuilder stringBuilder = new StringBuilder();
        for (String str : RC_DEPT) {
            stringBuilder.append(" and " + s1 + "." + str + " = " + s2 + "." + str);
        }
        return stringBuilder.toString();
    }

    public String rcdeptAsDept() {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < RC_DEPT.length; i++) {
            stringBuilder.append(", " + RC_DEPT[i] + " AS " + UPPER_RC_DEPT[i]);
        }
        return stringBuilder.toString();
    }


}
