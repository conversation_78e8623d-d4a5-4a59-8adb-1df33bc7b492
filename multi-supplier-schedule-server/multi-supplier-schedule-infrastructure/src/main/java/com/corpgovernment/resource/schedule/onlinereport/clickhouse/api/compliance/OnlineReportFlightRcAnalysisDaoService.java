package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTimesDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.FlightRcTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @className OnlineReportFlightRcAnalysisDaoService
 * @date 2024/9/27
 */
public interface OnlineReportFlightRcAnalysisDaoService extends OnlineReportTopRcAnalysisDaoService {

    List<RcTimesDTO> aggreationRcView(BaseQueryConditionDTO requestDto, String flightClass, String rcTypeFiled) throws Exception;

    List<RcTimesDTO> aggreationRcViewCorpAndIndustry(String startTime, String endTime, String flightClass, String rcTypeFiled,
                                                     List<String> industryList, DataTypeEnum dataTypeEnum,
                                                     String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

    <T> List<T> aggreationRcViewReason(BaseQueryConditionDTO requestDto, Class<T> clazz, String flightClass)
            throws Exception;

    <T> List<T> aggreationRcViewReasonDetail(BaseQueryConditionDTO requestDto, Class<T> clazz,
                                             String flightClass, FlightRcTypeEnum flightRcEnum, String lang) throws Exception;

    List<RcTrendDTO> aggreationRcTrend(BaseQueryConditionDTO requestDto, String flightClass, QueryReportAggDateDimensionEnum dateDimensionEnum) throws Exception;

    List<RcTrendDTO> aggreationRcTrendCorpAndIndustry(String startTime, String endTime, String flightClass, QueryReportAggDateDimensionEnum dateDimensionEnum,
                                                      List<String> industryList, DataTypeEnum dataTypeEnum,
                                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

}
