package com.corpgovernment.resource.schedule.onlinereport.enums;

import com.corpgovernment.resource.schedule.onlinereport.dto.ClickHouseTablePartition;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.enums
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-03 19:17
 **/
public enum ClickHouseTable {

    /**
     * 在线报告概览表
     */
    ADM_INDEX_PRICE_SUMMARRY("adm_index_price_summarry_all", Boolean.TRUE),

    /**
     * 用车
     */
    ADM_INDEX_CAR_PRICE_DETAIL("adm_indexcar_price_detail_all", Boolean.TRUE),

    /**
     * 火车
     */
    ADM_INDEX_TRAIN_PRICE_DETAIL("adm_indextrain_price_detail_all", Boolean.TRUE),

    /**
     * 酒店
     */
    ADM_INDEX_HOTEL_PRICE_DETAIL("adm_indexhotel_price_detail_all", Boolean.TRUE),

    /**
     * 机票
     */
    ADM_INDEX_FLIGHT_PRICE_DETAIL("adm_indexflight_price_detail_all", Boolean.TRUE),

    /**
     * 一次行程
     */
    ADM_INDEX_ONE_TRIP_CORP("adm_index_one_trip_corp_all", Boolean.TRUE),

    /**
     * 火车明细，单状态(TD)
     */
    OLRPT_INDEXTRAINDOWNLOAD("olrpt_indextraindownload_all", Boolean.TRUE),

    /**
     * 火车明细，全状态(TD、C、N、TF、WP、WA、WT、PF)
     */
    OLRPT_INDEXTRAINDOWNLOAD_ALLORDER("olrpt_indextraindownload_allorder_all", Boolean.TRUE),

    /**
     * 酒店明细
     */
    OLRPT_INDEXHOTELDOWNLOAD("olrpt_indexhoteldownload_all", Boolean.TRUE),

    /**
     * 机票明细
     */
    OLRPT_INDEXFLIGHTDOWNLOAD("olrpt_indexflightdownload_all", Boolean.TRUE),

    /**
     * 汽车票
     */
    BUS_ORDERDETAIL("bus_order_detail_all", Boolean.TRUE),

    /**
     * 增值表明细
     */
    VASO_ORDERDETAIL("vaso_order_detail_all", Boolean.TRUE),

    /**
     * 火车-日本数据
     */
    ADM_INDEXTRAIN_PRICE_DETAIL_FOREIGN("adm_indextrain_price_detail_foreign_all", Boolean.TRUE),

    /**
     * 酒店-日本数据
     */
    ADM_INDEXHOTEL_PRICE_DETAIL_FOREIGN("adm_indexhotel_price_detail_foreign_all", Boolean.TRUE),

    /**
     * 机票-日本数据
     */
    ADM_INDEXFLIGHT_PRICE_DETAIL_FOREIGN("adm_indexflight_price_detail_foreign_all", Boolean.TRUE),

    /**
     * 概览-日本数据
     */
    ADM_INDEX_PRICE_SUMMARRY_FOREIGN("adm_index_price_summarry_foreign_all", Boolean.TRUE),

    /**
     * 酒店-日本数据-多币种
     */
    ADM_INDEXHOTEL_PRICE_DETAIL_FOREIGN_MULTI_CURRENCY("adm_indexhotel_price_detail_foreign_multicurrency_all", Boolean.TRUE),

    /**
     * 机票-日本数据-多币种
     */
    ADM_INDEXFLIGHT_PRICE_DETAIL_FOREIGN_MULTI_CURRENCY("adm_indexflight_price_detail_foreign_multicurrency_all", Boolean.TRUE),

    /**
     * 概览-日本数据-多币种
     */
    ADM_INDEX_PRICE_SUMMARRY_FOREIGN_MULTI_CURRENCY("adm_index_price_summarry_foreign_multicurrency_all", Boolean.TRUE),

    /**
     * 酒店优惠明细
     */
    ADM_HTL_ORD_PROMOTION("adm_htl_ord_promotion_all", Boolean.TRUE),

    /**
     * 国际火车票
     */
    ADM_INDEXTRAIN_INTL_DETAIL("adm_indextrain_intl_detail_all", Boolean.TRUE),

    /**
     * 个人足迹数据
     */
    ADM_INDEX_UID("adm_index_uid_all", Boolean.TRUE),

    /**
     * 个人足迹年度关键词
     */
    ADM_INDEX_UID_FOOTPRINT_KEYWORD("adm_index_uid_footprint_keyword_all", Boolean.TRUE),

    /**
     * 用车明细表
     */
    OLRPT_INDEXCARDOWNLOAD("olrpt_indexcardownload_all", Boolean.TRUE),

    /**
     * 风险订单酒店-提前离店
     */
    ADM_INDEXHOTEL_WASTE("adm_indexhotel_waste_v2_all", Boolean.TRUE),

    /**
     * 风险订单酒店-协同套现
     */
    ADM_INDEXHOTEL_RISKCONTROL_SCORES("adm_indexhotel_risk_control_order_post_scores_all", Boolean.TRUE),

    /**
     * 风险订单 机票私退私改操作表
     */
    OLRPT_RISK_FLIGHTREFUND_OPERATOR_LOG("olrpt_riskflightrefund_operator_log_all", Boolean.FALSE),

    /**
     * 风险订单 酒店操作表
     */
    OLRPT_RISK_HOTEL_OPERATOR_LOG("olrpt_riskhotel_operator_log_all", Boolean.FALSE),

    /**
     * 风险订单 用车操作表
     */
    OLRPT_RISK_CAR_OPERATOR_LOG("olrpt_riskcar_operator_log_all", Boolean.FALSE),

    /**
     * 风险订单 用车表
     */
    OLRPT_RISK_CAR("adm_index_car_monitor_all", Boolean.FALSE),

    /**
     * 事件表
     */
    ADM_INDEX_EVENT_ALL("adm_index_event_all", Boolean.TRUE),

    /**
     * 一次行程
     */
    ADM_INDEX_ONE_TRIP_FULL_TRIP_ID_GENERATE_ALL("adm_index_one_trip_full_trip_id_generate_all", Boolean.TRUE),


    /******************************以下为预订口径表********************************************/

    /**
     * 在线报告概览表
     */
    ADM_INDEX_PRICE_SUMMARRY_ODT("adm_index_price_summarry_odt_all", Boolean.TRUE),

    /**
     * 火车
     */
    ADM_INDEX_TRAIN_PRICE_DETAIL_ODT("adm_indextrain_price_detail_odt_all", Boolean.TRUE),

    /**
     * 用车(成交口径和预订口径是一样的所以是同一张表)
     */
    ADM_INDEX_CAR_PRICE_DETAIL_ODT("adm_indexcar_price_detail_all", Boolean.TRUE),

    /**
     * 酒店
     */
    ADM_INDEX_HOTEL_PRICE_DETAIL_ODT("adm_indexhotel_price_detail_odt_all", Boolean.TRUE),

    /**
     * 机票
     */
    ADM_INDEX_FLIGHT_PRICE_DETAIL_ODT("adm_indexflight_price_detail_odt_all", Boolean.TRUE),

    /**
     * 差旅足迹
     */
    ADM_INDEX_ONE_TRIP_FULL_TRIP_ID_GENERATE("adm_index_one_trip_full_trip_id_generate", Boolean.TRUE),


    /**
     * 多语言地理信息(酒店)
     */
    CORP_GEO_LOCATION_LANGUAGE("corp_geo_location_language_all", Boolean.TRUE),

    /**
     * mice信息
     */
    ADM_CORP_MICE_TOUR_CITY("adm_corp_mice_tour_city_all", Boolean.TRUE),

    /**
     * 城市维度
     */
    DIMCORP_PUB_CITY("dimcorp_pub_city_all", Boolean.TRUE),

    /**
     * 行业排名
     */
    ADM_INDEX_INDUSTRY("adm_index_industry_all", Boolean.TRUE),

    /**
     * 热门城市排名
     */
    ADM_INDEX_INDUSTRY_CITY("adm_index_industry_city_all", Boolean.TRUE),

    /**
     * 热门航线排名
     */
    ADM_INDEX_INDUSTRY_DCITYACITY("adm_index_industry_dcityacity_all", Boolean.TRUE),

    /**
     * 开通用户
     */
    ADM_INDEX_INDUSTRY_UIDS("adm_index_industry_uids_all", Boolean.TRUE),

    /**
     * 多语言地理信息(机票)
     */
    DIMCORP_FLT_CITYMULTILANG("dimcorp_flt_citymultilang_all", Boolean.TRUE),

    /**
     * 机场信息
     */
    DIMCORP_FLT_MULTI_LANGUAGE_AIRPORT("dimcorp_flt_multi_language_airport_all", Boolean.TRUE),

    /**
     * 航线
     */
    DIMCORP_FLT_MULTI_LANGUAGE_AIRLINE("dimcorp_flt_multi_language_airline_all", Boolean.TRUE),

    /**
     * 酒店品牌
     */
    DIMCORP_HTL_BRANDMULTILANG("dimcorp_htl_brandmultilang_all", Boolean.TRUE),

    /**
     * 酒店集团
     */
    DIMCORP_HTL_MGRGROUPMULTILANG("dimcorp_htl_mgrgroupmultilang_all", Boolean.TRUE),

    /**
     * 酒店房型
     */
    DIMCORP_HTL_ROOMMULTILANG("dimcorp_htl_roommultilang_all", Boolean.TRUE),

    /**
     * 酒店名称
     */
    DIMCORP_HTL_HTLINFOMULTILANG("dimcorp_htl_htlinfomultilang_all", Boolean.TRUE),


    /**
     * 机票-蓝色空间
     */
    OLRPT_INDEXFLIGHTDOWNLOAD_FOREIGN("olrpt_indexflightdownload_foreign_all", Boolean.TRUE),

    /**
     * 酒店-日本数据
     */
    OLRPT_INDEXHOTELDOWNLOAD_FOREIGN("olrpt_indexhoteldownload_foreign_all", Boolean.TRUE),

    /**
     * 火车-日本数据
     */
    OLRPT_INDEXTRAINDOWNLOAD_FOREIGN("olrpt_indextraindownload_foreign_all", Boolean.TRUE),

    /**
     * 机票-蓝色空间-多币种
     */
    OLRPT_INDEXFLIGHTDOWNLOAD_FOREIGN_MULTI_CURRENCY("olrpt_indexflightdownload_foreign_multicurrency_all", Boolean.TRUE),

    /**
     * 酒店-日本数据-多币种
     */
    OLRPT_INDEXHOTELDOWNLOAD_FOREIGN_MULTI_CURRENCY("olrpt_indexhoteldownload_foreign_multicurrency_all", Boolean.TRUE),


    /**
     * 酒店城市均价预测数据
     */
    ADM_CORP_HTL_CITY_PREDICT("adm_corp_htl_city_predict_all", Boolean.TRUE),

    /**
     * 公司维度--国内国际大盘酒店历史单间夜均价统计及未来3月价格预测
     */
    ADM_CORP_TOTAL_HTL_PRICE_PREDICT("adm_corp_total_htl_price_predict_all", Boolean.TRUE),

    /**
     * 机票航线均价预测数据
     */
    ADM_CORP_FLT_LINE_PREDICT("adm_corp_flt_line_predict_all", Boolean.TRUE),
    /**
     * 酒店夜审数据
     */
    ADM_INDEXHOTEL_ORDER_AUDIT("adm_indexhotel_order_audit_all", Boolean.TRUE),


    /**
     * 公司维度--国内国际大盘历史均价统计及未来3月价格预测
     */
    ADM_CORP_TOTAL_FLT_PRICE_PREDICT("adm_corp_total_flt_price_predict_all", Boolean.TRUE),

    /**
     * 各公司各职级近半年预订过的城市的新差标结果
     */
    ADM_FINAL_HTL_STD_DATA_ALL("adm_final_htl_std_data_all", Boolean.TRUE),

    /**
     * 碳排
     */
    ADM_INDEX_PRICE_SUMMARRY_CARBON("adm_index_price_summarry_carbon_all", Boolean.TRUE),

    /**
     * 碳排(碳排按出行时间统计的表)
     */
    ADM_INDEX_PRICE_SUMMARRY_CARBON_DTIME("adm_index_price_summarry_carbon_dtime_all", Boolean.TRUE),

    /**
     * 机票私退
     */
    OLRPT_INDEXFLIGHTDOWNLOAD_PRIVATEREFUND("olrpt_indexflightdownload_privaterefund_all", Boolean.TRUE),

    DIMCORP_CORPORATION("dimcorp_corporation_all", Boolean.TRUE),

    /**
     * 结算账单数据
     */
    STL_PLTF_ACCCHECK_AMOUNT("cdm_stl_pltf_acccheck_amount_statistics_day_all", Boolean.TRUE),

    /**
     * 程心贝数据
     */
    CORP_WELFARE_STOCK("corp_welfare_stock_all", Boolean.TRUE),


    /**
     * 差标计算器
     */
    HTL_STD_CALCULATOR_DATA("adm_htl_new_std_result_v5_all", Boolean.TRUE),

    /**
     * 差标计算器-酒店消费趋势
     */
    HTL_STD_CALCULATOR_TREND_DATA("adm_corp_rank_price_trend_all", Boolean.TRUE),
    /**
     * 机票协议明细
     */
    FLT_AGREEMENT_DETAIL("dimcorp_flt_agreement_all", Boolean.TRUE),
    ADM_INDEX_PRICE_SUMMARY_CARBON_ODT("adm_index_price_summarry_carbon_odt_all", Boolean.TRUE),
    /**
     * 设置指标展示
     */
    RISK_ORDER_DETAIL_COLUMN("riskorderdetailcolumn",Boolean.FALSE),
    ;


    private String table;
    private Boolean needPartition;
    /**
     * ck 表分区
     */
    public static Map<String, List<ClickHouseTablePartition>> tablePartitionMap = Maps.newHashMap();

    public static ClickHouseTable getTable(String tableName) {
        ClickHouseTable[] v = ClickHouseTable.values();
        for (ClickHouseTable table : v) {
            if (table.getTable().equalsIgnoreCase(tableName)) {
                return table;
            }
        }
        return null;
    }

    ClickHouseTable(String table, Boolean needPartition) {
        this.table = table;
        this.needPartition = needPartition;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public Boolean getNeedPartition() {
        return needPartition;
    }

    public void setNeedPartition(Boolean needPartition) {
        this.needPartition = needPartition;
    }
}
