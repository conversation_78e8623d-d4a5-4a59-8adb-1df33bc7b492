package com.corpgovernment.resource.schedule.onlinereport.clickhouse.supplier;

import cn.hutool.crypto.SmUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.CostCenterAndDepartSearchType;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.ctrip.corp.obt.generic.utils.EncryptUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 * @date 2022/6/20 13:08
 * @Desc
 */
@Service
@Slf4j
public abstract class AbstractSupplierMonitorDao extends AbstractCommonDao {

    private static final String LOG_TITLE = "AbstractSupplierMonitorDao";

    private final static String COUNT_ALIAS = "countAll";

    public abstract String agreementViewSql(List<Object> parmList, BaseQueryConditionDTO baseQueryConditionDto, String productType, boolean isBookCaliber);

    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> agreementView(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String querySql = agreementViewSql(parmList, requestDto, productType, isBookCaliber);
        // 查询clickhouse
        return queryBySql(querySql, parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "agreementView");
    }

    public abstract String agreementMomAndYoySql(List<Object> parmList, BaseQueryConditionDTO baseQueryConditionDto, String productType, boolean isBookCaliber);

    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> agreementMomAndYoy(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String querySql = agreementMomAndYoySql(parmList, requestDto, productType, isBookCaliber);
        // 查询clickhouse
        return queryBySql(querySql, parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "agreementView");
    }

    public abstract String agreementAggSql(List<Object> parmList, BaseQueryConditionDTO baseQueryConditionDto, String productType, boolean isBookCaliber);

    /**
     * 协议概览
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> agreementAgg(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String querySql = agreementAggSql(parmList, requestDto, productType, isBookCaliber);
        // 查询clickhouse
        return queryBySql(querySql, parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "agreementView");
    }

    /**
     * 协议消费明细
     *
     * @param requestDto
     * @param productType
     * @return
     * @throws Exception
     */
    public List<Map> agreementDetail(BaseQueryConditionDTO requestDto, String productType, String lang, boolean needGroup) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String querySql = agreementDetailSql(parmList, requestDto, productType, isBookCaliber, lang, needGroup);
        // 查询clickhouse
        return queryBySql(querySql, parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return DbResultMapUtils.mapResultList(u);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "agreementDetail");
    }

    /**
     * 协议消费明细
     * @param requestDto
     * @param lang
     * @param needGroup
     * @param clazz
     * @return
     * @param <T>
     * @throws Exception
     */
    public <T> List<T> agreementDetail(BaseQueryConditionDTO requestDto, String lang, boolean needGroup, Class<T> clazz) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String querySql = agreementDetailSql(parmList, requestDto, requestDto.getProductType(), isBookCaliber, lang, needGroup);
        // 查询clickhouse
        return commonList(clazz,querySql, parmList);
    }

    public abstract String agreementDetailSql(List<Object> parmList, BaseQueryConditionDTO baseQueryConditionDto, String productType, boolean isBookCaliber
            , String lang, boolean needGroup);

    /**
     * 协议消费明细
     *
     * @param requestDto
     * @param productType
     * @return
     * @throws Exception
     */
    public List<Map> agreementDeptDetail(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO requestDto,
                                         Pager pager, String productType, String user) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String querySql = agreementDeptDetailSql(parmList, analysisObjectEnum, requestDto, pager, productType, isBookCaliber, user);
        // 查询clickhouse
        return queryBySql(querySql, parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return DbResultMapUtils.mapResultList(u);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "agreementDeptDetail");
    }

    public abstract String agreementDeptDetailSql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO requestDto,
                                                  Pager pager, String productType, boolean isBookCaliber, String user);

    /**
     * 协议消费明细
     *
     * @param requestDto
     * @param productType
     * @return
     * @throws Exception
     */
    public Integer agreementDeptDetailCount(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO requestDto, String productType, String user) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String querySql = agreementDeptDetailCountSql(parmList, analysisObjectEnum, requestDto, productType, isBookCaliber, user);
        // 查询clickhouse
        return queryBySql(querySql, parmList, this::mapCommonRequest, (u, d) -> {
                    try {
                        return mapIntResult(u, COUNT_ALIAS);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return OrpConstants.ZERO;
                }, Integer.class, "agreementDeptDetailCount");
    }

    public abstract String agreementDeptDetailCountSql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto,
                                                       String productType, boolean isBookCaliber, String user);


    /**
     * 构建list查询条件
     *
     * @param list
     * @param parmList
     * @return
     */
    public static String buildListConditionPreSql(List<String> list, List<Object> parmList, String conditionFiled) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(list)) {
            sqlBuffer.append(" and " + conditionFiled + " in (");
            for (int i = 0; i < list.size(); i++) {
                sqlBuffer.append(" ? ");
                if (i != list.size() - 1) {
                    sqlBuffer.append(OrpConstants.COMMA);
                }
                parmList.add(list.get(i));
            }
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }

    /**
     * 酒店类型查询条件
     *
     * @param flightClass
     * @return
     */
    protected String getHotelCondition(String flightClass) {
        StringBuilder stringBuilder = new StringBuilder(String.format(" and order_status = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus")));
        //  国内及港澳台
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(flightClass, "dom")) {
            stringBuilder.append(" and is_oversea IN ('F','O')  ");
        }
        // 海外
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(flightClass, "inter")) {
            stringBuilder.append(" and is_oversea IN ('T')  ");
        }
        return stringBuilder.toString();
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinCondition(AnalysisObjectEnum analysisObjectEnum) {
        if (ConfigUtils.getBoolean("custom_dim_open", false)){
            return joinConditionCustom(analysisObjectEnum);
        }else {
            return joinConditionDefault(analysisObjectEnum);
        }
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionDefault(AnalysisObjectEnum analysisObjectEnum) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz = biz.setResultFields("corp_name as CORP")
                        .setGroupFields(" corp_corporation, corp_name");
                break;
            case ACCOUNT:
                biz = biz.setResultFields("corp_name as CORP, account_name as ACCOUNT")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_name");
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("corp_name as CORP, account_code as ACCOUNTCODE")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_code");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase();
                break;
            case COSTCENTER1:
                analysisObject = "cost_center1";
                break;
            case COSTCENTER2:
                analysisObject = "cost_center2";
                break;
            case COSTCENTER3:
                analysisObject = "cost_center3";
                break;
            case COSTCENTER4:
                analysisObject = "cost_center4";
                break;
            case COSTCENTER5:
                analysisObject = "cost_center5";
                break;
            case COSTCENTER6:
                analysisObject = "cost_center6";
                break;
            case UID:
                biz = biz.setResultFields("uid as UID, user_name as NAME, dept1, dept2, dept3, dept4, dept5, dept6, dept7, dept8, dept9, dept10" +
                                ", cost_center1, cost_center2, cost_center3, cost_center4, cost_center5, cost_center6")
                        .setGroupFields(" uid, user_name, dept1, dept2, dept3, dept4, dept5, dept6, dept7, dept8, dept9, dept10" +
                                ", cost_center1, cost_center2, cost_center3, cost_center4, cost_center5, cost_center6");
                break;
            default:
                break;
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(analysisObject)) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
            biz = biz.setResultFields(
                            "corp_name as CORP, case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                                    + "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(" corp_corporation, corp_name ," + analysisObject);

        }
        return biz;
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionCustom(AnalysisObjectEnum analysisObjectEnum) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        String analysisObjectAlias = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz = biz.setResultFields("corp_name as CORP")
                        .setGroupFields(" corp_corporation, corp_name");
                break;
            case ACCOUNT:
                biz = biz.setResultFields("corp_name as CORP, account_name as ACCOUNT")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_name");
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("corp_name as CORP, account_code as ACCOUNTCODE")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_code");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase().concat("_custom");
                break;
            case COSTCENTER1:
                analysisObject = "costcenter1_custom";
                break;
            case COSTCENTER2:
                analysisObject = "costcenter2_custom";
                break;
            case COSTCENTER3:
                analysisObject = "costcenter3_custom";
                break;
            case COSTCENTER4:
                analysisObject = "costcenter4_custom";
                break;
            case COSTCENTER5:
                analysisObject = "costcenter5_custom";
                break;
            case COSTCENTER6:
                analysisObject = "costcenter6_custom";
                break;
            case UID:
                biz = biz.setResultFields("uid as UID, user_name as NAME, " +
                                "dept1_custom as dept1, dept2_custom as dept2, dept3_custom as dept3, dept4_custom as dept4, dept5_custom as dept5, " +
                                "dept6_custom as dept6, dept7_custom as dept7, dept8_custom as dept8, dept9_custom as dept9, dept10_custom as dept10, " +
                                "costcenter1_custom as cost_center1, costcenter2_custom as cost_center2, costcenter3_custom as cost_center3, " +
                                "costcenter4_custom as cost_center4, costcenter5_custom as cost_center5, costcenter6_custom as cost_center6 ")
                        .setGroupFields(" uid, user_name, " +
                                "dept1_custom, dept2_custom, dept3_custom, dept4_custom, dept5_custom, dept6_custom, dept7_custom, dept8_custom, dept9_custom, dept10_custom, " +
                                "costcenter1_custom, costcenter2_custom, costcenter3_custom, costcenter4_custom, costcenter5_custom, costcenter6_custom ");
                break;
            default:
                break;
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(analysisObject)) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
            biz = biz.setResultFields(
                            "corp_name as CORP, case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                                    + "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(" corp_corporation, corp_name ," + analysisObjectEnum.toString());

        }
        return biz;
    }

    /**
     * 预订人
     * @param user （uid, user_name）
     * @param parmList
     * @return
     */
    protected String buildPreSqlUser(String user, List<Object> parmList){
        StringBuffer sqlBuffer = new StringBuffer();
        String val = org.apache.commons.lang3.StringUtils.trimToEmpty(user);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(val)) {
            String uid = val;
            val = DbResultMapUtils.sm4Encrypt(val);
            sqlBuffer.append(" and ( uid like ? or user_name like ? )");
            parmList.add("%" + uid + "%");
            parmList.add("%" + val + "%");
        }
        return sqlBuffer.toString();
    }

    public List<String> queryCostCenterOrDepartmentOrCorpId(CostCenterAndDepartSearchType searchType, ClickHouseTable clickHouseTable) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append("select ");
        sqlBuilder.append(buildSelect(searchType));
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        parmList.add(queryPartition(clickHouseTable));
        return queryBySql(sqlBuilder.toString(), parmList, this::mapCommonRequest, (u, d) -> {
            try {
                return DbResultMapUtils.mapStrResultList(u, "name");
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, Map.class, "queryCostCenterOrDepartmentOrCorpId");
    }
    public String buildSelect(CostCenterAndDepartSearchType searchType) throws BusinessException {
        if (CostCenterAndDepartSearchType.CORP.equals(searchType)){
            return "distinct corp_name as name";
        }
        if (CostCenterAndDepartSearchType.DEPARTMENT.equals(searchType)){
            return "distinct dept1 as name";
        }
        if (CostCenterAndDepartSearchType.COSTCENTER.equals(searchType)){
            return "distinct cost_center1 as name";
        }
        throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), " searchType error ");

    }

    @Data
    @Accessors(chain = true)
    class JoinCondition {
        private String resultFields;

        private String groupFields;
    }
}
