package com.corpgovernment.resource.schedule.onlinereport.clickhouse.saveanalysis;


import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportOverviewTrendDao;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportPotentialSaveTrendDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.List;
import java.util.Map;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/**
 * <AUTHOR>
 * @date ：Created in 2021/11/10 10:26
 * @description：
 * @modified By：
 * @version: $
 */
@Repository
@Slf4j
public class OnlineReportPotentialSaveTrendStarRocksDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "OnlineReportPotentialSaveTrendDao";

    /**
     * 查询 在线报告概况-潜在节省趋势机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryFlightTrend(OnlineTrendRequestDto request) throws Exception {
        String trendSql = buildFlightTrendSql(request);
        return queryBySql(trendSql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportPotentialSaveTrendDTO.class, "queryOnlineReportPotentialSaveFlightTrend");
    }

    /**
     * 查询 在线报告概况-潜在节省趋势酒店
     *
     * @param request
     * @return
     * @throws Exception
     */
    public <T> List<T> queryHotelTrend(OnlineTrendRequestDto request) throws Exception {
        String trendSql = buildHotelTrendSql(request);
        return queryBySql(trendSql, request, this::mapRequest, (u, d) -> {
            try {
                return mapResultList(u, d);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, OnlineReportPotentialSaveTrendDTO.class, "queryOnlineReportPotentialSaveHotelTrend");
    }

    public String buildFlightTrendSql(OnlineTrendRequestDto requestDto) {
        Map<String, String> extParams = requestDto.getExtParams();
        String dim = extParams.getOrDefault("dim", "''");
        return conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getStartTime(),
                requestDto.getEndTime(),
                flightPotentialTrend(requestDto.getProductType(), requestDto.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                requestDto.getBaseQueryCondition());
    }

    public String buildHotelTrendSql(OnlineTrendRequestDto requestDto) {
        Map<String, String> extParams = requestDto.getExtParams();
        String dim = extParams.getOrDefault("dim", "''");
        return conditionWrapAsSubSql(dim, requestDto.getDateDimension(), requestDto.getStartTime(),
                requestDto.getEndTime(),
                hotelPotentialTrend(requestDto.getProductType(), requestDto.getBaseQueryCondition().getStatisticalCaliber()).toString(),
                requestDto.getBaseQueryCondition());
    }

    public StringBuilder flightPotentialTrend(String productType, String statisticalCaliber) {
        // 产线 - 月/季/半年 - 日期
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {group_date} as date, ");
        stringBuilder.append("SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as flightRcPotentialSave, ");
        stringBuilder.append("SUM(coalesce(change_fee, 0) + coalesce(rebook_price_differential, 0) + coalesce(rebook_service_fee, 0) " +
                "+ if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0))" +
                "+ coalesce(rebook_behind_service_fee, 0)) as flightRebookPotentialSave, ");
        stringBuilder.append("SUM(coalesce(refund_fee, 0) + coalesce(refund_service_fee, 0) + coalesce(refund_behind_service_fee, 0)) as flightRefundPotentialSave, ");
        stringBuilder.append("SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) + " +
                " SUM(coalesce(change_fee, 0) + coalesce(rebook_price_differential, 0) + coalesce(rebook_service_fee, 0) " +
                "+ if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0))" +
                "+ coalesce(rebook_behind_service_fee, 0)) + " +
                " SUM(coalesce(refund_fee, 0) + coalesce(refund_service_fee, 0) + coalesce(refund_behind_service_fee, 0)) as totalPotentialSave, ");

        // 退改张数 及 超标次数 及次数占比
        stringBuilder.append("COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END) as flightRcTimes, ");
        stringBuilder.append("SUM(case when is_rebook='T' then quantity else 0 end) as flightRebookTimes, ");
        stringBuilder.append("SUM(refundtkt) as flightRefundTimes, ");

        stringBuilder.append("if((COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END)) != 0, " +
                " (COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END)) * 100 / (COUNT(DISTINCT CASE WHEN is_refund = 'F' " +
                " THEN order_id END)), " +
                " 0) as flightRcTimesRate, ");
        stringBuilder.append("if(SUM(ordertkt) != 0, " +
                " SUM(case when is_rebook='T' then quantity else 0 end) * 100 / SUM(ordertkt), " +
                " 0) as flightRebookTimesRate, ");
        stringBuilder.append("if(SUM(ordertkt) != 0, " +
                " SUM(refundtkt) * 100 / SUM(ordertkt), " +
                " 0) as flightRefundTimesRate, ");

        stringBuilder.append("COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END) + " +
                " SUM(case when is_rebook='T' then quantity else 0 end) + " +
                " SUM(refundtkt) as totalPotentialTimes ");

        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        stringBuilder.append(SaveCondition.getFlightClassCondition(productType));
        stringBuilder.append(" and audited <> 'F' ");
        stringBuilder.append(" group by {group_date} ");
        return stringBuilder;
    }

    public StringBuilder hotelPotentialTrend(String productType, String statisticalCaliber) {
        // 产线 - 月/季/半年 - 日期
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select {group_date} as date, ");
        stringBuilder.append("SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) hotelRcPotentialSave, ");
        stringBuilder.append("SUM(coalesce(cancel_esti_save_amount, 0)) hotelCancelPotentialSave, ");
        stringBuilder.append("SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) +" +
                " SUM(coalesce(cancel_esti_save_amount, 0)) totalPotentialSave, ");

        stringBuilder.append("(COUNT(DISTINCT CASE " +
                "        WHEN is_mix_payment = 'F' " +
                "        AND dead_price_onenight > 0 " +
                "        AND (coalesce(reason_code, '') <> '' " +
                "            OR coalesce(min_price_rc, '') <> '') " +
                "        AND is_refund = 'F' THEN order_id " +
                "    END)) as hotelRcPotentialTimes, ");
        stringBuilder.append("SUM(coalesce(rfd_quantity, 0)) as hotelCancelPotentialTimes, ");
        stringBuilder.append("if( (COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END)) != 0, " +
                " (COUNT(DISTINCT CASE " +
                "        WHEN is_mix_payment = 'F'" +
                "        AND dead_price_onenight > 0" +
                "        AND (coalesce(reason_code, '') <> ''" +
                "            OR coalesce(min_price_rc, '') <> '')" +
                "        AND is_refund = 'F' THEN order_id" +
                "    END)) * 100 / (COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id END)), " +
                " 0) as hotelRcPotentialTimesRate, ");
        stringBuilder.append("if( SUM(coalesce(quantity, 0)) != 0, " +
                " SUM(coalesce(rfd_quantity, 0)) * 100 / SUM(coalesce(quantity, 0)), " +
                " 0) as hotelCancelPotentialTimesRate, ");
        stringBuilder.append("SUM(coalesce(rfd_quantity, 0)) + (COUNT(DISTINCT CASE " +
                "        WHEN is_mix_payment = 'F'" +
                "        AND dead_price_onenight > 0" +
                "        AND (coalesce(reason_code, '') <> ''" +
                "            OR coalesce(min_price_rc, '') <> '')" +
                "        AND is_refund = 'F' THEN order_id" +
                "    END)) as totalPotentialTimes ");

        stringBuilder.append("from ");
        stringBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        stringBuilder.append(" where d = '").append(partition).append("' and ");
        // 口径切换
        if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    ORDERDT, ORDERDT)
            );
        } else {
            stringBuilder.append(String.format("%s >= '{start}' and %s <= '{end}' and ({scope_condition})  ",
                    REPORT_DATE, REPORT_DATE)
            );
        }
        stringBuilder.append(SaveCondition.getHotelCondition(productType));
        stringBuilder.append(" group by {group_date} ");
        return stringBuilder;
    }

    private String conditionWrapAsSubSql(String dim, String dateDimension, String start, String end, String sqlTemplate,
                                         BaseQueryConditionDTO baseQueryConditionDTO) {
        String group_date = "";
        String statisticalCaliber = baseQueryConditionDTO.getStatisticalCaliber();
        boolean isBookingCaliber = StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        switch (dateDimension) {
            case "month":
                group_date = isBookingCaliber ? "firstday_of_month_odt" : "firstday_of_month";
                break;
            case "half":
                group_date = isBookingCaliber ? "firstday_of_halfyear_odt" : "firstday_of_halfyear";
                break;
            case "quarter":
                group_date = isBookingCaliber ? "firstday_of_quarter_odt" : "firstday_of_quarter";
                break;
            case "day":
                group_date = isBookingCaliber ? ORDERDT : REPORT_DATE;
                break;
            default:
                break;
        }
        String scope_condition = OnlineReportOverviewTrendDao.buildScopeFilter(baseQueryConditionDTO);
        sqlTemplate = sqlTemplate.replace("{group_date}", group_date).replace("{group_dim}", dim)
                .replace("{scope_condition}", scope_condition)
                .replace("{start}", start)
                .replace("{end}", end);
        return sqlTemplate;
    }

    private PreparedStatement mapRequest(OnlineTrendRequestDto requestDto, PreparedStatement statement) {
        return statement;
    }
}
