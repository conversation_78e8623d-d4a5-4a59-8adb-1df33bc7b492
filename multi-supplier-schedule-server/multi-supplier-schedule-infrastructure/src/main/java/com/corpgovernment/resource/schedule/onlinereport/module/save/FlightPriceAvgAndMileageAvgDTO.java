package com.corpgovernment.resource.schedule.onlinereport.module.save;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.save
 * @description:机票节省分析-平均票价/平均里程
 * @author: md_wang
 * @create: 2022-08-02 18:04
 **/
@Data
public class FlightPriceAvgAndMileageAvgDTO {

    /**
     * 折扣分布字段
     */
    @Column(name = "avgPrice")
    @Type(value = Types.VARCHAR)
    private String avgPrice;

    /**
     * 票张
     */
    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    /**
     * 里程字段
     */
    @Column(name = "avgMileage")
    @Type(value = Types.VARCHAR)
    private String avgMileage;

    /**
     * 总金额
     */
    @Column(name = "totalPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalPrice;

    /**
     * 平均价格
     */
    @Column(name = "avgAmountPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgAmountPrice;
}
