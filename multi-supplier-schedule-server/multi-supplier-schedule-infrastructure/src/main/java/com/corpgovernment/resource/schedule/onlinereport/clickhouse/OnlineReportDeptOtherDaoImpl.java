package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptOtherDaoService;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDER_DATE;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
@Slf4j
public class OnlineReportDeptOtherDaoImpl extends AbstractClickhouseBaseDao implements OnlineReportDeptOtherDaoService {

    private static final String log_TITLE = "OnlineReportDeptOtherDao";

    private final static String COUNT_ALIAS = "countAll";

    /**
     * 部门消费
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> deptAnalysis(BaseQueryConditionDTO requestDto, AnalysisTypeEnum analysisTypeEnum,
                                    AnalysisObjectEnum analysisObjectEnum, Class<T> clazz, Pager pager) throws Exception {
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = anaylsisTable(analysisTypeEnum, requestDto.getStatisticalCaliber());
        String analysis = convertSqlField(analysisObjectEnum);
        StringBuilder sqlBuilder = new StringBuilder();
        String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
        sqlBuilder
                .append("select case when (agg is null or agg = '') then '" + other + "' else agg end aggType, result ");
        if (analysisTypeEnum == AnalysisTypeEnum.FLT_EXCEEDING_STANDARD
                || analysisTypeEnum == AnalysisTypeEnum.HTL_EXCEEDING_STANDARD) {
            sqlBuilder.append(", orderCount ");
        }
        sqlBuilder.append(" from (");
        sqlBuilder.append("select ");
        sqlBuilder.append(String.format(" %s AS agg, %s from  ", analysis, anaylsisType(analysisTypeEnum)));
        sqlBuilder.append(tableAndTimeColBind.getClickHouseTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, tableAndTimeColBind.getDateColumn()));
        sqlBuilder.append(specialCondition(analysisTypeEnum));
        sqlBuilder.append(String.format(" group by %s  ", analysis));
        sqlBuilder.append(") temp  where 1=1  ");
        sqlBuilder.append(excludeCondition(analysisTypeEnum));
        if (analysisTypeEnum == AnalysisTypeEnum.FLT_EXCEEDING_STANDARD
                || analysisTypeEnum == AnalysisTypeEnum.HTL_EXCEEDING_STANDARD) {
            sqlBuilder.append(" order by result desc, orderCount desc ");
        } else {
            sqlBuilder.append(" order by result desc ");
        }
        sqlBuilder.append(" limit ?, ?");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), OrpConstants.EMPTY,
                (req, statement) -> mapCommonRequest(parmList, statement, tableAndTimeColBind.getClickHouseTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(log_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "deptAnalysis");
    }

    /**
     * 部门商旅数据
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param analysisTypeEnum
     * @param consumptionLevel 消费等级
     * @param compareCorpSameLevel 是否对比同级比较, 商旅数据
     * @return
     * @throws Exception
     */
    public BigDecimal deptCorpAnalysis(String startTime, String endTime, String statisticalCaliber, AnalysisTypeEnum analysisTypeEnum,
                                       String consumptionLevel, String compareCorpSameLevel)
            throws Exception {
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = anaylsisTable(analysisTypeEnum, statisticalCaliber);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append(String.format(" %s from  ", anaylsisType(analysisTypeEnum)));
        sqlBuilder.append(tableAndTimeColBind.getClickHouseTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d = ?");
        List<Object> parmList = new ArrayList<>();
        // sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(requestDto, parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, tableAndTimeColBind.getDateColumn()));
        sqlBuilder.append(specialCondition(analysisTypeEnum));
        if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), OrpConstants.EMPTY,
                (req, statement) -> mapCommonRequest(parmList, statement, tableAndTimeColBind.getClickHouseTable()), (u, d) -> {
                    try {
                        return DbResultMapUtils.mapBigDecimalResult(u, "result");
                    } catch (Exception e) {
                        log.error(log_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return BigDecimal.ZERO;
                }, BigDecimal.class, "deptCorpAnalysis");
    }

    private PreparedStatement mapCommonRequest(List<Object> parmList, PreparedStatement statement,
                                               ClickHouseTable clickHouseTable) {
        int index = OrpConstants.ONE;
        try {
            // 分区
            statement.setString(index++, queryPartition(clickHouseTable));

            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }

    /**
     * 前5部门消费
     *
     * @param
     * @return
     * @throws Exception
     */
    public Integer count(AnalysisObjectEnum analysisObjectEnum, AnalysisTypeEnum analysisTypeEnum,
                         BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = anaylsisTable(analysisTypeEnum, baseQueryConditionDto.getStatisticalCaliber());
        String analysis = convertSqlField(analysisObjectEnum);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select count(1) as countAll from ( select ");
        if (analysisTypeEnum == AnalysisTypeEnum.FLT_EXCEEDING_STANDARD
                || analysisTypeEnum == AnalysisTypeEnum.HTL_EXCEEDING_STANDARD) {
            sqlBuilder.append(" (count(distinct case when (is_refund = 'F' AND is_rc = 'T') then order_id end )) AS result ");
        } else {
            sqlBuilder.append(" 1 ");
        }
        sqlBuilder.append("from  ");
        sqlBuilder.append(tableAndTimeColBind.getClickHouseTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d = ?");
        List<Object> parmList = new ArrayList<>();
        // sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(baseQueryConditionDto, parmList, tableAndTimeColBind.getDateColumn()));
        sqlBuilder.append(specialCondition(analysisTypeEnum));
        sqlBuilder.append(String.format(" group by %s  ", analysis));
        sqlBuilder.append(") temp  where 1=1  ");
        sqlBuilder.append(excludeCondition(analysisTypeEnum));
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), OrpConstants.EMPTY,
                (req, statement) -> mapCommonRequest(parmList, statement, tableAndTimeColBind.getClickHouseTable()), (u, d) -> {
                    try {
                        return mapIntResult(u, COUNT_ALIAS);
                    } catch (Exception e) {
                        // log.error(log_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return OrpConstants.ZERO;
                }, Integer.class, "count");
    }

    private String convertSqlField(AnalysisObjectEnum analysisObjectEnum) {
        if (ConfigUtils.getBoolean("custom_dim_open", false)) {
            return convertSqlFieldCustom(analysisObjectEnum);
        } else {
            return convertSqlFieldDefault(analysisObjectEnum);
        }
    }

    private String convertSqlFieldDefault(AnalysisObjectEnum analysisObjectEnum) {
        String sqlField = " corp_corporation, corp_name ";
        switch (analysisObjectEnum) {
            case CORP:
                sqlField = " corp_corporation, corp_name ";
                break;
            case ACCOUNT:
                sqlField = "account_id, account_name";
                break;
            case ACCOUNTCODE:
                sqlField = "account_id, account_code";
                break;
            case DEPT1:
                sqlField = "dept1";
                break;
            case DEPT2:
                sqlField = "dept2";
                break;
            case DEPT3:
                sqlField = "dept3";
                break;
            case DEPT4:
                sqlField = "dept4";
                break;
            case DEPT5:
                sqlField = "dept5";
                break;
            case DEPT6:
                sqlField = "dept6";
                break;
            case DEPT7:
                sqlField = "dept7";
                break;
            case DEPT8:
                sqlField = "dept8";
                break;
            case DEPT9:
                sqlField = "dept9";
                break;
            case DEPT10:
                sqlField = "dept10";
                break;
            case COSTCENTER1:
                sqlField = "cost_center1";
                break;
            case COSTCENTER2:
                sqlField = "cost_center2";
                break;
            case COSTCENTER3:
                sqlField = "cost_center3";
                break;
            case COSTCENTER4:
                sqlField = "cost_center4";
                break;
            case COSTCENTER5:
                sqlField = "cost_center5";
                break;
            case COSTCENTER6:
                sqlField = "cost_center6";
                break;
            default:
        }
        return sqlField;
    }

    private String convertSqlFieldCustom(AnalysisObjectEnum analysisObjectEnum) {
        String sqlField = " corp_corporation, corp_name ";
        switch (analysisObjectEnum) {
            case CORP:
                sqlField = " corp_corporation, corp_name ";
                break;
            case ACCOUNT:
                sqlField = "account_id, account_name";
                break;
            case ACCOUNTCODE:
                sqlField = "account_id, account_code";
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                sqlField = analysisObjectEnum.toString().toLowerCase().concat("_custom");
                break;
            case COSTCENTER1:
                sqlField = "costcenter1_custom";
                break;
            case COSTCENTER2:
                sqlField = "costcenter2_custom";
                break;
            case COSTCENTER3:
                sqlField = "costcenter3_custom";
                break;
            case COSTCENTER4:
                sqlField = "costcenter4_custom";
                break;
            case COSTCENTER5:
                sqlField = "costcenter5_custom";
                break;
            case COSTCENTER6:
                sqlField = "costcenter6_custom";
                break;
            default:
        }
        return sqlField;
    }

    /**
     * 统计不同指标
     *
     * @param analysisTypeEnum
     * @return
     */
    private String anaylsisType(AnalysisTypeEnum analysisTypeEnum) {
        String aggSql = StringUtils.EMPTY;
        switch (analysisTypeEnum) {
            case FLT_EXCEEDING_STANDARD:
            case HTL_EXCEEDING_STANDARD:
                aggSql =
                        " round(CASE WHEN count(distinct case when is_refund = 'F' then order_id end ) != 0 "
                                + "THEN divide(toFloat64(count(distinct case when (is_refund = 'F' AND is_rc = 'T') then order_id end ))"
                                + ", count(distinct case when is_refund = 'F' then order_id end )) ELSE 0 END * 100, 4) AS result"
                                + ", count(distinct case when (is_refund = 'F' AND is_rc = 'T') then order_id end ) as orderCount";
                break;
            case FLT_AVG_PRICE_MILEAGE:
                aggSql = " round(CASE  WHEN toFloat64(SUM(CASE \n"
                        + "            WHEN class_type = 'Y' THEN coalesce(tpms, 0)  ELSE 0\n"
                        + "        END)) != 0 THEN divide(toFloat64(SUM(CASE \n"
                        + "            WHEN class_type = 'Y' THEN coalesce(netfare, 0) + coalesce(rebook_price_differential, 0)\n"
                        + "            ELSE 0 END)), toFloat64(SUM(CASE \n"
                        + "            WHEN class_type = 'Y' THEN tpms ELSE 0 END)))\n"
                        + "        ELSE 0 END, 4) AS result ";
                break;
            case HTL_AVG_PRICE_ROOM_NIGHT:
                aggSql =
                        " round(CASE WHEN SUM(quantity) != 0 THEN divide(toFloat64(SUM(room_price)), SUM(quantity)) ELSE 0 END, 4) AS result";
                break;
            default:
        }
        return aggSql;
    }

    private BaseConditionPrebuilder.TableAndTimeColBind anaylsisTable(AnalysisTypeEnum analysisTypeEnum, String statisticalCaliber) {
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = new BaseConditionPrebuilder.TableAndTimeColBind();
//        String statisticalCaliber = requestDto.getStatisticalCaliber();
        ClickHouseTable clickHouseTable = null;
        switch (analysisTypeEnum) {
            case FLT_AVG_PRICE_MILEAGE:
                clickHouseTable = ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL;
                if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                    clickHouseTable = ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL_ODT;
                }
                tableAndTimeColBind.setClickHouseTable(clickHouseTable);
                tableAndTimeColBind.setDateColumn(REPORT_DATE);
                // clickHouseTable = ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL;
                break;
            case FLT_EXCEEDING_STANDARD:
                clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
                tableAndTimeColBind.setClickHouseTable(clickHouseTable);
                tableAndTimeColBind.setDateColumn(REPORT_DATE);
                if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                    tableAndTimeColBind.setDateColumn(ORDER_DATE);
                }
                break;
            case HTL_EXCEEDING_STANDARD:
                clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
                tableAndTimeColBind.setClickHouseTable(clickHouseTable);
                tableAndTimeColBind.setDateColumn(REPORT_DATE);
                if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                    tableAndTimeColBind.setDateColumn(ORDER_DATE);
                }
                break;
            case HTL_AVG_PRICE_ROOM_NIGHT:
                clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL;
                if (StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
                    clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL_ODT;
                }
                tableAndTimeColBind.setClickHouseTable(clickHouseTable);
                tableAndTimeColBind.setDateColumn(REPORT_DATE);
                // clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL;
                break;
            default:
        }
        return tableAndTimeColBind;
    }

    /**
     * 排除未超标数据
     *
     * @param analysisTypeEnum
     * @return
     */
    private String excludeCondition(AnalysisTypeEnum analysisTypeEnum) {
        String condition = StringUtils.EMPTY;
        switch (analysisTypeEnum) {
            case FLT_EXCEEDING_STANDARD:
            case HTL_EXCEEDING_STANDARD:
                condition = " and result != 0";
                break;
            default:
        }
        return condition;
    }

    private String specialCondition(AnalysisTypeEnum analysisTypeEnum) {
        String condition = StringUtils.EMPTY;
        switch (analysisTypeEnum) {
            case HTL_AVG_PRICE_ROOM_NIGHT:
            case HTL_EXCEEDING_STANDARD:
                condition = String.format(" and order_status = '%s'",
                        OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus"));
                break;
            case FLT_EXCEEDING_STANDARD:
                condition = " and audited <> 'F' ";
                break;
            default:
        }
        return condition;
    }

}
