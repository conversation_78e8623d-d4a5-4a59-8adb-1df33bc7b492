package com.corpgovernment.resource.schedule.onlinereport.reportlib.consumeprofile;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.consumeprofile.ConsumeProfileDao;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.OrderAggBO;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.OrderConsumeBO;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.OrderConsumeDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Service
public class FlightConsumeProfileBiz {

    @Autowired
    protected ConsumeProfileDao reportConsumeDao;

    public List<OrderAggBO> aggreationConsume(BaseQueryConditionDTO baseQueryConditionDto, List<String> momTimes, List<String> yoyTimes,
                                              Set points, QueryReportAggDateDimensionEnum dimensionEnum, String lang) throws Exception {
        Map<String, List<OrderConsumeDTO>> groupData = null;
        List<OrderConsumeDTO> list = (List<OrderConsumeDTO>) reportConsumeDao.aggreationFlight(baseQueryConditionDto, OrderConsumeDTO.class, momTimes, yoyTimes, dimensionEnum);
        list = Optional.ofNullable(list).orElse(new ArrayList<>());
        list.stream().filter(Objects::nonNull).forEach(i -> i.setAggDate(BizUtils.dateFormat(i.getAggDate(), dimensionEnum)));
        // 补空数据
        for (Object date : points) {
            if (!list.stream().anyMatch(i -> StringUtils.equalsIgnoreCase((String) date, i.getAggDate()) && StringUtils.equalsIgnoreCase(i.getOrderType(), "N"))) {
                OrderConsumeDTO orderConsumeDTO = new OrderConsumeDTO();
                orderConsumeDTO.setOrderType("N");
                orderConsumeDTO.setAggDate((String) date);
                orderConsumeDTO.setTotalCurrentAmount(BigDecimal.ZERO);
                orderConsumeDTO.setTotalYoyAmount(BigDecimal.ZERO);
                orderConsumeDTO.setTotalMomAmount(BigDecimal.ZERO);
                list.add(orderConsumeDTO);
            }
            if (!list.stream().anyMatch(i -> StringUtils.equalsIgnoreCase((String) date, i.getAggDate()) && StringUtils.equalsIgnoreCase(i.getOrderType(), "I"))) {
                OrderConsumeDTO orderConsumeDTO = new OrderConsumeDTO();
                orderConsumeDTO.setOrderType("I");
                orderConsumeDTO.setAggDate((String) date);
                orderConsumeDTO.setTotalCurrentAmount(BigDecimal.ZERO);
                orderConsumeDTO.setTotalYoyAmount(BigDecimal.ZERO);
                orderConsumeDTO.setTotalMomAmount(BigDecimal.ZERO);
                list.add(orderConsumeDTO);
            }
        }
        groupData = list.stream().sorted(Comparator.comparing(OrderConsumeDTO::getOrderType).reversed()).collect(Collectors.groupingBy(OrderConsumeDTO::getAggDate));
        for (Map.Entry<String, List<OrderConsumeDTO>> entry : groupData.entrySet()) {
            OrderConsumeDTO orderConsumeDTO = new OrderConsumeDTO();
            orderConsumeDTO.setTotalCurrentAmount(Optional.ofNullable(entry.getValue()).orElse(new ArrayList<>())
                    .stream().map(OrderConsumeDTO::getTotalCurrentAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            orderConsumeDTO.setTotalMomAmount(Optional.ofNullable(entry.getValue()).orElse(new ArrayList<>())
                    .stream().map(OrderConsumeDTO::getTotalMomAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            orderConsumeDTO.setTotalYoyAmount(Optional.ofNullable(entry.getValue()).orElse(new ArrayList<>())
                    .stream().map(OrderConsumeDTO::getTotalYoyAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            orderConsumeDTO.setAggDate(entry.getKey());
            orderConsumeDTO.setOrderType("total");
            entry.getValue().add(orderConsumeDTO);
        }
        return calculate(groupData, lang);
    }

    private List<OrderAggBO> calculate(Map<String, List<OrderConsumeDTO>> mapCurrent, String lang) {
        List<OrderAggBO> result = new ArrayList<>();

        for (Map.Entry<String, List<OrderConsumeDTO>> entry : mapCurrent.entrySet()) {
            OrderAggBO genralConsume = new OrderAggBO();
            genralConsume.setAggDate(entry.getKey());
            List<OrderConsumeDTO> list = entry.getValue();
            List<OrderConsumeBO> orderConsumeBOList = new ArrayList<>();

            for (OrderConsumeDTO orderConsumeDTO : list) {
                String orderType = orderConsumeDTO.getOrderType();
                OrderConsumeBO orderConsumeBO = new OrderConsumeBO();
                orderConsumeBO.setAggType(getDimensionDesc(orderType, lang));
                orderConsumeBO.setTotalCurrentAmount(orderConsumeDTO.getTotalCurrentAmount());
                orderConsumeBO.setTotalYoyAmount(orderConsumeDTO.getTotalYoyAmount());
                orderConsumeBO.setTotalMomAmount(orderConsumeDTO.getTotalMomAmount());
                orderConsumeBO.setYoy(OrpReportUtils.divideWithPercent(orderConsumeDTO.getTotalCurrentAmount().subtract(orderConsumeDTO.getTotalYoyAmount())
                        , orderConsumeDTO.getTotalYoyAmount().abs()));
                orderConsumeBO.setMom(OrpReportUtils.divideWithPercent(orderConsumeDTO.getTotalCurrentAmount().subtract(orderConsumeDTO.getTotalMomAmount())
                        , orderConsumeDTO.getTotalMomAmount().abs()));
                orderConsumeBOList.add(orderConsumeBO);
            }
            genralConsume.setList(orderConsumeBOList);
            result.add(genralConsume);
        }
        return result;
    }

    public String getDimensionDesc(String s, String lang) {
        if (StringUtils.equalsIgnoreCase(s, "N")) {
            return SharkUtils.get("Index.domair", lang);
        } else if (StringUtils.equalsIgnoreCase(s, "I")) {
            return SharkUtils.get("Index.interair", lang);
        } else if (StringUtils.equalsIgnoreCase(s, "TOTAL")) {
            return SharkUtils.get("Exceltopname.subtotalflight", lang);
        } else {
            return s;
        }
    }
}
