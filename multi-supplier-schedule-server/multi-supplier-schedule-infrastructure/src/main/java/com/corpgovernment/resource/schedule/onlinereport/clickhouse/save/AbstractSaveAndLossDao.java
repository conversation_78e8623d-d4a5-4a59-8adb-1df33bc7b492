package com.corpgovernment.resource.schedule.onlinereport.clickhouse.save;


import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 * @date 2022/6/23 23:23
 * @Desc
 */
@Slf4j
@Repository
public abstract class AbstractSaveAndLossDao extends AbstractCommonDao {

    private static final String LOG_TITLE = "AbstractSaveAndLossDao";

    private final static String COUNT_ALIAS = "countAll";

    /**
     * 前5部门消费
     *
     * @param
     * @return
     * @throws Exception
     */
    public Integer topRcAnalysisCount(AnalysisObjectEnum analysisObjectEnum,
                                      BaseQueryConditionDTO baseQueryConditionDto, String orderType) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String countSql = countSql(parmList, getTargetTable(), analysisObjectEnum, baseQueryConditionDto, orderType);
        // 查询clickhouse
        return queryBySql(countSql, parmList, (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapIntResult(u, COUNT_ALIAS);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return OrpConstants.ZERO;
                }, Integer.class, "count");
    }

    /**
     * 前5部门消费
     *
     * @param
     * @return
     * @throws Exception
     */
    public List<Map> topAnalysis(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto,
                                 Pager pager, String orderType) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String querySql = querySql(parmList, analysisObjectEnum, baseQueryConditionDto, pager, orderType);
        // 查询clickhouse
        return queryBySql(querySql, parmList, (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return DbResultMapUtils.mapResultList(u);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "deptDetailAnalysis");
    }


    public abstract String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum,
                                    BaseQueryConditionDTO baseQueryConditionDto, Pager pager, String orderType);

    protected abstract ClickHouseTable getTargetTable();

    public abstract String countSql(List<Object> parmList, ClickHouseTable clickHouseTable,
                                    AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, String orderType);


    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinCondition(AnalysisObjectEnum analysisObjectEnum) {
        if (ConfigUtils.getBoolean("custom_dim_open", false)) {
            return joinConditionCustom(analysisObjectEnum);
        } else {
            return joinConditionDefault(analysisObjectEnum);
        }
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionDefault(AnalysisObjectEnum analysisObjectEnum) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz = biz.setResultFields("corp_name as CORP")
                        .setGroupFields(" corp_corporation, corp_name");
                break;
            case ACCOUNT:
                biz = biz.setResultFields("corp_name as CORP, account_name as ACCOUNT")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_name");
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("corp_name as CORP, account_code as ACCOUNTCODE")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_code");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase();
                break;
            case COSTCENTER1:
                analysisObject = "cost_center1";
                break;
            case COSTCENTER2:
                analysisObject = "cost_center2";
                break;
            case COSTCENTER3:
                analysisObject = "cost_center3";
                break;
            case COSTCENTER4:
                analysisObject = "cost_center4";
                break;
            case COSTCENTER5:
                analysisObject = "cost_center5";
                break;
            case COSTCENTER6:
                analysisObject = "cost_center6";
                break;
            case UID:
                biz = biz.setResultFields("uid as UID, user_name as NAME, " +
                                "dept1, dept2, dept3, dept4, dept5, dept6, dept7, dept8, dept9, dept10, " +
                                "cost_center1, cost_center2, cost_center3, cost_center4, cost_center5, cost_center6 ")
                        .setGroupFields(" uid, user_name, dept1, dept2, dept3, dept4, dept5, dept6, dept7, dept8, dept9, dept10, " +
                                "cost_center1, cost_center2, cost_center3, cost_center4, cost_center5, cost_center6 ");
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject)) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
            biz = biz.setResultFields(
                            "corp_name as CORP, case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                                    + "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(" corp_corporation, corp_name ," + analysisObject);

        }
        return biz;
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionCustom(AnalysisObjectEnum analysisObjectEnum) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        String analysisObjectAlias = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz = biz.setResultFields("corp_name as CORP")
                        .setGroupFields(" corp_corporation, corp_name");
                break;
            case ACCOUNT:
                biz = biz.setResultFields("corp_name as CORP, account_name as ACCOUNT")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_name");
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("corp_name as CORP, account_code as ACCOUNTCODE")
                        .setGroupFields(" corp_corporation, corp_name, account_id, account_code");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase().concat("_custom");
                break;
            case COSTCENTER1:
                analysisObject = "costcenter1_custom";
                break;
            case COSTCENTER2:
                analysisObject = "costcenter2_custom";
                break;
            case COSTCENTER3:
                analysisObject = "costcenter3_custom";
                break;
            case COSTCENTER4:
                analysisObject = "costcenter4_custom";
                break;
            case COSTCENTER5:
                analysisObject = "costcenter5_custom";
                break;
            case COSTCENTER6:
                analysisObject = "costcenter6_custom";
                break;
            case UID:
                biz = biz.setResultFields("uid as UID, user_name as NAME, " +
                                "coalesce(dept1_custom, '') as dept1, coalesce(dept2_custom, '') as dept2, coalesce(dept3_custom, '') as dept3, " +
                                "coalesce(dept4_custom, '') as dept4, coalesce(dept5_custom, '') as dept5, " +
                                "coalesce(dept6_custom, '') as dept6, coalesce(dept7_custom, '') as dept7, coalesce(dept8_custom, '') as dept8, " +
                                "coalesce(dept9_custom, '') as dept9, coalesce(dept10_custom, '') as dept10, " +
                                "coalesce(costcenter1_custom, '') as cost_center1, coalesce(costcenter2_custom, '') as cost_center2, " +
                                "coalesce(costcenter3_custom, '') as cost_center3, " +
                                "coalesce(costcenter4_custom, '') as cost_center4, coalesce(costcenter5_custom, '') as cost_center5, " +
                                "coalesce(costcenter6_custom, '') as cost_center6 ")
                        .setGroupFields(" uid, user_name, " +
                                "dept1_custom, dept2_custom, dept3_custom, dept4_custom, dept5_custom, dept6_custom, dept7_custom, dept8_custom, dept9_custom, dept10_custom, " +
                                "costcenter1_custom, costcenter2_custom, costcenter3_custom, costcenter4_custom, costcenter5_custom, costcenter6_custom ");
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject)) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
            biz = biz.setResultFields(
                            "corp_name as CORP, case when (" + analysisObject + " is null or " + analysisObject + " = '') "
                                    + "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(" corp_corporation, corp_name ," + analysisObject);

        }
        return biz;
    }

    @Data
    @Accessors(chain = true)
    class JoinCondition {
        private String resultFields;

        private String groupFields;
    }

    /**
     * 酒店
     *
     * @return
     */
    protected String getHotelCondition(String hotelType) {
        StringBuilder stringBuilder = new StringBuilder(String.format(" and order_status = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus")));
        // 国内、港澳台
        if (StringUtils.equalsIgnoreCase(hotelType, "dom")) {
            stringBuilder.append(" and is_oversea in ('F','O') ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(hotelType, "inter")) {
            stringBuilder.append(" and is_oversea in ('T') ");
        }
        return stringBuilder.toString();
    }

    /**
     * 机票类型查询条件
     *
     * @param flightClass
     * @return
     */
    protected String getFlightClassCondition(String flightClass) {
        if (StringUtils.equalsIgnoreCase(flightClass, "dom")) {
            return " and flight_class = 'N' ";
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(flightClass, "inter")) {
            return " and flight_class = 'I' ";
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }
}
