/*
package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AnalysisObjectOrgInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.model.dept.OverviewTopDeptDTO;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


*/
/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 *//*

@Repository
public class OnlineReportDeptConsumeDao extends AbstractCommonDao {

    @Autowired
    private OnlineReportOverViewJoinCondition onlineReportOverViewJoinCondition;

    private static final String CORP_PAY = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.corpPayType");

    private static final String DEAL = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Deal");

    private static final String OVERVIEW_RC_SQL = "SELECT %s  \n"
            + ",coalesce(flight.rcTimes, 0) + coalesce(hotel.rcTimes, 0) + coalesce(train.rcTimes, 0) AS totalRcTimes\n"
            + ",coalesce(flight.orderCount, 0) + coalesce(hotel.orderCount, 0) + coalesce(train.orderCount, 0) AS totalOrderCount "
            + " FROM ( SELECT %s \n"
            + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s  group by %s ) \n"
            + "         flight full JOIN (\n"
            + "        SELECT %s \n"
            + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
            + "        FROM olrpt_indexhoteldownload_all WHERE %s %s group by %s )\n"
            + "         hotel on %s full JOIN (\n"
            + "        SELECT %s \n"
            + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes\n"
            + "        FROM olrpt_indextraindownload_all WHERE %s AND order_status  in ('TA','RP','EP','EA') group by %s)\n"
            + "         train on %s";

    private static final String OVERVIEW_RC_CORP_INDUSTRY_SQL = "SELECT \n"
            + "coalesce(flight.rcTimes, 0) + coalesce(hotel.rcTimes, 0) + coalesce(train.rcTimes, 0) AS totalRcTimes\n"
            + ",coalesce(flight.orderCount, 0) + coalesce(hotel.orderCount, 0) + coalesce(train.orderCount, 0) AS totalOrderCount "
            + " FROM ( SELECT  \n"
            + "        COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s \n"
            + "        having (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0) \n"
            + "         flight CROSS JOIN (\n"
            + "        SELECT \n"
            + "        COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
            + "        FROM olrpt_indexhoteldownload_all WHERE %s %s \n"
            + "          having (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0)\n"
            + "         hotel CROSS JOIN (\n"
            + "        SELECT \n"
            + "        COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes\n"
            + "        FROM olrpt_indextraindownload_all WHERE %s AND order_status  in ('TA','RP','EP','EA')\n"
            + "            having (COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END)) != 0)\n"
            + "         train ";

    private static final String OVERVIEW_OVER_AMOUNT_SQL = "SELECT %s  \n"
            + ",coalesce(flight.lossPrice, 0) + coalesce(hotel.lossPrice, 0) AS totalOverAmount\n"
            + " FROM ( SELECT %s \n"
            + "        , round(sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end), 2) as lossPrice"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s  group by %s \n"
            + "        having (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0) \n"
            + "         flight full JOIN (\n"
            + "        SELECT %s \n"
            + "        ,  round(SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end),2) as lossPrice \n"
            + "        FROM olrpt_indexhoteldownload_all WHERE %s %s group by %s \n"
            + "          having (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0)\n"
            + "         hotel on %s";

    private static final String OVERVIEW_CARBON_SQL = "SELECT %s  \n"
            + ",coalesce(flight.totalCarbonEmission, 0) + coalesce(car.totalCarbonEmission, 0) + coalesce(train.totalCarbonEmission, 0) AS totalCarbonEmission\n"
            + " FROM ( SELECT %s \n"
            + "        , SUM(coalesce(carbon_emission, 0)) AS totalCarbonEmission"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s  group by %s ) \n"
            + "         flight full JOIN (\n"
            + "        SELECT %s \n"
            + "        , SUM(coalesce(carbon_emission, 0)) AS totalCarbonEmission"
            + "        FROM olrpt_indextraindownload_all WHERE %s group by %s )\n"
            + "         train on %s full JOIN (\n"
            + "        SELECT %s \n"
            + "        , SUM(coalesce(carbon_emission, 0)) AS totalCarbonEmission"
            + "        FROM olrpt_indexcardownload_all WHERE %s AND order_status  in ('TA','RP','EP','EA') group by %s)\n"
            + "         car on %s";

    public String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum,
                           BaseQueryConditionDTO baseQueryConditionDto, Pager pager, QueryReportBuTypeEnum queryBu) throws Exception {

        JoinCondition biz = joinCondition(analysisObjectEnum, null);
        String base = String.format("SELECT %s FROM", biz.getResultFields() + staticalsSql());
        StringBuilder baseSql = new StringBuilder(base);
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String statisticalCaliber = baseQueryConditionDto.getStatisticalCaliber();
        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partion = queryPartition(clickHouseTable);

        String currentCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion);
        int intervalMonths =
                OrpDateTimeUtils.getMonthNum(baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime());
        baseSql.append(String.format(
                "( SELECT %s  FROM %s\n"
                        + "    WHERE %s GROUP BY %s) %s",
                biz.getGroupFields() + ", " + getStaticalField(queryBu) + ", " + getStaticalQuantity(queryBu)
                , clickHouseTable.getTable()
                , currentCondition
                , biz.getGroupFields()
                , "crt")
        );
        // 同比去年
        List<String> yoyTimes = OrpDateTimeUtils.yoyTime(startTime, endTime, OrpConstants.ONE_YEAR_DAYS);

        String yoyCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, yoyTimes.get(0), yoyTimes.get(1), partion);
        baseSql.append(String.format(
                " LEFT JOIN (SELECT %s\n"
                        + "        FROM %s  WHERE %s\n"
                        + "        GROUP BY %s) %s  ON  %s",
                biz.getGroupFields() + ", " + getStaticalField(queryBu)
                , clickHouseTable.getTable()
                , yoyCondition, biz.getGroupFields()
                , "yoy", biz.getCurrentJoinYoy()));
        // 同比前年
        List<String> yoyBeforeTimes = OrpDateTimeUtils.yoyTime(startTime, endTime, OrpConstants.TWO_YEAR_DAYS);
        String yoyBeforeLastCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, yoyBeforeTimes.get(0), yoyBeforeTimes.get(1), partion);
        baseSql.append(String.format(
                " LEFT JOIN (SELECT %s\n"
                        + "        FROM %s  WHERE %s\n"
                        + "        GROUP BY %s) %s  ON  %s",
                biz.getGroupFields() + ", " + getStaticalField(queryBu)
                , clickHouseTable.getTable()
                , yoyBeforeLastCondition
                , biz.getGroupFields(), "yoyBeforeLast", biz.getCurrentJoinYoyBeforeLast()));
        // 环比
        List<String> momTimes = OrpDateTimeUtils.momTime(startTime, endTime);
        String momCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, momTimes.get(0), momTimes.get(1), partion);
        baseSql.append(String.format(
                " LEFT JOIN (SELECT %s\n"
                        + "        FROM %s  WHERE %s\n"
                        + "        GROUP BY %s) %s  ON  %s",
                biz.getGroupFields() + ", " + getStaticalField(queryBu)
                , clickHouseTable.getTable()
                , momCondition, biz.getGroupFields()
                , "mom", biz.getCurrentJoinMom()));
        currentCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion);
        baseSql.append(String.format(
                " CROSS JOIN (SELECT %s FROM %s  WHERE %s ) total",
                getStaticalField(queryBu), clickHouseTable.getTable(), currentCondition));
        baseSql.append(" order by crt.real_pay desc");
        baseSql.append(" limit ?, ? ");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        return baseSql.toString();
    }

    private String staticalsSql() {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(" , round(crt.real_pay, 2) AS realPay");
        stringBuffer.append(" , crt.totalQuantity AS totalQuantity");
        stringBuffer.append(" , round(case when toFloat64(abs(coalesce(yoy.real_pay, 0))) !=0 "
                + "then divide(toFloat64(coalesce(crt.real_pay, 0) - coalesce(yoy.real_pay, 0))"
                + ", toFloat64(abs(coalesce(yoy.real_pay, 0)))) * 100 else 0 end, 2) AS yoy");
        stringBuffer.append(" , round(case when toFloat64(abs(coalesce(yoyBeforeLast.real_pay, 0))) !=0 "
                + "then divide(toFloat64(coalesce(crt.real_pay, 0) - coalesce(yoyBeforeLast.real_pay, 0))"
                + ", toFloat64(abs(coalesce(yoyBeforeLast.real_pay, 0)))) * 100 else 0 end, 2) AS yoyBeforeLast");
        stringBuffer.append(" , round(case when toFloat64(abs(coalesce(mom.real_pay, 0))) !=0 "
                + "then divide(toFloat64(coalesce(crt.real_pay, 0) - coalesce(mom.real_pay, 0))"
                + ", toFloat64(abs(coalesce(mom.real_pay, 0)))) * 100 else 0 end, 2) AS mom");
        stringBuffer.append(" , round(case when toFloat64(abs(coalesce(crt.real_pay, 0))) !=0 "
                + "then divide(toFloat64(coalesce(crt.real_pay, 0)), toFloat64(abs(coalesce(total.real_pay, 0))))* 100 else 0 end, 2) AS realPayPercent");
        return stringBuffer.toString();
    }

    */
/**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     *//*

    public JoinCondition joinCondition(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        if (ConfigUtils.getBoolean("custom_dim_open", false)) {
            return joinConditionCustom(analysisObjectEnum, analysisObjectOrgInfo);
        } else {
            return joinConditionDefault(analysisObjectEnum, analysisObjectOrgInfo);
        }
    }

    */
/**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     *//*

    public JoinCondition joinConditionDefault(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz = biz.setSpecialResultFields("crt.corp_corporation as aggId, crt.corp_name as aggType").setResultFields("crt.corp_name as aggType")
                        .setOrderByFields("crt.corp_corporation, crt.corp_name")
                        .setGroupFields("corp_corporation, corp_name")
                        .setCurrentJoinMom(
                                "crt.corp_corporation = mom.corp_corporation and crt.corp_name = mom.corp_name")
                        .setCurrentJoinYoy(
                                "crt.corp_corporation = yoy.corp_corporation and crt.corp_name = yoy.corp_name")
                        .setCurrentJoinYoyBeforeLast(
                                "crt.corp_corporation = yoyBeforeLast.corp_corporation and crt.corp_name = yoyBeforeLast.corp_name");
                break;
            case ACCOUNT:
                biz = biz.setSpecialResultFields("crt.account_id as aggId, crt.account_name as aggType").setResultFields("crt.account_name as aggType")
                        .setOrderByFields("crt.account_id, crt.account_name")
                        .setGroupFields("account_id, account_name")
                        .setCurrentJoinMom(
                                "crt.account_id = mom.account_id and crt.account_name = mom.account_name")
                        .setCurrentJoinYoy(
                                "crt.account_id = yoy.account_id and crt.account_name = yoy.account_name")
                        .setCurrentJoinYoyBeforeLast(
                                "crt.account_id = yoyBeforeLast.account_id and crt.account_name = yoyBeforeLast.account_name");
                break;
            case ACCOUNTCODE:
                biz = biz.setSpecialResultFields("crt.account_id as aggId, crt.account_code as aggType").setResultFields("crt.account_code as aggType")
                        .setOrderByFields("crt.account_id, crt.account_code")
                        .setGroupFields("account_id, account_code")
                        .setCurrentJoinMom(
                                "crt.account_id = mom.account_id and crt.account_code = mom.account_code")
                        .setCurrentJoinYoy(
                                "crt.account_id = yoy.account_id and crt.account_code = yoy.account_code")
                        .setCurrentJoinYoyBeforeLast(
                                "crt.account_id = yoyBeforeLast.account_id and crt.account_code = yoyBeforeLast.account_code");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase();
                break;
            case COSTCENTER1:
                analysisObject = "cost_center1";
                break;
            case COSTCENTER2:
                analysisObject = "cost_center2";
                break;
            case COSTCENTER3:
                analysisObject = "cost_center3";
                break;
            case COSTCENTER4:
                analysisObject = "cost_center4";
                break;
            case COSTCENTER5:
                analysisObject = "cost_center5";
                break;
            case COSTCENTER6:
                analysisObject = "cost_center6";
                break;
            case ORG:
                analysisObject = getAnalysisObjectOrgInfo(analysisObjectOrgInfo);
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject)) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");

            biz = biz.setResultFields(" case when (coalesce(crt." + analysisObject + ",'')='') then '" + other + "' else crt." + analysisObject + " end as " + "aggType")
                    .setSpecialResultFields(" case when (coalesce(crt." + analysisObject + ",'')='') then '" + other + "' else crt." + analysisObject + " end as " +
                            "aggId ,case when (coalesce(crt." + analysisObject + ",'')='') then '" + other + "' else crt." + analysisObject + " end as " + "aggType")
                    .setOrderByFields("crt." + analysisObject).setGroupFields(analysisObject)
                    .setCurrentJoinMom(String.format("crt.%s = mom.%s", analysisObject, analysisObject))
                    .setCurrentJoinYoy(String.format("crt.%s = yoy.%s", analysisObject, analysisObject))
                    .setCurrentJoinYoyBeforeLast(
                            String.format("crt.%s = yoyBeforeLast.%s", analysisObject, analysisObject));
        }
        if (analysisObjectEnum == AnalysisObjectEnum.ORG) {
            biz = biz.setResultFields(analysisObject + "aggType")
                    .setSpecialResultFields(" '' as aggId " + " ," + analysisObject + "aggType")
                    .setOrderByFields(" aggId, aggType").setGroupFields(" aggId, aggType");
        }
        return biz;
    }


    */
/**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     *//*

    public JoinCondition joinConditionCustom(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz = biz.setSpecialResultFields("crt.corp_corporation as aggId, crt.corp_name as aggType").setResultFields("crt.corp_name as aggType")
                        .setOrderByFields("crt.corp_corporation, crt.corp_name")
                        .setGroupFields("corp_corporation, corp_name")
                        .setCurrentJoinMom(
                                "crt.corp_corporation = mom.corp_corporation and crt.corp_name = mom.corp_name")
                        .setCurrentJoinYoy(
                                "crt.corp_corporation = yoy.corp_corporation and crt.corp_name = yoy.corp_name")
                        .setCurrentJoinYoyBeforeLast(
                                "crt.corp_corporation = yoyBeforeLast.corp_corporation and crt.corp_name = yoyBeforeLast.corp_name");
                break;
            case ACCOUNT:
                biz = biz.setSpecialResultFields("crt.account_id as aggId, crt.account_name as aggType").setResultFields("crt.account_name as aggType")
                        .setOrderByFields("crt.account_id, crt.account_name")
                        .setGroupFields("account_id, account_name")
                        .setCurrentJoinMom(
                                "crt.account_id = mom.account_id and crt.account_name = mom.account_name")
                        .setCurrentJoinYoy(
                                "crt.account_id = yoy.account_id and crt.account_name = yoy.account_name")
                        .setCurrentJoinYoyBeforeLast(
                                "crt.account_id = yoyBeforeLast.account_id and crt.account_name = yoyBeforeLast.account_name");
                break;
            case ACCOUNTCODE:
                biz = biz.setSpecialResultFields("crt.account_id as aggId, crt.account_code as aggType").setResultFields("crt.account_code as aggType")
                        .setOrderByFields("crt.account_id, crt.account_code")
                        .setGroupFields("account_id, account_code")
                        .setCurrentJoinMom(
                                "crt.account_id = mom.account_id and crt.account_code = mom.account_code")
                        .setCurrentJoinYoy(
                                "crt.account_id = yoy.account_id and crt.account_code = yoy.account_code")
                        .setCurrentJoinYoyBeforeLast(
                                "crt.account_id = yoyBeforeLast.account_id and crt.account_code = yoyBeforeLast.account_code");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase().concat("_custom");
                break;
            case COSTCENTER1:
                analysisObject = "costcenter1_custom";
                break;
            case COSTCENTER2:
                analysisObject = "costcenter2_custom";
                break;
            case COSTCENTER3:
                analysisObject = "costcenter3_custom";
                break;
            case COSTCENTER4:
                analysisObject = "costcenter4_custom";
                break;
            case COSTCENTER5:
                analysisObject = "costcenter5_custom";
                break;
            case COSTCENTER6:
                analysisObject = "costcenter6_custom";
                break;
            case ORG:
                analysisObject = getAnalysisObjectOrgInfo(analysisObjectOrgInfo);
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject) && analysisObjectEnum != AnalysisObjectEnum.ORG) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
            biz = biz.setResultFields(" case when (coalesce(crt." + analysisObject + ",'')='') then '" + other + "' else crt." + analysisObject + " end as " + "aggType")
                    .setSpecialResultFields(" case when (coalesce(crt." + analysisObject + ",'')='') then '" + other + "' else crt." + analysisObject + " end as "
                            + "aggId,case when (coalesce(crt." + analysisObject + ",'')='') then '" + other + "' else crt." + analysisObject + " end as " + "aggType")
                    .setOrderByFields("crt." + analysisObject).setGroupFields(analysisObject)
                    .setCurrentJoinMom(String.format("crt.%s = mom.%s", analysisObject, analysisObject))
                    .setCurrentJoinYoy(String.format("crt.%s = yoy.%s", analysisObject, analysisObject))
                    .setCurrentJoinYoyBeforeLast(
                            String.format("crt.%s = yoyBeforeLast.%s", analysisObject, analysisObject));
        }
        if (analysisObjectEnum == AnalysisObjectEnum.ORG) {
            biz = biz.setResultFields(analysisObject + "aggType")
                    .setSpecialResultFields(" '' as aggId " + " ," + analysisObject + "aggType")
                    .setOrderByFields(" aggId, aggType").setGroupFields(" aggId, aggType");
        }
        return biz;
    }

    private String getAnalysisObjectOrgInfo(AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        return String.format("visitParamExtractString(crt.orginfo, 'org%d')", analysisObjectOrgInfo.getLevel() + 1);
    }

    private String getAnalysisObjectOrgInfoCondition(AnalysisObjectOrgInfo analysisObjectOrgInfo, AnalysisObjectEnum analysisObjectEnum) {
        StringBuilder stringBuilder = new StringBuilder();
        if (analysisObjectOrgInfo == null || StringUtils.isEmpty(analysisObjectOrgInfo.getOrgName()) || analysisObjectEnum != AnalysisObjectEnum.ORG) {
            return stringBuilder.toString();
        }
        stringBuilder.append(" AND coalesce(orginfo, '') != '' ");
        if (analysisObjectOrgInfo.getLevel() > 0) {
            stringBuilder.append(String.format(" AND visitParamExtractString(orginfo, 'org%d') = '%s' ",
                    analysisObjectOrgInfo.getLevel(), analysisObjectOrgInfo.getOrgName()));
            stringBuilder.append(String.format(" AND visitParamExtractString(orginfo, 'org%d') != '' ", analysisObjectOrgInfo.getLevel() + 1));
        }
        return stringBuilder.toString();
    }

    */
/**
     * 前5部门消费
     *
     * @param
     * @return
     * @throws Exception
     *//*

    public <T> List<T> deptConsume(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto,
                                   Pager pager, Class<T> clazz, QueryReportBuTypeEnum queryBu) throws Exception {
        List<Object> parmList = new ArrayList<>();
        String querySql = querySql(parmList, analysisObjectEnum, baseQueryConditionDto, pager, queryBu);
        // 查询clickhouse
        return commonList(clazz, querySql, parmList);
    }

    */
/**
     * 前5部门消费
     *
     * @param
     * @return
     * @throws Exception
     *//*

    public Integer count(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto)
            throws Exception {
        List<Object> parmList = new ArrayList<>();

        String statisticalCaliber = baseQueryConditionDto.getStatisticalCaliber();
        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String countSql =
                countSql(parmList, clickHouseTable, analysisObjectEnum, baseQueryConditionDto);
        // 查询clickhouse
        return commonCount(countSql, parmList);
    }

    public String countSql(List<Object> parmList, ClickHouseTable clickHouseTable,
                           AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        JoinCondition biz = joinCondition(analysisObjectEnum, null);
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String partion = queryPartition(clickHouseTable);
        String currentCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion);
        return String.format(
                "select count(1) as countAll from(SELECT 1  FROM  %s  WHERE %s  GROUP by %s )",
                clickHouseTable.getTable(), currentCondition, biz.groupFields);
    }

    @Data
    @Accessors(chain = true)
    class JoinCondition {
        private String resultFields;
        private String specialResultFields;
        private String groupFields;

        private String currentJoinMom;

        private String currentJoinYoy;

        private String currentJoinYoyBeforeLast;

        private String orderByFields;
    }

    private String getStaticalField(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        String staticalField = " SUM(coalesce(amount_total,0)) AS real_pay ";
        if (queryReportBuTypeEnum == null) {
            return staticalField;
        }
        switch (queryReportBuTypeEnum) {
            case overview:
                staticalField = " SUM(coalesce(amount_total,0)) AS real_pay ";
                break;
            case flight:
                staticalField = " SUM(coalesce(amount_flt,0)) AS real_pay ";
                break;
            case hotel:
                staticalField = " SUM(coalesce(amount_htl,0)) AS real_pay ";
                break;
            case train:
                staticalField = " SUM(coalesce(amount_train,0)) AS real_pay ";
                break;
            case car:
                staticalField = " SUM(coalesce(amount_car,0)) AS real_pay ";
                break;
            case bus:
                staticalField = " SUM(coalesce(amount_bus,0)) AS real_pay ";
                break;
            case vaso:
                staticalField = " SUM(coalesce(amount_vas,0)) AS real_pay ";
                break;
            default:
                staticalField = " SUM(coalesce(amount_total,0)) AS real_pay ";
                break;
        }
        return staticalField;
    }

    private String getStaticalQuantity(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        String staticalField = " SUM(coalesce(quantity_total,0)) AS totalQuantity ";
        if (queryReportBuTypeEnum == null) {
            return staticalField;
        }
        switch (queryReportBuTypeEnum) {
            case overview:
                staticalField = " SUM(coalesce(quantity_total,0)) AS totalQuantity ";
                break;
            case flight:
                staticalField = " SUM(coalesce(quantity_flt,0)) AS totalQuantity ";
                break;
            case hotel:
                staticalField = " SUM(coalesce(quantity_htl,0)) AS totalQuantity ";
                break;
            case train:
                staticalField = " SUM(coalesce(quantity_train,0)) AS totalQuantity ";
                break;
            case car:
                staticalField = " SUM(coalesce(quantity_car,0)) AS totalQuantity ";
                break;
            case bus:
                staticalField = " SUM(coalesce(quantity_bus,0)) AS totalQuantity ";
                break;
            case vaso:
                staticalField = " SUM(coalesce(quantity_vas,0)) AS totalQuantity ";
                break;
            default:
                staticalField = " SUM(coalesce(quantity_total,0)) AS totalQuantity ";
                break;
        }
        return staticalField;
    }

    public <T> List<T> topDeptMetricAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum,
                                             Class<T> clazz, QueryReportBuTypeEnum queryBu, String metric, Pager pager) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        JoinCondition biz = joinCondition(analysisObjectEnum, null);

        List<Object> parmList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        switch (queryBu) {
            case flight:
                sql.append(fltMetricSql(requestDto, metric, biz, parmList, isBookCaliber));
                break;
            case hotel:
                sql.append(htlMetricSql(requestDto, metric, biz, parmList, isBookCaliber));
                break;
            case train:
                sql.append(trainMetricSql(requestDto, metric, biz, parmList, isBookCaliber));
                break;
            default:
                break;
        }
        sql.append(" limit ?, ? ");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        // 查询clickhouse
        return commonList(clazz, sql.toString(), parmList);
    }

    private String fltMetricSql(BaseQueryConditionDTO requestDto,
                                String metric, JoinCondition biz, List<Object> parmList, boolean isBookCaliber) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ").append(biz.getResultFields());
        if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.REFUND_QUANTITY.toString())) {
            sqlBuilder.append(" , SUM(coalesce(refundtkt, 0)) AS metric ");
            sqlBuilder.append(",SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0)) + SUM(coalesce(refund_behind_service_fee, 0)) as loss");
        } else if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.REBOOK_QUANTITY.toString())) {
            sqlBuilder.append(" , SUM(CASE WHEN is_rebook = 'T' THEN quantity ELSE 0 END) AS metric ");
            sqlBuilder.append(",SUM(coalesce(change_fee,0))+SUM(coalesce(rebook_price_differential,0))+SUM(coalesce(rebook_service_fee,0))" +
                    " + SUM(if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0)))+SUM(coalesce(rebook_behind_service_fee, 0)) as loss");
        } else if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.LOW_RC_COUNT.toString())) {
            sqlBuilder.append(" , count(distinct CASE WHEN is_refund = 'F' and low_rid IS NOT NULL AND low_rid != '' THEN order_id END) AS metric ");
            sqlBuilder.append(" , SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as loss ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable()).append(" as crt");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by metric desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    private String htlMetricSql(BaseQueryConditionDTO requestDto, String metric,
                                JoinCondition biz, List<Object> parmList, boolean isBookCaliber) {
        StringBuilder sqlBuilder = new StringBuilder();

        sqlBuilder.append(" SELECT ").append(biz.getResultFields());
        if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.REFUND_QUANTITY.toString())) {
            sqlBuilder.append(" , SUM(CASE WHEN is_refund = 'T' THEN abs(coalesce(quantity,0)) ELSE 0 END) AS metric ");
            sqlBuilder.append(" , sum(coalesce(cancel_esti_save_amount,0)) AS loss ");
        } else if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.LOW_RC_COUNT.toString())) {
            sqlBuilder.append(" , count(distinct CASE WHEN reason_code IS NOT NULL AND reason_code != '' THEN order_id END) AS metric ");
            sqlBuilder.append(" , SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) AS loss ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable()).append(" as crt");
        ;
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by metric desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    private String trainMetricSql(BaseQueryConditionDTO requestDto, String metric,
                                  JoinCondition biz, List<Object> parmList, boolean isBookCaliber) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ").append(biz.getResultFields());
        if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.REFUND_QUANTITY.toString())) {
            sqlBuilder.append(" , sum(case when  coalesce(quantity, 0) < 0 then abs(coalesce(quantity,0)) else 0 end) AS metric ");
            sqlBuilder.append(" , sum(case when refund_status = 'S' THEN coalesce(ticket_price,0) + coalesce(refund_ticket_fee,0) else 0 end) AS loss ");
        } else if (StringUtils.equalsIgnoreCase(metric, DeptMetricEnum.REBOOK_QUANTITY.toString())) {
            sqlBuilder.append(" , SUM(coalesce(change_quantity, 0)) AS metric ");
            sqlBuilder.append(" ,SUM(coalesce(changebalance, 0)) + SUM(coalesce(deal_change_service_fee, 0)) + SUM(coalesce(afterchangeservicefee, 0)) AS loss ");
        }
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable()).append(" as crt");
        ;
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by metric desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    enum DeptMetricEnum {
        REFUND_QUANTITY, REBOOK_QUANTITY, LOW_RC_COUNT
    }


    public <T> List<T> topDeptAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum, Class<T> clazz,
                                       QueryReportBuTypeEnum queryBu, AnalysisObjectOrgInfo analysisObjectOrgInfo) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        JoinCondition biz = joinCondition(analysisObjectEnum, analysisObjectOrgInfo);

        List<Object> parmList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        switch (queryBu) {
            case flight:
                sql.append(fltSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum));
                break;
            case hotel:
                sql.append(htlSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum));
                break;
            case train:
                sql.append(trainSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum));
                break;
            case car:
                sql.append(carSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum));
                break;
            case overview:
                sql.append(overviewSql(requestDto, biz, parmList, isBookCaliber, analysisObjectOrgInfo, analysisObjectEnum));
                break;
            default:
                break;
        }
        // 查询clickhouse
        return commonList(clazz, sql.toString(), parmList);
    }

    public <T> List<T> topDeptRcPercentAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum, Class<T> clazz,
                                                AnalysisObjectOrgInfo analysisObjectOrgInfo) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        OnlineReportOverViewJoinCondition.JoinCondition biz = onlineReportOverViewJoinCondition.joinCondition(analysisObjectEnum, analysisObjectOrgInfo);
        List<Object> parmList = new ArrayList<>();
        StringBuffer baseSql = new StringBuffer();

        String flightPartition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        String hotelPartition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        String trainPartition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);

        String orgCondition = getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum);
        baseSql.append(String.format(OVERVIEW_RC_SQL, biz.getRcFullJoinResultFields(),
                biz.getResultFields(),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, flightPartition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition,
                getFlightClassConditionWithAudited(requestDto.getProductType()), biz.getGroupFields(),
                biz.getResultFields(),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, hotelPartition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition,
                getHotelOrderTypeCondition(requestDto.getProductType()), biz.getGroupFields(), biz.getFlightFullJoinHotel(),
                biz.getResultFields(),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, trainPartition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition,
                biz.getGroupFields(), biz.getHotelFullJointrain()));
        // 查询clickhouse
        return commonList(clazz, baseSql.toString(), parmList);
    }


    public List<OverviewTopDeptDTO> topDeptRcPercentAnalysisCorpAndIndustry(String statisticalCaliber, String startTime, String endTime, String productType,
                                                                            List<String> industryList, DataTypeEnum dataTypeEnum,
                                                                            String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuffer baseSql = new StringBuffer();

        String flightPartition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        String hotelPartition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        String trainPartition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);

        String consumptionlevelSql = "";
        boolean ignoreTenantId = false;
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                consumptionlevelSql = " and consumptionlevel = '" + consumptionLevel + "' ";
            }
            baseSql.append(String.format(OVERVIEW_RC_CORP_INDUSTRY_SQL,
                    BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, flightPartition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE) +
                            consumptionlevelSql,
                    getFlightClassConditionWithAudited(productType),
                    BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, hotelPartition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE) +
                            consumptionlevelSql,
                    getHotelOrderTypeCondition(productType),
                    BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, trainPartition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE) +
                            consumptionlevelSql));
            ignoreTenantId = true;
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                consumptionlevelSql = " and consumptionlevel = '" + consumptionLevel + "' ";
            }
            baseSql.append(String.format(OVERVIEW_RC_CORP_INDUSTRY_SQL,
                    BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, flightPartition, isBookCaliber ? ORDERDT : REPORT_DATE) + consumptionlevelSql,
                    getFlightClassConditionWithAudited(productType),
                    BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, hotelPartition, isBookCaliber ? ORDERDT : REPORT_DATE) + consumptionlevelSql,
                    getHotelOrderTypeCondition(productType),
                    BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, trainPartition, isBookCaliber ? ORDERDT : REPORT_DATE) + consumptionlevelSql));
            ignoreTenantId = true;
        }
        // 查询clickhouse
        return commonList(OverviewTopDeptDTO.class, baseSql.toString(), parmList, ignoreTenantId);
    }

    public <T> List<T> topDeptOverAmountAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum, Class<T> clazz,
                                                 AnalysisObjectOrgInfo analysisObjectOrgInfo) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        OnlineReportOverViewJoinCondition.JoinCondition biz = onlineReportOverViewJoinCondition.joinCondition(analysisObjectEnum, analysisObjectOrgInfo);
        List<Object> parmList = new ArrayList<>();
        StringBuffer baseSql = new StringBuffer();

        String flightPartition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        String hotelPartition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);

        String orgCondition = getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum);
        baseSql.append(String.format(OVERVIEW_OVER_AMOUNT_SQL, biz.getOverAmountFullJoinResultFields(),
                biz.getResultFields(),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, flightPartition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition,
                getFlightClassConditionWithAudited(requestDto.getProductType()), biz.getGroupFields(),
                biz.getResultFields(),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, hotelPartition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition,
                getHotelOrderTypeCondition(requestDto.getProductType()), biz.getGroupFields(), biz.getFlightFullJoinHotel()
        ));
        // 查询clickhouse
        return commonList(clazz, baseSql.toString(), parmList);
    }

    public <T> List<T> topDeptCarbonsAnalysis(BaseQueryConditionDTO requestDto, AnalysisObjectEnum analysisObjectEnum, Class<T> clazz,
                                              AnalysisObjectOrgInfo analysisObjectOrgInfo) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        OnlineReportOverViewJoinCondition.JoinCondition biz = onlineReportOverViewJoinCondition.joinCondition(analysisObjectEnum, analysisObjectOrgInfo);
        List<Object> parmList = new ArrayList<>();
        StringBuffer baseSql = new StringBuffer();
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        String orgCondition = getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum);
        baseSql.append(String.format(OVERVIEW_CARBON_SQL, biz.getCarbonsFullJoinResultFields(),
                biz.getResultFields(),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition,
                getFlightClassConditionWithAudited(requestDto.getProductType()), biz.getGroupFields(),
                biz.getResultFields(),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE) + orgCondition,
                biz.getGroupFields(), biz.getFlightFullJoinTrain(),
                biz.getResultFields(),
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, ORDERDT) + orgCondition,
                biz.getGroupFields(), biz.getTrainFullJoinCar()));
        // 查询clickhouse
        return commonList(clazz, baseSql.toString(), parmList);
    }

    public <T> List<T> topDeptAnalysisCorpAndIndustry(String statisticalCaliber, Class<T> clazz, QueryReportBuTypeEnum queryBu, String startTime, String endTime,
                                                      String productType, List<String> industryList, DataTypeEnum dataTypeEnum,
                                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        boolean isBookCaliber = org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        switch (queryBu) {
            case flight:
                sql.append(fltSqlCorpAndIndustry(parmList, isBookCaliber, startTime, endTime, productType,
                        industryList, dataTypeEnum, compareSameLevel, consumptionLevel, compareCorpSameLevel));
                break;
            case hotel:
                sql.append(htlSqlCorpAndIndustry(parmList, isBookCaliber, startTime, endTime, productType,
                        industryList, dataTypeEnum, compareSameLevel, consumptionLevel, compareCorpSameLevel));
                break;
            case train:
                sql.append(trainSqlCorpAndIndustry(parmList, isBookCaliber, startTime, endTime, productType,
                        industryList, dataTypeEnum, compareSameLevel, consumptionLevel, compareCorpSameLevel));
                break;
            case car:
                sql.append(carSqlCorpAndIndustry(parmList, isBookCaliber, startTime, endTime, productType,
                        industryList, dataTypeEnum, compareSameLevel, consumptionLevel, compareCorpSameLevel));
                break;
            case overview:
                sql.append(overviewSqlCorpAndIndustry(parmList, isBookCaliber, startTime, endTime, productType,
                        industryList, dataTypeEnum, compareSameLevel, consumptionLevel, compareCorpSameLevel));
                break;
            default:
                break;
        }

        // 查询行业数据/商旅数据 不添加租户id
        if (DataTypeEnum.INDUSTRY.name().equalsIgnoreCase(dataTypeEnum.name())
            || DataTypeEnum.CORP.name().equalsIgnoreCase(dataTypeEnum.name())){
            commonList(clazz, sql.toString(), parmList, true);
        }


        // 查询clickhouse
        return commonList(clazz, sql.toString(), parmList);
    }

    private String fltSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                          AnalysisObjectEnum analysisObjectEnum) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(biz.getSpecialResultFields()).append(",");
        // 消费金额
        sqlBuilder.append("sum(coalesce(real_pay, 0)) as totalAmount,");
        // 票张数
        sqlBuilder.append("sum(coalesce(quantity, 0)) as totalQuantity,");
        // 成交净价（经济舱）
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(price, 0), 0)) as totalEconmyPrice,");
        // 总里程（经济舱）计算里程均价
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(tpms, 0), 0)) as totalEconmyTpms,");
        // 总票张（经济舱）计算平均票价
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(quantity, 0), 0)) as totalEconmyQuantity,");
        // 总折扣（国内、经济舱）计算平均折扣
        sqlBuilder.append("SUM(if(class_type = 'Y' and flight_class = 'N', coalesce(price_rate, 0) * coalesce(quantity, 0), 0)) as totalDomEconmyDiscount,");
        // 总票张（国内、经济舱）计算平均折扣
        sqlBuilder.append("SUM(if(class_type = 'Y' and flight_class = 'N', coalesce(quantity, 0), 0)) as totalDomEconmyQuantity,");
        // 全价票张
        sqlBuilder.append("sum(coalesce(fullfaretkt, 0)) as totalFullfaretkt,");
        // 全价票张（国内）计算全价票张占比
        sqlBuilder.append("SUM(if( flight_class = 'N', coalesce(fullfaretkt, 0), 0)) as totalDomEconmyFullfaretkt,");
        // 出票张数（国内）计算全价票张占比
        sqlBuilder.append("SUM(if( flight_class = 'N', coalesce(ordertkt, 0), 0)) as totalDomEconmyOrdertkt,");
        // 退票费
        sqlBuilder.append("sum(coalesce(refund_fee, 0)) as totalRefundFee,");
        // 退票张数
        sqlBuilder.append("sum(case when is_refund = 'T' then coalesce(refundtkt, 0) else 0 end ) as totalRefundtkt,");
        // 改签费
        sqlBuilder.append("sum(coalesce(change_fee, 0)) as totalRebookFee,");
        // 改签张数
        sqlBuilder.append("sum(case when is_rebook = 'T' then coalesce(quantity, 0) else 0 end) as totalRebooktkt,");
        // 出票张数
        sqlBuilder.append("sum(coalesce(ordertkt, 0)) as totalOrdertkt,");
        // 超标损失
        sqlBuilder.append("sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as totalOverAmount,");
        // rc次数
        sqlBuilder.append("COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS totalRcTimes,");
        // 总订单数
        sqlBuilder.append("COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS totalOrderCount,");
        // 三方节省金额
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_3c, 0) else 0 end) AS totalSaveAmount3c, ");
        // 三方成交净价
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_3c, 0) else 0 end) AS totalNetfare3c, ");
        // 两方节省金额
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) AS totalSaveAmountPremium, ");
        // 两方成交净价
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_premium, 0) else 0 end) AS totalNetfarePremium, ");
        // 管控节省金额
        sqlBuilder.append("SUM(coalesce(saving_price, 0)) AS totalControlSave, ");
        // 管控成交净价
        sqlBuilder.append("sum(case when coalesce(saving_price, 0) != 0 then coalesce(netfare, 0) else 0 end) AS totalControlNetfare, ");
        // 碳排放
        sqlBuilder.append("SUM(coalesce(carbon_emission, 0)) AS totalCarbons, ");
        // 碳排放中位数
        sqlBuilder.append("SUM(coalesce(median_carbon_emission, 0)) AS totalMedianCarbons, ");
        sqlBuilder.append("sum(if(median_carbon_emission <> 0 AND carbon_emission <> 0, median_carbon_emission - carbon_emission, 0)) as totalCarbonSave");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable()).append(" as crt");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(requestDto.getProductType()));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum));
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    private String fltSqlCorpAndIndustry(List<Object> parmList, boolean isBookCaliber, String startTime, String endTime, String productType,
                                         List<String> industryList, DataTypeEnum dataTypeEnum,
                                         String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        // 成交净价（经济舱）
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(price, 0), 0)) as totalEconmyPrice,");
        // 总里程（经济舱）计算里程均价
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(tpms, 0), 0)) as totalEconmyTpms,");
        // 总票张（经济舱）计算平均票价
        sqlBuilder.append("SUM(if(class_type = 'Y', coalesce(quantity, 0), 0)) as totalEconmyQuantity,");
        // 总折扣（国内、经济舱）计算平均折扣
        sqlBuilder.append("SUM(if(class_type = 'Y' and flight_class = 'N', coalesce(price_rate, 0) * coalesce(quantity, 0), 0)) as totalDomEconmyDiscount,");
        // 总票张（国内、经济舱）计算平均折扣
        sqlBuilder.append("SUM(if(class_type = 'Y' and flight_class = 'N', coalesce(quantity, 0), 0)) as totalDomEconmyQuantity,");
        // 全价票张
        sqlBuilder.append("sum(coalesce(fullfaretkt, 0)) as totalFullfaretkt,");
        // 全价票张（国内）计算全价票张占比
        sqlBuilder.append("SUM(if( flight_class = 'N', coalesce(fullfaretkt, 0), 0)) as totalDomEconmyFullfaretkt,");
        // 出票张数（国内）计算全价票张占比
        sqlBuilder.append("SUM(if( flight_class = 'N', coalesce(ordertkt, 0), 0)) as totalDomEconmyOrdertkt,");
        // 退票张数
        sqlBuilder.append("sum(case when is_refund = 'T' then coalesce(refundtkt, 0) else 0 end ) as totalRefundtkt,");
        // 改签张数
        sqlBuilder.append("sum(case when is_rebook = 'T' then coalesce(quantity, 0) else 0 end) as totalRebooktkt,");
        // 出票张数
        sqlBuilder.append("sum(coalesce(ordertkt, 0)) as totalOrdertkt,");
        // 超标损失
        sqlBuilder.append("sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as totalOverAmount,");
        // rc次数
        sqlBuilder.append("COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS totalRcTimes,");
        // 总订单数
        sqlBuilder.append("COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS totalOrderCount,");
        // 三方节省金额
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_3c, 0) else 0 end) AS totalSaveAmount3c, ");
        // 三方成交净价
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_3c, 0) else 0 end) AS totalNetfare3c, ");
        // 两方节省金额
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) AS totalSaveAmountPremium, ");
        // 两方成交净价
        sqlBuilder.append("sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_premium, 0) else 0 end) AS totalNetfarePremium, ");
        // 管控节省金额
        sqlBuilder.append("SUM(coalesce(saving_price, 0)) AS totalControlSave, ");
        // 管控成交净价
        sqlBuilder.append("sum(case when coalesce(saving_price, 0) != 0 then coalesce(netfare, 0) else 0 end) AS totalControlNetfare, ");
        // 碳排放
        sqlBuilder.append("SUM(coalesce(carbon_emission, 0)) AS totalCarbons, ");
        // 碳排放中位数
        sqlBuilder.append("SUM(coalesce(median_carbon_emission, 0)) AS totalMedianCarbons, ");
        sqlBuilder.append("sum(if(median_carbon_emission <> 0 AND carbon_emission <> 0, median_carbon_emission - carbon_emission, 0)) as totalCarbonSave");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (org.apache.commons.lang.StringUtils.isNotEmpty(compareSameLevel) && org.apache.commons.lang.StringUtils.equalsIgnoreCase(compareSameLevel, "T")
                    && org.apache.commons.lang.StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (org.apache.commons.lang.StringUtils.isNotEmpty(compareCorpSameLevel) && org.apache.commons.lang.StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T")
                    && org.apache.commons.lang.StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        return sqlBuilder.toString();
    }

    private String htlSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                          AnalysisObjectEnum analysisObjectEnum) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ").append(biz.getSpecialResultFields());
        // 消费金额
        sqlBuilder.append(",sum(coalesce(real_pay_with_servicefee, 0)) as totalAmount");
        // 票张数
        sqlBuilder.append(",sum(coalesce(quantity, 0)) as totalQuantity");
        // 房价
        sqlBuilder.append(",sum(coalesce(room_price, 0)) as totalRoomPrice");

        // 协议
        sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(real_pay, 0) else 0 end) AS totalAmount_ta  \n");
        sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(quantity, 0) else 0 end) AS totalQuantity_ta  \n");
        sqlBuilder.append(", SUM(CASE WHEN producttype_all = '" + ta + "' THEN coalesce(room_price, 0) else 0 end) AS totalRoomPrice_ta  \n");
        // 非协议
        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(real_pay, 0) else 0 end) AS totalAmount_nta  \n");
        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(quantity, 0) else 0 end) AS totalQuantity_nta  \n");
        sqlBuilder.append(", SUM(CASE WHEN producttype_all != '" + ta + "' THEN coalesce(room_price, 0) else 0 end) AS totalRoomPrice_nta  \n");

        // 国内
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'F' or is_oversea = 'O') THEN coalesce(real_pay, 0) else 0 end) AS totalAmount_dom  \n");
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'F' or is_oversea = 'O') THEN coalesce(quantity, 0) else 0 end) AS totalQuantity_dom  \n");
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'F' or is_oversea = 'O') THEN coalesce(room_price, 0) else 0 end) AS totalRoomPrice_dom  \n");
        // 海外
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'T' ) THEN coalesce(real_pay, 0) else 0 end) AS totalAmount_inter  \n");
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'T' ) THEN coalesce(quantity, 0) else 0 end) AS totalQuantity_inter  \n");
        sqlBuilder.append(", SUM(CASE WHEN (is_oversea = 'T') THEN coalesce(room_price, 0) else 0 end) AS totalRoomPrice_inter  \n");
        // 超标损失
        sqlBuilder.append(", SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as totalOverAmount");
        // rc次数
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS totalRcTimes \n");
        // 总订单数
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS totalOrderCount \n");
        // 三方协议的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_3c, 0)) as totalSaveAmount3c \n");
        sqlBuilder.append(", SUM(CASE WHEN (producttype_all = '" + ta + "' and save_amount_3c is not null) then toFloat64(corp_real_pay) else 0 end) as totalCorpRealPay3c \n");
        // 两方尊享的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_premium, 0)) as totalSaveAmountPremium \n");
        sqlBuilder.append(", SUM(CASE WHEN (producttype_all = '" + premium + "' and save_amount_premium is not null) then corp_real_pay else 0 end) as totalCorpRealPayPremium\n");

        // 促销优惠活动的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_promotion, 0)) as totalSaveAmountPromotion \n");
        sqlBuilder.append(", SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then corp_real_pay else 0 end) as totalCorpRealPayPromotion  \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as totalControlSave \n");
        sqlBuilder.append(", sum(case when coalesce(saving_price, 0) != 0 then coalesce(corp_real_pay, 0) else 0 end) as totalControlCorpRealPay  \n");

        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable()).append(" as crt");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getHotelOrderTypeCondition(requestDto.getProductType()));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum));
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    private String htlSqlCorpAndIndustry(List<Object> parmList, boolean isBookCaliber, String startTime, String endTime, String productType,
                                         List<String> industryList, DataTypeEnum dataTypeEnum,
                                         String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT sum(coalesce(real_pay_with_servicefee, 0)) as totalAmount");
        // 票张数
        sqlBuilder.append(",sum(coalesce(quantity, 0)) as totalQuantity");
        // 房价
        sqlBuilder.append(",sum(coalesce(room_price, 0)) as totalRoomPrice");

        // 超标损失
        sqlBuilder.append(", SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as totalOverAmount");
        // rc次数
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS totalRcTimes \n");
        // 总订单数
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS totalOrderCount \n");
        // 三方协议的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_3c, 0)) as totalSaveAmount3c \n");
        sqlBuilder.append(", SUM(CASE WHEN (producttype_all = '" + ta + "' and save_amount_3c is not null) then toFloat64(corp_real_pay) else 0 end) as totalCorpRealPay3c \n");
        // 两方尊享的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_premium, 0)) as totalSaveAmountPremium \n");
        sqlBuilder.append(", SUM(CASE WHEN (producttype_all = '" + premium + "' and save_amount_premium is not null) then corp_real_pay else 0 end) as totalCorpRealPayPremium \n");

        // 促销优惠活动的节省金额
        sqlBuilder.append(", SUM(coalesce(save_amount_promotion, 0)) as totalSaveAmountPromotion \n");
        sqlBuilder.append(", SUM(CASE WHEN coalesce(save_amount_promotion, 0) <> 0 then corp_real_pay else 0 end) as totalCorpRealPayPromotion  \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as totalControlSave \n");
        sqlBuilder.append(", sum(case when coalesce(saving_price, 0) != 0 then coalesce(corp_real_pay, 0) else 0 end) as totalControlCorpRealPay  \n");

        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (org.apache.commons.lang.StringUtils.isNotEmpty(compareSameLevel) && org.apache.commons.lang.StringUtils.equalsIgnoreCase(compareSameLevel, "T")
                    && org.apache.commons.lang.StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (org.apache.commons.lang.StringUtils.isNotEmpty(compareCorpSameLevel) && org.apache.commons.lang.StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T")
                    && org.apache.commons.lang.StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        sqlBuilder.append(getHotelOrderTypeCondition(productType));
        return sqlBuilder.toString();
    }

    private String trainSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> parmList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                            AnalysisObjectEnum analysisObjectEnum) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(biz.getSpecialResultFields());
        // 消费金额
        sqlBuilder.append(",sum(coalesce(real_pay, 0)) as totalAmount");
        sqlBuilder.append(",SUM(coalesce(ticket_price,0)+coalesce(changebalance,0)+coalesce(refund_ticket_fee,0)) as totalPrice");
        // 票张数
        sqlBuilder.append(",sum(coalesce(quantity, 0)) as totalQuantity");
        // 退票张数
        sqlBuilder.append(", sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as totalRefundtkt ");
        // 改签张数
        sqlBuilder.append(", sum(coalesce(change_quantity,0)) as totalRebooktkt");
        // 出票张数
        sqlBuilder.append(", sum(case when  quantity > 0 then abs(coalesce(quantity,0)) else 0 end) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable()).append(" as crt");
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum));
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    private String trainSqlCorpAndIndustry(List<Object> parmList, boolean isBookCaliber, String startTime, String endTime, String productType,
                                           List<String> industryList, DataTypeEnum dataTypeEnum,
                                           String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT sum(coalesce(real_pay, 0)) as totalAmount");
        sqlBuilder.append(",SUM(coalesce(ticket_price,0)+coalesce(changebalance,0)+coalesce(refund_ticket_fee,0)) as totalPrice");
        // 票张数
        sqlBuilder.append(",sum(coalesce(quantity, 0)) as totalQuantity");
        // 退票张数
        sqlBuilder.append(", sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as totalRefundtkt ");
        // 改签张数
        sqlBuilder.append(", sum(coalesce(change_quantity,0)) as totalRebooktkt");
        // 出票张数
        sqlBuilder.append(", sum(case when  quantity > 0 then abs(coalesce(quantity,0)) else 0 end) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        sqlBuilder.append(getTrainOrderStatusCondition());
        return sqlBuilder.toString();
    }

    private String carSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> paramList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                          AnalysisObjectEnum analysisObjectEnum) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(biz.getSpecialResultFields());
        // 用车类型, airportpick接送机(order_type IN (1,2)),Charter包车(order_type = 3),rent租车(order_type = 4),tax打车(order_type = 6)
        sqlBuilder.append(", SUM(coalesce(real_pay, 0)) as totalAmount \n");
        sqlBuilder.append(", SUM(if(order_type = 1,coalesce(real_pay, 0),0)) as totalAmountAirportpickDom \n");
        sqlBuilder.append(", SUM(if(order_type = 2,coalesce(real_pay, 0),0)) as totalAmountAirportpickInter \n");
        sqlBuilder.append(", SUM(if(order_type = 3,coalesce(real_pay, 0),0)) as totalAmountCharter \n");
        sqlBuilder.append(", SUM(if(order_type = 4,coalesce(real_pay, 0),0)) as totalAmountRent \n");
        sqlBuilder.append(", SUM(if(order_type = 6,coalesce(real_pay, 0),0)) as totalAmountTax \n");
        sqlBuilder.append(", SUM(cnt_order) as totalOrderCount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable()).append(" as crt");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList,
                queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD), ORDERDT));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum));
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    private String carSqlCorpAndIndustry(List<Object> parmList, boolean isBookCaliber, String startTime, String endTime, String productType,
                                         List<String> industryList, DataTypeEnum dataTypeEnum,
                                         String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ");
        // 用车类型, airportpick接送机(order_type IN (1,2)),Charter包车(order_type = 3),rent租车(order_type = 4),tax打车(order_type = 6)
        sqlBuilder.append("  SUM(coalesce(real_pay, 0)) as totalAmount \n");
        sqlBuilder.append(", SUM(if(order_type = 1,coalesce(real_pay, 0),0)) as totalAmountAirportpickDom \n");
        sqlBuilder.append(", SUM(if(order_type = 2,coalesce(real_pay, 0),0)) as totalAmountAirportpickInter \n");
        sqlBuilder.append(", SUM(if(order_type = 3,coalesce(real_pay, 0),0)) as totalAmountCharter \n");
        sqlBuilder.append(", SUM(if(order_type = 4,coalesce(real_pay, 0),0)) as totalAmountRent \n");
        sqlBuilder.append(", SUM(if(order_type = 6,coalesce(real_pay, 0),0)) as totalAmountTax \n");
        sqlBuilder.append(", count(distinct order_id) as totalOrderCount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, isBookCaliber ? ORDERDT : REPORT_DATE));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        return sqlBuilder.toString();
    }

    private String overviewSql(BaseQueryConditionDTO requestDto, JoinCondition biz, List<Object> paramList, boolean isBookCaliber, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                               AnalysisObjectEnum analysisObjectEnum) {
        StringBuilder sqlBuilder = new StringBuilder();

        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (isBookCaliber) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partion = queryPartition(clickHouseTable);

        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(biz.getSpecialResultFields());
        sqlBuilder.append(", SUM(coalesce(amount_total, 0)) as totalAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(clickHouseTable.getTable()).append(" as crt");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, paramList, requestDto.getStartTime(), requestDto.getEndTime(), partion));
        sqlBuilder.append(getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum));
        sqlBuilder.append(" group by ").append(biz.getGroupFields());
        sqlBuilder.append(" order by totalAmount desc").append(",").append(biz.getGroupFields());
        return sqlBuilder.toString();
    }

    private String overviewSqlCorpAndIndustry(List<Object> parmList, boolean isBookCaliber, String startTime, String endTime, String productType,
                                              List<String> industryList, DataTypeEnum dataTypeEnum,
                                              String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) {
        StringBuilder sqlBuilder = new StringBuilder();

        ClickHouseTable clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        if (isBookCaliber) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        }
        String partition = queryPartition(clickHouseTable);

        sqlBuilder.append(" SELECT ");
        sqlBuilder.append(" SUM(coalesce(amount_total, 0)) as totalAmount \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, REPORT_DATE));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, partition, REPORT_DATE));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        return sqlBuilder.toString();
    }
}
*/
