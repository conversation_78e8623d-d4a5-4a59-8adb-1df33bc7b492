package com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-09-21 11:24
 * @desc
 */
@Data
public class CarbonsViewDTO {
    // 总计
    @Column(name = "sumCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumCarbons;
    // 单程
    @Column(name = "avgCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgCarbons;
    // 环比
    @Column(name = "momSumCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal momSumCarbons;
    // 同比
    @Column(name = "yoySumCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal yoySumCarbons;
}
