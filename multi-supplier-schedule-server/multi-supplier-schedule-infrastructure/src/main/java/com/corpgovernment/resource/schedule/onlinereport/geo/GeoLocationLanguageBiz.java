package com.corpgovernment.resource.schedule.onlinereport.geo;


import com.corpgovernment.resource.schedule.onlinereport.clickhouse.geo.CorpGeoLocationDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.multilanguage.CorpFltMultiLangDao;
import com.corpgovernment.resource.schedule.onlinereport.enums.GeoCategoryEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.geo.GeoLocationDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.geo.MultiLangDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-01-16 11:08
 * @desc
 */
@Service
public class GeoLocationLanguageBiz {

    @Autowired
    private CorpGeoLocationDao corpGeoLocationDao;

    @Autowired
    private CorpFltMultiLangDao corpFltMultiLangDao;

    public List<GeoLocationDTO> queryGeoLocationEntity(List<Integer> idList, List<GeoCategoryEnum> categoryEnums, String lang) throws Exception {
        lang = StringUtils.equalsIgnoreCase(lang, "zh-hk") ? "zh-CHT" : (StringUtils.equalsIgnoreCase(lang, "zh-cn") ? "zh-CHS" : lang);
        if (CollectionUtils.isEmpty(idList)) {
            return null;
        }
        return corpGeoLocationDao.queryGeoLocation(idList, lang, GeoLocationDTO.class, categoryEnums);
    }

    public List<MultiLangDTO> queryFltGeoLocationEntity(List<Integer> idList, String lang) throws Exception {
        lang = StringUtils.equalsIgnoreCase(lang, "zh-hk") ? "zh-CHT" : (StringUtils.equalsIgnoreCase(lang, "zh-cn") ? "zh-CHS" : lang);
        if (CollectionUtils.isEmpty(idList)) {
            return null;
        }
        return corpFltMultiLangDao.queryGeoLocation(idList, lang, MultiLangDTO.class);
    }

    public List<GeoLocationDTO> queryGeoLocationEntity(String name, GeoCategoryEnum categoryEnum, String lang) throws Exception {
        lang = StringUtils.equalsIgnoreCase(lang, "zh-hk") ? "zh-CHT" : (StringUtils.equalsIgnoreCase(lang, "zh-cn") ? "zh-CHS" : lang);
        return corpGeoLocationDao.queryGeoLocation(name, lang, GeoLocationDTO.class, categoryEnum);
    }
}
