package com.corpgovernment.resource.schedule.geography.mysql.mapper;

import com.corpgovernment.resource.schedule.config.TkMapper;
import com.corpgovernment.resource.schedule.geography.mysql.entity.CtripCountryInfoDo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.dao.DataAccessException;

import java.util.List;

/**
 * <AUTHOR> zhang
 * @date 2023/10/23 13:53
 */
@Mapper
public interface MsBaseCountryMapper extends TkMapper<CtripCountryInfoDo> {

    /**
     * 获取国家CtripId
     * @return 国家Id
     */
    List<String> getCtripCountryIds();

}
