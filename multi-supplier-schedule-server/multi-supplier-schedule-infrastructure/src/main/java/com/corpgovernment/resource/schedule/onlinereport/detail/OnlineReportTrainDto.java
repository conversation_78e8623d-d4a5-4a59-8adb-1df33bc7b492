package com.corpgovernment.resource.schedule.onlinereport.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.entity
 * @description: 火车票
 * @author: <PERSON>
 * @create: 2021-11-08 13:05
 **/
@Entity
//@Database(name = "ck_corpbi_htlpftdb")
@Table(name = "adm_indextrain_price_detail")
@Data
public class OnlineReportTrainDto {
    /**
     * 查询时间
     */
    @Column(name = "reportDate")
    @Type(value = Types.VARCHAR)
    private String reportDate;
    /**
     * 后收后取票服务费
     */
    @Column(name = "afteraftertaketicketfee")
    @Type(value = Types.DECIMAL)
    private BigDecimal afteraftertaketicketfee;

    /**
     * 后收改签服务费
     */
    @Column(name = "afterchangeservicefee")
    @Type(value = Types.DECIMAL)
    private BigDecimal afterchangeservicefee;

    /**
     * 代取票人工费
     */
    @Column(name = "aftertaketicketfee")
    @Type(value = Types.DECIMAL)
    private BigDecimal aftertaketicketfee;

    /**
     * 后收服务费
     */
    @Column(name = "after_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal afterServiceFee;

    /**
     * 改签时票面差价
     */
    @Column(name = "changebalance")
    @Type(value = Types.DECIMAL)
    private BigDecimal changebalance;

    /**
     * 改签费
     */
    @Column(name = "delay_reschedule_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal delayRescheduleFee;

    /**
     * 改签退款手续费
     */
    @Column(name = "est_fee_12306")
    @Type(value = Types.DECIMAL)
    private BigDecimal estFee12306;

    /**
     * 改签时商旅收取的管理服务费
     */
    @Column(name = "deal_change_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal dealChangeServiceFee;

    /**
     * 配送费
     */
    @Column(name = "deliver_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal deliverFee;

    /**
     * 抢票费
     */
    @Column(name = "grab_service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal grabServiceFee;

    /**
     * 保险费
     */
    @Column(name = "insurance_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal insuranceFee;

    /**
     * 订单状态
     */
    @Column(name = "order_status")
    @Type(value = Types.VARCHAR)
    private String orderStatus;
    /**
     * 纸质出票费
     */
    @Column(name = "paper_ticket_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal paperTicketFee;


    /**
     * 退票时退补给客户的金额
     */
    @Column(name = "refund_ticket_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal refundTicketFee;

    /**
     * 基础服务费
     */
    @Column(name = "service_fee")
    @Type(value = Types.DECIMAL)
    private BigDecimal serviceFee;

    /**
     * 原始出票金额(票价)
     */
    @Column(name = "ticket_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal ticketPrice;


    /**
     * 消费金额- 同环比
     */
    @Column(name = "realPay")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPay;
    @Column(name = "realPayYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPayYoy;
    @Column(name = "realPayMom")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPayMom;

    /**
     * 火车票张数- 同环比
     */
    @Column(name = "quantityV")
    @Type(value = Types.BIGINT)
    private Long quantityV;
    @Column(name = "quantityYoy")
    @Type(value = Types.BIGINT)
    private Long quantityYoy;
    @Column(name = "quantityMom")
    @Type(value = Types.BIGINT)
    private Long quantityMom;
}
