package com.corpgovernment.resource.schedule.onlinereport.utils.adapter;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.utils.adapter
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-03 19:01
 **/
public class NullStringToEmptyAdapterFactory<T> implements TypeAdapterFactory {

    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
        Class<T> rawType = (Class<T>) type.getRawType();
        if (!rawType.equals(String.class)) {
            return null;
        }
        return (TypeAdapter<T>) new StringNullAdapter();
    }
}
