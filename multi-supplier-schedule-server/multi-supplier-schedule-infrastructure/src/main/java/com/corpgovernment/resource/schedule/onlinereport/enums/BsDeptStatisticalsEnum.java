package com.corpgovernment.resource.schedule.onlinereport.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-22 17:13
 * @desc 蓝色空间部门分析统计字段枚举
 */
public enum BsDeptStatisticalsEnum {
    /***************************************** 公共 *********************************************/
    // 消费金额
    REAL_PAY("overview", "Index.costmoney", 2, false, ""),
    // 转换后的消费金额
    CONVERSION_REAL_PAY("overview", "Index.costmoney", 2, false, ""),
    // 消费金额占比
    REAL_PAY_PERCENT("overview", "Index.costper", 2, true, ""),
    // 同比去年
    YOY_LAST("overview", "Index.CompareLast1yr", 2, true, ""),
    // 同比前年
    YOY_BEFORE_LAST("overview", "Index.CompareLast2yr", 2, true, ""),
    // 环比
    MOM("overview", "Index.rad", 2, true, ""),

    /***************************************** 机票 *********************************************/
    // 张数
    FLT_QUANTITY("flight", "Index.number", 0, false, "1,2,3,4,5"),
    // 张数占比
    FLT_QUANTITY_PERCENT("flight", "Index.numper", 2, true, "1,2,3,4,5"),
    /***************************************** 酒店 *********************************************/
    // 间夜数
    HTL_QUANTITY("hotel", "Index.nightnum", 0, false, ""),
    // 间夜占比
    HTL_QUANTITY_PERCENT("hotel", "Index.nightnumper", 2, true, ""),

    /***************************************** 火车 *********************************************/
    // 张数
    TRAIN_QUANTITY("train", "Index.number", 0, false, ""),
    // 张数占比
    TRAIN_QUANTITY_PERCENT("train", "Index.numper", 2, true, ""),

    /***************************************** 用车 *********************************************/
    // 订单数
    CAR_ORDER_QUANTITY("car", "Index.ordernumber", 0, false, ""),
    // 订单数占比
    CAR_ORDER_QUANTITY_PERCENT("car", "Booking.Percentage", 2, true, ""),
    // 里程均价
    CAR_AVG_MILE_PRICE("car", "Exceltopname.avgmilprice", 2, false, ""),

    /***************************************** 汽车票*********************************************/
    // 票张
    BUS_QUANTITY("bus","Index.num",0,false,""),

    // 票张占比
    BUS_QUANTITY_PERCENT("bus","Index.numpercentage",2,true,""),

    /***************************************** 汽车票*********************************************/
    // 票张
    VASO_QUANTITY("vaso","Index.num",0,false,""),

    // 票张占比
    VASO_QUANTITY_PERCENT("vaso","Index.numpercentage",2,true,"");



    private String bizType;

    private String sharkKey;

    private int num;

    private boolean isPercent;

    private String subQueryBu;

    public String getBizType() {
        return bizType;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public int getNum() {
        return num;
    }

    public boolean isPercent() {
        return isPercent;
    }

    public String getSubQueryBu() {
        return subQueryBu;
    }

    BsDeptStatisticalsEnum(String s, String r, int a, boolean flag, String subQueryBu) {
        this.bizType = s;
        this.sharkKey = r;
        this.num = a;
        this.isPercent = flag;
        this.subQueryBu = subQueryBu;
    }

    public static List<BsDeptStatisticalsEnum> getStaticalsByBizType(String bizType, SubQueryReportBuTypeEnum subQueryReportBuType) {
        List<BsDeptStatisticalsEnum> result = new ArrayList<>();
        BsDeptStatisticalsEnum[] deptStatisticalsEnums = BsDeptStatisticalsEnum.values();
        for (BsDeptStatisticalsEnum deptStatisticalsEnum : deptStatisticalsEnums) {
            if (StringUtils.equalsIgnoreCase(deptStatisticalsEnum.getBizType(), bizType)) {
                result.add(deptStatisticalsEnum);
            }
        }
        return result;
    }

    public static BsDeptStatisticalsEnum getStaticalsByEnumName(String enumName) {
        BsDeptStatisticalsEnum[] deptStatisticalsEnums = BsDeptStatisticalsEnum.values();
        for (BsDeptStatisticalsEnum deptStatisticalsEnum : deptStatisticalsEnums) {
            if (StringUtils.equalsIgnoreCase(deptStatisticalsEnum.toString(), enumName)) {
                return deptStatisticalsEnum;
            }
        }
        return null;
    }
}
