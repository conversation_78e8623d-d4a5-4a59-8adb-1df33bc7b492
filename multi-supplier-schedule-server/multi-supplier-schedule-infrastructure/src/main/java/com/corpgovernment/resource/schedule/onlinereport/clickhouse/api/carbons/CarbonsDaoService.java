package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.carbons;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TrendDimensionTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto.CarBonTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto.CarbonsAndSaveDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
public interface CarbonsDaoService {
    /**
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryCarbons(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception;

    /**
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryCarbonType(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception;

    /**
     * 前5部门
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> queryCarbonsTop5Dept(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, TrendDimensionTypeEnum typeEnum,
                                     AnalysisObjectEnum analysisObjectEnum, Pager pager) throws Exception;

    /**
     * @param requestDto
     * @param productType
     * @param pager
     * @param analysisObjectEnum
     * @param typeEnum
     * @param startMonthTime     月份开始时间
     * @return
     * @throws Exception
     */
    List<Map> queryCarbonsDeptDetail(BaseQueryConditionDTO requestDto, String productType, Pager pager, AnalysisObjectEnum analysisObjectEnum,
                                     TrendDimensionTypeEnum typeEnum, String startMonthTime) throws Exception;

    /**
     * @param requestDto
     * @return
     * @throws Exception
     */
    List<CarbonsAndSaveDTO> queryCarbonSave(BaseQueryConditionDTO requestDto, String startTime, String endTime) throws Exception;

    /**
     * @param startTime
     * @param endTime
     * @param dataTypeEnum
     * @param industryList
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @return
     * @throws Exception
     */
    List<CarbonsAndSaveDTO> queryCarbonSaveCorpAndIndustry(
            String startTime, String endTime, DataTypeEnum dataTypeEnum, List<String> industryList, String statisticalCaliber,
            String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

    /**
     * @param analysisObjectEnum
     * @param baseQueryConditionDto
     * @param pager
     * @param uids                  出行人uid
     * @param userNames             出行人姓名
     * @param eids                  出行人员工编号
     * @return
     * @throws Exception
     */
    List<Map> queryTopDeptDetail(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, Pager pager,
                                 List<String> uids, List<String> userNames, List<String> eids) throws Exception;

    Integer count(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto,
                  List<String> uids, List<String> userNames, List<String> eids) throws Exception;

    List<CarBonTrendDTO> overviewAccTrend(OnlineTrendRequestDto request) throws Exception;

    /**
     * 查询 在线报告概况-消费金额趋势数据 - 概览 （bu + 字段名 -> 数据聚合维度 日期聚合 -> 数据聚合粒度 同环比)
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<CarBonTrendDTO> queryOnlineReportCurrentOverviewTrend(OnlineTrendRequestDto request)
            throws Exception;

    Integer countCarbonsDeptDetail(BaseQueryConditionDTO requestDto, String productType, AnalysisObjectEnum analysisObjectEnum) throws Exception;
}
