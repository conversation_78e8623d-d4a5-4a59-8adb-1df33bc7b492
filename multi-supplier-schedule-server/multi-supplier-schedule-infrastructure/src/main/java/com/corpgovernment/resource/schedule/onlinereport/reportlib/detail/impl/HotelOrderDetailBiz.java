package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.impl;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.multilanguage.CorpHtlMultiLangDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.AbstractDetaiDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.HtlDetaiDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.HtlOrderDetailMapper;
import com.corpgovernment.resource.schedule.onlinereport.enums.GeoCategoryEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.reportlib.HtlOrderStatusEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.geo.MultiLangDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.HotelOrderDTO;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.AbstractOrderDetailBiz;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.DEFAULT_LANG;


/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Service
public class HotelOrderDetailBiz extends AbstractOrderDetailBiz {


    @Qualifier("screenThreadPoolExecutor")
    @Resource
    private ThreadPoolExecutor executorService;
    @Autowired
    private HtlDetaiDaoImpl detaiDao;
    @Autowired
    private HtlOrderDetailMapper detailMapper;
    @Autowired
    private CorpHtlMultiLangDao corpHtlMultiLangDao;

    @Override
    protected AbstractDetaiDao current(OnlineReportOrderDetailRequest request) {
        return detaiDao;
    }

    @Override
    public OnlineReportOrderDetailInfo queryOrderDetail(OnlineReportOrderDetailRequest request) throws Exception {
        BaseQueryCondition baseQueryCondition = request.getBasecondition();
        OnlineReportOrderDetailInfo detailInfo = new OnlineReportOrderDetailInfo();
        List<HotelOrderDTO> list = queryOrderDetailEntity(request, HotelOrderDTO.class);
        if (BlueSpaceUtils.isBlueSpace(baseQueryCondition.getBlueSpace()) || BlueSpaceUtils.isForeign(baseQueryCondition.getPos(), baseQueryCondition.getBlueSpace())) {
            transLate(list, request.getLang());
        }
        if (CollectionUtils.isNotEmpty(list)) {
            detailInfo.setHtlOrderList(detailMapper.toDTOs(list));
        }
        return detailInfo;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel;
    }

    public void transLate(List<HotelOrderDTO> list, String lang) throws Exception {
        Map orderStatusMap = initOrderStatus(lang);
        Map yesOrNoMap = initYesOrNo(lang);
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Integer> countryIds = new HashSet<>();
            Set<Integer> cityIds = new HashSet<>();
            Set<Integer> brandIds = new HashSet<>();
            Set<Integer> groupIds = new HashSet<>();
            Set<Integer> hotelIds = new HashSet<>();
            Set<String> roomIds = new HashSet<>();
            for (HotelOrderDTO hotelOrderDTO : list) {
                if (Objects.nonNull(hotelOrderDTO.getCountryid())) {
                    countryIds.add(hotelOrderDTO.getCountryid());
                }
                if (Objects.nonNull(hotelOrderDTO.getCity())) {
                    cityIds.add(hotelOrderDTO.getCity());
                }
                if (Objects.nonNull(hotelOrderDTO.getBrandid())) {
                    brandIds.add(hotelOrderDTO.getBrandid());
                }
                if (Objects.nonNull(hotelOrderDTO.getMgrgroupid())) {
                    groupIds.add(hotelOrderDTO.getMgrgroupid());
                }
                if (Objects.nonNull(hotelOrderDTO.getMasterhotelid())) {
                    hotelIds.add(hotelOrderDTO.getMasterhotelid());
                }
                if (Objects.nonNull(hotelOrderDTO.getRoomId())) {
                    roomIds.add(hotelOrderDTO.getRoomId());
                }
            }
            Future<List<MultiLangDTO>> future1 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpHtlMultiLangDao.queryGeoLocation(new ArrayList<>(countryIds), lang, MultiLangDTO.class, GeoCategoryEnum.COUNTRY);
                }
            });
            Future<List<MultiLangDTO>> future2 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpHtlMultiLangDao.queryGeoLocation(new ArrayList<>(cityIds), lang, MultiLangDTO.class, GeoCategoryEnum.CITY);
                }
            });
            Future<List<MultiLangDTO>> future3 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpHtlMultiLangDao.queryHtlBrandMultiLang(new ArrayList<>(brandIds), lang, MultiLangDTO.class);
                }
            });
            Future<List<MultiLangDTO>> future4 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpHtlMultiLangDao.queryHtlGroupMultiLang(new ArrayList<>(groupIds), lang, MultiLangDTO.class);
                }
            });
            Future<List<MultiLangDTO>> future5 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpHtlMultiLangDao.queryHtlInfoMultiLang(new ArrayList<>(hotelIds), lang, MultiLangDTO.class);
                }
            });
            Future<List<MultiLangDTO>> future6 = executorService.submit(new Callable<List<MultiLangDTO>>() {
                @Override
                public List<MultiLangDTO> call() throws Exception {
                    return corpHtlMultiLangDao.queryHtlRoomMultiLang(new ArrayList<>(roomIds), lang, MultiLangDTO.class);
                }
            });
            List<MultiLangDTO> countryMultiLangs = future1.get();
            List<MultiLangDTO> cityMultiLangs = future2.get();
            List<MultiLangDTO> brandMultiLangs = future3.get();
            List<MultiLangDTO> groupMultiLangs = future4.get();
            List<MultiLangDTO> hotelMultiLangs = future5.get();
            List<MultiLangDTO> roomMultiLangs = future6.get();
            for (HotelOrderDTO hotelOrderDTO : list) {
                hotelOrderDTO.setCountryName(matchMultiLangInfo(countryMultiLangs, hotelOrderDTO.getCountryid(), hotelOrderDTO.getCountryNameEn()));
                hotelOrderDTO.setCityName(matchMultiLangInfo(cityMultiLangs, hotelOrderDTO.getCity(), hotelOrderDTO.getCityNameEn()));
                hotelOrderDTO.setHotelBrandName(matchMultiLangInfo(brandMultiLangs, String.valueOf(hotelOrderDTO.getBrandid()), StringUtils.EMPTY));
                hotelOrderDTO.setHotelGroupName(matchMultiLangInfo(groupMultiLangs, String.valueOf(hotelOrderDTO.getMgrgroupid()), hotelOrderDTO.getHotelGroupNameEn()));
                hotelOrderDTO.setHotelName(matchMultiLangInfo(hotelMultiLangs, hotelOrderDTO.getMasterhotelid(), hotelOrderDTO.getHotelNameEn()));
                hotelOrderDTO.setRoomName(matchMultiLangInfo(roomMultiLangs, String.valueOf(hotelOrderDTO.getRoomId()), StringUtils.EMPTY));
                hotelOrderDTO.setOrderStatus(StringUtils.trimToEmpty((String) orderStatusMap.get(StringUtils.trimToEmpty(hotelOrderDTO.getOrderStatus()))));
                hotelOrderDTO.setIsRefund(StringUtils.trimToEmpty((String) yesOrNoMap.get(StringUtils.trimToEmpty(hotelOrderDTO.getIsRefund()))));
            }
        }
    }

    private Map initOrderStatus(String lang) {
        return ImmutableMap.builder()
                .put(SharkUtils.get(HtlOrderStatusEnum.P.getName(), DEFAULT_LANG), SharkUtils.get(HtlOrderStatusEnum.P.getName(), lang))
                .put(SharkUtils.get(HtlOrderStatusEnum.C.getName(), DEFAULT_LANG), SharkUtils.get(HtlOrderStatusEnum.C.getName(), lang))
                .put(SharkUtils.get(HtlOrderStatusEnum.S.getName(), DEFAULT_LANG), SharkUtils.get(HtlOrderStatusEnum.S.getName(), lang))
                .put(SharkUtils.get(HtlOrderStatusEnum.U.getName(), DEFAULT_LANG), SharkUtils.get(HtlOrderStatusEnum.U.getName(), lang))
                .put(SharkUtils.get(HtlOrderStatusEnum.SW.getName(), DEFAULT_LANG), SharkUtils.get(HtlOrderStatusEnum.SW.getName(), lang))
                .put(SharkUtils.get(HtlOrderStatusEnum.SP.getName(), DEFAULT_LANG), SharkUtils.get(HtlOrderStatusEnum.SP.getName(), lang))
                .put(SharkUtils.get(HtlOrderStatusEnum.O.getName(), DEFAULT_LANG), SharkUtils.get(HtlOrderStatusEnum.O.getName(), lang))
                .build();
    }

    /**
     * 由于底表酒店明细中的order_status发生了变化由code变成name了所以要convert一下
     *
     * @param orderstatusList
     * @param
     * @return
     */
    @Override
    protected List<String> convertOrderStatus(List<String> orderstatusList) {
        if (CollectionUtils.isEmpty(orderstatusList)) {
            return orderstatusList;
        }
        List<String> result = new ArrayList<>();
        for (String str : orderstatusList) {
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "P")) {
                // 处理中
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus6", DEFAULT_LANG));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "S")) {
                // 成交
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus1", DEFAULT_LANG));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "C")) {
                // 取消
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus2", DEFAULT_LANG));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "U")) {
                // 修改
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus4", DEFAULT_LANG));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "SW")) {
                // 已提交，待处理
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus5", DEFAULT_LANG));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "SP")) {
                // 已提交，处理中
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus7", DEFAULT_LANG));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "O")) {
                // 其他
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus3", DEFAULT_LANG));
            } else {
                result.add(str);
            }
        }
        return result;
    }

}
