package com.corpgovernment.resource.schedule.geography.gatewayimpl;

import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.redis.cache.RedisUtils;
import com.corpgovernment.resource.core.geography.dto.GeographyAuthResponseDto;
import com.corpgovernment.resource.core.geography.dto.GeographyMappingRequestDto;
import com.corpgovernment.resource.core.geography.dto.GeographyRequestDto;
import com.corpgovernment.resource.core.geography.dto.GeographyResponseDto;
import com.corpgovernment.resource.core.geography.soa.GeographyClient;
import com.corpgovernment.resource.schedule.domain.geography.gateway.GeographyTaskGateway;
import com.corpgovernment.resource.schedule.domain.geography.model.SpecifyGeographyUpdateModel;
import com.corpgovernment.resource.schedule.geography.apollo.GeographyApolloDao;
import com.corpgovernment.resource.schedule.geography.constant.GeographyTypeEnum;
import com.corpgovernment.resource.schedule.geography.mysql.mapper.MsBaseCityMapper;
import com.corpgovernment.resource.schedule.geography.mysql.mapper.MsBaseCountryMapper;
import com.corpgovernment.resource.schedule.geography.mysql.mapper.MsBaseProvinceMapper;
import com.ctrip.corp.obt.async.redis.RedisClientDelegate;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

import static com.corpgovernment.resource.schedule.geography.constant.GeographyTypeEnum.getGeographyEnumByType;

/**
 * <AUTHOR> zhang
 * @date 2023/12/26 12:09
 */
@Slf4j
@Component
public class GeographyTaskGatewayImpl implements GeographyTaskGateway {

    public static final int GEOGRAPHY_ERROR_ID = -1;
    public static final String GEOGRAPHY_PROVINCE_LIST = "geography:province:country:list";
    public static final String GEOGRAPHY_CITY_LIST = "geography:city:country:list";
    public static final String GEOGRAPHY_TRAIN_STATION_LIST = "geography:train:station:country:list";
    public static final String GEOGRAPHY_CORP_BRAND_LIST = "geography:corp:brand:city:list";
    public static final String GEOGRAPHY_METRO_LINE_LIST = "geography:metro:line:city:list";
    public static final String GEOGRAPHY_DOMESTIC_ZONE_LIST = "geography:domestic:zone:city:list";
    public static final String GEOGRAPHY_TIME_ZONE_LIST = "geography:time:zone:city:list";
    public static final String GEOGRAPHY_CITY_LANDMARK_LIST = "geography:city:landmark:city:list";
    public static final String GEOGRAPHY_HOTEL_CITY_LIST = "geography:hotel:city:country:list";
    public static final String GEOGRAPHY_HOTEL_CITY_INDIA_LIST = "geography:hotel:city:india:province:list";
    public static final String GEOGRAPHY_REDIS_CITY_STATUS = "apollo:common:geography:redis:city:update:status";
    public static final String GEOGRAPHY_UPDATE_REDIS_STREAM_KEY = "geography:update:redis:stream";
    private static final int REDIS_CACHE_UPDATE_DURATIONS = 2 * 60;
    private static final int REDIS_LIST_CACHE_POP_DURATIONS = 2;
    /**
     * 同步更新状态：update:有更新
     */
    public static final String GEOGRAPHY_CACHE_UPDATE_STATUS_TYPE = "UPDATE";
    /**
     * 同步更新状态：:
     */
    public static final String GEOGRAPHY_CACHE_FINISH_STATUS_TYPE = "FINISH";

    /**
     * 国家ID Redis存放时间
     */
    private static final long COUNTRY_REDIS_CACHE_DURATIONS = 24 * 60 * 60;
    /**
     * 城市ID Redis存放时间
     */
    private static final long CITY_REDIS_CACHE_DURATIONS = 15 * 24 * 60 * 60;
    /**
     * 印度countryId
     */
    private static final long GEOGRAPHY_INDIA_COUNTRY_ID = 107L;

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private MsBaseCityMapper msBaseCityMapper;
    @Autowired
    private MsBaseCountryMapper msBaseCountryMapper;
    @Autowired
    private MsBaseProvinceMapper msBaseProvinceMapper;
    @Autowired
    private GeographyClient geographyClient;
    @Resource
    private RedisClientDelegate redisClientDelegate;
    @Autowired
    private GeographyApolloDao geographyApolloDao;

    @Override
    public void initRedisGeographyInfo() {
        log.info("begin initRedisGeographyInfo");
        //获取数据
        List<String> ctripCityIdList = msBaseCityMapper.getCtripCityIdList();
        log.info("ID List Push Redis");
        // 酒店品牌 CityIdList
        pushRedisGeographyList(GEOGRAPHY_CORP_BRAND_LIST, ctripCityIdList, CITY_REDIS_CACHE_DURATIONS);
        // 地铁线 CityIdList
        pushRedisGeographyList(GEOGRAPHY_METRO_LINE_LIST, ctripCityIdList, CITY_REDIS_CACHE_DURATIONS);
        // 商业区 CityIdList
        pushRedisGeographyList(GEOGRAPHY_DOMESTIC_ZONE_LIST, ctripCityIdList, CITY_REDIS_CACHE_DURATIONS);
        // 城市地标 CityIdList
        pushRedisGeographyList(GEOGRAPHY_CITY_LANDMARK_LIST, ctripCityIdList, CITY_REDIS_CACHE_DURATIONS);
        // 时区 CityIdList
        pushRedisGeographyList(GEOGRAPHY_TIME_ZONE_LIST, ctripCityIdList, CITY_REDIS_CACHE_DURATIONS);
        log.info("end initRedisGeographyInfo");
    }

    @Override
    public void initRedisGeographyInfoByCountryIds() {
        log.info("begin initRedisGeographyInfoByCountryIds");
        //获取数据
        List<String> ctripCountryIdList = msBaseCountryMapper.getCtripCountryIds();
        //印度城市经纬度特殊处理
        List<String> ctripProvinceIds = msBaseProvinceMapper.getCtripProvinceByCountryId(String.valueOf(GEOGRAPHY_INDIA_COUNTRY_ID));
        log.info("ID List Push CountryIds Redis");
        // 省份 CountryIdList
        pushRedisGeographyList(GEOGRAPHY_PROVINCE_LIST, ctripCountryIdList, COUNTRY_REDIS_CACHE_DURATIONS);
        // 城市 CountryIdList
        pushRedisGeographyList(GEOGRAPHY_CITY_LIST, ctripCountryIdList, COUNTRY_REDIS_CACHE_DURATIONS);
        // 火车站 CountryIdList
        pushRedisGeographyList(GEOGRAPHY_TRAIN_STATION_LIST, ctripCountryIdList, COUNTRY_REDIS_CACHE_DURATIONS);
        // 酒店城市 CountryIdList
        pushRedisGeographyList(GEOGRAPHY_HOTEL_CITY_LIST, ctripCountryIdList, COUNTRY_REDIS_CACHE_DURATIONS);
        // 酒店城市 ProvinceIdList(印度城市经纬度特殊处理)
        pushRedisGeographyList(GEOGRAPHY_HOTEL_CITY_INDIA_LIST, ctripProvinceIds, COUNTRY_REDIS_CACHE_DURATIONS);
        log.info("end initRedisGeographyInfoByCountryIds");
    }

    @Override
    public JSONResult<GeographyResponseDto> executeGeographyError(String param) {
        return geographyClient.clearGeographyErrorRecords();
    }

    @Override
    public JSONResult<GeographyResponseDto> mappingCityInfo(String param) {
        GeographyMappingRequestDto request = JsonUtils.parse(param, GeographyMappingRequestDto.class);
        return geographyClient.mappingGeographyCityInfo(request);
    }

    public JSONResult<GeographyResponseDto> updateGeographyInfoByTypeAndId(SpecifyGeographyUpdateModel specifyTypeAndId) {
        if (Objects.isNull(specifyTypeAndId) || StringUtils.isBlank(specifyTypeAndId.getGeographyType())) {
            return null;
        }
        //获取redis cityId列表和countryId列表
        GeographyRequestDto request = new GeographyRequestDto();
        request.setUpdate(true);
        GeographyTypeEnum geographyEnum = getGeographyEnumByType(specifyTypeAndId.getGeographyType());
        if (Objects.nonNull(geographyEnum)) {
            switch (geographyEnum) {
                case GEOGRAPHY_CTRIP_PROVINCE:
                    request.setCountryId(specifyTypeAndId.getCountryId());
                    geographyClient.pullProvinceInfo(request);
                    break;
                case GEOGRAPHY_CTRIP_CITY:
                    request.setCountryId(specifyTypeAndId.getCountryId());
                    geographyClient.pullGeographyInfo(request);
                    break;
                case GEOGRAPHY_CTRIP_TRAIN_STATION:
                    request.setCountryId(specifyTypeAndId.getCountryId());
                    geographyClient.pullTrainStationInfo(request);
                    break;
                case GEOGRAPHY_CTRIP_CORP_BRAND:
                    request.setCityId(specifyTypeAndId.getCityId());
                    geographyClient.pullCorpBrandInfo(request);
                    break;
                case GEOGRAPHY_CTRIP_DOMESTIC_ZONE:
                    request.setCityId(specifyTypeAndId.getCityId());
                    geographyClient.pullDomesticZoneInfo(request);
                    break;
                case GEOGRAPHY_CTRIP_TIME_ZONE:
                    request.setCityId(specifyTypeAndId.getCityId());
                    geographyClient.pullTimeZoneInfo(request);
                    break;
                case GEOGRAPHY_CTRIP_METRO_LINE:
                    request.setCityId(specifyTypeAndId.getCityId());
                    geographyClient.pullMetroLineInfo(request);
                    break;
                case GEOGRAPHY_CTRIP_CITYMARK:
                    request.setCityId(specifyTypeAndId.getCityId());
                    geographyClient.pullCityLandmarkInfo(request);
                    break;
                case GEOGRAPHY_CTRIP_HOTEL_CITY:
                    request.setCountryId(specifyTypeAndId.getCountryId());
                    request.setProvinceId(specifyTypeAndId.getProvinceId());
                    geographyClient.pullHotelCityInfo(request);
                    break;
                default:
                    log.info("GeographyType传入无效");
            }
        }
        return null;
    }

    public JSONResult<GeographyAuthResponseDto> pullAuthTicket() {
        log.info("geographyClient.pullAuthTicket");
        return geographyClient.pullGeographyAuth();
    }

    public JSONResult<GeographyResponseDto> pullCountryInfo(Boolean update) {
        GeographyRequestDto request = new GeographyRequestDto();
        request.setUpdate(update);
        log.info("geographyClient.pullCountryInfo request :{}", request);
        return geographyClient.pullCountryInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullProvinceInfo(Boolean update) {
        Integer id = popRedisGeographyList(GEOGRAPHY_PROVINCE_LIST, REDIS_LIST_CACHE_POP_DURATIONS);
        if (Objects.isNull(id) || Objects.equals(id, GEOGRAPHY_ERROR_ID)) {
            log.error("pullProvinceInfo failed! id:{}", id);
            return new JSONResult<>();
        }
        //服务调用
        GeographyRequestDto request = new GeographyRequestDto();
        request.setCountryId(id.longValue());
        request.setUpdate(update);
        log.info("geographyClient.pullProvinceInfo request :{}", request);
        return geographyClient.pullProvinceInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullCityInfo(Boolean update) {
        Integer id = popRedisGeographyList(GEOGRAPHY_CITY_LIST, REDIS_LIST_CACHE_POP_DURATIONS);
        if (Objects.isNull(id)) {
            log.error("pullCityInfo failed! id:{}", id);
            return new JSONResult<>();
        }
        if (id == GEOGRAPHY_ERROR_ID) {
            return new JSONResult<>();
        }
        //服务调用
        GeographyRequestDto request = new GeographyRequestDto();
        request.setCountryId(id.longValue());
        request.setUpdate(update);
        log.info("geographyClient.pullCityInfo request :{}", request);
        return geographyClient.pullGeographyInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullAirportInfo(Boolean update) {
        GeographyRequestDto request = new GeographyRequestDto();
        request.setUpdate(update);
        log.info("geographyClient.pullAirportInfo request :{}", request);
        return geographyClient.pullAirPortInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullTrainStationInfo(Boolean update) {
        Integer id = popRedisGeographyList(GEOGRAPHY_TRAIN_STATION_LIST, REDIS_LIST_CACHE_POP_DURATIONS);
        if (Objects.isNull(id) || Objects.equals(id, GEOGRAPHY_ERROR_ID)) {
            log.error("pullTrainStationInfo failed! id:{}", id);
            return new JSONResult<>();
        }
        //服务调用
        GeographyRequestDto request = new GeographyRequestDto();
        request.setCountryId(id.longValue());
        request.setUpdate(update);
        log.info("geographyClient.pullTrainStationInfo request :{}", request);
        return geographyClient.pullTrainStationInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullBusStationInfo(Boolean update) {
        GeographyRequestDto request = new GeographyRequestDto();
        request.setUpdate(update);
        log.info("geographyClient.pullBusStationInfo request :{}", request);
        return geographyClient.pullBusStationInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullDomesticZoneInfo(Boolean update) {
        Integer id = popRedisGeographyList(GEOGRAPHY_DOMESTIC_ZONE_LIST, REDIS_LIST_CACHE_POP_DURATIONS);
        if (Objects.isNull(id) || Objects.equals(id, GEOGRAPHY_ERROR_ID)) {
            log.error("pullDomesticZoneInfo failed! id:{}", id);
            return new JSONResult<>();
        }
        //服务调用
        GeographyRequestDto request = new GeographyRequestDto();
        request.setCityId(id.longValue());
        request.setUpdate(update);
        log.info("geographyClient.pullDomesticZoneInfo request :{}", request);
        return geographyClient.pullDomesticZoneInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullTimeZoneInfo(Boolean update) {
        Integer id = popRedisGeographyList(GEOGRAPHY_TIME_ZONE_LIST, REDIS_LIST_CACHE_POP_DURATIONS);
        if (Objects.isNull(id) || Objects.equals(id, GEOGRAPHY_ERROR_ID)) {
            log.error("pullTimeZoneInfo failed! id:{}", id);
            return new JSONResult<>();
        }
        // 服务调用
        GeographyRequestDto request = new GeographyRequestDto();
        request.setCityId(id.longValue());
        request.setUpdate(update);
        log.info("geographyClient.pullTimeZoneInfo request :{}", JsonUtils.toJsonString(request));
        return geographyClient.pullTimeZoneInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullCorpBrandInfo(Boolean update) {
        Integer id = popRedisGeographyList(GEOGRAPHY_CORP_BRAND_LIST, REDIS_LIST_CACHE_POP_DURATIONS);
        if (Objects.isNull(id) || Objects.equals(id, GEOGRAPHY_ERROR_ID)) {
            log.error("pullCorpBrandInfo failed! id:{}", id);
            return new JSONResult<>();
        }
        //服务调用
        GeographyRequestDto request = new GeographyRequestDto();
        request.setCityId(id.longValue());
        request.setUpdate(update);
        log.info("geographyClient.pullCorpBrandInfo request :{}", request);
        return geographyClient.pullCorpBrandInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullMetroLineInfo(Boolean update) {
        Integer id = popRedisGeographyList(GEOGRAPHY_METRO_LINE_LIST, REDIS_LIST_CACHE_POP_DURATIONS);
        if (Objects.isNull(id) || Objects.equals(id, GEOGRAPHY_ERROR_ID)) {
            log.error("pullMetroLineInfo failed! id:{}", id);
            return new JSONResult<>();
        }
        //服务调用
        GeographyRequestDto request = new GeographyRequestDto();
        request.setCityId(id.longValue());
        request.setUpdate(update);
        log.info("geographyClient.pullMetroLineInfo request :{}", request);
        return geographyClient.pullMetroLineInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullCityLandmarkInfo(Boolean update) {
        Integer id = popRedisGeographyList(GEOGRAPHY_CITY_LANDMARK_LIST, REDIS_LIST_CACHE_POP_DURATIONS);
        if (Objects.isNull(id) || Objects.equals(id, GEOGRAPHY_ERROR_ID)) {
            log.error("pullCityLandmarkInfo failed! id:{}", id);
            return new JSONResult<>();
        }
        //服务调用
        GeographyRequestDto request = new GeographyRequestDto();
        request.setCityId(id.longValue());
        request.setUpdate(update);
        log.info("geographyClient.pullCityLandmarkInfo request :{}", request);
        return geographyClient.pullCityLandmarkInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullHotelCityInfo(Boolean update) {
        Integer countryId = popRedisGeographyList(GEOGRAPHY_HOTEL_CITY_LIST, REDIS_LIST_CACHE_POP_DURATIONS);
        Integer provinceId = null;
        if (Objects.isNull(countryId) || Objects.equals(countryId, GEOGRAPHY_ERROR_ID)) {
            provinceId = popRedisGeographyList(GEOGRAPHY_HOTEL_CITY_INDIA_LIST, REDIS_LIST_CACHE_POP_DURATIONS);
            if (Objects.isNull(provinceId) || Objects.equals(provinceId, GEOGRAPHY_ERROR_ID)){
                log.error("pullHotelCityInfo failed! provinceId:{}", provinceId);
                return new JSONResult<>();
            }
            countryId = (int)GEOGRAPHY_INDIA_COUNTRY_ID;
        }
        //服务调用
        GeographyRequestDto request = new GeographyRequestDto();
        request.setCountryId(countryId.longValue());
        if (Objects.nonNull(provinceId)){
            request.setProvinceId(provinceId.longValue());
        }
        request.setUpdate(update);
        log.info("geographyClient.pullHotelCityInfo request :{}", request);
        return geographyClient.pullHotelCityInfo(request);
    }

    public JSONResult<GeographyResponseDto> pullMeiYaCityInfo(Boolean update) {
        GeographyRequestDto request = new GeographyRequestDto();
        request.setUpdate(update);
        log.info("geographyClient.pullMeiYaCityInfo request :{}", request);
        return geographyClient.pullMeiYaGeographyInfo(request);
    }


    /**
     * 城市和国家ID存入Redis
     */
    private void pushRedisGeographyInfo(List<String> idList, String key, long duration) {
        //存入Redis(忽略租户信息)
        Supplier<Object> supplier = () -> {
            redisUtils.delete(key);
            redisUtils.setList(key, idList, duration);
            return new Object();
        };
        TenantContext.ignoreTenantContext(supplier);
    }

    /**
     * 城市和国家index存入Redis
     */
    private void pushRedisGeographyList(String key, List<String> valueList, long duration) {
        if (StringUtils.isBlank(key) || CollectionUtils.isEmpty(valueList)) {
            return;
        }
        this.pushRedisGeographyInfo(valueList, key, duration);
    }

    /**
     * 获取城市和国家ID
     */
    private Integer popRedisGeographyList(String key, long duration) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        try {
            //存入Redis(忽略租户信息)
            Supplier<String> supplier = new Supplier<String>() {
                @Override
                public String get() {
                    return redisClientDelegate.withListCommandsClient(c -> {
                        return c.leftPop(key);
                    }, String.class);
                }
            };
            String id = TenantContext.ignoreTenantContext(supplier);
            if (StringUtils.isNotBlank(id)) {
                return Integer.parseInt(id);
            }
        } catch (Exception e) {
            log.error("popRedisGeographyList failed! key:{}", key, e);
        }
        return GEOGRAPHY_ERROR_ID;
    }

    private String getGeographyUpdateStatusType(String key) {
        //获取Redis(忽略租户信息)
        Supplier<String> supplier = () -> {
            return redisUtils.getCache(key);
        };
        return TenantContext.ignoreTenantContext(supplier);
    }

    private void updateGeographyUpdateStatusType(String key) {
        //存入Redis(忽略租户信息)
        Supplier<Object> supplier = () -> {
            return redisUtils.setCache(key, GEOGRAPHY_CACHE_FINISH_STATUS_TYPE, REDIS_CACHE_UPDATE_DURATIONS);
        };
        TenantContext.ignoreTenantContext(supplier);
        log.info("基础数据更新状态成功");
    }

    public JSONResult<GeographyResponseDto> updateGeographyInfoByType(String geographyType) {
        GeographyTypeEnum geographyEnum = getGeographyEnumByType(geographyType);
        //获取数据
        List<String> ctripCityIdList = msBaseCityMapper.getCtripCityIdList();
        List<String> ctripCountryIdList = msBaseCountryMapper.getCtripCountryIds();
        if (Objects.nonNull(geographyEnum)) {
            switch (geographyEnum) {
                case GEOGRAPHY_CTRIP_PROVINCE:
                    // 省份 CountryIdList
                    pushRedisGeographyList(GEOGRAPHY_PROVINCE_LIST, ctripCountryIdList, COUNTRY_REDIS_CACHE_DURATIONS);
                    break;
                case GEOGRAPHY_CTRIP_CITY:
                    // 城市 CountryIdList
                    pushRedisGeographyList(GEOGRAPHY_CITY_LIST, ctripCountryIdList, COUNTRY_REDIS_CACHE_DURATIONS);
                    break;
                case GEOGRAPHY_CTRIP_TRAIN_STATION:
                    // 火车站 CountryIdList
                    pushRedisGeographyList(GEOGRAPHY_TRAIN_STATION_LIST, ctripCountryIdList, COUNTRY_REDIS_CACHE_DURATIONS);
                    break;
                case GEOGRAPHY_CTRIP_CORP_BRAND:
                    // 酒店品牌 CityIdList
                    pushRedisGeographyList(GEOGRAPHY_CORP_BRAND_LIST, ctripCityIdList, CITY_REDIS_CACHE_DURATIONS);
                    break;
                case GEOGRAPHY_CTRIP_DOMESTIC_ZONE:
                    // 商业区 CityIdList
                    pushRedisGeographyList(GEOGRAPHY_DOMESTIC_ZONE_LIST, ctripCityIdList, CITY_REDIS_CACHE_DURATIONS);
                    break;
                case GEOGRAPHY_CTRIP_TIME_ZONE:
                    // 时区 CityIdList
                    pushRedisGeographyList(GEOGRAPHY_TIME_ZONE_LIST, ctripCityIdList, CITY_REDIS_CACHE_DURATIONS);
                    break;
                case GEOGRAPHY_CTRIP_METRO_LINE:
                    // 地铁线 CityIdList
                    pushRedisGeographyList(GEOGRAPHY_METRO_LINE_LIST, ctripCityIdList, CITY_REDIS_CACHE_DURATIONS);
                    break;
                case GEOGRAPHY_CTRIP_CITYMARK:
                    // 城市地标 CityIdList
                    pushRedisGeographyList(GEOGRAPHY_CITY_LANDMARK_LIST, ctripCityIdList, CITY_REDIS_CACHE_DURATIONS);
                    break;
                case GEOGRAPHY_CTRIP_HOTEL_CITY:
                    //印度城市经纬度特殊处理
                    List<String> ctripProvinceByCountryId = msBaseProvinceMapper.getCtripProvinceByCountryId(String.valueOf(GEOGRAPHY_INDIA_COUNTRY_ID));
                    // 酒店城市 CountryIdList
                    pushRedisGeographyList(GEOGRAPHY_HOTEL_CITY_LIST, ctripCountryIdList, COUNTRY_REDIS_CACHE_DURATIONS);
                    // 酒店城市 ProvinceIdList(印度城市经纬度特殊处理)
                    pushRedisGeographyList(GEOGRAPHY_HOTEL_CITY_INDIA_LIST, ctripProvinceByCountryId, COUNTRY_REDIS_CACHE_DURATIONS);
                    break;
                default:
                    log.info("GeographyType传入无效");
            }
        }
        return null;
    }
}
