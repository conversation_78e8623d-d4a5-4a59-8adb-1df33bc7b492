package com.corpgovernment.resource.schedule.onlinereport.enums;

/*
 * <AUTHOR>
 *
 * @date 2022/4/6 16:10
 *
 * @Desc 机票rc
 */
public enum FlightRcEnum {
    // 低价
    L("ComplianceMonitor.lowpricerc"),
    // 协议
    H("ComplianceMonitor.pactrc"),
    // 提前
    P("ComplianceMonitor.advancedrc"),
    // 舱等
    C("ComplianceMonitor.cabinrc"),
    // 时间
    T("ComplianceMonitor.timerc"),
    // 时间
    R("index.refundrc"),
    // 折扣rc
    D("ComplianceMonitor.discountrc");

    private String sharkKey;

    FlightRcEnum(String s) {
        this.sharkKey = s;
    }

    public String getSharkKey() {
        return sharkKey;
    }
}
