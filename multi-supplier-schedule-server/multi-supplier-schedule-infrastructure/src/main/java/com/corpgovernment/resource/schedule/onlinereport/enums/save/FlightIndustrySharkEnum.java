package com.corpgovernment.resource.schedule.onlinereport.enums.save;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.save
 * @description:机票行业shark
 * @author: md_wang
 * @create: 2022-08-02 20:00
 **/
public enum FlightIndustrySharkEnum {
    /**
     * 商旅
     */
    CORP("corp", "Index.tmc"),
    /**
     * 行业
     */
    INDUSTRY("industry", "Index.industry"),
    /**
     * 商旅及行业
     */
    CORP_INDUSTRY("corp_industry", "Save.Choice2"),

    /**
     * 航线
     */
    FLIGHT("flight", "Exceltopname.airline"),

    /**
     * 城市
     */
    CITY("city", "Exceltopname.city"),
    /**
     * 贵司数据表现优异，暂无建议。
     */
    NONE_NOTICE("none_notice","Save.FltAys1")
    ;

    private String name;
    private String sharkKey;

    FlightIndustrySharkEnum(String name, String sharkKey) {
        this.name = name;
        this.sharkKey = sharkKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public void setSharkKey(String sharkKey) {
        this.sharkKey = sharkKey;
    }
}
