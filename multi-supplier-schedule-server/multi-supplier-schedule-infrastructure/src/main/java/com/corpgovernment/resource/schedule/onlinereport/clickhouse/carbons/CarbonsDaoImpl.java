package com.corpgovernment.resource.schedule.onlinereport.clickhouse.carbons;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TrendDimensionTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.SearchDeptAndCostcneterDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto.CarBonTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto.CarbonsAndSaveDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-09-21 13:39
 * @desc
 */
@Service
public class CarbonsDaoImpl extends AbstractCommonDao {

    private final static String JOIN_MOM_CORP_DEFAULT =
            " current.corp_corporation = mom.corp_corporation and current.corp_name = mom.corp_name";
    private final static String JOIN_YOY_CORP_DEFAULT =
            " current.corp_corporation = yoy.corp_corporation and current.corp_name = yoy.corp_name";

    private final static String TWO_JOIN_FORMAT_DEFAULT = " %s AND %s";

    private final static String JOIN_MOM_ACCOUNT_DEFAULT =
            " current.account_id = mom.account_id and current.account_name = mom.account_name";

    private final static String JOIN_YOY_ACCOUNT_DEFAULT =
            "  current.account_id = yoy.account_id and current.account_name = yoy.account_name";

    private final static String JOIN_MOM_ACCOUNT_CODE_DEFAULT =
            " current.account_id = mom.account_id and current.account_code = mom.account_code";

    private final static String JOIN_YOY_ACCOUNT_CODE_DEFAULT =
            " current.account_id = yoy.account_id and current.account_code = yoy.account_code";

    private final static String GROUP_SQL_FORMAT_DEFAULT = " %s, %s";

    private final static String SQL_DEFAULT = " corp_corporation, corp_name ";

    private static final String JOIN_DATE_CONDITION_TEMPLATE =
            "" + "subStr(toString(addMonths(parseDateTimeBestEffortOrNull({type}), {duration})), 1, 10)";

    private static final HashMap<String, String> CHAIN_DURATION = new HashMap<String, String>() {
        {
            put("month", "1");
            put("quarter", "3");
            put("half", "6");
        }
    };

    private static final String OVERVIEW_SQL_TEMPLATE = "SELECT \n"
            + "  if(current.date != '', current.date, if(chain.date != '', {join_date_chain}, {join_date_yoy})) as date,\n" +
            // 销售额环比
            "  coalesce(round(CASE WHEN coalesce(chain.totalCarbons, 0) != 0 THEN 1.0 * (coalesce(current.totalCarbons, 0) - coalesce(chain.totalCarbons, 0)) " +
            "/ abs(coalesce(chain.totalCarbons, 0)) END * 100, 2), 0) AS totalCarbonsChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.totalFltCarbons, 0) != 0 THEN 1.0 * (coalesce(current.totalFltCarbons, 0) - coalesce(chain.totalFltCarbons, 0)) " +
            "/ abs(coalesce(chain.totalFltCarbons, 0)) END * 100, 2), 0) AS totalFltCarbonsChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.totalTrainCarbons, 0) != 0 THEN 1.0 * (coalesce(current.totalTrainCarbons, 0) - coalesce(chain.totalTrainCarbons, 0)) " +
            "/ abs(coalesce(chain.totalTrainCarbons, 0)) END * 100, 2), 0) AS totalTrainCarbonsChain,\n"
            + "  coalesce(round(CASE WHEN coalesce(chain.totalCarCarbons, 0) != 0 THEN 1.0 * (coalesce(current.totalCarCarbons, 0) - coalesce(chain.totalCarCarbons, 0)) " +
            "/ abs(coalesce(chain.totalCarCarbons, 0)) END * 100, 2), 0) AS totalCarCarbonsChain,\n"
            // 销售额同比
            + "  coalesce(round(CASE WHEN coalesce(yoy.totalCarbons, 0) != 0 THEN 1.0 * (coalesce(current.totalCarbons, 0) - coalesce(yoy.totalCarbons, 0)) " +
            "/ abs(coalesce(yoy.totalCarbons, 0)) END * 100, 2), 0) AS totalCarbonsYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.totalFltCarbons, 0) != 0 THEN 1.0 * (coalesce(current.totalFltCarbons, 0) - coalesce(yoy.totalFltCarbons, 0)) " +
            "/ abs(coalesce(yoy.totalFltCarbons, 0)) END * 100, 2), 0) AS totalFltCarbonsYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.totalTrainCarbons, 0) != 0 THEN 1.0 * (coalesce(current.totalTrainCarbons, 0) - coalesce(yoy.totalTrainCarbons, 0)) " +
            "/ abs(coalesce(yoy.totalTrainCarbons, 0)) END * 100, 2), 0) AS totalTrainCarbonsYoy,\n"
            + "  coalesce(round(CASE WHEN coalesce(yoy.totalCarCarbons, 0) != 0 THEN 1.0 * (coalesce(current.totalCarCarbons, 0) - coalesce(yoy.totalCarCarbons, 0)) " +
            "/ abs(coalesce(yoy.totalCarCarbons, 0)) END * 100, 2), 0) AS totalCarCarbonsYoy,\n"
            + "  coalesce(current.totalCarbons, 0) as totalCarbons,\n"
            + "  coalesce(current.totalFltCarbons, 0) as totalFltCarbons,\n"
            + "  coalesce(current.totalTrainCarbons, 0) as totalTrainCarbons,\n"
            + "  coalesce(current.totalCarCarbons, 0) as totalCarCarbons\n"
            + "  FROM ({current_sql}) current\n"
            + "FULL JOIN ({chain_sql}) chain on current.date = {join_date_chain} \n"
            + "FULL JOIN ({yoy_sql}) yoy on current.date = {join_date_yoy} ";

    /**
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryCarbons(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String partion = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" select current.sumCarbons as sumCarbons");
        sqlBuilder.append(" ,current.avgCarbons as avgCarbons");
        sqlBuilder.append(" ,mom.sumCarbons as momSumCarbons");
        sqlBuilder.append(" ,yoy.sumCarbons as yoySumCarbons");
        sqlBuilder.append(" from ");
        sqlBuilder.append("(SELECT SUM(coalesce(carbon_emission, 0)/1000) AS sumCarbons\n" +
                "    , CASE WHEN toFloat64(SUM(coalesce(quantity, 0))) != 0 THEN toFloat64(SUM(coalesce(carbon_emission, 0)/1000)) / toFloat64(SUM(coalesce(quantity, 0))) " +
                " ELSE 0 END AS avgCarbons\n" +
                " FROM  olrpt_indexflightdownload_all WHERE ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion,
                isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(" ) current");
        sqlBuilder.append(" cross join (SELECT SUM(coalesce(carbon_emission, 0)/1000) AS sumCarbons FROM olrpt_indexflightdownload_all WHERE ");
        List<String> momTimes = OrpDateTimeUtils.momTime(requestDto.getStartTime(), requestDto.getEndTime());
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, momTimes.get(0), momTimes.get(1), partion,
                isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(" ) mom");
        sqlBuilder.append(" cross join (SELECT SUM(coalesce(carbon_emission, 0)/1000) AS sumCarbons FROM olrpt_indexflightdownload_all WHERE ");
        List<String> yoyTimes = OrpDateTimeUtils.yoyTime(requestDto.getStartTime(), requestDto.getEndTime(), OrpConstants.ONE_YEAR_DAYS);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, yoyTimes.get(0), yoyTimes.get(1), partion,
                isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(") yoy");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryCarbonType(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String partion = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT SUM(coalesce(carbon_emission, 0)/1000) AS sumCarbons, SUM(coalesce(tpms, 0)) AS sumTpms\n" +
                "    , CASE WHEN tpms <= 200 THEN 1 when (tpms >200 and tpms < 1000) then 2 " +
                " when tpms >= 1000 then 3 ELSE 0 END AS carbonType\n" +
                " FROM  olrpt_indexflightdownload_all WHERE ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion,
                isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(" group by carbonType");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 前5部门
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryCarbonsTop5Dept(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, TrendDimensionTypeEnum typeEnum,
                                            AnalysisObjectEnum analysisObjectEnum, Pager pager) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String partion = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        String analysis = convertSqlField(analysisObjectEnum);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT dim");
        sqlBuilder.append(", current.sumCarbons as sumCarbons, current.avgCarbons as avgCarbons" +
                " , round(CASE WHEN toFloat64(total.sumCarbons) !=0 then toFloat64(current.sumCarbons)/toFloat64(total.sumCarbons) ELSE 0 END * 100, 2) as carbonsPercent\n" +
                " FROM (SELECT " + analysis + " AS dim, SUM(coalesce(carbon_emission, 0)/1000) AS sumCarbons\n" +
                "        , CASE WHEN toFloat64(SUM(coalesce(quantity, 0))) != 0 " +
                " THEN toFloat64(SUM(coalesce(carbon_emission, 0)/1000)) / toFloat64(SUM(coalesce(quantity, 0))) ELSE 0 " +
                " END AS avgCarbons\n" +
                "    FROM olrpt_indexflightdownload_all  WHERE ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion,
                isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(String.format(" GROUP BY %s", analysis));
        sqlBuilder.append(String.format(" ORDER BY %s DESC, %s ", typeEnum == TrendDimensionTypeEnum.ONEWAY ? "avgCarbons" : "sumCarbons", analysis));
        sqlBuilder.append(" limit ?, ? ");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        sqlBuilder.append(") current ");
        sqlBuilder.append(" CROSS JOIN ( SELECT SUM(coalesce(carbon_emission, 0)/1000) AS sumCarbons FROM olrpt_indexflightdownload_all WHERE ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion,
                isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(" ) total");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * @param requestDto
     * @param productType
     * @param pager
     * @param analysisObjectEnum
     * @param typeEnum
     * @param startMonthTime     月份开始时间
     * @return
     * @throws Exception
     */
    public List<Map> queryCarbonsDeptDetail(BaseQueryConditionDTO requestDto, String productType, Pager pager, AnalysisObjectEnum analysisObjectEnum,
                                            TrendDimensionTypeEnum typeEnum, String startMonthTime) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String partion = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        JoinCondition joinCondition = joinCondition(analysisObjectEnum);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(String.format(" SELECT %s", joinCondition.resultFields));
        sqlBuilder.append(", current.sumCarbons as YEAR_TOTAL_CARBONS");
        sqlBuilder.append(", current.avgCarbons as YEAR_AVG_CARBONS");
        sqlBuilder.append(", round((CASE WHEN toFloat64(total.sumCarbons) !=0 " +
                " then toFloat64(current.sumCarbons)/toFloat64(total.sumCarbons) ELSE 0 END) * 100, 2) as YEAR_CARBONS_PERCENT");
        sqlBuilder.append(", current.monthSumCarbons as MONTH_TOTAL_CARBONS");
        sqlBuilder.append(", current.avgMonthCarbons as MONTH_AVG_CARBONS");
        sqlBuilder.append(", round(CASE WHEN toFloat64(total.monthSumCarbons) !=0 " +
                " then toFloat64(current.monthSumCarbons)/toFloat64(total.monthSumCarbons) ELSE 0 END * 100, 2) as MONTH_CARBONS_PERCENT");
        sqlBuilder.append(" from (");
        sqlBuilder.append(String.format(" SELECT %s", joinCondition.groupFields));
        sqlBuilder.append(" , SUM(coalesce(carbon_emission, 0)/1000) AS sumCarbons");
        sqlBuilder.append(" , CASE WHEN toFloat64(SUM(coalesce(quantity, 0))) != 0 " +
                " THEN toFloat64(SUM(coalesce(carbon_emission, 0)/1000)) / toFloat64(SUM(coalesce(quantity, 0))) ELSE 0 END AS avgCarbons ");
        sqlBuilder.append(getMonthCarbonsSql(startMonthTime, requestDto.getEndTime(), parmList, isBookCaliber));
        sqlBuilder.append(getAvgMonthCarbonsSql(startMonthTime, requestDto.getEndTime(), parmList, isBookCaliber));
        sqlBuilder.append(" FROM olrpt_indexflightdownload_all  WHERE ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion,
                isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(String.format(" GROUP BY %s", joinCondition.groupFields));
        sqlBuilder.append(String.format(" ORDER BY %s DESC, %s ", typeEnum == TrendDimensionTypeEnum.ONEWAY ? "avgMonthCarbons" : "monthSumCarbons", joinCondition.groupFields));
        sqlBuilder.append(" limit ?, ? ");
        parmList.add((pager.pageIndex - 1) * pager.pageSize);
        parmList.add(pager.pageSize);
        sqlBuilder.append(" ) current ");
        sqlBuilder.append(" CROSS JOIN ( SELECT SUM(coalesce(carbon_emission, 0)/1000) AS sumCarbons ");
        sqlBuilder.append(getMonthCarbonsSql(startMonthTime, requestDto.getEndTime(), parmList, isBookCaliber));
        sqlBuilder.append(" FROM olrpt_indexflightdownload_all WHERE");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion,
                isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(" ) total");
        // 查询clickhouse
        return commonList(sqlBuilder.toString(), parmList);
    }

    /**
     * @param requestDto
     * @return
     * @throws Exception
     */
    public List<CarbonsAndSaveDTO> queryCarbonSave(BaseQueryConditionDTO requestDto, String startTime, String endTime) throws Exception {
        List<Object> parmList = new ArrayList<>();
        ClickHouseTable clickHouseTable = this.getTableByStaticalCaliber(requestDto.getStatisticalCaliber());
        String partion = queryPartition(clickHouseTable);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT  ");
        sqlBuilder.append("SUM(coalesce(carbon_save_flt, 0)/1000) AS sumFltCarbonSave\n" +
                "    , SUM(coalesce(green_flightno_count, 0)) AS sumFltGreenNoCount\n" +
                "    , SUM(coalesce(flightno_count, 0)) AS sumFltNoCount\n" +
                "    , SUM(coalesce(median_carbon_emission_flt, 0)/1000) AS sumFltMedianCarbons\n" +
                "    , SUM(coalesce(carbon_save_trn, 0)/1000) AS sumTrainCarbonSave\n" +
                "    , SUM(coalesce(median_carbon_emission_trn, 0)/1000) AS sumTrainMedianCarbons\n" +
                "    , SUM(coalesce(carbon_save_car, 0)/1000) AS sumCarCarbonSave\n" +
                "    , SUM(coalesce(green_car_quantity, 0)) AS sumCarGreenQuantity\n" +
                "    , SUM(coalesce(quantity_car, 0)) AS sumCarQuantity\n" +
                "    , SUM(coalesce(median_carbon_emission_car, 0)/1000) AS sumCarMedianCarbons ");
        sqlBuilder.append(",SUM(coalesce(carbon_emission_flt, 0)/1000) AS sumFltCarbons, " +
                "SUM(coalesce(carbon_emission_trn, 0)/1000) AS sumTrainCarbons, SUM(coalesce(carbon_emission_car, 0)/1000) AS sumCarCarbons ");
        sqlBuilder.append(", SUM(coalesce(normal_distance, 0)) AS sumNormalDistance");
        sqlBuilder.append(", SUM(coalesce(green_train_count, 0)) AS sumTrainGreenQuantity");
        sqlBuilder.append(", SUM(coalesce(quantity_train, 0)) AS sumTrainQuantity");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, startTime, endTime, partion, OrpConstants.REPORT_DATE));
        // 查询clickhouse
        return commonList(CarbonsAndSaveDTO.class, sqlBuilder.toString(), parmList);
    }

    /**
     * @param startTime
     * @param endTime
     * @param dataTypeEnum
     * @param industryList
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @return
     * @throws Exception
     */
    
    public List<CarbonsAndSaveDTO> queryCarbonSaveCorpAndIndustry(
            String startTime, String endTime, DataTypeEnum dataTypeEnum, List<String> industryList, String statisticalCaliber,
            String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        List<Object> parmList = new ArrayList<>();
        ClickHouseTable clickHouseTable = this.getTableByStaticalCaliber(statisticalCaliber);
        String partition = queryPartition(clickHouseTable);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT  ");
        sqlBuilder.append("SUM(coalesce(carbon_save_flt, 0)/1000) AS sumFltCarbonSave\n" +
                "    , SUM(coalesce(green_flightno_count, 0)) AS sumFltGreenNoCount\n" +
                "    , SUM(coalesce(flightno_count, 0)) AS sumFltNoCount\n" +
                "    , SUM(coalesce(median_carbon_emission_flt, 0)/1000) AS sumFltMedianCarbons\n" +
                "    , SUM(coalesce(carbon_save_trn, 0)/1000) AS sumTrainCarbonSave\n" +
                "    , SUM(coalesce(median_carbon_emission_trn, 0)/1000) AS sumTrainMedianCarbons\n" +
                "    , SUM(coalesce(carbon_save_car, 0)/1000) AS sumCarCarbonSave\n" +
                "    , SUM(coalesce(green_car_quantity, 0)) AS sumCarGreenQuantity\n" +
                "    , SUM(coalesce(quantity_car, 0)) AS sumCarQuantity\n" +
                "    , SUM(coalesce(median_carbon_emission_car, 0)/1000) AS sumCarMedianCarbons ");
        sqlBuilder.append(",SUM(coalesce(carbon_emission_flt, 0)/1000) AS sumFltCarbons, " +
                "SUM(coalesce(carbon_emission_trn, 0)/1000) AS sumTrainCarbons, SUM(coalesce(carbon_emission_car, 0)/1000) AS sumCarCarbons ");
        sqlBuilder.append(", SUM(coalesce(normal_distance, 0)) AS sumNormalDistance");
        sqlBuilder.append(", SUM(coalesce(green_train_count, 0)) AS sumTrainGreenQuantity");
        sqlBuilder.append(", SUM(coalesce(quantity_train, 0)) AS sumTrainQuantity");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            // download 表
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, partition, parmList, OrpConstants.REPORT_DATE));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            // detail 表
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList, partition));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        // 查询clickhouse
        return commonList(CarbonsAndSaveDTO.class, sqlBuilder.toString(), parmList);
    }

    /**
     * @param analysisObjectEnum
     * @param baseQueryConditionDto
     * @param pager
     * @param uids                  出行人uid
     * @param userNames             出行人姓名
     * @param eids                  出行人员工编号
     * @return
     * @throws Exception
     */
    public List<Map> queryTopDeptDetail(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, Pager pager,
                                        List<String> uids, List<String> userNames, List<String> eids) throws Exception {
        List<Object> paramList = new ArrayList<>();
        String dateField = "report_date";
        ClickHouseTable clickHouseTable = this.getTableByStaticalCaliber(baseQueryConditionDto.getStatisticalCaliber());
        String statical = "coalesce(current.totalCarbons, 0) as TOTAL_CARBONS, \n" +
                "round(CASE WHEN toFloat64(coalesce(yoy.totalCarbons, 0)) != 0 THEN 1.0 * (coalesce(current.totalCarbons, 0) - coalesce(yoy.totalCarbons, 0)) \n" +
                "            / abs(toFloat64(coalesce(yoy.totalCarbons, 0))) END * 100, 2) as YOY,\n" +
                "round(CASE WHEN toFloat64(coalesce(mom.totalCarbons, 0)) != 0 THEN 1.0 * (coalesce(current.totalCarbons, 0) - coalesce(mom.totalCarbons, 0)) \n" +
                "/ abs(toFloat64(coalesce(mom.totalCarbons, 0))) END * 100, 2) as MOM,\n" +
                "coalesce(totalCarbonSave, 0) as TOTAL_CARBON_SAVE, \n" +
                "coalesce(totalSaveRate, 0) as CARBON_SAVE_RATE, \n" +
                "coalesce(totalFltCarbons, 0) as FLT_CARBONS, \n" +
                "coalesce(totalFltQuantity, 0) as FLT_QUANTITY, \n" +
                "coalesce(totalFltMiles, 0) as FLT_MILES, \n" +
                "coalesce(totalFltCarbonSave, 0) as FLT_CARBON_SAVE, \n" +
                "coalesce(fltSaveRate, 0) as FLT_CARBON_SAVERATE, \n" +
                "coalesce(totalTrainCarbons, 0) as TRAIN_CARBONS, \n" +
                "coalesce(totalTrainCarbonSave, 0) as TRAIN_CARBON_SAVE, \n" +
                "coalesce(trainSaveRate, 0) as TRAIN_CARBON_SAVE_RATE, \n" +
                "coalesce(totalCarCarbons, 0) as CAR_CARBONS, \n" +
                "coalesce(totalCarOrderCount, 0) as CAR_ORDER_COUNT, \n" +
                "coalesce(totalCarGreenOrderCount, 0) as CAR_GREEN_ORDER_COUNT, \n" +
                "coalesce(carGreenOrderPercentage, 0) as CAR_GREEN_ORDER_COUNT_PERCENTAGE";
        JoinCondition biz = joinCondition(analysisObjectEnum);
        StringBuilder baseSql = new StringBuilder(String.format("SELECT %s  FROM ", biz.getResultFields() + OrpConstants.COMMA + statical));
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String partion = queryPartition(clickHouseTable);
        String currentCondition = BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, paramList, startTime, endTime, partion, dateField);
        String specialCondition = BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("uid", uids, paramList) +
                BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("user_name", userNames, paramList) +
                BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("employee_id", eids, paramList);
        String baseStatical = "sum(carbon_emission_flt + carbon_emission_trn + carbon_emission_car)/1000 AS totalCarbons" +
                "    , sum(carbon_save_flt + carbon_save_trn + carbon_save_car)/1000 AS totalCarbonSave\n" +
                "    , sum(carbon_emission_flt)/1000 AS totalFltCarbons, sum(carbon_save_flt)/1000 AS totalFltCarbonSave\n" +
                "    , sum(carbon_emission_trn)/1000 AS totalTrainCarbons, sum(carbon_save_trn)/1000 AS totalTrainCarbonSave\n" +
                "    , sum(carbon_emission_car)/1000 AS totalCarCarbons, sum(carbon_save_car)/1000 AS totalCarCarbonSave\n" +
                "    , sum(quantity_flt) AS totalFltQuantity, sum(tpms) AS totalFltMiles\n" +
                "    , sum(quantity_car) AS totalCarOrderCount, sum(green_car_quantity) AS totalCarGreenOrderCount,\n" +
                "     round(CASE WHEN sum(carbon_emission_flt + carbon_emission_trn + carbon_emission_car) != 0 THEN 1.0 * " +
                "sum(carbon_save_flt + carbon_save_trn + carbon_save_car) \n" +
                "            / abs(sum(median_carbon_emission_flt + median_carbon_emission_trn + median_carbon_emission_car)) END * 100, 2) as totalSaveRate,\n" +
                "    round(CASE WHEN sum(coalesce(median_carbon_emission_flt, 0)) != 0 THEN 1.0 * sum(coalesce(carbon_save_flt, 0)) \n" +
                "            / abs(sum(coalesce(median_carbon_emission_flt, 0))) END * 100, 2) as fltSaveRate,\n" +
                "            round(CASE WHEN sum(coalesce(median_carbon_emission_trn, 0)) != 0 THEN 1.0 * sum(coalesce(carbon_save_trn, 0)) \n" +
                "            / abs(sum(coalesce(median_carbon_emission_trn, 0))) END * 100, 2) as trainSaveRate,\n" +
                "            round(CASE WHEN sum(coalesce(quantity_car, 0)) != 0 THEN 1.0 * sum(coalesce(green_car_quantity, 0)) \n" +
                "            / abs(sum(coalesce(quantity_car, 0))) END * 100, 2) as carGreenOrderPercentage";

        baseSql.append(String.format("(SELECT %s ,%s FROM %s  WHERE %s %s GROUP BY %s %s) %s",
                biz.getGroupFields(), baseStatical, clickHouseTable.getTable(), currentCondition + specialCondition, StringUtils.trimToEmpty(biz.getSpecialCondition()),
                biz.getGroupFields(), String.format(" order by totalCarbons desc, %s desc limit ?, ?", biz.getOrderByFields()), " current"));
        paramList.add(pager.pageIndex * pager.pageSize);
        paramList.add(pager.pageSize);
        // 同比去年
        List<String> yoyTimes = OrpDateTimeUtils.yoyTime(startTime, endTime, OrpConstants.ONE_YEAR_DAYS);
        String yoyCondition =
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, paramList, yoyTimes.get(0), yoyTimes.get(1), partion, dateField);
        baseSql.append(String.format(" LEFT JOIN (SELECT %s, sum(carbon_emission_flt + carbon_emission_trn + carbon_emission_car) / 1000 as totalCarbons " +
                        "FROM %s  WHERE %s  GROUP BY %s) %s ON  %s",
                biz.getGroupFields(), clickHouseTable.getTable(), yoyCondition, biz.getGroupFields(), "yoy", biz.getCurrentJoinYoy()));
        // 环比
        List<String> momTimes = OrpDateTimeUtils.momTime(startTime, endTime);
        String momCondition = BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, paramList,
                momTimes.get(0), momTimes.get(1), partion, dateField);

        baseSql.append(String.format(" LEFT JOIN (SELECT %s, sum(carbon_emission_flt + carbon_emission_trn + carbon_emission_car) / 1000 as totalCarbons FROM %s  " +
                        " WHERE %s  GROUP BY %s) %s ON  %s",
                biz.getGroupFields(), clickHouseTable.getTable(), momCondition, biz.getGroupFields(), "mom", biz.getCurrentJoinMom()));
        return commonList(baseSql.toString(), paramList);
    }

    public Integer count(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto,
                         List<String> uids, List<String> userNames, List<String> eids) throws Exception {
        List<Object> paramList = new ArrayList<>();
        ClickHouseTable clickHouseTable = this.getTableByStaticalCaliber(baseQueryConditionDto.getStatisticalCaliber());
        JoinCondition biz = joinCondition(analysisObjectEnum);
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String partion = queryPartition(clickHouseTable);
        String currentCondition = BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, paramList, startTime, endTime, partion, "report_date");
        String specialCondition = BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("uid", uids, paramList) +
                BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("user_name", userNames, paramList) +
                BaseConditionPrebuilder.buildListConditionWtihAndMustAndCase("employee_id", eids, paramList);
        String sql = String.format(
                "select count(1) as countAll from(SELECT 1  FROM %s  WHERE %s %s GROUP by %s )",
                clickHouseTable.getTable(), currentCondition + specialCondition, StringUtils.trimToEmpty(biz.getSpecialCondition()), biz.groupFields);
        return commonCount(sql, paramList);
    }

    public List<CarBonTrendDTO> overviewAccTrend(OnlineTrendRequestDto request) throws Exception {
        List<Object> paramList = new ArrayList<>();
        ClickHouseTable clickHouseTable = this.getTableByStaticalCaliber(request.getBaseQueryCondition().getStatisticalCaliber());
        // 整体与分产线 - 月/季/半年 - 日期
        String group_date = "";
        switch (request.getDateDimension()) {
            case "month":
                group_date = "firstday_of_month";
                break;
            case "half":
                group_date = "firstday_of_halfyear";
                break;
            case "quarter":
                group_date = "firstday_of_quarter";
                break;
            default:
                break;
        }
        String partition = queryPartition(clickHouseTable);
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("select ").append(group_date).append(" as date, ");
        stringBuilder.append("sum(carbon_emission_flt + carbon_emission_trn + carbon_emission_car)/1000 as totalCarbons, ");
        stringBuilder.append("sum(carbon_emission_flt)/1000 as totalFltCarbons, ");
        stringBuilder.append("sum(carbon_emission_trn)/1000 as totalTrainCarbons, ");
        stringBuilder.append("sum(carbon_emission_car)/1000 as totalCarCarbons ");
        stringBuilder.append("from ");
        stringBuilder.append(clickHouseTable.getTable());
        stringBuilder.append(" where d = '").append(partition).append("'");
        stringBuilder.append(BaseConditionPrebuilder.buildPreSql(request.getBaseQueryCondition(), paramList));
        stringBuilder.append(" group by ").append(group_date);
        return commonList(CarBonTrendDTO.class, stringBuilder.toString(), paramList);
    }

    /**
     * 查询 在线报告概况-消费金额趋势数据 - 概览 （bu + 字段名 -> 数据聚合维度 日期聚合 -> 数据聚合粒度 同环比)
     *
     * @param request
     * @return
     * @throws Exception
     */
    public List<CarBonTrendDTO> queryOnlineReportCurrentOverviewTrend(OnlineTrendRequestDto request)
            throws Exception {
        List<Object> paramList = new ArrayList<>();
        String sql = buildCurrentOverViewSql(request,
                overviewTrend(request.getBaseQueryCondition().getStatisticalCaliber(), paramList).toString()
                , OVERVIEW_SQL_TEMPLATE, paramList);
        return commonList(CarBonTrendDTO.class, sql, paramList);
    }

    /**
     * @param requestDto
     * @param productType
     * @param analysisObjectEnum
     * @return
     * @throws Exception
     */
    public Integer countCarbonsDeptDetail(BaseQueryConditionDTO requestDto, String productType, AnalysisObjectEnum analysisObjectEnum) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        List<Object> parmList = new ArrayList<>();
        String partion = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        JoinCondition joinCondition = joinCondition(analysisObjectEnum);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT count(1) as countAll");
        sqlBuilder.append(" from (");
        sqlBuilder.append(" SELECT count(1)");
        sqlBuilder.append("FROM olrpt_indexflightdownload_all  WHERE ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion,
                isBookCaliber ? OrpConstants.ORDERDT : OrpConstants.REPORT_DATE));
        sqlBuilder.append(getFlightClassConditionWithAudited(productType));
        sqlBuilder.append(String.format(" GROUP BY %s", joinCondition.groupFields));
        sqlBuilder.append(" ) current ");
        // 查询clickhouse
        return commonCount(sqlBuilder.toString(), parmList);
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinCondition(AnalysisObjectEnum analysisObjectEnum) {
        if (ConfigUtils.getBoolean("custom_dim_open", false)){
            return joinConditionCustom(analysisObjectEnum);
        }else {
            return joinConditionDefault(analysisObjectEnum);
        }
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionDefault(AnalysisObjectEnum analysisObjectEnum) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        biz.setSpecialCondition(StringUtils.EMPTY);
        switch (analysisObjectEnum) {
            case CORP:
                biz.setResultFields("current.corp_name as CORP")
                        .setGroupFields(" corp_corporation, corp_name")
                        .setOrderByFields("corp_corporation, corp_name")
                        .setCurrentJoinMom(JOIN_MOM_CORP_DEFAULT).setCurrentJoinYoy(JOIN_YOY_CORP_DEFAULT);
                break;
            case ACCOUNT:
                biz = biz.setResultFields("current.corp_name as CORP, current.account_name as ACCOUNT")
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_name"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_name ")
                        .setCurrentJoinMom(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_ACCOUNT_DEFAULT))
                        .setCurrentJoinYoy(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_ACCOUNT_DEFAULT));
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("current.corp_name as CORP, current.account_code as ACCOUNTCODE")
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_code"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_code ")
                        .setCurrentJoinMom(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_ACCOUNT_CODE_DEFAULT))
                        .setCurrentJoinYoy(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_ACCOUNT_CODE_DEFAULT));
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase();
                break;
            case COSTCENTER1:
                analysisObject = "cost_center1";
                break;
            case COSTCENTER2:
                analysisObject = "cost_center2";
                break;
            case COSTCENTER3:
                analysisObject = "cost_center3";
                break;
            case COSTCENTER4:
                analysisObject = "cost_center4";
                break;
            case COSTCENTER5:
                analysisObject = "cost_center5";
                break;
            case COSTCENTER6:
                analysisObject = "cost_center6";
                break;
            case UID:
                biz = biz.setResultFields("current.uid as UID, current.user_name as NAME, current.employee_id as EID ")
                        .setGroupFields("uid, user_name, employee_id")
                        .setOrderByFields("uid, user_name, employee_id ")
                        .setCurrentJoinMom("current.uid = mom.uid and current.user_name = mom.user_name and current.employee_id = mom.employee_id")
                        .setCurrentJoinYoy("current.uid = yoy.uid and current.user_name = yoy.user_name and current.employee_id = yoy.employee_id")
                        .setSpecialCondition(" and coalesce(uid, '') != '' ");
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject)) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
            biz = biz
                    .setResultFields("current.corp_name as CORP, case when (current." + analysisObject + " is null or current." + analysisObject + " = '') " +
                            "then '" + other + "' else current." + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, analysisObject))
                    .setCurrentJoinMom(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT,
                            String.format("current.%s = mom.%s", analysisObject, analysisObject)))
                    .setCurrentJoinYoy(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT,
                            String.format("current.%s = yoy.%s", analysisObject, analysisObject)))
                    .setOrderByFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, analysisObject));

        }
        return biz;
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionCustom(AnalysisObjectEnum analysisObjectEnum) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        String analysisObjectAlias = null;
        biz.setSpecialCondition(StringUtils.EMPTY);
        switch (analysisObjectEnum) {
            case CORP:
                biz.setResultFields("current.corp_name as CORP")
                        .setGroupFields(" corp_corporation, corp_name")
                        .setOrderByFields("corp_corporation, corp_name")
                        .setCurrentJoinMom(JOIN_MOM_CORP_DEFAULT).setCurrentJoinYoy(JOIN_YOY_CORP_DEFAULT);
                break;
            case ACCOUNT:
                biz = biz.setResultFields("current.corp_name as CORP, current.account_name as ACCOUNT")
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_name"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_name ")
                        .setCurrentJoinMom(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_ACCOUNT_DEFAULT))
                        .setCurrentJoinYoy(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_ACCOUNT_DEFAULT));
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("current.corp_name as CORP, current.account_code as ACCOUNTCODE")
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_code"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_code ")
                        .setCurrentJoinMom(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_ACCOUNT_CODE_DEFAULT))
                        .setCurrentJoinYoy(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_ACCOUNT_CODE_DEFAULT));
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase().concat("_custom");
                break;
            case COSTCENTER1:
                analysisObject = "costcenter1_custom";
                break;
            case COSTCENTER2:
                analysisObject = "costcenter2_custom";
                break;
            case COSTCENTER3:
                analysisObject = "costcenter3_custom";
                break;
            case COSTCENTER4:
                analysisObject = "costcenter4_custom";
                break;
            case COSTCENTER5:
                analysisObject = "costcenter5_custom";
                break;
            case COSTCENTER6:
                analysisObject = "costcenter6_custom";
                break;
            case UID:
                biz = biz.setResultFields("current.uid as UID, current.user_name as NAME, current.employee_id as EID ")
                        .setGroupFields("uid, user_name, employee_id")
                        .setOrderByFields("uid, user_name, employee_id ")
                        .setCurrentJoinMom("current.uid = mom.uid and current.user_name = mom.user_name and current.employee_id = mom.employee_id")
                        .setCurrentJoinYoy("current.uid = yoy.uid and current.user_name = yoy.user_name and current.employee_id = yoy.employee_id")
                        .setSpecialCondition(" and coalesce(uid, '') != '' ");
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject)) {
            String other = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.other");
            biz = biz
                    .setResultFields("current.corp_name as CORP, case when (current." + analysisObject + " is null or current." + analysisObject + " = '') " +
                            "then '" + other + "' else current." + analysisObject + " end as " + analysisObjectEnum.toString())
                    .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, analysisObject))
                    .setCurrentJoinMom(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT,
                            String.format("current.%s = mom.%s", analysisObject, analysisObject)))
                    .setCurrentJoinYoy(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT,
                            String.format("current.%s = yoy.%s", analysisObject, analysisObject)))
                    .setOrderByFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, analysisObject));

        }
        return biz;
    }

    private String getMonthCarbonsSql(String startTime, String endTime, List<Object> paramList, boolean isBookCaliber) {
        paramList.add(startTime);
        paramList.add(endTime);
        if (isBookCaliber) {
            return " , SUM(CASE WHEN orderdt >= ? and orderdt <= ? THEN coalesce(carbon_emission, 0)/1000 ELSE 0 END) AS monthSumCarbons";
        } else {
            return " , SUM(CASE WHEN report_date >= ? and report_date <= ? THEN coalesce(carbon_emission, 0)/1000 ELSE 0 END) AS monthSumCarbons";
        }
    }

    private String getAvgMonthCarbonsSql(String startTime, String endTime, List<Object> paramList, boolean isBookCaliber) {
        paramList.add(startTime);
        paramList.add(endTime);
        paramList.add(startTime);
        paramList.add(endTime);
        paramList.add(startTime);
        paramList.add(endTime);
        if (isBookCaliber) {
            return " , CASE WHEN toFloat64(SUM(CASE WHEN orderdt >= ? and orderdt <= ? THEN coalesce(quantity, 0) ELSE 0 END)) != 0 THEN \n" +
                    "        toFloat64(SUM(CASE WHEN orderdt >= ? and orderdt <= ? THEN coalesce(carbon_emission, 0)/1000 ELSE 0 END)) / \n" +
                    "        toFloat64(SUM(CASE WHEN orderdt >= ? and orderdt <= ? THEN coalesce(quantity, 0) ELSE 0 END)) ELSE 0 END AS avgMonthCarbons";
        } else {
            return " , CASE WHEN toFloat64(SUM(CASE WHEN report_date >= ? and report_date <= ? THEN coalesce(quantity, 0) ELSE 0 END)) != 0 THEN \n" +
                    "        toFloat64(SUM(CASE WHEN report_date >= ? and report_date <= ? THEN coalesce(carbon_emission, 0)/1000 ELSE 0 END)) / \n" +
                    "        toFloat64(SUM(CASE WHEN report_date >= ? and report_date <= ? THEN coalesce(quantity, 0) ELSE 0 END)) ELSE 0 END AS avgMonthCarbons";
        }
    }

    private String convertSqlField(AnalysisObjectEnum analysisObjectEnum) {
        String sqlField = " corp_corporation, corp_name ";
        switch (analysisObjectEnum) {
            case CORP:
                sqlField = " corp_corporation, corp_name ";
                break;
            case ACCOUNT:
                sqlField = "account_id, account_name";
                break;
            case ACCOUNTCODE:
                sqlField = "account_id, account_code";
                break;
            case DEPT1:
                sqlField = "dept1";
                break;
            case DEPT2:
                sqlField = "dept2";
                break;
            case DEPT3:
                sqlField = "dept3";
                break;
            case DEPT4:
                sqlField = "dept4";
                break;
            case DEPT5:
                sqlField = "dept5";
                break;
            case DEPT6:
                sqlField = "dept6";
                break;
            case DEPT7:
                sqlField = "dept7";
                break;
            case DEPT8:
                sqlField = "dept8";
                break;
            case DEPT9:
                sqlField = "dept9";
                break;
            case DEPT10:
                sqlField = "dept10";
                break;
            case COSTCENTER1:
                sqlField = "cost_center1";
                break;
            case COSTCENTER2:
                sqlField = "cost_center2";
                break;
            case COSTCENTER3:
                sqlField = "cost_center3";
                break;
            case COSTCENTER4:
                sqlField = "cost_center4";
                break;
            case COSTCENTER5:
                sqlField = "cost_center5";
                break;
            case COSTCENTER6:
                sqlField = "cost_center6";
                break;
            default:
        }
        return sqlField;
    }


    @Data
    @Accessors(chain = true)
    class JoinCondition {
        private String resultFields;

        private String groupFields;

        private String currentJoinMom;

        private String currentJoinYoy;

        private String orderByFields;

        private String specialCondition;
    }

    private StringBuilder overviewTrend(String statisticalCaliber, List<Object> paramList) {
        ClickHouseTable clickHouseTable = this.getTableByStaticalCaliber(statisticalCaliber);
        // 整体与分产线 - 月/季/半年 - 日期
        String partition = queryPartition(clickHouseTable);
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("select {group_date} as date, ");
        stringBuilder.append("sum(carbon_emission_flt + carbon_emission_trn + carbon_emission_car)/1000 as totalCarbons, ");
        stringBuilder.append("sum(carbon_emission_flt)/1000 as totalFltCarbons, ");
        stringBuilder.append("sum(carbon_emission_trn)/1000 as totalTrainCarbons, ");
        stringBuilder.append("sum(carbon_emission_car)/1000 as totalCarCarbons ");
        stringBuilder.append("from ");
        stringBuilder.append(clickHouseTable.getTable());
        stringBuilder.append(" where d = ?").append(" and ");
        paramList.add(partition);
        stringBuilder.append("report_date >= {start} and report_date <= {end} and ({scope_condition})  ");
        stringBuilder.append("group by {group_date}");
        return stringBuilder;
    }

    public String buildCurrentOverViewSql(OnlineTrendRequestDto requestDto, String subSqlTemplate, String sqlTemplate
            , List<Object> paramList) {
        String dim = "''";
        String currentSubSql = conditionWrapAsSubSql(requestDto.getDateDimension(), requestDto.getStartTime(),
                requestDto.getEndTime(), subSqlTemplate, requestDto.getBaseQueryCondition(), paramList);
        String chainSubSql = conditionWrapAsSubSql(requestDto.getDateDimension(), requestDto.getChainStartTime(),
                requestDto.getChainEndTime(), subSqlTemplate, requestDto.getBaseQueryCondition(), paramList);
        String yoySubSql = conditionWrapAsSubSql(requestDto.getDateDimension(), requestDto.getYoyStartTime(),
                requestDto.getYoyEndTime(), subSqlTemplate, requestDto.getBaseQueryCondition(), paramList);
        String overviewSql = org.apache.commons.lang.StringUtils.replace(sqlTemplate, "{current_sql}", currentSubSql);
        overviewSql = org.apache.commons.lang.StringUtils.replace(overviewSql, "{chain_sql}", chainSubSql);
        overviewSql = org.apache.commons.lang.StringUtils.replace(overviewSql, "{yoy_sql}", yoySubSql);

        String chainJoin = org.apache.commons.lang.StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "chain.date");
        chainJoin = org.apache.commons.lang.StringUtils.replace(chainJoin, "{duration}", CHAIN_DURATION.get(requestDto.getDateDimension()));
        String yoyJoin = org.apache.commons.lang.StringUtils.replace(JOIN_DATE_CONDITION_TEMPLATE, "{type}", "yoy.date");
        yoyJoin = org.apache.commons.lang.StringUtils.replace(yoyJoin, "{duration}", String.valueOf(requestDto.getYoyDuration()));

        overviewSql = org.apache.commons.lang.StringUtils.replace(overviewSql, "{join_date_chain}", chainJoin);
        overviewSql = org.apache.commons.lang.StringUtils.replace(overviewSql, "{join_date_yoy}", yoyJoin);
        return overviewSql;
    }

    private String conditionWrapAsSubSql(String dateDimension, String start, String end, String sqlTemplate,
                                         BaseQueryConditionDTO baseQueryCondition, List<Object> paramList) {
         /*
         group_date group_dim
         start end chain_start chain_end yoy_start yoy_end
         scope_condition
         */
        String group_date = "";
        switch (dateDimension) {
            case "month":
                group_date = "firstday_of_month";
                break;
            case "half":
                group_date = "firstday_of_halfyear";
                break;
            case "quarter":
                group_date = "firstday_of_quarter";
                break;
            default:
                break;
        }
        String scope_condition = buildScopeFilter(baseQueryCondition, paramList);
        sqlTemplate = sqlTemplate.replace("{group_date}", group_date)
                .replace("{scope_condition}", scope_condition).replace("{start}", "?").replace("{end}", "?");
        paramList.add(start);
        paramList.add(end);
        sqlTemplate = sqlTemplate.replace("{group_date}", group_date).replace("{scope_condition}", scope_condition);
        return sqlTemplate;
    }

    public static String buildScopeFilter(BaseQueryConditionDTO baseQueryCondition, List<Object> paramList) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" 1 = 1");
        if (org.apache.commons.lang.StringUtils.isNotEmpty(baseQueryCondition.getGroupId())) {
            stringBuilder.append(" and companygroupid = ?");
            paramList.add(baseQueryCondition.getGroupId());
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCorpIds())) {
            stringBuilder.append(bulidCorpAndAccount("corp_corporation", baseQueryCondition.getCorpIds(), paramList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getAccountIds())) {
            stringBuilder.append(bulidCorpAndAccount("account_id", baseQueryCondition.getAccountIds(), paramList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCostCenterList())) {
            stringBuilder.append(bulidDeptAndCostcenter("cost_center", baseQueryCondition.getCostCenterList(), paramList));
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getDeptList())) {
            stringBuilder.append(buildOrgId(baseQueryCondition.getDeptList(), paramList));
        }
        return stringBuilder.toString();

    }

    /**
     * 构建公司和成本中心的查询条件
     *
     * @param key
     * @param list
     * @param
     */
    public static String bulidCorpAndAccount(String key, List<String> list, List<Object> paramList) {
        StringBuilder sqlBuffer = new StringBuilder();
        String sqlList = StringUtils.join(list.stream().map(corp -> "?").collect(Collectors.toList()), ",");
        sqlBuffer.append(" and ").append(key).append(" in (").append(sqlList).append(")");
        paramList.addAll(list);
        return sqlBuffer.toString();
    }

    /**
     * 构建成本中心和部门的查询条件
     *
     * @param key
     * @param list
     * @param
     */
    public static String bulidDeptAndCostcenter(String key, List<SearchDeptAndCostcneterDTO> list, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        for (SearchDeptAndCostcneterDTO searchDeptAndCostcneterDto : list) {
            Integer level = searchDeptAndCostcneterDto.getKey();
            if (Objects.isNull(level)) {
                continue;
            }
            String filedName = key + level;
            if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                sqlBuffer.append(" and " + filedName + " is not null");
                sqlBuffer.append(" and " + filedName + " <> ''");
            }
            List<String> vals = searchDeptAndCostcneterDto.getVals();
            if (CollectionUtils.isEmpty(vals)) {
                continue;
            }
            String valsSql =
                    org.apache.commons.lang.StringUtils.join(vals.stream().map(val -> "?").collect(Collectors.toList()), ",");
            sqlBuffer.append(" and " + filedName + " in (" + valsSql + ")");
            paramList.addAll(vals);
        }
        return sqlBuffer.toString();
    }

    public static String buildOrgId(List<SearchDeptAndCostcneterDTO> list, List<Object> paramList) {
        StringBuffer sqlBuffer = new StringBuffer();
        for (SearchDeptAndCostcneterDTO searchDeptAndCostcneterDto : list) {
            Integer level = searchDeptAndCostcneterDto.getKey();
            if (Objects.isNull(level)) {
                continue;
            }
            String filedName = "org_id";
            if (BooleanUtils.isTrue(searchDeptAndCostcneterDto.getSelectAll())) {
                return "";
            }
            List<String> vals = searchDeptAndCostcneterDto.getVals();
            if (CollectionUtils.isEmpty(vals)) {
                continue;
            }
            String valsSql =
                    org.apache.commons.lang.StringUtils.join(vals.stream().map(val -> "?").collect(Collectors.toList()), ",");
            sqlBuffer.append(" and " + filedName + " in (" + valsSql + ")");
            paramList.addAll(vals);
            break;
        }
        return sqlBuffer.toString();
    }

    /**
     * 获取口径ck表
     *
     * @param statisticalCaliber
     * @return
     */
    private ClickHouseTable getTableByStaticalCaliber(String statisticalCaliber) {
        StatiscalCaliberEnum statiscalCaliberEnum = StatiscalCaliberEnum.getByName(statisticalCaliber);
        if (null == statiscalCaliberEnum) {
            return ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_CARBON;
        }

        switch (statiscalCaliberEnum) {
            case BOOKING:
                return ClickHouseTable.ADM_INDEX_PRICE_SUMMARY_CARBON_ODT;
            case TRAVELING:
                return ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_CARBON_DTIME;
            default:
                return ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_CARBON;
        }
    }
}
