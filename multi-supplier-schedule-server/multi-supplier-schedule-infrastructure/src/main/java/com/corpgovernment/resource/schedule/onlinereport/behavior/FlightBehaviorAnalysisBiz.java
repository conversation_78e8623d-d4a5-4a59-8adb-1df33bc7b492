package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FlightBehaviorInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPreOrderDateRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportRebookTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportRefundTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PreOrderDateInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PreOrderdateRange;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RebookInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RebookTrendInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RefundInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RefundTrendInfo;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.behavior.FlightBehaviorAnlysisDao;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.behaivor.FlightBehaviorDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.behaivor.FlightPreOrderDateDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.behaivor.RefundRebookDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022-11-11 10:49
 * @desc
 */
@Service
public class FlightBehaviorAnalysisBiz {

    @Autowired
    private FlightBehaviorAnlysisDao flightBehaviorAnlysisDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    
    public List<FlightBehaviorInfo> behaviorInfos(OnlineReportBehaviorAnalysisRequest request) throws Exception {
        List<FlightBehaviorInfo> result = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        List<FlightBehaviorDTO> metricsDTOS = flightBehaviorAnlysisDao.behaviorAnaylsis(baseQueryConditionDto, FlightBehaviorDTO.class,
                Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()));
        metricsDTOS = Optional.ofNullable(metricsDTOS).orElse(new ArrayList<>());
        Integer sumQuantity = metricsDTOS.stream().filter(i -> Objects.nonNull(i.getTotalQuantity())).mapToInt(FlightBehaviorDTO::getTotalQuantity).sum();
        BigDecimal sumPrice = metricsDTOS.stream().filter(i -> Objects.nonNull(i.getTotalPrice())).map(FlightBehaviorDTO::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        for (FlightBehaviorDTO i : metricsDTOS) {
            FlightBehaviorInfo flightBehaviorInfo = new FlightBehaviorInfo();
            flightBehaviorInfo.setDim(i.getDim());
            flightBehaviorInfo.setTotalQuantity(i.getTotalQuantity());
            flightBehaviorInfo.setQuantityPercent(OrpReportUtils.divideWithPercent(i.getTotalQuantity(), sumQuantity).doubleValue());
            flightBehaviorInfo.setTotalRebookQuantity(i.getTotalRebookQuantity());
            flightBehaviorInfo.setRebookQuantityPercent(OrpReportUtils.divideWithPercent(i.getTotalRebookQuantity(), sumQuantity).doubleValue());
            flightBehaviorInfo.setTotalRefundQuantity(i.getTotalRefundQuantity());
            flightBehaviorInfo.setRefundQuantityPercent(OrpReportUtils.divideWithPercent(i.getTotalRefundQuantity(), sumQuantity).doubleValue());
            flightBehaviorInfo.setAvgPrice(OrpReportUtils.divide(i.getTotalPrice(), new BigDecimal(Optional.ofNullable(i.getTotalQuantity()).orElse(0)), OrpConstants.TWO));
            flightBehaviorInfo.setPricePercent(OrpReportUtils.divideWithPercent(i.getTotalPrice(), sumPrice).doubleValue());
            flightBehaviorInfo.setAvgTpmsPrice(OrpReportUtils.divide(i.getTotalPrice(), Optional.ofNullable(i.getTotalTpms()).orElse(BigDecimal.ZERO), OrpConstants.TWO));
            flightBehaviorInfo.setAvgDiscount(OrpReportUtils.divide(i.getTotalDiscount(),
                    new BigDecimal(Optional.ofNullable(i.getTotalQuantity()).orElse(0)), OrpConstants.TWO));
            flightBehaviorInfo.setTotalPrice(OrpReportUtils.formatBigDecimal(i.getTotalPrice()));
            result.add(flightBehaviorInfo);
        }
        return result;
    }

    
    public RefundInfo refund(OnlineReportRefundTrendRequest request) throws Exception {
        RefundInfo result = new RefundInfo();
        String index = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()).get("index");
        if (StringUtils.equalsIgnoreCase(index, "overview")) {
            result = refundOverview(request);
        } else if (StringUtils.equalsIgnoreCase(index, "trend")) {
            result = refundTrend(request);
        }
        return result;
    }

    private RefundInfo refundOverview(OnlineReportRefundTrendRequest request) throws Exception {
        RefundInfo result = new RefundInfo();
        BaseQueryConditionDTO dto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        dto.setProductType(request.getProductType());
        List<RefundRebookDTO> metricsDTOS = flightBehaviorAnlysisDao.refundOverView(dto, RefundRebookDTO.class,
                getIndustryList(dto.getIndustryType()), dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
        if (CollectionUtils.isNotEmpty(metricsDTOS)) {
            RefundRebookDTO refundRebookDTO = metricsDTOS.get(0);
            result.setTotalRefundQuantity(refundRebookDTO.getCompanyRfRbtkt());
            result.setRefundQuantityPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getCompanyRfRbtkt(), refundRebookDTO.getCompanytkt()).doubleValue());
            result.setTotalRefundLoss(OrpReportUtils.formatBigDecimal(OrpReportUtils.nonNegative(refundRebookDTO.getCompanyLoss())));
            result.setCorpRefundtktPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getCorpRfRbtkt(), refundRebookDTO.getCorptkt()).doubleValue());
            result.setIndustryRefundtktPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getIndustryRfRbtkt(), refundRebookDTO.getIndustrytkt()).doubleValue());
        }
        return result;
    }

    private RefundInfo refundTrend(OnlineReportRefundTrendRequest request) throws Exception {
        RefundInfo result = new RefundInfo();
        List<RefundTrendInfo> trendInfoList = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        List<RefundRebookDTO> metricsDTOS = flightBehaviorAnlysisDao.refundTrend(baseQueryConditionDto, RefundRebookDTO.class,
                Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()));
        metricsDTOS = Optional.ofNullable(metricsDTOS).orElse(new ArrayList<>());
        Set<String> points = BizUtils.getDatePoints(baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(), QueryReportAggDateDimensionEnum.month);
        for (String date : points) {
            RefundTrendInfo trendInfo = new RefundTrendInfo();
            trendInfo.setPoint(date);
            trendInfo.setTotalRefundQuantity(OrpConstants.ZERO);
            trendInfo.setCompanyRefundPercent(OrpReportUtils.formatBigDecimal(BigDecimal.ZERO).doubleValue());
            trendInfo.setTotalRefundLoss(OrpReportUtils.formatBigDecimal(BigDecimal.ZERO));
            Optional.ofNullable(metricsDTOS).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(BizUtils.dateFormat(i.getDim(), QueryReportAggDateDimensionEnum.month), date))
                    .findFirst().ifPresent(i -> {
                        trendInfo.setTotalQuantity(i.getCompanytkt());
                        trendInfo.setTotalRefundQuantity(i.getCompanyRfRbtkt());
                        trendInfo.setCompanyRefundPercent(OrpReportUtils.divideWithPercent(i.getCompanyRfRbtkt(), i.getCompanytkt()).doubleValue());
                    });
            trendInfoList.add(trendInfo);
        }
        trendInfoList.sort((obj1, obj2) -> {
            return obj1.getPoint().compareTo(obj2.getPoint());
        });
        result.setRefundTrendList(trendInfoList);
        return result;
    }

    
    public RebookInfo reBook(OnlineReportRebookTrendRequest request) throws Exception {
        RebookInfo result = new RebookInfo();
        String index = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()).get("index");
        if (StringUtils.equalsIgnoreCase(index, "overview")) {
            result = rebookOverview(request);
        } else if (StringUtils.equalsIgnoreCase(index, "trend")) {
            result = rebookTrend(request);
        }
        return result;
    }

    private RebookInfo rebookOverview(OnlineReportRebookTrendRequest request) throws Exception {
        RebookInfo result = new RebookInfo();
        BaseQueryConditionDTO dto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        dto.setProductType(request.getProductType());
        List<RefundRebookDTO> metricsDTOS = flightBehaviorAnlysisDao.rebookOverview(dto, RefundRebookDTO.class,
                getIndustryList(dto.getIndustryType()), dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
        if (CollectionUtils.isNotEmpty(metricsDTOS)) {
            RefundRebookDTO refundRebookDTO = metricsDTOS.get(0);
            result.setTotalRebookQuantity(refundRebookDTO.getCompanyRfRbtkt());
            result.setRebookQuantityPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getCompanyRfRbtkt(), refundRebookDTO.getCompanytkt()).doubleValue());
            result.setTotalRebookLoss(OrpReportUtils.formatBigDecimal(OrpReportUtils.nonNegative(refundRebookDTO.getCompanyLoss())));
            result.setCorpRebooktktPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getCorpRfRbtkt(), refundRebookDTO.getCorptkt()).doubleValue());
            result.setIndustryRebooktktPercent(OrpReportUtils.divideWithPercent(refundRebookDTO.getIndustryRfRbtkt(), refundRebookDTO.getIndustrytkt()).doubleValue());
        }
        return result;
    }

    private RebookInfo rebookTrend(OnlineReportRebookTrendRequest request) throws Exception {
        RebookInfo result = new RebookInfo();
        List<RebookTrendInfo> trendInfoList = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        List<RefundRebookDTO> metricsDTOS = flightBehaviorAnlysisDao.rebookTrend(baseQueryConditionDto, RefundRebookDTO.class,
                Optional.ofNullable(request.getExtData()).orElse(new HashMap<>()));
        metricsDTOS = Optional.ofNullable(metricsDTOS).orElse(new ArrayList<>());
        Set<String> points = BizUtils.getDatePoints(baseQueryConditionDto.getStartTime(), baseQueryConditionDto.getEndTime(), QueryReportAggDateDimensionEnum.month);
        for (String date : points) {
            RebookTrendInfo trendInfo = new RebookTrendInfo();
            trendInfo.setPoint(date);
            trendInfo.setTotalRebookQuantity(OrpConstants.ZERO);
            trendInfo.setCompanyRebookPercent(OrpReportUtils.formatBigDecimal(BigDecimal.ZERO).doubleValue());
            trendInfo.setTotalRebookLoss(OrpReportUtils.formatBigDecimal(BigDecimal.ZERO));
            Optional.ofNullable(metricsDTOS).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(BizUtils.dateFormat(i.getDim(), QueryReportAggDateDimensionEnum.month), date))
                    .findFirst().ifPresent(i -> {
                        trendInfo.setTotalQuantity(i.getCompanytkt());
                        trendInfo.setTotalRebookQuantity(i.getCompanyRfRbtkt());
                        trendInfo.setCompanyRebookPercent(OrpReportUtils.divideWithPercent(i.getCompanyRfRbtkt(), i.getCompanytkt()).doubleValue());
                    });
            trendInfoList.add(trendInfo);
        }
        trendInfoList.sort((obj1, obj2) -> {
            return obj1.getPoint().compareTo(obj2.getPoint());
        });
        result.setRebookTrendList(trendInfoList);
        return result;
    }

    
    public List<PreOrderDateInfo> flightPreorderdateAnalysis(OnlineReportPreOrderDateRequest request) throws Exception {
        List<PreOrderDateInfo> result = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setProductType(request.getProductType());
        List<PreOrderdateRange> preOrderdateRanges = request.getRangeList();
        if (CollectionUtils.isEmpty(preOrderdateRanges)) {
            // 默认提前时间天数
            preOrderdateRanges = Arrays.asList(new PreOrderdateRange(0, 0),new PreOrderdateRange(1, 1),
                    new PreOrderdateRange(2, 2), new PreOrderdateRange(3, 3),
                    new PreOrderdateRange(4, 4), new PreOrderdateRange(5, 365));
        }
        List<FlightPreOrderDateDTO> preNlist = flightBehaviorAnlysisDao.flightPreorderdateAnalysis(baseQueryConditionDto, FlightPreOrderDateDTO.class, preOrderdateRanges);
        List<FlightPreOrderDateDTO> pre4list = flightBehaviorAnlysisDao.flightPreorderdateAnalysis(baseQueryConditionDto, FlightPreOrderDateDTO.class);
        preNlist = Optional.ofNullable(preNlist).orElse(new ArrayList<>());
        int sumQuantity = preNlist.stream().mapToInt(i -> Optional.ofNullable(i.getTotalQuantity()).orElse(OrpConstants.ZERO)).sum();
        BigDecimal sumPrice = preNlist.stream().map(i -> Optional.ofNullable(i.getTotalPrice()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 提前>4天的平均折扣
        BigDecimal pre4AvgDiscount = OrpReportUtils.formatBigDecimal(BigDecimal.ZERO);
        if (CollectionUtils.isNotEmpty(pre4list)) {
            FlightPreOrderDateDTO preOrderDateDTO = pre4list.get(0);
            pre4AvgDiscount = OrpReportUtils.divideUp(preOrderDateDTO.getTotalPre4Discount(),
                    new BigDecimal(Optional.ofNullable(preOrderDateDTO.getTotalPre4Quantiy()).orElse(OrpConstants.ZERO)), OrpConstants.TWO);
        }
        for (PreOrderdateRange preOrderdateRange : preOrderdateRanges) {
            PreOrderDateInfo preOrderDateInfo = new PreOrderDateInfo();
            BigDecimal finalPre4AvgDiscount = pre4AvgDiscount;
            preOrderDateInfo.setRange(FlightBehaviorAnlysisDao.getPreorderdateDim(preOrderdateRange));
            Optional.ofNullable(preNlist).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(preOrderDateInfo.getRange(), i.getOrderDateRange()))
                    .findFirst().ifPresent(i -> {
                        // 票张
                        preOrderDateInfo.setTotalQuantity(i.getTotalQuantity());
                        // 票张占比
                        preOrderDateInfo.setQuantityPercent(OrpReportUtils.divideWithPercent(i.getTotalQuantity(), sumQuantity).doubleValue());
                        // 成交净价
                        preOrderDateInfo.setTotalPrice(OrpReportUtils.formatBigDecimal(i.getTotalPrice()));
                        // 占比
                        preOrderDateInfo.setPricePercent(OrpReportUtils.divideWithPercent(i.getTotalPrice(), sumPrice).doubleValue());
                        // 退票率
                        preOrderDateInfo.setRefundPercent(OrpReportUtils.divideWithPercent(i.getTotalRefundtkt(), i.getTotalOrdertkt()).doubleValue());
                        // 改签率
                        preOrderDateInfo.setRebookPercent(OrpReportUtils.divideWithPercent(i.getTotalRebooktkt(), i.getTotalOrdertkt()).doubleValue());
                        // 提前N天的平均折扣
                        BigDecimal preNAvgDiscount = OrpReportUtils.divideUp(i.getTotalDiscount(),
                                new BigDecimal(Optional.ofNullable(i.getTotalQuantity()).orElse(OrpConstants.ZERO)), OrpConstants.TWO);
                        preOrderDateInfo.setAvgPriceRate(preNAvgDiscount);
                        // 全价票张数
                        preOrderDateInfo.setTotaLFullfaretkt(i.getTotalFullfaretkt());
                        // 不含全价票的平均折扣
                        preOrderDateInfo.setAvgPriceRateWithoutFull(OrpReportUtils.divideUp(i.getTotalWithoutFullDiscount(),
                                new BigDecimal(Optional.ofNullable(i.getTotalWithoutFullQuantity()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
                        preOrderDateInfo.setTotalPotentialSaveAmount(OrpReportUtils.formatBigDecimal(BigDecimal.ZERO));
                        // 潜在节省 = (提前N天成交净价/提前N天的平均折扣)*(提前N天的平均折扣-提前>4天的平均折扣)
                        if (finalPre4AvgDiscount.compareTo(BigDecimal.ZERO) > 0) {
                            preOrderDateInfo.setTotalPotentialSaveAmount(OrpReportUtils.nonNegative(
                                    OrpReportUtils.formatBigDecimal(OrpReportUtils.divideUp(i.getTotalPrice(), preNAvgDiscount, OrpConstants.FOUR)
                                            .multiply(preNAvgDiscount.subtract(finalPre4AvgDiscount)))));
                        }
                    });
            result.add(preOrderDateInfo);
        }
        return result;
    }

    protected List getIndustryList(String industryType) {
        if (StringUtils.isNotEmpty(industryType)) {
            return Arrays.asList(industryType);
        }
        return null;
    }
}
