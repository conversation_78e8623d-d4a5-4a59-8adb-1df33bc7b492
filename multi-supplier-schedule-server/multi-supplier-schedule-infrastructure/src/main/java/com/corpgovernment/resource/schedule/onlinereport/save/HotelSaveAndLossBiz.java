package com.corpgovernment.resource.schedule.onlinereport.save;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.GeneralPotentialSaveInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.GeneralSaveDistributionInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.GeneralSaveInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPotentialSaveLowRclRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPromotionsSaveDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveGeneralRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PotentialSaveLowRcInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PromotionsSaveInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.save.HotelSaveAndLossDao;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.HotelSaveStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatisticalEnumerable;
import com.corpgovernment.resource.schedule.onlinereport.module.save.HotelLowRcPotentialSaveDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.save.HotelSaveDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.save.PotentialSaveDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.save.PromotionSaveDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.google.common.util.concurrent.AtomicDouble;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/*
 * <AUTHOR>
 * @date 2022/6/20 13:08
 * @Desc
 */
@Service
public class HotelSaveAndLossBiz extends AbstractSaveAndLossBiz {


    @Autowired
    private HotelSaveAndLossDao hotelSaveAndLossDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Qualifier("screenThreadPoolExecutor")
    @Resource
    private ThreadPoolExecutor executor;

    /**
     * 节省概览
     *
     * @param request
     * @throws Exception
     */
    
    public GeneralSaveInfo saveGeneral(OnlineReportSaveGeneralRequest request) throws Exception {
        BaseQueryConditionDTO dto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        GeneralSaveInfo saveBO = new GeneralSaveInfo();
        saveBO.setTotalSaveAmount(BigDecimal.ZERO);
        ExecutorService executorService = executor;
        Future<GeneralSaveInfo> future1 = executorService.submit(new Callable<GeneralSaveInfo>() {
            @Override
            public GeneralSaveInfo call() throws Exception {
                List<HotelSaveDTO> total = hotelSaveAndLossDao.agreementSaveTotal(dto, request.getProductType());
                if (CollectionUtils.isNotEmpty(total)) {
                    HotelSaveDTO hotelSaveDTO = total.get(0);
                    double saveAmount = hotelSaveDTO.getSaveAmount3c() + hotelSaveDTO.getSaveAmountPromotion() + hotelSaveDTO.getSaveAmountPremium() +
                            Optional.ofNullable(hotelSaveDTO.getControlSaveAmount()).orElse(0d);
                    saveBO.setTotalSaveAmount(OrpReportUtils.formatDouble(saveAmount));
                    saveBO.setReportDate(hotelSaveDTO.getReportDate());
                }
                return saveBO;
            }
        });
        Future<GeneralSaveInfo> future2 = executorService.submit(new Callable<GeneralSaveInfo>() {
            @Override
            public GeneralSaveInfo call() throws Exception {
                List<HotelSaveDTO> list = hotelSaveAndLossDao.agreementSave(dto, request.getProductType());
                List<HotelSaveDTO> corpList = hotelSaveAndLossDao.agreementSaveCorpAndIndustry(dto.getStartTime(), dto.getEndTime(), dto.getStatisticalCaliber(),
                        null, DataTypeEnum.CORP, request.getProductType(), dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
                List<HotelSaveDTO> industryList = hotelSaveAndLossDao.agreementSaveCorpAndIndustry(dto.getStartTime(), dto.getEndTime(), dto.getStatisticalCaliber(),
                        Optional.ofNullable(request.getBasecondition().getIndustryType()).map(Arrays::asList).orElse(null), DataTypeEnum.INDUSTRY, request.getProductType(),
                        dto.getCompareSameLevel(), dto.getConsumptionLevel(), dto.getCompareCorpSameLevel());
                
                if (CollectionUtils.isNotEmpty(list)) {
                    HotelSaveDTO hotelSaveDTO = list.get(0);
                    double saveAmount = Optional.ofNullable(hotelSaveDTO.getSaveAmount3c()).orElse(0d) +
                                        Optional.ofNullable(hotelSaveDTO.getSaveAmountPromotion()).orElse(0d) +
                                        Optional.ofNullable(hotelSaveDTO.getSaveAmountPremium()).orElse(0d) +
                                        Optional.ofNullable(hotelSaveDTO.getControlSaveAmount()).orElse(0d);
                    saveBO.setCompanySaveAmount(OrpReportUtils.formatDouble(saveAmount));
                    saveBO.setCompanySaveRate(OrpReportUtils.divideWithPercent(saveAmount, Optional.ofNullable(hotelSaveDTO.getSumCorpRealPay()).orElse(0d)));
                    saveBO.setCompanyPerQtySave(OrpReportUtils.formatDouble(Optional.ofNullable(hotelSaveDTO.getSavePerQuantity()).orElse(0d)));
                }
                
                if (CollectionUtils.isNotEmpty(corpList)) {
                    HotelSaveDTO hotelSaveDTO = corpList.get(0);
                    double saveAmount = Optional.ofNullable(hotelSaveDTO.getSaveAmount3c()).orElse(0d) +
                                        Optional.ofNullable(hotelSaveDTO.getSaveAmountPromotion()).orElse(0d) +
                                        Optional.ofNullable(hotelSaveDTO.getSaveAmountPremium()).orElse(0d) +
                                        Optional.ofNullable(hotelSaveDTO.getControlSaveAmount()).orElse(0d);
                    saveBO.setCorpSaveRate(OrpReportUtils.divideWithPercent(saveAmount, Optional.ofNullable(hotelSaveDTO.getSumCorpRealPay()).orElse(0d)));
                    saveBO.setCorpPerQtySave(OrpReportUtils.formatDouble(Optional.ofNullable(hotelSaveDTO.getSavePerQuantity()).orElse(0d)));
                }
                
                if (CollectionUtils.isNotEmpty(industryList)) {
                    HotelSaveDTO hotelSaveDTO = industryList.get(0);
                    double saveAmount = Optional.ofNullable(hotelSaveDTO.getSaveAmount3c()).orElse(0d) +
                                        Optional.ofNullable(hotelSaveDTO.getSaveAmountPromotion()).orElse(0d) +
                                        Optional.ofNullable(hotelSaveDTO.getSaveAmountPremium()).orElse(0d) +
                                        Optional.ofNullable(hotelSaveDTO.getControlSaveAmount()).orElse(0d);
                    saveBO.setIndustrySaveRate(OrpReportUtils.divideWithPercent(saveAmount, Optional.ofNullable(hotelSaveDTO.getSumCorpRealPay()).orElse(0d)));
                    saveBO.setIndustryPerQtySave(OrpReportUtils.formatDouble(Optional.ofNullable(hotelSaveDTO.getSavePerQuantity()).orElse(0d)));
                }
                
                return saveBO;
            }
        });
        future1.get();
        future2.get();
        return saveBO;
    }

    /**
     * 节省分布概览
     *
     * @param request
     * @throws Exception
     */
    
    public GeneralSaveDistributionInfo saveDistributionGeneral(OnlineReportSaveGeneralRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<HotelSaveDTO> list = hotelSaveAndLossDao.agreementSave(baseQueryConditionDto, request.getProductType());
        GeneralSaveDistributionInfo saveDistributionBO = new GeneralSaveDistributionInfo();
        if (CollectionUtils.isNotEmpty(list)) {
            HotelSaveDTO hotelSaveDTO = list.get(0);
            double saveAmount = hotelSaveDTO.getSaveAmount3c() + hotelSaveDTO.getSaveAmountPromotion() + compareZeroAndGetDefault(hotelSaveDTO.getSaveAmountPremium()) +
                    Optional.ofNullable(hotelSaveDTO.getControlSaveAmount()).orElse(0d);
            // 三方节省金额
            saveDistributionBO.setSaveAmount3c(OrpReportUtils.formatDouble(hotelSaveDTO.getSaveAmount3c()));
            // 三方节省金额占比
            saveDistributionBO.setSaveAmount3cPercent(OrpReportUtils.divideWithPercent(hotelSaveDTO.getSaveAmount3c(), saveAmount));

            // 两方节省金额
            saveDistributionBO.setSaveAmount2c(OrpReportUtils.formatDouble(hotelSaveDTO.getSaveAmountPremium()));
            // 两方节省金额占比
            saveDistributionBO.setSaveAmount2cPercent(OrpReportUtils.divideWithPercent(compareZeroAndGetDefault(hotelSaveDTO.getSaveAmountPremium()), saveAmount));

            // 促销优惠活动的节省金额
            saveDistributionBO.setSaveAmountPromotion(OrpReportUtils.formatDouble(hotelSaveDTO.getSaveAmountPromotion()));
            // 促销优惠活动的节省金额占比
            saveDistributionBO.setSaveAmountPromotionPercent(OrpReportUtils.divideWithPercent(hotelSaveDTO.getSaveAmountPromotion(), saveAmount));

            // 管控节省金额
            saveDistributionBO.setControlSave(OrpReportUtils.formatDouble(hotelSaveDTO.getControlSaveAmount()));
            // 管控节省金额占比
            saveDistributionBO.setControlSavePercent(OrpReportUtils.divideWithPercent(hotelSaveDTO.getControlSaveAmount(), saveAmount));
            // 总节省
            saveDistributionBO.setSaveAmount(OrpReportUtils.formatDouble(saveAmount));
        }
        if (Objects.isNull(request.getExtData()) || !StringUtils.equalsIgnoreCase(request.getExtData().get("needTotal"), "F")) {
            List<HotelSaveDTO> total = hotelSaveAndLossDao.agreementSaveTotal(baseQueryConditionDto, request.getProductType());
            if (CollectionUtils.isNotEmpty(total)) {
                HotelSaveDTO hotelSaveDTO = total.get(0);
                saveDistributionBO.setReportDate(hotelSaveDTO.getReportDate());
                // 三方节省金额
                saveDistributionBO.setTotalSaveAmount3c(OrpReportUtils.formatDouble(hotelSaveDTO.getSaveAmount3c()));
                // 两方节省金额
                saveDistributionBO.setTotalSaveAmount2c(OrpReportUtils.formatDouble(hotelSaveDTO.getSaveAmountPremium()));
                // 促销优惠活动的节省金额
                saveDistributionBO.setTotalSaveAmountPromotion(OrpReportUtils.formatDouble(hotelSaveDTO.getSaveAmountPromotion()));
                // 管控节省金额
                saveDistributionBO.setTotalControlSave(OrpReportUtils.formatDouble(hotelSaveDTO.getControlSaveAmount()));
            }
        }
        return saveDistributionBO;
    }

    /**
     * 潜在节省概览
     *
     * @param request
     * @throws Exception
     */
    
    public GeneralPotentialSaveInfo potentialSaveGeneral(OnlineReportSaveGeneralRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<PotentialSaveDTO> list = hotelSaveAndLossDao.agreementPotentialSave(baseQueryConditionDto, PotentialSaveDTO.class, request.getProductType());
        List<PotentialSaveDTO> totalList = hotelSaveAndLossDao.agreementPotentialSaveTotal(baseQueryConditionDto, PotentialSaveDTO.class, request.getProductType());

        GeneralPotentialSaveInfo potentialSaveBO = new GeneralPotentialSaveInfo();
        if (CollectionUtils.isNotEmpty(list)) {
            PotentialSaveDTO potentialSaveDTO = list.get(0);
            potentialSaveBO.setOverAmount(compareZeroAndGetDefault(OrpReportUtils.formatDouble(potentialSaveDTO.getOverAmount())));
            potentialSaveBO.setRcTimes(potentialSaveDTO.getRcTimes());
            potentialSaveBO.setRefundloss(compareZeroAndGetDefault(OrpReportUtils.formatDouble(potentialSaveDTO.getRefundloss())));
            potentialSaveBO.setRefundtkt(potentialSaveDTO.getRefundtkt());
            potentialSaveBO.setPotentialSaveAmount(OrpReportUtils.formatBigDecimal(potentialSaveBO.getOverAmount().add(potentialSaveBO.getRefundloss())));
        }
        if (CollectionUtils.isNotEmpty(totalList)) {
            PotentialSaveDTO potentialSaveDTO = totalList.get(0);
            potentialSaveBO.setReportDate(potentialSaveDTO.getReportDate());
            potentialSaveBO.setTotalOverAmount(compareZeroAndGetDefault(OrpReportUtils.formatDouble(potentialSaveDTO.getOverAmount())));
            potentialSaveBO.setTotalRefundloss(compareZeroAndGetDefault(OrpReportUtils.formatDouble(potentialSaveDTO.getRefundloss())));
            potentialSaveBO.setTotalPotentialSaveAmount(OrpReportUtils.formatBigDecimal(potentialSaveBO.getTotalOverAmount().add(potentialSaveBO.getTotalRefundloss())));
        }
        return potentialSaveBO;
    }

    /**
     * 酒店超标原因分析
     *
     * @param request
     * @throws Exception
     */
    
    public List<PotentialSaveLowRcInfo> lowRcAnalysis(OnlineReportPotentialSaveLowRclRequest request) throws Exception {
        List<PotentialSaveLowRcInfo> result = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<HotelLowRcPotentialSaveDTO> list = hotelSaveAndLossDao.hotelLowRcAnalysis(baseQueryConditionDto, HotelLowRcPotentialSaveDTO.class
                , request.getProductType(), request.getLang());
        // 总超标次数
        AtomicInteger sumRcTimes = new AtomicInteger();
        // 总潜在节省
        AtomicDouble sumOverAmount = new AtomicDouble();

        list.forEach(i -> {
            sumRcTimes.set(sumRcTimes.get() + Optional.ofNullable(i.getRcTimes()).orElse(0));
            sumOverAmount.set(sumOverAmount.get() + Optional.ofNullable(OrpReportUtils.nonNegative(i.getOverAmount())).orElse(0d));
        });
        if (CollectionUtils.isNotEmpty(list)) {
            for (HotelLowRcPotentialSaveDTO lowRcPotentialSaveDTO : list) {
                PotentialSaveLowRcInfo lowRcPotentialSaveBO = new PotentialSaveLowRcInfo();
                lowRcPotentialSaveBO.setLowRcCode(lowRcPotentialSaveDTO.getLowRcCode());
                lowRcPotentialSaveBO.setLowRcDesc(lowRcPotentialSaveDTO.getLowRcDesc());
                lowRcPotentialSaveBO.setRcTimes(lowRcPotentialSaveDTO.getRcTimes());
                lowRcPotentialSaveBO.setRcPercent(OrpReportUtils.divideWithPercent(lowRcPotentialSaveDTO.getRcTimes(), sumRcTimes.get()));
                lowRcPotentialSaveBO.setOverAmount(OrpReportUtils.formatDouble(OrpReportUtils.nonNegative(lowRcPotentialSaveDTO.getOverAmount())));
                lowRcPotentialSaveBO.setOverAmountPercent(OrpReportUtils.divideWithPercent(lowRcPotentialSaveBO.getOverAmount().doubleValue(), sumOverAmount.get()));
                result.add(lowRcPotentialSaveBO);
            }
            // 汇总
            PotentialSaveLowRcInfo sumBO = new PotentialSaveLowRcInfo();
            sumBO.setLowRcCode(SharkUtils.get("Index.sum", request.getLang()));
            sumBO.setLowRcDesc(StringUtils.EMPTY);
            sumBO.setRcTimes(sumRcTimes.get());
            sumBO.setRcPercent(OrpReportUtils.divideWithPercent(OrpConstants.ONE, OrpConstants.ONE));
            sumBO.setOverAmount(OrpReportUtils.formatDouble(sumOverAmount.get()));
            sumBO.setOverAmountPercent(OrpReportUtils.divideWithPercent(OrpConstants.ONE, OrpConstants.ONE));
            result.add(sumBO);
        }
        return result;
    }

    /**
     * 优惠活动节省明细
     *
     * @param request
     * @throws Exception
     */
    
    public List<PromotionsSaveInfo> promotionsSaveDetail(OnlineReportPromotionsSaveDetailRequest request) throws Exception {
        List<PromotionsSaveInfo> result = new ArrayList<>();
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<PromotionSaveDTO> list = hotelSaveAndLossDao.promotionSaveDetail(baseQueryConditionDto, PromotionSaveDTO.class, request.getProductType());
        // 总优惠节省金额
        Double sumSaveAmount = list.stream().mapToDouble(PromotionSaveDTO::getSaveAmount).sum();
        if (CollectionUtils.isNotEmpty(list)) {
            for (PromotionSaveDTO saveDTO : list) {
                PromotionsSaveInfo promotionsSaveInfo = new PromotionsSaveInfo();
                promotionsSaveInfo.setTagName(saveDTO.getTagName());
                promotionsSaveInfo.setSaveAmount(OrpReportUtils.formatDouble(saveDTO.getSaveAmount()));
                promotionsSaveInfo.setSaveAmountPercent(OrpReportUtils.divideWithPercent(saveDTO.getSaveAmount(), sumSaveAmount));
                result.add(promotionsSaveInfo);
            }
        }
        return result;
    }

    public List<Map> deptDetail(BaseQueryCondition request, AnalysisObjectEnum analysisObjectEnum, Pager pager,
                                String orderType) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request);
        List result = hotelSaveAndLossDao.topAnalysis(analysisObjectEnum, baseQueryConditionDto, BizUtils.initPager(pager), orderType);
        fomratResultData(result);
        return result;
    }

    /**
     * 查询酒店员工信息
     */
    public List<Map> hotelEmployeeDetail(BaseQueryCondition request, AnalysisObjectEnum analysisObjectEnum, Pager pager,
                                         String orderType) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request);
        List result = hotelSaveAndLossDao.topAnalysis(analysisObjectEnum, baseQueryConditionDto, BizUtils.initPager(pager), orderType);
        fomratResultData(result);
        return result;
    }

    public int count(BaseQueryCondition request, AnalysisObjectEnum analysisObjectEnum, String orderType)
            throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request);
        return hotelSaveAndLossDao.topRcAnalysisCount(analysisObjectEnum, baseQueryConditionDto, orderType);
    }

    /**
     * 统一格式化结果数据
     *
     * @param list
     */
    protected void fomratResultData(List<Map> list) {
        int i = 0;
        for (Map map : list) {
            i++;
            for (Object key : map.keySet()) {
                HotelSaveStatisticalsEnum saveStatisticalsEnum = getStaticalByKey((String) key);
                if (saveStatisticalsEnum != null) {
                    BigDecimal target = OrpReportUtils.formatBigDecimal(
                            Objects.isNull(map.get(key)) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(map.get(key))),
                            saveStatisticalsEnum.getNum(), true);
                    map.put(key, saveStatisticalsEnum.isPercent() ? target.toString().concat("%") : target);
                }
            }
        }
    }

    protected HotelSaveStatisticalsEnum getStaticalByKey(String key) {
        try {
            return HotelSaveStatisticalsEnum.valueOf((String) key);
        } catch (Exception e) {

        }
        return null;
    }

    @Override
    public List<StatisticalEnumerable> getStatisticalList(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return Arrays.stream(HotelSaveStatisticalsEnum.values()).collect(Collectors.toList());
    }
}
