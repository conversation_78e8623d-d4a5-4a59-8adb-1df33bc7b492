package com.corpgovernment.resource.schedule.screen;

import com.corpgovernment.resource.schedule.domain.screen.gateway.ScreenGateway;
import com.corpgovernment.resource.schedule.domain.screen.model.ConsumeRankBO;
import com.corpgovernment.resource.schedule.domain.screen.model.ConsumeRankDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.HotCityBO;
import com.corpgovernment.resource.schedule.domain.screen.model.HotCityDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapBO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDestinationInfoBO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDestinationInfoDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.SaveAmountBO;
import com.corpgovernment.resource.schedule.domain.screen.model.StandardRateBO;
import com.corpgovernment.resource.schedule.domain.screen.model.StandardRateDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.TotalAmountDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.TotalCountBO;
import com.corpgovernment.resource.schedule.domain.screen.model.TripRankBO;
import com.corpgovernment.resource.schedule.domain.screen.model.TripRankDTO;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import screen.response.AggregateResp;
import screen.response.TotalInfo;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR> Smith
 */
@Component
@Slf4j
public class ScreenGatewayCKImpl extends AbstractClickhouseBaseDao implements ScreenGateway {
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public List<TotalCountBO> totalAmountAndOrder(TotalAmountDTO totalAmountDTO) {
        // 使用与 generalReport/queryNewReportData 接口相同的计算逻辑
        String sql = "SELECT type,\n" +
                "       SUM(totalAmount)   AS totalAmount,\n" +
                "       SUM(totalQuantity) AS totalQuantity\n" +
                "FROM (\n" +
                "         SELECT 'flight'      AS type,\n" +
                "                CAST(SUM(real_pay) AS Decimal(38, 4)) AS totalAmount,\n" +
                "                CAST((count(distinct if(is_refund='F',order_id,null)) - count(distinct if(is_refund='T' and order_status='RA',order_id,null))) AS Decimal(38, 4)) AS totalQuantity\n" +
                "         FROM olrpt_indexflightdownload_all\n" +
                "         WHERE d = ?\n" +
                "           AND substr(report_date,1,10) >= ?\n" +
                "           AND substr(report_date,1,10) <= ?\n" +
                "           AND flight_class = '%s'\n" +
                "           AND fee_type = '因公'\n" +
                "           AND audited <> 'F'\n" +
                "           AND order_status in ('RA','TA','EP','EA','RP')\n" +
                "\n" +
                "         UNION ALL\n" +
                "\n" +
                "         SELECT 'hotel'       AS type,\n" +
                "                CAST(SUM(real_pay) AS Decimal(38, 4)) AS totalAmount,\n" +
                "                CAST((count(distinct if(is_refund='F',order_id,null)) - count(distinct if(is_refund='T' and order_status='已取消',order_id,null))) AS Decimal(38, 4)) AS totalQuantity\n" +
                "         FROM olrpt_indexhoteldownload_all\n" +
                "         WHERE d = ?\n" +
                "           AND substr(report_date,1,10) >= ?\n" +
                "           AND substr(report_date,1,10) <= ?\n" +
                "           AND is_oversea = '%s'\n" +
                "           AND order_status = '已完成'\n" +
                "           AND is_oversea IN ('F','O','T')\n" +
                "%s\n" +
                "         ) AS combined_data\n" +
                "GROUP BY type";


        List<Object> paramList = new ArrayList<>();
        String startTime = dateTimeFormatter.format(totalAmountDTO.getStartTime());
        String endTime = dateTimeFormatter.format(totalAmountDTO.getEndTime());

        String flightPartition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        String hotelPartition = queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD);
        List<String> partitions = Lists.newArrayList(flightPartition, hotelPartition);

        partitions.forEach(f -> {
            paramList.add(f);
            paramList.add(startTime);
            paramList.add(endTime);
        });

        if ("国内".equals(totalAmountDTO.getAreaType())) {
            String domesticSql = "\n" +
                    "         UNION ALL\n" +
                    "\n" +
                    "         SELECT 'train'       AS type,\n" +
                    "                CAST(SUM(real_pay) AS Decimal(38, 4)) AS totalAmount,\n" +
                    "                CAST((select count(distinct case when a.cnt>0 then a.order_id end)-count(distinct case when a.cnt< 0 then a.order_id end)\n" +
                    "                      from (select order_id, sum(quantity) cnt from olrpt_indextraindownload_all\n" +
                    "                            WHERE d = ?\n" +
                    "                            AND substr(report_date,1,10) >= ?\n" +
                    "                            AND substr(report_date,1,10) <= ?\n" +
                    "                            and order_status  in ('TA','RP','EP','EA')\n" +
                    "                            group by order_id) a) AS Decimal(38, 4)) AS totalQuantity\n" +
                    "         FROM olrpt_indextraindownload_all\n" +
                    "         WHERE d = ?\n" +
                    "           AND substr(report_date,1,10) >= ?\n" +
                    "           AND substr(report_date,1,10) <= ?\n" +
                    "           AND order_status IN ('TA', 'RP', 'EP', 'EA')\n" +
                    "\n" +
                    "         UNION ALL\n" +
                    "\n" +
                    "         SELECT 'car'         AS type,\n" +
                    "                CAST(SUM(real_pay) AS Decimal(38, 4)) AS totalAmount,\n" +
                    "                CAST(sum(coalesce(cnt_order,0)) AS Decimal(38, 4)) AS totalQuantity\n" +
                    "         FROM olrpt_indexcardownload_all\n" +
                    "         WHERE d = ?\n" +
                    "           AND substr(order_date,1,10) >= ?\n" +
                    "           AND substr(order_date,1,10) <= ?\n" +
                    "           AND fee_type = '因公'\n";

            sql = String.format(sql, "N", "F", domesticSql);

            String trainPartition = queryPartition(ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD);
            String carPartition = queryPartition(ClickHouseTable.OLRPT_INDEXCARDOWNLOAD);
            // 添加火车票子查询的参数
            paramList.add(trainPartition);
            paramList.add(startTime);
            paramList.add(endTime);
            // 添加火车票主查询的参数
            paramList.add(trainPartition);
            paramList.add(startTime);
            paramList.add(endTime);
            // 添加用车查询的参数
            paramList.add(carPartition);
            paramList.add(startTime);
            paramList.add(endTime);
        } else {
            sql = String.format(sql, "I", "T", "");
        }


        List<TotalCountBO> totalAmounts = findList(sql, paramList, TotalCountBO.class);

        log.info("totalAmounts: {}", totalAmounts);


        return totalAmounts;
    }


    @Override
    public List<ConsumeRankBO> consumeRankDept(ConsumeRankDTO consumeRankDTO) {
        List<Object> paramList = Lists.newArrayList();
        String sql = getConsumeRankSql(consumeRankDTO, paramList).replaceAll("mark", "org_id");

        log.info("the consume rank dept sql: {}", sql);

        List<ConsumeRankBO> list = findList(sql, paramList, ConsumeRankBO.class);
        log.info("the consume rank dept list: {}", list);

        return list;
    }

    private String getConsumeRankSql(ConsumeRankDTO consumeRankDTO, List<Object> paramList) {

        String sql = "SELECT name,\n" +
                "       SUM(count) AS count\n" +
                "FROM (\n" +
                "         SELECT mark         AS name,\n" +
                "                SUM(real_pay) AS count\n" +
                "         FROM adm_indexflight_price_detail_all\n" +
                "         WHERE d = ?\n" +
                "           AND report_date >= ?\n" +
                "           AND report_date <= ?\n" +
                "           and flight_class = '%s'\n" +
                "         and mark is not null and mark != ''\n" +
                "         group by mark\n" +
                "\n" +
                "         UNION ALL\n" +
                "\n" +
                "         SELECT mark         AS name,\n" +
                "                SUM(real_pay_with_servicefee) AS count\n" +
                "         FROM adm_indexhotel_price_detail_all\n" +
                "         WHERE d = ?\n" +
                "           AND report_date >= ?\n" +
                "           AND report_date <= ?\n" +
                "           AND order_status = '已完成' and is_oversea = '%s'\n" +
                "         and mark is not null and mark != ''\n" +
                "         group by mark\n" +
                "%s\n" +
                "         ) AS combined_data\n" +
                "GROUP BY name\n" +
                "order by count DESC limit 6";

        String flightPartition = queryPartition(ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL);
        String hotelPartition = queryPartition(ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL);
        List<String> partitions = Lists.newArrayList(flightPartition, hotelPartition);

        if (consumeRankDTO.domestic()) {
            String domesticSql = "\n" +
                    "         UNION ALL\n" +
                    "\n" +
                    "         SELECT mark         AS name,\n" +
                    "                SUM(real_pay) AS count\n" +
                    "         FROM adm_indextrain_price_detail_all\n" +
                    "         WHERE d = ?\n" +
                    "           AND report_date >= ?\n" +
                    "           AND report_date <= ?\n" +
                    "           and mark is not null and mark != ''\n" +
                    "         group by mark\n" +
                    "\n" +
                    "         UNION ALL\n" +
                    "\n" +
                    "         SELECT mark         AS name,\n" +
                    "                SUM(real_pay) AS count\n" +
                    "         FROM adm_indexcar_price_detail_all\n" +
                    "         WHERE d = ?\n" +
                    "           AND report_date >= ?\n" +
                    "           AND report_date <= ?\n" +
                    "           and mark is not null and mark != ''\n" +
                    "         group by mark";
            sql = String.format(sql, "N", "F", domesticSql);

            String trainPartition = queryPartition(ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL);
            String carPartition = queryPartition(ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL);
            partitions.add(trainPartition);
            partitions.add(carPartition);
        } else {
            sql = String.format(sql, "I", "T", "");
        }


        partitions.forEach(f -> {
            paramList.add(f);
            paramList.add(dateTimeFormatter.format(consumeRankDTO.getStartTime()));
            paramList.add(dateTimeFormatter.format(consumeRankDTO.getEndTime()));
        });

        log.info("the consume rank dept sql: {} , paramList: {}", sql, paramList);
        return sql;
    }

    @Override
    public List<HotCityBO> hotCity(HotCityDTO hotCityDTO) {
        String sql = "select any(cityName) as cityName, cityId, sum(realPay) as totalPay\n" +
                "from (\n" +
                "         SELECT arrival_city_name AS cityName,\n" +
                "                arrival_city_id   AS cityId,\n" +
                "                real_pay          AS realPay\n" +
                "\n" +
                "         FROM olrpt_indexflightdownload_all\n" +
                "WHERE d = ?\n" +
                "           AND report_date >= ?\n" +
                "           AND report_date <= ?\n" +
                "  AND flight_class = '%s'\n" +
                "\n" +
                "         union all\n" +
                "\n" +
                "         SELECT city_name                        AS cityName,\n" +
                "                CAST(city as String)             AS cityId,\n" +
                "                CAST(real_pay_with_servicefee as Decimal(19, 4)) AS realPay\n" +
                "         FROM olrpt_indexhoteldownload_all\n" +
                "         WHERE d = ?\n" +
                "           AND report_date >= ?\n" +
                "           AND report_date <= ?\n" +
                "           AND order_status = '已完成' and is_oversea = '%s'\n" +
                "%s\n" +
                "         )\n" +
                "group by cityId\n" +
                "order by totalPay desc\n" +
                "limit 15";

        List<String> partitions = Lists.newArrayList(queryPartition(ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL), queryPartition(ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL));

        if ("国内".equals(hotCityDTO.getAreaType())) {
            String domesticSql = "\n" +
                    "         union all\n" +
                    "\n" +
                    "         SELECT arrival_city_name AS cityName,\n" +
                    "                arrival_city_id   AS cityId,\n" +
                    "                real_pay          AS realPay\n" +
                    "         FROM olrpt_indextraindownload_all\n" +
                    "         WHERE d = ?\n" +
                    "           AND report_date >= ?\n" +
                    "           AND report_date <= ?\n";
            sql = String.format(sql, "N", "F", domesticSql);

            partitions.add(queryPartition(ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL));
        } else {
            sql = String.format(sql, "I", "T", "");
        }

        List<Object> paramList = Lists.newArrayList();
        partitions.forEach(f -> {
            paramList.add(f);
            paramList.add(dateTimeFormatter.format(hotCityDTO.getStartTime()));
            paramList.add(dateTimeFormatter.format(hotCityDTO.getEndTime()));
        });


        List<HotCityBO> hotCities = findList(sql, paramList, HotCityBO.class);
        log.info("the hot city list: {}", hotCities);

        return hotCities;
    }

    @Override
    public StandardRateBO standardRate(StandardRateDTO standardRateDTO) {
        String sql = "select sum(rcTimes + noRcTimes) as totalTimes,\n" +
                "       sum(rcTimes)             as rcTimesSum,\n" +
                "       sum(noRcTimes)           as noRcTimesSum\n" +
                "from (\n" +
                "         SELECT COUNT(DISTINCT CASE WHEN is_rc = 'T' THEN order_id END)  AS rcTimes,\n" +
                "                COUNT(DISTINCT CASE WHEN is_rc != 'T' THEN order_id END) AS noRcTimes\n" +
                "         FROM olrpt_indexflightdownload_all\n" +
                "         WHERE d = ?\n" +
                "           AND report_date >= ?\n" +
                "           AND report_date <= ?\n" +
                "           and flight_class = '%s'\n" +
                "\n" +
                "         union all\n" +
                "         SELECT COUNT(DISTINCT CASE WHEN is_rc = 'T' THEN order_id END)  AS rcTimes,\n" +
                "                COUNT(DISTINCT CASE WHEN is_rc != 'T' THEN order_id END) AS noRcTimes\n" +
                "         FROM olrpt_indexhoteldownload_all\n" +
                "         WHERE d = ?\n" +
                "           AND report_date >= ?\n" +
                "           AND report_date <= ?\n" +
                "           and is_oversea = '%s'\n" +
                "\n" +
                "%s\n" +
                "         ) combined_data";


        String flightPartition = queryPartition(ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL);
        String hotelPartition = queryPartition(ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL);
        List<String> partitions = Lists.newArrayList(flightPartition, hotelPartition);

        if (standardRateDTO.domestic()) {
            String domesticSql = "         union all\n" +
                    "         SELECT COUNT(DISTINCT CASE WHEN quantity = 1 AND is_rc = 'T' THEN order_id END) AS rcTimes,\n" +
                    "                COUNT(DISTINCT CASE WHEN quantity = 1 AND is_rc != 'T' THEN order_id END) AS noRcTimes\n" +
                    "         FROM olrpt_indextraindownload_all\n" +
                    "         WHERE d = ?\n" +
                    "           AND report_date >= ?\n" +
                    "           AND report_date <= ?\n" +
                    "           AND order_status IN ('TA', 'RP', 'EP', 'EA')";
            sql = String.format(sql, "N", "F", domesticSql);

            String trainPartition = queryPartition(ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL);
            partitions.add(trainPartition);
        } else {
            sql = String.format(sql, "I", "T", "");
        }

        List<Object> paramList = Lists.newArrayList();
        partitions.forEach(f -> {
            paramList.add(f);
            paramList.add(dateTimeFormatter.format(standardRateDTO.getStartTime()));
            paramList.add(dateTimeFormatter.format(standardRateDTO.getEndTime()));
        });

        log.info("the standard rate sql: {} , paramList: {}", sql, paramList);

        StandardRateBO standardRateBO = findOne(sql, paramList, StandardRateBO.class);
        log.info("the standard rate: {}", standardRateBO);


        return standardRateBO;
    }

    @Override
    public List<TripRankBO> tripRank(TripRankDTO tripRankDTO) {
        String sql = "select route, routeName, count(distinct order_id) as count\n" +
                "from (\n" +
                "         select concat(departure_city_id, '-', arrival_city_id)     as route,\n" +
                "                concat(departure_city_name, '-', arrival_city_name) as routeName, order_id\n" +
                "         from olrpt_indexflightdownload_all\n" +
                "WHERE d = ?\n" +
                "           AND report_date >= ?\n" +
                "           AND report_date <= ?\n" +
                "  AND flight_class = '%s'\n" +
                "\n" +
                "%s\n" +
                "         )\n" +
                "group by route, routeName\n" +
                "order by count desc limit 5";


        List<String> partitions = Lists.newArrayList(queryPartition(ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL));
        if (tripRankDTO.domestic()) {
            String domesticSql = "union all\n" +
                    "\n" +
                    "         select concat(departure_city_id, '-', arrival_city_id)     as route,\n" +
                    "                concat(departure_city_name, '-', arrival_city_name) as routeName , order_id\n" +
                    "         from olrpt_indextraindownload_all\n" +
                    "         WHERE d = ?\n" +
                    "           AND report_date >= ?\n" +
                    "           AND report_date <= ?\n";

            sql = String.format(sql, "N", domesticSql);

            partitions.add(queryPartition(ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL));
        } else {
            sql = String.format(sql, "I", "");
        }


        List<Object> paramList = Lists.newArrayList();
        partitions.forEach(f -> {
            paramList.add(f);
            paramList.add(dateTimeFormatter.format(tripRankDTO.getStartTime()));
            paramList.add(dateTimeFormatter.format(tripRankDTO.getEndTime()));
        });

        log.info("the trip rank sql: {} , paramList: {}", sql, paramList);

        List<TripRankBO> list = findList(sql, paramList, TripRankBO.class);

        log.info("the trip rank list: {}", list);

        return list;
    }

    @Override
    public List<MapBO> map(MapDTO mapDTO) {
        return Collections.emptyList();
    }

    @Override
    public List<ConsumeRankBO> consumeRankCostCenter(ConsumeRankDTO consumeRankDTO) {
        List<Object> paramList = Lists.newArrayList();
        String sql = getConsumeRankSql(consumeRankDTO, paramList).replaceAll("mark", "cost_center1");
        log.info("the consume rank costCenter list: {}", sql);


        List<ConsumeRankBO> list = findList(sql, paramList, ConsumeRankBO.class);
        log.info("the consume rank costCenter list: {}", list);

        return list;
    }

    @Override
    public List<MapDestinationInfoBO> destinationInfo(MapDestinationInfoDTO mapDestinationInfoDTO) {
        return Collections.emptyList();
    }

    @Override
    public void getSaveAmount(TotalAmountDTO totalAmountDTO, AggregateResp aggregateResp) {
        // Find flight save amount
        String flightSql = "SELECT sum(CASE\n" +
                "               WHEN class_type = 'Y' AND flight_class = '%s' THEN coalesce(save_amount_3c, 0)\n" +
                "               ELSE 0 END)            AS saveAmount3c,\n" +
                "       sum(CASE\n" +
                "               WHEN class_type = 'Y' AND flight_class = '%s' THEN coalesce(save_amount_premium, 0)\n" +
                "               ELSE 0 END)            AS saveAmountPremium,\n" +
                "       SUM(coalesce(saving_price, 0)) AS controlSave\n" +
                "FROM olrpt_indexflightdownload_all\n" +
                "WHERE d = ?\n" +
                "           AND report_date >= ?\n" +
                "           AND report_date <= ?\n" +
                "  AND flight_class = '%s'\n" +
                "  AND class_type = 'Y'\n" +
                "  AND audited <> 'F'";
        if ("国内".equals(totalAmountDTO.getAreaType())) {
            flightSql = String.format(flightSql, "N", "N", "N");
        } else {
            flightSql = String.format(flightSql, "I", "T", "T");
        }

        List<Object> flightParmList = new ArrayList<>();
        flightParmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        flightParmList.add(dateTimeFormatter.format(totalAmountDTO.getStartTime()));
        flightParmList.add(dateTimeFormatter.format(totalAmountDTO.getEndTime()));


        SaveAmountBO flightSaveAmountBO = findOne(flightSql, flightParmList, SaveAmountBO.class);
        log.info("find flight save amount: {}", flightSaveAmountBO);


        // Find hotel save amount
        String hotelSql = "SELECT 123.2        AS saveAmount3c,\n" +
                "       SUM(coalesce(save_amount_promotion, 0)) AS saveAmountPromotion,\n" +
                "       SUM(coalesce(save_amount_premium, 0))   AS saveAmountPremium,\n" +
                "       SUM(coalesce(saving_price, 0))          AS controlSave\n" +
                "FROM olrpt_indexhoteldownload_all\n" +
                "WHERE d = ?\n" +
                "           AND report_date >= ?\n" +
                "           AND report_date <= ?\n" +
                "  AND is_oversea = '%s'\n" +
                "  AND order_status = '已完成'";

        // 酒店类型 F(国内) O(港澳台)T(海外)
        if ("国内".equals(totalAmountDTO.getAreaType())) {
            hotelSql = String.format(hotelSql, "F");
        } else {
            hotelSql = String.format(hotelSql, "T");
        }

        List<Object> hotelParamList = new ArrayList<>();
        hotelParamList.add(queryPartition(ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD));
        hotelParamList.add(dateTimeFormatter.format(totalAmountDTO.getStartTime()));
        hotelParamList.add(dateTimeFormatter.format(totalAmountDTO.getEndTime()));

        SaveAmountBO hotelSaveAmountBO = findOne(hotelSql, hotelParamList, SaveAmountBO.class);
        log.info("find hotel save amount: {}", hotelSaveAmountBO);


        TotalInfo totalInfo = new TotalInfo();
        totalInfo.setFlightCount(flightSaveAmountBO.getFlightSaveAmount());
        totalInfo.setHotelCount(hotelSaveAmountBO.getHotelSaveAmount());

        totalInfo.setTotal(Optional.ofNullable(totalInfo.getFlightCount()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(totalInfo.getHotelCount()).orElse(BigDecimal.ZERO)));
        aggregateResp.setTrafficSaveAmount(totalInfo);
    }

    @Override
    public List<ConsumeRankBO> consumeLegalRank(ConsumeRankDTO consumeRankDTO) {
        List<Object> paramList = Lists.newArrayList();

        String sql = "SELECT name,\n" +
                "       SUM(count) AS count\n" +
                "FROM (\n" +
                "         SELECT cost_center_legal_entity_name         AS name,\n" +
                "                SUM(real_pay) AS count\n" +
                "         FROM olrpt_indexflightdownload_all\n" +
                "         WHERE d = ?\n" +
                "           AND reportdate >= ?\n" +
                "           AND reportdate <= ?\n" +
                "           and flight_class = '%s'\n" +
                "         and cost_center_legal_entity_name is not null and cost_center_legal_entity_name != ''\n" +
                "         group by cost_center_legal_entity_name\n" +
                "\n" +
                "         UNION ALL\n" +
                "\n" +
                "         SELECT cost_center_legal_entity_name         AS name,\n" +
                "                SUM(CAST(real_pay as decimal(19, 4))) AS count\n" +
                "         FROM olrpt_indexhoteldownload_all\n" +
                "         WHERE d = ?\n" +
                "           AND report_date >= ?\n" +
                "           AND report_date <= ?\n" +
                "           and is_oversea = '%s'\n" +
                "         and cost_center_legal_entity_name is not null and cost_center_legal_entity_name != ''\n" +
                "         group by cost_center_legal_entity_name\n" +
                "%s\n" +
                "         ) AS combined_data\n" +
                "GROUP BY name\n" +
                "order by count DESC limit 6";

        String flightPartition = queryPartition(ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL);
        String hotelPartition = queryPartition(ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL);
        List<String> partitions = Lists.newArrayList(flightPartition, hotelPartition);

        if (consumeRankDTO.domestic()) {
            String domesticSql = "\n" +
                    "         UNION ALL\n" +
                    "\n" +
                    "         SELECT cost_center_legal_entity_name         AS name,\n" +
                    "                SUM(real_pay) AS count\n" +
                    "         FROM olrpt_indextraindownload_all\n" +
                    "         WHERE d = ?\n" +
                    "           AND report_date >= ?\n" +
                    "           AND report_date <= ?\n" +
                    "           and cost_center_legal_entity_name is not null and cost_center_legal_entity_name != ''\n" +
                    "         group by cost_center_legal_entity_name\n" +
                    "\n" +
                    "         UNION ALL\n" +
                    "\n" +
                    "         SELECT cost_center_legal_entity_name         AS name,\n" +
                    "                SUM(real_pay) AS count\n" +
                    "         FROM olrpt_indexcardownload_all\n" +
                    "         WHERE d = ?\n" +
                    "           AND order_date >= ?\n" +
                    "           AND order_date <= ?\n" +
                    "           and cost_center_legal_entity_name is not null and cost_center_legal_entity_name != ''\n" +
                    "         group by cost_center_legal_entity_name";
            sql = String.format(sql, "N", "F", domesticSql);

            String trainPartition = queryPartition(ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL);
            String carPartition = queryPartition(ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL);
            partitions.add(trainPartition);
            partitions.add(carPartition);
        } else {
            sql = String.format(sql, "I", "T", "");
        }


        partitions.forEach(f -> {
            paramList.add(f);
            paramList.add(dateTimeFormatter.format(consumeRankDTO.getStartTime()));
            paramList.add(dateTimeFormatter.format(consumeRankDTO.getEndTime()));
        });


        List<ConsumeRankBO> list = findList(sql, paramList, ConsumeRankBO.class);
        log.info("the consume rank legal list: {}", list);

        return list;
    }
}
