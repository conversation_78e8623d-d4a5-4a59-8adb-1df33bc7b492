package com.corpgovernment.resource.schedule.onlinereport.module.behaivor;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class CarTravelMarkMetricDTO {

    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;

    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;

    @Column(name = "settlementAccntamt")
    @Type(value = Types.DECIMAL)
    private BigDecimal settlementAccntamt;

    @Column(name = "settlementPersonamt")
    @Type(value = Types.DECIMAL)
    private BigDecimal settlementPersonamt;

    @Column(name = "sumTotalQuantity")
    @Type(value = Types.INTEGER)
    private Integer sumTotalQuantity;

    @Column(name = "sumTotalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumTotalAmount;
}
