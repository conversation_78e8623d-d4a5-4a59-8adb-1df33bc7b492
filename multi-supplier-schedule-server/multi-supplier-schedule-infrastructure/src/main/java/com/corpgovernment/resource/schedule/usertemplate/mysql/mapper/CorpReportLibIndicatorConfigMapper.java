package com.corpgovernment.resource.schedule.usertemplate.mysql.mapper;

import com.corpgovernment.resource.schedule.usertemplate.mysql.entity.CorpReportLibIndicatorConfigDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CorpReportLibIndicatorConfigMapper {

    int insert(CorpReportLibIndicatorConfigDo record);

    int insertSelective(CorpReportLibIndicatorConfigDo record);

    CorpReportLibIndicatorConfigDo selectByPrimaryKey(Long id);

    CorpReportLibIndicatorConfigDo selectByReportCardNo(@Param("cardNo") String cardNo);

    List<CorpReportLibIndicatorConfigDo> selectByReportCardNoAndOperator(@Param("cardNo")String cardNo, @Param("operator")String operator);

    int updateByPrimaryKeySelective(CorpReportLibIndicatorConfigDo record);

    int updateByPrimaryKey(CorpReportLibIndicatorConfigDo record);

    int delByReportCardNo(@Param("cardNo") String cardNo, @Param("operator") String operator);
}