/*
package com.corpgovernment.resource.schedule.onlinereport.utils;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.google.common.base.Stopwatch;
import credis.java.client.util.CacheFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

*/
/**
 * @Description: Redis工具类
 * <AUTHOR>
 * @Date 2019/3/1
 *//*

@Slf4j
public class RedisCacheUtils {

    public static credis.java.client.CacheProvider cacheProvider = null;

    private RedisCacheUtils() {
    }

    public static void init(String redisName) {
        cacheProvider = CacheFactory.getProvider(redisName);
    }

    public static <T> T get(String key, Class<T> cls) {
        if (Objects.isNull(cacheProvider)) {
            init(OrpConstants.REDIS_CLUSTER);
        }
        String retVal = cacheProvider.get(key);
        return OrpJsonUtils.jsonTobject(retVal, cls);

    }

    public static String get(String key) {
        if (Objects.isNull(cacheProvider)) {
            init(OrpConstants.REDIS_CLUSTER);
        }
        return cacheProvider.get(key);
    }

    public static boolean set(String key, String value) {
        if (Objects.isNull(cacheProvider)) {
            init(OrpConstants.REDIS_CLUSTER);
        }
        return cacheProvider.set(key, value);
    }

    */
/**
     * 事务操作-设置带时间的缓存到redis
     *
     * @param key
     * @param value
     * @param times
     * @return
     *//*

    public static boolean setValueExpire(String key, String value, int times) {
        */
/**
         * 保证事务
         *//*

        return cacheProvider.setex(key, times, value);
    }

    public static boolean hmSet(String key, Map<String, String> hash, boolean logIsOn) {
        Stopwatch watchTotal = Stopwatch.createStarted();
        boolean retVal = cacheProvider.hmset(key, hash);
        if (logIsOn) {
            log.info("redis-hmset take time ：" + watchTotal.elapsed(TimeUnit.MILLISECONDS) + "ms");
        }
        return retVal;
    }

    public static <T> List<T> hmGet(String var1, Class<T> tClass, String... var2) {
        List<String> vals = cacheProvider.hmget(var1, var2);
        if (vals != null && !vals.isEmpty()) {
            List<T> listData = new ArrayList<>();
            for (int i = OrpConstants.ZERO; i < vals.size(); i++) {
                T obj = OrpJsonUtils.jsonTobject(vals.get(i), tClass);
                listData.add(obj);
            }
            return listData;
        }
        return null;
    }

    public static List<String> hvaLs(String key, boolean logIsOn) {
        Stopwatch watchTotal = Stopwatch.createStarted();
        List<String> retVal = cacheProvider.hvals(key);
        if (logIsOn) {
            log.info("redis-hvals take time ：" + watchTotal.elapsed(TimeUnit.MILLISECONDS) + "ms");
        }
        return retVal;
    }

    */
/**
     * 删除redis中的key
     *
     * @param key
     * @param logIsOn
     * @return
     *//*

    public static boolean delKey(String key, boolean logIsOn) {
        Stopwatch watchTotal = Stopwatch.createStarted();
        boolean isSuccess = cacheProvider.del(key);
        if (logIsOn) {
            log.info("redis-delKey take time ：" + watchTotal.elapsed(TimeUnit.MILLISECONDS) + "ms");
        }
        return isSuccess;
    }

    */
/**
     * 将list数组插入redis list中
     *
     * @param listKey
     * @param items
     * @param logIsOn
     * @return
     *//*

    public static <T> void addItemsToList(String listKey, List<T> items, boolean logIsOn) {
        Stopwatch watchTotal = Stopwatch.createStarted();
        for (T item : items) {
            cacheProvider.lpush(listKey, OrpJsonUtils.objectToString(item));
        }
        if (logIsOn) {
            log.info("redis-addItemsToList take time ：" + watchTotal.elapsed(TimeUnit.MILLISECONDS) + "ms");
        }
    }

    public static void addStringItemsToList(String listKey, List<String> items, boolean logIsOn) {
        Stopwatch watchTotal = Stopwatch.createStarted();
        for (int i = OrpConstants.ZERO; i < items.size(); i++) {
            cacheProvider.lpush(listKey, items.get(i));
        }
        if (logIsOn) {
            log.info("redis-addItemsToList take time ：" + watchTotal.elapsed(TimeUnit.MILLISECONDS) + "ms");
        }
    }

    public static List<String> addStringItemsToList(String listKey, List<String> items, boolean logIsOn, List<String> listFailed) {
        Stopwatch watchTotal = Stopwatch.createStarted();
        for (int i = OrpConstants.ZERO; i < items.size(); i++) {
            if (cacheProvider.lpush(listKey, items.get(i)) <= OrpConstants.ZERO) {
                listFailed.add(items.get(i));
            }
        }
        if (logIsOn) {
            log.info("redis-addItemsToList take time ：" + watchTotal.elapsed(TimeUnit.MILLISECONDS) + "ms");
        }
        return listFailed;
    }

    public static <T> void set(String key, T value, boolean writeLog) {
        String jsonString = OrpJsonUtils.objectToString(value);
        cacheProvider.set(key, jsonString);
        if (writeLog) {
            log.info("setToRedis", "key:" + key + " value:" + jsonString);
        }
    }

    public static <T> void set(String key, String value, int expire, boolean writeLog) {
        String jsonString = OrpJsonUtils.objectToString(value);
        cacheProvider.setex(key, expire, value);
        if (writeLog) {
            log.info("setToRedis", "key:" + key + " value:" + jsonString);
        }
    }

    public static <T> T get(String key, boolean logIsOn, Class<T> tClass) {
        Stopwatch watchTotal = Stopwatch.createStarted();
        String item = cacheProvider.get(key);
        if (logIsOn) {
            log.info("redis-getListAllItems take time ：" + watchTotal.elapsed(TimeUnit.MILLISECONDS) + "ms");
        }
        if (!item.isEmpty()) {
            T obj = OrpJsonUtils.jsonTobject(item, tClass);
            return obj;
        }
        return null;
    }

    */
/**
     * 批量 key 获取
     *
     * @param clazz
     * @param keys
     * @param <T>
     * @return
     *//*

    public static <T> List<T> mGet(Class<T> clazz, List<String> keys) {
        List<String> datas = mGet(keys);
        if (OrpConstants.ZERO == datas.size()) {
            return Collections.emptyList();
        }
        List<T> list = new ArrayList<>();
        if (!datas.isEmpty()) {
            for (int i = OrpConstants.ZERO; i < datas.size(); i++) {
                String item = datas.get(i);
                if (item == null || item.isEmpty()) {
                    continue;
                }
                T obj = OrpJsonUtils.jsonTobject(item, clazz);
                list.add(obj);
            }
        }
        return list;
    }

    */
/**
     * 批量 keys 获取
     *
     * @param keys
     * @return
     *//*

    public static List<String> mGet(List<String> keys) {
        String[] keyArr = new String[keys.size()];
        return cacheProvider.mget(keys.toArray(keyArr));
    }

    */
/**
     * 读取list中所有数据
     *
     * @param listKey
     * @param logIsOn
     * @return
     *//*

    public static <T> List<T> getListAllItems(String listKey, boolean logIsOn, Class<T> tclass) {
        Stopwatch watchTotal = Stopwatch.createStarted();
        List<String> items = cacheProvider.sortbyalpha(listKey);
        if (logIsOn) {
            log.info("redis-getListAllItems take time ：" + watchTotal.elapsed(TimeUnit.MILLISECONDS) + "ms");
        }
        if (!items.isEmpty()) {

            List<T> objItems = new ArrayList<>();

            for (String item : items) {
                T obj = OrpJsonUtils.jsonTobject(item, tclass);
                objItems.add(obj);
            }
            return objItems;
        }
        return new ArrayList<T>();
    }

    */
/**
     * 读取list中所有数据
     *
     * @param key
     * @return
     *//*

    public static List<String> getStringItemsFromList(String key) {
        long size = cacheProvider.llen(key);
        if (size <= OrpConstants.ZERO) {
            return null;
        }
        return cacheProvider.lrange(key, OrpConstants.ZERO, size);
    }

    public static List<String> getStringItemsFromList(String key, long start, long end) {
        return cacheProvider.lrange(key, start, end);
    }

    */
/**
     * 读取list长度
     *
     * @param key
     * @return
     *//*

    public static Long lLen(String key) {
        return cacheProvider.llen(key);
    }

    */
/**
     * 根据参数 count 的值，移除列表中与参数 val 相等的元素。
     *
     * @param key
     * @param count count > 0 : 从表头开始向表尾搜索，移除与 value 相等的元素，数量为 count 。 count < 0 : 从表尾开始向表头搜索，移除与 value 相等的元素，数量为 count
     *              的绝对值。 count = 0 : 移除表中所有与 value 相等的值。
     * @param val
     * @return
     *//*

    public static Long lRem(String key, long count, String val) {
        return cacheProvider.lrem(key, count, val);
    }

}
*/
