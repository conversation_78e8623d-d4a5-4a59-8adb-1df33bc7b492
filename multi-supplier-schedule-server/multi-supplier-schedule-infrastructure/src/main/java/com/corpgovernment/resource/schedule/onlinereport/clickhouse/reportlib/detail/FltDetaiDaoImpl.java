package com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail;

import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.TimeFilterTypeInfoDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import onlinereport.enums.FlightClassTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-21 13:39
 * @desc
 */
@Service
@Repository
public class FltDetaiDaoImpl extends AbstractDetaiDao {

    @Override
    protected ClickHouseTable getClickHouseTable(boolean isBluespace) {
        if (isBluespace) {
            return ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        }
        return ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
    }

    protected String buildPreSqlTime(List<TimeFilterTypeInfoDTO> timeFilterTypeInfoList, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isEmpty(timeFilterTypeInfoList)) {
            return sqlBuffer.toString();
        }
        for (TimeFilterTypeInfoDTO timeFilterTypeInfo : timeFilterTypeInfoList) {
            // 预订时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate") || StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
                sqlBuffer.append(buildTimeFilter(timeFilterTypeInfo, parmList));
            }
            // 起飞时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "usedate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    sqlBuffer.append(" and ").append("subString(takeoff_time, 1, 10)").append(" >= ? ");
                    parmList.add(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    sqlBuffer.append(" and ").append("subString(takeoff_time, 1, 10)").append(" <= ? ");
                    parmList.add(timeFilterTypeInfo.getEndTime());
                }
            }
        }
        return sqlBuffer.toString();
    }

    // flight passenger_uid(卡号), passenger_name
    @Override
    protected String buildPreSqlPassenger(List<String> passengers, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(passengers)) {
            sqlBuffer.append(" and (");
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionNoAnd("passenger_name", passengers, parmList,true));
            sqlBuffer.append(" or ");
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionNoAnd("upper(passenger_uid)", passengers, parmList));
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }

    @Override
    protected String buildProductTypeCondition(String productType) {
        return getFlightClassConditionWithAudited(productType);
    }

    @Override
    protected String buildOrderBySql(String pos, String blueSpace) {
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            return " order by order_id, passenger_uid, flight_city, is_refund desc";
        } else {
            return StringUtils.EMPTY;
        }
    }

    @Override
    protected String buildPreSqlExceedStandard(List<String> exceedStandard, List<Object> parmList) {
        if (CollectionUtils.isEmpty(exceedStandard) || exceedStandard.size() == 2) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.equalsIgnoreCase(exceedStandard.get(0), "T")) {
            return " AND is_rc = 'T'";
        }
        if (StringUtils.equalsIgnoreCase(exceedStandard.get(0), "F")) {
            return " AND is_rc = 'F'";
        }
        return StringUtils.EMPTY;
    }

    /**
     * @return
     */
    @Override
    protected String buildPreSqlSpecial() {
        return StringUtils.EMPTY;
    }

    @Override
    protected String buildContractTypeCondition(String contractType) {
        return getFlightContractTypeCondition(contractType);
    }

    protected String buildClassTypeCondition(String classType) {
        if (StringUtils.isNotEmpty(classType) && Arrays.stream(FlightClassTypeEnum.values()).map(Enum::toString)
                .anyMatch(s -> s.equalsIgnoreCase(classType))) {
            return String.format(" and class_type = '%s'", classType);
        }
        return StringUtils.EMPTY;
    }

    protected String buildFlightAirlineCondition(List<String> airlines, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(airlines)) {
            sqlBuffer.append(" and ");
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionNoAnd("upper(ticketairline)", airlines, parmList));
        }
        return sqlBuffer.toString();
    }

    protected String buildFlighNoCondition(List<String> flightNos, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(flightNos)) {
            sqlBuffer.append(" and ");
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionNoAnd("upper(flight_no)", flightNos, parmList));
        }
        return sqlBuffer.toString();
    }
}
