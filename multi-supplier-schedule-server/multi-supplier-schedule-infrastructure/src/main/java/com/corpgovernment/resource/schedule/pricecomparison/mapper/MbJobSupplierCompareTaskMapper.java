package com.corpgovernment.resource.schedule.pricecomparison.mapper;
import java.util.Date;
import com.corpgovernment.resource.schedule.pricecomparison.mysql.entity.MbJobSupplierCompareTask;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MbJobSupplierCompareTaskMapper {
    int insertList(@Param("list")List<MbJobSupplierCompareTask> list);

    List<MbJobSupplierCompareTask> selectByTaskTypeAndDatachangeCreatetime(@Param("offset") int offset, @Param("pageSize")int pageSize,
                                                                           @Param("taskType")String taskType,
                                                                           @Param("datachangeCreatetimeStart")Date datachangeCreatetimeStart,
                                                                           @Param("datachangeCreatetimeEnd")Date datachangeCreatetimeEnd,
                                                                           @Param("taskStatus")Integer taskStatus);
    Long countByTaskTypeAndDatachangeCreatetime(@Param("taskType")String taskType,
                                                                           @Param("datachangeCreatetimeStart")Date datachangeCreatetimeStart,
                                                                           @Param("datachangeCreatetimeEnd")Date datachangeCreatetimeEnd);
    int updateTaskStatusById(@Param("updatedTaskStatus")Integer updatedTaskStatus,@Param("id")Long id);

    List<MbJobSupplierCompareTask> selectByTaskId(@Param("taskId")String taskId);
}