package com.corpgovernment.resource.schedule.onlinereport.saveanalysis;


import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendRequest;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 11:02
 * @description：
 * @modified By：
 * @version: $
 */
public abstract class AbstractSaveAnalysisTrend implements SaveAnalysisTrend<OnlineReportTrendRequest> {

    public OnlineTrendRequestDto map(OnlineReportTrendRequest request, BaseQueryConditionMapper mapper) {
        // 机票 协议类型 或总计
        OnlineTrendRequestDto onlineTrendRequestDto = new OnlineTrendRequestDto();
        onlineTrendRequestDto.setStartTime(request.getBasecondition().startTime);
        onlineTrendRequestDto.setEndTime(request.getBasecondition().endTime);
        onlineTrendRequestDto.setBu(request.queryBu.name());
        onlineTrendRequestDto.setExtParams(request.getExtData());
        onlineTrendRequestDto.setDateDimension(request.dateDimension.name());
        onlineTrendRequestDto.setBaseQueryCondition(mapper.toDTO(request.basecondition));
        onlineTrendRequestDto.setQueryType(request.getQueryType().name());
        onlineTrendRequestDto.setProductType(request.getProductType());
        return onlineTrendRequestDto;
    }
}
