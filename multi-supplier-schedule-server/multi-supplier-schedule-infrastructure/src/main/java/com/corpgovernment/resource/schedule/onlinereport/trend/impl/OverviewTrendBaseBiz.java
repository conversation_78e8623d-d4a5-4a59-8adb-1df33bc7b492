package com.corpgovernment.resource.schedule.onlinereport.trend.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FltCarbonEmissionTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendPoint;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendRequest;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 10:25
 * @description：
 * @modified By：
 * @version: $
 */
public class OverviewTrendBaseBiz {

    public OnlineTrendRequestDto map(OnlineReportTrendRequest request, BaseQueryConditionMapper mapper) {
        OnlineTrendRequestDto onlineTrendRequestDto = new OnlineTrendRequestDto();
        onlineTrendRequestDto.setStartTime(request.getBasecondition().startTime);
        onlineTrendRequestDto.setEndTime(request.getBasecondition().endTime);
        onlineTrendRequestDto.setBu(request.queryBu.name());
        onlineTrendRequestDto.setExtParams(request.extData);
        onlineTrendRequestDto.setDateDimension(request.dateDimension.name());
        int yoyDuration = Integer.parseInt(request.getExtData().getOrDefault("yoyDuration", "12"));
        onlineTrendRequestDto.setYoyDuration(yoyDuration);
        String lang = request.getExtData().getOrDefault("lang", OrpConstants.EMPTY);
        onlineTrendRequestDto.setLang(lang);
        int offset = 1;
        String startTime = request.getBasecondition().startTime;
        String endTime = request.getBasecondition().endTime;
        DatePair datePair = timeSpanAdjust(startTime, endTime, request.getDateDimension());
        if (request.getDateDimension() == QueryReportAggDateDimensionEnum.quarter) {
            offset = 3;
        } else if (request.getDateDimension() == QueryReportAggDateDimensionEnum.half) {
            offset = 6;
        }
        onlineTrendRequestDto.setChainStartTime(OrpDateTimeUtils.startTimeMom(datePair.start, offset));
        onlineTrendRequestDto.setChainEndTime(OrpDateTimeUtils.endTimeMom(datePair.end, offset));
        onlineTrendRequestDto.setYoyStartTime(OrpDateTimeUtils.startTimeMom(datePair.start, yoyDuration));
        onlineTrendRequestDto.setYoyEndTime(OrpDateTimeUtils.endTimeMom(datePair.end, yoyDuration));
        onlineTrendRequestDto.setBaseQueryCondition(mapper.toDTO(request.basecondition));
        onlineTrendRequestDto.setQueryType(request.getQueryType().name());
        onlineTrendRequestDto.setMethod("queryOnlineReportAccOtherBuTrend");
        return onlineTrendRequestDto;
    }

    public OnlineTrendRequestDto mapFlt(FltCarbonEmissionTrendRequest request, BaseQueryConditionMapper mapper) {
        OnlineTrendRequestDto trendReqDto = new OnlineTrendRequestDto();
        trendReqDto.setStartTime(request.getBasecondition().startTime);
        trendReqDto.setEndTime(request.getBasecondition().endTime);
        trendReqDto.setExtParams(Maps.newHashMap());
        trendReqDto.setDateDimension(request.dateDimension.name());
        trendReqDto.setDimensionType(request.getDimensionType());
        /**
         * 同比去年
         */
        int yoyDuration = 12;
        trendReqDto.setYoyDuration(yoyDuration);
        trendReqDto.setLang(request.getLang());
        int offset = 1;
        String startTime = request.getBasecondition().startTime;
        String endTime = request.getBasecondition().endTime;
        DatePair datePair = timeSpanAdjust(startTime, endTime, request.getDateDimension());
        if (request.getDateDimension() == QueryReportAggDateDimensionEnum.quarter) {
            offset = 3;
        } else if (request.getDateDimension() == QueryReportAggDateDimensionEnum.half) {
            offset = 6;
        }
        trendReqDto.setChainStartTime(OrpDateTimeUtils.startTimeMom(datePair.start, offset));
        trendReqDto.setChainEndTime(OrpDateTimeUtils.endTimeMom(datePair.end, offset));
        trendReqDto.setYoyStartTime(OrpDateTimeUtils.startTimeMom(datePair.start, yoyDuration));
        trendReqDto.setYoyEndTime(OrpDateTimeUtils.endTimeMom(datePair.end, yoyDuration));
        trendReqDto.setBu(request.getQueryBu().name());
        trendReqDto.setBaseQueryCondition(mapper.toDTO(request.basecondition));

        trendReqDto.setMethod("FltCarbonEmissionTrend");
        /**
         * 碳排放
         */
        trendReqDto.setQueryType("carbonEmission");
        trendReqDto.setProductType(request.getProductType());
        return trendReqDto;
    }

    protected List<Field> getField(Class clazz) {
        return Arrays.stream(clazz.getDeclaredFields()).filter(f -> !(f.getName().equals("date") || f.getName().equals("dim"))).collect(Collectors.toList());
    }

    protected List<Field> needRound2Filed(Class clazz) {
        return Arrays.stream(clazz.getDeclaredFields())
                .filter(f -> !(f.getName().equals("date") || f.getName().equals("dim")))
                .filter(f -> !(f.getName().toLowerCase().contains("yoy") || f.getName().toLowerCase().contains("chain")))
                .collect(Collectors.toList());
    }

    public BigDecimal field2Value(Object target, Field filed) {
        BigDecimal value = new BigDecimal(OrpConstants.ZERO_CHAR_1);
        try {
            filed.setAccessible(Boolean.TRUE);
            value = Optional.ofNullable(filed.get(target)).map(t -> (BigDecimal) t).orElse(new BigDecimal(OrpConstants.ZERO_CHAR_1));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    public BigDecimal round2(BigDecimal d) {
        return d.setScale(OrpConstants.TWO, BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal round0(BigDecimal d) {
        return d.setScale(OrpConstants.ZERO, BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal round2JP(BigDecimal d) {
        return d.setScale(OrpConstants.ZERO, BigDecimal.ROUND_HALF_UP);
    }

    public static String dateFormat(String date, QueryReportAggDateDimensionEnum span) {
        String formatDate = date;
        if (span == QueryReportAggDateDimensionEnum.month) {
            formatDate = date.substring(0, 4) + "-" + date.substring(5, 7);
        }
        if (span == QueryReportAggDateDimensionEnum.quarter) {
            // 1 4 7 10
            formatDate = date.substring(0, 4) + "-Q" + (Integer.parseInt(date.substring(5, 7)) / 3 + 1);
        }
        if (span == QueryReportAggDateDimensionEnum.half) {
            // 1 7
            formatDate = date.substring(0, 4) + "-H" + (Integer.parseInt(date.substring(5, 7)) / 6 + 1);
        }
        return formatDate;
    }

    /**
     * 根据所选 日期范围 及 聚合维度 调整时间跨度 算同环比
     * 季度 起始日期所在季度的首日 结束日期 所在季度的末日
     * 半年 起始日期所在半年的首日 结束日期 所在半年的末日
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public DatePair timeSpanAdjust(String startTime, String endTime, QueryReportAggDateDimensionEnum span) {
        DatePair datePair = new DatePair(startTime, endTime);
        if (span == QueryReportAggDateDimensionEnum.quarter) {
            datePair.setStart(getQuarterStartTime(startTime));
            datePair.setEnd(getQuarterEndTime(endTime));
        }
        if (span == QueryReportAggDateDimensionEnum.half) {
            datePair.setStart(getHalfStartTime(startTime));
            datePair.setEnd(getHalfEndTime(endTime));
        }
        return datePair;
    }

    public static String getHalfStartTime(String date) {
        if (Integer.parseInt(date.substring(5, 7)) < 7) {
            return date.substring(0, 5) + "01-01";
        }
        return date.substring(0, 5) + "07-01";
    }

    public static String getHalfEndTime(String date) {
        if (Integer.parseInt(date.substring(5, 7)) < 7) {
            return date.substring(0, 5) + "06-30";
        }
        return date.substring(0, 5) + "12-31";
    }

    public static String getQuarterStartTime(String date) {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
        String now = null;
        try {
            Date dateTime = shortSdf.parse(date);
            c.setTime(dateTime);
            int currentMonth = c.get(Calendar.MONTH) + 1;
            if (currentMonth <= 3) {
                c.set(Calendar.MONTH, 0);
            } else if (currentMonth <= 6) {
                c.set(Calendar.MONTH, 3);
            } else if (currentMonth <= 9) {
                c.set(Calendar.MONTH, 6);
            } else if (currentMonth <= 12) {
                c.set(Calendar.MONTH, 9);
            }
            c.set(Calendar.DATE, 1);
            now = shortSdf.format(c.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return now;
    }

    public static String getQuarterEndTime(String date) {
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            cal.setTime(shortSdf.parse(getQuarterStartTime(date)));
            cal.add(Calendar.MONTH, 2);
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return shortSdf.format(cal.getTime());
    }

    public static String getWeekStartTime(String date) {
        if (Integer.parseInt(date.substring(5, 7)) < 7) {
            return date.substring(0, 5) + "01-01";
        }
        return date.substring(0, 5) + "07-01";
    }

    public static List<String> getDateRangeFullItem(String startTime, String endTime, QueryReportAggDateDimensionEnum span) {
        List<String> allDate = new ArrayList<>();
        if (span == QueryReportAggDateDimensionEnum.month) {
            List<String> monthsShort = OrpDateTimeUtils.getMonthBetween(startTime.substring(0, 7), endTime.substring(0, 7));
            return monthsShort.stream().map(month -> month + "-01").collect(Collectors.toList());
        }
        if (span == QueryReportAggDateDimensionEnum.quarter) {
            String startQuarter = getQuarterStartTime(startTime);
            String endQuarter = getQuarterStartTime(endTime);
            allDate.add(startQuarter);
            while (startQuarter.compareTo(endQuarter) < 0) {
                startQuarter = OrpDateTimeUtils.startTimeMom(startQuarter, -3);
                allDate.add(startQuarter);
            }
        }
        if (span == QueryReportAggDateDimensionEnum.half) {
            String startHalf = getHalfStartTime(startTime);
            String endHalf = getHalfStartTime(endTime);
            allDate.add(startHalf);
            while (startHalf.compareTo(endHalf) < 0) {
                startHalf = OrpDateTimeUtils.startTimeMom(startHalf, -6);
                allDate.add(startHalf);
            }
        }
        if (span == QueryReportAggDateDimensionEnum.day) {
            List<String> dayShort = OrpDateTimeUtils.getdayBetween(startTime, endTime);
            return dayShort.stream().collect(Collectors.toList());
        }
        if (span == QueryReportAggDateDimensionEnum.week) {
            String startWeek = OrpDateTimeUtils.getFirstDayOfWeek(startTime);
            String endWeek = OrpDateTimeUtils.getFirstDayOfWeek(endTime);
            allDate.add(startWeek);
            while (startWeek.compareTo(endWeek) < 0) {
                startWeek = OrpDateTimeUtils.dateOffset(startWeek, OrpConstants.SEVEN);
                allDate.add(startWeek);
            }
        }
        return allDate;
    }

    protected static boolean needStartRemoveYoyChain(String dateTime, String dateType, QueryReportAggDateDimensionEnum span) {
        boolean need = false;
        if (span == QueryReportAggDateDimensionEnum.month) {
            if (dateType.equalsIgnoreCase(OrpConstants.START)) {
                return OrpDateTimeUtils.getLocalDateStartTimeByString(dateTime).getDayOfMonth() != OrpConstants.ONE;
            } else {
                LocalDateTime endTime = OrpDateTimeUtils.getLocalDateEndTimeByString(dateTime);
                return endTime.getDayOfMonth() != endTime.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate().getDayOfMonth();
            }
        }
        if (span == QueryReportAggDateDimensionEnum.quarter) {
            if (dateType.equalsIgnoreCase(OrpConstants.START)) {
                LocalDateTime startTime = OrpDateTimeUtils.getLocalDateStartTimeByString(dateTime);
                return !(startTime.getDayOfMonth() == OrpConstants.ONE && (startTime.getMonthValue() == OrpConstants.ONE ||
                        startTime.getMonthValue() == OrpConstants.FOUR || startTime.getMonthValue() == OrpConstants.SEVEN ||
                        startTime.getMonthValue() == OrpConstants.TEN));
            } else {
                LocalDateTime endTime = OrpDateTimeUtils.getLocalDateEndTimeByString(dateTime);
                return !(endTime.getDayOfMonth() == endTime.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate().getDayOfMonth() &&
                        (endTime.getMonthValue() == OrpConstants.THREE || endTime.getMonthValue() == OrpConstants.SIX ||
                                endTime.getMonthValue() == OrpConstants.NINE || endTime.getMonthValue() == OrpConstants.TWELVE));
            }
        }
        if (span == QueryReportAggDateDimensionEnum.half) {
            if (dateType.equalsIgnoreCase(OrpConstants.START)) {
                LocalDateTime startTime = OrpDateTimeUtils.getLocalDateStartTimeByString(dateTime);
                return !(startTime.getDayOfMonth() == OrpConstants.ONE && (startTime.getMonthValue() == OrpConstants.ONE || startTime.getMonthValue() == OrpConstants.SEVEN));
            } else {
                LocalDateTime endTime = OrpDateTimeUtils.getLocalDateEndTimeByString(dateTime);
                return !(endTime.getDayOfMonth() == endTime.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate().getDayOfMonth() &&
                        (endTime.getMonthValue() == OrpConstants.SIX || endTime.getMonthValue() == OrpConstants.TWELVE));
            }
        }
        return need;
    }

    /**
     * 周转换按时间区间显示的形式如2023-01-02-2023-01-08
     *
     * @param resultPoints
     * @param startTime
     * @param endTime
     * @param span
     * @return
     */
    protected static boolean weekInterval(List<OnlineReportTrendPoint> resultPoints, String startTime, String endTime, QueryReportAggDateDimensionEnum span) {
        boolean need = false;

        if (span == QueryReportAggDateDimensionEnum.week) {
            // 开始时间所在周的最后一天
            String startTimelastDayOfWeek = OrpDateTimeUtils.getLastDayOfWeek(startTime);
            for (int i = 0; i < resultPoints.size(); i++) {
                if (i == 0) {
                    if (startTimelastDayOfWeek.compareTo(endTime) >= 0) {
                        resultPoints.get(i).axis = String.format("%s-%s", startTime, endTime);
                    } else {
                        resultPoints.get(i).axis = String.format("%s-%s", startTime, OrpDateTimeUtils.getLastDayOfWeek(startTime));
                    }
                } else {
                    // axis所在周的最后一天
                    String axisLastDayOfWeek = OrpDateTimeUtils.getLastDayOfWeek(resultPoints.get(i).axis);
                    if (axisLastDayOfWeek.compareTo(endTime) < 0) {
                        resultPoints.get(i).axis = String.format("%s-%s", resultPoints.get(i).axis, OrpDateTimeUtils.getLastDayOfWeek(resultPoints.get(i).axis));
                    } else {
                        resultPoints.get(i).axis = String.format("%s-%s", resultPoints.get(i).axis, endTime);
                    }
                }
            }
        }
        return need;
    }

    public OnlineReportTrendPoint removeYoyChain(OnlineReportTrendPoint point) {
        point.data.entrySet().removeIf(item -> item.getKey().endsWith("Chain") || item.getKey().endsWith("Yoy"));
        return point;
    }

    @Data
    @AllArgsConstructor
    public static class DatePair {
        String start;

        String end;
    }
}
