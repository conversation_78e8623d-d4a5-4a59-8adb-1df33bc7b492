package com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/11 11:02
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class OnlineReportPotentialSaveTrendDTO {

    @Column(name = "date")
    @Type(value = Types.VARCHAR)
    private String date;

    @Column(name = "flightRcPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRcPotentialSave;

    @Column(name = "flightRebookPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRebookPotentialSave;

    @Column(name = "flightRefundPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRefundPotentialSave;

    @Column(name = "flightRcTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRcTimes;

    @Column(name = "flightRebookTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRebookTimes;

    @Column(name = "flightRefundTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRefundTimes;

    @Column(name = "flightRcTimesRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRcTimesRate;

    @Column(name = "flightRebookTimesRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRebookTimesRate;

    @Column(name = "flightRefundTimesRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal flightRefundTimesRate;

    // 酒店
    @Column(name = "hotelRcPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelRcPotentialSave;

    @Column(name = "hotelCancelPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelCancelPotentialSave;

    @Column(name = "hotelRcPotentialTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelRcPotentialTimes;

    @Column(name = "hotelCancelPotentialTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelCancelPotentialTimes;

    @Column(name = "hotelRcPotentialTimesRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelRcPotentialTimesRate;

    @Column(name = "hotelCancelPotentialTimesRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal hotelCancelPotentialTimesRate;

    @Column(name = "totalPotentialSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalPotentialSave;

    @Column(name = "totalPotentialTimes")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalPotentialTimes;

}
