package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import cn.hutool.core.util.CharsetUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.base.CKSqlParser;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.base.LoggingPreparedStatementProxy;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.base.TableInfo;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.deadprice.ConnectionProxy;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.common.SmConstant;
import com.corpgovernment.resource.schedule.onlinereport.dto.ClickHouseTablePartition;
import com.corpgovernment.resource.schedule.onlinereport.dto.DbResultSetDto;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpGsonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.lang.reflect.Proxy;
import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.BiFunction;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.clickhouse.dao
 * @description:
 * @author: Chris Yu
 * @create: 2021-11-03 18:54
 **/
@Slf4j
public abstract class AbstractClickhouseBaseDao {

    /**
     * 从动态数据源中获取的mysql数据源
     */
    @Autowired(required = false)
    //@Qualifier("commonDataSource")
    protected DataSource dynamicRoutingDataSource;

    @Autowired(required = false)
    @Qualifier("onlinereportDataSource")
    private DataSource onlinereportDataSource;

    /**
     * 程曦埋点数据源
     */
    @Autowired(required = false)
    @Qualifier("frontDataSource")
    private DataSource frontDataSource;

    /**
     * 管控数据源
     */
    @Autowired(required = false)
    @Qualifier("managementDataSource")
    private DataSource managementDataSource;

    /**
     * data change
     */
    @Value("${supplier_data_decrypt_new:==}")
    private String supplierDataDecryptNew;

    @Value("${supplier_data_decrypt_length:10}")
    private Integer supplierDataDecryptLength;

    @Value("${supplier_data_decrypt_map_size_limit:10000}")
    private Integer supplierDataDecryptMapSizeLimit;

    private Pattern pattern = Pattern.compile("[a-zA-Z0-9]+"); // 编译正则表达式

    // 创建缓存实例，设置最大大小为10000，写入后5小时过期
    Cache<String, String> decryptMap;

    @PostConstruct
    private void init(){
        decryptMap = Caffeine.newBuilder()
        .maximumSize(supplierDataDecryptMapSizeLimit)
                .expireAfterAccess(5, TimeUnit.HOURS)
            // 软引用值（内存不足时回收）
        .softValues()
        .build();
    }
    public Connection getConnection() throws SQLException {
        log.info("getConnection");
        Connection connection = onlinereportDataSource.getConnection();
        return ConnectionProxy.newInstance(connection);
    }

    public Connection getCommonConnection() throws SQLException {
        log.info("getCommonConnection");
        Connection connection = dynamicRoutingDataSource.getConnection();
        return ConnectionProxy.newInstance(connection);
    }

    public Connection getFrontConnection() throws SQLException {
        log.info("getFrontConnection");
        Connection connection = frontDataSource.getConnection();
        return ConnectionProxy.newInstance(connection);
    }

    public Connection getManagementConnection() throws SQLException {
        log.info("getManagementConnection");
        Connection connection = managementDataSource.getConnection();
        return ConnectionProxy.newInstance(connection);
    }

    public <Req, Resp> Resp queryBySqlMySql(String sql, Req req, BiFunction<Req, PreparedStatement, PreparedStatement> mapParameters,
                                            BiFunction<ResultSet, Class, Resp> mapResultHandler, Class clazz, String method) throws Exception {

        return queryBySql(sql, req, mapParameters, mapResultHandler, clazz, method, getCommonConnection(),false);

    }

    public <Req, Resp> Resp queryBySqlFront(String sql, Req req, BiFunction<Req, PreparedStatement, PreparedStatement> mapParameters,
                                            BiFunction<ResultSet, Class, Resp> mapResultHandler, Class clazz, String method) throws Exception {
        return queryBySql(sql, req, mapParameters, mapResultHandler, clazz, method, getFrontConnection(), false);

    }

    public <Req, Resp> Resp queryBySqlManagement(String sql, Req req, BiFunction<Req, PreparedStatement, PreparedStatement> mapParameters,
                                                 BiFunction<ResultSet, Class, Resp> mapResultHandler, Class clazz, String method) throws Exception {
        return queryBySql(sql, req, mapParameters, mapResultHandler, clazz, method, getManagementConnection(), false);
    }

    /**
     * ClickHouse sql查询
     *
     * @param sql              查询sql
     * @param req              请求参数
     * @param mapParameters    map请求函数
     * @param mapResultHandler 返回对象map函数
     * @param <Req>
     * @param <Resp>
     * @return
     */
    public <Req, Resp> Resp queryBySql(String sql, Req req, BiFunction<Req, PreparedStatement, PreparedStatement> mapParameters,
                                       BiFunction<ResultSet, Class, Resp> mapResultHandler, Class clazz, String method, Connection conn, boolean ignoreTenantId) throws Exception {
        long startTime = System.currentTimeMillis();
        Stopwatch watchTotal = Stopwatch.createStarted();
        Resp resp;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            resp = (Resp) clazz.getClassLoader();

            PreparedStatement originalStmt = conn.prepareStatement(sql);
            stmt = LoggingPreparedStatementProxy.createProxy(originalStmt, sql);
            stmt = mapParameters.apply(req, stmt);


            // 获取完整SQL
            String fullSql = ((LoggingPreparedStatementProxy) Proxy.getInvocationHandler(stmt)).getFullSql();

            //log.info("ClickhouseDbClient querySql unProcessedSql {}", fullSql);
            // 处理后的SQL
            String processedSql ;
            if (ignoreTenantId){
                processedSql = CKSqlParser.ignoreTenantIdCondition(fullSql);
            }else {
                processedSql = CKSqlParser.sqlAddTenantCondition(fullSql);
            }

            // 不输出 adm_mon_corpdata_jobstatus 表的sql
            if (!sql.contains("adm_mon_corpdata_jobstatus")) {
                log.info("\n╔════════════════════════════════════════════════════════════════════ ClickhouseDbClient Query Execution Start ════════════════════════════════════════════════════════════════════════╗\n\n"
                        + processedSql +
                        "\n\n╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝");
            }/*else{
                log.info("\n══════════════════════════════════════════════════════ Start ═════════════════════════════════════════════════════\n\n"
                        + processedSql +
                        "\n\n═════════════════════════════════════════════════════════════════════════════════════════════════════════════════");
            }*/
            log.info("queryBySql执行conn.prepareStatement前耗时：{}",System.currentTimeMillis()-startTime);
            startTime = System.currentTimeMillis();
            PreparedStatement newStmt = conn.prepareStatement(processedSql);
            log.info("queryBySql执行conn.prepareStatement耗时：{}",System.currentTimeMillis()-startTime);
            startTime = System.currentTimeMillis();
            rs = newStmt.executeQuery();
            log.info("queryBySql执行newStmt.executeQuery耗时：{}",System.currentTimeMillis()-startTime);
            startTime = System.currentTimeMillis();
            resp = mapResultHandler.apply(rs, clazz);
            log.info("queryBySql执行mapResultHandler.apply耗时：{},processedSql:{}",(System.currentTimeMillis()-startTime),processedSql);
        } catch (SQLException e) {
            Map<String, String> tagMap = Maps.newHashMap();
            tagMap.put("sql", sql);
            tagMap.put("method", method);
            log.error("ClickhouseDbClient querySql Fail>>>", ExceptionUtils.getFullStackTrace(e), tagMap);
            throw e;
        } finally {
            close(conn, stmt, rs);
            Map<String, String> tag = Maps.newHashMap();
            String totalTime = String.valueOf(watchTotal.elapsed(TimeUnit.MILLISECONDS));
            tag.put("elapsedTime", totalTime);
            tag.put("method", method);
            tag.put("db", OrpConstants.DB_NAME_PRO);

            /*log.info("\n╔═══════════════════════════════════════════════════════════════════════════════════ ClickhouseDbClient Query Execution Start ═════════════════════════════════════════════════════════════════════════════════════╗\n\n"
                    + DbResultMapUtils.formatSql(req, sql, totalTime, "ck") +
                    "\n\n╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝", tag);*/
        }
        return resp;
    }


    /**
     * ClickHouse sql查询
     *
     * @param sql              查询sql
     * @param req              请求参数
     * @param mapParameters    map请求函数
     * @param mapResultHandler 返回对象map函数
     * @param <Req>
     * @param <Resp>
     * @return
     */
    public <Req, Resp> Resp queryBySql(String sql, Req req,
                                       BiFunction<Req, PreparedStatement, PreparedStatement> mapParameters,
                                       BiFunction<ResultSet, Class, Resp> mapResultHandler,
                                       Class clazz,
                                       String method) throws Exception {

        return queryBySql(sql, req, mapParameters, mapResultHandler, clazz, method, getConnection(), false);
    }

    public <Req, Resp> Resp query(String sql, Req req,
                                  BiFunction<Req, PreparedStatement, PreparedStatement> mapParameters,
                                  BiFunction<ResultSet, Class, Resp> mapResultHandler,
                                  Class clazz,
                                  String method) {

        try {
            return queryBySql(sql, req, mapParameters, mapResultHandler, clazz, method);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public <Resp> List<Resp> findList(String sql, List<Object> parmList, Class<Resp> respClass) {
        return query(sql, parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error("the findList error {}", ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, respClass, "findList"
        );
    }

    public <Resp> Resp findOne(String sql, List<Object> parmList, Class<Resp> respClass) {
        return query(sql, parmList,
                (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        List<Resp> objects = mapResultList(u, d);

                        return objects.stream().findAny().orElse(null);
                    } catch (Exception e) {
                        log.error("the findOne error {}", ExceptionUtils.getFullStackTrace(e));
                    }
                    return null;
                }, respClass, "findOne"
        );
    }


    /**
     * ClickHouse sql查询
     *
     * @param sql              查询sql
     * @param req              请求参数
     * @param mapParameters    map请求函数
     * @param mapResultHandler 返回对象map函数
     * @param <Req>
     * @param <Resp>
     * @return
     */
    public <Req, Resp> Resp queryBySql(String sql, Req req,
                                       BiFunction<Req, PreparedStatement, PreparedStatement> mapParameters,
                                       BiFunction<ResultSet, Class, Resp> mapResultHandler,
                                       Class clazz,
                                       String method, boolean ignoreTenantId) throws Exception {

        return queryBySql(sql, req, mapParameters, mapResultHandler, clazz, method, getConnection(), ignoreTenantId);
    }

    /**
     * 批量插入
     *
     * @param insertSql       : INSERT INTO TABLE (col1,col2,col3..) VALUES (?,?,?..)
     * @param mapParameterFun
     * @return
     * @throws Exception
     */
    public <Req> int[] batchInsert(StringBuilder insertSql, List<Req> reqList, BiFunction<Req, PreparedStatement, PreparedStatement> mapParameterFun) throws SQLException {
        Stopwatch watchTotal = Stopwatch.createStarted();
        PreparedStatement stmt = null;
        Connection conn = null;
        try {
            if (CollectionUtils.isEmpty(reqList)) {
                return new int[]{};
            }
            conn = getConnection();
            stmt = conn.prepareStatement(insertSql.toString());
            for (Req req : reqList) {
                if (Objects.nonNull(req)) {
                    stmt = mapParameterFun.apply(req, stmt);
                    stmt.addBatch();
                }
            }
            return stmt.executeBatch();
        } catch (Exception e) {
            Map<String, String> tagMap = Maps.newHashMap();
            tagMap.put("sql", insertSql.toString());
            tagMap.put("req", OrpGsonUtils.toJsonStr(reqList));
            log.error("ClickhouseDbClient batchInsert>>>>>", ExceptionUtils.getFullStackTrace(e), tagMap);
        } finally {
            close(conn, stmt, null);
            Map<String, String> tag = Maps.newHashMap();
            String totalTime = String.valueOf(watchTotal.elapsed(TimeUnit.MILLISECONDS));
            tag.put("elapsedTime", totalTime);
            tag.put("reqList", OrpGsonUtils.toJsonStr(reqList));
            log.info("ClickhouseDbClient batchInsert>>>>>>>", insertSql.toString(), tag);
        }
        return new int[]{};
    }

    /**
     * single record insert
     *
     * @param insertSql       : INSERT INTO TABLE (col1,col2,col3..) VALUES (?,?,?..)
     * @param mapParameterFun
     * @return
     * @throws Exception
     */
    public <Req> int singleInsert(StringBuilder insertSql, Req req, BiFunction<Req, PreparedStatement, PreparedStatement> mapParameterFun) throws SQLException {
        Stopwatch watchTotal = Stopwatch.createStarted();
        PreparedStatement stmt = null;
        Connection conn = null;
        try {
            if (Objects.isNull(req)) {
                return OrpConstants.ZERO;
            }
            conn = getConnection();
            stmt = conn.prepareStatement(insertSql.toString());
            stmt = mapParameterFun.apply(req, stmt);
            if (Objects.isNull(stmt)) {
                return OrpConstants.ZERO;
            }
            Integer result = stmt.executeUpdate();
            return Objects.isNull(result) || result <= OrpConstants.ZERO ? OrpConstants.ZERO : OrpConstants.ONE;
        } catch (Exception e) {
            Map<String, String> tagMap = Maps.newHashMap();
            tagMap.put("sql", insertSql.toString());
            log.error("ClickhouseDbClient singleInsert>>>>>", ExceptionUtils.getFullStackTrace(e), tagMap);
        } finally {
            close(conn, stmt, null);
            Map<String, String> tag = Maps.newHashMap();
            String totalTime = String.valueOf(watchTotal.elapsed(TimeUnit.MILLISECONDS));
            tag.put("elapsedTime", totalTime);
            log.info("ClickhouseDbClient singleInsert>>>>>>>", insertSql.toString(), tag);
        }
        return OrpConstants.ZERO;
    }

    /**
     * 修改方法
     */
    public <Req> Integer updateBySql(StringBuilder appendSql,
                                     Req req,
                                     BiFunction<Req, PreparedStatement, PreparedStatement> mapUpdateParameter) throws SQLException {
        Stopwatch watchTotal = Stopwatch.createStarted();
        String executeSql = appendSql.toString();
        PreparedStatement stmt = null;
        Connection conn = null;
        try {
            if (StringUtils.isEmpty(executeSql)) {
                return OrpConstants.ZERO;
            }
            conn = getConnection();
            stmt = conn.prepareStatement(executeSql);
            stmt = mapUpdateParameter.apply(req, stmt);
            return stmt.executeUpdate();
        } catch (Exception e) {
            Map<String, String> tagMap = Maps.newHashMap();
            tagMap.put("sql", executeSql);
            tagMap.put("req", new Gson().toJson(req));
            log.error("ClickhouseDbClient updateBySql", ExceptionUtils.getFullStackTrace(e), tagMap);
        } finally {
            close(conn, stmt, null);
            Map<String, String> tag = Maps.newHashMap();
            tag.put("elapsedTime", String.valueOf(watchTotal.elapsed(TimeUnit.MILLISECONDS)));
            tag.put("req", OrpGsonUtils.toJsonStr(req));
            log.info("ClickhouseDbClient updateBySql>>>>>>>", executeSql, tag);
        }
        return OrpConstants.ZERO;
    }
    /**
     * data change
     */
    @Value("${supplier_data_change:}")
    private String supplierDataChange;
    @Value("${supplier_data_thread_count:5}")
    private Integer threadCount;
    /**
     * map ck result to response
     */
    protected <T> List<T> mapResultList(ResultSet resultSet, Class clazz) throws Exception {
        List<T> respList = Lists.newArrayList();
        // 需要做预处理，反射回来的数据-不存在需要跑异常
        Map<String, Field> fieldMap = DbResultMapUtils.getAllFieldMap(clazz);
        if (MapUtils.isEmpty(fieldMap)) {
            return Lists.newArrayList();
        }
        while (resultSet.next()) {
            ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
            int size = DbResultMapUtils.getColumnCount(resultSetMetaData);
            T entity = (T) clazz.newInstance();
            for (int i = OrpConstants.ONE; i <= size; i++) {
                String columnName = OrpReportUtils.lineToHump(resultSetMetaData.getColumnLabel(i));
                if (fieldMap.containsKey(columnName)) {
                    Object object = resultSet.getObject(i);
                    if (object instanceof String){
                        String str = object.toString().trim();
                        String[] split = str.split(",");
                        DbResultMapUtils.setMethodSetResult(entity, fieldMap.get(columnName), decrypt(split));
                    }else {
                        DbResultMapUtils.setMethodSetResult(entity, fieldMap.get(columnName), decrypt(object));
                    }
                }
            }
            respList.add(entity);
        }
        return respList;
    }

    /**
     * map ck result to response
     */
    protected <T> List<T> mapResultList2(ResultSet resultSet, Class clazz) throws Exception {
        log.info("mapResultList_supplierDataChange:{}",supplierDataChange);
        if(supplierDataChange.contains("111")){
            long startTime = System.currentTimeMillis();
            List list = mapResultList1(resultSet, clazz);
            log.info("queryDetail执行mapResultList耗时：{}",System.currentTimeMillis()-startTime);
            return list;
        }
        List<T> respList = Lists.newArrayList();
        // 需要做预处理，反射回来的数据-不存在需要跑异常
        Map<String, Field> fieldMap = DbResultMapUtils.getAllFieldMap(clazz);
        if (MapUtils.isEmpty(fieldMap)) {
            return Lists.newArrayList();
        }
        while (resultSet.next()) {
            ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
            int size = DbResultMapUtils.getColumnCount(resultSetMetaData);
            T entity = (T) clazz.newInstance();
            for (int i = OrpConstants.ONE; i <= size; i++) {
                String columnName = OrpReportUtils.lineToHump(resultSetMetaData.getColumnLabel(i));
                if (fieldMap.containsKey(columnName)) {
                    Object object = resultSet.getObject(i);
                    if (object instanceof String){
                        String str = object.toString().trim();
                        String[] split = str.split(",");
                        DbResultMapUtils.setMethodSetResult(entity, fieldMap.get(columnName), decrypt(split));
                    }else {
                        DbResultMapUtils.setMethodSetResult(entity, fieldMap.get(columnName), decrypt(object));
                    }
                }
            }
            respList.add(entity);
        }
        return respList;
    }

    protected <T> List<T> mapResultList1(ResultSet resultSet, Class<T> clazz) throws Exception {
        List<T> resultList = new ArrayList<>();
        Map<String, Field> fieldMap = DbResultMapUtils.getAllFieldMap(clazz);

        if (fieldMap.isEmpty()) {
            return resultList;
        }
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        List<Future<List<T>>> futures = new ArrayList<>();
        // 分区大小
        int batchSize = 100;
        int countNum = 0;
        List<List<DbResultSetDto>> batch = new ArrayList<>();
        List<DbResultSetDto> batchDbResultSetDto = new ArrayList<>();

        while (resultSet.next()) {
            synchronized (resultSet) {
                countNum += 1;
                ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
                int size = resultSetMetaData.getColumnCount();
                T entity = clazz.getDeclaredConstructor().newInstance();

                DbResultSetDto<T> dbResultSetDto = new DbResultSetDto<T>();
                List<DbResultSetDto.FieldValues> fieldValues = new ArrayList<>();
                dbResultSetDto.setEntity(entity);
                for (int i = 1; i <= size; i++) {
                    String columnName = OrpReportUtils.lineToHump(resultSetMetaData.getColumnLabel(i));
                    if (fieldMap.containsKey(columnName)) {
                        Object object = resultSet.getObject(i);
                        DbResultSetDto.FieldValues fieldValue = new DbResultSetDto.FieldValues();
                        fieldValue.setField(fieldMap.get(columnName));
                        fieldValue.setValue(object);
                        fieldValues.add(fieldValue);
                    }
                }
                dbResultSetDto.setFieldValues(fieldValues);
                batch.add(batchDbResultSetDto);
                // 当达到批次大小时，提交任务
                if (batch.size() >= batchSize) {
                    List<T> finalBatch = new ArrayList(batch);
                    futures.add(executorService.submit(() -> processBatch(finalBatch)));
                    batch.clear();
                }
                log.info("测试countNum:{}",countNum);
            }
        }

        // 提交剩余的批次
        if (!batch.isEmpty()) {
            List<T> finalBatch = new ArrayList(batch);
            futures.add(executorService.submit(() -> processBatch(finalBatch)));
        }

        // 合并结果
        for (Future<List<T>> future : futures) {
            resultList.addAll(future.get());
        }

        executorService.shutdown();
        return resultList;
    }

    private <T> List<T> processBatch(List<T> batch) {
        List<T> finalBatch = new ArrayList(batch.size());
        batch.forEach(x -> {
            List<DbResultSetDto<T>> batchDbResultSetDto = (List<DbResultSetDto<T>>) x;
            T entity = null;
            for(DbResultSetDto b : batchDbResultSetDto){
                if(entity==null){
                    entity = (T) b.getEntity();
                }
                List<DbResultSetDto.FieldValues> fieldValues = b.getFieldValues();
                for (DbResultSetDto.FieldValues fv : fieldValues){
                    Object object = fv.getValue();
                    if (object instanceof String){
                        String str = object.toString().trim();
                        String[] split = str.split(",");
                        DbResultMapUtils.setMethodSetResult(entity, fv.getField(), decrypt(split));
                    }else {
                        DbResultMapUtils.setMethodSetResult(entity, fv.getField(), decrypt(object));
                    }
                }
                finalBatch.add(entity);
            }
        });
        return batch;
    }

    protected Object decrypt(Object[] split) {
        if (CollectionUtils.isEmpty(Arrays.asList(split))) {
            return OrpConstants.EMPTY;
        }
        return Arrays.stream(split).map(this::decrypt).map(Object::toString).collect(Collectors.joining(","));
    }

    /**
     * 解密
     */
    protected Object decrypt(Object value) {
        try {
            if (Objects.isNull(value)) {
                return OrpConstants.EMPTY;
            }

            // 类型不是String直接返回
            if (!(value instanceof String)) {
                return value.toString();
            }
            String tempValue = value.toString();
            String decryptVal = decryptMap.getIfPresent(tempValue);
            if(StringUtils.isNotBlank(decryptVal)){
                return decryptVal;
            }
            if(hasLongerConsecutiveEnglishOrDigit(tempValue) || (StringUtils.isNotBlank(supplierDataDecryptNew) && tempValue.contains(supplierDataDecryptNew))){
                decryptVal = SmConstant.sm4.decryptStr(tempValue, CharsetUtil.CHARSET_UTF_8);
                decryptMap.put(tempValue,decryptVal);
                return decryptVal;
            }else{
                return value;
            }
        } catch (Exception e) {
            return value;
        }
    }
    private boolean hasLongerConsecutiveEnglishOrDigit(String str) {
        Matcher matcher = pattern.matcher(str); // 创建matcher对象
        while (matcher.find()) { // 查找匹配项
            if (matcher.group().length() > supplierDataDecryptLength) { // 检查匹配项长度是否大于supplierDataDecryptLength
                return true; // 如果找到长度大于5的匹配项，返回true
            }
        }
        return false; // 没有找到长度大于5的匹配项，返回false
    }

    /**
     * map ck string type result
     */
    protected Integer mapIntResult(ResultSet resultSet, String colName) throws Exception {
        List<Integer> respList = Lists.newArrayList();
        while (resultSet.next()) {
            respList.add(resultSet.getInt(colName));
        }
        return CollectionUtils.isEmpty(respList) ? OrpConstants.ZERO : respList.get(OrpConstants.ZERO);
    }

    /**
     * get current partition from cache if cache not exist from db and write to cache if get current don`t current
     * day,not newest partition and need query db get newest partition and write cache
     */
    protected String queryPartition(ClickHouseTable table) {
        List<Object> parmList = Lists.newArrayList(table.getTable());

        String querySql = "select `finishdate`  from `adm_mon_corpdata_jobstatus` where `tablename` = ? ORDER BY `finishdate` DESC LIMIT  1";

        try {
            return queryBySql(querySql, parmList, (req, statement) -> mapCommonRequest(parmList, statement),
                    (u, d) -> {
                        try {
                            return DbResultMapUtils.mapStringResult(u, "finishdate");
                        } catch (Exception e) {
                            log.error("getCurrentPartition>>>>>", ExceptionUtils.getFullStackTrace(e));
                        }
                        return OrpConstants.EMPTY;
                    }, String.class, "deptDetailAnalysis");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Find the last finishDate from adm_mon_corpdata_jobstatus
     */
    protected String queryPartitionByTable() {
        List<Object> parmList = Lists.newArrayList();

        List<String> tableNames = TableInfo.tableInfo().stream()
                .map(TableInfo::getName)
                .collect(Collectors.toList());

        StringBuilder querySql = new StringBuilder("select `finishdate`  from `adm_mon_corpdata_jobstatus` where `tablename` in(");
        querySql.append(tableNames.stream().map(m -> "'" + m + "'").collect(Collectors.joining(",")));
        querySql.append(") ORDER BY `finishdate` DESC LIMIT  1");

        try {
            return queryBySql(querySql.toString(), parmList, (req, statement) -> mapCommonRequest(parmList, statement),
                    (u, d) -> {
                        try {
                            return DbResultMapUtils.mapStringResult(u, "finishdate");
                        } catch (Exception e) {
                            log.error("getCurrentPartition>>>>>", ExceptionUtils.getFullStackTrace(e));
                        }
                        return OrpConstants.EMPTY;
                    }, String.class, "deptDetailAnalysis");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private String getPartitionByConfig(ClickHouseTable clickHouseTable) {
        // 单个表分区配置
        String singlePartition = getSingleTablePartitionByConfig(clickHouseTable);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(org.apache.commons.lang3.StringUtils.trimToEmpty(singlePartition))) {
            return singlePartition;
        }
        // 总配置
        String partition = ConfigUtils.getString("online_report_ck_partition", org.apache.commons.lang3.StringUtils.EMPTY);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(org.apache.commons.lang3.StringUtils.trimToEmpty(partition))) {
            return partition;
        }
        return org.apache.commons.lang3.StringUtils.EMPTY;
    }

    private String getSingleTablePartitionByConfig(ClickHouseTable clickHouseTable) {
        // 单个表分区配置
        String singlePartition = ConfigUtils.getString(clickHouseTable.getTable() + "_partition", org.apache.commons.lang3.StringUtils.EMPTY);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(org.apache.commons.lang3.StringUtils.trimToEmpty(singlePartition))) {
            return singlePartition;
        }
        return org.apache.commons.lang3.StringUtils.EMPTY;
    }

    private String defaultPartition() {
        return OrpDateTimeUtils.formatDateTimeToStr(new Date(), OrpDateTimeUtils.DEFAULT_DATE);
    }

    /**
     * query all clickhouse table partition
     */
    public List<ClickHouseTablePartition> queryTablePartition() throws Exception {
        String sql = "select max(partition) as partition,table from system.parts_columns where database='corpbi_onlinereport' group by table";
        List<ClickHouseTablePartition> resultList = queryBySql(sql, OrpConstants.EMPTY, (req, statement) -> statement, (u, d) -> {
            try {
                return DbResultMapUtils.mapResultList(u, d);
            } catch (Exception e) {
                log.error("queryTablePartition", ExceptionUtils.getFullStackTrace(e));
            }
            return Lists.newArrayList();
        }, ClickHouseTablePartition.class, "queryTablePartition");
        return Optional.ofNullable(resultList).orElse(Lists.newArrayList()).stream()
                .filter(t -> Objects.nonNull(t.getPartition()))
                .filter(t -> Objects.nonNull(OrpDateTimeUtils.getDateFormat(t.getPartition()))).collect(Collectors.toList());
    }

    /**
     * query clickhouse partition by table
     */
    private String queryTablePartitionByTable(String table) throws Exception {
        String sql = "select max(partition) as partition from system.parts_columns where database='corpbi_onlinereport' and table = ?";
        List<Object> paramList = Lists.newArrayList();
        paramList.add(table);
        return queryBySql(sql, paramList, (req, statement) -> mapCommonRequest(paramList, statement), (u, d) -> {
            try {
                return DbResultMapUtils.mapStringResult(u, OrpConstants.PARTITION);
            } catch (Exception e) {
                log.error("queryTablePartitionByTable", ExceptionUtils.getFullStackTrace(e));
            }
            return OrpConstants.EMPTY;
        }, String.class, "queryTablePartitionByTable");
    }

    /**
     * query clickhouse partition by table
     */
    private String queryTablePartition(String table) throws Exception {
        String sql = String.format("select max(d) as partition from %s ", table);
        return queryBySql(sql, table, (req, statement) -> statement, (u, d) -> {
            try {
                return DbResultMapUtils.mapStringResult(u, OrpConstants.PARTITION);
            } catch (Exception e) {
                log.error("queryTablePartition", ExceptionUtils.getFullStackTrace(e));
            }
            return OrpConstants.EMPTY;
        }, String.class, "queryTablePartition");
    }

    protected PreparedStatement mapCommonRequest(List<Object> paramList, PreparedStatement statement) {
        int index = OrpConstants.ONE;
        try {
            for (Object obj : paramList) {
                statement.setObject(index++, obj);
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }

    private void close(Connection conn, Statement stmt, ResultSet rs) {
        if (Objects.nonNull(rs)) {
            try {
                rs.close();
            } catch (SQLException e) {
                log.error("closeCKRSError", ExceptionUtils.getFullStackTrace(e));
            }
        }
        if (Objects.nonNull(stmt)) {
            try {
                stmt.close();
            } catch (SQLException e) {
                log.error("closeCKStatementError", ExceptionUtils.getFullStackTrace(e));
            }
        }
        if (Objects.nonNull(conn)) {
            try {
                conn.close();
            } catch (SQLException e) {
                log.error("closeCKConnectionError", ExceptionUtils.getFullStackTrace(e));
            }
        }
    }

    protected String querySingleTablePartition(ClickHouseTable table) {
        return queryPartition(table);
     /*   try {

            String configPartition = getSingleTablePartitionByConfig(table);
            if (StringUtils.isNotEmpty(configPartition)) {
                log.info("getSingleTablePartitionByConfig>>>>>>>", String.format("%s%s%s", table.getTable(), ">>>>>>>", configPartition));
                return configPartition;
            }
            if (ConfigUtils.getModulePartitionSwitch(ThreadPartitionContextUtils.getModule())) {
                String modulePartition = ThreadPartitionContextUtils.getPartition();
                log.info("getModulePartition>>>>>>>", String.format("%s%s%s", ThreadPartitionContextUtils.getModule(), ">>>>>>>",
                        org.apache.commons.lang3.StringUtils.trimToEmpty(modulePartition)));
                if (StringUtils.isNotEmpty(modulePartition)) {
                    return modulePartition;
                }
            }
            *//**
         * 优先取缓存分区 每个表对应一个key
         *//*
            String key = String.format("%s_%s", OrpConstants.READ_CLICKHOUSE_SINGLE_PARTITION, table.getTable());
*//*            String cachePartition = RedisCacheUtils.get(key);
            if (StringUtils.isNotEmpty(cachePartition)) {
                log.info("getCacheSingleTablePartition>>>>>>>", String.format("%s%s%s", table.getTable(), ">>>>>>>", cachePartition));
                return cachePartition;
            }
            log.warn("getCacheSingleTablePartition>>>>>>>", String.format("%s%s%s", table.getTable(), ">>>>>>>", cachePartition));*//*

         *//**
         * 表名称需要去除_all 才能查到分区的数据
         *//*
            String tableName = table.getTable();
*//*            if (table.getTable().endsWith("_all")) {
                tableName = table.getTable().substring(0, table.getTable().length() - 4);
            } else {
                tableName = table.getTable();
            }*//*
            String p = queryTablePartitionByTable(tableName);
            if (StringUtils.isEmpty(p)) {
                log.warn("getCurrentSingleTablePartition>>>>>>>", String.format("%s%s%s", table.getTable(), ">>>>>>>", "not exist partition"));
                if (OrpReportUtils.isFatEvn()) {
                    OrpReportUtils.printMsg(String.format("%s--------------->%s", table.getTable(), defaultPartition()));
                }
                p = queryTablePartition(table.getTable());
                if (StringUtils.isNotEmpty(p)) {
                    *//**
         * 设置缓存 时间单位秒
         *//*
                    //RedisCacheUtils.set(key, p, 3600, true);
                    return p;
                }
            }
            if (StringUtils.isNotEmpty(p)) {
                *//**
         * 设置缓存 时间单位秒
         *//*
                //RedisCacheUtils.set(key, p, 3600, true);
            }
            return p;
        } catch (Exception e) {
            log.error("querySingleTablePartition>>>>>", ExceptionUtils.getFullStackTrace(e));
        }
        return defaultPartition();*/
    }

    protected Integer limitStr(Pager pager) {
        return Optional.ofNullable(pager).filter(t -> Objects.nonNull(t.getPageIndex()))
                .filter(t -> Objects.nonNull(t.getPageSize()))
                .filter(t -> t.getPageIndex() >= OrpConstants.ONE)
                .filter(t -> t.getPageSize() > OrpConstants.ZERO)
                .map(t -> t.getPageIndex() - OrpConstants.ONE).map(t -> t.intValue() * pager.getPageSize()).orElse(OrpConstants.ZERO);
    }

    protected Integer limitEnd(Pager pager) {
        return Optional.ofNullable(pager).filter(t -> Objects.nonNull(t.getPageSize()))
                .filter(t -> t.getPageSize() > OrpConstants.ZERO).map(t -> t.getPageSize()).orElse(OrpConstants.TEN);
    }

    protected String getLogSql(List<Object> paramList, String sqlBuild) {
        try {
            char[] chars = sqlBuild.toCharArray();
            int index = 0;
            StringBuffer stringBuffer = new StringBuffer();
            char b = '?';
            for (int i = 0; i < chars.length; i++) {
                if (Objects.equals(b, chars[i])) {
                    if (paramList.get(index) instanceof String) {
                        stringBuffer.append("'").append(paramList.get(index)).append("'");
                    } else {
                        stringBuffer.append(paramList.get(index));
                    }
                    index++;
                } else {
                    stringBuffer.append(chars[i]);
                }
            }
            return stringBuffer.toString();
        } catch (Exception e) {
            return sqlBuild;
        }
    }


    /**
     * 查询下一个ID
     * @param table
     * @return
     * @throws SQLException
     */
    public long getNextId(String table) throws SQLException {
        String sql = "SELECT MAX(id) + 1 AS next_id FROM " + table;
        try (PreparedStatement stmt = getConnection().prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getLong("next_id");
            }
        }
        return 1; // Default to 1 if no records exist
    }
}
