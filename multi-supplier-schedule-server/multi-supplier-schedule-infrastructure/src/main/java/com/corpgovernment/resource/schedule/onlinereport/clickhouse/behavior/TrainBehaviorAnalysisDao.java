package com.corpgovernment.resource.schedule.onlinereport.clickhouse.behavior;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/**
 * <AUTHOR>
 * @date 2022-11-10 11:00
 * @desc
 */
@Repository
public class TrainBehaviorAnalysisDao extends OnlineReportBehaviorAnalysisDao {

    private ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;


    public <T> List<T> behaviorAnaylsis(BaseQueryConditionDTO requestDto, Class<T> clazz, String dim, String lang) throws Exception {
        String sql = null;
        List<Object> parmList = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(BehaviorDimEnum.SEAT_TYPE.name(), dim)) {
            sql = seatTypeAnaylsisSql(requestDto, parmList, lang);
        } else if (StringUtils.equalsIgnoreCase(BehaviorDimEnum.REFUND_TIME_DIS.name(), dim)) {
            sql = refundTimeDisSql(requestDto, parmList);
        }
        return commonList(clazz, sql.toString(), parmList);
    }

    /**
     * 坐席分析
     *
     * @param requestDto
     * @param paramList
     * @param lang
     * @return
     * @throws Exception
     */
    public String seatTypeAnaylsisSql(BaseQueryConditionDTO requestDto, List<Object> paramList, String lang) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT ").append(SharkUtils.isEN(lang) ? "first_seat_type_name_en" : "first_seat_type_name").append(" AS dim");
        sqlBuilder.append(",SUM(coalesce(quantity,0)) as totalQuantity");
        sqlBuilder.append(",SUM(coalesce(quantity,0)) as totalQuantity");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(this.clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(this.clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, ORDERDT));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(" GROUP BY dim  order by totalQuantity desc");
        return sqlBuilder.toString();
    }

    /**
     * 退票距发车时间分布
     *
     * @param requestDto
     * @param paramList
     * @return
     * @throws Exception
     */
    public String refundTimeDisSql(BaseQueryConditionDTO requestDto, List<Object> paramList) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT ");
        sqlBuilder.append(" CASE WHEN dateDiff('hour', toDateTime(print_time), toDateTime(departure_date_time)) > 48 THEN '>48hour'");
        sqlBuilder.append(" WHEN dateDiff('hour', toDateTime(print_time), toDateTime(departure_date_time)) >= 24" +
                "        AND dateDiff('hour', toDateTime(print_time), toDateTime(departure_date_time)) <= 48 THEN '24-48hour'");
        sqlBuilder.append(" WHEN dateDiff('hour', toDateTime(print_time), toDateTime(departure_date_time)) < 24 THEN '<24hour' END AS dim");
        sqlBuilder.append(" , SUM(CASE WHEN quantity < 0 THEN abs(quantity) ELSE 0 END) AS totalRefundQuantity");
        sqlBuilder.append(" , sum(case when refund_status = 'S' THEN coalesce(ticket_price,0) + coalesce(refund_ticket_fee,0) else 0 end) AS totalRefundLoss");
        sqlBuilder.append(" , SUM(ticket_price * (multiIf(dateDiff('hour', toDateTime(print_time), toDateTime(departure_date_time)) > 48, 0.05, " +
                "dateDiff('hour', toDateTime(print_time), toDateTime(departure_date_time)) < 24, 0.15, 0.1) - 0.05)) AS totalPotentialSaveAmount");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(this.clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(this.clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, ORDERDT));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(" AND (coalesce(print_time, '') != '' and coalesce(departure_date_time, '') != '') AND refund_status = 'S' GROUP BY dim");
        return sqlBuilder.toString();
    }

    /**
     * @param requestDto
     * @param clazz
     * @param industryList
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> refundOverview(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> industryList,
                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        String dateField = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name()) ? ORDERDT : REPORT_DATE;
        List<Object> paramList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select company.totalRfRbtkt as companyRfRbtkt, corp.totalRfRbtkt as corpRfRbtkt, industry.totalRfRbtkt as industryRfRbtkt");
        sqlBuilder.append(", company.totalOrdertkt as companytkt, corp.totalOrdertkt as corptkt, industry.totalOrdertkt as industrytkt ");
        sqlBuilder.append(", company.totalLoss as companyLoss ");
        sqlBuilder.append(" from (");
        sqlBuilder.append("SELECT sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as totalRfRbtkt");
        sqlBuilder.append(",SUM(case when coalesce(quantity, 0) > 0 then coalesce(quantity, 0) else 0 end ) as totalOrdertkt ");
        sqlBuilder.append(",sum(case when refund_status = 'S' THEN coalesce(ticket_price,0) + coalesce(refund_ticket_fee,0) else 0 end) as totalLoss");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(this.clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(this.clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, dateField));
//        sqlBuilder.append(" and quantity < 0");
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(") company cross join ( ");

        sqlBuilder.append("SELECT sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as totalRfRbtkt");
        sqlBuilder.append(",SUM(case when coalesce(quantity, 0) > 0 then coalesce(quantity, 0) else 0 end ) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(this.clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(requestDto, paramList, partition, dateField));
//        sqlBuilder.append(" and quantity < 0");
        sqlBuilder.append(getTrainOrderStatusCondition());
        if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(" ) corp cross join (");
        sqlBuilder.append("SELECT sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as totalRfRbtkt");
        sqlBuilder.append(",SUM(case when coalesce(quantity, 0) > 0 then coalesce(quantity, 0) else 0 end ) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(this.clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(requestDto, industryList, partition, paramList, dateField));
//        sqlBuilder.append(" and quantity < 0");
        sqlBuilder.append(getTrainOrderStatusCondition());
        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(" ) industry");
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    public <T> List<T> refundTrend(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        String dateField = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name()) ? ORDERDT : REPORT_DATE;
        String partition = queryPartition(this.clickHouseTable);
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT firstday_of_month AS dim");
        sqlBuilder.append(",sum(case when coalesce(quantity, 0) > 0 then coalesce(quantity, 0) else 0 end ) AS companytkt");
        sqlBuilder.append(",sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end) as companyRfRbtkt");
        sqlBuilder.append(",sum(case when refund_status = 'S' THEN coalesce(ticket_price,0) + coalesce(refund_ticket_fee,0) else 0 end) as companyLoss");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(this.clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, dateField));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(" group by dim ");
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    public <T> List<T> rebookTrend(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception {
        String dateField = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name()) ? ORDERDT : REPORT_DATE;
        String partition = queryPartition(this.clickHouseTable);
        List<Object> parmList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT firstday_of_month AS dim ");
        sqlBuilder.append(",SUM(case when coalesce(quantity, 0) > 0 then coalesce(quantity, 0) else 0 end ) AS companytkt");
        sqlBuilder.append(",SUM(coalesce(change_quantity, 0)) AS companyRfRbtkt");
        sqlBuilder.append(",SUM(coalesce(changebalance, 0)) + SUM(coalesce(deal_change_service_fee, 0)) + SUM(coalesce(afterchangeservicefee, 0)) as companyLoss");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(this.clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, parmList, partition, dateField));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(" group by dim ");
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * @param requestDto
     * @param clazz
     * @param industryList
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> rebookOverview(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> industryList,
                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        String dateField = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name()) ? ORDERDT : REPORT_DATE;
        List<Object> paramList = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select company.totalRfRbtkt as companyRfRbtkt, corp.totalRfRbtkt as corpRfRbtkt, industry.totalRfRbtkt as industryRfRbtkt");
        sqlBuilder.append(", company.totalOrdertkt as companytkt, corp.totalOrdertkt as corptkt, industry.totalOrdertkt as industrytkt ");
        sqlBuilder.append(", company.totalLoss as companyLoss ");
        sqlBuilder.append(" from (");
        sqlBuilder.append("SELECT SUM(coalesce(change_quantity, 0)) AS totalRfRbtkt, SUM((case when coalesce(quantity, 0) > 0 " +
                "then coalesce(quantity, 0) else 0 end )) as totalOrdertkt");
        sqlBuilder.append(",SUM(coalesce(changebalance, 0)) + SUM(coalesce(deal_change_service_fee, 0)) + SUM(coalesce(afterchangeservicefee, 0)) as totalLoss");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(this.clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        String partition = queryPartition(this.clickHouseTable);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(requestDto, paramList, partition, dateField));
        sqlBuilder.append(getTrainOrderStatusCondition());
        sqlBuilder.append(") company cross join ( ");

        sqlBuilder.append("SELECT SUM(coalesce(change_quantity, 0)) AS totalRfRbtkt, SUM((case when coalesce(quantity, 0) > 0 " +
                "then coalesce(quantity, 0) else 0 end )) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(this.clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(requestDto, paramList, partition, dateField));
        sqlBuilder.append(getTrainOrderStatusCondition());
        if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(" ) corp cross join (");
        sqlBuilder.append("SELECT SUM(coalesce(change_quantity, 0)) AS totalRfRbtkt, SUM((case when coalesce(quantity, 0) > 0 " +
                "then coalesce(quantity, 0) else 0 end )) as totalOrdertkt ");
        sqlBuilder.append(OrpConstants.FROM);
        sqlBuilder.append(this.clickHouseTable.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(requestDto, industryList, partition, paramList, dateField));
        sqlBuilder.append(getTrainOrderStatusCondition());
        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(" ) industry");
        return commonList(clazz, sqlBuilder.toString(), paramList);
    }

    enum BehaviorDimEnum {
        SEAT_TYPE, REFUND_TIME_DIS
    }
}
