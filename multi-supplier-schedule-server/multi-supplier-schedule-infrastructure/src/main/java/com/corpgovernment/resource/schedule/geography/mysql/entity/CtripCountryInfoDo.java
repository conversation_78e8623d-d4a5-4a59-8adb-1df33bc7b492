package com.corpgovernment.resource.schedule.geography.mysql.entity;


import cn.hutool.core.date.DateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;

/**
 * <AUTHOR> zhang
 * @date 2023/10/23 13:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CtripCountryInfoDo {
    /**
     * 多供应商平台国家ID
     */
    @Id
    private Long id;
    /**
     * 国家ID
     */
    private String countryId;
    /**
     * 国家名
     */
    private String countryName;
    /**
     * 国家英文名
     */
    private String countryEnName;
    /**
     * 国家码
     */
    private String countryCode;
    /**
     * 国家所在洲ID
     */
    private String continentId;
    /**
     * 国家所在洲名
     */
    private String continentName;
    /**
     * 地区码
     */
    private String areaCode;
    /**
     * MD5值
     */
    private String hashCode;
    /**
     * 创建时间
     */
    private DateTime datachangeCreatetime;
    /**
     * 修改时间
     */
    private DateTime datachangeLasttime;
    /**
     * 逻辑删除
     */
    private Byte isDeleted;

}
