package com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-10-19 13:23
 * @desc
 */
@Data
public class CarBonTrendDTO {

    @Column(name = "date")
    @Type(value = Types.VARCHAR)
    private String date;

    /**
     * 碳排放
     */
    @Column(name = "totalCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbons;

    /**
     * 占比
     */
    @Column(name = "totalCarbonsChain")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbonsChain;
    /**
     * 占比
     */
    @Column(name = "totalCarbonsYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbonsYoy;

    /**
     * 碳排放
     */
    @Column(name = "totalFltCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal fltCarbons;

    /**
     * 占比
     */
    @Column(name = "totalFltCarbonsChain")
    @Type(value = Types.DECIMAL)
    private BigDecimal fltCarbonsChain;
    /**
     * 占比
     */
    @Column(name = "totalFltCarbonsYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal fltCarbonsYoy;

    /**
     * 碳排放
     */
    @Column(name = "totalTrainCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal trainCarbons;

    /**
     * 占比
     */
    @Column(name = "totalTrainCarbonsChain")
    @Type(value = Types.DECIMAL)
    private BigDecimal trainCarbonsChain;
    /**
     * 占比
     */
    @Column(name = "totalTrainCarbonsYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal trainCarbonsYoy;

    /**
     * 碳排放
     */
    @Column(name = "totalCarCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal carCarbons;

    /**
     * 占比
     */
    @Column(name = "totalCarCarbonsChain")
    @Type(value = Types.DECIMAL)
    private BigDecimal carCarbonsChain;
    /**
     * 占比
     */
    @Column(name = "totalCarCarbonsYoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal carCarbonsYoy;
}
