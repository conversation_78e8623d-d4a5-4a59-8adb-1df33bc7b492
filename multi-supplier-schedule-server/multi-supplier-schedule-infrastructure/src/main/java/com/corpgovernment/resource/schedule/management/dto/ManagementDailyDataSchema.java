package com.corpgovernment.resource.schedule.management.dto;

import com.corpgovernment.dto.management.dto.ManagementRuleDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ManagementDailyDataSchema {

    /**
     * The data from the management/verifyTravelStandard interface
     */
    private List<ManagementRuleDTO> verifyTravelStandard;

}
