package com.corpgovernment.resource.schedule.onlinereport.clickhouse.base;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.deadprice.ConnectionProxy;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 * @className AbstractStarRocksOnlineReportBaseDao
 * @date 2024/5/30
 */
@Slf4j
public class AbstractStarRocksOnlineReportBaseDao {

    private static final String QUERY_TITLE = "AbstractStarRocksOnlineReportBaseDao query";

    @Autowired(required = false)
    @Qualifier("onlinereportDataSource")
    private DataSource onlinereportDataSource;


    public Connection getConnection() throws SQLException {
        Connection connection = onlinereportDataSource.getConnection();
        return ConnectionProxy.newInstance(connection);
    }

    /**
     * ClickHouse sql查询
     *
     * @param sql              查询sql
     * @param req              请求参数
     * @param mapParameters    map请求函数
     * @param mapResultHandler 返回对象map函数
     * @param <Req>
     * @param <Resp>
     * @return
     */
    public <Req, Resp> Resp querySrBySql(String sql, Req req,
                                         BiFunction<Req, PreparedStatement, PreparedStatement> mapParameters,
                                         BiFunction<ResultSet, Class, Resp> mapResultHandler,
                                         Class clazz,
                                         String method) throws Exception {
        Stopwatch watchTotal = Stopwatch.createStarted();
        Map<String, String> tagMap = Maps.newHashMap();
        Resp resp;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Connection con = null;
        try {
            resp = (Resp) clazz.getClassLoader();
            con = getConnection();
            stmt = con.prepareStatement(sql);
            if (Objects.isNull(stmt)) {
                return resp;
            }
            stmt = mapParameters.apply(req, stmt);
            rs = stmt.executeQuery();
            resp = mapResultHandler.apply(rs, clazz);
        } catch (SQLException e) {
            tagMap.put("sql", sql);
            tagMap.put("method", method);
            log.error(ExceptionUtils.getFullStackTrace(e), tagMap);
            throw e;
        } catch (Exception ex) {
            tagMap.put("sql", sql);
            tagMap.put("method", method);
            log.error(ExceptionUtils.getFullStackTrace(ex), tagMap);
            throw ex;
        } finally {
            close(con, stmt, rs);
            String totalTime = String.valueOf(watchTotal.elapsed(TimeUnit.MILLISECONDS));
            tagMap.put("elapsedTime", totalTime);
            tagMap.put("method", method);
            //tagMap.put("db", OrpReportUtils.getSrDb());
            log.info(DbResultMapUtils.formatSql(req, sql, totalTime, "sr"), tagMap);
        }
        return resp;
    }

    private void close(Connection con, Statement stmt, ResultSet rs) {
        if (Objects.nonNull(rs)) {
            try {
                rs.close();
            } catch (SQLException e) {
                log.error("closeSRRSError {}", ExceptionUtils.getFullStackTrace(e));
            }

        }
        if (Objects.nonNull(stmt)) {
            try {
                stmt.close();
            } catch (SQLException e) {
                log.error("closeSRStatementError {}", ExceptionUtils.getFullStackTrace(e));
            }
        }
        if (Objects.nonNull(con)) {
            try {
                con.close();
            } catch (SQLException e) {
                log.error("closeSRConnectionError {}", ExceptionUtils.getFullStackTrace(e));
            }
        }
    }


    protected Integer limitStr(Pager pager) {
        return Optional.ofNullable(pager).filter(t -> Objects.nonNull(t.getPageIndex()))
                .filter(t -> Objects.nonNull(t.getPageSize()))
                .filter(t -> t.getPageIndex() >= OrpConstants.ONE)
                .filter(t -> t.getPageSize() > OrpConstants.ZERO)
                .map(t -> t.getPageIndex() - OrpConstants.ONE).map(t -> t.intValue() * pager.getPageSize()).orElse(OrpConstants.ZERO);
    }

    protected Integer limitEnd(Pager pager) {
        return Optional.ofNullable(pager).filter(t -> Objects.nonNull(t.getPageSize()))
                .filter(t -> t.getPageSize() > OrpConstants.ZERO).map(t -> t.getPageSize()).orElse(OrpConstants.TEN);
    }

}
