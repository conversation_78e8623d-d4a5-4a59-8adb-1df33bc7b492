package com.corpgovernment.resource.schedule.execution.apollo.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-08-12 02:09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionConfigDo {

    private String supplierCode;

    private List<TaskConfig> taskConfigList;

    private List<ExecutionNodeConfig> executionNodeConfigList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaskConfig {

        private String taskCode;

        private Boolean enable;

        private String taskStartModeCode;

        private String operation;

        private Integer priority;

        private String cronExpression;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExecutionNodeConfig {

        private String executionNodeCode;

        private Integer flowRate;

        private List<NextExecutionNodeConfig> nextExecutionNodeConfigList;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NextExecutionNodeConfig {

        private String executionNodeCode;

        private Integer priority;

        private String supplierCode;

    }

}
