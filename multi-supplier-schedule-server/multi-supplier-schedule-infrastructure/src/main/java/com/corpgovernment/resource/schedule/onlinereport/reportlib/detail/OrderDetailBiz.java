package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.CostCenterDepartInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.onlinereport.dept.MatchBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-17 00:04
 * @desc
 */
public interface OrderDetailBiz<T> extends MatchBean<QueryReportBuTypeEnum> {
    OnlineReportOrderDetailInfo queryOrderDetail(T t) throws Exception;

    Integer queryOrderDetailCount(T t) throws Exception;
}
