package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.overview;


import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/24
 */
public interface ReportConsumeDaoService {
    <T> List<T> queryDataTypeFltOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;

    <T> List<T> queryDataTypeHtlOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;

    <T> List<T> queryDataTypeTrainOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;

    <T> List<T> queryDataTypeCarOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;

    <T> List<T> queryDataTypeBusOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;

    <T> List<T> queryDataTypeAddOrderNumSql(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;

    <T> List<T> queryCorpConsumeAmount(String corpId, Class<T> clazz) throws Exception;

    /**
     * 差旅分析-总体概览-概览
     * 维度：消费金额
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> aggreationWithConditionOverview(BaseQueryConditionDTO requestDto, Class<T> clazz)
            throws Exception;

    /**
     * 差旅分析-总体概览-机票
     * 维度：消费金额
     *
     * @param requestDto
     * @param clazz
     * @param otherFlag  是否查询其他指标（平均票价、里程均价、商旅服务费、退票张数、 改签张数）
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> aggreationFlightWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz, boolean otherFlag)
            throws Exception;

    /**
     * 差旅分析-总体概览-酒店
     * 维度：消费金额
     *
     * @param requestDto
     * @param clazz
     * @param otherFlag  是否查询其他指标（间夜均价、商旅管理服务费、退订间夜数）
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> aggreationHotelWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz, boolean otherFlag) throws Exception;

    /**
     * 差旅分析-总体概览-火车
     * 维度：消费金额
     *
     * @param requestDto
     * @param clazz
     * @param otherFlag  是否查询其他指标（平均票价、商旅管理服务费、退票张数、改签张数）
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> aggreationTrainWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz, boolean otherFlag) throws Exception;

    /**
     * 差旅分析-总体概览-火车（国际）
     * 维度：消费金额
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> aggreationIntTrainWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;

    /**
     * 差旅分析-总体概览-汽车
     * 维度：消费金额
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> List<T> aggreationCarWithCondition(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;


    /**
     * 差旅分析-总体概览-概览
     * 维度：消费金额
     * 查询 总行程/票张/订单/间夜
     *
     * @return
     * @throws Exception
     */
    Integer aggreationOverview(BaseQueryConditionDTO requestDto) throws Exception;

}
