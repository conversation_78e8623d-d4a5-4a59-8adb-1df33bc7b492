package com.corpgovernment.resource.schedule.onlinereport.module.save;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.save
 * @description:机票节省分析-酒店星級
 * @author: md_wang
 * @create: 2022-08-02 18:04
 **/
@Data
public class HotelSaveLossStarDTO {

    @Column(name = "starOneTicket")
    @Type(value = Types.INTEGER)
    private Integer starOneTicket;
    @Column(name = "starOneRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal starOneRate;

    @Column(name = "starTwoTicket")
    @Type(value = Types.INTEGER)
    private Integer starTwoTicket;
    @Column(name = "starTwoRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal starTwoRate;

    @Column(name = "starThreeTicket")
    @Type(value = Types.INTEGER)
    private Integer starThreeTicket;
    @Column(name = "starThreeRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal starThreeRate;

    @Column(name = "starFourTicket")
    @Type(value = Types.INTEGER)
    private Integer starFourTicket;
    @Column(name = "starFourRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal starFourRate;

    @Column(name = "starFiveTicket")
    @Type(value = Types.INTEGER)
    private Integer starFiveTicket;
    @Column(name = "starFiveRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal starFiveRate;

    @Column(name = "starNoTicket")
    @Type(value = Types.INTEGER)
    private Integer starNoTicket;
    @Column(name = "starNoRate")
    @Type(value = Types.DECIMAL)
    private BigDecimal starNoRate;
}
