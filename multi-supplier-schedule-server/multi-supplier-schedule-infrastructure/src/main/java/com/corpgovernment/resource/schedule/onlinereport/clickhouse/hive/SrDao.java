package com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive;


import com.corpgovernment.resource.schedule.domain.onlinereport.common.ChineseLanguageConfig;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.JDBCUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2020/9/16 14:29
 * @Desc
 */
@Repository
@Slf4j
public class SrDao {

    private static final int OFFSET = 100;

    private static final String SQLLOGTITLE = "sqlSrLog";

    private static final String COMMA = ",";

    @Autowired
    private JDBCUtil jdbcUtil;

    /**
     * 构建结果
     *
     * @param clazz 类型
     * @return 构建结果
     */
    public static <T> List<T> buildMapToEntity(final Class<T> clazz, List<Map> resList) {
        T bean = null;
        List list = new ArrayList();
        try {
            for (Map map : resList) {
                bean = clazz.newInstance();
                BeanUtils.copyProperties(bean, map);
                list.add(bean);
            }
        } catch (Exception e) {
            log.error("buildMapToEntity error", e);
        }
        return list;
    }

    /**
     * 机票订单明细
     *
     * @param hiveFilter
     */
    public List<FlightEntity> searchFlightOrderdetail(HiveFilter hiveFilter) {
        List<FlightEntity> result = new ArrayList<>();
        Connection connection = jdbcUtil.getSrConnection();
        PreparedStatement ptmt = null;
        ResultSet resultSet = null;
        try {
            if (connection == null) {
                throw new RuntimeException("get sr connection fail");
            }
            int index = 1;
            StringBuffer pageBuffer = null;
            StringBuffer stringBuffer = new StringBuffer(" select * from olrpt_indexflightdownload_all ");
            String reportType = StringUtils.EMPTY;
            if (hiveFilter != null) {
                if (hiveFilter.getExtData() != null) {
                    reportType = hiveFilter.getExtData().get("reportType");
                }
                if (StringUtils.isNotEmpty(hiveFilter.getPartition())) {
                    stringBuffer.append(" where d = ?");
                }
                if (hiveFilter.getDealStartTime() != null && hiveFilter.getDealEndTime() != null) {
                    if (StringUtils.equalsIgnoreCase(reportType, "F_REBOOK")) {
                        stringBuffer.append(" and subString(rebook_time, 1, 10) >= ? and subString(rebook_time, 1, 10) <= ?");
                    } else {
                        stringBuffer.append(" and subString(print_ticket_time, 1, 10) >= ? and subString(print_ticket_time, 1, 10) <= ?");
                    }
                }
                if (hiveFilter.getUseStartTime() != null && hiveFilter.getUseEndTime() != null) {
                    stringBuffer.append(" and subString(takeoff_time, 1, 10) >= ? and subString(takeoff_time, 1, 10) <= ?");
                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getPassengers())) {
                    stringBuffer.append(" and (upper(passenger_uid) in (");
                    for (int i = 0; i < hiveFilter.getPassengers().size(); i++) {
                        String passenger = StringUtils.trimToEmpty(hiveFilter.getPassengers().get(i));
                        if (StringUtils.isNotEmpty(passenger)) {
                            stringBuffer.append(" ? ");
                            if (i != hiveFilter.getPassengers().size() - 1) {
                                stringBuffer.append(COMMA);
                            }
                        }
                    }
                    stringBuffer.append(")");
                    stringBuffer.append(" or upper(passenger_name) in (");
                    for (int i = 0; i < hiveFilter.getPassengers().size(); i++) {
                        String passenger = StringUtils.trimToEmpty(hiveFilter.getPassengers().get(i));
                        if (StringUtils.isNotEmpty(passenger)) {
                            stringBuffer.append(" ? ");
                            if (i != hiveFilter.getPassengers().size() - 1) {
                                stringBuffer.append(COMMA);
                            }
                        }
                    }
                    stringBuffer.append("))");
                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getExceedStandard()) && hiveFilter.getExceedStandard().size() != 2) {
                    if (StringUtils.equalsIgnoreCase(hiveFilter.getExceedStandard().get(0), "T")) {
                        stringBuffer.append(" AND is_rc = 'T'");
                    }
                    if (StringUtils.equalsIgnoreCase(hiveFilter.getExceedStandard().get(0), "F")) {
                        stringBuffer.append(" AND is_rc = 'F'");
                    }
                }
                stringBuffer.append(" and audited <> 'F'");
                if (StringUtils.equalsIgnoreCase(reportType, "F_REFUND")) {
                    stringBuffer.append(" and is_refund = 'T' ");
                } else if (StringUtils.equalsIgnoreCase(reportType, "F_REBOOK")) {
                    stringBuffer.append(" and is_rebook = 'T'");
                } else if (StringUtils.equalsIgnoreCase(reportType, "F_UNUSE")) {
                    stringBuffer.append(" and flight_class = 'N' and (airline_isbudget <> 1 or airline_isbudget is null) AND ticket_status = 1");
                }
                pageBuffer = buildCommonCondition(stringBuffer, hiveFilter, "order_id", " order by print_ticket_time,guid desc ");
            } else {
                return result;
            }
            ptmt = connection.prepareStatement(pageBuffer.toString());
            List<Object> parameterValues = new ArrayList();
            if (hiveFilter != null) {
                if (StringUtils.isNotEmpty(hiveFilter.getPartition())) {
                    parameterValues.add(index - 1, hiveFilter.getPartition());
                    ptmt.setString(index++, hiveFilter.getPartition());
                }
                if (hiveFilter.getDealStartTime() != null && hiveFilter.getDealEndTime() != null) {
                    parameterValues.add(index - 1, hiveFilter.getDealStartTime());
                    ptmt.setString(index++, hiveFilter.getDealStartTime());
                    parameterValues.add(index - 1, hiveFilter.getDealEndTime());
                    ptmt.setString(index++, hiveFilter.getDealEndTime());
                }
                if (hiveFilter.getUseStartTime() != null && hiveFilter.getUseEndTime() != null) {
                    parameterValues.add(index - 1, hiveFilter.getUseStartTime());
                    ptmt.setString(index++, hiveFilter.getUseStartTime());
                    parameterValues.add(index - 1, hiveFilter.getUseEndTime());
                    ptmt.setString(index++, hiveFilter.getUseEndTime());

                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getPassengers())) {
                    for (int i = 0; i < hiveFilter.getPassengers().size(); i++) {
                        String passenger = StringUtils.trimToEmpty(hiveFilter.getPassengers().get(i)).toUpperCase();
                        if (StringUtils.isNotEmpty(passenger)) {
                            parameterValues.add(index - 1, passenger);
                            ptmt.setString(index++, passenger);
                        }
                    }
                    for (int i = 0; i < hiveFilter.getPassengers().size(); i++) {
                        String passenger = StringUtils.trimToEmpty(hiveFilter.getPassengers().get(i)).toUpperCase();
                        if (StringUtils.isNotEmpty(passenger)) {
                            parameterValues.add(index - 1, passenger);
                            ptmt.setString(index++, passenger);
                        }
                    }
                }
                buildCommonCondition(hiveFilter, ptmt, index, parameterValues);
            }
            String sqlStr = getQueryString(pageBuffer.toString(), parameterValues);
            log.info(SQLLOGTITLE, sqlStr);
            resultSet = ptmt.executeQuery();
            List<Map> mapList = convertToMap(resultSet);
            result = buildMapToEntity(FlightEntity.class, mapList);
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("searchFlightOrderDetail error", e);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            jdbcUtil.release(connection, ptmt, resultSet);
        }
        return result;
    }

    private StringBuffer buildCommonCondition(StringBuffer stringBuffer, HiveFilter hiveFilter, String fieldName, String orderField) {
        if (CollectionUtils.isNotEmpty(hiveFilter.getOrderIds())) {
            stringBuffer.append(" and " + fieldName + " in (");
            for (int i = 0; i < hiveFilter.getOrderIds().size(); i++) {
                stringBuffer.append(" ? ");
                if (i != hiveFilter.getOrderIds().size() - 1)
                    stringBuffer.append(COMMA);
            }
            stringBuffer.append(")");
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getCorpIds())) {
            stringBuffer.append(" and corp_corporation in (");
            for (int i = 0; i < hiveFilter.getCorpIds().size(); i++) {
                stringBuffer.append(" ? ");
                if (i != hiveFilter.getCorpIds().size() - 1)
                    stringBuffer.append(COMMA);
            }
            stringBuffer.append(")");
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getAccountIds())) {
            stringBuffer.append(" and account_id in (");
            for (int i = 0; i < hiveFilter.getAccountIds().size(); i++) {
                stringBuffer.append(" ? ");
                if (i != hiveFilter.getAccountIds().size() - 1)
                    stringBuffer.append(COMMA);
            }
            stringBuffer.append(")");
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getCostcenters())) {
            Map<Integer, List<CostcenterAndDeptFilter>> coscenterMap = hiveFilter.getCostcenters().stream().collect(Collectors.groupingBy(CostcenterAndDeptFilter::getLevel));
            for (Map.Entry entry : coscenterMap.entrySet()) {
                CostcenterAndDeptFilter costcenterAndDeptFilter = ((List<CostcenterAndDeptFilter>) entry.getValue()).get(0);
//                cost_center
                if (Objects.isNull(costcenterAndDeptFilter.getWay()) || costcenterAndDeptFilter.getWay() != 3) {
                    // 包含
                    if (BooleanUtils.isTrue(costcenterAndDeptFilter.getSelectAll())) {
                        stringBuffer.append(" and coalesce(cost_center" + entry.getKey() + ", '') <> ''");
                    }
                    if (CollectionUtils.isNotEmpty(costcenterAndDeptFilter.getInfo())) {
                        stringBuffer.append(" and cost_center" + entry.getKey() + " in (");
                        for (int i = 0; i < costcenterAndDeptFilter.getInfo().size(); i++) {
                            stringBuffer.append(" ? ");
                            if (i != costcenterAndDeptFilter.getInfo().size() - 1)
                                stringBuffer.append(COMMA);
                        }
                        stringBuffer.append(")");
                    }
                } else {
                    // 剔除
                    if (BooleanUtils.isTrue(costcenterAndDeptFilter.getSelectAll())) {
                        stringBuffer.append(" and coalesce(cost_center" + entry.getKey() + ", '') = ''");
                    }
                    if (CollectionUtils.isNotEmpty(costcenterAndDeptFilter.getInfo())) {
                        stringBuffer.append(" and cost_center" + entry.getKey() + " not in (");
                        for (int i = 0; i < costcenterAndDeptFilter.getInfo().size(); i++) {
                            stringBuffer.append(" ? ");
                            if (i != costcenterAndDeptFilter.getInfo().size() - 1)
                                stringBuffer.append(COMMA);
                        }
                        stringBuffer.append(")");
                    }
                    if (CollectionUtils.isNotEmpty(costcenterAndDeptFilter.getPermitVals())) {
                        stringBuffer.append(" and cost_center" + entry.getKey() + " in (");
                        for (int i = 0; i < costcenterAndDeptFilter.getPermitVals().size(); i++) {
                            stringBuffer.append(" ? ");
                            if (i != costcenterAndDeptFilter.getPermitVals().size() - 1)
                                stringBuffer.append(COMMA);
                        }
                        stringBuffer.append(")");
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getDepts())) {
            Map<Integer, List<CostcenterAndDeptFilter>> deptMap = hiveFilter.getDepts().stream().collect(Collectors.groupingBy(CostcenterAndDeptFilter::getLevel));
            for (Map.Entry entry : deptMap.entrySet()) {
                CostcenterAndDeptFilter costcenterAndDeptFilter = ((List<CostcenterAndDeptFilter>) entry.getValue()).get(0);
                if (Objects.isNull(costcenterAndDeptFilter.getWay()) || costcenterAndDeptFilter.getWay() != 3) {
                    // 包含
                    if (BooleanUtils.isTrue(costcenterAndDeptFilter.getSelectAll())) {
                        stringBuffer.append(" and coalesce(dept" + entry.getKey() + ", '') <> ''");
                    }
                    if (CollectionUtils.isNotEmpty(costcenterAndDeptFilter.getInfo())) {
                        stringBuffer.append(" and dept" + entry.getKey() + " in (");
                        for (int i = 0; i < costcenterAndDeptFilter.getInfo().size(); i++) {
                            stringBuffer.append(" ? ");
                            if (i != costcenterAndDeptFilter.getInfo().size() - 1)
                                stringBuffer.append(COMMA);
                        }
                        stringBuffer.append(")");
                    }
                } else {
                    // 剔除
                    if (BooleanUtils.isTrue(costcenterAndDeptFilter.getSelectAll())) {
                        stringBuffer.append(" and coalesce(dept" + entry.getKey() + ", '') = ''");
                    }
                    if (CollectionUtils.isNotEmpty(costcenterAndDeptFilter.getInfo())) {
                        stringBuffer.append(" and dept" + entry.getKey() + " not in (");
                        for (int i = 0; i < costcenterAndDeptFilter.getInfo().size(); i++) {
                            stringBuffer.append(" ? ");
                            if (i != costcenterAndDeptFilter.getInfo().size() - 1)
                                stringBuffer.append(COMMA);
                        }
                        stringBuffer.append(")");
                    }
                    if (CollectionUtils.isNotEmpty(costcenterAndDeptFilter.getPermitVals())) {
                        stringBuffer.append(" and dept" + entry.getKey() + " in (");
                        for (int i = 0; i < costcenterAndDeptFilter.getPermitVals().size(); i++) {
                            stringBuffer.append(" ? ");
                            if (i != costcenterAndDeptFilter.getPermitVals().size() - 1)
                                stringBuffer.append(COMMA);
                        }
                        stringBuffer.append(")");
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getFlightClassList())) {
            stringBuffer.append(" and flight_class in (");
            for (int i = 0; i < hiveFilter.getFlightClassList().size(); i++) {
                stringBuffer.append(" ? ");
                if (i != hiveFilter.getFlightClassList().size() - 1)
                    stringBuffer.append(COMMA);
            }
            stringBuffer.append(")");
        }
        if (hiveFilter.getOrderStartTime() != null && hiveFilter.getOrderEndTime() != null) {
            stringBuffer.append(" and subString(order_date, 1, 10) >= ? and subString(order_date, 1, 10) <= ?");
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getOrderstatusList())) {
            stringBuffer.append(" and order_status in (");
            for (int i = 0; i < hiveFilter.getOrderstatusList().size(); i++) {
                stringBuffer.append(" ? ");
                if (i != hiveFilter.getOrderstatusList().size() - 1)
                    stringBuffer.append(COMMA);
            }
            stringBuffer.append(")");
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getUsers())) {
            stringBuffer.append(" and (uid in (");
            for (int i = 0; i < hiveFilter.getUsers().size(); i++) {
                String user = StringUtils.trimToEmpty(hiveFilter.getUsers().get(i));
                if (StringUtils.isNotEmpty(user)) {
                    stringBuffer.append(" ? ");
                    if (i != hiveFilter.getUsers().size() - 1) {
                        stringBuffer.append(COMMA);
                    }
                }
            }
            stringBuffer.append(")");
            stringBuffer.append(" or upper(user_name) in (");
            for (int i = 0; i < hiveFilter.getUsers().size(); i++) {
                String user = StringUtils.trimToEmpty(hiveFilter.getUsers().get(i));
                if (StringUtils.isNotEmpty(user)) {
                    stringBuffer.append(" ? ");
                    if (i != hiveFilter.getUsers().size() - 1) {
                        stringBuffer.append(COMMA);
                    }
                }
            }
            stringBuffer.append("))");
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getEmployeIds())) {
            stringBuffer.append(" and upper(employe_id) in (");
            for (int i = 0; i < hiveFilter.getEmployeIds().size(); i++) {
                String employe = StringUtils.trimToEmpty(hiveFilter.getEmployeIds().get(i));
                if (StringUtils.isNotEmpty(employe)) {
                    stringBuffer.append(" ? ");
                    if (i != hiveFilter.getEmployeIds().size() - 1) {
                        stringBuffer.append(COMMA);
                    }
                }
            }
            stringBuffer.append(")");
        }
        stringBuffer.append(" ").append(orderField);
        if (hiveFilter.getPageSize() != null && hiveFilter.getPageNum() != null && hiveFilter.getPageNum() > 0 && hiveFilter.getPageSize() > 0) {
            stringBuffer.append(" limit  ?, ? ");
        } else {
            stringBuffer.append(" limit  0, ? ");
        }
        return stringBuffer;
    }

    /**
     * 酒店订单明细
     *
     * @param hiveFilter
     */
    public List<HotelEntity> searchHotelOrderdetail(HiveFilter hiveFilter) {
        List<HotelEntity> result = new ArrayList<>();
        Connection connection = jdbcUtil.getSrConnection();
        PreparedStatement ptmt = null;
        ResultSet resultSet = null;
        try {
            if (connection == null) {
                throw new RuntimeException("get sr connection fail");
            }
            int index = 1;
            StringBuffer pageBuffer = null;
            String orderStatus = ChineseLanguageConfig.get("done");
            StringBuffer stringBuffer = new StringBuffer(" select * from olrpt_indexhoteldownload_all ");
            if (hiveFilter != null) {
                if (StringUtils.isNotEmpty(hiveFilter.getPartition())) {
                    stringBuffer.append(" where d = ?");
                }
                if (hiveFilter.getDealStartTime() != null && hiveFilter.getDealEndTime() != null) {
                    stringBuffer.append(" and subString(deal_date, 1, 10) >= ? and subString(deal_date, 1, 10) <= ?");
                }
                if (hiveFilter.getUseStartTime() != null && hiveFilter.getUseEndTime() != null) {
                    stringBuffer.append(" and subString(arrival_date_time, 1, 10) >= ? and subString(arrival_date_time, 1, 10) <= ?");
                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getPassengers())) {
                    if (hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                            .anyMatch(i -> StringUtils.isNotEmpty(i))) {
                        stringBuffer.append(" AND array_length(array_intersect(split(regexp_replace(upper(IFNULL(client_name, '')), ' ', ''), ','), split(?,','))) > 0 ");
                    }
                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getExceedStandard()) && hiveFilter.getExceedStandard().size() != 2) {
                    if (StringUtils.equalsIgnoreCase(hiveFilter.getExceedStandard().get(0), "T")) {
                        stringBuffer.append(" AND is_rc = 'T'");
                    }
                    if (StringUtils.equalsIgnoreCase(hiveFilter.getExceedStandard().get(0), "F")) {
                        stringBuffer.append(" AND is_rc = 'F'");
                    }
                }
                pageBuffer = buildCommonCondition(stringBuffer, hiveFilter, "order_id", " order by deal_date,guid desc ");
            } else {
                return result;
            }
            ptmt = connection.prepareStatement(pageBuffer.toString());
            List<Object> parameterValues = new ArrayList();
            if (hiveFilter != null) {
                if (StringUtils.isNotEmpty(hiveFilter.getPartition())) {
                    parameterValues.add(index - 1, hiveFilter.getPartition());
                    ptmt.setString(index++, hiveFilter.getPartition());
                }
                if (hiveFilter.getDealStartTime() != null && hiveFilter.getDealEndTime() != null) {
                    parameterValues.add(index - 1, hiveFilter.getDealStartTime());
                    ptmt.setString(index++, hiveFilter.getDealStartTime());
                    parameterValues.add(index - 1, hiveFilter.getDealEndTime());
                    ptmt.setString(index++, hiveFilter.getDealEndTime());
                }
                if (hiveFilter.getUseStartTime() != null && hiveFilter.getUseEndTime() != null) {
                    parameterValues.add(index - 1, hiveFilter.getUseStartTime());
                    ptmt.setString(index++, hiveFilter.getUseStartTime());
                    parameterValues.add(index - 1, hiveFilter.getUseEndTime());
                    ptmt.setString(index++, hiveFilter.getUseEndTime());

                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getPassengers())) {
                    if (hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                            .anyMatch(i -> StringUtils.isNotEmpty(i))) {
                        String arr = hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                                .filter(i -> StringUtils.isNotEmpty(i)).map(StringUtils::upperCase).collect(Collectors.joining(COMMA));
                        if (StringUtils.isNotEmpty(arr)) {
                            parameterValues.add(index - 1, arr);
                            ptmt.setString(index++, arr);
                        }
                    }
                }
                buildCommonCondition(hiveFilter, ptmt, index, parameterValues);
            }
            String sqlStr = getQueryString(pageBuffer.toString(), parameterValues);
            log.info(SQLLOGTITLE, sqlStr);
            resultSet = ptmt.executeQuery();
            List<Map> mapList = convertToMap(resultSet);
            result = buildMapToEntity(HotelEntity.class, mapList);
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("searchHotelOrderdetail error", e);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            jdbcUtil.release(connection, ptmt, resultSet);
        }
        return result;
    }

    /**
     * 酒店订单审核明细
     *
     * @param hiveFilter
     */
    public List<HotelAuditEntity> searchHotelOrderAuditdetail(HiveFilter hiveFilter) {
        List<HotelAuditEntity> result = new ArrayList<>();
        Connection connection = jdbcUtil.getSrConnection();
        PreparedStatement ptmt = null;
        ResultSet resultSet = null;
        try {
            if (connection == null) {
                throw new RuntimeException("get sr connection fail");
            }
            int index = 1;
            StringBuffer pageBuffer = null;
            StringBuffer stringBuffer = new StringBuffer(" select * from adm_indexhotel_order_audit_all ");
            if (hiveFilter != null) {
                if (StringUtils.isNotEmpty(hiveFilter.getPartition())) {
                    stringBuffer.append(" where d = ?");
                }
                stringBuffer.append(" and is_audit = '" + ChineseLanguageConfig.get(GlobalConst.HOTEL_ISAUDIT_KEY) + "'");
                if (hiveFilter.getUseStartTime() != null && hiveFilter.getUseEndTime() != null) {
                    stringBuffer.append(" and subString(arrival_date_time, 1, 10) >= ? and subString(arrival_date_time, 1, 10) <= ?");
                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getPassengers())) {
                    if (hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                            .anyMatch(i -> StringUtils.isNotEmpty(i))) {
                        stringBuffer.append(" AND array_length(array_intersect(split(regexp_replace(upper(IFNULL(audit_client_name, '')), ' ', ''), ','), split(?,','))) > 0 ");
                    }
                }
                pageBuffer = buildCommonCondition(stringBuffer, hiveFilter, "orderid", " order by order_date,order_id, audit_client_name desc ");
            } else {
                return result;
            }
            ptmt = connection.prepareStatement(pageBuffer.toString());
            List<Object> parameterValues = new ArrayList();
            if (hiveFilter != null) {
                if (StringUtils.isNotEmpty(hiveFilter.getPartition())) {
                    parameterValues.add(index - 1, hiveFilter.getPartition());
                    ptmt.setString(index++, hiveFilter.getPartition());
                }
                if (hiveFilter.getDealStartTime() != null && hiveFilter.getDealEndTime() != null) {
                    parameterValues.add(index - 1, hiveFilter.getDealStartTime());
                    ptmt.setString(index++, hiveFilter.getDealStartTime());
                    parameterValues.add(index - 1, hiveFilter.getDealEndTime());
                    ptmt.setString(index++, hiveFilter.getDealEndTime());
                }
                if (hiveFilter.getUseStartTime() != null && hiveFilter.getUseEndTime() != null) {
                    parameterValues.add(index - 1, hiveFilter.getUseStartTime());
                    ptmt.setString(index++, hiveFilter.getUseStartTime());
                    parameterValues.add(index - 1, hiveFilter.getUseEndTime());
                    ptmt.setString(index++, hiveFilter.getUseEndTime());

                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getPassengers())) {
                    if (hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                            .anyMatch(i -> StringUtils.isNotEmpty(i))) {
                        String arr = hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                                .filter(i -> StringUtils.isNotEmpty(i)).map(StringUtils::upperCase).collect(Collectors.joining(COMMA));
                        parameterValues.add(index - 1, arr);
                        ptmt.setString(index++, arr);
                    }
                }
                buildCommonCondition(hiveFilter, ptmt, index, parameterValues);
            }
            String sqlStr = getQueryString(pageBuffer.toString(), parameterValues);
            log.info(SQLLOGTITLE, sqlStr);
            resultSet = ptmt.executeQuery();
            List<Map> mapList = convertToMap(resultSet);
            result = buildMapToEntity(HotelAuditEntity.class, mapList);
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("searchHotelOrderAuditdetail error", e);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            jdbcUtil.release(connection, ptmt, resultSet);
        }
        return result;
    }

    /**
     * 火车订单明细
     *
     * @param hiveFilter
     */
    public List<TrainEntity> searchTrainOrderdetail(HiveFilter hiveFilter) {
        List<TrainEntity> result = new ArrayList<>();
        Connection connection = jdbcUtil.getSrConnection();
        PreparedStatement ptmt = null;
        ResultSet resultSet = null;
        try {
            if (connection == null) {
                throw new RuntimeException("get sr connection fail");
            }
            int index = 1;
            StringBuffer pageBuffer = null;
            StringBuffer stringBuffer = new StringBuffer(" select * from olrpt_indextraindownload_all ");
            if (hiveFilter != null) {
                if (StringUtils.isNotEmpty(hiveFilter.getPartition())) {
                    stringBuffer.append(" where d = ?");
                }
                if (hiveFilter.getDealStartTime() != null && hiveFilter.getDealEndTime() != null) {
                    stringBuffer.append(" and subString(print_time, 1, 10) >= ? and subString(print_time, 1, 10) <= ?");
                }
                if (hiveFilter.getUseStartTime() != null && hiveFilter.getUseEndTime() != null) {
                    stringBuffer.append(" and subString(departure_date_time, 1, 10) >= ? and subString(departure_date_time, 1, 10) <= ?");
                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getPassengers())) {
                    if (hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                            .anyMatch(i -> StringUtils.isNotEmpty(i))) {
                        stringBuffer.append(" AND array_length(array_intersect(split(regexp_replace(upper(IFNULL(passenger_name, '')), ' ', ''), ','), split(?,','))) > 0 ");
                    }
                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getExceedStandard()) && hiveFilter.getExceedStandard().size() != 2) {
                    if (StringUtils.equalsIgnoreCase(hiveFilter.getExceedStandard().get(0), "T")) {
                        stringBuffer.append(" AND is_rc = 'T'");
                    }
                    if (StringUtils.equalsIgnoreCase(hiveFilter.getExceedStandard().get(0), "F")) {
                        stringBuffer.append(" AND is_rc = 'F'");
                    }
                }
                pageBuffer = buildCommonCondition(stringBuffer, hiveFilter, "order_id", " order by print_time,guid desc ");
            } else {
                return result;
            }
            ptmt = connection.prepareStatement(pageBuffer.toString());
            List<Object> parameterValues = new ArrayList();
            if (hiveFilter != null) {
                if (StringUtils.isNotEmpty(hiveFilter.getPartition())) {
                    parameterValues.add(index - 1, hiveFilter.getPartition());
                    ptmt.setString(index++, hiveFilter.getPartition());
                }
                if (hiveFilter.getDealStartTime() != null && hiveFilter.getDealEndTime() != null) {
                    parameterValues.add(index - 1, hiveFilter.getDealStartTime());
                    ptmt.setString(index++, hiveFilter.getDealStartTime());
                    parameterValues.add(index - 1, hiveFilter.getDealEndTime());
                    ptmt.setString(index++, hiveFilter.getDealEndTime());
                }
                if (hiveFilter.getUseStartTime() != null && hiveFilter.getUseEndTime() != null) {
                    parameterValues.add(index - 1, hiveFilter.getUseStartTime());
                    ptmt.setString(index++, hiveFilter.getUseStartTime());
                    parameterValues.add(index - 1, hiveFilter.getUseEndTime());
                    ptmt.setString(index++, hiveFilter.getUseEndTime());

                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getPassengers())) {
                    if (hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                            .anyMatch(i -> StringUtils.isNotEmpty(i))) {
                        String arr = hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                                .filter(i -> StringUtils.isNotEmpty(i)).map(StringUtils::upperCase).collect(Collectors.joining(COMMA));
                        parameterValues.add(index - 1, arr);
                        ptmt.setString(index++, arr);
                    }
                }
                buildCommonCondition(hiveFilter, ptmt, index, parameterValues);
            }
            String sqlStr = getQueryString(pageBuffer.toString(), parameterValues);
            log.info(SQLLOGTITLE, sqlStr);
            resultSet = ptmt.executeQuery();
            List<Map> mapList = convertToMap(resultSet);
            result = buildMapToEntity(TrainEntity.class, mapList);
        } catch (SQLException e) {
            log.error("searchTrainOrderdetail error", e);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            jdbcUtil.release(connection, ptmt, resultSet);
        }
        return result;
    }

    /**
     * 用车订单明细
     *
     * @param hiveFilter
     */
    public List<CarEntity> searchCarOrderdetail(HiveFilter hiveFilter) {
        List<CarEntity> result = new ArrayList<>();
        Connection connection = jdbcUtil.getSrConnection();
        PreparedStatement ptmt = null;
        ResultSet resultSet = null;
        try {
            if (connection == null) {
                throw new RuntimeException("get sr connection fail");
            }
            int index = 1;
            StringBuffer pageBuffer = null;
            StringBuffer stringBuffer = new StringBuffer(" select * from olrpt_indexcardownload_all ");
            if (hiveFilter != null) {
                if (StringUtils.isNotEmpty(hiveFilter.getPartition())) {
                    stringBuffer.append(" where d = ? ");
                }
                if (hiveFilter.getDealStartTime() != null && hiveFilter.getDealEndTime() != null) {
                    stringBuffer.append(" and subString(order_date, 1, 10) >= ? and subString(order_date, 1, 10) <= ?");
                }
                if (hiveFilter.getUseStartTime() != null && hiveFilter.getUseEndTime() != null) {
                    stringBuffer.append(" and subString(start_time, 1, 10) >= ? and subString(start_time, 1, 10) <= ?");
                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getPassengers())) {
                    if (hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                            .anyMatch(i -> StringUtils.isNotEmpty(i))) {
                        stringBuffer.append(" AND array_length(array_intersect(split(regexp_replace(upper(IFNULL(passenger_name, '')), ' ', ''), ','), split(?,','))) > 0 ");
                    }
                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getExceedStandard()) && hiveFilter.getExceedStandard().size() != 2) {
                    if (StringUtils.equalsIgnoreCase(hiveFilter.getExceedStandard().get(0), "T")) {
                        stringBuffer.append(" AND is_rc = 'T'");
                    }
                    if (StringUtils.equalsIgnoreCase(hiveFilter.getExceedStandard().get(0), "F")) {
                        stringBuffer.append(" AND is_rc = 'F'");
                    }
                }
                pageBuffer = buildCommonCondition(stringBuffer, hiveFilter, "order_id", " order by order_date,guid desc ");
            } else {
                return result;
            }
            ptmt = connection.prepareStatement(pageBuffer.toString());
            List<Object> parameterValues = new ArrayList();
            if (hiveFilter != null) {
                if (StringUtils.isNotEmpty(hiveFilter.getPartition())) {
                    parameterValues.add(index - 1, hiveFilter.getPartition());
                    ptmt.setString(index++, hiveFilter.getPartition());
                }
                if (hiveFilter.getDealStartTime() != null && hiveFilter.getDealEndTime() != null) {
                    parameterValues.add(index - 1, hiveFilter.getDealStartTime());
                    ptmt.setString(index++, hiveFilter.getDealStartTime());
                    parameterValues.add(index - 1, hiveFilter.getDealEndTime());
                    ptmt.setString(index++, hiveFilter.getDealEndTime());
                }
                if (hiveFilter.getUseStartTime() != null && hiveFilter.getUseEndTime() != null) {
                    parameterValues.add(index - 1, hiveFilter.getUseStartTime());
                    ptmt.setString(index++, hiveFilter.getUseStartTime());
                    parameterValues.add(index - 1, hiveFilter.getUseEndTime());
                    ptmt.setString(index++, hiveFilter.getUseEndTime());
                }
                if (CollectionUtils.isNotEmpty(hiveFilter.getPassengers())) {
                    if (hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                            .anyMatch(i -> StringUtils.isNotEmpty(i))) {
                        String arr = hiveFilter.getPassengers().stream().filter(Objects::nonNull).map(StringUtils::trimToEmpty)
                                .filter(i -> StringUtils.isNotEmpty(i)).map(StringUtils::upperCase).collect(Collectors.joining(COMMA));
                        parameterValues.add(index - 1, arr);
                        ptmt.setString(index++, arr);
                    }
                }
                buildCommonCondition(hiveFilter, ptmt, index, parameterValues);
            }
            String sqlStr = getQueryString(pageBuffer.toString(), parameterValues);
            log.info(SQLLOGTITLE, sqlStr);
            resultSet = ptmt.executeQuery();
            List<Map> mapList = convertToMap(resultSet);
            result = buildMapToEntity(CarEntity.class, mapList);
        } catch (SQLException e) {
            log.error("searchCarOrderdetail error", e);
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            jdbcUtil.release(connection, ptmt, resultSet);
        }
        return result;
    }

    private void buildCommonCondition(HiveFilter hiveFilter, PreparedStatement ptmt, int index, List<Object> parameterValues) throws SQLException {
        if (CollectionUtils.isNotEmpty(hiveFilter.getOrderIds())) {
            for (int i = 0; i < hiveFilter.getOrderIds().size(); i++) {
                parameterValues.add(index - 1, hiveFilter.getOrderIds().get(i));
                ptmt.setLong(index++, hiveFilter.getOrderIds().get(i));
            }
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getCorpIds())) {
            for (int i = 0; i < hiveFilter.getCorpIds().size(); i++) {
                parameterValues.add(index - 1, hiveFilter.getCorpIds().get(i).toUpperCase());
                ptmt.setString(index++, hiveFilter.getCorpIds().get(i).toUpperCase());
            }
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getAccountIds())) {
            for (int i = 0; i < hiveFilter.getAccountIds().size(); i++) {
                parameterValues.add(index - 1, hiveFilter.getAccountIds().get(i));
                ptmt.setLong(index++, hiveFilter.getAccountIds().get(i));
            }
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getCostcenters())) {
            Map<Integer, List<CostcenterAndDeptFilter>> coscenterMap = hiveFilter.getCostcenters().stream().collect(Collectors.groupingBy(CostcenterAndDeptFilter::getLevel));
            for (Map.Entry entry : coscenterMap.entrySet()) {
                CostcenterAndDeptFilter costcenterAndDeptFilter = ((List<CostcenterAndDeptFilter>) entry.getValue()).get(0);
                if (Objects.isNull(costcenterAndDeptFilter.getWay()) || costcenterAndDeptFilter.getWay() != 3) {
                    // 包含
                    for (String str : costcenterAndDeptFilter.getInfo()) {
                        parameterValues.add(index - 1, str);
                        ptmt.setString(index++, str);
                    }
                } else {
                    // 剔除
                    if (CollectionUtils.isNotEmpty(costcenterAndDeptFilter.getInfo())) {
                        for (String str : costcenterAndDeptFilter.getInfo()) {
                            parameterValues.add(index - 1, str);
                            ptmt.setString(index++, str);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(costcenterAndDeptFilter.getPermitVals())) {
                        for (String str : costcenterAndDeptFilter.getPermitVals()) {
                            parameterValues.add(index - 1, str);
                            ptmt.setString(index++, str);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getDepts())) {
            Map<Integer, List<CostcenterAndDeptFilter>> deptMap = hiveFilter.getDepts().stream().collect(Collectors.groupingBy(CostcenterAndDeptFilter::getLevel));
            for (Map.Entry entry : deptMap.entrySet()) {
                CostcenterAndDeptFilter costcenterAndDeptFilter = ((List<CostcenterAndDeptFilter>) entry.getValue()).get(0);
                if (Objects.isNull(costcenterAndDeptFilter.getWay()) || costcenterAndDeptFilter.getWay() != 3) {
                    // 包含
                    for (String str : costcenterAndDeptFilter.getInfo()) {
                        parameterValues.add(index - 1, str);
                        ptmt.setString(index++, str);
                    }
                } else {
                    // 剔除
                    if (CollectionUtils.isNotEmpty(costcenterAndDeptFilter.getInfo())) {
                        for (String str : costcenterAndDeptFilter.getInfo()) {
                            parameterValues.add(index - 1, str);
                            ptmt.setString(index++, str);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(costcenterAndDeptFilter.getPermitVals())) {
                        for (String str : costcenterAndDeptFilter.getPermitVals()) {
                            parameterValues.add(index - 1, str);
                            ptmt.setString(index++, str);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getFlightClassList())) {
            for (int i = 0; i < hiveFilter.getFlightClassList().size(); i++) {
                parameterValues.add(index - 1, hiveFilter.getFlightClassList().get(i).toUpperCase());
                ptmt.setString(index++, hiveFilter.getFlightClassList().get(i).toUpperCase());
            }
        }

        if (hiveFilter.getOrderStartTime() != null && hiveFilter.getOrderEndTime() != null) {
            parameterValues.add(index - 1, hiveFilter.getOrderStartTime());
            ptmt.setString(index++, hiveFilter.getOrderStartTime());
            parameterValues.add(index - 1, hiveFilter.getOrderEndTime());
            ptmt.setString(index++, hiveFilter.getOrderEndTime());
        }

        if (CollectionUtils.isNotEmpty(hiveFilter.getOrderstatusList())) {
            for (int i = 0; i < hiveFilter.getOrderstatusList().size(); i++) {
                parameterValues.add(index - 1, hiveFilter.getOrderstatusList().get(i).toUpperCase());
                ptmt.setString(index++, hiveFilter.getOrderstatusList().get(i).toUpperCase());
            }
        }
        if (CollectionUtils.isNotEmpty(hiveFilter.getUsers())) {
            for (int i = 0; i < hiveFilter.getUsers().size(); i++) {
                String user = StringUtils.trimToEmpty(hiveFilter.getUsers().get(i)).toUpperCase();
                if (StringUtils.isNotEmpty(user)) {
                    parameterValues.add(index - 1, user);
                    ptmt.setString(index++, user);
                }
            }
            for (int i = 0; i < hiveFilter.getUsers().size(); i++) {
                String user = StringUtils.trimToEmpty(hiveFilter.getUsers().get(i)).toUpperCase();
                if (StringUtils.isNotEmpty(user)) {
                    parameterValues.add(index - 1, user);
                    ptmt.setString(index++, user);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(hiveFilter.getEmployeIds())) {
            for (int i = 0; i < hiveFilter.getEmployeIds().size(); i++) {
                String employe = StringUtils.trimToEmpty(hiveFilter.getEmployeIds().get(i)).toUpperCase();
                if (StringUtils.isNotEmpty(employe)) {
                    parameterValues.add(index - 1, employe);
                    ptmt.setString(index++, employe);
                }
            }
        }

        if (hiveFilter.getPageSize() != null && hiveFilter.getPageNum() != null && hiveFilter.getPageNum() > 0 && hiveFilter.getPageSize() > 0) {
            int offset = (hiveFilter.getPageNum() - 1) * hiveFilter.getPageSize();
            parameterValues.add(index - 1, offset);
            ptmt.setLong(index++, offset);
            parameterValues.add(index - 1, hiveFilter.getPageSize());
            ptmt.setLong(index++, hiveFilter.getPageSize());
        } else {
            parameterValues.add(index - 1, OFFSET);
            ptmt.setInt(index++, OFFSET);
        }
    }

    private List<Map> convertToMap(ResultSet resultSet) {
        List<Map> result = new ArrayList<>();
        try {
            ResultSetMetaData metaData = resultSet.getMetaData();
            while (resultSet.next()) {
                Map<String, Object> map = new HashMap<>();
                for (int i = 1; i <= metaData.getColumnCount(); i++) {
                    map.put(metaData.getColumnName(i), resultSet.getObject(i));
                }
                result.add(map);
            }
        } catch (Exception e) {
            log.error("convertToMap error", e);
        }
        return result;
    }

    public String getQueryString(String sqlTemplate, List<Object> parameterValues) {
        try {
            int len = sqlTemplate.length();
            StringBuffer t = new StringBuffer(len * 2);
            if (parameterValues != null) {
                int i = 1, limit = 0, base = 0;

                while ((limit = sqlTemplate.indexOf('?', limit)) != -1) {
                    t.append(sqlTemplate.substring(base, limit));
                    Object parameterObj = parameterValues.get(i - 1);
                    if (parameterObj instanceof String) {
                        t.append("'" + parameterValues.get(i - 1) + "'");
                    } else {
                        t.append(parameterValues.get(i - 1));
                    }
                    i++;
                    limit++;
                    base = limit;
                }
                if (base < len) {
                    t.append(sqlTemplate.substring(base));
                }
            }
            return t.toString();
        } catch (Exception e) {
            System.out.println(e);
        }
        return null;
    }
}
