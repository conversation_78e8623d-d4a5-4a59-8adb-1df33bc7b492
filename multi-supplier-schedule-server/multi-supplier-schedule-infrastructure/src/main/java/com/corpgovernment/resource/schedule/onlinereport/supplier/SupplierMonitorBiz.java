package com.corpgovernment.resource.schedule.onlinereport.supplier;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AgreementStatusLabelEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.config.SupplierMonitorDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.config.inner.AirlineBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.config.inner.TimeScaleBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.*;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.CostCenterAndDepartSearchType;
import com.corpgovernment.resource.schedule.onlinereport.AbstractDeptDetailCommonBiz;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.supplier.*;
import com.corpgovernment.resource.schedule.onlinereport.common.JacksonMapper;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.AgreementDetailEnum;
import com.corpgovernment.resource.schedule.onlinereport.supplier.convert.AgreementAggMapper;
import com.corpgovernment.resource.schedule.onlinereport.supplier.convert.SupplierTrendMapper;
import com.corpgovernment.resource.schedule.onlinereport.supplier.dto.*;
import com.corpgovernment.resource.schedule.onlinereport.supplier.vo.AgreementExpiringDetailVO;
import com.corpgovernment.resource.schedule.onlinereport.utils.*;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022-09-06 10:35
 * @desc
 */
@Service
public class SupplierMonitorBiz extends AbstractDeptDetailCommonBiz {

    @Autowired
    private FltSupplierMonitorDaoImpl fltSupplierMonitorDaoImpl;
    @Autowired
    private HtlSupplierMonitorDaoImpl htlSupplierMonitorDaoImpl;
    @Autowired
    private TrainSupplierMonitorDaoImpl trainSupplierMonitorDaoImpl;

    @Autowired
    private CarSupplierMonitorDaoImpl carSupplierMonitorDao;

    @Autowired
    private SupplierTrendMapper supplierTrendMapper;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private AgreementAggMapper agreementAggMapper;

    @Autowired
    private SupplierMonitorDto supplierMonitor;

    @Resource(name = "inheritableSupplierMonitorExecutePool")
    private ExecutorService executorService;

    private final static int TOP_LIMIT_20 = 20;

    private final static int TOP_LIMIT_10 = 10;


    
    public AgreementViewInfo agreementView(OnlineReportAgreementConsumeRequest request) throws Exception {

        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        QueryReportBuTypeEnum queryReportBuTypeEnum = request.getQueryBu();
        List<AgreementViewDTO> list1 = null;
        List<AgreementMomAndYoyDTO> list2 = null;
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight){
             list1 = fltSupplierMonitorDaoImpl.agreementView(baseQueryConditionDTO, AgreementViewDTO.class, request.getProductType());
             list2 = fltSupplierMonitorDaoImpl.agreementMomAndYoy(baseQueryConditionDTO, AgreementMomAndYoyDTO.class, request.getProductType());
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel){
            list1 = htlSupplierMonitorDaoImpl.agreementView(baseQueryConditionDTO, AgreementViewDTO.class, request.getProductType());
            list2 = htlSupplierMonitorDaoImpl.agreementMomAndYoy(baseQueryConditionDTO, AgreementMomAndYoyDTO.class, request.getProductType());
        }
        list1 = Optional.ofNullable(list1).orElse(new ArrayList<>());
        // 总成交净价
        BigDecimal sumPrice = list1.stream().map(i->i.getTotalPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 总票张
        Integer sumQuantity = list1.stream().map(i->i.getTotalQuantity()).reduce(0, Integer::sum);
        Integer sumQuantityEconomy = 0;
        BigDecimal sumPriceEconomy = BigDecimal.ZERO;
        // 总票张经济舱
        sumQuantityEconomy = list1.stream().map(i->i.getTotalQuantityEconomy()).reduce(0, Integer::sum);
        // 总成交净价经济舱
        sumPriceEconomy = list1.stream().map(i->i.getTotalPriceEconomy()).reduce(BigDecimal.ZERO, BigDecimal::add);

        List<AgreementConsumeInfo> list = new ArrayList<>();
        AgreementViewInfo agreementViewBO = new AgreementViewInfo();
        Optional.ofNullable(list2).orElse(new ArrayList<>()).stream().findFirst().ifPresent(i->{
            agreementViewBO.setTotalPriceTA(OrpReportUtils.formatBigDecimal(i.getCurrentTotalPrice()));
            agreementViewBO.setTotalQuantityTA(OrpReportUtils.formatInteger(i.getTotalQuantity()));
            agreementViewBO.setAvgPriceTA(OrpReportUtils.formatBigDecimal(i.getAvgPrice()));
            agreementViewBO.setYoyTA(OrpReportUtils.divideWithPercent(
                    OrpReportUtils.formatBigDecimal(i.getCurrentTotalPrice()).subtract(OrpReportUtils.formatBigDecimal(i.getYoyTotalPrice()))
                    , OrpReportUtils.formatBigDecimal(i.getYoyTotalPrice()).abs()));
            agreementViewBO.setMomTA(OrpReportUtils.divideWithPercent(
                    OrpReportUtils.formatBigDecimal(i.getCurrentTotalPrice()).subtract(OrpReportUtils.formatBigDecimal(i.getMomTotalPrice()))
                    , OrpReportUtils.formatBigDecimal(i.getMomTotalPrice()).abs()));
        });
        agreementViewBO.setTotalQuantity(sumQuantity);
        agreementViewBO.setTotalPrice(sumPrice);
        agreementViewBO.setPriceTaPercent(OrpReportUtils.divideWithPercent(agreementViewBO.getTotalPriceTA(), agreementViewBO.getTotalPrice()));
        list1.forEach(i->{
            AgreementConsumeInfo bo = new AgreementConsumeInfo();
            bo.setDataType(i.getIsAgreement());
            bo.setTotalPrice(OrpReportUtils.formatBigDecimal(i.getTotalPrice()));
            bo.setTotalQuantity(i.getTotalQuantity());
            bo.setAvgPrice(OrpReportUtils.divide(i.getTotalPriceEconomy(), new BigDecimal(i.getTotalQuantityEconomy()), OrpConstants.TWO));
            list.add(bo);
        });
        // 汇总
        AgreementConsumeInfo sumAgreementPriceBO = new AgreementConsumeInfo();
        sumAgreementPriceBO.setDataType("ALL");
        sumAgreementPriceBO.setTotalPrice(OrpReportUtils.formatBigDecimal(sumPrice));
        sumAgreementPriceBO.setTotalQuantity(sumQuantity);
        sumAgreementPriceBO.setAvgPrice(OrpReportUtils.divide(sumPriceEconomy, new BigDecimal(sumQuantityEconomy), OrpConstants.TWO));
        list.add(sumAgreementPriceBO);
        agreementViewBO.setList(list);
        return agreementViewBO;
    }

    
    public AgreementAggInfo agreementAgg(OnlineReportAgreementConsumeRequest request) throws Exception {

        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<AgreementAggDTO> list1 = null;
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight){
            list1 = fltSupplierMonitorDaoImpl.agreementAgg(baseQueryConditionDTO, AgreementAggDTO.class, request.getProductType());
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel){
            list1 = htlSupplierMonitorDaoImpl.agreementAgg(baseQueryConditionDTO, AgreementAggDTO.class, request.getProductType());
        }
        if (CollectionUtils.isNotEmpty(list1)){
            return  agreementAggMapper.toBO(list1.get(0));
        }
        return null;

    }

    
    public List<String> agreementDetail(OnlineReportAgreementDetailRequest request) throws Exception {

        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<Map> result = new ArrayList<>();
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight){
            baseQueryConditionDTO.setAirLines(request.getBasecondition().getAirLines());
            result = fltSupplierMonitorDaoImpl.agreementDetail( baseQueryConditionDTO, request.getProductType(), request.getLang(), true);
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel){
            baseQueryConditionDTO.setHotelGroups(request.getBasecondition().getHotelGroups());
            result = htlSupplierMonitorDaoImpl.agreementDetail( baseQueryConditionDTO, request.getProductType(), request.getLang(), true);
        }
        if (CollectionUtils.isNotEmpty(result)){
            fomratResultData(result, request.getLang());
            return (List) result.stream().map(i-> OrpGsonUtils.toJsonNullStr(i)).collect(Collectors.toList());
        }
        return null;
    }

    
    public List<String> agreementDetailSum(OnlineReportAgreementDetailRequest request) throws Exception {

        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<Map> sum = new ArrayList<>();
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight){
            baseQueryConditionDTO.setAirLines(request.getBasecondition().getAirLines());
            sum = fltSupplierMonitorDaoImpl.agreementDetail( baseQueryConditionDTO, request.getProductType(), request.getLang(), false);
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel){
            baseQueryConditionDTO.setHotelGroups(request.getBasecondition().getHotelGroups());
            sum = htlSupplierMonitorDaoImpl.agreementDetail( baseQueryConditionDTO, request.getProductType(), request.getLang(), false);
        }
        if (CollectionUtils.isNotEmpty(sum)){
            fomratResultData(sum, request.getLang());
            return (List) sum.stream().map(i-> OrpGsonUtils.toJsonNullStr(i)).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 获取标题
     *
     * @return
     */
    public List<HeaderKeyValDataType> getAgreementDetailHearder(String lang,  QueryReportBuTypeEnum queryReportBuTypeEnum) {
        List<HeaderKeyValDataType> mapDimensions = getDimesionsDesc(queryReportBuTypeEnum, lang);
        List<HeaderKeyValDataType> mapStaticals = getStaticalsDes(getStatisticalList(queryReportBuTypeEnum.toString()), lang);
        mapDimensions.addAll(mapStaticals);
        return mapDimensions;
    }

    //    
    public List<Map> agreementDeptDetail(OnlineReportAgreementDetailRequest request, AnalysisObjectEnum analysisObjectEnum, Pager pager) throws Exception {

        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<Map> result = null;
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight){
            result = fltSupplierMonitorDaoImpl.agreementDeptDetail(analysisObjectEnum, baseQueryConditionDTO, BizUtils.initPagerStartOne(pager), request.getProductType(),
                    request.getUser());
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel){
            result = htlSupplierMonitorDaoImpl.agreementDeptDetail(analysisObjectEnum, baseQueryConditionDTO, BizUtils.initPagerStartOne(pager), request.getProductType(),
                    request.getUser());
        }
        fomratResultData(result, request.getLang());
        return result;
    }

    /**
     * 获取标题
     *
     * @return
     */
    public List<HeaderKeyValDataType> getAgreementDeptDetailHearder(String lang,  AnalysisObjectEnum analysisObjectEnum, QueryReportBuTypeEnum queryReportBuTypeEnum) {
        List<HeaderKeyValDataType> mapDimensions = getDimesionsDesc(analysisObjectEnum, lang);
        List<HeaderKeyValDataType> mapStaticals = getStaticalsDes(getStatisticalList(queryReportBuTypeEnum.toString(), 1), lang);
        mapDimensions.addAll(mapStaticals);
        if (analysisObjectEnum == AnalysisObjectEnum.UID){
            mapDimensions.add(getHeaderKeyValMap("dept1", "Exceltopname.depone", lang));
            mapDimensions.add(getHeaderKeyValMap("cost_center1", "Exceltopname.costcenterone", lang));
        }
        return mapDimensions;
    }

    
    public Integer count(OnlineReportAgreementDetailRequest request, AnalysisObjectEnum analysisObjectEnum)
            throws Exception {

        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        int count = 0;
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight){
            count = fltSupplierMonitorDaoImpl.agreementDeptDetailCount(analysisObjectEnum, baseQueryConditionDTO, request.getProductType(), request.getUser());
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel){
            count = htlSupplierMonitorDaoImpl.agreementDeptDetailCount(analysisObjectEnum, baseQueryConditionDTO, request.getProductType(), request.getUser());
        }
        return count;
    }


    /**
     * 获取统计项标题
     *
     * @param statisticalStringList
     * @return
     */
    private List<HeaderKeyValDataType> getStaticalsDes(List<AgreementDetailEnum> statisticalStringList, String lang) {
        List<HeaderKeyValDataType> result = new ArrayList<>();
        for (AgreementDetailEnum statisticalEnum : statisticalStringList) {
            HeaderKeyValDataType headerKeyValMap = new HeaderKeyValDataType();
            headerKeyValMap.setHeaderKey(statisticalEnum.toString());
            headerKeyValMap.setHeaderValue(SharkUtils.get(statisticalEnum.getSharkKey(), lang));
            headerKeyValMap.setDataType(statisticalEnum.getDataType());
            result.add(headerKeyValMap);
        }
        return result;
    }

    public List<AgreementDetailEnum> getStatisticalList(String bizType) {
        return AgreementDetailEnum.getStaticalsByBizType(bizType);
    }

    public List<AgreementDetailEnum> getStatisticalList(String bizType, int aggType) {
        return AgreementDetailEnum.getStaticalsByAggType(bizType, aggType);
    }

    /**
     * 获得维度标题
     *
     * @param buTypeEnum
     * @param lang
     * @return
     */
    private List<HeaderKeyValDataType> getDimesionsDesc(QueryReportBuTypeEnum buTypeEnum, String lang) {
        List<HeaderKeyValDataType> result = new ArrayList<>();
        switch (buTypeEnum) {
            case flight:
                result.add(getHeaderKeyValMap("airlineName", "SupplierMonitor.Airlines", lang));
                break;
            case hotel:
                result.add(getHeaderKeyValMap("hotelGroupName", "Exceltopname.hotelgroup", lang));
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 统一格式化结果数据
     *
     * @param list
     */
    protected void fomratResultData(List<Map> list, String lang) {
        int i = 0;
        for (Map map : list) {
            i++;
            for (Object key : map.keySet()) {
                // 协议状态要映射中文，特殊处理
                AgreementDetailEnum rcStatisticalsEnum = getStaticalByKey((String) key);
                if (rcStatisticalsEnum != null) {
                    BigDecimal target = OrpReportUtils.formatBigDecimal(
                            Objects.isNull(map.get(key)) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(map.get(key))),
                            rcStatisticalsEnum.getNum(), true);
                    map.put(key, rcStatisticalsEnum.isPercent() ? target.toString().concat("%") : target);
                }
            }
        }
    }

    private String getAgreementStatusDesc(String code, String lang){
        String sharkKey = StringUtils.EMPTY;
        if (StringUtils.isEmpty(code)){
            return sharkKey;
        }
        code = StringUtils.trim(code);
        if (StringUtils.equalsIgnoreCase(code, "ADVISE")){
            // 前期建议
            sharkKey = "SupplierMonitor.AgreementStatus1";
        }else if (StringUtils.equalsIgnoreCase(code, "STOP")){
            // 建议终止
            sharkKey = "SupplierMonitor.AgreementStatus2";
        }else if (StringUtils.equalsIgnoreCase(code, "SIGNING")){
            // 签约中
            sharkKey = "SupplierMonitor.AgreementStatus3";
        }else if (StringUtils.equalsIgnoreCase(code, "SIGNED")){
            // 在约
            sharkKey = "SupplierMonitor.AgreementStatus4";
        }else if (StringUtils.equalsIgnoreCase(code, "EXPIRED")){
            // 到期
            sharkKey = "SupplierMonitor.AgreementStatus5";
        }else if (StringUtils.equalsIgnoreCase(code, "BROKEN")){
            // 断约
            sharkKey = "SupplierMonitor.AgreementStatus6";
        }else {
            return code;
        }
        return SharkUtils.get(sharkKey, lang);
    }

    protected AgreementDetailEnum getStaticalByKey(String key) {
        Optional optional = Arrays.stream(AgreementDetailEnum.values()).filter(j->j.name().equals((String) key)).findFirst();
        if (optional.isPresent()){
            return (AgreementDetailEnum) optional.get();
        }else {
            return null;
        }
    }

    /**
     * 简报
     * @param request
     * @return
     * @throws Exception
     */
    
    public Map<String, String> jianbao(OnlineReportAgreementConsumeRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight){
            return flightJianbao(request, baseQueryConditionDTO);
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel){
            return hotelJianbao(request, baseQueryConditionDTO);
        }
        return null;
    }

    private Map flightJianbao(OnlineReportAgreementConsumeRequest request, BaseQueryConditionDTO baseQueryConditionDTO) throws Exception {

        String ticketAirlineDim = SharkUtils.isEN(request.getLang()) ? "airline_en_name" : "airline_cn_name";
        String flightCityDim = SharkUtils.isEN(request.getLang()) ? "flight_city_en" : "flight_city";
        // 协议航司
        List<String> listTa = fltSupplierMonitorDaoImpl.queryAgreementAirlinesName(baseQueryConditionDTO, ticketAirlineDim, request.getProductType());
        // 协议信息不需要查询
//        List<AgreementDetailDto> listTaDetail = this.queryTaDetail(baseQueryConditionDTO, ticketAirlineDim, request.getProductType());
        // 航司
        String exAirlineSqlCondition = Optional.ofNullable(supplierMonitor.getExcludedAirlines()).map(Collection::stream)
                .map(stream -> stream.map(AirlineBo::getAirlineCode).collect(
                        Collectors.joining("','", " and airline not in ('", "')")))
                .orElse(null);
        List<AgreementAnalysisDTO> list1 = fltSupplierMonitorDaoImpl.jianbao(baseQueryConditionDTO, AgreementAnalysisDTO.class,
                request.getProductType(), ticketAirlineDim, exAirlineSqlCondition);
        Map<String, BigDecimal> dimSaveAmountMap = list1.stream().collect(Collectors.toMap(AgreementAnalysisDTO::getDim,
                AgreementAnalysisDTO::getTotalSaveAmount));
        // 航线
        List<AgreementAnalysisDTO> list2 = fltSupplierMonitorDaoImpl.jianbao(baseQueryConditionDTO, AgreementAnalysisDTO.class,
                request.getProductType(), flightCityDim, null);
        // 总协议成交净价
        BigDecimal sumTaPrice = Optional.ofNullable(list1).orElse(new ArrayList<>()).stream().map(i->i.getSumPriceTa()).reduce(BigDecimal.ZERO, BigDecimal::add);
        list1.sort((o1, o2) -> o2.getSumPrice().subtract(o1.getSumPrice()).intValue());
        list2.sort((o1, o2) -> o2.getSumPrice().subtract(o1.getSumPrice()).intValue());
        list1 = list1.size() > TOP_LIMIT_10 ? list1.subList(OrpConstants.ZERO, TOP_LIMIT_10) : list1;
        list2 = list2.size() > TOP_LIMIT_10 ? list2.subList(OrpConstants.ZERO, TOP_LIMIT_10) : list2;
        // 前三航线
        List<String> top3LineCitys = new ArrayList<>();
        // 前三非协航
        List<String> top3Ntas = new ArrayList<>();
        int count = 0;
        for (AgreementAnalysisDTO dto : list2){
            if (dto.getAvgPrice().compareTo(new BigDecimal(1000)) > 0 && count < 3){
                top3LineCitys.add(dto.getDim());
                count++;
            }
        }
        count = 0;
        // 当前时间端内命中协航的数量
        int airLineCountTa = 0;
        for (AgreementAnalysisDTO dto : list1){
            if (Optional.ofNullable(listTa).orElse(new ArrayList<>()).stream().anyMatch(j->StringUtils.equalsIgnoreCase(dto.getDim(), j))){
                airLineCountTa++;
            }else {
                if (count < 3){
                    top3Ntas.add(dto.getDim());
                }
                count++;
            }
        }
        // 当前航司数量
        int airLineCount = list1.size();
        // 当前时间段内协航覆盖率
        BigDecimal coverageTa = OrpReportUtils.divideWithPercent(airLineCountTa,airLineCount);
        /*
         * sumPriceTa: 三方成交净价
         * quantityTa：三方协议航司数量，coverageTa：top20航司中协议覆盖率，highFrequencyNta：使用率较高的三个分协议航司，
         * hotFlightCity：均价大于1000的三个航线
         * listExpiringTa: 即将到期协议集合
         * */
        return ImmutableMap.builder()
                .put("sumPriceTa", OrpReportUtils.formatBigDecimal(sumTaPrice).toPlainString())
                .put("quantityTa", String.valueOf(listTa.size()))
                .put("coverageTa", coverageTa.toPlainString())
                .put("highFrequencyNta", StringUtils.join(top3Ntas, ","))
                .put("hotFlightCity", StringUtils.join(top3LineCitys, ",")).build();
//                .put("listExpiringTa", JacksonMapper.DEFAULT.toJson(buildExpiringTa(listTaDetail, dimSaveAmountMap))).build();
    }

    private List<AgreementExpiringDetailVO> buildExpiringTa(List<AgreementDetailDto> listTaDetail, Map<String, BigDecimal> dimSaveAmountMap) {
        if (CollectionUtils.isEmpty(listTaDetail)) {
            return Lists.newArrayList();
        }

        Map<String, AgreementDetailDto> dimLastTaDetailMap = listTaDetail.stream().collect(Collectors.toMap(AgreementDetailDto::getDim,
                Function.identity(), (a, b) -> a.getAgreementEndDate().compareTo(b.getAgreementEndDate()) > 0 ? a : b));

        TimeScaleBo timeScale = supplierMonitor.getTimeScale();
        Date dateOffset = OrpDateTimeUtils.currentDateOffset(timeScale.getTimeScale(), timeScale.getTimeUnit());
        // 即将到期协议
        List<AgreementDetailDto> expiringTaList = dimLastTaDetailMap.values().stream().filter(ta ->
                        dateOffset.after(OrpDateTimeUtils.dateTimeStrToDate(ta.getAgreementEndDate(), OrpDateTimeUtils.DEFAULT_DATE))
                                && new Date().before(OrpDateTimeUtils.dateTimeStrToDate(ta.getAgreementEndDate(), OrpDateTimeUtils.DEFAULT_DATE)))
                .collect(Collectors.toList());

        return expiringTaList.stream().map(dto -> new AgreementExpiringDetailVO(dto.getDim(), dto.getAgreementEndDate(),
                dimSaveAmountMap.get(dto.getDim()))).sorted(Comparator.comparing(AgreementExpiringDetailVO::getEndDate).reversed()
                .thenComparing(AgreementExpiringDetailVO::getSavePrice).reversed()).collect(Collectors.toList());
    }

    private List<AgreementDetailDto> queryTaDetail(BaseQueryConditionDTO baseQueryConditionDTO, String ticketAirlineDim, String productType) throws Exception {

        List<AgreementDetailDto> res = Lists.newArrayList();
        List<AgreementDimDTO> agreementDimDTOS = fltSupplierMonitorDaoImpl.queryAgreementAirlines(baseQueryConditionDTO, AgreementDimDTO.class, ticketAirlineDim, productType);
        if (CollectionUtils.isEmpty(agreementDimDTOS)) {
            return res;
        }
        Map<String, String> dimIdNameMap = agreementDimDTOS.stream().filter(StreamUtils.distinctByKey(AgreementDimDTO::getDimId))
                .collect(Collectors.toMap(AgreementDimDTO::getDimId, AgreementDimDTO::getDim));
        res = fltSupplierMonitorDaoImpl.queryAgreementDetail(new ArrayList<>(dimIdNameMap.keySet()), AgreementDetailDto.class, baseQueryConditionDTO.getCorpIds());
        res.stream().forEach(dto -> dto.setDim(dimIdNameMap.getOrDefault(dto.getDimId(), "")));
        return res;
    }

    private Map hotelJianbao(OnlineReportAgreementConsumeRequest request, BaseQueryConditionDTO baseQueryConditionDTO) throws Exception {

        // 酒店集团
        String hotelGroupNameDim = SharkUtils.isEN(request.getLang()) ? "agreement_mgrgroup_name_en" : "agreement_mgrgroup_name";
        List<String> listTa = htlSupplierMonitorDaoImpl.queryAgreementHotelGroupName(baseQueryConditionDTO, hotelGroupNameDim, request.getProductType());
        List<AgreementAnalysisDTO> list1 = htlSupplierMonitorDaoImpl.jianbao(baseQueryConditionDTO, AgreementAnalysisDTO.class, request.getProductType(), hotelGroupNameDim);
        // 酒店城市
        String hotelCityNameDim = SharkUtils.isEN(request.getLang()) ? "city_name_en" : "city_name";
        List<AgreementAnalysisDTO> list2 = htlSupplierMonitorDaoImpl.jianbao(baseQueryConditionDTO, AgreementAnalysisDTO.class, request.getProductType(), hotelCityNameDim);
        BigDecimal sumTaAmount = Optional.ofNullable(list1).orElse(new ArrayList<>()).stream().map(i->i.getSumAmountTa()).reduce(BigDecimal.ZERO, BigDecimal::add);
        list1.sort((o1, o2) -> o2.getSumAmount().subtract(o1.getSumAmount()).intValue());
        list2.sort((o1, o2) -> o2.getSumAmount().subtract(o1.getSumAmount()).intValue());
        list1 = list1.size() > TOP_LIMIT_10 ? list1.subList(OrpConstants.ZERO, TOP_LIMIT_10) : list1;
        AtomicInteger countTa = new AtomicInteger();
        list1.stream().forEach(i->{
            if (Optional.ofNullable(listTa).orElse(new ArrayList<>()).stream().anyMatch(j->StringUtils.equalsIgnoreCase(i.getDim(), j))){
                countTa.getAndIncrement();
            }
        });
        // 当前酒店集团数量
        int hotelGroupCount = list1.size();
        // 当前时间段内协议酒店集团覆盖率
        BigDecimal coverageTa = OrpReportUtils.divideWithPercent(countTa.get(),hotelGroupCount);
        // 取top20
        list2 = list2.size() > TOP_LIMIT_10 ? list2.subList(OrpConstants.ZERO, TOP_LIMIT_10) : list2;
        list2.sort((o1, o2) -> o1.getAgreementPercent().subtract(o2.getAgreementPercent()).intValue());
        // 协议使用率偏低的三个城市
        int count = 0;
        List top3TaLows = new ArrayList();
        for (AgreementAnalysisDTO dto : list2){
            if (count < 3){
                top3TaLows.add(dto.getDim());
                count++;
            }
        }
        // 酒店星级
        List<AgreementAnalysisDTO> list3 = htlSupplierMonitorDaoImpl.jianbao(baseQueryConditionDTO, AgreementAnalysisDTO.class, request.getProductType(), "star");
        // 占比
        BigDecimal totalQuantity = new BigDecimal("0");
        for (AgreementAnalysisDTO dto: list3) {
            totalQuantity = totalQuantity.add(dto.getSumQuantity());
        }
        String star = StringUtils.EMPTY;
        BigDecimal usePercent = BigDecimal.ZERO;
        BigDecimal userPercentTa = BigDecimal.ZERO;
        for (AgreementAnalysisDTO dto: list3) {
            BigDecimal usePercentTemp = OrpReportUtils.divideWithPercent(dto.getSumQuantity(), totalQuantity);
            if (usePercentTemp.compareTo(usePercent) > 0){
                usePercent = usePercentTemp;
                star = dto.getDim();
                userPercentTa = dto.getAgreementPercent();
            }
        }
        /*
        * sumAmountTa: 三方消费金额
        * quantityTa：三方协议酒店集团数量，coverageTa：top20集团中协议覆盖率，lowUsage：协议使用率底的3个城市，
        * starOfMaxPercent：入住率最高的星级，maxPercent：最高入住率，taPercent：最高入住率的协议使用率
        * */
        return ImmutableMap.builder()
                .put("sumAmountTa", OrpReportUtils.formatBigDecimal(sumTaAmount).toPlainString())
                .put("quantityTa", String.valueOf(listTa.size()))
                .put("coverageTa", coverageTa.toPlainString())
                .put("lowUsage", StringUtils.join(top3TaLows, ","))
                .put("starOfMaxPercent", star)
                .put("maxPercent", usePercent.toPlainString())
                .put("taPercent", userPercentTa.toPlainString()).build();
    }

    /**
     * app入口
     * @param request
     * @return
     * @throws Exception
     */
    
    public Map<String, String> appIndex(OnlineReportAgreementConsumeRequest request) throws Exception {

        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        Map<String, String> result = new HashMap<>();
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel){
            String hotelGroupNameDim = SharkUtils.isEN(request.getLang()) ? "agreement_mgrgroup_name_en" : "agreement_mgrgroup_name";

            List<String> listTa = htlSupplierMonitorDaoImpl.queryAgreementHotelGroupName(baseQueryConditionDTO, hotelGroupNameDim, request.getProductType());
            // 酒店集团
            List<AgreementAnalysisDTO> list1 = htlSupplierMonitorDaoImpl.index(baseQueryConditionDTO, AgreementAnalysisDTO.class, request.getProductType(), hotelGroupNameDim);
            // 总协议消费金额
            BigDecimal sumTaAmount = Optional.ofNullable(list1).orElse(new ArrayList<>()).stream().map(i->i.getSumAmountTa()).reduce(BigDecimal.ZERO, BigDecimal::add);
            list1 = Optional.ofNullable(list1).orElse(new ArrayList<>()).stream().filter(i->StringUtils.isNotEmpty(i.getDim())).collect(Collectors.toList());
            list1.sort((o1, o2) -> o2.getSumAmount().subtract(o1.getSumAmount()).intValue());
            list1 = list1.size() > TOP_LIMIT_10 ? list1.subList(OrpConstants.ZERO, TOP_LIMIT_10) : list1;
            AtomicInteger countTa = new AtomicInteger();
            list1.stream().forEach(i->{
                if (Optional.ofNullable(listTa).orElse(new ArrayList<>()).stream().anyMatch(j->StringUtils.equalsIgnoreCase(i.getDim(), j))){
                    countTa.getAndIncrement();
                }
            });
            // 当前酒店集团数量
            int hotelGroupCount = list1.size();
            // 当前时间段内协议酒店集团覆盖率
            BigDecimal coverageTa = OrpReportUtils.divideWithPercent(countTa.get(),hotelGroupCount);
            result = ImmutableMap.<String, String>builder()
                    .put("amountTa", OrpReportUtils.formatBigDecimal(sumTaAmount).toString())
                    .put("coverageTa", coverageTa.toString()).build();
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight){
            String airlineDim = SharkUtils.isEN(request.getLang()) ? "airline_en_name" : "airline_cn_name";

            List<String> listTa = fltSupplierMonitorDaoImpl.queryAgreementAirlinesName(baseQueryConditionDTO, airlineDim, request.getProductType());
            // 航司
            List<AgreementAnalysisDTO> list1 = fltSupplierMonitorDaoImpl.index(baseQueryConditionDTO, AgreementAnalysisDTO.class, request.getProductType(), airlineDim);
            // 总协议成交净价
            BigDecimal sumTaAmount = Optional.ofNullable(list1).orElse(new ArrayList<>()).stream().map(i->i.getSumPriceTa()).reduce(BigDecimal.ZERO, BigDecimal::add);
            list1 = Optional.ofNullable(list1).orElse(new ArrayList<>()).stream().filter(i->StringUtils.isNotEmpty(i.getDim())).collect(Collectors.toList());
            list1.sort((o1, o2) -> o2.getSumPrice().subtract(o1.getSumPrice()).intValue());
            list1 = list1.size() > TOP_LIMIT_10 ? list1.subList(OrpConstants.ZERO, TOP_LIMIT_10) : list1;
            AtomicInteger countTa = new AtomicInteger();
            list1.stream().forEach(i->{
                if (Optional.ofNullable(listTa).orElse(new ArrayList<>()).stream().anyMatch(j->StringUtils.equalsIgnoreCase(i.getDim(), j))){
                    countTa.getAndIncrement();
                }
            });
            // 当前航司数量
            int airLineCount = list1.size();
            // 当前协议一航司覆盖率
            BigDecimal coverageTa = OrpReportUtils.divideWithPercent(countTa.get(),airLineCount);
            result = ImmutableMap.<String, String>builder()
                    .put("amountTa", OrpReportUtils.formatBigDecimal(sumTaAmount).toString())
                    .put("coverageTa", coverageTa.toString()).build();
        }
        return result;
    }

    /**
     *
     * @param request
     * @return
     * @throws Exception
     */
    
    public List<HeaderKeyValMap> queryBaseData(OnlineReportAgreementBaseDataRequest request) throws Exception {

        List<HeaderKeyValMap> result = new ArrayList();
        List<AgreementDimDTO> list = new ArrayList();
        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel){
            String hotelGroupNameDim = SharkUtils.isEN(request.getLang()) ? "agreement_mgrgroup_name_en" : "agreement_mgrgroup_name";
            list = htlSupplierMonitorDaoImpl.queryAgreementHotelGroup(baseQueryConditionDTO, AgreementDimDTO.class, hotelGroupNameDim, request.getProductType());
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight){
            String airlineDim = SharkUtils.isEN(request.getLang()) ? "airline_en_name" : "airline_cn_name";
            list = fltSupplierMonitorDaoImpl.queryAgreementAirlines(baseQueryConditionDTO, AgreementDimDTO.class, airlineDim, request.getProductType());
        }
        for (AgreementDimDTO agreementDimDTO : list){
            HeaderKeyValMap headerKeyValMap = new HeaderKeyValMap();
            headerKeyValMap.setHeaderKey(agreementDimDTO.getDimId());
            headerKeyValMap.setHeaderValue(agreementDimDTO.getDim());
            result.add(headerKeyValMap);
        }
        return result;
    }

    
    public List<OnlineReportSupplierTrendInfo> trend(OnlineReportSupplierTrendRequest request) throws Exception {

        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        List<OnlineReportSupplierTrendInfo> result = new ArrayList<>();
        List<AgreementTrendAnalysisDTO> list = null;
        List<AgreementDetailDto> listTaDetail = null;
        List<AgreementIndustryCorpInfoDto> listIndustryAndCorpInfo = null;
        // 协议航司
        List<String> listTa = null;
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        String dim = (String) map.get("dim");
        // download为T的时候不限制top 20
        String download = (String) map.get("download");
        AgreementTrendAnalysis agreementTrendAnalysis = new AgreementTrendAnalysis();
        String destinationType = (String) map.get("destinationType");
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight){
            list = fltSupplierMonitorDaoImpl.queryTop(baseQueryConditionDTO, AgreementTrendAnalysisDTO.class, request.getProductType(), dim, destinationType);
            // 飞机协议明细
//            listTaDetail = fltSupplierMonitorDaoImpl.queryAgreementDetail(list.stream().map(AgreementTrendAnalysisDTO::getDimId).collect(Collectors.toList()),
//                    AgreementDetailDto.class, baseQueryConditionDTO.getCorpIds());
            if (StringUtils.equalsIgnoreCase("airline_en_name", dim) || StringUtils.equalsIgnoreCase("airline_cn_name", dim)) {
                listTa = fltSupplierMonitorDaoImpl.queryAgreementAirlinesName(baseQueryConditionDTO, dim, request.getProductType());
            }
            // top20航司、航线，查询行业、商旅信息
            List<String> fltDimFliedList = Lists.newArrayList("airline_en_name", "airline_cn_name",
                    "flight_city", "flight_city_en");
            if (StringUtils.isNotEmpty(dim) && fltDimFliedList.contains(dim)) {
                List<String> dimList = list.stream().map(AgreementTrendAnalysisDTO::getDim).collect(Collectors.toList());
                listIndustryAndCorpInfo = PageOperateUtils.pageFunctionAsync(dimList, 2000, executorService,
                        (subList) -> {
                            try {
                                return fltSupplierMonitorDaoImpl.listCorpAndIndustryDataByDimList(baseQueryConditionDTO,
                                        request.getProductType(), subList, dim, AgreementIndustryCorpInfoDto.class);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });
            }
            list.sort((o1, o2) -> o2.getSumPrice().compareTo(o1.getSumPrice()));
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel){
            // 酒店类型 C:协议,NC:非协议
            String contractType = (String) map.get("contractType");
            list = htlSupplierMonitorDaoImpl.queryTop(baseQueryConditionDTO, AgreementTrendAnalysisDTO.class, request.getProductType(), dim, destinationType, contractType);
            if (StringUtils.equalsIgnoreCase("agreement_mgrgroup_name_en", dim) || StringUtils.equalsIgnoreCase("agreement_mgrgroup_name", dim)) {
                listTa = htlSupplierMonitorDaoImpl.queryAgreementHotelGroupName(baseQueryConditionDTO, dim, request.getProductType());
            }
            // top20城市、酒店集团、酒店品牌、酒店，查询行业、商旅信息
            List<String> dimFiledList = Lists.newArrayList("hotel_name", "hotel_name_en", "agreement_mgrgroup_name_en",
                    "agreement_mgrgroup_name", "city_name_en", "city_name", "hotel_brand_name");
            if (StringUtils.isNotEmpty(dim) && dimFiledList.contains(dim)) {
                List<String> dimList =
                        list.stream().map(AgreementTrendAnalysisDTO::getDim).collect(Collectors.toList());
                listIndustryAndCorpInfo = PageOperateUtils.pageFunctionAsync(
                        dimList, 2000, executorService,
                        (subList) -> {
                            try {
                                return htlSupplierMonitorDaoImpl.listCorpAndIndustryDataByDimList(baseQueryConditionDTO,
                                        request.getProductType(), destinationType, contractType, subList, dim, AgreementIndustryCorpInfoDto.class);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });
            }
            list.sort((o1, o2) -> o2.getSumAmount().compareTo(o1.getSumAmount()));
        }
        if (request.getQueryBu() == QueryReportBuTypeEnum.train){
            list = trainSupplierMonitorDaoImpl.queryTop(baseQueryConditionDTO, AgreementTrendAnalysisDTO.class, dim);
            list.sort((o1, o2) -> o2.getSumAmount().compareTo(o1.getSumAmount()));
        }
        agreementTrendAnalysis.setAirLines(listTa);
        agreementTrendAnalysis.setTrendList(list);
        if (!StringUtils.equalsIgnoreCase(download, "T")){
            // 非下载的时候如果传了topLimit则按topLimit取，否则取前20
            int topLimit = Optional.ofNullable(request.getTopLimit()).orElse(TOP_LIMIT_20);
            list = list.size() > topLimit ? list.subList(OrpConstants.ZERO, topLimit) : list;
        }else {
            // 下载的时候如果传了topLimit则按topLimit取，否则取全部
            if (Objects.nonNull(request.getTopLimit()) && request.getTopLimit() > 0){
                list = list.size() > request.getTopLimit() ? list.subList(OrpConstants.ZERO, request.getTopLimit()) : list;
            }
        }
        if (CollectionUtils.isNotEmpty(list)){
            result = supplierTrendMapper.toBOs(list, agreementTrendAnalysis);
            // 协议到期时间
            Map<String, AgreementDetailDto> dimLastTaDetailMap = Maps.newHashMap();
            // 行业/商旅信息
            Map<String, AgreementIndustryCorpInfoDto> dimIndustryAndCorpInfoMap =
                    Optional.ofNullable(listIndustryAndCorpInfo).map(Collection::stream).map(stream ->
                                    stream.collect(Collectors.toMap(AgreementIndustryCorpInfoDto::getDim, Function.identity(),
                                            (a, b) -> a.getIndustryAvgPrice().compareTo(b.getIndustryAvgPrice()) > 0 ? a : b)))
                            .orElse(Maps.newHashMap());
            // 插入 ->>> 协议到期时间，行业/商旅信息
            result.forEach(r -> {
                if (MapUtils.isNotEmpty(dimLastTaDetailMap)) {
                    AgreementDetailDto detailDto = dimLastTaDetailMap.getOrDefault(r.getDimId(), new AgreementDetailDto());
                    // 若协议状态为到期，且到期时间大于当前时间，则到期时间不显示
                    if (AgreementStatusLabelEnum.EXPIRED.getCode().equalsIgnoreCase(detailDto.getAgreementStatusLabel()) &&
                            OrpDateTimeUtils.compareDatePair(
                                    OrpDateTimeUtils.getOrDefaultDate(detailDto.getAgreementEndDate(), null),
                                    new Date())) {
                        r.setAgreementEndDate(StringUtils.EMPTY);
                    } else {
                        r.setAgreementEndDate(detailDto.getAgreementEndDate());
                    }
                    r.setAgreementStatusLabel(StringUtils.isEmpty(detailDto.getAgreementStatusLabel()) ? "DEFAULT" : detailDto.getAgreementStatusLabel());
                    r.setAgreementStatusLabelDesc(AgreementStatusLabelEnum.getNameByCode(detailDto.getAgreementStatusLabel(), request.getLang()));
                }
                if (MapUtils.isNotEmpty(dimIndustryAndCorpInfoMap)) {
                    AgreementIndustryCorpInfoDto IndustryCorpInfo = dimIndustryAndCorpInfoMap.getOrDefault(r.getDim(), new AgreementIndustryCorpInfoDto());
                    r.setIndustryAvgPrice(OrpReportUtils.formatBigDecimal(IndustryCorpInfo.getIndustryAvgPrice()));
                    r.setCorpAvgPrice(OrpReportUtils.formatBigDecimal(IndustryCorpInfo.getCorpAvgPrice()));
                    r.setIndustryAvgPriceTa(OrpReportUtils.formatBigDecimal(IndustryCorpInfo.getIndustryAvgPriceTa()));
                    r.setCorpAvgPriceTa(OrpReportUtils.formatBigDecimal(IndustryCorpInfo.getCorpAvgPriceTa()));
                    r.setIndustryAvgPriceNta(OrpReportUtils.formatBigDecimal(IndustryCorpInfo.getIndustryAvgPriceNta()));
                    r.setCorpAvgPriceNta(OrpReportUtils.formatBigDecimal(IndustryCorpInfo.getCorpAvgPriceNta()));
                }
            });
        }
        // 特殊处理，非协议预估节省、ppt过滤特殊协议航司
        if (request.getQueryBu() == QueryReportBuTypeEnum.flight
                && (StringUtils.equalsIgnoreCase("airline_en_name", dim) || StringUtils.equalsIgnoreCase("airline_cn_name", dim))) {
            List<String> airlineNtaList = result.stream().filter(i -> i.getAgreementTag() == 0).map(OnlineReportSupplierTrendInfo::getDim).collect(Collectors.toList());
            if (StringUtils.equalsIgnoreCase((CharSequence) map.get("from"), "ppt")){
                // 针对ppt做特殊处理,既不是协议也不是非协议，tag=2
                List<String> excluedAirlineList = Optional.ofNullable(supplierMonitor.getExcludedAirlines()).map(Collection::stream)
                        .map(stream -> stream.map(AirlineBo::getAirlineCode).collect(Collectors.toList()))
                        .orElse(null);
                if (CollectionUtils.isNotEmpty(excluedAirlineList)){
                    for (OnlineReportSupplierTrendInfo info : result){
                        if (excluedAirlineList.stream().anyMatch(i->StringUtils.equalsIgnoreCase(i, info.getDimId()))){
                            info.setAgreementTag(2);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(airlineNtaList)){
                String exAirlineSqlCondition = Optional.ofNullable(supplierMonitor.getExcludedAirlines()).map(Collection::stream)
                        .map(stream -> stream.map(AirlineBo::getAirlineCode).collect(
                                Collectors.joining("','", " and airline not in ('", "')")))
                        .orElse(null);
                List<FlightSaveDTO> flightSaveDTOList = fltSupplierMonitorDaoImpl.queryEstimateSave(baseQueryConditionDTO,
                        FlightSaveDTO.class, request.getProductType(), dim, airlineNtaList, exAirlineSqlCondition);
                for (FlightSaveDTO flightSaveDTO : flightSaveDTOList){
                    BigDecimal sumSave = OrpReportUtils.formatBigDecimal(Optional.ofNullable(flightSaveDTO.getSaveAmount3c()).orElse(BigDecimal.ZERO)
                            .add(compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getSaveAmountPremium())));
                    BigDecimal sumNetfare = OrpReportUtils.formatBigDecimal(Optional.ofNullable(flightSaveDTO.getNetfare3c()).orElse(BigDecimal.ZERO)
                            .add(compareValAndGetDefault(flightSaveDTO.getSaveAmountPremium(), flightSaveDTO.getNetfarePremium())));
                    OnlineReportSupplierTrendInfo trendInfo = result.stream().filter(i->StringUtils.equalsIgnoreCase(i.getDim(), flightSaveDTO.getDim())).findFirst().get();
                    // 预估节省金额
                    trendInfo.setEstimateSaveAmount(OrpReportUtils.formatBigDecimal(trendInfo.getSumPrice()
                            .multiply(OrpReportUtils.divide(sumSave, sumNetfare, OrpConstants.TWO))));
                }
            }
        }
        return result;
    }

    protected BigDecimal compareValAndGetDefault(BigDecimal saveAmountPremium, BigDecimal defaultVal) {
        if (saveAmountPremium == null || saveAmountPremium.compareTo(BigDecimal.ZERO) < 0) {
            return BigDecimal.ZERO;
        }
        return Optional.ofNullable(defaultVal).orElse(BigDecimal.ZERO);
    }


    public List<String> queryCostCenterOrDepartmentOrCorpId(CostCenterAndDepartSearchType searchType) throws Exception {

        // 各个产线获取
        List<String> fltResult = fltSupplierMonitorDaoImpl.queryCostCenterOrDepartmentOrCorpId(searchType);
        List<String> htlResult = htlSupplierMonitorDaoImpl.queryCostCenterOrDepartmentOrCorpId(searchType);
        List<String> carResult = carSupplierMonitorDao.queryCostCenterOrDepartmentOrCorpId(searchType);
        List<String> trainResult = trainSupplierMonitorDaoImpl.queryCostCenterOrDepartmentOrCorpId(searchType);
        if (CollectionUtils.isNotEmpty(fltResult) || CollectionUtils.isNotEmpty(htlResult)
                || CollectionUtils.isNotEmpty(carResult) || CollectionUtils.isNotEmpty(trainResult)) {
            return Stream.of(fltResult, htlResult, carResult, trainResult).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
