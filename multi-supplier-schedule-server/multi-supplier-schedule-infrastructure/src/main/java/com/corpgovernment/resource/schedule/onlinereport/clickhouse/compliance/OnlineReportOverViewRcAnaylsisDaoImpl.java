package com.corpgovernment.resource.schedule.onlinereport.clickhouse.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTimesDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTrendDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
import static com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;

/*
 * <AUTHOR>
 *
 * @date 2022/4/10 20:49
 *
 * @Desc
 */
@Service
@Slf4j
public class OnlineReportOverViewRcAnaylsisDaoImpl extends AbstractOnlineReportTopRcAnaylsisDao {

    private static final String LOG_TITLE = "OnlineReportOverViewRcAnaylsisDaoImpl";

    private static final String CORP_PAY = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.corpPayType");

    private static final String DEAL = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Deal");

    private static final String OVERVIEW_RC_SQL = "SELECT %s  \n"
            + ",coalesce(flight.rcTimes, 0) + coalesce(hotel.rcTimes, 0) + coalesce(train.rcTimes, 0) AS RC_TIMES_OVERVIEW\n"
            + "    , round(toFloat64(coalesce(flight.saveAmount, 0) + coalesce(hotel.saveAmount, 0)), 2) AS RC_SAVEAMOUNT_OVERVIEW "
            + "    , round(case when coalesce(flight.orderCount, 0) + coalesce(hotel.orderCount, 0) + coalesce(train.orderCount, 0) !=0  "
            + "   then divide(coalesce(flight.rcTimes, 0) + coalesce(hotel.rcTimes, 0) + coalesce(train.rcTimes, 0)"
            + "    , coalesce(flight.orderCount, 0) + coalesce(hotel.orderCount, 0) + coalesce(train.orderCount, 0)) * 100 else 0 end, 2) as RC_PERCENT_OVERVIEW"
            + "    , coalesce(flight.rcTimes, 0) AS FLT_RC_TIMES_OVERVIEW\n"
            + "    , round(case when coalesce(flight.orderCount, 0) !=0  then divide(coalesce(flight.rcTimes, 0)"
            + ", coalesce(flight.orderCount, 0)) * 100 else 0 end, 2) as FLT_RC_PERCENT_OVERVIEW "
            + "    , round(toFloat64(coalesce(flight.saveAmount, 0)) , 2) AS FLT_RC_SAVEAMOUNT_OVERVIEW "
            + "    , coalesce(hotel.rcTimes, 0) AS HTL_RC_TIMES_OVERVIEW\n"
            + "    , round(case when coalesce(hotel.orderCount, 0) !=0  then divide(coalesce(hotel.rcTimes, 0)"
            + ", coalesce(hotel.orderCount, 0)) * 100 else 0 end, 2) as HTL_RC_PERCENT_OVERVIEW "
            + "    , round(toFloat64(coalesce(hotel.saveAmount, 0)) , 2) AS HTL_RC_SAVEAMOUNT_OVERVIEW "
            + "    , coalesce(train.rcTimes, 0) AS TRAIN_RC_TIMES_OVERVIEW\n"
            + "    , round(case when coalesce(train.orderCount, 0) !=0  then divide(coalesce(train.rcTimes, 0)"
            + ", coalesce(train.orderCount, 0)) * 100 else 0 end, 2) as TRAIN_RC_PERCENT_OVERVIEW "
            + " FROM ( SELECT %s \n"
            + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
            + "        , SUM(CASE WHEN  fee_type = '" + CORP_PAY + "' AND audited <> 'F' AND %s "
            + "         AND  is_refund = 'F' THEN over_std_esti_save_amount END) AS saveAmount"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s  group by %s \n"
            + "        having (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0) \n"
            + "         flight full JOIN (\n"
            + "        SELECT %s \n"
            + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
            + "        , SUM(CASE WHEN  order_status = '" + DEAL + "' AND is_oversea IN ('F','O','T') AND %s "
            + "        AND  is_refund = 'F' THEN over_std_esti_save_amount END) AS saveAmount "
            + "        FROM olrpt_indexhoteldownload_all WHERE %s %s group by %s \n"
            + "          having (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0)\n"
            + "         hotel on %s full JOIN (\n"
            + "        SELECT %s \n"
            + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
            + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes\n"
            + "        FROM olrpt_indextraindownload_all WHERE %s AND order_status  in ('TA','RP','EP','EA') group by %s\n"
            + "            having (COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END)) != 0)\n"
            + "         train on %s   ORDER BY RC_TIMES_OVERVIEW DESC";

    private static final String OVERVIEW_RC_COUNT_SQL = "SELECT  COUNT(distinct %s) as countAll \n"
            + " FROM (select %s " +
            " , coalesce(sum(fltOrderCount), 0) + coalesce(sum(htlOrderCount), 0) + coalesce(sum(trainOrderCount), 0) as totalOrderCount" +
            " , coalesce(sum(fltRcTimes), 0) + coalesce(sum(htlRcTimes), 0) + coalesce(sum(trainRcTimes), 0) AS RC_TIMES_OVERVIEW" +
            " , coalesce(sum(fltRcTimes), 0) AS FLT_RC_TIMES_OVERVIEW" +
            " , coalesce(sum(fltOrderCount), 0) AS totalFltOrderCount" +
            " , coalesce(sum(htlRcTimes), 0) AS HTL_RC_TIMES_OVERVIEW" +
            " , coalesce(sum(htlOrderCount), 0) AS totalHtlOrderCount" +
            " , coalesce(sum(trainRcTimes), 0) AS TRAIN_RC_TIMES_OVERVIEW" +
            " , coalesce(sum(trainOrderCount), 0) AS totalTrainOrderCount" +
            " from (SELECT %s \n"
            + "          , COUNT(DISTINCT CASE  WHEN is_refund = 'F' THEN order_id END) AS fltOrderCount\n" +
            "        , COUNT(DISTINCT CASE  WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END) AS fltRcTimes\n" +
            "        , 0 AS htlOrderCount, 0 AS htlRcTimes\n" +
            "        , 0 AS trainOrderCount , 0 AS trainRcTimes \n"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s  group by %s \n"
            + "         union all\n"
            + "        SELECT %s \n"
            + "          , 0 AS fltOrderCount , 0 AS fltRcTimes\n" +
            "        , COUNT(DISTINCT CASE  WHEN is_refund = 'F' THEN order_id END) AS htlOrderCount\n" +
            "        , COUNT(DISTINCT CASE  WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id END) AS htlRcTimes\n" +
            "        , 0 AS trainRcTimes , 0 AS trainOrderCount \n"
            + "        FROM olrpt_indexhoteldownload_all WHERE %s %s group by %s \n"
            + "         union all\n"
            + "        SELECT %s \n"
            + "          , 0 AS fltOrderCount , 0 AS fltRcTimes\n" +
            "        , 0 AS htlOrderCount , 0 AS htlRcTimes\n" +
            "        , COUNT(DISTINCT CASE  WHEN refund_status <> 'S'  OR quantity = 1 THEN order_id END) AS trainOrderCount\n" +
            "        , COUNT(DISTINCT CASE  WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id END) AS trainRcTimes \n"
            + "        FROM olrpt_indextraindownload_all WHERE %s AND order_status  in ('TA','RP','EP','EA') group by %s) group by %s ) " +
            "          where FLT_RC_TIMES_OVERVIEW > 0 OR HTL_RC_TIMES_OVERVIEW > 0 OR TRAIN_RC_TIMES_OVERVIEW > 0\n"
            + "          ";

    private static final String OVERVIEW_RC_TREND_SQL =
            "SELECT date \n"
                    + ", coalesce(SUM(fltRcTimes), 0) + coalesce(SUM(htlRcTimes), 0) + coalesce(SUM(trainRcTimes), 0) AS rcTimes \n"
                    + ", coalesce(SUM(fltOrderCount), 0) + coalesce(SUM(htlOrderCount), 0) + coalesce(SUM(trainOrderCount), 0) AS orderCount \n"
                    + "FROM ( SELECT %s \n"
                    + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS fltOrderCount \n"
                    + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS fltRcTimes \n"
                    + "        , 0 AS htlOrderCount, 0 AS htlRcTimes\n"
                    + "        , 0 AS trainOrderCount , 0 AS trainRcTimes \n"
                    + "    FROM olrpt_indexflightdownload_all  WHERE %s %s group by %s  \n"
                    + "    union all "
                    + "    SELECT %s  \n"
                    + "        , 0 AS fltOrderCount, 0 AS fltRcTimes\n"
                    + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS htlOrderCount \n"
                    + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS htlRcTimes \n"
                    + "        , 0 AS trainOrderCount, 0 AS trainRcTimes\n"
                    + "        FROM olrpt_indexhoteldownload_all WHERE %s %s group by %s \n"
                    + "    union all \n "
                    + "    SELECT %s \n"
                    + "        , 0 AS fltOrderCount, 0 AS fltRcTimes\n"
                    + "        , 0 AS htlOrderCount, 0 AS htlRcTimes\n"
                    + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS trainOrderCount \n"
                    + "        , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS trainRcTimes \n"
                    + "        FROM olrpt_indextraindownload_all   WHERE %s AND order_status  in ('TA','RP','EP','EA') group by %s ) group by date";

    @Override
    public String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum,
                           BaseQueryConditionDTO baseQueryConditionDto, Pager pager, String orderType, String user) {
        StringBuffer baseSql = new StringBuffer();
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String partitionFlight = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        String partitionHotel = queryPartition(OLRPT_INDEXHOTELDOWNLOAD);
        String partitionTrain = queryPartition(OLRPT_INDEXTRAINDOWNLOAD);

        AbstractOnlineReportTopRcAnaylsisDao.JoinCondition biz = joinCondition(analysisObjectEnum);
        baseSql.append(String.format(OVERVIEW_RC_SQL, biz.getFullJoinResultFields(), biz.getGroupFields(),
                BaseConditionPrebuilder.buildFlightSaveAmount(parmList, startTime, endTime),
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partitionFlight) + buildPreSqlUser(user, parmList),
                getFlightClassCondition(orderType), biz.getGroupFields(), biz.getGroupFields(),
                BaseConditionPrebuilder.buildHotelSaveAmount(parmList, startTime, endTime),
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partitionHotel) + buildPreSqlUser(user, parmList),
                getHotelOrderTypeCondition(orderType), biz.getGroupFields(), biz.getFlightFullJoinHotel(),
                biz.getGroupFields(),
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partitionTrain) + buildPreSqlUser(user, parmList),
                biz.getGroupFields(), biz.getHotelFullJointrain()));
        baseSql.append(" limit ?, ? ");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        return baseSql.toString();
    }

    @Override
    protected ClickHouseTable getTargetTable() {
        return ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
    }

    @Override
    public String countSql(List<Object> parmList, ClickHouseTable clickHouseTable,
                           AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, String orderType, String user) {
        StringBuffer baseSql = new StringBuffer();
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String partion = queryPartition(clickHouseTable);
        AbstractOnlineReportTopRcAnaylsisDao.JoinCondition biz = joinCondition(analysisObjectEnum);
        baseSql.append(String.format(OVERVIEW_RC_COUNT_SQL
                , biz.getGroupFields()
                , biz.getGroupFields(), biz.getGroupFields(), BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion) + buildPreSqlUser(user, parmList)
                , getFlightClassCondition(orderType), biz.getGroupFields()
                , biz.getGroupFields(), BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion) + buildPreSqlUser(user, parmList)
                , getHotelOrderTypeCondition(orderType), biz.getGroupFields()
                , biz.getGroupFields(), BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion) + buildPreSqlUser(user, parmList), biz.getGroupFields()
                , biz.getGroupFields()));
        return baseSql.toString();
    }

    @Override
    protected String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return null;
    }

    /**
     * rc概览
     *
     * @param requestDto
     * @param orderType
     * @return
     * @throws Exception
     */
    
    public List<RcTimesDTO> aggreationRcView(BaseQueryConditionDTO requestDto, String orderType)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append("select ");
        sqlBuilder.append(
                " coalesce(flight.rcTimes, 0) + coalesce(hotel.rcTimes, 0) + coalesce(train.rcTimes, 0) AS rcTimes\n"
                        + "    , coalesce(flight.noRcTimes, 0) + coalesce(hotel.noRcTimes, 0) + coalesce(train.noRcTimes, 0) AS noRcTimes"
                        + ", coalesce(flight.orderCount, 0) + coalesce(hotel.orderCount, 0) + coalesce(train.orderCount, 0) AS orderCount ");
        sqlBuilder.append(", round(coalesce(flight.lossPrice, 0) + coalesce(hotel.lossPrice, 0), 2) as lossPrice \n");

        sqlBuilder.append(" from  ");
        sqlBuilder.append("(SELECT COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes \n"
                + "        , COUNT(DISTINCT CASE WHEN is_rc != 'T' AND is_refund = 'F' THEN order_id  END) AS noRcTimes \n");
        sqlBuilder.append(", round(sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end), 2) as lossPrice \n");
        sqlBuilder.append(" FROM olrpt_indexflightdownload_all  ");
        sqlBuilder.append(OrpConstants.WHERE);
        String partion = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partion));
        sqlBuilder.append(getFlightClassCondition(orderType));
        sqlBuilder.append(") flight ");
        sqlBuilder.append("CROSS JOIN");
        sqlBuilder.append("(SELECT COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + "            , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes \n"
                + "            , COUNT(DISTINCT CASE WHEN is_rc != 'T' AND is_refund = 'F' THEN order_id  END) AS noRcTimes \n");
        sqlBuilder.append(", round(SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end),2) as lossPrice \n");
        sqlBuilder.append(" FROM olrpt_indexhoteldownload_all  ");
        sqlBuilder.append(OrpConstants.WHERE);
        partion = queryPartition(OLRPT_INDEXHOTELDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partion));
        sqlBuilder.append(getHotelOrderTypeCondition(orderType));
        sqlBuilder.append(") hotel ");
        sqlBuilder.append("CROSS JOIN");
        sqlBuilder.append("( SELECT COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
                + "            , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes\n"
                + "            , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc != 'T' THEN order_id  END) AS noRcTimes  \n");
        sqlBuilder.append(" FROM olrpt_indextraindownload_all ");
        sqlBuilder.append(OrpConstants.WHERE);
        partion = queryPartition(OLRPT_INDEXTRAINDOWNLOAD);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partion));
        sqlBuilder.append("AND order_status  in ('TA','RP','EP','EA') ");
        sqlBuilder.append(") train ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTimesDTO.class, "aggreationRcView");
    }

    /**
     * rc概览
     *
     * @param startTime
     * @param endTime
     * @param orderType
     * @param industryList
     * @param dataTypeEnum
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @return
     * @throws Exception
     */
    
    public List<RcTimesDTO> aggreationRcViewCorpAndIndustry(String startTime, String endTime, String orderType,
                                                            List<String> industryList, DataTypeEnum dataTypeEnum,
                                                            String compareSameLevel, String consumptionLevel, String compareCorpSameLevel)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
//        String partion = queryPartition(getTargetTable());
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append("select ");
        sqlBuilder.append(
                " coalesce(flight.rcTimes, 0) + coalesce(hotel.rcTimes, 0) + coalesce(train.rcTimes, 0) AS rcTimes\n"
                        + "    , coalesce(flight.noRcTimes, 0) + coalesce(hotel.noRcTimes, 0) + coalesce(train.noRcTimes, 0) AS noRcTimes"
                        + ", coalesce(flight.orderCount, 0) + coalesce(hotel.orderCount, 0) + coalesce(train.orderCount, 0) AS orderCount ");
        sqlBuilder.append(", round(coalesce(flight.lossPrice, 0) + coalesce(hotel.lossPrice, 0), 2) as lossPrice \n");

        sqlBuilder.append(" from  ");
        sqlBuilder.append("(SELECT COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes \n"
                + "        , COUNT(DISTINCT CASE WHEN is_rc != 'T' AND is_refund = 'F' THEN order_id  END) AS noRcTimes \n");
        sqlBuilder.append(", round(sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end), 2) as lossPrice \n");
        sqlBuilder.append(" FROM olrpt_indexflightdownload_all  ");
        sqlBuilder.append(OrpConstants.WHERE);
        String partion = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList, partion));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList, partion));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        sqlBuilder.append(getFlightClassCondition(orderType));
        sqlBuilder.append(") flight ");
        sqlBuilder.append("CROSS JOIN");
        sqlBuilder.append("(SELECT COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + "            , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes \n"
                + "            , COUNT(DISTINCT CASE WHEN is_rc != 'T' AND is_refund = 'F' THEN order_id  END) AS noRcTimes \n");
        sqlBuilder.append(", round(SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end),2) as lossPrice \n");
        sqlBuilder.append(" FROM olrpt_indexhoteldownload_all  ");
        sqlBuilder.append(OrpConstants.WHERE);
        partion = queryPartition(OLRPT_INDEXHOTELDOWNLOAD);
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList, partion));
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList, partion));
        }
        sqlBuilder.append(getHotelOrderTypeCondition(orderType));
        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(") hotel ");
        sqlBuilder.append("CROSS JOIN");
        sqlBuilder.append("( SELECT COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
                + "            , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes\n"
                + "            , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc != 'T' THEN order_id  END) AS noRcTimes  \n");
        sqlBuilder.append(" FROM olrpt_indextraindownload_all ");
        sqlBuilder.append(OrpConstants.WHERE);
        partion = queryPartition(OLRPT_INDEXTRAINDOWNLOAD);
        boolean ignoreTenantId = false;
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList, partion));
            ignoreTenantId = true;
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList, partion));
            ignoreTenantId = true;
        }
        sqlBuilder.append("AND order_status  in ('TA','RP','EP','EA') ");
        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
        }
        sqlBuilder.append(") train ");

        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTimesDTO.class, "aggreationRcViewCorpAndIndustry", ignoreTenantId);
    }

    /**
     * rc原因
     *
     * @param requestDto
     * @param clazz
     * @param orderType
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationRcViewBu(BaseQueryConditionDTO requestDto, Class<T> clazz, String orderType)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();

        String flightPartition = queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD);
        String hotelPartition = queryPartition(OLRPT_INDEXHOTELDOWNLOAD);
        String trainPartition = queryPartition(OLRPT_INDEXTRAINDOWNLOAD);

        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append("select ");
        sqlBuilder.append(" coalesce(flight.rcTimes, 0) AS flightRcTimes , coalesce(hotel.rcTimes, 0) AS hotelRcTimes\n"
                + "    , coalesce(train.rcTimes, 0) AS trainRcTimes ");
        sqlBuilder.append(" from  ");
        sqlBuilder.append("(SELECT COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes \n"
                + "        , COUNT(DISTINCT CASE WHEN is_rc != 'T' AND is_refund = 'F' THEN order_id  END) AS noRcTimes \n"
                + "          FROM olrpt_indexflightdownload_all");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList, flightPartition));
        sqlBuilder.append(getFlightClassCondition(orderType));
        sqlBuilder.append(") flight ");
        sqlBuilder.append("CROSS JOIN");
        sqlBuilder.append("(SELECT COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + "            , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes \n"
                + "            , COUNT(DISTINCT CASE WHEN is_rc != 'T' AND is_refund = 'F' THEN order_id  END) AS noRcTimes \n"
                + "              FROM olrpt_indexhoteldownload_all");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList, hotelPartition));
        sqlBuilder.append(getHotelOrderTypeCondition(orderType));
        sqlBuilder.append(") hotel ");
        sqlBuilder.append("CROSS JOIN");
        sqlBuilder.append("( SELECT COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) THEN order_id  END) AS orderCount \n"
                + "            , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc = 'T' THEN order_id  END) AS rcTimes \n"
                + "            , COUNT(DISTINCT CASE WHEN (refund_status <> 'S' OR quantity = 1) AND is_rc != 'T' THEN order_id  END) AS noRcTimes \n"
                + "              FROM olrpt_indextraindownload_all ");
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList, trainPartition));
        sqlBuilder.append(" AND order_status  in ('TA','RP','EP','EA') ");
        sqlBuilder.append(") train ");
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationRcViewBu");
    }

    /**
     * rc趋势
     *
     * @param requestDto
     * @param orderType
     * @param dateDimensionEnum
     * @return
     * @throws Exception
     */
    public List<RcTrendDTO> aggreationRcTrend(BaseQueryConditionDTO requestDto, String orderType, QueryReportAggDateDimensionEnum dateDimensionEnum) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String partion = queryPartition(getTargetTable());
        List<Object> parmList = new ArrayList<>();
        Map map = getGroupFieldBy(dateDimensionEnum);
        String sql = (String) map.get(TREND_DIM_KEY);
        String groupField = (String) map.get(TREND_GROUP_KEY);
        sqlBuilder.append(String.format(OVERVIEW_RC_TREND_SQL, sql,
                BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partion), getFlightClassCondition(orderType),
                groupField, sql, BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partion),
                getHotelOrderTypeCondition(orderType), groupField, sql,
                BaseConditionPrebuilder.buildPreSql(requestDto, parmList, partion), groupField));
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTrendDTO.class, "aggreationRcTrend");
    }

    /**
     * rc趋势
     *
     * @param startTime
     * @param endTime
     * @param orderType
     * @param dateDimensionEnum
     * @param industryList
     * @param dataTypeEnum
     * @param compareSameLevel  是否对比同级比较
     * @param consumptionLevel  消费等级
     * @return
     * @throws Exception
     */
    
    public List<RcTrendDTO> aggreationRcTrendCorpAndIndustry(String startTime, String endTime, String orderType, QueryReportAggDateDimensionEnum dateDimensionEnum,
                                                             List<String> industryList, DataTypeEnum dataTypeEnum,
                                                             String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String partion = queryPartition(getTargetTable());
        List<Object> parmList = new ArrayList<>();
        Map map = getGroupFieldBy(dateDimensionEnum);
        String sql = (String) map.get(TREND_DIM_KEY);
        String groupField = (String) map.get(TREND_GROUP_KEY);
        String compareSameLevelSql = "";
        if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
            compareSameLevelSql = " and consumptionlevel = '" + consumptionLevel + "' ";
        }
        boolean ignoreTenantId = false;
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(String.format(OVERVIEW_RC_TREND_SQL, sql,
                    BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList, partion) + compareSameLevelSql,
                    getFlightClassCondition(orderType), groupField, sql,
                    BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList, partion) + compareSameLevelSql,
                    getHotelOrderTypeCondition(orderType), groupField, sql,
                    BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList, partion) + compareSameLevelSql, groupField));
            ignoreTenantId = true;
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(String.format(OVERVIEW_RC_TREND_SQL, sql,
                    BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList, partion) + compareSameLevelSql,
                    getFlightClassCondition(orderType), groupField, sql,
                    BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList, partion) + compareSameLevelSql,
                    getHotelOrderTypeCondition(orderType), groupField, sql,
                    BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList, partion) + compareSameLevelSql, groupField));
            ignoreTenantId = true;
        }
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTrendDTO.class, "aggreationRcTrendCorpAndIndustry", ignoreTenantId);
    }

}
