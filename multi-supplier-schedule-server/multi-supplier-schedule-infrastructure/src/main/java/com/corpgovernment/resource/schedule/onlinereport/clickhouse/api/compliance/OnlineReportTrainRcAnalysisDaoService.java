package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTimesDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.TrainRcEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @className OnlineReportTrainRcAnalysisDaoService
 * @date 2024/9/29
 */
public interface OnlineReportTrainRcAnalysisDaoService extends OnlineReportTopRcAnalysisDaoService {

    List<RcTimesDTO> aggreationRcView(BaseQueryConditionDTO requestDto) throws Exception;

    List<RcTimesDTO> aggreationRcViewCorpAndIndustry(String startTime, String endTime, List<String> industryList, DataTypeEnum dataTypeEnum,
                                                     String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

    <T> List<T> aggreationRcViewReason(BaseQueryConditionDTO requestDto, Class<T> clazz) throws Exception;

    <T> List<T> aggreationRcViewReasonDetail(BaseQueryConditionDTO requestDto, Class<T> clazz,
                                             TrainRcEnum trainRcEnum) throws Exception;

    List<RcTrendDTO> aggreationRcTrend(BaseQueryConditionDTO requestDto, QueryReportAggDateDimensionEnum dateDimensionEnum) throws Exception;

    List<RcTrendDTO> aggreationRcTrendCorpAndIndustry(String startTime, String endTime, QueryReportAggDateDimensionEnum dateDimensionEnum,
                                                      List<String> industryList, DataTypeEnum dataTypeEnum,
                                                      String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception;

}
