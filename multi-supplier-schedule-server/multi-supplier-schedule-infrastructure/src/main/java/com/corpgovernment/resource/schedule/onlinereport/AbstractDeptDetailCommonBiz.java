package com.corpgovernment.resource.schedule.onlinereport;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValDataType;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-21 11:23
 * @desc 机票碳排放
 */
public abstract class AbstractDeptDetailCommonBiz {

    /**
     * 获得维度标题
     *
     * @param analysisObjectEnum
     * @param lang
     * @return
     */
    public List<HeaderKeyValDataType> getDimesionsDesc(AnalysisObjectEnum analysisObjectEnum, String lang) {
        List<HeaderKeyValDataType> result = new ArrayList<>();
        switch (analysisObjectEnum) {
            case CORP:
                result.add(getHeaderKeyValMap(AnalysisObjectEnum.CORP.toString(), "Index.public", lang));
                break;
            case ACCOUNT:
                result = defaultHeader(lang);
                result.add(getHeaderKeyValMap(AnalysisObjectEnum.ACCOUNT.toString(), "DeptAnalysis.AccountName", lang));
                break;
            case ACCOUNTCODE:
                result = defaultHeader(lang);
                result.add(getHeaderKeyValMap(AnalysisObjectEnum.ACCOUNTCODE.toString(), "DeptAnalysis.AccountCode", lang));
                break;
            case DEPT1:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.depone", lang));
                break;
            case DEPT2:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.deptwo", lang));
                break;
            case DEPT3:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.depthree", lang));
                break;
            case DEPT4:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.depfour", lang));
                break;
            case DEPT5:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.depfive", lang));
                break;
            case DEPT6:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.depsix", lang));
                break;
            case DEPT7:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.depseven", lang));
                break;
            case DEPT8:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.depeight", lang));
                break;
            case DEPT9:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.depnight", lang));
                break;
            case DEPT10:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.depten", lang));
                break;
            case COSTCENTER1:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.costcenterone", lang));
                break;
            case COSTCENTER2:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.costcentertwo", lang));
                break;
            case COSTCENTER3:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.costcenterthree", lang));
                break;
            case COSTCENTER4:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.costcenterfour", lang));
                break;
            case COSTCENTER5:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.costcenterfive", lang));
                break;
            case COSTCENTER6:
                result.addAll(defaultHeader(lang));
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.costcentersix", lang));
                break;
            case UID:
                result.add(getHeaderKeyValMap(analysisObjectEnum.toString(), "Exceltopname.uidnumber", lang));
                result.add(getHeaderKeyValMap("NAME", "Index.idholder", lang));
                break;
            default:
                break;
        }
        return result;
    }

    public List<HeaderKeyValDataType> getDimesionsDescV2(AnalysisObjectEnum analysisObjectEnum, String lang) {
        List<HeaderKeyValDataType> result = new ArrayList<>(getDimesionsDesc(analysisObjectEnum, lang));
        switch (analysisObjectEnum) {
            case UID:
                result.add(getHeaderKeyValMap("EID", "Exceltopname.cardholdernumber", lang));
                break;
            default:
                break;
        }
        return result;
    }

    public List defaultHeader(String lang) {
        List result = new ArrayList<>();
        result.add(getHeaderKeyValMap(AnalysisObjectEnum.CORP.toString(), "Index.public", lang));
        return result;
    }

    public HeaderKeyValDataType getHeaderKeyValMap(String key, String sharkKey, String lang) {
        HeaderKeyValDataType headerKeyValMap = new HeaderKeyValDataType();
        headerKeyValMap.setHeaderKey(key);
        headerKeyValMap.setHeaderValue(SharkUtils.get(sharkKey, lang));
        headerKeyValMap.setDataType(0);
        return headerKeyValMap;
    }
}
