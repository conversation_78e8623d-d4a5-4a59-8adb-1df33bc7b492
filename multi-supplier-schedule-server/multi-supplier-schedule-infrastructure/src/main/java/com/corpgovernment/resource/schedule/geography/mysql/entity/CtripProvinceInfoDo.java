package com.corpgovernment.resource.schedule.geography.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR> zhang
 * @date 2023/10/26 11:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CtripProvinceInfoDo{
    /**
     * 多供应商平台省份ID
     */
    @Id
    private Long id;
    private String provinceId;
    private String provinceName;
    private String provinceEnName;
    private String countryId;
    private String hashCode;
    /**
     * 创建时间
     */
    private Date datachangeCreatetime;
    /**
     * 修改时间
     */
    private Date datachangeLasttime;
    /**
     * 逻辑删除
     */
    private Byte isDeleted;

}
