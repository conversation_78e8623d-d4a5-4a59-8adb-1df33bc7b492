package com.corpgovernment.resource.schedule.onlinereport.clickhouse.travelmark;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
public class OnlineReportTravelMarkTrendDao extends AbstractCommonDao {

    private final static String TRAVEL_MARK_METRIC_MONTH_TREND = "SELECT if(company.dim != '', " +
            "company.dim, if(corp.dim != '', corp.dim, industry.dim)) AS dim\n" +
            "    , coalesce(companyFZ, 0) AS companyFZ, coalesce(companyFM, 0) AS companyFM\n" +
            "    , coalesce(corpFZ, 0) AS corpFZ, coalesce(corpFM, 0) AS corpFM\n" +
            "    , coalesce(industryFZ, 0) AS industryFZ, coalesce(industryFM, 0) AS industryFM\n" +
            "FROM (\n" +
            "    SELECT %s as dim, %s as companyFZ, %s as companyFM\n" +
            "    FROM %s\n" +
            "    WHERE %s %s GROUP BY dim ) company\n" +
            "    FULL JOIN (\n" +
            "        SELECT %s as dim, %s as corpFZ, %s as corpFM\n" +
            "        FROM %s\n" +
            "        WHERE %s %s GROUP BY dim ) corp\n" +
            "    ON company.dim = corp.dim\n" +
            "    FULL JOIN (\n" +
            "        SELECT %s as dim, %s as industryFZ, %s as industryFM\n" +
            "        FROM %s\n" +
            "        WHERE %s %s GROUP BY dim ) industry\n" +
            "    ON if(company.dim != '', company.dim, corp.dim) = industry.dim";


    private final static String TRAVEL_MARK_METRIC_MONTH = "SELECT coalesce(company.metric) as companyMetric , " +
            " coalesce(corp.metric) as corpMetric, coalesce(industry.metric) as industryMetric\n" +
            "          FROM (SELECT %s as metric FROM %s WHERE %s %s  ) company\n" +
            "    cross JOIN (SELECT %s as metric FROM %s WHERE %s %s  ) corp\n" +
            "    cross JOIN (SELECT %s as metric FROM %s WHERE %s %s  ) industry\n";

    /**
     * 差旅评分指标趋势
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> travelMarkMetricTrend(BaseQueryConditionDTO requestDto, List<String> industryList, Map extMap,
                                             Class<T> clazz, QueryReportAggDateDimensionEnum dateDimensionEnum) throws Exception {
        String dateField = getDateFieldByCaliber(requestDto.getStatisticalCaliber());
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        // 分子
        String fzSql = StringUtils.EMPTY;
        // 分母
        String fmSql = StringUtils.EMPTY;
        String ortherCondition = StringUtils.EMPTY;
        String metric = (String) extMap.get("metric");
        String dimesion = getGroupFieldBy(dateDimensionEnum);
        if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.AVG_PRICE_TREND.name())) {
            String classType = (String) extMap.get("classType");
            // 平均票价
            fzSql = "toFloat64(SUM(coalesce(price, 0)))";
            fmSql = "toFloat64(SUM(coalesce(quantity, 0)))";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType()) + getFlightClassTypeCondition(classType);
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.TPMS_AVG_PRICE_TREND.name())) {
            String classType = (String) extMap.get("classType");
            // 里程均价
            fzSql = "toFloat64(SUM(coalesce(price, 0)))";
            fmSql = "toFloat64(SUM(coalesce(tpms, 0)))";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType()) + getFlightClassTypeCondition(classType);
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.FULL_FARE_PERCENT_TREND.name())) {
            // 全价票张占比, 占比是百分数，需要乘100
            fzSql = "toFloat64(SUM(coalesce(fullfaretkt, 0))) * 100";
            fmSql = "toFloat64(SUM(coalesce(ordertkt, 0)))";
            ortherCondition = " and flight_class = 'N' " + getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.AVG_PRICE_RATE_TREND.name())) {
            // 机票国内经济舱平均折扣趋势
            fzSql = "toFloat64(SUM(coalesce(price_rate, 0) * coalesce(quantity, 0)))";
            fmSql = "toFloat64(SUM(coalesce(quantity, 0)))";
            ortherCondition = " and flight_class = 'N' AND class_type = 'Y'" + getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.AVG_PRE_ORDERDATE_TREND.name())) {
            // 机票国内经济舱平均提前预订天数趋势
            fzSql = "toFloat64(SUM(coalesce(pre_order_date, 0) * coalesce(quantity, 0)))";
            fmSql = "toFloat64(SUM(coalesce(quantity, 0)))";
            // 如果查国际的数据就不限制flight_class
            if (!StringUtils.equalsIgnoreCase(requestDto.getProductType(), "inter")) {
                // 国内
                ortherCondition = " and flight_class = 'N' AND class_type = 'Y'" + getFlightClassConditionWithAudited(requestDto.getProductType());
            } else {
                ortherCondition = " and class_type = 'Y'" + getFlightClassConditionWithAudited(requestDto.getProductType());
            }
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.AVG_NIGHT_PRICE_TREND.name())) {
            // 酒店间夜均价趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(SUM(coalesce(room_price, 0)))";
            fmSql = "toFloat64(SUM(quantity))";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.AVG_TICKET_PRICE_TREND.name())) {
            // 火车均价趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(SUM(coalesce(ticket_price,0)+coalesce(changebalance,0)+coalesce(refund_ticket_fee,0)))";
            fmSql = "toFloat64(SUM(quantity))";
            ortherCondition = getTrainOrderStatusCondition();
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.PRE_4_PERCENT_TREND.name())) {
            // 提前4天占比
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(SUM(if(pre_order_date > 4, coalesce(quantity, 0), 0)))";
            fmSql = "toFloat64(SUM(quantity))";
            ortherCondition = " AND class_type = 'Y' " + getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.FLT_RC_PERCENT_TREND.name())) {
            // 机票RC趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(count(distinct if(is_rc = 'T' and is_refund = 'F', order_id, null)))";
            fmSql = "toFloat64(count(distinct if(is_refund = 'F', order_id, null)))";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.FLT_LOW_RC_PERCENT_TREND.name())) {
            // 机票低价RC趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(count(distinct if(coalesce(low_rid, '') != '' and is_refund = 'F', order_id, null)))";
            fmSql = "toFloat64(count(distinct if(is_refund = 'F', order_id, null)))";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.FLT_REFUND_PERCENT_TREND.name())) {
            // 机票退票趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(sum(refundtkt))";
            fmSql = "toFloat64(sum(ordertkt))";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.FLT_REBOOK_PERCENT_TREND.name())) {
            // 机票改签趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(sum(if(is_rebook = 'T', coalesce(quantity, 0), 0)))";
            fmSql = "toFloat64(sum(ordertkt))";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.HTL_RC_PERCENT_TREND.name())) {
            // 酒店rc趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(count(distinct if(is_rc = 'T' and is_refund = 'F', order_id, null)))";
            fmSql = "toFloat64(count(distinct if(is_refund = 'F', order_id, null)))";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.HTL_LOW_RC_PERCENT_TREND.name())) {
            // 酒店低价rc趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(count(distinct if(coalesce(reason_code, '') != '' and is_refund = 'F', order_id, null)))";
            fmSql = "toFloat64(count(distinct if(is_refund = 'F', order_id, null)))";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.HTL_AGREEMENT_RC_PERCENT_TREND.name())) {
            // 酒店协议rc趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(count(distinct if(coalesce(agreement_rc, '') != '' and is_refund = 'F', order_id, null)))";
            fmSql = "toFloat64(count(distinct if(is_refund = 'F', order_id, null)))";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.HTL_SAVE_RATE_TREND.name())) {
            // 酒店节省率趋势
            String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
            String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "SUM(coalesce(save_amount_3c, 0)) + SUM(coalesce(save_amount_premium, 0)) + SUM(coalesce(save_amount_promotion, 0))";
            fmSql = "sum(case when (producttype_all ='" + ta + "' and save_amount_3c is not null) " +
                    "or (producttype_all ='" + premium + "' and save_amount_premium is not null) " +
                    "or save_amount_promotion != 0 then coalesce(corp_real_pay, 0) else 0 end) ";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.HTL_POTENTIAL_SAVE_RATE_TREND.name())) {
            // 酒店潜在节省率趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) + sum(coalesce(cancel_esti_save_amount,0))";
            fmSql = "sum(coalesce(real_pay,0))";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.TRAIN_REFUND_PERCENT_TREND.name())) {
            // 火车退票趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end))";
            fmSql = "toFloat64(sum(case when coalesce(quantity, 0) > 0 then coalesce(quantity, 0) else 0 end ))";
            ortherCondition = getTrainOrderStatusCondition();
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.TRAIN_REBOOK_PERCENT_TREND.name())) {
            // 火车改签趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;
            partition = queryPartition(clickHouseTable);
            fzSql = "toFloat64(SUM(coalesce(change_quantity, 0)))";
            fmSql = "toFloat64(sum(case when coalesce(quantity, 0) > 0 then coalesce(quantity, 0) else 0 end ))";
            ortherCondition = getTrainOrderStatusCondition();
        }

        String compareSameLevelSql = "";
        if (StringUtils.isNotEmpty(requestDto.getCompareSameLevel()) && StringUtils.equalsIgnoreCase(requestDto.getCompareSameLevel(), "T") &&
                StringUtils.isNotEmpty(requestDto.getConsumptionLevel())) {
            compareSameLevelSql = " and consumptionlevel = '" + requestDto.getConsumptionLevel() + "' ";
        }
        String compareCorpSameLevelSql = "";
        if (StringUtils.isNotEmpty(requestDto.getCompareCorpSameLevel()) && StringUtils.equalsIgnoreCase(requestDto.getCompareCorpSameLevel(), "T") &&
                StringUtils.isNotEmpty(requestDto.getConsumptionLevel())) {
            compareCorpSameLevelSql = " and consumptionlevel = '" + requestDto.getConsumptionLevel() + "' ";
        }

        List<Object> parmList = new ArrayList<>();
        String sql = String.format(TRAVEL_MARK_METRIC_MONTH_TREND
                , dimesion, fzSql, fmSql, clickHouseTable.getTable()
                , BaseConditionPrebuilder.buildTenantIdPreSql(requestDto, parmList, partition, dateField, TenantContext.getTenantId()), ortherCondition
                , dimesion, fzSql, fmSql, clickHouseTable.getTable()
                , BaseConditionPrebuilder.buildCorpPreSqlWithCol(requestDto, parmList, partition, dateField), ortherCondition + compareCorpSameLevelSql
                , dimesion, fzSql, fmSql, clickHouseTable.getTable()
                , BaseConditionPrebuilder.buildIndustryPreSqlWithCol(requestDto, industryList, partition, parmList, dateField),
                ortherCondition + compareSameLevelSql);
        return commonList(clazz, sql, parmList, true);
    }

    /**
     * 差旅评分指标趋势
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> travelMarkMetric(BaseQueryConditionDTO requestDto, List<String> industryList, Map extMap,
                                        Class<T> clazz) throws Exception {
        String dateField = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name()) ? ORDERDT : REPORT_DATE;
        ClickHouseTable clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        String partition = queryPartition(clickHouseTable);
        String avgPriceSql = StringUtils.EMPTY;
        String ortherCondition = StringUtils.EMPTY;
        String metric = (String) extMap.get("metric");
        if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.AVG_PRICE_TREND.name())) {
            String classType = (String) extMap.get("classType");
            // 平均票价
            avgPriceSql = "if(toFloat64(SUM(coalesce(quantity, 0))) = 0, 0, toFloat64(SUM(coalesce(price, 0))) / toFloat64(SUM(coalesce(quantity, 0))))";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType()) + getFlightClassTypeCondition(classType);
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.TPMS_AVG_PRICE_TREND.name())) {
            String classType = (String) extMap.get("classType");
            // 里程均价
            avgPriceSql = "if(toFloat64(SUM(coalesce(tpms, 0))) = 0, 0, toFloat64(SUM(coalesce(price, 0))) / toFloat64(SUM(coalesce(tpms, 0))))";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType()) + getFlightClassTypeCondition(classType);
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.FULL_FARE_PERCENT_TREND.name())) {
            // 全价票张占比
            avgPriceSql = "if(toFloat64(SUM(coalesce(ordertkt, 0))) = 0, 0, toFloat64(SUM(coalesce(fullfaretkt, 0))) / toFloat64(SUM(coalesce(ordertkt, 0))) * 100)";
            ortherCondition = " and flight_class = 'N' " + getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.AVG_PRICE_RATE_TREND.name())) {
            // 机票国内经济舱平均折扣趋势
            avgPriceSql = "if(toFloat64(SUM(coalesce(quantity, 0))) = 0,0,toFloat64(SUM(coalesce(price_rate, 0) * coalesce(quantity, 0)))/toFloat64(SUM(coalesce(quantity, 0))))";
            ortherCondition = " and flight_class = 'N' AND class_type = 'Y' " + getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.AVG_PRE_ORDERDATE_TREND.name())) {
            // 机票国内经济舱平均提前预订天数趋势
            avgPriceSql = "if(toFloat64(SUM(coalesce(quantity, 0))) = 0, 0, toFloat64(SUM(coalesce(pre_order_date, 0) * coalesce(quantity, 0))) " +
                    "/ toFloat64(SUM(coalesce(quantity, 0))))";
            // 如果查国际的数据就不限制flight_class
            if (!StringUtils.equalsIgnoreCase(requestDto.getProductType(), "inter")) {
                // 国内
                ortherCondition = " and flight_class = 'N' AND class_type = 'Y' " + getFlightClassConditionWithAudited(requestDto.getProductType());
            } else {
                ortherCondition = " and class_type = 'Y'" + getFlightClassConditionWithAudited(requestDto.getProductType());
            }
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.AVG_NIGHT_PRICE_TREND.name())) {
            // 酒店间夜均价趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            avgPriceSql = "if(toFloat64(SUM(quantity)) != 0, toFloat64(SUM(coalesce(room_price, 0))) / toFloat64(SUM(quantity)), 0)";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.AVG_TICKET_PRICE_TREND.name())) {
            // 火车均价趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;
            avgPriceSql = "if(toFloat64(SUM(quantity)) != 0, " +
                    "toFloat64(SUM(coalesce(ticket_price,0)+coalesce(changebalance,0)+coalesce(refund_ticket_fee,0))) / toFloat64(SUM(quantity)), 0)";
            ortherCondition = getTrainOrderStatusCondition();
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.PRE_4_PERCENT_TREND.name())) {
            // 提前4天占比
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
            avgPriceSql = "if(toFloat64(SUM(coalesce(quantity,0))) != 0, " +
                    "toFloat64(SUM(if(pre_order_date > 4, coalesce(quantity, 0), 0))) / toFloat64(SUM(quantity)), 0)";
            ortherCondition = " AND class_type = 'Y' " + getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.FLT_RC_PERCENT_TREND.name())) {
            // 机票RC趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
            avgPriceSql = "if(toFloat64(count(distinct if(is_refund = 'F', order_id, null))) != 0, " +
                    "toFloat64(count(distinct if(is_rc = 'T' AND is_refund = 'F', order_id, null))) / toFloat64(count(distinct if(is_refund = 'F', order_id, null))), 0)";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.FLT_LOW_RC_PERCENT_TREND.name())) {
            // 机票低价RC趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
            avgPriceSql = "if(toFloat64(count(distinct if(is_refund = 'F', order_id, null))) != 0, " +
                    "toFloat64(count(distinct if(coalesce(low_rid, '') != '' AND is_refund = 'F', order_id, null))) " +
                    "/ toFloat64(count(distinct if(is_refund = 'F', order_id, null))), 0)";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.FLT_REFUND_PERCENT_TREND.name())) {
            // 机票退票趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
            avgPriceSql = "if(toFloat64(sum(ordertkt)) != 0, " +
                    "toFloat64(sum(refundtkt)) / toFloat64(sum(ordertkt)), 0)";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.FLT_REBOOK_PERCENT_TREND.name())) {
            // 机票改签趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
            avgPriceSql = "if(toFloat64(sum(ordertkt)) != 0, " +
                    "toFloat64(sum(if(is_rebook = 'T', coalesce(quantity, 0), 0))) / toFloat64(sum(ordertkt)), 0)";
            ortherCondition = getFlightClassConditionWithAudited(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.HTL_RC_PERCENT_TREND.name())) {
            // 酒店rc趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            avgPriceSql = "if(toFloat64(count(distinct if(is_refund = 'F', order_id, null))) != 0, " +
                    "toFloat64(count(distinct if(is_rc = 'T' AND is_refund = 'F', order_id, null))) / toFloat64(count(distinct order_id)), 0)";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.HTL_LOW_RC_PERCENT_TREND.name())) {
            // 酒店低价rc趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            avgPriceSql = "if(toFloat64(count(distinct if(is_refund = 'F', order_id, null))) != 0, " +
                    " toFloat64(count(distinct if(coalesce(reason_code, '') != '' AND is_refund = 'F', order_id, null)))/toFloat64(count(distinct order_id)), 0)";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.HTL_AGREEMENT_RC_PERCENT_TREND.name())) {
            // 酒店协议rc趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            avgPriceSql = "if(toFloat64(count(distinct if(is_refund = 'F', order_id, null))) != 0, " +
                    "toFloat64(count(distinct if(coalesce(agreement_rc, '') != '' AND is_refund = 'F', order_id, null))) / toFloat64(count(distinct order_id)), 0)";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.HTL_SAVE_RATE_TREND.name())) {
            // 酒店节省率趋势
            String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
            String premium = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Premium");
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            avgPriceSql = "if(toFloat64(sum(case when (producttype_all ='" + ta + "' and save_amount_3c is not null) " +
                    "or (producttype_all ='" + premium + "' and save_amount_premium is not null) " +
                    "or save_amount_promotion != 0 then coalesce(corp_real_pay, 0) else 0 end)) != 0, " +
                    "toFloat64(SUM(coalesce(save_amount_3c, 0)) + SUM(coalesce(save_amount_premium, 0)) + SUM(coalesce(save_amount_promotion, 0))) / " +
                    "toFloat64(sum(case when (producttype_all ='" + ta + "' and save_amount_3c is not null) " +
                    "or (producttype_all ='" + premium + "' and save_amount_premium is not null) or " +
                    "save_amount_promotion != 0 then coalesce(corp_real_pay, 0) else 0 end)), 0)";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.HTL_POTENTIAL_SAVE_RATE_TREND.name())) {
            // 酒店潜在节省率趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
            avgPriceSql = "if(toFloat64(sum(coalesce(real_pay,0))) != 0, " +
                    "toFloat64(SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) + sum(coalesce(cancel_esti_save_amount,0))) " +
                    "/ toFloat64(sum(coalesce(real_pay,0))), 0)";
            ortherCondition = getHotelOrderTypeCondition(requestDto.getProductType());
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.TRAIN_REFUND_PERCENT_TREND.name())) {
            // 火车退票趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;
            avgPriceSql = "if(toFloat64(sum(case when coalesce(quantity, 0) > 0 then coalesce(quantity, 0) else 0 end )) != 0, " +
                    "toFloat64(sum(case when  quantity < 0 then abs(coalesce(quantity,0)) else 0 end)) " +
                    "/ toFloat64(sum(case when coalesce(quantity, 0) > 0 then coalesce(quantity, 0) else 0 end )), 0)";
            ortherCondition = getTrainOrderStatusCondition();
        } else if (StringUtils.equalsIgnoreCase(metric, MarkMetricEnum.TRAIN_REBOOK_PERCENT_TREND.name())) {
            // 火车改签趋势
            clickHouseTable = ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;
            avgPriceSql = "if(toFloat64(SUM(coalesce(change_quantity, 0))) != 0, " +
                    "toFloat64(sum(case when coalesce(quantity, 0) > 0 then coalesce(quantity, 0) else 0 end ))/ toFloat64(SUM(coalesce(change_quantity, 0))), 0)";
            ortherCondition = getTrainOrderStatusCondition();
        }
        String compareSameLevelSql = "";
        if (StringUtils.isNotEmpty(requestDto.getCompareSameLevel()) && StringUtils.equalsIgnoreCase(requestDto.getCompareSameLevel(), "T") &&
                StringUtils.isNotEmpty(requestDto.getConsumptionLevel())) {
            compareSameLevelSql = " and consumptionlevel = '" + requestDto.getConsumptionLevel() + "' ";
        }
        String compareCorpSameLevelSql = "";
        if (StringUtils.isNotEmpty(requestDto.getCompareCorpSameLevel()) && StringUtils.equalsIgnoreCase(requestDto.getCompareCorpSameLevel(), "T") &&
                StringUtils.isNotEmpty(requestDto.getConsumptionLevel())) {
            compareCorpSameLevelSql = " and consumptionlevel = '" + requestDto.getConsumptionLevel() + "' ";
        }
        List<Object> parmList = new ArrayList<>();
        String sql = String.format(TRAVEL_MARK_METRIC_MONTH
                , avgPriceSql, clickHouseTable.getTable(), BaseConditionPrebuilder.buildTenantIdPreSql(requestDto, parmList, partition, dateField, TenantContext.getTenantId()), ortherCondition
                , avgPriceSql, clickHouseTable.getTable(), BaseConditionPrebuilder.buildCorpPreSqlWithCol(requestDto, parmList, partition, dateField),
                ortherCondition + compareCorpSameLevelSql
                , avgPriceSql, clickHouseTable.getTable(), BaseConditionPrebuilder.buildIndustryPreSqlWithCol(requestDto, industryList, partition, parmList, dateField),
                ortherCondition + compareSameLevelSql);
        return commonList(clazz, sql, parmList, true);
    }


    /**
     * AVG_PRICE_TREND：机票均价，TPMS_AVG_PRICE_TREND：里程均价，
     * FULL_FARE_PERCENT_TREND：全价票张占比，AVG_PRICE_RATE_TREND：平均折扣，
     * AVG_PRE_ORDERDATE_TREND：平均提前预订天数,AVG_TICKET_PRICE_TREND:火车平均票价
     * PRE_4_PERCENT_TREND:提前4天趋势
     * FLT_RC_PERCENT_TREND:机票rc趋势
     * FLT_LOW_RC_PERCENT_TREND:机票低价rc趋势
     * FLT_REFUND_PERCENT_TREND:机票退票趋势
     * FLT_REBOOK_PERCENT_TREND:机票改签趋势
     * HTL_RC_PERCENT_TREND:酒店rc趋势
     * HTL_LOW_RC_PERCENT_TREND:酒店低价rc趋势
     * HTL_AGREEMENT_RC_PERCENT_TREND:酒店协议rc趋势
     * HTL_SAVE_RATE_TREND:酒店节省率趋势
     * HTL_POTENTIAL_SAVE_RATE_TREND:酒店潜在节省率趋势
     * TRAIN_REFUND_PERCENT_TREND:火车退票趋势,TRAIN_REBOOK_PERCENT_TREND:火车改签趋势
     */
    enum MarkMetricEnum {
        AVG_PRICE_TREND, TPMS_AVG_PRICE_TREND,
        FULL_FARE_PERCENT_TREND, AVG_PRICE_RATE_TREND,
        AVG_PRE_ORDERDATE_TREND, AVG_NIGHT_PRICE_TREND,
        AVG_TICKET_PRICE_TREND, PRE_4_PERCENT_TREND,
        FLT_RC_PERCENT_TREND, FLT_LOW_RC_PERCENT_TREND,
        FLT_REFUND_PERCENT_TREND, FLT_REBOOK_PERCENT_TREND,
        HTL_RC_PERCENT_TREND, HTL_LOW_RC_PERCENT_TREND,
        HTL_AGREEMENT_RC_PERCENT_TREND, HTL_SAVE_RATE_TREND,
        HTL_POTENTIAL_SAVE_RATE_TREND,
        TRAIN_REFUND_PERCENT_TREND, TRAIN_REBOOK_PERCENT_TREND,
    }

    protected String getFlightClassTypeCondition(String classType) {
        // 经济舱
        if (StringUtils.equalsIgnoreCase(classType, "Y")) {
            return " and class_type = 'Y'  ";
        }
        // 头等舱
        if (StringUtils.equalsIgnoreCase(classType, "F")) {
            return " and class_type = 'F'  ";
        }
        // 公务舱
        if (StringUtils.equalsIgnoreCase(classType, "C")) {
            return " and class_type = 'C'  ";
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获得趋势聚合字段
     *
     * @param dateDimensionEnum
     * @return
     */
    protected String getGroupFieldBy(QueryReportAggDateDimensionEnum dateDimensionEnum) {
        String groupField = StringUtils.EMPTY;
        switch (dateDimensionEnum) {
            case month:
                groupField = " firstday_of_month ";
                break;
            case quarter:
                groupField = " firstday_of_quarter ";
                break;
            case half:
                groupField = " firstday_of_halfyear ";
                break;
            default:
                break;
        }
        return groupField;
    }

}
