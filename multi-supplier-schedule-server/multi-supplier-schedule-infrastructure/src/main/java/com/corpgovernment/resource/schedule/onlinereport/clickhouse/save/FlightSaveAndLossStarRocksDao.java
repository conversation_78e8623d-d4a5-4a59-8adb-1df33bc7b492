package com.corpgovernment.resource.schedule.onlinereport.clickhouse.save;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.FlightSaveStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.save.FlightSaveDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/*
 * <AUTHOR>
 * @date 2022/6/20 13:08
 * @Desc
 */
@Service
@Slf4j
@Repository
public class FlightSaveAndLossStarRocksDao extends AbstractSaveAndLossDao {

    private static final String LOG_TITLE = "FlightSaveAndLossDao";
    private static final String LOG_TITLE_M1 = "queryFlightEmployeeByPage";
    private static final String LOG_TITLE_M2 = "queryFlightEmployeeCount";


    private static final String TOP_DEPT_POTENTIAL_SAVE_SQL = "SELECT %s \n" +
            "    , round(if(CAST(coalesce(a.overAmount, 0) AS DOUBLE)>0,CAST(coalesce(a.overAmount, 0) AS DOUBLE),0) " +
            "+ if(CAST(coalesce(a.refundloss, 0) AS DOUBLE)>0,CAST(coalesce(a.refundloss, 0) AS DOUBLE),0) " +
            "+ if(CAST(coalesce(a.rebookloss, 0) AS DOUBLE)>0,CAST(coalesce(a.rebookloss, 0) AS DOUBLE),0), 2) AS "
            + FlightSaveStatisticalsEnum.TOTAL_SAVE_POTENTIAL.toString() + "\n" +
            "    , round(case when CAST(if(CAST(coalesce(b.overAmount, 0) AS DOUBLE)>0,CAST(coalesce(b.overAmount, 0) AS DOUBLE),0)" +
            " + if(CAST(coalesce(b.refundloss, 0) AS DOUBLE)>0,CAST(coalesce(b.refundloss, 0) AS DOUBLE),0) " +
            "+ if(CAST(coalesce(b.rebookloss, 0) AS DOUBLE)>0,CAST(coalesce(b.rebookloss, 0) AS DOUBLE),0) AS DOUBLE) != 0 \n" +
            "      then (if(CAST(coalesce(a.overAmount, 0) AS DOUBLE)>0,CAST(coalesce(a.overAmount, 0) AS DOUBLE),0)" +
            " + if(CAST(coalesce(a.refundloss, 0) AS DOUBLE)>0,CAST(coalesce(a.refundloss, 0) AS DOUBLE),0)" +
            " + if(CAST(coalesce(a.rebookloss, 0) AS DOUBLE)>0,CAST(coalesce(a.rebookloss, 0) AS DOUBLE),0)) / " +
            "CAST(if(CAST(coalesce(b.overAmount, 0) AS DOUBLE)>0,CAST(coalesce(b.overAmount, 0) AS DOUBLE), 0) \n" +
            "    + if(CAST(coalesce(b.refundloss, 0) AS DOUBLE)>0,CAST(coalesce(b.refundloss, 0) AS DOUBLE),0)" +
            " + if(CAST(coalesce(b.rebookloss, 0) AS DOUBLE)>0,CAST(coalesce(b.rebookloss, 0) AS DOUBLE),0) AS DOUBLE) * 100 else 0 end, 2) AS "
            + FlightSaveStatisticalsEnum.TOTAL_SAVE_POTENTIAL_PERCENT.toString() + "\n" +
            "    , if(CAST(coalesce(a.overAmount, 0) AS DOUBLE)>0,CAST(coalesce(a.overAmount, 0) AS DOUBLE),0) as " + FlightSaveStatisticalsEnum.OVER_AMOUNT.toString() + "\n" +
            "    , a.corpPriceAdj " + FlightSaveStatisticalsEnum.CORP_PRICE_ADJ.toString() + "\n" +
            "    , round(case when CAST(coalesce(b.refundloss, 0) AS DOUBLE) > 0 and CAST(coalesce(a.refundloss, 0) AS DOUBLE) > 0 then CAST(coalesce(a.refundloss, 0) AS DOUBLE) " +
            "    / CAST(coalesce(b.refundloss, 0) AS DOUBLE) * 100 else 0 end, 2) AS "
            + FlightSaveStatisticalsEnum.REFUND_LOSS_PERCENT.toString() + "\n" +
            "    , if(CAST(coalesce(a.refundloss, 0) AS DOUBLE)>0,CAST(coalesce(a.refundloss, 0) AS DOUBLE),0) as " + FlightSaveStatisticalsEnum.REFUND_LOSS.toString() + "\n" +
            "    , round(case when CAST(coalesce(b.rebookloss, 0) AS DOUBLE) > 0 and CAST(coalesce(a.rebookloss, 0) AS DOUBLE) > 0 then CAST(coalesce(a.rebookloss, 0) AS DOUBLE) " +
            "    / CAST(coalesce(b.rebookloss, 0) AS DOUBLE) * 100 else 0 end, 2) AS "
            + FlightSaveStatisticalsEnum.REBOOK_LOSS_PERCENT.toString() + "\n" +
            "    , if(CAST(coalesce(a.rebookloss, 0) AS DOUBLE)>0,CAST(coalesce(a.rebookloss, 0) AS DOUBLE),0) as " + FlightSaveStatisticalsEnum.REBOOK_LOSS.toString() + "\n" +
            "FROM (\n" +
            "    SELECT %s \n" +
            "        , SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as overAmount\n" +
            "        , SUM(CASE WHEN low_rid <> '' AND low_rid IS NOT NULL AND abs(coalesce(netfare, 0)) > abs(coalesce(corp_price_adj, 0)) AND is_refund = 'F' " +
            "                   THEN coalesce(corp_price_adj, 0) * quantity ELSE 0 END) AS corpPriceAdj\n" +

            "        , COUNT(DISTINCT CASE  WHEN is_refund = 'F' AND low_rid IS NOT NULL  AND low_rid != '' THEN order_id  END) AS rcTimes\n" +
            "        , SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0))+SUM(coalesce(refund_behind_service_fee, 0)) AS refundloss\n" +
            "        , SUM(CASE WHEN is_refund = 'T' THEN coalesce(refundtkt, 0) ELSE 0 END) AS refundtkt\n" +
            "        , SUM(coalesce(change_fee,0)) + SUM(coalesce(rebook_price_differential,0)) + SUM(coalesce(rebook_service_fee,0)) \n" +
            "        + SUM(if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0))) " +
            "        + SUM(coalesce(rebook_behind_service_fee,0)) AS rebookloss\n" +
            "        , SUM(CASE WHEN is_rebook = 'T' THEN coalesce(quantity, 0) ELSE 0 END) AS rebooktkt\n" +
            "    FROM olrpt_indexflightdownload_all\n" +
            "    WHERE %s \n" +
            "        AND (coalesce(over_std_esti_save_amount,0) != 0 \n" +
            "            OR (coalesce(refund_fee,0) + coalesce(refund_service_fee,0) + coalesce(refund_behind_service_fee, 0)) != 0 \n" +
            "            OR (coalesce(change_fee,0) + coalesce(rebook_price_differential,0) + coalesce(rebook_service_fee,0) \n" +
            "            + if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0)) + coalesce(rebook_behind_service_fee,0)) != 0)\n" +
            "    GROUP BY %s \n" +
            ") a\n" +
            "    CROSS JOIN ( \n" +
            "    SELECT\n" +
            "          SUM(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as overAmount\n" +
            "        , COUNT(DISTINCT CASE  WHEN is_refund = 'F' AND low_rid IS NOT NULL  AND low_rid != '' THEN order_id  END) AS rcTimes\n" +
            "        , SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0))+SUM(coalesce(refund_behind_service_fee, 0)) AS refundloss\n" +
            "        , SUM(CASE WHEN is_refund = 'T' THEN coalesce(refundtkt, 0) ELSE 0 END) AS refundtkt\n" +
            "        , SUM(coalesce(change_fee,0))+SUM(coalesce(rebook_price_differential,0)) + SUM(coalesce(rebook_service_fee,0)) \n" +
            "        + SUM(if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0))) " +
            "        + SUM(coalesce(rebook_behind_service_fee,0)) AS rebookloss\n" +
            "        , SUM(CASE WHEN is_rebook = 'T' THEN coalesce(quantity, 0) ELSE 0 END) AS rebooktkt\n" +
            "        FROM olrpt_indexflightdownload_all\n" +
            "        WHERE %s \n" +
            "        AND (coalesce(over_std_esti_save_amount,0) != 0 \n" +
            "            OR (coalesce(refund_fee,0) + coalesce(refund_service_fee,0) + coalesce(refund_behind_service_fee, 0)) != 0 \n" +
            "            OR (coalesce(change_fee,0) + coalesce(rebook_price_differential,0) + coalesce(rebook_service_fee,0) \n" +
            "            + if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0)) + coalesce(rebook_behind_service_fee,0)) != 0)\n" +
            "    ) b\n" +
            "    order by " + FlightSaveStatisticalsEnum.TOTAL_SAVE_POTENTIAL.toString() + " desc,%s";

    private static final String TOP_DEPT_POTENTIAL_SAVE_SQL_COUNT = "SELECT COUNT(1) AS countAll\n" +
            "FROM (SELECT 1 FROM olrpt_indexflightdownload_all\n" +
            "    WHERE %s \n" +
            "        AND (coalesce(over_std_esti_save_amount,0) != 0 \n" +
            "            OR (coalesce(refund_fee,0) + coalesce(refund_service_fee,0) + coalesce(refund_behind_service_fee, 0)) != 0 \n" +
            "            OR (coalesce(change_fee,0) + coalesce(rebook_price_differential,0) + coalesce(rebook_service_fee,0) \n" +
            "            + if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0)) + coalesce(rebook_behind_service_fee,0)) != 0) group by %s) t";

    /**
     * 机票节省
     *
     * @param startTime
     * @param endTime
     * @param statisticalCaliber
     * @param industryList
     * @param dataTypeEnum
     * @param productType
     * @param compareSameLevel   是否对比同级比较
     * @param consumptionLevel   消费等级
     * @return
     * @throws Exception
     */
    
    public List<FlightSaveDTO> agreementSaveCorpAndIndustry(String startTime, String endTime, String statisticalCaliber, List<String> industryList, DataTypeEnum dataTypeEnum,
                                                            String productType, String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 三方节省金额
        sqlBuilder.append(" sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_3c, 0) else 0 end) as saveAmount3c \n");
        // 两方节省金额
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) as saveAmountPremium \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as controlSave \n");
        // 三方成交净价（不含改签差价，不含服务费）
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_3c, 0) else 0 end) as netfare3c \n");
        // 两方成交净价（不含改签差价，不含服务费）
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_premium, 0) else 0 end) as netfarePremium \n");
        // 管控成交净价（不含改签差价，不含服务费）
        sqlBuilder.append(", sum(case when coalesce(saving_price, 0) != 0 then coalesce(netfare, 0) else 0 end) as netfareControl \n");
        // 三方票张
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(quantity_3c, 0) else 0 end)  as qty3c \n");
        // 两方票张
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(quantity_premium, 0) else 0 end)  as qtyPremium \n");
        // 管控票张
        sqlBuilder.append(", sum(case when coalesce(saving_price, 0) != 0 then coalesce(quantity, 0) else 0 end)  as qtyControl \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.PRE_WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        boolean ignoreTenantId = false;
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            if (isBookCaliber) {
                sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSqlWithCol(startTime, endTime, industryList, parmList, ORDERDT));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList));
            }
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            if (isBookCaliber) {
                sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSqlWithCol(startTime, endTime, parmList, ORDERDT));
            } else {
                sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList));
            }
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        }
        sqlBuilder.append(getFlightClassCondition(productType));
        sqlBuilder.append(" and audited <> 'F' ");
        // 查询clickhouse
        return commonList(FlightSaveDTO.class, sqlBuilder.toString(), parmList, ignoreTenantId);
    }

    /**
     * 机票节省
     *
     * @param requestDto
     * @param productType
     * @return
     * @throws Exception
     */
    
    public List<FlightSaveDTO> agreementSave(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 三方节省金额
        sqlBuilder.append(" sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_3c, 0) else 0 end) as saveAmount3c \n");
        // 两方节省金额
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) as saveAmountPremium \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as controlSave \n");
        // 三方成交净价（不含改签差价，不含服务费）
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_3c, 0) else 0 end) as netfare3c \n");
        // 两方成交净价（不含改签差价，不含服务费）
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(netfare_premium, 0) else 0 end) as netfarePremium \n");
        // 管控成交净价（不含改签差价，不含服务费）
        sqlBuilder.append(", sum(case when coalesce(saving_price, 0) != 0 then coalesce(netfare, 0) else 0 end) as netfareControl \n");
        // 三方票张
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(quantity_3c, 0) else 0 end)  as qty3c \n");
        // 两方票张
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(quantity_premium, 0) else 0 end)  as qtyPremium \n");
        // 管控票张
        sqlBuilder.append(", sum(case when coalesce(saving_price, 0) != 0 then coalesce(quantity, 0) else 0 end)  as qtyControl \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.PRE_WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassCondition(productType));
        sqlBuilder.append(" and audited <> 'F' ");
        // 查询clickhouse
        return commonList(FlightSaveDTO.class, sqlBuilder.toString(), parmList);
    }


    /**
     * 机票累计节省(从有消费记录开始)
     *
     * @param requestDto
     * @param productType
     * @return
     * @throws Exception
     */
    
    public List<FlightSaveDTO> agreementSaveTotal(BaseQueryConditionDTO requestDto, String productType) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 最早消费时间
        if (isBookCaliber) {
            sqlBuilder.append(String.format(" min(%s) as report_date \n", ORDERDT));
        } else {
            sqlBuilder.append(" min(report_date) as report_date \n");
        }
        // 三方节省金额
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_3c, 0) else 0 end) as saveAmount3c \n");
        // 两方节省金额
        sqlBuilder.append(", sum(case when class_type = 'Y' and flight_class = 'N' then coalesce(save_amount_premium, 0) else 0 end) as saveAmountPremium \n");
        // 管控节省金额
        sqlBuilder.append(", SUM(coalesce(saving_price, 0)) as controlSave \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.PRE_WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList));
        sqlBuilder.append(getFlightSaveCondition());
        sqlBuilder.append(getFlightClassCondition(productType));
        sqlBuilder.append(" and audited <> 'F' ");

        log.info("the sql is: " + sqlBuilder.toString());
        // 查询clickhouse
        return commonList(FlightSaveDTO.class, sqlBuilder.toString(), parmList);
    }

    /**
     * 机票潜在节省
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> agreementPotentialSave(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 超标损失
        sqlBuilder.append("sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as overAmount \n");
        // 超标次数
        sqlBuilder.append(", COUNT(DISTINCT CASE WHEN is_refund = 'F' AND low_rid IS NOT NULL AND low_rid != '' THEN order_id END) AS rcTimes \n");
        // 退票损失
        sqlBuilder.append(", SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0)) + SUM(coalesce(refund_behind_service_fee, 0)) " +
                "as refundloss \n");
        // 退票张数
        sqlBuilder.append(", sum(case when is_refund = 'T' then coalesce(refundtkt, 0) else 0 end ) as refundtkt \n");
        // 改签损失
        sqlBuilder.append(", SUM(coalesce(change_fee,0)) + SUM(coalesce(rebook_price_differential,0)) " +
                "+ SUM(if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0))) " +
                "+ SUM(coalesce(rebook_service_fee,0)) + SUM(coalesce(rebook_behind_service_fee,0)) as rebookloss \n");
        // 改签张数
        sqlBuilder.append(", sum(case when  is_rebook = 'T' then coalesce(quantity,0) else 0 end) as rebooktkt \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.PRE_WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassCondition(productType));
        sqlBuilder.append(" and audited <> 'F' ");

        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 机票潜在节省(从有消费记录开始)
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> agreementPotentialSaveTotal(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        // 最早时间
        if (isBookCaliber) {
            sqlBuilder.append(String.format("min(%s) as report_date \n", ORDERDT));
        } else {
            sqlBuilder.append(String.format("min(%s) as report_date \n", REPORT_DATE));
        }
        // 超标损失
        sqlBuilder.append(", sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end) as overAmount \n");
        // 退票损失
        sqlBuilder.append(", SUM(coalesce(refund_fee,0)) + SUM(coalesce(refund_service_fee,0)) + SUM(coalesce(refund_behind_service_fee, 0)) " +
                "as refundloss \n");
        // 改签损失
        sqlBuilder.append(", SUM(coalesce(change_fee,0)) + SUM(coalesce(rebook_price_differential,0)) + SUM(coalesce(rebook_service_fee,0)) " +
                "+ SUM(if(flight_class='N',coalesce(oilfeedifferential,0),coalesce(tax_differential,0))) " +
                "+ SUM(coalesce(rebook_behind_service_fee,0)) as rebookloss \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.PRE_WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        parmList.add(queryPartition(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD));
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList));
        sqlBuilder.append(getFlightClassCondition(productType));
        sqlBuilder.append(" and audited <> 'F' ");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 机票超标原因分析
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> flightLowRcAnalysis(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String lang) throws Exception {
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuilder sqlBuilder = new StringBuilder();
        boolean isEn = SharkUtils.isEN(lang);
        sqlBuilder.append(" SELECT  low_rid , " + (isEn ? "low_rc_en " : "low_rc") + " as low_rc_desc \n");
        sqlBuilder.append(" , COUNT(DISTINCT order_id) AS rcTimes \n");
        sqlBuilder.append(" ,sum(netfare) as amount \n");
        sqlBuilder.append(" ,sum(print_price) as printPrice \n");
        sqlBuilder.append(" ,sum(CASE WHEN low_rid <> '' AND low_rid IS NOT NULL AND abs(coalesce(netfare, 0)) >= abs(coalesce(corp_price_adj, 0)) AND is_refund = 'F' " +
                "                     THEN coalesce(corp_price_adj, 0) * quantity ELSE 0 END) as corpPriceAdj \n");
        sqlBuilder.append(" ,SUM(over_std_esti_save_amount) as overAmount \n");
        sqlBuilder.append("from ");
        sqlBuilder.append(ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(" and low_rid != '' ");
        sqlBuilder.append(" and is_refund = 'F' ");
        if (isBookCaliber) {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlWithCol(requestDto, parmList, ORDERDT));
        } else {
            sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        }
        sqlBuilder.append(getFlightClassCondition(productType));
        sqlBuilder.append(" group by ");
        sqlBuilder.append("low_rid, " + (isEn ? "low_rc_en " : "low_rc"));
        sqlBuilder.append(" order by ");
        sqlBuilder.append("overAmount desc");
        sqlBuilder.append(",low_rid, " + (isEn ? "low_rc_en " : "low_rc"));

        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "flightLowRcAnalysis");
    }

    @Override
    public String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum
            , BaseQueryConditionDTO baseQueryConditionDto, Pager pager, String productType) {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                baseQueryConditionDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        StringBuffer baseSql = new StringBuffer();
        ClickHouseTable clickHouseTable = getTargetTable();
        String partion = queryPartition(clickHouseTable);
        JoinCondition biz = joinCondition(analysisObjectEnum);
        if (isBookCaliber) {
            baseSql.append(String.format(TOP_DEPT_POTENTIAL_SAVE_SQL, biz.getResultFields(), biz.getGroupFields()
                    , BaseConditionPrebuilder.buildPreSqlWithCol(baseQueryConditionDto, parmList, partion, ORDERDT).concat(getFlightClassCondition(productType))
                    , biz.getGroupFields(),
                    BaseConditionPrebuilder.buildPreSqlWithCol(baseQueryConditionDto, parmList, partion, ORDERDT).concat(getFlightClassCondition(productType))
                    , biz.getGroupFields()));
        } else {
            baseSql.append(String.format(TOP_DEPT_POTENTIAL_SAVE_SQL, biz.getResultFields(), biz.getGroupFields()
                    , BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, partion).concat(getFlightClassCondition(productType))
                    , biz.getGroupFields(),
                    BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, partion).concat(getFlightClassCondition(productType))
                    , biz.getGroupFields()));
        }
        baseSql.append(" limit ?, ? ");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        return baseSql.toString();
    }

    @Override
    protected ClickHouseTable getTargetTable() {
        return ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
    }

    @Override
    public String countSql(List<Object> parmList, ClickHouseTable clickHouseTable, AnalysisObjectEnum analysisObjectEnum
            , BaseQueryConditionDTO baseQueryConditionDto, String productType) {
        // 是否是预订口径
        boolean isBookCaliber = StringUtils.equalsIgnoreCase(
                baseQueryConditionDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name());
        String partion = queryPartition(clickHouseTable);
        JoinCondition biz = joinCondition(analysisObjectEnum);
        if (isBookCaliber) {
            return String.format(TOP_DEPT_POTENTIAL_SAVE_SQL_COUNT,
                    BaseConditionPrebuilder.buildPreSqlWithCol(baseQueryConditionDto, parmList, partion, ORDERDT).concat(getFlightClassCondition(productType)),
                    biz.getGroupFields());
        } else {
            return String.format(TOP_DEPT_POTENTIAL_SAVE_SQL_COUNT,
                    BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, partion).concat(getFlightClassCondition(productType)), biz.getGroupFields());
        }
    }

    /**
     * 机票节省只统计国内经济舱
     *
     * @return
     */
    protected String getFlightSaveCondition() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" and flight_class = 'N' ");
        stringBuilder.append(" and class_type = 'Y' ");
        return stringBuilder.toString();
    }
}
