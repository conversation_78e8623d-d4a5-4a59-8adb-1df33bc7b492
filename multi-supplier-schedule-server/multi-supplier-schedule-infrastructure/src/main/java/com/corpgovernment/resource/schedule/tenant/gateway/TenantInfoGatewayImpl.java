package com.corpgovernment.resource.schedule.tenant.gateway;

import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.vo.tenant.TenantInfoVo;
import com.corpgovernment.resource.schedule.domain.tenant.TenantInfoGateway;
import com.corpgovernment.resource.schedule.tenant.mysql.entity.TenantInfoDo;
import com.corpgovernment.resource.schedule.tenant.mysql.mapper.TenantInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/9 23:00
 * @description
 */
@Service
public class TenantInfoGatewayImpl implements TenantInfoGateway {

    @Autowired
    private TenantInfoMapper tenantInfoMapper;

    @Override
    public TenantInfoVo getTenantInfo() {
        TenantInfoDo tenantInfoDo = tenantInfoMapper.selectTenantInfoByTenantId();

        return convert2Vo(tenantInfoDo);
    }

    private TenantInfoVo convert2Vo(TenantInfoDo tenantInfoDo) {
        if (tenantInfoDo == null) {
            return null;
        }
        TenantInfoVo tenantInfoVo = new TenantInfoVo();
        tenantInfoVo.setTenantId(tenantInfoDo.getTenantId());
        tenantInfoVo.setTenantName(tenantInfoDo.getTenantName());
        tenantInfoVo.setStdIndustry1(tenantInfoDo.getStdIndustry1());
        tenantInfoVo.setStdIndustry2(tenantInfoDo.getStdIndustry2());
        tenantInfoVo.setStartTime(tenantInfoDo.getStartTime());
        tenantInfoVo.setRemark(tenantInfoDo.getRemark());
        return tenantInfoVo;
    }
}
