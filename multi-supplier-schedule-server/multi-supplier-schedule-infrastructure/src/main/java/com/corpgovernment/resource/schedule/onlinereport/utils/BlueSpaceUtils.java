package com.corpgovernment.resource.schedule.onlinereport.utils;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023-01-06 17:14
 * @desc
 */
public class BlueSpaceUtils {
    /**
     * 是否是蓝色空间
     * @param blueSpace
     * @return
     */
    public static boolean isBlueSpace(String blueSpace){
        return StringUtils.equalsIgnoreCase("T", blueSpace);
    }

    /**
     * 是否是ja-jp(pos为ja-jp不一定是蓝色空间，有可能是TokyoMaster)
     * @param pos
     * @return
     */
    public static boolean isJajp(String pos){
        return StringUtils.equalsIgnoreCase(OrpConstants.POS_JP, pos);
    }

    /**
     * 是否是TokyoMaster
     * @param pos
     * @return
     */
    public static boolean isTokyoMaster(String pos, String blueSpace){
        return isJajp(pos) && !isBlueSpace(blueSpace);
    }

    /**
     * 是否国外
     * @param pos
     * @param blueSpace
     * @return
     */
    public static boolean isForeign(String pos, String blueSpace){
        return isBlueSpace(blueSpace) || isJajp(pos);
    }
}
