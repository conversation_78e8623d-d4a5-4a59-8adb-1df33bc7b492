package com.corpgovernment.resource.schedule.onlinereport.consume.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.CarConsume;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.OnlineReportConsumeBO;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportConsumeDao;
import com.corpgovernment.resource.schedule.onlinereport.consume.AbstractGenralConsume;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Service
public class CarGenralBiz extends AbstractGenralConsume {

    @Autowired
    private OnlineReportConsumeDao reportConsumeDao;

    @Override
    protected BigDecimal getTotalAmount1(OnlineReportConsumeBO genralConsumeCurrent) {
        return getTotalAmount3(genralConsumeCurrent);
    }

    @Override
    protected Integer getTotalQuantity1(OnlineReportConsumeBO genralConsumeCurrent) {
        return getTotalQuantity3(genralConsumeCurrent);
    }

    @Override
    protected BigDecimal getTotalAmount3(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneAmount())
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalTwoAmount()))
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalThreeAmount()))
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFourAmount()))
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFiveAmount()))
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalSixAmount()))
                .add(OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalSevenAmount()));
    }

    @Override
    protected Integer getTotalQuantity3(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneQuantity())
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalTwoQuantity()))
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalThreeQuantity()))
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFourQuantity()))
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalFiveQuantity()))
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalSixQuantity()))
                + (OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalSevenQuantity()));
    }

    @Override
    protected OnlineReportConsumeBO aggreationGenralConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        List<CarConsume> carAggreationConsumeDtoList = (List<CarConsume>) reportConsumeDao.aggreationCarWithCondition(baseQueryConditionDto, CarConsume.class);
        return convert(carAggreationConsumeDtoList);
    }

    @Override
    protected OnlineReportConsumeBO aggreationCurrentConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        List<CarConsume> carAggreationConsumeDtoList = (List<CarConsume>) reportConsumeDao.aggreationCarWithCondition(baseQueryConditionDto, CarConsume.class);

        OnlineReportConsumeBO genralConsume = convert(carAggreationConsumeDtoList);
        // 商旅服务费
        Double totalCorpServiceFee = carAggreationConsumeDtoList.stream()
                .mapToDouble(i -> Optional.ofNullable(i.getTotalCorpServiceFee()).orElse(0d)).sum();
        genralConsume.setTotalCorpServiceFee(OrpReportUtils.formatBigDecimal(new BigDecimal(totalCorpServiceFee)));

        Double totalAmount = carAggreationConsumeDtoList.stream()
                .mapToDouble(i -> Optional.ofNullable(i.getTotalAmount()).orElse(0d)).sum();
        int totalCntOrder = carAggreationConsumeDtoList.stream()
                .mapToInt(i -> Optional.ofNullable(i.getTotalCntOrder()).orElse(0)).sum();
        // 订单均价
        genralConsume.setAvgPrice(OrpReportUtils.divide(new BigDecimal(totalAmount - totalCorpServiceFee)
                , new BigDecimal(totalCntOrder), OrpConstants.TWO));
        return genralConsume;
    }

    private OnlineReportConsumeBO convert(List<CarConsume> carAggreationConsumeDtoList) {
        OnlineReportConsumeBO genralConsume = new OnlineReportConsumeBO();
        Double currentTotalAmount = 0d;
        Integer currentTotalQuantity = 0;
        if (CollectionUtils.isNotEmpty(carAggreationConsumeDtoList)) {
            currentTotalAmount = carAggreationConsumeDtoList.stream().mapToDouble(CarConsume::getTotalAmount).filter(Objects::nonNull).sum();
            currentTotalQuantity = carAggreationConsumeDtoList.stream().mapToInt(CarConsume::getTotalCntOrder).filter(Objects::nonNull).sum();
            // 国内打车
            carAggreationConsumeDtoList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getOrderType(), "6") && StringUtils.equalsIgnoreCase(i.getSubProductLine(), "1"))
                    .findFirst().ifPresent(a -> {
                        genralConsume.setTotalOneAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(a.getTotalAmount())));
                        genralConsume.setTotalOneQuantity(a.getTotalCntOrder());
                    });
            // 国际打车
            carAggreationConsumeDtoList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getOrderType(), "6")
                            && StringUtils.equalsIgnoreCase(i.getSubProductLine(), "CAR_TAXI_INTL"))
                    .findFirst().ifPresent(a -> {
                        genralConsume.setTotalFiveAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(a.getTotalAmount())));
                        genralConsume.setTotalFiveQuantity(a.getTotalCntOrder());
                    });
            // 国内接送机
            carAggreationConsumeDtoList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getOrderType(), "1")).findFirst().ifPresent(a -> {
                genralConsume.setTotalTwoAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(a.getTotalAmount())));
                genralConsume.setTotalTwoQuantity(a.getTotalCntOrder());
            });
            // 国际接送机
            carAggreationConsumeDtoList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getOrderType(), "2")).findFirst().ifPresent(a -> {
                genralConsume.setTotalSixAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(a.getTotalAmount())));
                genralConsume.setTotalSixQuantity(a.getTotalCntOrder());
            });
            // 国内租车
            carAggreationConsumeDtoList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getOrderType(), "4")).findFirst().ifPresent(a -> {
                genralConsume.setTotalThreeAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(a.getTotalAmount())));
                genralConsume.setTotalThreeQuantity(a.getTotalCntOrder());
            });
            // 包车
            carAggreationConsumeDtoList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getOrderType(), "3")).findFirst().ifPresent(a -> {
                genralConsume.setTotalFourAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(a.getTotalAmount())));
                genralConsume.setTotalFourQuantity(a.getTotalCntOrder());
            });
            // 国际租车
            carAggreationConsumeDtoList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getOrderType(), "18")).findFirst().ifPresent(a -> {
                genralConsume.setTotalThreeAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(a.getTotalAmount())));
                genralConsume.setTotalThreeQuantity(a.getTotalCntOrder());
            });
        }
        genralConsume.setTotalAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(currentTotalAmount)));
        genralConsume.setTotalQuantity(currentTotalQuantity);
        return genralConsume;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum s) {
        return QueryReportBuTypeEnum.car == s;
    }
}
