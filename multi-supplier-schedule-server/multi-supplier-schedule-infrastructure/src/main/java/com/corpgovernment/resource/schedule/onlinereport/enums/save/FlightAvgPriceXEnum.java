package com.corpgovernment.resource.schedule.onlinereport.enums.save;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;

import java.util.Arrays;
import java.util.List;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.save
 * @description:机酒节省分析-平均票价X轴
 * @author: md_wang
 * @create: 2022-08-02 20:00
 **/
public enum FlightAvgPriceXEnum {
    /**
     * [0~300)
     */
    ZERO("0-300", "ticket300", "ticketRate300"),
    /**
     * [300~600)
     */
    ONE("300-600", "ticket600", "ticketRate600"),
    /**
     * [600-900)
     */
    TWO("600-900", "ticket900", "ticketRate900"),
    /**
     * [900-1200)
     */
    THREE("900-1200", "ticket1200", "ticketRate1200"),
    /**
     * [1200,1500)
     */
    FOUR("1200-1500", "ticket1500", "ticketRate1500"),
    /**
     * 1500及以上
     */
    FIVE(">=1500", "ticketGt1500", "ticketRateGt1500"),
    ;

    private String x;
    private String ticket;
    private String ticketRate;

    FlightAvgPriceXEnum(String x, String ticket, String ticketRate) {
        this.x = x;
        this.ticket = ticket;
        this.ticketRate = ticketRate;
    }

    public static List<FlightAvgPriceXEnum> gt1200() {
        return Arrays.asList(FOUR, FIVE);
    }

    public static List<FlightAvgPriceXEnum> gt1500() {
        return Arrays.asList(FIVE);
    }

    public static String getPrice(FlightAvgPriceXEnum x) {
        if (FlightAvgPriceXEnum.FOUR.equals(x)) {
            return "1200";
        }
        if (FlightAvgPriceXEnum.FIVE.equals(x)) {
            return "1500";
        }
        return OrpConstants.EMPTY;
    }

    public String getX() {
        return x;
    }

    public void setX(String x) {
        this.x = x;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public String getTicketRate() {
        return ticketRate;
    }

    public void setTicketRate(String ticketRate) {
        this.ticketRate = ticketRate;
    }
}
