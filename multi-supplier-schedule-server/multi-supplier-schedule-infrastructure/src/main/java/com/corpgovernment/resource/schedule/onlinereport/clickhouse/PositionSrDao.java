package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.base.AbstractCommonSrDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.StarRocksTable;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-19 14:56
 * @desc 地图信息
 */
@Service
@Repository
public class PositionSrDao extends AbstractCommonSrDao {

    private static final StarRocksTable TABLE_NEW = StarRocksTable.ADM_INDEX_ONE_TRIP_FULL_TRIP_ID_GENERATE_NEW;


    /**
     * 查询足迹
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryTrip(BaseQueryConditionDTO requestDto, Class<T> clazz, QueryReportBuTypeEnum queryReportBuTypeEnum
            , String productType, boolean needAgg, String lang) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dimName = SharkUtils.isZH(lang) ? " sub_trip_city_name" : " sub_trip_city_name_en ";
        if (!needAgg) {
            sqlBuilder.append("select ");
        } else {
            sqlBuilder.append("select sub_trip_city_id, " + dimName + " as subTripCityName ,sub_trip_city_glat,sub_trip_city_glon,");
        }
        sqlBuilder.append("  COUNT(distinct CASE \n" +
                "            WHEN (substring(start_sub_trip_date, 1, 10) <= ?\n" +
                "            AND substring(end_sub_trip_date, 1, 10) >= ?\n" +
                "            AND substring(end_time, 1, 10) >= ?\n" +
                "            AND substring(start_time, 1, 10) <= ?) THEN processed_user_name" +
                "            ELSE null\n" +
                "        END) AS m_trip_cnt  ");
        sqlBuilder.append(" , COUNT(distinct CASE \n" +
                "            WHEN (substring(end_sub_trip_date, 1, 10) < ?\n" +
                "            AND substring(end_sub_trip_date, 1, 10) >= ?\n" +
                "            AND substring(end_time, 1, 10) >= ?) THEN processed_user_name" +
                "            ELSE null\n" +
                "        END) AS h_trip_cnt \n");
        sqlBuilder.append(" , COUNT(distinct CASE \n" +
                "            WHEN substring(start_sub_trip_date, 1, 10) > ? THEN processed_user_name" +
                "            ELSE null\n" +
                "        END) AS  w_trip_cnt ");
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getStartTime());
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getStartTime());
        parmList.add(requestDto.getStartTime());
        parmList.add(requestDto.getEndTime());
        if (QueryReportBuTypeEnum.hotel == queryReportBuTypeEnum) {
            sqlBuilder.append(" , COUNT(distinct CASE \n" +
                    "            WHEN substring(start_sub_trip_date, 1, 10) > ? THEN sub_one_trip_id \n" +
                    "            ELSE null\n" +
                    "        END) AS  w_htl_trip_cnt ");
            parmList.add(requestDto.getEndTime());
        }
        sqlBuilder.append(" from  ");
        sqlBuilder.append(TABLE_NEW.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" 1 = 1");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList));
        if (QueryReportBuTypeEnum.hotel == queryReportBuTypeEnum) {
            sqlBuilder.append(" and order_type = 'htl'");
        }
        if (CollectionUtils.isNotEmpty(requestDto.getCityIds())) {
            if (CollectionUtils.isNotEmpty(requestDto.getProvinceIds()) || CollectionUtils.isNotEmpty(requestDto.getCountryIds())) {
                sqlBuilder.append(" and (").append(buildCityPreSql(requestDto.getCityIds(), parmList, "sub_trip_city_id"));
            } else {
                sqlBuilder.append(" and ").append(buildCityPreSql(requestDto.getCityIds(), parmList, "sub_trip_city_id"));
            }
            if (CollectionUtils.isNotEmpty(requestDto.getProvinceIds())) {
                sqlBuilder.append(" or ").append(buildCityPreSql(requestDto.getProvinceIds(), parmList, "sub_trip_province_id")).append(")");
            }
            if (CollectionUtils.isNotEmpty(requestDto.getCountryIds())) {
                sqlBuilder.append(" or ").append(buildCityPreSql(requestDto.getCountryIds(), parmList, "sub_trip_city_countryid")).append(")");
            }
        } else {
            if (CollectionUtils.isNotEmpty(requestDto.getProvinceIds())) {
                sqlBuilder.append(" and ").append(buildCityPreSql(requestDto.getProvinceIds(), parmList, "sub_trip_province_id"));
            }
            if (CollectionUtils.isNotEmpty(requestDto.getCountryIds())) {
                sqlBuilder.append(" and ").append(buildCityPreSql(requestDto.getCountryIds(), parmList, "sub_trip_city_countryid"));
            }
        }
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        sqlBuilder.append(getProductTypeCondition(productType));
        sqlBuilder.append(" and substring(end_time, 1, 10) >= ? ");
        parmList.add(requestDto.getStartTime());
        if (needAgg) {
            sqlBuilder.append(" GROUP BY sub_trip_city_id," + dimName + ",sub_trip_city_glat,sub_trip_city_glon  ");
        }
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 查询城市轨迹
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryTravelTrack(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, boolean needAgg, String lang)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dimName1 = SharkUtils.isZH(lang) ? " start_city_name" : " start_city_name_en ";
        String dimName2 = SharkUtils.isZH(lang) ? " end_city_name" : " end_city_name_en ";
        if (needAgg) {
            sqlBuilder.append("select start_city_id, " + dimName1 + " as startCityName, start_city_glat, start_city_glon, end_city_id\n" +
                    "    , " + dimName2 + " as endCityName, end_city_glat, end_city_glon,order_type, count(distinct sub_one_trip_id) as trip_cnt  ");
        } else {
            sqlBuilder.append("select count(distinct processed_user_name) as trip_cnt  ");
        }
        sqlBuilder.append(" , (CASE \n" +
                "            WHEN (substring(start_sub_trip_date, 1, 10) <= ?\n" +
                "            AND substring(end_sub_trip_date, 1, 10) >= ?\n" +
                "            AND substring(end_time, 1, 10) >= ?\n" +
                "            AND substring(start_time, 1, 10) <= ?) then 'M' " +
                "            when (substring(end_sub_trip_date, 1, 10) < ?\n" +
                "            AND substring(end_sub_trip_date, 1, 10) >= ?\n" +
                "            AND substring(end_time, 1, 10) >= ?) THEN 'H'\n" +
                "            when (substring(start_sub_trip_date, 1, 10) > ?) THEN 'W'\n" +
                "            ELSE NULL\n" +
                "        END) AS line_type \n");
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getStartTime());
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getStartTime());
        parmList.add(requestDto.getStartTime());
        parmList.add(requestDto.getEndTime());
        sqlBuilder.append(" from  ");
        sqlBuilder.append(TABLE_NEW.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" 1 = 1 ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList));
        // 路线图只查询机票和火车产线
        sqlBuilder.append(" and order_type in('flt','train')");
        if (CollectionUtils.isNotEmpty(requestDto.getCityIds())) {
            if (CollectionUtils.isNotEmpty(requestDto.getProvinceIds()) || CollectionUtils.isNotEmpty(requestDto.getCountryIds())) {
                sqlBuilder.append(" and (").append(buildCityPreSql(requestDto.getCityIds(), parmList, "sub_trip_city_id"));
            } else {
                sqlBuilder.append(" and ").append(buildCityPreSql(requestDto.getCityIds(), parmList, "sub_trip_city_id"));
            }
            if (CollectionUtils.isNotEmpty(requestDto.getProvinceIds())) {
                sqlBuilder.append(" or ").append(buildCityPreSql(requestDto.getProvinceIds(), parmList, "sub_trip_province_id")).append(")");
            }
            if (CollectionUtils.isNotEmpty(requestDto.getCountryIds())) {
                sqlBuilder.append(" or ").append(buildCityPreSql(requestDto.getCountryIds(), parmList, "sub_trip_city_countryid")).append(")");
            }
        } else {
            if (CollectionUtils.isNotEmpty(requestDto.getProvinceIds())) {
                sqlBuilder.append(" and ").append(buildCityPreSql(requestDto.getProvinceIds(), parmList, "sub_trip_province_id"));
            }
            if (CollectionUtils.isNotEmpty(requestDto.getCountryIds())) {
                sqlBuilder.append(" and ").append(buildCityPreSql(requestDto.getCountryIds(), parmList, "sub_trip_city_countryid"));
            }
        }
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        sqlBuilder.append(getProductTypeCondition(productType));
        sqlBuilder.append(" and substring(end_time, 1, 10) >= ? ");
        parmList.add(requestDto.getStartTime());
        if (needAgg) {
            sqlBuilder.append(" GROUP BY start_city_id, " + dimName1 + ", start_city_glat, start_city_glon, end_city_id\n" +
                    "    , " + dimName2 + ", end_city_glat, end_city_glon,order_type,line_type  ");
        } else {
            sqlBuilder.append("GROUP BY line_type ");
        }

        return commonList(clazz, sqlBuilder.toString(), parmList);
    }


    /**
     * 查询正在出差的人
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryTripPassenger(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();

        sqlBuilder.append("select passengeruid, pin_yin_user_name,processed_user_name");
        sqlBuilder.append(" from  ");
        sqlBuilder.append(TABLE_NEW.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" 1 = 1 ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList));
        sqlBuilder.append(" and (substring(start_sub_trip_date, 1, 10) <= ?\n" +
                "            AND substring(end_sub_trip_date, 1, 10) >= ?\n" +
                "            AND substring(end_time, 1, 10) >= ?\n" +
                "            AND substring(start_time, 1, 10) <= ?) ");
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getStartTime());
        parmList.add(requestDto.getEndTime());
        if (CollectionUtils.isNotEmpty(requestDto.getCityIds())) {
            sqlBuilder.append(buildCityPreSqlWithAnd(requestDto.getCityIds(), parmList, "sub_trip_city_id"));
        }
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        sqlBuilder.append(getProductTypeCondition(productType));
        sqlBuilder.append(" and substring(end_time, 1, 10) >= ? ");
        parmList.add(requestDto.getStartTime());
        sqlBuilder.append(" GROUP BY passengeruid, pin_yin_user_name,processed_user_name  ");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 构建城市查询条件
     *
     * @param list
     * @param parmList
     * @return
     */
    public static String buildCityPreSqlWithAnd(List<Integer> list, List<Object> parmList, String conditionFiled) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(list)) {
            sqlBuffer.append(" and " + conditionFiled + " in (");
            for (int i = 0; i < list.size(); i++) {
                sqlBuffer.append(" ? ");
                if (i != list.size() - 1) {
                    sqlBuffer.append(OrpConstants.COMMA);
                }
                parmList.add(list.get(i));
            }
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }

    /**
     * 构建城市查询条件
     *
     * @param list
     * @param parmList
     * @return
     */
    public static String buildCityPreSql(List<Integer> list, List<Object> parmList, String conditionFiled) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(list)) {
            sqlBuffer.append(" " + conditionFiled + " in (");
            for (int i = 0; i < list.size(); i++) {
                sqlBuffer.append(" ? ");
                if (i != list.size() - 1) {
                    sqlBuffer.append(OrpConstants.COMMA);
                }
                parmList.add(list.get(i));
            }
            sqlBuffer.append(")");
        }
        return sqlBuffer.toString();
    }


    /**
     * 类型查询条件
     *
     * @param productType
     * @return
     */
    protected String getProductTypeCondition(String productType) {
        StringBuilder stringBuilder = new StringBuilder();
        // 国内
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            stringBuilder.append(" and sub_trip_city_countryid = 1");
        }
        // 国际
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            stringBuilder.append(" and sub_trip_city_countryid != 1");
        }
        return stringBuilder.toString();
    }

    /**
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param searchType
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryTopTrip(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, String searchType, String lang)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dimName = SharkUtils.isZH(lang) ? " sub_trip_city_name" : " sub_trip_city_name_en ";

        sqlBuilder.append("select sub_trip_city_id, ").append(dimName + " as subTripCityName");
        if (StringUtils.equalsIgnoreCase(searchType, "w")) {
            sqlBuilder.append(" , COUNT(distinct CASE \n" +
                    "            WHEN substring(start_sub_trip_date, 1, 10) > ? THEN processed_user_name \n" +
                    "            ELSE null\n" +
                    "        END) AS passenger_cnt \n");
            sqlBuilder.append(" , COUNT(distinct CASE \n" +
                    "            WHEN substring(start_sub_trip_date, 1, 10) > ? THEN sub_one_trip_id \n" +
                    "            ELSE null\n" +
                    "        END) AS trip_cnt \n");
            parmList.add(requestDto.getEndTime());
            parmList.add(requestDto.getEndTime());
        } else {
            sqlBuilder.append(" , COUNT(distinct CASE \n" +
                    "            WHEN (substring(start_sub_trip_date, 1, 10) <= ?\n" +
                    "            AND substring(end_sub_trip_date, 1, 10) >= ?\n" +
                    "            AND substring(end_time, 1, 10) >= ?\n" +
                    "            AND substring(start_time, 1, 10) <= ?) THEN processed_user_name \n" +
                    "            ELSE null\n" +
                    "        END) AS passenger_cnt  ");
            sqlBuilder.append(" , COUNT(distinct CASE \n" +
                    "            WHEN (substring(start_sub_trip_date, 1, 10) <= ?\n" +
                    "            AND substring(end_sub_trip_date, 1, 10) >= ?\n" +
                    "            AND substring(end_time, 1, 10) >= ?\n" +
                    "            AND substring(start_time, 1, 10) <= ?) THEN sub_one_trip_id \n" +
                    "            ELSE null\n" +
                    "        END) AS trip_cnt  ");
            parmList.add(requestDto.getEndTime());
            parmList.add(requestDto.getEndTime());
            parmList.add(requestDto.getStartTime());
            parmList.add(requestDto.getEndTime());
            parmList.add(requestDto.getEndTime());
            parmList.add(requestDto.getEndTime());
            parmList.add(requestDto.getStartTime());
            parmList.add(requestDto.getEndTime());
        }
        sqlBuilder.append(" from  ");
        sqlBuilder.append(TABLE_NEW.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" 1 = 1 ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList));
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        sqlBuilder.append(getProductTypeCondition(productType));
        sqlBuilder.append(" and substring(end_time, 1, 10) >= ? ");
        parmList.add(requestDto.getStartTime());
        sqlBuilder.append(" GROUP BY sub_trip_city_id, ").append(dimName);
        sqlBuilder.append(" order BY trip_cnt desc limit 3");
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

    /**
     * 根据城市id查询行程数据
     *
     * @param requestDto
     * @param clazz
     * @param productType
     * @param cityIds
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> queryTripByCity(BaseQueryConditionDTO requestDto, Class<T> clazz, String productType, List<Integer> cityIds, String lang)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        List<Object> parmList = new ArrayList<>();
        String dimName = SharkUtils.isZH(lang) ? " sub_trip_city_name" : " sub_trip_city_name_en ";

        sqlBuilder.append("select sub_trip_city_id, ").append(dimName + " as subTripCityName");
        sqlBuilder.append(" , COUNT(distinct CASE \n" +
                "            WHEN (substring(start_sub_trip_date, 1, 10) <= ?\n" +
                "            AND substring(end_sub_trip_date, 1, 10) >= ?\n" +
                "            AND substring(end_time, 1, 10) >= ?\n" +
                "            AND substring(start_time, 1, 10) <= ?) THEN processed_user_name \n" +
                "            ELSE null\n" +
                "        END) AS m_trip_cnt  ");
        sqlBuilder.append(" , COUNT(distinct CASE \n" +
                "            WHEN (substring(end_sub_trip_date, 1, 10) < ?\n" +
                "            AND substring(end_sub_trip_date, 1, 10) >= ?\n" +
                "            AND substring(end_time, 1, 10) >= ?) THEN processed_user_name \n" +
                "            ELSE null\n" +
                "        END) AS h_trip_cnt \n");
        sqlBuilder.append(" , COUNT(distinct CASE \n" +
                "            WHEN substring(start_sub_trip_date, 1, 10) > ? THEN processed_user_name\n" +
                "            ELSE null\n" +
                "        END) AS  w_trip_cnt ");
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getStartTime());
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getEndTime());
        parmList.add(requestDto.getStartTime());
        parmList.add(requestDto.getStartTime());
        parmList.add(requestDto.getEndTime());
        sqlBuilder.append(" from  ");
        sqlBuilder.append(TABLE_NEW.getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append(" 1 = 1 ");
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSqlNoTime(requestDto, parmList));
        // 因公
        sqlBuilder.append(String.format(" and isself = '%s' ", SharkUtils.getChineseVal("FlightFeeType")));
        sqlBuilder.append(getProductTypeCondition(productType));
        sqlBuilder.append(buildCityPreSqlWithAnd(cityIds, parmList, "sub_trip_city_id"));
        sqlBuilder.append(" and substring(end_time, 1, 10) >= ? ");
        parmList.add(requestDto.getStartTime());
        sqlBuilder.append(" GROUP BY sub_trip_city_id, ").append(dimName);
        // 查询clickhouse
        return commonList(clazz, sqlBuilder.toString(), parmList);
    }

}
