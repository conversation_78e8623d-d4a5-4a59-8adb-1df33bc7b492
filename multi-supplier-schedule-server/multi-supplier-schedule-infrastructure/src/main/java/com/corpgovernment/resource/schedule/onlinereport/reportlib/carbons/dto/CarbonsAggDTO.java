package com.corpgovernment.resource.schedule.onlinereport.reportlib.carbons.dto;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-09-21 11:24
 * @desc
 */
@Data
public class CarbonsAggDTO {
    // 维度
    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;
    // 总计
    @Column(name = "sumCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumCarbons;
    // 单程
    @Column(name = "avgCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal avgCarbons;
    // 占比
    @Column(name = "carbonsPercent")
    @Type(value = Types.DECIMAL)
    private BigDecimal carbonsPercent;
}
