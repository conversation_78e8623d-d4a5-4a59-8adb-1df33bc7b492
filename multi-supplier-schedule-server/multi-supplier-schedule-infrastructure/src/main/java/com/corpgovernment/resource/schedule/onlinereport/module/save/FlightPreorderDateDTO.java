package com.corpgovernment.resource.schedule.onlinereport.module.save;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2022/6/21 15:27
 * @Desc
 */
@Data
public class FlightPreorderDateDTO {
    @Column(name = "preOrderDateRange")
    @Type(value = Types.VARCHAR)
    private String orderDateRange;

    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    @Column(name = "totalEconomyPrice")
    @Type(value = Types.DOUBLE)
    private Double totalEconomyPrice;

    @Column(name = "totalEconomyPriceRate")
    @Type(value = Types.DOUBLE)
    private Double totalEconomyPriceRate;

    @Column(name = "totalEconomyQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalEconomyQuantity;

    @Column(name = "totalFullPriceQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalFullPriceQuantity;

    @Column(name = "totalPreNPrice")
    @Type(value = Types.DOUBLE)
    private Double totalPreNPrice;

    @Column(name = "totalPreNPriceRate")
    @Type(value = Types.DOUBLE)
    private Double totalPreNPriceRate;

    @Column(name = "totalPreNQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalPreNQuantity;
}
