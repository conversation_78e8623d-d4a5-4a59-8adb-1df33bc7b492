package com.corpgovernment.resource.schedule.onlinereport.hotanalysis.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportTypeionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportHotAanlysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendLegend;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendPoint;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.OnlineReportHotAnalysisDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.dto.OnlineReportHotAnalysisDto;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hotanalysis.dto.OnlineReportHotAnalysisRequestBo;
import com.corpgovernment.resource.schedule.onlinereport.enums.HotAnalysisEnum;
import com.corpgovernment.resource.schedule.onlinereport.hotanalysis.AbstractHotAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/25 15:28
 * @description：
 * @modified By：
 * @version: $
 */
@Component
public class HotAnalysisHotelBiz extends AbstractHotAnalysisBiz {

    @Autowired
    OnlineReportHotAnalysisDaoImpl onlineReportHotAnalysisDaoImpl;


    @Override
    
    public List<OnlineReportTrendPoint> trendBody(OnlineReportHotAanlysisRequest request) throws Exception {

        OnlineReportHotAnalysisRequestBo requestBo = mapper(request);
        List<OnlineReportHotAnalysisDto> queryData = onlineReportHotAnalysisDaoImpl.query(requestBo);
        QueryReportTypeionEnum queryReportTypeionEnum = request.getQueryType();
        if (queryReportTypeionEnum == QueryReportTypeionEnum.amount) {
            queryData.sort((o1, o2) -> o2.getSumAmount().subtract(o1.getSumAmount()).intValue());
        } else {
            queryData.sort((o1, o2) -> o2.getSumQuantity().subtract(o1.getSumQuantity()).intValue());
        }

        // 占比
        BigDecimal totalAmount = new BigDecimal("0");
        BigDecimal totalQuantity = new BigDecimal("0");
        for (OnlineReportHotAnalysisDto dto: queryData) {
            totalAmount = totalAmount.add(dto.getSumAmount());
            totalQuantity = totalQuantity.add(dto.getSumQuantity());
        }

        if (!isDownload(request)){
            // 非下载的时候如果传了topLimit则按topLimit取，否则取前20
            int topLimit = Optional.ofNullable(request.getTopLimit()).orElse(OrpConstants.TWENTY);
            queryData = queryData.size() > topLimit ? queryData.subList(OrpConstants.ZERO, topLimit) : queryData;
        }else {
            // 下载的时候如果传了topLimit则按topLimit取，否则取全部
            if (Objects.nonNull(request.getTopLimit()) && request.getTopLimit() > 0){
                queryData = queryData.size() > request.getTopLimit() ? queryData.subList(OrpConstants.ZERO, request.getTopLimit()) : queryData;
            }
        }

        List<Field> fields = Arrays.stream(OnlineReportHotAnalysisDto.class.getDeclaredFields())
                .filter(f -> !(f.getName().equals("dim")))
                .collect(Collectors.toList());
        BigDecimal finalTotalAmount = totalAmount;
        BigDecimal finalTotalQuantity = totalQuantity;
        List<OnlineReportTrendPoint> onlineReportTrendPointList = queryData.stream().map(o -> mapPoint(o, fields, finalTotalAmount, finalTotalQuantity))
                .collect(Collectors.toList());
        // 协议酒店集团打标
        String dim = requestBo.getExtParams().get("dim");
        if (StringUtils.equalsIgnoreCase(dim, "agreement_mgrgroup_name_en") || StringUtils.equalsIgnoreCase(dim, "agreement_mgrgroup_name")) {
            List<String> list = onlineReportHotAnalysisDaoImpl.queryAgreementHotelGroupName(requestBo);
            onlineReportTrendPointList.stream().forEach(i->i.data.put("agreementTag", isAgreement(i.getAxis(), list)));
        }
        return onlineReportTrendPointList;
    }

    private BigDecimal isAgreement(String hotelGroupName, List<String> list){
        return Optional.ofNullable(list).orElse(new ArrayList<>()).stream().anyMatch(i->StringUtils.equalsIgnoreCase(i,hotelGroupName)) ?
                BigDecimal.ONE.setScale(0) : BigDecimal.ZERO.setScale(0);
    }

    @Override
    public List<OnlineReportTrendLegend> trendLegend(OnlineReportHotAanlysisRequest request) {
        List<OnlineReportTrendLegend> legends = new ArrayList<>();
        String lang = request.getLang();
        HotAnalysisEnum[] enums = {
                HotAnalysisEnum.HTL_AMOUNT, HotAnalysisEnum.HTL_QUANTITY,
                HotAnalysisEnum.HTL_AVG_PRICE, HotAnalysisEnum.HTL_AMOUNT_RATE,
                HotAnalysisEnum.HTL_QUANTITY_RATE,HotAnalysisEnum.HTL_AGREEMENT_TAG
        };
        for (HotAnalysisEnum en : enums) {
            OnlineReportTrendLegend legend = new OnlineReportTrendLegend();
            legend.setName(SharkUtils.getHeaderVal(en.getNameKey(), lang));
            legend.setUnit(SharkUtils.getHeaderVal(en.getUnitKey(), lang));
            legend.setKey(en.getValueName());
            legends.add(legend);
        }
        return legends;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel;
    }
}
