package com.corpgovernment.resource.schedule.onlinereport.common;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.function.Function;

/**
 * Jackson {@link ObjectMapper} wrapper to make json operation easier
 *
 * <AUTHOR>
 * @date 2020/8/10
 */
public class JacksonMapper {
    public static final JacksonMapper DEFAULT = builder().build();

    private final ObjectMapper om;

    JacksonMapper(ObjectMapper om) {
        this.om = om;
    }

    public static JacksonBuilder builder() {
        return new JacksonBuilder();
    }

    public String toJson(Object object) {
        try {
            return om.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(String.format("json format error: %s", object.getClass()), e);
        }
    }

    public byte[] toJsonBytes(Object object) {
        try {
            return om.writeValueAsBytes(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(String.format("json format error: %s", object.getClass()), e);
        }
    }

    public <T> T parse(String json, Class<T> clazz) {
        try {
            return om.readValue(json, clazz);
        } catch (IOException e) {
            throw new RuntimeException(String.format("json parse error:%s", StringUtils.abbreviate(json, 100)), e);
        }
    }

    public <T> List<T> parseList(String json, Class<T> clazz) {
        try {
            CollectionType collectionType = om.getTypeFactory().constructCollectionType(List.class, clazz);
            return om.readValue(json, collectionType);
        } catch (IOException e) {
            throw new RuntimeException(String.format("json parse error:%s", StringUtils.abbreviate(json, 100)), e);
        }
    }

    public <T> T parse(InputStream is, Class<T> clazz) {
        try {
            return om.readValue(is, clazz);
        } catch (IOException e) {
            throw new RuntimeException("json parse error", e);
        }
    }

    public <T> T parse(byte[] content, Class<T> clazz) {
        try {
            return om.readValue(content, clazz);
        } catch (IOException e) {
            throw new RuntimeException("json parse error", e);
        }
    }

    public <T> T parse(File file, Class<T> clazz) {
        try {
            return om.readValue(file, clazz);
        } catch (IOException e) {
            throw new RuntimeException("json parse error", e);
        }
    }

    public <T> T parse(String json, TypeReference<T> type) {
        try {
            return om.readValue(json, type);
        } catch (IOException e) {
            throw new RuntimeException(String.format("json parse error:%s", StringUtils.abbreviate(json, 100)), e);
        }
    }

    public <T> T parse(String json, JavaType type) {
        try {
            return om.readValue(json, type);
        } catch (IOException e) {
            throw new RuntimeException(String.format("json parse error:%s", StringUtils.abbreviate(json, 100)), e);
        }
    }

    public <T> List<T> parseArray(String json, Class<T> clazz) {
        if (StringUtils.isBlank(json)) {
            return Lists.newArrayList();
        }
        return parse(json, tf -> tf.constructCollectionType(List.class, clazz));
    }

    public <T> T parse(String json, Function<TypeFactory, JavaType> typeBuilder) {
        TypeFactory typeFactory = om.getTypeFactory();
        JavaType type = typeBuilder.apply(typeFactory);
        return parse(json, type);
    }

    public JsonNode parse(String json) {
        try {
            return om.readTree(json);
        } catch (IOException e) {
            throw new RuntimeException(String.format("json parse error:%s", StringUtils.abbreviate(json, 100)), e);
        }
    }
}
