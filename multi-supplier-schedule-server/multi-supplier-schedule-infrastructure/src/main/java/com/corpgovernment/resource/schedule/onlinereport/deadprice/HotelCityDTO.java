package com.corpgovernment.resource.schedule.onlinereport.deadprice;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2022/7/20 16:31
 * @Desc
 */
@Data
public class HotelCityDTO {

    @Column(name = "pcitylevel")
    @Type(value = Types.VARCHAR)
    private String pcitylevel;

    @Column(name = "city")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    @Column(name = "cityName")
    @Type(value = Types.VARCHAR)
    private String cityName;
}
