package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.saveanalysis;


import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;
import com.corpgovernment.resource.schedule.onlinereport.module.saveanalysis.OnlineReportPotentialSaveTrendDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29
 */
public interface OnlineReportPotentialSaveTrendDaoService {
    /**
     * 查询 在线报告概况-潜在节省趋势机票
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportPotentialSaveTrendDTO> queryFlightTrend(OnlineTrendRequestDto request) throws Exception;

    /**
     * 查询 在线报告概况-潜在节省趋势酒店
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportPotentialSaveTrendDTO> queryHotelTrend(OnlineTrendRequestDto request) throws Exception;
}
