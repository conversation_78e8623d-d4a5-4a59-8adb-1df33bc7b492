package com.corpgovernment.resource.schedule.onlinereport.dept.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptDetailAnalysisRequest;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportDeptBusDetailDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptDetailDaoService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.dept.sr.SrOnlineReportDeptBusDetailDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dept.AbstractDeptDetailAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.DeptStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/*
 * <AUTHOR>
 *
 * @date 2022/09/20 16:15
 *
 * @Desc
 */


@Service
public class DeptDetailBusBizImpl extends AbstractDeptDetailAnalysisBiz {
    @Autowired
    private OnlineReportDeptBusDetailDaoImpl onlineReportDeptDetailDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private SrOnlineReportDeptBusDetailDaoImpl srOnlineReportDeptBusDetailDao;

    /**
     * 获取查询的dao
     */
    public OnlineReportDeptDetailDaoService getOnlineReportDeptDetailDao(BaseQueryConditionDTO baseQueryConditionDTO) {
        boolean useSr = baseQueryConditionDTO != null && BooleanUtils.isTrue(baseQueryConditionDTO.useStarRocks);
        return useSr ? srOnlineReportDeptBusDetailDao : onlineReportDeptDetailDao;
    }

    public List<Map> deptDetail(OnlineReportDeptDetailAnalysisRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setLang(request.getLang());
        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        if (StringUtils.equalsIgnoreCase("inter", request.getProductType())) {
            return Lists.newArrayList();
        }
        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum = SubQueryReportBuTypeEnum.getByOrdinal(request.getSubQueryBu());
        List<Map> list = getOnlineReportDeptDetailDao(baseQueryConditionDto).deptDetailAnalysis(analysisObjectEnum,
                baseQueryConditionDto, initPager(request.getPage()), subQueryReportBuTypeEnum, request.getAnalysisObjectOrgInfo(), (String) map.get("analysisObjectVal")
                , request.getProductType());
        fomratResultData(list);
        return list;
    }

    @Override
    public int count(OnlineReportDeptDetailAnalysisRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setLang(request.getLang());
        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum = SubQueryReportBuTypeEnum.getByOrdinal(request.getSubQueryBu());
        return getOnlineReportDeptDetailDao(baseQueryConditionDto).count(analysisObjectEnum, baseQueryConditionDto, subQueryReportBuTypeEnum,
                request.getAnalysisObjectOrgInfo(), (String) map.get("analysisObjectVal"), request.getProductType());
    }

    @Override
    public List<DeptStatisticalsEnum> getStatisticalList(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        List list1 = DeptStatisticalsEnum.getStaticalsByBizType(QueryReportBuTypeEnum.overview.toString(), subQueryReportBuTypeEnum);
        List list2 = DeptStatisticalsEnum.getStaticalsByBizType(QueryReportBuTypeEnum.bus.toString(), subQueryReportBuTypeEnum);
        list1.addAll(list2);
        return list1;
    }

    @Override
    public boolean matching(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return subQueryReportBuTypeEnum == SubQueryReportBuTypeEnum.BUS_TICKETS;

    }


    public boolean match(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return subQueryReportBuTypeEnum == SubQueryReportBuTypeEnum.BUS_TICKETS;
    }
}
