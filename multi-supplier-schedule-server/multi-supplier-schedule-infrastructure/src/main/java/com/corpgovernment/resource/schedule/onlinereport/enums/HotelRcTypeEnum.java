package com.corpgovernment.resource.schedule.onlinereport.enums;


import com.corpgovernment.resource.schedule.onlinereport.enums.travelposition.RcDefaultType;

/*
 * <AUTHOR>
 *
 * @date 2022/4/6 16:10
 *
 * @Desc 酒店rc
 */
public enum HotelRcTypeEnum implements RcDefaultType {
    // 低价RC
    L("ComplianceMonitor.lowpricerc", "reason_code"),
    // 最低价RC
//    N("ComplianceMonitor.lowestpricerc"),
    // 协议RC
    A("ComplianceMonitor.pactrc", "agreement_rc"),
    ALL(DEFAULT_SHARK_KEY, DEFAULT_FILED_CODE),
    ;

    private String sharkKey;

    private String filedCode;

    HotelRcTypeEnum(String sharkKey, String filedCode) {
        this.sharkKey = sharkKey;
        this.filedCode = filedCode;
    }

    HotelRcTypeEnum(String s) {
        this.sharkKey = s;
    }

    public static HotelRcTypeEnum getEnum(String rcType) {
        for (HotelRcTypeEnum value : values()) {
            if (value.toString().equalsIgnoreCase(rcType)) {
                return value;
            }
        }
        return null;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public String getFiledCode() {
        return filedCode;
    }
}
