package com.corpgovernment.resource.schedule.hotel.dao.mysql.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "ms_supplier_hotel_static_resource")
public class SupplierHotelStaticResourceDo {

    /**
     * 自增主键
     */
    @Id
    private Long id;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 国内酒店1海外酒店0
     */
    private Boolean atDomestic;

    /**
     * 酒店id
     */
    private String hotelId;

    /**
     * 酒店名称
     */
    private String name;

    private String nameEn;

    /**
     * 酒店logo
     */
    private String logoUrl;

    /**
     * 酒店地址
     */
    private String address;

    private String addressEn;

    /**
     * 酒店挂牌星级(0-5)
     */
    private String starRate;

    /**
     * 酒店推荐星级(0-5)
     */
    private String recommendStarRate;

    /**
     * 酒店评分
     */
    private String reviewScore;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 经度
     */
    private Double latitudeBaiDu;

    /**
     * 纬度
     */
    private Double longitudeBaiDu;

    /**
     * 经度
     */
    private Double latitudeGaoDe;

    /**
     * 纬度
     */
    private Double longitudeGaoDe;

    /**
     * 经度
     */
    private Double latitudeGoogle;

    /**
     * 纬度
     */
    private Double longitudeGoogle;

    /**
     * 国家id
     */
    private String countryId;

    /**
     * 省id
     */
    private String provinceId;

    /**
     * 城市id
     */
    private String cityId;

    /**
     * 城市名
     */
    private String cityName;

    /**
     * 行政区id
     */
    private String districtId;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后修改时间
     */
    private Date updateTime;

}
