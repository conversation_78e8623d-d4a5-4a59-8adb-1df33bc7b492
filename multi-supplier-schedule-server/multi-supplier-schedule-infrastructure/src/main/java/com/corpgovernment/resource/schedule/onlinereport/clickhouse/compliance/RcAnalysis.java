package com.corpgovernment.resource.schedule.onlinereport.clickhouse.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValDataType;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcTrend;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcView;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcViewBu;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcViewCount;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RcViewReason;
import com.corpgovernment.resource.schedule.onlinereport.dept.MatchBean;

import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 *
 * @date 2022/4/6 19:10
 *
 * @Desc
 */
public interface RcAnalysis<T> extends MatchBean<QueryReportBuTypeEnum> {

    // rc 概览
    abstract RcView getRcView(T request, String orderType) throws Exception;

    // rc占比
    abstract RcViewCount getRcPercent(T request, String orderType) throws Exception;

    // rc分析明细
    abstract List<RcViewReason> getRcViewReason(T request, String orderType, String lang) throws Exception;

    // rc产线分布
    abstract RcViewBu getRcViewBu(T request, String orderType) throws Exception;

    abstract List<RcTrend> getRcTrend(T request, QueryReportAggDateDimensionEnum dateDimensionEnum,
                                      String orderType) throws Exception;

    List<Map> deptDetail(T request, AnalysisObjectEnum analysisObjectEnum, Pager pager, String orderType, String user)
        throws Exception;

    int count(T request, AnalysisObjectEnum analysisObjectEnum, String orderType, String user) throws Exception;

    List<HeaderKeyValDataType> getHearder(AnalysisObjectEnum analysisObjectEnum, String lang,
                                          QueryReportBuTypeEnum queryReportBuTypeEnum);

}
