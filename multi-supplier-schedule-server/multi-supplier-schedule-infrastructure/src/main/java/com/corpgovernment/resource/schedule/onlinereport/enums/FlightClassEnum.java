package com.corpgovernment.resource.schedule.onlinereport.enums;

import org.apache.commons.lang.StringUtils;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums
 * @description:机票类型
 * @author: md_wang
 * @create: 2022-08-18 16:36
 **/
public enum FlightClassEnum {
    /**
     * 国内
     */
    N("dom", "国内"),
    /**
     * 国际
     */
    I("inter", "国际");

    private String classType;
    private String className;

    FlightClassEnum(String classType, String className) {
        this.classType = classType;
        this.className = className;
    }

    public static FlightClassEnum getFlightClassEnum(String classType) {
        if (StringUtils.isEmpty(classType)) {
            return null;
        }
        FlightClassEnum[] enums = FlightClassEnum.values();
        for (FlightClassEnum classEnum : enums) {
            if (classEnum.getClassType().equals(classType)) {
                return classEnum;
            }
        }
        return null;
    }

    public String getClassType() {
        return classType;
    }

    public void setClassType(String classType) {
        this.classType = classType;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }
}
