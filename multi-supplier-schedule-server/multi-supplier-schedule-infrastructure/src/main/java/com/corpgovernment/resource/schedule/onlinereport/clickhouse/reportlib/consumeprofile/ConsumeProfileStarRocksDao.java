package com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.consumeprofile;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractCommonDao;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-31 14:52
 * @desc
 */
@Repository
public class ConsumeProfileStarRocksDao extends AbstractCommonDao {

    private static final int YOY_OFFSET = 12;

    private static final String CONSUME_PROFILE_SQL = "SELECT if(crt.date != '', crt.date\n" +
            "    , if(mom.date != '', subStr(CAST(add_Months(cast(mom.date as date), %d) AS STRING), 1, %d), " +
            "    subStr(date_format(add_Months(cast(yoy.date as date), 12), '%Y-%m-%d %H:%i:%s'), 1, %d))) AS aggDate\n" +
            "    , if(crt.dim != '', crt.dim, if(mom.dim != '', mom.dim, yoy.dim)) AS orderType\n" +
            "    , sum(coalesce(crt.totalAmount, 0)) AS totalCurrentAmount\n" +
            "    , sum(coalesce(mom.totalAmount, 0)) AS totalMomAmount\n" +
            "    , sum(coalesce(yoy.totalAmount, 0)) AS totalYoyAmount\n" +
            "FROM (\n" +
            "    SELECT %s AS date, %s AS dim, CAST(SUM(%s) AS DOUBLE) AS totalAmount FROM %s WHERE %s %s GROUP BY date, dim ) crt\n" +
            "    FULL JOIN ( SELECT %s AS date, %s AS dim, CAST(SUM(%s) AS DOUBLE) AS totalAmount FROM %s WHERE %s %s GROUP BY date, dim ) mom\n" +
            "    ON crt.date = subStr(CAST(add_Months(cast(mom.date as date), %d) AS STRING), 1, %d) AND crt.dim = mom.dim\n" +
            "    FULL JOIN ( SELECT %s AS date, %s AS dim, CAST(SUM(%s) AS DOUBLE) AS totalAmount FROM %s WHERE %s %s GROUP BY date, dim ) yoy\n" +
            "    ON crt.date = subStr(date_format(add_Months(cast(yoy.date as date), 12), '%Y-%m-%d %H:%i:%s'), 1, %d) AND crt.dim = yoy.dim" +
            " group by aggDate, orderType";

    private static final String BUS_CONSUME_PROFILE_SQL = "SELECT if(crt.date != '', crt.date\n" +
            "    , if(mom.date != '', subStr(CAST(add_Months(cast(mom.date as date), %d) AS STRING), 1, %d), " +
            "    subStr(CAST(add_Months(cast(yoy.date as date), 12) AS STRING), 1, %d))) AS aggDate\n" +
            "    , if(crt.dim != '', crt.dim, if(mom.dim != '', mom.dim, yoy.dim)) AS orderType\n" +
            "    , sum(coalesce(crt.totalAmount, 0)) AS totalCurrentAmount\n" +
            "    , sum(coalesce(mom.totalAmount, 0)) AS totalMomAmount\n" +
            "    , sum(coalesce(yoy.totalAmount, 0)) AS totalYoyAmount\n" +
            "FROM (\n" +
            "    SELECT %s AS date, %s AS dim, CAST(SUM(%s) AS DOUBLE) AS totalAmount FROM %s WHERE %s GROUP BY date, dim ) crt\n" +
            "    FULL JOIN ( SELECT %s AS date, %s AS dim, CAST(SUM(%s) AS DOUBLE) AS totalAmount\n" +
            "    FROM %s WHERE %s GROUP BY date, dim ) mom\n" +
            "    ON crt.date = subStr(CAST(add_Months(cast(mom.date as date), %d) AS STRING), 1, %d) AND crt.dim = mom.dim\n" +
            "    FULL JOIN ( SELECT %s AS date, %s AS dim, CAST(SUM(%s) AS DOUBLE) AS totalAmount\n" +
            "    FROM %s WHERE %s GROUP BY date, dim ) yoy\n" +
            "    ON crt.date = subStr(CAST(add_Months(cast(yoy.date as date), 12) AS STRING), 1, %d) AND crt.dim = yoy.dim" +
            " group by aggDate, orderType";

    /**
     * 查询 在线报告概览
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationFlight(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> momTimes, List<String> yoyTimes,
                                        QueryReportAggDateDimensionEnum dimensionEnum)
            throws Exception {
        ClickHouseTable clickHouseTable = null;
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL_ODT;
        } else {
            // 默认使用成交口径
            clickHouseTable = ClickHouseTable.ADM_INDEX_FLIGHT_PRICE_DETAIL;
        }
        List<Object> parmList = new ArrayList<>();
        String partion = queryPartition(clickHouseTable);
        String groupDateField = getGroupDateFieldBy(dimensionEnum);
        int momOffset = momOffset(dimensionEnum);
        String currentCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion);
        String momCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, momTimes.get(0), momTimes.get(1), partion);
        String yoyCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, yoyTimes.get(0), yoyTimes.get(1), partion);
        int subLength = getSubLength(dimensionEnum);
        String sumField = "real_pay";
        String sql = String.format(CONSUME_PROFILE_SQL, momOffset, subLength, subLength,
                groupDateField, "flight_class", sumField, clickHouseTable.getTable(), currentCondition, StringUtils.EMPTY,
                groupDateField, "flight_class", sumField, clickHouseTable.getTable(), momCondition, StringUtils.EMPTY, momOffset, subLength,
                groupDateField, "flight_class", sumField, clickHouseTable.getTable(), yoyCondition, StringUtils.EMPTY, subLength);
        return commonList(clazz, sql, parmList);
    }

    /**
     * 查询 在线报告概览
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationHotel(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> momTimes, List<String> yoyTimes,
                                       QueryReportAggDateDimensionEnum dimensionEnum) throws Exception {
        ClickHouseTable clickHouseTable = null;
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL_ODT;
        } else {
            // 默认使用成交口径
            clickHouseTable = ClickHouseTable.ADM_INDEX_HOTEL_PRICE_DETAIL;
        }
        List<Object> parmList = new ArrayList<>();
        String partion = queryPartition(clickHouseTable);
        String groupDateField = getGroupDateFieldBy(dimensionEnum);
        int momOffset = momOffset(dimensionEnum);
        String currentCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion);
        String momCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, momTimes.get(0), momTimes.get(1), partion);
        String yoyCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, yoyTimes.get(0), yoyTimes.get(1), partion);
        int subLength = getSubLength(dimensionEnum);
        String sumField = "real_pay";
        String orderstatus = String.format("and order_status = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus"));
        String sql = String.format(CONSUME_PROFILE_SQL, momOffset, subLength, subLength,
                groupDateField, "order_type", sumField, clickHouseTable.getTable(), currentCondition, orderstatus,
                groupDateField, "order_type", sumField, clickHouseTable.getTable(), momCondition, orderstatus, momOffset, subLength,
                groupDateField, "order_type", sumField, clickHouseTable.getTable(), yoyCondition, orderstatus, subLength);
        return commonList(clazz, sql, parmList);
    }

    /**
     * 查询 在线报告概览
     *
     * @param requestDto
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationTrain(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> momTimes, List<String> yoyTimes,
                                       QueryReportAggDateDimensionEnum dimensionEnum) throws Exception {
        ClickHouseTable clickHouseTable = null;
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL_ODT;
        } else {
            // 默认使用成交口径
            clickHouseTable = ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL;
        }
        List<Object> parmList = new ArrayList<>();
        String partion = queryPartition(clickHouseTable);
        String groupDateField = getGroupDateFieldBy(dimensionEnum);
        int momOffset = momOffset(dimensionEnum);
        String currentCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion);
        String momCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, momTimes.get(0), momTimes.get(1), partion);
        String yoyCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, yoyTimes.get(0), yoyTimes.get(1), partion);
        int subLength = getSubLength(dimensionEnum);
        String sumField = "real_pay";
        String sql = String.format(CONSUME_PROFILE_SQL, momOffset, subLength, subLength,
                groupDateField, "''", sumField, clickHouseTable.getTable(), currentCondition, StringUtils.EMPTY,
                groupDateField, "''", sumField, clickHouseTable.getTable(), momCondition, StringUtils.EMPTY, momOffset, subLength,
                groupDateField, "''", sumField, clickHouseTable.getTable(), yoyCondition, StringUtils.EMPTY, subLength);
        return commonList(clazz, sql, parmList);
    }

    /**
     * 查询 在线报告概览
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationCar(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> momTimes, List<String> yoyTimes,
                                     QueryReportAggDateDimensionEnum dimensionEnum) throws Exception {
        ClickHouseTable clickHouseTable = null;
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL_ODT;
        } else {
            // 默认使用成交口径
            clickHouseTable = ClickHouseTable.ADM_INDEX_CAR_PRICE_DETAIL;
        }
        List<Object> parmList = new ArrayList<>();
        String partion = queryPartition(clickHouseTable);
        String groupDateField = getGroupDateFieldBy(dimensionEnum);
        int momOffset = momOffset(dimensionEnum);
        String currentCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion);
        String momCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, momTimes.get(0), momTimes.get(1), partion);
        String yoyCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, yoyTimes.get(0), yoyTimes.get(1), partion);
        int subLength = getSubLength(dimensionEnum);
        String sumField = "real_pay";
        String sql = String.format(CONSUME_PROFILE_SQL, momOffset, subLength, subLength,
                groupDateField, "CAST(order_type AS STRING)", sumField, clickHouseTable.getTable(), currentCondition, StringUtils.EMPTY,
                groupDateField, "CAST(order_type AS STRING)", sumField, clickHouseTable.getTable(), momCondition, StringUtils.EMPTY, momOffset, subLength,
                groupDateField, "CAST(order_type AS STRING)", sumField, clickHouseTable.getTable(), yoyCondition, StringUtils.EMPTY, subLength);
        return commonList(clazz, sql, parmList);
    }

    /**
     * 查询 在线报告概览
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationBusOrVas(BaseQueryConditionDTO requestDto, Class<T> clazz, List<String> momTimes, List<String> yoyTimes,
                                          QueryReportAggDateDimensionEnum dimensionEnum, QueryReportBuTypeEnum buTypeEnum) throws Exception {
        ClickHouseTable clickHouseTable = null;
        if (StringUtils.equalsIgnoreCase(requestDto.getStatisticalCaliber(), StatiscalCaliberEnum.BOOKING.name())) {
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT;
        } else {
            // 默认使用成交口径
            clickHouseTable = ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY;
        }
        List<Object> parmList = new ArrayList<>();
        String partion = queryPartition(clickHouseTable);
        String groupDateField = getGroupDateFieldBy(dimensionEnum);
        int momOffset = momOffset(dimensionEnum);
        String currentCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, requestDto.getStartTime(), requestDto.getEndTime(), partion);
        String momCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, momTimes.get(0), momTimes.get(1), partion);
        String yoyCondition = BaseConditionPrebuilder.buildPreSql(requestDto, parmList, yoyTimes.get(0), yoyTimes.get(1), partion);
        String sumField = "amount_bus";
        if (buTypeEnum == QueryReportBuTypeEnum.bus) {
            sumField = "amount_bus";
        } else {
            sumField = "amount_vas";
        }
        int subLength = getSubLength(dimensionEnum);
        String sql = String.format(BUS_CONSUME_PROFILE_SQL, momOffset, subLength, subLength,
                groupDateField, "''", sumField, clickHouseTable.getTable(), currentCondition,
                groupDateField, "''", sumField, clickHouseTable.getTable(), momCondition, momOffset, subLength,
                groupDateField, "''", sumField, clickHouseTable.getTable(), yoyCondition, subLength);
        return commonList(clazz, sql, parmList);
    }

    /**
     * 获得趋势聚合字段
     *
     * @param dateDimensionEnum
     * @return
     */
    protected String getGroupDateFieldBy(QueryReportAggDateDimensionEnum dateDimensionEnum) {
        String groupField = StringUtils.EMPTY;
        switch (dateDimensionEnum) {
            case month:
                groupField = " firstday_of_month ";
                break;
            case quarter:
                groupField = " firstday_of_quarter ";
                break;
            case half:
                groupField = " firstday_of_halfyear ";
                break;
            case year:
                groupField = " subString(firstday_of_halfyear, 1, 4) ";
                break;
            default:
                break;
        }
        return groupField;
    }

    /**
     * 环比偏移量
     *
     * @param dateDimensionEnum
     * @return
     */
    protected int momOffset(QueryReportAggDateDimensionEnum dateDimensionEnum) {
        int offset = 0;
        switch (dateDimensionEnum) {
            case month:
                offset = 1;
                break;
            case quarter:
                offset = 3;
                break;
            case half:
                offset = 6;
                break;
            case year:
                offset = 12;
                break;
            default:
                break;
        }
        return offset;
    }

    protected int getSubLength(QueryReportAggDateDimensionEnum dateDimensionEnum) {
        int subLength = 10;
        switch (dateDimensionEnum) {
            case year:
                subLength = 4;
                break;
            default:
                break;
        }
        return subLength;
    }

}
