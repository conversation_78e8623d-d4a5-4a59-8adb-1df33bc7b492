/*
package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


*/
/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 *//*

@Repository
public class OnlineReportDeptTrainDetailDao extends AbstractOnlineReportDeptDetailDao {

    */
/**
     * 统计字段
     *
     * @return
     *//*

    @Override
    protected String statical() {
        List sql = new ArrayList();
        sql.add("sum(coalesce(real_pay, 0)) as TOTAL_REAL_PAY");
        sql.add("sum(coalesce(quantity, 0)) as TOTAL_QUANTITY");
        sql.add("sum(coalesce(ordertkt, 0)) as TOTAL_ORDERTKT");
        sql.add("sum(coalesce(refund_quantity, 0)) as TOTAL_REFUND_QUANTITY");
        sql.add("sum(coalesce(change_quantity, 0)) as TOTAL_CHANGE_QUANTITY");
        sql.add("sum(coalesce(ticket_price, 0)) as TOTAL_TICKET_PRICE");
        sql.add("sum(coalesce(changebalance, 0)) as TOTAL_CHANGEBALANCE");
        sql.add("sum(coalesce(refund_ticket_fee, 0)) as TOTAL_REFUND_TICKET_FEE");
        sql.add("sum(case when seat_type = 'YDZ' then quantity else 0 end) as TOTAL_TRAIN_FIRST_CLASS_QUANTITY");
        sql.add("sum(case when seat_type = 'EDZ' then quantity else 0 end) as TOTAL_TRAIN_SECOND_CLASS_QUANTITY");
        sql.add("sum(case when seat_type = 'SWZ' then quantity else 0 end) as TOTAL_TRAIN_BUSINESS_CLASS_QUANTITY");
        sql.add("sum(case when seat_type = 'TDZ' then quantity else 0 end) as TOTAL_TRAIN_PRINCE_CLASS_QUANTITY");
        sql.add("sum(case when seat_type = 'YW' then quantity else 0 end) as TOTAL_TRAIN_HARD_SLEEPER_QUANTITY");
        sql.add("sum(case when seat_type = 'YZ' then quantity else 0 end) as TOTAL_TRAIN_HARD_SEAT_QUANTITY");
        sql.add("sum(case when seat_type = 'RW' then quantity else 0 end) as TOTAL_TRAIN_SOFT_SLEEPER_QUANTITY");
        sql.add("sum(case when seat_type = 'DW' then quantity else 0 end) as TOTAL_TRAIN_MOVING_SLEEPER_QUANTITY");
        sql.add("sum(case when seat_type = 'RZ' then quantity else 0 end) as TOTAL_TRAIN_SOFT_SEAT_QUANTITY");
        sql.add("sum(case when seat_type = 'WZ' then quantity else 0 end) as TOTAL_TRAIN_STANDING_TICKET_QUANTITY");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    @Override
    protected String totalField() {
        return "SUM(coalesce(real_pay, 0)) AS TOTAL_REAL_PAY, SUM(coalesce(quantity, 0)) AS TOTAL_QUANTITY";
    }

    */
/**
     * 同环比，总计数据
     *
     * @return
     *//*

    @Override
    protected String momAndYoy() {
        String sql = StringUtils.EMPTY;
        return "sum(coalesce(real_pay, 0))  as TOTAL_REAL_PAY";
    }

    */
/**
     * 返回字段
     *
     * @return
     *//*

    @Override
    protected String baseQueryField() {
        List sql = new ArrayList();
        sql.add("round(crt.TOTAL_REAL_PAY, 4) AS REAL_PAY");
        sql.add("round(case when coalesce(total.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(crt.TOTAL_REAL_PAY, 0))"
                + ", toFloat64(coalesce(total.TOTAL_REAL_PAY, 0))) * 100 else 0 end, 4) as REAL_PAY_PERCENT");
        sql.add("round(case when coalesce(yoy.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(crt.TOTAL_REAL_PAY, 0) - coalesce(yoy.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(yoy.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_LAST");
        sql.add("round(case when coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(crt.TOTAL_REAL_PAY, 0) - coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_BEFORE_LAST");
        sql.add("round(case when coalesce(mom.TOTAL_REAL_PAY, 0) !=0 "
                + "then divide(toFloat64(coalesce(crt.TOTAL_REAL_PAY, 0) - coalesce(mom.TOTAL_REAL_PAY, 0)) "
                + ", toFloat64(abs(coalesce(mom.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as MOM");
        sql.add("crt.TOTAL_QUANTITY AS TRAIN_QUANTITY");
        sql.add(
                "round(case when coalesce(total.TOTAL_QUANTITY, 0) !=0 " + "then divide(coalesce(crt.TOTAL_QUANTITY, 0)"
                        + ", coalesce(total.TOTAL_QUANTITY, 0)) * 100 else 0 end, 4) as TRAIN_QUANTITY_PERCENT");
        sql.add("round(case when coalesce(crt.TOTAL_QUANTITY, 0) !=0 "
                + "then divide(toFloat64(coalesce(crt.TOTAL_TICKET_PRICE, 0) + coalesce(crt.TOTAL_CHANGEBALANCE, 0) + coalesce(crt.TOTAL_REFUND_TICKET_FEE, 0))"
                + ",toFloat64( coalesce(crt.TOTAL_QUANTITY, 0))) else 0 end, 4) as TRAIN_AVG_PRICE");
        sql.add("round(case when coalesce(crt.TOTAL_ORDERTKT, 0) !=0 "
                + "then divide(coalesce(crt.TOTAL_CHANGE_QUANTITY, 0)"
                + ", coalesce(crt.TOTAL_ORDERTKT, 0)) * 100 else 0 end, 4) as TRAIN_REEBOOK_RATE");
        sql.add("round(case when coalesce(crt.TOTAL_ORDERTKT, 0) !=0 "
                + "then divide(coalesce(crt.TOTAL_REFUND_QUANTITY, 0)"
                + ", coalesce(crt.TOTAL_ORDERTKT, 0)) * 100 else 0 end, 4) as TRAIN_REFUND_RATE");
        sql.add("TOTAL_TRAIN_FIRST_CLASS_QUANTITY AS TRAIN_FIRST_CLASS_QUANTITY");
        sql.add("TOTAL_TRAIN_SECOND_CLASS_QUANTITY AS TRAIN_SECOND_CLASS_QUANTITY");
        sql.add("TOTAL_TRAIN_BUSINESS_CLASS_QUANTITY AS TRAIN_BUSINESS_CLASS_QUANTITY");
        sql.add("TOTAL_TRAIN_PRINCE_CLASS_QUANTITY AS TRAIN_PRINCE_CLASS_QUANTITY");
        sql.add("TOTAL_TRAIN_HARD_SLEEPER_QUANTITY AS TRAIN_HARD_SLEEPER_QUANTITY");
        sql.add("TOTAL_TRAIN_HARD_SEAT_QUANTITY AS TRAIN_HARD_SEAT_QUANTITY");
        sql.add("TOTAL_TRAIN_SOFT_SLEEPER_QUANTITY AS TRAIN_SOFT_SLEEPER_QUANTITY");
        sql.add("TOTAL_TRAIN_MOVING_SLEEPER_QUANTITY AS TRAIN_MOVING_SLEEPER_QUANTITY");
        sql.add("TOTAL_TRAIN_SOFT_SEAT_QUANTITY AS TRAIN_SOFT_SEAT_QUANTITY");
        sql.add("TOTAL_TRAIN_STANDING_TICKET_QUANTITY AS TRAIN_STANDING_TICKET_QUANTITY");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    @Override
    protected String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return StringUtils.EMPTY;
    }

    @Override
    protected String orderByField() {
        return "TOTAL_REAL_PAY desc";
    }

    @Override
    protected BaseConditionPrebuilder.TableAndTimeColBind getTargetTableAndTimeColumn(String statisticalCaliber, Boolean isForegin) {
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = new BaseConditionPrebuilder.TableAndTimeColBind();
        tableAndTimeColBind.setClickHouseTable(ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL);
        tableAndTimeColBind.setDateColumn(REPORT_DATE);
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            tableAndTimeColBind.setClickHouseTable(ClickHouseTable.ADM_INDEX_TRAIN_PRICE_DETAIL_ODT);
        }
        return tableAndTimeColBind;
    }


}
*/
