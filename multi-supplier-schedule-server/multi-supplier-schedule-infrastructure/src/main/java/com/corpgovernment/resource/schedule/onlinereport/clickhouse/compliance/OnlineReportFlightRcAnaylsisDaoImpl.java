package com.corpgovernment.resource.schedule.onlinereport.clickhouse.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTimesDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.compliance.RcTrendDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.DataTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.FlightRcEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 *
 * @date 2022/4/10 20:49
 *
 * @Desc
 */
@Service
@Slf4j
public class OnlineReportFlightRcAnaylsisDaoImpl extends AbstractOnlineReportTopRcAnaylsisDao {

    private static final String LOG_TITLE = "OnlineReportFlightRcAnaylsisDaoImpl";

    private static final String CORP_PAY = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.corpPayType");

    private static final String FLIGHT_RC_SQL = "SELECT %s, rcTimes as FLT_RC_TIMES"
            + ", round(case when coalesce(orderCount, 0) !=0  then divide(coalesce(rcTimes, 0), coalesce(orderCount, 0)) * 100 else 0 end, 2) as FLT_RC_PERCENT"
            + ", lowRcTimes as FLT_LOW_RC_TIMES \n"
            + ", round(toFloat64(saveAmount),2) as FLT_RC_SAVEAMOUNT "
            + ", classRcTimes as FLT_CLASS_RC_TIMES \n"
            + ", agreementRcTimes as FLT_AGREEMENT_RC_TIMES \n"
            + ", preRcTimes as FLT_PRE_RC_TIMES \n"
            + ", timeRcTimes as FLT_TIME_RC_TIMES \n"
            + ", refundRcTimes as FLT_REFUND_RC_TIMES \n"
            + ", discountRcTimes as FLT_DISCOUNT_RC_TIMES \n"
            + " FROM (SELECT %s \n"
            + ", COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount\n"
            + ", COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END) AS rcTimes \n"
            + ", COUNT(DISTINCT CASE WHEN is_refund = 'F' AND low_rid IS NOT NULL AND low_rid != '' THEN order_id  END) AS lowRcTimes\n"
            + ", COUNT(DISTINCT CASE WHEN is_refund = 'F' AND class_rid IS NOT NULL AND class_rid != '' THEN order_id  END) AS classRcTimes  \n"
            + ", COUNT(DISTINCT CASE WHEN is_refund = 'F' AND agreement_rid IS NOT NULL AND agreement_rid != '' THEN order_id  END) AS agreementRcTimes \n"
            + ", COUNT(DISTINCT CASE WHEN is_refund = 'F' AND pre_rid IS NOT NULL AND pre_rid != '' THEN order_id  END) AS preRcTimes \n"
            + ", COUNT(DISTINCT CASE WHEN is_refund = 'F' AND time_rid IS NOT NULL AND time_rid != '' THEN order_id  END) AS timeRcTimes\n"
            + ", COUNT(DISTINCT CASE WHEN is_refund = 'F' AND coalesce(custom_refund_reason_code ,'') != '' THEN order_id  END) AS refundRcTimes\n"
            + ", COUNT(DISTINCT CASE WHEN is_refund = 'F' AND coalesce(discount_reason_code ,'') != '' THEN order_id  END) AS discountRcTimes\n"
            + ", SUM(CASE WHEN  fee_type = '" + CORP_PAY + "' AND audited <> 'F' AND %s "
            + "  AND  is_refund = 'F' THEN over_std_esti_save_amount END) AS saveAmount"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s  GROUP BY %s\n"
            + "    HAVING (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0 ) f ORDER BY f.rcTimes DESC";

    private static final String FLIGHT_RC_COUNT_SQL = "SELECT count(1) as countAll FROM (\n"
            + "    SELECT %s,  COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END) AS rcTimes"
            + "    FROM olrpt_indexflightdownload_all WHERE %s %s  GROUP BY %s\n"
            + "    HAVING (COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END)) != 0) f ";

    @Override
    public String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum,
                           BaseQueryConditionDTO baseQueryConditionDto, Pager pager, String orderType, String user) {
        StringBuffer baseSql = new StringBuffer();
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        ClickHouseTable clickHouseTable = getTargetTable();
        String partion = queryPartition(clickHouseTable);
        AbstractOnlineReportTopRcAnaylsisDao.JoinCondition biz = joinCondition(analysisObjectEnum);
        baseSql.append(String.format(FLIGHT_RC_SQL, biz.getResultFields(), biz.getGroupFields(),
                BaseConditionPrebuilder.buildFlightSaveAmount(parmList, startTime, endTime),
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion) + buildPreSqlUser(user, parmList),
                getFlightClassCondition(orderType), biz.getGroupFields()));
        baseSql.append(" limit ?, ? ");
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        return baseSql.toString();
    }

    @Override
    protected ClickHouseTable getTargetTable() {
        return ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
    }

    @Override
    public String countSql(List<Object> parmList, ClickHouseTable clickHouseTable,
                           AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, String orderType, String user) {
        StringBuffer baseSql = new StringBuffer();
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String partion = queryPartition(clickHouseTable);
        String currentCondition =
                BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion) + buildPreSqlUser(user, parmList);
        AbstractOnlineReportTopRcAnaylsisDao.JoinCondition biz = joinCondition(analysisObjectEnum);
        baseSql.append(String.format(FLIGHT_RC_COUNT_SQL, biz.getGroupFields(), currentCondition,
                getFlightClassCondition(orderType), biz.getGroupFields()));
        return baseSql.toString();
    }

    @Override
    protected String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return null;
    }


    /**
     * rc占比
     *
     * @param requestDto
     * @param flightClass
     * @return
     * @throws Exception sum( case when is_rc ='T' then coalesce(netfare, 0)-coalesce(corp_price_adj, 0) else 0 end)
     */
    
    public List<RcTimesDTO> aggreationRcView(BaseQueryConditionDTO requestDto, String flightClass) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append(" COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + ", COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
                + ", COUNT(DISTINCT CASE WHEN is_rc != 'T' AND is_refund = 'F' THEN order_id  END) AS noRcTimes ");
        // 超标损失
        sqlBuilder.append(", round(sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end), 2) as lossPrice \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        sqlBuilder.append(getFlightClassCondition(flightClass));
        log.info("aggreationRcView sqlBuilder is {} parmList is {}",sqlBuilder , JsonUtils.toJsonString(parmList));
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTimesDTO.class, "aggreationRcView");
    }

    /**
     * rc占比
     *
     * @param startTime
     * @param endTime
     * @param flightClass
     * @param industryList
     * @param dataTypeEnum
     * @param compareSameLevel 是否对比同级比较
     * @param consumptionLevel 消费等级
     * @return
     * @throws Exception sum( case when is_rc ='T' then coalesce(netfare, 0)-coalesce(corp_price_adj, 0) else 0 end)
     */

    
    public List<RcTimesDTO> aggreationRcViewCorpAndIndustry(String startTime, String endTime, String flightClass,
                                                            List<String> industryList, DataTypeEnum dataTypeEnum,
                                                            String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append(" COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + ", COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
                + ", COUNT(DISTINCT CASE WHEN is_rc != 'T' AND is_refund = 'F' THEN order_id  END) AS noRcTimes ");
        // 超标损失
        sqlBuilder.append(", round(sum(case when is_refund = 'F' then coalesce(over_std_esti_save_amount, 0) else 0 end), 2) as lossPrice \n");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        boolean ignoreTenantId = false;
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
            ignoreTenantId = true;
        }
        sqlBuilder.append(getFlightClassCondition(flightClass));
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTimesDTO.class, "aggreationRcViewCorpAndIndustry", ignoreTenantId);
    }

    /**
     * rc原因
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationRcViewReason(BaseQueryConditionDTO requestDto, Class<T> clazz, String flightClass)
            throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select ");
        sqlBuilder.append(" COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + "        , COUNT(DISTINCT CASE WHEN is_rc = 'T' AND is_refund = 'F' THEN order_id  END) AS rcTimes\n"
                + "        , COUNT(DISTINCT CASE WHEN is_rc != 'T' AND is_refund = 'F' THEN order_id  END) AS noRcTimes\n"
                + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND low_rid IS NOT NULL AND low_rid != '' THEN order_id  END) AS lowRcTimes\n"
                + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND class_rid IS NOT NULL AND class_rid != '' THEN order_id  END)  AS classRcTimes \n"
                + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND agreement_rid IS NOT NULL AND agreement_rid != '' THEN order_id  END)  AS agreementRcTimes\n"
                + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND pre_rid IS NOT NULL AND pre_rid != '' THEN order_id  END) AS preRcTimes \n"
                + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND time_rid IS NOT NULL AND time_rid != '' THEN order_id  END) AS timeRcTimes "
                + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND coalesce(custom_refund_reason_code ,'') != '' THEN order_id  END) AS refundRcTimes "
                + "        , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND coalesce(discount_reason_code ,'') != '' THEN order_id  END) AS discountRcTimes ");
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        sqlBuilder.append(getFlightClassCondition(flightClass));
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationRcViewReason");
    }

    /**
     * rc原因明细
     *
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> List<T> aggreationRcViewReasonDetail(BaseQueryConditionDTO requestDto, Class<T> clazz,
                                                    String flightClass, FlightRcEnum flightRcEnum, String lang) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        String sql = StringUtils.EMPTY;
        String groupField = StringUtils.EMPTY;
        String rcCondition = StringUtils.EMPTY;
        boolean isEn = SharkUtils.isEN(lang);
        switch (flightRcEnum) {
            case L:
                // low_rc low_rc_en
                sql =
                        "low_rid as rcCode, " + (isEn ? "low_rc_en " : "low_rc") + " as rcDesc, COUNT(DISTINCT CASE WHEN is_refund = 'F' " +
                                " AND low_rid IS NOT NULL AND low_rid != '' " +
                                " THEN order_id  END) AS rcTimes";
                groupField = "low_rid, " + (isEn ? "low_rc_en " : "low_rc");
                rcCondition = "low_rid is not null and low_rid != ''";
                break;
            case H:
                // agreement_rc agreement_rc_en
                sql =
                        "agreement_rid as rcCode, " + (isEn ? "agreement_rc_en " : "agreement_rc") + " as rcDesc, COUNT(DISTINCT CASE WHEN is_refund = 'F' " +
                                " AND agreement_rid IS NOT NULL AND agreement_rid != '' " +
                                " THEN order_id  END) AS rcTimes";
                groupField = "agreement_rid, " + (isEn ? "agreement_rc_en " : "agreement_rc");
                rcCondition = "agreement_rid is not null and agreement_rid != ''";
                break;
            case P:
                // pre_rc pre_rc_en
                sql =
                        "pre_rid as rcCode, " + (isEn ? "pre_rc_en " : "pre_rc") + " as rcDesc, COUNT(DISTINCT CASE WHEN is_refund = 'F' " +
                                " AND pre_rid IS NOT NULL AND pre_rid != '' " +
                                " THEN order_id  END) AS rcTimes";
                groupField = "pre_rid, " + (isEn ? "pre_rc_en " : "pre_rc");
                rcCondition = "pre_rid is not null and pre_rid != ''";
                break;
            case C:
                // class_rc class_rc_en
                sql =
                        "class_rid as rcCode, " + (isEn ? "class_rc_en " : "class_rc") + " as rcDesc, COUNT(DISTINCT CASE WHEN is_refund = 'F' " +
                                " AND class_rid IS NOT NULL AND class_rid != '' " +
                                " THEN order_id  END) rcTimes";
                groupField = "class_rid, " + (isEn ? "class_rc_en " : "class_rc");
                rcCondition = "class_rid is not null and class_rid != ''";
                break;
            case T:
                // time_rc time_rc_en
                sql =
                        "time_rid as rcCode, " + (isEn ? "time_rc_en " : "time_rc") + " as rcDesc, COUNT(DISTINCT CASE WHEN is_refund = 'F' " +
                                " AND time_rid IS NOT NULL AND time_rid != '' " +
                                " THEN order_id  END) rcTimes";
                groupField = "time_rid, " + (isEn ? "time_rc_en " : "time_rc");
                rcCondition = "time_rid is not null and time_rid != ''";
                break;
            case R:
                // time_rc time_rc_en
                sql =
                        "custom_refund_reason_code as rcCode, custom_refund_reason_code_desc as rcDesc, COUNT(DISTINCT CASE WHEN is_refund = 'F' " +
                                " AND coalesce(custom_refund_reason_code ,'') != '' " +
                                " THEN order_id  END) rcTimes";
                groupField = "custom_refund_reason_code, custom_refund_reason_code_desc";
                rcCondition = "coalesce(custom_refund_reason_code ,'') != ''";
                break;
            case D:
                // discount_reason_code custom_discount_reason
                sql =
                        "discount_reason_code as rcCode, custom_discount_reason as rcDesc, COUNT(DISTINCT CASE WHEN is_refund = 'F' " +
                                " AND coalesce(discount_reason_code ,'') != '' " +
                                " THEN order_id  END) rcTimes";
                groupField = "discount_reason_code, custom_discount_reason";
                rcCondition = "coalesce(discount_reason_code ,'') != ''";
                break;
            default:
                break;
        }
        sqlBuilder.append("select ");
        sqlBuilder.append(sql);
        sqlBuilder.append(" from ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        sqlBuilder.append(getFlightClassCondition(flightClass));
        sqlBuilder.append(" and ");
        sqlBuilder.append(rcCondition);
        sqlBuilder.append(" group by ");
        sqlBuilder.append(groupField);

        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "aggreationFlightWithCondition");
    }

    /**
     * rc趋势
     *
     * @param requestDto
     * @param flightClass
     * @param dateDimensionEnum
     * @return
     * @throws Exception
     */
    public List<RcTrendDTO> aggreationRcTrend(BaseQueryConditionDTO requestDto, String flightClass, QueryReportAggDateDimensionEnum dateDimensionEnum) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        Map map = getGroupFieldBy(dateDimensionEnum);
        String sql = (String) map.get(TREND_DIM_KEY);
        String groupField = (String) map.get(TREND_GROUP_KEY);
        sqlBuilder.append("select ");
        sqlBuilder.append(sql);
        sqlBuilder.append("  , COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + " , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END) AS rcTimes ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        sqlBuilder.append(BaseConditionPrebuilder.buildPreSql(requestDto, parmList));
        sqlBuilder.append(getFlightClassCondition(flightClass));
        sqlBuilder.append(" group by");
        sqlBuilder.append(groupField);
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTrendDTO.class, "aggreationRcTrend");
    }

    /**
     * rc趋势
     *
     * @param startTime
     * @param endTime
     * @param flightClass
     * @param dateDimensionEnum
     * @param industryList
     * @param dataTypeEnum
     * @param compareSameLevel  是否对比同级比较
     * @param consumptionLevel  消费等级
     * @return
     * @throws Exception
     */
    
    public List<RcTrendDTO> aggreationRcTrendCorpAndIndustry(String startTime, String endTime, String flightClass, QueryReportAggDateDimensionEnum dateDimensionEnum,
                                                             List<String> industryList, DataTypeEnum dataTypeEnum,
                                                             String compareSameLevel, String consumptionLevel, String compareCorpSameLevel) throws Exception {
        StringBuilder sqlBuilder = new StringBuilder();
        Map map = getGroupFieldBy(dateDimensionEnum);
        String sql = (String) map.get(TREND_DIM_KEY);
        String groupField = (String) map.get(TREND_GROUP_KEY);
        sqlBuilder.append("select ");
        sqlBuilder.append(sql);
        sqlBuilder.append("  , COUNT(DISTINCT CASE WHEN is_refund = 'F' THEN order_id  END) AS orderCount \n"
                + " , COUNT(DISTINCT CASE WHEN is_refund = 'F' AND is_rc = 'T' THEN order_id  END) AS rcTimes ");
        sqlBuilder.append(" from ");
        sqlBuilder.append(getTargetTable().getTable());
        sqlBuilder.append(OrpConstants.WHERE);
        sqlBuilder.append("d = ?");
        List<Object> parmList = new ArrayList<>();
        if (DataTypeEnum.INDUSTRY == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildIndustryPreSql(startTime, endTime, industryList, parmList));
            if (StringUtils.isNotEmpty(compareSameLevel) && StringUtils.equalsIgnoreCase(compareSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        } else if (DataTypeEnum.CORP == dataTypeEnum) {
            sqlBuilder.append(BaseConditionPrebuilder.buildCorpPreSql(startTime, endTime, parmList));
            if (StringUtils.isNotEmpty(compareCorpSameLevel) && StringUtils.equalsIgnoreCase(compareCorpSameLevel, "T") && StringUtils.isNotEmpty(consumptionLevel)) {
                sqlBuilder.append(" and consumptionlevel = '" + consumptionLevel + "' ");
            }
        }
        sqlBuilder.append(getFlightClassCondition(flightClass));
        sqlBuilder.append(" group by");
        sqlBuilder.append(groupField);
        // 查询clickhouse
        return queryBySql(sqlBuilder.toString(), parmList,
                (req, statement) -> mapCommonRequest(parmList, statement, getTargetTable()), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, RcTrendDTO.class, "aggreationRcTrendCorpAndIndustry");
    }
}
