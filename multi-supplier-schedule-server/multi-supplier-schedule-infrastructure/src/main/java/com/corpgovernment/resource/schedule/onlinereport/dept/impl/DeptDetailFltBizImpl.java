package com.corpgovernment.resource.schedule.onlinereport.dept.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportDeptDetailAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportDeptFlightDetailDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.dept.OnlineReportDeptDetailDaoService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.dept.sr.SrOnlineReportDeptFlightDetailDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dept.AbstractDeptDetailAnalysisBiz;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.BsDeptStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.DeptStatisticalsEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

/*
 * <AUTHOR>
 * @date 2021/12/31 11:16
 * @Desc 机票部门明细分析
 */
@Service
public class DeptDetailFltBizImpl extends AbstractDeptDetailAnalysisBiz {
    @Autowired
    private OnlineReportDeptFlightDetailDaoImpl onlineReportDeptDetailDao;

    @Autowired
    private SrOnlineReportDeptFlightDetailDaoImpl srOnlineReportDeptDetailDao;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    /**
     * 获取查询的dao
     */
    public OnlineReportDeptDetailDaoService getOnlineReportDeptDetailDao(BaseQueryConditionDTO baseQueryConditionDTO) {
        boolean useSr = baseQueryConditionDTO != null && BooleanUtils.isTrue(baseQueryConditionDTO.useStarRocks);
        return useSr ? srOnlineReportDeptDetailDao : onlineReportDeptDetailDao;
    }

    public List<Map> deptDetail(OnlineReportDeptDetailAnalysisRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setLang(request.getLang());
        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum = SubQueryReportBuTypeEnum.getByOrdinal(request.getSubQueryBu());
        List<Map> list = getOnlineReportDeptDetailDao(baseQueryConditionDto).deptDetailAnalysis(analysisObjectEnum,
                baseQueryConditionDto, initPager(request.getPage()), subQueryReportBuTypeEnum, request.getAnalysisObjectOrgInfo(), (String) map.get("analysisObjectVal")
                , request.getProductType());
        fomratResultData(list);
        return list;
    }

    @Override
    public int count(OnlineReportDeptDetailAnalysisRequest request) throws Exception {
        BaseQueryConditionDTO baseQueryConditionDto = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDto.setLang(request.getLang());
        AnalysisObjectEnum analysisObjectEnum = request.getAnalysisObjectEnum();
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum = SubQueryReportBuTypeEnum.getByOrdinal(request.getSubQueryBu());
        return getOnlineReportDeptDetailDao(baseQueryConditionDto).count(analysisObjectEnum, baseQueryConditionDto, subQueryReportBuTypeEnum, request.getAnalysisObjectOrgInfo(),
                (String) map.get("analysisObjectVal"), request.getProductType());
    }

    @Override
    public List<DeptStatisticalsEnum> getStatisticalList(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        List list1 = DeptStatisticalsEnum.getStaticalsByBizType(QueryReportBuTypeEnum.overview.toString(), subQueryReportBuTypeEnum);
        List list2 = DeptStatisticalsEnum.getStaticalsBySubBuType(subQueryReportBuTypeEnum.ordinal());
        list1.addAll(list2);
        return list1;
    }

    @Override
    public List<BsDeptStatisticalsEnum> getBsStatisticalList(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        List list1 = BsDeptStatisticalsEnum.getStaticalsByBizType(QueryReportBuTypeEnum.overview.toString(), subQueryReportBuTypeEnum);
        List list2 = BsDeptStatisticalsEnum.getStaticalsByBizType(QueryReportBuTypeEnum.flight.toString(), subQueryReportBuTypeEnum);
        list1.addAll(list2);
        return list1;
    }

    @Override
    public boolean matching(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return subQueryReportBuTypeEnum.ordinal() == SubQueryReportBuTypeEnum.FLIGHT.ordinal()
                || subQueryReportBuTypeEnum.ordinal() == SubQueryReportBuTypeEnum.FLIGHT_N.ordinal()
                || subQueryReportBuTypeEnum.ordinal() == SubQueryReportBuTypeEnum.FLIGHT_I.ordinal()
                || subQueryReportBuTypeEnum.ordinal() == SubQueryReportBuTypeEnum.FLIGHT_AGREEMENT.ordinal()
                || subQueryReportBuTypeEnum.ordinal() == SubQueryReportBuTypeEnum.FLIGHT_NON_AGREEMENT.ordinal();
    }

    @Override
    protected void deptDetailCal(List<Map> list, List<Map> listMom, List<Map> listYoy, List<Map> listYoyBefore, List<Map> listSum, List<HeaderKeyValMap> dimensions,
                                 List<BsDeptStatisticalsEnum> statisticalsEnums, boolean isJp, List<Map> origianList) {
        AtomicReference<BigDecimal> sumRealPay = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<Long> sumQuantity = new AtomicReference<>(0L);
        Set<String> set = new HashSet<>();
        Optional.ofNullable(listSum).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).forEach(map -> {
            sumRealPay.set(OrpReportUtils.add(sumRealPay.get(), (BigDecimal) map.get("TOTAL_REAL_PAY")));
            sumQuantity.updateAndGet(v -> v + Optional.ofNullable((Long) map.get("TOTAL_FLT_QUANTITY")).orElse(0L));
            set.add((String) map.get("CURRENCY"));
        });
        boolean isMulitCurrency = set.size() > 1;
        // 金额日本站点取整
        int precision = isJp ? OrpConstants.ZERO : OrpConstants.TWO;
        for (Map map : list) {
            String key = getKey(map, dimensions);
            Map mapMom = getMapByKey(listMom, key, dimensions);
            Map mapYoy = getMapByKey(listYoy, key, dimensions);
            Map mapYoyBefore = getMapByKey(listYoyBefore, key, dimensions);
            Map origian = getMapByKey(origianList, key, dimensions);
            for (BsDeptStatisticalsEnum deptStatisticalsEnum : statisticalsEnums) {
                switch (deptStatisticalsEnum) {
                    case REAL_PAY:
                        if (ConfigUtils.isMustCurrencySwitch()) {
                            // 原币种
                            map.put(deptStatisticalsEnum.toString(), OrpReportUtils.formatBigDecimal((BigDecimal) origian.get("TOTAL_REAL_PAY"), precision));
                        } else {
                            map.put(deptStatisticalsEnum.toString(), OrpReportUtils.formatBigDecimal((BigDecimal) map.get("TOTAL_REAL_PAY"), precision));
                        }
                        break;
                    case CONVERSION_REAL_PAY:
                        // 转换后的币种
                        map.put(deptStatisticalsEnum.toString(), OrpReportUtils.formatBigDecimal((BigDecimal) map.get("TOTAL_REAL_PAY"), precision));
                        break;
                    case REAL_PAY_PERCENT:
                        if (ConfigUtils.isMustCurrencySwitch()) {
                            // 转换后的币种占比
                            map.put(deptStatisticalsEnum.toString(), OrpReportUtils.divideWithPercentSymbol((BigDecimal) map.get("TOTAL_REAL_PAY"), sumRealPay.get()));
                        } else {
                            if (isMulitCurrency) {
                                // 多币种不显示消费金额占比
                                map.put(deptStatisticalsEnum.toString(), StringUtils.EMPTY);
                            } else {
                                map.put(deptStatisticalsEnum.toString(), OrpReportUtils.divideWithPercentSymbol((BigDecimal) map.get("TOTAL_REAL_PAY"), sumRealPay.get()));
                            }
                        }
                        break;
                    case YOY_LAST:
                        map.put(deptStatisticalsEnum.toString(),
                                OrpReportUtils.divideWithPercentYoyMomWithSymbol((BigDecimal) map.get("TOTAL_REAL_PAY"), (BigDecimal) mapYoy.get("TOTAL_REAL_PAY")));
                        break;
                    case YOY_BEFORE_LAST:
                        map.put(deptStatisticalsEnum.toString(),
                                OrpReportUtils.divideWithPercentYoyMomWithSymbol((BigDecimal) map.get("TOTAL_REAL_PAY"), (BigDecimal) mapYoyBefore.get("TOTAL_REAL_PAY")));
                        break;
                    case MOM:
                        map.put(deptStatisticalsEnum.toString(),
                                OrpReportUtils.divideWithPercentYoyMomWithSymbol((BigDecimal) map.get("TOTAL_REAL_PAY"), (BigDecimal) mapMom.get("TOTAL_REAL_PAY")));
                        break;
                    case FLT_QUANTITY:
                        map.put(deptStatisticalsEnum.toString(), map.get("TOTAL_FLT_QUANTITY"));
                        break;
                    case FLT_QUANTITY_PERCENT:
                        map.put(deptStatisticalsEnum.toString(), OrpReportUtils.divideWithPercentSymbol((Long) map.get("TOTAL_FLT_QUANTITY"), sumQuantity.get()));
                        break;
                    default:
                        break;
                }

            }
        }
    }

    @Override
    protected List<Map> getDeptDetailV2(BaseQueryConditionDTO dto, AnalysisObjectEnum analysisObjectEnum, SubQueryReportBuTypeEnum subQueryReportBuTypeEnum, Pager pager,
                                        String startTime, String endTime, boolean needOrigianlCurrency) throws Exception {
        return getOnlineReportDeptDetailDao(dto).deptDetailAnalysis(analysisObjectEnum, dto, pager, subQueryReportBuTypeEnum, startTime, endTime, needOrigianlCurrency, null);
    }
}
