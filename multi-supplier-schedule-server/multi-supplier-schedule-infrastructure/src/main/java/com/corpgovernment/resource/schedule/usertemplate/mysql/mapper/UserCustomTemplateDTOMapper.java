package com.corpgovernment.resource.schedule.usertemplate.mysql.mapper;

import com.corpgovernment.resource.schedule.usertemplate.mysql.entity.UserCustomTemplateDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserCustomTemplateDTOMapper {

    int deleteByTemplateNo(@Param("templateNo") String templateNo);

    int deleteBatchTemplateNos(@Param("templateNos") List<String> templateNos);

    int insertSelective(UserCustomTemplateDo record);


    List<UserCustomTemplateDo> selectByUidAndTemplateNo(@Param("uid") String uid,
                                                         @Param("templateNo") String templateNo, @Param("reportKey") String reportKey);

    int countByTemplateNo(@Param("templateNo") String templateNo);

    int updateByTemplateNoSelective(UserCustomTemplateDo record);

    int countByTemplateNameAndUid(@Param("templateName") String templateName, @Param("uid") String uid, @Param("reportKey") String reportKey);
}