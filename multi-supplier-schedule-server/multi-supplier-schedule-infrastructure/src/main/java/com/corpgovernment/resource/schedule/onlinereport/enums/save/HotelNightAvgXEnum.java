package com.corpgovernment.resource.schedule.onlinereport.enums.save;



import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;

import java.util.Arrays;
import java.util.List;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.enums.save
 * @description:机酒节省分析-酒店间夜均价X轴
 * @author: md_wang
 * @create: 2022-08-02 20:00
 **/
public enum HotelNightAvgXEnum {
    /**
     * [0~100)
     */
    ONE("0-100"),
    /**
     * [100~200)
     */
    TWO("100-200"),
    /**
     * [200~300)
     */
    THREE("200-300"),
    /**
     * [300~400)
     */
    FOUR("300-400"),
    /**
     * [400-500)
     */
    FIVE("400-500"),
    /**
     * [500-600)
     */
    SIX("500-600"),
    /**
     * [600,700)
     */
    SEVEN("600-700"),
    /**
     * [700,800)
     */
    EIGHT("700-800"),
    /**
     * [800,900)
     */
    NINE("800-900"),
    /**
     * [1000,900)
     */
    TEN("900-1000"),
    /**
     * [1000,1500)
     */
    ELEVEN("1000-1500"),
    /**
     * [1500,2000)
     */
    TWELVE("1500-2000"),
    /**
     * 2000及以上
     */
    THIRTEEN(">=2000"),
    ;

    private String x;

    HotelNightAvgXEnum(String x) {
        this.x = x;
    }

    /**
     * >600
     */
    public static List<HotelNightAvgXEnum> gt600() {
        return Arrays.asList(HotelNightAvgXEnum.SEVEN, HotelNightAvgXEnum.EIGHT, HotelNightAvgXEnum.NINE
                , HotelNightAvgXEnum.TEN, HotelNightAvgXEnum.ELEVEN, HotelNightAvgXEnum.TWELVE, HotelNightAvgXEnum.THIRTEEN);
    }

    /**
     * >2000
     */
    public static List<HotelNightAvgXEnum> gt2000() {
        return Arrays.asList(THIRTEEN);
    }

    /**
     * >1500
     */
    public static List<HotelNightAvgXEnum> gt1500() {
        return Arrays.asList(TWELVE, THIRTEEN);
    }

    /**
     * >1000
     */
    public static List<HotelNightAvgXEnum> gt1000() {
        return Arrays.asList(ELEVEN, TWELVE, THIRTEEN);
    }

    /**
     * >900
     */
    public static List<HotelNightAvgXEnum> gt900() {
        return Arrays.asList(TEN, ELEVEN, TWELVE, THIRTEEN);
    }

    /**
     * >800
     */
    public static List<HotelNightAvgXEnum> gt800() {
        return Arrays.asList(NINE, TEN, ELEVEN, TWELVE, THIRTEEN);
    }

    /**
     * >800
     */
    public static List<HotelNightAvgXEnum> gt700() {
        return Arrays.asList(EIGHT, NINE, TEN, ELEVEN, TWELVE, THIRTEEN);
    }

    public static String getPrice(HotelNightAvgXEnum x) {
        if (HotelNightAvgXEnum.SEVEN.equals(x)) {
            return "600";
        }
        if (HotelNightAvgXEnum.EIGHT.equals(x)) {
            return "700";
        }
        if (HotelNightAvgXEnum.NINE.equals(x)) {
            return "800";
        }
        if (HotelNightAvgXEnum.TEN.equals(x)) {
            return "900";
        }
        if (HotelNightAvgXEnum.ELEVEN.equals(x)) {
            return "1000";
        }
        if (HotelNightAvgXEnum.TWELVE.equals(x)) {
            return "1500";
        }
        if (HotelNightAvgXEnum.THIRTEEN.equals(x)) {
            return "2000";
        }
        return OrpConstants.EMPTY;
    }

    public String getX() {
        return x;
    }

    public void setX(String x) {
        this.x = x;
    }

}
