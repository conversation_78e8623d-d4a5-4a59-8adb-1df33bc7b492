/*
package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;





*/
/*
 * <AUTHOR>
 *
 * @date 2022/09/20 16:15
 *
 * @Desc
 *//*


@Repository
public class OnlineReportDeptOasoDetailDao extends AbstractOnlineReportDeptDetailDao {
    @Override
    protected String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return StringUtils.EMPTY;
    }

    */
/**
     * 统计字段
     *
     * @return
     *//*

    @Override
    protected String statical() {
        List sql = new ArrayList();
        sql.add("sum(coalesce(amount_vas, 0)) as TOTAL_REAL_PAY");
        sql.add("sum(coalesce(quantity_vas,0)) as TOTAL_CAR_QUANTITY");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

    @Override
    protected String orderByField() {
        return "TOTAL_REAL_PAY desc";
    }

    @Override
    protected BaseConditionPrebuilder.TableAndTimeColBind getTargetTableAndTimeColumn(String statisticalCaliber, Boolean isForegin) {
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = new BaseConditionPrebuilder.TableAndTimeColBind();
        tableAndTimeColBind.setDateColumn(REPORT_DATE);
        tableAndTimeColBind.setClickHouseTable(ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY);
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name())) {
            tableAndTimeColBind.setClickHouseTable(ClickHouseTable.ADM_INDEX_PRICE_SUMMARRY_ODT);
        }
        return tableAndTimeColBind;
    }

    @Override
    protected String totalField() {
        return "SUM(coalesce(amount_vas, 0)) AS TOTAL_REAL_PAY,sum(coalesce(quantity_vas,0)) as TOTAL_CAR_QUANTITY";
    }

    */
/**
     * 同环比，总计数据
     *
     * @return
     *//*

    @Override
    protected String momAndYoy() {
        return "sum(coalesce(amount_vas, 0))  as TOTAL_REAL_PAY";
    }

    */
/**
     * 返回字段
     *
     * @return
     *//*

    protected String baseQueryField() {
        List sql = new ArrayList();
        sql.add("round(crt.TOTAL_REAL_PAY, 4) AS REAL_PAY");
        sql.add("round(case when coalesce(total.TOTAL_REAL_PAY, 0) !=0 then divide(toFloat64(coalesce(crt.TOTAL_REAL_PAY, 0)), \n"
                + "  toFloat64(coalesce(total.TOTAL_REAL_PAY, 0))) * 100 else 0 end, 4) as REAL_PAY_PERCENT");
        sql.add("round(case when coalesce(yoy.TOTAL_REAL_PAY, 0) !=0 then divide(toFloat64(coalesce(crt.TOTAL_REAL_PAY, 0) -\n" +
                "   coalesce(yoy.TOTAL_REAL_PAY, 0)) , toFloat64(abs(coalesce(yoy.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as YOY_LAST");
        sql.add("round(case when coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0) !=0 then divide(toFloat64(coalesce(crt.TOTAL_REAL_PAY, 0) - \n"
                + "  coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)) , toFloat64(abs(coalesce(yoyBeforeLast.TOTAL_REAL_PAY, 0)))) * 100 \n" +
                "   else 0 end, 4) as YOY_BEFORE_LAST");
        sql.add("round(case when coalesce(mom.TOTAL_REAL_PAY, 0) !=0 then divide(toFloat64(coalesce(crt.TOTAL_REAL_PAY, 0) - \n" +
                "   coalesce(mom.TOTAL_REAL_PAY, 0)) , toFloat64(abs(coalesce(mom.TOTAL_REAL_PAY, 0)))) * 100 else 0 end, 4) as MOM");
        sql.add("crt.TOTAL_CAR_QUANTITY AS VASO_QUANTITY ");
        sql.add("round(case when coalesce(total.TOTAL_CAR_QUANTITY, 0) != 0 then divide(coalesce(crt.TOTAL_CAR_QUANTITY, 0), \n" +
                "  coalesce(total.TOTAL_CAR_QUANTITY, 0)) * 100 else 0 end, 4) as VASO_QUANTITY_PERCENT ");
        return StringUtils.join(sql, OrpConstants.COMMA);
    }

}
*/
