package com.corpgovernment.resource.schedule.onlinereport.clickhouse.api.overview;


import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportOverviewTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineReportOverviewTrendJPDTO;
import com.corpgovernment.resource.schedule.onlinereport.detail.OnlineTrendRequestDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/24
 */
public interface OnlineReportOverviewTrendDaoService {
    List<OnlineReportOverviewTrendJPDTO> queryAccJPOverviewTripTrendOrderNum(OnlineTrendRequestDto requestDto)
            throws Exception;

    List<OnlineReportOverviewTrendJPDTO> queryCurrentJPOverviewTripTrendOrderNum(OnlineTrendRequestDto requestDto)
            throws Exception;

    /**
     * 查询 在线报告概况-订单数趋势数据 - 概览 （bu + 字段名 -> 数据聚合维度 日期聚合 -> 数据聚合粒度 同环比)
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportOverviewTrendJPDTO> queryOnlineReportCurrentJPOverviewTrendOrderNum(OnlineTrendRequestDto request)
            throws Exception;

    /**
     * 差旅分析-概览-累计订单数趋势查询（整体）
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    List<OnlineReportOverviewTrendDTO> queryAccOverviewTripTrendOrderNum(OnlineTrendRequestDto requestDto)
            throws Exception;

    /**
     * 差旅分析-概览-累计订单数趋势查询（分产品）
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportOverviewTrendDTO> queryOnlineReportAccOverviewTrendOrderNum(OnlineTrendRequestDto request)
            throws Exception;

    List<OnlineReportOverviewTrendDTO> queryCurrentOverviewTripTrendOrderNum(OnlineTrendRequestDto requestDto)
            throws Exception;

    /**
     * 查询 在线报告概况-订单数趋势数据 - 概览 （bu + 字段名 -> 数据聚合维度 日期聚合 -> 数据聚合粒度 同环比)
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportOverviewTrendDTO> queryOnlineReportCurrentOverviewTrendOrderNum(OnlineTrendRequestDto request)
            throws Exception;

    List<OnlineReportOverviewTrendJPDTO> queryOnlineReportAccJPOverviewTrendOrderNum(OnlineTrendRequestDto request)
            throws Exception;


    /**
     * 查询 在线报告差旅分析-概况-消费金额趋势数据 - 概览 （bu + 字段名 -> 数据聚合维度 日期聚合 -> 数据聚合粒度 同环比)
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportOverviewTrendDTO> queryOnlineReportCurrentOverviewTrend(OnlineTrendRequestDto request)
            throws Exception;

    List<OnlineReportOverviewTrendDTO> queryOnlineReportAccOverviewTrend(OnlineTrendRequestDto request)
            throws Exception;

    List<OnlineReportOverviewTrendJPDTO> queryOnlineReportCurrentJPOverviewTrend(OnlineTrendRequestDto request)
            throws Exception;

    /**
     * JA-JP
     *
     * @param request
     * @return
     * @throws Exception
     */
    List<OnlineReportOverviewTrendJPDTO> queryOnlineReportAccJPOverviewTrend(OnlineTrendRequestDto request)
            throws Exception;

}
