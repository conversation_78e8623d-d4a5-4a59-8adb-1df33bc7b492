package com.corpgovernment.resource.schedule.onlinereport.clickhouse;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AnalysisObjectOrgInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.SubQueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpDateTimeUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 *
 * @date 2021/11/8 16:15
 *
 * @Desc
 */
@Repository
@Slf4j
public abstract class AbstractOnlineReportDeptDetailStarRocksDao extends AbstractClickhouseBaseDao {

    private static final String log_TITLE = "OnlineReportDeptOverViewDetailDao";

    private final static String SQL_DEFAULT = " corp_corporation, corp_name ";
    private final static String GROUP_SQL_FORMAT_DEFAULT = " %s, %s";

    private final static String GROUP_SQL_FORMAT_DEFAULT_FREGIN = " %s, %s, %s";
    private final static String TWO_JOIN_FORMAT_DEFAULT = " %s AND %s";

    private final static String THREE_JOIN_FORMAT_DEFAULT = " %s AND %s AND %s";
    private final static String JOIN_MOM_CORP_DEFAULT =
            " current.corp_corporation = mom.corp_corporation and current.corp_name = mom.corp_name";
    private final static String JOIN_MOM_ACCOUNT_DEFAULT =
            " current.account_id = mom.account_id and current.account_name = mom.account_name";

    private final static String JOIN_MOM_ACCOUNT_CODE_DEFAULT =
            " current.account_id = mom.account_id and current.account_code = mom.account_code";

    private final static String JOIN_MOM_UID_DEFAULT =
            " current.uid = mom.uid and current.user_name = mom.user_name";

    private final static String JOIN_MOM_DEFAULT_FOREGIN =
            " current.o_currency = mom.o_currency";
    private final static String BUS_MOM_CORP_DEFAULT =
            " current.corpid = mom.corpid and current.corp_name = mom.corp_name";

    private final static String JOIN_YOY_CORP_DEFAULT =
            " current.corp_corporation = yoy.corp_corporation and current.corp_name = yoy.corp_name";

    private final static String JOIN_YOY_UID_DEFAULT =
            " current.uid = yoy.uid and current.user_name = yoy.user_name";
    private final static String JOIN_YOY_ACCOUNT_DEFAULT =
            "  current.account_id = yoy.account_id and current.account_name = yoy.account_name";

    private final static String JOIN_YOY_ACCOUNT_CODE_DEFAULT =
            "  current.account_id = yoy.account_id and current.account_code = yoy.account_code";

    private final static String JOIN_YOY_DEFAULT_FOREGIN =
            " current.o_currency = yoy.o_currency";
    private final static String BUS_YOY_CORP_DEFAULT =
            "  current.corpid = yoy.corpid and current.corp_name = yoy.corp_name and current.o_currency = yoy.o_currency";

    private final static String JOIN_YOYBEFORELAST_CORP_DEFAULT =
            " current.corp_corporation = yoyBeforeLast.corp_corporation and current.corp_name = yoyBeforeLast.corp_name";
    private final static String JOIN_YOYBEFORELAST_ACCOUNT_DEFAULT =
            " current.account_id = yoyBeforeLast.account_id and current.account_name = yoyBeforeLast.account_name";

    private final static String JOIN_YOYBEFORELAST_ACCOUNT_CODE_DEFAULT =
            " current.account_id = yoyBeforeLast.account_id and current.account_code = yoyBeforeLast.account_code";

    private final static String JOIN_YOYBEFORELAST_UID_DEFAULT =
            " current.uid = yoyBeforeLast.uid and current.user_name = yoyBeforeLast.user_name";
    private final static String JOIN_YOYBEFORELAST_DEFAULT_FOREGIN =
            " current.o_currency = yoyBeforeLast.o_currency";
    private final static String BUS_YOYBEFORELAST_CORP_DEFAULT =
            "  current.corpid = yoyBeforeLast.corpid and current.corp_name = yoyBeforeLast.corp_name";

    private final static String COUNT_ALIAS = "countAll";

    private final static String CURRENT_TABLE_ALIAS = "current";

    private final static String YOY_TABLE_ALIAS = "yoy";

    private final static String YOYBEFORE_TABLE_ALIAS = "yoyBeforeLast";

    private final static String MOM_TABLE_ALIAS = "mom";

    private final static String TOTAL_TABLE_ALIAS = "total";

    public String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum,
                           BaseQueryConditionDTO baseQueryConditionDto, Pager pager, SubQueryReportBuTypeEnum subQueryReportBuTypeEnum,
                           AnalysisObjectOrgInfo analysisObjectOrgInfo, String analysisObjectVal, String productType) {
        Boolean isForeign = BlueSpaceUtils.isForeign(baseQueryConditionDto.getPos(), baseQueryConditionDto.getBlueSpace());
        JoinCondition biz = null;
        if (isForeign) {
            biz = joinConditionDefaultForeign(analysisObjectEnum, analysisObjectOrgInfo, baseQueryConditionDto.getLang());
        } else {
            biz = joinCondition(analysisObjectEnum, analysisObjectOrgInfo, baseQueryConditionDto.getLang());
        }
        String currentSql = statical();
        StringBuilder baseSql =
                new StringBuilder(String.format("SELECT %s FROM", biz.getResultFields() + OrpConstants.COMMA + baseQueryField() + baseQueryField(subQueryReportBuTypeEnum)));
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = getTargetTableAndTimeColumn(baseQueryConditionDto.getStatisticalCaliber(), isForeign);
        String partion = queryPartition(tableAndTimeColBind.getClickHouseTable());
        String currentCondition =
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList,
                        startTime, endTime, partion, tableAndTimeColBind.getDateColumn());
        String specialCondition =
                specialCondition(subQueryReportBuTypeEnum) + getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo,
                        analysisObjectEnum, parmList)
                        + getProcutTypeCondition(productType);
        String downConditionWithSpecialCol = downConditionWithSpecialCol(analysisObjectEnum, analysisObjectVal,
                analysisObjectOrgInfo, subQueryReportBuTypeEnum, parmList);
        currentCondition = currentCondition + specialCondition + downConditionWithSpecialCol;
        baseSql.append(
                String.format("(SELECT %s  FROM %s  WHERE %s  GROUP BY %s) %s",
                        biz.getResultFieldsAlias() + OrpConstants.COMMA + currentSql + dept(subQueryReportBuTypeEnum),
                        tableAndTimeColBind.getClickHouseTable().getTable()
                        , currentCondition,
                        biz.getGroupFields() + dept(subQueryReportBuTypeEnum)
                                .concat(String.format(" order by %s %s desc", sortCurrency(isForeign), orderByField().concat(",").concat(biz.getOrderByFields())))
                                .concat(" limit ?, ? "),
                        CURRENT_TABLE_ALIAS));
        parmList.add(pager.pageIndex * pager.pageSize);
        parmList.add(pager.pageSize);
        // 同比去年
        List<String> yoyTimes = OrpDateTimeUtils.yoyTime(startTime, endTime, OrpConstants.ONE_YEAR_DAYS);
        String yoyCondition =
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList,
                        yoyTimes.get(0), yoyTimes.get(1), partion, tableAndTimeColBind.getDateColumn())
                        + specialCondition + downConditionWithSpecialCol;
        baseSql.append(String.format(
                " LEFT JOIN (SELECT %s  FROM %s  WHERE %s  GROUP BY %s) %s ON  %s",
                biz.getResultFieldsAlias() + OrpConstants.COMMA + momAndYoy() + dept(subQueryReportBuTypeEnum), tableAndTimeColBind.getClickHouseTable().getTable()
                , yoyCondition, biz.getGroupFields() + dept(subQueryReportBuTypeEnum),
                YOY_TABLE_ALIAS, biz.getCurrentJoinYoy() + getYoy(subQueryReportBuTypeEnum)));
        // 同比前年
        List<String> yoyBeforeTimes = OrpDateTimeUtils.yoyTime(startTime, endTime, OrpConstants.TWO_YEAR_DAYS);
        String yoyBeforeLastCondition =
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList
                        , yoyBeforeTimes.get(0), yoyBeforeTimes.get(1)
                        , partion, tableAndTimeColBind.getDateColumn())
                        + specialCondition + downConditionWithSpecialCol;

        baseSql.append(String.format(
                " LEFT JOIN (SELECT %s  FROM %s  WHERE %s  GROUP BY %s) %s ON  %s",
                biz.getResultFieldsAlias() + OrpConstants.COMMA + momAndYoy() + dept(subQueryReportBuTypeEnum), tableAndTimeColBind.getClickHouseTable().getTable()
                , yoyBeforeLastCondition,
                biz.getGroupFields() + dept(subQueryReportBuTypeEnum), YOYBEFORE_TABLE_ALIAS,
                biz.getCurrentJoinYoyBeforeLast() + getYoybeforelast(subQueryReportBuTypeEnum)));
        // 环比
        List<String> momTimes = OrpDateTimeUtils.momTime(startTime, endTime);
        String momCondition =
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList,
                        momTimes.get(0), momTimes.get(1), partion, tableAndTimeColBind.getDateColumn())
                        + specialCondition + downConditionWithSpecialCol;

        baseSql.append(String.format(
                " LEFT JOIN (SELECT %s  FROM %s  WHERE %s  GROUP BY %s) %s ON  %s",
                biz.getResultFieldsAlias() + OrpConstants.COMMA + momAndYoy() + dept(subQueryReportBuTypeEnum),
                tableAndTimeColBind.getClickHouseTable().getTable()
                , momCondition, biz.getGroupFields() + dept(subQueryReportBuTypeEnum),
                MOM_TABLE_ALIAS, biz.getCurrentJoinMom() + getMom(subQueryReportBuTypeEnum)));
        String totalCondition =
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList, startTime, endTime,
                        partion, tableAndTimeColBind.getDateColumn())
                        + specialCondition + downConditionWithSpecialCol;

        baseSql.append(String.format(
                " CROSS JOIN (SELECT %s  FROM %s  WHERE %s) %s ",
                totalField(), tableAndTimeColBind.getClickHouseTable().getTable()
                , totalCondition, TOTAL_TABLE_ALIAS));
        return baseSql.toString();
    }

    public String querySql(List<Object> parmList, AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, Pager pager,
                           SubQueryReportBuTypeEnum subQueryReportBuTypeEnum, String startTime, String endTime, boolean needOrigianlCurrency,
                           AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        Boolean isForeign = BlueSpaceUtils.isForeign(baseQueryConditionDto.getPos(), baseQueryConditionDto.getBlueSpace());
        JoinCondition biz = null;
        if (isForeign) {
            biz = joinConditionDefaultForeignV2(analysisObjectEnum, baseQueryConditionDto.getLang());
        } else {
            biz = joinCondition(analysisObjectEnum, analysisObjectOrgInfo, baseQueryConditionDto.getLang());
        }
        String currentSql = staticalForeign();
        StringBuilder baseSql = new StringBuilder();
        BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind = getTargetTableAndTimeColumn(baseQueryConditionDto.getStatisticalCaliber(), isForeign);
        String partion = queryPartition(tableAndTimeColBind.getClickHouseTable());
        String currentCondition =
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList,
                        startTime, endTime, partion, tableAndTimeColBind.getDateColumn());
        String specialCondition = specialCondition(subQueryReportBuTypeEnum) + specialCondition(baseQueryConditionDto, needOrigianlCurrency, parmList);
        currentCondition = currentCondition + specialCondition;
        if (needOrigianlCurrency) {
            // 需要查询原币种金额信息
            currentCondition += " and is_same_currency=1 ";
        }
        baseSql.append(String.format("SELECT %s  FROM %s  WHERE %s  GROUP BY %s",
                biz.getResultFields() + OrpConstants.COMMA + currentSql,
                tableAndTimeColBind.getClickHouseTable().getTable(), currentCondition, biz.getGroupFields()));
        baseSql.append(String.format(" order by %s %s", sortCurrency(isForeign), orderByField().concat(",").concat(biz.getOrderByFields())));
        if (pager != null) {
            baseSql.append(" limit ?, ? ");
            parmList.add(pager.pageIndex * pager.pageSize);
            parmList.add(pager.pageSize);
        }
        return baseSql.toString();
    }

    protected abstract String totalField();

    protected abstract String momAndYoy();

    protected abstract String baseQueryField();

    protected String baseQueryField(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return StringUtils.EMPTY;
    }

    protected String getYoy(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return StringUtils.EMPTY;
    }

    protected String getYoybeforelast(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return StringUtils.EMPTY;
    }

    protected String getMom(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return StringUtils.EMPTY;
    }

    protected String dept(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum) {
        return StringUtils.EMPTY;
    }

    protected abstract String statical();

    protected String staticalForeign() {
        return statical();
    }

    ;

    protected abstract String orderByField();

    protected abstract BaseConditionPrebuilder.TableAndTimeColBind getTargetTableAndTimeColumn(String statisticalCaliber, Boolean isForeign);

    public String countSql(List<Object> parmList, BaseConditionPrebuilder.TableAndTimeColBind tableAndTimeColBind,
                           AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto,
                           SubQueryReportBuTypeEnum reportBuTypeEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String analysisObjectVal, String productType) {
        Boolean isForeign = BlueSpaceUtils.isForeign(baseQueryConditionDto.getPos(), baseQueryConditionDto.getBlueSpace());
        JoinCondition biz = null;
        if (isForeign) {
            biz = joinConditionDefaultForeignV2(analysisObjectEnum, baseQueryConditionDto.getLang());
        } else {
            biz = joinCondition(analysisObjectEnum, analysisObjectOrgInfo, baseQueryConditionDto.getLang());
        }
        String startTime = baseQueryConditionDto.getStartTime();
        String endTime = baseQueryConditionDto.getEndTime();
        String partion = queryPartition(tableAndTimeColBind.getClickHouseTable());
        // String currentCondition =
        //    BaseConditionPrebuilder.buildPreSql(baseQueryConditionDto, parmList, startTime, endTime, partion);
        String downConditionWithSpecialCol = downConditionWithSpecialCol(analysisObjectEnum, analysisObjectVal,
                analysisObjectOrgInfo, reportBuTypeEnum, parmList);

        String currentCondition =
                BaseConditionPrebuilder.buildPreSqlWithTimeWithCol(baseQueryConditionDto, parmList, startTime, endTime
                        , partion, tableAndTimeColBind.getDateColumn());
        currentCondition =
                currentCondition + specialCondition(reportBuTypeEnum) + specialCondition(baseQueryConditionDto, false, parmList) +
                        getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum, parmList) + downConditionWithSpecialCol + getProcutTypeCondition(productType);
        return String.format(
                "select count(1) as countAll from(SELECT %s  FROM %s  WHERE %s GROUP by %s ) tmp",
                biz.getResultFieldsAlias(), tableAndTimeColBind.getClickHouseTable().getTable(), currentCondition, biz.groupFields + dept(reportBuTypeEnum));
    }

    /**
     * 前5部门消费
     *
     * @param
     * @return
     * @throws Exception
     */
    public List<Map> deptDetailAnalysis(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, Pager pager,
                                        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String analysisObjectVal,
                                        String productType)
            throws Exception {
        List<Object> parmList = new ArrayList<>();
        String querySql = querySql(parmList, analysisObjectEnum, baseQueryConditionDto, pager, subQueryReportBuTypeEnum, analysisObjectOrgInfo, analysisObjectVal, productType);
        // 查询clickhouse
        return queryBySql(querySql, parmList, (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return DbResultMapUtils.mapResultList(u);
                    } catch (Exception e) {
                        log.error(log_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "deptDetailAnalysis");
    }

    /**
     * 前5部门消费
     *
     * @param
     * @return
     * @throws Exception
     */
    public List<Map> deptDetailAnalysis(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, Pager pager,
                                        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum, String startTime, String endTime, boolean needOrigianlCurrency,
                                        AnalysisObjectOrgInfo analysisObjectOrgInfo)
            throws Exception {
        List<Object> parmList = new ArrayList<>();
        String querySql = querySql(parmList, analysisObjectEnum, baseQueryConditionDto, pager, subQueryReportBuTypeEnum, startTime, endTime, needOrigianlCurrency,
                analysisObjectOrgInfo);
        // 查询clickhouse
        return queryBySql(querySql, parmList, (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return DbResultMapUtils.mapResultList(u);
                    } catch (Exception e) {
                        log.error(log_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "deptDetailAnalysis");
    }


    /**
     * 前5部门消费
     *
     * @param
     * @return
     * @throws Exception
     */
    public Integer count(AnalysisObjectEnum analysisObjectEnum, BaseQueryConditionDTO baseQueryConditionDto, SubQueryReportBuTypeEnum subQueryReportBuTypeEnum,
                         AnalysisObjectOrgInfo analysisObjectOrgInfo, String analysisObjectVal, String productType) throws Exception {
        List<Object> parmList = new ArrayList<>();
        Boolean isForeign = BlueSpaceUtils.isForeign(baseQueryConditionDto.getPos(), baseQueryConditionDto.getBlueSpace());
        String countSql =
                countSql(parmList, getTargetTableAndTimeColumn(baseQueryConditionDto.getStatisticalCaliber(), isForeign)
                        , analysisObjectEnum, baseQueryConditionDto, subQueryReportBuTypeEnum, analysisObjectOrgInfo, analysisObjectVal, productType);
        // 查询clickhouse
        return queryBySql(countSql, parmList, (req, statement) -> mapCommonRequest(parmList, statement),
                (u, d) -> {
                    try {
                        return mapIntResult(u, COUNT_ALIAS);
                    } catch (Exception e) {
                        log.error(log_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return OrpConstants.ZERO;
                }, Integer.class, "count");
    }

    @Override
    public PreparedStatement mapCommonRequest(List<Object> parmList, PreparedStatement statement) {
        int index = OrpConstants.ONE;
        try {
            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }

    protected abstract String specialCondition(SubQueryReportBuTypeEnum subQueryReportBuTypeEnum);

    protected abstract String getProcutTypeCondition(String productType);

    protected String specialCondition(BaseQueryConditionDTO dto, Boolean needOrigianlCurrency, List<Object> paramList) {
        if (BlueSpaceUtils.isForeign(dto.getPos(), dto.getBlueSpace())) {
            if (ConfigUtils.isMustCurrencySwitch() && org.apache.commons.lang.StringUtils.isNotBlank(dto.getCurrency()) && !needOrigianlCurrency) {
                // 币种条件
                String sql = " and termcurrency = ?";
                paramList.add(dto.getCurrency());
                return sql;
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinCondition(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String lang) {
        if (ConfigUtils.getBoolean("custom_dim_open", false)) {
            return joinConditionCustom(analysisObjectEnum, analysisObjectOrgInfo, lang);
        } else {
            return joinConditionDefault(analysisObjectEnum, lang);
        }
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionDefault(AnalysisObjectEnum analysisObjectEnum, String lang) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz.setResultFields("current.corp_name as CORP, current.corp_corporation as dimId")
                        .setResultFieldsAlias(" corp_corporation, corp_name")
                        .setGroupFields(" corp_corporation, corp_name")
                        .setOrderByFields("corp_corporation, corp_name")
                        .setCurrentJoinMom(JOIN_MOM_CORP_DEFAULT).setCurrentJoinYoy(JOIN_YOY_CORP_DEFAULT)
                        .setCurrentJoinYoyBeforeLast(JOIN_YOYBEFORELAST_CORP_DEFAULT);
                break;
            case ACCOUNT:
                biz = biz.setResultFields("current.corp_name as CORP, current.account_name as ACCOUNT, current.account_id as dimId")
                        .setResultFieldsAlias(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_name"))
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_name"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_name ")
                        .setCurrentJoinMom(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_ACCOUNT_DEFAULT))
                        .setCurrentJoinYoy(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_ACCOUNT_DEFAULT))
                        .setCurrentJoinYoyBeforeLast(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                                JOIN_YOYBEFORELAST_ACCOUNT_DEFAULT));
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("current.corp_name as CORP, current.account_code as ACCOUNTCODE, current.account_id as dimId")
                        .setResultFieldsAlias(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_code"))
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_code"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_code ")
                        .setCurrentJoinMom(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_ACCOUNT_CODE_DEFAULT))
                        .setCurrentJoinYoy(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_ACCOUNT_CODE_DEFAULT))
                        .setCurrentJoinYoyBeforeLast(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                                JOIN_YOYBEFORELAST_ACCOUNT_CODE_DEFAULT));
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase();
                break;
            case COSTCENTER1:
                analysisObject = "cost_center1";
                break;
            case COSTCENTER2:
                analysisObject = "cost_center2";
                break;
            case COSTCENTER3:
                analysisObject = "cost_center3";
                break;
            case COSTCENTER4:
                analysisObject = "cost_center4";
                break;
            case COSTCENTER5:
                analysisObject = "cost_center5";
                break;
            case COSTCENTER6:
                analysisObject = "cost_center6";
                break;
            case UID:
                biz.setResultFields("current.uid as UID,current.user_name as NAME")
                        .setResultFieldsAlias(" uid, user_name")
                        .setGroupFields(" uid, user_name")
                        .setOrderByFields("uid, user_name ")
                        .setCurrentJoinMom(JOIN_MOM_UID_DEFAULT).setCurrentJoinYoy(JOIN_YOY_UID_DEFAULT)
                        .setCurrentJoinYoyBeforeLast(JOIN_YOYBEFORELAST_UID_DEFAULT);
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject) && analysisObjectEnum != AnalysisObjectEnum.ORG) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            String temp = "case when (" + analysisObject + " is null or " + analysisObject + " = '') " +
                    "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString();
            String temp1 = "case when current." + analysisObjectEnum.toString() + " = '" + other + "' then '' else current." + analysisObjectEnum.toString() + " end as dimId";
            biz = biz
                    .setResultFields("current.corp_name as CORP, current." + analysisObjectEnum.toString() + " as " + analysisObjectEnum + "," + temp1)
                    .setResultFieldsAlias(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, temp))
                    .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, analysisObjectEnum.toString()))
                    .setOrderByFields("corp_corporation, corp_name " + ", " + analysisObjectEnum.toString())
                    .setCurrentJoinMom(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT,
                            String.format("current.%s = mom.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())))
                    .setCurrentJoinYoy(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT,
                            String.format("current.%s = yoy.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())))
                    .setCurrentJoinYoyBeforeLast(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                            String.format("current.%s = yoyBeforeLast.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())));

        }
        if (analysisObjectEnum == AnalysisObjectEnum.ORG) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            String temp = "case when (" + analysisObject + " is null or " + analysisObject + " = '') " +
                    "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString();
            String temp1 = "case when current." + analysisObjectEnum.toString() + " = '" + other + "' then '' else current." + analysisObjectEnum.toString() + " end as dimId";
            biz = biz.setResultFields(" current.corp_name as CORP, current." + analysisObjectEnum.toString() + " as " + analysisObjectEnum.toString() + "," + temp1)
                    .setResultFieldsAlias(" corp_corporation, corp_name, " + temp)
                    .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, analysisObjectEnum.toString()))
                    .setOrderByFields("corp_corporation, corp_name " + ", " + analysisObjectEnum.toString())
                    .setCurrentJoinMom(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT,
                            String.format("current.%s = mom.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())))
                    .setCurrentJoinYoy(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT,
                            String.format("current.%s = yoy.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())))
                    .setCurrentJoinYoyBeforeLast(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                            String.format("current.%s = yoyBeforeLast.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())));
        }
        return biz;
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionDefaultForeign(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String lang) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz.setResultFields("current.corp_name as CORP, current.o_currency AS CURRENCY")
                        .setResultFieldsAlias(" corp_corporation, corp_name, o_currency")
                        .setGroupFields(" corp_corporation, corp_name, o_currency")
                        .setOrderByFields("corp_corporation, corp_name, o_currency")
                        .setCurrentJoinMom(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_DEFAULT_FOREGIN))
                        .setCurrentJoinYoy(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_DEFAULT_FOREGIN))
                        .setCurrentJoinYoyBeforeLast(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT, JOIN_YOYBEFORELAST_DEFAULT_FOREGIN));
                break;
            case ACCOUNT:
                biz = biz.setResultFields("current.corp_name as CORP, current.account_name as ACCOUNT, current.o_currency AS CURRENCY")
                        .setResultFieldsAlias(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_name, o_currency"))
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_name, o_currency"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_name, o_currency ")
                        .setCurrentJoinMom(String.format(THREE_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_ACCOUNT_DEFAULT, JOIN_MOM_DEFAULT_FOREGIN))
                        .setCurrentJoinYoy(String.format(THREE_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_ACCOUNT_DEFAULT, JOIN_YOY_DEFAULT_FOREGIN))
                        .setCurrentJoinYoyBeforeLast(String.format(THREE_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                                JOIN_YOYBEFORELAST_ACCOUNT_DEFAULT, JOIN_YOYBEFORELAST_DEFAULT_FOREGIN));
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("current.corp_name as CORP, current.account_code as ACCOUNTCODE, current.o_currency AS CURRENCY")
                        .setResultFieldsAlias(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_code, o_currency"))
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_code, o_currency"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_code, o_currency ")
                        .setCurrentJoinMom(String.format(THREE_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_ACCOUNT_CODE_DEFAULT, JOIN_MOM_DEFAULT_FOREGIN))
                        .setCurrentJoinYoy(String.format(THREE_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_ACCOUNT_CODE_DEFAULT, JOIN_YOY_DEFAULT_FOREGIN))
                        .setCurrentJoinYoyBeforeLast(String.format(THREE_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                                JOIN_YOYBEFORELAST_ACCOUNT_CODE_DEFAULT, JOIN_YOYBEFORELAST_DEFAULT_FOREGIN));
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase();
                break;
            case COSTCENTER1:
                analysisObject = "cost_center1";
                break;
            case COSTCENTER2:
                analysisObject = "cost_center2";
                break;
            case COSTCENTER3:
                analysisObject = "cost_center3";
                break;
            case COSTCENTER4:
                analysisObject = "cost_center4";
                break;
            case COSTCENTER5:
                analysisObject = "cost_center5";
                break;
            case COSTCENTER6:
                analysisObject = "cost_center6";
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject)) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            biz = biz
                    .setResultFields("current.corp_name as CORP, case when (current." + analysisObject + " is null or current." + analysisObject + " = '') " +
                            "then '" + other + "' else current." + analysisObject + " end as " + analysisObjectEnum.toString() + ", current.o_currency AS CURRENCY")
                    .setResultFieldsAlias(String.format(GROUP_SQL_FORMAT_DEFAULT_FREGIN, SQL_DEFAULT, analysisObject, "o_currency"))
                    .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT_FREGIN, SQL_DEFAULT, analysisObject, "o_currency"))
                    .setOrderByFields("corp_corporation, corp_name," + analysisObject.toString())
                    .setCurrentJoinMom(String.format(THREE_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT,
                            String.format("current.%s = mom.%s", analysisObject, analysisObject), JOIN_MOM_DEFAULT_FOREGIN))
                    .setCurrentJoinYoy(String.format(THREE_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT,
                            String.format("current.%s = yoy.%s", analysisObject, analysisObject), JOIN_YOY_DEFAULT_FOREGIN))
                    .setCurrentJoinYoyBeforeLast(String.format(THREE_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                            String.format("current.%s = yoyBeforeLast.%s", analysisObject, analysisObject), JOIN_YOYBEFORELAST_DEFAULT_FOREGIN));

        }
        return biz;
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionDefaultForeignV2(AnalysisObjectEnum analysisObjectEnum, String lang) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz.setResultFields("corp_name as CORP, o_currency AS CURRENCY")
                        .setGroupFields(" corp_corporation, corp_name, o_currency")
                        .setOrderByFields("corp_corporation, corp_name, o_currency");
                break;
            case ACCOUNT:
                biz = biz.setResultFields("corp_name as CORP, account_name as ACCOUNT, o_currency AS CURRENCY")
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_name, o_currency"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_name, o_currency ");
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("corp_name as CORP, account_code as ACCOUNTCODE, o_currency AS CURRENCY")
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_code, o_currency"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_code, o_currency ");
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase();
                break;
            case COSTCENTER1:
                analysisObject = "cost_center1";
                break;
            case COSTCENTER2:
                analysisObject = "cost_center2";
                break;
            case COSTCENTER3:
                analysisObject = "cost_center3";
                break;
            case COSTCENTER4:
                analysisObject = "cost_center4";
                break;
            case COSTCENTER5:
                analysisObject = "cost_center5";
                break;
            case COSTCENTER6:
                analysisObject = "cost_center6";
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject)) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            biz = biz.setResultFields("corp_name as CORP, case when (" + analysisObject + " is null or " + analysisObject + " = '') " +
                            "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString() + ", o_currency AS CURRENCY")
                    .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT_FREGIN, SQL_DEFAULT, analysisObject, "o_currency"))
                    .setOrderByFields("corp_corporation, corp_name," + analysisObject.toString());

        }
        return biz;
    }

    /**
     * 币种排序
     * 美元，新加坡元，日元，韩元，港币
     * @return
     */
    private String sortCurrency(boolean isForeign) {
        if (isForeign) {
            return "case when o_currency = 'USD' then 1 when o_currency = 'SGD' then 2 when o_currency = 'JPY' then 3 " +
                    "when o_currency = 'KRW' then 4 when o_currency = 'HKD' then 5 else 0 end asc,";
        }
        // 中文站不需要排序
        return StringUtils.EMPTY;
    }

    /**
     * 关联条件
     *
     * @param analysisObjectEnum
     * @return
     */
    public JoinCondition joinConditionCustom(AnalysisObjectEnum analysisObjectEnum, AnalysisObjectOrgInfo analysisObjectOrgInfo, String lang) {
        JoinCondition biz = new JoinCondition();
        String analysisObject = null;
        switch (analysisObjectEnum) {
            case CORP:
                biz.setResultFields("current.corp_name as CORP, current.corp_corporation as dimId").setGroupFields(" corp_corporation, corp_name")
                        .setResultFieldsAlias("corp_corporation, corp_name").setGroupFields(" corp_corporation, corp_name")
                        .setOrderByFields("corp_corporation, corp_name")
                        .setCurrentJoinMom(JOIN_MOM_CORP_DEFAULT).setCurrentJoinYoy(JOIN_YOY_CORP_DEFAULT)
                        .setCurrentJoinYoyBeforeLast(JOIN_YOYBEFORELAST_CORP_DEFAULT);
                break;
            case ACCOUNT:
                biz = biz.setResultFields("current.corp_name as CORP, current.account_name as ACCOUNT, current.account_id as dimId")
                        .setResultFieldsAlias(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_name"))
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_name"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_name ")
                        .setCurrentJoinMom(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_ACCOUNT_DEFAULT))
                        .setCurrentJoinYoy(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_ACCOUNT_DEFAULT))
                        .setCurrentJoinYoyBeforeLast(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                                JOIN_YOYBEFORELAST_ACCOUNT_DEFAULT));
                break;
            case ACCOUNTCODE:
                biz = biz.setResultFields("current.corp_name as CORP, current.account_code as ACCOUNTCODE, current.account_id as dimId")
                        .setResultFieldsAlias(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_code"))
                        .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, " account_id, account_code"))
                        .setOrderByFields("corp_corporation, corp_name, account_id, account_code ")
                        .setCurrentJoinMom(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT, JOIN_MOM_ACCOUNT_CODE_DEFAULT))
                        .setCurrentJoinYoy(
                                String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT, JOIN_YOY_ACCOUNT_CODE_DEFAULT))
                        .setCurrentJoinYoyBeforeLast(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                                JOIN_YOYBEFORELAST_ACCOUNT_CODE_DEFAULT));
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
                analysisObject = analysisObjectEnum.toString().toLowerCase().concat("_custom");
                break;
            case COSTCENTER1:
                analysisObject = "costcenter1_custom";
                break;
            case COSTCENTER2:
                analysisObject = "costcenter2_custom";
                break;
            case COSTCENTER3:
                analysisObject = "costcenter3_custom";
                break;
            case COSTCENTER4:
                analysisObject = "costcenter4_custom";
                break;
            case COSTCENTER5:
                analysisObject = "costcenter5_custom";
                break;
            case COSTCENTER6:
                analysisObject = "costcenter6_custom";
                break;
            case ORG:
                analysisObject = getAnalysisObjectOrgInfo(analysisObjectOrgInfo);
                break;
            case UID:
                biz.setResultFields("current.uid as UID,current.user_name as NAME")
                        .setResultFieldsAlias(" uid, user_name")
                        .setGroupFields(" uid, user_name")
                        .setOrderByFields("uid, user_name ")
                        .setCurrentJoinMom(JOIN_MOM_UID_DEFAULT).setCurrentJoinYoy(JOIN_YOY_UID_DEFAULT)
                        .setCurrentJoinYoyBeforeLast(JOIN_YOYBEFORELAST_UID_DEFAULT);
                break;
            default:
                break;
        }
        if (StringUtils.isNotEmpty(analysisObject) && analysisObjectEnum != AnalysisObjectEnum.ORG) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            String temp = "case when (" + analysisObject + " is null or " + analysisObject + " = '') " +
                    "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString();
            String temp1 = "case when current." + analysisObjectEnum.toString() + " = '" + other + "' then '' else current." + analysisObjectEnum.toString() + " end as dimId";
            biz = biz
                    .setResultFields("current.corp_name as CORP, current." + analysisObjectEnum.toString() + " as " + analysisObjectEnum + "," + temp1)
                    .setResultFieldsAlias(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, temp))
                    .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, analysisObjectEnum.toString()))
                    .setOrderByFields("corp_corporation, corp_name " + ", " + analysisObjectEnum.toString())
                    .setCurrentJoinMom(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT,
                            String.format("current.%s = mom.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())))
                    .setCurrentJoinYoy(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT,
                            String.format("current.%s = yoy.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())))
                    .setCurrentJoinYoyBeforeLast(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                            String.format("current.%s = yoyBeforeLast.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())));

        }
        if (analysisObjectEnum == AnalysisObjectEnum.ORG) {
            String other = SharkUtils.isZH(lang) ? OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.Empty") : "(Empty)";
            String temp = "case when (" + analysisObject + " is null or " + analysisObject + " = '') " +
                    "then '" + other + "' else " + analysisObject + " end as " + analysisObjectEnum.toString();
            String temp1 = "case when current." + analysisObjectEnum.toString() + " = '" + other + "' then '' else current." + analysisObjectEnum.toString() + " end as dimId";

            biz = biz.setResultFields(" current.corp_name as CORP, current." + analysisObjectEnum.toString() + " as " + analysisObjectEnum.toString() + "," + temp1)
                    .setResultFieldsAlias(" corp_corporation, corp_name, " + temp)
                    .setGroupFields(String.format(GROUP_SQL_FORMAT_DEFAULT, SQL_DEFAULT, analysisObjectEnum.toString()))
                    .setOrderByFields("corp_corporation, corp_name " + ", " + analysisObjectEnum.toString())
                    .setCurrentJoinMom(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_MOM_CORP_DEFAULT,
                            String.format("current.%s = mom.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())))
                    .setCurrentJoinYoy(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOY_CORP_DEFAULT,
                            String.format("current.%s = yoy.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())))
                    .setCurrentJoinYoyBeforeLast(String.format(TWO_JOIN_FORMAT_DEFAULT, JOIN_YOYBEFORELAST_CORP_DEFAULT,
                            String.format("current.%s = yoyBeforeLast.%s", analysisObjectEnum.toString(), analysisObjectEnum.toString())));
        }
        return biz;
    }

    private String getAnalysisObjectOrgInfo(AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        int level = analysisObjectOrgInfo.getLevel();
        switch (level) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
                return String.format("visitParamExtractString(orginfo, 'org%d')", analysisObjectOrgInfo.getLevel() + 1);
            default:
                return "";
        }
    }

    private String getAnalysisObjectOrgInfoCondition(AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                                     AnalysisObjectEnum analysisObjectEnum, List<Object> paramList) {
        StringBuilder stringBuilder = new StringBuilder();
        if (analysisObjectOrgInfo == null || StringUtils.isEmpty(analysisObjectOrgInfo.getOrgName()) || analysisObjectEnum != AnalysisObjectEnum.ORG) {
            return stringBuilder.toString();
        }
        stringBuilder.append(" AND coalesce(orginfo, '') != '' ");
        if (analysisObjectOrgInfo.getLevel() > 0) {
            stringBuilder.append(String.format(" AND visitParamExtractString(orginfo, 'org%d') = ? ", analysisObjectOrgInfo.getLevel()));
            paramList.add(analysisObjectOrgInfo.getOrgName());
        }
        return stringBuilder.toString();
    }

    private String getAnalysisObjectOrgInfoCondition(AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                                     AnalysisObjectEnum analysisObjectEnum, String val, List<Object> paramList) {
        StringBuilder stringBuilder = new StringBuilder();
        if (analysisObjectOrgInfo == null || StringUtils.isEmpty(analysisObjectOrgInfo.getOrgName()) || analysisObjectEnum != AnalysisObjectEnum.ORG) {
            return stringBuilder.toString();
        }
        stringBuilder.append(" AND coalesce(orginfo, '') != '' ");
        if (analysisObjectOrgInfo.getLevel() > 0) {
            stringBuilder.append(String.format(" AND visitParamExtractString(orginfo, 'org%d') = ? ", analysisObjectOrgInfo.getLevel()));
            paramList.add(val);
        }
        return stringBuilder.toString();
    }

    /**
     * 具体查询条件
     *
     * @param analysisObjectEnum
     * @param analysisObjectVal
     * @param analysisObjectOrgInfo
     * @param subQueryReportBuTypeEnum
     * @return
     */
    public String downConditionWithSpecialCol(AnalysisObjectEnum analysisObjectEnum, String analysisObjectVal, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                              SubQueryReportBuTypeEnum subQueryReportBuTypeEnum,
                                              List<Object> paramList) {
        String conditon = StringUtils.EMPTY;
        if (analysisObjectEnum == null || StringUtils.isEmpty(analysisObjectVal)) {
            return conditon;
        }
        switch (analysisObjectEnum) {
            case CORP:
                if (subQueryReportBuTypeEnum == SubQueryReportBuTypeEnum.BUS_TICKETS) {
                    conditon = " and corpid = ? ";
                    paramList.add(analysisObjectVal);
                } else if (subQueryReportBuTypeEnum == SubQueryReportBuTypeEnum.VASO_ORDER) {
                    conditon = " and company = ? ";
                    paramList.add(analysisObjectVal);
                } else {
                    conditon = " and corp_corporation = ? ";
                    paramList.add(analysisObjectVal);
                }
                break;
            case ACCOUNT:
            case ACCOUNTCODE:
                if (subQueryReportBuTypeEnum == SubQueryReportBuTypeEnum.BUS_TICKETS || subQueryReportBuTypeEnum == SubQueryReportBuTypeEnum.VASO_ORDER) {
                    conditon = " and accountid = ? ";
                    paramList.add(Integer.valueOf(analysisObjectVal));
                } else {
                    conditon = " and account_id = ? ";
                    paramList.add(Integer.valueOf(analysisObjectVal));
                }
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
            case COSTCENTER1:
            case COSTCENTER2:
            case COSTCENTER3:
            case COSTCENTER4:
            case COSTCENTER5:
            case COSTCENTER6:
                if (ConfigUtils.getBoolean("custom_dim_open", false)) {
                    conditon = String.format(" and %s_custom = ? ", analysisObjectEnum.toString().toLowerCase());
                    paramList.add(analysisObjectVal);
                } else {
                    conditon = String.format(" and %s = ? ", analysisObjectEnum.toString().toLowerCase());
                    paramList.add(analysisObjectVal);
                }
                break;
            case ORG:
                conditon = getAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo, analysisObjectEnum,
                        analysisObjectVal, paramList);
                break;
            case UID:
                conditon = " and uid = ? ";
                paramList.add(analysisObjectVal);
                break;
            default:
                break;
        }
        return conditon;
    }

    @Data
    @Accessors(chain = true)
    class JoinCondition {
        private String resultFields;

        private String groupFields;

        private String currentJoinMom;

        private String currentJoinYoy;

        private String currentJoinYoyBeforeLast;

        private String orderByFields;

        private String resultFieldsAlias;
    }
}
