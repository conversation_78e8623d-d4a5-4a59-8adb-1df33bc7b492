package com.corpgovernment.resource.schedule.onlinereport.clickhouse;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.enums.StatiscalCaliberEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.DbResultMapUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.PreparedStatement;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.ORDERDT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants.REPORT_DATE;


/**
 * <AUTHOR>
 * @date 2022-09-21 13:39
 * @desc
 */
@Slf4j
public abstract class AbstractCommonDao extends AbstractClickhouseBaseDao {

    private static final String LOG_TITLE = "AbstractCarbonsDao";

    private final static String COUNT_ALIAS = "countAll";

    /**
     * @param clazz
     * @param sql
     * @param parmList
     * @param <T>
     * @return
     * @throws Exception
     */
    protected <T> List<T> commonList(Class<T> clazz, String sql, List<Object> parmList) throws Exception {
        long startTime = System.currentTimeMillis();
        List<T> commonList = queryBySql(sql, parmList,
                (req, statement) -> mapCommonRequest(parmList, statement), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "commonList");
        log.info("queryDetail执行queryOrderDetail后innner_commonList耗时：{}",System.currentTimeMillis()-startTime);
        return commonList;
    }

    /**
     * @param clazz
     * @param sql
     * @param parmList
     * @param <T>
     * @return
     * @throws Exception
     */
    protected <T> List<T> commonList(Class<T> clazz, String sql, List<Object> parmList, boolean ignoreTenantId) throws Exception {

        return queryBySql(sql, parmList,
                (req, statement) -> mapCommonRequest(parmList, statement), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "commonList", ignoreTenantId);
    }


    protected <T> List<T> commonListMySql(Class<T> clazz, String sql, List<Object> parmList) throws Exception {
        return queryBySqlMySql(sql, parmList,
                (req, statement) -> mapCommonRequest(parmList, statement), (u, d) -> {
                    try {
                        return mapResultList(u, d);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, clazz, "commonList");
    }


    /**
     * @param sql
     * @param parmList
     * @return
     * @throws Exception
     */
    protected List<Map> commonList(String sql, List<Object> parmList) throws Exception {
        return queryBySql(sql, parmList,
                (req, statement) -> mapCommonRequest(parmList, statement), (u, d) -> {
                    try {
                        return DbResultMapUtils.mapResultList(u);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return Lists.newArrayList();
                }, Map.class, "commonList");
    }

    /**
     * @param sql
     * @param paramList
     * @return
     * @throws Exception
     */
    protected Integer commonCount(String sql, List<Object> paramList) throws Exception {
        return queryBySql(sql, paramList, (req, statement) -> mapCommonRequest(paramList, statement),
                (u, d) -> {
                    try {
                        return mapIntResult(u, COUNT_ALIAS);
                    } catch (Exception e) {
                        log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
                    }
                    return OrpConstants.ZERO;
                }, Integer.class, "commonCount");
    }


    protected PreparedStatement mapCommonRequest(List<Object> parmList, PreparedStatement statement,
                                                 ClickHouseTable clickHouseTable) {
        int index = OrpConstants.ONE;
        try {
            // 分区
            String x = queryPartition(clickHouseTable);
            statement.setString(index++, x);

            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }

            parmList.add(0, x);
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }

    @Override
    protected PreparedStatement mapCommonRequest(List<Object> parmList, PreparedStatement statement) {
        int index = OrpConstants.ONE;
        try {
            for (int i = 0; i < parmList.size(); i++) {
                statement.setObject(index++, parmList.get(i));
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getFullStackTrace(e));
        }
        return statement;
    }

    /**
     * 机票类型查询条件
     *
     * @param flightClass
     * @return
     */
    protected String getFlightClassConditionWithAudited(String flightClass) {
        StringBuilder stringBuilder = new StringBuilder(" and audited <> 'F' ");
        if (StringUtils.equalsIgnoreCase(flightClass, "dom")) {
            stringBuilder.append(" and flight_class = 'N'  ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(flightClass, "inter")) {
            stringBuilder.append(" and flight_class = 'I'  ");
        }
        return stringBuilder.toString();
    }

    /**
     * 机票类型查询条件
     *
     * @param flightClass
     * @return
     */
    protected String getFlightClassConditionNoAudited(String flightClass) {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.equalsIgnoreCase(flightClass, "dom")) {
            stringBuilder.append(" and flight_class = 'N'  ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(flightClass, "inter")) {
            stringBuilder.append(" and flight_class = 'I'  ");
        }
        return stringBuilder.toString();
    }

    /**
     * 酒店类型查询条件
     *
     * @param hotelType
     * @return
     */
    protected String getHotelOrderTypeCondition(String hotelType) {
        StringBuilder stringBuilder = new StringBuilder(String.format(" and order_status = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus")));
        // 国内
        if (StringUtils.equalsIgnoreCase(hotelType, "domf")) {
            stringBuilder.append(" and is_oversea in ('F') ");
        }
        // 国内、港澳台
        if (StringUtils.equalsIgnoreCase(hotelType, "dom")) {
            stringBuilder.append(" and is_oversea in ('F','O') ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(hotelType, "inter")) {
            stringBuilder.append(" and is_oversea in ('T') ");
        }
        return stringBuilder.toString();
    }

    /**
     * 火车状态限制
     *
     * @return
     */
    protected String getTrainOrderStatusCondition() {
        return " and order_status  in ('TA','RP','EP','EA','RA')";
    }

    protected String getTrainTypeCondition(String trainType) {
        StringBuilder stringBuilder = new StringBuilder();
        // 火车没有国际 所以all 和dom 不用做区分
//        if (StringUtils.equalsIgnoreCase(trainType, "all") || StringUtils.equalsIgnoreCase(trainType, "dom")) {
//            stringBuilder.append(" and (pos = 'ZH-CN' or pos =='')");
//        }
        if (StringUtils.equalsIgnoreCase(trainType, "inter")) {
            stringBuilder.append(" and pos != 'ZH-CN'  and pos!=''");
        }
        return stringBuilder.toString();
    }

    /**
     * 酒店协议会员
     *
     * @param orderType
     * @return
     */
    protected String getHtlTypeCondition(String orderType) {
        // 会员
        if (StringUtils.equalsIgnoreCase(orderType, "M")) {
            return " and order_type = 'M' ";
        }
        // 协议
        if (StringUtils.equalsIgnoreCase(orderType, "C")) {
            return " and order_type in 'C' ";
        }
        return StringUtils.EMPTY;
    }

    protected String getHtlContractTypeCondition(String contractType) {
        StringBuilder stringBuilder = new StringBuilder();
        // 三方协议
        if (StringUtils.equalsIgnoreCase(contractType, "C")) {
            stringBuilder.append(" and order_type = 'C'  ");
        }
        // 非三方协议
        if (StringUtils.equalsIgnoreCase(contractType, "NC")) {
            stringBuilder.append(" and order_type = 'M'  ");
        }
        return stringBuilder.toString();
    }

    /**
     * 酒店类型查询条件
     *
     * @param hotelType
     * @return
     */
    protected String getHotelOrderTypeConditionNoOrderStatus(String hotelType) {
        StringBuilder stringBuilder = new StringBuilder();
        // 国内
        if (StringUtils.equalsIgnoreCase(hotelType, "domf")) {
            stringBuilder.append(" and is_oversea in ('F') ");
        }
        // 国内、港澳台
        if (StringUtils.equalsIgnoreCase(hotelType, "dom")) {
            stringBuilder.append(" and is_oversea in ('F','O') ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(hotelType, "inter")) {
            stringBuilder.append(" and is_oversea in ('T') ");
        }
        return stringBuilder.toString();
    }

    public String getCarOrderTypeCondition(String orderType) {
        // airportpick接送机,airportpickDom接送机,airportpickInter接送机,Charter包车,rent租车,tax打车,taxDom国内打车,taxInter国际打车
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "airportpick")) {
            return " and order_type IN (1,2) ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "airportpickDom")) {
            return " and order_type = 1 ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "airportpickInter")) {
            return " and order_type = 2 ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "charter")) {
            return " and order_type = 3 ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "rent")) {
            return " and order_type = 4 ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "tax")) {
            return " and order_type = 6 ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "taxDom")) {
            return " and order_type = 6 and sub_product_line = '1' ";
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(orderType, "taxInter")) {
            return " and order_type = 6 and sub_product_line = 'CAR_TAXI_INTL'";
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }

    /**
     * 用车类型查询条件
     *
     * @param productType
     * @return
     */
    protected String getCarProductTypeCondition(String productType) {
        StringBuilder stringBuilder = new StringBuilder();
        // 国内
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            // 3、4不区分国内、国际
            stringBuilder.append(" and (order_type in(1,3,4) or (order_type = 6 and sub_product_line = '1')) ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            // 3、4不区分国内、国际
            stringBuilder.append(" and (order_type in(2,3,4) or (order_type = 6 and sub_product_line = 'CAR_TAXI_INTL')) ");
        }
        return stringBuilder.toString();
    }


    /**
     * @param paramList
     * @param pager
     * @return
     */
    protected String buildPage(List<Object> paramList, Pager pager) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" limit ?, ? ");
        paramList.add((pager.pageIndex - 1) * pager.pageSize);
        paramList.add(pager.pageSize);
        return stringBuilder.toString();
    }

    /**
     * 根据统计口径获得查询的的时间字段
     *
     * @param statisticalCaliber
     * @return
     */
    public String getDateFieldByCaliber(String statisticalCaliber) {
        return StringUtils.equalsIgnoreCase(statisticalCaliber, StatiscalCaliberEnum.BOOKING.name()) ? ORDERDT : REPORT_DATE;
    }

    /**
     * 构建机票协议/非协议条件（C：协议；NC：非协议）
     *
     * @param contractType
     * @return
     */
    protected String getFlightContractTypeCondition(String contractType) {
        if (StringUtils.isEmpty(contractType)) {
            return StringUtils.EMPTY;
        }
        StringBuilder stringBuilder = new StringBuilder();
        String ta = OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.threeAgreement");
        String taListStr = Lists.newArrayList(ta, "B2G").stream().map(item -> String.format("'%s'", item))
                .collect(Collectors.joining(", ", "(", ")"));

        if (StringUtils.equalsIgnoreCase(contractType, "C")) {
            stringBuilder.append(" and agreement_type_name in " + taListStr);
        }
        if (StringUtils.equalsIgnoreCase(contractType, "NC")) {
            stringBuilder.append(" and agreement_type_name not in " + taListStr);
        }
        return stringBuilder.toString();
    }
}
