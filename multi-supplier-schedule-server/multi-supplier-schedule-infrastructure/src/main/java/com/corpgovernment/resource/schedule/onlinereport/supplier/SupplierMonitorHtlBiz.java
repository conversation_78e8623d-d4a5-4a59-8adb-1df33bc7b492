package com.corpgovernment.resource.schedule.onlinereport.supplier;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSupplierHtlTop;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSupplierTopRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.SupplierHtlTopInfo;
import com.corpgovernment.resource.schedule.onlinereport.AbstractDeptDetailCommonBiz;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.supplier.HtlSupplierMonitorDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.supplier.convert.SupplierTrendMapper;
import com.corpgovernment.resource.schedule.onlinereport.supplier.dto.HotelTrendDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-09-06 10:35
 * @desc
 */
@Service
public class SupplierMonitorHtlBiz extends AbstractDeptDetailCommonBiz {

    @Autowired
    private HtlSupplierMonitorDaoImpl htlSupplierMonitorDaoImpl;
    @Autowired
    SupplierTrendMapper supplierTrendMapper;

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;



    
    public SupplierHtlTopInfo trend(OnlineReportSupplierTopRequest request) throws Exception {

        BaseQueryConditionDTO baseQueryConditionDTO = baseQueryConditionMapper.toDTO(request.getBasecondition());
        baseQueryConditionDTO.setProductType(request.getProductType());
        SupplierHtlTopInfo result = new SupplierHtlTopInfo();
        List<OnlineReportSupplierHtlTop> topList = new ArrayList<>();
        result.setTopList(topList);
        Map map = Optional.ofNullable(request.getExtData()).orElse(new HashMap<>());
        String dim = (String) map.get("dim");
        String contractType = (String) map.get("contractType");
        String needCorp = (String) map.get("needCorp");
        List<HotelTrendDTO> list = htlSupplierMonitorDaoImpl.queryTop(baseQueryConditionDTO, HotelTrendDTO.class, dim, contractType, needCorp);
        list.sort((o1, o2) -> o2.getSumAmount().subtract(o1.getSumAmount()).intValue());

        HotelTrendDTO sumTop = new HotelTrendDTO();
        HotelTrendDTO sumOther = new HotelTrendDTO();
        HotelTrendDTO sumAll = new HotelTrendDTO();
        int topLimit = Optional.ofNullable(request.getTopLimit()).orElse(OrpConstants.TEN);
        if (CollectionUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                HotelTrendDTO deptConsume = list.get(i);
                selfAdd(sumAll, deptConsume);
                if (i < topLimit) {
                    selfAdd(sumTop, deptConsume);
                    topList.add(convertToFltBO(deptConsume));
                } else {
                    // 超过topLimit以外的数据都聚合成“other”
                    selfAdd(sumOther, deptConsume);
                    result.setOtherInfo(convertToFltBO(sumOther));
                }
            }
            result.setSumInfo(convertToFltBO(sumAll));
            ;
        }
        return result;
    }

    private void selfAdd(HotelTrendDTO sumTop, HotelTrendDTO deptConsume) {
        sumTop.setSumAmount(deptConsume.getSumAmount().add(Optional.ofNullable(sumTop.getSumAmount()).orElse(BigDecimal.ZERO)));
        sumTop.setSumRoomPrice(deptConsume.getSumRoomPrice().add(Optional.ofNullable(sumTop.getSumRoomPrice()).orElse(BigDecimal.ZERO)));
        sumTop.setSumQuantity(deptConsume.getSumQuantity() + Optional.ofNullable(sumTop.getSumQuantity()).orElse(OrpConstants.ZERO));

        sumTop.setSumAmountTa(deptConsume.getSumAmountTa().add(Optional.ofNullable(sumTop.getSumAmountTa()).orElse(BigDecimal.ZERO)));
        sumTop.setSumRoomPriceTa(deptConsume.getSumRoomPriceTa().add(Optional.ofNullable(sumTop.getSumRoomPriceTa()).orElse(BigDecimal.ZERO)));
        sumTop.setSumQuantityTa(deptConsume.getSumQuantityTa() + Optional.ofNullable(sumTop.getSumQuantityTa()).orElse(OrpConstants.ZERO));
        sumTop.setCorpSumRoomPriceTa(Optional.ofNullable(deptConsume.getCorpSumRoomPriceTa()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getCorpSumRoomPriceTa()).orElse(BigDecimal.ZERO)));
        sumTop.setCorpSumQuantityTa(Optional.ofNullable(deptConsume.getCorpSumQuantityTa()).orElse(OrpConstants.ZERO)
                + Optional.ofNullable(sumTop.getCorpSumQuantityTa()).orElse(OrpConstants.ZERO));
        sumTop.setSumAmountNta(deptConsume.getSumAmountNta().add(Optional.ofNullable(sumTop.getSumAmountNta()).orElse(BigDecimal.ZERO)));
        sumTop.setSumRoomPriceNta(deptConsume.getSumRoomPriceNta().add(Optional.ofNullable(sumTop.getSumRoomPriceNta()).orElse(BigDecimal.ZERO)));
        sumTop.setSumQuantityNta(deptConsume.getSumQuantityNta() + Optional.ofNullable(sumTop.getSumQuantityNta()).orElse(OrpConstants.ZERO));
        sumTop.setCorpSumRoomPriceNta(Optional.ofNullable(deptConsume.getCorpSumRoomPriceNta()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getCorpSumRoomPriceNta()).orElse(BigDecimal.ZERO)));
        sumTop.setCorpSumQuantityNta(Optional.ofNullable(deptConsume.getCorpSumQuantityNta()).orElse(OrpConstants.ZERO)
                + Optional.ofNullable(sumTop.getCorpSumQuantityNta()).orElse(OrpConstants.ZERO));

        sumTop.setSumAmountDom(Optional.ofNullable(deptConsume.getSumAmountDom()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getSumAmountDom()).orElse(BigDecimal.ZERO)));
        sumTop.setSumRoomPriceDom(Optional.ofNullable(deptConsume.getSumRoomPriceDom()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getSumRoomPriceDom()).orElse(BigDecimal.ZERO)));
        sumTop.setSumQuantityDom(Optional.ofNullable(deptConsume.getSumQuantityDom()).orElse(OrpConstants.ZERO)
                + Optional.ofNullable(sumTop.getSumQuantityDom()).orElse(OrpConstants.ZERO));
        sumTop.setSumAmountInter(Optional.ofNullable(deptConsume.getSumAmountInter()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getSumAmountInter()).orElse(BigDecimal.ZERO)));
        sumTop.setSumRoomPriceInter(Optional.ofNullable(deptConsume.getSumRoomPriceInter()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getSumRoomPriceInter()).orElse(BigDecimal.ZERO)));
        sumTop.setSumQuantityInter(Optional.ofNullable(deptConsume.getSumQuantityInter()).orElse(OrpConstants.ZERO)
                + Optional.ofNullable(sumTop.getSumQuantityInter()).orElse(OrpConstants.ZERO));

        sumTop.setSumAmountFirstTier(Optional.ofNullable(deptConsume.getSumAmountFirstTier()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getSumAmountFirstTier()).orElse(BigDecimal.ZERO)));
        sumTop.setSumRoomPriceFirstTier(Optional.ofNullable(deptConsume.getSumRoomPriceFirstTier()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getSumRoomPriceFirstTier()).orElse(BigDecimal.ZERO)));
        sumTop.setSumQuantityFirstTier(Optional.ofNullable(deptConsume.getSumQuantityFirstTier()).orElse(OrpConstants.ZERO)
                + Optional.ofNullable(sumTop.getSumQuantityFirstTier()).orElse(OrpConstants.ZERO));
        sumTop.setSumAmountNonFirstTier(Optional.ofNullable(deptConsume.getSumAmountNonFirstTier()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getSumAmountNonFirstTier()).orElse(BigDecimal.ZERO)));
        sumTop.setSumRoomPriceNonFirstTier(Optional.ofNullable(deptConsume.getSumRoomPriceNonFirstTier()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(sumTop.getSumRoomPriceNonFirstTier()).orElse(BigDecimal.ZERO)));
        sumTop.setSumQuantityNonFirstTier(Optional.ofNullable(deptConsume.getSumQuantityNonFirstTier()).orElse(OrpConstants.ZERO)
                + Optional.ofNullable(sumTop.getSumQuantityNonFirstTier()).orElse(OrpConstants.ZERO));
    }

    private OnlineReportSupplierHtlTop convertToFltBO(HotelTrendDTO topDeptDTO) {
        OnlineReportSupplierHtlTop fltTopDeptBO = new OnlineReportSupplierHtlTop();
        fltTopDeptBO.setDim(topDeptDTO.getDim());
        fltTopDeptBO.setSumAmount(OrpReportUtils.formatBigDecimal(topDeptDTO.getSumAmount()));
        fltTopDeptBO.setSumQuantity(topDeptDTO.getSumQuantity());
        fltTopDeptBO.setAvgPrice(OrpReportUtils.divide(topDeptDTO.getSumRoomPrice(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getSumQuantity()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));

        fltTopDeptBO.setSumAmountTa(OrpReportUtils.formatBigDecimal(topDeptDTO.getSumAmountTa()));
        fltTopDeptBO.setSumQuantityTa(topDeptDTO.getSumQuantityTa());
        fltTopDeptBO.setAvgPriceTa(OrpReportUtils.divide(topDeptDTO.getSumRoomPriceTa(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getSumQuantityTa()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
        fltTopDeptBO.setCorpAvgPriceTa(OrpReportUtils.divide(topDeptDTO.getCorpSumRoomPriceTa(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getCorpSumQuantityTa()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));

        fltTopDeptBO.setSumAmountNta(OrpReportUtils.formatBigDecimal(topDeptDTO.getSumAmountNta()));
        fltTopDeptBO.setSumQuantityNta(topDeptDTO.getSumQuantityNta());
        fltTopDeptBO.setAvgPriceNta(OrpReportUtils.divide(topDeptDTO.getSumRoomPriceNta(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getSumQuantityNta()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
        fltTopDeptBO.setCorpAvgPriceNta(OrpReportUtils.divide(topDeptDTO.getCorpSumRoomPriceNta(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getCorpSumQuantityNta()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));

        fltTopDeptBO.setSumAmountDom(OrpReportUtils.formatBigDecimal(topDeptDTO.getSumAmountDom()));
        fltTopDeptBO.setSumQuantityDom(topDeptDTO.getSumQuantityDom());
        fltTopDeptBO.setAvgPriceDom(OrpReportUtils.divide(topDeptDTO.getSumRoomPriceDom(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getSumQuantityDom()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
        fltTopDeptBO.setSumAmountInter(OrpReportUtils.formatBigDecimal(topDeptDTO.getSumAmountInter()));
        fltTopDeptBO.setSumQuantityInter(topDeptDTO.getSumQuantityInter());
        fltTopDeptBO.setAvgPriceInter(OrpReportUtils.divide(topDeptDTO.getSumRoomPriceInter(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getSumQuantityInter()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));

        fltTopDeptBO.setSumAmountFirstTier(OrpReportUtils.formatBigDecimal(topDeptDTO.getSumAmountFirstTier()));
        fltTopDeptBO.setSumQuantityFirstTier(topDeptDTO.getSumQuantityFirstTier());
        fltTopDeptBO.setAvgPriceFirstTier(OrpReportUtils.divide(topDeptDTO.getSumRoomPriceFirstTier(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getSumQuantityFirstTier()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
        fltTopDeptBO.setSumAmountNonFirstTier(OrpReportUtils.formatBigDecimal(topDeptDTO.getSumAmountNonFirstTier()));
        fltTopDeptBO.setSumQuantityNonFirstTier(topDeptDTO.getSumQuantityNonFirstTier());
        fltTopDeptBO.setAvgPriceNonFirstTier(OrpReportUtils.divide(topDeptDTO.getSumRoomPriceNonFirstTier(),
                new BigDecimal(Optional.ofNullable(topDeptDTO.getSumQuantityNonFirstTier()).orElse(OrpConstants.ZERO)), OrpConstants.TWO));
        return fltTopDeptBO;
    }
}
