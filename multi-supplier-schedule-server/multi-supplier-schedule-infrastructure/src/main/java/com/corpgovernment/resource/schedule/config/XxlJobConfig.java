package com.corpgovernment.resource.schedule.config;

import com.corpgovernment.common.apollo.JobCenterApollo;
import com.corpgovernment.common.constant.CommonConst;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * xxl-job config
 *
 * <AUTHOR> 2017-04-28
 */
@Configuration
@Slf4j
public class XxlJobConfig {

    @Value("${xxl.job.admin.addresses}")
    private String xxlJobAdminAddresses;
    @Value("${xxl.job.accessToken}")
    private String xxlJobAccessToken;
    @Value("${xxl.job.executor.appname}")
    private String xxlJobExecutorAppname;
    @Value("${xxl.job.executor.address}")
    private String xxlJobExecutorAddress;
    @Value("${xxl.job.executor.ip}")
    private String xxlJobExecutorIp;
    @Value("${xxl.job.executor.port}")
    private int xxlJobExecutorPort;
    @Value("${xxl.job.executor.logpath}")
    private String xxlJobExecutorLogpath;
    @Value("${xxl.job.executor.logretentiondays}")
    private int xxlJobExecutorLogretentiondays;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        log.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(xxlJobAdminAddresses);
        xxlJobSpringExecutor.setAppname(xxlJobExecutorAppname);
        xxlJobSpringExecutor.setAddress(xxlJobExecutorAddress);
        xxlJobSpringExecutor.setIp(xxlJobExecutorIp);
        xxlJobSpringExecutor.setPort(xxlJobExecutorPort);
        xxlJobSpringExecutor.setAccessToken(xxlJobAccessToken);
        xxlJobSpringExecutor.setLogPath(xxlJobExecutorLogpath);
        xxlJobSpringExecutor.setLogRetentionDays(xxlJobExecutorLogretentiondays);
        return xxlJobSpringExecutor;
    }

}