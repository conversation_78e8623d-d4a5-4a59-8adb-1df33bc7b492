package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportHtlOrderAuditInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportQueryHtlOrderAuditRequest;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.HtlOrderAudidDetailDaoImpl;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.convert.HtlOrderDetailMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.module.repotlib.HotelOrderAuditDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-17 00:04
 * @desc 酒店夜审数据
 */
@Service
public class HtlOrderAuditBiz {

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;

    @Autowired
    private HtlOrderAudidDetailDaoImpl htlOrderAudidDetailDao;

    @Autowired
    private HtlOrderDetailMapper detailMapper;


    public List<OnlineReportHtlOrderAuditInfo> queryOrderDetail(OnlineReportQueryHtlOrderAuditRequest request) throws Exception {
        BaseQueryConditionDTO base = convertRequest(request);
        List<HotelOrderAuditDTO> htlOrderAudidDetailDTOS = htlOrderAudidDetailDao.queryDetail(base, HotelOrderAuditDTO.class);
        if (CollectionUtils.isNotEmpty(htlOrderAudidDetailDTOS)) {
            return detailMapper.toAuditDTOs(htlOrderAudidDetailDTOS);
        }
        return ListUtils.EMPTY_LIST;
    }

    public Integer queryOrderDetailCount(OnlineReportQueryHtlOrderAuditRequest request) throws Exception {
        BaseQueryConditionDTO base = convertRequest(request);
        return htlOrderAudidDetailDao.detailCount(base);
    }

    private BaseQueryConditionDTO convertRequest(OnlineReportQueryHtlOrderAuditRequest request) {
        BaseQueryConditionDTO base = baseQueryConditionMapper.toDTO(request.getBasecondition());
        base.setOrderIds(request.getOrderIds());
        base.setUsers(request.getUsers());
        base.setPager(BizUtils.initPagerStartOne(request.getPage()));
        return base;
    }
}
