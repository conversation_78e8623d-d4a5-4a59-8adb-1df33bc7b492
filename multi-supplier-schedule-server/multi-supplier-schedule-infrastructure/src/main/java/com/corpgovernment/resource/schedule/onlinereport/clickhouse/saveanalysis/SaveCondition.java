package com.corpgovernment.resource.schedule.onlinereport.clickhouse.saveanalysis;

import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpCommonUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date ：Created in 2021/11/10 10:26
 * @description：
 * @modified By：
 * @version: $
 */
public class SaveCondition {

    public static String getHotelCondition(String productType) {
        StringBuilder stringBuilder = new StringBuilder(String.format(" and order_status = '%s'",
                OrpCommonUtils.getProperties(OrpConstants.CHINESE_FILE, "Report.hotel.orderstatus")));
        // 国内、港澳台
        if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            stringBuilder.append(" and is_oversea in ('F','O') ");
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            stringBuilder.append(" and is_oversea in ('T') ");
        }
        return stringBuilder.toString();
    }

    public static String getFlightClassCondition(String flightClass) {
        if (StringUtils.equalsIgnoreCase(flightClass, "dom")) {
            return " and flight_class = 'N' ";
        }
        // 海外
        if (StringUtils.equalsIgnoreCase(flightClass, "inter")) {
            return " and flight_class = 'I' ";
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }
}
