package com.corpgovernment.resource.schedule.onlinereport.clickhouse.base;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;

/**
 * PreparedStatement 代理类，用于记录 PreparedStatement 的参数。后续可以通过 getFullSql() 方法获取完整的 SQL 语句
 * <AUTHOR>
 */
public class LoggingPreparedStatementProxy implements InvocationHandler {
    private final PreparedStatement target;
    private final String sql;
    private final List<Object> parameters = new ArrayList<>();

    public LoggingPreparedStatementProxy(PreparedStatement target, String sql) {
        this.target = target;
        this.sql = sql;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        if (method.getName().startsWith("set") && args != null && args.length > 1) {
            // 记录参数
            parameters.add(args[1]);
        }
        return method.invoke(target, args);
    }

    public String getFullSql() {
        String fullSql = sql;
        for (Object param : parameters) {
            fullSql = fullSql.replaceFirst("\\?", param instanceof String ? "'" + param + "'" : String.valueOf(param));
        }
        return fullSql;
    }

    public static PreparedStatement createProxy(PreparedStatement stmt, String sql) {
        return (PreparedStatement) Proxy.newProxyInstance(
                PreparedStatement.class.getClassLoader(),
                new Class<?>[]{PreparedStatement.class},
                new LoggingPreparedStatementProxy(stmt, sql)
        );
    }
}
