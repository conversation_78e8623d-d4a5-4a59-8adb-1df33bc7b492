package com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/9/18 11:32
 * @Desc
 */
@Data
public class TrainEntity {
    private Long order_id;//	"订单号",
    private String order_status;//	"订单状态",
    private String order_date;//	"订单预定时间",
    private String corp_corporation;//	"公司ID",
    private String corp_name;//	"公司名称",
    private String companygroupid;//	"公司集团ID",
    private String companygroup;//	"公司集团",
    private String account_id;//	"主账户账号",
    private String account_code;//	"主账户代号",
    private String account_name;//	"主账户公司名称",
    private String sub_account_id;//	"子账户账号",
    private String sub_account_code;//	"子账户代号",
    private String sub_account_name;//	"子账户公司名称",
    private String industry_type;//	"行业类型",
    private String industry_type_name;//	"行业名称",
    private String uid;//	"持卡人卡号",
    private String user_name;//	"持卡人姓名",
    private String employe_id;//	"持卡人员工编号",
    private String work_city;//	"工作所在城市",
    private String rank_name;//	"持卡人职级CN",
    private String cost_center1;//	"成本中心1",
    private String cost_center2;//	"成本中心2",
    private String cost_center3;//	"成本中心3",
    private String cost_center4;//	"成本中心4",
    private String cost_center5;//	"成本中心5",
    private String cost_center6;//	"成本中心6",
    private String dept1;//	"部门1",
    private String dept2;//	"部门2",
    private String dept3;//	"部门3",
    private String dept4;//	"部门4",
    private String dept5;//	"部门5",
    private String dept6;//	"部门6",
    private String dept7;//	"部门7",
    private String dept8;//	"部门8",
    private String dept9;//	"部门9",
    private String dept10;//	"部门10",
    private String verbal_authorize;//	"是否口头授权T/F",
    private String confirm_person;//	"一次授权人姓名",
    private String confirm_type;//	"一次授权方式",
    private String confirm_person2;//	"二次授权人姓名",
    private String confirm_type2;//	"二次授权方式",
    private String fee_type;//	"是否个人消费行为:因公,因私,",
    private String is_online;//	"预订方式",
    private String prepay_type;//	"支付方式（ACCNT:公司账户CCARD:信用卡CASH:现付）",
    private String acb_prepay_type;//	"结算类型",
    private String bosstype;//	"是否BOSS(T/F)",
    private String trip_id;//	"所属行程订单号",
    private String journey_no;//	"关联行程单号",
    private String journey_reason;//	"出行目的",
    private String journey_reason_code;//	"出行目的编号",
    private String project_code;//	"项目编号",
    private String project;//	"项目名称",
    private Integer group_month;//	"年月",
    private Integer print_month;//	"出票、退票审核月份",
    private Integer print_year;//	"出票日期-年",
    private String print_time;//	"出票时间",
    private Long passenger_id;//	"乘客id",
    private String passenger_name;//	"乘客姓名",
    private String passenger_name_py;//	"乘客姓名-拼音",
    private Integer persons;//	"乘客人数",
    private String print_ticket_type;//	"P(纸质票)；E(电子票)",
    private Integer quantity;//	"火车票张数",
    private Integer change_quantity;//	"火车票改签张数",
    private String change_status;//	"改签状态",
    private String refund_status;//	"退票状态",
    private String ticket_type;//	"车票类型（D原车次车票；C改签车次车票）",
    private String train_name;//	"车次",
    private String first_seat_type_name;//	"席别",
    private String seat_type;//	"坐席代号",
    private Float real_pay;//	"原始出票金额/出票总金额/订单总金额",
    private Float ticket_price;//	"票价",
    private Float insurance_fee;//	"保险费",
    private Float service_fee;//	"基础服务费",
    private Float refund_ticket_fee;//	"退票金额",
    private Float deliver_fee;//	"配送费",
    private Float paper_ticket_fee;//	"纸质出票费",
    private Float deal_change_service_fee;//	"改签服务费",
    private Float grab_service_fee;//	"抢票服务费",
    private Float after_service_fee;//	"后收服务费",
    private Float afterchangeservicefee;//	"后收改签服务费",
    private Float aftertaketicketfee;//	"后取票服务费",
    private Float afteraftertaketicketfee;//	"后收后取票服务费",
    private Float changebalance;//	"改签差价",
    private String departure_date_time;//	"出发日期",
    private String departure_station_name;//	"出发站(中)",
    private String departure_city_id;//	"出发城市ID",
    private String departure_city_name;//	"出发城市",
    private String departure_province_name;//	"出发省份",
    private String arrival_date_time;//	"到达日期",
    private String arrival_station_name;//	"到达站(中)",
    private String arrival_city_id;//	"到达城市ID",
    private String arrival_city_name;//	"到达城市",
    private String arrival_province_name;//	"到达省份",
    private String line_city;//	"出发城市名称-到达城市名称",
    private String is_rc;//	"是否有RC，T(是)/F(否)",
    private String seattype_rccodeid;//	"坐席RC",
    private String seattype_rccodename;//	"坐席RC说明",
    private String ticket_rccodeid;//	"票张RC",
    private String ticket_rccodename;//	"票张RC说明",
    private String userdefined_rid;//	"用户自定义RCcode,",
    private String userdefined_rc;//	"用户自定义RC说明,",
    private String datachange_lasttime;//	"创建时间",
    private Long report_etltime;// '同步job时间'
    private String o_currency;//原币种(客户支付币种)
    private Float o_exchangerate;//汇率(客户支付币种汇率)

    private String first_seat_type_name_en;//席别英文
    private String departure_station_name_en;//出发站英文
    private String departure_city_name_en;//出发城市英文
    private String departure_province_name_en;//出发省份英文
    private String arrival_station_name_en;//到达站英文
    private String arrival_city_name_en;//到城市英文
    private String arrival_province_name_en;//到达省份英文
    private String line_city_en;//出发城市名称-到达城市名称
    private String approvalpasstime;//授权通过时间
    private String actionname;//授权结果
    private String defineflag;//自定义成本中心
    private String defineflag2;//自定义成本中心2
    private String confirmtimepoint;//一次授权时间
    private String confirmtimepoint2;//二次授权时间

    private String auditorid; // 一次审核人uid
    private String auditorid2; // 二次审核人uid

    private String refund_rc;// 退票rc code
    private String refund_rc_desc;// 退票rc code说明

    private Float est_fee_12306;// 预估手续费12306

    private String taketicketstatus; // 后取票状态

    private Integer carbon_emission; // 碳排放

    private Integer median_carbon_emission; // 碳排量中位数

    private String std_industry1;//	行业大类,

    private String std_industry2;//	行业小类,

    private String passenger_employeeid;//	出行人员工编号,
    /**
     * 改签费
     */
    private BigDecimal delay_reschedule_fee;
}
