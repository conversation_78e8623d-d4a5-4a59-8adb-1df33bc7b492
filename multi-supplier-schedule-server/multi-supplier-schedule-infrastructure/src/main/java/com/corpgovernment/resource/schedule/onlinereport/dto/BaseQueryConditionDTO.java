package com.corpgovernment.resource.schedule.onlinereport.dto;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TravelPositionStepEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AnalysisObjectOrgInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionDetail;
import lombok.Data;

import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 * @date 2021/11/8 15:20
 * @Desc
 */
@Data
public class BaseQueryConditionDTO {
    private String reportId;
    private String uid;
    private String eid;
    private String groupId;
    private String startTime;
    private String endTime;
    private List<String> orderChannel;
    private List<String> corpIds;
    private List<String> accountIds;
    private List<SearchDeptAndCostcneterDTO> deptList;
    private List<SearchDeptAndCostcneterDTO> costCenterList;
    private String pos;
    private String industryType;
    private List<Integer> startCityId;
    private List<Integer> arriveCityId;
    private String flightCity;
    private List<String> airLines;
    private List<String> hotelGroups;
    /**
     * 城市
     */
    private List<Integer> cityIds;
    private List<String> cityNames;
    /**
     * 省份
     */
    private List<Integer> provinceIds;
    private List<String> provinceNames;
    /**
     * 国家
     */
    private List<String> countryNames;
    private List<Integer> countryIds;

    // 统计口径 成交/预订
    private String statisticalCaliber;

    private Pager pager;
    private List<QueryReportBuTypeEnum> buType;
    private TravelPositionStepEnum travelStep;
    private List<TravelPositionDetail> queryColumn;
    // all dom inner
    private String productType;

    private List<TimeFilterTypeInfoDTO> timeFilterList;

    // 订单号
    private List<String> orderIds;
    // 出差申请单号
    private List<String> bizApprovalNos;
    // 订单状态
    private List<String> orderstatusList;
    // 预订人卡号或姓名
    private List<String> users;
    // 出差申请人
    private List<String> approvers;
    // 出行人卡号或姓名
    private List<String> passengers;
    // 是否超标 T:是,F:否
    private List<String> exceedStandard;
    // 持卡人员工编号
    private List<String> employeIds;
    // 蓝色空间
    private String blueSpace;
    // AMOUNT 请求金额数据 ORDER_NUM 请求订单数 默认全部（金额 ，订单数）
    private String queryDataType;

    private SearchDeptAndCostcneterDTO deptEntity;
    private SearchDeptAndCostcneterDTO costCenterEntity;
    public String compareSameLevel;
    public String consumptionLevel;
    public String partition;
    public String compareCorpSameLevel;
    // 币种
    public String currency;
    // 是否三方协议 C：是；NC：否
    public String contractType;

    public String lang;
    // 查询bu
    public QueryReportBuTypeEnum queryBu;
    //  卡片携带的查询条件
    public String cardTakeawayCondition;
    // 舱位类型（Y(经济舱(包含全价经济舱、折扣经济舱、前返经济舱)),S(超级经济舱),F(头等舱),C(公务舱)）
    public String classType;

    public String analysisObject;
    public AnalysisObjectOrgInfo analysisObjectOrgInfo;
    public List<String> analysisObjectDimIds;
    public Boolean useStarRocks;
    public Map<String, String> otherParams;
    /**
     * 分区
     */
    public String dPartition;

    public List<String> flightNos;

    public List<String> hotelIds;
}
