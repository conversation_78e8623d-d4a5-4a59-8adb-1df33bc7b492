package com.corpgovernment.resource.schedule.onlinereport.supplier.dto;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-09-06 14:14
 * @desc
 */
@Data
public class CarTrendDTO {

    /**
     * dim 维度枚举值
     */
    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;

    @Column(name = "sumAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumAmount;

    @Column(name = "sumAmountAirportpickDom")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumAmountAirportpickDom;

    @Column(name = "sumAmountAirportpickInter")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumAmountAirportpickInter;

    @Column(name = "sumAmountCharter")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumAmountCharter;

    @Column(name = "sumAmountRent")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumAmountRent;

    @Column(name = "sumAmountTax")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumAmountTax;
}
