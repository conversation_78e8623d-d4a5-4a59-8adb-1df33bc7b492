package com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail;


import com.corpgovernment.resource.schedule.onlinereport.common.ConfigUtils;
import com.corpgovernment.resource.schedule.onlinereport.dao.BaseConditionPrebuilder;
import com.corpgovernment.resource.schedule.onlinereport.dto.TimeFilterTypeInfoDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.utils.BlueSpaceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-21 13:39
 * @desc
 */
@Service
@Repository
public class TrainDetaiDaoImpl extends AbstractDetaiDao {

    @Override
    protected ClickHouseTable getClickHouseTable(boolean isBluespace) {
        if (isBluespace) {
            return ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD_FOREIGN;
        }
        if (ConfigUtils.getBoolean("train_order_all_status_switch", false)) {
            return ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD_ALLORDER;
        } else {
            return ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;
        }
    }

    protected String buildPreSqlTime(List<TimeFilterTypeInfoDTO> timeFilterTypeInfoList, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isEmpty(timeFilterTypeInfoList)) {
            return sqlBuffer.toString();
        }
        for (TimeFilterTypeInfoDTO timeFilterTypeInfo : timeFilterTypeInfoList) {
            // 预订时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate") || StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
                sqlBuffer.append(buildTimeFilter(timeFilterTypeInfo, parmList));
            }
            // 出发日期
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "usedate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    sqlBuffer.append(" and ").append("subString(departure_date_time, 1, 10)").append(" >= ? ");
                    parmList.add(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    sqlBuffer.append(" and ").append("subString(departure_date_time, 1, 10)").append(" <= ? ");
                    parmList.add(timeFilterTypeInfo.getEndTime());
                }
            }
        }
        return sqlBuffer.toString();
    }

    // train passenger_name
    @Override
    protected String buildPreSqlPassenger(List<String> passengers, List<Object> parmList) {
        StringBuffer sqlBuffer = new StringBuffer();
        if (CollectionUtils.isNotEmpty(passengers)) {
            sqlBuffer.append(" and ");
            sqlBuffer.append(BaseConditionPrebuilder.buildListConditionNoAnd("passenger_name", passengers, parmList,true));
        }
        return sqlBuffer.toString();
    }

    @Override
    protected String buildProductTypeCondition(String productType) {
        return StringUtils.EMPTY;
    }

    @Override
    protected String buildOrderBySql(String pos, String blueSpace) {
        if (BlueSpaceUtils.isForeign(pos, blueSpace)) {
            return " order by order_id, passenger_name, refund_status desc";
        } else {
            return StringUtils.EMPTY;
        }
    }

    @Override
    protected String buildPreSqlExceedStandard(List<String> exceedStandard, List<Object> parmList) {
        if (CollectionUtils.isEmpty(exceedStandard) || exceedStandard.size() == 2) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.equalsIgnoreCase(exceedStandard.get(0), "T")) {
            return " AND is_rc = 'T'";
        }
        if (StringUtils.equalsIgnoreCase(exceedStandard.get(0), "F")) {
            return " AND is_rc = 'F'";
        }
        return StringUtils.EMPTY;
    }
}
