package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AnalysisObjectOrgInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BizUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.reportlib.detail.AbstractDetaiDao;
import com.corpgovernment.resource.schedule.onlinereport.convert.BaseQueryConditionMapper;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.enums.YesOrNotEnum;
import com.corpgovernment.resource.schedule.onlinereport.module.geo.MultiLangDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.BizFiledNameConvertUtils;
import com.corpgovernment.resource.schedule.onlinereport.utils.ConfigUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.reportlib.HotelOrderStatusEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2021/11/9 20:21
 * @Desc
 */
public abstract class AbstractOrderDetailBiz implements OrderDetailBiz<OnlineReportOrderDetailRequest> {

    @Autowired
    private BaseQueryConditionMapper baseQueryConditionMapper;


    public <T> List<T> queryOrderDetailEntity(OnlineReportOrderDetailRequest request, Class<T> clazz) throws Exception {
        BaseQueryConditionDTO base = convertRequest(request);
        return current(request).queryDetail(clazz, base);
    }

    public Integer queryOrderDetailCount(OnlineReportOrderDetailRequest request) throws Exception {
        BaseQueryConditionDTO base = convertRequest(request);
        return current(request).detailCount(base);
    }


    private BaseQueryConditionDTO convertRequest(OnlineReportOrderDetailRequest request) throws Exception {
        BaseQueryConditionDTO base = baseQueryConditionMapper.toDTO(request.getBasecondition());
        base.setOrderIds(request.getOrderIds());
        base.setOrderstatusList(request.getOrderstatusList());
        base.setUsers(request.getUsers());
        base.setPassengers(request.getPassengers());
        if (request.getPage() != null){
            base.setPager(BizUtils.initPagerStartOne(request.getPage()));
        }
        base.setProductType(request.getProductType());
        base.setContractType(request.getContractType());
        base.setClassType(request.getClassType());
        base.setExceedStandard(request.getExceedStandard());
        base.setEmployeIds(request.getEmployeIds());
        base.setOrderstatusList(convertOrderStatus(request.getOrderstatusList()));
        base.setQueryBu(request.getQueryBu());
        base.setFlightNos(request.getFlightNos());
        if (!ObjectUtils.isEmpty(request.getExtData()) && StringUtils.isNotEmpty(request.getExtData().get("cardId"))) {
            MDC.put("card-takeaway-id", request.getExtData().get("cardId"));
            String extCondition = cardTakeawayCondition(request.getExtData().get("cardId"), request.getBasecondition().getUid());
            MDC.put("card-takeaway-condition", extCondition);
            String takeawayAnalysisObjectCondition = this.buildCardTakeawayAnalysisObjectCondition(request.getAnalysisObject(),
                    request.getAnalysisObjectOrgInfo(), request.getAnalysisObjectDimIds(), request.getQueryBu());
            MDC.put("card-takeaway-analysis-object-condition", takeawayAnalysisObjectCondition);

            List<String> conditionList = Lists.newArrayList(extCondition, takeawayAnalysisObjectCondition);
            String cardTakeawayCondition = conditionList.stream().filter(StringUtils::isNotEmpty).collect(Collectors.joining(" and "));
            base.setCardTakeawayCondition(cardTakeawayCondition);
        }
        return base;
    }

    private String cardTakeawayCondition(String cardId, String uid) throws Exception {
//        QueryIndicatorConfigResponseType responseType = corpOnlineReportPrivilegeService.queryIndicatorConfig(
//                new QueryIndicatorConfigRequestType(uid, cardId));
//        if (responseType != null && responseType.getResponseStatus().getAck() == AckCodeType.Success
//                && responseType.getResultStatus() != null && responseType.getResultStatus().getCode() == 0) {
//            return Optional.ofNullable(responseType.getConfig()).map(ReportLibTakeawayIndicatorConfig::getExtCondition).orElse(StringUtils.EMPTY);
//        }
        return StringUtils.EMPTY;
    }


    private String buildCardTakeawayAnalysisObjectCondition(String analysisObject, AnalysisObjectOrgInfo analysisObjectOrgInfo,
                                                            List<String> analysisObjectDimIds,
                                                            QueryReportBuTypeEnum queryReportBu) {
        AnalysisObjectEnum analysisObjectEnum = StringUtils.isEmpty(analysisObject) ?
                (Objects.isNull(analysisObjectOrgInfo) ? AnalysisObjectEnum.CORP : AnalysisObjectEnum.ORG) :
                AnalysisObjectEnum.valueOf(analysisObject.toUpperCase());
        String conditon = StringUtils.EMPTY;
        if (analysisObjectEnum == null || CollectionUtils.isEmpty(analysisObjectDimIds)) {
            return conditon;
        }

        String dimIdsStr = analysisObjectDimIds.stream().collect(Collectors.joining("', '", "'", "'"));
        switch (analysisObjectEnum) {
            case CORP:
                conditon = " corp_corporation in ({dimIds}) ";
                break;
            case ACCOUNT:
            case ACCOUNTCODE:
                conditon = " account_id in ({dimIds}) ";
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
            case COSTCENTER1:
            case COSTCENTER2:
            case COSTCENTER3:
            case COSTCENTER4:
            case COSTCENTER5:
            case COSTCENTER6:
                if (ConfigUtils.getBoolean("custom_dim_open", false)) {
                    conditon = String.format(" %s_custom in ({dimIds}) ", analysisObjectEnum.toString().toLowerCase());
                } else {
                    String costCenterName;
                    if (queryReportBu == QueryReportBuTypeEnum.bus || queryReportBu == QueryReportBuTypeEnum.vaso) {
                        costCenterName = analysisObjectEnum.toString().toLowerCase();
                    } else {
                        costCenterName = BizFiledNameConvertUtils.convertCostCenterName(analysisObjectEnum.toString());
                    }
                    conditon = String.format(" %s in ({dimIds})  ", costCenterName);
                }
                break;
            case ORG:
                conditon = getDownAnalysisObjectOrgInfoCondition(analysisObjectOrgInfo);
                break;
            case UID:
                conditon = " uid in ({dimIds}) ";
                break;
            default:
                break;
        }
        return conditon.replace("{dimIds}", dimIdsStr);
    }

    private String getDownAnalysisObjectOrgInfoCondition(AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        StringBuilder stringBuilder = new StringBuilder();
        if (analysisObjectOrgInfo == null || StringUtils.isEmpty(analysisObjectOrgInfo.getOrgName())) {
            return stringBuilder.toString();
        }
        stringBuilder.append(" coalesce(orginfo, '') != '' ");
        if (analysisObjectOrgInfo.getLevel() > 0) {
            stringBuilder.append(String.format(" and get_json_object(orginfo, 'org%d') != '' ", analysisObjectOrgInfo.getLevel()));
        }
        return stringBuilder.toString();
    }

    protected abstract AbstractDetaiDao current(OnlineReportOrderDetailRequest request);

    protected List<String> convertOrderStatus(List<String> orderstatusList) {
        return orderstatusList;
    }

    protected String matchMultiLangInfo(List<MultiLangDTO> multiLangDTOList, Integer id, String defaultVal) {
        if (Objects.isNull(id)) {
            return StringUtils.trimToEmpty(defaultVal);
        }
        if (CollectionUtils.isNotEmpty(multiLangDTOList)) {
            for (MultiLangDTO multiLangDTO : multiLangDTOList) {
                if (Objects.nonNull(multiLangDTO.getId()) && multiLangDTO.getId().equals(id)) {
                    return multiLangDTO.getMultiLangName();
                }
            }
        }
        return StringUtils.trimToEmpty(defaultVal);
    }

    protected String matchMultiLangInfo(List<MultiLangDTO> multiLangDTOList, Long id, String defaultVal) {
        if (Objects.isNull(id)) {
            return StringUtils.trimToEmpty(defaultVal);
        }
        if (CollectionUtils.isNotEmpty(multiLangDTOList)) {
            for (MultiLangDTO multiLangDTO : multiLangDTOList) {
                if (Objects.nonNull(multiLangDTO.getBigId()) && multiLangDTO.getBigId().equals(id)) {
                    return multiLangDTO.getMultiLangName();
                }
            }
        }
        return StringUtils.trimToEmpty(defaultVal);
    }

    protected String matchMultiLangInfo(List<MultiLangDTO> multiLangDTOList, String id, String defaultVal) {
        if (StringUtils.isEmpty(id)) {
            return StringUtils.trimToEmpty(defaultVal);
        }
        if (CollectionUtils.isNotEmpty(multiLangDTOList)) {
            for (MultiLangDTO multiLangDTO : multiLangDTOList) {
                if (StringUtils.isNotEmpty(id) && StringUtils.equalsIgnoreCase(multiLangDTO.getStrId(), id)) {
                    return multiLangDTO.getMultiLangName();
                }
            }
        }
        return StringUtils.trimToEmpty(defaultVal);
    }


    protected Map initYesOrNo(String lang) {
        return ImmutableMap.builder()
                .put(YesOrNotEnum.T.toString(), SharkUtils.get("lbl_T", lang))
                .put(YesOrNotEnum.F.toString(), SharkUtils.get("lbl_F", lang))
                .build();
    }
}
