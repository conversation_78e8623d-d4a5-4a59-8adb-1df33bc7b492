package com.corpgovernment.resource.schedule.onlinereport.consume.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.OnlineReportConsumeBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.consume.TrainConsume;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.OnlineReportConsumeDao;
import com.corpgovernment.resource.schedule.onlinereport.consume.AbstractGenralConsume;
import com.corpgovernment.resource.schedule.onlinereport.dto.BaseQueryConditionDTO;
import com.corpgovernment.resource.schedule.onlinereport.utils.OrpReportUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2021/11/8 10:16
 * @Desc
 */
@Service
public class TrainGenralBiz extends AbstractGenralConsume {
    @Autowired
    private OnlineReportConsumeDao reportConsumeDao;

    @Override
    protected BigDecimal getTotalAmount1(OnlineReportConsumeBO genralConsumeCurrent) {
        return getTotalAmount3(genralConsumeCurrent);
    }

    @Override
    protected Integer getTotalQuantity1(OnlineReportConsumeBO genralConsumeCurrent) {
        return getTotalQuantity3(genralConsumeCurrent);
    }

    @Override
    protected BigDecimal getTotalAmount3(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneAmount());
    }

    @Override
    protected Integer getTotalQuantity3(OnlineReportConsumeBO genralConsumeCurrent) {
        return OrpReportUtils.nonNegative(genralConsumeCurrent.getTotalOneQuantity());
    }

    @Override
    protected OnlineReportConsumeBO aggreationGenralConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        List<TrainConsume> trainGenralDTOList = (List<TrainConsume>) reportConsumeDao.aggreationTrainWithCondition(baseQueryConditionDto, TrainConsume.class, false);
        //List<TrainConsume> intTrainGenralDTOList = (List<TrainConsume>) reportConsumeDao.aggreationIntTrainWithCondition(baseQueryConditionDto, TrainConsume.class);
        List<TrainConsume> intTrainGenralDTOList = Lists.newArrayList();
        return convert(trainGenralDTOList, intTrainGenralDTOList);
    }

    @Override
    protected OnlineReportConsumeBO aggreationCurrentConsume(BaseQueryConditionDTO baseQueryConditionDto) throws Exception {
        List<TrainConsume> trainGenralDTOList = (List<TrainConsume>) reportConsumeDao.aggreationTrainWithCondition(baseQueryConditionDto, TrainConsume.class, true);
        //List<TrainConsume> intTrainGenralDTOList = (List<TrainConsume>) reportConsumeDao.aggreationIntTrainWithCondition(baseQueryConditionDto, TrainConsume.class);
        List<TrainConsume> intTrainGenralDTOList = Lists.newArrayList();

        OnlineReportConsumeBO genralConsume = convert(trainGenralDTOList, intTrainGenralDTOList);
        // 商旅服务费
        genralConsume.setTotalCorpServiceFee(OrpReportUtils.formatBigDecimal(new BigDecimal(trainGenralDTOList.stream()
                .mapToDouble(i -> Optional.ofNullable(i.getTotalCorpServiceFee()).orElse(0d)).sum())));
        Double totalTicketPriceChangeRefundFee = trainGenralDTOList.stream()
                .mapToDouble(i -> Optional.ofNullable(i.getTotalTicketPriceChangeRefundFee()).orElse(0d)).sum();
        int totalQuantity = trainGenralDTOList.stream()
                .mapToInt(i -> Optional.ofNullable(i.getTotalQuantity()).orElse(0)).sum();
        // 平均票价
        genralConsume.setAvgPrice(OrpReportUtils.divide(new BigDecimal(totalTicketPriceChangeRefundFee)
                , new BigDecimal(totalQuantity), OrpConstants.TWO));
        // 改签张数
        genralConsume.setTotalRebookQuantity(trainGenralDTOList.stream().mapToInt(i -> Optional.ofNullable(i.getTotalRebookQuantity()).orElse(0)).sum());
        // 退订张数
        genralConsume.setTotalRefundQuantity(Math.abs(trainGenralDTOList.stream().mapToInt(i -> Optional.ofNullable(i.getTotalRefundQuantity()).orElse(0)).sum()));
        /* 订单数
        List<TrainConsume> trainOrderNumList = reportConsumeDao.aggreationTrainOrderNumWithCondition(baseQueryConditionDto, TrainConsume.class);
        genralConsume.setTotalCntOrder(trainOrderNumList.stream().mapToInt(i-> Optional.ofNullable(i.getTotalCntOrder()).orElse(0)).sum());\
         */
        return genralConsume;
    }

    private OnlineReportConsumeBO convert(List<TrainConsume> trainGenralDTOList, List<TrainConsume> intTrainGenralDTOList) {
        OnlineReportConsumeBO genralConsume = new OnlineReportConsumeBO();
        double currentTotalAmount = 0;
        int currentTotalQuantity = 0;
        // 国际
        Double intCurrentTotalAmount = 0d;
        Integer intCurrentTotalQuantity = 0;
        if (CollectionUtils.isNotEmpty(trainGenralDTOList)) {
            currentTotalAmount = Optional.ofNullable(trainGenralDTOList.get(0).getTotalAmount()).orElse(0d);
            currentTotalQuantity = Optional.ofNullable(trainGenralDTOList.get(0).getTotalQuantity()).orElse(0);
        }
        if (CollectionUtils.isNotEmpty(intTrainGenralDTOList)) {
            intCurrentTotalAmount = Optional.ofNullable(intTrainGenralDTOList.get(0).getTotalAmount()).orElse(0d);
            intCurrentTotalQuantity = Optional.ofNullable(intTrainGenralDTOList.get(0).getTotalQuantity()).orElse(0);
        }
        // 国内
        genralConsume.setTotalOneAmount(OrpReportUtils.formatDouble(currentTotalAmount));
        genralConsume.setTotalOneQuantity(currentTotalQuantity);
        // 国际 目前国际不计入总金额
        genralConsume.setTotalTwoAmount(OrpReportUtils.formatDouble(intCurrentTotalAmount));
        genralConsume.setTotalTwoQuantity(intCurrentTotalQuantity);

        genralConsume.setTotalAmount(OrpReportUtils.formatBigDecimal(new BigDecimal(currentTotalAmount)));
        genralConsume.setTotalQuantity(currentTotalQuantity);
        return genralConsume;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum s) {
        return QueryReportBuTypeEnum.train == s;
    }
}
