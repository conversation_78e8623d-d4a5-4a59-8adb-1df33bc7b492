package com.corpgovernment.resource.schedule.onlinereport;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.onlinereport.behavior.BehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.BookTypeBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.CarUseTypeBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.FltCabinBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.FltDepartureTimeBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.FltDiscountRateBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.HotelAvgPriceBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.HotelStarBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.HtlBalanceTypeBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.MixPaymentBeahviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.PreOrderdateBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.RebookBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.RefundBehavioAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.RoomShareBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.behavior.TrainSeatBehaviorAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.dept.DeptConsumeAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.position.TravelPositionAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.rc.RcAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.save.SaveAnalysisAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.sum.TripGeneralAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.supplier.HotAnalysisReportDataAdaaptor;
import com.corpgovernment.resource.schedule.onlinereport.supplier.SupplierMonitorAdaptor;
import com.corpgovernment.resource.schedule.onlinereport.travelmark.TravelMarkAdaptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/25 10:25
 */
@Service
@Slf4j
public class ReportDataNewAdaptorService {

    public Map<String, BaseReportDataAdaptor> adaptors = new HashMap<>(64);
    @Autowired
    private DeptConsumeAdaptor deptConsumeAdaptor;
    @Autowired
    private RcAnalysisAdaptor rcAnalysisAdaptor;
    @Autowired
    private BasePermitVerfiyService basePermitVerfiyService;
    @Autowired
    private TripGeneralAdaptor tripGeneralSumAdaptor;
    @Autowired
    private TravelMarkAdaptor travelMarkAdaptor;
    @Autowired
    private FltDiscountRateBehaviorAnalysisAdaptor fltDiscountRateBehaviorAnalysisAdaptor;
    @Autowired
    private PreOrderdateBehaviorAnalysisAdaptor preOrderdateBehaviorAnalysisAdaptor;
    @Autowired
    private FltCabinBehaviorAnalysisAdaptor fltCabinBehaviorAnalysisAdaptor;
    @Autowired
    private BookTypeBehaviorAnalysisAdaptor bookTypeBehaviorAnalysisAdaptor;
    @Autowired
    private RefundBehavioAnalysisAdaptor refundAnalysisAdaptor;
    @Autowired
    private RebookBehaviorAnalysisAdaptor rebookAnalysisAdaptor;
    @Autowired
    private FltDepartureTimeBehaviorAnalysisAdaptor fltDepartureTimeBehaviorAnalysisAdaptor;
    @Autowired
    private HotelAvgPriceBehaviorAnalysisAdaptor hotelBehaviorAnalysisAdaptor;
    @Autowired
    private HotelStarBehaviorAnalysisAdaptor hotelStarBehaviorAnalysisAdaptor;
    @Autowired
    private MixPaymentBeahviorAnalysisAdaptor mixPaymentBeahviorAnalysisAdaptor;
    @Autowired
    private RoomShareBehaviorAnalysisAdaptor roomShareBehaviorAnalysisAdaptor;
    @Autowired
    private TrainSeatBehaviorAnalysisAdaptor trainSeatBehaviorAnalysisAdaptor;
    @Autowired
    private BehaviorAnalysisAdaptor behaviorAnalysisAdaptor;
    @Autowired
    private CarUseTypeBehaviorAnalysisAdaptor carUseTypeBehaviorAnalysisAdaptor;
    @Autowired
    private SaveAnalysisAdaptor saveAnalysisAdaptor;
    @Autowired
    private TravelPositionAdaptor positionAdaptor;

    @Resource
    private OnlineReportService onlineReportService;


    @Autowired
    private HtlBalanceTypeBehaviorAnalysisAdaptor htlBalanceTypeBehaviorAnalysisAdaptor;

    @Autowired
    private SupplierMonitorAdaptor supplierMonitorAdaptor;

    @Autowired
    private HotAnalysisReportDataAdaaptor hotAnalysisReportDataAdaaptor;

    @PostConstruct
    public void init() {
        adaptors.put("TravelAnalysis:DeptAnalysis", deptConsumeAdaptor); //部门消费分析
        adaptors.put("TravelAnalysis:OverView:ConsumeOverview", tripGeneralSumAdaptor); //消费金融概况
        adaptors.put("TravelAnalysis:TravelMark:DomesticFltAvgPrice", travelMarkAdaptor); //国内机票平均票价
        adaptors.put("ComplianceMonitor:RcAnalysis:TopRcDept", rcAnalysisAdaptor);// 合规监测-TOP部门/员工RC占比
        adaptors.put("ComplianceMonitor:RcAnalysis:RcPercent", rcAnalysisAdaptor);// 合规监测-rc占比概览
        adaptors.put("ComplianceMonitor:RcAnalysis:RcTrend", rcAnalysisAdaptor);// 合规监测-rc趋势
        adaptors.put("TravelAnalysis:TravelMark:OverseaFltAvgMileagePrice", travelMarkAdaptor); //国际机票里程均价
        adaptors.put("TravelAnalysis:TravelMark:DomesticFltFullPriceRatio", travelMarkAdaptor); //国内机票全价票张占比
        adaptors.put("TravelAnalysis:Behavior:FltDiscountRate", fltDiscountRateBehaviorAnalysisAdaptor);  //折扣分布
        adaptors.put("TravelAnalysis:Behavior:FltPreOrderDays", preOrderdateBehaviorAnalysisAdaptor); //机票提前预订天数
        adaptors.put("ValidateMonitor:FltCabin", fltCabinBehaviorAnalysisAdaptor); //机票仓位分析
        adaptors.put("TravelAnalysis:Behavior:BookMethod", bookTypeBehaviorAnalysisAdaptor);// 预订方式
        adaptors.put("TravelAnalysis:Behavior:RefundAnalysis", refundAnalysisAdaptor); // 退
        adaptors.put("TravelAnalysis:Behavior:RebookAnalysis", rebookAnalysisAdaptor); // 改
        adaptors.put("TravelAnalysis:Behavior:FltDepartureTime", fltDepartureTimeBehaviorAnalysisAdaptor);  //机票起飞时间段
        adaptors.put("TravelAnalysis:TravelMark:HotelCountNightAvgPrice", hotelBehaviorAnalysisAdaptor); //酒店间夜均价
        adaptors.put("ValidateMonitor:HotelStar", hotelStarBehaviorAnalysisAdaptor); //酒店星级分析
        adaptors.put("TravelAnalysis:Behavior:HotelMixPayment", mixPaymentBeahviorAnalysisAdaptor); //酒店随心订
        adaptors.put("TravelAnalysis:Behavior:HtlRoomShareAnalysis", roomShareBehaviorAnalysisAdaptor);
        adaptors.put("TravelAnalysis:TravelMark:TrainTicketAvgPrice", travelMarkAdaptor);  //火车平均票价
        adaptors.put("ValidateMonitor:TrainSeat", trainSeatBehaviorAnalysisAdaptor); //火车坐席分析
        adaptors.put("TravelAnalysis:Behavior:CarMixPayment", mixPaymentBeahviorAnalysisAdaptor); //用车随心订
        adaptors.put("TravelAnalysis:Behavior:CarUseType", carUseTypeBehaviorAnalysisAdaptor); //同城\跨域用车分析
        adaptors.put("TravelAnalysis:Behavior:CarUseTimeRange", behaviorAnalysisAdaptor); //用车时间段分布
        adaptors.put("TravelAnalysis:SaveLoss:Save", saveAnalysisAdaptor);// 节省分析-节省
        adaptors.put("TravelAnalysis:SaveLoss:SavePotential", saveAnalysisAdaptor);// 节省分析-潜在
        adaptors.put("TravelAnalysis:SaveLoss:SaveAnalysis", saveAnalysisAdaptor);// 节省分析-分析
        adaptors.put("TravelAnalysis:Behavior:HtlPreOrderDays", preOrderdateBehaviorAnalysisAdaptor);  //酒店提前预订天数
        adaptors.put("TravelAnalysis:Behavior:HotelBalanceType", htlBalanceTypeBehaviorAnalysisAdaptor);  //酒店支付方式
        adaptors.put("CorpPositionTrack", positionAdaptor);// 差旅定位

        // 供应商监控
        adaptors.put("SupplierMonitor", supplierMonitorAdaptor); //简报
        adaptors.put("SupplierMonitor:FltCompanyConsume", supplierMonitorAdaptor); //协议/非协议航司消费概况
        adaptors.put("SupplierMonitor:HotelConsume", supplierMonitorAdaptor);  //协议/会员酒店消费概况
        adaptors.put("SupplierMonitor:AgreementDeptDetail", supplierMonitorAdaptor);  //各部门/员工三方协议消费详情
        adaptors.put("SupplierMonitor:FltAgreementAirCompletion", supplierMonitorAdaptor);  //三方协议航司消费详情
        adaptors.put("SupplierMonitor:HtlAgreementCompletion", supplierMonitorAdaptor);  //三方协议酒店集团消费详情
        adaptors.put("SupplierMonitor:Top10FltCompanyConsume", supplierMonitorAdaptor); //Top 20 航司消费排行
        adaptors.put("SupplierMonitor:Top10FltLineConsume", supplierMonitorAdaptor); //Top 20 航线
        adaptors.put("SupplierMonitor:Top10HotelConsume", supplierMonitorAdaptor); //Top 20 酒店消费排行
        adaptors.put("SupplierMonitor:Top10HotelCityConsume", supplierMonitorAdaptor); //Top 20 城市酒店消费排行
        adaptors.put("SupplierMonitor:Top10TrainLineConsume", hotAnalysisReportDataAdaaptor); //Top 20 火车票线路消费排行
        adaptors.put("SupplierMonitor:Top10CarCityConsume", hotAnalysisReportDataAdaaptor); //Top 20 用车城市消费排行
        adaptors.put("TravelAnalysis:HotAnalysis", hotAnalysisReportDataAdaaptor);  //协议/会员酒店消费概况

    }


    public BaseReportDataAdaptor getReportAdaptor(String reportId) {
        return adaptors.get(reportId);
    }


    public Object adaptor(BaseQueryConditionBO request) {
        try {
            return adaptor("", request, "");
        } catch (BusinessException e) {
            log.error("adaptor error", e);
            throw new RuntimeException(e);
        }
    }

    public Object adaptor(String uid, BaseQueryConditionBO request, String lang) throws BusinessException {
        // TODO 校验权限

        // UserPermissionsBo userPermissionsBo = basePermitVerfiyService.vaildPermit(uid, request, lang);
        //BaseQueryConditionBO baseQueryCondition = basePermitVerfiyService.convertToBaseStaticQuery(userPermissionsBo, request);
        BaseQueryConditionBO baseQueryCondition = basePermitVerfiyService.convertToBaseStaticQuery(request);
        // 填充自定义对比参数
        //basePermitVerfiyService.fillCustomComparatorParam(baseQueryCondition.getBaseQueryCondition(), uid);
        baseQueryCondition.setLang(lang);
        BaseReportDataAdaptor adaptor = adaptors.get(baseQueryCondition.getBaseQueryCondition().getReportId());
        log.info("adaptor:{}", adaptor);
        if (adaptor == null) {
            return null;
        }

        return adaptor.adapt(null, baseQueryCondition);
    }


    public List<ChartExcelEntity> exportExcel(String uid, BaseQueryConditionBO request, String lang) throws BusinessException {
        UserPermissionsBo userPermissionsBo = basePermitVerfiyService.vaildPermit(uid, request, lang);
        BaseQueryConditionBO baseQueryCondition = basePermitVerfiyService.convertToBaseStaticQuery(request);
        baseQueryCondition.setLang(lang);
        BaseReportDataAdaptor adaptor = adaptors.get(baseQueryCondition.getBaseQueryCondition().getReportId());
        return adaptor.buildExcel(userPermissionsBo, baseQueryCondition);
    }

    public String getFileName(String uid, BaseQueryConditionBO request, String lang) throws BusinessException {
        BaseReportDataAdaptor adaptor = adaptors.get(request.getBaseQueryCondition().getReportId());
        return adaptor.buildExcelName(request, uid, lang);
    }

    public String queryBaseDataLastUpdateTime() {
        return onlineReportService.queryBaseDataLastUpdateTime();
    }
}
