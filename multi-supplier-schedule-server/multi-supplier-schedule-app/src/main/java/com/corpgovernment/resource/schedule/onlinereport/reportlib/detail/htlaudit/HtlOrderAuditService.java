package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.htlaudit;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.orderdetail.OrderDetailBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.dto.orderdetail.OrderDetailDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportHtlOrderAuditInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportQueryHtlOrderAuditRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportQueryHtlOrderAuditResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveFilter;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HotelAuditEntity;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.SrDao;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.IOrderService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.SrSwitchConfig;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.AbstractOrderCommonService;
import onlinereport.enums.PageErrorCodeEnum;
import onlinereport.enums.reportlib.HotelOrderAuditEnum;
import onlinereport.enums.reportlib.OrderDetailEnumerable;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022-10-17 19:34
 * @desc
 */
@Service
public class HtlOrderAuditService extends AbstractOrderCommonService implements IOrderService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private HiveDao hiveDao;

    @Autowired
    private SrSwitchConfig srSwitchConfig;

    @Autowired
    private SrDao srDao;

    private OrderDetailDTO queryOrderDetail(BaseQueryConditionBO baseQueryCondition) throws BusinessException {
        OnlineReportQueryHtlOrderAuditRequest request = new OnlineReportQueryHtlOrderAuditRequest();
        BaseQueryCondition baseQueryCondition1 = baseQueryCondition.getBaseQueryCondition();
        request.setBasecondition(baseQueryCondition1);
        request.setLang(baseQueryCondition.getLang());
        request.setOrderIds(baseQueryCondition.getOrderids());
        request.setPage(baseQueryCondition.getPager());
        request.setPassengers(baseQueryCondition.getPassengers());
        request.setUsers(baseQueryCondition.getUsers());
        request.setExtData(baseQueryCondition.getExtData());
        OnlineReportQueryHtlOrderAuditResponse response = corpOnlineReportPlatformService.queryHtlOrderAuditInfo(request);
        if (response == null || response.getResponseCode() != 20000) {
            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
        }
        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        orderDetailDTO.setData(this.convertMapData(Optional.ofNullable(response.getHtlAuditList()).orElse(new ArrayList<>()), baseQueryCondition.getLang()));
        orderDetailDTO.setTotalRecords(Optional.ofNullable(response.getTotalRecords()).orElse(0));
        return orderDetailDTO;
    }

    protected List<Map<String, Object>> convertMapData(List<OnlineReportHtlOrderAuditInfo> data, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        for (OnlineReportHtlOrderAuditInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(HotelOrderAuditEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrderId()));
            map.put(HotelOrderAuditEnum.ORDERSTATUS.getCode(), MapperUtils.trim(order.getOrderStatus()));
            map.put(HotelOrderAuditEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(HotelOrderAuditEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorpName()));
            map.put(HotelOrderAuditEnum.HOTELNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotelname(), order.getHotelnameEn()));
            map.put(HotelOrderAuditEnum.HOTELGROUPNAME.getCode(), MapperUtils.getValByLang(lang, order.getGroupname(), order.getGroupnameEn()));
            map.put(HotelOrderAuditEnum.CITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getCityname(), order.getCitynameEn()));
            map.put(HotelOrderAuditEnum.QUANTITY.getCode(), MapperUtils.convertDigitToString(order.getQuantity()));
            map.put(HotelOrderAuditEnum.AMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getAmount()).toString());
            map.put(HotelOrderAuditEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrival()));
            map.put(HotelOrderAuditEnum.DEPARTUREDATETIME.getCode(), MapperUtils.trim(order.getDeparture()));
            map.put(HotelOrderAuditEnum.PROVINCENAME.getCode(), MapperUtils.getValByLang(lang, order.getProvincename(), order.getProvincenameEn()));
            map.put(HotelOrderAuditEnum.PRODUCTTYPE.getCode(), MapperUtils.trim(order.getProducttype()));
            map.put(HotelOrderAuditEnum.BALANCETYPENAME.getCode(), MapperUtils.trim(order.getBalancetypename()));
            map.put(HotelOrderAuditEnum.ROOM_TYPE.getCode(), getRoomTypeDesc(order.getRoomTypeCode(), lang));
            map.put(HotelOrderAuditEnum.ROOM_NO.getCode(), MapperUtils.trim(order.getRoomNo()));

            map.put(HotelOrderAuditEnum.IS_AUDIT.getCode(), MapperUtils.trim(order.getIsAudit()));
            map.put(HotelOrderAuditEnum.AUDIT_STATUS.getCode(), MapperUtils.trim(order.getAuditStatus()));
            map.put(HotelOrderAuditEnum.AUDIT_CLIENT_NAME.getCode(), MapperUtils.trim(order.getAuditClientName()));
            map.put(HotelOrderAuditEnum.AUDIT_CHECKIN_DATE.getCode(), MapperUtils.trim(order.getAuditCheckinDate()));
            map.put(HotelOrderAuditEnum.AUDIT_CHECKOUT_DATE.getCode(), MapperUtils.trim(order.getAuditCheckoutDate()));

            map.put(HotelOrderAuditEnum.AUDIT_QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getAuditQuantity()));
            map.put(HotelOrderAuditEnum.AUDIT_REFUND_AMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getAuditRefundAmount()).toString());
            map.put(HotelOrderAuditEnum.AUDIT_REFUND_QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getAuditRefundQuantity()));
            result.add(map);
        }
        return result;
    }

    private OrderDetailDTO queryOrderDetailHive(BaseQueryConditionBO baseQueryCondition) throws BusinessException {
        HiveFilter hiveFilter = getHiveFilter(baseQueryCondition);
        List<HotelAuditEntity> hotelAuditEntityList = null;
        if (srSwitchConfig.isOpenSrSwitch(hiveFilter.getUid(), "hotel")) {
            hotelAuditEntityList = srDao.searchHotelOrderAuditdetail(hiveFilter);
        } else {
            hotelAuditEntityList = hiveDao.searchHotelOrderAuditdetail(hiveFilter);
        }
        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        orderDetailDTO.setData(this.convertMapDataHive(Optional.ofNullable(hotelAuditEntityList).orElse(new ArrayList<>()), baseQueryCondition.getLang()));
        return orderDetailDTO;
    }

    protected List<Map<String, Object>> convertMapDataHive(List<HotelAuditEntity> data, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        for (HotelAuditEntity order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(HotelOrderAuditEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrderid()));
            map.put(HotelOrderAuditEnum.ORDERSTATUS.getCode(), MapperUtils.trim(order.getOrder_status()));
            map.put(HotelOrderAuditEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
            map.put(HotelOrderAuditEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorp_name()));
            map.put(HotelOrderAuditEnum.HOTELNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotel_name(), order.getHotel_group_name_en()));
            map.put(HotelOrderAuditEnum.HOTELGROUPNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotel_group_name(), order.getHotel_group_name_en()));
            map.put(HotelOrderAuditEnum.CITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getCity_name(), order.getCity_name_en()));
            map.put(HotelOrderAuditEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getOrder_quantity()));
            map.put(HotelOrderAuditEnum.AMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getOrder_amount()));
            map.put(HotelOrderAuditEnum.ARRIVALDATETIME.getCode(), MapperUtils.subStrTime(order.getArrival_date_time()));
            map.put(HotelOrderAuditEnum.DEPARTUREDATETIME.getCode(), MapperUtils.subStrTime(order.getDeparture_date_time()));
            map.put(HotelOrderAuditEnum.PROVINCENAME.getCode(), MapperUtils.getValByLang(lang, order.getProvince_name(), order.getProvince_name_en()));
            map.put(HotelOrderAuditEnum.PRODUCTTYPE.getCode(), MapperUtils.trim(order.getProducttype_all()));
            map.put(HotelOrderAuditEnum.BALANCETYPENAME.getCode(), MapperUtils.trim(order.getBalancetypename()));
            map.put(HotelOrderAuditEnum.ROOM_TYPE.getCode(), getRoomTypeDesc(order.getRoom_type_code(), lang));
            map.put(HotelOrderAuditEnum.ROOM_NO.getCode(), MapperUtils.trim(order.getRoom_no()));

            map.put(HotelOrderAuditEnum.IS_AUDIT.getCode(), MapperUtils.trim(order.getIs_audit()));
            map.put(HotelOrderAuditEnum.AUDIT_STATUS.getCode(), MapperUtils.trim(order.getAudit_status()));
            map.put(HotelOrderAuditEnum.AUDIT_CLIENT_NAME.getCode(), MapperUtils.trim(order.getAudit_client_name()));
            map.put(HotelOrderAuditEnum.AUDIT_CHECKIN_DATE.getCode(), MapperUtils.subStrTime(order.getAudit_checkin_date()));
            map.put(HotelOrderAuditEnum.AUDIT_CHECKOUT_DATE.getCode(), MapperUtils.subStrTime(order.getAudit_checkout_date()));

            map.put(HotelOrderAuditEnum.AUDIT_QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getAudit_quantity()));
            map.put(HotelOrderAuditEnum.AUDIT_REFUND_AMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getAuditRefundAmount()));
            map.put(HotelOrderAuditEnum.AUDIT_REFUND_QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getAudit_refund_quantity()));
            result.add(map);
        }
        return result;
    }

    private OrderDetailBO convertData(String lang, OrderDetailEnumerable[] orderDetailEnumerables, List<Map<String, Object>> queryData, boolean isDownload) {
        OrderDetailBO orderDetail = new OrderDetailBO();
        List<OrderDetailEnumerable> newList = Arrays.asList(orderDetailEnumerables);
        newList.sort(new Comparator<OrderDetailEnumerable>() {
            @Override
            public int compare(OrderDetailEnumerable o1, OrderDetailEnumerable o2) {
                if (o1.getOrder() == o2.getOrder()) {
                    return o1.getEnum().ordinal() - o2.getEnum().ordinal();
                } else {
                    return o1.getOrder() - o2.getOrder();
                }
            }
        });
        //标题
        List<String> title = new ArrayList<>();
        for (OrderDetailEnumerable item : newList) {
            title.add(SharkUtils.get(item.getName(), item.getName(), lang));
        }
        orderDetail.setTitle(title);
        if (CollectionUtils.isEmpty(queryData)) {
            return orderDetail;
        }
        List<List<Object>> result = new ArrayList<>();
        //数据
        for (Map<String, Object> map : queryData) {
            List<Object> data = new ArrayList<>();
            for (OrderDetailEnumerable item : newList) {
                if (isDownload) {
                    data.add(map.get(item.getCode()));
                } else {
                    data.add(formatData(map.get(item.getCode())));
                }
            }
            result.add(data);
        }
        orderDetail.setData(result);
        return orderDetail;
    }

    @Override
    public OrderDetailBO queryDetail(String uid, BaseQueryConditionBO baseQueryCondition, boolean isDownload) throws BusinessException {
        if (isDownload) {
            OrderDetailDTO orderDetailDTO = queryOrderDetailHive(baseQueryCondition);
            OrderDetailBO orderDetail = convertData(baseQueryCondition.getLang(), HotelOrderAuditEnum.values(), orderDetailDTO.getData(), isDownload);
            orderDetail.setTotalRecords(orderDetailDTO.getTotalRecords());
            return orderDetail;
        } else {
            OrderDetailDTO orderDetailDTO = queryOrderDetail(baseQueryCondition);
            OrderDetailBO orderDetail = convertData(baseQueryCondition.getLang(), HotelOrderAuditEnum.values(), orderDetailDTO.getData(), isDownload);
            orderDetail.setTotalRecords(orderDetailDTO.getTotalRecords());
            return orderDetail;
        }
    }

    private String getRoomTypeDesc(Integer roomTypeCode, String lang) {
        if (Objects.isNull(roomTypeCode)) {
            return StringUtils.EMPTY;
        }
        // 0 标准房 1 钟点房
        if (roomTypeCode == 0) {
            return SharkUtils.get("index.standardroom", "index.standardroom", lang);
        }
        if (roomTypeCode == 1) {
            return SharkUtils.get("index.temproom", "index.temproom", lang);
        }
        return MapperUtils.convertDigitToZeroString(roomTypeCode);
    }
}
