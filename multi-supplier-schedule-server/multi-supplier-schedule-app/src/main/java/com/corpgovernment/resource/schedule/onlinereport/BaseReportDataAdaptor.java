package com.corpgovernment.resource.schedule.onlinereport;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import onlinereport.enums.PageErrorCodeEnum;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/3/19 19:16
 */
public abstract class BaseReportDataAdaptor<T> {

    protected String LOG_TITLE = this.getClass().getSimpleName();

    public abstract T convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException;

    public abstract List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException;

    public T adapt(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        return convert(userPermissionsBo, baseCondition);
    }

    public abstract String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang);

    public String buildExcelName() {
        return String.valueOf(System.currentTimeMillis());
    }

    public void errorReturn(Object response, Integer responseCode, String msg) throws BusinessException {
        BusinessException businessException;
        if (Objects.isNull(response)) {
            businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
        } else {
            // soa的30004 mapping web的ParamError，其他都做InterfaceError处理
            if (Objects.nonNull(responseCode) && responseCode == 30004) {
                businessException = new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            } else {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), msg);
            }
        }
        throw businessException;
    }

    public void erroThrow(Object response, Integer responseCode, String msg) throws BusinessException {
        BusinessException businessException;
        if (Objects.isNull(response)) {
            businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
        } else {
            // soa的30004 mapping web的ParamError，其他都做InterfaceError处理
            if (Objects.nonNull(responseCode) && responseCode == 30004) {
                businessException = new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            } else {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), msg);
            }
        }
        throw businessException;
    }
}
