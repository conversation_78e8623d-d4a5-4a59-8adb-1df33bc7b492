package com.corpgovernment.resource.schedule.onlinereport.dept;


import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.DeptUidStatisticalsEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.WelfareDeptFieldEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.*;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.CommonUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.OrpGsonUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import onlinereport.enums.SubQueryReportBuTypeEnum;
import onlinereport.enums.dept.CarDeptFieldEnum;
import onlinereport.enums.dept.FlightDeptFieldEnum;
import onlinereport.enums.dept.HotelDeptFieldEnum;
import onlinereport.enums.dept.OverViewDeptFieldEnum;
import onlinereport.enums.dept.TrainDeptFieldEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/5 21:36
 * @Desc 部门分析
 */
@Service
@Slf4j
public class DeptConsumeService extends AbstractGenralExportDataService {

    private final static String PERCENT_QUOTE = "%";
    private final static String DEPT_DETAIL_DOWNLOAD_MODE_KEY = "dept_detail_download_mode";
    // 默认下载条数
    private final static int DEPT_DETAIL_DOWNLOAD_LIMIT_DEFAULT = 10000;
    private final static String DEPT_DETAIL_DOWNLOAD_LIMIT_KEY = "dept_detail_download_limit";
    protected String LOG_TITLE = this.getClass().getSimpleName();
    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

/*    @Autowired
    private CorpUserInfoService corpUserInfoService;

    @Autowired
    private RpcGroup4jServiceClientService rpcGroup4jServiceClientService;*/

    /**
     * 前5部门消费分析
     *
     * @param baseCondition
     * @return
     * @throws BusinessException
     */
    public Object getTop5DeptConsumeAnalysis(BaseQueryConditionBO baseCondition, AnalysisObjectEnum analysisObjectEnum) throws BusinessException {
        OnlineReportTopFiveDeptConsumeRequest reportConsumeRequest = new OnlineReportTopFiveDeptConsumeRequest();
        reportConsumeRequest.setAnalysisObjectEnum(analysisObjectEnum);
        reportConsumeRequest.setBasecondition(baseCondition.getBaseQueryCondition());
        OnlineReportTopFiveDeptConsumeResponse responseType = getTopFiveDeptConsumeResponse(reportConsumeRequest);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            List<OnlineReportDeptConsume> deptCousumeList = Optional.ofNullable(responseType.getDeptCousumeList()).orElse(new ArrayList<>());
            List list = new ArrayList();
            for (OnlineReportDeptConsume deptConsume : deptCousumeList) {
                Map map = new HashMap();
                map.put("name", deptConsume.getDept());
                map.put("totalAmount", deptConsume.getTotalAmount());
                map.put("percentage", MapperUtils.convertDigitToZero(deptConsume.getPercentage()));
                map.put("momAmount", MapperUtils.convertDigitToZero(deptConsume.getMomAmount()));
                map.put("yoyAmount", MapperUtils.convertDigitToZero(deptConsume.getYoyAmount()));
                map.put("yoyAmountBeforeLast", MapperUtils.convertDigitToZero(deptConsume.getYoyAmountBeforeLast()));
                list.add(map);
            }
            return list;
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(responseType)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
            }
            throw businessException;
        }
    }

    /**
     * 前5部门其他指标分析
     *
     * @param baseCondition
     * @param analysisTypeEnum
     * @return
     * @throws BusinessException
     */
    public Object getTop5DeptOtherAnalysis(BaseQueryConditionBO baseCondition, AnalysisTypeEnum analysisTypeEnum, AnalysisObjectEnum analysisObjectEnum) throws BusinessException {
        Map resp = new HashMap();
        OnlineReportTopFiveDeptAnalysisRequest reportConsumeRequest = new OnlineReportTopFiveDeptAnalysisRequest();
        reportConsumeRequest.setAnalysisObjectEnum(analysisObjectEnum);
        reportConsumeRequest.setAnalysisTypeEnum(analysisTypeEnum);
        reportConsumeRequest.setBasecondition(baseCondition.getBaseQueryCondition());
        OnlineReportTopFiveDeptAnalysisResponse responseType = getTopFiveDeptAnalysisResponse(reportConsumeRequest);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            List<OnlineReportTopFiveAnalysis> deptCousumeList = responseType.getDeptAnalysisList();
            BigDecimal corpVal = responseType.getCorpVal();
            List list = new ArrayList();
            boolean isNeedPercentQuote = isNeedPercentQuote(analysisTypeEnum);
            if (CollectionUtils.isNotEmpty(deptCousumeList)) {
                for (OnlineReportTopFiveAnalysis deptConsume : deptCousumeList) {
                    Map map = new HashMap();
                    map.put("name", deptConsume.getDept());
                    map.put("val", isNeedPercentQuote ? MapperUtils.convertDigitToZeroString(deptConsume.getResult()).concat(PERCENT_QUOTE) : MapperUtils.convertDigitToZero(deptConsume.getResult()));
                    list.add(map);
                }
            }
            resp.put("corpVal", isNeedPercentQuote ? MapperUtils.convertDigitToZeroString(corpVal).concat(PERCENT_QUOTE) : MapperUtils.convertDigitToZero(corpVal));
            resp.put("list", list);
            return resp;
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(responseType)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
            }
            throw businessException;
        }
    }

    /**
     * 部门明细分析
     *
     * @param baseCondition
     * @return
     * @throws BusinessException
     */
    public Object getDeptDetailAnalysis(BaseQueryConditionBO baseCondition, AnalysisObjectEnum analysisObjectEnum) throws BusinessException {
        Map resp = new HashMap();
        OnlineReportDeptDetailAnalysisRequest request = new OnlineReportDeptDetailAnalysisRequest();
        request.setAnalysisObjectEnum(analysisObjectEnum);
        request.setAnalysisObjectOrgInfo(baseCondition.getAnalysisObjectOrgInfo());
        request.setPage(baseCondition.getPager());
        request.setSubQueryBu(baseCondition.getSubQueryBu());
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setLang(baseCondition.getLang());
        request.setProductType(baseCondition.getProductType());
        Map<String, String> extMap = Objects.isNull(baseCondition.getExtData()) ? new HashMap<>() : baseCondition.getExtData();
        request.setExtData(extMap);
        extMap.put("analysisObjectVal", baseCondition.getAnalysisObjectVal());
        OnlineReportDeptDetailAnalysisResponse responseType = getDeptDetailAnalysisResponse(request);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            List<String> deptCousumeList = responseType.getDeptDetail();
            List<HeaderKeyValMap> headerKeyValMaps = responseType.getHeaderData();
            int totalRecords = Optional.ofNullable(responseType.getTotalRecords()).orElse(0);
            List list = new ArrayList();
            if (CollectionUtils.isNotEmpty(deptCousumeList)) {
                for (String detail : deptCousumeList) {
                    Map map = OrpGsonUtils.fromToJsonTypeTest(detail, Map.class);
                    if (map.containsKey("REAL_PAY")) {
                        map.put("REAL_PAY", CommonUtils.fmtMicrometer(((BigDecimal) map.get("REAL_PAY")).doubleValue()));
                    }
                    if (map.containsKey("FLT_REAL_PAY")) {
                        map.put("FLT_REAL_PAY", CommonUtils.fmtMicrometer(((BigDecimal) map.get("FLT_REAL_PAY")).doubleValue()));
                    }
                    if (map.containsKey("HLT_REAL_PAY")) {
                        map.put("HLT_REAL_PAY", CommonUtils.fmtMicrometer(((BigDecimal) map.get("HLT_REAL_PAY")).doubleValue()));
                    }
                    if (map.containsKey("THREE_PARTY_AMT") && Objects.nonNull(map.get("THREE_PARTY_AMT"))) {
                        map.put("THREE_PARTY_AMT", CommonUtils.fmtMicrometer(((BigDecimal) map.get("THREE_PARTY_AMT")).doubleValue()));
                    }
                    if (map.containsKey("FLT_AVG_PRICE_ECONOMY_CLASS") && Objects.nonNull(map.get("FLT_AVG_PRICE_ECONOMY_CLASS"))) {
                        map.put("FLT_AVG_PRICE_ECONOMY_CLASS", CommonUtils.fmtMicrometer(((BigDecimal) map.get("FLT_AVG_PRICE_ECONOMY_CLASS")).doubleValue()));
                    }
                    if (map.containsKey("FLT_AVG_PRICE_RATE_ECONOMY_CLASS_DOM") && Objects.nonNull(map.get("FLT_AVG_PRICE_RATE_ECONOMY_CLASS_DOM"))) {
                        map.put("FLT_AVG_PRICE_RATE_ECONOMY_CLASS_DOM", CommonUtils.fmtMicrometer(((BigDecimal) map.get("FLT_AVG_PRICE_RATE_ECONOMY_CLASS_DOM")).doubleValue()));
                    }
                    if (map.containsKey("FLT_AVG_MILE_PRICE_ECONOMY_CLASS") && Objects.nonNull(map.get("FLT_AVG_MILE_PRICE_ECONOMY_CLASS"))) {
                        map.put("FLT_AVG_MILE_PRICE_ECONOMY_CLASS", CommonUtils.fmtMicrometer(((BigDecimal) map.get("FLT_AVG_MILE_PRICE_ECONOMY_CLASS")).doubleValue()));
                    }
                    if (map.containsKey("FLT_SAVE") && Objects.nonNull(map.get("FLT_SAVE"))) {
                        map.put("FLT_SAVE", CommonUtils.fmtMicrometer(((BigDecimal) map.get("FLT_SAVE")).doubleValue()));
                    }
                    if (map.containsKey("FLT_LOSS") && Objects.nonNull(map.get("FLT_LOSS"))) {
                        map.put("FLT_LOSS", CommonUtils.fmtMicrometer(((BigDecimal) map.get("FLT_LOSS")).doubleValue()));
                    }
                    if (map.containsKey("FLT_REFUND_FEE") && Objects.nonNull(map.get("FLT_REFUND_FEE"))) {
                        map.put("FLT_REFUND_FEE", CommonUtils.fmtMicrometer(((BigDecimal) map.get("FLT_REFUND_FEE")).doubleValue()));
                    }
                    if (map.containsKey("FLT_REBOOK_FEE") && Objects.nonNull(map.get("FLT_REBOOK_FEE"))) {
                        map.put("FLT_REBOOK_FEE", CommonUtils.fmtMicrometer(((BigDecimal) map.get("FLT_REBOOK_FEE")).doubleValue()));
                    }
                    if (map.containsKey("FLT_REFUND_REBOOK_FEE") && Objects.nonNull(map.get("FLT_REFUND_REBOOK_FEE"))) {
                        map.put("FLT_REFUND_REBOOK_FEE", CommonUtils.fmtMicrometer(((BigDecimal) map.get("FLT_REFUND_REBOOK_FEE")).doubleValue()));
                    }
                    if (map.containsKey("HTL_AVG_PRICE") && Objects.nonNull(map.get("HTL_AVG_PRICE"))) {
                        map.put("HTL_AVG_PRICE", CommonUtils.fmtMicrometer(((BigDecimal) map.get("HTL_AVG_PRICE")).doubleValue()));
                    }
                    if (map.containsKey("HTL_AVG_DEAD_PRICE") && Objects.nonNull(map.get("HTL_AVG_DEAD_PRICE"))) {
                        map.put("HTL_AVG_DEAD_PRICE", CommonUtils.fmtMicrometer(((BigDecimal) map.get("HTL_AVG_DEAD_PRICE")).doubleValue()));
                    }
                    if (map.containsKey("HTL_OVER_AMOUNT") && Objects.nonNull(map.get("HTL_OVER_AMOUNT"))) {
                        map.put("HTL_OVER_AMOUNT", CommonUtils.fmtMicrometer(((BigDecimal) map.get("HTL_OVER_AMOUNT")).doubleValue()));
                    }
                    if (map.containsKey("TRAIN_AVG_PRICE") && Objects.nonNull(map.get("TRAIN_AVG_PRICE"))) {
                        map.put("TRAIN_AVG_PRICE", CommonUtils.fmtMicrometer(((BigDecimal) map.get("TRAIN_AVG_PRICE")).doubleValue()));
                    }
                    if (map.containsKey("CAR_AVG_MILE_PRICE") && Objects.nonNull(map.get("CAR_AVG_MILE_PRICE"))) {
                        map.put("CAR_AVG_MILE_PRICE", CommonUtils.fmtMicrometer(((BigDecimal) map.get("CAR_AVG_MILE_PRICE")).doubleValue()));
                    }
                    list.add(map);
                }
            }
            resp.put("header", headerKeyValMaps);
            resp.put("list", list);
            resp.put("totalRecords", totalRecords);
            return resp;
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(responseType)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
            }
            throw businessException;
        }
    }

    /**
     * 部门明细分析
     *
     * @param baseCondition
     * @return
     * @throws BusinessException
     */
    public Object getDeptUidDetailAnalysis(BaseQueryConditionBO baseCondition) throws BusinessException {
        Map resp = new HashMap();
        SubQueryReportBuTypeEnum subQueryReportBuTypeEnum = SubQueryReportBuTypeEnum.getByOrdinal(String.valueOf(baseCondition.getSubQueryBu()));
        QueryReportBuTypeEnum queryReportBuTypeEnum = QueryReportBuTypeEnum.valueOf(subQueryReportBuTypeEnum.getBizType().toLowerCase());
        OnlineReportDeptUidConsumeDetailRequest request = new OnlineReportDeptUidConsumeDetailRequest();
        request.setAnalysisObjectOrgInfo(baseCondition.getAnalysisObjectOrgInfo());
        request.setDrillDownVal(baseCondition.getDrillDownVal());
        request.setUser(baseCondition.getUser());
        // 下转对象
        AnalysisObjectEnum scrollDownObj = StringUtils.isEmpty(baseCondition.getDrillDownObjectEnum()) ? null : AnalysisObjectEnum.valueOf(baseCondition.getDrillDownObjectEnum().toUpperCase());
        request.setDrillDownObjectEnum(scrollDownObj);
        request.setPage(baseCondition.getPager());
        request.setQueryBu(queryReportBuTypeEnum);
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setLang(baseCondition.getLang());
        request.setProductType(baseCondition.getProductType());
        Map<String, String> extMap = Objects.isNull(baseCondition.getExtData()) ? new HashMap<>() : baseCondition.getExtData();
        extMap.put("analysisObjectVal", baseCondition.getAnalysisObjectVal());
        request.setExtData(extMap);
        OnlineReportDeptUidConsumeDetailResponse responseType = corpOnlineReportPlatformService.queryDeptUidConsumeDetail(request);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            OnlineReportDeptUidConsumeDetailInfo uidConsumeDetailInfo = Optional.ofNullable(responseType.getDeptUidConsumeDetailInfo()).orElse(new OnlineReportDeptUidConsumeDetailInfo());
            List<HeaderKeyValMap> headerKeyValMaps = Lists.newArrayList();
            HeaderKeyValMap headerKeyValMap = new HeaderKeyValMap();
            headerKeyValMap.setHeaderKey("UID");
            headerKeyValMap.setHeaderValue(SharkUtils.get("Exceltopname.uidnumber", request.getLang()));
            headerKeyValMaps.add(headerKeyValMap);
            HeaderKeyValMap headerKeyValMap1 = new HeaderKeyValMap();
            headerKeyValMap1.setHeaderKey("USERNAME");
            headerKeyValMap1.setHeaderValue(SharkUtils.get("Exceltopname.cardholder", request.getLang()));
            headerKeyValMaps.add(headerKeyValMap1);
            List dataList = new ArrayList();
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.flight.name())) {
                List<OnlineReportFltTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getFltList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.flight.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    HeaderKeyValMap temp = new HeaderKeyValMap();
                    temp.setHeaderKey(deptUidStatisticalsEnum.toString());
                    temp.setHeaderValue(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), baseCondition.getLang()));
                    headerKeyValMaps.add(temp);
                }
                for (OnlineReportFltTopDeptConsume consume : consumeList) {
                    Map map = new HashMap();
                    map.put("UID", consume.getDimId());
                    map.put("USERNAME", consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        map.put(deptUidStatisticalsEnum.toString(), getFlightUidStatisticalsVal(deptUidStatisticalsEnum, consume, true));
                    }
                    dataList.add(map);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.hotel.name())) {
                List<OnlineReportHtlTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getHtlList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.hotel.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    HeaderKeyValMap temp = new HeaderKeyValMap();
                    temp.setHeaderKey(deptUidStatisticalsEnum.toString());
                    temp.setHeaderValue(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), baseCondition.getLang()));
                    headerKeyValMaps.add(temp);
                }
                for (OnlineReportHtlTopDeptConsume consume : consumeList) {
                    Map map = new HashMap();
                    map.put("UID", consume.getDimId());
                    map.put("USERNAME", consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        map.put(deptUidStatisticalsEnum.toString(), getHotelUidStatisticalsVal(deptUidStatisticalsEnum, consume, true));
                    }
                    dataList.add(map);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.train.name())) {
                List<OnlineReportTrainTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getTrainList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.train.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    HeaderKeyValMap temp = new HeaderKeyValMap();
                    temp.setHeaderKey(deptUidStatisticalsEnum.toString());
                    temp.setHeaderValue(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), baseCondition.getLang()));
                    headerKeyValMaps.add(temp);
                }
                for (OnlineReportTrainTopDeptConsume consume : consumeList) {
                    Map map = new HashMap();
                    map.put("UID", consume.getDimId());
                    map.put("USERNAME", consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        map.put(deptUidStatisticalsEnum.toString(), getTrainUidStatisticalsVal(deptUidStatisticalsEnum, consume, true));
                    }
                    dataList.add(map);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.car.name())) {
                List<OnlineReportCarTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getCarList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.car.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    HeaderKeyValMap temp = new HeaderKeyValMap();
                    temp.setHeaderKey(deptUidStatisticalsEnum.toString());
                    temp.setHeaderValue(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), baseCondition.getLang()));
                    headerKeyValMaps.add(temp);
                }
                for (OnlineReportCarTopDeptConsume consume : consumeList) {
                    Map map = new HashMap();
                    map.put("UID", consume.getDimId());
                    map.put("USERNAME", consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        map.put(deptUidStatisticalsEnum.toString(), getCarUidStatisticalsVal(deptUidStatisticalsEnum, consume, true));
                    }
                    dataList.add(map);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.bus.name())) {
                List<OnlineReportBusTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getBusList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.bus.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    HeaderKeyValMap temp = new HeaderKeyValMap();
                    temp.setHeaderKey(deptUidStatisticalsEnum.toString());
                    temp.setHeaderValue(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), baseCondition.getLang()));
                    headerKeyValMaps.add(temp);
                }
                for (OnlineReportBusTopDeptConsume consume : consumeList) {
                    Map map = new HashMap();
                    map.put("UID", consume.getDimId());
                    map.put("USERNAME", consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        map.put(deptUidStatisticalsEnum.toString(), getBusUidStatisticalsVal(deptUidStatisticalsEnum, consume, true));
                    }
                    dataList.add(map);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.vaso.name())) {
                List<OnlineReportVasoTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getVasoList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.vaso.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    HeaderKeyValMap temp = new HeaderKeyValMap();
                    temp.setHeaderKey(deptUidStatisticalsEnum.toString());
                    temp.setHeaderValue(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), baseCondition.getLang()));
                    headerKeyValMaps.add(temp);
                }
                for (OnlineReportVasoTopDeptConsume consume : consumeList) {
                    Map map = new HashMap();
                    map.put("UID", consume.getDimId());
                    map.put("USERNAME", consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        map.put(deptUidStatisticalsEnum.toString(), getVasoUidStatisticalsVal(deptUidStatisticalsEnum, consume, true));
                    }
                    dataList.add(map);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.welfare.name())) {
                List<OnlineReportWelfareTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getWelfareList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.welfare.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    HeaderKeyValMap temp = new HeaderKeyValMap();
                    temp.setHeaderKey(deptUidStatisticalsEnum.toString());
                    temp.setHeaderValue(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), baseCondition.getLang()));
                    headerKeyValMaps.add(temp);
                }
                for (OnlineReportWelfareTopDeptConsume consume : consumeList) {
                    Map map = new HashMap();
                    map.put("UID", consume.getDimId());
                    map.put("USERNAME", consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        map.put(deptUidStatisticalsEnum.toString(), getWelfareUidStatisticalVal(deptUidStatisticalsEnum, consume, true));
                    }
                    dataList.add(map);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.overview.name())) {
                List<OnlineReportOverviewTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getOverViewList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.overview.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    HeaderKeyValMap temp = new HeaderKeyValMap();
                    temp.setHeaderKey(deptUidStatisticalsEnum.toString());
                    temp.setHeaderValue(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), baseCondition.getLang()));
                    headerKeyValMaps.add(temp);
                }
                for (OnlineReportOverviewTopDeptConsume consume : consumeList) {
                    Map map = new HashMap();
                    map.put("UID", consume.getDimId());
                    map.put("USERNAME", consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        map.put(deptUidStatisticalsEnum.toString(), getOverviewUidStatisticalsVal(deptUidStatisticalsEnum, consume, true));
                    }
                    dataList.add(map);
                }
            }

            int totalRecords = Optional.ofNullable(uidConsumeDetailInfo.getTotalRecords()).orElse(0);
            resp.put("header", headerKeyValMaps);
            resp.put("list", dataList);
            resp.put("totalRecords", totalRecords);
            return resp;
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(responseType)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
            }
            throw businessException;
        }
    }

    /**
     * 部门明细分析
     *
     * @param baseCondition
     * @return
     * @throws BusinessException
     */
    public Object getDeptStiticalAnalysis(BaseQueryConditionBO baseCondition, AnalysisObjectEnum analysisObjectEnum) throws BusinessException {
        Map resp = new HashMap();
        String indicator = baseCondition.getIndicator();
        OnlineReportTopDeptConsumeDetailRequest request = new OnlineReportTopDeptConsumeDetailRequest();
        request.setLang(baseCondition.getLang());
        request.setAnalysisObjectEnum(analysisObjectEnum);
        request.setAnalysisObjectOrgInfo(baseCondition.getAnalysisObjectOrgInfo());
        request.setTopLimit(5);
        request.setProductType(baseCondition.getProductType());
        request.setDrillDownVal(baseCondition.getDrillDownVal());
        // 下转对象
        AnalysisObjectEnum scrollDownObj = StringUtils.isEmpty(baseCondition.getDrillDownObjectEnum()) ? null : AnalysisObjectEnum.valueOf(baseCondition.getDrillDownObjectEnum().toUpperCase());
        request.setDrillDownObjectEnum(scrollDownObj);
        // 下转对象的值
        request.setDrillDownVal(baseCondition.getDrillDownVal());
        String lang = baseCondition.getLang();
        FlightDeptFieldEnum flightDeptFieldEnum = FlightDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        HotelDeptFieldEnum hotelDeptFieldEnum = HotelDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        TrainDeptFieldEnum trainDeptFieldEnum = TrainDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        CarDeptFieldEnum carDeptFieldEnum = CarDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        WelfareDeptFieldEnum welfareDeptFieldEnum = WelfareDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        OverViewDeptFieldEnum overViewDeptFieldEnum = OverViewDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        String needIndustry = "";
        if (flightDeptFieldEnum != null) {
            needIndustry = flightDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.flight);
        } else if (hotelDeptFieldEnum != null) {
            needIndustry = hotelDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.hotel);
        } else if (trainDeptFieldEnum != null) {
            needIndustry = trainDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.train);
        } else if (carDeptFieldEnum != null) {
            needIndustry = carDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.car);
        } else if (welfareDeptFieldEnum != null) {
            needIndustry = welfareDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.welfare);
        } else if (overViewDeptFieldEnum != null) {
            needIndustry = overViewDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.overview);
        }
        if (request.getQueryBu() == null) {
            throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
        }
        Map<String, String> map = Objects.isNull(baseCondition.getExtData()) ? new HashMap<>() : baseCondition.getExtData();
        request.setExtData(map);
        map.put("needIndustry", needIndustry);
        map.put("sortStatistics", indicator);
        QueryReportBuTypeEnum queryBu = request.getQueryBu();
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setLang(baseCondition.getLang());

        if (StringUtils.isNotEmpty(baseCondition.getDrillDownObjectEnum())) {
            request.setDrillDownObjectEnum(AnalysisObjectEnum.valueOf(baseCondition.getDrillDownObjectEnum().toUpperCase()));
        }

        OnlineReportTopDeptConsumeDetailResponse response = corpOnlineReportPlatformService.queryTopDeptConsumeDetail(request);
        if (response == null || response.getResponseCode() != 20000) {
            BusinessException businessException = null;
            if (Objects.isNull(response)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
            }
            throw businessException;
        }
        if (queryBu == QueryReportBuTypeEnum.flight) {
            FltTopDeptConsumeInfo fltTopDeptConsumeInfo = Optional.ofNullable(response.getFltTopConsumeInfo()).orElse(new FltTopDeptConsumeInfo());
            List<OnlineReportFltTopDeptConsume> markMetricInfoList = Optional.ofNullable(fltTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportFltTopDeptConsume companySum = Optional.ofNullable(fltTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportFltTopDeptConsume());
            List list = new ArrayList();
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                for (OnlineReportFltTopDeptConsume deptConsume : markMetricInfoList) {
                    list.add(dataMap(deptConsume.getDim(), getFltStatitical(flightDeptFieldEnum, deptConsume, false), flightDeptFieldEnum.getUnit(),
                            getFltStatiticalPercent(flightDeptFieldEnum, deptConsume, companySum, false), flightDeptFieldEnum.getDataType(), lang, deptConsume.getDimId()));
                }
            }
            if (!flightDeptFieldEnum.isSupportPie()) {
                OnlineReportFltTopDeptConsume corp = Optional.ofNullable(fltTopDeptConsumeInfo.getCorpConsume()).orElse(new OnlineReportFltTopDeptConsume());
                OnlineReportFltTopDeptConsume industry = Optional.ofNullable(fltTopDeptConsumeInfo.getIndustryConsume()).orElse(new OnlineReportFltTopDeptConsume());
                resp.put("companyVal", getFltStatitical(flightDeptFieldEnum, companySum, false));
                resp.put("corpVal", getFltStatitical(flightDeptFieldEnum, corp, false));
                resp.put("industryVal", getFltStatitical(flightDeptFieldEnum, industry, false));
            } else {
                OnlineReportFltTopDeptConsume other = fltTopDeptConsumeInfo.getOtherConsume();
                if (Objects.nonNull(other)) {
                    resp.put("other", dataMap(SharkUtils.get("Index.others", lang), getFltStatitical(flightDeptFieldEnum, other, false), flightDeptFieldEnum.getUnit(),
                            getFltStatiticalPercent(flightDeptFieldEnum, other, companySum, false), flightDeptFieldEnum.getDataType(), lang, companySum.getDimId()));
                }
            }
            resp.put("isSupportPie", flightDeptFieldEnum.isSupportPie());
            resp.put("list", list);
            return resp;
        } else if (queryBu == QueryReportBuTypeEnum.hotel) {
            HtlTopDeptConsumeInfo htlTopDeptConsumeInfo = Optional.ofNullable(response.getHtlTopConsumeInfo()).orElse(new HtlTopDeptConsumeInfo());
            List<OnlineReportHtlTopDeptConsume> markMetricInfoList = Optional.ofNullable(htlTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportHtlTopDeptConsume companySum = Optional.ofNullable(htlTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportHtlTopDeptConsume());
            List list = new ArrayList();
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                for (OnlineReportHtlTopDeptConsume deptConsume : markMetricInfoList) {
                    list.add(dataMap(deptConsume.getDim(), getHtlStatitical(hotelDeptFieldEnum, deptConsume, false), hotelDeptFieldEnum.getUnit(),
                            getHtlStatiticalPercent(hotelDeptFieldEnum, deptConsume, companySum, false), hotelDeptFieldEnum.getDataType(), lang, deptConsume.getDimId()));
                }
            }
            if (!hotelDeptFieldEnum.isSupportPie()) {
                OnlineReportHtlTopDeptConsume corp = Optional.ofNullable(htlTopDeptConsumeInfo.getCorpConsume()).orElse(new OnlineReportHtlTopDeptConsume());
                OnlineReportHtlTopDeptConsume industry = Optional.ofNullable(htlTopDeptConsumeInfo.getIndustryConsume()).orElse(new OnlineReportHtlTopDeptConsume());
                resp.put("companyVal", getHtlStatitical(hotelDeptFieldEnum, companySum, false));
                resp.put("corpVal", getHtlStatitical(hotelDeptFieldEnum, corp, false));
                resp.put("industryVal", getHtlStatitical(hotelDeptFieldEnum, industry, false));
            } else {
                OnlineReportHtlTopDeptConsume other = htlTopDeptConsumeInfo.getOtherConsume();
                if (Objects.nonNull(other)) {
                    resp.put("other", dataMap(SharkUtils.get("Index.others", lang), getHtlStatitical(hotelDeptFieldEnum, other, false), hotelDeptFieldEnum.getUnit(),
                            getHtlStatiticalPercent(hotelDeptFieldEnum, other, companySum, false), hotelDeptFieldEnum.getDataType(), lang, companySum.getDimId()));
                }
            }
            resp.put("isSupportPie", hotelDeptFieldEnum.isSupportPie());
            resp.put("list", list);
            return resp;
        } else if (queryBu == QueryReportBuTypeEnum.train) {
            TrainTopDeptConsumeInfo trainTopDeptConsumeInfo = Optional.ofNullable(response.getTrainTopConsumeInfo()).orElse(new TrainTopDeptConsumeInfo());
            List<OnlineReportTrainTopDeptConsume> markMetricInfoList = Optional.ofNullable(trainTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportTrainTopDeptConsume companySum = Optional.ofNullable(trainTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportTrainTopDeptConsume());
            List list = new ArrayList();
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                for (OnlineReportTrainTopDeptConsume deptConsume : markMetricInfoList) {
                    list.add(dataMap(deptConsume.getDim(), getTrainStatitical(trainDeptFieldEnum, deptConsume, false), trainDeptFieldEnum.getUnit(),
                            getTrainStatiticalPercent(trainDeptFieldEnum, deptConsume, companySum, false), trainDeptFieldEnum.getDataType(), lang, deptConsume.getDimId()));
                }
            }
            if (!trainDeptFieldEnum.isSupportPie()) {
                OnlineReportTrainTopDeptConsume corp = Optional.ofNullable(trainTopDeptConsumeInfo.getCorpConsume()).orElse(new OnlineReportTrainTopDeptConsume());
                OnlineReportTrainTopDeptConsume industry = Optional.ofNullable(trainTopDeptConsumeInfo.getIndustryConsume()).orElse(new OnlineReportTrainTopDeptConsume());
                resp.put("companyVal", getTrainStatitical(trainDeptFieldEnum, companySum, false));
                resp.put("corpVal", getTrainStatitical(trainDeptFieldEnum, corp, false));
                resp.put("industryVal", getTrainStatitical(trainDeptFieldEnum, industry, false));
            } else {
                OnlineReportTrainTopDeptConsume other = trainTopDeptConsumeInfo.getOtherConsume();
                if (Objects.nonNull(other)) {
                    resp.put("other", dataMap(SharkUtils.get("Index.others", lang), getTrainStatitical(trainDeptFieldEnum, other, false), trainDeptFieldEnum.getUnit(),
                            getTrainStatiticalPercent(trainDeptFieldEnum, other, companySum, false), trainDeptFieldEnum.getDataType(), lang, companySum.getDimId()));
                }
            }
            resp.put("isSupportPie", trainDeptFieldEnum.isSupportPie());
            resp.put("list", list);
            return resp;
        } else if (queryBu == QueryReportBuTypeEnum.car) {
            CarTopDeptConsumeInfo carTopDeptConsumeInfo = Optional.ofNullable(response.getCarTopConsumeInfo()).orElse(new CarTopDeptConsumeInfo());
            List<OnlineReportCarTopDeptConsume> markMetricInfoList = Optional.ofNullable(carTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportCarTopDeptConsume companySum = Optional.ofNullable(carTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportCarTopDeptConsume());
            List list = new ArrayList();
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                for (OnlineReportCarTopDeptConsume deptConsume : markMetricInfoList) {
                    list.add(dataMap(deptConsume.getDim(), getCarStatitical(carDeptFieldEnum, deptConsume), carDeptFieldEnum.getUnit(),
                            getCarStatiticalPercent(carDeptFieldEnum, deptConsume, companySum, false), carDeptFieldEnum.getDataType(), lang, deptConsume.getDimId()));
                }
            }
            if (!carDeptFieldEnum.isSupportPie()) {
                OnlineReportCarTopDeptConsume corp = Optional.ofNullable(carTopDeptConsumeInfo.getCorpConsume()).orElse(new OnlineReportCarTopDeptConsume());
                OnlineReportCarTopDeptConsume industry = Optional.ofNullable(carTopDeptConsumeInfo.getIndustryConsume()).orElse(new OnlineReportCarTopDeptConsume());
                resp.put("companyVal", getCarStatitical(carDeptFieldEnum, companySum));
                resp.put("corpVal", getCarStatitical(carDeptFieldEnum, corp));
                resp.put("industryVal", getCarStatitical(carDeptFieldEnum, industry));
            } else {
                OnlineReportCarTopDeptConsume other = carTopDeptConsumeInfo.getOtherConsume();
                if (Objects.nonNull(other)) {
                    resp.put("other", (dataMap(SharkUtils.get("Index.others", lang), getCarStatitical(carDeptFieldEnum, other), carDeptFieldEnum.getUnit(),
                            getCarStatiticalPercent(carDeptFieldEnum, other, companySum, false), carDeptFieldEnum.getDataType(), lang, companySum.getDimId())));
                }
            }
            resp.put("isSupportPie", carDeptFieldEnum.isSupportPie());
            resp.put("list", list);
            return resp;
        } else if (QueryReportBuTypeEnum.welfare.equals(queryBu)) {
            WelfareTopDeptConsumeInfo welfareTopDeptConsumeInfo = Optional.ofNullable(response.getWelfareTopConsumeInfo()).orElse(new WelfareTopDeptConsumeInfo());
            List<OnlineReportWelfareTopDeptConsume> markMetricInfoList = Optional.ofNullable(response.getWelfareTopConsumeInfo().getTopList()).orElse(new ArrayList<>());
            OnlineReportWelfareTopDeptConsume companySum =
                    Optional.ofNullable(response.getWelfareTopConsumeInfo().getSumConsume()).orElse(new OnlineReportWelfareTopDeptConsume());
            List list = new ArrayList();
            Boolean isSupportPie = false;
            if (null != welfareDeptFieldEnum) {
                if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                    for (OnlineReportWelfareTopDeptConsume deptConsume : markMetricInfoList) {
                        list.add(dataMap(deptConsume.getDim(), getWelfareStatistical(welfareDeptFieldEnum, deptConsume),
                                welfareDeptFieldEnum.getUnit(),
                                getWelfareStatisticalPercent(welfareDeptFieldEnum, deptConsume, companySum, false),
                                welfareDeptFieldEnum.getDataType(), lang, deptConsume.getDimId()));
                    }
                }
                if (!welfareDeptFieldEnum.isSupportPie()) {
                    resp.put("companyVal", getWelfareStatistical(welfareDeptFieldEnum, companySum));
                } else {
                    OnlineReportWelfareTopDeptConsume otherConsume = welfareTopDeptConsumeInfo.getOtherConsume();
                    if (Objects.nonNull(otherConsume)) {
                        resp.put("other", dataMap(SharkUtils.get("Index.others", lang), getWelfareStatistical(welfareDeptFieldEnum, otherConsume),
                                welfareDeptFieldEnum.getUnit(), getWelfareStatisticalPercent(welfareDeptFieldEnum, otherConsume, companySum, false),
                                welfareDeptFieldEnum.getDataType(), lang, companySum.getDimId()));
                    }
                }
                isSupportPie = welfareDeptFieldEnum.isSupportPie();
            }
            resp.put("isSupportPie", isSupportPie);
            resp.put("list", list);
            return resp;
        } else if (queryBu == QueryReportBuTypeEnum.overview) {
            OverviewTopDeptConsumeInfo overviewTopDeptConsumeInfo = Optional.ofNullable(response.getOverviewTopConsumeInfo()).orElse(new OverviewTopDeptConsumeInfo());
            List<OnlineReportOverviewTopDeptConsume> markMetricInfoList = Optional.ofNullable(overviewTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportOverviewTopDeptConsume companySum = Optional.ofNullable(overviewTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportOverviewTopDeptConsume());
            List list = new ArrayList();
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                for (OnlineReportOverviewTopDeptConsume deptConsume : markMetricInfoList) {
                    list.add(dataMap(deptConsume.getDim(), getOverviewStatitical(overViewDeptFieldEnum, deptConsume, false), overViewDeptFieldEnum.getUnit(),
                            getOverviewStatiticalPercent(overViewDeptFieldEnum, deptConsume, companySum, false), overViewDeptFieldEnum.getDataType(), lang, deptConsume.getDimId()));
                }
            }
            if (!overViewDeptFieldEnum.isSupportPie()) {
                OnlineReportOverviewTopDeptConsume corp = Optional.ofNullable(overviewTopDeptConsumeInfo.getCorpConsume()).orElse(new OnlineReportOverviewTopDeptConsume());
                OnlineReportOverviewTopDeptConsume industry = Optional.ofNullable(overviewTopDeptConsumeInfo.getIndustryConsume()).orElse(new OnlineReportOverviewTopDeptConsume());
                resp.put("companyVal", getOverviewStatitical(overViewDeptFieldEnum, companySum, false));
                resp.put("corpVal", getOverviewStatitical(overViewDeptFieldEnum, corp, false));
                resp.put("industryVal", getOverviewStatitical(overViewDeptFieldEnum, industry, false));
            } else {
                OnlineReportOverviewTopDeptConsume other = overviewTopDeptConsumeInfo.getOtherConsume();
                if (Objects.nonNull(other)) {
                    resp.put("other", dataMap(SharkUtils.get("Index.others", lang), getOverviewStatitical(overViewDeptFieldEnum, other, false), overViewDeptFieldEnum.getUnit(),
                            getOverviewStatiticalPercent(overViewDeptFieldEnum, other, companySum, false), overViewDeptFieldEnum.getDataType(), lang, companySum.getDimId()));
                }
            }
            resp.put("isSupportPie", overViewDeptFieldEnum.isSupportPie());
            resp.put("list", list);
            return resp;
        }
        return null;
    }

    private Map dataMap(String name, Object val, String unit, Object percent, Integer dataType, String lang, String dimId) {
        Map map = new HashMap();
        map.put("name", name);
        map.put("val", val);
        map.put("unit", SharkUtils.get(unit, lang));
        map.put("percent", percent);
        map.put("dataType", dataType);
        map.put("dimId", dimId);
        return map;
    }

    private Object getFltStatitical(FlightDeptFieldEnum flightDeptFieldEnum, OnlineReportFltTopDeptConsume metricInfo, boolean needQuote) {
        metricInfo = Optional.ofNullable(metricInfo).orElse(new OnlineReportFltTopDeptConsume());
        Object result = null;
        switch (flightDeptFieldEnum) {
            case FLIGHT_AMOUNT:
                result = DigitBaseUtils.formatDigit(metricInfo.getTotalAmount());
                break;
            case FLIGHT_QUANTITY:
                result = Optional.ofNullable(metricInfo.getTotalQuantity()).orElse(0);
                break;
            case FLIGHT_AVG_PRICE:
                result = DigitBaseUtils.formatDigit(metricInfo.getAvgPrice());
                break;
            case FLIGHT_AVG_TPMS_PRICE:
                result = DigitBaseUtils.formatDigit(metricInfo.getAvgTpmsPrice());
                break;
            case FLIGHT_FULL_FARETKT_PERCENT:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getFullfaretktPercent()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getFullfaretktPercent()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case FLIGHT_OVER_AMOUNT:
                result = DigitBaseUtils.formatDigit(metricInfo.getTotalOverAmount());
                break;
            case FLIGHT_RC_PERCENT:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRcPercent()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRcPercent()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case FLIGHT_REFUND_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRefundRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRefundRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case FLIGHT_REBOOK_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRebookRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRebookRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case FLIGHT_SAVE_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
  /*          case FLIGHT_SAVE_3C_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveAmount3cRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveAmount3cRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case FLIGHT_SAVE_2C_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveAmount2cRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveAmount2cRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case FLIGHT_SAVE_CONTROL_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getControlSaveRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getControlSaveRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case FLIGHT_CARBONS:
                result = DigitBaseUtils.formatDigit(metricInfo.getTotalCarbons());
                break;
            case FLIGHT_CARBONS_SAVE_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getCarbonSaveRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getCarbonSaveRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;*/
        }
        return result;
    }

    private Object getFltStatiticalPercent(FlightDeptFieldEnum flightDeptFieldEnum, OnlineReportFltTopDeptConsume metricInfo, OnlineReportFltTopDeptConsume sum, boolean needQuote) {
        Object result = null;
        switch (flightDeptFieldEnum) {
            case FLIGHT_AMOUNT:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalAmount(), sum.getTotalAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalAmount(), sum.getTotalAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
            case FLIGHT_QUANTITY:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalQuantity(), sum.getTotalQuantity(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalQuantity(), sum.getTotalQuantity(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
            case FLIGHT_OVER_AMOUNT:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalOverAmount(), sum.getTotalOverAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalOverAmount(), sum.getTotalOverAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
/*            case FLIGHT_CARBONS:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalCarbons(), sum.getTotalCarbons(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalCarbons(), sum.getTotalCarbons(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;*/
            default:
                break;
        }
        return result;
    }

    private Object getHtlStatitical(HotelDeptFieldEnum hotelDeptFieldEnum, OnlineReportHtlTopDeptConsume metricInfo, boolean needQuote) {
        metricInfo = Optional.ofNullable(metricInfo).orElse(new OnlineReportHtlTopDeptConsume());
        Object result = null;
        switch (hotelDeptFieldEnum) {
            case HOTEL_AMOUNT:
                result = DigitBaseUtils.formatDigit(metricInfo.getTotalAmount());
                break;
            case HOTEL_QUANTITY:
                result = Optional.ofNullable(metricInfo.getTotalQuantity()).orElse(0);
                break;
            case HOTEL_AVG_PRICE:
                result = DigitBaseUtils.formatDigit(metricInfo.getAvgPrice());
                break;
            case HOTEL_OVER_AMOUNT:
                // 确保 totalOverAmount 不为 null，如果为 null 则使用 BigDecimal.ZERO
                BigDecimal overAmount = Optional.ofNullable(metricInfo.getTotalOverAmount()).orElse(BigDecimal.ZERO);
                result = DigitBaseUtils.formatDigit(overAmount);
                break;
            case HOTEL_RC_PERCENT:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRcPercent()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRcPercent()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case HOTEL_SAVE_3C_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveAmount3cRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveAmount3cRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case HOTEL_SAVE_2C_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveAmount2cRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveAmount2cRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case HOTEL_SAVE_CONTROL_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getControlSaveRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getControlSaveRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case HOTEL_SAVE_PROMOTION_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveAmountPromotionRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getSaveAmountPromotionRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
        }
        return result;
    }

    private Object getHtlStatiticalPercent(HotelDeptFieldEnum hotelDeptFieldEnum, OnlineReportHtlTopDeptConsume metricInfo, OnlineReportHtlTopDeptConsume sum, boolean needQuote) {
        Object result = null;
        switch (hotelDeptFieldEnum) {
            case HOTEL_AMOUNT:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalAmount(), sum.getTotalAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalAmount(), sum.getTotalAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
            case HOTEL_QUANTITY:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalQuantity(), sum.getTotalQuantity(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalQuantity(), sum.getTotalQuantity(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
            case HOTEL_OVER_AMOUNT:
                // 确保 totalOverAmount 不为 null，如果为 null 则使用 BigDecimal.ZERO
                BigDecimal metricOverAmount = Optional.ofNullable(metricInfo.getTotalOverAmount()).orElse(BigDecimal.ZERO);
                BigDecimal sumOverAmount = Optional.ofNullable(sum.getTotalOverAmount()).orElse(BigDecimal.ZERO);
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricOverAmount, sumOverAmount, 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricOverAmount, sumOverAmount, 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
            default:
                break;
        }
        return result;
    }

    private Object getTrainStatitical(TrainDeptFieldEnum trainDeptFieldEnum, OnlineReportTrainTopDeptConsume metricInfo, boolean needQuote) {
        metricInfo = Optional.ofNullable(metricInfo).orElse(new OnlineReportTrainTopDeptConsume());
        Object result = null;
        switch (trainDeptFieldEnum) {
            case TRAIN_AMOUNT:
                result = DigitBaseUtils.formatDigit(metricInfo.getTotalAmount());
                break;
            case TRAIN_QUANTITY:
                result = Optional.ofNullable(metricInfo.getTotalQuantity()).orElse(0);
                break;
            case TRAIN_AVG_PRICE:
                result = DigitBaseUtils.formatDigit(metricInfo.getAvgPrice());
                break;
            case TRAIN_REFUND_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRefundRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRefundRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
            case TRAIN_REBOOK_RATE:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRebookRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getRebookRate()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
        }
        return result;
    }

    private Object getTrainStatiticalPercent(TrainDeptFieldEnum trainDeptFieldEnum, OnlineReportTrainTopDeptConsume metricInfo, OnlineReportTrainTopDeptConsume sum, boolean needQuote) {
        Object result = null;
        switch (trainDeptFieldEnum) {
            case TRAIN_AMOUNT:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalAmount(), sum.getTotalAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalAmount(), sum.getTotalAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
            case TRAIN_QUANTITY:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalQuantity(), sum.getTotalQuantity(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalQuantity(), sum.getTotalQuantity(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
            default:
                break;
        }
        return result;
    }

    private Object getCarStatitical(CarDeptFieldEnum CarDeptFieldEnum, OnlineReportCarTopDeptConsume metricInfo) {
        metricInfo = Optional.ofNullable(metricInfo).orElse(new OnlineReportCarTopDeptConsume());
        Object result = null;
        switch (CarDeptFieldEnum) {
            case CAR_AMOUNT:
                result = DigitBaseUtils.formatDigit(metricInfo.getTotalAmount());
                break;
            case CAR_QUANTITY:
                result = Optional.ofNullable(metricInfo.getTotalOrderCount()).orElse(0);
                break;
        }
        return result;
    }

    private Object getCarStatiticalPercent(CarDeptFieldEnum CarDeptFieldEnum, OnlineReportCarTopDeptConsume metricInfo, OnlineReportCarTopDeptConsume sum, boolean needQuote) {
        Object result = null;
        switch (CarDeptFieldEnum) {
            case CAR_AMOUNT:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalAmount(), sum.getTotalAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalAmount(), sum.getTotalAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
            case CAR_QUANTITY:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalOrderCount(), sum.getTotalOrderCount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalOrderCount(), sum.getTotalOrderCount(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
        }
        return result;
    }

    private Object getWelfareStatistical(WelfareDeptFieldEnum welfareDeptFieldEnum, OnlineReportWelfareTopDeptConsume metricInfo) {
        metricInfo = Optional.ofNullable(metricInfo).orElse(new OnlineReportWelfareTopDeptConsume());
        Object result = null;
        if (WelfareDeptFieldEnum.WELFARE_ALLOT_AMOUNT.equals(welfareDeptFieldEnum)) {
            result = DigitBaseUtils.formatDigit(metricInfo.getAllotAmount());
        } else if (WelfareDeptFieldEnum.WELFARE_RETURN_AMOUNT.equals(welfareDeptFieldEnum)) {
            result = DigitBaseUtils.formatDigit(metricInfo.getReturnAmount());
        } else if (WelfareDeptFieldEnum.WELFARE_DEDUCT_AMOUNT.equals(welfareDeptFieldEnum)) {
            result = DigitBaseUtils.formatDigit(metricInfo.getDeductAmount());
        } else if (WelfareDeptFieldEnum.WELFARE_REFUND_AMOUNT.equals(welfareDeptFieldEnum)) {
            result = DigitBaseUtils.formatDigit(metricInfo.getRefundAmount());
        }
        return result;
    }

    private Object getWelfareStatisticalPercent(WelfareDeptFieldEnum welfareDeptFieldEnum, OnlineReportWelfareTopDeptConsume metricInfo,
                                                OnlineReportWelfareTopDeptConsume sum, boolean needQuote) {
        Object result = null;
        if (WelfareDeptFieldEnum.WELFARE_ALLOT_AMOUNT.equals(welfareDeptFieldEnum)) {
            if (needQuote) {
                result =
                        DigitBaseUtils.divide(metricInfo.getAllotAmount(), sum.getAllotAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
            } else {
                result = DigitBaseUtils.divide(metricInfo.getAllotAmount(), sum.getAllotAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
            }
        } else if (WelfareDeptFieldEnum.WELFARE_RETURN_AMOUNT.equals(welfareDeptFieldEnum)) {
            if (needQuote) {
                result =
                        DigitBaseUtils.divide(metricInfo.getReturnAmount(), sum.getReturnAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
            } else {
                result = DigitBaseUtils.divide(metricInfo.getReturnAmount(), sum.getReturnAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
            }
        } else if (WelfareDeptFieldEnum.WELFARE_DEDUCT_AMOUNT.equals(welfareDeptFieldEnum)) {
            if (needQuote) {
                result =
                        DigitBaseUtils.divide(metricInfo.getDeductAmount(), sum.getDeductAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
            } else {
                result = DigitBaseUtils.divide(metricInfo.getDeductAmount(), sum.getDeductAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
            }
        } else if (WelfareDeptFieldEnum.WELFARE_REFUND_AMOUNT.equals(welfareDeptFieldEnum)) {
            if (needQuote) {
                result =
                        DigitBaseUtils.divide(metricInfo.getRefundAmount(), sum.getRefundAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
            } else {
                result = DigitBaseUtils.divide(metricInfo.getRefundAmount(), sum.getRefundAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
            }
        }
        return result;
    }

    private Object getOverviewStatitical(OverViewDeptFieldEnum overViewDeptFieldEnum, OnlineReportOverviewTopDeptConsume metricInfo, boolean needQuote) {
        metricInfo = Optional.ofNullable(metricInfo).orElse(new OnlineReportOverviewTopDeptConsume());
        Object result = null;
        switch (overViewDeptFieldEnum) {
            case OVERVIEW_AMOUNT:
                result = DigitBaseUtils.formatDigit(metricInfo.getTotalAmount());
                break;
  /*          case OVERVIEW_CARBONS:
                result = DigitBaseUtils.formatDigit(metricInfo.getTotalCarbons());
                break;*/
            case OVERVIEW_OVER_AMOUNT:
                result = DigitBaseUtils.formatDigit(metricInfo.getTotalOverAmount());
                break;
            case OVERVIEW_RC_PERCENT:
                if (needQuote) {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getTotalRcPercent()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100))) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.formatDigit(Optional.ofNullable(metricInfo.getTotalRcPercent()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)));
                }
                break;
        }
        return result;
    }

    private Object getOverviewStatiticalPercent(OverViewDeptFieldEnum overViewDeptFieldEnum, OnlineReportOverviewTopDeptConsume metricInfo, OnlineReportOverviewTopDeptConsume sum, boolean needQuote) {
        Object result = null;
        switch (overViewDeptFieldEnum) {
            case OVERVIEW_AMOUNT:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalAmount(), sum.getTotalAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalAmount(), sum.getTotalAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
/*            case OVERVIEW_CARBONS:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalCarbons(), sum.getTotalCarbons(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalCarbons(), sum.getTotalCarbons(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;*/
            case OVERVIEW_OVER_AMOUNT:
                if (needQuote) {
                    result = DigitBaseUtils.divide(metricInfo.getTotalOverAmount(), sum.getTotalOverAmount(), 4).multiply(new BigDecimal(100)).setScale(2) + PERCENT_QUOTE;
                } else {
                    result = DigitBaseUtils.divide(metricInfo.getTotalOverAmount(), sum.getTotalOverAmount(), 4).multiply(new BigDecimal(100)).setScale(2);
                }
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 前五部门消费金额分析
     *
     * @param reportConsumeRequest
     * @return
     */
    public List<ChartExcelEntity> getTopFiveDeptConsumeAnalysisExcel(OnlineReportTopFiveDeptConsumeRequest reportConsumeRequest, String lang) throws BusinessException {
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        Pager pager = new Pager(0l, 10000, 0, 0);
        reportConsumeRequest.setPage(pager);
        OnlineReportTopFiveDeptConsumeResponse responseType = getTopFiveDeptConsumeResponse(reportConsumeRequest);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            List<OnlineReportDeptConsume> deptCousumeList = responseType.getDeptCousumeList();
            Map map = getConditionContent(reportConsumeRequest.getBasecondition().getUid(), reportConsumeRequest.getBasecondition(), lang);
            if (CollectionUtils.isNotEmpty(deptCousumeList)) {
                ChartExcelEntity sheet1 = new ChartExcelEntity();
                sheet1.setHeaders(Arrays.asList(SharkUtils.get("Public.department", lang), SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.CompareLast1yr", lang),
                        SharkUtils.get("Index.CompareLast2yr", lang),
                        SharkUtils.get("Index.rad", lang)));
                List data = new ArrayList();
                for (OnlineReportDeptConsume deptConsume : deptCousumeList) {
                    data.add(Arrays.asList(deptConsume.getDept(),
                            MapperUtils.convertDigitToZeroString(deptConsume.getTotalAmount()),
                            MapperUtils.convertDigitToZeroString(deptConsume.getYoyAmount()).concat(PERCENT_QUOTE),
                            MapperUtils.convertDigitToZeroString(deptConsume.getYoyAmountBeforeLast()).concat(PERCENT_QUOTE),
                            MapperUtils.convertDigitToZeroString(deptConsume.getMomAmount()).concat(PERCENT_QUOTE)));
                }
                data.addAll(buttonContent(lang, map, reportConsumeRequest.getAnalysisObjectEnum()));
                sheet1.setSheetTitle(getDeptConsumeSheetName(reportConsumeRequest.getAnalysisObjectEnum(), lang));
                sheet1.setSheetNum(0);
                sheet1.setData(data);
                chartExcelEntityList.add(sheet1);
            }
        }
        return chartExcelEntityList;
    }

    /**
     * 前5部门其他指标分析
     * <p>
     * =     * @param reportConsumeRequest
     *
     * @return
     * @throws BusinessException
     */
    public List<ChartExcelEntity> getTop5DeptOtherAnalysisExcel(OnlineReportTopFiveDeptAnalysisRequest reportConsumeRequest, String lang) throws BusinessException {
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        Pager pager = new Pager(0l, 10000, 0, 0);
        reportConsumeRequest.setPage(pager);
        OnlineReportTopFiveDeptAnalysisResponse responseType = getTopFiveDeptAnalysisResponse(reportConsumeRequest);
        AnalysisTypeEnum analysisTypeEnum = reportConsumeRequest.getAnalysisTypeEnum();
        if (responseType != null && responseType.getResponseCode() == 20000) {
            Map map = getConditionContent(reportConsumeRequest.getBasecondition().getUid(), reportConsumeRequest.getBasecondition(), lang);
            List<OnlineReportTopFiveAnalysis> deptCousumeList = responseType.getDeptAnalysisList();
            BigDecimal corpVal = responseType.getCorpVal();
            if (CollectionUtils.isNotEmpty(deptCousumeList)) {
                boolean isNeedPercentQuote = isNeedPercentQuote(analysisTypeEnum);
                ChartExcelEntity sheet1 = new ChartExcelEntity();
                sheet1.setHeaders(Arrays.asList(SharkUtils.get("Public.department", lang), getOtherAnalysisExcelTitle(analysisTypeEnum, lang), SharkUtils.get("Index.tmc", lang)));
                List data = new ArrayList();
                for (OnlineReportTopFiveAnalysis deptConsume : deptCousumeList) {
                    data.add(Arrays.asList(deptConsume.getDept(), isNeedPercentQuote ? MapperUtils.convertDigitToZeroString(deptConsume.getResult()).concat(PERCENT_QUOTE) : MapperUtils.convertDigitToZero(deptConsume.getResult())
                            , isNeedPercentQuote ? MapperUtils.convertDigitToZeroString(corpVal).concat(PERCENT_QUOTE) : MapperUtils.convertDigitToZero(corpVal)));
                }
                data.addAll(buttonContent(lang, map, reportConsumeRequest.getAnalysisObjectEnum()));
                sheet1.setSheetTitle(getOtherAnalysisSheetName(analysisTypeEnum, lang));
                sheet1.setSheetNum(0);
                sheet1.setData(data);
                chartExcelEntityList.add(sheet1);
            }
        }
        return chartExcelEntityList;
    }

    private String getDrillDownValDesc(AnalysisObjectEnum drillDownObj, String drillDownVal) {
        String drillDownValDesc = StringUtils.EMPTY;
        switch (drillDownObj) {
            case CORP:
                drillDownValDesc = getCorpName(drillDownVal);
                break;
            case ACCOUNT:
                drillDownValDesc = getAccountName(drillDownVal);
                break;
            case ACCOUNTCODE:
                drillDownValDesc = getAccountCode(drillDownVal);
                break;
            case DEPT1:
            case DEPT2:
            case DEPT3:
            case DEPT4:
            case DEPT5:
            case DEPT6:
            case DEPT7:
            case DEPT8:
            case DEPT9:
            case DEPT10:
            case COSTCENTER1:
            case COSTCENTER2:
            case COSTCENTER3:
            case COSTCENTER4:
            case COSTCENTER5:
            case COSTCENTER6:
                drillDownValDesc = StringUtils.trimToEmpty(drillDownVal);
                break;
            default:
                break;
        }
        return drillDownValDesc;
    }

    private String getCorpName(String val) {
        //return corpUserInfoService.getCorpName(val);
        return "";
    }

    private String getAccountName(String val) {
        /*Integer accountId = Integer.valueOf(val);
        Map<String, String> map = rpcGroup4jServiceClientService.getAccountNameMap(Arrays.asList(accountId), null);
        return StringUtils.trimToEmpty(map.get(val));*/
        return "";
    }

    private String getAccountCode(String val) {
        /*Integer accountId = Integer.valueOf(val);
        Map<String, String> map = rpcGroup4jServiceClientService.getAccountCodeMap(Arrays.asList(accountId), null);
        return StringUtils.trimToEmpty(map.get(val));*/
        return "";
    }

    /**
     * 前5部门其他指标分析
     * <p>
     * =     * @param reportConsumeRequest
     *
     * @return
     * @throws BusinessException
     */
    public List<ChartExcelEntity> getTop5DeptStatiticalAnalysisExcel(BaseQueryConditionBO baseCondition, AnalysisObjectEnum analysisObjectEnum, String lang) throws BusinessException {
        String indicator = baseCondition.getIndicator();
        OnlineReportTopDeptConsumeDetailRequest request = new OnlineReportTopDeptConsumeDetailRequest();
        request.setLang(lang);
        request.setAnalysisObjectEnum(analysisObjectEnum);
        request.setAnalysisObjectOrgInfo(baseCondition.getAnalysisObjectOrgInfo());
        request.setProductType(baseCondition.getProductType());
        request.setDrillDownVal(baseCondition.getDrillDownVal());
        // 下转对象
        AnalysisObjectEnum scrollDownObj = StringUtils.isEmpty(baseCondition.getDrillDownObjectEnum()) ? null : AnalysisObjectEnum.valueOf(baseCondition.getDrillDownObjectEnum().toUpperCase());
        request.setDrillDownObjectEnum(scrollDownObj);
        // 下转对象的值
        request.setDrillDownVal(baseCondition.getDrillDownVal());

        FlightDeptFieldEnum flightDeptFieldEnum = FlightDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        HotelDeptFieldEnum hotelDeptFieldEnum = HotelDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        TrainDeptFieldEnum trainDeptFieldEnum = TrainDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        CarDeptFieldEnum carDeptFieldEnum = CarDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        WelfareDeptFieldEnum welfareDeptFieldEnum = WelfareDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        OverViewDeptFieldEnum overViewDeptFieldEnum = OverViewDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        String needIndustry = "";
        if (flightDeptFieldEnum != null) {
            needIndustry = flightDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.flight);
        } else if (hotelDeptFieldEnum != null) {
            needIndustry = hotelDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.hotel);
        } else if (trainDeptFieldEnum != null) {
            needIndustry = trainDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.train);
        } else if (carDeptFieldEnum != null) {
            needIndustry = carDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.car);
        } else if (welfareDeptFieldEnum != null) {
            needIndustry = welfareDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.welfare);
        } else if (overViewDeptFieldEnum != null) {
            needIndustry = overViewDeptFieldEnum.isSupportPie() ? "F" : "T";
            request.setQueryBu(QueryReportBuTypeEnum.overview);
        }
        if (request.getQueryBu() == null) {
            throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
        }
        Map<String, String> extMap = Objects.isNull(baseCondition.getExtData()) ? new HashMap<>() : baseCondition.getExtData();
        request.setExtData(extMap);
        extMap.put("needIndustry", needIndustry);
        extMap.put("sortStatistics", indicator);
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setLang(baseCondition.getLang());

        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        request.setTopLimit(10000);
        QueryReportBuTypeEnum queryBu = request.getQueryBu();
        OnlineReportTopDeptConsumeDetailResponse response = corpOnlineReportPlatformService.queryTopDeptConsumeDetail(request);
        String tmcTitle = Objects.isNull(scrollDownObj) ? SharkUtils.get("Index.tmc", lang) : SharkUtils.get("Index.public", lang);
        String industryTitle = SharkUtils.get("Index.industry", lang);
        String publicTitle = Objects.isNull(scrollDownObj) ? SharkUtils.get("Index.public", lang) : (getDrillDownValDesc(scrollDownObj, baseCondition.getDrillDownVal()));

        if (response == null || response.getResponseCode() != 20000) {
            BusinessException businessException = null;
            if (Objects.isNull(response)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
            }
            throw businessException;
        }
        Map map = getConditionContent(request.getBasecondition().getUid(), request.getBasecondition(), lang);

        if (queryBu == QueryReportBuTypeEnum.flight) {
            FltTopDeptConsumeInfo fltTopDeptConsumeInfo = Optional.ofNullable(response.getFltTopConsumeInfo()).orElse(new FltTopDeptConsumeInfo());
            List<OnlineReportFltTopDeptConsume> markMetricInfoList = Optional.ofNullable(fltTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportFltTopDeptConsume companySum = Optional.ofNullable(fltTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportFltTopDeptConsume());
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                ChartExcelEntity sheet1 = new ChartExcelEntity();
                List headers = Lists.newArrayList(getAnalysisObjectName(analysisObjectEnum, lang), SharkUtils.get(flightDeptFieldEnum.getHeaderKey(), lang));
                List data = new ArrayList();
                for (OnlineReportFltTopDeptConsume deptConsume : markMetricInfoList) {
                    List dataItem = Lists.newArrayList(deptConsume.getDim(), getFltStatitical(flightDeptFieldEnum, deptConsume, true));
                    if (flightDeptFieldEnum.isSupportPie()) {
                        dataItem.add(getFltStatiticalPercent(flightDeptFieldEnum, deptConsume, companySum, false) + PERCENT_QUOTE);
                    }
                    data.add(dataItem);
                }
                if (!flightDeptFieldEnum.isSupportPie()) {
                    OnlineReportFltTopDeptConsume corp = Optional.ofNullable(fltTopDeptConsumeInfo.getCorpConsume()).orElse(new OnlineReportFltTopDeptConsume());
                    OnlineReportFltTopDeptConsume industry = Optional.ofNullable(fltTopDeptConsumeInfo.getIndustryConsume()).orElse(new OnlineReportFltTopDeptConsume());
                    data.add(Arrays.asList(publicTitle, getFltStatitical(flightDeptFieldEnum, companySum, true)));
                    data.add(Arrays.asList(tmcTitle, getFltStatitical(flightDeptFieldEnum, corp, true)));
                    if (Objects.isNull(scrollDownObj)) {
                        data.add(Arrays.asList(industryTitle, getFltStatitical(flightDeptFieldEnum, industry, true)));
                    }
                } else {
                    headers.add(SharkUtils.get("Exceltopname.numberPercentage", lang));
                }
                data.addAll(buttonContent(lang, map, request.getAnalysisObjectEnum()));
                sheet1.setHeaders(headers);
                sheet1.setSheetTitle(getAnalysisSheetName(baseCondition, analysisObjectEnum, lang));
                sheet1.setSheetNum(0);
                sheet1.setData(data);
                chartExcelEntityList.add(sheet1);
            }

        } else if (queryBu == QueryReportBuTypeEnum.hotel) {
            HtlTopDeptConsumeInfo htlTopDeptConsumeInfo = Optional.ofNullable(response.getHtlTopConsumeInfo()).orElse(new HtlTopDeptConsumeInfo());
            List<OnlineReportHtlTopDeptConsume> markMetricInfoList = Optional.ofNullable(htlTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportHtlTopDeptConsume companySum = Optional.ofNullable(htlTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportHtlTopDeptConsume());
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                ChartExcelEntity sheet1 = new ChartExcelEntity();
                List headers = Lists.newArrayList(getAnalysisObjectName(analysisObjectEnum, lang), SharkUtils.get(hotelDeptFieldEnum.getHeaderKey(), lang));
                List data = new ArrayList();
                for (OnlineReportHtlTopDeptConsume deptConsume : markMetricInfoList) {
                    List dataItem = Lists.newArrayList(deptConsume.getDim(), getHtlStatitical(hotelDeptFieldEnum, deptConsume, true));
                    if (hotelDeptFieldEnum.isSupportPie()) {
                        dataItem.add(getHtlStatiticalPercent(hotelDeptFieldEnum, deptConsume, companySum, false) + PERCENT_QUOTE);
                    }
                    data.add(dataItem);
                }
                if (!hotelDeptFieldEnum.isSupportPie()) {
                    OnlineReportHtlTopDeptConsume corp = Optional.ofNullable(htlTopDeptConsumeInfo.getCorpConsume()).orElse(new OnlineReportHtlTopDeptConsume());
                    OnlineReportHtlTopDeptConsume industry = Optional.ofNullable(htlTopDeptConsumeInfo.getIndustryConsume()).orElse(new OnlineReportHtlTopDeptConsume());
                    data.add(Arrays.asList(publicTitle, getHtlStatitical(hotelDeptFieldEnum, companySum, true)));
                    data.add(Arrays.asList(tmcTitle, getHtlStatitical(hotelDeptFieldEnum, corp, true)));
                    if (Objects.isNull(scrollDownObj)) {
                        data.add(Arrays.asList(industryTitle, getHtlStatitical(hotelDeptFieldEnum, industry, true)));
                    }
                } else {
                    headers.add(SharkUtils.get("Exceltopname.numberPercentage", lang));
                }
                data.addAll(buttonContent(lang, map, request.getAnalysisObjectEnum()));
                sheet1.setHeaders(headers);
                sheet1.setSheetTitle(getAnalysisSheetName(baseCondition, analysisObjectEnum, lang));
                sheet1.setSheetNum(0);
                sheet1.setData(data);
                chartExcelEntityList.add(sheet1);
            }
        } else if (queryBu == QueryReportBuTypeEnum.train) {
            TrainTopDeptConsumeInfo trainTopDeptConsumeInfo = Optional.ofNullable(response.getTrainTopConsumeInfo()).orElse(new TrainTopDeptConsumeInfo());
            List<OnlineReportTrainTopDeptConsume> markMetricInfoList = Optional.ofNullable(trainTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportTrainTopDeptConsume companySum = Optional.ofNullable(trainTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportTrainTopDeptConsume());
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                ChartExcelEntity sheet1 = new ChartExcelEntity();
                List headers = Lists.newArrayList(getAnalysisObjectName(analysisObjectEnum, lang), SharkUtils.get(trainDeptFieldEnum.getHeaderKey(), lang));
                List data = new ArrayList();
                for (OnlineReportTrainTopDeptConsume deptConsume : markMetricInfoList) {
                    List dataItem = Lists.newArrayList(deptConsume.getDim(), getTrainStatitical(trainDeptFieldEnum, deptConsume, true));
                    if (trainDeptFieldEnum.isSupportPie()) {
                        dataItem.add(getTrainStatiticalPercent(trainDeptFieldEnum, deptConsume, companySum, false) + PERCENT_QUOTE);
                    }
                    data.add(dataItem);
                }
                if (!trainDeptFieldEnum.isSupportPie()) {
                    OnlineReportTrainTopDeptConsume corp = Optional.ofNullable(trainTopDeptConsumeInfo.getCorpConsume()).orElse(new OnlineReportTrainTopDeptConsume());
                    OnlineReportTrainTopDeptConsume industry = Optional.ofNullable(trainTopDeptConsumeInfo.getIndustryConsume()).orElse(new OnlineReportTrainTopDeptConsume());
                    data.add(Arrays.asList(publicTitle, getTrainStatitical(trainDeptFieldEnum, companySum, true)));
                    data.add(Arrays.asList(tmcTitle, getTrainStatitical(trainDeptFieldEnum, corp, true)));
                    if (Objects.isNull(scrollDownObj)) {
                        data.add(Arrays.asList(industryTitle, getTrainStatitical(trainDeptFieldEnum, industry, true)));
                    }
                } else {
                    headers.add(SharkUtils.get("Exceltopname.numberPercentage", lang));
                }
                data.addAll(buttonContent(lang, map, request.getAnalysisObjectEnum()));
                sheet1.setHeaders(headers);
                sheet1.setSheetTitle(getAnalysisSheetName(baseCondition, analysisObjectEnum, lang));
                sheet1.setSheetNum(0);
                sheet1.setData(data);
                chartExcelEntityList.add(sheet1);
            }
        } else if (queryBu == QueryReportBuTypeEnum.car) {
            CarTopDeptConsumeInfo carTopDeptConsumeInfo = Optional.ofNullable(response.getCarTopConsumeInfo()).orElse(new CarTopDeptConsumeInfo());
            List<OnlineReportCarTopDeptConsume> markMetricInfoList = Optional.ofNullable(carTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportCarTopDeptConsume companySum = Optional.ofNullable(carTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportCarTopDeptConsume());
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                ChartExcelEntity sheet1 = new ChartExcelEntity();
                List headers = Lists.newArrayList(getAnalysisObjectName(analysisObjectEnum, lang), SharkUtils.get(carDeptFieldEnum.getHeaderKey(), lang));
                List data = new ArrayList();
                for (OnlineReportCarTopDeptConsume deptConsume : markMetricInfoList) {
                    List dataItem = Lists.newArrayList(deptConsume.getDim(), getCarStatitical(carDeptFieldEnum, deptConsume));
                    if (carDeptFieldEnum.isSupportPie()) {
                        dataItem.add(getCarStatiticalPercent(carDeptFieldEnum, deptConsume, companySum, false) + PERCENT_QUOTE);
                    }
                    data.add(dataItem);
                }
                if (!carDeptFieldEnum.isSupportPie()) {
                    OnlineReportCarTopDeptConsume corp = Optional.ofNullable(carTopDeptConsumeInfo.getCorpConsume()).orElse(new OnlineReportCarTopDeptConsume());
                    OnlineReportCarTopDeptConsume industry = Optional.ofNullable(carTopDeptConsumeInfo.getIndustryConsume()).orElse(new OnlineReportCarTopDeptConsume());
                    data.add(Arrays.asList(publicTitle, getCarStatitical(carDeptFieldEnum, companySum)));
                    data.add(Arrays.asList(tmcTitle, getCarStatitical(carDeptFieldEnum, corp)));
                    if (Objects.isNull(scrollDownObj)) {
                        data.add(Arrays.asList(industryTitle, getCarStatitical(carDeptFieldEnum, industry)));
                    }
                } else {
                    headers.add(SharkUtils.get("Exceltopname.numberPercentage", lang));
                }
                data.addAll(buttonContent(lang, map, request.getAnalysisObjectEnum()));
                sheet1.setHeaders(headers);
                sheet1.setSheetTitle(getAnalysisSheetName(baseCondition, analysisObjectEnum, lang));
                sheet1.setSheetNum(0);
                sheet1.setData(data);
                chartExcelEntityList.add(sheet1);
            }
        } else if (queryBu == QueryReportBuTypeEnum.welfare) {
            WelfareTopDeptConsumeInfo welfareTopDeptConsumeInfo = Optional.ofNullable(response.getWelfareTopConsumeInfo()).orElse(new WelfareTopDeptConsumeInfo());
            List<OnlineReportWelfareTopDeptConsume> markMetricInfoList = Optional.ofNullable(welfareTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportWelfareTopDeptConsume companySum =
                    Optional.ofNullable(welfareTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportWelfareTopDeptConsume());
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                ChartExcelEntity sheet1 = new ChartExcelEntity();
                List headers = Lists.newArrayList(getAnalysisObjectName(analysisObjectEnum, lang), SharkUtils.get(welfareDeptFieldEnum.getHeaderKey(), lang));
                List data = new ArrayList();
                for (OnlineReportWelfareTopDeptConsume deptConsume : markMetricInfoList) {
                    List dataItem = Lists.newArrayList(deptConsume.getDim(), getWelfareStatistical(welfareDeptFieldEnum, deptConsume));
                    if (welfareDeptFieldEnum.isSupportPie()) {
                        dataItem.add(getWelfareStatisticalPercent(welfareDeptFieldEnum, deptConsume, companySum, false) + PERCENT_QUOTE);
                    }
                    data.add(dataItem);
                }
                if (!welfareDeptFieldEnum.isSupportPie()) {
                    data.add(Arrays.asList(publicTitle, getWelfareStatistical(welfareDeptFieldEnum, companySum)));
                } else {
                    headers.add(SharkUtils.get("Exceltopname.numberPercentage", lang));
                }
                data.addAll(buttonContent(lang, map, request.getAnalysisObjectEnum()));
                sheet1.setHeaders(headers);
                sheet1.setSheetTitle(getAnalysisSheetName(baseCondition, analysisObjectEnum, lang));
                sheet1.setSheetNum(0);
                sheet1.setData(data);
                chartExcelEntityList.add(sheet1);
            }
        } else if (queryBu == QueryReportBuTypeEnum.overview) {
            OverviewTopDeptConsumeInfo overviewTopDeptConsumeInfo = Optional.ofNullable(response.getOverviewTopConsumeInfo()).orElse(new OverviewTopDeptConsumeInfo());
            List<OnlineReportOverviewTopDeptConsume> markMetricInfoList = Optional.ofNullable(overviewTopDeptConsumeInfo.getTopList()).orElse(new ArrayList<>());
            OnlineReportOverviewTopDeptConsume companySum = Optional.ofNullable(overviewTopDeptConsumeInfo.getSumConsume()).orElse(new OnlineReportOverviewTopDeptConsume());
            if (CollectionUtils.isNotEmpty(markMetricInfoList)) {
                ChartExcelEntity sheet1 = new ChartExcelEntity();
                List headers = Lists.newArrayList(getAnalysisObjectName(analysisObjectEnum, lang), SharkUtils.get(overViewDeptFieldEnum.getHeaderKey(), lang));
                List data = new ArrayList();
                for (OnlineReportOverviewTopDeptConsume deptConsume : markMetricInfoList) {
                    List dataItem = Lists.newArrayList(deptConsume.getDim(), getOverviewStatitical(overViewDeptFieldEnum, deptConsume, true));
                    if (overViewDeptFieldEnum.isSupportPie()) {
                        dataItem.add(getOverviewStatiticalPercent(overViewDeptFieldEnum, deptConsume, companySum, false) + PERCENT_QUOTE);
                    }
                    data.add(dataItem);
                }
                if (!overViewDeptFieldEnum.isSupportPie()) {
                    OnlineReportOverviewTopDeptConsume corp = Optional.ofNullable(overviewTopDeptConsumeInfo.getCorpConsume()).orElse(new OnlineReportOverviewTopDeptConsume());
                    OnlineReportOverviewTopDeptConsume industry = Optional.ofNullable(overviewTopDeptConsumeInfo.getIndustryConsume()).orElse(new OnlineReportOverviewTopDeptConsume());
                    data.add(Arrays.asList(publicTitle, getOverviewStatitical(overViewDeptFieldEnum, companySum, true)));
                    data.add(Arrays.asList(tmcTitle, getOverviewStatitical(overViewDeptFieldEnum, corp, true)));
                    if (Objects.isNull(scrollDownObj)) {
                        data.add(Arrays.asList(industryTitle, getOverviewStatitical(overViewDeptFieldEnum, industry, true)));
                    }
                } else {
                    headers.add(SharkUtils.get("Exceltopname.numberPercentage", lang));
                }
                data.addAll(buttonContent(lang, map, request.getAnalysisObjectEnum()));
                sheet1.setHeaders(headers);
                sheet1.setSheetTitle(getAnalysisSheetName(baseCondition, analysisObjectEnum, lang));
                sheet1.setSheetNum(0);
                sheet1.setData(data);
                chartExcelEntityList.add(sheet1);
            }
        }
        return chartExcelEntityList;
    }

    /**
     * 部门明细分析
     *
     * @param request
     * @param lang
     * @return
     */
    public List<ChartExcelEntity> getDeptDetailAnalysisExcel(OnlineReportDeptDetailAnalysisRequest request, String lang) {
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        Map map = getConditionContent(request.getBasecondition().getUid(), request.getBasecondition(), lang);
        String downloadMode = QConfigUtils.getNullDefaultValue(DEPT_DETAIL_DOWNLOAD_MODE_KEY, DeptDetailDownloadMode.SINGLE.toString());
        int downloadLimt = QConfigUtils.getNullDefaultInterValue(DEPT_DETAIL_DOWNLOAD_LIMIT_KEY, DEPT_DETAIL_DOWNLOAD_LIMIT_DEFAULT);
        Pager pager = new Pager(0l, downloadLimt, 0, 0);
        request.setPage(pager);
        if (StringUtils.equalsIgnoreCase(DeptDetailDownloadMode.SINGLE.toString(), downloadMode)) {
            List<Integer> queryBuList = getSubQueruBuList(request.getSubQueryBu());
            if (CollectionUtils.isNotEmpty(queryBuList)) {
                for (Integer i : queryBuList) {
                    request.setSubQueryBu(i);
                    chartExcelEntityList.add(getSubBusheet(request, lang, map, queryBuList.indexOf(i)));
                }
            }
        } else {
            for (int j = 0; j < 24; j++) {
                request.setSubQueryBu(j);

                chartExcelEntityList.add(getSubBusheet(request, lang, map, j));
            }
        }
        return chartExcelEntityList;
    }

    public List<ChartExcelEntity> getDeptUidDetailAnalysisExcel(OnlineReportDeptUidConsumeDetailRequest request, String lang) {
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        Map map = getConditionContent(request.getBasecondition().getUid(), request.getBasecondition(), lang);
        int downloadLimt = QConfigUtils.getNullDefaultInterValue(DEPT_DETAIL_DOWNLOAD_LIMIT_KEY, DEPT_DETAIL_DOWNLOAD_LIMIT_DEFAULT);
        Pager pager = new Pager(0l, downloadLimt, 0, 0);
        request.setPage(pager);
        chartExcelEntityList.add(getUidSubBusheet(request, lang, map, 0));
        return chartExcelEntityList;
    }

    private ChartExcelEntity getSubBusheet(OnlineReportDeptDetailAnalysisRequest request, String lang, Map map, int sheetNum) {
        OnlineReportDeptDetailAnalysisResponse responseType = getDeptDetailAnalysisResponse(request);
        ChartExcelEntity sheet = new ChartExcelEntity();
        if (responseType != null && responseType.getResponseCode() == 20000) {
            List<String> deptCousumeList = responseType.getDeptDetail();
            List<HeaderKeyValMap> headerMap = responseType.getHeaderData();
            List<List<String>> data = new ArrayList();
            List headerDesc = headerMap.stream().map(i -> i.getHeaderValue()).collect(Collectors.toList());
            for (String detail : deptCousumeList) {
                Map temp = OrpGsonUtils.fromToJsonTypeTest(detail, Map.class);
                List list = new ArrayList();
                for (HeaderKeyValMap valMap : headerMap) {
                    list.add(temp.get(valMap.getHeaderKey()));
                }
                data.add(list);
            }
            sheet.setHeaders(headerDesc);
            data.addAll(buttonContent(lang, map, request.getAnalysisObjectEnum()));
            sheet.setData(data);
            sheet.setSheetNum(sheetNum);
            sheet.setSheetTitle(getDeptDetailExcelSheetName(request.getSubQueryBu(), lang));
        }
        return sheet;
    }

    private ChartExcelEntity getUidSubBusheet(OnlineReportDeptUidConsumeDetailRequest request, String lang, Map map, int sheetNum) {
        return new ChartExcelEntity();
        /*QueryReportBuTypeEnum queryReportBuTypeEnum = request.getQueryBu();
        OnlineReportDeptUidConsumeDetailResponse responseType = corpOnlineReportPlatformService.queryDeptUidConsumeDetail(request);
        ChartExcelEntity sheet = new ChartExcelEntity();
        List dataList = new ArrayList();
        if (responseType != null && responseType.getResponseCode() == 20000) {
            OnlineReportDeptUidConsumeDetailInfo uidConsumeDetailInfo = Optional.ofNullable(responseType.getDeptUidConsumeDetailInfo()).orElse(new OnlineReportDeptUidConsumeDetailInfo());
            List headers = Lists.newArrayList(SharkUtils.get("Exceltopname.uidnumber", lang), SharkUtils.get("Exceltopname.cardholder", lang));
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.flight.name())) {
                List<OnlineReportFltTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getFltList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.flight.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    headers.add(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), lang));
                }
                for (OnlineReportFltTopDeptConsume consume : consumeList) {
                    List dataItem = Lists.newArrayList(consume.getDimId(), consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        dataItem.add(getFlightUidStatisticalsVal(deptUidStatisticalsEnum, consume, false));
                    }
                    dataList.add(dataItem);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.hotel.name())) {
                List<OnlineReportHtlTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getHtlList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.hotel.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    headers.add(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), lang));
                }
                for (OnlineReportHtlTopDeptConsume consume : consumeList) {
                    List dataItem = Lists.newArrayList(consume.getDimId(), consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        dataItem.add(getHotelUidStatisticalsVal(deptUidStatisticalsEnum, consume, false));
                    }
                    dataList.add(dataItem);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.train.name())) {
                List<OnlineReportTrainTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getTrainList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.train.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    headers.add(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), lang));
                }
                for (OnlineReportTrainTopDeptConsume consume : consumeList) {
                    List dataItem = Lists.newArrayList(consume.getDimId(), consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        dataItem.add(getTrainUidStatisticalsVal(deptUidStatisticalsEnum, consume, false));
                    }
                    dataList.add(dataItem);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.car.name())) {
                List<OnlineReportCarTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getCarList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.car.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    headers.add(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), lang));
                }
                for (OnlineReportCarTopDeptConsume consume : consumeList) {
                    List dataItem = Lists.newArrayList(consume.getDimId(), consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        dataItem.add(getCarUidStatisticalsVal(deptUidStatisticalsEnum, consume, false));
                    }
                    dataList.add(dataItem);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.bus.name())) {
                List<OnlineReportBusTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getBusList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.bus.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    headers.add(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), lang));
                }
                for (OnlineReportBusTopDeptConsume consume : consumeList) {
                    List dataItem = Lists.newArrayList(consume.getDimId(), consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        dataItem.add(getBusUidStatisticalsVal(deptUidStatisticalsEnum, consume, false));
                    }
                    dataList.add(dataItem);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.vaso.name())) {
                List<OnlineReportVasoTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getVasoList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.vaso.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    headers.add(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), lang));
                }
                for (OnlineReportVasoTopDeptConsume consume : consumeList) {
                    List dataItem = Lists.newArrayList(consume.getDimId(), consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        dataItem.add(getVasoUidStatisticalsVal(deptUidStatisticalsEnum, consume, false));
                    }
                    dataList.add(dataItem);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.welfare.name())) {
                List<OnlineReportWelfareTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getWelfareList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.welfare.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    headers.add(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), lang));
                }
                for (OnlineReportWelfareTopDeptConsume consume : consumeList) {
                    List dataItem = Lists.newArrayList(consume.getDimId(), consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        dataItem.add(getWelfareUidStatisticalVal(deptUidStatisticalsEnum, consume, false));
                    }
                    dataList.add(dataItem);
                }
            }
            if (StringUtils.equalsIgnoreCase(queryReportBuTypeEnum.toString(), QueryReportBuTypeEnum.overview.name())) {
                List<OnlineReportOverviewTopDeptConsume> consumeList = Optional.ofNullable(uidConsumeDetailInfo.getOverViewList()).orElse(Lists.newArrayList());
                List<DeptUidStatisticalsEnum> list = DeptUidStatisticalsEnum.getUidStatisticals(QueryReportBuTypeEnum.overview.name());
                for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                    headers.add(SharkUtils.get(deptUidStatisticalsEnum.getSharkKey(), lang));
                }
                for (OnlineReportOverviewTopDeptConsume consume : consumeList) {
                    List dataItem = Lists.newArrayList(consume.getDimId(), consume.getDim());
                    for (DeptUidStatisticalsEnum deptUidStatisticalsEnum : list) {
                        dataItem.add(getOverviewUidStatisticalsVal(deptUidStatisticalsEnum, consume, false));
                    }
                    dataList.add(dataItem);
                }
            }
            sheet.setHeaders(headers);
            dataList.addAll(buttonContent(lang, map, AnalysisObjectEnum.UID));
            sheet.setData(dataList);
            sheet.setSheetNum(sheetNum);
            sheet.setSheetTitle(getDeptUidDetailExcelSheetName(request.getQueryBu(), lang));
        }
        return sheet;*/
    }


    /**
     * 是否需要添加百分号
     *
     * @param analysisTypeEnum
     * @return
     */
    private boolean isNeedPercentQuote(AnalysisTypeEnum analysisTypeEnum) {
        boolean isNeed = false;
        switch (analysisTypeEnum) {
            case FLT_EXCEEDING_STANDARD:
            case HTL_EXCEEDING_STANDARD:
                isNeed = true;
                break;
            case FLT_AVG_PRICE_MILEAGE:
            case HTL_AVG_PRICE_ROOM_NIGHT:
                isNeed = false;
                break;
        }
        return isNeed;
    }


    /**
     * 前5部门消费金额
     *
     * @param reportConsumeRequest
     * @return
     */
    private OnlineReportTopFiveDeptConsumeResponse getTopFiveDeptConsumeResponse(OnlineReportTopFiveDeptConsumeRequest reportConsumeRequest) {
        OnlineReportTopFiveDeptConsumeResponse responseType = null;
        try {
            responseType = corpOnlineReportPlatformService.queryTopFiveDeptConsume(reportConsumeRequest);
        } catch (Exception e) {
            log.error(LOG_TITLE, e);
        }
        return responseType;
    }

    /**
     * 前5部门其他指标分析
     *
     * @param reportConsumeRequest
     * @return
     */
    private OnlineReportTopFiveDeptAnalysisResponse getTopFiveDeptAnalysisResponse(OnlineReportTopFiveDeptAnalysisRequest reportConsumeRequest) {
        OnlineReportTopFiveDeptAnalysisResponse responseType = null;
        try {
            responseType = corpOnlineReportPlatformService.queryTopFiveDeptAnalysis(reportConsumeRequest);
        } catch (Exception e) {
            log.error(LOG_TITLE, e);
        }
        return responseType;
    }

    /**
     * 前5部门明细分析
     *
     * @param reportConsumeRequest
     * @return
     */
    private OnlineReportDeptDetailAnalysisResponse getDeptDetailAnalysisResponse(OnlineReportDeptDetailAnalysisRequest reportConsumeRequest) {
        OnlineReportDeptDetailAnalysisResponse responseType = null;
        try {
            responseType = corpOnlineReportPlatformService.queryDeptDetailAnalysis(reportConsumeRequest);

        } catch (Exception e) {
            log.error(LOG_TITLE, e);
        }
        return responseType;
    }

    /**
     * 分析对象
     *
     * @param lang
     * @param analysisObjectEnum
     * @return
     */
    protected List getAnalysisObjectContent(String lang, AnalysisObjectEnum analysisObjectEnum) {
        return Arrays.asList(Arrays.asList(SharkUtils.get("Index.AnalyseObject", lang), getAnalysisObject(analysisObjectEnum, lang)));
    }

    /**
     * 获取excel name
     *
     * @param analysisObjectEnum
     * @param lang
     * @return
     */
    private String getDeptConsumeSheetName(AnalysisObjectEnum analysisObjectEnum, String lang) {
        String sheetName = "sheet1";
//        switch (analysisTypeEnum) {
//            case FLT_EXCEEDING_STANDARD:
//                sheetName = SharkUtils.get("DeptAnalysis.DeptFLTRC1", lang);
//                break;
//            case HTL_EXCEEDING_STANDARD:
//                sheetName = SharkUtils.get("HTL_EXCEEDING_STANDARD", lang);
//                break;
//            case FLT_AVG_PRICE_MILEAGE:
//                sheetName = SharkUtils.get("DeptAnalysis.DeptFltPriceTop5", lang);
//                break;
//            case HTL_AVG_PRICE_ROOM_NIGHT:
//                sheetName = SharkUtils.get("DeptAnalysis.DeptHtlPriceTop5", lang);
//                break;
//        }
        return sheetName;
    }

    /**
     * 获取excel name
     *
     * @param analysisTypeEnum
     * @param lang
     * @return
     */
    private String getOtherAnalysisSheetName(AnalysisTypeEnum analysisTypeEnum, String lang) {
        String sheetName = "sheet1";
//        switch (analysisTypeEnum) {
//            case FLT_EXCEEDING_STANDARD:
//                sheetName = SharkUtils.get("DeptAnalysis.DeptFLTRC1", lang);
//                break;
//            case HTL_EXCEEDING_STANDARD:
//                sheetName = SharkUtils.get("HTL_EXCEEDING_STANDARD", lang);
//                break;
//            case FLT_AVG_PRICE_MILEAGE:
//                sheetName = SharkUtils.get("DeptAnalysis.DeptFltPriceTop5", lang);
//                break;
//            case HTL_AVG_PRICE_ROOM_NIGHT:
//                sheetName = SharkUtils.get("DeptAnalysis.DeptHtlPriceTop5", lang);
//                break;
//        }
        return sheetName;
    }

    private String getAnalysisSheetName(BaseQueryConditionBO baseCondition, AnalysisObjectEnum analysisObjectEnum, String lang) {
        String sheetName = "sheet1";
        String indicator = baseCondition.getIndicator();
        FlightDeptFieldEnum flightDeptFieldEnum = FlightDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        HotelDeptFieldEnum hotelDeptFieldEnum = HotelDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        TrainDeptFieldEnum trainDeptFieldEnum = TrainDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        CarDeptFieldEnum carDeptFieldEnum = CarDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        WelfareDeptFieldEnum welfareDeptFieldEnum = WelfareDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        OverViewDeptFieldEnum overViewDeptFieldEnum = OverViewDeptFieldEnum.getEnumByName(StringUtils.trimToEmpty(indicator));
        if (flightDeptFieldEnum != null) {
            sheetName = SharkUtils.get(flightDeptFieldEnum.getHeaderKey(), lang);
        } else if (hotelDeptFieldEnum != null) {
            sheetName = SharkUtils.get(hotelDeptFieldEnum.getHeaderKey(), lang);
        } else if (trainDeptFieldEnum != null) {
            sheetName = SharkUtils.get(trainDeptFieldEnum.getHeaderKey(), lang);
        } else if (carDeptFieldEnum != null) {
            sheetName = SharkUtils.get(carDeptFieldEnum.getHeaderKey(), lang);
        } else if (welfareDeptFieldEnum != null) {
            sheetName = SharkUtils.get(welfareDeptFieldEnum.getHeaderKey(), lang);
        } else if (overViewDeptFieldEnum != null) {
            sheetName = SharkUtils.get(overViewDeptFieldEnum.getHeaderKey(), lang);
        }
        return sheetName;
    }

    /**
     * 数据标题
     *
     * @param analysisTypeEnum
     * @param lang
     * @return
     */
    private String getOtherAnalysisExcelTitle(AnalysisTypeEnum analysisTypeEnum, String lang) {
        String title = "";
        switch (analysisTypeEnum) {
            case FLT_EXCEEDING_STANDARD:
            case HTL_EXCEEDING_STANDARD:
                title = SharkUtils.get("Exceltopname.RCPercentage", lang);
                break;
            case FLT_AVG_PRICE_MILEAGE:
                title = SharkUtils.get("Exceltopname.avgmilprice", lang);
                break;
            case HTL_AVG_PRICE_ROOM_NIGHT:
                title = SharkUtils.get("Travelanalysis.averagehotel", lang);
                break;
        }
        return title;
    }

    /**
     * 获取部门分析明细excel的显示的分析对象
     *
     * @param analysisObjectEnum
     * @param lang
     * @return
     */
    private String getAnalysisObject(AnalysisObjectEnum analysisObjectEnum, String lang) {
        String result = StringUtils.EMPTY;
        switch (analysisObjectEnum) {
            case CORP:
                result = SharkUtils.get("Index.public", lang);
                break;
            case ACCOUNT:
                result = SharkUtils.get("Public.account", lang);
                break;
            case ACCOUNTCODE:
                result = SharkUtils.get("Exceltopname.accountname", lang);
                break;
            case DEPT1:
                result = SharkUtils.get("Exceltopname.depone", lang);
                break;
            case DEPT2:
                result = SharkUtils.get("Exceltopname.deptwo", lang);
                break;
            case DEPT3:
                result = SharkUtils.get("Exceltopname.depthree", lang);
                break;
            case DEPT4:
                result = SharkUtils.get("Exceltopname.depfour", lang);
                break;
            case DEPT5:
                result = SharkUtils.get("Exceltopname.depfive", lang);
                break;
            case DEPT6:
                result = SharkUtils.get("Exceltopname.depsix", lang);
                break;
            case DEPT7:
                result = SharkUtils.get("Exceltopname.depseven", lang);
                break;
            case DEPT8:
                result = SharkUtils.get("Exceltopname.depeight", lang);
                break;
            case DEPT9:
                result = SharkUtils.get("Exceltopname.depnight", lang);
                break;
            case DEPT10:
                result = SharkUtils.get("Exceltopname.depten", lang);
                break;
            case COSTCENTER1:
                result = SharkUtils.get("Exceltopname.costcenterone", lang);
                break;
            case COSTCENTER2:
                result = SharkUtils.get("Exceltopname.costcentertwo", lang);
                break;
            case COSTCENTER3:
                result = SharkUtils.get("Exceltopname.costcenterthree", lang);
                break;
            case COSTCENTER4:
                result = SharkUtils.get("Exceltopname.costcenterfour", lang);
                break;
            case COSTCENTER5:
                result = SharkUtils.get("Exceltopname.costcenterfive", lang);
                break;
            case COSTCENTER6:
                result = SharkUtils.get("Exceltopname.costcentersix", lang);
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 获取部门分析明细excel的sheetName
     *
     * @param subQueryBu
     * @param lang
     * @return
     */
    private String getDeptDetailExcelSheetName(int subQueryBu, String lang) {
        String result = GlobalConst.STRING_EMPTY;
        switch (subQueryBu) {
            case 0:
                result = SharkUtils.get("Exceltopname.byall", lang);
                break;
            case 1:
                result = SharkUtils.get("Index.air", lang);
                break;
            case 2:
                result = SharkUtils.get("Index.domair", lang);
                break;
            case 3:
                result = SharkUtils.get("Index.interair", lang);
                break;
            case 4:
                result = SharkUtils.get("Agreement.Rate.Flight", lang);
                break;
            case 5:
                result = SharkUtils.get("Non-agreement.Rate.Flight", lang);
                break;
            case 6:
                result = SharkUtils.get("Index.hotel", lang);
                break;
            case 7:
                result = SharkUtils.get("Exceltopname.hotelinmainlandchina", lang);
                break;
            case 8:
                result = SharkUtils.get("Exceltopname.hotelinoverseas", lang);
                break;
            case 9:
                result = SharkUtils.get("Supplier.pacthotel", lang);
                break;
            case 10:
                result = SharkUtils.get("Supplier.memberhotel", lang);
                break;
            case 11:
                result = SharkUtils.get("Index.train", lang);
                break;
            case 12:
                result = SharkUtils.get("Index.car", lang);
                break;
            case 13:
                result = SharkUtils.get("index.domtaxi", lang);
                break;
            case 14:
                result = SharkUtils.get("Index.domairportpick", lang);
                break;
            case 15:
                result = SharkUtils.get("Index.rentcar", lang);
                break;
            case 16:
                result = SharkUtils.get("Index.CharteredCar", lang);
                break;
            case 17:
                result = SharkUtils.get("Index.intetrairportpick", lang);
                break;
            case 18:
                result = SharkUtils.get("App.Realtime.CarProduct1", lang);
                break;
            case 19:
                result = SharkUtils.get("Overview.VAS", lang);
                break;
            case 20:
                result = SharkUtils.get("index.inttaxi", lang);
                break;
            case 21:
                result = SharkUtils.get("Exceltopname.intcarrental", lang);
                break;
            case 22:
                result = SharkUtils.get("Index.domtrain", lang);
                break;
            case 23:
                result = SharkUtils.get("Index.intertrain", lang);
                break;
            case 24:
                result = SharkUtils.get("Dept.Welfare", lang);
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 获取部门分析明细excel的sheetName
     *
     * @param queryReportBuTypeEnum
     * @param lang
     * @return
     */
    private String getDeptUidDetailExcelSheetName(QueryReportBuTypeEnum queryReportBuTypeEnum, String lang) {
        String result = GlobalConst.STRING_EMPTY;
        switch (queryReportBuTypeEnum) {
            case flight:
                result = SharkUtils.get("Index.air", lang);
                break;
            case hotel:
                result = SharkUtils.get("Index.hotel", lang);
                break;
            case train:
                result = SharkUtils.get("Index.train", lang);
                break;
            case car:
                result = SharkUtils.get("Index.car", lang);
                break;
            case bus:
                result = SharkUtils.get("Index.bus", lang);
                break;
            case vaso:
                result = SharkUtils.get("Overview.VAS", lang);
                break;
            case welfare:
                result = SharkUtils.get("Dept.Welfare", lang);
                break;
            case overview:
                result = SharkUtils.get("Index.public", lang);
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 获取需要查询的产线
     *
     * @param subQueryBu
     * @return
     */
    private List<Integer> getSubQueruBuList(int subQueryBu) {
        List<Integer> result = null;
        switch (subQueryBu) {
            case 0:
                result = Arrays.asList(0);
                break;
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
                result = Arrays.asList(1, 2, 3, 4, 5);
                break;
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
                result = Arrays.asList(6, 7, 8, 9, 10);
                break;
            case 11:
            case 22:
            case 23:
                result = Arrays.asList(11, 22, 23);
                break;
            case 12:
            case 13:
            case 14:
            case 15:
            case 16:
            case 17:
            case 20:
                result = Arrays.asList(12, 13, 14, 15, 16, 17, 20);
                break;
            case 18:
                result = Arrays.asList(18);
                break;
            case 19:
                result = Arrays.asList(19);
                break;
            case 24:
                result = Arrays.asList(24);
            default:
                break;
        }
        return result;
    }

    private Object getOverviewUidStatisticalsVal(DeptUidStatisticalsEnum deptUidStatisticalsEnum, OnlineReportOverviewTopDeptConsume consume, Boolean needMicrometer) {
        Object result = null;
        switch (deptUidStatisticalsEnum) {
            case TOTAL_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalAmount()) : consume.getTotalAmount();
                break;
            case AMOUNT_PERCENT:
                result = DigitBaseUtils.multiply(consume.getAmountPercent(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case TOTAL_ORDER_COUNT:
                result = consume.getTotalAllOrderCount();
                break;
            case TOTAL_SAVE_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalSaveAmount()) : consume.getTotalSaveAmount();
                break;
            case TOTAL_SAVE_POTENTIAL:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalSavePotential()) : consume.getTotalSavePotential();
                break;
            case TOP_DESTINATION:
                result = consume.getTopDestination();
                break;
            default:
                break;
        }
        return result;
    }

    private Object getFlightUidStatisticalsVal(DeptUidStatisticalsEnum deptUidStatisticalsEnum, OnlineReportFltTopDeptConsume consume, Boolean needMicrometer) {
        Object result = null;
        switch (deptUidStatisticalsEnum) {
            case FLT_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalAmount()) : consume.getTotalAmount();
                break;
            case FLT_AMOUNT_PERCENT:
                result = DigitBaseUtils.multiply(consume.getAmountPercent(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case FLT_ORDER_COUNT:
                result = consume.getTotalAllOrderCount();
                break;
            case FLT_SAVE_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalSaveAmount()) : consume.getTotalSaveAmount();
                break;
            case FLT_SAVE_POTENTIAL:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalSavePotential()) : consume.getTotalSavePotential();
                break;
            case FLT_TOP_FLIGHT_CITY:
                result = consume.getTopFlightCity();
                break;
            case FLT_AVG_PRE_ORDER_DATE:
                result = consume.getAvgPreOrderDate();
                break;
            case FLT_AVG_DISCOUNT:
                result = consume.getAvgDiscount();
                break;
            case FLT_AVG_MILE_PRICE:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getAvgTpmsPrice()) : consume.getAvgTpmsPrice();
                break;
            case FLT_AVG_PRICE:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getAvgPrice()) : consume.getAvgPrice();
                break;
/*            case FLT_CARBONS:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalCarbons()) : consume.getTotalCarbons();
                break;
            case FLT_AVG_MILE_CARBONS:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalAmount()) : consume.getTotalAmount();
                break;*/
            case FLT_RC_TIMES:
                result = consume.getTotalRcTimes();
                break;
            case FLT_RC_PERCENT:
                result = DigitBaseUtils.multiply(consume.getRcPercent(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case FLT_REFUND_RATE:
                result = DigitBaseUtils.multiply(consume.getRefundRate(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case FLT_REBOOK_RATE:
                result = DigitBaseUtils.multiply(consume.getRebookRate(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            default:
                break;
        }
        return result;
    }

    private Object getHotelUidStatisticalsVal(DeptUidStatisticalsEnum deptUidStatisticalsEnum, OnlineReportHtlTopDeptConsume consume, Boolean needMicrometer) {
        Object result = null;
        switch (deptUidStatisticalsEnum) {
            case HTL_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalAmount()) : consume.getTotalAmount();
                break;
            case HTL_AMOUNT_PERCENT:
                result = DigitBaseUtils.multiply(consume.getAmountPercent(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case HTL_ORDER_COUNT:
                result = consume.getTotalAllOrderCount();
                break;
            case HTL_QUANTITY:
                result = consume.getTotalQuantity();
                break;
            case HTL_SAVE_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalSaveAmount()) : consume.getTotalSaveAmount();
                break;
            case HTL_SAVE_POTENTIAL:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalSavePotential()) : consume.getTotalSavePotential();
                break;
            case HTL_TOP_CITY:
                result = consume.getTopCity();
                break;
            case HTL_TOP_STAR:
                result = consume.getTopStar();
                break;
            case HTL_USAGE:
                result = consume.getUsage().toString().concat(PERCENT_QUOTE);
                break;
            case HTL_AVG_PRICE:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getAvgPrice()) : consume.getAvgPrice();
                break;
            case HTL_ROOM_PRICE:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getRoomPrice()) : consume.getRoomPrice();
                break;
            case HTL_RC_TIMES:
                result = consume.getRcTimes();
                break;
            case HTL_RC_PERCENT:
                result = DigitBaseUtils.multiply(consume.getRcPercent(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case HTL_CANCEL_RATE:
                result = DigitBaseUtils.multiply(consume.getCancelRate(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            default:
                break;
        }
        return result;
    }

    private Object getTrainUidStatisticalsVal(DeptUidStatisticalsEnum deptUidStatisticalsEnum, OnlineReportTrainTopDeptConsume consume, Boolean needMicrometer) {
        Object result = null;
        switch (deptUidStatisticalsEnum) {
            case TRAIN_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalAmount()) : consume.getTotalAmount();
                break;
            case TRAIN_AMOUNT_PERCENT:
                result = DigitBaseUtils.multiply(consume.getAmountPercent(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case TRAIN_ORDER_COUNT:
                result = consume.getTotalAllOrderCount();
                break;
            case TRAIN_QUANTITY:
                result = consume.getTotalQuantity();
                break;
            case TRAIN_TOP_LINE:
                result = consume.getTopLine();
                break;
            case TRAIN_TOP_SEAT:
                result = consume.getTopSeat();
                break;
            // case TRAIN_CARBONS: - Removed carbon emission field
            //     result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalCarbons()) : consume.getTotalCarbons();
            //     break;
            case TRAIN_REFUND_RATE:
                result = DigitBaseUtils.multiply(consume.getRefundRate(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case TRAIN_REBOOK_RATE:
                result = DigitBaseUtils.multiply(consume.getRebookRate(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            default:
                break;
        }
        return result;
    }

    private Object getCarUidStatisticalsVal(DeptUidStatisticalsEnum deptUidStatisticalsEnum, OnlineReportCarTopDeptConsume consume, Boolean needMicrometer) {
        Object result = null;
        switch (deptUidStatisticalsEnum) {
            case CAR_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalAmount()) : consume.getTotalAmount();
                break;
            case CAR_AMOUNT_PERCENT:
                result = DigitBaseUtils.multiply(consume.getAmountPercent(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case CAR_ORDER_COUNT:
                result = consume.getTotalOrderCount();
                break;
            case CAR_AVG_MILE_PRICE:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getAvgTpmsPrice()) : consume.getAvgTpmsPrice();
                break;
            // case CAR_CARBONS: - Removed carbon emission field
            //     result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalCarbons()) : consume.getTotalCarbons();
            //     break;
            // case CAR_AVG_MILE_CARBONS: - Removed carbon emission field
            //     result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getAvgTpmsCarbons()) : consume.getAvgTpmsCarbons();
            //     break;
            default:
                break;
        }
        return result;
    }

    private Object getBusUidStatisticalsVal(DeptUidStatisticalsEnum deptUidStatisticalsEnum, OnlineReportBusTopDeptConsume consume, Boolean needMicrometer) {
        Object result = null;
        switch (deptUidStatisticalsEnum) {
            case BUS_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalAmount()) : consume.getTotalAmount();
                break;
            case BUS_AMOUNT_PERCENT:
                result = DigitBaseUtils.multiply(consume.getAmountPercent(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case BUS_ORDER_COUNT:
                result = consume.getTotalOrderCount();
                break;
            default:
                break;
        }
        return result;
    }

    private Object getVasoUidStatisticalsVal(DeptUidStatisticalsEnum deptUidStatisticalsEnum, OnlineReportVasoTopDeptConsume consume, Boolean needMicrometer) {
        Object result = null;
        switch (deptUidStatisticalsEnum) {
            case VASO_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalAmount()) : consume.getTotalAmount();
                break;
            case VASO_AMOUNT_PERCENT:
                result = DigitBaseUtils.multiply(consume.getAmountPercent(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case VASO_ORDER_COUNT:
                result = consume.getTotalOrderCount();
                break;
            default:
                break;
        }
        return result;
    }

    private Object getWelfareUidStatisticalVal(DeptUidStatisticalsEnum deptUidStatisticalsEnum, OnlineReportWelfareTopDeptConsume consume, Boolean needMicrometer) {
        Object result = null;
        switch (deptUidStatisticalsEnum) {
            case WELFARE_ALLOT_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getAllotAmount()) : consume.getAllotAmount();
                break;
            case WELFARE_ALLOT_AMOUNT_PERCENT:
                result =
                        DigitBaseUtils.multiply(consume.getAllotAmountPercent(), new BigDecimal(100)).setScale(2).toString().concat(PERCENT_QUOTE);
                break;
            case WELFARE_RECYCLE_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getReturnAmount()) : consume.getReturnAmount();
                break;
            case WELFARE_RETURN_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getRefundAmount()) : consume.getRefundAmount();
                break;
            case WELFARE_DEDUCT_AMOUNT:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getDeductAmount()) : consume.getDeductAmount();
                break;
            case WELFARE_BALANCE:
                result = needMicrometer ? CommonUtils.fmtMicrometer(consume.getTotalBalance()) : consume.getTotalBalance();
                break;
            default:
                break;
        }

        return result;
    }

    // SINGLE:每次根据查询条件只下载一个sheet，ALL:每次现在下载所有的sheet
    enum DeptDetailDownloadMode {
        SINGLE, ALL
    }
}
