package com.corpgovernment.resource.schedule.management;

import com.corpgovernment.management.ManagementReq;
import com.corpgovernment.resource.schedule.domain.management.ManagementDailyWeeklyGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ManagementDailyWeeklyService {

    @Resource
    private ManagementDailyWeeklyGateway managementDailyWeeklyGateway;

    public void managementDaily(ManagementReq parse) {
        log.info("managementDaily");

        managementDailyWeeklyGateway.managementDaily(parse);
    }

    public void managementWeekly(ManagementReq req) {
        log.info("managementWeekly");

        managementDailyWeeklyGateway.managementWeekly(req);
    }
}
