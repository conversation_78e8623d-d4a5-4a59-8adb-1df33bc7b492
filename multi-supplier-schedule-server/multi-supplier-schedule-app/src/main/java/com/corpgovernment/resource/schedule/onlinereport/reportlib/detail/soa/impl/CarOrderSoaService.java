package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.soa.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.common.BaseDataUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportCarOrderInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.CarOrderDetailService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.soa.AbstractOrderDetailSoaService;
import onlinereport.enums.CarRentOrderStatusEnum;
import onlinereport.enums.OrderTypeEnum;
import onlinereport.enums.reportlib.CarOrderDetailEnum;
import onlinereport.enums.reportlib.OrderDetailEnumerable;
import onlinereport.enums.reportlib.uid.CarUidOrderDetailEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst.FOUR_DIGIT_NUM;


/**
 * <AUTHOR>
 * @date 2022-10-13 13:58
 * @desc
 */
@Service
public class CarOrderSoaService extends AbstractOrderDetailSoaService {


    @Override
    public OrderDetailEnumerable[] getEnum(String reportType) {
        if (StringUtils.equalsIgnoreCase(reportType, "C_UID_DETAIL")) {
            return CarUidOrderDetailEnum.values();
        } else {
            return CarOrderDetailEnum.values();
        }
    }

    @Override
    public List<Map<String, Object>> convertMapData(OnlineReportOrderDetailInfo detailInfo, String lang, String reportType) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportCarOrderInfo> data = detailInfo.getCarOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        if (StringUtils.equalsIgnoreCase(reportType, "C_UID_DETAIL")) {
            return convertUidMapData(detailInfo, lang);
        } else {
            return convertDetailMapData(detailInfo, lang);
        }
    }

    protected List<Map<String, Object>> convertUidMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportCarOrderInfo> data = detailInfo.getCarOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        for (OnlineReportCarOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(CarUidOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZeroString(order.getOrderId()));
            map.put(CarUidOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrderType(), order.getOrderStatus()));
            map.put(CarUidOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(CarUidOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(CarUidOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
            map.put(CarUidOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getAccountId()));
            map.put(CarUidOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(CarUidOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
            map.put(CarUidOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRankName()));
            map.put(CarUidOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCostCenter1()));
            map.put(CarUidOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCostCenter2()));
            map.put(CarUidOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCostCenter3()));
            map.put(CarUidOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCostCenter4()));
            map.put(CarUidOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCostCenter5()));
            map.put(CarUidOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCostCenter6()));
            map.put(CarUidOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(CarUidOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(CarUidOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(CarUidOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
            map.put(CarUidOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
            map.put(CarUidOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
            map.put(CarUidOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
            map.put(CarUidOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
            map.put(CarUidOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
            map.put(CarUidOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(CarUidOrderDetailEnum.ORDERTYPE.getCode(), convertOrderType(order.getOrderType(), order.getSubProductLine()));
            map.put(CarUidOrderDetailEnum.PASSENGERNAME.getCode(), MapperUtils.trim(order.getPassengerName()));
            map.put(CarUidOrderDetailEnum.PERSONS.getCode(), MapperUtils.convertDigitToString(order.getPersons()));
            map.put(CarUidOrderDetailEnum.DELREALPAY.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));
            map.put(CarUidOrderDetailEnum.DEPARTYRECITYNAME.getCode(), MapperUtils.trim(order.getDepartureCityName()));
            map.put(CarUidOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.trim(order.getArrivalCityName()));
            map.put(CarUidOrderDetailEnum.STARTADDRESSDES.getCode(), MapperUtils.trim(order.getStartAddressDes()));
            map.put(CarUidOrderDetailEnum.ENDADDRESSDES.getCode(), MapperUtils.trim(order.getEndAddressDes()));
            map.put(CarUidOrderDetailEnum.NORMALDISTANCE.getCode(), DigitBaseUtils.formatDigit(order.getNormalDistance()));
            map.put(CarUidOrderDetailEnum.USEDURATION.getCode(), MapperUtils.trim(order.getUseDuration()));
            map.put(CarUidOrderDetailEnum.VEHICLENAME.getCode(), MapperUtils.trim(order.getVehicleName()));
            map.put(CarUidOrderDetailEnum.ACTUALSTARTSERVICETIME.getCode(), MapperUtils.trim(order.getActualStartservicetime()));
            map.put(CarUidOrderDetailEnum.ACTUALDRIVEDURATION.getCode(), MapperUtils.trim(order.getActualDriveduration()));
            map.put(CarUidOrderDetailEnum.BOOKINGTYPE_DES.getCode(), MapperUtils.trim(order.getBookingtypeDes()));
            map.put(CarUidOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(CarUidOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccountCode()));
            map.put(CarUidOrderDetailEnum.BASICFEE.getCode(), DigitBaseUtils.formatDigit(order.getBasicFee()));
            result.add(map);
        }
        return result;
    }

    protected List<Map<String, Object>> convertDetailMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportCarOrderInfo> data = detailInfo.getCarOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        Map yesOrNotMap = BaseDataUtils.getYesOrNoMap(lang);
        for (OnlineReportCarOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(CarOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZeroString(order.getOrderId()));
            map.put(CarOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrderType(), order.getOrderStatus()));
            map.put(CarOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
//            map.put(CarOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(CarOrderDetailEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorpName()));
//            map.put(CarOrderDetailEnum.COMPANYGROUP.getCode(), MapperUtils.trim(order.getCompanygroup()));
//            map.put(CarOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
//            map.put(CarOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getAccountId()));
//            map.put(CarOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccountCode()));
//            map.put(CarOrderDetailEnum.ACCOUNTNAME.getCode(), MapperUtils.trim(order.getAccountName()));
//            map.put(CarOrderDetailEnum.SUBACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getSubAccountId()));
//            map.put(CarOrderDetailEnum.SUBACCOUNTCODE.getCode(), MapperUtils.trim(order.getSubAccountCode()));
//            map.put(CarOrderDetailEnum.SUBACCOUNTNAME.getCode(), MapperUtils.trim(order.getSubAccountName()));
//            map.put(CarOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(CarOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(CarOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
//            map.put(CarOrderDetailEnum.WORKCITY.getCode(), MapperUtils.trim(order.getWorkCity()));
//            map.put(CarOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRankName()));
            map.put(CarOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCostCenter1()));
//            map.put(CarOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCostCenter2()));
//            map.put(CarOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCostCenter3()));
//            map.put(CarOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCostCenter4()));
//            map.put(CarOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCostCenter5()));
//            map.put(CarOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCostCenter6()));
            map.put(CarOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
//            map.put(CarOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
//            map.put(CarOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
//            map.put(CarOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
//            map.put(CarOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
//            map.put(CarOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
//            map.put(CarOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
//            map.put(CarOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
//            map.put(CarOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
//            map.put(CarOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(CarOrderDetailEnum.ISONLINE.getCode(), MapperUtils.convertBookTypeSingle(order.getIsOnline()));
//            map.put(CarOrderDetailEnum.FEETYPE.getCode(), MapperUtils.trim(order.getFeeType()));
            map.put(CarOrderDetailEnum.PREPAYTYPE.getCode(), CarOrderDetailService.convertPrepayType(MapperUtils.trim(order.getPrepayType())));
//            map.put(CarOrderDetailEnum.ACBPREPAYTYPE.getCode(), MapperUtils.trim(order.getAcbPrepayType()));
//            map.put(CarOrderDetailEnum.ISBOSS.getCode(), MapperUtils.trim(order.getBosstype()));
            map.put(CarOrderDetailEnum.JOURNEYNO.getCode(), MapperUtils.trim(order.getJourneyNo()));
            map.put(CarOrderDetailEnum.TRIPID.getCode(), MapperUtils.convertDigitToDash(order.getTripId()));
//            map.put(CarOrderDetailEnum.JOUNARYREASON.getCode(), MapperUtils.trim(order.getJourneyReason()));
//            map.put(CarOrderDetailEnum.PROJECT.getCode(), MapperUtils.trim(order.getProject()));
//            map.put(CarOrderDetailEnum.VERBALAUTHORIZE.getCode(), MapperUtils.convertTorF(order.getVerbalAuthorize(), yesOrNotMap));
//            map.put(CarOrderDetailEnum.CONFIRMPERSON.getCode(), MapperUtils.trim(order.getConfirmPerson()));
//            map.put(CarOrderDetailEnum.CONFIRMPTYPE.getCode(), MapperUtils.trim(order.getConfirmType()));
//            map.put(CarOrderDetailEnum.CONFIRMPERSON2.getCode(), MapperUtils.trim(order.getConfirmPerson2()));
//            map.put(CarOrderDetailEnum.CONFIRMPTYPE2.getCode(), MapperUtils.trim(order.getConfirmType2()));
            map.put(CarOrderDetailEnum.GROUPMONTH.getCode(), MapperUtils.convertDigitToString(order.getGroupMonth()));
//            map.put(CarOrderDetailEnum.USERDEFINEDRID.getCode(), MapperUtils.trim(order.getUserdefinedRid()));
//            map.put(CarOrderDetailEnum.USERDEFINEDRC.getCode(), MapperUtils.trim(order.getUserdefinedRc()));
            /////////////////////////////////////////////////////////////////////////////////////////////////////
            map.put(CarOrderDetailEnum.ORDERTYPE.getCode(), convertOrderType(order.getOrderType(), order.getSubProductLine()));
            map.put(CarOrderDetailEnum.PAYMENTSTATUS.getCode(), CarOrderDetailService.convertPaymentStatus(order.getPaymentStatus()));
            map.put(CarOrderDetailEnum.PASSENGERNAME.getCode(), MapperUtils.trim(order.getPassengerName()));
            map.put(CarOrderDetailEnum.PERSONS.getCode(), MapperUtils.convertDigitToString(order.getPersons()));
            map.put(CarOrderDetailEnum.CONTACTNAME.getCode(), MapperUtils.trim(order.getContactName()));
            map.put(CarOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToString(order.getCntOrder()));
            map.put(CarOrderDetailEnum.ESTIMATEAMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getEstimateAmount()));
            map.put(CarOrderDetailEnum.DELREALPAY.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));
            map.put(CarOrderDetailEnum.BASICFEE.getCode(), DigitBaseUtils.formatDigit(order.getBasicFee()));
            map.put(CarOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getServiceFee()));
//            map.put(CarOrderDetailEnum.REFUNDAMOUNT.getCode(), DigitBaseUtils.formatDigit((order.getRefundAmount())));
//            map.put(CarOrderDetailEnum.OCURRENCY.getCode(), MapperUtils.trim(order.getOCurrency()));
//            map.put(CarOrderDetailEnum.OEXCHANGERATE.getCode(), DigitBaseUtils.formatDigit(order.getOExchangerate(), FOUR_DIGIT_NUM));
            map.put(CarOrderDetailEnum.DEPARTYRECITYNAME.getCode(), MapperUtils.trim(order.getDepartureCityName()));
            map.put(CarOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.trim(order.getArrivalCityName()));
            map.put(CarOrderDetailEnum.ADDRESS.getCode(), MapperUtils.trim(order.getAddress()));
//            map.put(CarOrderDetailEnum.FIXEDLOCATIONNAME.getCode(), MapperUtils.trim(order.getFixedLocationName()));
            map.put(CarOrderDetailEnum.FLIGHTTRAINNUM.getCode(), MapperUtils.trim(order.getFlightTrainNum()));
//            map.put(CarOrderDetailEnum.PATTERNTYPE.getCode(), MapperUtils.convertPatternType(order.getPatternType(), lang));
            map.put(CarOrderDetailEnum.STARTADDRESSDES.getCode(), MapperUtils.trim(order.getStartAddressDes()));
            map.put(CarOrderDetailEnum.TAKETIME.getCode(), MapperUtils.trim(order.getStartTime()));
            map.put(CarOrderDetailEnum.ENDADDRESSDES.getCode(), MapperUtils.trim(order.getEndAddressDes()));
            map.put(CarOrderDetailEnum.ENDTIME.getCode(), MapperUtils.trim(order.getEndTime()));
            map.put(CarOrderDetailEnum.ESTIMATEDISTANCE.getCode(), MapperUtils.convertDigitToString(order.getEstimateDistance()));
            map.put(CarOrderDetailEnum.NORMALDISTANCE.getCode(), DigitBaseUtils.formatDigit(order.getNormalDistance()));
            map.put(CarOrderDetailEnum.DRIVERNAME.getCode(), MapperUtils.trim(order.getDriverName()));

//            map.put(CarOrderDetailEnum.USEDURATION.getCode(), MapperUtils.trim(order.getUseDuration()));
//            map.put(CarOrderDetailEnum.USETYPE.getCode(), MapperUtils.trim(order.getUseType()));
            map.put(CarOrderDetailEnum.VEHICLENAME.getCode(), MapperUtils.trim(order.getVehicleName()));
            map.put(CarOrderDetailEnum.VENDORNAME.getCode(), MapperUtils.trim(order.getVendorName()));
//            map.put(CarOrderDetailEnum.SCENENAME.getCode(), MapperUtils.trim(order.getScenename()));
//            map.put(CarOrderDetailEnum.WORKCITY.getCode(), MapperUtils.trim(order.getWorkCity()));
//            map.put(CarOrderDetailEnum.VENDORORDERID.getCode(), MapperUtils.trim(order.getVendorId()));
            map.put(CarOrderDetailEnum.ACTUALSTARTSERVICETIME.getCode(), MapperUtils.trim(order.getActualStartservicetime()));
            map.put(CarOrderDetailEnum.ACTUALDRIVEDURATION.getCode(), MapperUtils.trim(order.getActualDriveduration()));
//            map.put(CarOrderDetailEnum.CANCELFEERATE.getCode(), DigitBaseUtils.formatDigit(order.getCancelfeeRate()));
//            map.put(CarOrderDetailEnum.PACKAGENAME.getCode(), MapperUtils.trim(order.getPackageName()));
            map.put(CarOrderDetailEnum.ACTUALSTARTADRESS.getCode(), MapperUtils.trim(order.getActualStartAddress()));
            map.put(CarOrderDetailEnum.ACTUALENDADRESS.getCode(), MapperUtils.trim(order.getActualEndAddress()));
//            map.put(CarOrderDetailEnum.AUDITORID.getCode(), MapperUtils.trim(order.getAuditorid()));
//            map.put(CarOrderDetailEnum.AUDITORID2.getCode(), MapperUtils.trim(order.getAuditorid2()));
//            map.put(CarOrderDetailEnum.ISABNORMAL.getCode(), MapperUtils.convertTorF(order.getIsAbnormal(), yesOrNotMap));
//            map.put(CarOrderDetailEnum.ISABNORMALCONFIRM.getCode(), MapperUtils.convertTorF(order.getIsAbnormalUserconfirm(), yesOrNotMap));
//            map.put(CarOrderDetailEnum.ISMIXPAYMENT.getCode(), MapperUtils.trim(order.getIsMixPayment()));
//            map.put(CarOrderDetailEnum.SETTLEMENTPERSONAMT.getCode(), DigitBaseUtils.formatDigit(order.getSettlementPersonAmt()));
//            map.put(CarOrderDetailEnum.SETTLEMENTACCNTAMT.getCode(), DigitBaseUtils.formatDigit(order.getSettlementAccntAmt()));
            map.put(CarOrderDetailEnum.BOOKINGTYPE_DES.getCode(), MapperUtils.trim(order.getBookingtypeDes()));
//            map.put(CarOrderDetailEnum.DEFINDFLAG.getCode(), MapperUtils.trim(order.getDefineflag()));
//            map.put(CarOrderDetailEnum.DEFINDFLAG2.getCode(), MapperUtils.trim(order.getDefineflag2()));
//            map.put(CarOrderDetailEnum.SERVICE_TIME.getCode(), MapperUtils.trim(order.getServiceTime()));
//            map.put(CarOrderDetailEnum.ONLINE_HIT_ABNORMAL_RULE_DESC.getCode(), MapperUtils.trim(order.getOnlineHitAbnormalRuleDesc()));
//            map.put(CarOrderDetailEnum.CARCARBONS.getCode(), MapperUtils.convertDigitToString(order.getCarbonEmission()));
//            map.put(CarOrderDetailEnum.IS_GREEN_CAR.getCode(), MapperUtils.convertTorF(order.getIsGreenCar(), yesOrNotMap));
//            map.put(CarOrderDetailEnum.STDINDUSTRY1.getCode(), MapperUtils.trim(order.getStdIndustry1()));
//            map.put(CarOrderDetailEnum.STDINDUSTRY2.getCode(), MapperUtils.trim(order.getStdIndustry2()));

            result.add(map);
        }
        return result;
    }

    private String convertOrderType(int orderType, String subProductLine) {
        if (OrderTypeEnum.D.getOrderType() == orderType) {
            return OrderTypeEnum.D.getDesCn();
        } else if (OrderTypeEnum.S.getOrderType() == orderType) {
            return OrderTypeEnum.S.getDesCn();
        } else if (OrderTypeEnum.B.getOrderType() == orderType) {
            return OrderTypeEnum.B.getDesCn();
        } else if (OrderTypeEnum.R.getOrderType() == orderType) {
            return OrderTypeEnum.R.getDesCn();
        } else if (OrderTypeEnum.P.getOrderType() == orderType) {
            return OrderTypeEnum.P.getDesCn();
        } else if (OrderTypeEnum.U.getOrderType() == orderType) {
            if (StringUtils.equalsIgnoreCase(subProductLine, "1")) {
                return "国内打车";
            } else if (StringUtils.equalsIgnoreCase(subProductLine, "CAR_TAXI_INTL")) {
                return "国内打车";
            } else {
                return OrderTypeEnum.U.getDesCn();
            }
        }
        return MapperUtils.convertDigitToString(orderType);
    }

    private String convertOrderStatus(int orderType, String orderStatus) {
        Map<String, String> map = CarRentOrderStatusEnum.toMap();
        if (orderType == OrderTypeEnum.U.getValue()) {
            return map.get(orderStatus);
        } else if (orderType == OrderTypeEnum.B.getValue()) {
            return map.get(orderStatus);
        } else if (orderType == OrderTypeEnum.D.getValue()) {
            return map.get(orderStatus);
        } else if (orderType == OrderTypeEnum.S.getValue() || orderType == OrderTypeEnum.R.getValue()) {
            return map.get(orderStatus);
        }
        return orderStatus;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.car;
    }
}
