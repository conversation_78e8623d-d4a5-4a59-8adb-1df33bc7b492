package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.bluespace.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportFlightOrderInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.bluespace.AbstractOrderDetailBlueSpaceService;
import onlinereport.enums.reportlib.OrderDetailEnumerable;
import onlinereport.enums.reportlib.bluespace.BsFlightOrderDetailEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2022-10-13 13:58
 * @desc
 */
@Service
public class FlightOrderBluespaceService extends AbstractOrderDetailBlueSpaceService {

    @Override
    public OrderDetailEnumerable[] getEnum(String reportType) {
        return BsFlightOrderDetailEnum.values();
    }


    @Override
    protected List<Map<String, Object>> convertMapData(OnlineReportOrderDetailInfo detailInfo, String lang, String reportType) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportFlightOrderInfo> data = detailInfo.getFltOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        return convertFlightOrderMapData(detailInfo, lang);
    }

    protected List<Map<String, Object>> convertFlightOrderMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportFlightOrderInfo> data = detailInfo.getFltOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        for (OnlineReportFlightOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(BsFlightOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZeroString(order.getOrderId()));
            map.put(BsFlightOrderDetailEnum.ORDERSTATUS.getCode(), order.getOrderStatus());
            map.put(BsFlightOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(BsFlightOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(BsFlightOrderDetailEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorpName()));
            map.put(BsFlightOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(BsFlightOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
            map.put(BsFlightOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZeroString(order.getQuantity()));
            map.put(BsFlightOrderDetailEnum.REFUNDTKT.getCode(), MapperUtils.convertDigitToZeroString(order.getRefundtkt()));
            map.put(BsFlightOrderDetailEnum.NETFARE.getCode(), DigitBaseUtils.formatDigit(order.getNetfare()).toString());
            map.put(BsFlightOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()).toString());
            map.put(BsFlightOrderDetailEnum.OILFEE.getCode(), DigitBaseUtils.formatDigit(order.getOilFee()).toString());
            map.put(BsFlightOrderDetailEnum.TAX.getCode(), DigitBaseUtils.formatDigit(order.getTax()).toString());
            map.put(BsFlightOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getServiceFee()).toString());
            map.put(BsFlightOrderDetailEnum.INSERANCEFEE.getCode(), DigitBaseUtils.formatDigit(order.getInsuranceFee()).toString());
            map.put(BsFlightOrderDetailEnum.CHANGEFEE.getCode(), DigitBaseUtils.formatDigit(order.getChangeFee()).toString());
            map.put(BsFlightOrderDetailEnum.REBOOKPRICEDIFFERENT.getCode(), DigitBaseUtils.formatDigit(order.getRebookPriceDifferential()).toString());
            map.put(BsFlightOrderDetailEnum.REBOOKSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebookServiceFee()).toString());
            map.put(BsFlightOrderDetailEnum.REFUNDFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundFee()).toString());
            map.put(BsFlightOrderDetailEnum.REFUNDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundServiceFee()).toString());
            map.put(BsFlightOrderDetailEnum.SENDTICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getSendTicketFee()).toString());
            map.put(BsFlightOrderDetailEnum.TICKETBEHINDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getTicketBehindServiceFee()).toString());
            map.put(BsFlightOrderDetailEnum.REBOOKBEHINDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebookBehindServiceFee()).toString());
            map.put(BsFlightOrderDetailEnum.REFUNDTBEHINDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundBehindServiceFee()).toString());
            map.put(BsFlightOrderDetailEnum.ISREFUND.getCode(), order.getIsRefund());
            map.put(BsFlightOrderDetailEnum.ISREBOOK.getCode(), order.getIsRebook());
            map.put(BsFlightOrderDetailEnum.REBOOKTIME.getCode(), MapperUtils.trim(order.getRebookTime()));
            map.put(BsFlightOrderDetailEnum.CHANGEFLIGHTNO.getCode(), MapperUtils.trim(order.getChangeFlightNo()));
            map.put(BsFlightOrderDetailEnum.CHANGETAKEOFFTIME.getCode(), MapperUtils.trim(order.getChangeTakeoffTime()));
            map.put(BsFlightOrderDetailEnum.CHANGEARRIVALTIME.getCode(), MapperUtils.trim(order.getChangeArrivalDatetime()));
            map.put(BsFlightOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassengerName()));
            map.put(BsFlightOrderDetailEnum.SEQUENCE.getCode(), MapperUtils.convertDigitToString(order.getSequence()));
            map.put(BsFlightOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlightNo()));
            map.put(BsFlightOrderDetailEnum.AIRLINE.getCode(), MapperUtils.trim(order.getAirline()));
            map.put(BsFlightOrderDetailEnum.AIRLINECN.getCode(), MapperUtils.trim(order.getAirlineCnName()));
            map.put(BsFlightOrderDetailEnum.TICKETNO.getCode(), MapperUtils.trim(order.getTicketNo()));
            map.put(BsFlightOrderDetailEnum.REALCLASS.getCode(), order.getRealClass());
            map.put(BsFlightOrderDetailEnum.FLIGHTCITYCODE.getCode(), MapperUtils.trim(order.getFlightCityCode()));
            map.put(BsFlightOrderDetailEnum.FLIGHTCITY.getCode(), MapperUtils.trim(order.getFlightCity()));
            map.put(BsFlightOrderDetailEnum.FLIGHTCITY2.getCode(), MapperUtils.trim(order.getFlightCity2En()));
            map.put(BsFlightOrderDetailEnum.TPMS.getCode(), DigitBaseUtils.formatDigit(order.getTpms()).toString());
            map.put(BsFlightOrderDetailEnum.DEPARTUREPORTNAME.getCode(), MapperUtils.trim(order.getDeparturePortName()));
            map.put(BsFlightOrderDetailEnum.DPORTCODE.getCode(), MapperUtils.trim(order.getDportCode()));
            map.put(BsFlightOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.trim(order.getDepartureCityName()));
            map.put(BsFlightOrderDetailEnum.DEPARTURECOUNTRY.getCode(), MapperUtils.trim(order.getDepartureCountry()));
            map.put(BsFlightOrderDetailEnum.DEPARTURECONTINENT.getCode(), MapperUtils.trim(order.getDepartureContinent()));
            map.put(BsFlightOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoffTime()));
            map.put(BsFlightOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.trim(order.getArrivalCityName()));
            map.put(BsFlightOrderDetailEnum.ARRIVALPORTNAME.getCode(), MapperUtils.trim(order.getArrivalPortName()));
            map.put(BsFlightOrderDetailEnum.APORTCODE.getCode(), MapperUtils.trim(order.getAportCode()));
            map.put(BsFlightOrderDetailEnum.ARRIVALCONUNTRY.getCode(), MapperUtils.trim(order.getArrivalCountry()));
            map.put(BsFlightOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrivalDateTime()));
            map.put(BsFlightOrderDetailEnum.DESTCONTINENT.getCode(), MapperUtils.trim(order.getDestContinent()));
            map.put(BsFlightOrderDetailEnum.DESTCOUNTRY.getCode(), MapperUtils.trim(order.getDestCountry()));
            map.put(BsFlightOrderDetailEnum.DESTCITYNAME.getCode(), MapperUtils.trim(order.getDestCityName()));
            map.put(BsFlightOrderDetailEnum.OCURRENCY.getCode(), MapperUtils.trim(order.getOCurrency()));
            map.put(BsFlightOrderDetailEnum.OEXCHANGERATE.getCode(), DigitBaseUtils.formatDigit(order.getOExchangerate()).toString());
            result.add(map);
        }
        return result;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.flight;
    }
}
