package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FlightBehaviorInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import com.google.common.collect.ImmutableMap;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc 机票起飞时间段
 */
@Service
public class FltDepartureTimeBehaviorAnalysisAdaptor extends BaseReportDataAdaptor<Object> {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private FltDepartureTimeBehaviorAnalysisExcelService fltDepartureTimeBehaviorAnalysisExcelService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        OnlineReportBehaviorAnalysisRequest request = new OnlineReportBehaviorAnalysisRequest();
        request.setProductType(baseCondition.getProductType());
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
        extMap.put("dim", "TAKEOFF_TIME");
        request.setQueryBu(QueryReportBuTypeEnum.flight);
        request.setExtData(extMap);
        OnlineReportBehaviorAnalysisResponse responseType = corpOnlineReportPlatformService.queryBehaviorAnalysis(request);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            return flgihtBehavior(Optional.ofNullable(responseType.getFlightBehaviorList()).orElse(new ArrayList<>()), lang);
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(responseType)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
            }
            throw businessException;
        }
    }

    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        return fltDepartureTimeBehaviorAnalysisExcelService.buildExcel(baseCondition);
    }

    @Override
    public String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang) {
        String fileName_format = "%s_%s_%s_%s";
        String middle = SharkUtils.get("Travelanalysis.takeofftime", lang);
        String prefix = SharkUtils.get("Catalog.BehaviorAnalysis", lang);
        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
        return String.format(fileName_format, prefix, middle, uid, str);
    }

    private Object flgihtBehavior(List<FlightBehaviorInfo> flightBehaviorInfoList, String lang) {
        TakeoffTimeRangeEnum[] fltTakeoffTimeRangeEnums = TakeoffTimeRangeEnum.values();
        List quantityDataList = new ArrayList();
        for (TakeoffTimeRangeEnum fltTakeoffTimeRangeEnum : fltTakeoffTimeRangeEnums) {
            FlightBehaviorInfo flightBehaviorInfo = flightBehaviorInfoList.stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), fltTakeoffTimeRangeEnum.getKey()))
                    .findFirst().orElse(new FlightBehaviorInfo());
            Map trendAmountQuantityData = ImmutableMap.builder()
                    .put("quantity", MapperUtils.convertDigitToZero(flightBehaviorInfo.getTotalQuantity()))
                    .put("price", MapperUtils.convertDigitToZero(flightBehaviorInfo.getTotalPrice()))
                    .put("avgPrice", MapperUtils.convertDigitToZero(flightBehaviorInfo.getAvgPrice()))
                    .put("avgDiscount", MapperUtils.convertDigitToZero(flightBehaviorInfo.getAvgDiscount())).build();
            quantityDataList.add(ImmutableMap.builder()
                    .put("axis", fltTakeoffTimeRangeEnum.getKey())
                    .put("data", trendAmountQuantityData).build());
        }
        return quantityDataList;
    }

    enum TakeoffTimeRangeEnum {
        RANGE1("00:00-06:00"),
        RANGE2("06:00-08:00"),
        RANGE3("08:00-10:00"),
        RANGE4("10:00-12:00"),
        RANGE5("12:00-14:00"),
        RANGE6("14:00-16:00"),
        RANGE7("16:00-18:00"),
        RANGE8("18:00-20:00"),
        RANGE9("20:00-22:00"),
        RANGE10("22:00-24:00"),
        ;
        String key;

        TakeoffTimeRangeEnum(String s) {
            this.key = s;
        }

        public String getKey() {
            return key;
        }

    }
}
