package com.corpgovernment.resource.schedule.onlinereport.reportlib;

import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportLibFilterBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib.ReportLibResultEntity;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/8/21
 */
@Service
public class HotelOrderDetailService extends OrderDeatailService {
    public static String convertPrepayType(String prepayType) {
        if (StringUtils.equalsIgnoreCase(prepayType, "ACCNT")) {
            return "公司账户";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "PERSONAL")) {
            return "个人支付";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "CCARD")) {
            return "信用卡";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "WSCAN")) {
            return "微信支付";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "ALIPAY")) {
            return "支付宝(移动版)";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "ALPAY")) {
            return "支付宝";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "TMPAY")) {
            return "礼品卡";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "MAPAY")) {
            return "支付宝(移动版)";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "CARD")) {
            return "银行卡";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "cash")) {
            return "现金";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "more")) {
            return "第三方支付";
        } else if (StringUtils.equalsIgnoreCase(prepayType, "epay")) {
            return "信用卡";
        }
        return prepayType;
    }

    @Override
    public ReportLibResultEntity queryOrderdetails(ReportLibFilterBO reportLibFilterBO) {
        return null;
    }
}
