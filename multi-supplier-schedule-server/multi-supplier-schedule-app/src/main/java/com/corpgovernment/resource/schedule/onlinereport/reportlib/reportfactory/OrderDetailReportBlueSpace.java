package com.corpgovernment.resource.schedule.onlinereport.reportlib.reportfactory;

import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.cglib.seriallizer.JacksonUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TimeFilterTypeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.task.CorpreportDownloadTaskService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BasePermitVerfiyService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.OrderDetailBluespaceService;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.reportlib.TrainOrderStatusEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.TempFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2020/9/28 17:44
 * @Desc 报告库
 */
@Service
@Slf4j
public class OrderDetailReportBlueSpace extends AbstractGenralExportDataService implements IReport {

    public final static String CORP_KEY = "corp";
    public final static String ACCOUNT_KEY = "account";
    public final static String DEPT_KEY = "dept";
    public final static String COST_CENTER_KEY = "costcenter";
    public final static String TIME_RANGE_FORMAT = "%s~%s";
    protected static final String LOG_TITLE = OrderDetailReportBlueSpace.class.getSimpleName();
    private final static int DEFAULT_PAGESIZE = 1000;
    //默认最大间隔天数，超过就要分批
    private final static int MAX_INTERVAL_DAYS_DEAFULT = 33;

    @Autowired
    private OrderDetailBluespaceService orderDetailService;

    @Autowired
    private BasePermitVerfiyService basePermitVerfiyService;

    public Workbook create(TaskEntity taskEntity) throws Exception {
        Workbook workbook = null;
        try {
            String conditions = taskEntity.getConditions();
            log.info(LOG_TITLE, conditions);
            BaseQueryConditionBO baseQueryConditionBO = (BaseQueryConditionBO) JacksonUtil.deserialize(conditions, BaseQueryConditionBO.class);
            String uid = taskEntity.getUid();
            baseQueryConditionBO.getBaseQueryCondition().setUid(uid);
            String lang = taskEntity.getLang();
            String sheetName = CorpreportDownloadTaskService.getReportNameBluespace(lang, baseQueryConditionBO.getBaseQueryCondition().getReportId());
            String download_pageSize = QConfigUtils.getValue("bluespace_download_pageSize");
            int pageSize = StringUtils.isEmpty(download_pageSize) ? DEFAULT_PAGESIZE : Integer.valueOf(download_pageSize);
            UserPermissionsBo userPermissionsBo = basePermitVerfiyService.vaildPermit(uid, baseQueryConditionBO, lang);
            BaseQueryConditionBO baseQueryCondition = basePermitVerfiyService.convertToBaseOrderQuery(userPermissionsBo, baseQueryConditionBO);
            Pager pager = new Pager(0l, pageSize, 1, 0);
            baseQueryCondition.setLang(lang);
            baseQueryCondition.setPager(pager);
            List filterList = filllFilterContent(lang, baseQueryConditionBO, uid, baseQueryConditionBO.getOrderstatusList());

            workbook = createBigData(baseQueryCondition, sheetName, pageSize, uid, lang, filterList);
        } catch (Exception e) {
            log.error(LOG_TITLE, e);
            if (workbook != null) {
  /*              if (workbook instanceof SXSSFWorkbook) {
                    ((SXSSFWorkbook) workbook).dispose();
                }
                workbook.close();*/
            }
            throw e;
        }
        return workbook;
    }

    private Workbook createBigData(BaseQueryConditionBO reportLibFilterBO, String sheetName, int pageSize, String uid, String lang, List filterList) throws IOException, BusinessException {
        // 创建工作簿
        String tempPath = System.getProperty(TempFile.JAVA_IO_TMPDIR);
        log.info(LOG_TITLE, "createBigData tempPath:" + tempPath);
        return createBigDataMulitBatchByDate(reportLibFilterBO, sheetName, pageSize, uid, lang, filterList);
    }

    /**
     * @param lang
     * @param baseQueryConditionBO
     * @param uid
     * @return
     */
    public List filllFilterContent(String lang, BaseQueryConditionBO baseQueryConditionBO, String uid, List<String> orderstatusList) {
        BaseQueryCondition baseQueryCondition = baseQueryConditionBO.getBaseQueryCondition();
        String queryBu = baseQueryConditionBO.getQueryBu();
        List data = new ArrayList();
        data.addAll(getTimeRangeContent(lang, baseQueryCondition.getTimeFilterList(), queryBu, baseQueryCondition.getReportId()));
        if (!StringUtils.equalsIgnoreCase(baseQueryCondition.getReportId(), "DetailReport:UnUseFltTicketDetails")) {
            Map map = getConditionContentBluespace(uid, baseQueryCondition, lang);
            data.addAll(getFilterContent(orderstatusList, baseQueryConditionBO.getUsers(),
                    baseQueryConditionBO.getPassengers(), baseQueryConditionBO.getOrderids(), baseQueryConditionBO.getEmployeIds(),
                    queryBu, lang, baseQueryCondition.getReportId()));
            data.addAll(getDataRangeContent(lang, map));
        }
        return data;
    }

    /**
     * 数据范围
     *
     * @param lang
     * @param map
     * @return
     */
    protected List getDataRangeContent(String lang, Map map) {
        return Arrays.asList(
                Arrays.asList(SharkUtils.getBluespace("Index.public", lang), (String) map.get(CORP_KEY)),
                Arrays.asList(SharkUtils.getBluespace("Public.account", lang), (String) map.get(ACCOUNT_KEY)),
                Arrays.asList(SharkUtils.getBluespace("Public.department", lang), (String) map.get(DEPT_KEY)),
                Arrays.asList(SharkUtils.getBluespace("Public.costcenter", lang), (String) map.get(COST_CENTER_KEY)));
    }

    /**
     * @param lang
     * @param timeFilterTypeInfoList
     * @param queryBu
     * @return
     */
    protected List getTimeRangeContent(String lang, List<TimeFilterTypeInfo> timeFilterTypeInfoList, String queryBu, String reportId) {
        List list = new ArrayList();
        list.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY));
        for (TimeFilterTypeInfo timeFilterTypeInfo : timeFilterTypeInfoList) {
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate")) {
                list.add(Arrays.asList(SharkUtils.getBluespace("Exceltopname.date", lang),
                        String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
            } else if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
                if (StringUtils.equalsIgnoreCase("DetailReport:FltTicketRefundDetails", reportId)) {
                    list.add(Arrays.asList(SharkUtils.getBluespace("Exceltopname.RefundTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase("DetailReport:FltTicketRebookDetails", reportId)) {
                    list.add(Arrays.asList(SharkUtils.getBluespace("Report.RebookTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase("DetailReport:UnUseFltTicketDetails", reportId)) {
                    list.add(Arrays.asList(SharkUtils.getBluespace("Exceltopname.TicketingTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else {
                    list.add(Arrays.asList(SharkUtils.getBluespace("Exceltopname.dealdate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                }
            } else if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "usedate")) {
                if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.flight.toString())) {
                    list.add(Arrays.asList(SharkUtils.getBluespace("Report.TakeoffTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.hotel.toString())) {
                    list.add(Arrays.asList(SharkUtils.getBluespace("Exceltopname.livedate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.train.toString())) {
                    list.add(Arrays.asList(SharkUtils.getBluespace("Exceltopname.depaturedate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.car.toString())) {
                    list.add(Arrays.asList(SharkUtils.getBluespace("Report.begaindate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                }
            }
        }
        return list;
    }

    /**
     * @param orderStatusList 订单状态
     * @param users           预订人
     * @param passengers      出行人
     * @param orderids        订单号
     * @param queryBu         产线
     * @param lang            语言
     * @return
     */
    protected List getFilterContent(List<String> orderStatusList, List<String> users, List<String> passengers, List<String> orderids, List<String> employeeIds,
                                    String queryBu, String lang, String reportId) {
        List list = new ArrayList();
        String default_tip = SharkUtils.getBluespace("Report.SelectResult2", lang);
        list.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY));
        // 退票和改签不显示订单状态
        if (!StringUtils.equalsIgnoreCase(reportId, "DetailReport:FltTicketRefundDetails") && !StringUtils.equalsIgnoreCase(reportId, "DetailReport:FltTicketRebookDetails")) {
            // 订单状态
            list.add(Arrays.asList(SharkUtils.getBluespace("Exceltopname.orderstatus", lang),
                    getOrderStatusDesc(orderStatusList, queryBu, lang)));
        }
        // 订单号，订单号是数字，excel导入的时候会检查千分位，所以订单号用'、'分割
        list.add(Arrays.asList(SharkUtils.getBluespace("TravelPosition.order", lang),
                CollectionUtils.isEmpty(orderids) ? default_tip : StringUtils.join(orderids, "、")));
        // 预订人
        list.add(Arrays.asList(SharkUtils.getBluespace("UserSurvey.Q1.Opt5", lang),
                CollectionUtils.isEmpty(users) ? default_tip : StringUtils.join(users, GlobalConst.SEPARATOR)));
        return list;
    }


    private String getOrderStatusDesc(List<String> orderStatusList, String queryBU, String lang) {
        if (CollectionUtils.isEmpty(orderStatusList)) {
            return SharkUtils.getBluespace("Public.all", lang);
        }
        if (StringUtils.equalsIgnoreCase(queryBU, QueryReportBuTypeEnum.flight.toString())) {
            return getFlightOrderStatusDesc(orderStatusList, lang);
        } else if (StringUtils.equalsIgnoreCase(queryBU, QueryReportBuTypeEnum.hotel.toString())) {
            return getHotelOrderStatusDesc(orderStatusList, lang);
        } else if (StringUtils.equalsIgnoreCase(queryBU, QueryReportBuTypeEnum.train.toString())) {
            return getTrainOrderStatusDesc(orderStatusList, lang);
        } else {
            return CollectionUtils.isEmpty(orderStatusList) ? StringUtils.EMPTY : StringUtils.join(orderStatusList, GlobalConst.SEPARATOR);
        }
    }

    private String getFlightOrderStatusDesc(List<String> orderStatusList, String lang) {
        List list = new ArrayList();
        Optional.ofNullable(orderStatusList).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).forEach(i -> {
            if (StringUtils.equalsIgnoreCase(i, "S")) {
                // 已成交
                list.add(SharkUtils.getBluespace("RiskOrder.FltStatus4", lang));
            } else if (StringUtils.equalsIgnoreCase(i, "R")) {
                // 全部退票
                list.add(SharkUtils.getBluespace("RiskOrder.FltStatus7", lang));
            } else if (StringUtils.equalsIgnoreCase(i, "C")) {
                // 已取消
                list.add(SharkUtils.getBluespace("RiskOrder.FltStatus1", lang));
            } else if (StringUtils.equalsIgnoreCase(i, "T")) {
                // 部分退票
                list.add(SharkUtils.getBluespace("RiskOrder.FltStatus3", lang));
            } else {
                list.add(i);
            }
        });
        return StringUtils.join(list, GlobalConst.SEPARATOR);
    }

    private String getHotelOrderStatusDesc(List<String> orderStatusList, String lang) {
        List list = new ArrayList();
        Optional.ofNullable(orderStatusList).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).forEach(i -> {
            if (StringUtils.equalsIgnoreCase(i, "P")) {
                // 处理中
                list.add(SharkUtils.getBluespace("RiskOrder.HtlOrderStatus6", lang));
            } else if (StringUtils.equalsIgnoreCase(i, "S")) {
                // 成交
                list.add(SharkUtils.getBluespace("RiskOrder.HtlOrderStatus1", lang));
            } else if (StringUtils.equalsIgnoreCase(i, "C")) {
                // 取消
                list.add(SharkUtils.getBluespace("RiskOrder.HtlOrderStatus2", lang));
            } else if (StringUtils.equalsIgnoreCase(i, "U")) {
                // 修改
                list.add(SharkUtils.getBluespace("RiskOrder.HtlOrderStatus4", lang));
            } else if (StringUtils.equalsIgnoreCase(i, "SW")) {
                // 已提交，待处理
                list.add(SharkUtils.getBluespace("RiskOrder.HtlOrderStatus5", lang));
            } else if (StringUtils.equalsIgnoreCase(i, "SP")) {
                // 已提交，处理中
                list.add(SharkUtils.getBluespace("RiskOrder.HtlOrderStatus7", lang));
            } else if (StringUtils.equalsIgnoreCase(i, "O")) {
                // 其他
                list.add(SharkUtils.getBluespace("RiskOrder.HtlOrderStatus3", lang));
            } else {
                list.add(i);
            }
        });
        return StringUtils.join(list, GlobalConst.SEPARATOR);
    }

    private String getTrainOrderStatusDesc(List<String> orderStatusList, String lang) {
        List list = new ArrayList();
        Optional.ofNullable(orderStatusList).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).forEach(i -> {
            if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.N.toString())) {
                // 未提交
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.N.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.WP.toString())) {
                // 待支付
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.WP.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.PP.toString())) {
                // 支付处理中
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.PP.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.PF.toString())) {
                // 支付失败
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.PF.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.WA.toString())) {
                // 待授权
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.WA.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.AR.toString())) {
                // 授权拒绝
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.AR.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.WT.toString())) {
                // 待出票
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.WT.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.TP.toString())) {
                // 购票中
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.TP.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.TD.toString())) {
                // 已购票"
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.TD.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.TF.toString())) {
                // 出票失败
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.TF.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.C.toString())) {
                // 已取消
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.C.getName(), lang));
            } else {
                list.add(i);
            }
        });
        return StringUtils.join(list, GlobalConst.SEPARATOR);
    }

    /**
     * @param baseQueryConditionBO
     * @param sheetName
     * @param pageSize
     * @param uid
     * @param lang
     * @return
     * @throws IOException
     * @throws BusinessException
     */
    private Workbook createBigDataMulitBatchByDate(BaseQueryConditionBO baseQueryConditionBO, String sheetName, int pageSize, String uid, String lang, List filterList) throws IOException, BusinessException {
        /*Integer sheetLimit = QConfigUtils.getInterValue("sheetLimit");
        if (sheetLimit == null || sheetLimit <= 0) {
            sheetLimit = 1000;
        }
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        SXSSFWorkbook workbook = new SXSSFWorkbook(hssfWorkbook, sheetLimit);
        int pageIndex = 1;
        boolean isEmpty = false;//是否为空
        boolean isLastPage = false;//最后一页
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info(LOG_TITLE, "createBigDataMulitBatchByDate start");
        int startRow = 0;
        List<TimeFilterTypeInfo> timeFilterTypeInfoList = baseQueryConditionBO.getBaseQueryCondition().getTimeFilterList();

        Map<Integer, Integer> maxWidth = new HashMap<>();
        TimeFilterTypeInfo timeFilterTypeInfo = null;
        Optional optional1 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getTimeType(), "orderdate"))
                .findFirst();
        Optional optional2 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getTimeType(), "dealdate"))
                .findFirst();
        Optional optional3 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getTimeType(), "usedate"))
                .findFirst();
        if (optional1.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional1.get();
        } else if (optional2.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional2.get();
        } else if (optional3.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional3.get();
        }
        long intervalDays = DateUtil.betweenDay(timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime());
        if (intervalDays < MAX_INTERVAL_DAYS_DEAFULT) {
            while (!isLastPage) {
                List<List<Object>> dataSource = new ArrayList<>();
                Pager pager = baseQueryConditionBO.getPager();
                pager.setPageIndex(pageIndex);
                OrderDetailBO orderDetail = orderDetailService.queryDetail(uid, baseQueryConditionBO, true);
                if (pageIndex == 1) {
                    dataSource.add(new ArrayList<>(orderDetail.getTitle()));
                }
                pageIndex++;
                isEmpty = CollectionUtils.isEmpty(orderDetail.getData());
                isLastPage = isEmpty || orderDetail.getData().size() < pageSize;
                if (!isEmpty) {
                    dataSource.addAll(orderDetail.getData());
                }
                PoiCustomExcelUtils.createExcelBigDataMulitBatchObj(workbook, dataSource, startRow, sheetName, maxWidth);
                startRow += dataSource.size();
            }
        } else {
            boolean hasTitle = false;
            String startTime = timeFilterTypeInfo.getStartTime();
            String endTime = timeFilterTypeInfo.getEndTime();
            String startTimeTemp = startTime;
            String endTimeTemp = DateUtil.offsetTime(startTime, MAX_INTERVAL_DAYS_DEAFULT, Calendar.DAY_OF_YEAR);
            while (endTimeTemp.compareTo(endTime) <= 0 && startTimeTemp.compareTo(endTime) <= 0) {
                timeFilterTypeInfo.setStartTime(startTimeTemp);
                timeFilterTypeInfo.setEndTime(endTimeTemp);
                pageIndex = 1;
                isLastPage = false;//最后一页
                while (!isLastPage) {
                    List<List<Object>> dataSource = new ArrayList<>();
                    Pager pager = baseQueryConditionBO.getPager();
                    pager.setPageIndex(pageIndex);
                    OrderDetailBO orderDetail = orderDetailService.queryDetail(uid, baseQueryConditionBO, true);
                    if (pageIndex == 1 && !hasTitle) {
                        dataSource.add(new ArrayList<>(orderDetail.getTitle()));
                        hasTitle = true;
                    }
                    pageIndex++;
                    isEmpty = CollectionUtils.isEmpty(orderDetail.getData());
                    isLastPage = isEmpty || orderDetail.getData().size() < pageSize;
                    if (!isEmpty) {
                        dataSource.addAll(orderDetail.getData());
                    }
                    PoiCustomExcelUtils.createExcelBigDataMulitBatchObj(workbook, dataSource, startRow, sheetName, maxWidth);
                    startRow += dataSource.size();
                }
                startTimeTemp = DateUtil.offsetTime(endTimeTemp, 1, Calendar.DAY_OF_YEAR);
                endTimeTemp = DateUtil.offsetTime(endTimeTemp, MAX_INTERVAL_DAYS_DEAFULT, Calendar.DAY_OF_YEAR);
                if (endTimeTemp.compareTo(endTime) > 0 && startTimeTemp.compareTo(endTime) <= 0) {
                    startTimeTemp = startTimeTemp.compareTo(endTime) > 0 ? endTime : startTimeTemp;
                    endTimeTemp = endTimeTemp.compareTo(endTime) > 0 ? endTime : endTimeTemp;
                    timeFilterTypeInfo.setStartTime(startTimeTemp);
                    timeFilterTypeInfo.setEndTime(endTimeTemp);
                    pageIndex = 1;
                    isLastPage = false;//最后一页
                    while (!isLastPage) {
                        List<List<Object>> dataSource = new ArrayList<>();
                        Pager pager = baseQueryConditionBO.getPager();
                        pager.setPageIndex(pageIndex);
                        OrderDetailBO orderDetail = orderDetailService.queryDetail(uid, baseQueryConditionBO, true);
                        if (pageIndex == 1 && !hasTitle) {
                            dataSource.add(new ArrayList<>(orderDetail.getTitle()));
                            hasTitle = true;
                        }
                        pageIndex++;
                        isEmpty = CollectionUtils.isEmpty(orderDetail.getData());
                        isLastPage = isEmpty || orderDetail.getData().size() < pageSize;
                        if (!isEmpty) {
                            dataSource.addAll(orderDetail.getData());
                        }
                        PoiCustomExcelUtils.createExcelBigDataMulitBatchObj(workbook, dataSource, startRow, sheetName, maxWidth);
                        startRow += dataSource.size();
                    }
                    break;
                }
            }
        }
        PoiCustomExcelUtils.createExcelMulitBatch(hssfWorkbook, filterList, 1, SharkUtils.getBluespace("Report.SelectResult1", lang), maxWidth);
        log.info(LOG_TITLE, String.format("createBigDataMulitBatchByDate end, data size : %d , take time : %s", startRow, stopWatch.getTime()));
        return workbook;*/
        return null;
    }

}
