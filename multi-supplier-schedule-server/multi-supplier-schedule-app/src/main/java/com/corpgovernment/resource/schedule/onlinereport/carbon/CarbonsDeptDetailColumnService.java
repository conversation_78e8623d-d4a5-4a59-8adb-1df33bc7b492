package com.corpgovernment.resource.schedule.onlinereport.carbon;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpRiskDetailCustomColumnPO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.risk.RiskOrderReportBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValDataType;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportCarbonsDeptDetailV2Request;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportCarbonsDeptDetailV2Response;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.risk.CorpRiskOrderDetailCustomColumnDaoImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/6 18:20
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class CarbonsDeptDetailColumnService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    CorpRiskOrderDetailCustomColumnDaoImpl corpDeptDetailColumnDao;

    private final static String  TOP_DEPT_CARBONS = "TOP_DEPT_CARBONS";

    /**
     * 自定义展示全部字段中，剔除必展示的订单号字段
     */
    public List getFullDetailColumns(AnalysisObjectEnum analysisObjectEnum, String lang) {
        OnlineReportCarbonsDeptDetailV2Request request = new OnlineReportCarbonsDeptDetailV2Request();
        request.setAnalysisObjectEnum(analysisObjectEnum);
        request.setLang(lang);
        OnlineReportCarbonsDeptDetailV2Response response = corpOnlineReportPlatformService.queryCarbonsDeptDetailV2Header(request);
        List<HeaderKeyValDataType> headerKeyValDataTypeList = response.getHeaderData();
        excludeHeaderKey(headerKeyValDataTypeList);
        return response.getHeaderData();
    }


    // 查询自定字段, 未查询到，返回默认字段
    public List<String> queryCustomColumns(RiskOrderReportBO riskOrderReportBO) throws SQLException {
        return queryCustomColumns(riskOrderReportBO.getUid());

    }


    // 查询自定字段, 未查询到，返回默认字段
    public List<String> queryCustomColumns(String uid) throws SQLException {
        List<CorpRiskDetailCustomColumnPO> pos = corpDeptDetailColumnDao.queryByUIDAndScene(
                uid, TOP_DEPT_CARBONS);
        List<String> columns = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pos)) {
            CorpRiskDetailCustomColumnPO po = pos.get(0);
            List<String> customColumns = Arrays.asList(
                    StringUtils.splitByWholeSeparatorPreserveAllTokens(po.getCustomColumns(), ",")
            );
            columns.addAll(customColumns);
            return columns;
        }
        return columns;
    }

    public List<String> fillDeptColumns(List<HeaderKeyValDataType> allColumns) {
        List<String> columns = new ArrayList<>();
        for (HeaderKeyValDataType headerKeyValMap : allColumns) {
            columns.add(headerKeyValMap.getHeaderKey());
        }
        return columns;
    }

    // 更新字段
    public int updateCustomColumns(RiskOrderReportBO riskOrderReportBO) throws SQLException {
        if (riskOrderReportBO.getCustomColumns() == null) {
            return 0;
        }
        return corpDeptDetailColumnDao.update(
                riskOrderReportBO.getUid(), TOP_DEPT_CARBONS, StringUtils.join(riskOrderReportBO.getCustomColumns(), ",")
        );
    }

    /**
     * 排除维度，保留指标
     *
     * @param columns
     * @return
     */
    public void excludeDim(List<String> columns){
        List<String> carbonsDims = Arrays.stream(AnalysisObjectEnum.values()).map(AnalysisObjectEnum::toString).collect(Collectors.toList());
        carbonsDims.addAll(Arrays.asList("NAME", "EID"));
        if (CollectionUtils.isNotEmpty(columns)){
            Iterator iterator = columns.iterator();
            while (iterator.hasNext()){
                String headerKey = (String) iterator.next();
                if (isDim(headerKey)){
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 排除维度，保留指标
     *
     * @param columns
     * @return
     */
    public void excludeHeaderKey(List<HeaderKeyValDataType> columns){
        List<String> carbonsDims = Arrays.stream(AnalysisObjectEnum.values()).map(AnalysisObjectEnum::toString).collect(Collectors.toList());
        carbonsDims.addAll(Arrays.asList("NAME", "EID"));
        if (CollectionUtils.isNotEmpty(columns)){
            Iterator iterator = columns.iterator();
            while (iterator.hasNext()){
                HeaderKeyValDataType headerKey = (HeaderKeyValDataType) iterator.next();
                if (isDim(headerKey.getHeaderKey())){
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 是否是指标
     *
     * @param header
     */
    public boolean isDim(String header){
        List<String> carbonsDims = Arrays.stream(AnalysisObjectEnum.values()).map(AnalysisObjectEnum::toString).collect(Collectors.toList());
        carbonsDims.addAll(Arrays.asList("NAME", "EID"));
        return carbonsDims.stream().anyMatch(i-> org.apache.commons.lang3.StringUtils.equalsIgnoreCase(header, i));
    }
}