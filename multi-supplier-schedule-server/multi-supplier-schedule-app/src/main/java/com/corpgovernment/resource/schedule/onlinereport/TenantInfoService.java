package com.corpgovernment.resource.schedule.onlinereport;

import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.vo.tenant.TenantInfoVo;
import com.corpgovernment.resource.schedule.domain.tenant.TenantInfoGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/9 22:46
 * @description
 */
@Service
public class TenantInfoService {

    @Autowired
    private TenantInfoGateway tenantInfoGateway;


    public TenantInfoVo getTenantNameByTenantId() {
        return tenantInfoGateway.getTenantInfo();
    }
}
