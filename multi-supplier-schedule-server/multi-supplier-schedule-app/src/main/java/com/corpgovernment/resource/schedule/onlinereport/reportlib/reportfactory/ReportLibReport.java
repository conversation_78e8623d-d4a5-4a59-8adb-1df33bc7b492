package com.corpgovernment.resource.schedule.onlinereport.reportlib.reportfactory;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

/*
 * <AUTHOR>
 * @date 2020/9/28 17:44
 * @Desc 报告库
 */
@Service
@Slf4j
public class ReportLibReport implements IReport {
//    @Override
//    public Workbook create(TaskEntity taskEntity) throws Exception {
//        return null;
//    }

    protected static final String LOG_TITLE = ReportLibReport.class.getSimpleName();
    private final static int DEFAULT_PAGESIZE = 1000;

//    @Autowired
//    private ReportLibReportDataService reportLibReportDataService;

    public Workbook create(TaskEntity taskEntity) throws Exception {
//        Workbook workbook = null;
//        try {
//            String conditions = taskEntity.getConditions();
//            log.info(LOG_TITLE, conditions);
//            ReportLibFilterBO reportLibFilterBO = (ReportLibFilterBO) JacksonUtil.deserialize(conditions, ReportLibFilterBO.class);
//            reportLibFilterBO.setUid(taskEntity.getUid());
//            String switchType = "download";
//            String lang = taskEntity.getLang();
//            reportLibFilterBO.setSwitchType(switchType);
//            String sheetName = ExcelSheetNameUtil.getSheetName(reportLibFilterBO.getBizType(), reportLibFilterBO.getQueryType(), lang);
//            ;
//            String download_pageSize = QConfigUtils.getValue("download_pageSize");
//            int pageSize = StringUtils.isEmpty(download_pageSize) ? DEFAULT_PAGESIZE : Integer.valueOf(download_pageSize);
//            reportLibFilterBO.setPageIndex(1);
//            reportLibFilterBO.setPageSize(pageSize);
//            reportLibFilterBO.setLang(lang);
//            List<List<String>> dataSource = new ArrayList<>();
//            boolean hasPermission = reportLibReportDataService.validCorpFilter(reportLibFilterBO);
//            if (!hasPermission) {
//                log.info(LOG_TITLE, String.format("%s no permit", reportLibFilterBO.getUid()));
//                return null;
//            }
//            boolean containConsume = StringUtils.equalsIgnoreCase(reportLibFilterBO.getQueryType(), OrderQueryEnum.CONSUME.toString());//是否是消费概况
//            boolean containDept = StringUtils.equalsIgnoreCase(reportLibFilterBO.getQueryType(), OrderQueryEnum.DEPT.toString());//是否是部门分析
//            boolean isEmpty = false;//是否为空
//            log.info(LOG_TITLE, "search data start");
//            if (containConsume || containDept) {
//                ReportLibResultEntity reportLibResultEntity = reportLibReportDataService.query(reportLibFilterBO);
//                isEmpty = CollectionUtils.isEmpty(reportLibResultEntity.getRowList());
//                if (!isEmpty) {
//                    dataSource.addAll(reportLibResultEntity.getRowList());
//                } else {
//                    log.info(LOG_TITLE, "containConsume or containDept no data");
//                }
//                workbook = PoiCustomExcelUtils.createExcel(dataSource, 0, 1, sheetName);
//
//            } else {
//                String excel_create_type_switch = QConfigUtils.getValue("excel_create_type_switch");
//                if (StringUtils.equalsIgnoreCase(excel_create_type_switch, "T")) {
//                    workbook = createBigData(reportLibFilterBO, sheetName, pageSize);
//                } else {
//                    workbook = createCommon(reportLibFilterBO, sheetName, pageSize);
//                }
//            }
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//            if (workbook != null) {
//                if (workbook instanceof SXSSFWorkbook) {
//                    ((SXSSFWorkbook) workbook).dispose();
//                }
//                workbook.close();
//            }
//            throw e;
//        }
        return null;
    }

//    private Workbook createCommon(ReportLibFilterBO reportLibFilterBO, String sheetName, int pageSize) throws IOException {
//        String excel_create_mulit_switch = QConfigUtils.getValue("excel_create_mulit_switch");
//        // 创建工作簿
//        Workbook workbook = null;
//        if (StringUtils.equalsIgnoreCase(excel_create_mulit_switch, "T")) {
//            workbook = createCommonMulitBatch(reportLibFilterBO, sheetName, pageSize);
//        } else {
//            workbook = createCommonOneBatch(reportLibFilterBO, sheetName, pageSize);
//        }
//        return workbook;
//    }
//
//    private Workbook createCommonOneBatch(ReportLibFilterBO reportLibFilterBO, String sheetName, int pageSize) throws IOException {
//        List<List<String>> dataSource = new ArrayList<>();
//        int pageIndex = 1;
//        boolean isEmpty = false;//是否为空
//        boolean isLastPage = false;//最后一页
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//        log.info(LOG_TITLE, "createCommonOneBatch search data start");
//        while (!isLastPage) {
//            reportLibFilterBO.setPageIndex(pageIndex);
//            ReportLibResultEntity reportLibResultEntity = reportLibReportDataService.query(reportLibFilterBO);
//            if (CollectionUtils.isNotEmpty(reportLibResultEntity.getTitleList()) && pageIndex == 1) {
//                dataSource.add(reportLibResultEntity.getTitleList());
//            }
//            pageIndex++;
//            isEmpty = CollectionUtils.isEmpty(reportLibResultEntity.getRowList());
//            isLastPage = isEmpty || reportLibResultEntity.getRowList().size() < pageSize;
//            if (!isEmpty) {
//                dataSource.addAll(reportLibResultEntity.getRowList());
//            }
//        }
//        int dataSize = dataSource.size();
//        stopWatch.stop();
//        log.info(LOG_TITLE, String.format("createCommonOneBatch search data end, data size : %d , take time : %s", dataSize, stopWatch.getTime()));
//        stopWatch.reset();
//        stopWatch.start();
//        log.info(LOG_TITLE, String.format("createCommonOneBatch excel create start, data size %d", dataSize));
//        Workbook workbook = PoiCustomExcelUtils.createExcel(dataSource, sheetName);
//        stopWatch.stop();
//        log.info(LOG_TITLE, String.format("createCommonOneBatch excel create end, data size %d,take time : %d", dataSize, stopWatch.getTime()));
//        return workbook;
//    }
//
//    private Workbook createCommonMulitBatch(ReportLibFilterBO reportLibFilterBO, String sheetName, int pageSize) throws IOException {
//        XSSFWorkbook workbook = new XSSFWorkbook();
//        int pageIndex = 1;
//        boolean isEmpty = false;//是否为空
//        boolean isLastPage = false;//最后一页
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//        log.info(LOG_TITLE, "createCommonMulitBatch start");
//        int startRow = 0;
//        Map<Integer, Integer> maxWidth = new HashMap<>();
//        while (!isLastPage) {
//            List<List<String>> dataSource = new ArrayList<>();
//            reportLibFilterBO.setPageIndex(pageIndex);
//            ReportLibResultEntity reportLibResultEntity = reportLibReportDataService.query(reportLibFilterBO);
//            if (CollectionUtils.isNotEmpty(reportLibResultEntity.getTitleList()) && pageIndex == 1) {
//                dataSource.add(reportLibResultEntity.getTitleList());
//            }
//            pageIndex++;
//            isEmpty = CollectionUtils.isEmpty(reportLibResultEntity.getRowList());
//            isLastPage = isEmpty || reportLibResultEntity.getRowList().size() < pageSize;
//            if (!isEmpty) {
//                dataSource.addAll(reportLibResultEntity.getRowList());
//            }
//            PoiCustomExcelUtils.createExcelMulitBatch(workbook, dataSource, startRow, sheetName, maxWidth);
//            startRow += dataSource.size();
//        }
//        stopWatch.stop();
//        log.info(LOG_TITLE, String.format("createCommonMulitBatch end, data size : %d , take time : %s", startRow, stopWatch.getTime()));
//        return workbook;
//    }
//
//    private Workbook createBigData(ReportLibFilterBO reportLibFilterBO, String sheetName, int pageSize) throws IOException {
//        String excel_create_mulit_switch = QConfigUtils.getValue("excel_create_mulit_switch");
//        // 创建工作簿
//        Workbook workbook = null;
//        String tempPath = System.getProperty(TempFile.JAVA_IO_TMPDIR);
//        log.info(LOG_TITLE, "createBigData tempPath:" + tempPath);
//        if (StringUtils.equalsIgnoreCase(excel_create_mulit_switch, "T")) {
//            String excel_create_mulit_date_switch = QConfigUtils.getValue("excel_create_mulit_date_switch");
//            String uids = QConfigUtils.getValue("excel_create_mulit_date_switch_uid");
//            List<String> uidList = StringUtils.isNotEmpty(uids) ? Arrays.asList(uids.split(";")) : new ArrayList<>();
//            if (StringUtils.isEmpty(excel_create_mulit_date_switch) || StringUtils.equalsIgnoreCase(excel_create_mulit_date_switch, "F") ||
//                    (CollectionUtils.isNotEmpty(uidList) && uidList.contains(reportLibFilterBO.getUid()))) {
//                workbook = createBigDataMulitBatch(reportLibFilterBO, sheetName, pageSize);
//            } else {
//                workbook = createBigDataMulitBatchByDate(reportLibFilterBO, sheetName, pageSize);
//            }
//        } else {
//            workbook = createBigDataOneBatch(reportLibFilterBO, sheetName, pageSize);
//        }
//        return workbook;
//    }
//
//    private Workbook createBigDataOneBatch(ReportLibFilterBO reportLibFilterBO, String sheetName, int pageSize) throws IOException {
//        List<List<String>> dataSource = new ArrayList<>();
//        int pageIndex = 1;
//        boolean isEmpty = false;//是否为空
//        boolean isLastPage = false;//最后一页
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//        log.info(LOG_TITLE, "createBigDataOneBatch start");
//        while (!isLastPage) {
//            reportLibFilterBO.setPageIndex(pageIndex);
//            ReportLibResultEntity reportLibResultEntity = reportLibReportDataService.query(reportLibFilterBO);
//            if (CollectionUtils.isNotEmpty(reportLibResultEntity.getTitleList()) && pageIndex == 1) {
//                dataSource.add(reportLibResultEntity.getTitleList());
//            }
//            pageIndex++;
//            isEmpty = CollectionUtils.isEmpty(reportLibResultEntity.getRowList());
//            isLastPage = isEmpty || reportLibResultEntity.getRowList().size() < pageSize;
//            if (!isEmpty) {
//                dataSource.addAll(reportLibResultEntity.getRowList());
//            }
//        }
//        int dataSize = dataSource.size();
//        stopWatch.stop();
//        log.info(LOG_TITLE, String.format("createBigDataOneBatch end, data size : %d , take time : %s", dataSize, stopWatch.getTime()));
//        stopWatch.reset();
//        stopWatch.start();
//        log.info(LOG_TITLE, String.format("createBigDataOneBatch excel create start, data size %d", dataSize));
//        Workbook workbook = PoiCustomExcelUtils.createExcelBigData(dataSource, sheetName);
//        stopWatch.stop();
//        log.info(LOG_TITLE, String.format("createBigDataOneBatch excel create end, data size %d,take time : %d", dataSize, stopWatch.getTime()));
//        return workbook;
//    }
//
//    private Workbook createBigDataMulitBatch(ReportLibFilterBO reportLibFilterBO, String sheetName, int pageSize) throws IOException {
//        Integer sheetLimit = QConfigUtils.getInterValue("sheetLimit");
//        if (sheetLimit == null || sheetLimit <= 0) {
//            sheetLimit = 10000;
//        }
//        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
//        SXSSFWorkbook workbook = new SXSSFWorkbook(hssfWorkbook, sheetLimit);
//        int pageIndex = 1;
//        boolean isEmpty = false;//是否为空
//        boolean isLastPage = false;//最后一页
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//        log.info(LOG_TITLE, "createBigDataMulitBatch start");
//        int startRow = 0;
//        Map<Integer, Integer> maxWidth = new HashMap<>();
//        while (!isLastPage) {
//            List<List<String>> dataSource = new ArrayList<>();
//            reportLibFilterBO.setPageIndex(pageIndex);
//            ReportLibResultEntity reportLibResultEntity = reportLibReportDataService.query(reportLibFilterBO);
//            if (CollectionUtils.isNotEmpty(reportLibResultEntity.getTitleList()) && pageIndex == 1) {
//                dataSource.add(reportLibResultEntity.getTitleList());
//            }
//            pageIndex++;
//            isEmpty = CollectionUtils.isEmpty(reportLibResultEntity.getRowList());
//            isLastPage = isEmpty || reportLibResultEntity.getRowList().size() < pageSize;
//            if (!isEmpty) {
//                dataSource.addAll(reportLibResultEntity.getRowList());
//            }
//            PoiCustomExcelUtils.createExcelBigDataMulitBatch(workbook, dataSource, startRow, sheetName, maxWidth);
//            printLog(dataSource, reportLibFilterBO.getUid());
//            startRow += dataSource.size();
//        }
//        log.info(LOG_TITLE, String.format("createBigDataMulitBatch end, data size : %d , take time : %s", startRow, stopWatch.getTime()));
//        return workbook;
//    }
//
//    *
//     * 多批次写入excel
//     *
//     * @param reportLibFilterBO
//     * @param sheetName
//     * @param pageSize
//     * @return
//     * @throws IOException
//
//    private Workbook createBigDataMulitBatchByDate(ReportLibFilterBO reportLibFilterBO, String sheetName, int pageSize) throws IOException {
//        Integer sheetLimit = QConfigUtils.getInterValue("sheetLimit");
//        if (sheetLimit == null || sheetLimit <= 0) {
//            sheetLimit = 10000;
//        }
//        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
//        SXSSFWorkbook workbook = new SXSSFWorkbook(hssfWorkbook, sheetLimit);
//        int pageIndex = 1;
//        boolean isLastPage = false;//最后一页
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//        log.info(LOG_TITLE, "createBigDataMulitBatchByDate start");
//        int startRow = 0;
//        Map<Integer, Integer> maxWidth = new HashMap<>();
//        long constantMilseconds = 33l * 24l * 60l * 60l * 1000l;
//        long start = reportLibFilterBO.getStartTime();
//        long maxEndTime = reportLibFilterBO.getEndTime();
//        long startMiles = start;
//        long endMiles = start + constantMilseconds;
//        if (endMiles > maxEndTime) {
//            boolean hasTitle = false;
//            while (!isLastPage) {
//                List<List<String>> dataSource = new ArrayList<>();
//                reportLibFilterBO.setPageIndex(pageIndex);
//                ReportLibResultEntity reportLibResultEntity = reportLibReportDataService.query(reportLibFilterBO);
//                if (CollectionUtils.isNotEmpty(reportLibResultEntity.getTitleList()) && !hasTitle) {
//                    dataSource.add(reportLibResultEntity.getTitleList());
//                    hasTitle = true;
//                }
//                pageIndex++;
//                isLastPage = CollectionUtils.isEmpty(reportLibResultEntity.getRowList()) || reportLibResultEntity.getRowList().size() < pageSize;
//                if (CollectionUtils.isNotEmpty(reportLibResultEntity.getRowList())) {
//                    dataSource.addAll(reportLibResultEntity.getRowList());
//                }
//                PoiCustomExcelUtils.createExcelBigDataMulitBatch(workbook, dataSource, startRow, sheetName, maxWidth);
//                printLog(dataSource, reportLibFilterBO.getUid());
//                startRow += dataSource.size();
//            }
//        } else {
//            boolean hasTitle = false;
//            while (startMiles <= maxEndTime && endMiles <= maxEndTime) {
//                reportLibFilterBO.setStartTime(startMiles);
//                reportLibFilterBO.setEndTime(endMiles);
//                pageIndex = 1;
//                isLastPage = false;//最后一页
//                while (!isLastPage) {
//                    List<List<String>> dataSource = new ArrayList<>();
//                    reportLibFilterBO.setPageIndex(pageIndex);
//                    ReportLibResultEntity reportLibResultEntity = reportLibReportDataService.query(reportLibFilterBO);
//                    if (CollectionUtils.isNotEmpty(reportLibResultEntity.getTitleList()) && !hasTitle) {
//                        dataSource.add(reportLibResultEntity.getTitleList());
//                        hasTitle = true;
//                    }
//                    pageIndex++;
//                    isLastPage = CollectionUtils.isEmpty(reportLibResultEntity.getRowList()) || reportLibResultEntity.getRowList().size() < pageSize;
//                    if (CollectionUtils.isNotEmpty(reportLibResultEntity.getRowList())) {
//                        dataSource.addAll(reportLibResultEntity.getRowList());
//                    }
//                    PoiCustomExcelUtils.createExcelBigDataMulitBatch(workbook, dataSource, startRow, sheetName, maxWidth);
//                    printLog(dataSource, reportLibFilterBO.getUid());
//                    startRow += dataSource.size();
//                }
//                startMiles = endMiles + 1;
//                endMiles = endMiles + constantMilseconds;
//                if (endMiles > maxEndTime && startMiles <= maxEndTime) {
//                    //最后一次查询任务
//                    long startLast = startMiles > maxEndTime ? maxEndTime : startMiles;
//                    long endLast = endMiles > maxEndTime ? maxEndTime : endMiles;
//                    reportLibFilterBO.setStartTime(startLast);
//                    reportLibFilterBO.setEndTime(endLast);
//                    pageIndex = 1;
//                    isLastPage = false;//最后一页
//                    while (!isLastPage) {
//                        List<List<String>> dataSource = new ArrayList<>();
//                        reportLibFilterBO.setPageIndex(pageIndex);
//                        ReportLibResultEntity reportLibResultEntity = reportLibReportDataService.query(reportLibFilterBO);
//                        if (CollectionUtils.isNotEmpty(reportLibResultEntity.getTitleList()) && !hasTitle) {
//                            dataSource.add(reportLibResultEntity.getTitleList());
//                            hasTitle = true;
//                        }
//                        pageIndex++;
//                        isLastPage = CollectionUtils.isEmpty(reportLibResultEntity.getRowList()) || reportLibResultEntity.getRowList().size() < pageSize;
//                        if (CollectionUtils.isNotEmpty(reportLibResultEntity.getRowList())) {
//                            dataSource.addAll(reportLibResultEntity.getRowList());
//                        }
//                        PoiCustomExcelUtils.createExcelBigDataMulitBatch(workbook, dataSource, startRow, sheetName, maxWidth);
//                        printLog(dataSource, reportLibFilterBO.getUid());
//                        startRow += dataSource.size();
//                    }
//                    break;
//                }
//            }
//        }
//        log.info(LOG_TITLE, String.format("createBigDataMulitBatchByDate end, data size : %d , take time : %s", startRow, stopWatch.getTime()));
//        return workbook;
//    }
//
//    private void printLog(List<List<String>> dataSource, String uid) {
//        try {
//            String logUid = QConfigUtils.getValue("print_data_log_uid");
//            if (CollectionUtils.isNotEmpty(dataSource) && StringUtils.equalsIgnoreCase(uid, logUid)) {
//                for (List<String> data : dataSource) {
//                    log.info(LOG_TITLE, String.format("data log, data : %s", data));
//                }
//            }
//        } catch (Exception e) {
//
//        }
//    }
}
