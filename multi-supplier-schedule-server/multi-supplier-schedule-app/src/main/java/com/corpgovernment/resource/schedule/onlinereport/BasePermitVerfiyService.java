package com.corpgovernment.resource.schedule.onlinereport;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.CostCenterAndDept;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.SearchDeptAndCostcneterEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SqlFieldValidator;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/8/25 10:25
 */
@Service
@Slf4j
public class BasePermitVerfiyService {

    protected static final String LOG_TITLE = BasePermitVerfiyService.class.getSimpleName();

/*    @Autowired
    private UserPermissionService userPermissionService;

    @Autowired
    private RpcCorpOnlineReportPrivilegeService rpcCorpOnlineReportPrivilegeService;


    @Autowired
    private CustomDimConfigService customDimConfigService;


    List<ManagerTypeEnum> excludes = Arrays.asList(ManagerTypeEnum.SUPERADMIN, ManagerTypeEnum.MANAGEMENT);

    List<ManagerTypeEnum> adminExcludes = Arrays.asList(
            ManagerTypeEnum.CUSTOMERMANAGER, ManagerTypeEnum.CUSTOMERASSISTANT, ManagerTypeEnum.OPERATOR,
            ManagerTypeEnum.IMPLEMENTMANGER, ManagerTypeEnum.SALESMANGER
    );*/

    public BaseQueryConditionBO convertToBaseStaticQuery(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO request) throws BusinessException {
        BaseQueryCondition requestBaseQueryCondition = request.getBaseQueryCondition();
        if (requestBaseQueryCondition == null) {
            throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
        }

        // Validate dim field in extData to prevent SQL injection
        validateExtDataDimField(request);
        String startTime = requestBaseQueryCondition.getStartTime();
        String endTime = requestBaseQueryCondition.getEndTime();

        // Validate date strings to prevent SQL injection
        validateDateFields(startTime, endTime);

        BaseQueryConditionBO baseQueryConditionBO = new BaseQueryConditionBO();
        BaseQueryCondition baseQueryCondition = convert(userPermissionsBo, requestBaseQueryCondition);

        baseQueryCondition.setCorpIds(requestBaseQueryCondition.getCorpIds());
        baseQueryCondition.setAccountIds(requestBaseQueryCondition.getAccountIds());
        baseQueryCondition.setDeptList(requestBaseQueryCondition.getDeptList());
        baseQueryCondition.setCostCenterList(requestBaseQueryCondition.getCostCenterList());
        /*if (!ENV.isPRO() && StringUtils.isNotEmpty(QConfigUtils.getTestCorp())) {
            baseQueryCondition.setCorpIds(Arrays.asList(QConfigUtils.getTestCorp()));
        }*/
        baseQueryCondition.setUserCorpId(requestBaseQueryCondition.getUserCorpId());
        baseQueryCondition.setIndustryType(requestBaseQueryCondition.getIndustryType());
        //baseQueryCondition.setUid(userPermissionsBo.getUid());
        baseQueryCondition.setCityIds(requestBaseQueryCondition.getCityIds());
        baseQueryCondition.setProvinceIds(requestBaseQueryCondition.getProvinceIds());
        baseQueryCondition.setCountryIds(requestBaseQueryCondition.getCountryIds());
        baseQueryCondition.setAirLines(requestBaseQueryCondition.getAirLines());
        baseQueryCondition.setHotelGroups(requestBaseQueryCondition.getHotelGroups());
        baseQueryCondition.setStatisticalCaliber(requestBaseQueryCondition.getStatisticalCaliber());
        baseQueryCondition.setConsumptionLevel(requestBaseQueryCondition.getConsumptionLevel());
        //开始-结束时间
        if (startTime != null && endTime != null) {
            baseQueryCondition.setStartTime(startTime);
            baseQueryCondition.setEndTime(endTime);
        } else {
            log.warn(LOG_TITLE, "startTime&endTime incorrect");
            throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), "startTime&endTime incorrect");
        }
        baseQueryConditionBO.setQueryBu(request.getQueryBu());
        baseQueryConditionBO.setIndex(request.getIndex());
        baseQueryConditionBO.setQueryType(request.getQueryType());
        baseQueryConditionBO.setDateDimension(request.getDateDimension());
        baseQueryConditionBO.setExtData(request.getExtData());
        baseQueryConditionBO.setAggType(request.getAggType());
        baseQueryConditionBO.setBaseQueryCondition(baseQueryCondition);
        baseQueryConditionBO.setYoy_type(request.getYoy_type());
        baseQueryConditionBO.setAnalysisObject(request.getAnalysisObject());
        baseQueryConditionBO.setPager(request.getPager());
        baseQueryConditionBO.setSubQueryBu(request.getSubQueryBu());
        baseQueryConditionBO.setProductType(request.getProductType());

        baseQueryConditionBO.setAsc(request.isAsc());
        baseQueryConditionBO.setSortKey(request.getSortKey());
        baseQueryConditionBO.setUids(request.getUids());
        baseQueryConditionBO.setOrderids(request.getOrderids());
        baseQueryConditionBO.setRiskScene(request.getRiskScene());
        baseQueryConditionBO.setStartCityId(request.getStartCityId());
        baseQueryConditionBO.setArriveCityId(request.getArriveCityId());
        baseQueryConditionBO.setFlightCity(request.getFlightCity());
        baseQueryConditionBO.setQueryType(request.getQueryType());
        baseQueryConditionBO.setCityName(request.getCityName());
        baseQueryConditionBO.setProvinceName(request.getProvinceName());
        baseQueryConditionBO.setRiskOrderOperateBO(request.getRiskOrderOperateBO());
        baseQueryConditionBO.setOperatorStatus(request.getOperatorStatus());

        baseQueryConditionBO.setQueryBus(request.getQueryBus());
        baseQueryConditionBO.setTravelStep(request.getTravelStep());
        baseQueryConditionBO.setQueryColumn(request.getQueryColumn());
        baseQueryConditionBO.setRiskReasons(request.getRiskReasons());
        baseQueryConditionBO.setCountryName(request.getCountryName());
        baseQueryConditionBO.setTrendDimensionType(request.getTrendDimensionType());
        baseQueryConditionBO.setUserNames(request.getUserNames());
        baseQueryConditionBO.setEmployeIds(request.getEmployeIds());

        baseQueryConditionBO.setCityTypeIds(request.getCityTypeIds());
        baseQueryConditionBO.setCityIds(request.getCityIds());
        baseQueryConditionBO.setRankIds(request.getRankIds());
        //baseQueryConditionBO.setUuid(request.getUuid());
        // 差标调整建议筛选
        baseQueryConditionBO.setSuggestions(request.getSuggestions());
        baseQueryConditionBO.setIndicator(request.getIndicator());
        baseQueryConditionBO.setAnalysisObjectOrgInfo(request.getAnalysisObjectOrgInfo());
        baseQueryConditionBO.setDrillDownVal(request.getDrillDownVal());
        baseQueryConditionBO.setDrillDownObjectEnum(request.getDrillDownObjectEnum());
        baseQueryConditionBO.setRcType(request.getRcType());
        baseQueryConditionBO.setUser(request.getUser());
        baseQueryConditionBO.setAnalysisObjectVal(request.getAnalysisObjectVal());
        baseQueryConditionBO.setBudgetPanel(request.getBudgetPanel());
        baseQueryConditionBO.setRcType(request.getRcType());
        return baseQueryConditionBO;
    }

    public BaseQueryConditionBO convertToBaseOrderQuery(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO request) throws BusinessException {
        BaseQueryCondition requestBaseQueryCondition = request.getBaseQueryCondition();
        if (requestBaseQueryCondition == null) {
            throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
        }

        // Validate dim field in extData to prevent SQL injection
        validateExtDataDimField(request);

        // Validate date strings to prevent SQL injection
        validateDateFields(requestBaseQueryCondition.getStartTime(), requestBaseQueryCondition.getEndTime());

        BaseQueryConditionBO baseQueryConditionBO = new BaseQueryConditionBO();
        BaseQueryCondition baseQueryCondition = convert(userPermissionsBo, requestBaseQueryCondition);
/*        if (!ENV.isPRO() && StringUtils.isNotEmpty(QConfigUtils.getTestCorp())) {
            baseQueryCondition.setCorpIds(Arrays.asList(QConfigUtils.getTestCorp()));
        }*/
        baseQueryCondition.setUserCorpId(requestBaseQueryCondition.getUserCorpId());
        baseQueryCondition.setIndustryType(requestBaseQueryCondition.getIndustryType());
        //baseQueryCondition.setUid(userPermissionsBo.getUid());
        baseQueryCondition.setStartTime(requestBaseQueryCondition.getStartTime());
        baseQueryCondition.setEndTime(requestBaseQueryCondition.getEndTime());
        baseQueryCondition.setTimeFilterList(requestBaseQueryCondition.getTimeFilterList());
        baseQueryCondition.setCorpIds(requestBaseQueryCondition.getCorpIds());
        baseQueryConditionBO.setBaseQueryCondition(baseQueryCondition);
        baseQueryConditionBO.setQueryBu(request.getQueryBu());
        baseQueryConditionBO.setExtData(request.getExtData());
        baseQueryConditionBO.setPager(request.getPager());
        baseQueryConditionBO.setProductType(request.getProductType());
        baseQueryConditionBO.setOrderids(request.getOrderids());
        baseQueryConditionBO.setPassengers(request.getPassengers());
        baseQueryConditionBO.setUsers(request.getUsers());
        baseQueryConditionBO.setContractType(request.getContractType());
//        baseQueryConditionBO.setOrderstatusList(convertOrderStatus(request.getOrderstatusList(), request.getQueryBu()));
        baseQueryConditionBO.setOrderstatusList(request.getOrderstatusList());
        baseQueryConditionBO.setExceedStandard(request.getExceedStandard());
        baseQueryConditionBO.setReportColumns(request.getReportColumns());
        baseQueryConditionBO.setDateDimension(request.getDateDimension());
        baseQueryConditionBO.setQueryBus(request.getQueryBus());
        baseQueryConditionBO.setStar(request.getStar());
        baseQueryConditionBO.setEmployeIds(request.getEmployeIds());
        baseQueryConditionBO.setAnalysisObject(request.getAnalysisObject());
        return baseQueryConditionBO;
    }

    /**
     * 由于底表酒店明细中的order_status发生了变化由code变成name了所以要convert一下
     *
     * @param orderstatusList
     * @param queryBu
     * @return
     */
/*    protected List<String> convertOrderStatus(List<String> orderstatusList, String queryBu) {
        if (CollectionUtils.isEmpty(orderstatusList)) {
            return orderstatusList;
        }
        if (!StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.hotel.toString())) {
            return orderstatusList;
        }
        List<String> result = new ArrayList<>();
        for (String str : orderstatusList) {
            if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "P")) {
                // 处理中
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus6", LanguageEnum.ZH_CN));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "S")) {
                // 成交
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus1", LanguageEnum.ZH_CN));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "C")) {
                // 取消
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus2", LanguageEnum.ZH_CN));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "U")) {
                // 修改
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus4", LanguageEnum.ZH_CN));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "SW")) {
                // 已提交，待处理
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus5", LanguageEnum.ZH_CN));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "SP")) {
                // 已提交，处理中
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus7", LanguageEnum.ZH_CN));
            } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(str, "O")) {
                // 其他
                result.add(SharkUtils.get("RiskOrder.HtlOrderStatus3", LanguageEnum.ZH_CN));
            } else {
                result.add(str);
            }
        }
        return result;
    }*/

    private BaseQueryCondition convert(UserPermissionsBo userPermissionsBo, BaseQueryCondition requestBaseQueryCondition) throws BusinessException {
        List<String> searchCorpIdList = requestBaseQueryCondition.getCorpIds();
        List<String> searchAccountIdList = requestBaseQueryCondition.getAccountIds();
        String reportId = requestBaseQueryCondition.getReportId();
        String startTime = requestBaseQueryCondition.getStartTime();
        String endTime = requestBaseQueryCondition.getEndTime();

        // Validate date strings to prevent SQL injection
        validateDateFields(startTime, endTime);

        List<String> bookTypeList = requestBaseQueryCondition.getOrderChannel();
        BaseQueryCondition baseQueryCondition = new BaseQueryCondition();
        baseQueryCondition.setReportId(reportId);
        baseQueryCondition.setStatisticalCaliber(requestBaseQueryCondition.getStatisticalCaliber());
        //获取主账户权限集合
        Set<String> permissionMainAccountIds = new HashSet<>();
/*        if (userPermissionsBo.getCorpAccountsMap() != null) {
            userPermissionsBo.getCorpAccountsMap().forEach((k, v) -> {
                List<MainAccounts> mainAccountsList = v.getAccounts();
                if (mainAccountsList != null) {
                    List<String> tempAccountIds = mainAccountsList.stream().map(x -> x.getId()).collect(Collectors.toList());
                    permissionMainAccountIds.addAll(tempAccountIds);
                }
            });
        }*/

/*        if (excludes.contains(userPermissionsBo.getManagerType()) || excludeManagement(userPermissionsBo.getUid())) {
            baseQueryCondition.setCorpIds(userPermissionsBo.getCorpIds());
            baseQueryCondition.setAccountIds(searchAccountIdList);
        }*/
        //设置默认查询条件
/*        if (userPermissionsBo.getManagerType() == ManagerTypeEnum.GROUPMANAGER || userPermissionsBo.getManagerType() == ManagerTypeEnum.CORPMANAGER) {
            //集团管理员
            //公司管理员
            baseQueryCondition.setCorpIds(userPermissionsBo.getCorpIds());
        } else if (userPermissionsBo.getManagerType() == ManagerTypeEnum.COMMONUSER) {
            //普通用户，如果普通用户的mainAccountIds为空，则说明拥有公司下的所有主账户权限，则只要根据corpIds进行查询即可
            baseQueryCondition.setCorpIds(userPermissionsBo.getCorpIds());
            if (permissionMainAccountIds.size() > 0) {
                //主账户集合不为空，说明对主账户做了权控
                baseQueryCondition.setAccountIds(new ArrayList<>(permissionMainAccountIds));
            }
        }*/

        //如果用户搜索带入了过滤条件，则根据过滤条件来进行查询
/*        if (CollectionUtils.isNotEmpty(searchCorpIdList)) {
            //移除不在权限范围内的CorpId
            Iterator<String> corpIterator = searchCorpIdList.iterator();
            while (corpIterator.hasNext()) {
                String corpId = corpIterator.next();
                if (userPermissionsBo.getCorpIds().stream().noneMatch(i -> org.apache.commons.lang3.StringUtils.equalsIgnoreCase(corpId, i))) {
                    corpIterator.remove();
                }
            }
            baseQueryCondition.setCorpIds(searchCorpIdList);
        }*/
        if (CollectionUtils.isNotEmpty(searchAccountIdList)) {
            List<String> accountIds = new ArrayList<>();
            for (String accountId : searchAccountIdList) {
                if (permissionMainAccountIds.contains(accountId)) {
                    accountIds.add(accountId);
                }
            }
            if (CollectionUtils.isNotEmpty(accountIds)) {
                baseQueryCondition.setAccountIds(accountIds);
            }
        }
        //预订渠道
        if (CollectionUtils.isNotEmpty(bookTypeList)) {
            baseQueryCondition.setOrderChannel(bookTypeList);
        }
        //用户的查询条件全不在权限范围内，直接抛异常
/*        if (StringUtils.isEmpty(baseQueryCondition.getGroupId()) && CollectionUtils.isEmpty(baseQueryCondition.getCorpIds())
                && CollectionUtils.isEmpty(baseQueryCondition.getAccountIds())) {
            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), "User Try To Query No Permission Data");
        }*/
        //成本中心
        List<SearchDeptAndCostcneterEntity> costSearchEntityList = buildCostCenterAndDept(requestBaseQueryCondition.getCostCenterList(), Collections.emptyList());
        baseQueryCondition.setCostCenterList(costSearchEntityList);
        //部门
        List<SearchDeptAndCostcneterEntity> deptSearchEntityList = buildCostCenterAndDept(requestBaseQueryCondition.getDeptList(), Collections.emptyList());
        baseQueryCondition.setDeptList(deptSearchEntityList);
        baseQueryCondition.setDeptEntity(requestBaseQueryCondition.getDeptEntity());
        baseQueryCondition.setCostCenterEntity(requestBaseQueryCondition.getCostCenterEntity());
        return baseQueryCondition;
    }

    /**
     * @param selectList    选择的值
     * @param privilegeList 权控的值
     * @return
     */
    private static List<SearchDeptAndCostcneterEntity> buildCostCenterAndDept(List<SearchDeptAndCostcneterEntity> selectList, List<CostCenterAndDept> privilegeList) {
        if (CollectionUtils.isNotEmpty(privilegeList) && privilegeList.stream().anyMatch(i -> CollectionUtils.isNotEmpty(i.getInfo()))) {//存在权限限制
            List<SearchDeptAndCostcneterEntity> costSearchEntityList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(selectList)) {
                for (SearchDeptAndCostcneterEntity searchDeptAndCostcneterEntity : selectList) {
                    int finalI = searchDeptAndCostcneterEntity.getKey();
                    CostCenterAndDept costCenterAndDept = privilegeList.stream().filter(j -> finalI == j.getLevel()).findFirst().orElse(new CostCenterAndDept());
                    if (Objects.isNull(searchDeptAndCostcneterEntity.getWay()) || searchDeptAndCostcneterEntity.getWay() != 3) {
                        // 包含
                        if (Objects.nonNull(costCenterAndDept.getLevel())) {
                            List<String> authlist = costCenterAndDept.getInfo();
                            List<String> vals = searchDeptAndCostcneterEntity.getVals();
                            Set<String> set1 = new HashSet<>(CollectionUtils.isEmpty(authlist) ? new ArrayList<>() : authlist);
                            Set<String> set2 = new HashSet<>(CollectionUtils.isEmpty(vals) ? set1 : vals);
                            set1.retainAll(set2);
                            if (CollectionUtils.isNotEmpty(set1)) {
                                SearchDeptAndCostcneterEntity costSearchEntity = new SearchDeptAndCostcneterEntity();
                                costSearchEntity.setKey(searchDeptAndCostcneterEntity.getKey());
                                costSearchEntity.setVals(new ArrayList<>(set1));
                                costSearchEntityList.add(costSearchEntity);
                            }
                        } else {
                            costSearchEntityList.add(searchDeptAndCostcneterEntity);
                        }
                    } else {
                        // 剔除
                        SearchDeptAndCostcneterEntity costSearchEntity = new SearchDeptAndCostcneterEntity();
                        costSearchEntity.setKey(searchDeptAndCostcneterEntity.getKey());
                        costSearchEntity.setVals(searchDeptAndCostcneterEntity.getVals());
                        costSearchEntity.setWay(searchDeptAndCostcneterEntity.getWay());
                        costSearchEntity.setPermitVals(costCenterAndDept.getInfo());
                        costSearchEntityList.add(costSearchEntity);
                    }
                }
            } else {
                for (CostCenterAndDept costCenterAndDept : privilegeList) {
                    SearchDeptAndCostcneterEntity costSearchEntity = new SearchDeptAndCostcneterEntity();
                    costSearchEntity.setKey(costCenterAndDept.getLevel());
                    costSearchEntity.setVals(costCenterAndDept.getInfo());
                    costSearchEntityList.add(costSearchEntity);
                }
            }
            return costSearchEntityList;
        } else {
            return selectList;
        }
    }

    /**
     * 校验权限（需要校验resource）
     *
     * @param uid
     * @param request
     * @param lang
     * @return
     * @throws BusinessException
     */
    public UserPermissionsBo vaildPermit(String uid, BaseQueryConditionBO request, String lang) throws BusinessException {
       /* UserPermissionsBo userPermissionsBo = userPermissionService.queryUserPermissions(uid);
        if (userPermissionsBo == null) {
            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), PageErrorCodeEnum.InvalidOperation.getDescription());
        }
        if (CollectionUtils.isEmpty(userPermissionsBo.getCorpIds()) && MapUtils.isEmpty(userPermissionsBo.getCorpAccountsMap())) {
            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), SharkUtils.get(PageErrorCodeEnum.InvalidOperation.getSharkKey(), lang));
        }
        if (excludes.contains(userPermissionsBo.getManagerType()) || excludeManagement(uid)) {
            return userPermissionsBo;
        }
        if (adminExcludes.contains(userPermissionsBo.getManagerType()) && !excludeManagement(uid)) {
            throw new BusinessException(PageErrorCodeEnum.OPERATOR_ADMIN.getValue(), SharkUtils.get(PageErrorCodeEnum.OPERATOR_ADMIN.getSharkKey(), lang));
        }
        //校验是否有页面模块资源权限
        if (userPermissionsBo.getResourceKeys() == null
                || CollectionUtils.isEmpty(userPermissionsBo.getProductLines())) {
            log.warn(LOG_TITLE, String.format("%s no permit", uid));
            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), PageErrorCodeEnum.InvalidOperation.getDescription());
        }
        if (request.getBaseQueryCondition() == null) {
            throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
        }
        String reportId = request.getBaseQueryCondition().getReportId();
        try {
            List<String> resources = userPermissionsBo.getResourceKeys();
            if (!QConfigUtils.compatibleOldReport()) {
                hasResourcePermit(resources, reportId, request, uid, lang);
            } else {
                if (StringUtils.equalsIgnoreCase("ComplianceMonitor:RiskOrder", reportId)) {
                    if (!resources.contains(reportId) && !resources.contains("DetailReport:ExceptionFltTicketDetails")) {
                        log.warn(LOG_TITLE, String.format("%s no permit", uid));
                        throw new BusinessException(PageErrorCodeEnum.Unauthorized.getValue(), SharkUtils.get(PageErrorCodeEnum.Unauthorized.getSharkKey(), lang));
                    }
                } else {
                    hasResourcePermit(resources, reportId, request, uid, lang);
                }
            }
        } catch (BusinessException e) {
            if (e.getCode() == PageErrorCodeEnum.Unauthorized.getValue()) {
                List<ResourceInfo> resourceInfos = null;
                try {
                    resourceInfos = rpcCorpOnlineReportPrivilegeService.queryResourceInfoByUid(uid);
                } catch (Exception e1) {
                    throw new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                }
                // 公司的resource
                List<String> resourceKeys = Optional.ofNullable(resourceInfos).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).map(ResourceInfo::getResourceKey).collect(Collectors.toList());
                hasResourcePermit(resourceKeys, reportId, request, uid, lang);
                // 公司有权限，但是角色没有权限 抛出异常code为RoleUnauthorized
                throw new BusinessException(PageErrorCodeEnum.RoleUnauthorized.getValue(), SharkUtils.get(PageErrorCodeEnum.RoleUnauthorized.getSharkKey(), lang));
            }
            throw e;
        }
        return userPermissionsBo;*/

        return null;
    }

    /**
     * 校验权限（不用校验resource）
     *
     * @param uid
     * @param lang
     * @return
     * @throws BusinessException
     */
    public UserPermissionsBo vaildPermit(String uid, String lang) throws BusinessException {
/*        UserPermissionsBo userPermissionsBo = userPermissionService.queryUserPermissions(uid);
        if (userPermissionsBo == null) {
            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), PageErrorCodeEnum.InvalidOperation.getDescription());
        }
        if (excludes.contains(userPermissionsBo.getManagerType()) || excludeManagement(uid)) {
            return userPermissionsBo;
        }
        if (adminExcludes.contains(userPermissionsBo.getManagerType()) && !excludeManagement(uid)) {
            throw new BusinessException(PageErrorCodeEnum.OPERATOR_ADMIN.getValue(), SharkUtils.get(PageErrorCodeEnum.OPERATOR_ADMIN.getSharkKey(), lang));
        }
        return userPermissionsBo;
    }

    private void hasResourcePermit(List<String> resources, String reportId, BaseQueryConditionBO request, String uid, String lang) throws BusinessException {
        // 兼容老版的rc图表,若原来开通了RC占比分析、RC类型次数占比排行、TOP10RC排行的，任意一个，则开通RC订单分析的RC趋势和RC占比
        if (StringUtils.equalsIgnoreCase("ComplianceMonitor:RcAnalysis:RcPercent", reportId)
                || StringUtils.equalsIgnoreCase("ComplianceMonitor:RcAnalysis:RcPercent", reportId)) {
            if (!hasRcAnalysisReportPermit(request, resources) && !resources.contains("ValidateMonitor:RcTypeCountRatioRanking")
                    && !resources.contains("ValidateMonitor:RcRatio") && !resources.contains("ValidateMonitor:Top10Rc")) {
                log.warn(LOG_TITLE, String.format("%s no permit", uid));
                throw new BusinessException(PageErrorCodeEnum.Unauthorized.getValue(), SharkUtils.get(PageErrorCodeEnum.Unauthorized.getSharkKey(), lang));
            }
        } else {
            if (!resources.contains(reportId)) {
                log.warn(LOG_TITLE, String.format("%s no permit", uid));
                throw new BusinessException(PageErrorCodeEnum.Unauthorized.getValue(), SharkUtils.get(PageErrorCodeEnum.Unauthorized.getSharkKey(), lang));
            }
        }
        // 部门分析兼容老的,若有老版的权限新版则也有权限
        hasDeptAnalysisReportPermit(reportId, resources, uid, lang);*/
        return null;
    }

    /**
     * RC趋势和RC占比单独分析
     *
     * @param request
     * @param resources
     * @return
     * @throws BusinessException
     */
    private boolean hasRcAnalysisReportPermit(BaseQueryConditionBO request, List<String> resources) {
        boolean result = false;
        try {
            String reportId = request.getBaseQueryCondition().getReportId();
            if (StringUtils.equalsIgnoreCase("ComplianceMonitor:RcAnalysis:RcPercent", reportId)
                    || StringUtils.equalsIgnoreCase("ComplianceMonitor:RcAnalysis:RcTrend", reportId)) {
                result = true;
            }
        } catch (Exception e) {
            result = false;
        }
        return result;
    }

    /**
     * 部门分析兼容老的
     *
     * @param reporId
     * @param resources
     * @return
     * @throws BusinessException
     */
    private void hasDeptAnalysisReportPermit(String reporId, List<String> resources, String uid, String lang) throws BusinessException {
        if (!resources.contains(reporId)
                && !resources.contains("DetailReport:FltDeptAndCostcenterAnalysis")
                && !resources.contains("DetailReport:HtlDeptAndCostcenterAnalysis")
                && !resources.contains("DetailReport:TrainDeptAndCostcenterAnalysis")
                && !resources.contains("DetailReport:CarDeptAndCostcenterAnalysis")) {
            log.warn(LOG_TITLE, String.format("%s no permit", uid));
            throw new BusinessException(PageErrorCodeEnum.Unauthorized.getValue(), SharkUtils.get(PageErrorCodeEnum.Unauthorized.getSharkKey(), lang));
        }
    }

    private boolean excludeManagement(String uid) {
        if (StringUtils.isEmpty(uid)) {
            return false;
        }
        String management = QConfigUtils.uidOfCanViewCtripData();
        if (management.contains(uid)) {
            return true;
        }
        return false;
    }

    /**
     * Validates the dim field in extData map to prevent SQL injection
     *
     * @param request the BaseQueryConditionBO containing extData
     * @throws BusinessException if the dim field is invalid
     */
    private void validateExtDataDimField(BaseQueryConditionBO request) throws BusinessException {
        if (request != null && request.getExtData() != null) {
            Map<String, String> extData = request.getExtData();
            String dim = extData.get("dim");
            if (dim != null) {
                // Validate and replace the dim value with the validated one
                String validatedDim = SqlFieldValidator.validateDimField(dim);
                extData.put("dim", validatedDim);
            }
        }
    }

    /**
     * Validates date strings to prevent SQL injection
     *
     * @param startTime the start date string to validate
     * @param endTime the end date string to validate
     * @throws BusinessException if any date string is invalid
     */
    private void validateDateFields(String startTime, String endTime) throws BusinessException {
        if (startTime != null) {
            SqlFieldValidator.validateDateString(startTime);
        }
        if (endTime != null) {
            SqlFieldValidator.validateDateString(endTime);
        }
    }

    /**
     * 填充自定义对比参数
     *
     * @param baseQueryCondition
     * @param uid
     */
/*    public void fillCustomComparatorParam(BaseQueryCondition baseQueryCondition, String uid) {
        try {
            IndustryCustomComparatorInfoVO info = customDimConfigService.queryCustomComparatorInfo(uid);
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(info.getStdIndustry1()) || org.apache.commons.lang3.StringUtils.isNotEmpty(info.getStdIndustry2())) {
                baseQueryCondition.setIndustryType(info.getStdIndustry1() + "#" + info.getStdIndustry2());
            }
            baseQueryCondition.setCompareSameLevel(info.getCompareSameLevel());
            baseQueryCondition.setCompareCorpSameLevel(info.getCompareCorpLevel());
        } catch (Exception e) {
            log.error("ReportDataNewAdaptorService.fillCustomComparatorParam", e);
        }
    }*/
}
