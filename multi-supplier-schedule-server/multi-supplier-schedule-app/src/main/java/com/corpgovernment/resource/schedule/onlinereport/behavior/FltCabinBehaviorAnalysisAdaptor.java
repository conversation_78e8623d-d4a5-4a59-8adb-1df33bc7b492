package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FlightBehaviorInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import com.google.common.collect.ImmutableMap;
import onlinereport.enums.FlightClassTypeEnum;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc 机票仓位分析
 */
@Service
public class FltCabinBehaviorAnalysisAdaptor extends BaseReportDataAdaptor<Object> {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private FltCabinBehaviorAnalysisExcelService fltCabinBehaviorAnalysisExcelService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();

        OnlineReportBehaviorAnalysisRequest request = new OnlineReportBehaviorAnalysisRequest();
        request.setProductType(baseCondition.getProductType());
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
        extMap.put("dim", "class_type");

        request.setQueryBu(QueryReportBuTypeEnum.flight);
        request.setExtData(extMap);
        OnlineReportBehaviorAnalysisResponse responseType = corpOnlineReportPlatformService.queryBehaviorAnalysis(request);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            return flgihtBehavior(Optional.ofNullable(responseType.getFlightBehaviorList()).orElse(new ArrayList<>()), lang);
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(responseType)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
            }
            throw businessException;
        }
    }

    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        return fltCabinBehaviorAnalysisExcelService.buildExcel(baseCondition);
    }

    @Override
    public String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang) {
        String fileName_format = "%s_%s_%s_%s";
        String middle = SharkUtils.get("Save.FltClass", lang);
        String prefix = SharkUtils.get("Catalog.BehaviorAnalysis", lang);
        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
        return String.format(fileName_format, prefix, middle, uid, str);
    }

    private Object flgihtBehavior(List<FlightBehaviorInfo> flightBehaviorInfoList, String lang) {
        FlightClassTypeEnum[] fltClassTypeEnums = FlightClassTypeEnum.values();
        List quantityDataList = new ArrayList();
        for (FlightClassTypeEnum fltClassTypeEnum : fltClassTypeEnums) {
            FlightBehaviorInfo flightBehaviorInfo = flightBehaviorInfoList.stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), fltClassTypeEnum.name()))
                    .findFirst().orElse(new FlightBehaviorInfo());
            Map trendAmountQuantityData = ImmutableMap.builder()
                    .put("quantity", MapperUtils.convertDigitToZero(flightBehaviorInfo.getTotalQuantity()))
                    .put("price", MapperUtils.convertDigitToZero(flightBehaviorInfo.getTotalPrice()))
                    .put("pricePercent", MapperUtils.convertDigitToZero(flightBehaviorInfo.getPricePercent()))
                    .put("avgPrice", MapperUtils.convertDigitToZero(flightBehaviorInfo.getAvgPrice()))
                    .put("avgTpmsPrice", MapperUtils.convertDigitToZero(flightBehaviorInfo.getAvgTpmsPrice()))
                    .put("avgDiscount", MapperUtils.convertDigitToZero(flightBehaviorInfo.getAvgDiscount())).build();
            quantityDataList.add(ImmutableMap.builder()
                    .put("axis", SharkUtils.get(fltClassTypeEnum.getSharkKey(), lang))
                    .put("data", trendAmountQuantityData).build());
        }
        return quantityDataList;
    }
}
