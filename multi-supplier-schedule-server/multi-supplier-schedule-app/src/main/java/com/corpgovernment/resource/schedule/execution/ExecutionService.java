package com.corpgovernment.resource.schedule.execution;

import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.resource.core.hotelv2.domain.enums.HandleTypeEnum;
import com.corpgovernment.resource.core.hotelv2.domain.enums.TaskEnum;
import com.corpgovernment.resource.core.hotelv2.domain.enums.TaskStartModeEnum;
import com.corpgovernment.resource.core.hotelv2.dto.ExecutionPlanRunReqDto;
import com.corpgovernment.resource.core.hotelv2.dto.HandleDeadExecutionUnitReqDto;
import com.corpgovernment.resource.core.hotelv2.dto.LoopStartHotelMatchTaskReqDto;
import com.corpgovernment.resource.core.hotelv2.dto.TaskStartReqDto;
import com.corpgovernment.resource.core.hotelv2.dto.createTaskInProgressReqDto;
import com.corpgovernment.resource.core.hotelv2.soa.IExecutionClient;
import com.corpgovernment.resource.schedule.domain.execution.model.ExecutionConfig;
import com.corpgovernment.resource.schedule.domain.execution.service.IExecutionDomainService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-06 14:07
 */
@Service
@Slf4j
public class ExecutionService implements IExecutionService {

    @Resource
    private IExecutionClient executionClient;

    @Resource
    private IExecutionDomainService executionDomainService;

    @Resource(name = "submitExecutionPlanThreadPool")
    private ThreadPoolExecutor submitExecutionPlanThreadPool;

    @Override
    @BusinessBehaviorMonitor
    public void startTask() {
        // 获取配置列表
        List<ExecutionConfig> executionConfigList = executionDomainService.getExecutionConfigList();
        
        // 获取需要执行的任务配置列表
        List<ExecutionConfig.TaskConfig> needWorkTaskConfigList = executionDomainService.getNeedWorkTaskConfigList(executionConfigList);

        // 启动任务
        startTask(needWorkTaskConfigList);
    }

    @Override
    @BusinessBehaviorMonitor
    public void submitExecutionPlan() {
        // 获取配置列表
        List<ExecutionConfig> executionConfigList = executionDomainService.getExecutionConfigList();

        // 获取需要执行的执行节点配置列表
        List<ExecutionConfig.ExecutionNodeConfig> needWorkExecutionNodeConfigList = executionDomainService.getNeedWorkExecutionNodeConfigList(executionConfigList);
        if (CollectionUtils.isEmpty(needWorkExecutionNodeConfigList)) {
            return;
        }

        // 遍历执行配置列表
        for (ExecutionConfig.ExecutionNodeConfig executionNodeConfig : needWorkExecutionNodeConfigList) {
            if (executionNodeConfig == null || executionNodeConfig.getExecutionNodeEnum() == null) {
                continue;
            }

            CompletableFuture.runAsync(() -> {
                // 生成执行计划
                ExecutionPlanRunReqDto executionPlanRunReqDto = ExecutionPlanRunReqDto.builder()
                        .executionNodeConfig(ExecutionPlanRunReqDto.ExecutionNodeConfig.builder()
                                .executionNodeCode(executionNodeConfig.getExecutionNodeEnum().getCode())
                                .supplierCode(executionNodeConfig.getSupplierCode())
                                .flowRate(executionNodeConfig.getFlowRate()).build())
                        .nextExecutionNodeConfigList(buildNextExecutionNodeConfigList(executionNodeConfig.getNextExecutionNodeConfigList())).build();

                // 运行执行计划
                executionClient.runExecutePlan(executionPlanRunReqDto);
            }, submitExecutionPlanThreadPool);
        }
    }

    private List<ExecutionPlanRunReqDto.NextExecutionNodeConfig> buildNextExecutionNodeConfigList(List<ExecutionConfig.NextExecutionNodeConfig> nextExecutionNodeConfigList) {
        if (CollectionUtils.isEmpty(nextExecutionNodeConfigList)) {
            return null;
        }

        List<ExecutionPlanRunReqDto.NextExecutionNodeConfig> resultList = new ArrayList<>();
        nextExecutionNodeConfigList.forEach(nextExecutionNodeConfig -> {
            if (nextExecutionNodeConfig == null || nextExecutionNodeConfig.getExecutionNodeEnum() == null) {
                return;
            }

            resultList.add(ExecutionPlanRunReqDto.NextExecutionNodeConfig.builder()
                    .executionNodeCode(nextExecutionNodeConfig.getExecutionNodeEnum().getCode())
                    .supplierCode(nextExecutionNodeConfig.getSupplierCode())
                    .priority(nextExecutionNodeConfig.getPriority()).build());
        });
        return resultList;
    }

    @Override
    public void retryExecutionUnit() {
        executionClient.retryExecutionUnit();
    }
    
    @Override
    public void manualStartTask(String param) {
        // 获取需要执行的任务配置列表
        List<ExecutionConfig.TaskConfig> taskConfigList = executionDomainService.getTaskConfigList(param);
        
        // 启动任务
        startTask(taskConfigList);
    }
    
    @Override
    public void clearDeadExecutionUnit(String param) {
        if (StringUtils.isBlank(param)) {
            return;
        }
        
        String[] split = param.split(" ");
        if (split.length != 2) {
            return;
        }
        HandleDeadExecutionUnitReqDto handleDeadExecutionUnitReqDto = HandleDeadExecutionUnitReqDto.builder()
                .supplierCode(split[0])
                .executionCode(split[1])
                .handleType(HandleTypeEnum.CLEAR.getCode()).build();
        executionClient.handleDeadExecutionUnit(handleDeadExecutionUnitReqDto);
    }
    
    @Override
    public void resetDeadExecutionUnit(String param) {
        if (StringUtils.isBlank(param)) {
            return;
        }
        
        String[] split = param.split(" ");
        if (split.length != 2) {
            return;
        }
        HandleDeadExecutionUnitReqDto handleDeadExecutionUnitReqDto = HandleDeadExecutionUnitReqDto.builder()
                .supplierCode(split[0])
                .executionCode(split[1])
                .handleType(HandleTypeEnum.RESET.getCode()).build();
        executionClient.handleDeadExecutionUnit(handleDeadExecutionUnitReqDto);
    }
    
    @Override
    public void loopStartMatchTask(String param) {
        if (StringUtils.isBlank(param)) {
            return;
        }
        
        // 获取任务列表
        List<ExecutionConfig> executionConfigs = executionDomainService.getExecutionConfigList();
        List<LoopStartHotelMatchTaskReqDto.Task> tasks = extractTasks(executionConfigs);
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }
        
        // 解析参数
        String[] split = param.split(" ");
        if  (split.length < 2) {
            return;
        }
        
        List<String> supplierCodes = Arrays.stream(split)
                .skip(1)
                .collect(Collectors.toList());
        
        LoopStartHotelMatchTaskReqDto loopStartHotelMatchTaskReqDto = LoopStartHotelMatchTaskReqDto.builder()
                .supplierCodes(supplierCodes)
                .tasks(tasks)
                .maxIdleCount(Integer.valueOf(split[0]))
                .build();
        executionClient.loopStartMatchTask(loopStartHotelMatchTaskReqDto);
    }
    
    @Override
    public void createTaskInProgress(String param) {
        if (StringUtils.isBlank(param)) {
            return;
        }
        
        String[] items = param.split(" ");
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        
        for (String item : items) {
            // 解析参数
            String[] split = item.split("#");
            if  (split.length != 3) {
                return;
            }
            
            createTaskInProgressReqDto createTaskInProgressReqDto = new createTaskInProgressReqDto();
            createTaskInProgressReqDto.setSupplierCode(split[0]);
            createTaskInProgressReqDto.setExecutionNode(split[1]);
            createTaskInProgressReqDto.setPreStoreQuantity(Integer.valueOf(split[2]));
            executionClient.createTaskInProgress(createTaskInProgressReqDto);
        }
    }
    
    private List<LoopStartHotelMatchTaskReqDto.Task> extractTasks(List<ExecutionConfig> executionConfigList) {
        if (CollectionUtils.isEmpty(executionConfigList)) {
            return null;
        }
        
        return executionConfigList.stream()
                .filter(item -> item != null && item.getTaskConfigList() != null)
                .flatMap(item -> item.getTaskConfigList().stream())
                .filter(item -> item != null && Objects.equals(item.getTaskEnum(), TaskEnum.HOTEL_MATCH))
                .map(this::toTask)
                .collect(Collectors.toList());
    }
    
    private LoopStartHotelMatchTaskReqDto.Task toTask(ExecutionConfig.TaskConfig taskConfig) {
        if (taskConfig == null || taskConfig.getTaskEnum() == null || taskConfig.getTaskStartModeEnum() == null) {
            return null;
        }
        return LoopStartHotelMatchTaskReqDto.Task.builder()
        		.supplierCode(taskConfig.getSupplierCode())
        		.taskCode(taskConfig.getTaskEnum().getCode())
        		.taskStartModeCode(taskConfig.getTaskStartModeEnum().getCode())
        		.operation(taskConfig.getOperation())
                .priority(taskConfig.getPriority())
        		.build();
        
    }
    
    private void startTask(List<ExecutionConfig.TaskConfig> taskConfigList) {
        log.info("taskConfigList={}", JsonUtils.toJsonString(taskConfigList));
        if (CollectionUtils.isEmpty(taskConfigList)) {
            return;
        }
        
        // 遍历任务，openfeign启用任务
        for (ExecutionConfig.TaskConfig taskConfig : taskConfigList) {
            if (taskConfig == null || taskConfig.getTaskEnum() == null) {
                continue;
            }
            TaskStartReqDto taskStartReqDto = TaskStartReqDto.builder()
                    .taskCode(taskConfig.getTaskEnum().getCode())
                    .taskStartModeCode(Optional.ofNullable(taskConfig.getTaskStartModeEnum()).map(TaskStartModeEnum::getCode).orElse(null))
                    .supplierCode(taskConfig.getSupplierCode())
                    .enable(taskConfig.getEnable())
                    .operation(taskConfig.getOperation())
                    .priority(taskConfig.getPriority()).build();
            executionClient.startTask(taskStartReqDto);
        }
    }
    
}
