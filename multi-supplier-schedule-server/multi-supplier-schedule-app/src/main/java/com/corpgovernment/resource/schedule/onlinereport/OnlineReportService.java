package com.corpgovernment.resource.schedule.onlinereport;

import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class OnlineReportService {

    @Resource
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;


    public String queryBaseDataLastUpdateTime() {
        return corpOnlineReportPlatformService.queryBaseDataLastUpdateTime();
    }
}
