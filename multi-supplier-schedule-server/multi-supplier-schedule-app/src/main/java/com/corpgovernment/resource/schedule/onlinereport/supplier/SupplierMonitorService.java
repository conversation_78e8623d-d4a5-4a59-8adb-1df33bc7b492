package com.corpgovernment.resource.schedule.onlinereport.supplier;

import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.*;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.OrpGsonUtils;
import com.corpgovernment.resource.schedule.onlinereport.enums.ProductTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import onlinereport.enums.HtlCityLevelEnum;
import onlinereport.enums.HtlStarEnum;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-08-22 18:18
 * @desc
 */
@Service
public class SupplierMonitorService extends AbstractGenralExportDataService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    // 默认下载条数
    private final static int AGREEMENT_CONSUME_DETAIL_DOWNLOAD_LIMIT_DEFAULT = 20000;

    private final static String AGREEMENT_CONSUME_DETAIL_DOWNLOAD_LIMIT_KEY = "agreement_consume_detail_download_limit";

    // 默认下载条数
    private final static int SUPPLIER_DOWNLOAD_LIMIT_DEFAULT = 1000;

    private final static String SUPPLIER_DOWNLOAD_LIMIT_KEY = "supplier_detail_download_limit";

    // 下载模式
    private final static String SUPPLIER_DOWNLOAD_MODE_KEY = "supplier_download_mode";

    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String reportId = baseCondition.getBaseQueryCondition().getReportId();
        if (StringUtils.equalsIgnoreCase("SupplierMonitor:FltCompanyConsume", reportId) || StringUtils.equalsIgnoreCase("SupplierMonitor:HotelConsume", reportId)){
            String index = baseCondition.getIndex();
            OnlineReportAgreementConsumeRequest request = new OnlineReportAgreementConsumeRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setProductType(baseCondition.getProductType());
            String queryBu = baseCondition.getQueryBu();
            request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
            Map<String, String> map = Objects.isNull(baseCondition.getExtData()) ? new HashMap<>() : baseCondition.getExtData();
            request.setExtData(map);
            map.put("index", index);
            String lang = baseCondition.getLang();
            request.setLang(lang);

            OnlineReportAgreementConsumeResponse responseType = corpOnlineReportPlatformService.queryAgreementConsume(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                if (StringUtils.equalsIgnoreCase("view", index)){
                    if (Objects.isNull(responseType.getAgreementViewInfo())) {
                        throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
                    }
                    return responseType.getAgreementViewInfo();
                }
                if (StringUtils.equalsIgnoreCase("agg", index)){
                    if (Objects.isNull(responseType.getAgreementAggInfo())) {
                        throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
                    }
                    return convertAgg(queryBu, lang, Optional.ofNullable(responseType.getAgreementAggInfo()).orElse(new AgreementAggInfo()));
                }
                if (StringUtils.equalsIgnoreCase("briefing", index)){
                    if (MapUtils.isEmpty(responseType.getExtData())) {
                        throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
                    }
                    return responseType.getExtData();
                }
            } else {
                errorReturn(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        }

        if (StringUtils.equalsIgnoreCase("SupplierMonitor", reportId)){
            String index = baseCondition.getIndex();
            if (StringUtils.equalsIgnoreCase("briefing", index)) {
                OnlineReportAgreementConsumeRequest request = new OnlineReportAgreementConsumeRequest();
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(baseCondition.getProductType());
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                Map<String, String> map = Objects.isNull(baseCondition.getExtData()) ? new HashMap<>() : baseCondition.getExtData();
                request.setExtData(map);
                map.put("index", index);
                String lang = baseCondition.getLang();
                request.setLang(lang);

                OnlineReportAgreementConsumeResponse responseType = corpOnlineReportPlatformService.queryAgreementConsume(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    if (MapUtils.isEmpty(responseType.getExtData())) {
                        throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
                    }
                    return responseType.getExtData();
                } else {
                    errorReturn(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
                }
            }
        }

        if (StringUtils.equalsIgnoreCase("SupplierMonitor:AgreementDeptDetail", reportId) ||
                StringUtils.equalsIgnoreCase("SupplierMonitor:FltAgreementAirCompletion", reportId) ||
                StringUtils.equalsIgnoreCase("SupplierMonitor:HtlAgreementCompletion", reportId)) {
            String index = baseCondition.getIndex();
            if (StringUtils.equalsIgnoreCase("dept", index) || StringUtils.equalsIgnoreCase("other", index)) {
                OnlineReportAgreementDetailRequest request = new OnlineReportAgreementDetailRequest();
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(baseCondition.getProductType());
                request.setUser(baseCondition.getUser());
                if (StringUtils.equalsIgnoreCase("dept", index)) {
                    request.setAnalysisObjectEnum(AnalysisObjectEnum.valueOf(StringUtils.upperCase(baseCondition.getAnalysisObject())));
                }
                if (StringUtils.isEmpty(baseCondition.getQueryBu())) {
                    throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
                }
                String queryBu = baseCondition.getQueryBu();
                if (!StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.flight.toString()) &&
                        !StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.hotel.toString())) {
                    throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
                }
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(queryBu));
                Map<String, String> map = Objects.isNull(baseCondition.getExtData()) ? new HashMap<>() : baseCondition.getExtData();
                request.setExtData(map);
                map.put("index", index);
                String lang = baseCondition.getLang();
                request.setLang(lang);
                request.setPage(baseCondition.getPager());
                OnlineReportAgreementDetailResponse responseType = corpOnlineReportPlatformService.queryAgreementDetail(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    List<HeaderKeyValDataType> headerKeyValMaps = responseType.getHeaderData();
                    List<String> topRcList = responseType.getDeptDetail();
                    int totalRecords = Optional.ofNullable(responseType.getTotalRecords()).orElse(0);
                    List list = new ArrayList();
                    if (CollectionUtils.isNotEmpty(topRcList)) {
                        for (String detail : topRcList) {
                            Map dataMap = OrpGsonUtils.fromToJsonTypeTest(detail, Map.class);
                            list.add(dataMap);
                        }
                    }
                    List<String> sumList = responseType.getDeptSum();
                    Map sumMap = new HashMap();
                    if (CollectionUtils.isNotEmpty(sumList)) {
                        sumMap = OrpGsonUtils.fromToJsonTypeTest(sumList.get(0), Map.class);
                    }
                    return ImmutableMap.builder()
                            .put("header", headerKeyValMaps)
                            .put("list", list)
                            .put("summary", sumMap)
                            .put("totalRecords", totalRecords).build();

                } else {
                    errorReturn(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
                }
            }
        }

        if (StringUtils.equalsIgnoreCase("SupplierMonitor:Top10FltCompanyConsume", reportId) || StringUtils.equalsIgnoreCase("SupplierMonitor:Top10FltLineConsume", reportId) ||
                StringUtils.equalsIgnoreCase("SupplierMonitor:Top10HotelConsume", reportId) || StringUtils.equalsIgnoreCase("SupplierMonitor:Top10HotelCityConsume", reportId)){
           return getNewHotAnalysisData(baseCondition);
        }
        return null;
    }

    private Map convertAgg(String queryBu, String lang, AgreementAggInfo agreementAggInfo){
        // 三方协议
        Map flightLegend1 = ImmutableMap.builder().put("key", "Ta").put("name", SharkUtils.get("SupplierMonitor.ThreeParty", lang)).build();
        // 非三方协议
        Map flightLegend2 = ImmutableMap.builder().put("key", "Nta").put("unit", "dom").put("name", SharkUtils.get("SupplierMonitor.NotThreeParty", lang)).build();
        // 国内三方协议
        Map flightLegend3 = ImmutableMap.builder().put("key", "DomTa").put("unit", "dom").put("name", SharkUtils.get("SupplierMonitor.ThreePartyChina", lang)).build();
        // 国际三方协议
        Map flightLegend5 = ImmutableMap.builder().put("key", "InterTa").put("unit", "dom").put("name", SharkUtils.get("SupplierMonitor.ThreePartyInt", lang)).build();
        // 国内非三方协议
        Map flightLegend4 = ImmutableMap.builder().put("key", "DomNta").put("unit", "dom").put("name", SharkUtils.get("SupplierMonitor.NotThreePartyChina", lang)).build();
        // 国际非三方协议
        Map flightLegend6 = ImmutableMap.builder().put("key", "InterNta").put("unit", "dom").put("name", SharkUtils.get("SupplierMonitor.NotThreePartyInt", lang)).build();

        // 三方协议
        Map hotelLegend1 = ImmutableMap.builder().put("key", "Ta").put("name", SharkUtils.get("SupplierMonitor.ThreeParty", lang)).build();
        // 非三方协议
        Map hotelLegend2 = ImmutableMap.builder().put("key", "Nta").put("name", SharkUtils.get("SupplierMonitor.NotThreeParty", lang)).build();
        // 国内三方协议
        Map hotelLegend3 = ImmutableMap.builder().put("key", "DomTa").put("name", SharkUtils.get("SupplierMonitor.ThreePartyChina", lang)).build();
        // 国际三方协议
        Map hotelLegend5 = ImmutableMap.builder().put("key", "InterTa").put("name", SharkUtils.get("SupplierMonitor.ThreePartyInt", lang)).build();
        // 国内非三方协议
        Map hotelLegend4 = ImmutableMap.builder().put("key", "DomNta").put("name", SharkUtils.get("SupplierMonitor.NotThreePartyChina", lang)).build();
        // 国际非三方协议
        Map hotelLegend6 = ImmutableMap.builder().put("key", "InterNta").put("name", SharkUtils.get("SupplierMonitor.NotThreePartyInt", lang)).build();
        Map data1Item = ImmutableMap.builder()
                .put("DomNta", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalPriceDomNta()))
                .put("DomTa", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalPriceDomTa()))
                .put("InterNta", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalPriceInterNta()))
                .put("InterTa", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalPriceInterTa()))
                .put("Nta", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalPriceNta()))
                .put("Ta", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalPriceTa())).build();
        List legend = null;
        if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.flight.toString())){
            legend = Arrays.asList(flightLegend1,flightLegend2,flightLegend3,flightLegend4,flightLegend5,flightLegend6);
        }
        if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.hotel.toString())){
            legend = Arrays.asList(hotelLegend1,hotelLegend2,hotelLegend3,hotelLegend4,hotelLegend5,hotelLegend6);
        }
        Map data1 = ImmutableMap.builder()
                .put("legend", legend)
                .put("data", data1Item).build();

        Map data2Item = ImmutableMap.builder()
                .put("DomNta", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalQuantityDomNta()))
                .put("DomTa", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalQuantityDomTa()))
                .put("InterNta", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalQuantityInterNta()))
                .put("InterTa", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalQuantityInterTa()))
                .put("Nta", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalQuantityNta()))
                .put("Ta", MapperUtils.convertDigitToZero(agreementAggInfo.getTotalQuantityTa())).build();
        Map data2 = ImmutableMap.builder()
                .put("legend", legend)
                .put("data", data2Item).build();

        return ImmutableMap.builder()
                .put("numberOfTrips", data2)
                .put("amount", data1).build();
    }

    public List<ChartExcelEntity> buildExcel(BaseQueryConditionBO baseCondition, String uid) throws BusinessException {
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        ProductTypeEnum[] productTypeEnums = ProductTypeEnum.values();
        String reportId = baseCondition.getBaseQueryCondition().getReportId();
        String index = baseCondition.getIndex();
        if (StringUtils.equalsIgnoreCase("SupplierMonitor:FltCompanyConsume", reportId)){
            if (StringUtils.equalsIgnoreCase("view", index)){
                for (ProductTypeEnum productTypeEnum : productTypeEnums){
                    chartExcelEntityList.add(agreementViewExcel(productTypeEnum, baseCondition, uid));
                }
            }
            if (StringUtils.equalsIgnoreCase("agg", index)){
                chartExcelEntityList.add(agreementViewExcel(ProductTypeEnum.ALL, baseCondition, uid));
            }
        }
        if (StringUtils.equalsIgnoreCase("SupplierMonitor:HotelConsume", reportId)){
            if (StringUtils.equalsIgnoreCase("view", index)){
                for (ProductTypeEnum productTypeEnum : productTypeEnums){
                    chartExcelEntityList.add(agreementViewExcel(productTypeEnum, baseCondition, uid));
                }
            }
            if (StringUtils.equalsIgnoreCase("agg", index)){
                chartExcelEntityList.add(agreementViewExcel(ProductTypeEnum.ALL, baseCondition, uid));
            }
        }
        if (StringUtils.equalsIgnoreCase("SupplierMonitor:AgreementDeptDetail", reportId) ||
                StringUtils.equalsIgnoreCase("SupplierMonitor:FltAgreementAirCompletion", reportId) ||
                StringUtils.equalsIgnoreCase("SupplierMonitor:HtlAgreementCompletion", reportId)){
            for (ProductTypeEnum productTypeEnum : productTypeEnums){
                chartExcelEntityList.add(agreementDetailExcel(productTypeEnum, baseCondition, uid));
            }
        }

        if (StringUtils.equalsIgnoreCase("SupplierMonitor:Top10FltCompanyConsume", reportId) || StringUtils.equalsIgnoreCase("SupplierMonitor:Top10FltLineConsume", reportId) ||
                StringUtils.equalsIgnoreCase("SupplierMonitor:Top10HotelConsume", reportId) || StringUtils.equalsIgnoreCase("SupplierMonitor:Top10HotelCityConsume", reportId)){
            return getNewHotAnalysisDataExcel(baseCondition, uid);
        }
        return chartExcelEntityList;
    }

    private ChartExcelEntity agreementViewExcel(ProductTypeEnum productTypeEnum, BaseQueryConditionBO baseCondition, String uid){
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        String index = baseCondition.getIndex();
        OnlineReportAgreementConsumeRequest request = new OnlineReportAgreementConsumeRequest();
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setLang(baseCondition.getLang());
        request.setProductType(baseCondition.getProductType());
        QueryReportBuTypeEnum reportBuTypeEnum = QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu());
        request.setQueryBu(reportBuTypeEnum);
        Map<String, String> map = Objects.isNull(baseCondition.getExtData()) ? new HashMap<>() : baseCondition.getExtData();
        request.setExtData(map);
        map.put("index", index);
        String lang = baseCondition.getLang();
        request.setLang(lang);
        request.setProductType(productTypeEnum.toString());
        OnlineReportAgreementConsumeResponse responseType = corpOnlineReportPlatformService.queryAgreementConsume(request);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            Map dataRangeMap = getConditionContent(uid, baseCondition.getBaseQueryCondition(), lang);
            List data = new ArrayList();
            if (StringUtils.equalsIgnoreCase("view", index)){
                AgreementViewInfo agreementViewInfo = Optional.ofNullable(responseType.getAgreementViewInfo()).orElse(new AgreementViewInfo());
                List<AgreementConsumeInfo> list = Optional.ofNullable(agreementViewInfo.getList()).orElse(new ArrayList<>());
                AgreementConsumeInfo agreementConsumeInfo1 = list.stream().filter(i->StringUtils.equalsIgnoreCase(i.getDataType(), "all")).findFirst().orElse(new AgreementConsumeInfo());

                if (reportBuTypeEnum == QueryReportBuTypeEnum.flight){
                    sheet1.setHeaders(Arrays.asList(StringUtils.EMPTY,
                            SharkUtils.get("Index.netprice", lang),
                            SharkUtils.get("Index.num", lang),
                            SharkUtils.get("Overview.FlightAvgTktPrice", lang)));
                   data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.FltAll", lang),
                            MapperUtils.convertDigitToZeroString(agreementConsumeInfo1.getTotalPrice()),
                            MapperUtils.convertDigitToZeroString(agreementConsumeInfo1.getTotalQuantity()),
                            MapperUtils.convertDigitToZeroString(agreementConsumeInfo1.getAvgPrice())));
                }
                if (reportBuTypeEnum == QueryReportBuTypeEnum.hotel){
                    sheet1.setHeaders(Arrays.asList(StringUtils.EMPTY,
                            SharkUtils.get("Index.costmoney", lang),
                            SharkUtils.get("Index.nightnum", lang),
                            SharkUtils.get("Overview.HotelAvgNightPrice", lang)));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.HtlAll", lang),
                            MapperUtils.convertDigitToZeroString(agreementConsumeInfo1.getTotalPrice()),
                            MapperUtils.convertDigitToZeroString(agreementConsumeInfo1.getTotalQuantity()),
                            MapperUtils.convertDigitToZeroString(agreementConsumeInfo1.getAvgPrice())));
                }
                AgreementConsumeInfo agreementConsumeInfo2 = list.stream().filter(i->StringUtils.equalsIgnoreCase(i.getDataType(), "ta"))
                        .findFirst().orElse(new AgreementConsumeInfo());
                data.add(Arrays.asList(SharkUtils.get("Save.Agree", lang),
                        MapperUtils.convertDigitToZeroString(agreementConsumeInfo2.getTotalPrice()),
                        MapperUtils.convertDigitToZeroString(agreementConsumeInfo2.getTotalQuantity()),
                        MapperUtils.convertDigitToZeroString(agreementConsumeInfo2.getAvgPrice())));
                AgreementConsumeInfo agreementConsumeInfo3 = list.stream().filter(i->StringUtils.equalsIgnoreCase(i.getDataType(), "nta"))
                        .findFirst().orElse(new AgreementConsumeInfo());
                data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.NotThreeParty", lang),
                        MapperUtils.convertDigitToZeroString(agreementConsumeInfo3.getTotalPrice()),
                        MapperUtils.convertDigitToZeroString(agreementConsumeInfo3.getTotalQuantity()),
                        MapperUtils.convertDigitToZeroString(agreementConsumeInfo3.getAvgPrice())));
            }
            if (StringUtils.equalsIgnoreCase("agg", index)){
                if (reportBuTypeEnum == QueryReportBuTypeEnum.flight){
                    sheet1.setHeaders(Arrays.asList(StringUtils.EMPTY,
                            SharkUtils.get("Index.num", lang),
                            SharkUtils.get("Index.costmoney", lang)));
                    AgreementAggInfo agreementAggInfo = Optional.ofNullable(responseType.getAgreementAggInfo()).orElse(new AgreementAggInfo());
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.ThreeParty", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityTa())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceTa())));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.NotThreeParty", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityNta())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceNta())));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.ThreePartyChina", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityDomTa())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceDomTa())));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.ThreePartyInt", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityInterTa())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceInterTa())));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.NotThreePartyChina", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityDomNta())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceDomNta())));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.NotThreePartyInt", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityInterNta())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceInterNta())));
                }
                if (reportBuTypeEnum == QueryReportBuTypeEnum.hotel){
                    sheet1.setHeaders(Arrays.asList(StringUtils.EMPTY,
                            SharkUtils.get("Index.nightnum", lang),
                            SharkUtils.get("Index.costmoney", lang)));
                    AgreementAggInfo agreementAggInfo = Optional.ofNullable(responseType.getAgreementAggInfo()).orElse(new AgreementAggInfo());
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.ThreeParty", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityTa())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceTa())));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.NotThreeParty", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityNta())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceNta())));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.ThreePartyChina", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityDomTa())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceDomTa())));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.ThreePartyInt", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityInterTa())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceInterTa())));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.NotThreePartyChina", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityDomNta())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceDomNta())));
                    data.add(Arrays.asList(SharkUtils.get("SupplierMonitor.NotThreePartyInt", lang), MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalQuantityInterNta())
                            , MapperUtils.convertDigitToZeroString(agreementAggInfo.getTotalPriceInterNta())));
                }

            }
            data.addAll(buttonContent(lang, dataRangeMap));
            sheet1.setSheetTitle(getSheetNameByProductType(lang, productTypeEnum.toString()));
            sheet1.setSheetNum(productTypeEnum.ordinal());
            sheet1.setData(data);
        }
        return sheet1;
    }

    private ChartExcelEntity agreementDetailExcel(ProductTypeEnum productTypeEnum, BaseQueryConditionBO baseCondition, String uid) throws BusinessException {
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        String index = baseCondition.getIndex();
        if (StringUtils.equalsIgnoreCase("dept", index) || StringUtils.equalsIgnoreCase("other", index)) {
            OnlineReportAgreementDetailRequest request = new OnlineReportAgreementDetailRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setProductType(baseCondition.getProductType());
            request.setUser(baseCondition.getUser());
            if (StringUtils.equalsIgnoreCase("dept", index)) {
                request.setAnalysisObjectEnum(AnalysisObjectEnum.valueOf(StringUtils.upperCase(baseCondition.getAnalysisObject())));
            }
            if (StringUtils.isEmpty(baseCondition.getQueryBu())) {
                throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            }
            String queryBu = baseCondition.getQueryBu();
            if (!StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.flight.toString()) && !StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.hotel.toString())) {
                throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            }
            request.setQueryBu(QueryReportBuTypeEnum.valueOf(queryBu));
            Map<String, String> map = Objects.isNull(baseCondition.getExtData()) ? new HashMap<>() : baseCondition.getExtData();
            request.setExtData(map);
            map.put("index", index);
            String lang = baseCondition.getLang();
            request.setLang(lang);
            int downloadLimt = QConfigUtils.getNullDefaultInterValue(AGREEMENT_CONSUME_DETAIL_DOWNLOAD_LIMIT_KEY, AGREEMENT_CONSUME_DETAIL_DOWNLOAD_LIMIT_DEFAULT);
            Pager pager = new Pager(0l, downloadLimt, 1, 0);
            request.setPage(pager);
            request.setProductType(productTypeEnum.toString());
            OnlineReportAgreementDetailResponse responseType = corpOnlineReportPlatformService.queryAgreementDetail(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                Map dataRangeMap = getConditionContent(uid, baseCondition.getBaseQueryCondition(), lang);

                List<String> deptCousumeList = responseType.getDeptDetail();
                List<HeaderKeyValDataType> headerMap = responseType.getHeaderData();
                List<List<String>> data = new ArrayList();
                List headerDesc = headerMap.stream().map(i -> i.getHeaderValue()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(deptCousumeList)) {
                    for (String detail : deptCousumeList) {
                        Map temp = OrpGsonUtils.fromToJsonTypeTest(detail, Map.class);
                        List list = new ArrayList();
                        for (HeaderKeyValDataType valMap : headerMap) {
                            list.add(temp.get(valMap.getHeaderKey()));
                        }
                        data.add(list);
                    }
                }
                List<String> deptSumList = responseType.getDeptSum();
                if (CollectionUtils.isNotEmpty(deptSumList)) {
                    Map temp = OrpGsonUtils.fromToJsonTypeTest(deptSumList.get(0), Map.class);
                    List list = new ArrayList();
                    for (HeaderKeyValDataType valMap : headerMap) {
                        list.add(temp.get(valMap.getHeaderKey()));
                    }
                    data.add(list);
                }
                sheet1.setHeaders(headerDesc);
                if (StringUtils.equalsIgnoreCase("dept", index)){
                    data.addAll(buttonContent(lang, dataRangeMap, request.getAnalysisObjectEnum()));
                }else {
                    data.addAll(buttonContent(lang, dataRangeMap));
                }
                sheet1.setData(data);
                sheet1.setSheetNum(productTypeEnum.ordinal());
                sheet1.setSheetTitle(getSheetNameByProductType(lang, productTypeEnum.toString()));
            }
        }
        return sheet1;
    }

    private void errorReturn(Object response, Integer responseCode, String msg) throws BusinessException {
        BusinessException businessException;
        if (Objects.isNull(response)) {
            businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
        } else {
            businessException = new BusinessException(responseCode, msg);
        }
        throw businessException;
    }


    /**
     * 分析对象
     *
     * @param lang
     * @param analysisObjectEnum
     * @return
     */
    protected List getAnalysisObjectContent(String lang, AnalysisObjectEnum analysisObjectEnum) {
        return Arrays.asList(Arrays.asList(SharkUtils.get("Index.AnalyseObject", lang), getAnalysisObjectName(analysisObjectEnum, lang)));
    }


    public Object getNewHotAnalysisData(BaseQueryConditionBO baseCondition) throws BusinessException {
        OnlineReportSupplierTrendRequest request = new OnlineReportSupplierTrendRequest();
        request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
        request.setLang(baseCondition.getLang());
//        request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
        request.setEId(baseCondition.getBaseQueryCondition().getUid());
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        String dim = baseCondition.getExtData().getOrDefault("dim", "");
        dimEn(baseCondition.getLang(), baseCondition.getExtData(), request.getQueryBu());
        request.setExtData(baseCondition.getExtData());
        request.setProductType(baseCondition.getProductType());
        OnlineReportSupplierTrendResponse response = corpOnlineReportPlatformService.querySupplierTrend(request);
        if (response != null && response.getResponseCode() == 20000) {
            if (CollectionUtils.isEmpty(response.getTrends())) {
//                throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
                return ImmutableMap.builder()
                        .put("data", Lists.newArrayList())
                        .put("legends", Lists.newArrayList()).build();
            }
            return postHandleNew(baseCondition, response.getTrends(), QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()), dim);
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(response)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
            }
            throw businessException;
        }
    }

    public Object postHandleNew(BaseQueryConditionBO baseCondition, List<OnlineReportSupplierTrendInfo> trends
            , QueryReportBuTypeEnum queryReportBuTypeEnum, String dim) {
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight){
            List legends = new ArrayList();
            FltEnum[] fltEnums;
            if (StringUtils.equalsIgnoreCase("SupplierMonitor:Top10FltCompanyConsume",
                    baseCondition.getBaseQueryCondition().getReportId())) {
                fltEnums = FltEnum.values();
            } else {
                fltEnums = FltEnum.buildArrayExcludeEstimateSaveAmount();
            }
            for (FltEnum fltEnum : fltEnums){
                Map legendMap = new HashMap();
                legendMap.put("key",fltEnum.toString());
                legendMap.put("name",SharkUtils.get(fltEnum.getSharkkey(), baseCondition.getLang()));
                legends.add(legendMap);
            }
            List<Field> fields = Arrays.stream(OnlineReportSupplierTrendInfo.class.getDeclaredFields())
                    .filter(f -> !(f.getName().equals("dim")))
                    .collect(Collectors.toList());
            List dataList = new ArrayList();
            for (OnlineReportSupplierTrendInfo trendInfo : trends){
                Map dataMap = new HashMap();
                Map data = new HashMap();
                fields.forEach(f -> {
                    if (Arrays.stream(fltEnums).anyMatch(i-> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.toString(), f.getName()))){
                        dataMap.put(f.getName(), field2Value(trendInfo, f));
                    }
                });
                data.put("axis", trendInfo.getDim());
                data.put("data", dataMap);
                dataList.add(data);
            }
            return ImmutableMap.builder()
                    .put("data", dataList)
                    .put("legends", legends).build();
        }

        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel){
            List legends = new ArrayList();
            HtlEnum[] htlEnums = HtlEnum.values();
            for (HtlEnum htlEnum : htlEnums){
                Map legendMap = new HashMap();
                legendMap.put("key",htlEnum.toString());
                legendMap.put("name",SharkUtils.get(htlEnum.getSharkkey(), baseCondition.getLang()));
                legends.add(legendMap);
            }
            List<Field> fields = Arrays.stream(OnlineReportSupplierTrendInfo.class.getDeclaredFields())
                    .filter(f -> !(f.getName().equals("dim")))
                    .collect(Collectors.toList());
            List dataList = new ArrayList();
            // 星级
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase("star", dim)){
                HtlStarEnum[] htlStarEnums = HtlStarEnum.values();
                for (HtlStarEnum htlStarEnum : htlStarEnums) {
                    OnlineReportSupplierTrendInfo trendInfo = trends.stream()
                            .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getDim(), String.valueOf(htlStarEnum.getStar()))
                                    || (htlStarEnum == HtlStarEnum.OTHER && org.apache.commons.lang.StringUtils.isEmpty(i.getDim())))
                            .findFirst().orElse(new OnlineReportSupplierTrendInfo());
                    Map dataMap = new HashMap();
                    Map data = new HashMap();
                    fields.forEach(f -> {
                        if (Arrays.stream(htlEnums).anyMatch(i-> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.toString(), f.getName()))){
                            dataMap.put(f.getName(), field2Value(trendInfo, f));
                        }
                    });
                    data.put("axis", SharkUtils.get(htlStarEnum.getSharkKey(), baseCondition.getLang()));
                    data.put("data", dataMap);
                    dataList.add(data);
                }
            }else if (org.apache.commons.lang.StringUtils.equalsIgnoreCase("hotel_city_level", dim)){// 城市等级
                HtlCityLevelEnum[] cityLevelEnums = HtlCityLevelEnum.values();
                for (HtlCityLevelEnum htlCityLevelEnum : cityLevelEnums) {
                    OnlineReportSupplierTrendInfo trendInfo = trends.stream()
                            .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getDim(), String.valueOf(htlCityLevelEnum.getKey()))
                                    || (htlCityLevelEnum == HtlCityLevelEnum.OTHER_LEVEL && org.apache.commons.lang.StringUtils.isEmpty(i.getDim())))
                            .findFirst().orElse(new OnlineReportSupplierTrendInfo());
                    Map dataMap = new HashMap();
                    Map data = new HashMap();
                    fields.forEach(f -> {
                        if (Arrays.stream(htlEnums).anyMatch(i-> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.toString(), f.getName()))){
                            dataMap.put(f.getName(), field2Value(trendInfo, f));
                        }
                    });
                    data.put("axis", SharkUtils.get(htlCityLevelEnum.getSharkKey(), baseCondition.getLang()));
                    data.put("data", dataMap);
                    dataList.add(data);
                }
            }else {
                for (OnlineReportSupplierTrendInfo trendInfo : trends){
                    Map dataMap = new HashMap();
                    Map data = new HashMap();
                    fields.forEach(f -> {
                        if (Arrays.stream(htlEnums).anyMatch(i-> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.toString(), f.getName()))){
                            dataMap.put(f.getName(), field2Value(trendInfo, f));
                        }
                    });
                    data.put("axis", trendInfo.getDim());
                    data.put("data", dataMap);
                    dataList.add(data);
                }
            }
            return ImmutableMap.builder()
                    .put("data", dataList)
                    .put("legends", legends).build();
        }

        return null;
    }

    public Object field2Value(Object target, Field filed) {
        Object o = null;
        try {
            o =  filed.get(target);
            filed.setAccessible(true);
            if (o == null){
                Class clazz = filed.getType();
                if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(clazz.getSimpleName(), BigDecimal.class.getSimpleName())){
                    o = new BigDecimal("0.0");
                }else if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(clazz.getSimpleName(), Integer.class.getSimpleName())){
                    o = 0;
                }else if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(clazz.getSimpleName(), String.class.getSimpleName())){
                    o = org.apache.commons.lang.StringUtils.EMPTY;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return o;
    }

    public List<ChartExcelEntity> getNewHotAnalysisDataExcel(BaseQueryConditionBO baseCondition, String uid) throws BusinessException {
        List<ChartExcelEntity> result = new ArrayList<>();
        OnlineReportSupplierTrendRequest request = new OnlineReportSupplierTrendRequest();
        request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
        request.setLang(baseCondition.getLang());
        request.setEId(baseCondition.getBaseQueryCondition().getUid());
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        String dim = baseCondition.getExtData().getOrDefault("dim", "");
        dimEn(baseCondition.getLang(), baseCondition.getExtData(), request.getQueryBu());
        request.setExtData(baseCondition.getExtData());
        // 下载标志，download为T的时候查询接口不受数量限制
        request.getExtData().put("download", "T");
        int downloadLimt = QConfigUtils.getNullDefaultInterValue(SUPPLIER_DOWNLOAD_LIMIT_KEY, SUPPLIER_DOWNLOAD_LIMIT_DEFAULT);
        request.setTopLimit(downloadLimt);
        String downloadMode = QConfigUtils.getNullDefaultValue(SUPPLIER_DOWNLOAD_MODE_KEY, SupplierDownloadMode.SINGLE.toString());
        if (StringUtils.equalsIgnoreCase(SupplierDownloadMode.SINGLE.toString(), downloadMode)) {
            ProductTypeEnum productTypeEnum = ProductTypeEnum.valueOf(StringUtils.upperCase(baseCondition.getProductType()));
            request.setProductType(productTypeEnum.toString());
            OnlineReportSupplierTrendResponse response = corpOnlineReportPlatformService.querySupplierTrend(request);
            if (response != null && response.getResponseCode() == 20000) {
                result.add(postHandleNewExcel(baseCondition, response.getTrends(), dim, uid, productTypeEnum, 0));
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(response)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
                }
                throw businessException;
            }
        }else {
            ProductTypeEnum[] productTypeEnums = ProductTypeEnum.values();
            for (ProductTypeEnum productTypeEnum : productTypeEnums){
                request.setProductType(productTypeEnum.toString());
                OnlineReportSupplierTrendResponse response = corpOnlineReportPlatformService.querySupplierTrend(request);
                if (response != null && response.getResponseCode() == 20000) {
                    result.add(postHandleNewExcel(baseCondition, response.getTrends(), dim, uid, productTypeEnum, productTypeEnum.ordinal()));
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(response)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
                    }
                    throw businessException;
                }
            }
        }
        return result;
    }

    public ChartExcelEntity postHandleNewExcel(BaseQueryConditionBO baseCondition, List<OnlineReportSupplierTrendInfo> trends, String dim,
                                               String uid, ProductTypeEnum productTypeEnum, int sheetIndex) {
        String lang = baseCondition.getLang();
        QueryReportBuTypeEnum queryReportBuTypeEnum = QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu());
        Map dataRangeMap = getConditionContent(uid, baseCondition.getBaseQueryCondition(), lang);

        ChartExcelEntity sheet1 = new ChartExcelEntity();
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight){
            // 航司、航线
            if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(dim, "airline_name") || org.apache.commons.lang.StringUtils.equalsIgnoreCase(dim, "flight_city")){
                    List excelData = new ArrayList();
                    // 合并标题，数据格式(包含单元格行数:单元格内容)
                    List<String> rangeHeaders = null;
                    if (productTypeEnum == ProductTypeEnum.DOM){
                        rangeHeaders = Arrays.asList("1:", String.format("7:%s", SharkUtils.get("Public.all", lang)),
                                String.format("7:%s", SharkUtils.get("SupplierMonitor.ThreeParty", lang)),
                                String.format("7:%s", SharkUtils.get("SupplierMonitor.NotThreeParty", lang)));
                        sheet1.setHeaders(Arrays.asList(getExcelFirstTitle(lang, dim, queryReportBuTypeEnum),
                                SharkUtils.get("Index.netprice", lang), SharkUtils.get("Index.num", lang), SharkUtils.get("Overview.FlightAvgTktPrice", lang),
                                SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang),
                                SharkUtils.get("Exceltopname.avgmilprice", lang), SharkUtils.get("Index.avgdiscount", lang),
                                SharkUtils.get("Index.netprice", lang), SharkUtils.get("Index.num", lang), SharkUtils.get("Overview.FlightAvgTktPrice", lang),
                                SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang),
                                SharkUtils.get("Exceltopname.avgmilprice", lang), SharkUtils.get("Index.avgdiscount", lang),
                                SharkUtils.get("Index.netprice", lang), SharkUtils.get("Index.num", lang), SharkUtils.get("Overview.FlightAvgTktPrice", lang),
                                SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang),
                                SharkUtils.get("Exceltopname.avgmilprice", lang), SharkUtils.get("Index.avgdiscount", lang)
                        ));
                        for (OnlineReportSupplierTrendInfo trendInfo : trends){
                            excelData.add(Arrays.asList(trendInfo.getDim() + (Objects.nonNull(trendInfo.getAgreementTag()) && trendInfo.getAgreementTag() == 1 ?
                                            String.format("(%s)", SharkUtils.get("Index.pact", lang)) : org.apache.commons.lang.StringUtils.EMPTY),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumPrice()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantity()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgPrice()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPrice()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPrice()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgTpmsPrice()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgDiscount())
                                    ,
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumPriceTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPriceTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPriceTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgTpmsPriceTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgDiscountTa())
                                    ,
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumPriceNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPriceNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPriceNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgTpmsPriceNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgDiscountNta())));
                        }
                    }else {
                        rangeHeaders = Arrays.asList("1:", String.format("6:%s", SharkUtils.get("Public.all", lang)),
                                String.format("6:%s", SharkUtils.get("SupplierMonitor.ThreeParty", lang)),
                                String.format("6:%s", SharkUtils.get("SupplierMonitor.NotThreeParty", lang)));
                        sheet1.setHeaders(Arrays.asList(getExcelFirstTitle(lang, dim, queryReportBuTypeEnum),
                                SharkUtils.get("Index.netprice", lang), SharkUtils.get("Index.num", lang), SharkUtils.get("Overview.FlightAvgTktPrice", lang),
                                SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang),
                                SharkUtils.get("Exceltopname.avgmilprice", lang),
                                SharkUtils.get("Index.netprice", lang), SharkUtils.get("Index.num", lang), SharkUtils.get("Overview.FlightAvgTktPrice", lang),
                                SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang),
                                SharkUtils.get("Exceltopname.avgmilprice", lang),
                                SharkUtils.get("Index.netprice", lang), SharkUtils.get("Index.num", lang), SharkUtils.get("Overview.FlightAvgTktPrice", lang),
                                SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang),
                                SharkUtils.get("Exceltopname.avgmilprice", lang)
                        ));
                        for (OnlineReportSupplierTrendInfo trendInfo : trends){
                            excelData.add(Arrays.asList(trendInfo.getDim()+ (Objects.nonNull(trendInfo.getAgreementTag()) && trendInfo.getAgreementTag() == 1 ?
                                            String.format("(%s)", SharkUtils.get("Index.pact", lang)) : org.apache.commons.lang.StringUtils.EMPTY),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumPrice()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantity()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgPrice()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPrice()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPrice()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgTpmsPrice())
                                    ,
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumPriceTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPriceTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPriceTa()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgTpmsPriceTa())
                                    ,
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumPriceNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPriceNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPriceNta()),
                                    MapperUtils.convertDigitToZeroString(trendInfo.getAvgTpmsPriceNta())));
                        }
                    }

                    excelData.addAll(buttonContent(lang, dataRangeMap));
                    sheet1.setSheetTitle(getSheetNameByProductType(lang, productTypeEnum.toString()));
                    sheet1.setSheetNum(sheetIndex);
                    sheet1.setData(excelData);
                    sheet1.setRangeHeaders(rangeHeaders);
                }
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel){
            List excelData = new ArrayList();
            sheet1.setRangeHeaders(Arrays.asList("1:", String.format("7:%s", SharkUtils.get("Public.all", lang)),
                    String.format("7:%s", SharkUtils.get("SupplierMonitor.ThreeParty", lang)),
                    String.format("7:%s", SharkUtils.get("SupplierMonitor.NotThreeParty", lang))));
            sheet1.setHeaders(Arrays.asList(getExcelFirstTitle(lang, dim, queryReportBuTypeEnum),
                    SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang), SharkUtils.get("Index.nightnum", lang),
                    SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                    SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang),
                    SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang), SharkUtils.get("Index.nightnum", lang),
                    SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                    SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang),
                    SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang), SharkUtils.get("Index.nightnum", lang),
                    SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                    SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang)
            ));
            for (OnlineReportSupplierTrendInfo trendInfo : trends){
                excelData.add(Arrays.asList(org.apache.commons.lang.StringUtils.equalsIgnoreCase("agreement_mgrgroup_name", dim) ?
                                (MapperUtils.trim(trendInfo.getDim()) + (Objects.nonNull(trendInfo.getAgreementTag()) && trendInfo.getAgreementTag() == 1 ?
                                        String.format("(%s)", SharkUtils.get("Index.pact", lang)) : org.apache.commons.lang.StringUtils.EMPTY)) : MapperUtils.trim(trendInfo.getDim()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getSumAmount()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getAmountPercent()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantity()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getQuantityPercent()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getAvgPrice()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPrice()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPrice())
                        ,
                        MapperUtils.convertDigitToZeroString(trendInfo.getSumAmountTa()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getAmountTaPercent()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityTa()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getQuantityTaPercent()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceTa()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPriceTa()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPriceTa())
                        ,
                        MapperUtils.convertDigitToZeroString(trendInfo.getSumAmountNta()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getAmountNtaPercent()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityNta()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getQuantityNtaPercent()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceNta()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPriceNta()),
                        MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPriceNta())));
            }
            excelData.addAll(buttonContent(lang, dataRangeMap));
            sheet1.setSheetTitle(getSheetNameByProductType(lang, productTypeEnum.toString()));
            sheet1.setSheetNum(sheetIndex);
            sheet1.setData(excelData);
        }
        return sheet1;
    }


    /**
     * 获得到excel第一行第一列的标题
     * @param lang
     * @param dim
     * @return
     */
    public String getExcelFirstTitle(String lang, String dim, QueryReportBuTypeEnum queryReportBuTypeEnum) {
        String hearderTitle = org.apache.commons.lang.StringUtils.EMPTY;
        switch (dim) {
            // 机票
            case "airline_name":
                hearderTitle=SharkUtils.get("SupplierMonitor.Airlines", lang);
                break;
            case "flight_city":
                hearderTitle=SharkUtils.get("Exceltopname.airline", lang);
                break;
            // 酒店
            case "city_name":
                hearderTitle=SharkUtils.get("Exceltopname.city", lang);
                break;
            case "hotel_name":
                hearderTitle=SharkUtils.get("Index.hotel", lang);
                break;
            default:
                break;
        }
        return hearderTitle;
    }

    public void dimEn(String lang, Map<String, String> extData, QueryReportBuTypeEnum reportBuTypeEnum) {
        boolean isEn = SharkUtils.isEN(lang);
        String dim = extData.getOrDefault("dim", "");
        String dataDim;
        switch (dim) {
            // 机票
            case "airline_name":
                dataDim = isEn ? "airline_en_name" : "airline_cn_name";
                break;
            case "flight_city":
                dataDim = isEn ? "flight_city_en" : "flight_city";
                break;
            // 酒店
            case "city_name":
                dataDim = isEn ? "city_name_en" : "city_name";
                break;
            case "hotel_name":
                dataDim = isEn ? "hotel_name_en" : "hotel_name";
                break;
            default:
                dataDim = "";
        }
        extData.put("dim", dataDim);
    }

    enum FltEnum {
        agreementTag("Tag"),
        agreementEndDate("agreementEndDate"),
        agreementStatusLabel("SupplierMonitor.Flt.AgreementLabel"),
        sumQuantity("Index.num"),
        quantityPercent("Index.numpercentage"),
        sumAmount("Index.costmoney"),
        amountPercent("Index.moneyper"),
        sumPrice("Index.netprice"),
        pricePercent("Index.netpercentage"),
        avgPrice("Index.avgprice"),
        avgTpmsPrice("Exceltopname.avgmilprice"),

        sumQuantityTa("SupplierMonitor.FltTkt"),
        quantityTaPercent("Index.numpercentage"),
        sumAmountTa("Department.ThreePartyAmt"),
        amountTaPercent("Index.moneyper"),
        sumPriceTa("SupplierMonitor.FltNetfareTP"),
        priceTaPercent("Index.netpercentage"),
        avgPriceTa("Index.avgprice"),
        avgTpmsPriceTa("Exceltopname.avgmilprice"),

        sumQuantityNta("SupplierMonitor.FltTktNot"),
        quantityNtaPercent("Index.numpercentage"),
        sumAmountNta("Department.NotThreePartyAmt"),
        amountNtaPercent("Index.moneyper"),
        sumPriceNta("SupplierMonitor.FltNetfareNotTP"),
        priceNtaPercent("Index.netpercentage"),
        avgPriceNta("Index.avgprice"),
        avgTpmsPriceNta("Exceltopname.avgmilprice"),
        estimateSaveAmount("SupplierMonitor.estimateSaveAmount")
        ;
        String sharkkey;
        FltEnum(String s) {
            this.sharkkey = s;
        }
        public String getSharkkey() {
            return sharkkey;
        }

        public static FltEnum[] buildArrayExcludeEstimateSaveAmount() {
            FltEnum[] res = new FltEnum[FltEnum.values().length - 1];
            int index = 0;
            for (FltEnum value : values()) {
                if (!estimateSaveAmount.equals(value)) {
                    res[index++] = value;
                }
            }
            return res;
        }
    }

    enum HtlEnum {
        agreementTag("Tag"),
        sumQuantity("Index.nightnum"),
        quantityPercent("Index.nightnumper"),
        sumAmount("Index.costmoney"),
        amountPercent("Index.costper"),
        avgPrice("Overview.HotelAvgNightPrice"),

        sumQuantityTa("Department.ThreePartyQuantity"),
        quantityTaPercent("Index.nightnumper"),
        sumAmountTa("Department.ThreePartyAmt"),
        amountTaPercent("Index.costper"),
        avgPriceTa("Overview.HotelAvgNightPrice"),

        sumQuantityNta("Department.NotThreePartyQuantity"),
        quantityNtaPercent("Index.nightnumper"),
        sumAmountNta("Department.NotThreePartyAmt"),
        amountNtaPercent("Index.costper"),
        avgPriceNta("Overview.HotelAvgNightPrice"),
        ;
        String sharkkey;
        HtlEnum(String s) {
            this.sharkkey = s;
        }

        public String getSharkkey() {
            return sharkkey;
        }
    }

    // SINGLE:每次根据查询条件只下载一个sheet，ALL:每次现在下载所有的sheet
    enum SupplierDownloadMode {
        SINGLE, ALL
    }

}
