package com.corpgovernment.resource.schedule.onlinereport.reportlib.reportfactory;

import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.cglib.seriallizer.JacksonUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.orderdetail.OrderDetailBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TimeFilterTypeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.task.CorpreportDownloadTaskService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DateUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.PoiCustomExcelUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BasePermitVerfiyService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.OrderDetailService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.LanguageEnum;
import onlinereport.enums.reportlib.TrainOrderStatusEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.TempFile;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2020/9/28 17:44
 * @Desc 报告库
 */
@Service
@Slf4j
public class OrderDetailReport extends AbstractGenralExportDataService implements IReport {

    public final static String CORP_KEY = "corp";
    public final static String ACCOUNT_KEY = "account";
    public final static String DEPT_KEY = "dept";
    public final static String COST_CENTER_KEY = "costcenter";
    public final static String TIME_RANGE_FORMAT = "%s~%s";
    protected static final String LOG_TITLE = OrderDetailReport.class.getSimpleName();
    private final static int DEFAULT_PAGESIZE = 1000;
    //默认最大间隔天数，超过就要分批
    private final static int MAX_INTERVAL_DAYS_DEAFULT = 33;

    @Autowired
    private OrderDetailService orderDetailService;

    @Autowired
    private BasePermitVerfiyService basePermitVerfiyService;

    public Workbook create(TaskEntity taskEntity) throws Exception {
        Workbook workbook = null;
        try {
            String conditions = taskEntity.getConditions();
            log.info(LOG_TITLE, conditions);
            BaseQueryConditionBO baseQueryConditionBO = (BaseQueryConditionBO) JacksonUtil.deserialize(conditions, BaseQueryConditionBO.class);
            String uid = taskEntity.getUid();
            baseQueryConditionBO.getBaseQueryCondition().setUid(uid);
            String lang = taskEntity.getLang();
            String sheetName = CorpreportDownloadTaskService.getReportName(lang, baseQueryConditionBO.getBaseQueryCondition().getReportId());
            String download_pageSize = QConfigUtils.getValue("download_pageSize");
            int pageSize = StringUtils.isEmpty(download_pageSize)?DEFAULT_PAGESIZE:Integer.valueOf(download_pageSize);
//            UserPermissionsBo userPermissionsBo = basePermitVerfiyService.vaildPermit(uid, baseQueryConditionBO, lang);
            BaseQueryConditionBO baseQueryCondition = basePermitVerfiyService.convertToBaseOrderQuery(null, baseQueryConditionBO);
            Pager pager = new Pager(0l, pageSize, 1, 0);
            baseQueryCondition.setLang(lang);
            baseQueryCondition.setPager(pager);
            workbook = createBigData(baseQueryCondition, sheetName, pageSize, uid, lang);
        } catch (Exception e) {
            log.error(LOG_TITLE, e);
            if (workbook != null) {
                if (workbook instanceof SXSSFWorkbook) {
                    ((SXSSFWorkbook) workbook).dispose();
                }
                workbook.close();
            }
            throw e;
        }
        return workbook;
    }

    private Workbook createBigData(BaseQueryConditionBO reportLibFilterBO, String sheetName, int pageSize, String uid, String lang) throws IOException, BusinessException {
        // 创建工作簿
        String tempPath = System.getProperty(TempFile.JAVA_IO_TMPDIR);
        log.info(LOG_TITLE, "createBigData tempPath:" + tempPath);
        return createBigDataMulitBatchByDate(reportLibFilterBO, sheetName, pageSize, uid, lang);
    }

    /**
     * @param lang
     * @param baseQueryConditionBO
     * @param uid
     * @return
     */
    public List filllFilterContent(String lang, BaseQueryConditionBO baseQueryConditionBO, String uid) {
        BaseQueryCondition baseQueryCondition = baseQueryConditionBO.getBaseQueryCondition();
        String queryBu = baseQueryConditionBO.getQueryBu();
        List data = new ArrayList();
        data.addAll(getTimeRangeContent(lang, baseQueryCondition.getTimeFilterList(), queryBu, baseQueryCondition.getReportId()));
        if (!StringUtils.equalsIgnoreCase(baseQueryCondition.getReportId(), "DetailReport:UnUseFltTicketDetails")) {
            Map map = getConditionContent(uid, baseQueryCondition, lang);
            data.addAll(getFilterContent(baseQueryConditionBO.getOrderstatusList(), baseQueryConditionBO.getUsers(),
                    baseQueryConditionBO.getPassengers(), baseQueryConditionBO.getOrderids(), baseQueryConditionBO.getEmployeIds(),
                    queryBu, lang, baseQueryCondition.getReportId()));
            data.addAll(getDataRangeContent(lang, map));
            data.addAll(getRcFilterContent(baseQueryConditionBO.getExceedStandard(), queryBu, lang));
            data.addAll(getProductFilterContent(baseQueryConditionBO.getProductType(), queryBu, lang));
            data.addAll(getContractContent(baseQueryConditionBO.getContractType(), queryBu, lang));
        }
        return data;
    }

    /**
     * 数据范围
     *
     * @param lang
     * @param map
     * @return
     */
    protected List getDataRangeContent(String lang, Map map) {
        return Arrays.asList(
                Arrays.asList(SharkUtils.get("Index.public", lang), (String) map.get(CORP_KEY)),
                Arrays.asList(SharkUtils.get("Public.account", lang), (String) map.get(ACCOUNT_KEY)),
                Arrays.asList(SharkUtils.get("Public.department", lang), (String) map.get(DEPT_KEY)),
                Arrays.asList(SharkUtils.get("Public.costcenter", lang), (String) map.get(COST_CENTER_KEY)));
    }

    /**
     * @param lang
     * @param timeFilterTypeInfoList
     * @param queryBu
     * @return
     */
    protected List getTimeRangeContent(String lang, List<TimeFilterTypeInfo> timeFilterTypeInfoList, String queryBu, String reportId) {
        List list = new ArrayList();
        list.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY));
        for (TimeFilterTypeInfo timeFilterTypeInfo : timeFilterTypeInfoList) {
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate")) {
                list.add(Arrays.asList(SharkUtils.get("Exceltopname.date", lang),
                        String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
            } else if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
                if (StringUtils.equalsIgnoreCase("DetailReport:FltTicketRefundDetails", reportId)) {
                    list.add(Arrays.asList(SharkUtils.get("Exceltopname.RefundTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase("DetailReport:FltTicketRebookDetails", reportId)) {
                    list.add(Arrays.asList(SharkUtils.get("Report.RebookTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase("DetailReport:UnUseFltTicketDetails", reportId)) {
                    list.add(Arrays.asList(SharkUtils.get("Exceltopname.TicketingTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else {
                    list.add(Arrays.asList(SharkUtils.get("Exceltopname.dealdate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                }
            } else if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "usedate")) {
                if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.flight.toString())) {
                    list.add(Arrays.asList(SharkUtils.get("Report.TakeoffTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.hotel.toString())) {
                    list.add(Arrays.asList(SharkUtils.get("Exceltopname.livedate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.train.toString())) {
                    list.add(Arrays.asList(SharkUtils.get("Exceltopname.depaturedate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.car.toString())) {
                    list.add(Arrays.asList(SharkUtils.get("Report.begaindate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                }
            }
        }
        return list;
    }

    /**
     * @param orderStatusList 订单状态
     * @param users           预订人
     * @param passengers      出行人
     * @param orderids        订单号
     * @param queryBu         产线
     * @param lang            语言
     * @return
     */
    protected List getFilterContent(List<String> orderStatusList, List<String> users, List<String> passengers, List<String> orderids, List<String> employeeIds,
                                    String queryBu, String lang, String reportId) {
        List list = new ArrayList();
        String default_tip = SharkUtils.get("Report.SelectResult2", lang);
        list.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY));
        // 退票和改签不显示订单状态
        if (!StringUtils.equalsIgnoreCase(reportId, "DetailReport:FltTicketRefundDetails") && !StringUtils.equalsIgnoreCase(reportId, "DetailReport:FltTicketRebookDetails")) {
            // 订单状态
            list.add(Arrays.asList(SharkUtils.get("Exceltopname.orderstatus", lang),
                    getOrderStatusDesc(orderStatusList, queryBu, lang)));
        }
        // 订单号，订单号是数字，excel导入的时候会检查千分位，所以订单号用'、'分割
        list.add(Arrays.asList(SharkUtils.get("TravelPosition.order", lang),
                CollectionUtils.isEmpty(orderids) ? default_tip : StringUtils.join(orderids, "、")));
        // 预订人
        list.add(Arrays.asList(SharkUtils.get("UserSurvey.Q1.Opt5", lang),
                CollectionUtils.isEmpty(users) ? default_tip : StringUtils.join(users, GlobalConst.SEPARATOR)));
        if (!StringUtils.equalsIgnoreCase(reportId, "DetailReport:FltTicketRefundDetails") && !StringUtils.equalsIgnoreCase(reportId, "DetailReport:FltTicketRebookDetails")) {
            // 预订人员工编号
            list.add(Arrays.asList(SharkUtils.get("Report.Employeid", lang),
                    CollectionUtils.isEmpty(employeeIds) ? default_tip : StringUtils.join(employeeIds, "、")));
        }
        // 出行人
        list.add(Arrays.asList(SharkUtils.get("TravelPosition.passenger", lang),
                CollectionUtils.isEmpty(passengers) ? default_tip : StringUtils.join(passengers, GlobalConst.SEPARATOR)));
        return list;
    }

    /**
     * @param contractType 协议类型
     * @param queryBu      产线
     * @param lang         语言
     * @return
     */
    protected List getContractContent(String contractType, String queryBu, String lang) {
        List list = new ArrayList();
        List<String> includeBu = Lists.newArrayList(QueryReportBuTypeEnum.flight.toString(), QueryReportBuTypeEnum.hotel.toString());
        if (includeBu.contains(queryBu.toLowerCase())) {
            list.add(Arrays.asList(SharkUtils.get("Exceltopname.pacttype", lang), getContractDesc(contractType, lang)));
        }
        return list;
    }

    private String getContractDesc(String contractType, String lang) {
        if (StringUtils.isEmpty(contractType) || "all".equalsIgnoreCase(contractType)) {
            return SharkUtils.get("Index.allin", lang);
        } else if (StringUtils.equalsIgnoreCase(contractType, "C")) {
            return SharkUtils.get("Index.Agreement", lang);
        } else if (StringUtils.equalsIgnoreCase(contractType, "NC")) {
            return SharkUtils.get("Index.NonAgreement", lang);
        }
        return StringUtils.EMPTY;
    }

    /**
     * @param productType 产品类型
     * @param queryBu     产线
     * @param lang        语言
     * @return
     */
    protected List getProductFilterContent(String productType, String queryBu, String lang) {
        List list = new ArrayList();
        List<String> includeBu = Lists.newArrayList(QueryReportBuTypeEnum.flight.toString(), QueryReportBuTypeEnum.hotel.toString()
                , QueryReportBuTypeEnum.car.toString());
        if (!includeBu.contains(queryBu.toLowerCase())) {
            return list;
        }
        String conditionName;
        if (queryBu.toLowerCase().equalsIgnoreCase(QueryReportBuTypeEnum.flight.toString())) {
            conditionName = SharkUtils.get("Exceltopname.airlinetype", lang);
        } else if (queryBu.toLowerCase().equalsIgnoreCase(QueryReportBuTypeEnum.hotel.toString())) {
            conditionName = SharkUtils.get("Travelanalysis.hotelstyle", lang);
        } else {
            conditionName = SharkUtils.get("Exceltopname.usecartype", lang);
        }
        list.add(Arrays.asList(conditionName, getProductDesc(productType, lang)));
        return list;
    }

    private String getProductDesc(String productType, String lang) {
        if (StringUtils.isEmpty(productType) || "all".equalsIgnoreCase(productType)) {
            return SharkUtils.get("Index.allin", lang);
        } else if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            return SharkUtils.get("lbl_International", lang);
        } else if (StringUtils.equalsIgnoreCase(productType, "dom")) {
            return SharkUtils.get("lbl_Domestic", lang);
        }
        return StringUtils.EMPTY;
    }

    /**
     *
     * @param rcList 是否rc
     * @param queryBu 产线
     * @param lang    语言
     * @return
     */
    protected List getRcFilterContent(List<String> rcList, String queryBu, String lang) {
        List list = new ArrayList();
        List<String> excludeBu = Lists.newArrayList(QueryReportBuTypeEnum.car.toString(), QueryReportBuTypeEnum.bus.toString(),
                QueryReportBuTypeEnum.vaso.toString());
        if (!excludeBu.contains(queryBu.toLowerCase())){
            // 是否RC
            list.add(Arrays.asList(SharkUtils.get("Report.IsRC", lang),
                    getRcDesc(rcList, lang)));
        }
        return list;
    }

    private String getRcDesc(List<String> orderStatusList, String lang) {
        if (CollectionUtils.isEmpty(orderStatusList) || orderStatusList.size() == 2) {
            return SharkUtils.get("Public.all", lang);
        } else if (StringUtils.equalsIgnoreCase(orderStatusList.get(0), "T")) {
            return SharkUtils.get("RC", lang);
        } else if (StringUtils.equalsIgnoreCase(orderStatusList.get(0), "F")) {
            return SharkUtils.get("Report.IsRCFalse", lang);
        }
        return StringUtils.EMPTY;
    }

    private String getOrderStatusDesc(List<String> orderStatusList, String queryBU, String lang) {
        if (CollectionUtils.isEmpty(orderStatusList)) {
            return SharkUtils.get("Public.all", lang);
        }
        if (StringUtils.equalsIgnoreCase(queryBU, QueryReportBuTypeEnum.flight.toString())) {
            return getFlightOrderStatusDesc(orderStatusList);
        } else if (StringUtils.equalsIgnoreCase(queryBU, QueryReportBuTypeEnum.hotel.toString())) {
            return getHotelOrderStatusDesc(orderStatusList);
        } else if (StringUtils.equalsIgnoreCase(queryBU, QueryReportBuTypeEnum.train.toString())) {
            return getTrainOrderStatusDesc(orderStatusList, lang);
        } else if (StringUtils.equalsIgnoreCase(queryBU, QueryReportBuTypeEnum.car.toString())) {
            return getCarOrderStatusDesc(orderStatusList);
        } else {
            return CollectionUtils.isEmpty(orderStatusList) ? StringUtils.EMPTY : StringUtils.join(orderStatusList, GlobalConst.SEPARATOR);
        }
    }

    private String getFlightOrderStatusDesc(List<String> orderStatusList){
        List list = new ArrayList();
        Optional.ofNullable(orderStatusList).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).forEach(i->{
            if (StringUtils.equalsIgnoreCase(i, "S")){
                // 已成交
                list.add(SharkUtils.get("RiskOrder.FltStatus4", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "R")){
                // 全部退票
                list.add(SharkUtils.get("RiskOrder.FltStatus7", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "C")){
                // 已取消
                list.add(SharkUtils.get("RiskOrder.FltStatus1", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "T")){
                // 部分退票
                list.add(SharkUtils.get("RiskOrder.FltStatus3", LanguageEnum.ZH_CN.getDescription()));
            }else {
                list.add(i);
            }
        });
        return StringUtils.join(list, GlobalConst.SEPARATOR);
    }

    private String getHotelOrderStatusDesc(List<String> orderStatusList){
        List list = new ArrayList();
        Optional.ofNullable(orderStatusList).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).forEach(i->{
            if (StringUtils.equalsIgnoreCase(i, "P")){
                // 处理中
                list.add(SharkUtils.get("RiskOrder.HtlOrderStatus6", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "S")){
                // 成交
                list.add(SharkUtils.get("RiskOrder.HtlOrderStatus1", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "C")){
                // 取消
                list.add(SharkUtils.get("RiskOrder.HtlOrderStatus2", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "U")){
                // 修改
                list.add(SharkUtils.get("RiskOrder.HtlOrderStatus4", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "SW")){
                // 已提交，待处理
                list.add(SharkUtils.get("RiskOrder.HtlOrderStatus5", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "SP")){
                // 已提交，处理中
                list.add(SharkUtils.get("RiskOrder.HtlOrderStatus7", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "O")){
                // 其他
                list.add(SharkUtils.get("RiskOrder.HtlOrderStatus3", LanguageEnum.ZH_CN.getDescription()));
            }else {
                list.add(i);
            }
        });
        return StringUtils.join(list, GlobalConst.SEPARATOR);
    }

    private String getTrainOrderStatusDesc(List<String> orderStatusList, String lang) {
        List list = new ArrayList();
        Optional.ofNullable(orderStatusList).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).forEach(i -> {
            if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.N.toString())) {
                // 未提交
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.N.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.WP.toString())) {
                // 待支付
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.WP.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.PP.toString())) {
                // 支付处理中
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.PP.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.PF.toString())) {
                // 支付失败
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.PF.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.WA.toString())) {
                // 待授权
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.WA.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.AR.toString())) {
                // 授权拒绝
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.AR.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.WT.toString())) {
                // 待出票
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.WT.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.TP.toString())) {
                // 购票中
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.TP.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.TD.toString())) {
                // 已购票"
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.TD.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.TF.toString())) {
                // 出票失败
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.TF.getName(), lang));
            } else if (StringUtils.equalsIgnoreCase(i, TrainOrderStatusEnum.C.toString())) {
                // 已取消
                list.add(SharkUtils.getBluespace(TrainOrderStatusEnum.C.getName(), lang));
            } else {
                list.add(i);
            }
        });
        return StringUtils.join(list, GlobalConst.SEPARATOR);
    }

    private String getCarOrderStatusDesc(List<String> orderStatusList){
        List list = new ArrayList();
        Optional.ofNullable(orderStatusList).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).forEach(i->{
            if (StringUtils.equalsIgnoreCase(i, "Successful")){
                // 已成交
                list.add(SharkUtils.get("RiskOrder.Completed", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "Canceled")){
                // 已取消
                list.add(SharkUtils.get("RiskOrder.ReportTip10", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "Confirmed")){
                // 已确认待接单
                list.add(SharkUtils.get("Report.CarOrderStatus1", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "Submitted")){
                // 已提交
                list.add(SharkUtils.get("Report.CarOrderStatus2", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "WaitService")){
                // 等待接驾
                list.add(SharkUtils.get("Report.CarOrderStatus3", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "Arrived")){
                // 司机就位
                list.add(SharkUtils.get("Report.CarOrderStatus4", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "InService")){
                // 开始服务
                list.add(SharkUtils.get("Report.CarOrderStatus5", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "EndService")){
                // 服务完成
                list.add(SharkUtils.get("Report.CarOrderStatus6", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "DriverSetOut")){
                // 司机出发
                list.add(SharkUtils.get("Report.CarOrderStatus7", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "Processing")){
                // 处理中
                list.add(SharkUtils.get("trip.biz.reportlib.car.order.status.processing", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "WaitReply")){
                // 等待应答
                list.add(SharkUtils.get("Report.CarOrderStatus8", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "SupplierReceived")){
                // 供应商应单
                list.add(SharkUtils.get("Report.CarOrderStatus9", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "Canceling")){
                // 取消中
                list.add(SharkUtils.get("RiskOrder.ReportTip9", LanguageEnum.ZH_CN.getDescription()));
            }else if (StringUtils.equalsIgnoreCase(i, "Redispatched")){
                // 改派中
                list.add(SharkUtils.get("Report.CarOrderStatus10", LanguageEnum.ZH_CN.getDescription()));
            }else {
                list.add(i);
            }
        });
        return StringUtils.join(list, GlobalConst.SEPARATOR);
    }


    /**
     * @param baseQueryConditionBO
     * @param sheetName
     * @param pageSize
     * @param uid
     * @param lang
     * @return
     * @throws IOException
     * @throws BusinessException
     */
    public Workbook createBigDataMulitBatchByDate(BaseQueryConditionBO baseQueryConditionBO, String sheetName, int pageSize,
                                                  String uid, String lang) throws IOException, BusinessException {
        Integer sheetLimit = QConfigUtils.getInterValue("sheetLimit");
        if (sheetLimit == null || sheetLimit <= 0) {
            sheetLimit = 10000;
        }
        List filterList = filllFilterContent(lang, baseQueryConditionBO, uid);

        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        SXSSFWorkbook workbook = new SXSSFWorkbook(hssfWorkbook, sheetLimit);
        int pageIndex = 1;
        boolean isEmpty = false;//是否为空
        boolean isLastPage = false;//最后一页
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info(LOG_TITLE, "createBigDataMulitBatchByDate start");
        int startRow = 0;
        List<TimeFilterTypeInfo> timeFilterTypeInfoList = baseQueryConditionBO.getBaseQueryCondition().getTimeFilterList();

        Map<Integer, Integer> maxWidth = new HashMap<>();
        TimeFilterTypeInfo timeFilterTypeInfo = null;
        Optional optional1 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getTimeType(), "orderdate"))
                .findFirst();
        Optional optional2 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getTimeType(), "dealdate"))
                .findFirst();
        Optional optional3 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getTimeType(), "usedate"))
                .findFirst();
        if (optional1.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional1.get();
        } else if (optional2.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional2.get();
        } else if (optional3.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional3.get();
        }
        long intervalDays = DateUtil.betweenDay(timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime());
        String excel_create_mulit_date_switch = QConfigUtils.getValue("excel_create_mulit_date_switch");
        List<Integer> highlightTitle = Lists.newArrayList();
        if (intervalDays < MAX_INTERVAL_DAYS_DEAFULT || needSingleByCondition(baseQueryConditionBO) ||
                (StringUtils.equalsIgnoreCase(excel_create_mulit_date_switch,"F") && !needMulitByCorpIds(baseQueryConditionBO.getBaseQueryCondition().getCorpIds()))){
            while (!isLastPage){
                List<List<Object>> dataSource = new ArrayList<>();
                Pager pager = baseQueryConditionBO.getPager();
                pager.setPageIndex(pageIndex);
                OrderDetailBO orderDetail = orderDetailService.queryDetail(uid, baseQueryConditionBO, true);
                highlightTitle = orderDetail.getHighlightTitle();
                if (pageIndex == 1){
                    dataSource.add(new ArrayList<>(orderDetail.getTitle()));
                }
                pageIndex++;
                isEmpty = CollectionUtils.isEmpty(orderDetail.getData());
                isLastPage = isEmpty || orderDetail.getData().size() < pageSize;
                if (!isEmpty){
                    dataSource.addAll(orderDetail.getData());
                }
                PoiCustomExcelUtils.createExcelBigDataMulitBatchObj(workbook, dataSource, startRow, sheetName, maxWidth, orderDetail.getHighlightTitle());
                startRow += dataSource.size();
            }
        } else {
            boolean hasTitle = false;
            String startTime = timeFilterTypeInfo.getStartTime();
            String endTime = timeFilterTypeInfo.getEndTime();
            String startTimeTemp = startTime;
            String endTimeTemp = DateUtil.offsetTime(startTime, MAX_INTERVAL_DAYS_DEAFULT, Calendar.DAY_OF_YEAR);
            while (endTimeTemp.compareTo(endTime) <= 0 && startTimeTemp.compareTo(endTime) <= 0) {
                timeFilterTypeInfo.setStartTime(startTimeTemp);
                timeFilterTypeInfo.setEndTime(endTimeTemp);
                pageIndex = 1;
                isLastPage = false;//最后一页
                while (!isLastPage){
                    List<List<Object>> dataSource = new ArrayList<>();
                    Pager pager = baseQueryConditionBO.getPager();
                    pager.setPageIndex(pageIndex);
                    OrderDetailBO orderDetail = orderDetailService.queryDetail(uid, baseQueryConditionBO, true);
                    highlightTitle = orderDetail.getHighlightTitle();
                    if (pageIndex == 1 && !hasTitle){
                        dataSource.add(new ArrayList<>(orderDetail.getTitle()));
                        hasTitle = true;
                    }
                    pageIndex++;
                    isEmpty = CollectionUtils.isEmpty(orderDetail.getData());
                    isLastPage = isEmpty || orderDetail.getData().size() < pageSize;
                    if (!isEmpty){
                        dataSource.addAll(orderDetail.getData());
                    }
                    PoiCustomExcelUtils.createExcelBigDataMulitBatchObj(workbook, dataSource, startRow, sheetName, maxWidth, orderDetail.getHighlightTitle());
                    startRow += dataSource.size();
                }
                startTimeTemp = DateUtil.offsetTime(endTimeTemp, 1, Calendar.DAY_OF_YEAR);
                endTimeTemp = DateUtil.offsetTime(endTimeTemp, MAX_INTERVAL_DAYS_DEAFULT, Calendar.DAY_OF_YEAR);
                if (endTimeTemp.compareTo(endTime) > 0 && startTimeTemp.compareTo(endTime) <= 0){
                    startTimeTemp = startTimeTemp.compareTo(endTime) > 0 ? endTime : startTimeTemp;
                    endTimeTemp = endTimeTemp.compareTo(endTime) > 0 ? endTime : endTimeTemp;
                    timeFilterTypeInfo.setStartTime(startTimeTemp);
                    timeFilterTypeInfo.setEndTime(endTimeTemp);
                    pageIndex = 1;
                    isLastPage = false;//最后一页
                    while (!isLastPage) {
                        List<List<Object>> dataSource = new ArrayList<>();
                        Pager pager = baseQueryConditionBO.getPager();
                        pager.setPageIndex(pageIndex);
                        OrderDetailBO orderDetail = orderDetailService.queryDetail(uid, baseQueryConditionBO, true);
                        if (pageIndex == 1 && !hasTitle) {
                            dataSource.add(new ArrayList<>(orderDetail.getTitle()));
                            hasTitle = true;
                        }
                        pageIndex++;
                        isEmpty = CollectionUtils.isEmpty(orderDetail.getData());
                        isLastPage = isEmpty || orderDetail.getData().size() < pageSize;
                        if (!isEmpty){
                            dataSource.addAll(orderDetail.getData());
                        }
                        PoiCustomExcelUtils.createExcelBigDataMulitBatchObj(workbook, dataSource, startRow, sheetName, maxWidth, orderDetail.getHighlightTitle());
                        startRow += dataSource.size();
                    }
                    break;
                }
            }
        }
        // 汇总卡片携带字段到excel最后一行
        PoiCustomExcelUtils.sumColAndFilledInRear(workbook, sheetName, highlightTitle);
        PoiCustomExcelUtils.createExcelMulitBatch(hssfWorkbook, filterList, 1, SharkUtils.get("Report.SelectResult1", lang),maxWidth);
        log.info(LOG_TITLE,String.format("createBigDataMulitBatchByDate end, data size : %d , take time : %s",startRow,stopWatch.getTime()));
        return workbook;
    }

    private boolean needSingleByCondition(BaseQueryConditionBO baseQueryConditionBO){
        if (Optional.ofNullable(baseQueryConditionBO.getOrderids()).orElse(Lists.newArrayList()).stream()
                .filter(Objects::nonNull).filter(s -> StringUtils.isNotEmpty(s)).collect(Collectors.toList()).size() > 0){
            return true;
        }
        if (Optional.ofNullable(baseQueryConditionBO.getUsers()).orElse(Lists.newArrayList()).stream()
                .filter(Objects::nonNull).filter(s -> StringUtils.isNotEmpty(s)).collect(Collectors.toList()).size() > 0){
            return true;
        }
        if (Optional.ofNullable(baseQueryConditionBO.getPassengers()).orElse(Lists.newArrayList()).stream()
                .filter(Objects::nonNull).filter(s -> StringUtils.isNotEmpty(s)).collect(Collectors.toList()).size() > 0){
            return true;
        }
        if (Optional.ofNullable(baseQueryConditionBO.getEmployeIds()).orElse(Lists.newArrayList()).stream()
                .filter(Objects::nonNull).filter(s -> StringUtils.isNotEmpty(s)).collect(Collectors.toList()).size() > 0){
            return true;
        }
        return false;
    }

    private boolean needMulitByCorpIds(List<String> corpIds){
        List<String> needMulitCorpIds = QConfigUtils.getNeedMulitCorpIds();
        if (CollectionUtils.isEmpty(needMulitCorpIds)){
            return false;
        }
        // Collections.disjoint(list1, list2)方法会返回一个布尔值，指示两个集合是否没有共同元素。如果返回false，则表示有交集；如果返回true，则表示无交集。
        return !Collections.disjoint(corpIds, needMulitCorpIds);
    }


}
