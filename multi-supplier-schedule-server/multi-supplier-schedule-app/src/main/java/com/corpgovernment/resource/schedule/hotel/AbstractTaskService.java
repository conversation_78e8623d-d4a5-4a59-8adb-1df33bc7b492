package com.corpgovernment.resource.schedule.hotel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.common.common.CorpBusinessException;
import com.corpgovernment.resource.schedule.domain.ResponseCodeEnum;
import com.corpgovernment.resource.schedule.domain.hotel.enums.TaskEnum;
import com.corpgovernment.resource.schedule.domain.hotel.enums.TaskStatusEnum;
import com.corpgovernment.resource.schedule.domain.hotel.gateway.ITaskGateway;
import com.corpgovernment.resource.schedule.domain.hotel.gateway.ITaskMonitorGateway;
import com.corpgovernment.resource.schedule.domain.hotel.model.SupplierControl;
import com.corpgovernment.resource.schedule.domain.hotel.model.Task;
import com.corpgovernment.resource.schedule.hotel.dao.cache.IHotelCacheDao;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/25
 */
@Slf4j
public abstract class AbstractTaskService {

    @Autowired
    private ITaskGateway taskGateway;
    @Autowired
    private IHotelCacheDao hotelCacheDao;
    @Resource(name = "hotelExecuteTaskThreadPool")
    private ThreadPoolExecutor hotelExecuteTaskThreadPool;
    @Autowired
    private ITaskMonitorGateway taskMonitorGateway;

    public final void create() {
        TraceContext.setRequestId(String.valueOf(UUID.randomUUID()));
        String taskName = taskEnum().name();
        String key = "task" + StrUtil.UNDERLINE + "create" + StrUtil.UNDERLINE + taskName;
        List<SupplierControl> supplierControlList = listSupplierControl();
        log.info("任务创建 taskName={} key={} supplierControlList={}", taskName, key, supplierControlList);
        if (CollectionUtils.isEmpty(supplierControlList)) {
            return;
        }

        try {
            if (Boolean.FALSE.equals(hotelCacheDao.lock(key))) {
                log.info("任务创建中，无需多次创建 taskName={} key={}", taskName, key);
                return;
            }
            // 幂等处理 把该任务全部重建
            while (taskGateway.remove(taskName, ITaskGateway.BATCH_SIZE) > 0);
            log.info("任务幂等处理完成");
            // 任务创建
            for (SupplierControl supplierControl : supplierControlList) {
                String supplierCode = supplierControl.getSupplierCode();
                // 创建任务
                List<Task> taskList = createTask(supplierCode);
                if (CollectionUtils.isEmpty(taskList)) {
                    return;
                }
                // 参数补充
                taskList.forEach(item -> {
                    item.setSupplierCode(supplierCode);
                    item.setName(taskEnum().name());
                });
                // 批量插入
                CollUtil.split(taskList,  ITaskGateway.BATCH_SIZE).forEach(list -> taskGateway.batchCreate(list));
                log.info("任务创建完成 supplierCode={} taskName={} taskSize={}", supplierCode, taskName, taskList.size());
            }
        } finally {
            // 释放幂等锁
            hotelCacheDao.unlock(key);
        }
    }

    public final void execute() {
        String requestId = String.valueOf(UUID.randomUUID());
        TraceContext.setRequestId(requestId);
        String taskName = taskEnum().name();
        List<SupplierControl> supplierControlList = listSupplierControl();
        log.info("任务执行 taskName={} supplierControlList={}", taskName, supplierControlList);
        if (CollectionUtils.isEmpty(supplierControlList)) {
            return;
        }

        List<Task> taskList = new ArrayList<>();
        Map<String, SupplierControl> supplierControlMap = new HashMap<>();
        for (SupplierControl supplierControl : supplierControlList) {
            // 获取任务并锁定任务
            List<Task> tmpList = getTaskPool(supplierControl.getSupplierCode(), taskName, supplierControl.getTaskFlow());
            if (CollectionUtils.isEmpty(tmpList)) {
                continue;
            }
            taskMonitorGateway.count(supplierControl.getSupplierCode(), taskName, ITaskMonitorGateway.TASK_NEED_DO, tmpList.size());
            taskList.addAll(tmpList);
            supplierControlMap.put(supplierControl.getSupplierCode(), supplierControl);
        }
        log.info("任务收集 taskList={}", taskList);
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }

        // 任务排序
        Map<String, List<Task>> taskMap = taskList.stream().collect(Collectors.groupingBy(Task::getSupplierCode));
        List<String> supplierCodeList = new ArrayList<>(taskMap.keySet());
        List<Task> sortTaskList = new ArrayList<>(taskList.size());
        for (int i = 0; i < taskList.size(); i++) {
            for (String supplierCode : supplierCodeList) {
                List<Task> tmpList = taskMap.get(supplierCode);
                if (CollectionUtils.isEmpty(tmpList)) {
                    continue;
                }
                if (i < tmpList.size()) {
                    sortTaskList.add(tmpList.get(i));
                }
            }
        }

        sortTaskList.forEach(task -> CompletableFuture.runAsync(() -> {
            taskMonitorGateway.count(task.getSupplierCode(), taskName, ITaskMonitorGateway.TASK_DOING, 1);
            // 调用core微服务执行任务
            JSONResult<?> jsonResult = null;
            try {
                // 远程调用处理任务
                try {
                    jsonResult = processTask(task);
                } catch (Exception e) {
                    log.error("调用core微服务执行任务失败", e);
                } finally {
                    log.info("调用core微服务执行任结果 task={} jsonResult={}", task, jsonResult);
                }
                if (jsonResult == null || jsonResult.getStatus() != 200) {
                    throw new CorpBusinessException(ResponseCodeEnum.RPC_IS_ERROR);
                }
                // 任务传递
                String supplierCode = task.getSupplierCode();
                SupplierControl supplierControl = supplierControlMap.get(supplierCode);
                List<Task> deliverTaskList = deliverTask(supplierCode, jsonResult, task, supplierControl);
                if (CollectionUtils.isNotEmpty(deliverTaskList)) {
                    deliverTaskList.forEach(a -> a.setSupplierCode(supplierCode));
                    CollUtil.split(deliverTaskList, ITaskGateway.BATCH_SIZE).forEach(a -> taskGateway.batchCreate(a));
                }
                // 执行完成删除任务
                taskGateway.remove(task.getId());
            } catch (Exception e) {
                // 执行失败修改状态
                taskGateway.update(Collections.singletonList(task.getId()), TaskStatusEnum.FAILED.getCode());
                log.error("任务执行失败", e);
            }
            taskMonitorGateway.count(task.getSupplierCode(), taskName, ITaskMonitorGateway.TASK_DONE, 1);
        }, hotelExecuteTaskThreadPool));
    }

    public final void retryTask(Integer maxRetryCount) {
        // 限制重试次数，更新状态为已创建
        taskGateway.retry(maxRetryCount);
    }

    public final void releaseUnFinishedTask() {
        taskGateway.releaseUnFinished();
    }

    /**
     * 只需要填充taskParam
     */
    protected abstract List<Task> createTask(String supplierCode);
    /**
     * RPC调用
     */
    protected abstract JSONResult<?> processTask(Task task);
    /**
     * 只需要填充param和name
     */
    protected abstract List<Task> deliverTask(String supplierCode, JSONResult<?> jsonResult, Task task, SupplierControl supplierControl);
    /**
     * 获取需要使用的supplierCode
     */
    protected abstract List<SupplierControl> listSupplierControl();
    /**
     * 任务标签
     */
    protected abstract TaskEnum taskEnum();

    private List<Task> getTaskPool(String supplierCode, String taskName, Integer flow) {
        List<Task> taskList;
        String key = "task" + StrUtil.COLON + "execute" + StrUtil.COLON + supplierCode + StrUtil.COLON + taskName;
        try {
            while (Boolean.FALSE.equals(hotelCacheDao.lock(key))) {
                log.info("获取任务被锁定，自旋等待中。 supplierCode={} taskName={}", supplierCode, taskName);
            }
            // 分页查询任务
            taskList = taskGateway.page(taskName, supplierCode, flow);
            if (CollectionUtils.isEmpty(taskList)) {
                log.info("无可执行任务。supplierCode={} taskName={}", supplierCode, taskName);
                return null;
            }
            // 锁定任务
            taskGateway.update(taskList.stream().map(Task::getId).collect(Collectors.toList()), TaskStatusEnum.LOCKED.getCode());
        }
        catch (Exception e) {
            log.info("getTaskPool执行失败", e);
            return null;
        }
        finally {
            // 释放幂等锁
            hotelCacheDao.unlock(key);
        }
        return taskList;
    }

}
