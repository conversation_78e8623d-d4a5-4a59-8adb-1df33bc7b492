package com.corpgovernment.resource.schedule.onlinereport.travelmark;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.MarkMetricInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.MarkMetricTrendAndOverview;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMarkMetricTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMarkMetricTrendResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc
 */
@Service
public class TravelMarkAdaptor extends BaseReportDataAdaptor<Object> {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private TravelMarkExcelService travelMarkExcelService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String reportId = baseCondition.getBaseQueryCondition().getReportId();
        String lang = baseCondition.getLang();
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:DomesticFltAvgPrice", reportId)
                || StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:OverseaFltAvgMileagePrice", reportId)
                || StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:DomesticFltFullPriceRatio", reportId)
                || StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:TrainTicketAvgPrice", reportId)) {
            OnlineReportMarkMetricTrendRequest request = new OnlineReportMarkMetricTrendRequest();
            request.setProductType(baseCondition.getProductType());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            if (StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:DomesticFltAvgPrice", reportId)) {
                request.setQueryBu(QueryReportBuTypeEnum.flight);
                extMap.put("metric", "avg_price_trend");
            } else if (StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:OverseaFltAvgMileagePrice", reportId)) {
                request.setQueryBu(QueryReportBuTypeEnum.flight);
                extMap.put("metric", "tpms_avg_price_trend");
            } else if (StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:DomesticFltFullPriceRatio", reportId)) {
                request.setQueryBu(QueryReportBuTypeEnum.flight);
                extMap.put("metric", "full_fare_percent_trend");
            } else if (StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:TrainTicketAvgPrice", reportId)) {
                request.setQueryBu(QueryReportBuTypeEnum.train);
                extMap.put("metric", "avg_ticket_price_trend");
            }
            request.setExtData(extMap);
            OnlineReportMarkMetricTrendResponse responseType = corpOnlineReportPlatformService.queryMarkMetricTrend(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                if (StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:TrainTicketAvgPrice", reportId)) {
                    return metricTrend(Optional.ofNullable(responseType.getMarkMetricTrend()).orElse(new ArrayList<>()), lang);
                } else {
                    return metricOverview(Optional.ofNullable(responseType.getMarkMetric()).orElse(new MarkMetricTrendAndOverview()), lang);
                }
            } else {
                erroThrow(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        }
        return null;
    }


    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        return travelMarkExcelService.buildExcel(userPermissionsBo, baseCondition);
    }

    @Override
    public String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang) {
        String reportId = baseCondition.getBaseQueryCondition().getReportId();
        String fileName_format = "%s_%s_%s_%s";
        String middle = SharkUtils.get("Catalog.Carbon", lang);
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:DomesticFltAvgPrice", reportId)) {
            middle = SharkUtils.get("Overview.FlightAvgTktPrice", lang);
        } else if (StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:OverseaFltAvgMileagePrice", reportId)) {
            middle = SharkUtils.get("Exceltopname.avgmilprice", lang);
        } else if (StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:DomesticFltFullPriceRatio", reportId)) {
            middle = SharkUtils.get("UserSurvey.FulltktPercent", lang);
        } else if (StringUtils.equalsIgnoreCase("TravelAnalysis:TravelMark:TrainTicketAvgPrice", reportId)) {
            middle = SharkUtils.get("Travelanalysis.TrainAvg", lang);
        }
        String prefix = SharkUtils.get("Catalog.BehaviorAnalysis", lang);
        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
        String name = String.format(fileName_format, prefix, middle, uid, str);
        return name;
    }


    private Map metricOverview(MarkMetricTrendAndOverview markMetricTrendAndOverview, String lang) {
        List<MarkMetricInfo> markMetricInfoList = Optional.ofNullable(markMetricTrendAndOverview.getMarkMetricTrend()).orElse(new ArrayList<>());
        MarkMetricInfo markMetric = Optional.ofNullable(markMetricTrendAndOverview.getMarkMetricInfo()).orElse(new MarkMetricInfo());
        Map overviewMap = ImmutableMap.builder()
                .put("company", markMetric.getCompanyMetric())
                .put("corp", markMetric.getCorpMetric())
                .put("industry", markMetric.getIndustryMetric()).build();
        return ImmutableMap.builder()
                .put("overview", overviewMap)
                .put("trend", metricTrend(markMetricInfoList, lang)).build();
    }

    private Map metricTrend(List<MarkMetricInfo> markMetricInfoList, String lang) {
        List trendLegends = new ArrayList();
        Map trendLegendCompany = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.public", lang))
                .put("key", "company").build();
        trendLegends.add(trendLegendCompany);
        Map trendLegendIndustry = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.industry", lang))
                .put("key", "industry").build();
        trendLegends.add(trendLegendIndustry);
        Map trendLegendCorp = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.tmc", lang))
                .put("key", "corp").build();
        trendLegends.add(trendLegendCorp);
        List trendDataList = new ArrayList();
        for (MarkMetricInfo markMetricInfo : markMetricInfoList) {
            Map trendData = ImmutableMap.builder()
                    .put("company", MapperUtils.convertDigitToZero(markMetricInfo.getCompanyMetric()))
                    .put("industry", MapperUtils.convertDigitToZero(markMetricInfo.getIndustryMetric()))
                    .put("corp", MapperUtils.convertDigitToZero(markMetricInfo.getCorpMetric())).build();
            trendDataList.add(ImmutableMap.builder()
                    .put("axis", markMetricInfo.getDim())
                    .put("data", trendData).build());
        }
        return ImmutableMap.builder()
                .put("legends", trendLegends)
                .put("data", trendDataList).build();
    }
}
