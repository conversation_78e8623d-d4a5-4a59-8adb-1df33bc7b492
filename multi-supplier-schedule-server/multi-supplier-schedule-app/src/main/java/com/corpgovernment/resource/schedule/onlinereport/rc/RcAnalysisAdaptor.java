package com.corpgovernment.resource.schedule.onlinereport.rc;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/5 21:36
 * @Desc 差标使用
 */
@Service
@Slf4j
public class RcAnalysisAdaptor extends BaseReportDataAdaptor<Object> {


    @Autowired
    private RcAnalysisService rcAnalysisService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        Map resp = null;
        IndexEnum indexEnum = IndexEnum.valueOf(baseCondition.getIndex());
        switch (indexEnum) {
            case RC_OVERVIEW:
                resp = rcAnalysisService.rcView(baseCondition, QueryReportBuTypeEnum.overview);
                break;
            case RC_OVERVIEW_PERCENT:
                resp = rcAnalysisService.rcViewPercnet(baseCondition, QueryReportBuTypeEnum.overview);
                break;
            case RC_OVERVIEW_DETAIL:
                resp = rcAnalysisService.rcViewReason(baseCondition, QueryReportBuTypeEnum.overview);
                break;
            case RC_FLIGHT:
                resp = rcAnalysisService.rcView(baseCondition, QueryReportBuTypeEnum.flight);
                break;
            case RC_FLIGHT_PERCENT:
                resp = rcAnalysisService.rcViewPercnet(baseCondition, QueryReportBuTypeEnum.flight);
                break;
            case RC_FLIGHT_DETAIL:
                resp = rcAnalysisService.rcViewReason(baseCondition, QueryReportBuTypeEnum.flight);
                break;
            case RC_HOTEL:
                resp = rcAnalysisService.rcView(baseCondition, QueryReportBuTypeEnum.hotel);
                break;
            case RC_HOTEL_PERCENT:
                resp = rcAnalysisService.rcViewPercnet(baseCondition, QueryReportBuTypeEnum.hotel);
                break;
            case RC_HOTEL_DETAIL:
                resp = rcAnalysisService.rcViewReason(baseCondition, QueryReportBuTypeEnum.hotel);
                break;
            case RC_TRAIN:
                resp = rcAnalysisService.rcView(baseCondition, QueryReportBuTypeEnum.train);
                break;
            case RC_TRAIN_PERCENT:
                resp = rcAnalysisService.rcViewPercnet(baseCondition, QueryReportBuTypeEnum.train);
                break;
            case RC_TRAIN_DETAIL:
                resp = rcAnalysisService.rcViewReason(baseCondition, QueryReportBuTypeEnum.train);
                break;
            case RC_TREND:
                resp = rcAnalysisService.rcTrend(baseCondition);
                break;
            case RC_TOP_DEPT:
                resp = rcAnalysisService.topRc(baseCondition);
                break;
        }
        return resp;
    }


    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        IndexEnum indexEnum = IndexEnum.valueOf(baseCondition.getIndex());
        switch (indexEnum) {
            case RC_OVERVIEW_PERCENT:
                chartExcelEntityList = rcAnalysisService.rcViewPercnetExcel(baseCondition, QueryReportBuTypeEnum.overview);
                break;
            case RC_FLIGHT_PERCENT:
                chartExcelEntityList = rcAnalysisService.rcViewPercnetExcel(baseCondition, QueryReportBuTypeEnum.flight);
                break;
            case RC_HOTEL_PERCENT:
                chartExcelEntityList = rcAnalysisService.rcViewPercnetExcel(baseCondition, QueryReportBuTypeEnum.hotel);
                break;
            case RC_TRAIN_PERCENT:
                chartExcelEntityList = rcAnalysisService.rcViewPercnetExcel(baseCondition, QueryReportBuTypeEnum.train);
                break;
            case RC_OVERVIEW_DETAIL:
                chartExcelEntityList = rcAnalysisService.rcViewReasonExcel(baseCondition, QueryReportBuTypeEnum.overview);
                break;
            case RC_FLIGHT_DETAIL:
                chartExcelEntityList = rcAnalysisService.rcViewReasonExcel(baseCondition, QueryReportBuTypeEnum.flight);
                break;
            case RC_HOTEL_DETAIL:
                chartExcelEntityList = rcAnalysisService.rcViewReasonExcel(baseCondition, QueryReportBuTypeEnum.hotel);
                break;
            case RC_TRAIN_DETAIL:
                chartExcelEntityList = rcAnalysisService.rcViewReasonExcel(baseCondition, QueryReportBuTypeEnum.train);
                break;
            case RC_TREND:
                chartExcelEntityList = rcAnalysisService.rcTrendExcel(baseCondition);
                break;
            case RC_TOP_DEPT:
                chartExcelEntityList = rcAnalysisService.topRcExcel(baseCondition);
                break;
        }
        return chartExcelEntityList;
    }


    @Override
    public String buildExcelName(BaseQueryConditionBO request, String uid, String lang) {
        String middle = StringUtils.EMPTY;
        String fileName_format = "%s_%s_%s";
        if (StringUtils.isEmpty(request.getIndex())) {
            return super.buildExcelName();
        }
        IndexEnum indexEnum = IndexEnum.valueOf(request.getIndex());
        switch (indexEnum) {
            case RC_OVERVIEW_PERCENT:
            case RC_FLIGHT_PERCENT:
            case RC_HOTEL_PERCENT:
            case RC_TRAIN_PERCENT:
                middle = SharkUtils.get("RCAnalysis.RCNumPct", lang);
                break;
            case RC_OVERVIEW_DETAIL:
            case RC_FLIGHT_DETAIL:
            case RC_HOTEL_DETAIL:
            case RC_TRAIN_DETAIL:
                middle = SharkUtils.get("RCAnalysis.RCAnalysisbyProduct", lang);
                break;
            case RC_TREND:
                middle = SharkUtils.get("RCAnalysis.RCTrend", lang);
                break;
            case RC_TOP_DEPT:
                AnalysisObjectEnum analysisObjectEnum = AnalysisObjectEnum.valueOf(request.getAnalysisObject().toUpperCase());
                if (StringUtils.isNotEmpty(request.getQueryBu())) {
                    middle = String.format("%s-%s", getDetailMiddleName(analysisObjectEnum, lang), getBuName(QueryReportBuTypeEnum.valueOf(request.getQueryBu()), lang));
                } else {
                    middle = getDetailMiddleName(analysisObjectEnum, lang);
                }
                break;
            default:
                break;
        }
        String prefix = SharkUtils.get("Catalog.ComplianceMonitor", lang);
        String suffix = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
        return String.format(fileName_format, prefix, middle, uid, suffix);
    }

    /**
     * RC_OVERVIEW, RC_OVERVIEW_COUNT, RC_OVERVIEW_DETAIL,
     * RC_FLIGHT, RC_FLIGHT_COUNT, RC_FLIGHT_DETAIL,
     * RC_HOTEL, RC_HOTEL_COUNT, RC_HOTEL_DETAIL,
     * RC_TRAIN, RC_TRAIN_COUNT, RC_TRAIN_DETAIL,
     * RC_TREND, RC_TOP_DEPT
     */
    enum IndexEnum {
        RC_OVERVIEW, RC_OVERVIEW_PERCENT, RC_OVERVIEW_DETAIL,
        RC_FLIGHT, RC_FLIGHT_PERCENT, RC_FLIGHT_DETAIL,
        RC_HOTEL, RC_HOTEL_PERCENT, RC_HOTEL_DETAIL,
        RC_TRAIN, RC_TRAIN_PERCENT, RC_TRAIN_DETAIL,
        RC_TREND, RC_TOP_DEPT
    }

    private String getDetailMiddleName(AnalysisObjectEnum analysisObjectEnum, String lang) {
        String middleName = StringUtils.EMPTY;
        if (analysisObjectEnum == AnalysisObjectEnum.UID) {
            middleName = SharkUtils.get("RCAnalysis.RCTopEmployeeDetail", lang);
        } else {
            middleName = SharkUtils.get("RCAnalysis.RCTopDeptDetail", lang);
        }
        return middleName;
    }

    private String getBuName(QueryReportBuTypeEnum queryReportBuTypeEnum, String lang) {
        String buName = StringUtils.EMPTY;
        switch (queryReportBuTypeEnum) {
            case overview:
                buName = SharkUtils.get("Public.all", lang);
                break;
            case flight:
                buName = SharkUtils.get("Index.air", lang);
                break;
            case hotel:
                buName = SharkUtils.get("Index.hotel", lang);
                break;
            case train:
                buName = SharkUtils.get("Index.train", lang);
                break;
            default:
                break;
        }
        return buName;
    }
}
