package com.corpgovernment.resource.schedule.hotel.impl;

import cn.hutool.core.collection.CollUtil;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.core.hotel.soa.HotelClient;
import com.corpgovernment.resource.schedule.domain.hotel.enums.TaskEnum;
import com.corpgovernment.resource.schedule.domain.hotel.gateway.ITaskGateway;
import com.corpgovernment.resource.schedule.domain.hotel.model.HotelIdTaskParam;
import com.corpgovernment.resource.schedule.domain.hotel.model.HotelStaticResourceTaskParam;
import com.corpgovernment.resource.schedule.domain.hotel.model.SupplierControl;
import com.corpgovernment.resource.schedule.domain.hotel.model.Task;
import com.corpgovernment.resource.schedule.hotel.AbstractTaskService;
import com.corpgovernment.resource.schedule.hotel.dao.db.IHotelCityDao;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/18
 */
@Service
public class HotelIdTaskService extends AbstractTaskService {

    @Autowired
    private IHotelCityDao hotelCityDao;
    @Autowired
    private HotelClient hotelClient;
    @Autowired
    private ITaskGateway taskGateway;

    @Override
    protected List<Task> createTask(String supplierCode) {
        SupplierControl supplierControl = taskGateway.getSupplierControl(supplierCode);
        if (supplierControl == null) {
            return new ArrayList<>(0);
        }

        String listHotelCitySql = supplierControl.getListHotelCitySql();
        if (StringUtils.isBlank(listHotelCitySql)) {
            return new ArrayList<>(0);
        }

        List<String> cityIdList;
        String tmpString = listHotelCitySql.replace("is_deleted", "");
        // sql模式
        if (StringUtils.containsIgnoreCase(tmpString, "select") &&
                !StringUtils.containsIgnoreCase(tmpString, "delete") &&
                !StringUtils.containsIgnoreCase(tmpString, "insert") &&
                !StringUtils.containsIgnoreCase(tmpString, "update")) {
            // 查询城市
            cityIdList = hotelCityDao.list(listHotelCitySql);
        } else {
            cityIdList = Arrays.stream(listHotelCitySql.split(",")).map(String::trim).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(cityIdList)) {
            return new ArrayList<>(0);
        }
        return cityIdList.stream().map(item -> convert(item, supplierCode)).collect(Collectors.toList());
    }

    private Task convert(String cityId, String supplierCode) {
        Task task = new Task();
        HotelIdTaskParam hotelIdTaskParam = new HotelIdTaskParam();
        hotelIdTaskParam.setSupplierCode(supplierCode);
        hotelIdTaskParam.setCityId(cityId);
        task.setParam(JsonUtils.toJsonString(hotelIdTaskParam));
        return task;
    }

    @Override
    protected JSONResult<?> processTask(Task task) {
        HotelIdTaskParam paramDto = JsonUtils.parse(task.getParam(), HotelIdTaskParam.class);
        return hotelClient.saveHotelIdList(paramDto.getSupplierCode(), paramDto.getCityId());
    }

    @Override
    protected List<Task> deliverTask(String supplierCode, JSONResult<?> jsonResult, Task task, SupplierControl supplierControl) {
        List<String> hotelIdList = JsonUtils.convert(jsonResult.getData(), new TypeReference<List<String>>() {});
        if (CollectionUtils.isEmpty(hotelIdList)) {
            return new ArrayList<>(0);
        }
        Integer batchSize = supplierControl.getHotelStaticDetailBatchSize();
        if (batchSize == null || batchSize <= 0) {
            batchSize = 25;
        }
        // 构建任务
        List<Task> taskList = new ArrayList<>(hotelIdList.size()/batchSize);
        CollUtil.split(hotelIdList, batchSize).forEach(item -> {
            Task taskV2 = new Task();
            taskV2.setName(TaskEnum.HOTEL_STATIC_RESOURCE_TASK.name());
            HotelStaticResourceTaskParam paramDto = new HotelStaticResourceTaskParam();
            paramDto.setHotelIdList(item);
            paramDto.setSupplierCode(supplierCode);
            taskV2.setParam(JsonUtils.toJsonString(paramDto));
            taskList.add(taskV2);
        });
        return taskList;
    }

    @Override
    protected List<SupplierControl> listSupplierControl() {
        List<SupplierControl> supplierControlList = taskGateway.listSupplierControl();
        if (CollectionUtils.isEmpty(supplierControlList)) {
            return new ArrayList<>(0);
        }
        List<SupplierControl> tmpList = supplierControlList.stream()
                .filter(item -> item != null && item.getHotelIdTaskFlow() != null && item.getHotelIdTaskFlow() > 0).collect(Collectors.toList());
        tmpList.forEach(item -> item.setTaskFlow(item.getHotelIdTaskFlow()));
        return tmpList;
    }

    @Override
    public TaskEnum taskEnum() {
        return TaskEnum.HOTEL_ID_TASK;
    }
}
