package com.corpgovernment.resource.schedule.geography.service;

import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.core.geography.dto.GeographyAuthResponseDto;
import com.corpgovernment.resource.core.geography.dto.GeographyResponseDto;
import com.corpgovernment.resource.schedule.domain.geography.model.SpecifyGeographyUpdateModel;
import com.corpgovernment.resource.schedule.geography.apollo.GeographyApolloDao;
import com.corpgovernment.resource.schedule.geography.gatewayimpl.GeographyTaskGatewayImpl;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> zhang
 * @date 2023/12/26 11:56
 */
@Slf4j
@Component
public class GeographyTaskService {

    @Autowired
    private GeographyTaskGatewayImpl geographyTaskGateway;
    @Autowired
    private GeographyApolloDao geographyApolloDao;

    /**
     * 地理数据任务初始化—城市ID
     */
    public void initGeographyTask() {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        geographyTaskGateway.initRedisGeographyInfo();
    }

    /**
     * 地理数据任务初始化—国家ID
     */
    public void initRedisGeographyInfoByCountryIds() {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        geographyTaskGateway.initRedisGeographyInfoByCountryIds();
    }

    public void pullAuthTicket() {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("StartPullAuthTicket");
        JSONResult<GeographyAuthResponseDto> result = geographyTaskGateway.pullAuthTicket();
        log.info("EndPullAuthTicket,result:{}", result);
        if (Objects.isNull(result)) {
            log.error("pullAuthTicket Failed! {}", result);
        }
    }

    public void pullCountryInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullCountryInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullCountryInfo(update);
        log.info("pullCountryInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullCountryInfo Failed! {}", result);
        }
    }

    public void pullProvinceInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullProvinceInfo(update);
        if (Objects.isNull(result)) {
            log.error("pullProvinceInfo Failed! {}", result);
        }
    }

    public void pullCityInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullCityInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullCityInfo(update);
        log.info("pullCityInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullCityInfo Failed! {}", result);
        }
    }

    public void pullAirportInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullAirportInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullAirportInfo(update);
        log.info("pullAirportInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullAirportInfo Failed! {}", result);
        }
    }

    public void pullTrainStationInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullTrainStationInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullTrainStationInfo(update);
        log.info("pullTrainStationInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullTrainStationInfo Failed! {}", result);
        }
    }

    public void pullBusStationInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullBusStationInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullBusStationInfo(update);
        log.info("pullBusStationInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullBusStationInfo Failed! {}", result);
        }
    }

    public void pullDomesticZoneInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullDomesticZoneInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullDomesticZoneInfo(update);
        log.info("pullDomesticZoneInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullDomesticZoneInfo Failed! {}", result);
        }
    }

    public void pullTimeZoneInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullTimeZoneInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullTimeZoneInfo(update);
        log.info("pullTimeZoneInfo result :{}", JsonUtils.toJsonString(result));
        if (Objects.isNull(result)) {
            log.error("pullTimeZoneInfo Failed! {}", result);
        }
    }

    public void pullCorpBrandInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullCorpBrandInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullCorpBrandInfo(update);
        log.info("pullCorpBrandInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullCorpBrandInfo Failed! {}", result);
        }
    }

    public void pullMetroLineInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullMetroLineInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullMetroLineInfo(update);
        log.info("pullMetroLineInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullMetroLineInfo Failed! {}", result);
        }
    }

    public void pullCityLandmarkInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullCityLandmarkInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullCityLandmarkInfo(update);
        log.info("pullCityLandmarkInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullCityLandmarkInfo Failed! {}", result);
        }
    }

    public void pullHotelCityInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullHotelCityInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullHotelCityInfo(update);
        log.info("pullHotelCityInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullHotelCityInfo Failed! {}", result);
        }
    }

    public void pullMeiYaCityInfo(Boolean update) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        log.info("pullMeiYaCityInfo update :{}", update);
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.pullMeiYaCityInfo(update);
        log.info("pullMeiYaCityInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("pullMeiYaCityInfo Failed! {}", result);
        }
    }


    public void executeGeographyError(String param) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment) {
            return;
        }
        JSONResult<GeographyResponseDto> result = geographyTaskGateway.executeGeographyError(param);
        log.info("executeGeographyError result :{}", result);
        if (Objects.isNull(result)) {
            log.error("executeGeographyError Failed! {}", result);
        }
    }

    public void updateGeographyInfoByTypeAndId(String param) {
        Boolean preEnvironment = geographyApolloDao.getSupplierDistinguishPreUat();
        log.info("GeographyTaskService supplierDistinguishPreUat {}", preEnvironment);
        if (!preEnvironment || StringUtils.isBlank(param)) {
            return;
        }
        SpecifyGeographyUpdateModel specifyTypeAndId = JsonUtils.parse(param, SpecifyGeographyUpdateModel.class);
        if (Objects.isNull(specifyTypeAndId)){
            return;
        }
        JSONResult<GeographyResponseDto> result = null;
        if (Objects.nonNull(specifyTypeAndId.getCityId()) || Objects.nonNull(specifyTypeAndId.getCountryId()) || Objects.nonNull(specifyTypeAndId.getProvinceId())){
            result = geographyTaskGateway.updateGeographyInfoByTypeAndId(specifyTypeAndId);
        }else {
            result = geographyTaskGateway.updateGeographyInfoByType(specifyTypeAndId.getGeographyType());
        }
        log.info("executeGeographyError result :{}", result);
        if (Objects.isNull(result)) {
            log.error("executeGeographyError Failed! {}", result);
        }
    }

    public void mappingCityInfo(String param) {
        if (StringUtils.isBlank(param)){
            return;
        }
        JSONResult<GeographyResponseDto> result = null;
        result = geographyTaskGateway.mappingCityInfo(param);
        log.info("mappingCityInfo result :{}", result);
        if (Objects.isNull(result)) {
            log.error("mappingCityInfo Failed! {}", result);
        }
    }
}
