package com.corpgovernment.resource.schedule.onlinereport.behavior;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.customreport.CustomReportNewService;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.MarkMetricInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.MarkMetricTrendAndOverview;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMarkMetricTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMarkMetricTrendResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPreOrderDateRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPreOrderDateResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PreOrderDateInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PreOrderdateRange;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.po.CustomReportInfoNewPO;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.CommonUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import com.google.common.collect.ImmutableMap;
import onlinereport.enums.PageErrorCodeEnum;
import onlinereport.enums.ReportTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc
 */
@Service
public class PreOrderdateBehaviorAnalysisAdaptor extends BaseReportDataAdaptor<Object> {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private CustomReportNewService customReportNewService;

    @Autowired
    private PreOrderdateBehaviorAnalysisExcelService preOrderdateBehaviorAnalysisExcelService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String reportId = baseCondition.getBaseQueryCondition().getReportId();
        String lang = baseCondition.getLang();
        String index = baseCondition.getIndex();
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:FltPreOrderDays", reportId)) {
            if (StringUtils.equalsIgnoreCase(index, "trend")) {
                OnlineReportMarkMetricTrendRequest request = new OnlineReportMarkMetricTrendRequest();
                request.setProductType(baseCondition.getProductType());
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
                request.setQueryBu(QueryReportBuTypeEnum.flight);
                extMap.put("metric", "AVG_PRE_ORDERDATE_TREND");
                request.setExtData(extMap);
                OnlineReportMarkMetricTrendResponse responseType = corpOnlineReportPlatformService.queryMarkMetricTrend(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    return metricOverview(Optional.ofNullable(responseType.getMarkMetric()).orElse(new MarkMetricTrendAndOverview()), lang);
                } else {
                    erroThrow(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
                }
            }
            if (StringUtils.equalsIgnoreCase(index, "dis") || (StringUtils.equalsIgnoreCase(index, "detail"))) {
                OnlineReportPreOrderDateRequest request = new OnlineReportPreOrderDateRequest();
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setProductType(baseCondition.getProductType());
                request.setQueryBu(QueryReportBuTypeEnum.flight);
                //request.setRangeList(getCustomPreorderdate(userPermissionsBo.getUid()));
                Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
                extMap.put("from", "PC");
                request.setExtData(extMap);
                OnlineReportPreOrderDateResponse responseType = corpOnlineReportPlatformService.queryPreOrderDateAnalysis(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    if (StringUtils.equalsIgnoreCase(index, "dis")) {
                        return flgihtPreorderdateDis(Optional.ofNullable(responseType.getPreOrderDateList()).orElse(new ArrayList<>()), lang);
                    }
                    if (StringUtils.equalsIgnoreCase(index, "detail")) {
                        return flgihtPreorderdateDetail(Optional.ofNullable(responseType.getPreOrderDateList()).orElse(new ArrayList<>()), lang, baseCondition.getProductType());
                    }
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(responseType)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                    }
                    throw businessException;
                }
            }
        }
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HtlPreOrderDays", reportId)) {
            OnlineReportPreOrderDateRequest request = new OnlineReportPreOrderDateRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setQueryBu(QueryReportBuTypeEnum.hotel);
            request.setProductType(baseCondition.getProductType());
            OnlineReportPreOrderDateResponse responseType = corpOnlineReportPlatformService.queryPreOrderDateAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return hotelAvgPreOrderDate(Optional.ofNullable(responseType.getPreOrderDateList()).orElse(new ArrayList<>()), lang);
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        }
        return null;
    }

    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        return preOrderdateBehaviorAnalysisExcelService.buildExcel(userPermissionsBo, baseCondition);
    }

    @Override
    public String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang) {
        String reportId = baseCondition.getBaseQueryCondition().getReportId();
        String fileName_format = "%s_%s_%s_%s";
        String middle = StringUtils.EMPTY;
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:FltPreOrderDays", reportId)) {
            middle = SharkUtils.get("Travelanalysis.advancedbooking", lang);
        }
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HtlPreOrderDays", reportId)) {
            middle = SharkUtils.get("Save.PredateAys", lang);
        }
        String prefix = SharkUtils.get("Catalog.BehaviorAnalysis", lang);
        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
        return String.format(fileName_format, prefix, middle, uid, str);
    }

    private Map metricTrend(List<MarkMetricInfo> markMetricInfoList, String lang) {
        List trendLegends = new ArrayList();
        Map trendLegendCompany = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.public", lang))
                .put("key", "company").build();
        trendLegends.add(trendLegendCompany);
        Map trendLegendIndustry = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.industry", lang))
                .put("key", "industry").build();
        trendLegends.add(trendLegendIndustry);
        Map trendLegendCorp = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.tmc", lang))
                .put("key", "corp").build();
        trendLegends.add(trendLegendCorp);
        List trendDataList = new ArrayList();
        for (MarkMetricInfo markMetricInfo : markMetricInfoList) {
            Map trendData = ImmutableMap.builder()
                    .put("company", MapperUtils.convertDigitToZero(markMetricInfo.getCompanyMetric()))
                    .put("industry", MapperUtils.convertDigitToZero(markMetricInfo.getIndustryMetric()))
                    .put("corp", MapperUtils.convertDigitToZero(markMetricInfo.getCorpMetric())).build();
            trendDataList.add(ImmutableMap.builder()
                    .put("axis", markMetricInfo.getDim())
                    .put("data", trendData).build());
        }
        return ImmutableMap.builder()
                .put("legends", trendLegends)
                .put("data", trendDataList).build();
    }

    private Map metricOverview(MarkMetricTrendAndOverview markMetricTrendAndOverview, String lang) {
        List<MarkMetricInfo> markMetricInfoList = Optional.ofNullable(markMetricTrendAndOverview.getMarkMetricTrend()).orElse(new ArrayList<>());
        MarkMetricInfo markMetric = Optional.ofNullable(markMetricTrendAndOverview.getMarkMetricInfo()).orElse(new MarkMetricInfo());
        Map overviewMap = ImmutableMap.builder()
                .put("company", markMetric.getCompanyMetric())
                .put("corp", markMetric.getCorpMetric())
                .put("industry", markMetric.getIndustryMetric()).build();
        return ImmutableMap.builder()
                .put("overview", overviewMap)
                .put("trend", metricTrend(markMetricInfoList, lang)).build();
    }

    private Object hotelAvgPreOrderDate(List<PreOrderDateInfo> preOrderDateInfoList, String lang) {
        List quantityDataList = new ArrayList();
        PreOrderDateRangeEnum[] preOrderDateRangeEnums = PreOrderDateRangeEnum.values();
        for (PreOrderDateRangeEnum preOrderDateRangeEnum : preOrderDateRangeEnums) {
            PreOrderDateInfo preOrderDateInfo = preOrderDateInfoList.stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getRange(), preOrderDateRangeEnum.getName().toString()))
                    .findFirst().orElse(new PreOrderDateInfo());
            Map trendAmountQuantityData = ImmutableMap.builder()
                    .put("quantity", MapperUtils.convertDigitToZero(preOrderDateInfo.getTotalQuantity()))
                    .put("percentage", MapperUtils.convertDigitToZero(preOrderDateInfo.getQuantityPercent()))
                    .put("avgNightPrice", MapperUtils.convertDigitToZero(preOrderDateInfo.getAvgNightPrice())).build();
            quantityDataList.add(ImmutableMap.builder()
                    .put("axis", SharkUtils.get(preOrderDateRangeEnum.getSharkKey(), lang))
                    .put("data", trendAmountQuantityData).build());
        }
        return quantityDataList;
    }

    private Map flgihtPreorderdateDetail(List<PreOrderDateInfo> preOrderDateInfoList, String lang, String productType) {
        List detailDataList = new ArrayList();
        List title = new ArrayList();
        if (StringUtils.equalsIgnoreCase(productType, "inter")) {
            for (PreOrderDateInfo preOrderDateInfo : preOrderDateInfoList) {
                List list = Arrays.asList(preOrderDateInfo.getRange()
                        , MapperUtils.convertDigitToZero(preOrderDateInfo.getTotalQuantity())
                        , MapperUtils.convertDigitToZeroString(preOrderDateInfo.getQuantityPercent()).concat(GlobalConst.PERCENT_QUOTE)
                        , CommonUtils.fmtMicrometer(Optional.ofNullable(preOrderDateInfo.getTotalPrice()).orElse(BigDecimal.ZERO))
                        , MapperUtils.convertDigitToZeroString(preOrderDateInfo.getRefundPercent()).concat(GlobalConst.PERCENT_QUOTE)
                        , MapperUtils.convertDigitToZeroString(preOrderDateInfo.getRebookPercent()).concat(GlobalConst.PERCENT_QUOTE));
                detailDataList.add(list);
            }

            title = Arrays.asList(SharkUtils.get("Travelanalysis.advancedbooking", lang), SharkUtils.get("Index.num", lang),
                    SharkUtils.get("Index.numpercentage", lang), SharkUtils.get("Index.netprice", lang),
                    SharkUtils.get("Exceltopname.refundrate", lang), SharkUtils.get("Exceltopname.changerate", lang));
        } else {
            for (PreOrderDateInfo preOrderDateInfo : preOrderDateInfoList) {
                List list = Arrays.asList(preOrderDateInfo.getRange()
                        , MapperUtils.convertDigitToZero(preOrderDateInfo.getTotalQuantity())
                        , MapperUtils.convertDigitToZeroString(preOrderDateInfo.getQuantityPercent()).concat(GlobalConst.PERCENT_QUOTE)
                        , CommonUtils.fmtMicrometer(Optional.ofNullable(preOrderDateInfo.getTotalPrice()).orElse(BigDecimal.ZERO))
                        , MapperUtils.convertDigitToZeroString(preOrderDateInfo.getRefundPercent()).concat(GlobalConst.PERCENT_QUOTE)
                        , MapperUtils.convertDigitToZeroString(preOrderDateInfo.getRebookPercent()).concat(GlobalConst.PERCENT_QUOTE));
                detailDataList.add(list);
            }

            title = Arrays.asList(SharkUtils.get("Travelanalysis.advancedbooking", lang), SharkUtils.get("Index.num", lang),
                    SharkUtils.get("Index.numpercentage", lang), SharkUtils.get("Index.netprice", lang),
                    SharkUtils.get("Exceltopname.refundrate", lang), SharkUtils.get("Exceltopname.changerate", lang));
        }

        // 明细
        return ImmutableMap.builder()
                .put("data", detailDataList)
                .put("title", title).build();
    }

    private Map flgihtPreorderdateDis(List<PreOrderDateInfo> preOrderDateInfoList, String lang) {
        // 票张数、成交净价
        List quantityLegends = new ArrayList();
        Map legendQuantity = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.num", lang))
                .put("key", "quantity")
                .put("percentage", "quantityPercent").build();
        quantityLegends.add(legendQuantity);
        Map legendAmount = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.money", lang))
                .put("key", "price")
                .put("percentage", "pricePercent").build();
        quantityLegends.add(legendAmount);
        List quantityDataList = new ArrayList();
        for (PreOrderDateInfo preOrderDateInfo : preOrderDateInfoList) {
            Map trendAmountQuantityData = ImmutableMap.builder()
                    .put("quantity", MapperUtils.convertDigitToZero(preOrderDateInfo.getTotalQuantity()))
                    .put("quantityPercent", MapperUtils.convertDigitToZero(preOrderDateInfo.getQuantityPercent()))
                    .put("price", MapperUtils.convertDigitToZero(preOrderDateInfo.getTotalPrice()))
                    .put("pricePercent", MapperUtils.convertDigitToZero(preOrderDateInfo.getPricePercent())).build();
            quantityDataList.add(ImmutableMap.builder()
                    .put("axis", preOrderDateInfo.getRange())
                    .put("data", trendAmountQuantityData).build());
        }
        // 分布
        return ImmutableMap.builder()
                .put("legends", quantityLegends)
                .put("data", quantityDataList).build();
    }

    private List<PreOrderdateRange> getCustomPreorderdate(String uid) {
        List<PreOrderdateRange> rangeList = new ArrayList<>();
        try {
            CustomReportInfoNewPO customReportInfo = customReportNewService.queryByUid(uid, ReportTypeEnum.CustomPreOrderDate.getValue());
            if (customReportInfo != null && StringUtils.isNotEmpty(customReportInfo.getReportCondition())) {
                if (null != customReportInfo && org.apache.commons.lang.StringUtils.isNotEmpty(customReportInfo.getReportCondition())
                        && customReportInfo.getReportCondition().trim().startsWith("[")
                        && customReportInfo.getReportCondition().trim().endsWith("]")) {
                    //[[0,2],[3,5],[6,20]]
                    String subStr = customReportInfo.getReportCondition().substring(1, customReportInfo.getReportCondition().length() - 1);
                    //[0,2],[3,5],[6,20]
                    boolean add = false;
                    String sub = null;
                    PreOrderdateRange entity = null;
                    char[] charArr = subStr.toCharArray();
                    for (char c : charArr) {
                        if (c == '[') {
                            add = true;
                            sub = new String();
                            entity = new PreOrderdateRange();
                        } else if (c == ']') {
                            add = false;
                            entity.setEnd(Integer.valueOf(sub));
                            rangeList.add(entity);
                        } else if (add && c == ',') {
                            entity.setStart(Integer.valueOf(sub));
                            sub = new String();
                        } else if (add && '0' <= c && c <= '9') {
                            sub = sub + c;
                        }
                    }
                }
            } else {
                rangeList = defaultCustomPreorderdate();
            }
        } catch (Exception e) {
            rangeList = defaultCustomPreorderdate();
        }
        return rangeList;
    }

    private List<PreOrderdateRange> defaultCustomPreorderdate() {
        List<PreOrderdateRange> rangeList = new ArrayList<>();
        rangeList.add(new PreOrderdateRange(0, 0));
        rangeList.add(new PreOrderdateRange(1, 1));
        rangeList.add(new PreOrderdateRange(2, 2));
        rangeList.add(new PreOrderdateRange(3, 3));
        rangeList.add(new PreOrderdateRange(4, 4));
        rangeList.add(new PreOrderdateRange(5, 365));
        return rangeList;
    }

    /**
     * 0天，1-3 天，4-7 天，8-15天，16天及以上
     */
    enum PreOrderDateRangeEnum {
        RANGE1("rangeOne", "0", "App.Behavior.Preorderdate1"),
        RANGE2("rangeTwo", "1-3", "App.Behavior.Preorderdate2"),
        RANGE3("rangeThree", "4-7", "App.Behavior.Preorderdate3"),
        RANGE4("rangeFour", "8-15", "App.Behavior.Preorderdate4"),
        RANGE5("rangeFive", "16", "App.Behavior.Preorderdate5"),
        ;
        String key;
        String name;
        String sharkKey;

        PreOrderDateRangeEnum(String k, String n, String s) {
            this.key = k;
            this.name = n;
            this.sharkKey = s;
        }

        public String getKey() {
            return key;
        }

        public String getName() {
            return name;
        }

        public String getSharkKey() {
            return sharkKey;
        }
    }

}
