package com.corpgovernment.resource.schedule.hotelaudit.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-23 20:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuditHotelReqVo {
    
    private OrderIdMode orderIdMode;
    
    private TimeRangeMode timeRangeMode;
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OrderIdMode {
        
        private List<String> orderIdList;
        
        private String tenantId;
        
        private String productType;
        
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TimeRangeMode {
        
        private List<String> tenantIdList;
        
        private String startOrderTime;
        
        private String endOrderTime;
        
    }
    
}
