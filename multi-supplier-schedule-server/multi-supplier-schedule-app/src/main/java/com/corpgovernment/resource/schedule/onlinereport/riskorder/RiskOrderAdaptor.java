//package com.corpgovernment.resource.schedule.onlinereport.riskorder;
//
//import com.ctrip.corp.onlinereportweb.domain.bo.common.BusinessException;
//import com.ctrip.corp.onlinereportweb.domain.bo.entity.UserPermissionsBo;
//import com.ctrip.corp.onlinereportweb.domainNew.entity.ChartExcelEntity;
//import com.ctrip.corp.onlinereportweb.domainreport.adaptor.BaseReportDataAdaptor;
//import com.ctrip.corp.onlinereportweb.domainreport.bo.BaseQueryConditionBO;
//import com.ctrip.framework.clogging.agent.log.ILog;
//import com.ctrip.framework.clogging.agent.log.LogManager;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @date ：Created in 2022/4/13 13:38
// * @description：
// * @modified By：
// * @version: $
// */
//@Service
//public class RiskOrderAdaptor extends BaseReportDataAdaptor<Object> {
//
//
//    @Autowired
//    RiskOrderSummaryService summaryService;
//
//    @Autowired
//    RiskOrderDetailService detailService;
//
//    @Autowired
//    RiskOrderOperatorService riskOrderOperatorService;
//
//    @Autowired
//    RiskOrderReportService riskOrderReportService;
//
//    @Override
//    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
//        Map resp = null;
//        INDEX index = INDEX.valueOf(baseCondition.getIndex());
//
//        if (index == INDEX.TOTAL_VIEW) {
//            resp = summaryService.getRiskOrderTotal(baseCondition, userPermissionsBo);
//        }
//
//        if (index == INDEX.OVERVIEW) {
//            resp = summaryService.getRiskOrderSummary(baseCondition);
//        }
//
//        if (index == INDEX.DETAIL) {
//            resp = detailService.getRiskOrderDetail(baseCondition);
//        }
//
//        if (index == INDEX.OPERATE) {
//            resp = riskOrderOperatorService.operateOrderDetail(baseCondition);
//        }
//
//        if (index == INDEX.REPORT) {
//            resp = riskOrderReportService.queryHotelCashOutHotels(baseCondition);
//        }
//
//        return resp;
//    }
//
//    @Override
//    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
//        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
//        INDEX index = INDEX.valueOf(baseCondition.getIndex());
//        if (index == INDEX.DETAIL) {
//            return detailService.getRiskOrderDetailExcel(baseCondition);
//        }
//        return chartExcelEntityList;
//    }
//
//    @Override
//    public String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang) {
//        INDEX index = INDEX.valueOf(baseCondition.getIndex());
//        if (index == INDEX.DETAIL) {
//            return detailService.getFileName(baseCondition, uid, lang);
//        }
//        return "";
//    }
//
//    enum INDEX {
//        TOTAL_VIEW, OVERVIEW, DETAIL, OPERATE, REPORT
//    }
//
//}
