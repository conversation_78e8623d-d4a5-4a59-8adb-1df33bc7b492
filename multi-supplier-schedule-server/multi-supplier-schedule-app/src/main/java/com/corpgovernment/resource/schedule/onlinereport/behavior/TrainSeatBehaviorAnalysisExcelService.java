package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TrainBehaviorInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import onlinereport.enums.PageErrorCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc
 */
@Service
public class TrainSeatBehaviorAnalysisExcelService extends AbstractGenralExportDataService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    public List<ChartExcelEntity> buildExcel(BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        Map map = getConditionContent(baseCondition.getBaseQueryCondition().getUid(), baseCondition.getBaseQueryCondition(), lang);
        int sheetIndex = 0;
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        OnlineReportBehaviorAnalysisRequest request = new OnlineReportBehaviorAnalysisRequest();
        request.setProductType(baseCondition.getProductType());
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
        extMap.put("dim", "SEAT_TYPE");
        request.setQueryBu(QueryReportBuTypeEnum.train);
        request.setExtData(extMap);
        OnlineReportBehaviorAnalysisResponse responseType = corpOnlineReportPlatformService.queryBehaviorAnalysis(request);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            chartExcelEntityList.add(trainBehavior(Optional.ofNullable(responseType.getTrainBehaviorList()).orElse(new ArrayList<>()), lang, map, sheetIndex));
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(responseType)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
            }
            throw businessException;
        }
        return chartExcelEntityList;
    }

    private ChartExcelEntity trainBehavior(List<TrainBehaviorInfo> trainBehaviorInfoList, String lang, Map map, int sheetIndex) {
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(Arrays.asList(SharkUtils.get("Exceltopname.seat", lang), SharkUtils.get("Index.num", lang), SharkUtils.get("Index.numpercentage", lang)));
        List data = new ArrayList();
        for (TrainBehaviorInfo trainBehaviorInfo : trainBehaviorInfoList) {
            data.add(Arrays.asList(trainBehaviorInfo.getDim()
                    , MapperUtils.convertDigitToZero(trainBehaviorInfo.getTotalQuantity())
                    , MapperUtils.convertDigitToZeroString(trainBehaviorInfo.getQuantityPercent()).concat(GlobalConst.PERCENT_QUOTE)));
        }
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(SharkUtils.get("Exceltopname.seat", lang));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }
}
