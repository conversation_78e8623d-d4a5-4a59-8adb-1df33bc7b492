package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.hive.impl;

import com.corpgovernment.resource.schedule.domain.onlinereport.common.BaseDataUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.ChineseLanguageConfig;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveFilter;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HotelEntity;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.SrDao;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.HotelOrderDetailService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.SrSwitchConfig;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.hive.AbstractOrderDetailHiveService;
import onlinereport.enums.reportlib.HotelOrderDetailEnum;
import onlinereport.enums.reportlib.HotelOrderStatusEnum;
import onlinereport.enums.reportlib.HtlOrderStatusEnums;
import onlinereport.enums.reportlib.OrderDetailEnumerable;
import onlinereport.enums.reportlib.uid.HotelUidOrderDetailEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst.FOUR_DIGIT_NUM;


/**
 * Auther:abguo
 * Date:2019/8/21
 * Description:
 */
@Service
public class HotelOrderHiveService extends AbstractOrderDetailHiveService {

    @Autowired
    private HiveDao hiveDao;

    @Autowired
    private SrSwitchConfig srSwitchConfig;

    @Autowired
    private SrDao srDao;

    public static String getAllocationModeDesc(String mode, Map map) {
        if (StringUtils.isEmpty(mode) || MapUtils.isEmpty(map) || !map.containsKey(mode)) {
            return StringUtils.trimToEmpty(mode);
        }
        return (String) map.get(StringUtils.upperCase(mode));
    }

    @Override
    public OrderDetailEnumerable[] getEnum(String reportType) {
        if (StringUtils.equalsIgnoreCase(reportType, "H_UID_DETAIL")) {
            return HotelUidOrderDetailEnum.values();
        } else {
            return HotelOrderDetailEnum.values();
        }
    }

    /**
     * 酒店订单明细
     *
     * @param hiveFilter
     * @param lang
     * @param reportType
     * @return
     */
    public List<Map<String, Object>> queryOrderDetail(HiveFilter hiveFilter, String lang, String reportType) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<HotelEntity> hotelEntityList = null;
        if (srSwitchConfig.isOpenSrSwitch(hiveFilter.getUid(), "hotel")) {
            hotelEntityList = srDao.searchHotelOrderdetail(hiveFilter);
        } else {
            hotelEntityList = hiveDao.searchHotelOrderdetail(hiveFilter);
        }
        if (CollectionUtils.isEmpty(hotelEntityList)) {
            return result;
        }
        if (StringUtils.equalsIgnoreCase(reportType, "H_UID_DETAIL")) {
            return queryHotelUidOrderDetail(hotelEntityList, lang);
        } else {
            return queryHotelOrderDetail(hotelEntityList, lang);
        }
    }

    /**
     * 酒店订单明细
     *
     * @param hotelEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> queryHotelUidOrderDetail(List<HotelEntity> hotelEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(hotelEntityList)) {
            return result;
        }
        for (HotelEntity order : hotelEntityList) {
            Map<String, Object> map = new HashMap<>();
            map.put(HotelUidOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToString(order.getOrder_id()));
            map.put(HotelUidOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrder_status()));
            map.put(HotelUidOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
            map.put(HotelUidOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorp_corporation()));
            map.put(HotelUidOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
            map.put(HotelUidOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToString(order.getAccount_id()));
            map.put(HotelUidOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(HotelUidOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
            map.put(HotelUidOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRank_name()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCost_center1()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCost_center2()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCost_center3()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCost_center4()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCost_center5()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCost_center6()));
            map.put(HotelUidOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(HotelUidOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(HotelUidOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(HotelUidOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
            map.put(HotelUidOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
            map.put(HotelUidOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
            map.put(HotelUidOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
            map.put(HotelUidOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
            map.put(HotelUidOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
            map.put(HotelUidOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(HotelUidOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToString(order.getOrder_id()));
            map.put(HotelUidOrderDetailEnum.DEADPRICE.getCode(), DigitBaseUtils.formatDigit(order.getDead_price_onenight()));
            map.put(HotelUidOrderDetailEnum.STAR.getCode(), MapperUtils.trim(order.getStar()));
            map.put(HotelUidOrderDetailEnum.CITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getCity_name(), order.getCity_name_en()));
            map.put(HotelUidOrderDetailEnum.PROVINCENAME.getCode(), MapperUtils.getValByLang(lang, order.getProvince_name(), order.getProvince_name_en()));
            map.put(HotelUidOrderDetailEnum.COUNTRYNAME.getCode(), MapperUtils.getValByLang(lang, order.getCountry_name(), order.getCountry_name_en()));
            map.put(HotelUidOrderDetailEnum.CLIENTNAME.getCode(), MapperUtils.trim(order.getClient_name()));
            map.put(HotelUidOrderDetailEnum.ORDERTYPE.getCode(), MapperUtils.convertOrderType(order.getOrder_type()));
            map.put(HotelUidOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToString(order.getQuantity()));
            map.put(HotelUidOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrival_date_time()));
            map.put(HotelUidOrderDetailEnum.DEPARTUREDATETIME.getCode(), MapperUtils.trim(order.getDeparture_date_time()));
            map.put(HotelUidOrderDetailEnum.ROOMPRICE.getCode(), DigitBaseUtils.formatDigitV2(order.getRoom_price()));
            map.put(HotelUidOrderDetailEnum.AVGPRICE.getCode(), DigitBaseUtils.formatDigitV2(order.getAvgprice()));
            map.put(HotelUidOrderDetailEnum.CUSTOMEREVAL.getCode(), DigitBaseUtils.formatDigit(order.getCustomereval(), 1));
            map.put(HotelUidOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
            map.put(HotelUidOrderDetailEnum.HOTELGROUPNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotel_group_name(), order.getHotel_group_name_en()));
            map.put(HotelUidOrderDetailEnum.REALPAYWITHSERVICE.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay_with_servicefee()));
            map.put(HotelUidOrderDetailEnum.HOTELNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotel_name(), order.getHotel_name_en()));
            map.put(HotelUidOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccount_code()));
            map.put(HotelUidOrderDetailEnum.BASICROOMTYPENAME.getCode(), MapperUtils.trim(order.getBasic_room_type_name()));
            result.add(map);
        }
        return result;
    }

    /**
     * 酒店订单明细
     *
     * @param hotelEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> queryHotelOrderDetail(List<HotelEntity> hotelEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(hotelEntityList)) {
            return result;
        }
        Map yesOrNotMap = BaseDataUtils.getYesOrNoMap(lang);
        String accntPrepayType = ChineseLanguageConfig.get("Report.ReportLib.PrepayType.accnt");
        for (HotelEntity order : hotelEntityList) {
            Map<String, Object> map = new HashMap<>();
            map.put(HotelOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToString(order.getOrder_id()));
            map.put(HotelOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrder_status()));
            map.put(HotelOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
            map.put(HotelOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorp_corporation()));
            map.put(HotelOrderDetailEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorp_name()));
//            map.put(HotelOrderDetailEnum.COMPANYGROUP.getCode(), MapperUtils.trim(order.getCompanygroup()));
//            map.put(HotelOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
//            map.put(HotelOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToString(order.getAccount_id()));
//            map.put(HotelOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccount_code()));
//            map.put(HotelOrderDetailEnum.ACCOUNTNAME.getCode(), MapperUtils.trim(order.getAccount_name()));
//            map.put(HotelOrderDetailEnum.SUBACCOUNTID.getCode(), MapperUtils.convertDigitToString(order.getSub_account_id()));
//            map.put(HotelOrderDetailEnum.SUBACCOUNTCODE.getCode(), MapperUtils.trim(order.getSub_account_code()));
//            map.put(HotelOrderDetailEnum.SUBACCOUNTNAME.getCode(), MapperUtils.trim(order.getSub_account_name()));
            map.put(HotelOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(HotelOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
            map.put(HotelOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
            map.put(HotelOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRank_name()));
//            map.put(HotelOrderDetailEnum.WORKCITY.getCode(), MapperUtils.trim(order.getWork_city()));
            map.put(HotelOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCost_center1()));
            map.put(HotelOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCost_center2()));
            map.put(HotelOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCost_center3()));
            map.put(HotelOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCost_center4()));
            map.put(HotelOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCost_center5()));
            map.put(HotelOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCost_center6()));
            map.put(HotelOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(HotelOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(HotelOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(HotelOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
            map.put(HotelOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
            map.put(HotelOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
            map.put(HotelOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
            map.put(HotelOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
            map.put(HotelOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
            map.put(HotelOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(HotelOrderDetailEnum.ISONLINE.getCode(), MapperUtils.convertBookTypeSingle(order.getIs_online()));
//            map.put(HotelOrderDetailEnum.FEETYPE.getCode(), MapperUtils.trim(order.getFee_type()));
            map.put(HotelOrderDetailEnum.PREPAYTYPE.getCode(), HotelOrderDetailService.convertPrepayType(MapperUtils.trim(order.getPrepay_type())));
//            map.put(HotelOrderDetailEnum.ACBPREPAYTYPE.getCode(), MapperUtils.trim(order.getAcb_prepay_type()));
//            map.put(HotelOrderDetailEnum.ISBOSS.getCode(), MapperUtils.trim(order.getBosstype()));
            map.put(HotelOrderDetailEnum.JOURNEYNO.getCode(), MapperUtils.trim(order.getJourney_no()));
            map.put(HotelOrderDetailEnum.TRIPID.getCode(), MapperUtils.convertStrToDash(order.getTrip_id()));
//            map.put(HotelOrderDetailEnum.JOUNARYREASON.getCode(), MapperUtils.trim(order.getJourney_reason()));
//            map.put(HotelOrderDetailEnum.PROJECT.getCode(), MapperUtils.trim(order.getProject()));
//            map.put(HotelOrderDetailEnum.VERBALAUTHORIZE.getCode(), MapperUtils.convertTorF(order.getVerbal_authorize(), yesOrNotMap));
//            map.put(HotelOrderDetailEnum.CONFIRMPERSON.getCode(), MapperUtils.trim(order.getConfirm_person()));
//            map.put(HotelOrderDetailEnum.CONFIRMPTYPE.getCode(), MapperUtils.trim(order.getConfirm_type()));
//            map.put(HotelOrderDetailEnum.CONFIRMPERSON2.getCode(), MapperUtils.trim(order.getConfirm_person2()));
//            map.put(HotelOrderDetailEnum.CONFIRMPTYPE2.getCode(), MapperUtils.trim(order.getConfirm_type2()));
            map.put(HotelOrderDetailEnum.GROUPMONTH.getCode(), MapperUtils.convertDigitToString(order.getGroup_month()));
            map.put(HotelOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToString(order.getOrder_id()));
            map.put(HotelOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
            map.put(HotelOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            /////////////////////////////////////////////////////////////////////
            map.put(HotelOrderDetailEnum.DEALDATE.getCode(), MapperUtils.trim(order.getDeal_date()));
            map.put(HotelOrderDetailEnum.DEADPRICE.getCode(), DigitBaseUtils.formatDigit(order.getDead_price_onenight()));
            map.put(HotelOrderDetailEnum.ISMIXPAYMENT.getCode(), MapperUtils.convertTorF(order.getIs_mix_payment(), yesOrNotMap));
            map.put(HotelOrderDetailEnum.SETTLEMENTPERSONAMT.getCode(), DigitBaseUtils.formatDigit(convertPersonAmt(order, accntPrepayType)));
            map.put(HotelOrderDetailEnum.SETTLEMENTACCNTAMT.getCode(), DigitBaseUtils.formatDigit(convertAccntAmt(order, accntPrepayType)));
            map.put(HotelOrderDetailEnum.HOTELNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotel_name(), order.getHotel_name_en()));
//            map.put(HotelOrderDetailEnum.HOTELGROUPNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotel_group_name(), order.getHotel_group_name_en()));
            map.put(HotelOrderDetailEnum.HOTELBRANDNAME.getCode(), MapperUtils.trim(order.getHotel_brand_name()));
            map.put(HotelOrderDetailEnum.ISOVERSEA.getCode(), MapperUtils.convertIsOverSea(order.getIs_oversea(), lang));
            map.put(HotelOrderDetailEnum.STAR.getCode(), MapperUtils.trim(order.getStar()));
//            map.put(HotelOrderDetailEnum.ZONE.getCode(), MapperUtils.trim(order.getZone()));
            map.put(HotelOrderDetailEnum.LOCATION.getCode(), convertLocation(MapperUtils.trim(order.getLocation())));
            map.put(HotelOrderDetailEnum.CITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getCity_name(), order.getCity_name_en()));
//            map.put(HotelOrderDetailEnum.CITYLEVEL.getCode(), MapperUtils.trim(order.getPcitylevel())); // 使用父级城市id
            map.put(HotelOrderDetailEnum.PROVINCENAME.getCode(), MapperUtils.getValByLang(lang, order.getProvince_name(), order.getProvince_name_en()));
            map.put(HotelOrderDetailEnum.COUNTRYNAME.getCode(), MapperUtils.getValByLang(lang, order.getCountry_name(), order.getCountry_name_en()));
            map.put(HotelOrderDetailEnum.CLIENTNAME.getCode(), MapperUtils.trim(order.getClient_name()));
            map.put(HotelOrderDetailEnum.ORDERTYPE.getCode(), MapperUtils.convertOrderType(order.getOrder_type()));
            map.put(HotelOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            map.put(HotelOrderDetailEnum.ORDERROOMNUM.getCode(), MapperUtils.convertDigitToZero(order.getOrder_room_num()));
            map.put(HotelOrderDetailEnum.PERSONS.getCode(), MapperUtils.convertDigitToZero(order.getPersons()));
            map.put(HotelOrderDetailEnum.DAYNUM.getCode(), MapperUtils.convertDigitToZero(order.getDay_num()));
            map.put(HotelOrderDetailEnum.ARRIVALDATETIME.getCode(), subStrTime(MapperUtils.trim(order.getArrival_date_time())));
            map.put(HotelOrderDetailEnum.DEPARTUREDATETIME.getCode(), subStrTime(MapperUtils.trim(order.getDeparture_date_time())));
//            map.put(HotelOrderDetailEnum.ADDBREAKFASTPRICE.getCode(), DigitBaseUtils.formatDigit(order.getAdd_breakfast_price()));
            map.put(HotelOrderDetailEnum.POSTAMOUNT.getCode(), DigitBaseUtils.formatDigitV2(order.getPost_amount()));
            map.put(HotelOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getService_fee()));
            map.put(HotelOrderDetailEnum.REFUNDTIME.getCode(), MapperUtils.trim(order.getRefund_time()));
            map.put(HotelOrderDetailEnum.RFDAMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getRfd_amount()));
            map.put(HotelOrderDetailEnum.RFDQUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getRfd_quantity()));
            map.put(HotelOrderDetailEnum.BREAKFAST.getCode(), MapperUtils.convertDigitToZero(order.getBreakfast()));
//            map.put(HotelOrderDetailEnum.ISGDSORDER.getCode(), MapperUtils.convertTorF(order.getIs_gds_order(), yesOrNotMap));
//            map.put(HotelOrderDetailEnum.ISCU.getCode(), MapperUtils.convertTorF(order.getIs_cu(), yesOrNotMap));
//            map.put(HotelOrderDetailEnum.ISTMCPENJOY.getCode(), MapperUtils.convertTorF(order.getIs_tmcp_enjoy(), yesOrNotMap));
            map.put(HotelOrderDetailEnum.ROOMNAME.getCode(), MapperUtils.trim(order.getRoom_name()));
            map.put(HotelOrderDetailEnum.ISRC.getCode(), MapperUtils.convertTorF(order.getIs_rc(), yesOrNotMap));
            map.put(HotelOrderDetailEnum.REASONCODE.getCode(), MapperUtils.trim(order.getReason_code()));
            map.put(HotelOrderDetailEnum.LOWREASONINFO.getCode(), MapperUtils.getValByLang(lang, order.getLow_reasoninfo(), order.getLow_reasoninfo_en()));
            map.put(HotelOrderDetailEnum.LOWPRICEENVV.getCode(), MapperUtils.trim(order.getLow_price_en_vv()));
//            map.put(HotelOrderDetailEnum.MINPRICERC.getCode(), MapperUtils.trim(order.getMin_price_rc()));
//            map.put(HotelOrderDetailEnum.MINPRICERCVV.getCode(), MapperUtils.trim(order.getMin_price_rc_vv()));
//            map.put(HotelOrderDetailEnum.MINPRICERCREASONINFO.getCode(), MapperUtils.getValByLang(lang, order.getMin_price_rc_reasoninfo(), order.getMin_price_rc_reasoninfo_en()));
//            map.put(HotelOrderDetailEnum.AGREEMENTRC.getCode(), MapperUtils.trim(order.getAgreement_rc()));
//            map.put(HotelOrderDetailEnum.AGREEMENTRCVV.getCode(), MapperUtils.trim(order.getAgreement_rc_vv()));
//            map.put(HotelOrderDetailEnum.AGREEMENTREASONINFO.getCode(), MapperUtils.getValByLang(lang, order.getAgreement_reasoninfo(), order.getAgreement_reasoninfo_en()));
//            map.put(HotelOrderDetailEnum.OCURRENCY.getCode(), MapperUtils.trim(order.getO_currency()));
//            map.put(HotelOrderDetailEnum.OEXCHANGERATE.getCode(), DigitBaseUtils.formatDigit(order.getO_exchangerate(), FOUR_DIGIT_NUM));
            map.put(HotelOrderDetailEnum.POSTSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getPostservicefee()));
            map.put(HotelOrderDetailEnum.STARTTRUE.getCode(), MapperUtils.trim(order.getStar_true()));
            map.put(HotelOrderDetailEnum.MASTERHOTELID.getCode(), MapperUtils.convertDigitToString(order.getMasterhotelid()));
            map.put(HotelOrderDetailEnum.ROOMPRICE.getCode(), DigitBaseUtils.formatDigitV2(order.getRoom_price()));
//            map.put(HotelOrderDetailEnum.COUPONAMOUNT.getCode(), DigitBaseUtils.formatDigitV2(order.getCouponamount()));
            map.put(HotelOrderDetailEnum.SERVERFROMTYPE.getCode(), MapperUtils.trim(order.getServerfromtype()));
//            map.put(HotelOrderDetailEnum.ZIPCODE.getCode(), MapperUtils.trim(order.getZipcode()));
            map.put(HotelOrderDetailEnum.ADDRESS.getCode(), MapperUtils.trim(order.getAddress()));
//            map.put(HotelOrderDetailEnum.ISSAMEDAY.getCode(), MapperUtils.trim(order.getIs_sameday()));
//            map.put(HotelOrderDetailEnum.ISWORKTIME.getCode(), MapperUtils.trim(order.getIs_worktime()));
            map.put(HotelOrderDetailEnum.AVGPRICE.getCode(), DigitBaseUtils.formatDigitV2(order.getAvgprice()));
            map.put(HotelOrderDetailEnum.CORPREALPAY.getCode(), DigitBaseUtils.formatDigitV2(order.getCorp_real_pay()));
            map.put(HotelOrderDetailEnum.REALPAYWITHSERVICE.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay_with_servicefee()));
            map.put(HotelOrderDetailEnum.HOTELPASSENGERID.getCode(), MapperUtils.trim(order.getHotelpassengerid()));
//            map.put(HotelOrderDetailEnum.DOUBLEBOOKEDORDERID.getCode(), MapperUtils.trim(order.getDoublebookedorderid()));
//            map.put(HotelOrderDetailEnum.DOUBLEBOOKEDRC.getCode(), MapperUtils.trim(order.getDoublebookedrc()));
//            map.put(HotelOrderDetailEnum.DOUBLEBOOKEDRCDETAIL.getCode(), MapperUtils.trim(order.getDoublebookedrcdetail()));
//            map.put(HotelOrderDetailEnum.DOUBLEBOOKEDRCREASON.getCode(), MapperUtils.trim(order.getDoublebookedrcreason()));
//            map.put(HotelOrderDetailEnum.AUDITORID.getCode(), MapperUtils.trim(order.getAuditorid()));
//            map.put(HotelOrderDetailEnum.AUDITORID2.getCode(), MapperUtils.trim(order.getAuditorid2()));
            map.put(HotelOrderDetailEnum.SAVE_AMOUNT_3C.getCode(), DigitBaseUtils.formatDigit(order.getSave_amount_3c()));
//            map.put(HotelOrderDetailEnum.SAVE_AMOUNT_PROMOTION.getCode(), DigitBaseUtils.formatDigit(order.getSave_amount_promotion()));
//            map.put(HotelOrderDetailEnum.SAVE_AMOUNT_PREMINM.getCode(), DigitBaseUtils.formatDigit(order.getSave_amount_premium()));
            map.put(HotelOrderDetailEnum.CUSTOMEREVAL.getCode(), DigitBaseUtils.formatDigit(order.getCustomereval(), 1));
            map.put(HotelOrderDetailEnum.CONTROL_SAVE.getCode(), DigitBaseUtils.formatDigit(order.getSaving_price()));
//            map.put(HotelOrderDetailEnum.DEFINDFLAG.getCode(), MapperUtils.trim(order.getDefineflag()));
//            map.put(HotelOrderDetailEnum.DEFINDFLAG2.getCode(), MapperUtils.trim(order.getDefineflag2()));
            map.put(HotelOrderDetailEnum.ORIGINROOMDISTRIBUTIONPRICES.getCode(), MapperUtils.trim(order.getOrigin_room_distribution_prices()));
            map.put(HotelOrderDetailEnum.STDINDUSTRY1.getCode(), MapperUtils.trim(order.getStd_industry1()));
            map.put(HotelOrderDetailEnum.STDINDUSTRY2.getCode(), MapperUtils.trim(order.getStd_industry2()));
//            map.put(HotelOrderDetailEnum.ALLOCATION_MODE.getCode(), MapperUtils.trim(order.getAllocation_mode_desc()));
//            map.put(HotelOrderDetailEnum.HOUSESHARE_MODE_TYPE.getCode(), MapperUtils.trim(order.getHouseshare_mode_type()));
            result.add(map);
        }
        return result;
    }

    private String convertOrderStatus(String orderStauts) {
        Map<String, String> map = HotelOrderStatusEnum.toMap();

        return map.get(MapperUtils.trim(orderStauts));
    }

    private String convertLocation(String location) {
        if (StringUtils.equalsIgnoreCase("0", location)) {
            return GlobalConst.STRING_EMPTY;
        }
        return MapperUtils.trim(location);
    }

    private String subStrTime(String time) {
        if (StringUtils.isNotEmpty(time)) {
            return time.substring(0, 10);
        }
        return StringUtils.EMPTY;
    }

    private BigDecimal convertAccntAmt(HotelEntity order, String accntPrepayType) {
        // 公账支付金额
        Double result = 0D;
        String mixPayMent = order.getIs_mix_payment();
        // 混付
        if (StringUtils.equalsIgnoreCase(mixPayMent, "T")) {
            result = Optional.ofNullable(order.getSettlement_accnt_amt()).orElse(0F).doubleValue();
        } else {
            // 非混付
            // 公账支付方式
            List accntType = Arrays.asList("ACCNT", accntPrepayType, "APPAY", "AIRPLUS");
            String prepayType = order.getPrepay_type();
            if (accntType.stream().anyMatch(e -> StringUtils.equalsIgnoreCase(e.toString(), prepayType))) {
                //纯公付
                result = order.getReal_pay();
            }
        }
        return DigitBaseUtils.formatDigitV2(result);
    }

    private BigDecimal convertPersonAmt(HotelEntity order, String accntPrepayType) {
        // 个人支付金额
        Double result = 0D;
        if (StringUtils.equalsIgnoreCase(order.getIs_mix_payment(), "T")) {
            result = Optional.ofNullable(order.getSettlement_person_amt()).orElse(0F).doubleValue();
        } else {
            // 非混付
            List accntType = Arrays.asList("ACCNT", accntPrepayType, "APPAY", "AIRPLUS");
            String prepayType = order.getPrepay_type();
            if (accntType.stream().noneMatch(e -> StringUtils.equalsIgnoreCase(e.toString(), prepayType))) {
                //纯个付
                result = order.getReal_pay();
            }
        }
        return DigitBaseUtils.formatDigitV2(result);
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel;
    }
}
