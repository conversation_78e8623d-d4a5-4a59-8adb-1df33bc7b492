/*
package com.corpgovernment.resource.schedule.onlinereport.reportlib;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportTypeionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryYoyTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.*;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BasePermitVerfiyService;
import com.corpgovernment.resource.schedule.onlinereport.FltDiscountRangeEnum;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.BookTypeEnum;
import onlinereport.enums.FlightClassTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib.ReportLibCommonEntity.KPI_COUNT;
import static com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib.ReportLibCommonEntity.TOP_ONE_HUNDRED;
import static com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib.ReportLibRowEntity.*;


*/
/**
 * <AUTHOR>
 * @date 2020/9/21 15:12
 *//*

@Service
@Slf4j
public class OneKeyExportReportServiceNew {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private BasePermitVerfiyService basePermitVerfiyService;


    */
/**
     * 日期同比
     *
     * @param startTime YYYY-MM-DD
     * @param offset    需要向前推的偏移量
     * @return
     *//*

    public static String offsetTime(String startTime, int offset) {
        Date date = dateTimeStrToDate(startTime, DateFormatConst.DATE_FORMAT2);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.YEAR, offset);
        return formatDateTimeToStr(cal.getTime(), DateFormatConst.DATE_FORMAT2);
    }

    public static Date dateTimeStrToDate(String dateTimeStr, String format) {
        LocalDate localDate = LocalDate.parse(dateTimeStr, DateTimeFormatter.ofPattern(format));
        return localDateToDate(localDate);
    }

    */
/**
     * LocalDate to Date
     *//*

    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    */
/**
     * 指定日期与日期字串 转换成日期字符串
     *
     * @param date
     * @param pattern
     * @return
     *//*

    public static String formatDateTimeToStr(Date date, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        return localDateTime.format(dateTimeFormatter);
    }

    public Workbook exportReport(BaseQueryConditionBO conditions, String uid, String lang) throws ParseException, IOException, BusinessException {
        UserPermissionsBo userPermissionsBo = basePermitVerfiyService.vaildPermit(uid, conditions, lang);
        BaseQueryConditionBO baseQueryCondition = basePermitVerfiyService.convertToBaseOrderQuery(userPermissionsBo, conditions);
        // 填充自定义对比参数
        basePermitVerfiyService.fillCustomComparatorParam(baseQueryCondition.getBaseQueryCondition(), uid);
        baseQueryCondition.setLang(lang);
        Workbook workbook = exportReport(baseQueryCondition);
        return workbook;
    }

    private Workbook exportReport(BaseQueryConditionBO queryCondition) throws ParseException, IOException {
        InputStream inputStream = null;
        if (null != queryCondition.getLang() && SharkUtils.isEN(queryCondition.getLang())) {
            inputStream = this.getClass().getClassLoader().getResourceAsStream("excel/Report_demo_V4_EN.xls");
        } else {
            inputStream = this.getClass().getClassLoader().getResourceAsStream("excel/Report_demo_V4_CN.xls");
        }
        Workbook wb = new HSSFWorkbook(inputStream);
        Sheet dataSourceSheet = wb.getSheet("data");
        // 填充时间和分析对象
        fillContent(dataSourceSheet, queryCondition);
        // 消费月度累计
        cousumeMonthDetail(dataSourceSheet, queryCondition);
        // 机票-机票KPI月度累计
        fillFlightKpi(dataSourceSheet, queryCondition);
        // 机票-月度明细
        fillFlightMonthDetail(dataSourceSheet, queryCondition);
        // 机票-金额明细
        fillFlightAmountDetail(dataSourceSheet, queryCondition);
        // 机票-部门机票消费
        fillTopFlightDept(dataSourceSheet, queryCondition);
        // 机票-机票预订方式
        fillFlightBookType(dataSourceSheet, queryCondition);
        // 机票-节省分析
        fillFlightSaveAnalysis(dataSourceSheet, queryCondition);
        // 机票-潜在节省分析
        fillFlightPSaveAnalysis(dataSourceSheet, queryCondition);
        // 机票-低价rc明细
        fillFlightLowRcDetail(dataSourceSheet, queryCondition);
        // 机票-提前预订天数
        fillFlightPreOrderDate(dataSourceSheet, queryCondition);
        // 机票-舱位分析
        fillFlightCabinAnalysis(dataSourceSheet, queryCondition);
        // 机票-扣率分析
        fillFlightRateAnalysis(dataSourceSheet, queryCondition);
        // 机票-top5承运商
        fillFlightCarrier(dataSourceSheet, queryCondition);
        // 机票-top5航线
        fillFlightCity(dataSourceSheet, queryCondition);
        // 机票-Top5城市对-Top5承运商
        fillFlightCityAndCarrier(dataSourceSheet, queryCondition);
        // 机票-协议航空采购
        fillFlightAgreementAirPurchase(dataSourceSheet, queryCondition);
        // 机票-二氧化碳排放量
        fillFlightCarbon(dataSourceSheet, queryCondition);
        // 酒店月度明细
        fillHotelMonthDetail(dataSourceSheet, queryCondition);
        // 酒店-酒店KPI月度累计
        fillHotelKpi(dataSourceSheet, queryCondition);
        // 酒店-部门酒店消费
        fillHotelDeptDetail(dataSourceSheet, queryCondition);
        // 酒店-节省分析
        fillHotelSaveAnalysis(dataSourceSheet, queryCondition);
        // 酒店-酒店预订方式
        fillHotelBookType(dataSourceSheet, queryCondition);
        // 酒店-酒店星级分析
        fillHotelStar(dataSourceSheet, queryCondition);
        // 酒店-top5酒店城市分析
        fillHotelCity(dataSourceSheet, queryCondition);
        // 酒店-潜在节省
        fillHotelPSaveAnalysis(dataSourceSheet, queryCondition);
        //火车票月度明细
        fillTrainMonthDetail(dataSourceSheet, queryCondition);
        // 火车-火车票KPI月度累计
        fillTrainKpi(dataSourceSheet, queryCondition);
        // 火车-部门火车票消费
        fillTrainDeptDetail(dataSourceSheet, queryCondition);
        // 火车-top5火车票行程分析
        fillTrainLineCity(dataSourceSheet, queryCondition);
        // 火车-火车票预订方式
        fillTrainBookType(dataSourceSheet, queryCondition);
        // 用车月度明细
        fillCarMonthDetail(dataSourceSheet, queryCondition);
        // 用车-部门用车消费
        fillCarDeptDetail(dataSourceSheet, queryCondition);
        // 用车-TOP 5用车城市
        fillCarCity(dataSourceSheet, queryCondition);
        // 用车-TOP 5车型
        fillCarVehicle(dataSourceSheet, queryCondition);

        wb.setForceFormulaRecalculation(true);
        return wb;
    }


    */
/**
     * 填充机票-消费月度累计中的汽车和增值
     * @param sheet
     *//*


    */
/**
     * 填充机票-机票KPI月度累计
     *
     * @param sheet
     *//*

    private void fillFlightKpi(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            List<String> titleList = null;
            BaseQueryCondition baseQueryCondition = new BaseQueryCondition();
            BeanUtils.copyProperties(baseCondition.getBaseQueryCondition(), baseQueryCondition);
            //平均折扣
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), FLIGHT_KPI_START_ROW, "AVG_PRICE_RATE_TREND", false, baseCondition.getDateDimension(), "dom");
            //全家票张占比
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), FLIGHT_KPI_START_ROW + 4, "FULL_FARE_PERCENT_TREND", true, baseCondition.getDateDimension(), "dom");
            //RC订单比例
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), FLIGHT_KPI_START_ROW + 8, "FLT_RC_PERCENT_TREND", false, baseCondition.getDateDimension(), "dom");
            //低价RC订单比例
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), FLIGHT_KPI_START_ROW + 12, "FLT_LOW_RC_PERCENT_TREND", false, baseCondition.getDateDimension(), "dom");
            //提前5天以上预订比例
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), FLIGHT_KPI_START_ROW + 20, "PRE_4_PERCENT_TREND", false, baseCondition.getDateDimension(), "dom");
            //里程均价
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), FLIGHT_KPI_START_ROW + 25, "TPMS_AVG_PRICE_TREND", false, baseCondition.getDateDimension(), "inter");
            //提前预订天数月度累计
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), FLIGHT_KPI_START_ROW + 30, "AVG_PRE_ORDERDATE_TREND", false, baseCondition.getDateDimension(), "dom");
            //退票比例
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), FLIGHT_KPI_START_ROW + 43, "FLT_REFUND_PERCENT_TREND", false, baseCondition.getDateDimension(), StringUtils.EMPTY);
            //改签比例
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), FLIGHT_KPI_START_ROW + 47, "FLT_REBOOK_PERCENT_TREND", false, baseCondition.getDateDimension(), StringUtils.EMPTY);
            Row titleRow = sheet.createRow(FLIGHT_KPI_START_ROW - 1);
            // 设置月份
            if (CollectionUtils.isNotEmpty(titleList)) {
                for (int i = 0; i < titleList.size(); i++) {
                    titleRow.createCell(i).setCellValue(titleList.get(i));
                }
            }

        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightKpi", e);
        }

    }

    */
/**
     * 设置消费月度累计中的汽车和增值
     *
     * @param row
     * @param entity
     *//*


    private List<String> metricPercent(Sheet sheet, BaseQueryCondition baseQueryCondition, int start, String metric, boolean divde100, String dateDimesion, String productType) {
        BaseQueryCondition target = new BaseQueryCondition();
        BeanUtils.copyProperties(baseQueryCondition, target);
        OnlineReportMarkMetricTrendRequest request = new OnlineReportMarkMetricTrendRequest();
        request.setProductType(productType);
        request.setBasecondition(target);
        request.setDateDimension(getDateDimesion(dateDimesion));
        Map extMap = new HashMap();
        extMap.put("metric", metric);
        request.setExtData(extMap);
        OnlineReportMarkMetricTrendResponse responseType = corpOnlineReportPlatformService.queryMarkMetricTrend(request);
        extMap.put("yoyAndMom", "T");
        OnlineReportMarkMetricTrendResponse responseTypeYoyMom = corpOnlineReportPlatformService.queryMarkMetricTrend(request);
        extMap.put("yoyAndMom", StringUtils.EMPTY);
        // 去年同期数据
        target.setStartTime(offsetTime(target.getStartTime(), -1));
        target.setEndTime(offsetTime(target.getEndTime(), -1));
        OnlineReportMarkMetricTrendResponse responseTypeLast = corpOnlineReportPlatformService.queryMarkMetricTrend(request);
        extMap.put("yoyAndMom", "T");
        OnlineReportMarkMetricTrendResponse responseTypeLastYoyMom = corpOnlineReportPlatformService.queryMarkMetricTrend(request);
        List<String> titleList = null;
        if (responseType != null && responseType.getResponseCode() == 20000
                && responseTypeLast != null && responseTypeLast.getResponseCode() == 20000) {
            MarkMetricTrendAndOverview markMetricTrendAndOverview = responseType.getMarkMetric();
            List<MarkMetricInfo> list = markMetricTrendAndOverview.getMarkMetricTrend();

            MarkMetricTrendAndOverview markMetricTrendAndOverviewYoy = responseTypeLast.getMarkMetric();
            List<MarkMetricInfo> yoyList = markMetricTrendAndOverviewYoy.getMarkMetricTrend();
            if (CollectionUtils.isEmpty(titleList)) {
                titleList = list.stream().map(MarkMetricInfo::getDim).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(titleList)) {
                titleList = list.stream().map(MarkMetricInfo::getDim).collect(Collectors.toList());
            }
            setKpiRow(sheet, start, list, yoyList, divde100);
        }
        if (responseTypeYoyMom != null && responseTypeYoyMom.getResponseCode() == 20000
                && responseTypeLastYoyMom != null && responseTypeLastYoyMom.getResponseCode() == 20000) {
            MarkMetricInfoYoyAndMon yoyAndMon = Optional.ofNullable(responseTypeYoyMom.getYoyAndMom()).orElse(new MarkMetricInfoYoyAndMon());
            MarkMetricInfoYoyAndMon lastYoyAndMon = Optional.ofNullable(responseTypeLastYoyMom.getYoyAndMom()).orElse(new MarkMetricInfoYoyAndMon());
            setKpiRowYoyMom(sheet, start, yoyAndMon, lastYoyAndMon);
        }
        return titleList;
    }

    */
/**
     * KPI月度累计
     *
     * @param sheet
     * @param start
     * @param list
     * @param yoyList
     * @param divde100
     *//*

    private void setKpiRow(Sheet sheet, int start, List<MarkMetricInfo> list, List<MarkMetricInfo> yoyList, boolean divde100) {
        Row row1 = sheet.getRow(start);
        Row row2 = sheet.getRow(start + 1);
        Row row3 = sheet.getRow(start + 2);
        Row row4 = sheet.getRow(start + 3);
        row1 = Objects.isNull(row1) ? sheet.createRow(start) : sheet.getRow(start);
        row2 = Objects.isNull(row2) ? sheet.createRow(start + 1) : sheet.getRow(start + 1);
        row3 = Objects.isNull(row3) ? sheet.createRow(start + 2) : sheet.getRow(start + 2);
        row4 = Objects.isNull(row4) ? sheet.createRow(start + 3) : sheet.getRow(start + 3);
        for (int i = 0; i < list.size(); i++) {
            MarkMetricInfo item = list.get(i);
            MarkMetricInfo yoyItem = yoyList.get(i);
            row1.createCell(i).setCellType(CellType.NUMERIC);
            row1.getCell(i).setCellValue(DigitBaseUtils.divide(item.getCompanyMetric(), divde100 ? new BigDecimal(100) : new BigDecimal(1), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
            row2.createCell(i).setCellType(CellType.NUMERIC);
            row2.getCell(i).setCellValue(DigitBaseUtils.divide(item.getCorpMetric(), divde100 ? new BigDecimal(100) : new BigDecimal(1), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
            row3.createCell(i).setCellType(CellType.NUMERIC);
            row3.getCell(i).setCellValue(DigitBaseUtils.divide(item.getIndustryMetric(), divde100 ? new BigDecimal(100) : new BigDecimal(1), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
            row4.createCell(i).setCellType(CellType.NUMERIC);
            row4.getCell(i).setCellValue(DigitBaseUtils.divide(yoyItem.getCompanyMetric(), divde100 ? new BigDecimal(100) : new BigDecimal(1), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        }
    }
    */
/**
     * 填充机票-部门机票消费
     * @param sheet
     *//*


    */
/**
     * KPI月度累计同环比
     *
     * @param sheet
     * @param start
     * @param yoyAndMon
     *//*

    private void setKpiRowYoyMom(Sheet sheet, int start, MarkMetricInfoYoyAndMon yoyAndMon, MarkMetricInfoYoyAndMon lastYoyAndMon) {
        Row row1 = sheet.getRow(start);
        Row row2 = sheet.getRow(start + 1);
        Row row3 = sheet.getRow(start + 2);
        Row row4 = sheet.getRow(start + 3);
        row1 = Objects.isNull(row1) ? sheet.createRow(start) : sheet.getRow(start);
        row2 = Objects.isNull(row2) ? sheet.createRow(start + 1) : sheet.getRow(start + 1);
        row3 = Objects.isNull(row3) ? sheet.createRow(start + 2) : sheet.getRow(start + 2);
        row4 = Objects.isNull(row4) ? sheet.createRow(start + 3) : sheet.getRow(start + 3);
        MarkMetricInfo yoySum = Optional.ofNullable(yoyAndMon.getYoy()).orElse(new MarkMetricInfo());
        MarkMetricInfo lastYoySum = Optional.ofNullable(lastYoyAndMon.getYoy()).orElse(new MarkMetricInfo());

        row1.createCell(KPI_COUNT - 1).setCellType(CellType.NUMERIC);
        row1.getCell(KPI_COUNT - 1).setCellValue(DigitBaseUtils.divide(yoySum.getCompanyMetric(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row2.createCell(KPI_COUNT - 1).setCellType(CellType.NUMERIC);
        row2.getCell(KPI_COUNT - 1).setCellValue(DigitBaseUtils.divide(yoySum.getCorpMetric(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row3.createCell(KPI_COUNT - 1).setCellType(CellType.NUMERIC);
        row3.getCell(KPI_COUNT - 1).setCellValue(DigitBaseUtils.divide(yoySum.getIndustryMetric(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row4.createCell(KPI_COUNT - 1).setCellType(CellType.NUMERIC);
        row4.getCell(KPI_COUNT - 1).setCellValue(DigitBaseUtils.divide(lastYoySum.getCompanyMetric(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());

        MarkMetricInfo momSum = Optional.ofNullable(yoyAndMon.getMom()).orElse(new MarkMetricInfo());
        MarkMetricInfo lastMomSum = Optional.ofNullable(lastYoyAndMon.getMom()).orElse(new MarkMetricInfo());

        row1.createCell(KPI_COUNT - 2).setCellType(CellType.NUMERIC);
        row1.getCell(KPI_COUNT - 2).setCellValue(DigitBaseUtils.divide(momSum.getCompanyMetric(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row2.createCell(KPI_COUNT - 2).setCellType(CellType.NUMERIC);
        row2.getCell(KPI_COUNT - 2).setCellValue(DigitBaseUtils.divide(momSum.getCorpMetric(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row3.createCell(KPI_COUNT - 2).setCellType(CellType.NUMERIC);
        row3.getCell(KPI_COUNT - 2).setCellValue(DigitBaseUtils.divide(momSum.getIndustryMetric(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row4.createCell(KPI_COUNT - 2).setCellType(CellType.NUMERIC);
        row4.getCell(KPI_COUNT - 2).setCellValue(DigitBaseUtils.divide(lastMomSum.getCompanyMetric(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
    }

    */
/**
     * 填充机票月度明细
     *
     * @param sheet
     *//*

    private void fillFlightMonthDetail(Sheet sheet, BaseQueryConditionBO baseCondition) {
        //消费趋势
        OnlineReportTrendRequest request = new OnlineReportTrendRequest();
        request.setQueryBu(QueryReportBuTypeEnum.flight);
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setQueryType(QueryReportTypeionEnum.amount);
        request.setDateDimension(getDateDimesion(baseCondition.getDateDimension()));
        request.setAggType(QueryReportAggTypeEnum.current);
        request.setExtData(new HashMap<String, String>() {{
            put("dim", "flight_class");
            put("yoyDuration", "12");
        }});
        try {
            OnlineReportTrendResponse responseType = corpOnlineReportPlatformService.queryReportTrend(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                Row domMonthRow = sheet.createRow(FLIGHT_MONTH_DETAIL_N_START_ROW);
                Row domMoneyRow = sheet.createRow(FLIGHT_MONTH_DETAIL_N_START_ROW + 1);
                Row domTicketRow = sheet.createRow(FLIGHT_MONTH_DETAIL_N_START_ROW + 2);

                Row internalMonthRow = sheet.createRow(FLIGHT_MONTH_DETAIL_I_START_ROW);
                Row internalMoneyRow = sheet.createRow(FLIGHT_MONTH_DETAIL_I_START_ROW + 1);
                Row internalTicketRow = sheet.createRow(FLIGHT_MONTH_DETAIL_I_START_ROW + 2);

                List<OnlineReportTrendPoint> onlineReportTrendPoints = responseType.getData().getData();
                for (int i = 0; onlineReportTrendPoints != null && i < onlineReportTrendPoints.size(); i++) {
                    OnlineReportTrendPoint detail = onlineReportTrendPoints.get(i);
                    domMonthRow.createCell(i).setCellValue(detail.getAxis());
                    domMoneyRow.createCell(i).setCellType(CellType.NUMERIC);
                    domMoneyRow.getCell(i).setCellValue(detail.getData().getOrDefault("Namount", BigDecimal.ZERO).doubleValue());
                    domTicketRow.createCell(i).setCellValue(detail.getData().getOrDefault("Nquantity", BigDecimal.ZERO).intValue());

                    internalMonthRow.createCell(i).setCellValue(detail.getAxis());
                    internalMoneyRow.createCell(i).setCellType(CellType.NUMERIC);
                    internalMoneyRow.getCell(i).setCellValue(detail.getData().getOrDefault("Iamount", BigDecimal.ZERO).doubleValue());
                    internalTicketRow.createCell(i).setCellValue(detail.getData().getOrDefault("Iquantity", BigDecimal.ZERO).intValue());
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightMonthDetail", e);
        }

    }

    */
/**
     * 填充机票月度明细
     *
     * @param sheet
     *//*

    private void cousumeMonthDetail(Sheet sheet, BaseQueryConditionBO baseCondition) {
        //消费趋势
        OnlineReportTrendRequest request = new OnlineReportTrendRequest();
        request.setQueryBu(QueryReportBuTypeEnum.overview);
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setQueryType(QueryReportTypeionEnum.amount);
        request.setDateDimension(getDateDimesion(baseCondition.getDateDimension()));
        request.setAggType(QueryReportAggTypeEnum.current);
        request.setExtData(new HashMap<String, String>() {{
            put("dim", "order_type");
            put("yoyDuration", "12");
        }});
        try {
            OnlineReportTrendResponse responseType = corpOnlineReportPlatformService.queryReportTrend(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                Row busRow = sheet.createRow(BUS_MONTH_CONSUME_AMOUNT_ROW + 1);
                Row addRow = sheet.createRow(ADD_MONTH_CONSUME_AMOUNT_ROW + 1);
                List<OnlineReportTrendPoint> onlineReportTrendPoints = responseType.getData().getData();
                for (int i = 0; onlineReportTrendPoints != null && i < onlineReportTrendPoints.size(); i++) {
                    OnlineReportTrendPoint detail = onlineReportTrendPoints.get(i);
                    busRow.createCell(i).setCellType(CellType.NUMERIC);
                    busRow.getCell(i).setCellValue(detail.getData().getOrDefault("busAmount", BigDecimal.ZERO).doubleValue());

                    addRow.createCell(i).setCellType(CellType.NUMERIC);
                    addRow.getCell(i).setCellValue(detail.getData().getOrDefault("addAmount", BigDecimal.ZERO).doubleValue());
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightMonthDetail", e);
        }

    }

    */
/**
     * 填充机票金额明细
     *
     * @param sheet
     *//*

    private void fillFlightAmountDetail(Sheet sheet, BaseQueryConditionBO baseCondition) {
        //明细
        OnlineReportDetailRequest request = new OnlineReportDetailRequest();
        request.setLang(baseCondition.getLang());
        request.setQueryBu(QueryReportBuTypeEnum.flight);
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        // 同比去年/同比前年
        request.setYoyType(QueryYoyTypeEnum.valueOf(Optional.ofNullable(baseCondition.getYoy_type()).orElse(QueryYoyTypeEnum.yoy.name())));
        try {
            //设置时间

//            sheet.createRow(FLIGHT_AMOUNT_DETAIL_MONTH_START_ROW).createCell(0).setCellValue(responseType.getTimeRange());

            // 出票
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            extMap.put("isRefund", "F");
            request.setExtData(extMap);
            OnlineReportDetailResponse responseType1 = corpOnlineReportPlatformService.queryReportDetail(request);
            if (responseType1 != null && responseType1.getResponseCode() == 20000) {
                DetailAmountDto detailAmountDto = responseType1.getData().getDetailAmountData();
                //设置国内
                DetailData domDetailData = detailAmountDto.getDomesticDetail();
                if (CollectionUtils.isNotEmpty(domDetailData.getBodyList())) {
                    ReportDataDetailData dom = domDetailData.getBodyList().get(domDetailData.getBodyList().size() - 1).getDetailData();
                    setFlightAmountRow(sheet.createRow(FLIGHT_AMOUNT_DETAIL_START_ROW), dom);
                }
                //设置国际
                DetailData interDetailData = detailAmountDto.getInternationalDetail();
                if (CollectionUtils.isNotEmpty(interDetailData.getBodyList())) {
                    ReportDataDetailData inter = interDetailData.getBodyList().get(interDetailData.getBodyList().size() - 1).getDetailData();
                    setFlightAmountRow(sheet.createRow(FLIGHT_AMOUNT_DETAIL_START_ROW + 2), inter);
                }
            }
            // 退票
            extMap.put("isRefund", "T");
            request.setExtData(extMap);
            OnlineReportDetailResponse responseType2 = corpOnlineReportPlatformService.queryReportDetail(request);
            if (responseType2 != null && responseType2.getResponseCode() == 20000) {
                DetailAmountDto detailAmountDto = responseType2.getData().getDetailAmountData();
                //设置国内
                DetailData domDetailData = detailAmountDto.getDomesticDetail();
                if (CollectionUtils.isNotEmpty(domDetailData.getBodyList())) {
                    ReportDataDetailData dom = domDetailData.getBodyList().get(domDetailData.getBodyList().size() - 1).getDetailData();
                    setFlightAmountRow(sheet.createRow(FLIGHT_AMOUNT_DETAIL_START_ROW + 1), dom);
                }
                //设置国际
                DetailData interDetailData = detailAmountDto.getInternationalDetail();
                if (CollectionUtils.isNotEmpty(interDetailData.getBodyList())) {
                    ReportDataDetailData inter = interDetailData.getBodyList().get(interDetailData.getBodyList().size() - 1).getDetailData();
                    setFlightAmountRow(sheet.createRow(FLIGHT_AMOUNT_DETAIL_START_ROW + 3), inter);
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightAmountDetail", e);
        }

    }

    */
/**
     * 设置机票金额明细
     *
     * @param row
     * @param entity
     *//*

    private void setFlightAmountRow(Row row, ReportDataDetailData entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getNetfare()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getTax()));
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getOilFee()));
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getServiceFee()));
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getInsuranceFee()));
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.getCell(5).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getSendTicketFee()));
        row.createCell(6).setCellType(CellType.NUMERIC);
        row.getCell(6).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getServicepackageFee()));
        row.createCell(7).setCellType(CellType.NUMERIC);
        row.getCell(7).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getBindAmount()));
        row.createCell(8).setCellType(CellType.NUMERIC);
        row.getCell(8).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getRefundFee()));
        row.createCell(9).setCellType(CellType.NUMERIC);
        row.getCell(9).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getRefundServiceFee()));
        row.createCell(10).setCellType(CellType.NUMERIC);
        row.getCell(10).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getRefundItineraryFee()));
        row.createCell(11).setCellType(CellType.NUMERIC);
        row.getCell(11).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getChangeFee()));
        row.createCell(12).setCellType(CellType.NUMERIC);
        row.getCell(12).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getRebookPriceDifferential()));
        row.createCell(13).setCellType(CellType.NUMERIC);
        row.getCell(13).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getRebookServiceFee()));
        row.createCell(14).setCellType(CellType.NUMERIC);
        row.getCell(14).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getTicketBehindServicefee()));
        row.createCell(15).setCellType(CellType.NUMERIC);
        row.getCell(15).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getRebookBehindServiceFee()));
        row.createCell(16).setCellType(CellType.NUMERIC);
        row.getCell(16).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getRefundBehindServiceFee()));
        row.createCell(17).setCellType(CellType.NUMERIC);
        row.getCell(17).setCellValue(DigitBaseUtils.convertStringToDouble(entity.getTotalV()));
    }

    */
/**
     * 填充机票-部门机票消费
     *
     * @param sheet
     *//*

    private void fillTopFlightDept(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportTopDeptConsumeDetailRequest request = new OnlineReportTopDeptConsumeDetailRequest();
            request.setQueryBu(QueryReportBuTypeEnum.flight);
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            AnalysisObjectEnum analysisObjectEnum = StringUtils.isEmpty(baseCondition.getAnalysisObject()) ?
                    AnalysisObjectEnum.CORP : AnalysisObjectEnum.valueOf(baseCondition.getAnalysisObject().toUpperCase());
            request.setAnalysisObjectEnum(analysisObjectEnum);
            request.setProductType("DOM");
            OnlineReportTopDeptConsumeDetailResponse responseType1 = corpOnlineReportPlatformService.queryTopDeptConsumeDetail(request);
            if (responseType1 != null && responseType1.getResponseCode() == 20000) {
                FltTopDeptConsumeInfo detailAmountDto = responseType1.getFltTopConsumeInfo();
                List<OnlineReportFltTopDeptConsume> list = Optional.ofNullable(detailAmountDto.getTopList()).orElse(new ArrayList<>());
                for (int i = 0; i < list.size(); i++) {
                    setDomFlightDeptRow(sheet.createRow(FLIGHT_DEPT_DOM_START_ROW + i), Optional.ofNullable(list.get(i)).orElse(new OnlineReportFltTopDeptConsume()));
                }
                //设置国内总计
                Row nRow = sheet.createRow(FLIGHT_DEPT_DOM_START_ROW + TOP_ONE_HUNDRED + 1);
                OnlineReportFltTopDeptConsume nDetail = Optional.ofNullable(detailAmountDto.getSumConsume()).orElse(new OnlineReportFltTopDeptConsume());
                setDomFlightDeptRow(nRow, nDetail);
            }

            request.setProductType("INTER");
            OnlineReportTopDeptConsumeDetailResponse responseType2 = corpOnlineReportPlatformService.queryTopDeptConsumeDetail(request);
            if (responseType2 != null && responseType2.getResponseCode() == 20000) {
                FltTopDeptConsumeInfo detailAmountDto = responseType2.getFltTopConsumeInfo();
                List<OnlineReportFltTopDeptConsume> list = Optional.ofNullable(detailAmountDto.getTopList()).orElse(new ArrayList<>());
                for (int i = 0; i < list.size(); i++) {
                    setIntFlightDeptRow(sheet.createRow(FLIGHT_DEPT_INT_START_ROW + i), Optional.ofNullable(list.get(i)).orElse(new OnlineReportFltTopDeptConsume()));
                }
                //设置国外总计
                Row iRow = sheet.createRow(FLIGHT_DEPT_INT_START_ROW + TOP_ONE_HUNDRED + 1);
                OnlineReportFltTopDeptConsume iDetail = Optional.ofNullable(detailAmountDto.getSumConsume()).orElse(new OnlineReportFltTopDeptConsume());
                setIntFlightDeptRow(iRow, iDetail);
            }

        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillTopFlightDept", e);
        }
    }

    */
/**
     * 设置机票-部门机票消费-国内
     *
     * @param row
     * @param entity
     *//*

    private void setDomFlightDeptRow(Row row, OnlineReportFltTopDeptConsume entity) {
        row.createCell(13).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmount()).doubleValue());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalQuantity()));
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalFullfaretkt()));
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgTpmsPrice()).doubleValue());
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPrice()).doubleValue());
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.getCell(5).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgDiscount()).doubleValue());
        row.createCell(6).setCellType(CellType.NUMERIC);
        row.getCell(6).setCellValue(MapperUtils.convertDigitToZero(entity.getFullfaretktPercent()).doubleValue());
        row.createCell(7).setCellType(CellType.NUMERIC);
        row.getCell(7).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalSaveAmount()).doubleValue());
        row.createCell(8).setCellType(CellType.NUMERIC);
        row.getCell(8).setCellValue(MapperUtils.convertDigitToZero(entity.getSaveRate()).doubleValue());
        row.createCell(9).setCellType(CellType.NUMERIC);
        row.getCell(9).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalRefundFee()).doubleValue());
        row.createCell(10).setCellType(CellType.NUMERIC);
        row.getCell(10).setCellValue(MapperUtils.convertDigitToZero(entity.getRefundRate()).doubleValue());
        row.createCell(11).setCellType(CellType.NUMERIC);
        row.getCell(11).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalRebookFee()).doubleValue());
        row.createCell(12).setCellType(CellType.NUMERIC);
        row.getCell(12).setCellValue(MapperUtils.convertDigitToZero(entity.getRebookRate()).doubleValue());
    }

    */
/**
     * 设置机票-部门机票消费-国际
     *
     * @param row
     * @param entity
     *//*

    private void setIntFlightDeptRow(Row row, OnlineReportFltTopDeptConsume entity) {
        row.createCell(8).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmount()).doubleValue());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalQuantity()));
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgTpmsPrice()).doubleValue());
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPrice()).doubleValue());
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalRebookFee()).doubleValue());
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.getCell(5).setCellValue(MapperUtils.convertDigitToZero(entity.getRebookRate()).doubleValue());
        row.createCell(6).setCellType(CellType.NUMERIC);
        row.getCell(6).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalRefundFee()).doubleValue());
        row.createCell(7).setCellType(CellType.NUMERIC);
        row.getCell(7).setCellValue(MapperUtils.convertDigitToZero(entity.getRefundRate()).doubleValue());
    }

    */
/**
     * 填充机票-机票预订方式
     *
     * @param sheet
     *//*

    private void fillFlightBookType(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportBookTypeRequest bookTypeRequest = new OnlineReportBookTypeRequest();
            bookTypeRequest.setQueryBu(QueryReportBuTypeEnum.flight);
            bookTypeRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            // 国内机票
            bookTypeRequest.setProductType("dom");
            OnlineReportBookTypeResponse responseType1 = corpOnlineReportPlatformService.queryBookTypeAnalysis(bookTypeRequest);
            if (responseType1 != null && responseType1.getResponseCode() == 20000) {
                List<BookTypeInfo> domFlightBookType = responseType1.getBookTypeList();
                Row domRow = sheet.createRow(FLIGHT_BOOK_TYPE_START_ROW);
                setBookTypeRow(domRow, domFlightBookType);
            }
            // 国际机票
            bookTypeRequest.setProductType("inter");
            OnlineReportBookTypeResponse responseType2 = corpOnlineReportPlatformService.queryBookTypeAnalysis(bookTypeRequest);
            if (responseType2 != null && responseType2.getResponseCode() == 20000) {
                List<BookTypeInfo> intFlightBookType = responseType2.getBookTypeList();
                Row intRow = sheet.createRow(FLIGHT_BOOK_TYPE_START_ROW + 1);
                setBookTypeRow(intRow, intFlightBookType);
            }
            // 全部机票
            bookTypeRequest.setProductType("all");
            OnlineReportBookTypeResponse responseType3 = corpOnlineReportPlatformService.queryBookTypeAnalysis(bookTypeRequest);
            if (responseType3 != null && responseType3.getResponseCode() == 20000) {
                List<BookTypeInfo> intFlightBookType = responseType3.getBookTypeList();
                Row intRow = sheet.createRow(FLIGHT_BOOK_TYPE_START_ROW + 2);
                setBookTypeRow(intRow, intFlightBookType);
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightBookType", e);
        }

    }

    */
/**
     * 设置机票-机票预订方式
     *
     * @param row
     * @param entity
     *//*

    private void setBookTypeRow(Row row, List<BookTypeInfo> entity) {
        // app
        BookTypeInfo appBookTypeInfo = Optional.ofNullable(entity).orElse(new ArrayList<>()).stream()
                .filter(i -> StringUtils.equalsIgnoreCase(i.getBookType(), BookTypeEnum.M.toString()))
                .findFirst().orElse(new BookTypeInfo());
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(appBookTypeInfo.getTotalOrderCount()));
        // online
        BookTypeInfo onlineBookTypeInfo = Optional.ofNullable(entity).orElse(new ArrayList<>()).stream()
                .filter(i -> StringUtils.equalsIgnoreCase(i.getBookType(), BookTypeEnum.T.toString()))
                .findFirst().orElse(new BookTypeInfo());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(onlineBookTypeInfo.getTotalOrderCount()));
        // offline
        BookTypeInfo offlineBookTypeInfo = Optional.ofNullable(entity).orElse(new ArrayList<>()).stream()
                .filter(i -> StringUtils.equalsIgnoreCase(i.getBookType(), BookTypeEnum.F.toString()))
                .findFirst().orElse(new BookTypeInfo());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(offlineBookTypeInfo.getTotalOrderCount()));
    }

    */
/**
     * 填充机票-节省分析
     *
     * @param sheet
     *//*

    private void fillFlightSaveAnalysis(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportSaveProportionDetailRequest detailRequest = new OnlineReportSaveProportionDetailRequest();
            detailRequest.setQueryBu(QueryReportBuTypeEnum.flight);
            detailRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            detailRequest.setLang(baseCondition.getLang());
            detailRequest.setProductType("dom");
            OnlineReportSaveProportionDetailResponse detailResponse = corpOnlineReportPlatformService.querySaveProportionDetail(detailRequest);
            if (detailResponse != null && detailResponse.getResponseCode() == 20000) {
                List<OnlineReportSaveProportionDetail> list = Optional.ofNullable(detailResponse.getProportionDetails()).orElse(new ArrayList<>());
                setFlightSaveDetailRow(sheet.createRow(FLIGHT_SAVE_AND_LOSE_START_ROW), list.size() > 0 ? list.get(0) : new OnlineReportSaveProportionDetail());
                setFlightSaveDetailRow(sheet.createRow(FLIGHT_SAVE_AND_LOSE_START_ROW + 1), list.size() > 1 ? list.get(1) : new OnlineReportSaveProportionDetail());
            }
            OnlineReportSaveGeneralRequest saveGeneralRequest = new OnlineReportSaveGeneralRequest();
            saveGeneralRequest.setQueryBu(QueryReportBuTypeEnum.flight);
            saveGeneralRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            saveGeneralRequest.setLang(baseCondition.getLang());
            saveGeneralRequest.setProductType("dom");
            Map map = new HashMap();
            map.put("index", "GENERAL_SAVE");
            // 概览节省分布不需要total信息
            map.put("needTotal", "F");
            saveGeneralRequest.setExtData(map);
            OnlineReportSaveGeneralResponse saveGeneralResponse = corpOnlineReportPlatformService.querySaveGeneralInfo(saveGeneralRequest);
            if (saveGeneralResponse != null && saveGeneralResponse.getResponseCode() == 20000) {
                GeneralSaveInfo saveInfo = Optional.ofNullable(saveGeneralResponse.getSaveInfo()).orElse(new GeneralSaveInfo());
                setFlightSaveSumRow(sheet.createRow(FLIGHT_SAVE_AND_LOSE_START_ROW + 2), saveInfo);
            }

            // 节省趋势
            OnlineReportTrendRequest trendCurrentRequest = new OnlineReportTrendRequest();
            trendCurrentRequest.setQueryBu(QueryReportBuTypeEnum.flight);
            trendCurrentRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            trendCurrentRequest.setQueryType(QueryReportTypeionEnum.amount);
            trendCurrentRequest.setDateDimension(getDateDimesion(baseCondition.getDateDimension()));
            trendCurrentRequest.setAggType(QueryReportAggTypeEnum.current);
            trendCurrentRequest.setProductType("dom");
            Map trendCurrentMap = new HashMap();
            trendCurrentMap.put("lang", baseCondition.getLang());
            trendCurrentMap.put("dim", "type");
            trendCurrentRequest.setExtData(trendCurrentMap);
            OnlineReportTrendResponse trendCurrentResponse = corpOnlineReportPlatformService.queryReportSaveTrend(trendCurrentRequest);
            if (trendCurrentResponse != null && trendCurrentResponse.getResponseCode() == 20000) {
                List<OnlineReportTrendPoint> trendPoints = trendCurrentResponse.getData().getData();
                setFlightSaveMonthRow(sheet, trendPoints);
            }

        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightSaveAndLose", e);
        }

    }

    */
/**
     * 设置机票-节省分布详情
     *
     * @param row
     * @param entity
     *//*

    private void setFlightSaveDetailRow(Row row, OnlineReportSaveProportionDetail entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(entity.getSavePerQuantity()).doubleValue());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(DigitBaseUtils.divide(entity.getSaveRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getIndustrySavePerQuantity()).doubleValue());
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(DigitBaseUtils.divide(entity.getIndustrySaveRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(MapperUtils.convertDigitToZero(entity.getCorpSavePerQuantity()).doubleValue());
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.getCell(5).setCellValue(DigitBaseUtils.divide(entity.getCorpSaveRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(6).setCellValue(MapperUtils.trim(entity.getType()));

    }

    */
/**
     * 设置机票-节省分布汇总
     *
     * @param row
     * @param entity
     *//*

    private void setFlightSaveSumRow(Row row, GeneralSaveInfo entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(entity.getCompanyPerQtySave()).doubleValue());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(DigitBaseUtils.divide(entity.getCompanySaveRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getIndustryPerQtySave()).doubleValue());
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(DigitBaseUtils.divide(entity.getIndustrySaveRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(MapperUtils.convertDigitToZero(entity.getCorpPerQtySave()).doubleValue());
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.getCell(5).setCellValue(DigitBaseUtils.divide(entity.getCorpSaveRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
    }

    */
/**
     * 设置机票-节省分析-逐月
     *
     * @param sheet
     * @param trendPoints
     *//*

    private void setFlightSaveMonthRow(Sheet sheet, List<OnlineReportTrendPoint> trendPoints) {
        Row row1 = sheet.createRow(FLIGHT_SAVE_AND_LOSE_START_ROW + 4);
        Row row2 = sheet.createRow(FLIGHT_SAVE_AND_LOSE_START_ROW + 5);
        List<String> titleList = trendPoints.stream().map(OnlineReportTrendPoint::getAxis).collect(Collectors.toList());

        //设置月份标题
        Row titleRow = sheet.createRow(FLIGHT_SAVE_AND_LOSE_START_ROW + 3);
        for (int i = 0; i < titleList.size(); i++) {
            titleRow.createCell(i).setCellValue(titleList.get(i));
        }
        for (int i = 0; i < trendPoints.size(); i++) {
            Map<String, BigDecimal> map = trendPoints.get(i).getData();
            row1.createCell(i).setCellType(CellType.NUMERIC);
            row1.getCell(i).setCellValue(MapperUtils.convertDigitToZero((BigDecimal) map.get("cSave")).doubleValue());
            row2.createCell(i).setCellType(CellType.NUMERIC);
            row2.getCell(i).setCellValue(MapperUtils.convertDigitToZero((BigDecimal) map.get("premiumSave")).doubleValue());
        }
    }

    */
/**
     * 填充机票-潜在节省分析
     *
     * @param sheet
     *//*

    private void fillFlightPSaveAnalysis(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportSaveGeneralRequest saveGeneralRequest = new OnlineReportSaveGeneralRequest();
            saveGeneralRequest.setQueryBu(QueryReportBuTypeEnum.flight);
            saveGeneralRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            saveGeneralRequest.setLang(baseCondition.getLang());
//            saveGeneralRequest.setProductType("dom");
            Map map = new HashMap();
            map.put("index", "GENERAL_SAVE_POTENTIAL");
            // 概览节省分布不需要total信息
            map.put("needTotal", "F");
            saveGeneralRequest.setExtData(map);
            OnlineReportSaveGeneralResponse saveGeneralResponse = corpOnlineReportPlatformService.querySaveGeneralInfo(saveGeneralRequest);
            if (saveGeneralResponse != null && saveGeneralResponse.getResponseCode() == 20000) {
                GeneralPotentialSaveInfo saveInfo = Optional.ofNullable(saveGeneralResponse.getPotentialSaveInfo()).orElse(new GeneralPotentialSaveInfo());
                Row row = sheet.createRow(FLIGHT_PROPORTION_SAVE_START_ROW);
                row.createCell(0).setCellType(CellType.NUMERIC);
                row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(saveInfo.getOverAmount()).doubleValue());
                row.createCell(1).setCellType(CellType.NUMERIC);
                row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(saveInfo.getRefundloss()).doubleValue());
                row.createCell(2).setCellType(CellType.NUMERIC);
                row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(saveInfo.getRebookloss()).doubleValue());
            }

            OnlineReportPSaveProportionDetailRequest detailRequest = new OnlineReportPSaveProportionDetailRequest();
            detailRequest.setQueryBu(QueryReportBuTypeEnum.flight);
            detailRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            detailRequest.setLang(baseCondition.getLang());
//            detailRequest.setProductType("dom");
            OnlineReportPSaveProportionDetailResponse detailResponse = corpOnlineReportPlatformService.queryPSaveProportionDetail(detailRequest);
            if (detailResponse != null && detailResponse.getResponseCode() == 20000) {
                List<OnlineReportPotentionSaveProportionDetail> list = detailResponse.getProportionDetails();
                setFlightPSaveDetailRow(sheet.createRow(FLIGHT_PROPORTION_SAVE_START_ROW + 1), list.size() > 0 ? list.get(0) : new OnlineReportPotentionSaveProportionDetail());
                setFlightPSaveDetailRow(sheet.createRow(FLIGHT_PROPORTION_SAVE_START_ROW + 2), list.size() > 1 ? list.get(1) : new OnlineReportPotentionSaveProportionDetail());
                setFlightPSaveDetailRow(sheet.createRow(FLIGHT_PROPORTION_SAVE_START_ROW + 3), list.size() > 2 ? list.get(2) : new OnlineReportPotentionSaveProportionDetail());
            }

            // 节省趋势
            OnlineReportTrendRequest trendCurrentRequest = new OnlineReportTrendRequest();
            trendCurrentRequest.setQueryBu(QueryReportBuTypeEnum.flight);
            trendCurrentRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            trendCurrentRequest.setQueryType(QueryReportTypeionEnum.amount);
            trendCurrentRequest.setDateDimension(getDateDimesion(baseCondition.getDateDimension()));
            trendCurrentRequest.setAggType(QueryReportAggTypeEnum.current);
            trendCurrentRequest.setProductType("dom");
            Map trendCurrentMap = new HashMap();
            trendCurrentMap.put("lang", baseCondition.getLang());
            trendCurrentMap.put("dim", "");
            trendCurrentRequest.setExtData(trendCurrentMap);
            OnlineReportTrendResponse trendDomResponse = corpOnlineReportPlatformService.queryReportPotentialSaveTrend(trendCurrentRequest);
            trendCurrentRequest.setProductType("inter");
            OnlineReportTrendResponse trendInterResponse = corpOnlineReportPlatformService.queryReportPotentialSaveTrend(trendCurrentRequest);

            if (trendDomResponse != null && trendDomResponse.getResponseCode() == 20000 && trendInterResponse != null && trendInterResponse.getResponseCode() == 20000) {
                List<OnlineReportTrendPoint> trendDomPoints = Optional.ofNullable(Optional.ofNullable(trendDomResponse.getData())
                        .orElse(new OnlineReportTrendData()).getData()).orElse(new ArrayList<>());
                List<OnlineReportTrendPoint> trendInterPoints = Optional.ofNullable(Optional.ofNullable(trendInterResponse.getData())
                        .orElse(new OnlineReportTrendData()).getData()).orElse(new ArrayList<>());
                setFlightPSaveMonthRow(sheet, trendDomPoints, trendInterPoints);
            }

        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightPSaveAnalysis", e);
        }

    }

    */
/**
     * 设置机票-节省分布详情
     *
     * @param row
     * @param entity
     *//*

    private void setFlightPSaveDetailRow(Row row, OnlineReportPotentionSaveProportionDetail entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(DigitBaseUtils.divide(entity.getTimesRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(DigitBaseUtils.divide(entity.getIndustryTimesRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(DigitBaseUtils.divide(entity.getCorpTimesRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(3).setCellValue(MapperUtils.trim(entity.getType()));
    }

    */
/**
     * 设置机票-潜在节省分析-逐月
     *
     * @param sheet
     * @param trendDomPoints
     * @param trendInterPoints
     *//*

    private void setFlightPSaveMonthRow(Sheet sheet, List<OnlineReportTrendPoint> trendDomPoints, List<OnlineReportTrendPoint> trendInterPoints) {
        Row row1 = sheet.createRow(FLIGHT_PROPORTION_SAVE_START_ROW + 5);
        Row row2 = sheet.createRow(FLIGHT_PROPORTION_SAVE_START_ROW + 6);
        List<String> titleList = trendDomPoints.stream().map(OnlineReportTrendPoint::getAxis).collect(Collectors.toList());

        //设置月份标题
        Row titleRow = sheet.createRow(FLIGHT_PROPORTION_SAVE_START_ROW + 4);
        for (int i = 0; i < titleList.size(); i++) {
            titleRow.createCell(i).setCellValue(titleList.get(i));
        }
        for (int i = 0; i < trendDomPoints.size(); i++) {
            Map<String, BigDecimal> mapDom = Optional.ofNullable(Optional.ofNullable(trendDomPoints.get(i)).orElse(new OnlineReportTrendPoint()).getData()).orElse(new HashMap<>());
            row1.createCell(i).setCellType(CellType.NUMERIC);
            row1.getCell(i).setCellValue(MapperUtils.convertDigitToZero(((BigDecimal) mapDom.get("totalPotentialSave"))).doubleValue());
            Map<String, BigDecimal> mapInter = trendInterPoints.get(i).getData();
            row2.createCell(i).setCellType(CellType.NUMERIC);
            row2.getCell(i).setCellValue(MapperUtils.convertDigitToZero(((BigDecimal) mapInter.get("totalPotentialSave"))).doubleValue());
        }
    }

    */
/**
     * 填充机票-低价RC明细
     *
     * @param sheet
     * @param baseCondition
     *//*

    private void fillFlightLowRcDetail(Sheet sheet, BaseQueryConditionBO baseCondition) {
        BaseQueryCondition baseQueryCondition = new BaseQueryCondition();
        BeanUtils.copyProperties(baseCondition.getBaseQueryCondition(), baseQueryCondition);
        OnlineReportPotentialSaveLowRclRequest lowRclRequest = new OnlineReportPotentialSaveLowRclRequest();
        lowRclRequest.setQueryBu(QueryReportBuTypeEnum.flight);
        lowRclRequest.setBasecondition(baseQueryCondition);
        lowRclRequest.setLang(baseCondition.getLang());
        try {
            OnlineReportPotentialSaveLowRcResponse lowRcResponse = corpOnlineReportPlatformService.queryPotentialSaveLowRcDetail(lowRclRequest);
            if (lowRcResponse != null && lowRcResponse.getResponseCode() == 20000) {
                if (CollectionUtils.isNotEmpty(lowRcResponse.getLowRcList())) {
                    List<PotentialSaveLowRcInfo> lowRcInfos = lowRcResponse.getLowRcList();
                    for (int i = 0; i < lowRcInfos.size() - 1; i++) {
                        PotentialSaveLowRcInfo potentialSaveLowRcInfo = lowRcInfos.get(i);
                        Row row1 = sheet.createRow(FLIGHT_LOSS_DETAIL_START_ROW + i);
                        setFlightLowRcDetailRow(row1, potentialSaveLowRcInfo);
                    }
                    setFlightLowRcDetailRow(sheet.createRow(FLIGHT_LOSS_DETAIL_START_ROW + 13), lowRcInfos.get(lowRcInfos.size() - 1));
                }
            }

            OnlineReportTopDeptConsumeDetailRequest topConsumeRequest = new OnlineReportTopDeptConsumeDetailRequest();
            topConsumeRequest.setAnalysisObjectEnum(StringUtils.isEmpty(baseCondition.getAnalysisObject()) ?
                    AnalysisObjectEnum.DEPT1 : AnalysisObjectEnum.valueOf(baseCondition.getAnalysisObject().toUpperCase()));
            topConsumeRequest.setBasecondition(baseQueryCondition);
            topConsumeRequest.setTopLimit(5);
            topConsumeRequest.setQueryBu(QueryReportBuTypeEnum.overview);
            OnlineReportTopDeptConsumeDetailResponse topConsumeResponseType = corpOnlineReportPlatformService.queryTopDeptConsumeDetail(topConsumeRequest);
            if (topConsumeResponseType != null && topConsumeResponseType.getResponseCode() == 20000) {
                // 消费前5的部门
                List<OnlineReportTrainTopDeptConsume> topDepts = Optional.ofNullable(Optional.ofNullable(topConsumeResponseType.getTrainTopConsumeInfo()).orElse(new TrainTopDeptConsumeInfo()).getTopList())
                        .orElse(new ArrayList<>());
                List<String> depts = topDepts.stream().filter(Objects::nonNull).map(OnlineReportTrainTopDeptConsume::getDimId).collect(Collectors.toList());
                List<String> deptsDesc = topDepts.stream().filter(Objects::nonNull).map(OnlineReportTrainTopDeptConsume::getDim).collect(Collectors.toList());
                for (int i = 0; i < depts.size(); i++) {
                    // 设置前五部门损失明细—5个部门名称
                    if (i == 0) {
                        sheet.createRow(FLIGHT_LOSS_DEPT1_START_ROW - 1).createCell(1).setCellValue(deptsDesc.get(i));
                    } else if (i == 1) {
                        sheet.createRow(FLIGHT_LOSS_DEPT2_START_ROW - 1).createCell(1).setCellValue(deptsDesc.get(i));
                    } else if (i == 2) {
                        sheet.createRow(FLIGHT_LOSS_DEPT3_START_ROW - 1).createCell(1).setCellValue(deptsDesc.get(i));
                    } else if (i == 3) {
                        sheet.createRow(FLIGHT_LOSS_DEPT4_START_ROW - 1).createCell(1).setCellValue(deptsDesc.get(i));
                    } else if (i == 4) {
                        sheet.createRow(FLIGHT_LOSS_DEPT5_START_ROW - 1).createCell(1).setCellValue(deptsDesc.get(i));
                    }
                    OnlineReportPotentialSaveLowRclRequest lowRclRequest1 = new OnlineReportPotentialSaveLowRclRequest();
                    lowRclRequest1.setQueryBu(QueryReportBuTypeEnum.flight);
                    lowRclRequest1.setBasecondition(baseQueryCondition);
                    lowRclRequest1.setLang(baseCondition.getLang());
                    lowRclRequest1.setProductType("dom");
                    analysisObjectCondition(topConsumeRequest.getAnalysisObjectEnum(), depts.get(i), lowRclRequest1.getBasecondition());
                    OnlineReportPotentialSaveLowRcResponse lowRcResponse1 = corpOnlineReportPlatformService.queryPotentialSaveLowRcDetail(lowRclRequest1);
                    if (lowRcResponse1 != null && lowRcResponse1.getResponseCode() == 20000) {
                        if (CollectionUtils.isNotEmpty(lowRcResponse1.getLowRcList())) {
                            List<PotentialSaveLowRcInfo> lowRcInfos = lowRcResponse1.getLowRcList();
                            int currentIndex = 0;
                            for (int j = 0; j < lowRcInfos.size() - 1; j++) {
                                PotentialSaveLowRcInfo potentialSaveLowRcInfo = lowRcInfos.get(j);
                                Row row1 = null;
                                if (i == 0) {
                                    currentIndex = FLIGHT_LOSS_DEPT1_START_ROW;
                                } else if (i == 1) {
                                    currentIndex = FLIGHT_LOSS_DEPT2_START_ROW;
                                } else if (i == 2) {
                                    currentIndex = FLIGHT_LOSS_DEPT3_START_ROW;
                                } else if (i == 3) {
                                    currentIndex = FLIGHT_LOSS_DEPT4_START_ROW;
                                } else if (i == 4) {
                                    currentIndex = FLIGHT_LOSS_DEPT5_START_ROW;
                                }
                                if (currentIndex != 0) {
                                    row1 = sheet.createRow(currentIndex + j);
                                    setFlightLowRcDetailRow(row1, potentialSaveLowRcInfo);
                                }
                            }
                            if (currentIndex != 0) {
                                setFlightLowRcDetailRow(sheet.createRow(currentIndex + 14), lowRcInfos.get(lowRcInfos.size() - 1));
                            }
                        }
                    }
//                    // 设置前五部门损失明细—每个部门损失详情
//                    for (int i = 0; i < LOSS_DETAIL_COUNT; i++) {
//                        setFlightLossDeptRow(sheet.createRow(FLIGHT_LOSS_DEPT1_START_ROW+i), flightDeptLoss.get(0).getLostDetailList().get(i));
//                        setFlightLossDeptRow(sheet.createRow(FLIGHT_LOSS_DEPT2_START_ROW+i), flightDeptLoss.get(1).getLostDetailList().get(i));
//                        setFlightLossDeptRow(sheet.createRow(FLIGHT_LOSS_DEPT3_START_ROW+i), flightDeptLoss.get(2).getLostDetailList().get(i));
//                        setFlightLossDeptRow(sheet.createRow(FLIGHT_LOSS_DEPT4_START_ROW+i), flightDeptLoss.get(3).getLostDetailList().get(i));
//                        setFlightLossDeptRow(sheet.createRow(FLIGHT_LOSS_DEPT5_START_ROW+i), flightDeptLoss.get(4).getLostDetailList().get(i));
//                    }
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightLowRcDetail", e);
        }
    }

    */
/**
     * 设置机票-损失明细-国内损失明细
     *
     * @param row
     * @param entity
     *//*

    private void setFlightLowRcDetailRow(Row row, PotentialSaveLowRcInfo entity) {
        row.createCell(0).setCellValue(MapperUtils.trim(entity.getLowRcCode()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getRcTimes()));
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getAmount()).doubleValue());
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getCorpPriceAdj()).doubleValue());
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(MapperUtils.convertDigitToZero(entity.getOverAmount()).doubleValue());
        row.createCell(5).setCellValue(MapperUtils.trim(entity.getLowRcDesc()));
        row.createCell(6).setCellValue("-");
        row.createCell(7).setCellType(CellType.NUMERIC);
        row.getCell(7).setCellValue(MapperUtils.convertDigitToZero(DigitBaseUtils.divide(entity.getOverAmountPercent(), new BigDecimal(100), 4)).doubleValue());
        row.createCell(8).setCellType(CellType.NUMERIC);
        row.getCell(8).setCellValue(MapperUtils.convertDigitToZero(entity.getPrintPrice()).doubleValue());
    }

    private void analysisObjectCondition(AnalysisObjectEnum analysisObjectEnum, String dept, BaseQueryCondition baseQueryCondition) {
        switch (analysisObjectEnum) {
            case CORP:
                baseQueryCondition.setCorpIds(Arrays.asList(dept));
                break;
            case ACCOUNT:
            case ACCOUNTCODE:
                baseQueryCondition.setAccountIds(Arrays.asList(dept));
                break;
            case DEPT1:
                deptOrCostcenterCondition(dept, baseQueryCondition, 1, "dept");
                break;
            case DEPT2:
                deptOrCostcenterCondition(dept, baseQueryCondition, 2, "dept");
                break;
            case DEPT3:
                deptOrCostcenterCondition(dept, baseQueryCondition, 3, "dept");
                break;
            case DEPT4:
                deptOrCostcenterCondition(dept, baseQueryCondition, 4, "dept");
                break;
            case DEPT5:
                deptOrCostcenterCondition(dept, baseQueryCondition, 5, "dept");
                break;
            case DEPT6:
                deptOrCostcenterCondition(dept, baseQueryCondition, 6, "dept");
                break;
            case DEPT7:
                deptOrCostcenterCondition(dept, baseQueryCondition, 7, "dept");
                break;
            case DEPT8:
                deptOrCostcenterCondition(dept, baseQueryCondition, 8, "dept");
                break;
            case DEPT9:
                deptOrCostcenterCondition(dept, baseQueryCondition, 9, "dept");
                break;
            case DEPT10:
                deptOrCostcenterCondition(dept, baseQueryCondition, 10, "dept");
                break;
            case COSTCENTER1:
                deptOrCostcenterCondition(dept, baseQueryCondition, 1, "costcenter");
                break;
            case COSTCENTER2:
                deptOrCostcenterCondition(dept, baseQueryCondition, 2, "costcenter");
                break;
            case COSTCENTER3:
                deptOrCostcenterCondition(dept, baseQueryCondition, 3, "costcenter");
                break;
            case COSTCENTER4:
                deptOrCostcenterCondition(dept, baseQueryCondition, 4, "costcenter");
                break;
            case COSTCENTER5:
                deptOrCostcenterCondition(dept, baseQueryCondition, 5, "costcenter");
                break;
            case COSTCENTER6:
                deptOrCostcenterCondition(dept, baseQueryCondition, 6, "costcenter");
                break;
        }
    }

    private void deptOrCostcenterCondition(String dept, BaseQueryCondition baseQueryCondition, int level, String type) {
        List<SearchDeptAndCostcneterEntity> deptList = StringUtils.equalsIgnoreCase(type, "dept") ? baseQueryCondition.getDeptList() : baseQueryCondition.getCostCenterList();
        if (CollectionUtils.isNotEmpty(deptList)) {
            SearchDeptAndCostcneterEntity deptEntity = deptList.stream().filter(i -> i.getKey() == level).findFirst().orElse(new SearchDeptAndCostcneterEntity());
            // 排除
            if (deptEntity.getWay() != null && deptEntity.getKey() == level) {
                deptEntity.setPermitVals(Arrays.asList(dept));
            } else {
                // 包含
                deptEntity.setVals(Arrays.asList(dept));
            }
        } else {
            deptList = new ArrayList<>();
            SearchDeptAndCostcneterEntity searchDeptAndCostcneterEntity = new SearchDeptAndCostcneterEntity();
            searchDeptAndCostcneterEntity.setKey(level);
            searchDeptAndCostcneterEntity.setVals(Arrays.asList(dept));
            deptList.add(searchDeptAndCostcneterEntity);
            if (StringUtils.equalsIgnoreCase(type, "dept")) {
                baseQueryCondition.setDeptList(deptList);
            } else {
                baseQueryCondition.setCostCenterList(deptList);
            }
        }
    }

    */
/**
     * 填充机票-提前预订天数
     *
     * @param sheet
     * @param baseCondition
     *//*

    private void fillFlightPreOrderDate(Sheet sheet, BaseQueryConditionBO baseCondition) {
        OnlineReportPreOrderDateRequest request = new OnlineReportPreOrderDateRequest();
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setProductType("dom");
        request.setQueryBu(QueryReportBuTypeEnum.flight);
        request.setRangeList(defaultCustomPreorderdate());
        Map extMap = new HashMap();
        extMap.put("from", "PC");
        request.setExtData(extMap);
        try {
            OnlineReportPreOrderDateResponse responseType = corpOnlineReportPlatformService.queryPreOrderDateAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                if (CollectionUtils.isNotEmpty(responseType.getPreOrderDateList())) {
                    for (int i = 0; i < responseType.getPreOrderDateList().size(); i++) {
                        PreOrderDateInfo preOrderDateInfo = responseType.getPreOrderDateList().get(i);
                        Row row1 = sheet.createRow(FLIGHT_PRE_DATE_N_START_ROW + i);
                        setFlightPreorderdateNRow(row1, preOrderDateInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightPreOrderDate", e);
        }
    }

    private List<PreOrderdateRange> defaultCustomPreorderdate() {
        List<PreOrderdateRange> rangeList = new ArrayList<>();
        rangeList.add(new PreOrderdateRange(0, 0));
        rangeList.add(new PreOrderdateRange(1, 1));
        rangeList.add(new PreOrderdateRange(2, 2));
        rangeList.add(new PreOrderdateRange(3, 3));
        rangeList.add(new PreOrderdateRange(4, 4));
        rangeList.add(new PreOrderdateRange(5, 365));
        return rangeList;
    }

    */
/**
     * 设置机票-提前预订天数-国内
     *
     * @param row
     * @param entity
     *//*

    private void setFlightPreorderdateNRow(Row row, PreOrderDateInfo entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalQuantity()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(DigitBaseUtils.divide(entity.getQuantityPercent(), 100d, GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalPrice()).doubleValue());
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceRate()).doubleValue());
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(DigitBaseUtils.divide(entity.getRefundPercent(), 100d, GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.getCell(5).setCellValue(DigitBaseUtils.divide(entity.getRebookPercent(), 100d, GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(6).setCellType(CellType.NUMERIC);
        row.getCell(6).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalPotentialSaveAmount()).doubleValue());
    }

    */
/**
     * 填充机票-舱位分析
     *
     * @param sheet
     *//*

    private void fillFlightCabinAnalysis(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportBehaviorAnalysisRequest request = new OnlineReportBehaviorAnalysisRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map extMap = new HashMap();
            extMap.put("dim", "real_class");
            request.setExtData(extMap);
            request.setProductType("dom");
            request.setQueryBu(QueryReportBuTypeEnum.flight);

            OnlineReportBehaviorAnalysisResponse domResponseType = corpOnlineReportPlatformService.queryBehaviorAnalysis(request);
            if (domResponseType != null && domResponseType.getResponseCode() == 20000) {
                List<FlightBehaviorInfo> flightBehaviorInfoList = Optional.ofNullable(domResponseType.getFlightBehaviorList()).orElse(new ArrayList<>());
                // 国内折扣经济舱
                setFlightCabinRow(sheet.createRow(FLIGHT_CABIN_N_START_ROW), flightBehaviorInfoList.stream()
                        .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), "折扣经济舱"))
                        .findFirst().orElse(new FlightBehaviorInfo()));
                // 国内全价经济舱
                setFlightCabinRow(sheet.createRow(FLIGHT_CABIN_N_START_ROW + 1), flightBehaviorInfoList.stream()
                        .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), "全价经济舱"))
                        .findFirst().orElse(new FlightBehaviorInfo()));
                // 国内公务舱
                setFlightCabinRow(sheet.createRow(FLIGHT_CABIN_N_START_ROW + 2), flightBehaviorInfoList.stream()
                        .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), "公务舱"))
                        .findFirst().orElse(new FlightBehaviorInfo()));
                // 国内头等舱
                setFlightCabinRow(sheet.createRow(FLIGHT_CABIN_N_START_ROW + 3), flightBehaviorInfoList.stream()
                        .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), "头等舱"))
                        .findFirst().orElse(new FlightBehaviorInfo()));
            }
            extMap = new HashMap();
            extMap.put("dim", "class_type");
            request.setExtData(extMap);
            request.setProductType("inter");
            OnlineReportBehaviorAnalysisResponse interResponseType = corpOnlineReportPlatformService.queryBehaviorAnalysis(request);
            if (interResponseType != null && interResponseType.getResponseCode() == 20000) {
                List<FlightBehaviorInfo> flightBehaviorInfoList = Optional.ofNullable(interResponseType.getFlightBehaviorList()).orElse(new ArrayList<>());
                // 国外经济舱
                setFlightCabinRow(sheet.createRow(FLIGHT_CABIN_I_START_ROW), flightBehaviorInfoList.stream()
                        .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), FlightClassTypeEnum.Y.toString()))
                        .findFirst().orElse(new FlightBehaviorInfo()));
                // 国外公务舱
                setFlightCabinRow(sheet.createRow(FLIGHT_CABIN_I_START_ROW + 1), flightBehaviorInfoList.stream()
                        .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), FlightClassTypeEnum.C.toString()))
                        .findFirst().orElse(new FlightBehaviorInfo()));
                // 国外头等舱
                setFlightCabinRow(sheet.createRow(FLIGHT_CABIN_I_START_ROW + 2), flightBehaviorInfoList.stream()
                        .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), FlightClassTypeEnum.F.toString()))
                        .findFirst().orElse(new FlightBehaviorInfo()));
            }

        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightSaveAndLose", e);
        }

    }

    */
/**
     * 设置机票舱位分析
     *
     * @param row
     * @param entity
     *//*

    private void setFlightCabinRow(Row row, FlightBehaviorInfo entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(Optional.ofNullable(entity.getTotalPrice()).orElse(BigDecimal.ZERO).doubleValue());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(Optional.ofNullable(entity.getTotalQuantity()).orElse(0));
//        row.createCell(2).setCellType(CellType.NUMERIC);
//        row.getCell(2).setCellValue(entity.getQuantityPercent());
    }

    */
/**
     * 填充机票-扣率分析
     *
     * @param sheet
     *//*

    private void fillFlightRateAnalysis(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            OnlineReportFltDiscountRangeRequest request = new OnlineReportFltDiscountRangeRequest();
            request.setProductType("dom");
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            OnlineReportFltDiscountRangeResponse responseType = corpOnlineReportPlatformService.queryFltDiscountRangeAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                List<FltDiscountRangeInfo> discountRangeInfoList = responseType.getDiscountRangeList();
                FltDiscountRangeEnum[] fltDiscountRangeEnums = FltDiscountRangeEnum.values();
                for (int j = 0; j < fltDiscountRangeEnums.length; j++) {
                    FltDiscountRangeEnum fltDiscountRangeEnum = fltDiscountRangeEnums[j];
                    FltDiscountRangeInfo fltDiscountRangeInfo = discountRangeInfoList.stream()
                            .filter(i -> StringUtils.equalsIgnoreCase(i.getRange(), fltDiscountRangeEnum.getKey().toString()))
                            .findFirst().orElse(new FltDiscountRangeInfo());
                    setFlightRateRow(sheet.createRow(FLIGHT_RATE_START_ROW + j), fltDiscountRangeInfo);

                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightRateAnalysis", e);
        }
    }

    */
/**
     * 设置机票扣率分析
     *
     * @param row
     * @param entity
     *//*

    private void setFlightRateRow(Row row, FltDiscountRangeInfo entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalQuantity()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getQuantityPercent()));
    }

    */
/**
     * 填充机票-top5承运商
     *
     * @param sheet
     *//*

    private void fillFlightCarrier(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            OnlineReportSupplierTopRequest request = new OnlineReportSupplierTopRequest();
            request.setQueryBu(QueryReportBuTypeEnum.flight);
            request.setLang(baseCondition.getLang());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map<String, String> extData = new HashMap<>();
            extData.put("dim", dimEn(baseCondition.getLang(), "airline_name", QueryReportBuTypeEnum.flight));
            request.setExtData(extData);
            request.setTopLimit(5);
            // 国内
            request.setProductType("dom");
            OnlineReportSupplierTopResponse domResponseType = corpOnlineReportPlatformService.querySupplierTop(request);
            if (domResponseType != null && domResponseType.getResponseCode() == 20000) {
                SupplierFltTopInfo supplierFltTopInfo = Optional.ofNullable(domResponseType.getFltTopInfo()).orElse(new SupplierFltTopInfo());
                List<OnlineReportSupplierFltTop> list = Optional.ofNullable(supplierFltTopInfo.getTopList()).orElse(new ArrayList<>());
                for (int j = 0; j < list.size(); j++) {
                    setFlightCarrierRow(sheet.createRow(FLIGHT_CARRIER_DOM_START_ROW + j), Optional.ofNullable(list.get(j)).orElse(new OnlineReportSupplierFltTop()));
                }
                setFlightCarrierRow(sheet.createRow(FLIGHT_CARRIER_DOM_START_ROW + 5), Optional.ofNullable(supplierFltTopInfo.getOtherInfo()).orElse(new OnlineReportSupplierFltTop()));
            }
            // 国际
            request.setProductType("inter");
            OnlineReportSupplierTopResponse interResponseType = corpOnlineReportPlatformService.querySupplierTop(request);
            if (interResponseType != null && interResponseType.getResponseCode() == 20000) {
                SupplierFltTopInfo supplierFltTopInfo = Optional.ofNullable(interResponseType.getFltTopInfo()).orElse(new SupplierFltTopInfo());
                List<OnlineReportSupplierFltTop> list = Optional.ofNullable(supplierFltTopInfo.getTopList()).orElse(new ArrayList<>());
                for (int j = 0; j < list.size(); j++) {
                    setFlightCarrierRow(sheet.createRow(FLIGHT_CARRIER_INT_START_ROW + j), Optional.ofNullable(list.get(j)).orElse(new OnlineReportSupplierFltTop()));
                }
                setFlightCarrierRow(sheet.createRow(FLIGHT_CARRIER_INT_START_ROW + 5), Optional.ofNullable(supplierFltTopInfo.getOtherInfo()).orElse(new OnlineReportSupplierFltTop()));
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightCarrier", e);
        }
    }

    public String dimEn(String lang, String dim, QueryReportBuTypeEnum reportBuTypeEnum) {
        boolean isEn = SharkUtils.isEN(lang);
        String dataDim;
        switch (dim) {
            // 机票
            case "airline_name":
                dataDim = isEn ? "airline_en_name" : "airline_cn_name";
                break;
            case "flight_city":
                dataDim = isEn ? "flight_city_en" : "flight_city";
                break;
            // 机票、火车、用车（用车departure_city_name没有英文）
            case "departure_city_name":
                dataDim = reportBuTypeEnum == QueryReportBuTypeEnum.car ? "departure_city_name" : (isEn ? "departure_city_name_en" : "departure_city_name");
                break;
            // 机票、火车
            case "arrival_city_name":
                dataDim = isEn ? "arrival_city_name_en" : "arrival_city_name";
                break;
            // 酒店
            case "city_name":
                dataDim = isEn ? "city_name_en" : "city_name";
                break;
            case "hotel_name":
                dataDim = isEn ? "hotel_name_en" : "hotel_name";
                break;
            case "hotel_group_name":
                dataDim = isEn ? "hotel_group_name_en" : "hotel_group_name";
                break;
            case "hotel_brand_name":
                dataDim = "hotel_brand_name";
                break;
            case "hotel_city_level":
                dataDim = "pcitylevel";
                break;
            case "star":
                dataDim = "star";
                break;
            // 火车
            case "line_city":
                dataDim = isEn ? "line_city_en" : "line_city";
                break;
            // 用车
            case "vendor_name":
                dataDim = "vendor_name";
                break;
            case "vehicle_name":
                dataDim = "vehicle_name";
                break;
            default:
                dataDim = "";
        }
        return dataDim;
    }

    */
/**
     * 设置机票承运商
     *
     * @param row
     * @param entity
     *//*

    private void setFlightCarrierRow(Row row, OnlineReportSupplierFltTop entity) {
        row.createCell(0).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getSumPrice()).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getSumQuantity()));
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getQuantityPercent()).doubleValue());
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(MapperUtils.convertDigitToZero(entity.getAgreementTag()));
    }

    */
/**
     * 填充机票-top5航线
     *
     * @param sheet
     *//*

    private void fillFlightCity(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            OnlineReportSupplierTopRequest request = new OnlineReportSupplierTopRequest();
            request.setQueryBu(QueryReportBuTypeEnum.flight);
            request.setLang(baseCondition.getLang());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map<String, String> extData = new HashMap<>();
            extData.put("dim", dimEn(baseCondition.getLang(), "flight_city", QueryReportBuTypeEnum.flight));
            request.setExtData(extData);
            request.setTopLimit(5);
            // 国内
            request.setProductType("dom");
            OnlineReportSupplierTopResponse domResponseType = corpOnlineReportPlatformService.querySupplierTop(request);
            if (domResponseType != null && domResponseType.getResponseCode() == 20000) {
                SupplierFltTopInfo supplierFltTopInfo = Optional.ofNullable(domResponseType.getFltTopInfo()).orElse(new SupplierFltTopInfo());
                List<OnlineReportSupplierFltTop> list = Optional.ofNullable(supplierFltTopInfo.getTopList()).orElse(new ArrayList<>());
                for (int j = 0; j < list.size(); j++) {
                    setFlightCarrierRow(sheet.createRow(FLIGHT_CITY_DOM_START_ROW + j), Optional.ofNullable(list.get(j)).orElse(new OnlineReportSupplierFltTop()));
                }
                setFlightCarrierRow(sheet.createRow(FLIGHT_CITY_DOM_START_ROW + 5), Optional.ofNullable(supplierFltTopInfo.getOtherInfo()).orElse(new OnlineReportSupplierFltTop()));
            }
            // 国际
            request.setProductType("inter");
            OnlineReportSupplierTopResponse interResponseType = corpOnlineReportPlatformService.querySupplierTop(request);
            if (interResponseType != null && interResponseType.getResponseCode() == 20000) {
                SupplierFltTopInfo supplierFltTopInfo = Optional.ofNullable(interResponseType.getFltTopInfo()).orElse(new SupplierFltTopInfo());
                List<OnlineReportSupplierFltTop> list = Optional.ofNullable(supplierFltTopInfo.getTopList()).orElse(new ArrayList<>());
                for (int j = 0; j < list.size(); j++) {
                    setFlightCarrierRow(sheet.createRow(FLIGHT_CITY_INT_START_ROW + j), Optional.ofNullable(list.get(j)).orElse(new OnlineReportSupplierFltTop()));
                }
                setFlightCarrierRow(sheet.createRow(FLIGHT_CITY_INT_START_ROW + 5), Optional.ofNullable(supplierFltTopInfo.getOtherInfo()).orElse(new OnlineReportSupplierFltTop()));
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightCity", e);
        }
    }

    */
/**
     * 填充机票-Top5城市对-Top5承运商
     *
     * @param sheet
     *//*

    private void fillFlightCityAndCarrier(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            // 国内数据
            fillFlightCityAndCarrier(sheet, baseCondition, FLIGHT_CITY_AND_CARRIER_DOM_START_ROW, "dom");
            // 国际数据
            fillFlightCityAndCarrier(sheet, baseCondition, FLIGHT_CITY_AND_CARRIER_INT_START_ROW, "inter");
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightCity", e);
        }
    }

    */
/**
     * 填充机票-Top5城市对-Top5承运商
     *
     * @param sheet
     *//*

    private void fillFlightCityAndCarrier(Sheet sheet, BaseQueryConditionBO baseCondition, int startIndex, String productType) {
        try {
            OnlineReportSupplierTopRequest request = new OnlineReportSupplierTopRequest();
            request.setQueryBu(QueryReportBuTypeEnum.flight);
            request.setLang(baseCondition.getLang());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map<String, String> extData = new HashMap<>();
            extData.put("dim", dimEn(baseCondition.getLang(), "flight_city", QueryReportBuTypeEnum.flight));
            request.setExtData(extData);
            request.setTopLimit(5);
            // 国内
            request.setProductType(productType);
            OnlineReportSupplierTopResponse domResponseType = corpOnlineReportPlatformService.querySupplierTop(request);
            if (domResponseType != null && domResponseType.getResponseCode() == 20000) {
                SupplierFltTopInfo supplierFltTopInfo = Optional.ofNullable(domResponseType.getFltTopInfo()).orElse(new SupplierFltTopInfo());
                List<OnlineReportSupplierFltTop> list = Optional.ofNullable(supplierFltTopInfo.getTopList()).orElse(new ArrayList<>());
                for (int j = 0; j < list.size(); j++) {
                    Row domFlightCityRow1 = sheet.getRow(startIndex - 1);
                    domFlightCityRow1 = Objects.isNull(domFlightCityRow1) ? sheet.createRow(startIndex - 1) : domFlightCityRow1;
                    domFlightCityRow1.createCell(j * 4 + 1).setCellValue(list.get(j).getDim());
                    extData.put("dim", dimEn(baseCondition.getLang(), "airline_name", QueryReportBuTypeEnum.flight));
                    extData.put("flight_city", list.get(j).getDim());
                    request.setExtData(extData);
                    OnlineReportSupplierTopResponse domAirLineResponseType = corpOnlineReportPlatformService.querySupplierTop(request);
                    if (domAirLineResponseType != null && domAirLineResponseType.getResponseCode() == 20000) {
                        SupplierFltTopInfo airLineSupplierFltTopInfo = Optional.ofNullable(domAirLineResponseType.getFltTopInfo()).orElse(new SupplierFltTopInfo());
                        List<OnlineReportSupplierFltTop> airLinelist = Optional.ofNullable(airLineSupplierFltTopInfo.getTopList()).orElse(new ArrayList<>());
                        for (int k = 0; k < airLinelist.size(); k++) {
                            Row row = sheet.getRow(startIndex + k);
                            row = Objects.isNull(row) ? sheet.createRow(startIndex + k) : row;
                            setFlightCityAndCarrierRow(row, Optional.ofNullable(airLinelist.get(k)).orElse(new OnlineReportSupplierFltTop()), j * 4 + 1);
                        }
                        Row row = sheet.getRow(startIndex + 5);
                        row = Objects.isNull(row) ? sheet.createRow(startIndex + 5) : row;
                        setFlightCityAndCarrierRow(row, Optional.ofNullable(airLineSupplierFltTopInfo.getOtherInfo()).orElse(new OnlineReportSupplierFltTop()), j * 4 + 1);
                    }
                }

            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightCity", e);
        }
    }

    */
/**
     * 设置机票承运商
     * fillFlightCityAndCarrier
     *
     * @param row
     * @param entity
     *//*

    private void setFlightCityAndCarrierRow(Row row, OnlineReportSupplierFltTop entity, int startColIndex) {
        row.createCell(startColIndex).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(startColIndex + 1).setCellType(CellType.NUMERIC);
        row.getCell(startColIndex + 1).setCellValue(MapperUtils.convertDigitToZero(entity.getSumQuantity()));
    }

    */
/**
     * 填充机票-协议航空采购
     *
     * @param sheet
     *//*

    private void fillFlightAgreementAirPurchase(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportSupplierTopRequest request = new OnlineReportSupplierTopRequest();
            request.setQueryBu(QueryReportBuTypeEnum.flight);
            request.setLang(baseCondition.getLang());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map<String, String> extData = new HashMap<>();
            extData.put("dim", dimEn(baseCondition.getLang(), "airline_name", QueryReportBuTypeEnum.flight));
            // 只查协议航司
            extData.put("contractType", "C");
            request.setExtData(extData);
            request.setTopLimit(1000);
            OnlineReportSupplierTopResponse response = corpOnlineReportPlatformService.querySupplierTop(request);
            if (response != null && response.getResponseCode() == 20000) {
                SupplierFltTopInfo supplierFltTopInfo = Optional.ofNullable(response.getFltTopInfo()).orElse(new SupplierFltTopInfo());
                List<OnlineReportSupplierFltTop> list = Optional.ofNullable(supplierFltTopInfo.getTopList()).orElse(new ArrayList<>());
                for (int j = 0; j < list.size(); j++) {
                    setFlightAgreementAirPurchaseRow(sheet.createRow(FLIGHT_AGREEMENT_AIR_PURCHASE_START_ROW + 1 + j), Optional.ofNullable(list.get(j)).orElse(new OnlineReportSupplierFltTop()));
                }
            }

            OnlineReportSupplierTopRequest allRequest = new OnlineReportSupplierTopRequest();
            allRequest.setQueryBu(QueryReportBuTypeEnum.flight);
            allRequest.setLang(baseCondition.getLang());
            allRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            Map<String, String> allExtData = new HashMap<>();
            allExtData.put("dim", dimEn(baseCondition.getLang(), "airline_name", QueryReportBuTypeEnum.flight));
            allRequest.setExtData(allExtData);
            allRequest.setTopLimit(1000);
            OnlineReportSupplierTopResponse domResponseType = corpOnlineReportPlatformService.querySupplierTop(allRequest);
            if (domResponseType != null && domResponseType.getResponseCode() == 20000) {
                SupplierFltTopInfo supplierFltTopInfo = Optional.ofNullable(domResponseType.getFltTopInfo()).orElse(new SupplierFltTopInfo());
                OnlineReportSupplierFltTop sumInfo = Optional.ofNullable(supplierFltTopInfo.getSumInfo()).orElse(new OnlineReportSupplierFltTop());
                // 设置承运商总计行
                Row allRow = sheet.createRow(FLIGHT_AGREEMENT_AIR_PURCHASE_CARRIER_ROW);
                allRow.createCell(0).setCellType(CellType.NUMERIC);
                allRow.getCell(0).setCellValue(MapperUtils.convertDigitToZero(sumInfo.getSumQuantity()));
                allRow.createCell(1).setCellType(CellType.NUMERIC);
                allRow.getCell(1).setCellValue(MapperUtils.convertDigitToZero(sumInfo.getSumPrice()).doubleValue());
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightAgreementAirPurchase", e);
        }
    }

    */
/**
     * 设置机票-协议航空采购
     *
     * @param row
     * @param entity
     *//*

    private void setFlightAgreementAirPurchaseRow(Row row, OnlineReportSupplierFltTop entity) {
        row.createCell(0).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getSumQuantity()));
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getSumPrice()).doubleValue());
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getForecastAmount()).doubleValue());
    }

    */
/**
     * 填充机票-二氧化碳排放量
     *
     * @param sheet
     *//*

    private void fillFlightCarbon(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportCarbonsViewRequest request = new OnlineReportCarbonsViewRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            Map<String, String> extData = new HashMap<>();
            extData.put("queryType", "carbonType");
            request.setExtData(extData);
            request.setExtData(extData);
            OnlineReportCarbonsViewResponse responseType = corpOnlineReportPlatformService.queryCarbonsView(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                List<CarbonsTypeInfo> list = Optional.ofNullable(responseType.getCarbonTypeList()).orElse(new ArrayList<>());
                CarbonsTypeInfo shortFlightCarbon = list.stream().filter(Objects::nonNull).filter(i -> i.getCarbonType() == 1).findFirst().orElse(new CarbonsTypeInfo());
                CarbonsTypeInfo normalFlightCarbon = list.stream().filter(Objects::nonNull).filter(i -> i.getCarbonType() == 2).findFirst().orElse(new CarbonsTypeInfo());
                CarbonsTypeInfo longFlightCarbon = list.stream().filter(Objects::nonNull).filter(i -> i.getCarbonType() == 3).findFirst().orElse(new CarbonsTypeInfo());
                setFlightCarbonRow(sheet.createRow(FLIGHT_CARBON_START_ROW), shortFlightCarbon);
                setFlightCarbonRow(sheet.createRow(FLIGHT_CARBON_START_ROW + 1), normalFlightCarbon);
                setFlightCarbonRow(sheet.createRow(FLIGHT_CARBON_START_ROW + 2), longFlightCarbon);
            }

        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillFlightCarbon", e);
        }

    }

    */
/**
     * 设置机票-二氧化碳排放量
     *
     * @param row
     * @param entity
     *//*

    private void setFlightCarbonRow(Row row, CarbonsTypeInfo entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.createCell(0).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalTpms()).doubleValue());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.createCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalCarbons()).doubleValue());

    }

    */
/**
     * 填充酒店月度明细
     *
     * @param sheet
     *//*

    public void fillHotelMonthDetail(Sheet sheet, BaseQueryConditionBO baseCondition) {
        //消费趋势
        OnlineReportTrendRequest request = new OnlineReportTrendRequest();
        request.setQueryBu(QueryReportBuTypeEnum.hotel);
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setQueryType(QueryReportTypeionEnum.amount);
        request.setDateDimension(getDateDimesion(baseCondition.getDateDimension()));
        request.setAggType(QueryReportAggTypeEnum.current);
        request.setExtData(new HashMap<String, String>() {{
            put("dim", "order_type");
            put("yoyDuration", "12");
        }});
        try {
            OnlineReportTrendResponse orderTypeResponseType = corpOnlineReportPlatformService.queryReportTrend(request);
            if (orderTypeResponseType != null && orderTypeResponseType.getResponseCode() == 20000) {
                Row cMonthRow = sheet.createRow(HOTEL_MONTH_DETAIL_C_START_ROW);
                Row cMoneyRow = sheet.createRow(HOTEL_MONTH_DETAIL_C_START_ROW + 1);
                Row cTicketRow = sheet.createRow(HOTEL_MONTH_DETAIL_C_START_ROW + 2);

                Row mMonthRow = sheet.createRow(HOTEL_MONTH_DETAIL_M_START_ROW);
                Row mMoneyRow = sheet.createRow(HOTEL_MONTH_DETAIL_M_START_ROW + 1);
                Row mTicketRow = sheet.createRow(HOTEL_MONTH_DETAIL_M_START_ROW + 2);

                List<OnlineReportTrendPoint> onlineReportTrendPoints = orderTypeResponseType.getData().getData();
                for (int i = 0; onlineReportTrendPoints != null && i < onlineReportTrendPoints.size(); i++) {
                    OnlineReportTrendPoint detail = onlineReportTrendPoints.get(i);
                    cMonthRow.createCell(i).setCellValue(detail.getAxis());
                    cMoneyRow.createCell(i).setCellType(CellType.NUMERIC);
                    cMoneyRow.getCell(i).setCellValue(detail.getData().getOrDefault("Camount", BigDecimal.ZERO).doubleValue());
                    cTicketRow.createCell(i).setCellValue(detail.getData().getOrDefault("Cquantity", BigDecimal.ZERO).intValue());

                    mMonthRow.createCell(i).setCellValue(detail.getAxis());
                    mMoneyRow.createCell(i).setCellType(CellType.NUMERIC);
                    mMoneyRow.getCell(i).setCellValue(detail.getData().getOrDefault("Mamount", BigDecimal.ZERO).doubleValue());
                    mTicketRow.createCell(i).setCellValue(detail.getData().getOrDefault("Mquantity", BigDecimal.ZERO).intValue());
                }
            }
            request.setExtData(new HashMap<String, String>() {{
                put("dim", "is_oversea");
                put("yoyDuration", "12");
            }});
            OnlineReportTrendResponse isOverSeaResponseType = corpOnlineReportPlatformService.queryReportTrend(request);
            if (isOverSeaResponseType != null && isOverSeaResponseType.getResponseCode() == 20000) {
                Row domMonthRow = sheet.createRow(HOTEL_MONTH_DETAIL_T_START_ROW);
                Row domMoneyRow = sheet.createRow(HOTEL_MONTH_DETAIL_T_START_ROW + 1);
                Row domTicketRow = sheet.createRow(HOTEL_MONTH_DETAIL_T_START_ROW + 2);

                Row internalMonthRow = sheet.createRow(HOTEL_MONTH_DETAIL_F_START_ROW);
                Row internalMoneyRow = sheet.createRow(HOTEL_MONTH_DETAIL_F_START_ROW + 1);
                Row internalTicketRow = sheet.createRow(HOTEL_MONTH_DETAIL_F_START_ROW + 2);

                List<OnlineReportTrendPoint> onlineReportTrendPoints = isOverSeaResponseType.getData().getData();
                for (int i = 0; onlineReportTrendPoints != null && i < onlineReportTrendPoints.size(); i++) {
                    OnlineReportTrendPoint detail = onlineReportTrendPoints.get(i);
                    domMonthRow.createCell(i).setCellValue(detail.getAxis());
                    domMoneyRow.createCell(i).setCellType(CellType.NUMERIC);
                    domMoneyRow.getCell(i).setCellValue(detail.getData().getOrDefault("O/Famount", BigDecimal.ZERO).doubleValue());
                    domTicketRow.createCell(i).setCellValue(detail.getData().getOrDefault("O/Fquantity", BigDecimal.ZERO).intValue());

                    internalMonthRow.createCell(i).setCellValue(detail.getAxis());
                    internalMoneyRow.createCell(i).setCellType(CellType.NUMERIC);
                    internalMoneyRow.getCell(i).setCellValue(detail.getData().getOrDefault("Tamount", BigDecimal.ZERO).doubleValue());
                    internalTicketRow.createCell(i).setCellValue(detail.getData().getOrDefault("Tquantity", BigDecimal.ZERO).intValue());
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillHotelMonthDetail", e);
        }
    }

    */
/**
     * 填充机票-酒店KPI月度累计
     *
     * @param sheet
     *//*

    private void fillHotelKpi(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            List<String> titleList = null;
            BaseQueryCondition baseQueryCondition = new BaseQueryCondition();
            BeanUtils.copyProperties(baseCondition.getBaseQueryCondition(), baseQueryCondition);
            //平均折扣
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), HOTEL_KPI_START_ROW, "HTL_SAVE_RATE_TREND", false, baseCondition.getDateDimension(), StringUtils.EMPTY);
            //全家票张占比
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), HOTEL_KPI_START_ROW + 4, "HTL_POTENTIAL_SAVE_RATE_TREND", false, baseCondition.getDateDimension(), StringUtils.EMPTY);
            //RC订单比例
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), HOTEL_KPI_START_ROW + 8, "HTL_RC_PERCENT_TREND", false, baseCondition.getDateDimension(), StringUtils.EMPTY);
            //低价RC订单比例
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), HOTEL_KPI_START_ROW + 12, "HTL_LOW_RC_PERCENT_TREND", false, baseCondition.getDateDimension(), StringUtils.EMPTY);
            //提前5天以上预订比例
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), HOTEL_KPI_START_ROW + 20, "HTL_AGREEMENT_RC_PERCENT_TREND", false, baseCondition.getDateDimension(), StringUtils.EMPTY);
            Row titleRow = sheet.createRow(FLIGHT_KPI_START_ROW - 1);
            // 设置月份
            if (CollectionUtils.isNotEmpty(titleList)) {
                for (int i = 0; i < titleList.size(); i++) {
                    titleRow.createCell(i).setCellValue(titleList.get(i));
                }
            }

        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillHotelKpi", e);
        }

    }

    */
/**
     * 填充酒店-节省分析
     *
     * @param sheet
     *//*

    private void fillHotelSaveAnalysis(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportSaveProportionDetailRequest detailRequest = new OnlineReportSaveProportionDetailRequest();
            detailRequest.setQueryBu(QueryReportBuTypeEnum.hotel);
            detailRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            detailRequest.setLang(baseCondition.getLang());
            OnlineReportSaveProportionDetailResponse detailResponse = corpOnlineReportPlatformService.querySaveProportionDetail(detailRequest);
            if (detailResponse != null && detailResponse.getResponseCode() == 20000) {
                List<OnlineReportSaveProportionDetail> list = Optional.ofNullable(detailResponse.getProportionDetails()).orElse(new ArrayList<>());
                setHotelSaveDetailRow(sheet.createRow(HOTEL_SAVE_AND_LOSE_START_ROW), list.size() > 0 ? list.get(0) : new OnlineReportSaveProportionDetail());
                setHotelSaveDetailRow(sheet.createRow(HOTEL_SAVE_AND_LOSE_START_ROW + 1), list.size() > 1 ? list.get(1) : new OnlineReportSaveProportionDetail());
                setHotelSaveDetailRow(sheet.createRow(HOTEL_SAVE_AND_LOSE_START_ROW + 2), list.size() > 2 ? list.get(2) : new OnlineReportSaveProportionDetail());
            }
            OnlineReportSaveGeneralRequest saveGeneralRequest = new OnlineReportSaveGeneralRequest();
            saveGeneralRequest.setQueryBu(QueryReportBuTypeEnum.hotel);
            saveGeneralRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            saveGeneralRequest.setLang(baseCondition.getLang());
            Map map = new HashMap();
            map.put("index", "GENERAL_SAVE");
            // 概览节省分布不需要total信息
            map.put("needTotal", "F");
            saveGeneralRequest.setExtData(map);
            OnlineReportSaveGeneralResponse saveGeneralResponse = corpOnlineReportPlatformService.querySaveGeneralInfo(saveGeneralRequest);
            if (saveGeneralResponse != null && saveGeneralResponse.getResponseCode() == 20000) {
                GeneralSaveInfo saveInfo = Optional.ofNullable(saveGeneralResponse.getSaveInfo()).orElse(new GeneralSaveInfo());
                setHotelSaveSumRow(sheet.createRow(HOTEL_SAVE_AND_LOSE_START_ROW + 3), saveInfo);
            }

            // 节省趋势
            OnlineReportTrendRequest trendCurrentRequest = new OnlineReportTrendRequest();
            trendCurrentRequest.setQueryBu(QueryReportBuTypeEnum.hotel);
            trendCurrentRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            trendCurrentRequest.setQueryType(QueryReportTypeionEnum.amount);
            trendCurrentRequest.setDateDimension(getDateDimesion(baseCondition.getDateDimension()));
            trendCurrentRequest.setAggType(QueryReportAggTypeEnum.current);
            Map trendCurrentMap = new HashMap();
            trendCurrentMap.put("lang", baseCondition.getLang());
            trendCurrentMap.put("dim", "type");
            trendCurrentRequest.setExtData(trendCurrentMap);
            OnlineReportTrendResponse trendCurrentResponse = corpOnlineReportPlatformService.queryReportSaveTrend(trendCurrentRequest);
            if (trendCurrentResponse != null && trendCurrentResponse.getResponseCode() == 20000) {
                List<OnlineReportTrendPoint> trendPoints = trendCurrentResponse.getData().getData();
                setHotelSaveMonthRow(sheet, trendPoints);
            }

        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillHotelSaveAnalysis", e);
        }

    }

    */
/**
     * 设置酒店-节省分布详情
     *
     * @param row
     * @param entity
     *//*

    private void setHotelSaveDetailRow(Row row, OnlineReportSaveProportionDetail entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(entity.getTypeNum()).doubleValue());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getSavePerQuantity()).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(DigitBaseUtils.divide(entity.getSaveRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(3).setCellValue(MapperUtils.trim(entity.getType()));

    }

    */
/**
     * 设置酒店-节省分布汇总
     *
     * @param row
     * @param entity
     *//*

    private void setHotelSaveSumRow(Row row, GeneralSaveInfo entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(entity.getCompanySaveAmount()).doubleValue());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getCompanyPerQtySave()).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(DigitBaseUtils.divide(entity.getCompanySaveRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
    }

    */
/**
     * 设置酒店-节省分析-逐月
     *
     * @param sheet
     * @param trendPoints
     *//*

    private void setHotelSaveMonthRow(Sheet sheet, List<OnlineReportTrendPoint> trendPoints) {
        Row row1 = sheet.createRow(HOTEL_SAVE_AND_LOSE_START_ROW + 4);
        Row row2 = sheet.createRow(HOTEL_SAVE_AND_LOSE_START_ROW + 5);
        Row row3 = sheet.createRow(HOTEL_SAVE_AND_LOSE_START_ROW + 6);

        List<String> titleList = trendPoints.stream().map(OnlineReportTrendPoint::getAxis).collect(Collectors.toList());

        //设置月份标题
        Row titleRow = sheet.createRow(HOTEL_SAVE_AND_LOSE_START_ROW + 7);
        for (int i = 0; i < titleList.size(); i++) {
            titleRow.createCell(i).setCellValue(titleList.get(i));
        }
        for (int i = 0; i < trendPoints.size(); i++) {
            Map<String, BigDecimal> map = trendPoints.get(i).getData();
            row1.createCell(i).setCellType(CellType.NUMERIC);
            row1.getCell(i).setCellValue(MapperUtils.convertDigitToZero((BigDecimal) map.get("cSave")).doubleValue());
            row2.createCell(i).setCellType(CellType.NUMERIC);
            row2.getCell(i).setCellValue(MapperUtils.convertDigitToZero((BigDecimal) map.get("premiumSave")).doubleValue());
            row3.createCell(i).setCellType(CellType.NUMERIC);
            row3.getCell(i).setCellValue(MapperUtils.convertDigitToZero((BigDecimal) map.get("promotionSave")).doubleValue());
        }
    }

    */
/**
     * 填充酒店部门消费
     *
     * @param sheet
     *//*

    public void fillHotelDeptDetail(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            OnlineReportTopDeptConsumeDetailRequest request = new OnlineReportTopDeptConsumeDetailRequest();
            request.setQueryBu(QueryReportBuTypeEnum.hotel);
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            AnalysisObjectEnum analysisObjectEnum = StringUtils.isEmpty(baseCondition.getAnalysisObject()) ?
                    AnalysisObjectEnum.CORP : AnalysisObjectEnum.valueOf(baseCondition.getAnalysisObject().toUpperCase());
            request.setAnalysisObjectEnum(analysisObjectEnum);
            OnlineReportTopDeptConsumeDetailResponse responseType1 = corpOnlineReportPlatformService.queryTopDeptConsumeDetail(request);
            if (responseType1 != null && responseType1.getResponseCode() == 20000) {
                HtlTopDeptConsumeInfo detailAmountDto = Optional.ofNullable(responseType1.getHtlTopConsumeInfo()).orElse(new HtlTopDeptConsumeInfo());
                List<OnlineReportHtlTopDeptConsume> list = Optional.ofNullable(detailAmountDto.getTopList()).orElse(new ArrayList<>());
                for (int i = 0; i < list.size(); i++) {
                    setHotelDeptRow(sheet.createRow(HOTEL_DEPT_START_ROW + i), Optional.ofNullable(list.get(i)).orElse(new OnlineReportHtlTopDeptConsume()));
                }
                //设置国内总计
                OnlineReportHtlTopDeptConsume nDetail = Optional.ofNullable(detailAmountDto.getSumConsume()).orElse(new OnlineReportHtlTopDeptConsume());
                setHotelDeptRow(sheet.createRow(HOTEL_DEPT_START_ROW + TOP_ONE_HUNDRED + 1), nDetail);
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillHotelDeptDetail", e);
        }
    }

    */
/**
     * 设置酒店-部门酒店消费
     *
     * @param row
     * @param entity
     *//*

    private void setHotelDeptRow(Row row, OnlineReportHtlTopDeptConsume entity) {
        row.createCell(0).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmount()).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalQuantity()));
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPrice()).doubleValue());

        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmountTa()).doubleValue());
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.getCell(5).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalQuantityTa()));
        row.createCell(6).setCellType(CellType.NUMERIC);
        row.getCell(6).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceTa()).doubleValue());

        row.createCell(7).setCellType(CellType.NUMERIC);
        row.getCell(7).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmountNta()).doubleValue());
        row.createCell(8).setCellType(CellType.NUMERIC);
        row.getCell(8).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalQuantityNta()));
        row.createCell(9).setCellType(CellType.NUMERIC);
        row.getCell(9).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceNta()).doubleValue());

        row.createCell(10).setCellType(CellType.NUMERIC);
        row.getCell(10).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmountDom()).doubleValue());
        row.createCell(11).setCellType(CellType.NUMERIC);
        row.getCell(11).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalQuantityDom()));
        row.createCell(12).setCellType(CellType.NUMERIC);
        row.getCell(12).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceDom()).doubleValue());

        row.createCell(13).setCellType(CellType.NUMERIC);
        row.getCell(13).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmountInter()).doubleValue());
        row.createCell(14).setCellType(CellType.NUMERIC);
        row.getCell(14).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalQuantityInter()));
        row.createCell(15).setCellType(CellType.NUMERIC);
        row.getCell(15).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceInter()).doubleValue());

    }

    */
/**
     * 填充酒店-酒店预订方式
     *
     * @param sheet
     *//*

    private void fillHotelBookType(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportBookTypeRequest bookTypeRequest = new OnlineReportBookTypeRequest();
            bookTypeRequest.setQueryBu(QueryReportBuTypeEnum.hotel);
            bookTypeRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            // 三方协议
            bookTypeRequest.setExtData(new HashMap<String, String>() {{
                put("contractType", "C");
            }});
            OnlineReportBookTypeResponse responseType1 = corpOnlineReportPlatformService.queryBookTypeAnalysis(bookTypeRequest);
            if (responseType1 != null && responseType1.getResponseCode() == 20000) {
                List<BookTypeInfo> domFlightBookType = responseType1.getBookTypeList();
                Row domRow = sheet.createRow(HOTEL_BOOK_TYPE_START_ROW);
                setBookTypeRow(domRow, domFlightBookType);
            }
            // 非三方协议
            bookTypeRequest.setExtData(new HashMap<String, String>() {{
                put("contractType", "NC");
            }});
            OnlineReportBookTypeResponse responseType2 = corpOnlineReportPlatformService.queryBookTypeAnalysis(bookTypeRequest);
            if (responseType2 != null && responseType2.getResponseCode() == 20000) {
                List<BookTypeInfo> intFlightBookType = responseType2.getBookTypeList();
                Row intRow = sheet.createRow(HOTEL_BOOK_TYPE_START_ROW + 1);
                setBookTypeRow(intRow, intFlightBookType);
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillHotelBookType", e);
        }
    }

    */
/**
     * 填充酒店-星级分析
     *
     * @param sheet
     *//*

    private void fillHotelStar(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportSupplierTopRequest request = new OnlineReportSupplierTopRequest();
            request.setQueryBu(QueryReportBuTypeEnum.hotel);
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map<String, String> extData = new HashMap<>();
            extData.put("dim", dimEn(baseCondition.getLang(), "star", QueryReportBuTypeEnum.hotel));
            // 三方协议
            extData.put("contractType", "C");
            request.setExtData(extData);
            OnlineReportSupplierTopResponse response1 = corpOnlineReportPlatformService.querySupplierTop(request);
            if (response1 != null && response1.getResponseCode() == 20000) {
                SupplierHtlTopInfo supplierHtlTopInfo = Optional.ofNullable(response1.getHtlTopInfo()).orElse(new SupplierHtlTopInfo());
                List<OnlineReportSupplierHtlTop> list = Optional.ofNullable(supplierHtlTopInfo.getTopList()).orElse(new ArrayList<>());
                setH(sheet, list, HOTEL_STAR_C_START_ROW);
            }
            // 非三方协议
            extData.put("contractType", "NC");
            OnlineReportSupplierTopResponse response2 = corpOnlineReportPlatformService.querySupplierTop(request);
            if (response2 != null && response2.getResponseCode() == 20000) {
                SupplierHtlTopInfo supplierHtlTopInfo = Optional.ofNullable(response2.getHtlTopInfo()).orElse(new SupplierHtlTopInfo());
                List<OnlineReportSupplierHtlTop> list = Optional.ofNullable(supplierHtlTopInfo.getTopList()).orElse(new ArrayList<>());
                setH(sheet, list, HOTEL_STAR_M_START_ROW);
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillHotelBookType", e);
        }
    }

    private void setH(Sheet sheet, List<OnlineReportSupplierHtlTop> entity, Integer rownum) {
        for (int i = 0; entity != null && i < entity.size(); i++) {
            OnlineReportSupplierHtlTop top = Optional.ofNullable(entity.get(i)).orElse(new OnlineReportSupplierHtlTop());
            Integer star = Integer.parseInt(top.getDim());
            if (star == 1) {
                setHotelStarRow(sheet.createRow(rownum), top);
            } else if (star == 2) {
                setHotelStarRow(sheet.createRow(rownum + 1), top);
            } else if (star == 3) {
                setHotelStarRow(sheet.createRow(rownum + 2), top);
            } else if (star == 4) {
                setHotelStarRow(sheet.createRow(rownum + 3), top);
            } else if (star == 5) {
                setHotelStarRow(sheet.createRow(rownum + 4), top);
            }
        }
    }

    */
/**
     * 设置酒店-酒店星级分析
     *
     * @param row
     * @param entity
     *//*

    private void setHotelStarRow(Row row, OnlineReportSupplierHtlTop entity) {
        row.createCell(0).setCellValue(MapperUtils.trim(entity.getDim()));
        // 金额
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getSumAmount()).doubleValue());
        // 间夜
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getSumQuantity()).doubleValue());
        // 一线城市均价
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceFirstTier()).doubleValue());
        // 非一线城市均价
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceNonFirstTier()).doubleValue());
        // 国内均价
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.getCell(5).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceDom()).doubleValue());
        // 海外均价
        row.createCell(6).setCellType(CellType.NUMERIC);
        row.getCell(6).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceInter()).doubleValue());
    }

    */
/**
     * 填充酒店-top5酒店城市分析
     *
     * @param sheet
     *//*

    private void fillHotelCity(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            OnlineReportSupplierTopRequest request = new OnlineReportSupplierTopRequest();
            request.setQueryBu(QueryReportBuTypeEnum.hotel);
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map<String, String> extData = new HashMap<>();
            extData.put("dim", dimEn(baseCondition.getLang(), "city_name", QueryReportBuTypeEnum.hotel));
            // 需要商旅数据
            extData.put("needCorp", "T");
            request.setExtData(extData);
            request.setTopLimit(5);

            OnlineReportSupplierTopResponse response1 = corpOnlineReportPlatformService.querySupplierTop(request);
            if (response1 != null && response1.getResponseCode() == 20000) {
                SupplierHtlTopInfo supplierHtlTopInfo = Optional.ofNullable(response1.getHtlTopInfo()).orElse(new SupplierHtlTopInfo());
                List<OnlineReportSupplierHtlTop> list = Optional.ofNullable(supplierHtlTopInfo.getTopList()).orElse(new ArrayList<>());
                if (CollectionUtils.isNotEmpty(list)) {
                    for (int j = 0; j < list.size(); j++) {
                        OnlineReportSupplierHtlTop top = Optional.ofNullable(list.get(j)).orElse(new OnlineReportSupplierHtlTop());
                        setHotelCityRow(sheet.createRow(HOTEL_CITY_START_ROW + j), top);
                    }
                    OnlineReportSupplierHtlTop other = Optional.ofNullable(supplierHtlTopInfo.getOtherInfo()).orElse(new OnlineReportSupplierHtlTop());
                    OnlineReportSupplierHtlTop sum = Optional.ofNullable(supplierHtlTopInfo.getSumInfo()).orElse(new OnlineReportSupplierHtlTop());
                    setHotelCityRow(sheet.createRow(HOTEL_CITY_START_ROW + 5), other);
                    setHotelCityRow(sheet.createRow(HOTEL_CITY_START_ROW + 6), sum);

                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillHotelCity", e);
        }
    }

    */
/**
     * 设置酒店-top5酒店城市分析
     *
     * @param row
     * @param entity
     *//*

    private void setHotelCityRow(Row row, OnlineReportSupplierHtlTop entity) {
        row.createCell(0).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getSumAmount()).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getSumQuantity()));
        // 协议酒店-商旅参考均价
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getSumAmountTa()).doubleValue());
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(MapperUtils.convertDigitToZero(entity.getSumQuantityTa()).doubleValue());
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.getCell(5).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceTa()).doubleValue());
        row.createCell(6).setCellType(CellType.NUMERIC);
        row.getCell(6).setCellValue(MapperUtils.convertDigitToZero(entity.getCorpAvgPriceTa()).doubleValue());
        // 会员酒店-商旅参考均价
        row.createCell(7).setCellType(CellType.NUMERIC);
        row.getCell(7).setCellValue(MapperUtils.convertDigitToZero(entity.getSumAmountNta()).doubleValue());
        row.createCell(8).setCellType(CellType.NUMERIC);
        row.getCell(8).setCellValue(MapperUtils.convertDigitToZero(entity.getSumQuantityNta()).doubleValue());
        row.createCell(9).setCellType(CellType.NUMERIC);
        row.getCell(9).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPriceNta()).doubleValue());
        row.createCell(10).setCellType(CellType.NUMERIC);
        row.getCell(10).setCellValue(MapperUtils.convertDigitToZero(entity.getCorpAvgPriceNta()).doubleValue());
    }

    */
/**
     * 填充酒店-潜在节省分析
     *
     * @param sheet
     *//*

    private void fillHotelPSaveAnalysis(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            OnlineReportSaveGeneralRequest saveGeneralRequest = new OnlineReportSaveGeneralRequest();
            saveGeneralRequest.setQueryBu(QueryReportBuTypeEnum.hotel);
            saveGeneralRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            saveGeneralRequest.setLang(baseCondition.getLang());
            saveGeneralRequest.setProductType("dom");
            Map map = new HashMap();
            map.put("index", "GENERAL_SAVE_POTENTIAL");
            // 概览节省分布不需要total信息
            map.put("needTotal", "F");
            saveGeneralRequest.setExtData(map);
            OnlineReportSaveGeneralResponse saveGeneralResponse = corpOnlineReportPlatformService.querySaveGeneralInfo(saveGeneralRequest);
            if (saveGeneralResponse != null && saveGeneralResponse.getResponseCode() == 20000) {
                GeneralPotentialSaveInfo saveInfo = Optional.ofNullable(saveGeneralResponse.getPotentialSaveInfo()).orElse(new GeneralPotentialSaveInfo());
                Row row = sheet.createRow(HOTEL_PROPORTION_SAVE_START_ROW);
                row.createCell(0).setCellType(CellType.NUMERIC);
                row.getCell(0).setCellValue(MapperUtils.convertDigitToZero(saveInfo.getOverAmount()).doubleValue());
                row.createCell(1).setCellType(CellType.NUMERIC);
                row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(saveInfo.getRefundloss()).doubleValue());
            }

            OnlineReportPSaveProportionDetailRequest detailRequest = new OnlineReportPSaveProportionDetailRequest();
            detailRequest.setQueryBu(QueryReportBuTypeEnum.hotel);
            detailRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            detailRequest.setLang(baseCondition.getLang());
            OnlineReportPSaveProportionDetailResponse detailResponse = corpOnlineReportPlatformService.queryPSaveProportionDetail(detailRequest);
            if (detailResponse != null && detailResponse.getResponseCode() == 20000) {
                List<OnlineReportPotentionSaveProportionDetail> list = detailResponse.getProportionDetails();
                setHotelPSaveDetailRow(sheet.createRow(HOTEL_PROPORTION_SAVE_START_ROW + 1), list.size() > 0 ? list.get(0) : new OnlineReportPotentionSaveProportionDetail());
                setHotelPSaveDetailRow(sheet.createRow(HOTEL_PROPORTION_SAVE_START_ROW + 2), list.size() > 1 ? list.get(1) : new OnlineReportPotentionSaveProportionDetail());
            }

            OnlineReportPotentialSaveLowRclRequest lowRclRequest = new OnlineReportPotentialSaveLowRclRequest();
            lowRclRequest.setQueryBu(QueryReportBuTypeEnum.hotel);
            lowRclRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            lowRclRequest.setLang(baseCondition.getLang());
            OnlineReportPotentialSaveLowRcResponse lowRcResponse = corpOnlineReportPlatformService.queryPotentialSaveLowRcDetail(lowRclRequest);
            if (lowRcResponse != null && lowRcResponse.getResponseCode() == 20000) {
                if (CollectionUtils.isNotEmpty(lowRcResponse.getLowRcList())) {
                    List<PotentialSaveLowRcInfo> lowRcInfos = lowRcResponse.getLowRcList();
                    for (int i = 0; i < lowRcInfos.size() - 1; i++) {
                        PotentialSaveLowRcInfo potentialSaveLowRcInfo = lowRcInfos.get(i);
                        Row row1 = sheet.createRow(HOTEL_PROPORTION_SAVE_START_ROW + 4 + i);
                        setHotelLowRcDetailRow(row1, potentialSaveLowRcInfo);
                    }
                    Row row1 = sheet.createRow(HOTEL_PROPORTION_SAVE_START_ROW + 4 + 20);
                    setHotelLowRcDetailRow(row1, lowRcInfos.get(lowRcInfos.size() - 1));
                }
            }

        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillHotelPSaveAnalysis", e);
        }
    }

    */
/**
     * 设置机票-损失明细-国内损失明细
     *
     * @param row
     * @param entity
     *//*

    private void setHotelLowRcDetailRow(Row row, PotentialSaveLowRcInfo entity) {
        row.createCell(0).setCellValue(MapperUtils.trim(entity.getLowRcCode()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getRcTimes()));
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(DigitBaseUtils.divide(entity.getRcPercent(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getOverAmount()).doubleValue());
        row.createCell(4).setCellValue(MapperUtils.trim(entity.getLowRcDesc()));
        row.createCell(5).setCellValue("-");
    }

    */
/**
     * 设置酒店-节省分布详情
     *
     * @param row
     * @param entity
     *//*

    private void setHotelPSaveDetailRow(Row row, OnlineReportPotentionSaveProportionDetail entity) {
        row.createCell(0).setCellType(CellType.NUMERIC);
        row.getCell(0).setCellValue(DigitBaseUtils.divide(entity.getTimesRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(DigitBaseUtils.divide(entity.getIndustryTimesRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(DigitBaseUtils.divide(entity.getCorpTimesRate(), new BigDecimal(100), GlobalConst.FOUR_DIGIT_NUM).doubleValue());
        row.createCell(3).setCellValue(MapperUtils.trim(entity.getType()));
    }

    */
/**
     * 填充火车-火车KPI月度累计
     *
     * @param sheet
     *//*

    private void fillTrainKpi(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            List<String> titleList = null;
            //退票比例
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), TRAIN_KPI_START_ROW, "TRAIN_REFUND_PERCENT_TREND", false, baseCondition.getDateDimension(), StringUtils.EMPTY);
            //改签比例
            titleList = metricPercent(sheet, baseCondition.getBaseQueryCondition(), TRAIN_KPI_START_ROW + 4, "TRAIN_REBOOK_PERCENT_TREND", false, baseCondition.getDateDimension(), StringUtils.EMPTY);
            Row titleRow = sheet.createRow(FLIGHT_KPI_START_ROW - 1);
            // 设置月份
            if (CollectionUtils.isNotEmpty(titleList)) {
                for (int i = 0; i < titleList.size(); i++) {
                    titleRow.createCell(i).setCellValue(titleList.get(i));
                }
            }

        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillTrainKpi", e);
        }

    }

    */
/**
     * 填充用车月度明细
     *
     * @param sheet
     *//*

    public void fillTrainMonthDetail(Sheet sheet, BaseQueryConditionBO baseCondition) {
        //消费趋势
        OnlineReportTrendRequest request = new OnlineReportTrendRequest();
        request.setQueryBu(QueryReportBuTypeEnum.train);
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setQueryType(QueryReportTypeionEnum.amount);
        request.setDateDimension(getDateDimesion(baseCondition.getDateDimension()));
        request.setAggType(QueryReportAggTypeEnum.current);
        request.setExtData(new HashMap<String, String>() {{
            put("yoyDuration", "12");
        }});
        try {
            OnlineReportTrendResponse orderTypeResponseType = corpOnlineReportPlatformService.queryReportTrend(request);
            if (orderTypeResponseType != null && orderTypeResponseType.getResponseCode() == 20000) {
                Row cMonthRow = sheet.createRow(TRAIN_MONTH_DETAIL_START_ROW);
                Row cMoneyRow = sheet.createRow(TRAIN_MONTH_DETAIL_START_ROW + 1);
                Row cTicketRow = sheet.createRow(TRAIN_MONTH_DETAIL_START_ROW + 2);

                List<OnlineReportTrendPoint> onlineReportTrendPoints = orderTypeResponseType.getData().getData();
                for (int i = 0; onlineReportTrendPoints != null && i < onlineReportTrendPoints.size(); i++) {
                    OnlineReportTrendPoint detail = onlineReportTrendPoints.get(i);
                    cMonthRow.createCell(i).setCellValue(detail.getAxis());
                    cMoneyRow.createCell(i).setCellType(CellType.NUMERIC);
                    cMoneyRow.getCell(i).setCellValue(detail.getData().getOrDefault("amount", BigDecimal.ZERO).doubleValue());
                    cTicketRow.createCell(i).setCellValue(detail.getData().getOrDefault("quantity", BigDecimal.ZERO).intValue());
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillTrainMonthDetail", e);
        }
    }

    */
/**
     * 填充火车部门消费
     *
     * @param sheet
     *//*

    public void fillTrainDeptDetail(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            OnlineReportTopDeptConsumeDetailRequest request = new OnlineReportTopDeptConsumeDetailRequest();
            request.setQueryBu(QueryReportBuTypeEnum.train);
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            AnalysisObjectEnum analysisObjectEnum = StringUtils.isEmpty(baseCondition.getAnalysisObject()) ?
                    AnalysisObjectEnum.CORP : AnalysisObjectEnum.valueOf(baseCondition.getAnalysisObject().toUpperCase());
            request.setAnalysisObjectEnum(analysisObjectEnum);
            OnlineReportTopDeptConsumeDetailResponse responseType1 = corpOnlineReportPlatformService.queryTopDeptConsumeDetail(request);
            if (responseType1 != null && responseType1.getResponseCode() == 20000) {
                TrainTopDeptConsumeInfo detailAmountDto = Optional.ofNullable(responseType1.getTrainTopConsumeInfo()).orElse(new TrainTopDeptConsumeInfo());
                List<OnlineReportTrainTopDeptConsume> list = Optional.ofNullable(detailAmountDto.getTopList()).orElse(new ArrayList<>());
                for (int i = 0; i < list.size(); i++) {
                    setTrainDeptRow(sheet.createRow(TRAIN_DEPT_START_ROW + i), Optional.ofNullable(list.get(i)).orElse(new OnlineReportTrainTopDeptConsume()));
                }
                //设置国内总计
                OnlineReportTrainTopDeptConsume nDetail = Optional.ofNullable(detailAmountDto.getSumConsume()).orElse(new OnlineReportTrainTopDeptConsume());
                setTrainDeptRow(sheet.createRow(TRAIN_DEPT_START_ROW + TOP_ONE_HUNDRED + 1), nDetail);
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillTrainDeptDetail", e);
        }
    }

    */
/**
     * 设置火车-部门火车票消费
     *
     * @param row
     * @param entity
     *//*

    private void setTrainDeptRow(Row row, OnlineReportTrainTopDeptConsume entity) {
        row.createCell(0).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmount()).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalQuantity()).doubleValue());
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getAvgPrice()).doubleValue());
    }

    */
/**
     * 填充火车-top5行程
     *
     * @param sheet
     *//*

    private void fillTrainLineCity(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportSupplierTopRequest request = new OnlineReportSupplierTopRequest();
            request.setQueryBu(QueryReportBuTypeEnum.train);
            request.setLang(baseCondition.getLang());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map<String, String> extData = new HashMap<>();
            extData.put("dim", dimEn(baseCondition.getLang(), "line_city", QueryReportBuTypeEnum.train));
            request.setExtData(extData);
            request.setTopLimit(5);
            OnlineReportSupplierTopResponse domResponseType = corpOnlineReportPlatformService.querySupplierTop(request);
            if (domResponseType != null && domResponseType.getResponseCode() == 20000) {
                SupplierTrainTopInfo supplierTrainTopInfo = Optional.ofNullable(domResponseType.getTrainTopInfo()).orElse(new SupplierTrainTopInfo());
                List<OnlineReportSupplierTrainTop> list = Optional.ofNullable(supplierTrainTopInfo.getTopList()).orElse(new ArrayList<>());
                if (CollectionUtils.isNotEmpty(list)) {
                    for (int j = 0; j < list.size(); j++) {
                        OnlineReportSupplierTrainTop trainTop = Optional.ofNullable(list.get(j)).orElse(new OnlineReportSupplierTrainTop());
                        setTrainLineCityRow(sheet.createRow(TRAIN_LINE_CITY_START_ROW + j), trainTop);
                    }
                    OnlineReportSupplierTrainTop other = Optional.ofNullable(supplierTrainTopInfo.getOtherInfo()).orElse(new OnlineReportSupplierTrainTop());
                    setTrainLineCityRow(sheet.createRow(TRAIN_LINE_CITY_START_ROW + 5), other);
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillTrainLineCity", e);
        }
    }

    private void setTrainLineCityRow(Row row, OnlineReportSupplierTrainTop entity) {
        row.createCell(0).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.createCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getSumAmount()).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.createCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getSumQuantity()));
    }

    */
/**
     * 填充火车-火车预订方式
     *
     * @param sheet
     *//*

    private void fillTrainBookType(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportBookTypeRequest bookTypeRequest = new OnlineReportBookTypeRequest();
            bookTypeRequest.setQueryBu(QueryReportBuTypeEnum.train);
            bookTypeRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            OnlineReportBookTypeResponse responseType1 = corpOnlineReportPlatformService.queryBookTypeAnalysis(bookTypeRequest);
            if (responseType1 != null && responseType1.getResponseCode() == 20000) {
                List<BookTypeInfo> domFlightBookType = responseType1.getBookTypeList();
                Row domRow = sheet.createRow(TRAIN_BOOK_TYPE_START_ROW);
                setBookTypeRow(domRow, domFlightBookType);
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillTrainBookType", e);
        }
    }

    */
/**
     * 填充用车月度明细
     *
     * @param sheet
     *//*

    public void fillCarMonthDetail(Sheet sheet, BaseQueryConditionBO baseCondition) {
        //消费趋势
        OnlineReportTrendRequest request = new OnlineReportTrendRequest();
        request.setQueryBu(QueryReportBuTypeEnum.car);
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setQueryType(QueryReportTypeionEnum.amount);
        request.setDateDimension(getDateDimesion(baseCondition.getDateDimension()));
        request.setAggType(QueryReportAggTypeEnum.current);
        request.setExtData(new HashMap<String, String>() {{
            put("yoyDuration", "12");
        }});
        try {
            OnlineReportTrendResponse orderTypeResponseType = corpOnlineReportPlatformService.queryReportTrend(request);
            if (orderTypeResponseType != null && orderTypeResponseType.getResponseCode() == 20000) {
                Row cMonthRow = sheet.createRow(CAR_MONTH_DETAIL_START_ROW);
                Row cMoneyRow = sheet.createRow(CAR_MONTH_DETAIL_START_ROW + 1);
                Row cTicketRow = sheet.createRow(CAR_MONTH_DETAIL_START_ROW + 2);

                List<OnlineReportTrendPoint> onlineReportTrendPoints = orderTypeResponseType.getData().getData();
                for (int i = 0; onlineReportTrendPoints != null && i < onlineReportTrendPoints.size(); i++) {
                    OnlineReportTrendPoint detail = onlineReportTrendPoints.get(i);
                    cMonthRow.createCell(i).setCellValue(detail.getAxis());
                    cMoneyRow.createCell(i).setCellType(CellType.NUMERIC);
                    cMoneyRow.getCell(i).setCellValue(detail.getData().getOrDefault("amount", BigDecimal.ZERO).doubleValue());
                    cTicketRow.createCell(i).setCellValue(detail.getData().getOrDefault("quantity", BigDecimal.ZERO).intValue());
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillCarMonthDetail", e);
        }
    }

    */
/**
     * 填充用车部门消费
     *
     * @param sheet
     *//*

    public void fillCarDeptDetail(Sheet sheet, BaseQueryConditionBO baseCondition) {
        try {
            OnlineReportTopDeptConsumeDetailRequest request = new OnlineReportTopDeptConsumeDetailRequest();
            request.setQueryBu(QueryReportBuTypeEnum.car);
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            AnalysisObjectEnum analysisObjectEnum = StringUtils.isEmpty(baseCondition.getAnalysisObject()) ?
                    AnalysisObjectEnum.CORP : AnalysisObjectEnum.valueOf(baseCondition.getAnalysisObject().toUpperCase());
            request.setAnalysisObjectEnum(analysisObjectEnum);
            OnlineReportTopDeptConsumeDetailResponse responseType1 = corpOnlineReportPlatformService.queryTopDeptConsumeDetail(request);
            if (responseType1 != null && responseType1.getResponseCode() == 20000) {
                CarTopDeptConsumeInfo detailAmountDto = Optional.ofNullable(responseType1.getCarTopConsumeInfo()).orElse(new CarTopDeptConsumeInfo());
                List<OnlineReportCarTopDeptConsume> list = Optional.ofNullable(detailAmountDto.getTopList()).orElse(new ArrayList<>());
                for (int i = 0; i < list.size(); i++) {
                    setCarDeptRow(sheet.createRow(CAR_DEPT_START_ROW + i), Optional.ofNullable(list.get(i)).orElse(new OnlineReportCarTopDeptConsume()));
                }
                //设置国内总计
                OnlineReportCarTopDeptConsume nDetail = Optional.ofNullable(detailAmountDto.getSumConsume()).orElse(new OnlineReportCarTopDeptConsume());
                setCarDeptRow(sheet.createRow(CAR_DEPT_START_ROW + TOP_ONE_HUNDRED + 1), nDetail);
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillCarDeptDetail", e);
        }
    }

    private void setCarDeptRow(Row row, OnlineReportCarTopDeptConsume entity) {
        row.createCell(0).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.getCell(1).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmount()).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmountAirportpickDom()).doubleValue());
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.getCell(3).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmountAirportpickInter()).doubleValue());
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.getCell(4).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmountRent()).doubleValue());
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.getCell(5).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmountCharter()).doubleValue());
        row.createCell(6).setCellType(CellType.NUMERIC);
        row.getCell(6).setCellValue(MapperUtils.convertDigitToZero(entity.getTotalAmountTax()).doubleValue());
    }

    */
/**
     * 填充火车-TOP 5用车城市
     *
     * @param sheet
     *//*

    private void fillCarCity(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            OnlineReportSupplierTopRequest request = new OnlineReportSupplierTopRequest();
            request.setQueryBu(QueryReportBuTypeEnum.car);
            request.setLang(baseCondition.getLang());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map<String, String> extData = new HashMap<>();
            extData.put("dim", dimEn(baseCondition.getLang(), "departure_city_name", QueryReportBuTypeEnum.car));
            request.setExtData(extData);
            request.setTopLimit(5);
            OnlineReportSupplierTopResponse domResponseType = corpOnlineReportPlatformService.querySupplierTop(request);
            if (domResponseType != null && domResponseType.getResponseCode() == 20000) {
                SupplierCarTopInfo supplierCarTopInfo = Optional.ofNullable(domResponseType.getCarTopInfo()).orElse(new SupplierCarTopInfo());
                List<OnlineReportSupplierCarTop> list = Optional.ofNullable(supplierCarTopInfo.getTopList()).orElse(new ArrayList<>());
                if (CollectionUtils.isNotEmpty(list)) {
                    for (int j = 0; j < list.size(); j++) {
                        OnlineReportSupplierCarTop carTop = Optional.ofNullable(list.get(j)).orElse(new OnlineReportSupplierCarTop());
                        setCarCityRow(sheet.createRow(CAR_CITY_START_ROW + j), carTop);
                    }
                    OnlineReportSupplierCarTop other = Optional.ofNullable(supplierCarTopInfo.getOtherInfo()).orElse(new OnlineReportSupplierCarTop());
                    setCarCityRow(sheet.createRow(CAR_CITY_START_ROW + 5), other);
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillTrainLineCity", e);
        }
    }

    private void setCarCityRow(Row row, OnlineReportSupplierCarTop carTop) {
        row.createCell(0).setCellValue(MapperUtils.trim(carTop.getDim()));
        row.createCell(1).setCellType(CellType.NUMERIC);
        row.createCell(1).setCellValue(MapperUtils.convertDigitToZero(carTop.getSumAmount()).doubleValue());
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.createCell(2).setCellValue(MapperUtils.convertDigitToZero(carTop.getSumAmountAirportpickDom()).doubleValue());
        row.createCell(3).setCellType(CellType.NUMERIC);
        row.createCell(3).setCellValue(MapperUtils.convertDigitToZero(carTop.getSumAmountAirportpickInter()).doubleValue());
        row.createCell(4).setCellType(CellType.NUMERIC);
        row.createCell(4).setCellValue(MapperUtils.convertDigitToZero(carTop.getSumAmountRent()).doubleValue());
        row.createCell(5).setCellType(CellType.NUMERIC);
        row.createCell(5).setCellValue(MapperUtils.convertDigitToZero(carTop.getSumAmountCharter()).doubleValue());
        row.createCell(6).setCellType(CellType.NUMERIC);
        row.createCell(6).setCellValue(MapperUtils.convertDigitToZero(carTop.getSumAmountTax()).doubleValue());
    }

    */
/**
     * 填充火车-TOP 5车型
     *
     * @param sheet
     *//*

    private void fillCarVehicle(Sheet sheet, BaseQueryConditionBO baseCondition) {

        try {
            // 用车类型, airportpick接送机(order_type IN (1,2)),Charter包车(order_type = 3),rent租车(order_type = 4),tax打车(order_type = 6)

            List<String> orderTypes = Arrays.asList("airportpickDom", "airportpickInter", "Charter", "rent", "tax");
            OnlineReportSupplierTopRequest request = new OnlineReportSupplierTopRequest();
            request.setQueryBu(QueryReportBuTypeEnum.car);
            request.setLang(baseCondition.getLang());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map<String, String> extData = new HashMap<>();
            extData.put("dim", dimEn(baseCondition.getLang(), "vehicle_name", QueryReportBuTypeEnum.car));
            request.setExtData(extData);
            request.setTopLimit(5);
            for (String orderType : orderTypes) {
                extData.put("carOrderType", orderType);
                OnlineReportSupplierTopResponse domResponseType = corpOnlineReportPlatformService.querySupplierTop(request);
                if (domResponseType != null && domResponseType.getResponseCode() == 20000) {
                    SupplierCarTopInfo supplierCarTopInfo = Optional.ofNullable(domResponseType.getCarTopInfo()).orElse(new SupplierCarTopInfo());
                    List<OnlineReportSupplierCarTop> list = Optional.ofNullable(supplierCarTopInfo.getTopList()).orElse(new ArrayList<>());
                    for (int j = 0; j < list.size(); j++) {
                        OnlineReportSupplierCarTop carTop = Optional.ofNullable(list.get(j)).orElse(new OnlineReportSupplierCarTop());
                        if (StringUtils.equalsIgnoreCase(orderType, "airportpickDom")) {
                            setCarTypeRow(sheet.createRow(CAR_TYPE_DOM_START_ROW + j), carTop);
                        } else if (StringUtils.equalsIgnoreCase(orderType, "airportpickInter")) {
                            setCarTypeRow(sheet.createRow(CAR_TYPE_INT_START_ROW + j), carTop);
                        } else if (StringUtils.equalsIgnoreCase(orderType, "rent")) {
                            setCarTypeRow(sheet.createRow(CAR_TYPE_RENT_START_ROW + j), carTop);
                        } else if (StringUtils.equalsIgnoreCase(orderType, "Charter")) {
                            setCarTypeRow(sheet.createRow(CAR_TYPE_CHARTERED_START_ROW + j), carTop);
                        } else if (StringUtils.equalsIgnoreCase(orderType, "tax")) {
                            setCarTypeRow(sheet.createRow(CAR_TYPE_ONCALL_START_ROW + j), carTop);
                        }
                    }
                    OnlineReportSupplierCarTop other = Optional.ofNullable(supplierCarTopInfo.getOtherInfo()).orElse(new OnlineReportSupplierCarTop());
                    if (StringUtils.equalsIgnoreCase(orderType, "airportpickDom")) {
                        setCarTypeRow(sheet.createRow(CAR_TYPE_DOM_START_ROW + 5), other);
                    } else if (StringUtils.equalsIgnoreCase(orderType, "airportpickInter")) {
                        setCarTypeRow(sheet.createRow(CAR_TYPE_INT_START_ROW + 5), other);
                    } else if (StringUtils.equalsIgnoreCase(orderType, "rent")) {
                        setCarTypeRow(sheet.createRow(CAR_TYPE_RENT_START_ROW + 5), other);
                    } else if (StringUtils.equalsIgnoreCase(orderType, "Charter")) {
                        setCarTypeRow(sheet.createRow(CAR_TYPE_CHARTERED_START_ROW + 5), other);
                    } else if (StringUtils.equalsIgnoreCase(orderType, "tax")) {
                        setCarTypeRow(sheet.createRow(CAR_TYPE_ONCALL_START_ROW + 5), other);
                    }
                }
            }
        } catch (Exception e) {
            log.error("OneKeyExportReportService.fillCarVehicle", e);
        }
    }

    private void setCarTypeRow(Row row, OnlineReportSupplierCarTop entity) {
        row.createCell(1).setCellValue(MapperUtils.trim(entity.getDim()));
        row.createCell(2).setCellType(CellType.NUMERIC);
        row.getCell(2).setCellValue(MapperUtils.convertDigitToZero(entity.getSumAmount()).doubleValue());
    }

    private void fillContent(Sheet sheet, BaseQueryConditionBO baseCondition) {
        Row row = sheet.createRow(0);
        row.createCell(5).setCellValue(getDateRange(baseCondition));
        AnalysisObjectEnum analysisObjectEnum = StringUtils.isEmpty(baseCondition.getAnalysisObject()) ?
                AnalysisObjectEnum.CORP : AnalysisObjectEnum.valueOf(baseCondition.getAnalysisObject().toUpperCase());
        row.createCell(6).setCellValue(getAnalysisObjectDes(analysisObjectEnum, baseCondition.getLang()));
    }

    private String getDateRange(BaseQueryConditionBO baseCondition) {
        BaseQueryCondition baseQueryCondition = baseCondition.getBaseQueryCondition();
        return String.format("%s-%s", MapperUtils.trim(baseQueryCondition.getStartTime()), MapperUtils.trim(baseQueryCondition.getEndTime()));
    }

    private String getAnalysisObjectDes(AnalysisObjectEnum analysisObjectEnum, String lang) {
        String analysisObjectDes = StringUtils.EMPTY;
        switch (analysisObjectEnum) {
            case CORP:
                analysisObjectDes = SharkUtils.get("公司", lang);
                break;
            case ACCOUNT:
                analysisObjectDes = SharkUtils.get("主账户名称", lang);
                break;
            case ACCOUNTCODE:
                analysisObjectDes = SharkUtils.get("主账户代号", lang);
                break;
            case DEPT1:
                analysisObjectDes = SharkUtils.get("Exceltopname.depone", lang);
                break;
            case DEPT2:
                analysisObjectDes = SharkUtils.get("Exceltopname.deptwo", lang);
                break;
            case DEPT3:
                analysisObjectDes = SharkUtils.get("Exceltopname.depthree", lang);
                break;
            case DEPT4:
                analysisObjectDes = SharkUtils.get("Exceltopname.depfour", lang);
                break;
            case DEPT5:
                analysisObjectDes = SharkUtils.get("Exceltopname.depfive", lang);
                break;
            case DEPT6:
                analysisObjectDes = SharkUtils.get("Exceltopname.depsix", lang);
                break;
            case DEPT7:
                analysisObjectDes = SharkUtils.get("Exceltopname.depseven", lang);
                break;
            case DEPT8:
                analysisObjectDes = SharkUtils.get("Exceltopname.depeight", lang);
                break;
            case DEPT9:
                analysisObjectDes = SharkUtils.get("Exceltopname.depnine", lang);
                break;
            case DEPT10:
                analysisObjectDes = SharkUtils.get("Exceltopname.depten", lang);
                break;
            case COSTCENTER1:
                analysisObjectDes = SharkUtils.get("Exceltopname.costcenterone", lang);
                break;
            case COSTCENTER2:
                analysisObjectDes = SharkUtils.get("Exceltopname.costcentertwo", lang);
                break;
            case COSTCENTER3:
                analysisObjectDes = SharkUtils.get("Exceltopname.costcenterthree", lang);
                break;
            case COSTCENTER4:
                analysisObjectDes = SharkUtils.get("Exceltopname.costcenterfour", lang);
                break;
            case COSTCENTER5:
                analysisObjectDes = SharkUtils.get("Exceltopname.costcenterfive", lang);
                break;
            case COSTCENTER6:
                analysisObjectDes = SharkUtils.get("Exceltopname.costcentersix", lang);
                break;
            case UID:
                analysisObjectDes = SharkUtils.get("卡号", lang);
                break;
        }
        return analysisObjectDes;
    }

    */
/**
     * 默认按月维度
     *
     * @param dateDimesion
     * @return
     *//*

    private QueryReportAggDateDimensionEnum getDateDimesion(String dateDimesion) {
        return QueryReportAggDateDimensionEnum.month;
    }
}
*/
