package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.hive.impl;

import com.corpgovernment.resource.schedule.domain.onlinereport.common.BaseDataUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.ChineseLanguageConfig;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DateUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.FlightEntity;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveFilter;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.SrDao;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.FlightOrderDetailService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.SrSwitchConfig;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.hive.AbstractOrderDetailHiveService;
import com.google.common.collect.ImmutableMap;
import onlinereport.enums.AgeTypeEnums;
import onlinereport.enums.FlightTicketStatusEnum;
import onlinereport.enums.OrderTypeEnum;
import onlinereport.enums.YesOrNotEnum;
import onlinereport.enums.reportlib.*;
import onlinereport.enums.reportlib.uid.FlightUidOrderDetailEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst.FOUR_DIGIT_NUM;


/**
 * Auther:abguo
 * Date:2019/8/21
 * Description:
 */
@Service
public class FlightOrderHiveService extends AbstractOrderDetailHiveService {

    @Autowired
    private HiveDao hiveDao;

    @Autowired
    private SrSwitchConfig srSwitchConfig;

    @Autowired
    private SrDao srDao;

    @Override
    public OrderDetailEnumerable[] getEnum(String reportType) {
        if (StringUtils.equalsIgnoreCase(reportType, "F_REFUND")) {
            return FlightRefundOrderDetailEnum.values();
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_REBOOK")) {
            return FlightChangeOrderDetailEnum.values();
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_UNUSE")) {
            return UnUseFlightOrderDetailEnum.values();
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_UID_DETAIL")) {
            return FlightUidOrderDetailEnum.values();
        } else {
            return FlightOrderDetailEnum.values();
        }
    }

    /**
     * 机票相关订单明细
     *
     * @param hiveFilter
     * @param lang
     * @param reportType
     * @return
     */
    public List<Map<String, Object>> queryOrderDetail(HiveFilter hiveFilter, String lang, String reportType) {
        List<Map<String, Object>> result = new ArrayList<>();

        List<FlightEntity> flightEntityList = null;
        if (srSwitchConfig.isOpenSrSwitch(hiveFilter.getUid(), "flight")) {
            flightEntityList = srDao.searchFlightOrderdetail(hiveFilter);
        } else {
            flightEntityList = hiveDao.searchFlightOrderdetail(hiveFilter);
        }
        if (CollectionUtils.isEmpty(flightEntityList)) {
            return result;
        }
        if (StringUtils.equalsIgnoreCase(reportType, "F_REFUND")) {
            return convertFlightRefundOrderMapData(flightEntityList, lang);
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_REBOOK")) {
            return convertFlightRebookOrderMapData(flightEntityList, lang);
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_UNUSE")) {
            return convertFlightUnuseOrderMapData(flightEntityList, lang);
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_UID_DETAIL")) {
            return convertFlightUidOrderMapData(flightEntityList, lang);
        } else {
            return convertFlightOrderMapData(flightEntityList, lang);
        }
    }

    /**
     * 订单明细
     *
     * @param flightEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> convertFlightUidOrderMapData(List<FlightEntity> flightEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        Map yesOrNotMap = BaseDataUtils.getYesOrNoMap(lang);
        for (FlightEntity order : flightEntityList) {
            Map<String, Object> map = new HashMap<>();
            map.put(FlightUidOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrder_id()));
            map.put(FlightUidOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrder_status()));
            map.put(FlightUidOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
            map.put(FlightUidOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorp_corporation()));
            map.put(FlightUidOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
            map.put(FlightUidOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getAccount_id()));
            map.put(FlightUidOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(FlightUidOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
            map.put(FlightUidOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRank_name()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCost_center1()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCost_center2()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCost_center3()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCost_center4()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCost_center5()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCost_center6()));
            map.put(FlightUidOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(FlightUidOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(FlightUidOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(FlightUidOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
            map.put(FlightUidOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
            map.put(FlightUidOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
            map.put(FlightUidOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
            map.put(FlightUidOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
            map.put(FlightUidOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
            map.put(FlightUidOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(FlightUidOrderDetailEnum.PRINTTICKETTIME.getCode(), MapperUtils.trim(order.getPrint_ticket_time()));
            map.put(FlightUidOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            map.put(FlightUidOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));
            map.put(FlightUidOrderDetailEnum.PRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrice()));
            map.put(FlightUidOrderDetailEnum.PRINTPRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrint_price()));
            map.put(FlightUidOrderDetailEnum.PRICERATE.getCode(), DigitBaseUtils.formatDigit(order.getPrice_rate()));
            map.put(FlightUidOrderDetailEnum.AGREEMENTTYPENAME.getCode(), MapperUtils.trim(order.getAgreement_type_name()));
            map.put(FlightUidOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassenger_name()));
            map.put(FlightUidOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlight_no()));
            map.put(FlightUidOrderDetailEnum.SUBCLASS.getCode(), MapperUtils.trim(order.getSub_class()));
            map.put(FlightUidOrderDetailEnum.FLIGHTCITY.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getFlight_city()), MapperUtils.trim(order.getFlight_city_en())));
            map.put(FlightUidOrderDetailEnum.DEPARTUREPORTNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDeparture_port_name()), MapperUtils.trim(order.getDeparture_port_name_en())));
            map.put(FlightUidOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDeparture_city_name()), MapperUtils.trim(order.getDeparture_city_name_en())));
            map.put(FlightUidOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoff_time()));
            map.put(FlightUidOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getArrival_city_name()), MapperUtils.trim(order.getArrival_city_name_en())));
            map.put(FlightUidOrderDetailEnum.ARRIVALPORTNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getArrival_port_name()), MapperUtils.trim(order.getArrival_port_name_en())));
            map.put(FlightUidOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrival_date_time()));
            map.put(FlightUidOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccount_code()));
            map.put(FlightUidOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
            map.put(FlightUidOrderDetailEnum.ORDERTKT.getCode(), MapperUtils.convertDigitToZero(order.getOrdertkt()));
            map.put(FlightUidOrderDetailEnum.REFUNDTKT.getCode(), MapperUtils.convertDigitToZero(order.getRefundtkt()));
            map.put(FlightUidOrderDetailEnum.ISREFUND.getCode(), MapperUtils.convertTorF(order.getIs_refund(), yesOrNotMap));
            map.put(FlightUidOrderDetailEnum.ISREBOOK.getCode(), MapperUtils.convertTorF(order.getIs_rebook(), yesOrNotMap));
            map.put(FlightUidOrderDetailEnum.CHANGETAKEOFFTIME.getCode(), MapperUtils.trim(order.getChange_takeoff_time()));
            map.put(FlightUidOrderDetailEnum.CHANGEARRIVALTIME.getCode(), MapperUtils.trim(order.getChange_arrival_datetime()));
            map.put(FlightUidOrderDetailEnum.FLIGHTCLASS.getCode(), FlightOrderDetailService.convertFlightClass(MapperUtils.trim(order.getFlight_class())));
            map.put(FlightUidOrderDetailEnum.AIRLINECN.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getAirline_cn_name()), MapperUtils.trim(order.getAirline_en_name())));
            result.add(map);
        }
        return result;
    }

    /**
     * 订单明细
     *
     * @param flightEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> convertFlightOrderMapData(List<FlightEntity> flightEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        Map yesOrNotMap = BaseDataUtils.getYesOrNoMap(lang);
        Map flightTicketStatusMap = initFlightTicketStatus(lang);
        Map type = init();
        for (FlightEntity order : flightEntityList) {
            Map<String, Object> map = new HashMap<>();
//            map.put(FlightOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrder_id()));
//            map.put(FlightOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrder_status()));
//            map.put(FlightOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
//            map.put(FlightOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorp_corporation()));
//            map.put(FlightOrderDetailEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorp_name()));
//            map.put(FlightOrderDetailEnum.COMPANYGROUP.getCode(), MapperUtils.trim(order.getCompanygroup()));
//            map.put(FlightOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
//            map.put(FlightOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getAccount_id()));
//            map.put(FlightOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccount_code()));
//            map.put(FlightOrderDetailEnum.ACCOUNTNAME.getCode(), MapperUtils.trim(order.getAccount_name()));
//            map.put(FlightOrderDetailEnum.SUBACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getSub_account_id()));
//            map.put(FlightOrderDetailEnum.SUBACCOUNTCODE.getCode(), MapperUtils.trim(order.getSub_account_code()));
//            map.put(FlightOrderDetailEnum.SUBACCOUNTNAME.getCode(), MapperUtils.trim(order.getSub_account_name()));
//            map.put(FlightOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
//            map.put(FlightOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
//            map.put(FlightOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
//            map.put(FlightOrderDetailEnum.WORKCITY.getCode(), MapperUtils.trim(order.getWork_city()));
//            map.put(FlightOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRank_name()));
//            map.put(FlightOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCost_center1()));
//            map.put(FlightOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCost_center2()));
//            map.put(FlightOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCost_center3()));
//            map.put(FlightOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCost_center4()));
//            map.put(FlightOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCost_center5()));
//            map.put(FlightOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCost_center6()));
//            map.put(FlightOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
//            map.put(FlightOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
//            map.put(FlightOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
//            map.put(FlightOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
//            map.put(FlightOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
//            map.put(FlightOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
//            map.put(FlightOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
//            map.put(FlightOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
//            map.put(FlightOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
//            map.put(FlightOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
//            map.put(FlightOrderDetailEnum.ISONLINE.getCode(), MapperUtils.convertBookTypeSingle(order.getIs_online()));
//            map.put(FlightOrderDetailEnum.FEETYPE.getCode(), MapperUtils.trim(order.getFee_type()));
//            map.put(FlightOrderDetailEnum.PREPAYTYPE.getCode(), FlightOrderDetailService.convertPrepayType(MapperUtils.trim(order.getPrepay_type())));
//            map.put(FlightOrderDetailEnum.ACBPREPAYTYPE.getCode(), MapperUtils.trim(order.getAcb_prepay_type()));
////            map.put(FlightOrderDetailEnum.ISBOSS.getCode(), MapperUtils.trim(order.getBosstype()));
//            map.put(FlightOrderDetailEnum.JOURNEYNO.getCode(), MapperUtils.trim(order.getJourney_no()));
//            map.put(FlightOrderDetailEnum.TRIPID.getCode(), MapperUtils.convertDigitToDash(order.getTrip_id()));
//            map.put(FlightOrderDetailEnum.JOUNARYREASON.getCode(), MapperUtils.trim(order.getJourney_reason()));
//            map.put(FlightOrderDetailEnum.PROJECT.getCode(), MapperUtils.trim(order.getProject()));
//            map.put(FlightOrderDetailEnum.VERBALAUTHORIZE.getCode(), MapperUtils.convertTorF(order.getVerbal_authorize(), yesOrNotMap));
////            map.put(FlightOrderDetailEnum.CONFIRMPERSON.getCode(), MapperUtils.trim(order.getConfirm_person()));
////            map.put(FlightOrderDetailEnum.CONFIRMPTYPE.getCode(), MapperUtils.trim(order.getConfirm_type()));
////            map.put(FlightOrderDetailEnum.CONFIRMPERSON2.getCode(), MapperUtils.trim(order.getConfirm_person2()));
////            map.put(FlightOrderDetailEnum.CONFIRMPTYPE2.getCode(), MapperUtils.trim(order.getConfirm_type2()));
//            map.put(FlightOrderDetailEnum.GROUPMONTH.getCode(), MapperUtils.convertDigitToString(order.getGroup_month()));
//            map.put(FlightOrderDetailEnum.PRINTTICKETTIME.getCode(), MapperUtils.trim(order.getPrint_ticket_time()));
//            map.put(FlightOrderDetailEnum.PROVIDEBILLTYPE.getCode(), MapperUtils.trim(order.getProvide_bill_type()));
//            map.put(FlightOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
//            map.put(FlightOrderDetailEnum.FULLQUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getFullfaretkt()));
//            map.put(FlightOrderDetailEnum.ORDERTKT.getCode(), MapperUtils.convertDigitToZero(order.getOrdertkt()));
//            map.put(FlightOrderDetailEnum.REFUNDTKT.getCode(), MapperUtils.convertDigitToZero(order.getRefundtkt()));
//            map.put(FlightOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));
//            map.put(FlightOrderDetailEnum.PRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrice()));
//            map.put(FlightOrderDetailEnum.NETFARE.getCode(), DigitBaseUtils.formatDigit(order.getNetfare()));
//            map.put(FlightOrderDetailEnum.OILFEE.getCode(), DigitBaseUtils.formatDigit(order.getOil_fee()));
//            map.put(FlightOrderDetailEnum.TAX.getCode(), DigitBaseUtils.formatDigit(order.getTax()));
//            map.put(FlightOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getService_fee()));
//            map.put(FlightOrderDetailEnum.INSERANCEFEE.getCode(), DigitBaseUtils.formatDigit(order.getInsurance_fee()));
//            map.put(FlightOrderDetailEnum.BINDAMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getBind_amount()));
//            map.put(FlightOrderDetailEnum.CHANGEFEE.getCode(), DigitBaseUtils.formatDigit(order.getChange_fee()));
//            map.put(FlightOrderDetailEnum.REBOOKPRICEDIFFERENT.getCode(), DigitBaseUtils.formatDigit(order.getRebook_price_differential()));
//            map.put(FlightOrderDetailEnum.REBOOKSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebook_service_fee()));
//            map.put(FlightOrderDetailEnum.REFUNDFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefund_fee()));
//            map.put(FlightOrderDetailEnum.REFUNDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefund_service_fee()));
//            map.put(FlightOrderDetailEnum.SENDTICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getSend_ticket_fee()));
//            map.put(FlightOrderDetailEnum.TICKETBEHINDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getTicket_behind_service_fee()));
//            map.put(FlightOrderDetailEnum.REBOOKBEHINDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebook_behind_service_fee()));
//            map.put(FlightOrderDetailEnum.REFUNDTBEHINDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefund_behind_service_fee()));
//            map.put(FlightOrderDetailEnum.STDPRICE.getCode(), DigitBaseUtils.formatDigit(order.getStd_price()));
//            map.put(FlightOrderDetailEnum.PRINTPRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrint_price()));
//            map.put(FlightOrderDetailEnum.PRICERATE.getCode(), DigitBaseUtils.formatDigit(order.getPrice_rate()));
//            map.put(FlightOrderDetailEnum.CORPPRICEADJ.getCode(), DigitBaseUtils.formatDigit(order.getCorp_price_adj()));
//            map.put(FlightOrderDetailEnum.ISREFUND.getCode(), MapperUtils.convertTorF(order.getIs_refund(), yesOrNotMap));
//            map.put(FlightOrderDetailEnum.REFUNDREASONDESC.getCode(), MapperUtils.trim(order.getRefund_reson_desc()));
//            map.put(FlightOrderDetailEnum.REFUNDTYPE.getCode(), MapperUtils.trim(order.getFlightrefundtype()));
//            map.put(FlightOrderDetailEnum.REFUNDRTIME.getCode(), MapperUtils.trim(order.getRefund_time()));
//            map.put(FlightOrderDetailEnum.ISREBOOK.getCode(), MapperUtils.convertTorF(order.getIs_rebook(), yesOrNotMap));
//            map.put(FlightOrderDetailEnum.REBOOKREASONDESC.getCode(), MapperUtils.trim(order.getRebook_reson_desc()));
//            map.put(FlightOrderDetailEnum.REBOOKTYPE.getCode(), MapperUtils.trim(order.getFlightrebooktype()));
//            map.put(FlightOrderDetailEnum.REBOOKTIME.getCode(), MapperUtils.trim(order.getRebook_time()));
//            map.put(FlightOrderDetailEnum.ORIGINALORDERID.getCode(), MapperUtils.convertDigitToString(order.getOriginal_order_id()));
//            map.put(FlightOrderDetailEnum.CHANGEFLIGHTNO.getCode(), MapperUtils.trim(order.getChange_flight_no()));
//            map.put(FlightOrderDetailEnum.CHANGETAKEOFFTIME.getCode(), MapperUtils.trim(order.getChange_takeoff_time()));
//            map.put(FlightOrderDetailEnum.CHANGEARRIVALTIME.getCode(), MapperUtils.trim(order.getChange_arrival_datetime()));
//            map.put(FlightOrderDetailEnum.PRODUCTCATEGORY.getCode(), MapperUtils.trim(order.getProductcategory()));
//            map.put(FlightOrderDetailEnum.BFRETURN.getCode(), MapperUtils.convertTorF(order.getBf_return(), yesOrNotMap));
//            map.put(FlightOrderDetailEnum.CARBONS.getCode(), DigitBaseUtils.formatDigit(convertUnit(order.getCarbon_emission())));
//            map.put(FlightOrderDetailEnum.CONTRACTTYPE.getCode(), StringUtils.equalsIgnoreCase(order.getContract_type(), "C") ? MapperUtils.convertTorF(YesOrNotEnum.T.toString(), yesOrNotMap) : MapperUtils.convertTorF(YesOrNotEnum.F.toString(), yesOrNotMap));
//            map.put(FlightOrderDetailEnum.AGREEMENTTYPENAME.getCode(), MapperUtils.trim(order.getAgreement_type_name()));
//            map.put(FlightOrderDetailEnum.AGREEMENTRATE.getCode(), DigitBaseUtils.formatDigit(order.getAgreement_rate()));
//            map.put(FlightOrderDetailEnum.PREORDERDATE.getCode(), ObjectUtils.defaultIfNull(order.getPre_order_date(), 0).toString());
//            map.put(FlightOrderDetailEnum.FLIGHTWAYDESC.getCode(), MapperUtils.trim(order.getFlight_way_desc()));
//            map.put(FlightOrderDetailEnum.FLIGHTCLASS.getCode(), FlightOrderDetailService.convertFlightClass(MapperUtils.trim(order.getFlight_class())));
//            map.put(FlightOrderDetailEnum.FLIGHTCONTINENT.getCode(), MapperUtils.getFlightContinent(order.getFlight_continent()));
//            map.put(FlightOrderDetailEnum.TICKETWAYNAME.getCode(), MapperUtils.trim(order.getGet_ticket_way_name()));
//            map.put(FlightOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassenger_name()));
//            map.put(FlightOrderDetailEnum.SEQUENCE.getCode(), MapperUtils.convertDigitToString(order.getSequence()));
//            map.put(FlightOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlight_no()));
//            map.put(FlightOrderDetailEnum.SUBCLASS.getCode(), MapperUtils.trim(order.getSub_class()));
//            map.put(FlightOrderDetailEnum.FLIGHTSTATUS.getCode(), MapperUtils.trim(order.getFlight_status()));
//            map.put(FlightOrderDetailEnum.FLIGHTTIME.getCode(), DigitBaseUtils.formatDigit(order.getFlight_time()));
//            map.put(FlightOrderDetailEnum.AIRLINE.getCode(), MapperUtils.trim(order.getAirline()));
//            map.put(FlightOrderDetailEnum.AIRLINECN.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getAirline_cn_name()), MapperUtils.trim(order.getAirline_en_name())));
//            map.put(FlightOrderDetailEnum.TICKETNO.getCode(), MapperUtils.trim(order.getTicket_no()));
//            if (null != order.getTicket_no()) {
//                map.put(FlightOrderDetailEnum.TICKETSTATUS.getCode(), getDesc(order.getTicket_status(), flightTicketStatusMap));
//            } else {
//                map.put(FlightOrderDetailEnum.TICKETSTATUS.getCode(), getDesc(0, flightTicketStatusMap));
//            }
//            map.put(FlightOrderDetailEnum.REALCLASS.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getReal_class()), MapperUtils.trim(order.getReal_class_en())));
//            map.put(FlightOrderDetailEnum.FLIGHTCITYCODE.getCode(), MapperUtils.trim(order.getFlight_city_code()));
//            map.put(FlightOrderDetailEnum.FLIGHTCITY.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getFlight_city()), MapperUtils.trim(order.getFlight_city_en())));
//            map.put(FlightOrderDetailEnum.FLIGHTCITY2.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getFlight_city2()), MapperUtils.trim(order.getFlight_city2_en())));
//            map.put(FlightOrderDetailEnum.TPMS.getCode(), DigitBaseUtils.formatDigit(order.getTpms()));
//            map.put(FlightOrderDetailEnum.TPMSEN.getCode(), DigitBaseUtils.formatDigit(order.getTpms_en()));
//            map.put(FlightOrderDetailEnum.DEPARTUREPORTNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDeparture_port_name()), MapperUtils.trim(order.getDeparture_port_name_en())));
//            map.put(FlightOrderDetailEnum.DPORTCODE.getCode(), MapperUtils.trim(order.getDport_code()));
//            map.put(FlightOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDeparture_city_name()), MapperUtils.trim(order.getDeparture_city_name_en())));
//            map.put(FlightOrderDetailEnum.DEPARTURECITYCODE.getCode(), MapperUtils.trim(order.getDeparture_city_code()));
//            map.put(FlightOrderDetailEnum.DEPARTURECOUNTRY.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDeparture_country()), MapperUtils.trim(order.getDeparture_country_en())));
//            map.put(FlightOrderDetailEnum.DEPARTURECONTINENT.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDeparture_continent()), MapperUtils.trim(order.getDeparture_continent_en())));
//            map.put(FlightOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoff_time()));
//            map.put(FlightOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getArrival_city_name()), MapperUtils.trim(order.getArrival_city_name_en())));
//            map.put(FlightOrderDetailEnum.ARRIVALCITYCODE.getCode(), MapperUtils.trim(order.getArrival_city_code()));
//            map.put(FlightOrderDetailEnum.ARRIVALPORTNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getArrival_port_name()), MapperUtils.trim(order.getArrival_port_name_en())));
//            map.put(FlightOrderDetailEnum.APORTCODE.getCode(), MapperUtils.trim(order.getAport_code()));
//            map.put(FlightOrderDetailEnum.ARRIVALCONUNTRY.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getArrival_country()), MapperUtils.trim(order.getArrival_country_en())));
//            map.put(FlightOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrival_date_time()));
//            map.put(FlightOrderDetailEnum.DESTCONTINENT.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDest_continent()), MapperUtils.trim(order.getDest_continent_en())));
//            map.put(FlightOrderDetailEnum.DESTCOUNTRY.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDest_country()), MapperUtils.trim(order.getDest_country_en())));
//            map.put(FlightOrderDetailEnum.DESTPROVINCE.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDest_province()), MapperUtils.trim(order.getDest_province_en())));
//            map.put(FlightOrderDetailEnum.DESTCITYNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDest_city_name()), MapperUtils.trim(order.getDest_city_name_en())));
//            map.put(FlightOrderDetailEnum.LOWDTIME.getCode(), MapperUtils.trim(order.getLow_dtime()));
//            map.put(FlightOrderDetailEnum.CLASSRID.getCode(), MapperUtils.trim(order.getClass_rid()));
//            map.put(FlightOrderDetailEnum.CLASSRC.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getClass_rc()), MapperUtils.trim(order.getClass_rc_en())));
//            map.put(FlightOrderDetailEnum.AGREEMENTRID.getCode(), MapperUtils.trim(order.getAgreement_rid()));
//            map.put(FlightOrderDetailEnum.AGREEMENTRC.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getAgreement_rc()), MapperUtils.trim(order.getAgreement_rc_en())));
//            map.put(FlightOrderDetailEnum.AGREEMENTRCVV.getCode(), MapperUtils.trim(order.getAgreement_rc_vv()));
//            map.put(FlightOrderDetailEnum.LOWRID.getCode(), MapperUtils.trim(order.getLow_rid()));
//            map.put(FlightOrderDetailEnum.LOWRC.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getLow_rc()), MapperUtils.trim(order.getLow_rc_en())));
//            map.put(FlightOrderDetailEnum.LOWPRICERCVV.getCode(), MapperUtils.trim(order.getLowprice_rc_vv()));
//            map.put(FlightOrderDetailEnum.PRERID.getCode(), MapperUtils.trim(order.getPre_rid()));
//            map.put(FlightOrderDetailEnum.PRERC.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getPre_rc()), MapperUtils.trim(order.getPre_rc_en())));
//            map.put(FlightOrderDetailEnum.TIMERID.getCode(), MapperUtils.trim(order.getTime_rid()));
//            map.put(FlightOrderDetailEnum.TIMERC.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getTime_rc()), MapperUtils.trim(order.getTime_rc_en())));
//            map.put(FlightOrderDetailEnum.TIMERCVV.getCode(), MapperUtils.trim(order.getTime_rc_vv()));
//            map.put(FlightOrderDetailEnum.DISTANCERCVV.getCode(), MapperUtils.trim(order.getDistance_rc_vv()));
//            map.put(FlightOrderDetailEnum.SERVICEPACKAGEFEE.getCode(), DigitBaseUtils.formatDigit(order.getServicepackage_fee()));
//            map.put(FlightOrderDetailEnum.OCURRENCY.getCode(), MapperUtils.trim(order.getO_currency()));
//            map.put(FlightOrderDetailEnum.OEXCHANGERATE.getCode(), DigitBaseUtils.formatDigit(order.getO_exchangerate(), FOUR_DIGIT_NUM));
//            map.put(FlightOrderDetailEnum.LOWFLIGHT.getCode(), MapperUtils.trim(order.getLow_flight()));
//            map.put(FlightOrderDetailEnum.LOWSUBCLASS.getCode(), MapperUtils.trim(order.getLow_subclass()));
//            map.put(FlightOrderDetailEnum.AGETYPE.getCode(), convertAgeType(MapperUtils.trim(order.getAge_type())));//ADU, 成人票； BAB ,婴儿票；   CHI，儿童票
//            map.put(FlightOrderDetailEnum.DEFINDFLAG.getCode(), MapperUtils.trim(order.getDefineflag()));
//            map.put(FlightOrderDetailEnum.DEFINDFLAG2.getCode(), MapperUtils.trim(order.getDefineflag2()));
//            map.put(FlightOrderDetailEnum.ISSHARED.getCode(), MapperUtils.convertTorF(order.getIsshared(), yesOrNotMap));
//            map.put(FlightOrderDetailEnum.CARRIERFLIGHTNO.getCode(), MapperUtils.trim(order.getCarrierflightno()));
//            map.put(FlightOrderDetailEnum.REFUNDCUSTOMERSTATUS.getCode(), convertRefundcustomerstatus(order.getRefund_customer_status()));
//            map.put(FlightOrderDetailEnum.PASSENGENO.getCode(), MapperUtils.trim(order.getPassenger_no()));
//            map.put(FlightOrderDetailEnum.LOWRC_MARK.getCode(), MapperUtils.trim(order.getLow_reason_remarks()));
//            map.put(FlightOrderDetailEnum.HOTELORDERRC.getCode(), MapperUtils.trim(order.getNoorderhtlreason()));
//            map.put(FlightOrderDetailEnum.HOTELORDERRCDETAIL.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getNoorderhtlreasondesc()), MapperUtils.trim(order.getNoorderhtlreason_en())));
//            map.put(FlightOrderDetailEnum.NOTSELECTINSURANCEFEE.getCode(), DigitBaseUtils.formatDigit(order.getNotselect_insurance_fee()));
//            map.put(FlightOrderDetailEnum.PUBLISHPRICE.getCode(), DigitBaseUtils.formatDigit(order.getPublishprice()));
//            map.put(FlightOrderDetailEnum.CUSTOMERID.getCode(), MapperUtils.trim(order.getCustomerid()));
////            map.put(FlightOrderDetailEnum.AUDITORID.getCode(), MapperUtils.trim(order.getAuditorid()));
////            map.put(FlightOrderDetailEnum.AUDITORID2.getCode(), MapperUtils.trim(order.getAuditorid2()));
//            map.put(FlightOrderDetailEnum.SAVE_AMOUNT_3C.getCode(), DigitBaseUtils.formatDigit(order.getSave_amount_3c()));
//            map.put(FlightOrderDetailEnum.SAVE_AMOUNT_PREMIUM.getCode(), DigitBaseUtils.formatDigit(order.getSave_amount_premium()));
//            map.put(FlightOrderDetailEnum.CUSTOM_REFUND_REASON_CODE.getCode(), MapperUtils.trim(order.getCustom_refund_reason_code()));
//            map.put(FlightOrderDetailEnum.CUSTOM_REFUND_REASON_CODE_DESC.getCode(), MapperUtils.trim(order.getCustom_refund_reason_code_desc()));
//            map.put(FlightOrderDetailEnum.OILFEEDIFFERENTIAL.getCode(), DigitBaseUtils.formatDigit(order.getOilfeedifferential()));
//            map.put(FlightOrderDetailEnum.TAX_DIFFERENTIAL.getCode(), DigitBaseUtils.formatDigit(order.getTax_differential()));
//            map.put(FlightOrderDetailEnum.SETTLEMENTACCNTAMT.getCode(), DigitBaseUtils.formatDigit(convertAccntAmt(order, type)));
//            map.put(FlightOrderDetailEnum.SETTLEMENTPERSONAMT.getCode(), DigitBaseUtils.formatDigit(convertPersonAmt(order, type)));
//            map.put(FlightOrderDetailEnum.ISMIXPAYMENT.getCode(), MapperUtils.trim(order.getIsmixpayment()));
//            map.put(FlightOrderDetailEnum.CONTROL_SAVE.getCode(), DigitBaseUtils.formatDigit(order.getSaving_price()));
//            map.put(FlightOrderDetailEnum.DISCOUNT_REASON_CODE.getCode(), MapperUtils.trim(order.getDiscount_reason_code()));
//            map.put(FlightOrderDetailEnum.CUSTOM_DISCOUNT_REASON.getCode(), MapperUtils.trim(order.getCustom_discount_reason()));
//            map.put(FlightOrderDetailEnum.COUNTOFPASSENGERFLIGHT.getCode(), MapperUtils.convertDigitToZero(order.getCountofpassengerflight()));
//            map.put(FlightOrderDetailEnum.COMPANYFEE.getCode(), DigitBaseUtils.formatDigit(order.getCompanyfee()));
//            map.put(FlightOrderDetailEnum.REBOOK_PREPAYTYPENAME.getCode(), MapperUtils.trim(order.getRebook_prepaytypename()));
//            map.put(FlightOrderDetailEnum.FLTMEDIANCARBONS.getCode(), MapperUtils.convertDigitToString(convertUnit(order.getMedian_carbon_emission())));
//            map.put(FlightOrderDetailEnum.FLTCARBONSAVE.getCode(), MapperUtils.convertDigitToString(calSaveCarbon(order.getCarbon_emission(), order.getMedian_carbon_emission())));
//            map.put(FlightOrderDetailEnum.STDINDUSTRY1.getCode(), MapperUtils.trim(order.getStd_industry1()));
//            map.put(FlightOrderDetailEnum.STDINDUSTRY2.getCode(), MapperUtils.trim(order.getStd_industry2()));
            result.add(map);
        }
        return result;
    }

    private Double calSaveCarbon(Integer carbon, Integer medianCarbon) {
        if (carbon == null || medianCarbon == null || carbon == 0 || medianCarbon == 0) {
            return 0d;
        }
        return DigitBaseUtils.divide(medianCarbon - carbon, 1000).doubleValue();
    }

    ;

    private Double convertUnit(Integer carbonEmission) {
        if (carbonEmission == null || carbonEmission == 0) {
            return 0D;
        }
        return DigitBaseUtils.divide(carbonEmission, 1000).doubleValue();
    }

    /**
     * 改签明细
     *
     * @param flightEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> convertFlightRebookOrderMapData(List<FlightEntity> flightEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (FlightEntity order : flightEntityList) {
            Map<String, Object> map = new HashMap<>();
            map.put(FlightChangeOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
            map.put(FlightChangeOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrder_id()));
            map.put(FlightChangeOrderDetailEnum.PRINTTICKETTIME.getCode(), MapperUtils.trim(order.getPrint_ticket_time()));
            map.put(FlightChangeOrderDetailEnum.REALCLASS.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getReal_class()), MapperUtils.trim(order.getReal_class_en())));
            map.put(FlightChangeOrderDetailEnum.SUBCLASS.getCode(), MapperUtils.trim(order.getSub_class()));
            map.put(FlightChangeOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(FlightChangeOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
            map.put(FlightChangeOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassenger_name()));
            map.put(FlightChangeOrderDetailEnum.EMPLOYEEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
            map.put(FlightChangeOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(FlightChangeOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(FlightChangeOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(FlightChangeOrderDetailEnum.FLIGHTCLASS.getCode(), FlightOrderDetailService.convertFlightClass(MapperUtils.trim(order.getFlight_class())));
            map.put(FlightChangeOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoff_time()));
            map.put(FlightChangeOrderDetailEnum.FLIGHTCITY2.getCode(), MapperUtils.getValByLang(lang, order.getFlight_city2(), order.getFlight_city2_en()));
            map.put(FlightChangeOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getDeparture_city_name(), order.getDeparture_city_name_en()));
            map.put(FlightChangeOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrival_city_name(), order.getArrival_city_name_en()));

            map.put(FlightChangeOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlight_no()));
            map.put(FlightChangeOrderDetailEnum.AIRELINE.getCode(), MapperUtils.trim(order.getAirline()));
            map.put(FlightChangeOrderDetailEnum.STDPRICE.getCode(), getStdPrice(order.getFlight_class(), DigitBaseUtils.formatDigit(order.getStd_price()).toString()));
            map.put(FlightChangeOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));

            map.put(FlightChangeOrderDetailEnum.PRICE.getCode(), DigitBaseUtils.formatDigit(Double.valueOf(order.getPrice())));
            map.put(FlightChangeOrderDetailEnum.TAX.getCode(), DigitBaseUtils.formatDigit(order.getTax()));
            map.put(FlightChangeOrderDetailEnum.OILFEE.getCode(), DigitBaseUtils.formatDigit(order.getOil_fee()));
            map.put(FlightChangeOrderDetailEnum.CHANGEFEE.getCode(), DigitBaseUtils.formatDigit(order.getChange_fee()));
            map.put(FlightChangeOrderDetailEnum.INSERANCEFEE.getCode(), DigitBaseUtils.formatDigit(order.getInsurance_fee()));
            map.put(FlightChangeOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getService_fee()));
            map.put(FlightChangeOrderDetailEnum.REFUNDFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefund_fee()));
            map.put(FlightChangeOrderDetailEnum.REFUNDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefund_service_fee()));
            map.put(FlightChangeOrderDetailEnum.REBOOKSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebook_service_fee()));
            map.put(FlightChangeOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));

            Set<String> rc = new HashSet<>();
            if (StringUtils.isNotEmpty(order.getPre_rid())) {
                rc.add(order.getPre_rid());
            }
            if (StringUtils.isNotEmpty(order.getLow_rid())) {
                rc.add(order.getLow_rid());
            }
            if (StringUtils.isNotEmpty(order.getClass_rid())) {
                rc.add(order.getClass_rid());
            }
            if (StringUtils.isNotEmpty(order.getAgreement_rid())) {
                rc.add(order.getAgreement_rid());
            }
            map.put(FlightChangeOrderDetailEnum.REASONCODE.getCode(), (rc.size() == 0) ? GlobalConst.STRING_EMPTY : StringUtils.join(rc.toArray(), GlobalConst.SEPARATOR));
            map.put(FlightChangeOrderDetailEnum.BOOKTYPE.getCode(), MapperUtils.convertBookTypeSingle(order.getIs_online()));

            map.put(FlightChangeOrderDetailEnum.CORPPRICE.getCode(), DigitBaseUtils.formatDigit(order.getCorp_price()));
            map.put(FlightChangeOrderDetailEnum.TICKETNO.getCode(), MapperUtils.trim(order.getTicket_no()));

            Set<String> rcDesc = new HashSet<>();
            if (StringUtils.isNotEmpty(order.getPre_rid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getPre_rc(), order.getPre_rc_en()));
            }
            if (StringUtils.isNotEmpty(order.getLow_rid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getLow_rc(), order.getLow_rc_en()));
            }
            if (StringUtils.isNotEmpty(order.getClass_rid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getClass_rc(), order.getClass_rc_en()));
            }
            if (StringUtils.isNotEmpty(order.getAgreement_rid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getAgreement_rc(), order.getAgreement_rc_en()));
            }
            map.put(FlightChangeOrderDetailEnum.REASON.getCode(), (rcDesc.size() == 0) ? GlobalConst.STRING_EMPTY : StringUtils.join(rcDesc.toArray(), GlobalConst.SEPARATOR));

            map.put(FlightChangeOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrival_date_time()));
            map.put(FlightChangeOrderDetailEnum.AMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));
            map.put(FlightChangeOrderDetailEnum.DEPARTURECITYANDPORT.getCode(), MapperUtils.getValByLang(lang, order.getDeparture_city_name() + "/" + order.getDeparture_port_name(), order.getDeparture_city_name_en() + "/" + order.getDeparture_port_name_en()));
            map.put(FlightChangeOrderDetailEnum.ARRIVALCITYANDPORT.getCode(), MapperUtils.getValByLang(lang, order.getArrival_city_name() + "/" + order.getArrival_port_name(), order.getArrival_city_name_en() + "/" + order.getArrival_port_name_en()));
            map.put(FlightChangeOrderDetailEnum.PASSENGERNAMEPY.getCode(), MapperUtils.trim(order.getPassenger_name_py()));
            map.put(FlightChangeOrderDetailEnum.TPMS.getCode(), DigitBaseUtils.formatDigit(order.getTpms()));
            map.put(FlightChangeOrderDetailEnum.PREORDERDATE.getCode(), MapperUtils.convertDigitToZero(order.getPre_order_date()));

            map.put(FlightChangeOrderDetailEnum.REBOOKTIMES.getCode(), MapperUtils.convertDigitToZero(order.getRebooktimes()));
            map.put(FlightChangeOrderDetailEnum.REBOOKPRICEDIFFERENTIAL.getCode(), DigitBaseUtils.formatDigit(order.getRebook_price_differential()));
            map.put(FlightChangeOrderDetailEnum.REBOOKSUBCLASS.getCode(), MapperUtils.trim(order.getRebooksubclass()));
            map.put(FlightChangeOrderDetailEnum.REBOOKREASONDESC.getCode(), MapperUtils.trim(order.getRebook_reson_desc()));
            map.put(FlightChangeOrderDetailEnum.SUBSIDY.getCode(), org.apache.commons.lang3.StringUtils.EMPTY);
            result.add(map);
        }
        return result;
    }

    /**
     * 退票明细
     *
     * @param flightEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> convertFlightRefundOrderMapData(List<FlightEntity> flightEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (FlightEntity order : flightEntityList) {
            Map<String, Object> map = new HashMap<>();
            map.put(FlightRefundOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
            map.put(FlightRefundOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrder_id()));
            map.put(FlightRefundOrderDetailEnum.PRINTTICKETTIME.getCode(), MapperUtils.trim(order.getPrint_ticket_time()));
            map.put(FlightRefundOrderDetailEnum.REALCLASS.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getReal_class()), MapperUtils.trim(order.getReal_class_en())));
            map.put(FlightRefundOrderDetailEnum.SUBCLASS.getCode(), MapperUtils.trim(order.getSub_class()));
            map.put(FlightRefundOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(FlightRefundOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
            map.put(FlightRefundOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassenger_name()));
            map.put(FlightRefundOrderDetailEnum.EMPLOYEEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
            map.put(FlightRefundOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(FlightRefundOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(FlightRefundOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(FlightRefundOrderDetailEnum.FLIGHTCLASS.getCode(), OrderTypeEnum.getEnumByName(order.getFlight_class()).getDesCn());
            map.put(FlightRefundOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoff_time()));
            map.put(FlightRefundOrderDetailEnum.FLIGHTCITY2.getCode(), MapperUtils.getValByLang(lang, order.getFlight_city2(), order.getFlight_city2_en()));
            map.put(FlightRefundOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlight_no()));

            map.put(FlightRefundOrderDetailEnum.PRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrice()));
            map.put(FlightRefundOrderDetailEnum.TAX.getCode(), DigitBaseUtils.formatDigit(order.getTax()));
            map.put(FlightRefundOrderDetailEnum.OILFEE.getCode(), DigitBaseUtils.formatDigit(order.getOil_fee()));
            map.put(FlightRefundOrderDetailEnum.CHANGEFEE.getCode(), DigitBaseUtils.formatDigit(order.getChange_fee()));
            map.put(FlightRefundOrderDetailEnum.INSERANCEFEE.getCode(), DigitBaseUtils.formatDigit(order.getInsurance_fee()));
            map.put(FlightRefundOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getService_fee()));
            map.put(FlightRefundOrderDetailEnum.REFUNDFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefund_fee()));
            map.put(FlightRefundOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));
            map.put(FlightRefundOrderDetailEnum.REFUNDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefund_service_fee()));
            map.put(FlightRefundOrderDetailEnum.REBOOKSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebook_service_fee()));

            Set<String> rc = new HashSet<>();
            if (StringUtils.isNotEmpty(order.getPre_rid())) {
                rc.add(order.getPre_rid());
            }
            if (StringUtils.isNotEmpty(order.getLow_rid())) {
                rc.add(order.getLow_rid());
            }
            if (StringUtils.isNotEmpty(order.getClass_rid())) {
                rc.add(order.getClass_rid());
            }
            if (StringUtils.isNotEmpty(order.getAgreement_rid())) {
                rc.add(order.getAgreement_rid());
            }
            map.put(FlightRefundOrderDetailEnum.REASONCODE.getCode(), (rc.size() == 0) ? GlobalConst.STRING_EMPTY : StringUtils.join(rc.toArray(), GlobalConst.SEPARATOR));

            map.put(FlightRefundOrderDetailEnum.CORPPRICE.getCode(), DigitBaseUtils.formatDigit(order.getCorp_price()));
            map.put(FlightRefundOrderDetailEnum.TICKETNO.getCode(), MapperUtils.trim(order.getTicket_no()));

            map.put(FlightRefundOrderDetailEnum.BOOKTYPE.getCode(), MapperUtils.convertBookTypeSingle(order.getIs_online()));

            Set<String> rcDesc = new HashSet<>();
            if (StringUtils.isNotEmpty(order.getPre_rid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getPre_rc(), order.getPre_rc_en()));
            }
            if (StringUtils.isNotEmpty(order.getLow_rid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getLow_rc(), order.getLow_rc_en()));
            }
            if (StringUtils.isNotEmpty(order.getClass_rid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getClass_rc(), order.getLow_rc_en()));
            }
            if (StringUtils.isNotEmpty(order.getAgreement_rid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getAgreement_rc(), order.getLow_rc_en()));
            }
            map.put(FlightRefundOrderDetailEnum.REASON.getCode(), (rcDesc.size() == 0) ? GlobalConst.STRING_EMPTY : StringUtils.join(rcDesc.toArray(), GlobalConst.SEPARATOR));

            map.put(FlightRefundOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrival_date_time()));
            map.put(FlightRefundOrderDetailEnum.AMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));

            map.put(FlightRefundOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getDeparture_city_name(), order.getDeparture_city_name_en()));
            map.put(FlightRefundOrderDetailEnum.DEPARTUREPORTNAME.getCode(), MapperUtils.getValByLang(lang, order.getDeparture_port_name(), order.getDeparture_port_name_en()));
            map.put(FlightRefundOrderDetailEnum.DPORTCODE.getCode(), MapperUtils.trim(order.getDport_code()));
            map.put(FlightRefundOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrival_city_name(), order.getArrival_city_name_en()));
            map.put(FlightRefundOrderDetailEnum.ARRIVALPORTNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrival_port_name(), order.getArrival_port_name_en()));
            map.put(FlightRefundOrderDetailEnum.APORTCODE.getCode(), MapperUtils.trim(order.getAport_code()));
            map.put(FlightRefundOrderDetailEnum.PASSENGERNAMEPY.getCode(), MapperUtils.trim(order.getPassenger_name_py()));
            map.put(FlightRefundOrderDetailEnum.TPMS.getCode(), DigitBaseUtils.formatDigit(order.getTpms()));

            map.put(FlightRefundOrderDetailEnum.REFUNDTIME.getCode(), MapperUtils.trim(order.getRefundtime()));
            map.put(FlightRefundOrderDetailEnum.REFUNDREASONDESC.getCode(), MapperUtils.trim(order.getRefund_reson_desc()));
            map.put(FlightRefundOrderDetailEnum.SUBSIDY.getCode(), org.apache.commons.lang3.StringUtils.EMPTY);

            result.add(map);
        }
        return result;
    }

    /**
     * 未使用明细
     *
     * @param flightEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> convertFlightUnuseOrderMapData(List<FlightEntity> flightEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        String segStatus = SharkUtils.get("Exceltopname.Unusedticket", lang);
        for (FlightEntity order : flightEntityList) {
            Map<String, Object> map = new HashMap<>();
            map.put(UnUseFlightOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorp_corporation()));
            map.put(UnUseFlightOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrder_id()));
            map.put(UnUseFlightOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(UnUseFlightOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
            map.put(UnUseFlightOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassenger_name()));
            map.put(UnUseFlightOrderDetailEnum.PRINTTICKETTIME.getCode(), MapperUtils.trim(order.getPrint_ticket_time()));
            map.put(UnUseFlightOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoff_time()));
            map.put(UnUseFlightOrderDetailEnum.FLIGHTCITY.getCode(), MapperUtils.getValByLang(lang, order.getFlight_city(), order.getFlight_city_en()));
            map.put(UnUseFlightOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlight_no()));
            map.put(UnUseFlightOrderDetailEnum.SUBCLASS.getCode(), MapperUtils.trim(order.getSub_class()));
            map.put(UnUseFlightOrderDetailEnum.PRICERATE.getCode(), DigitBaseUtils.formatDigit(order.getPrice_rate()));
            map.put(UnUseFlightOrderDetailEnum.PRINTPRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrint_price()));
            map.put(UnUseFlightOrderDetailEnum.TICKETNO.getCode(), MapperUtils.trim(order.getTicket_no()));
            map.put(UnUseFlightOrderDetailEnum.SEGSTATUS.getCode(), segStatus);
            map.put(UnUseFlightOrderDetailEnum.ISPERSONAL.getCode(), MapperUtils.trim(order.getFee_type()));
            map.put(UnUseFlightOrderDetailEnum.PAYMENTTYPE.getCode(), MapperUtils.trim(order.getPrepay_type()));
            map.put(UnUseFlightOrderDetailEnum.WORKCITY.getCode(), MapperUtils.trim(order.getWork_city()));
            map.put(UnUseFlightOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCost_center1()));
            map.put(UnUseFlightOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCost_center2()));
            map.put(UnUseFlightOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCost_center3()));
            map.put(UnUseFlightOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(UnUseFlightOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(UnUseFlightOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(UnUseFlightOrderDetailEnum.FLIGHTCLASS.getCode(), FlightOrderDetailService.convertFlightClass(MapperUtils.trim(order.getFlight_class())));
            map.put(UnUseFlightOrderDetailEnum.EMPLOYEEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
            map.put(UnUseFlightOrderDetailEnum.TRIPID.getCode(), MapperUtils.convertDigitToDash(order.getTrip_id()));
            map.put(UnUseFlightOrderDetailEnum.EXPIREDATE.getCode(), DateUtils.getExpireDate(order.getPrint_ticket_time()));
            map.put(UnUseFlightOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrder_status()));
            map.put(UnUseFlightOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));
            map.put(UnUseFlightOrderDetailEnum.RANK.getCode(), MapperUtils.trim(order.getRank_name()));
            result.add(map);
        }
        return result;
    }


    private String convertOrderStatus(String orderStatus) {
        Map<String, String> map = FlightOrderStatusNewEnum.toMap();
        return map.get(MapperUtils.trim(orderStatus));
    }

    private String getDesc(Integer key, Map map) {
        if (Objects.isNull(key) || Objects.isNull(map) || map.isEmpty()) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.isEmpty(StringUtils.trimToEmpty((String) map.get(key)))) {
            return String.valueOf(key);
        }
        return (String) map.get(key);
    }

    private Map initFlightTicketStatus(String lang) {
        return ImmutableMap.builder()
                .put(FlightTicketStatusEnum.Unknown_0.getValue(), SharkUtils.get(FlightTicketStatusEnum.Unknown_0.getDescription(), lang))
                .put(FlightTicketStatusEnum.Unknown_1.getValue(), SharkUtils.get(FlightTicketStatusEnum.Unknown_1.getDescription(), lang))
                .put(FlightTicketStatusEnum.Unknown_2.getValue(), SharkUtils.get(FlightTicketStatusEnum.Unknown_2.getDescription(), lang))
                .put(FlightTicketStatusEnum.Unknown_3.getValue(), SharkUtils.get(FlightTicketStatusEnum.Unknown_3.getDescription(), lang))
                .put(FlightTicketStatusEnum.UnUse.getValue(), SharkUtils.get(FlightTicketStatusEnum.UnUse.getDescription(), lang))
                .put(FlightTicketStatusEnum.Used.getValue(), SharkUtils.get(FlightTicketStatusEnum.Used.getDescription(), lang))
                .put(FlightTicketStatusEnum.CheckIn.getValue(), SharkUtils.get(FlightTicketStatusEnum.CheckIn.getDescription(), lang))
                .put(FlightTicketStatusEnum.OutGoing.getValue(), SharkUtils.get(FlightTicketStatusEnum.OutGoing.getDescription(), lang))
                .put(FlightTicketStatusEnum.Cancelled.getValue(), SharkUtils.get(FlightTicketStatusEnum.Cancelled.getDescription(), lang))
                .put(FlightTicketStatusEnum.Refunded.getValue(), SharkUtils.get(FlightTicketStatusEnum.Refunded.getDescription(), lang))
                .put(FlightTicketStatusEnum.Changed.getValue(), SharkUtils.get(FlightTicketStatusEnum.Changed.getDescription(), lang))
                .put(FlightTicketStatusEnum.Pending.getValue(), SharkUtils.get(FlightTicketStatusEnum.Pending.getDescription(), lang))
                .put(FlightTicketStatusEnum.Control9.getValue(), SharkUtils.get(FlightTicketStatusEnum.Control9.getDescription(), lang))
                .put(FlightTicketStatusEnum.Control10.getValue(), SharkUtils.get(FlightTicketStatusEnum.Control10.getDescription(), lang))
                .put(FlightTicketStatusEnum.Disabled.getValue(), SharkUtils.get(FlightTicketStatusEnum.Disabled.getDescription(), lang))
                .build();
    }


    private String convertAgeType(String ageType) {
        if (StringUtils.isEmpty(ageType)) {
            return MapperUtils.trim(ageType);
        }
        if (StringUtils.equalsIgnoreCase(AgeTypeEnums.ADU.toString(), ageType.trim())) {
            return ChineseLanguageConfig.get("ageType_adu");
        } else if (StringUtils.equalsIgnoreCase(AgeTypeEnums.BAB.toString(), ageType.trim())) {
            return ChineseLanguageConfig.get("ageType_bab");
        } else if (StringUtils.equalsIgnoreCase(AgeTypeEnums.CHI.toString(), ageType.trim())) {
            return ChineseLanguageConfig.get("ageType_chi");
        }
        return MapperUtils.trim(ageType);
    }

    public String convertRefundcustomerstatus(String status) {
        if (StringUtils.isEmpty(status)) {
            return MapperUtils.trim(status);
        }
        if (StringUtils.equalsIgnoreCase("T", status.trim())) {
            return ChineseLanguageConfig.get("refund_customer_status_t");
        } else if (StringUtils.equalsIgnoreCase("F", status.trim())) {
            return ChineseLanguageConfig.get("refund_customer_status_f");
        } else {
            return MapperUtils.trim(status);
        }
    }

    private String getStdPrice(String flightClass, String stdPrice) {
        if (StringUtils.equalsIgnoreCase(flightClass, OrderTypeEnum.N.toString())) {
            return DigitBaseUtils.formatDigit(stdPrice);
        } else {
            return GlobalConst.STRING_EMPTY;
        }
    }

    private Float convertAccntAmt(FlightEntity order, Map map) {
        Float result = 0F;
        // 混付
        String ismixpayment = (String) map.get("Report.ReportLib.flt.mixpayment");
        // 公司账户
        String accntPrepayType = (String) map.get("Report.ReportLib.PrepayType.accnt");
        // 公账支付
        String rebookPrepaytypenameAccnt = (String) map.get("Report.ReportLib.rebookPrepaytypename.accnt");
        // 银联公账支付
        String rebookPrepaytypenameUnionPay = (String) map.get("Report.ReportLib.rebookPrepaytypename.UnionPay");
        //改签公账支付方式
        List rebookAccntTypereList = Arrays.asList(rebookPrepaytypenameAccnt, rebookPrepaytypenameUnionPay);
        Float changeFee = Optional.ofNullable(order.getChange_fee()).orElse(0f)
                + Optional.ofNullable(order.getRebook_service_fee()).orElse(0f)
                + Optional.ofNullable(order.getRebook_behind_service_fee()).orElse(0f);
        String rebookPrepaytypename = order.getRebook_prepaytypename();
        if (StringUtils.equalsIgnoreCase(order.getIsmixpayment(), ismixpayment)) {
            result = Optional.ofNullable(order.getSettlementaccntamt()).orElse(0f);
            if (rebookAccntTypereList.stream().anyMatch(e -> StringUtils.equalsIgnoreCase(e.toString(), rebookPrepaytypename))) {
                result = result + changeFee;
            }
        } else {
            // 公账支付方式
            List accntType = Arrays.asList("ACCNT", accntPrepayType, "APPAY", "AIRPLUS");
            String prepayType = order.getPrepay_type();
            if (accntType.stream().anyMatch(e -> StringUtils.equalsIgnoreCase(e.toString(), prepayType))) {
                //纯公付
                result = Optional.ofNullable(order.getReal_pay()).orElse(0f);
                if (rebookAccntTypereList.stream().noneMatch(e -> StringUtils.equalsIgnoreCase(e.toString(), rebookPrepaytypename))) {
                    result = result - changeFee;
                }
            } else {
                //纯个付
                result = 0f;
                if (rebookAccntTypereList.stream().anyMatch(e -> StringUtils.equalsIgnoreCase(e.toString(), rebookPrepaytypename))) {
                    result = result + changeFee;
                }
            }
        }
        return result;
    }

    private Float convertPersonAmt(FlightEntity order, Map map) {
        Float result = 0F;
        // 混付
        String ismixpayment = (String) map.get("Report.ReportLib.flt.mixpayment");
        // 公司账户
        String accntPrepayType = (String) map.get("Report.ReportLib.PrepayType.accnt");
        // 公账支付
        String rebookPrepaytypenameAccnt = (String) map.get("Report.ReportLib.rebookPrepaytypename.accnt");
        // 银联公账支付
        String rebookPrepaytypenameUnionPay = (String) map.get("Report.ReportLib.rebookPrepaytypename.UnionPay");
        //改签公账支付方式
        List rebookAccntTypereList = Arrays.asList(rebookPrepaytypenameAccnt, rebookPrepaytypenameUnionPay);
        Float changeFee = Optional.ofNullable(order.getChange_fee()).orElse(0f)
                + Optional.ofNullable(order.getRebook_service_fee()).orElse(0f)
                + Optional.ofNullable(order.getRebook_behind_service_fee()).orElse(0f);
        String rebookPrepaytypename = order.getRebook_prepaytypename();
        if (StringUtils.equalsIgnoreCase(order.getIsmixpayment(), ismixpayment)) {
            result = Optional.ofNullable(order.getSettlementpersonamt()).orElse(0f);
            if (rebookAccntTypereList.stream().noneMatch(e -> StringUtils.equalsIgnoreCase(e.toString(), rebookPrepaytypename))) {
                result = result + changeFee;
            }
        } else {
            // 公账支付方式
            List accntType = Arrays.asList("ACCNT", accntPrepayType, "APPAY", "AIRPLUS");
            String prepayType = order.getPrepay_type();
            if (accntType.stream().anyMatch(e -> StringUtils.equalsIgnoreCase(e.toString(), prepayType))) {
                //纯公付
                result = 0F;
                if (rebookAccntTypereList.stream().noneMatch(e -> StringUtils.equalsIgnoreCase(e.toString(), rebookPrepaytypename))) {
                    result = result + changeFee;
                }
            } else {
                //纯个付
                result = Optional.ofNullable(order.getReal_pay()).orElse(0f);
                if (rebookAccntTypereList.stream().anyMatch(e -> StringUtils.equalsIgnoreCase(e.toString(), rebookPrepaytypename))) {
                    result = result - changeFee;
                }
            }
        }
        return result;
    }

    private Map init() {
        return ImmutableMap.builder()
                .put("Report.ReportLib.flt.mixpayment", ChineseLanguageConfig.get("Report.ReportLib.flt.mixpayment"))
                .put("Report.ReportLib.PrepayType.accnt", ChineseLanguageConfig.get("Report.ReportLib.PrepayType.accnt"))
                .put("Report.ReportLib.rebookPrepaytypename.accnt", ChineseLanguageConfig.get("Report.ReportLib.rebookPrepaytypename.accnt"))
                .put("Report.ReportLib.rebookPrepaytypename.UnionPay", ChineseLanguageConfig.get("Report.ReportLib.rebookPrepaytypename.UnionPay"))
                .build();
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.flight;
    }
}
