package com.corpgovernment.resource.schedule.redemptioncode;

import com.corpgovernment.resource.schedule.domain.redemptioncode.gateway.RedemptionCodeGateway;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class RedemptionCodeService {

    @Resource
    private RedemptionCodeGateway redemptionCodeGateway;

    public void sendRedemptionCode() {
        redemptionCodeGateway.bindAllRedemptionCode();
    }
}
