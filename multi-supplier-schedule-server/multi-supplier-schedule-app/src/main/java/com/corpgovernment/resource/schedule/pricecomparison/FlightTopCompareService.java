package com.corpgovernment.resource.schedule.pricecomparison;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.corpgovernment.pricecomparison.FlightPriceCompareModel;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.CommonConst;
import com.corpgovernment.resource.schedule.domain.pricecomparison.enums.ComparePriceFlightTypeEnum;
import com.corpgovernment.resource.schedule.domain.pricecomparison.enums.ComparePriceProductTypeEnum;
import com.corpgovernment.resource.schedule.domain.pricecomparison.enums.TaskStatusComparisonEnum;
import com.corpgovernment.resource.schedule.pricecomparison.mapper.MbJobFlightTopCompareMapper;
import com.corpgovernment.resource.schedule.pricecomparison.mapper.MbJobSupplierCompareTaskMapper;
import com.corpgovernment.resource.schedule.pricecomparison.mysql.entity.MbJobFlightTopCompareDo;
import com.corpgovernment.resource.schedule.pricecomparison.mysql.entity.MbJobSupplierCompareTask;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class FlightTopCompareService {

    @Autowired
    private MbJobFlightTopCompareMapper mbJobFlightTopCompareMapper;

    @Autowired
    private MbJobSupplierCompareTaskMapper mbJobSupplierCompareTaskMapper;


    // 热门航线100查询人的配置 "{ \"tenantId\": \"123\", \"uid\": \"abc\", \"corpId\": \"456\", \"flightIintlClazzList\": [\"Y\", \"C\"] }";
    @Value("${flight.Top.Compare.Config: }")
    private String flightTopCompareConfig;

    public void createFlightTopCompareTask(String param) {

        // 1. 获取Apollo配置
        if(StringUtils.isBlank(flightTopCompareConfig)) {
            log.error("FlightTopCompareService flightTopCompareConfig is empty");
            return;
        }
        JSONObject obj = JSON.parseObject(flightTopCompareConfig);
        String uid = obj.getString("uid");
        String corpId = obj.getString("corpId");
        List<String> cabinList = obj.getJSONArray("flightIintlClazzList").toJavaList(String.class);
        if (StringUtils.isBlank(uid) || StringUtils.isBlank(corpId)) {
            log.error("FlightTopCompareService Apollo配置不完整: uid={}, corpId={}", uid, corpId);
            return;
        }
        if(CollectionUtils.isEmpty(cabinList)) {
            cabinList.add("Y"); // 默认添加经济舱
        }
        // 2. 查询top100航线
        List<MbJobFlightTopCompareDo> topFlights =  mbJobFlightTopCompareMapper.selectAll();
        if(CollectionUtils.isEmpty(topFlights)) {
            log.error("热门航线表无数据");
            return;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonConst.DATE_FORMAT2);
        LocalDate today = LocalDate.now();
        String todayStr = today.format(formatter);
        String plus7Str = today.plusDays(7).format(formatter);
        String plus14Str = today.plusDays(14).format(formatter);

        // 3. 检查是否已创建任务，top一天只创建一次
        List<MbJobSupplierCompareTask> taskExistList= mbJobSupplierCompareTaskMapper.selectByTaskId(todayStr+ComparePriceProductTypeEnum.FLIGHT_TOP.getName());
        if(CollectionUtils.isNotEmpty(taskExistList)){
            log.info("当天任务已经创建：{}", todayStr+ComparePriceProductTypeEnum.FLIGHT_TOP.getName());
            return;
        }

        // 4. 组装数据
        List<MbJobSupplierCompareTask> tasks = new ArrayList<>();
        for (MbJobFlightTopCompareDo flight : topFlights) {
            if(flight == null || StringUtils.isBlank(flight.getFlightType()) || StringUtils.isBlank(flight.getDepartCityCode()) || StringUtils.isBlank(flight.getArriveCityCode())) {
                log.error("热门航线数据不完整: {}", flight);
                continue;
            }
            if(ComparePriceFlightTypeEnum.FLIGHT_INTL.getCode().equals(flight.getFlightType())) {
                // 国际航线，区分舱位
                for (String cabin : cabinList) {
                    if (StringUtils.isBlank(cabin)) {
                        log.warn("热门航线配置中舱位为空，跳过该航线: {}", flight);
                        continue;
                    }
                    // 创建两个任务，分别对应7天和14天后的对比
                    tasksAddByFlight(tasks, flight, todayStr, plus7Str, plus14Str, uid, corpId, cabin);
                }
            } else if(ComparePriceFlightTypeEnum.FLIGHT.getCode().equals(flight.getFlightType())) {
                 // 创建两个任务，分别对应7天和14天后的对比,国际不区分舱等
                 tasksAddByFlight(tasks, flight, todayStr, plus7Str, plus14Str, uid, corpId, null);
            } else {
                log.warn("未知的航线类型: {}", flight.getFlightType());
            }
        }
        // 5. 批量插入
        if(CollectionUtils.isNotEmpty(tasks)) {
            try {
                mbJobSupplierCompareTaskMapper.insertList(tasks);
                log.info("成功创建{}条热门航线对比任务", tasks.size());
            } catch (Exception e) {
                log.error("创建热门航线对比任务失败", e);
            }
        } else {
            log.warn("没有需要创建的热门航线对比任务");
        }
    }

    private void tasksAddByFlight(List<MbJobSupplierCompareTask> tasks, MbJobFlightTopCompareDo flight, String todayStr,
                                  String plus7Str, String plus14Str, String uid, String corpId, String cabin) {
        MbJobSupplierCompareTask task = new MbJobSupplierCompareTask();
        task.setTaskId(todayStr+ComparePriceProductTypeEnum.FLIGHT_TOP.getName());
        task.setTaskType(ComparePriceProductTypeEnum.FLIGHT_TOP.getName());
        task.setTaskStatus(TaskStatusComparisonEnum.READY.getCode());
        task.setCreateEid("系统自动");
        task.setCreateName("系统自动");
        task.setRequestid(String.valueOf(UUID.randomUUID()));
        FlightPriceCompareModel compareModel = new FlightPriceCompareModel();
        compareModel.setFlightType(flight.getFlightType());
        compareModel.setDepartCityCode(flight.getDepartCityCode());
        compareModel.setArriveCityCode(flight.getArriveCityCode());
        compareModel.setClazzCode(cabin);
        compareModel.setDepartDate(plus7Str);
        compareModel.setUid(uid);
        compareModel.setCorpId(corpId);
        task.setCompareData(JSONObject.toJSONString(compareModel));
        tasks.add(task);

        MbJobSupplierCompareTask taskTwo = new MbJobSupplierCompareTask();
        taskTwo.setTaskId(task.getTaskId());
        taskTwo.setTaskType(task.getTaskType());
        taskTwo.setTaskStatus(task.getTaskStatus());
        taskTwo.setCreateEid(task.getCreateEid());
        taskTwo.setCreateName(task.getCreateName());
        taskTwo.setRequestid(String.valueOf(UUID.randomUUID()));
        compareModel.setDepartDate(plus14Str);
        taskTwo.setCompareData(JSONObject.toJSONString(compareModel));
        tasks.add(taskTwo);
    }


}
