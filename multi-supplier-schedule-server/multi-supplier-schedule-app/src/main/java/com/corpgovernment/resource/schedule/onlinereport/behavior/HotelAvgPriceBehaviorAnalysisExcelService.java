package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AvgNightPriceOverviewInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AvgNightPriceRangeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HotelBehaviorInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.MarkMetricInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportAvgNightPriceRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportAvgNightPriceResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMarkMetricTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMarkMetricTrendResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc
 */
@Service
public class HotelAvgPriceBehaviorAnalysisExcelService extends AbstractGenralExportDataService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        Map map = getConditionContent(baseCondition.getBaseQueryCondition().getUid(), baseCondition.getBaseQueryCondition(), lang);
        int sheetIndex = 0;
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        chartExcelEntityList.add(buildExcel(baseCondition, sheetIndex, "overview", map));
        chartExcelEntityList.add(buildExcel(baseCondition, ++sheetIndex, "dis", map));
        chartExcelEntityList.add(buildExcel(baseCondition, ++sheetIndex, "trend_compare_corp_industry", map));
        chartExcelEntityList.add(buildExcel(baseCondition, ++sheetIndex, "trend_compare_mc", map));
        return chartExcelEntityList;
    }


    public ChartExcelEntity buildExcel(BaseQueryConditionBO baseCondition, int sheetIndex, String index, Map map) throws BusinessException {
        String lang = baseCondition.getLang();
        if (StringUtils.equalsIgnoreCase(index, "trend_compare_corp_industry")) {
            OnlineReportMarkMetricTrendRequest request = new OnlineReportMarkMetricTrendRequest();
            request.setProductType(baseCondition.getProductType());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            request.setQueryBu(QueryReportBuTypeEnum.hotel);
            extMap.put("metric", "avg_night_price_trend");
            request.setExtData(extMap);
            OnlineReportMarkMetricTrendResponse responseType = corpOnlineReportPlatformService.queryMarkMetricTrend(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return metricTrend(Optional.ofNullable(responseType.getMarkMetricTrend()).orElse(new ArrayList<>()), lang, map, sheetIndex, baseCondition.getProductType());
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        } else if (StringUtils.equalsIgnoreCase(index, "trend_compare_mc")) {
            OnlineReportBehaviorAnalysisRequest request = new OnlineReportBehaviorAnalysisRequest();
            request.setProductType(baseCondition.getProductType());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            request.setQueryBu(QueryReportBuTypeEnum.hotel);
            request.setExtData(extMap);
            OnlineReportBehaviorAnalysisResponse responseType = corpOnlineReportPlatformService.queryBehaviorAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return metricTrendMC(Optional.ofNullable(responseType.getHotelBehaviorList()).orElse(new ArrayList<>()), lang, map, sheetIndex, baseCondition.getProductType());
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        } else if (StringUtils.equalsIgnoreCase(index, "dis") || StringUtils.equalsIgnoreCase(index, "overview")) {
            OnlineReportAvgNightPriceRequest request = new OnlineReportAvgNightPriceRequest();
            request.setProductType(baseCondition.getProductType());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            // 需要查询行业数据
            extMap.put("needIndustry", "T");
            extMap.put("index", index);
            request.setExtData(extMap);
            OnlineReportAvgNightPriceResponse responseType = corpOnlineReportPlatformService.queryHtlAvgNightPriceAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                if (StringUtils.equalsIgnoreCase(index, "dis")) {
                    return hotelAvgNightPriceRange(Optional.ofNullable(responseType.getRangeList()).orElse(new ArrayList<>()), lang, map, sheetIndex, baseCondition.getProductType());
                }
                if (StringUtils.equalsIgnoreCase(index, "overview")) {
                    return hotelAvgNightPriceOverview(Optional.ofNullable(responseType.getOverviewInfo()).orElse(new AvgNightPriceOverviewInfo()), lang, map, sheetIndex,
                            baseCondition.getProductType());
                }
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        }
        return null;
    }


    private ChartExcelEntity metricTrend(List<MarkMetricInfo> markMetricInfoList, String lang, Map map, int sheetIndex, String productType) {
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(Arrays.asList(
                SharkUtils.get("Index.month", lang),
                SharkUtils.get("Index.public", lang),
                SharkUtils.get("Index.tmc", lang),
                SharkUtils.get("Index.industry", lang)));
        List data = new ArrayList();
        for (MarkMetricInfo markMetricInfo : markMetricInfoList) {
            data.add(Arrays.asList(
                    MapperUtils.trim(markMetricInfo.getDim()),
                    MapperUtils.convertDigitToZeroString(markMetricInfo.getCompanyMetric()),
                    MapperUtils.convertDigitToZeroString(markMetricInfo.getCorpMetric()),
                    MapperUtils.convertDigitToZeroString(markMetricInfo.getIndustryMetric())));
        }
        data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                Arrays.asList(SharkUtils.get("Travelanalysis.hotelstyle", lang),
                        getSheetNameByProductType(lang, productType))));
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(SharkUtils.get("Travelanalysis.averagehotel", lang));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }

    private ChartExcelEntity metricTrendMC(List<HotelBehaviorInfo> behaviorInfos, String lang, Map map, int sheetIndex, String productType) {
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(Arrays.asList(
                SharkUtils.get("Index.month", lang),
                SharkUtils.get("Index.pact", lang),
                SharkUtils.get("Index.NonAgreement", lang)));
        List data = new ArrayList();
        for (HotelBehaviorInfo behaviorInfo : behaviorInfos) {
            data.add(Arrays.asList(
                    MapperUtils.trim(behaviorInfo.getDim()),
                    MapperUtils.convertDigitToZeroString(behaviorInfo.getAvgNightPriceC()),
                    MapperUtils.convertDigitToZeroString(behaviorInfo.getAvgNightPriceM())));
        }
        data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                Arrays.asList(SharkUtils.get("Travelanalysis.hotelstyle", lang),
                        getSheetNameByProductType(lang, productType))));
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(SharkUtils.get("Travelanalysis.Compare", lang));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }

    private ChartExcelEntity hotelAvgNightPriceRange(List<AvgNightPriceRangeInfo> avgNightPriceRangeInfoList, String lang, Map map, int sheetIndex, String productType) {
        HtlAvgNightPriceRangeEnum[] avgNightPriceRangeEnums = HtlAvgNightPriceRangeEnum.values();
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(Arrays.asList(
                SharkUtils.get("APP.Behavior.AvgNightPriceInterval", lang),
                SharkUtils.get("Index.nightnum", lang),
                SharkUtils.get("Index.nightnumper", lang),
                SharkUtils.get("Save.ExcelCtripAvgTimes", lang),
                SharkUtils.get("Save.ExcelIdyAvgTimes", lang),
                SharkUtils.get("Index.costmoney", lang),
                SharkUtils.get("Index.costper", lang),
                SharkUtils.get("Save.ExcelCtripAvgTimes", lang),
                SharkUtils.get("Save.ExcelIdyAvgTimes", lang)));
        List data = new ArrayList();
        for (HtlAvgNightPriceRangeEnum rangeEnum : avgNightPriceRangeEnums) {
            AvgNightPriceRangeInfo avgNightPriceRangeInfo = avgNightPriceRangeInfoList.stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getRange(), rangeEnum.getKey().toString()))
                    .findFirst().orElse(new AvgNightPriceRangeInfo());
            data.add(Arrays.asList(
                    rangeEnum == HtlAvgNightPriceRangeEnum.RANGE13 ?
                            String.format("%s%s%s", SharkUtils.get("App.General.GreaterThan", lang), rangeEnum.getKey(), SharkUtils.get("Unit.Yuan", lang)) :
                            String.format("%s%s", rangeEnum.getKey(), SharkUtils.get("Unit.Yuan", lang)),
                    MapperUtils.convertDigitToZero(avgNightPriceRangeInfo.getTotalQuantity()),
                    MapperUtils.convertDigitToZeroString(avgNightPriceRangeInfo.getQuantityPercent()).concat(GlobalConst.PERCENT_QUOTE),
                    MapperUtils.convertDigitToZeroString(avgNightPriceRangeInfo.getCorpQuantityPercent()).concat(GlobalConst.PERCENT_QUOTE),
                    MapperUtils.convertDigitToZeroString(avgNightPriceRangeInfo.getIndustryQuantityPercent()).concat(GlobalConst.PERCENT_QUOTE),
                    MapperUtils.convertDigitToZero(avgNightPriceRangeInfo.getTotalAmount()),
                    MapperUtils.convertDigitToZeroString(avgNightPriceRangeInfo.getAmountPercent()).concat(GlobalConst.PERCENT_QUOTE),
                    MapperUtils.convertDigitToZeroString(avgNightPriceRangeInfo.getCorpAmountPercent()).concat(GlobalConst.PERCENT_QUOTE),
                    MapperUtils.convertDigitToZeroString(avgNightPriceRangeInfo.getIndustryAmountPercent()).concat(GlobalConst.PERCENT_QUOTE)));
        }
        data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                Arrays.asList(SharkUtils.get("Travelanalysis.hotelstyle", lang),
                        getSheetNameByProductType(lang, productType))));
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(SharkUtils.get("APP.Behavior.AvgNightPriceDist", lang));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }

    private ChartExcelEntity hotelAvgNightPriceOverview(AvgNightPriceOverviewInfo avgNightPriceOverviewInfo, String lang, Map map, int sheetIndex, String productType) {
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(Arrays.asList(
                SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                SharkUtils.get("DeadPriceAnalysis.AvgNightPriceTMC", lang),
                SharkUtils.get("DeadPriceAnalysis.AvgNightPriceIndustry", lang)));
        List data = new ArrayList();
        data.add(Arrays.asList(
                MapperUtils.convertDigitToZero(avgNightPriceOverviewInfo.getCompanyAvgNightPrice()),
                MapperUtils.convertDigitToZero(avgNightPriceOverviewInfo.getCorpAvgNightPrice()),
                MapperUtils.convertDigitToZero(avgNightPriceOverviewInfo.getIndustryAvgNightPrice())));
        data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                Arrays.asList(SharkUtils.get("Travelanalysis.hotelstyle", lang),
                        getSheetNameByProductType(lang, productType))));
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(SharkUtils.get("Tab.Overview", lang));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }

    enum HtlAvgNightPriceRangeEnum {
        RANGE1("0-100", "0-100"),
        RANGE2("100-200", "100-200"),
        RANGE3("200-300", "200-300"),
        RANGE4("300-400", "300-400"),
        RANGE5("400-500", "400-500"),
        RANGE6("500-600", "500-600"),
        RANGE7("600-700", "600-700"),
        RANGE8("700-800", "700-800"),
        RANGE9("800-900", "800-900"),
        RANGE10("900-1000", "900-1000"),
        RANGE11("900-1500", "900-1500"),
        RANGE12("1500-2000", "1500-2000"),
        RANGE13("2000", "2000"),
        ;
        String key;
        String sharkKey;

        HtlAvgNightPriceRangeEnum(String s, String m) {
            this.key = s;
            this.sharkKey = m;
        }

        public String getKey() {
            return key;
        }

        public String getSharkKey() {
            return sharkKey;
        }
    }
}
