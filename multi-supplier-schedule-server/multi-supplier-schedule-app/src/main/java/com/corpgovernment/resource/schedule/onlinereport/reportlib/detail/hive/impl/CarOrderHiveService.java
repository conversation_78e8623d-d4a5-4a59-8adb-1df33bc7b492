package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.hive.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.common.BaseDataUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.CarEntity;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveFilter;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.SrDao;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.CarOrderDetailService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.SrSwitchConfig;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.hive.AbstractOrderDetailHiveService;
import onlinereport.enums.OrderTypeEnum;
import onlinereport.enums.reportlib.CarOrderDetailEnum;
import onlinereport.enums.reportlib.OrderDetailEnumerable;
import onlinereport.enums.reportlib.uid.CarUidOrderDetailEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst.FOUR_DIGIT_NUM;


/**
 * Auther:abguo
 * Date:2019/8/21
 * Description:
 */
@Service
public class CarOrderHiveService extends AbstractOrderDetailHiveService {

    @Autowired
    private HiveDao hiveDao;

    @Autowired
    private SrSwitchConfig srSwitchConfig;

    @Autowired
    private SrDao srDao;

    @Override
    public OrderDetailEnumerable[] getEnum(String reportType) {
        if (StringUtils.equalsIgnoreCase(reportType, "C_UID_DETAIL")) {
            return CarUidOrderDetailEnum.values();
        } else {
            return CarOrderDetailEnum.values();
        }
    }

    public List<Map<String, Object>> queryOrderDetail(HiveFilter hiveFilter, String lang, String reportType) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<CarEntity> carEntityList = null;
        if (srSwitchConfig.isOpenSrSwitch(hiveFilter.getUid(), "car")) {
            carEntityList = srDao.searchCarOrderdetail(hiveFilter);
        } else {
            carEntityList = hiveDao.searchCarOrderdetail(hiveFilter);
        }
        if (CollectionUtils.isEmpty(carEntityList)) {
            return result;
        }
        if (StringUtils.equalsIgnoreCase(reportType, "C_UID_DETAIL")) {
            return queryCarUidOrderDetail(carEntityList, lang);
        } else {
            return queryCarOrderDetail(carEntityList, lang);
        }
    }

    /**
     * 订单明细
     *
     * @param carEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> queryCarUidOrderDetail(List<CarEntity> carEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(carEntityList)) {
            return result;
        }
        for (CarEntity order : carEntityList) {
            Map<String, Object> map = new HashMap<>();
            map.put(CarUidOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToString(order.getOrder_id()));
            map.put(CarUidOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrder_type(), MapperUtils.trim(order.getOrder_status())));
            map.put(CarUidOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
            map.put(CarUidOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorp_corporation()));
            map.put(CarUidOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
            map.put(CarUidOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToString(order.getAccount_id()));
            map.put(CarUidOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(CarUidOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
            map.put(CarUidOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRank_name()));
            map.put(CarUidOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCost_center1()));
            map.put(CarUidOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCost_center2()));
            map.put(CarUidOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCost_center3()));
            map.put(CarUidOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCost_center4()));
            map.put(CarUidOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCost_center5()));
            map.put(CarUidOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCost_center6()));
            map.put(CarUidOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(CarUidOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(CarUidOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(CarUidOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
            map.put(CarUidOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
            map.put(CarUidOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
            map.put(CarUidOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
            map.put(CarUidOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
            map.put(CarUidOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
            map.put(CarUidOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(CarUidOrderDetailEnum.ORDERTYPE.getCode(), convertOrderType(order.getOrder_type(), order.getSub_product_line()));
            map.put(CarUidOrderDetailEnum.PASSENGERNAME.getCode(), MapperUtils.trim(order.getPassenger_name()));
            map.put(CarUidOrderDetailEnum.PERSONS.getCode(), MapperUtils.convertDigitToString(order.getPersons()));
            map.put(CarUidOrderDetailEnum.DELREALPAY.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));
            map.put(CarUidOrderDetailEnum.BASICFEE.getCode(), DigitBaseUtils.formatDigit(order.getBasic_fee()));
            map.put(CarUidOrderDetailEnum.DEPARTYRECITYNAME.getCode(), MapperUtils.trim(order.getDeparture_city_name()));
            map.put(CarUidOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.trim(order.getArrival_city_name()));
            map.put(CarUidOrderDetailEnum.STARTADDRESSDES.getCode(), MapperUtils.trim(order.getStart_address_des()));
            map.put(CarUidOrderDetailEnum.ENDADDRESSDES.getCode(), MapperUtils.trim(order.getEnd_address_des()));
            map.put(CarUidOrderDetailEnum.NORMALDISTANCE.getCode(), DigitBaseUtils.formatDigit(order.getNormal_distance()));
            map.put(CarUidOrderDetailEnum.USEDURATION.getCode(), MapperUtils.trim(order.getUse_duration()));
            map.put(CarUidOrderDetailEnum.VEHICLENAME.getCode(), MapperUtils.trim(order.getVehicle_name()));
            map.put(CarUidOrderDetailEnum.ACTUALSTARTSERVICETIME.getCode(), MapperUtils.trim(order.getActual_startservicetime()));
            map.put(CarUidOrderDetailEnum.ACTUALDRIVEDURATION.getCode(), MapperUtils.trim(order.getActual_driveduration()));
            map.put(CarUidOrderDetailEnum.BOOKINGTYPE_DES.getCode(), MapperUtils.trim(order.getBookingtype_des()));
            map.put(CarUidOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
            map.put(CarUidOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccount_code()));
            result.add(map);
        }
        return result;
    }

    /**
     * 订单明细
     *
     * @param carEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> queryCarOrderDetail(List<CarEntity> carEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(carEntityList)) {
            return result;
        }
        Map yesOrNotMap = BaseDataUtils.getYesOrNoMap(lang);
        for (CarEntity order : carEntityList) {
            Map<String, Object> map = new HashMap<>();
            map.put(CarOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToString(order.getOrder_id()));
            map.put(CarOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrder_type(), MapperUtils.trim(order.getOrder_status())));
            map.put(CarOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
//            map.put(CarOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorp_corporation()));
            map.put(CarOrderDetailEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorp_name()));
//            map.put(CarOrderDetailEnum.COMPANYGROUP.getCode(), MapperUtils.trim(order.getCompanygroup()));
//            map.put(CarOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
//            map.put(CarOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToString(order.getAccount_id()));
//            map.put(CarOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccount_code()));
//            map.put(CarOrderDetailEnum.ACCOUNTNAME.getCode(), MapperUtils.trim(order.getAccount_name()));
//            map.put(CarOrderDetailEnum.SUBACCOUNTID.getCode(), MapperUtils.convertDigitToString(order.getSub_account_id()));
//            map.put(CarOrderDetailEnum.SUBACCOUNTCODE.getCode(), MapperUtils.trim(order.getSub_account_code()));
//            map.put(CarOrderDetailEnum.SUBACCOUNTNAME.getCode(), MapperUtils.trim(order.getSub_account_name()));
//            map.put(CarOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(CarOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
            map.put(CarOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
//            map.put(CarOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRank_name()));
            map.put(CarOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCost_center1()));
//            map.put(CarOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCost_center2()));
//            map.put(CarOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCost_center3()));
//            map.put(CarOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCost_center4()));
//            map.put(CarOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCost_center5()));
//            map.put(CarOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCost_center6()));
            map.put(CarOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
//            map.put(CarOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
//            map.put(CarOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
//            map.put(CarOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
//            map.put(CarOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
//            map.put(CarOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
//            map.put(CarOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
//            map.put(CarOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
//            map.put(CarOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
//            map.put(CarOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(CarOrderDetailEnum.ISONLINE.getCode(), MapperUtils.convertBookTypeSingle(order.getIs_online()));
//            map.put(CarOrderDetailEnum.FEETYPE.getCode(), MapperUtils.trim(order.getFee_type()));
            map.put(CarOrderDetailEnum.PREPAYTYPE.getCode(), CarOrderDetailService.convertPrepayType(MapperUtils.trim(order.getPrepay_type())));
//            map.put(CarOrderDetailEnum.ACBPREPAYTYPE.getCode(), MapperUtils.trim(order.getAcb_prepay_type()));
//            map.put(CarOrderDetailEnum.ISBOSS.getCode(), MapperUtils.trim(order.getBosstype()));
            map.put(CarOrderDetailEnum.JOURNEYNO.getCode(), MapperUtils.trim(order.getJourney_no()));
            map.put(CarOrderDetailEnum.TRIPID.getCode(), MapperUtils.convertDigitToDash(order.getTrip_id()));
//            map.put(CarOrderDetailEnum.JOUNARYREASON.getCode(), MapperUtils.trim(order.getJourney_reason()));
//            map.put(CarOrderDetailEnum.PROJECT.getCode(), MapperUtils.trim(order.getProject()));
//            map.put(CarOrderDetailEnum.VERBALAUTHORIZE.getCode(), MapperUtils.convertTorF(order.getVerbal_authorize(), yesOrNotMap));
//            map.put(CarOrderDetailEnum.CONFIRMPERSON.getCode(), MapperUtils.trim(order.getConfirm_person()));
//            map.put(CarOrderDetailEnum.CONFIRMPTYPE.getCode(), MapperUtils.trim(order.getConfirm_type()));
//            map.put(CarOrderDetailEnum.CONFIRMPERSON2.getCode(), MapperUtils.trim(order.getConfirm_person2()));
//            map.put(CarOrderDetailEnum.CONFIRMPTYPE2.getCode(), MapperUtils.trim(order.getConfirm_type2()));
            map.put(CarOrderDetailEnum.GROUPMONTH.getCode(), MapperUtils.convertDigitToString(order.getGroup_month()));
//            map.put(CarOrderDetailEnum.USERDEFINEDRID.getCode(), MapperUtils.trim(order.getUserdefined_rid()));
//            map.put(CarOrderDetailEnum.USERDEFINEDRC.getCode(), MapperUtils.trim(order.getUserdefined_rc()));
            /////////////////////////////////////////////////////////////////////////////////////////////////////
            map.put(CarOrderDetailEnum.ORDERTYPE.getCode(), convertOrderType(order.getOrder_type(), order.getSub_product_line()));
            map.put(CarOrderDetailEnum.PAYMENTSTATUS.getCode(), CarOrderDetailService.convertPaymentStatus(order.getPayment_status()));
            map.put(CarOrderDetailEnum.PASSENGERNAME.getCode(), MapperUtils.trim(order.getPassenger_name()));
            map.put(CarOrderDetailEnum.PERSONS.getCode(), MapperUtils.convertDigitToString(order.getPersons()));
            map.put(CarOrderDetailEnum.CONTACTNAME.getCode(), MapperUtils.trim(order.getContact_name()));
            map.put(CarOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getCnt_order()));
            map.put(CarOrderDetailEnum.ESTIMATEAMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getEstimate_amount()));
            map.put(CarOrderDetailEnum.DELREALPAY.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));
            map.put(CarOrderDetailEnum.BASICFEE.getCode(), DigitBaseUtils.formatDigit(order.getBasic_fee()));
            map.put(CarOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getService_fee()));
//            map.put(CarOrderDetailEnum.REFUNDAMOUNT.getCode(), DigitBaseUtils.formatDigit((order.getRefund_amount())));
//            map.put(CarOrderDetailEnum.OCURRENCY.getCode(), MapperUtils.trim(order.getO_currency()));
//            map.put(CarOrderDetailEnum.OEXCHANGERATE.getCode(), DigitBaseUtils.formatDigit(order.getO_exchangerate(), FOUR_DIGIT_NUM));
            map.put(CarOrderDetailEnum.DEPARTYRECITYNAME.getCode(), MapperUtils.trim(order.getDeparture_city_name()));
            map.put(CarOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.trim(order.getArrival_city_name()));
            map.put(CarOrderDetailEnum.ADDRESS.getCode(), MapperUtils.trim(order.getAddress()));
//            map.put(CarOrderDetailEnum.FIXEDLOCATIONNAME.getCode(), MapperUtils.trim(order.getFixed_location_name()));
            map.put(CarOrderDetailEnum.FLIGHTTRAINNUM.getCode(), MapperUtils.trim(order.getFlight_train_num()));
//            map.put(CarOrderDetailEnum.PATTERNTYPE.getCode(), MapperUtils.convertPatternType(order.getPattern_type(), lang));
            map.put(CarOrderDetailEnum.STARTADDRESSDES.getCode(), MapperUtils.trim(order.getStart_address_des()));
            map.put(CarOrderDetailEnum.TAKETIME.getCode(), MapperUtils.trim(order.getStart_time()));
            map.put(CarOrderDetailEnum.ENDADDRESSDES.getCode(), MapperUtils.trim(order.getEnd_address_des()));
            map.put(CarOrderDetailEnum.ENDTIME.getCode(), MapperUtils.trim(order.getEnd_time()));
            map.put(CarOrderDetailEnum.ESTIMATEDISTANCE.getCode(), MapperUtils.convertDigitToString(order.getEstimate_distance()));
            map.put(CarOrderDetailEnum.NORMALDISTANCE.getCode(), DigitBaseUtils.formatDigit(order.getNormal_distance()));
//            map.put(CarOrderDetailEnum.USEDURATION.getCode(), MapperUtils.trim(order.getUse_duration()));
//            map.put(CarOrderDetailEnum.USETYPE.getCode(), MapperUtils.trim(order.getUse_type()));
            map.put(CarOrderDetailEnum.VEHICLENAME.getCode(), MapperUtils.trim(order.getVehicle_name()));
            map.put(CarOrderDetailEnum.VENDORNAME.getCode(), MapperUtils.trim(order.getVendor_name()));
//            map.put(CarOrderDetailEnum.SCENENAME.getCode(), MapperUtils.trim(order.getScenename()));
//            map.put(CarOrderDetailEnum.WORKCITY.getCode(), MapperUtils.trim(order.getWork_city()));
//            map.put(CarOrderDetailEnum.VENDORORDERID.getCode(), MapperUtils.trim(order.getVendororderid()));
            map.put(CarOrderDetailEnum.ACTUALSTARTSERVICETIME.getCode(), MapperUtils.trim(order.getActual_startservicetime()));
            map.put(CarOrderDetailEnum.ACTUALDRIVEDURATION.getCode(), MapperUtils.trim(order.getActual_driveduration()));
//            map.put(CarOrderDetailEnum.CANCELFEERATE.getCode(), DigitBaseUtils.formatDigit(order.getCancelfee_rate()));
//            map.put(CarOrderDetailEnum.PACKAGENAME.getCode(), MapperUtils.trim(order.getPackage_name()));
//            map.put(CarOrderDetailEnum.ACTUALSTARTADRESS.getCode(), MapperUtils.trim(order.getActual_start_address()));
//            map.put(CarOrderDetailEnum.ACTUALENDADRESS.getCode(), MapperUtils.trim(order.getActual_end_address()));
//            map.put(CarOrderDetailEnum.AUDITORID.getCode(), MapperUtils.trim(order.getAuditorid()));
//            map.put(CarOrderDetailEnum.AUDITORID2.getCode(), MapperUtils.trim(order.getAuditorid2()));
//            map.put(CarOrderDetailEnum.ISABNORMAL.getCode(), MapperUtils.convertTorF(order.getIs_abnormal(), yesOrNotMap));
//            map.put(CarOrderDetailEnum.ISABNORMALCONFIRM.getCode(), MapperUtils.convertTorF(order.getIs_abnormal_userconfirm(), yesOrNotMap));
//            map.put(CarOrderDetailEnum.ISMIXPAYMENT.getCode(), MapperUtils.trim(order.getIsmixpayment()));
//            map.put(CarOrderDetailEnum.SETTLEMENTPERSONAMT.getCode(), DigitBaseUtils.formatDigit(order.getSettlementpersonamt()));
//            map.put(CarOrderDetailEnum.SETTLEMENTACCNTAMT.getCode(), DigitBaseUtils.formatDigit(order.getSettlementaccntamt()));
            map.put(CarOrderDetailEnum.BOOKINGTYPE_DES.getCode(), MapperUtils.trim(order.getBookingtype_des()));
//            map.put(CarOrderDetailEnum.DEFINDFLAG.getCode(), MapperUtils.trim(order.getDefineflag()));
//            map.put(CarOrderDetailEnum.DEFINDFLAG2.getCode(), MapperUtils.trim(order.getDefineflag2()));
//            map.put(CarOrderDetailEnum.SERVICE_TIME.getCode(), MapperUtils.trim(order.getService_time()));
//            map.put(CarOrderDetailEnum.ONLINE_HIT_ABNORMAL_RULE_DESC.getCode(), MapperUtils.trim(order.getOnline_hit_abnormal_rule_desc()));
//            map.put(CarOrderDetailEnum.CARCARBONS.getCode(), MapperUtils.convertDigitToString(convertUnit(order.getCarbon_emission())));
//            map.put(CarOrderDetailEnum.IS_GREEN_CAR.getCode(), MapperUtils.convertTorF(order.getIsgreencar(), yesOrNotMap));
//            map.put(CarOrderDetailEnum.STDINDUSTRY1.getCode(), MapperUtils.trim(order.getStd_industry1()));
//            map.put(CarOrderDetailEnum.STDINDUSTRY2.getCode(), MapperUtils.trim(order.getStd_industry2()));
            result.add(map);
        }
        return result;
    }

    private Double convertUnit(Integer carbonEmission) {
        if (carbonEmission == null || carbonEmission == 0) {
            return 0D;
        }
        return DigitBaseUtils.divide(carbonEmission, 1000).doubleValue();
    }

    private String convertOrderType(Integer orderType, String subProductLine) {
        if (orderType == null) {
            return GlobalConst.STRING_EMPTY;
        }
        if (OrderTypeEnum.D.getOrderType().equals(orderType)) {
            return OrderTypeEnum.D.getDesCn();
        } else if (OrderTypeEnum.S.getOrderType().equals(orderType)) {
            return OrderTypeEnum.S.getDesCn();
        } else if (OrderTypeEnum.B.getOrderType().equals(orderType)) {
            return OrderTypeEnum.B.getDesCn();
        } else if (OrderTypeEnum.R.getOrderType().equals(orderType)) {
            return OrderTypeEnum.R.getDesCn();
        } else if (OrderTypeEnum.P.getOrderType().equals(orderType)) {
            return OrderTypeEnum.P.getDesCn();
        } else if (OrderTypeEnum.U.getOrderType().equals(orderType)) {
            if (StringUtils.equalsIgnoreCase(subProductLine, "1")) {
                return "国内打车";
            } else if (StringUtils.equalsIgnoreCase(subProductLine, "CAR_TAXI_INTL")) {
                return "国内打车";
            } else {
                return OrderTypeEnum.U.getDesCn();
            }
        }
        return MapperUtils.convertDigitToString(orderType);
    }

    private String convertOrderStatus(int orderType, String orderStatus) {

        if (orderType == OrderTypeEnum.U.getValue()) {
            return CarOrderDetailService.convertOrderStatusU(orderStatus);
        } else if (orderType == OrderTypeEnum.B.getValue()) {
            return CarOrderDetailService.convertOrderStatusB(orderStatus);
        } else if (orderType == OrderTypeEnum.D.getValue()) {
            return CarOrderDetailService.convertOrderStatus2(orderStatus);
        } else if (orderType == OrderTypeEnum.S.getValue() || orderType == OrderTypeEnum.R.getValue()) {
            return CarOrderDetailService.convertOrderStatus1(orderStatus);
        }
        return orderStatus;
    }


    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.car;
    }
}
