package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FlightBehaviorInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportRebookTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportRebookTrendResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RebookInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RebookPercentInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RebookTrendInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022-11-18 14:53
 * @desc
 */
@Service
public class RebookBehaviorAnalysisAdaptor extends BaseReportDataAdaptor<Object> {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private RebookBehaviorAnalysisExcerlService rebookBehaviorAnalysisExcerlService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        String index = baseCondition.getIndex();
        if (StringUtils.equalsIgnoreCase(RebookIndexEnum.REbook_TYPE_DIS.toString(), index)) {
            OnlineReportBehaviorAnalysisRequest request = new OnlineReportBehaviorAnalysisRequest();
            request.setProductType(baseCondition.getProductType());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            extMap.put("dim", index);
            request.setExtData(extMap);
            OnlineReportBehaviorAnalysisResponse responseType = corpOnlineReportPlatformService.queryBehaviorAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return rebookDis(Optional.ofNullable(responseType.getFlightBehaviorList()).orElse(new ArrayList<>()), lang);
            } else {
                erroThrow(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        } else {
            OnlineReportRebookTrendRequest rebookTrendRequest = new OnlineReportRebookTrendRequest();
            rebookTrendRequest.setProductType(baseCondition.getProductType());
            rebookTrendRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            // 和app的退区分
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            extMap.put("from", "pc");
            extMap.put("index", index);
            rebookTrendRequest.setExtData(extMap);
            if (StringUtils.equalsIgnoreCase(RebookIndexEnum.ALL.toString(), index)) {
                rebookTrendRequest.setQueryBu(QueryReportBuTypeEnum.overview);
            } else {
                rebookTrendRequest.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
            }
            OnlineReportRebookTrendResponse responseType = corpOnlineReportPlatformService.queryRebookTrendAnalysis(rebookTrendRequest);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                if (StringUtils.equalsIgnoreCase(RebookIndexEnum.ALL.toString(), baseCondition.getIndex())) {
                    return rebookALL(Optional.ofNullable(responseType.getRebookPercentInfo()).orElse(new RebookPercentInfo()));
                } else if (StringUtils.equalsIgnoreCase(RebookIndexEnum.overview.toString(), index)) {
                    return rebookOverview(Optional.ofNullable(responseType.getRebookInfo()).orElse(new RebookInfo()));
                } else if (StringUtils.equalsIgnoreCase(RebookIndexEnum.trend.toString(), index)) {
                    return rebook(Optional.ofNullable(responseType.getRebookInfo()).orElse(new RebookInfo()), lang);
                }
            } else {
                erroThrow(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        }

        return null;
    }

    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        return rebookBehaviorAnalysisExcerlService.buildExcel(userPermissionsBo, baseCondition);
    }

    @Override
    public String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang) {
        String fileName_format = "%s_%s_%s_%s";
        String middle = SharkUtils.get("Travelanalysis.changeanalysis", lang);
        String prefix = SharkUtils.get("Catalog.BehaviorAnalysis", lang);
        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
        return String.format(fileName_format, prefix, middle, uid, str);
    }

    private Map rebookALL(RebookPercentInfo rebookPercentInfo) {
        return ImmutableMap.builder()
                .put("flightRebookPercent", MapperUtils.convertDigitToZero(rebookPercentInfo.getFlightRebookPercent()))
                .put("trainRebookQuantity", MapperUtils.convertDigitToZero(rebookPercentInfo.getTrainRebookPercent())).build();
    }

    private Map rebook(RebookInfo rebookInfo, String lang) {
        List trendLegends = new ArrayList();
        Map trendLegendQuantity = ImmutableMap.builder()
                .put("name", SharkUtils.get("App.index.num", lang))
                .put("key", "rebookQuantity")
                .put("percentage", "rebookPercent").build();
        trendLegends.add(trendLegendQuantity);

        List trendDataList = new ArrayList();
        List<RebookTrendInfo> rebookTrendInfoList = Optional.ofNullable(rebookInfo.getRebookTrendList()).orElse(new ArrayList<>());
        for (RebookTrendInfo trendInfo : rebookTrendInfoList) {
            Map trendData = ImmutableMap.builder()
                    .put("rebookQuantity", MapperUtils.convertDigitToZero(trendInfo.getTotalRebookQuantity()))
                    .put("rebookPercent", MapperUtils.convertDigitToZero(trendInfo.getCompanyRebookPercent())).build();
            trendDataList.add(ImmutableMap.builder()
                    .put("axis", trendInfo.getPoint())
                    .put("data", trendData).build());
        }
        return ImmutableMap.builder()
                .put("legends", trendLegends)
                .put("data", trendDataList).build();
    }

    private Object rebookDis(List<FlightBehaviorInfo> list, String lang) {
        List trendDataList = new ArrayList();
        FlightRebookTypeEnum[] flightRebookTypeEnums = FlightRebookTypeEnum.values();
        for (FlightRebookTypeEnum flightRebookTypeEnum : flightRebookTypeEnums) {
            FlightBehaviorInfo trendInfo = list.stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), flightRebookTypeEnum.toString()))
                    .findFirst().orElse(new FlightBehaviorInfo());
            Map trendData = ImmutableMap.builder()
                    .put("rebookQuantity", MapperUtils.convertDigitToZero(trendInfo.getTotalRebookQuantity()))
                    .put("rebookPercent", MapperUtils.convertDigitToZero(trendInfo.getRebookQuantityPercent())).build();
            trendDataList.add(ImmutableMap.builder()
                    .put("axis", SharkUtils.get(flightRebookTypeEnum.getSharkKey(), lang))
                    .put("data", trendData).build());
        }
        return trendDataList;
    }

    private Map rebookOverview(RebookInfo rebookInfo) {
        Map percentData = new HashMap();
        percentData.put("rebookPercent", MapperUtils.convertDigitToZero(rebookInfo.getRebookQuantityPercent()));
        percentData.put("rebookQuanttiy", MapperUtils.convertDigitToZero(rebookInfo.getTotalRebookQuantity()));
        percentData.put("loss", MapperUtils.convertDigitToZero(rebookInfo.getTotalRebookLoss()));
        percentData.put("corpRebookPercent", MapperUtils.convertDigitToZero(rebookInfo.getCorpRebooktktPercent()));
        percentData.put("industryRebookPercent", MapperUtils.convertDigitToZero(rebookInfo.getIndustryRebooktktPercent()));
        return percentData;
    }

    /**
     * ALL:改签占比概览
     */
    enum RebookIndexEnum {
        ALL, overview, trend, REbook_TYPE_DIS
    }

    /**
     * VOLUNTARY:自愿,offline:offline
     */
    enum FlightRebookTypeEnum {
        VOLUNTARY("Travelanalysis.Reson1"),
        INVOLUNTARY("Travelanalysis.Reson2"),
        SPECIAL("Travelanalysis.Reson3"),
        OTHER("Index.others"),
        ;
        private String sharkKey;

        FlightRebookTypeEnum(String s) {
            this.sharkKey = s;
        }

        public String getSharkKey() {
            return sharkKey;
        }
    }
}
