package com.corpgovernment.resource.schedule.pricecomparison;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.corpgovernment.api.flight.soa.FlightBookingClient;
import com.corpgovernment.api.flight.vo.request.FlightPriceCompareRequest;
import com.corpgovernment.api.flight.vo.response.FlightPriceCompareResponse;
import com.corpgovernment.api.hotel.booking.core.PriceComparisonReqVo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.pricecomparison.FlightPriceCompareModel;
import com.corpgovernment.resource.schedule.domain.pricecomparison.enums.ComparePriceFlightTypeEnum;
import com.corpgovernment.resource.schedule.domain.pricecomparison.enums.ComparePriceProductTypeEnum;
import com.corpgovernment.resource.schedule.domain.pricecomparison.enums.TaskStatusComparisonEnum;
import com.corpgovernment.resource.schedule.domain.pricecomparison.model.QueryXrayComparePriceUploadDateResponseType;
import com.corpgovernment.resource.schedule.pricecomparison.mapper.MbJobSupplierCompareTaskMapper;
import com.corpgovernment.resource.schedule.pricecomparison.mysql.entity.MbJobSupplierCompareTask;
import com.corpgovernment.resource.schedule.pricecomparison.soa.ApprovalCityCompatibleLoader;
import com.corpgovernment.resource.schedule.pricecomparison.soa.HotelSoaLoader;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class FlightHotelPriceComparisonService {
    private static final int RESPONSECODE_SUCCESS = 2000; // 成功返回标志
    private static final int BATCH_INSERT_SIZE = 100;
    private static final int PAGE_SIZE = 100;
    @Autowired
    private MbJobSupplierCompareTaskMapper mbJobSupplierCompareTaskMapper;
    // 热门航线100查询人的配置 "{ \"tenantId\": \"123\", \"uid\": \"abc\", \"corpId\": \"456\", \"flightIintlClazzList\": [\"Y\", \"C\"] }";
    @Value("${flight.Top.Compare.Config: }")
    private String flightTopCompareConfig;
    @Autowired
    private HotelSoaLoader hotelSoaLoader;
    @Autowired
    private FlightBookingClient flightBookingClient;
    @Autowired
    ApprovalCityCompatibleLoader approvalCityCompatibleLoader;

    public void flightHotelPriceComparison(String param) {
        log.info("获取机票和酒店比价任务结束执行-开始");

        QueryXrayComparePriceUploadDateResponseType queryXrayComparePriceUploadDateResponseType = approvalCityCompatibleLoader.queryXrayComparePriceUploadDate(ComparePriceProductTypeEnum.HOTEL.getCode());
        log.info("酒店比价,queryXrayComparePriceUploadDateResponseType:{}", JsonUtils.toJsonString(queryXrayComparePriceUploadDateResponseType));
        handlePriceComparisonDate(queryXrayComparePriceUploadDateResponseType);

        queryXrayComparePriceUploadDateResponseType = approvalCityCompatibleLoader.queryXrayComparePriceUploadDate(ComparePriceProductTypeEnum.FLIGHT.getCode());
        log.info("机票比价,queryXrayComparePriceUploadDateResponseType:{}", JsonUtils.toJsonString(queryXrayComparePriceUploadDateResponseType));
        handlePriceComparisonDate(queryXrayComparePriceUploadDateResponseType);

        log.info("获取机票和酒店比价任务结束执行-结束");
    }

    /**
     * 处理机票和酒店比价数据的日期信息
     *
     */
    private void handlePriceComparisonDate(QueryXrayComparePriceUploadDateResponseType responseType) {
        if (responseType == null ||
                RESPONSECODE_SUCCESS != responseType.getResponseCode() ||
                CollectionUtils.isEmpty(responseType.getRecords())) {
            log.warn("响应数据异常: responseType={}", JsonUtils.toJsonString(responseType));
            return;
        }

        List<MbJobSupplierCompareTask> taskList = new ArrayList<>(BATCH_INSERT_SIZE);

        responseType.getRecords().forEach(recordsDTO -> {
            // 判断当前任务是否已经拉取
            String taskId = recordsDTO.getTaskId();
            List<MbJobSupplierCompareTask>  taskExistList= mbJobSupplierCompareTaskMapper.selectByTaskId(taskId);
            if(CollectionUtils.isNotEmpty(taskExistList)){
                log.info("任务已经拉取过, taskId:{}",taskId);
                return;
            }
            MbJobSupplierCompareTask baseTask = createBaseTask(recordsDTO);
            if (baseTask == null){
                return;
            }

            Optional.ofNullable(recordsDTO.getProductType())
                    .ifPresent(productType -> {
                        if (ComparePriceProductTypeEnum.HOTEL.getCode().equals(productType)) {
                            processHotelData(recordsDTO, baseTask, taskList);
                        } else if (ComparePriceProductTypeEnum.FLIGHT.getCode().equals(productType)) {
                            processFlightData(recordsDTO, baseTask, taskList);
                        }
                    });
        });

        if (CollectionUtils.isNotEmpty(taskList)) {
            batchInsertTasks(taskList);
        }
    }

    /**
     * 创建基础比较任务
     *
     * @param recordsDTO 记录数据传输对象
     * @return 返回创建的基础比较任务对象，如果创建失败则返回null
     */
    private MbJobSupplierCompareTask createBaseTask(QueryXrayComparePriceUploadDateResponseType.RecordsDTO recordsDTO) {
        try {
            MbJobSupplierCompareTask task = new MbJobSupplierCompareTask();
            task.setTaskId(recordsDTO.getTaskId());
            task.setTaskType(ComparePriceProductTypeEnum.getNameByCode(recordsDTO.getProductType()));
            task.setTaskStatus(recordsDTO.getTaskStatus());
            task.setCreateEid(recordsDTO.getCreateEid());
            task.setCreateName(recordsDTO.getCreateName());
            task.setRequestid(String.valueOf(UUID.randomUUID()));
            task.setDatachangeCreatetime(new Date());
            task.setDatachangeLasttime(new Date());
            return task;
        } catch (Exception e) {
            log.error("基础任务创建失败, recordsDTO: {}", recordsDTO, e);
            return null;
        }
    }

    private void processHotelData(QueryXrayComparePriceUploadDateResponseType.RecordsDTO recordsDTO, MbJobSupplierCompareTask baseTask, List<MbJobSupplierCompareTask> taskList) {
        Optional.ofNullable(recordsDTO.getHotelData())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(hotelDataList -> {
                    hotelDataList.forEach(hotelData -> {
                        try {
                            MbJobSupplierCompareTask task = new MbJobSupplierCompareTask();
                            BeanUtil.copyProperties(baseTask, task);
                            task.setCompareData(JsonUtils.toJsonString(hotelData));
                            taskList.add(task);
                        } catch (Exception e) {
                            log.error("酒店数据处理失败, hotelData: {}", hotelData, e);
                        }
                        checkBatchInsert(taskList);
                    });
                });
    }

    private void processFlightData(QueryXrayComparePriceUploadDateResponseType.RecordsDTO recordsDTO, MbJobSupplierCompareTask baseTask, List<MbJobSupplierCompareTask> taskList) {
        List<String> cabinList = new ArrayList<>();
        cabinList.add("Y");
        cabinList.add("C");
        cabinList.add("F");
        cabinList.add("W");
        // 获取租户配置
        if(StringUtils.isBlank(flightTopCompareConfig)) {
            log.error("FlightTopCompareService flightTopCompareConfig is empty");
            return;
        }
        Optional.ofNullable(recordsDTO.getFlightData())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(flightDataList -> {
                    flightDataList.forEach(flightData -> {
                        try {
                            // 处理机票待执行数据
                            if(ComparePriceFlightTypeEnum.FLIGHT_INTL.getCode().equals(flightData.getFlightClass())) {
                                for (String cabin : cabinList) {
                                    // 创建任务
                                    MbJobSupplierCompareTask task = convertFlightDataToTask(flightData, baseTask, cabin);
                                    taskList.add(task);
                                }
                            } else if(ComparePriceFlightTypeEnum.FLIGHT.getCode().equals(flightData.getFlightClass())) {
                                // 创建任务
                                MbJobSupplierCompareTask task = convertFlightDataToTask(flightData, baseTask, null);
                                taskList.add(task);
                            }
                        } catch (Exception e) {
                            log.error("机票数据处理失败, flightData: {}", flightData, e);
                        }
                        checkBatchInsert(taskList);
                    });
                });
    }

    private MbJobSupplierCompareTask convertFlightDataToTask(QueryXrayComparePriceUploadDateResponseType.RecordsDTO.FlightDataDTO flightData,
                                                             MbJobSupplierCompareTask baseTask, String cabin) {
        MbJobSupplierCompareTask task = new MbJobSupplierCompareTask();
        task.setTaskId(baseTask.getTaskId());
        task.setTaskType(ComparePriceProductTypeEnum.FLIGHT.getName());
        task.setTaskStatus(TaskStatusComparisonEnum.READY.getCode());
        task.setCreateEid(baseTask.getCreateEid());
        task.setCreateName(baseTask.getCreateName());
        task.setRequestid(String.valueOf(UUID.randomUUID()));
        FlightPriceCompareModel compareModel = new FlightPriceCompareModel();
        compareModel.setFlightType(flightData.getFlightClass());
        compareModel.setDepartCityCode(flightData.getDepartureCityCode());
        compareModel.setArriveCityCode(flightData.getArrivalCityCode());
        compareModel.setClazzCode(cabin);
        compareModel.setDepartDate(flightData.getDepartureDate());
        compareModel.setUid(flightData.getCompareUid());
        compareModel.setCorpId(flightData.getCorpId());
        task.setCompareData(JSONObject.toJSONString(compareModel));
        return task;
    }

    private void checkBatchInsert(List<MbJobSupplierCompareTask> taskList) {
        if (taskList.size() >= BATCH_INSERT_SIZE) {
            batchInsertTasks(taskList);
        }
    }

    private synchronized void batchInsertTasks(List<MbJobSupplierCompareTask> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }

        try {
            mbJobSupplierCompareTaskMapper.insertList(new ArrayList<>(taskList));
            log.info("批量插入成功, batchInsertTasks,taskList:{}", JsonUtils.toJsonString(taskList));
            taskList.clear();
        } catch (Exception e) {
            log.error("批量插入失败, size: {}", taskList.size(), e);
        }
    }

    /**
     * 执行任务
     * @param comparePriceProductType 任务参数
     */
    public Boolean executeFlightHotelCompareTask(String comparePriceProductType) {
        // 1. 查询1条待执行的任务
        List<MbJobSupplierCompareTask> mbJobSupplierCompareTaskList =
                mbJobSupplierCompareTaskMapper.selectByTaskTypeAndDatachangeCreatetime(0,1, comparePriceProductType,
                        null,null, TaskStatusComparisonEnum.READY.getCode());
        log.info("查询1条待执行的任务mbJobSupplierCompareTaskList: {}", JsonUtils.toJsonString(mbJobSupplierCompareTaskList));
        if (CollectionUtils.isEmpty(mbJobSupplierCompareTaskList)) {
            log.info("没有待执行的任务");
            return false;
        }

        //获取数据
        MbJobSupplierCompareTask mbJobSupplierCompareTask = mbJobSupplierCompareTaskList.get(0);
        if (mbJobSupplierCompareTask == null) {
            log.error("待执行的任务数据为null");
            return false;
        }
        TraceContext.setRequestId(mbJobSupplierCompareTask.getRequestid());
        // 更新任务状态为处理中
        mbJobSupplierCompareTaskMapper.updateTaskStatusById(TaskStatusComparisonEnum.INQUIRY.getCode(), mbJobSupplierCompareTask.getId());
        if (StringUtils.isBlank(mbJobSupplierCompareTask.getCompareData())) {
            log.error("待执行的任务数据不完整: {}", mbJobSupplierCompareTask);
            return false;
        }

        Boolean resultFlag = false;
        if(ComparePriceProductTypeEnum.FLIGHT_TOP.getName().equals(comparePriceProductType) || ComparePriceProductTypeEnum.FLIGHT.getName().equals(comparePriceProductType)) {
            FlightPriceCompareModel compareModel = null;
            try {
                compareModel = JsonUtils.parse(mbJobSupplierCompareTask.getCompareData(), FlightPriceCompareModel.class);
            } catch (Exception e) {
                log.info("热门航线对比任务数据解析失败: {}", mbJobSupplierCompareTask.getCompareData());
                return false;
            }
            if (compareModel == null || StringUtils.isBlank(compareModel.getDepartCityCode())
                    || StringUtils.isBlank(compareModel.getArriveCityCode()) || StringUtils.isBlank(compareModel.getDepartDate())
                    || StringUtils.isBlank(compareModel.getFlightType()) || StringUtils.isBlank(compareModel.getUid())
                    || StringUtils.isBlank(compareModel.getCorpId())) {
                log.error("热门航线对比任务数据解析失败: {}", mbJobSupplierCompareTask.getCompareData());
                return false;
            }
            FlightPriceCompareRequest queryRequest = getFlightPriceCompareRequest(compareModel, mbJobSupplierCompareTask.getTaskId());
            resultFlag = this.queryFlightPriceComparison(queryRequest);
        } else if(ComparePriceProductTypeEnum.HOTEL.getName().equals(comparePriceProductType)) {
            QueryXrayComparePriceUploadDateResponseType.RecordsDTO.HotelDataDTO  hotelDataDTO = null;
            try {
                hotelDataDTO = JsonUtils.parse(mbJobSupplierCompareTask.getCompareData(), QueryXrayComparePriceUploadDateResponseType.RecordsDTO.HotelDataDTO .class);
            } catch (Exception e) {
                log.info("酒店对比任务数据解析失败: {}", mbJobSupplierCompareTask.getCompareData());
                return false;
            }
            PriceComparisonReqVo request = getPriceComparisonReqVo(hotelDataDTO, mbJobSupplierCompareTask);
            resultFlag = hotelSoaLoader.priceComparison(request);
            log.info("酒店比价结果, request:{},resultFlag: {}",JsonUtils.toJsonString(request), resultFlag);
        }
        if (resultFlag) {
            mbJobSupplierCompareTaskMapper.updateTaskStatusById(TaskStatusComparisonEnum.SUCCESS.getCode(), mbJobSupplierCompareTask.getId());
        } else {
            mbJobSupplierCompareTaskMapper.updateTaskStatusById(TaskStatusComparisonEnum.FAIL.getCode(), mbJobSupplierCompareTask.getId());
        }
        return resultFlag;
    }

    private static PriceComparisonReqVo getPriceComparisonReqVo(QueryXrayComparePriceUploadDateResponseType.RecordsDTO.HotelDataDTO hotelDataDTO,
                                                                MbJobSupplierCompareTask mbJobSupplierCompareTask) {
        PriceComparisonReqVo request = new PriceComparisonReqVo();
        if(ObjectUtil.isNotNull(hotelDataDTO.getMasterhotelid())) {
            request.setHotelId(String.valueOf(hotelDataDTO.getMasterhotelid()));
        }
        request.setCompanyCode(hotelDataDTO.getCorpId());
        request.setCheckinDate(hotelDataDTO.getCheckinDate());
        request.setTaskId(mbJobSupplierCompareTask.getTaskId());
        request.setCountryType(hotelDataDTO.getCountryType());
        request.setUid(hotelDataDTO.getBizUid());
        request.setCorpId(hotelDataDTO.getCorpId());
        return request;
    }

    private static FlightPriceCompareRequest getFlightPriceCompareRequest(FlightPriceCompareModel compareModel, String taskId) {
        FlightPriceCompareRequest queryRequest = new FlightPriceCompareRequest();
        queryRequest.setTaskId(taskId);
        queryRequest.setFlightType(compareModel.getFlightType());
        queryRequest.setDepartCityCode(compareModel.getDepartCityCode());
        queryRequest.setArriveCityCode(compareModel.getArriveCityCode());
        queryRequest.setClazzCode(compareModel.getClazzCode());
        queryRequest.setDepartDate(compareModel.getDepartDate());
        queryRequest.setUid(compareModel.getUid());
        queryRequest.setCorpId(compareModel.getCorpId());
        return queryRequest;
    }

    private Boolean queryFlightPriceComparison(FlightPriceCompareRequest queryRequest) {
        // 随机等待5-10秒，模拟供应商调用延时
        try {
            Thread.sleep((long) (Math.random() * 5000 + 5000)); // 5-10秒
        } catch (InterruptedException e) {
            log.error("线程休眠异常", e);
        }
        JSONResult<FlightPriceCompareResponse> result = flightBookingClient.queryFlightPriceComparison(queryRequest);
        log.info("queryFlightPriceComparison result: {}", JsonUtils.toJsonString(result));
        if (result != null && result.getData() != null) {
            return result.getData().getSuccess();
        } else {
            log.error("Failed to find queryFlightPriceComparison: {}, error: {}", JsonUtils.toJsonString(queryRequest), result.getMsg());
            return false;
        }

    }


}
