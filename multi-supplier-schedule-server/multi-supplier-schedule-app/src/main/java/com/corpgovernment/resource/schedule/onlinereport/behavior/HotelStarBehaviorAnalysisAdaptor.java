package com.corpgovernment.resource.schedule.onlinereport.behavior;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HotelStarInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportHotelStarRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportHotelStarResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import com.google.common.collect.ImmutableMap;
import onlinereport.enums.HtlStarEnum;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc
 */
@Service
public class HotelStarBehaviorAnalysisAdaptor extends BaseReportDataAdaptor<Object> {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private HotelStarBehaviorAnalysisExcelService hotelStarBehaviorAnalysisExcelService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        OnlineReportHotelStarRequest request = new OnlineReportHotelStarRequest();
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
        // 查询的数据包含行业和商旅数据
        extMap.put("needIndustry", "T");
        request.setExtData(extMap);
        request.setProductType(baseCondition.getProductType());
        OnlineReportHotelStarResponse responseType = corpOnlineReportPlatformService.queryHotelStarAnalysis(request);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            return hotelstar(Optional.ofNullable(responseType.getStarList()).orElse(new ArrayList<>()), lang);
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(responseType)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
            }
            throw businessException;
        }
    }

    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        return hotelStarBehaviorAnalysisExcelService.buildExcel(baseCondition);
    }

    @Override
    public String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang) {
        String fileName_format = "%s_%s_%s_%s";
        String middle = SharkUtils.get("Save.HtlStar", lang);
        String prefix = SharkUtils.get("Catalog.BehaviorAnalysis", lang);
        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
        return String.format(fileName_format, prefix, middle, uid, str);
    }

    private Object hotelstar(List<HotelStarInfo> hotelStarInfoList, String lang) {
        List quantityDataList = new ArrayList();
        HtlStarEnum[] htlStarEnums = HtlStarEnum.values();
        for (HtlStarEnum htlStarEnum : htlStarEnums) {
            HotelStarInfo hotelStarInfo = hotelStarInfoList.stream()
                    .filter(i -> i.getStar() == htlStarEnum.getStar())
                    .findFirst().orElse(new HotelStarInfo());
            Map trendAmountQuantityData = ImmutableMap.builder()
                    .put("quantity", MapperUtils.convertDigitToZero(hotelStarInfo.getTotalQuantity()))
                    .put("percentage", MapperUtils.convertDigitToZero(hotelStarInfo.getQuantityPercent()))
                    .put("avgNightPrice", MapperUtils.convertDigitToZero(hotelStarInfo.getAvgNightPrice()))
                    .put("corpPercentage", MapperUtils.convertDigitToZero(hotelStarInfo.getCorpQuantityPercent()))
                    .put("industryPercentage", MapperUtils.convertDigitToZero(hotelStarInfo.getIndustryQuantityPercent())).build();
            quantityDataList.add(ImmutableMap.builder()
                    .put("axis", SharkUtils.get(htlStarEnum.getSharkKey(), lang))
                    .put("data", trendAmountQuantityData).build());
        }
        return quantityDataList;
    }

}
