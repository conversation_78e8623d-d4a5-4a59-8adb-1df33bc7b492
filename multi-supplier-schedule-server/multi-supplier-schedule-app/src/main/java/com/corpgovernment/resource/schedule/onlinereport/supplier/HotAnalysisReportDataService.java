package com.corpgovernment.resource.schedule.onlinereport.supplier;

import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportTypeionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.*;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.onlinereport.enums.ProductTypeEnum;
import com.corpgovernment.resource.schedule.onlinereport.utils.SharkUtils;
import com.google.common.collect.ImmutableMap;
import onlinereport.enums.CarOrderTypeEnum;
import onlinereport.enums.HtlCityLevelEnum;
import onlinereport.enums.HtlStarEnum;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/26 14:24
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class HotAnalysisReportDataService extends AbstractGenralExportDataService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    // 默认下载条数
    private final static int SUPPLIER_DOWNLOAD_LIMIT_DEFAULT = 1000;

    private final static String SUPPLIER_DOWNLOAD_LIMIT_KEY = "supplier_detail_download_limit";

    // 下载模式
    private final static String SUPPLIER_DOWNLOAD_MODE_KEY = "supplier_download_mode";


    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        if (!dimCheck(baseCondition.getExtData(), QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()))){
            return getNewHotAnalysisData(baseCondition);
        }else {
            return getHotAnalysisData(baseCondition);
        }
    }

    public List<ChartExcelEntity> buildExcel(BaseQueryConditionBO baseCondition, String uid) throws BusinessException {
        if (!dimCheck(baseCondition.getExtData(), QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()))){
            return getNewHotAnalysisDataExcel(baseCondition, uid);
        }else {
            // 火车、用车
            return getHotAnalysisDataExcel(baseCondition, uid);
        }
    }


    public List<ChartExcelEntity> getHotAnalysisDataExcel(BaseQueryConditionBO baseCondition, String uid) throws BusinessException {
        List<ChartExcelEntity> result = new ArrayList<>();
        String lang = baseCondition.getLang();
        String dim = baseCondition.getExtData().getOrDefault("dim", "");
        int downloadLimt = QConfigUtils.getNullDefaultInterValue(SUPPLIER_DOWNLOAD_LIMIT_KEY, SUPPLIER_DOWNLOAD_LIMIT_DEFAULT);
        if (StringUtils.equalsIgnoreCase(baseCondition.getQueryBu(), QueryReportBuTypeEnum.train.toString())){
            ProductTypeEnum[] productTypeEnums = ProductTypeEnum.values();
            for (ProductTypeEnum productTypeEnum : productTypeEnums) {
                OnlineReportHotAanlysisRequest request = mapRequest(baseCondition);
                request.setProductType(productTypeEnum.toString());
                // 下载标志，download为T的时候查询接口不受数量限制
                request.getExtData().put("download", "T");
                request.setTopLimit(downloadLimt);
                OnlineReportHotAanlysisResponse response = corpOnlineReportPlatformService.queryHotAnalysis(request);
                if (response != null && response.getResponseCode() == 20000) {
                    result.add(postHandle(uid, baseCondition.getLang(), response.getData(),
                            baseCondition.getBaseQueryCondition(), productTypeEnum.ordinal(),
                            getSheetNameByProductType(lang, productTypeEnum.toString()), dim, QueryReportBuTypeEnum.train));
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(response)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
                    }
                    throw businessException;
                }
            }
        }
        if (StringUtils.equalsIgnoreCase(baseCondition.getQueryBu(), QueryReportBuTypeEnum.car.toString())){
            OnlineReportHotAanlysisRequest request = mapRequest(baseCondition);
            // 下载标志，download为T的时候查询接口不受数量限制
            request.getExtData().put("download", "T");
            request.setTopLimit(downloadLimt);
            CarOrderTypeEnum[] carOrderTypes = CarOrderTypeEnum.values();
            // carOrderType:用车类型, airportpick（接送机）,Charter（包车）,rent（租车）,tax（打车）
            for (CarOrderTypeEnum carOrderTypeEnum : carOrderTypes){
                request.getExtData().put("carOrderType", carOrderTypeEnum.toString());
                OnlineReportHotAanlysisResponse response = corpOnlineReportPlatformService.queryHotAnalysis(request);
                if (response != null && response.getResponseCode() == 20000) {
                    result.add(postHandle(uid, baseCondition.getLang(), response.getData(), baseCondition.getBaseQueryCondition(), carOrderTypeEnum.ordinal(),
                            SharkUtils.get(carOrderTypeEnum.getSharkKey(), lang), dim, QueryReportBuTypeEnum.car));
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(response)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
                    }
                    throw businessException;
                }
            }
        }
        return result;
    }



    public ChartExcelEntity postHandle(String uid, String lang, OnlineReportTrendData data, BaseQueryCondition baseCondition, int sheetIndex, String sheetTitle,
                                       String dim, QueryReportBuTypeEnum queryReportBuTypeEnum) {
        Map dataRangeMap = getConditionContent(uid, baseCondition, lang);
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        List<OnlineReportTrendPoint> trendPoints = data.getData();
        List<OnlineReportTrendLegend> legends = data.getLegends();
        List<String> headers = legends.stream().map(i->i.getName()).collect(Collectors.toList());
        headers.add(0, getExcelFirstTitle(lang, dim, queryReportBuTypeEnum));
        sheet1.setHeaders(headers);
        List excelData = new ArrayList();

        for (OnlineReportTrendPoint trendPoint : trendPoints){
            List rowData = new ArrayList();
            rowData.add(trendPoint.getAxis());
            for (OnlineReportTrendLegend trendLegend : legends){
                rowData.add(trendPoint.getData().get(trendLegend.getKey()));
            }
            excelData.add(rowData);
        }
        excelData.addAll(buttonContent(lang, dataRangeMap));
        sheet1.setData(excelData);
        sheet1.setSheetNum(sheetIndex);
        sheet1.setSheetTitle(sheetTitle);
        return sheet1;
    }

    public boolean dimCheck(Map<String, String> extData, QueryReportBuTypeEnum reportBuTypeEnum) {
        String dim = extData.getOrDefault("dim", "");
        boolean flag = false;
        switch (dim) {
            // 机票、火车、用车（用车departure_city_name没有英文）
            case "departure_city_name":
            // 机票、火车
            case "arrival_city_name":
                // 协议适用酒店集团
            case "agreement_mgrgroup_name":
            case "hotel_brand_name":
            case "hotel_city_level":
            case "star":
            // 火车
            case "line_city":
            // 用车
            case "vendor_name":
            case "vehicle_name":
                flag = true;
                break;
            default:
                flag = false;
        }
        return flag && (reportBuTypeEnum == QueryReportBuTypeEnum.train || reportBuTypeEnum == QueryReportBuTypeEnum.car);
    }

    public Object getHotAnalysisData(BaseQueryConditionBO baseCondition) throws BusinessException {
        OnlineReportHotAanlysisRequest request = mapRequest(baseCondition);
        OnlineReportHotAanlysisResponse response = corpOnlineReportPlatformService.queryHotAnalysis(request);
        if (response != null && response.getResponseCode() == 20000) {
            return postHandle(response.getData());
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(response)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
            }
            throw businessException;
        }
    }

    public Object postHandle(OnlineReportTrendData data) throws BusinessException {
        // FIXME 此处无数据，应该不需要报错
    /*    if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getData())) {
            throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
        }*/
        return data;
    }

    public OnlineReportHotAanlysisRequest mapRequest(BaseQueryConditionBO baseCondition) {
        OnlineReportHotAanlysisRequest request = new OnlineReportHotAanlysisRequest();
        request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
        request.setLang(baseCondition.getLang());
        if (StringUtils.isEmpty(baseCondition.getQueryType())){
            request.setQueryType(QueryReportTypeionEnum.amount);
        }else {
            request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
        }
        request.setEId(baseCondition.getBaseQueryCondition().getUid());
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        dimEn(baseCondition.getLang(), baseCondition.getExtData(), request.getQueryBu());
        request.setExtData(baseCondition.getExtData());
        request.setProductType(baseCondition.getProductType());
        return request;
    }

    public void dimEn(String lang, Map<String, String> extData, QueryReportBuTypeEnum reportBuTypeEnum) {
        boolean isEn = SharkUtils.isEN(lang);
        String dim = extData.getOrDefault("dim", "");
        String dataDim;
        switch (dim) {
            // 机票、火车、用车（用车departure_city_name没有英文）
            case "departure_city_name":
                dataDim = reportBuTypeEnum == QueryReportBuTypeEnum.car ? "departure_city_name" : (isEn ? "departure_city_name_en" : "departure_city_name");
                break;
            // 机票、火车
            case "arrival_city_name":
                dataDim = isEn ? "arrival_city_name_en" : "arrival_city_name";
                break;
            case "hotel_group_name":
                dataDim = isEn ? "hotel_group_name_en" : "hotel_group_name";
                break;
            case "hotel_brand_name":
                dataDim = "hotel_brand_name";
                break;
            case "hotel_city_level":
                dataDim = "pcitylevel";
                break;
            case "star":
                dataDim = "star";
                break;
            // 火车
            case "line_city":
                dataDim = isEn ? "line_city_en" : "line_city";
                break;
            // 用车
            case "vendor_name":
                dataDim = "vendor_name";
                break;
            case "vehicle_name":
                dataDim = "vehicle_name";
                break;
            default:
                dataDim = "";
        }
        extData.put("dim", dataDim);
    }


    /**
     * 获得到excel第一行第一列的标题
     * @param lang
     * @param dim
     * @return
     */
    public String getExcelFirstTitle(String lang, String dim, QueryReportBuTypeEnum queryReportBuTypeEnum) {
        String hearderTitle = StringUtils.EMPTY;
        switch (dim) {
            // 机票、火车、用车（用车departure_city_name没有英文）
            case "departure_city_name":
                hearderTitle=SharkUtils.get("Index.depcity", lang);
                if (QueryReportBuTypeEnum.car == queryReportBuTypeEnum ){
                    hearderTitle=SharkUtils.get("Exceltopname.city", lang);
                }
                break;
            // 机票、火车
            case "arrival_city_name":
                hearderTitle=SharkUtils.get("Index.arrcity", lang);
                break;
            // 协议适用酒店集团
            case "agreement_mgrgroup_name":
                hearderTitle=SharkUtils.get("Exceltopname.hotelgroup", lang);
                break;
            case "hotel_brand_name":
                hearderTitle=SharkUtils.get("Exceltopname.hotelbrand", lang);
                break;
            case "hotel_city_level":
                hearderTitle=SharkUtils.get("Exceltopname.citylevel", lang);
                break;
            case "star":
                hearderTitle=SharkUtils.get("Index.star", lang);
                break;
            // 火车
            case "line_city":
                hearderTitle=SharkUtils.get("Exceltopname.RailLines", lang);
                break;
            // 用车
            case "vendor_name":
                hearderTitle=SharkUtils.get("Exceltopname.suppliername", lang);
                break;
            case "vehicle_name":
                hearderTitle=SharkUtils.get("Exceltopname.cartype", lang);
                break;
            default:
                break;
        }
        return hearderTitle;
    }

    public Object getNewHotAnalysisData(BaseQueryConditionBO baseCondition) throws BusinessException {
        OnlineReportSupplierTrendRequest request = new OnlineReportSupplierTrendRequest();
        request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
        request.setLang(baseCondition.getLang());
//        request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
        request.setEId(baseCondition.getBaseQueryCondition().getUid());
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        String dim = baseCondition.getExtData().getOrDefault("dim", "");
        dimEn(baseCondition.getLang(), baseCondition.getExtData(), request.getQueryBu());
        request.setExtData(baseCondition.getExtData());
        request.setProductType(baseCondition.getProductType());
        OnlineReportSupplierTrendResponse response = corpOnlineReportPlatformService.querySupplierTrend(request);
        if (response != null && response.getResponseCode() == 20000) {
            if (CollectionUtils.isEmpty(response.getTrends())) {
                return ImmutableMap.builder()
                        .put("data", new ArrayList<>())
                        .put("legends", new ArrayList<>()).build();
                //throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
            }
            return postHandleNew(baseCondition, response.getTrends(), QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()), dim);
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(response)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
            }
            throw businessException;
        }
    }

    public Object postHandleNew(BaseQueryConditionBO baseCondition, List<OnlineReportSupplierTrendInfo> trends
            , QueryReportBuTypeEnum queryReportBuTypeEnum, String dim) {
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight){
            List legends = new ArrayList();
            SupplierMonitorService.FltEnum[] fltEnums = SupplierMonitorService.FltEnum.values();
            for (SupplierMonitorService.FltEnum fltEnum : fltEnums){
                Map legendMap = new HashMap();
                legendMap.put("key",fltEnum.toString());
                legendMap.put("name",SharkUtils.get(fltEnum.getSharkkey(), baseCondition.getLang()));
                legends.add(legendMap);
            }
            List<Field> fields = Arrays.stream(OnlineReportSupplierTrendInfo.class.getDeclaredFields())
                    .filter(f -> !(f.getName().equals("dim")))
                    .collect(Collectors.toList());
            List dataList = new ArrayList();
            for (OnlineReportSupplierTrendInfo trendInfo : trends){
                Map dataMap = new HashMap();
                Map data = new HashMap();
                fields.forEach(f -> {
                    if (Arrays.stream(fltEnums).anyMatch(i-> StringUtils.equalsIgnoreCase(i.toString(), f.getName()))){
                        dataMap.put(f.getName(), field2Value(trendInfo, f));
                    }
                });
                data.put("axis", trendInfo.getDim());
                data.put("data", dataMap);
                dataList.add(data);
            }
            return ImmutableMap.builder()
                    .put("data", dataList)
                    .put("legends", legends).build();
        }

        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel){
            List legends = new ArrayList();
            SupplierMonitorService.HtlEnum[] htlEnums = SupplierMonitorService.HtlEnum.values();
            for (SupplierMonitorService.HtlEnum htlEnum : htlEnums){
                Map legendMap = new HashMap();
                legendMap.put("key",htlEnum.toString());
                legendMap.put("name",SharkUtils.get(htlEnum.getSharkkey(), baseCondition.getLang()));
                legends.add(legendMap);
            }
            List<Field> fields = Arrays.stream(OnlineReportSupplierTrendInfo.class.getDeclaredFields())
                    .filter(f -> !(f.getName().equals("dim")))
                    .collect(Collectors.toList());
            List dataList = new ArrayList();
            // 星级
            if (StringUtils.equalsIgnoreCase("star", dim)){
                HtlStarEnum[] htlStarEnums = HtlStarEnum.values();
                for (HtlStarEnum htlStarEnum : htlStarEnums) {
                    OnlineReportSupplierTrendInfo trendInfo = trends.stream()
                            .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), String.valueOf(htlStarEnum.getStar()))
                                    || (htlStarEnum == HtlStarEnum.OTHER && StringUtils.isEmpty(i.getDim())))
                            .findFirst().orElse(new OnlineReportSupplierTrendInfo());
                    Map dataMap = new HashMap();
                    Map data = new HashMap();
                    fields.forEach(f -> {
                        if (Arrays.stream(htlEnums).anyMatch(i-> StringUtils.equalsIgnoreCase(i.toString(), f.getName()))){
                            dataMap.put(f.getName(), field2Value(trendInfo, f));
                        }
                    });
                    data.put("axis", SharkUtils.get(htlStarEnum.getSharkKey(), baseCondition.getLang()));
                    data.put("data", dataMap);
                    dataList.add(data);
                }
            }else if (StringUtils.equalsIgnoreCase("hotel_city_level", dim)){// 城市等级
                HtlCityLevelEnum[] cityLevelEnums = HtlCityLevelEnum.values();
                for (HtlCityLevelEnum htlCityLevelEnum : cityLevelEnums) {
                    OnlineReportSupplierTrendInfo trendInfo = trends.stream()
                            .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), String.valueOf(htlCityLevelEnum.getKey()))
                                    || (htlCityLevelEnum == HtlCityLevelEnum.OTHER_LEVEL && StringUtils.isEmpty(i.getDim())))
                            .findFirst().orElse(new OnlineReportSupplierTrendInfo());
                    Map dataMap = new HashMap();
                    Map data = new HashMap();
                    fields.forEach(f -> {
                        if (Arrays.stream(htlEnums).anyMatch(i-> StringUtils.equalsIgnoreCase(i.toString(), f.getName()))){
                            dataMap.put(f.getName(), field2Value(trendInfo, f));
                        }
                    });
                    data.put("axis", SharkUtils.get(htlCityLevelEnum.getSharkKey(), baseCondition.getLang()));
                    data.put("data", dataMap);
                    dataList.add(data);
                }
            }else {
                for (OnlineReportSupplierTrendInfo trendInfo : trends){
                    Map dataMap = new HashMap();
                    Map data = new HashMap();
                    fields.forEach(f -> {
                        if (Arrays.stream(htlEnums).anyMatch(i-> StringUtils.equalsIgnoreCase(i.toString(), f.getName()))){
                            dataMap.put(f.getName(), field2Value(trendInfo, f));
                        }
                    });
                    data.put("axis", trendInfo.getDim());
                    data.put("data", dataMap);
                    dataList.add(data);
                }
            }
            return ImmutableMap.builder()
                    .put("data", dataList)
                    .put("legends", legends).build();
        }

        return null;
    }

    public Object field2Value(Object target, Field filed) {
        Object o = null;
        try {
            o =  filed.get(target);
            filed.setAccessible(true);
            if (o == null){
                Class clazz = filed.getType();
                if (StringUtils.equalsIgnoreCase(clazz.getSimpleName(), BigDecimal.class.getSimpleName())){
                    o = new BigDecimal("0.0");
                }else if (StringUtils.equalsIgnoreCase(clazz.getSimpleName(), Integer.class.getSimpleName())){
                    o = 0;
                }else if (StringUtils.equalsIgnoreCase(clazz.getSimpleName(), String.class.getSimpleName())){
                    o = StringUtils.EMPTY;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return o;
    }

    public List<ChartExcelEntity> getNewHotAnalysisDataExcel(BaseQueryConditionBO baseCondition, String uid) throws BusinessException {
        List<ChartExcelEntity> result = new ArrayList<>();
        OnlineReportSupplierTrendRequest request = new OnlineReportSupplierTrendRequest();
        request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
        request.setLang(baseCondition.getLang());
        request.setEId(baseCondition.getBaseQueryCondition().getUid());
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        String dim = baseCondition.getExtData().getOrDefault("dim", "");
        dimEn(baseCondition.getLang(), baseCondition.getExtData(), request.getQueryBu());
        request.setExtData(baseCondition.getExtData());
        // 下载标志，download为T的时候查询接口不受数量限制
        request.getExtData().put("download", "T");
        int downloadLimt = QConfigUtils.getNullDefaultInterValue(SUPPLIER_DOWNLOAD_LIMIT_KEY, SUPPLIER_DOWNLOAD_LIMIT_DEFAULT);
        request.setTopLimit(downloadLimt);
        String downloadMode = QConfigUtils.getNullDefaultValue(SUPPLIER_DOWNLOAD_MODE_KEY, SupplierMonitorService.SupplierDownloadMode.SINGLE.toString());
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(SupplierMonitorService.SupplierDownloadMode.SINGLE.toString(), downloadMode)) {
            ProductTypeEnum productTypeEnum = ProductTypeEnum.valueOf(StringUtils.upperCase(baseCondition.getProductType()));
            request.setProductType(productTypeEnum.toString());
            OnlineReportSupplierTrendResponse response = corpOnlineReportPlatformService.querySupplierTrend(request);
            if (response != null && response.getResponseCode() == 20000) {
                result.add(postHandleNewExcel(baseCondition, response.getTrends(), dim, uid, productTypeEnum, 0));
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(response)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
                }
                throw businessException;
            }
        }else {
            ProductTypeEnum[] productTypeEnums = ProductTypeEnum.values();
            for (ProductTypeEnum productTypeEnum : productTypeEnums){
                request.setProductType(productTypeEnum.toString());
                OnlineReportSupplierTrendResponse response = corpOnlineReportPlatformService.querySupplierTrend(request);
                if (response != null && response.getResponseCode() == 20000) {
                    result.add(postHandleNewExcel(baseCondition, response.getTrends(), dim, uid, productTypeEnum, productTypeEnum.ordinal()));
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(response)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(response.getResponseCode(), response.getResponseDesc());
                    }
                    throw businessException;
                }
            }
        }
        return result;
    }

    public ChartExcelEntity postHandleNewExcel(BaseQueryConditionBO baseCondition, List<OnlineReportSupplierTrendInfo> trends, String dim,
                                               String uid, ProductTypeEnum productTypeEnum, int sheetIndex) {
        String lang = baseCondition.getLang();
        QueryReportBuTypeEnum queryReportBuTypeEnum = QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu());
        Map dataRangeMap = getConditionContent(uid, baseCondition.getBaseQueryCondition(), lang);

        ChartExcelEntity sheet1 = new ChartExcelEntity();
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight){
            if (StringUtils.equalsIgnoreCase(dim, "departure_city_name") || StringUtils.equalsIgnoreCase(dim, "arrival_city_name")){
                List excelData = new ArrayList();
                sheet1.setHeaders(Arrays.asList(getExcelFirstTitle(lang, dim, queryReportBuTypeEnum),
                        SharkUtils.get("Index.netprice", lang), SharkUtils.get("Index.num", lang), SharkUtils.get("Index.costmoney", lang)
                ));

                for (OnlineReportSupplierTrendInfo trendInfo : trends){
                    excelData.add(Arrays.asList(trendInfo.getDim(),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumPrice()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantity()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumAmount())));
                }
                excelData.addAll(buttonContent(lang, dataRangeMap));
                sheet1.setSheetTitle(getSheetNameByProductType(lang, productTypeEnum.toString()));
                sheet1.setSheetNum(sheetIndex);
                sheet1.setData(excelData);
            }
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel){

            // 星级
            if (StringUtils.equalsIgnoreCase("star", dim)){
                List excelData = new ArrayList();
                sheet1.setRangeHeaders(Arrays.asList("1:", String.format("5:%s", SharkUtils.get("Public.all", lang)),
                        String.format("5:%s", SharkUtils.get("SupplierMonitor.ThreeParty", lang)),
                        String.format("5:%s", SharkUtils.get("SupplierMonitor.NotThreeParty", lang))));
                sheet1.setHeaders(Arrays.asList(getExcelFirstTitle(lang, dim, queryReportBuTypeEnum),
                        SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang), SharkUtils.get("Index.nightnum", lang), SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                        SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang), SharkUtils.get("Index.nightnum", lang), SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                        SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang), SharkUtils.get("Index.nightnum", lang), SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang)
                ));
                HtlStarEnum[] htlStarEnums = HtlStarEnum.values();
                for (HtlStarEnum htlStarEnum : htlStarEnums) {
                    OnlineReportSupplierTrendInfo trendInfo = trends.stream()
                            .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), String.valueOf(htlStarEnum.getStar()))
                                    || (htlStarEnum == HtlStarEnum.OTHER && StringUtils.isEmpty(i.getDim())))
                            .findFirst().orElse(new OnlineReportSupplierTrendInfo());
                    excelData.add(Arrays.asList(SharkUtils.get(htlStarEnum.getSharkKey(), baseCondition.getLang()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumAmount()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAmountPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantity()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getQuantityPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAvgPrice())
                            ,
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumAmountTa()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAmountTaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityTa()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getQuantityTaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceTa())
                            ,
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumAmountNta()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAmountNtaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityNta()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getQuantityNtaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceNta())));
                }
                excelData.addAll(buttonContent(lang, dataRangeMap));
                sheet1.setSheetTitle(getSheetNameByProductType(lang, productTypeEnum.toString()));
                sheet1.setSheetNum(sheetIndex);
                sheet1.setData(excelData);
            }else if (StringUtils.equalsIgnoreCase("hotel_city_level", dim)){// 城市等级
                List excelData = new ArrayList();
                sheet1.setRangeHeaders(Arrays.asList("1:", String.format("5:%s", SharkUtils.get("Public.all", lang)),
                        String.format("5:%s", SharkUtils.get("SupplierMonitor.ThreeParty", lang)),
                        String.format("5:%s", SharkUtils.get("SupplierMonitor.NotThreeParty", lang))));
                sheet1.setHeaders(Arrays.asList(getExcelFirstTitle(lang, dim, queryReportBuTypeEnum),
                        SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang), SharkUtils.get("Index.nightnum", lang), SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                        SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang), SharkUtils.get("Index.nightnum", lang), SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                        SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang), SharkUtils.get("Index.nightnum", lang), SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang)
                ));
                HtlCityLevelEnum[] cityLevelEnums = HtlCityLevelEnum.values();
                for (HtlCityLevelEnum htlCityLevelEnum : cityLevelEnums) {
                    OnlineReportSupplierTrendInfo trendInfo = trends.stream()
                            .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), String.valueOf(htlCityLevelEnum.getKey()))
                                    || (htlCityLevelEnum == HtlCityLevelEnum.OTHER_LEVEL && StringUtils.isEmpty(i.getDim())))
                            .findFirst().orElse(new OnlineReportSupplierTrendInfo());
                    excelData.add(Arrays.asList(SharkUtils.get(htlCityLevelEnum.getSharkKey(), baseCondition.getLang()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumAmount()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAmountPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantity()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getQuantityPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAvgPrice())
                            ,
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumAmountTa()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAmountTaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityTa()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getQuantityTaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceTa())
                            ,
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumAmountNta()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAmountNtaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityNta()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getQuantityNtaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceNta())));
                }
                excelData.addAll(buttonContent(lang, dataRangeMap));
                sheet1.setSheetTitle(getSheetNameByProductType(lang, productTypeEnum.toString()));
                sheet1.setSheetNum(sheetIndex);
                sheet1.setData(excelData);
            }else {
                List excelData = new ArrayList();
                sheet1.setRangeHeaders(Arrays.asList("1:", String.format("7:%s", SharkUtils.get("Public.all", lang)),
                        String.format("7:%s", SharkUtils.get("SupplierMonitor.ThreeParty", lang)),
                        String.format("7:%s", SharkUtils.get("SupplierMonitor.NotThreeParty", lang))));
                sheet1.setHeaders(Arrays.asList(getExcelFirstTitle(lang, dim, queryReportBuTypeEnum),
                        SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang),
                        SharkUtils.get("Index.nightnum", lang), SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                        SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang),
                        SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang),
                        SharkUtils.get("Index.nightnum", lang), SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                        SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang),
                        SharkUtils.get("Index.costmoney", lang), SharkUtils.get("Index.costper", lang),
                        SharkUtils.get("Index.nightnum", lang), SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang),
                        SharkUtils.get("SupplierMonitor.Industry.Avg.Price", lang), SharkUtils.get("SupplierMonitor.Corp.Avg.Price", lang)
                ));
                for (OnlineReportSupplierTrendInfo trendInfo : trends){
                    excelData.add(Arrays.asList(StringUtils.equalsIgnoreCase("agreement_mgrgroup_name", dim) ?
                                    (MapperUtils.trim(trendInfo.getDim()) + (Objects.nonNull(trendInfo.getAgreementTag()) && trendInfo.getAgreementTag() == 1 ?
                                            String.format("(%s)", SharkUtils.get("Index.pact", lang)) : StringUtils.EMPTY)) : MapperUtils.trim(trendInfo.getDim()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumAmount()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAmountPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantity()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getQuantityPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAvgPrice()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPrice()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPrice())
                            ,
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumAmountTa()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAmountTaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityTa()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getQuantityTaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceTa()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPriceTa()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPriceTa())
                            ,
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumAmountNta()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAmountNtaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getSumQuantityNta()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getQuantityNtaPercent()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getAvgPriceNta()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getIndustryAvgPriceNta()),
                            MapperUtils.convertDigitToZeroString(trendInfo.getCorpAvgPriceNta())));
                }
                excelData.addAll(buttonContent(lang, dataRangeMap));
                sheet1.setSheetTitle(getSheetNameByProductType(lang, productTypeEnum.toString()));
                sheet1.setSheetNum(sheetIndex);
                sheet1.setData(excelData);
            }
        }
        return sheet1;
    }
}
