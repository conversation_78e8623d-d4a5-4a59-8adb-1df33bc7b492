package com.corpgovernment.resource.schedule.onlinereport.reportlib.reportfactory;

import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.OrderQueryEnum;
import org.apache.commons.lang3.StringUtils;

/*
 * <AUTHOR>
 * @date 2020/9/28 17:44
 * @Desc 报告库
 */
@Slf4j
public class ExcelSheetNameUtil {

    protected static final String LOG_TITLE = ExcelSheetNameUtil.class.getSimpleName();

    /**
     * 获得sheetName
     *
     * @param bizType
     * @param queryType
     * @param lang
     * @return
     */
    public static String getSheetName(String bizType, String queryType, String lang) {
        String sheetName = getReportName(bizType, queryType, lang);
        return sheetName;
    }

    /**
     * 获得ReportName
     *
     * @param bizType
     * @param queryType
     * @param lang
     * @return
     */
    public static String getReportName(String bizType, String queryType, String lang) {
        String reportName = null;
        try {
            OrderQueryEnum orderQueryEnum = convertOrderQueryEnum(bizType, queryType);
            switch (orderQueryEnum) {
                case F:
                    reportName = SharkUtils.get("Report.airticketorder", lang);
                    break;
                case H:
                    reportName = SharkUtils.get("Report.hotelorder", lang);
                    break;
                case T:
                    reportName = SharkUtils.get("Report.trainorder", lang);
                    break;
                case C:
                    reportName = SharkUtils.get("Report.carorder", lang);
                    break;
                case F_CHANGE:
                    reportName = SharkUtils.get("Report.changeticketorder", lang);
                    break;
                case F_ABNORMAL:
                    reportName = SharkUtils.get("Report.unnormalticket", lang);
                    break;
                case F_REFUND:
                    reportName = SharkUtils.get("Report.refundticketorder", lang);
                    break;
                case F_UNUSE:
                    reportName = SharkUtils.get("Report.nouseairticket", lang);
                    break;
                case CONSUME:
                    reportName = SharkUtils.get("Report.cost", lang);
                    break;
                case HC:
                    reportName = SharkUtils.get("Report.cityhotel", lang);
                    break;
                case DEPT:
                    reportName = getDeptReportName(bizType, lang);
                    break;
            }
        } catch (Exception e) {
            log.error(LOG_TITLE, e);
        }
        if (StringUtils.isEmpty(reportName)) {
            reportName = "orderdetail";
        }
        //文件名中的特殊字符
        if (reportName.contains("/")) {
            reportName = reportName.replace("/", "_");
        }
        return reportName;
    }

//    /**
//     * 获得ReportName
//     * @param bizType
//     * @param lang
//     * @return
//     */
//    public static String getReportName(String bizType, String lang){
//        String reportName = null;
//        QueryReportBuTypeEnum queryReportBuTypeEnum = QueryReportBuTypeEnum.valueOf(bizType);
//        switch (queryReportBuTypeEnum) {
//            case flight:
//                if (StringUtils.equalsIgnoreCase("DetailReport:FltOrderDetails", lang)){
//                    reportName = SharkUtils.get("Report.airticketorder",lang);
//                }else if (StringUtils.equalsIgnoreCase("DetailReport:UnUseFltTicketDetails", lang)){
//                    reportName = SharkUtils.get("Report.nouseairticket",lang);
//                }else if (StringUtils.equalsIgnoreCase("DetailReport:FltTicketRefundDetails", lang)){
//                    reportName = SharkUtils.get("Report.refundticketorder",lang);
//                }else if (StringUtils.equalsIgnoreCase("DetailReport:FltTicketRebookDetails", lang)){
//                    reportName = SharkUtils.get("Report.changeticketorder",lang);
//                }
//            case hotel:
//                reportName = SharkUtils.get("Report.hotelorder",lang);
//                break;
//            case train:
//                reportName = SharkUtils.get("Report.trainorder",lang);
//                break;
//            case car:
//                reportName = SharkUtils.get("Report.carorder",lang);
//                break;
//        }
//        if (StringUtils.isEmpty(reportName)){
//            reportName = "orderdetail";
//        }
//        //文件名中的特殊字符
//        if (reportName.contains("/")){
//            reportName = reportName.replace("/","_");
//        }
//        return reportName;
//    }

    /**
     * 获得部门/成本中心分析ReportName
     *
     * @param bizType 产线
     * @param lang    语言
     * @return sheetname
     */
    private static String getDeptReportName(String bizType, String lang) {
        String sheetName = null;
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(OrderQueryEnum.F.toString(), bizType)) {
            sheetName = SharkUtils.get("Report.airdepartment", lang);
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(OrderQueryEnum.H.toString(), bizType)) {
            sheetName = SharkUtils.get("Report.hoteldepartment", lang);
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(OrderQueryEnum.T.toString(), bizType)) {
            sheetName = SharkUtils.get("Report.traindepartment", lang);
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(OrderQueryEnum.C.toString(), bizType)) {
            sheetName = SharkUtils.get("Report.cardepartment", lang);
        }
        if (org.apache.commons.lang.StringUtils.equalsIgnoreCase(OrderQueryEnum.HC.toString(), bizType)) {
            sheetName = SharkUtils.get("Report.cityhotel", lang);
            if (sheetName != null) {
                if (sheetName.contains(" ")) {
                    sheetName = sheetName.replace(" ", "");
                }
            }
        }
        return sheetName;
    }

    /**
     * 转换查询对象
     *
     * @param bizType   [F,H,T,C]
     * @param queryType [F_CHANGE,F_ABNORMAL,F_REFUND,F_UNUSE,CONSUME,HC,DEPT]
     * @return
     */
    private static OrderQueryEnum convertOrderQueryEnum(String bizType, String queryType) {
        OrderQueryEnum orderQueryEnum = null;
        //订单明细
        if (StringUtils.isNotEmpty(bizType)) {
            orderQueryEnum = OrderQueryEnum.valueOf(bizType.trim());
        }
        //非订单明细
        if (StringUtils.isNotEmpty(queryType)) {
            orderQueryEnum = OrderQueryEnum.valueOf(queryType.trim());
        }
        return orderQueryEnum;
    }
}
