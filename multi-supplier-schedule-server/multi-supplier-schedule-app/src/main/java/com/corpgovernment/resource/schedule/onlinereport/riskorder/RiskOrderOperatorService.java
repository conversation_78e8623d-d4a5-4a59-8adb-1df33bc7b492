//package com.corpgovernment.resource.schedule.onlinereport.riskorder;
//
//import com.ctrip.corp.onlinereportweb.commmon.enums.PageErrorCodeEnum;
//import com.ctrip.corp.onlinereportweb.commmon.utils.OrpGsonUtils;
//import com.ctrip.corp.onlinereportweb.domain.bo.common.BusinessException;
//import com.ctrip.corp.onlinereportweb.domain.rpc.ICorpOnlineReportPlatformService;
//import com.ctrip.corp.onlinereportweb.domainNew.service.CorpUserInfoService;
//import com.ctrip.corp.onlinereportweb.domainreport.adaptor.biz.AbstractGenralExportDataService;
//import com.ctrip.corp.onlinereportweb.domainreport.bo.BaseQueryConditionBO;
//import com.ctrip.framework.clogging.agent.log.ILog;
//import com.ctrip.framework.clogging.agent.log.LogManager;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @date ：Created in 2022/4/12 18:20
// * @description：
// * @modified By：
// * @version: $
// */
//@Service
//public class RiskOrderOperatorService extends AbstractGenralExportDataService {
//
//    private ILog log = LogManager.getLogger(RiskOrderOperatorService.class);
//
//    private String LOG_TITLE = this.getClass().getSimpleName();
//
//    @Autowired
//    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;
//
//    @Autowired
//    private CorpUserInfoService corpUserInfoService;
//
//    public Map operateOrderDetail(BaseQueryConditionBO baseCondition) throws BusinessException {
//        BusinessException businessException;
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "FLT_REFUND")) {
//            OnlineReportFlightRiskOrderOperatorRequest request = mapFlightRiskOrderOperator(baseCondition);
//            request.setCorpId(corpUserInfoService.getCorpId(baseCondition.getBaseQueryCondition().getUid()));
//            OnlineReportFlightRiskOrderOperatorResponse responseType = updateFlightOrder(request);
//            if (responseType != null && responseType.getResponseCode() == 20000) {
//                return OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(responseType), Map.class);
//            }
//            if (Objects.isNull(responseType)) {
//                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
//            } else {
//                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
//            }
//            throw businessException;
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "HTL_CASH_OUT") ||
//                StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "HTL_UNDER_STAY")) {
//            OnlineReportHotelRiskOrderOperatorRequest request = mapHotelRiskOrderOperator(baseCondition);
//            request.setCorpId(corpUserInfoService.getCorpId(baseCondition.getBaseQueryCondition().getUid()));
//            OnlineReportHotelRiskOrderOperatorResponse responseType = updateHotelOrder(request);
//            if (responseType != null && responseType.getResponseCode() == 20000) {
//                return OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(responseType), Map.class);
//            }
//            if (Objects.isNull(responseType)) {
//                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
//            } else {
//                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
//            }
//            throw businessException;
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "CAR_DOUBLE_BOOK")) {
//            OnlineReportCarRiskOrderOperatorRequest request = mapCarRiskOrderOperator(baseCondition);
//            request.setCorpId(corpUserInfoService.getCorpId(baseCondition.getBaseQueryCondition().getUid()));
//            OnlineReportCarRiskOrderOperatorResponse responseType = updateCarOrder(request);
//            if (responseType != null && responseType.getResponseCode() == 20000) {
//                return OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(responseType), Map.class);
//            }
//            if (Objects.isNull(responseType)) {
//                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
//            } else {
//                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
//            }
//            throw businessException;
//        }
//        return null;
//    }
//
//
//    public OnlineReportFlightRiskOrderOperatorResponse updateFlightOrder(OnlineReportFlightRiskOrderOperatorRequest request) {
//        OnlineReportFlightRiskOrderOperatorResponse response = null;
//        try {
//            log.info(LOG_TITLE + "-flight", request.toString());
//            response = corpOnlineReportPlatformService.updateReportRiskFlightOrderOperatorLog(request);
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return response;
//    }
//
//    public OnlineReportHotelRiskOrderOperatorResponse updateHotelOrder(OnlineReportHotelRiskOrderOperatorRequest request) {
//        OnlineReportHotelRiskOrderOperatorResponse response = null;
//        try {
//            log.info(LOG_TITLE + "-hotel", request.toString());
//            response = corpOnlineReportPlatformService.updateReportRiskHotelOrderOperatorLog(request);
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return response;
//    }
//
//    public OnlineReportCarRiskOrderOperatorResponse updateCarOrder(OnlineReportCarRiskOrderOperatorRequest request) {
//        OnlineReportCarRiskOrderOperatorResponse response = null;
//        try {
//            log.info(LOG_TITLE + "-car", request.toString());
//            response = corpOnlineReportPlatformService.updateReportRiskCarOrderOperatorLog(request);
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return response;
//    }
//
//    public OnlineReportFlightRiskOrderOperatorRequest mapFlightRiskOrderOperator(BaseQueryConditionBO baseCondition) {
//        OnlineReportFlightRiskOrderOperatorRequest request = new OnlineReportFlightRiskOrderOperatorRequest();
//        List<FlightRiskOrderOperator> operatorList = Optional.ofNullable(baseCondition.getRiskOrderOperateBO().getFlightRiskOrderOperateBOList())
//                .orElse(Collections.emptyList())
//                .stream()
//                .map(bo -> new FlightRiskOrderOperator(
//                        bo.getOrderId(), bo.getPassengerName(), bo.getFlightCity(), baseCondition.getRiskScene(),
//                        bo.getOperateStatus(), bo.getOperatorTime(), bo.getOperator(), bo.getOperatorMark())
//                )
//                .collect(Collectors.toList());
//        request.setOperatorList(operatorList);
//        return request;
//    }
//
//    public OnlineReportHotelRiskOrderOperatorRequest mapHotelRiskOrderOperator(BaseQueryConditionBO baseCondition) {
//        OnlineReportHotelRiskOrderOperatorRequest request = new OnlineReportHotelRiskOrderOperatorRequest();
//        List<HotelRiskOrderOperator> operatorList = Optional.ofNullable(baseCondition.getRiskOrderOperateBO().getHotelRiskOrderOperateBOList())
//                .orElse(Collections.emptyList())
//                .stream()
//                .map(bo -> new HotelRiskOrderOperator(
//                        bo.getOrderId(), bo.getClientName(), baseCondition.getRiskScene(), bo.getOperateStatus(),
//                        bo.getOperatorTime(), bo.getOperator(), bo.getOperatorMark())
//                )
//                .collect(Collectors.toList());
//        request.setOperatorList(operatorList);
//        return request;
//    }
//
//    public OnlineReportCarRiskOrderOperatorRequest mapCarRiskOrderOperator(BaseQueryConditionBO baseCondition) {
//        OnlineReportCarRiskOrderOperatorRequest request = new OnlineReportCarRiskOrderOperatorRequest();
//        List<CarRiskOrderOperator> operatorList = Optional.ofNullable(baseCondition.getRiskOrderOperateBO().getCarRiskOrderOperateBOList())
//                .orElse(Collections.emptyList())
//                .stream()
//                .map(bo -> new CarRiskOrderOperator(
//                        bo.getOrderId(), baseCondition.getRiskScene(), bo.getOperateStatus(), bo.getOperatorTime(),
//                        bo.getOperator(), bo.getOperatorMark())
//                )
//                .collect(Collectors.toList());
//        request.setOperatorList(operatorList);
//        return request;
//    }
//}
