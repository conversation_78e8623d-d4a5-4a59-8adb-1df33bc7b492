package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.CommonConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.ExtFiledEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.ReportLibTakeawayIndicatorConfigBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.orderdetail.OrderDetailBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.dto.orderdetail.OrderDetailDTO;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.types.AckCodeType;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.dept.FactoryList;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.IOrderService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.hive.OrderDetailHiveService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.soa.OrderDetailSoaService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.netflix.discovery.CommonConstants;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import onlinereport.enums.reportlib.OrderDetailEnumerable;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-10-14 11:41
 * @desc
 */
@Service
@Slf4j
public class OrderDetailService implements IOrderService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private FactoryList<OrderDetailSoaService, QueryReportBuTypeEnum> factoryList1;

    @Autowired
    private FactoryList<OrderDetailHiveService, QueryReportBuTypeEnum> factoryList2;


    @Override
    public OrderDetailBO queryDetail(String uid, BaseQueryConditionBO baseQueryCondition, boolean isDownload) throws BusinessException {
        log.info("queryDetail入参baseQueryCondition:{}",JsonUtils.toJsonString(baseQueryCondition));
        long startTime = System.currentTimeMillis();
        if (StringUtils.isEmpty(baseQueryCondition.getQueryBu())) {
            throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
        }
        QueryReportBuTypeEnum queryReportBuTypeEnum = QueryReportBuTypeEnum.valueOf(baseQueryCondition.getQueryBu());
        List<Map<String, Object>> data = null;
        OrderDetailEnumerable[] detailEnumerables = null;
        int totalCount = 0;
        String reportType = checkFlgDetailType(queryReportBuTypeEnum, baseQueryCondition.getBaseQueryCondition().getReportId());
        Map<String, String> extMap = Optional.ofNullable(baseQueryCondition.getExtData()).orElse(Maps.newHashMap());
        extMap.put("reportType", reportType);
        baseQueryCondition.setExtData(extMap);
        if (isDownload) {
//            baseQueryCondition.setPager(null);
            log.info("queryDetail执行queryOrderDetail前耗时：{}",System.currentTimeMillis()-startTime);
            startTime = System.currentTimeMillis();
            OrderDetailDTO orderDetailDTO = Optional.ofNullable(factoryList1.getBean(queryReportBuTypeEnum).queryOrderDetail(baseQueryCondition)).orElse(new OrderDetailDTO());
            log.info("queryDetail执行queryOrderDetail后耗时：{}",System.currentTimeMillis()-startTime);
            startTime = System.currentTimeMillis();
            data = orderDetailDTO.getData();
//            data = factoryList2.getBean(queryReportBuTypeEnum).queryOrderDetail(baseQueryCondition);
            detailEnumerables = factoryList1.getBean(queryReportBuTypeEnum).getEnum(reportType);
        } else {
            OrderDetailDTO orderDetailDTO = Optional.ofNullable(factoryList1.getBean(queryReportBuTypeEnum).queryOrderDetail(baseQueryCondition)).orElse(new OrderDetailDTO());
            data = orderDetailDTO.getData();
            totalCount = orderDetailDTO.getTotalRecords();
            detailEnumerables = factoryList1.getBean(queryReportBuTypeEnum).getEnum(reportType);
        }
        // 扩展字段聚合
        String cardNo = extMap.getOrDefault(CommonConst.CARD_ID, StringUtils.EMPTY);
        List<ExtFiledEntity> extFiledEntities = null;
        if (StringUtils.isNotEmpty(cardNo)) {
            log.info("queryDetail执行corpOnlineReportPlatformService.queryIndicatorConfig前耗时：{}",System.currentTimeMillis()-startTime);
            startTime = System.currentTimeMillis();
            ReportLibTakeawayIndicatorConfigBo configBo = corpOnlineReportPlatformService.queryIndicatorConfig(cardNo);
            log.info("queryDetail执行corpOnlineReportPlatformService.queryIndicatorConfig后耗时：{}",System.currentTimeMillis()-startTime);
            startTime = System.currentTimeMillis();
            extFiledEntities = Optional.ofNullable(configBo).map(ReportLibTakeawayIndicatorConfigBo::
                    getExtFieldEntities).orElse(new ArrayList<>());
        }

        // 自定义列
        List<String> reportColumns = baseQueryCondition.getReportColumns();
        OrderDetailBO orderDetail = convertData(reportColumns, baseQueryCondition.getLang(), detailEnumerables, data, isDownload, extFiledEntities);
        log.info("queryDetail执行convertData前耗时：{}",System.currentTimeMillis()-startTime);
        orderDetail.setTotalRecords(totalCount);
        return orderDetail;
    }

    private String checkFlgDetailType(QueryReportBuTypeEnum queryReportBuTypeEnum, String reportId) {
        if (StringUtils.equalsIgnoreCase(reportId, "DetailReport:UidOrderDetails")) {
            if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
                return "F_UID_DETAIL";
            }
            if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
                return "H_UID_DETAIL";
            }
            if (queryReportBuTypeEnum == QueryReportBuTypeEnum.train) {
                return "T_UID_DETAIL";
            }
            if (queryReportBuTypeEnum == QueryReportBuTypeEnum.car) {
                return "C_UID_DETAIL";
            }
        } else {
            if (queryReportBuTypeEnum == QueryReportBuTypeEnum.flight) {
                if (StringUtils.equalsIgnoreCase(reportId, "DetailReport:FltOrderDetails")) {
                    return "F_DETAIL";
                } else if (StringUtils.equalsIgnoreCase(reportId, "DetailReport:FltTicketRefundDetails")) {
                    return "F_REFUND";
                } else if (StringUtils.equalsIgnoreCase(reportId, "DetailReport:FltTicketRebookDetails")) {
                    return "F_REBOOK";
                } else if (StringUtils.equalsIgnoreCase(reportId, "DetailReport:UnUseFltTicketDetails")) {
                    return "F_UNUSE";
                } else {
                    return "F_DETAIL";
                }
            }
        }
        return StringUtils.EMPTY;
    }

    private OrderDetailBO convertData(List<String> reportColumns, String lang, OrderDetailEnumerable[] orderDetailEnumerables,
                                      List<Map<String, Object>> queryData, boolean isDownload, List<ExtFiledEntity> extFiledEntities) {
        log.info("convertData:queryData={}", JsonUtils.toJsonString(queryData));
        OrderDetailBO orderDetail = new OrderDetailBO();
        List<OrderDetailEnumerable> newList = Arrays.asList(orderDetailEnumerables);
        if (CollectionUtils.isNotEmpty(reportColumns)) {
            newList = newList.stream().filter(j -> reportColumns.stream().anyMatch(i -> StringUtils.equalsIgnoreCase(i, j.getCode()))).collect(Collectors.toList());
        }
        newList.sort(new Comparator<OrderDetailEnumerable>() {
            @Override
            public int compare(OrderDetailEnumerable o1, OrderDetailEnumerable o2) {
                if (o1.getOrder() == o2.getOrder()) {
                    return o1.getEnum().ordinal() - o2.getEnum().ordinal();
                } else {
                    return o1.getOrder() - o2.getOrder();
                }
            }
        });
        //标题

        List<String> title = new ArrayList<>();
        for (OrderDetailEnumerable item : newList) {
            title.add(SharkUtils.get(item.getName(), item.getName(), lang));
        }
        orderDetail.setTitle(title);
        if (CollectionUtils.isEmpty(queryData)) {
            return orderDetail;
        }
        List<List<Object>> result = new ArrayList<>();
        log.info("convertData:newList={}", JsonUtils.toJsonString(newList));
        //数据
        for (Map<String, Object> map : queryData) {
            List<Object> data = new ArrayList<>();
            for (OrderDetailEnumerable item : newList) {
                if (isDownload){
                    data.add(map.get(item.getCode()));
                }else {
                    data.add(formatData(map.get(item.getCode())));
                }
            }
            result.add(data);
        }
        log.info("convertData:result={}", JsonUtils.toJsonString(newList));
        orderDetail.setData(result);
        // 记录需要高亮和聚合的标题索引
        if (CollectionUtils.isNotEmpty(extFiledEntities)) {
            List<Integer> extHeadIndex = Lists.newArrayList();
            for (int i = 0; i < newList.size(); i++) {
                OrderDetailEnumerable curEnum = newList.get(i);
                if (extFiledEntities.stream().map(ExtFiledEntity::getCode).anyMatch(ext -> StringUtils.equalsIgnoreCase(curEnum.getCode(), ext))) {
                    extHeadIndex.add(i);
                }
            }
            orderDetail.setHighlightTitle(extHeadIndex);
        }
        return orderDetail;
    }
}
