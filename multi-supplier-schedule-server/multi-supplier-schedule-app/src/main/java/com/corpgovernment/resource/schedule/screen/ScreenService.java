package com.corpgovernment.resource.schedule.screen;


import com.corpgovernment.api.basic.request.BasicCityInfoRequest;
import com.corpgovernment.api.basic.response.BasicCityInfoResponse;
import com.corpgovernment.api.basic.soa.BasicDataClient;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.GeneralSaveInfo;
import com.corpgovernment.resource.schedule.domain.screen.gateway.ScreenGateway;
import com.corpgovernment.resource.schedule.domain.screen.model.ConsumeRankBO;
import com.corpgovernment.resource.schedule.domain.screen.model.ConsumeRankDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.HotCityBO;
import com.corpgovernment.resource.schedule.domain.screen.model.HotCityDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapBO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDestinationInfoBO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDestinationInfoDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.StandardRateBO;
import com.corpgovernment.resource.schedule.domain.screen.model.StandardRateDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.TotalAmountDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.TotalCountBO;
import com.corpgovernment.resource.schedule.domain.screen.model.TripRankBO;
import com.corpgovernment.resource.schedule.domain.screen.model.TripRankDTO;
import com.corpgovernment.resource.schedule.onlinereport.ReportDataNewAdaptorService;
import com.corpgovernment.resource.schedule.screen.converter.ScreenConvert;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import screen.dto.MapInfoDTO;
import screen.request.AggregateReq;
import screen.request.ConsumeRankReq;
import screen.request.HotCityReq;
import screen.request.MapReq;
import screen.request.StandardRateReq;
import screen.request.TotalAmountReq;
import screen.request.TripRankReq;
import screen.response.AggregateResp;
import screen.response.HotCityResp;
import screen.response.MapCoordinateResp;
import screen.response.MapDestinationInfoResp;
import screen.response.MapResp;
import screen.response.TotalInfo;
import screen.response.TripRankResp;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Smith
 */
@Component
@Slf4j
public class ScreenService {

    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    @Resource(name = "screenGatewayCKImpl")
    private ScreenGateway screenGateway;
    @Resource
    private ScreenConvert screenConvert;
    @Qualifier("screenThreadPoolExecutor")
    @Resource
    private ThreadPoolExecutor executor;
    @Autowired
    private BasicDataClient basicDataClient;
    @Resource
    private ReportDataNewAdaptorService reportDataNewAdaptorService;

    /**
     * 获取机票的国内和国际消费金额数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 包含国内和国际机票金额的Map，key为"dom"和"int"
     */
    private Map<String, BigDecimal> getFlightDomIntAmount(String startTime, String endTime) {
        Map<String, BigDecimal> result = new HashMap<>();
        result.put("dom", BigDecimal.ZERO); // 默认值
        result.put("int", BigDecimal.ZERO); // 默认值

        try {
            // 准备调用机票消费金额接口的参数
            BaseQueryConditionBO baseQueryConditionBO = new BaseQueryConditionBO();
            baseQueryConditionBO.setIndex("CONSUME");
            baseQueryConditionBO.setQueryType("amount");
            baseQueryConditionBO.setQueryBu("flight"); // 指定为机票

            // 设置查询条件
            BaseQueryCondition baseStaticQueryBO = new BaseQueryCondition();
            baseStaticQueryBO.setStartTime(startTime);
            baseStaticQueryBO.setEndTime(endTime);
            baseStaticQueryBO.setReportId("TravelAnalysis:OverView:ConsumeOverview");
            baseStaticQueryBO.setStatisticalCaliber("dealing");

            baseQueryConditionBO.setBaseQueryCondition(baseStaticQueryBO);

            // 调用接口获取机票国内国际金额数据
            Map<String, Object> flightResult = (Map<String, Object>) reportDataNewAdaptorService.adaptor("", baseQueryConditionBO, "zh-CN");
            log.info("Flight dom/int amount result: {}", flightResult);

            if (flightResult != null) {
                List<Map<String, Object>> right = (List<Map<String, Object>>) flightResult.get("right");

                if (right != null && !right.isEmpty()) {
                    // 获取第一个图表数据
                    Map<String, Object> firstChart = right.get(0);
                    List<Map<String, Object>> data = (List<Map<String, Object>>) firstChart.get("data");

                    if (data != null && !data.isEmpty()) {
                        Map<String, Object> firstData = data.get(0);

                        // 获取国内和国际机票金额
                        Number domAmount = (Number) firstData.get("dom");
                        Number intAmount = (Number) firstData.get("int");

                        // 转换为BigDecimal
                        if (domAmount != null) {
                            result.put("dom", new BigDecimal(domAmount.toString()));
                        }

                        if (intAmount != null) {
                            result.put("int", new BigDecimal(intAmount.toString()));
                        }

                        log.info("Got flight dom/int amount: dom={}, int={}", result.get("dom"), result.get("int"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error getting flight dom/int amount", e);
        }

        return result;
    }

    /**
     * 获取酒店的国内和国际消费金额数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 包含国内和国际酒店金额的Map，key为"dom"和"int"
     */
    private Map<String, BigDecimal> getHotelDomIntAmount(String startTime, String endTime) {
        Map<String, BigDecimal> result = new HashMap<>();
        result.put("dom", BigDecimal.ZERO); // 默认值
        result.put("int", BigDecimal.ZERO); // 默认值

        try {
            // 准备调用酒店消费金额接口的参数
            BaseQueryConditionBO baseQueryConditionBO = new BaseQueryConditionBO();
            baseQueryConditionBO.setIndex("CONSUME");
            baseQueryConditionBO.setQueryType("amount");
            baseQueryConditionBO.setQueryBu("hotel"); // 指定为酒店

            // 设置查询条件
            BaseQueryCondition baseStaticQueryBO = new BaseQueryCondition();
            baseStaticQueryBO.setStartTime(startTime);
            baseStaticQueryBO.setEndTime(endTime);
            baseStaticQueryBO.setReportId("TravelAnalysis:OverView:ConsumeOverview");
            baseStaticQueryBO.setStatisticalCaliber("dealing");

            baseQueryConditionBO.setBaseQueryCondition(baseStaticQueryBO);

            // 调用接口获取酒店国内国际金额数据
            Map<String, Object> hotelResult = (Map<String, Object>) reportDataNewAdaptorService.adaptor("", baseQueryConditionBO, "zh-CN");
            log.info("Hotel dom/int amount result: {}", hotelResult);

            if (hotelResult != null) {
                List<Map<String, Object>> right = (List<Map<String, Object>>) hotelResult.get("right");

                if (right != null && !right.isEmpty()) {
                    // 获取第一个图表数据
                    Map<String, Object> firstChart = right.get(0);
                    List<Map<String, Object>> data = (List<Map<String, Object>>) firstChart.get("data");

                    if (data != null && !data.isEmpty()) {
                        Map<String, Object> firstData = data.get(0);

                        // 获取国内和国际酒店金额
                        Number domAmount = (Number) firstData.get("dom");
                        Number intAmount = (Number) firstData.get("int");

                        // 转换为BigDecimal
                        if (domAmount != null) {
                            result.put("dom", new BigDecimal(domAmount.toString()));
                        }

                        if (intAmount != null) {
                            result.put("int", new BigDecimal(intAmount.toString()));
                        }

                        log.info("Got hotel dom/int amount: dom={}, int={}", result.get("dom"), result.get("int"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error getting hotel dom/int amount", e);
        }

        return result;
    }

    public void totalAmountAndOrder(TotalAmountReq request, AggregateResp aggregateResp) {
        log.info("totalAmount request: {}", request);

        try {
            // 准备调用 queryNewReportData 接口的参数
            BaseQueryConditionBO baseQueryConditionBO = new BaseQueryConditionBO();
            baseQueryConditionBO.setIndex("CONSUME");
            baseQueryConditionBO.setQueryType("amount");
            baseQueryConditionBO.setQueryBu("overview");

            // 设置查询条件
            BaseQueryCondition baseStaticQueryBO = new BaseQueryCondition();

            String startTime = dateTimeFormatter.format(request.getStartTime());
            String endTime = dateTimeFormatter.format(request.getEndTime());

            baseStaticQueryBO.setStartTime(startTime);
            baseStaticQueryBO.setEndTime(endTime);
            baseStaticQueryBO.setReportId("TravelAnalysis:OverView:ConsumeOverview");
            baseStaticQueryBO.setStatisticalCaliber("dealing");
            //baseStaticQueryBO.setPageKey("root");

            // 根据 areaType 设置国内国际区分
            if (request.getAreaType() != null) {
                if (request.getAreaType() == 1) { // 国内
                    baseQueryConditionBO.setProductType("dom"); // 国内
                } else if (request.getAreaType() == 2) { // 国际
                    baseQueryConditionBO.setProductType("inter"); // 国际
                }
            }

            baseQueryConditionBO.setBaseQueryCondition(baseStaticQueryBO);

            // 获取机票的国内和国际消费金额数据
            Map<String, BigDecimal> flightDomIntAmount = getFlightDomIntAmount(startTime, endTime);
            BigDecimal flightDomAmount = flightDomIntAmount.get("dom");
            BigDecimal flightIntAmount = flightDomIntAmount.get("int");
            BigDecimal totalFlightAmount = flightDomAmount.add(flightIntAmount);

            // 获取酒店的国内和国际消费金额数据
            Map<String, BigDecimal> hotelDomIntAmount = getHotelDomIntAmount(startTime, endTime);
            BigDecimal hotelDomAmount = hotelDomIntAmount.get("dom");
            BigDecimal hotelIntAmount = hotelDomIntAmount.get("int");
            BigDecimal totalHotelAmount = hotelDomAmount.add(hotelIntAmount);

            // 调用 queryNewReportData 接口获取金额数据
            Map<String, Object> result = (Map<String, Object>) reportDataNewAdaptorService.adaptor("", baseQueryConditionBO, "zh-CN");
            log.info("queryNewReportData amount result: {}", result);

            if (result != null) {
                // 从结果中提取金额信息
                Map<String, Object> left = (Map<String, Object>) result.get("left");
                List<Map<String, Object>> right = (List<Map<String, Object>>) result.get("right");

                if (left != null && right != null && !right.isEmpty()) {
                    // 获取总金额
                    Number total = (Number) left.get("total");

                    // 获取各产品线金额
                    ArrayList data = (ArrayList) ((Map<String, Object>) right.get(0)).get("data");
                    if (data != null && !data.isEmpty()) {
                        Map<String, Object> productData = (Map<String, Object>) data.get(0);

                        // 使用从专门接口获取的机票和酒店金额，而不是从overview接口获取
                        // Number flightAmount = (Number) productData.get("flight");
                        // Number hotelAmount = (Number) productData.get("hotel");
                        Number trainAmount = (Number) productData.get("train");
                        Number carAmount = (Number) productData.get("car");

                        // 设置金额信息
                        // BigDecimal flightAmountBD = flightAmount != null ? new BigDecimal(flightAmount.toString()) : BigDecimal.ZERO;
                        // BigDecimal hotelAmountBD = hotelAmount != null ? new BigDecimal(hotelAmount.toString()) : BigDecimal.ZERO;
                        BigDecimal flightAmountBD = totalFlightAmount; // 使用从专门接口获取的机票总金额
                        BigDecimal hotelAmountBD = totalHotelAmount; // 使用从专门接口获取的酒店总金额
                        BigDecimal trainAmountBD = trainAmount != null ? new BigDecimal(trainAmount.toString()) : BigDecimal.ZERO;
                        BigDecimal carAmountBD = carAmount != null ? new BigDecimal(carAmount.toString()) : BigDecimal.ZERO;

                        // 对于国际数据，火车和用车可能没有数据，需要特殊处理
                        if (request.getAreaType() != null && request.getAreaType() == 2) { // 国际
                            // 火车和用车没有国际数据，设置为0
                            trainAmountBD = BigDecimal.ZERO;
                            carAmountBD = BigDecimal.ZERO;
                            // 如果是国际，只使用国际机票和酒店金额
                            flightAmountBD = flightIntAmount;
                            hotelAmountBD = hotelIntAmount;
                        } else if (request.getAreaType() != null && request.getAreaType() == 1) { // 国内
                            // 如果是国内，只使用国内机票和酒店金额
                            flightAmountBD = flightDomAmount;
                            hotelAmountBD = hotelDomAmount;
                        }

                        aggregateResp.setTrafficTotalAmount(new TotalInfo(flightAmountBD, hotelAmountBD, trainAmountBD, carAmountBD));
                        log.info("Set trafficTotalAmount from queryNewReportData: {}", aggregateResp.getTrafficTotalAmount());
                    }
                }
            }

            // 再次调用 queryNewReportData 接口获取订单数量数据
            baseQueryConditionBO.setQueryType("numberOfTrips");
            Map<String, Object> orderResult = (Map<String, Object>) reportDataNewAdaptorService.adaptor("", baseQueryConditionBO, "zh-CN");
            log.info("queryNewReportData order result: {}", orderResult);

            if (orderResult != null) {
                // 从结果中提取订单数量信息
                Map<String, Object> left = (Map<String, Object>) orderResult.get("left");
                List<Map<String, Object>> right = (List<Map<String, Object>>) orderResult.get("right");

                if (left != null && right != null && !right.isEmpty()) {
                    // 获取总订单数
                    Number total = (Number) left.get("total");

                    // 获取各产品线订单数
                    ArrayList data = (ArrayList) ((Map<String, Object>) right.get(0)).get("data");
                    if (data != null && !data.isEmpty()) {
                        Map<String, Object> productData = (Map<String, Object>) data.get(0);

                        Number flightCount = (Number) productData.get("flight");
                        Number hotelCount = (Number) productData.get("hotel");
                        Number trainCount = (Number) productData.get("train");
                        Number carCount = (Number) productData.get("car");

                        // 对于国际数据，火车和用车可能没有数据，需要特殊处理
                        if (request.getAreaType() != null && request.getAreaType() == 2) { // 国际
                            // 火车和用车没有国际数据，设置为0
                            trainCount = 0;
                            carCount = 0;
                        }

                        // 设置订单数量信息
                        aggregateResp.setTrafficTotalInfo(new TotalInfo(
                                total != null ? total.doubleValue() : 0.0,
                                flightCount != null ? flightCount.doubleValue() : 0.0,
                                hotelCount != null ? hotelCount.doubleValue() : 0.0,
                                trainCount != null ? trainCount.doubleValue() : 0.0,
                                carCount != null ? carCount.doubleValue() : 0.0
                        ));

                        log.info("Set trafficTotalInfo from queryNewReportData: {}", aggregateResp.getTrafficTotalInfo());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error calling queryNewReportData", e);

            // 出错时回退到原来的方法
            TotalAmountDTO totalAmountDTO = screenConvert.toTotalAmountDTO(request);
            List<TotalCountBO> totalAmounts = screenGateway.totalAmountAndOrder(totalAmountDTO);

            if (!CollectionUtils.isEmpty(totalAmounts)) {
                Map<String, TotalCountBO> totalMap = totalAmounts.stream().collect(Collectors.toMap(TotalCountBO::getType, Function.identity()));

                TotalCountBO flight = totalMap.getOrDefault("flight", new TotalCountBO());
                TotalCountBO hotel = totalMap.getOrDefault("hotel", new TotalCountBO());
                TotalCountBO train = totalMap.getOrDefault("train", new TotalCountBO());
                TotalCountBO car = totalMap.getOrDefault("car", new TotalCountBO());

                aggregateResp.setTrafficTotalAmount(new TotalInfo(flight.getTotalAmount(), hotel.getTotalAmount(), train.getTotalAmount(), car.getTotalAmount()));
                aggregateResp.setTrafficTotalInfo(new TotalInfo(flight.getTotalQuantity(), hotel.getTotalQuantity(), train.getTotalQuantity(), car.getTotalQuantity()));
            }
        }
    }


    /**
     * 获取机票的总节省金额数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param areaType 区域类型：1=国内，2=国际
     * @return 机票总节省金额
     */
    private BigDecimal getFlightSaveAmount(String startTime, String endTime, Integer areaType) {
        BigDecimal result = BigDecimal.ZERO; // 默认值

        try {
            // 准备调用机票节省金额接口的参数
            BaseQueryConditionBO baseQueryConditionBO = new BaseQueryConditionBO();
            baseQueryConditionBO.setIndex("GENERAL_SAVE");
            baseQueryConditionBO.setQueryBu("flight");

            // 根据 areaType 设置国内国际区分
            if (areaType != null && areaType == 2) { // 国际
                //baseQueryConditionBO.setRcRegion("inter");
                baseQueryConditionBO.setProductType("inter");
            } else { // 国内或未指定，默认为国内
                //baseQueryConditionBO.setRcRegion("dom");
                baseQueryConditionBO.setProductType("dom");
            }

            // 设置查询条件
            BaseQueryCondition baseStaticQueryBO = new BaseQueryCondition();
            baseStaticQueryBO.setStartTime(startTime);
            baseStaticQueryBO.setEndTime(endTime);
            baseStaticQueryBO.setReportId("TravelAnalysis:SaveLoss:Save");
            baseStaticQueryBO.setStatisticalCaliber("dealing");
            baseStaticQueryBO.setConsumptionLevel("S");
            //baseStaticQueryBO.setPageKey("root");

            baseQueryConditionBO.setBaseQueryCondition(baseStaticQueryBO);

            // 调用接口获取机票节省金额数据
            GeneralSaveInfo flightResult = (GeneralSaveInfo) reportDataNewAdaptorService.adaptor("", baseQueryConditionBO, "zh-CN");
            log.info("Flight save amount result (areaType={}): {}", areaType, flightResult);

            if (flightResult != null) {
                // 获取机票节省金额
                BigDecimal companySaveAmount = flightResult.getCompanySaveAmount();

                // 直接使用 BigDecimal 类型
                if (companySaveAmount != null) {
                    result = companySaveAmount;
                }

                log.info("Got flight save amount (areaType={}): {}", areaType, result);
            }
        } catch (Exception e) {
            log.error("Error getting flight save amount for areaType={}", areaType, e);
        }

        return result;
    }

    /**
     * 获取酒店的总节省金额数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param areaType 区域类型：1=国内，2=国际
     * @return 酒店总节省金额
     */
    private BigDecimal getHotelSaveAmount(String startTime, String endTime, Integer areaType) {
        BigDecimal result = BigDecimal.ZERO; // 默认值

        try {
            // 准备调用酒店节省金额接口的参数
            BaseQueryConditionBO baseQueryConditionBO = new BaseQueryConditionBO();
            baseQueryConditionBO.setIndex("GENERAL_SAVE");
            baseQueryConditionBO.setQueryBu("hotel");

            // 根据 areaType 设置国内国际区分
            if (areaType != null && areaType == 2) { // 国际
                //baseQueryConditionBO.setRcRegion("inter");
                baseQueryConditionBO.setProductType("inter");
            } else { // 国内或未指定，默认为国内
                //baseQueryConditionBO.setRcRegion("dom");
                baseQueryConditionBO.setProductType("dom");
            }

            // 设置查询条件
            BaseQueryCondition baseStaticQueryBO = new BaseQueryCondition();
            baseStaticQueryBO.setStartTime(startTime);
            baseStaticQueryBO.setEndTime(endTime);
            baseStaticQueryBO.setReportId("TravelAnalysis:SaveLoss:Save");
            baseStaticQueryBO.setStatisticalCaliber("dealing");
            baseStaticQueryBO.setConsumptionLevel("S");
            //baseStaticQueryBO.setPageKey("root");

            baseQueryConditionBO.setBaseQueryCondition(baseStaticQueryBO);

            // 调用接口获取酒店节省金额数据
            GeneralSaveInfo hotelResult = (GeneralSaveInfo) reportDataNewAdaptorService.adaptor("", baseQueryConditionBO, "zh-CN");
            log.info("Hotel save amount result (areaType={}): {}", areaType, hotelResult);

            if (hotelResult != null) {
                // 获取酒店节省金额
                BigDecimal companySaveAmount = hotelResult.getCompanySaveAmount();

                // 直接使用 BigDecimal 类型
                if (companySaveAmount != null) {
                    result = companySaveAmount;
                }

                log.info("Got hotel save amount (areaType={}): {}", areaType, result);
            }
        } catch (Exception e) {
            log.error("Error getting hotel save amount for areaType={}", areaType, e);
        }

        return result;
    }

    public void getSaveAmount(TotalAmountReq request, AggregateResp aggregateResp) {
        log.info("getSaveAmount request: {}", request);

        try {
            String startTime = dateTimeFormatter.format(request.getStartTime());
            String endTime = dateTimeFormatter.format(request.getEndTime());

            // 获取机票和酒店的节省金额
            BigDecimal flightSaveAmount = getFlightSaveAmount(startTime, endTime, request.getAreaType());
            BigDecimal hotelSaveAmount = getHotelSaveAmount(startTime, endTime, request.getAreaType());

            // 计算总节省金额
            BigDecimal totalSaveAmount = flightSaveAmount.add(hotelSaveAmount);

            // 设置节省金额信息
            aggregateResp.setTrafficSaveAmount(new TotalInfo(flightSaveAmount, hotelSaveAmount, BigDecimal.ZERO, BigDecimal.ZERO));
            log.info("Set trafficSaveAmount: total={}, flight={}, hotel={}", totalSaveAmount, flightSaveAmount, hotelSaveAmount);
        } catch (Exception e) {
            log.error("Error getting save amount", e);

            // 出错时回退到原来的方法
            TotalAmountDTO totalAmountDTO = screenConvert.toTotalAmountDTO(request);
            screenGateway.getSaveAmount(totalAmountDTO, aggregateResp);
        }
    }

    public void consumeRank(ConsumeRankReq request, AggregateResp aggregateResp) {
        log.info("consumeRank request: {}", request);
        ConsumeRankDTO consumeRankDTO = screenConvert.toConsumeRankDTO(request);

        List<ConsumeRankBO> consumeRanksBO = screenGateway.consumeRankDept(consumeRankDTO);
        if (CollectionUtils.isEmpty(consumeRanksBO)) {
            return;
        }

        aggregateResp.setConsumeRanks(screenConvert.toConsumeRanksResp(consumeRanksBO));
    }

    public void consumeRankCostCenter(ConsumeRankReq request, AggregateResp aggregateResp) {
        log.info("consumeRankCostCenter request: {}", request);
        ConsumeRankDTO consumeRankDTO = screenConvert.toConsumeRankDTO(request);

        List<ConsumeRankBO> consumeRanksBO = screenGateway.consumeRankCostCenter(consumeRankDTO);

        if (CollectionUtils.isEmpty(consumeRanksBO)) {
            return;
        }

        aggregateResp.setConsumeCenterRanks(screenConvert.toConsumeRanksResp(consumeRanksBO));
    }


    public void hotCity(HotCityReq request, AggregateResp aggregateResp) {
        log.info("hotCity request: {}", request);
        HotCityDTO hotCityDTO = screenConvert.toHotCityDTO(request);

        List<HotCityBO> hotCitiesBO = screenGateway.hotCity(hotCityDTO);

        log.info("hotCitiesBO: {}", hotCitiesBO);


        if (CollectionUtils.isEmpty(hotCitiesBO)) {
            log.info("hotCitiesBO is empty");
            return;
        }

        List<HotCityResp> hotCitiesResp = screenConvert.toHotCitiesResp(hotCitiesBO);

        aggregateResp.setHotCities(hotCitiesResp);
    }

    public void standardRate(StandardRateReq request, AggregateResp aggregateResp) {
        log.info("standardRate request: {}", request);
        StandardRateDTO standardRateDTO = screenConvert.toStandardRateDTO(request);

        StandardRateBO standardRateBO = screenGateway.standardRate(standardRateDTO);

        log.info("standardRateBO: {}", standardRateBO);

        aggregateResp.setStandardRate(screenConvert.toStandardRateResp(standardRateBO));
    }

    public void tripRank(TripRankReq request, AggregateResp aggregateResp) {
        log.info("tripRank request: {}", request);
        TripRankDTO tripRankDTO = screenConvert.toTripRankDTO(request);


        List<TripRankBO> tripRanksBO = screenGateway.tripRank(tripRankDTO);

        log.info("tripRanksBO: {}", tripRanksBO);


        if (CollectionUtils.isEmpty(tripRanksBO)) {
            log.info("tripRanksBO is empty");
            return;
        }

        List<TripRankResp> tripRanksResp = screenConvert.toTripRanksResp(tripRanksBO);

        aggregateResp.setTripRanks(tripRanksResp);
    }

    public MapCoordinateResp map(MapReq request) {
        log.info("map request: {}", request);
        MapDTO mapDTO = screenConvert.toMapDTO(request);

        List<MapBO> mapsBO = screenGateway.map(mapDTO);
        log.info("mapsBO: {}", mapsBO);

        if (CollectionUtils.isEmpty(mapsBO)) {
            return new MapCoordinateResp();
        }


        List<List<MapResp>> maps = mapsBO.stream()
                // 拆分 【上海-北京】 字符串
                .map(m -> MapResp.getMapResp(m.getLabel()))
                .collect(Collectors.toList());


        Map<String, Collection<Double>> data = Maps.newHashMap();

        // 从城市名称字典中，获取 basic-manage 服务接口，城市名称对应的格式。如：中国香港:香港,中国澳门:澳门
        Map<String, String> cityNameDict = cityNameDict();

        maps.parallelStream()
                .flatMap(Collection::stream)
                // 去除重复的城市
                .distinct()
                // 获取城市坐标
                .forEach(mapResp -> {
                    BasicCityInfoRequest basicCityInfoRequest = new BasicCityInfoRequest();
                    String name = mapResp.getName();
                    String cityName = cityNameDict.getOrDefault(name, name);
                    basicCityInfoRequest.setCityName(cityName);


                    JSONResult<BasicCityInfoResponse> result = basicDataClient.getCityInfoByIdOrName(basicCityInfoRequest);
                    log.info("result: {}", result);

                    BasicCityInfoResponse cityInfoByIdOrName = result.getData();

                    if (cityInfoByIdOrName == null) {
                        return;
                    }

                    cityInfoByIdOrName.getBasiCityInfoList().stream()
                            .filter(f -> f.getCityName().equals(cityName))
                            .findFirst()
                            .ifPresent(f -> data.put(name, Lists.newArrayList(f.getCenterLon(), f.getCenterLat())));
                });

        MapCoordinateResp mapCoordinateResp = new MapCoordinateResp();
        mapCoordinateResp.setMapInfoDTO(new MapInfoDTO(data));
        mapCoordinateResp.setMap(maps);

        return mapCoordinateResp;
    }


    /**
     * 从城市名称字典中，获取 basic-manage 服务接口，城市名称对应的格式。如：中国香港:香港,中国澳门:澳门
     */
    private Map<String, String> cityNameDict() {
        Map<String, String> cityNameDict = ConfigService.getAppConfig().getProperty("cityNameDict", name -> JsonUtils.parse(name, new TypeReference<Map<String, String>>() {
                }),
                ImmutableMap.of("中国香港", "香港", "中国澳门", "澳门")
        );

        log.info("cityNameDict: {}", cityNameDict);

        return cityNameDict;
    }

    private List<MapDestinationInfoResp> destinationInfo(MapReq request) {
        log.info("destinationInfo request: {}", request);
        MapDestinationInfoDTO mapDestinationInfoDTO = screenConvert.toMapDestinationInfoDTO(request);

        List<MapDestinationInfoBO> mapDestinationInfoBO = screenGateway.destinationInfo(mapDestinationInfoDTO);
        log.info("mapDestinationInfoBO: {}", mapDestinationInfoBO);

        if (CollectionUtils.isEmpty(mapDestinationInfoBO)) {
            return Lists.newArrayList();
        }

        return screenConvert.toMapDestinationInfoResp(mapDestinationInfoBO);
    }


    private String getScreenName() {
        Config config = ConfigService.getAppConfig();
        return config.getProperty("screenName", "程曦BI大屏");
    }

    public AggregateResp aggregate(AggregateReq request) {
        log.info("request aggregate: {}", request);
        AggregateResp aggregateResp = new AggregateResp();
        // 大屏名称
        aggregateResp.setName(getScreenName());

        List<CompletableFuture<Void>> futures = Lists.newArrayList(
                // 总消费金额、总订单量
                CompletableFuture.runAsync(() -> totalAmountAndOrder(screenConvert.reqToTotalAmountReq(request), aggregateResp), executor).exceptionally(e -> {
                    log.error("totalAmount and order error", e);
                    return null;
                }),
                // 总节省金额
                CompletableFuture.runAsync(() -> getSaveAmount(screenConvert.reqToTotalAmountReq(request), aggregateResp), executor).exceptionally(e -> {
                    log.error("saveAmount error", e);
                    return null;
                }),
                // 消费部门排行(部门)
                CompletableFuture.runAsync(() -> consumeRank(screenConvert.reqToConsumeRankReq(request), aggregateResp), executor).exceptionally(e -> {
                    log.error("consumeRank error", e);
                    return null;
                }),
                // 消费部门排行(成本中心)
                CompletableFuture.runAsync(() -> consumeRankCostCenter(screenConvert.reqToConsumeRankReq(request), aggregateResp), executor).exceptionally(e -> {
                    log.error("consumeRankCostCenter error", e);
                    return null;
                }),
                // 法人主体消费排行
                CompletableFuture.runAsync(() -> consumeLegalRank(screenConvert.reqToConsumeRankReq(request), aggregateResp), executor).exceptionally(e -> {
                    log.error("consumeRankCostCenter error", e);
                    return null;
                }),
                // 差标合标率
                CompletableFuture.runAsync(() -> standardRate(screenConvert.reqToStandardRateReq(request), aggregateResp), executor).exceptionally(e -> {
                    log.error("standardRate error", e);
                    return null;
                }),
                // 差标行程排行
                CompletableFuture.runAsync(() -> tripRank(screenConvert.reqToTripRankReq(request), aggregateResp), executor).exceptionally(e -> {
                    log.error("tripRank error", e);
                    return null;
                }),
                // 热门出差城市
                CompletableFuture.runAsync(() -> hotCity(screenConvert.reqToHotCityReq(request), aggregateResp), executor).exceptionally(e -> {
                    log.error("hotCity error", e);
                    return null;
                })
        );

        // 等待所有任务执行完毕
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();


        return aggregateResp;
    }

    private void consumeLegalRank(ConsumeRankReq request, AggregateResp aggregateResp) {
        log.info("consumeLegalRank request: {}", request);
        ConsumeRankDTO consumeRankDTO = screenConvert.toConsumeRankDTO(request);

        List<ConsumeRankBO> consumeRanksBO = screenGateway.consumeLegalRank(consumeRankDTO);
        if (CollectionUtils.isEmpty(consumeRanksBO)) {
            return;
        }

        aggregateResp.setConsumeLegalRanks(screenConvert.toConsumeRanksResp(consumeRanksBO));
    }


    /**
     * 格式化金额。默认四舍五入与千分位：999,999,999
     */
    private String formatAmount(BigDecimal bigDecimal) {
        DecimalFormat decimalFormat = new DecimalFormat();
        return decimalFormat.format(bigDecimal.setScale(2, RoundingMode.HALF_UP));
    }
}
