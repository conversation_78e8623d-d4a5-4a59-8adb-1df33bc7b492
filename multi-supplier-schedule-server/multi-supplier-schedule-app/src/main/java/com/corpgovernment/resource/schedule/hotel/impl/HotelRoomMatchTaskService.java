package com.corpgovernment.resource.schedule.hotel.impl;

import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.core.hotel.soa.HotelClient;
import com.corpgovernment.resource.schedule.domain.hotel.enums.TaskEnum;
import com.corpgovernment.resource.schedule.domain.hotel.gateway.ITaskGateway;
import com.corpgovernment.resource.schedule.domain.hotel.model.HotelRoomMatchTaskParam;
import com.corpgovernment.resource.schedule.domain.hotel.model.SupplierControl;
import com.corpgovernment.resource.schedule.domain.hotel.model.Task;
import com.corpgovernment.resource.schedule.hotel.AbstractTaskService;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/18
 */
@Service
public class HotelRoomMatchTaskService extends AbstractTaskService {

    @Autowired
    private HotelClient hotelClient;
    @Autowired
    private ITaskGateway taskGateway;

    @Override
    protected List<Task> createTask(String supplierCode) {
        return null;
    }

    @Override
    protected JSONResult<?> processTask(Task task) {
        HotelRoomMatchTaskParam paramDto = JsonUtils.parse(task.getParam(), HotelRoomMatchTaskParam.class);
        return hotelClient.matchRoom(paramDto.getMasterSupplierCode(), paramDto.getMasterHotelId(), paramDto.getSubSupplierCode(), paramDto.getSubHotelId());
    }

    @Override
    protected List<Task> deliverTask(String supplierCode, JSONResult<?> jsonResult, Task task, SupplierControl supplierControl) {
        return null;
    }

    @Override
    protected List<SupplierControl> listSupplierControl() {
        List<SupplierControl> supplierControlList = taskGateway.listSupplierControl();
        if (CollectionUtils.isEmpty(supplierControlList)) {
            return new ArrayList<>(0);
        }
        List<SupplierControl> tmpList = supplierControlList.stream()
                .filter(item -> item != null && item.getHotelRoomMatchTaskFlow() != null && item.getHotelRoomMatchTaskFlow() > 0).collect(Collectors.toList());
        tmpList.forEach(item -> item.setTaskFlow(item.getHotelRoomMatchTaskFlow()));
        return tmpList;
    }

    @Override
    public TaskEnum taskEnum() {
        return TaskEnum.HOTEL_ROOM_MATCH_TASK;
    }
}
