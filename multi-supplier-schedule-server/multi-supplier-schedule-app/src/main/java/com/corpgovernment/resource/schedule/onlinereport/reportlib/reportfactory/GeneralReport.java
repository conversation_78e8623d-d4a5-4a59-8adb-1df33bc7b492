package com.corpgovernment.resource.schedule.onlinereport.reportlib.reportfactory;

import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.ExceLite;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/*
 * <AUTHOR>
 * @date 2020/9/28 17:44
 * @Desc 一键导出报表
 */
@Service
@Slf4j
public class GeneralReport implements IReport {
    protected static final String LOG_TITLE = GeneralReport.class.getSimpleName();

   /* @Autowired
    private ReportDataAdaptorService reportDataAdaptorService;*/

    @Resource
    private ExceLite exceLite;


    public Workbook create(TaskEntity taskEntity) throws Exception {
  /*      String reportCondition = taskEntity.getConditions();
        log.info(LOG_TITLE, reportCondition);
        QueryGeneralReportBo input = (QueryGeneralReportBo) JacksonUtil.deserialize(reportCondition, QueryGeneralReportBo.class);
        String lang = taskEntity.getLang();
        List<ChartExcelEntity> excelEntities = reportDataAdaptorService.exportExcel(taskEntity.getUid(), input, lang);
        Workbook workbook = exportExcel(excelEntities);
        return workbook;*/
        return null;
    }

    /**
     * @throws Exception
     * @Title: 导出Excel
     * @Description: 导出Excel
     */
    private Workbook exportExcel(List<ChartExcelEntity> excelEntities)
            throws Exception {
        Workbook workbook = new HSSFWorkbook();
        for (int i = 0; excelEntities != null && i < excelEntities.size(); i++) {
            ChartExcelEntity chartExcelEntity = excelEntities.get(i);
            exceLite.exportExcel((HSSFWorkbook) workbook, chartExcelEntity.getSheetNum(), chartExcelEntity.getSheetTitle(), chartExcelEntity.getHeaders(), chartExcelEntity.getData());
        }
        return workbook;
    }
}
