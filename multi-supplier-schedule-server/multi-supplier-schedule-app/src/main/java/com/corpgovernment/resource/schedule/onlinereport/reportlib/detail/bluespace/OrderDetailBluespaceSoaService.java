package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.bluespace;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.dto.orderdetail.OrderDetailDTO;
import com.corpgovernment.resource.schedule.onlinereport.dept.MatchBean;
import onlinereport.enums.reportlib.OrderDetailEnumerable;

/**
 * <AUTHOR>
 * @date 2022-10-17 19:34
 * @desc
 */
public interface OrderDetailBluespaceSoaService extends MatchBean<QueryReportBuTypeEnum> {
    public abstract OrderDetailDTO queryOrderDetail(BaseQueryConditionBO baseQueryCondition) throws BusinessException;

    public abstract OrderDetailEnumerable[] getEnum(String reportType);
}
