package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FltDiscountRangeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.MarkMetricInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.MarkMetricTrendAndOverview;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportFltDiscountRangeRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportFltDiscountRangeResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMarkMetricTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMarkMetricTrendResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.FltDiscountRangeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc
 */
@Service
public class FltDiscountRateBehaviorAnalysisExcelService extends AbstractGenralExportDataService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    public List<ChartExcelEntity> buildExcel(BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        Map map = getConditionContent(baseCondition.getBaseQueryCondition().getUid(), baseCondition.getBaseQueryCondition(), lang);
        int sheetIndex = 0;
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        chartExcelEntityList.add(buildExcel(baseCondition, sheetIndex++, "trend", map));
        chartExcelEntityList.add(buildExcel(baseCondition, sheetIndex++, "detail", map));
        return chartExcelEntityList;
    }

    public ChartExcelEntity buildExcel(BaseQueryConditionBO baseCondition, int sheetIndex, String index, Map map) throws BusinessException {
        String lang = baseCondition.getLang();
        if (StringUtils.equalsIgnoreCase(index, "trend")) {
            OnlineReportMarkMetricTrendRequest request = new OnlineReportMarkMetricTrendRequest();
            request.setProductType(baseCondition.getProductType());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            request.setQueryBu(QueryReportBuTypeEnum.flight);
            extMap.put("metric", "AVG_PRICE_RATE_TREND");
            request.setExtData(extMap);
            OnlineReportMarkMetricTrendResponse responseType = corpOnlineReportPlatformService.queryMarkMetricTrend(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return metricTrend(Optional.ofNullable(responseType.getMarkMetric()).orElse(new MarkMetricTrendAndOverview()), lang, map, sheetIndex, baseCondition.getProductType());
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        } else {
            OnlineReportFltDiscountRangeRequest bookTypeRequest = new OnlineReportFltDiscountRangeRequest();
            bookTypeRequest.setProductType(baseCondition.getProductType());
            bookTypeRequest.setBasecondition(baseCondition.getBaseQueryCondition());
            OnlineReportFltDiscountRangeResponse responseType = corpOnlineReportPlatformService.queryFltDiscountRangeAnalysis(bookTypeRequest);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return flgihtDiscount(Optional.ofNullable(responseType.getDiscountRangeList()).orElse(new ArrayList<>()), lang, map, sheetIndex, baseCondition.getProductType());
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        }
    }

    private ChartExcelEntity flgihtDiscount(List<FltDiscountRangeInfo> discountRangeInfoList, String lang, Map map, int sheetIndex, String productType) {
        FltDiscountRangeEnum[] fltDiscountRangeEnums = FltDiscountRangeEnum.values();
        List title = Arrays.asList(SharkUtils.get("Travelanalysis.ticketsales", lang), SharkUtils.get("Index.num", lang),
                SharkUtils.get("Index.numpercentage", lang), SharkUtils.get("Index.netprice", lang));
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(title);
        List data = new ArrayList();
        for (FltDiscountRangeEnum fltDiscountRangeEnum : fltDiscountRangeEnums) {
            FltDiscountRangeInfo fltDiscountRangeInfo = discountRangeInfoList.stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getRange(), fltDiscountRangeEnum.getKey().toString()))
                    .findFirst().orElse(new FltDiscountRangeInfo());
            data.add(Arrays.asList(fltDiscountRangeEnum.getKey()
                    , MapperUtils.convertDigitToZero(fltDiscountRangeInfo.getTotalQuantity())
                    , MapperUtils.convertDigitToZeroString(fltDiscountRangeInfo.getQuantityPercent()).concat(GlobalConst.PERCENT_QUOTE)
                    , MapperUtils.convertDigitToZero(fltDiscountRangeInfo.getTotalPrice())));
        }
        data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                Arrays.asList(SharkUtils.get("Exceltopname.airtickettype", lang),
                        getSheetNameByProductType(lang, productType))));
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(SharkUtils.get("UserSurvey.DiscountDis", lang));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }

    private ChartExcelEntity metricTrend(MarkMetricTrendAndOverview markMetricTrendAndOverview, String lang, Map map, int sheetIndex, String productType) {
        List<MarkMetricInfo> markMetricInfoList = Optional.ofNullable(markMetricTrendAndOverview.getMarkMetricTrend()).orElse(new ArrayList<>());

        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(Arrays.asList(
                SharkUtils.get("Index.month", lang),
                SharkUtils.get("Index.public", lang),
                SharkUtils.get("Index.tmc", lang),
                SharkUtils.get("Index.industry", lang)));
        List data = new ArrayList();
        for (MarkMetricInfo markMetricInfo : markMetricInfoList) {
            data.add(Arrays.asList(
                    MapperUtils.trim(markMetricInfo.getDim()),
                    MapperUtils.convertDigitToZeroString(markMetricInfo.getCompanyMetric()),
                    MapperUtils.convertDigitToZeroString(markMetricInfo.getCorpMetric()),
                    MapperUtils.convertDigitToZeroString(markMetricInfo.getIndustryMetric())));
        }
        MarkMetricInfo markMetricInfo = Optional.ofNullable(markMetricTrendAndOverview.getMarkMetricInfo()).orElse(new MarkMetricInfo());
        data.add(Arrays.asList(StringUtils.EMPTY));
        data.add(Arrays.asList(SharkUtils.get("Index.public", lang), MapperUtils.convertDigitToZeroString(markMetricInfo.getCompanyMetric())));
        data.add(Arrays.asList(SharkUtils.get("Index.industry", lang), MapperUtils.convertDigitToZeroString(markMetricInfo.getIndustryMetric())));
        data.add(Arrays.asList(SharkUtils.get("Index.tmc", lang), MapperUtils.convertDigitToZeroString(markMetricInfo.getCorpMetric())));


        data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                Arrays.asList(SharkUtils.get("Exceltopname.airtickettype", lang),
                        getSheetNameByProductType(lang, productType))));
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(SharkUtils.get("UserSurvey.DiscountTrend", lang));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }
}
