package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.soa.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.common.BaseDataUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrainOrderInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.TrainOrderDetailService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.soa.AbstractOrderDetailSoaService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.reportlib.*;
import onlinereport.enums.reportlib.uid.TrainUidOrderDetailEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst.FOUR_DIGIT_NUM;


/**
 * <AUTHOR>
 * @date 2022-10-13 13:58
 * @desc
 */
@Service
@Slf4j
public class TrainOrderSoaService extends AbstractOrderDetailSoaService {

    @Override
    public OrderDetailEnumerable[] getEnum(String reportType) {
        if (StringUtils.equalsIgnoreCase(reportType, "T_UID_DETAIL")) {
            return TrainUidOrderDetailEnum.values();
        } else {
            return TrainOrderDetailEnum.values();
        }
    }

    /**
     * 火车订单明细
     *
     * @param detailInfo
     * @param lang
     * @param reportType
     * @return
     */
    public List<Map<String, Object>> convertMapData(OnlineReportOrderDetailInfo detailInfo, String lang, String reportType) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportTrainOrderInfo> data = detailInfo.getTrainOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        if (StringUtils.equalsIgnoreCase(reportType, "T_UID_DETAIL")) {
            return convertUidMapData(detailInfo, lang);
        } else {
            return convertDetailMapData(detailInfo, lang);
        }
    }

    protected List<Map<String, Object>> convertUidMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportTrainOrderInfo> data = detailInfo.getTrainOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        log.info("TrainOrderSoaService.convertUidMapData:data={}", JsonUtils.toJsonString(data));
        Map orderStatusMap = initOrderStatus(lang);
        Map refundMap = initRefunstatusDes(lang);
        Map rebookMap = initReBooktatusDes(lang);
        for (OnlineReportTrainOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(TrainUidOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrderId()));
            map.put(TrainUidOrderDetailEnum.ORDERSTATUS.getCode(), getDesc(order.getOrderStatus(), orderStatusMap));
            map.put(TrainUidOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(TrainUidOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(TrainUidOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
            map.put(TrainUidOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getAccountId()));
            map.put(TrainUidOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(TrainUidOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
            map.put(TrainUidOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRankName()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCostCenter1()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCostCenter2()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCostCenter3()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCostCenter4()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCostCenter5()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCostCenter6()));
            map.put(TrainUidOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(TrainUidOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(TrainUidOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(TrainUidOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
            map.put(TrainUidOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
            map.put(TrainUidOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
            map.put(TrainUidOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
            map.put(TrainUidOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
            map.put(TrainUidOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
            map.put(TrainUidOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(TrainUidOrderDetailEnum.PRINTTIME.getCode(), MapperUtils.trim(order.getPrintTime()));
            map.put(TrainUidOrderDetailEnum.PASSENGERNAME.getCode(), MapperUtils.trim(order.getPassengerName()));
            map.put(TrainUidOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            map.put(TrainUidOrderDetailEnum.CHANGEQUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getChangeQuantity()));
            map.put(TrainUidOrderDetailEnum.CHANGESTATUS.getCode(), getDesc(order.getChangeStatus(), rebookMap));
            map.put(TrainUidOrderDetailEnum.REFUNDSTATUS.getCode(), getDesc(order.getRefundStatus(), refundMap));
            map.put(TrainUidOrderDetailEnum.TRAINNAME.getCode(), MapperUtils.trim(order.getTrainName()));
            map.put(TrainUidOrderDetailEnum.FIRSTSEATTYPENAME.getCode(), MapperUtils.getValByLang(lang, order.getFirstSeatTypeName(), order.getFirstSeatTypeNameEn()));
            map.put(TrainUidOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));
            map.put(TrainUidOrderDetailEnum.TICKETPRICE.getCode(), DigitBaseUtils.formatDigit(order.getTicketPrice()));
            map.put(TrainUidOrderDetailEnum.DEPARTUREDATETIME.getCode(), MapperUtils.trim(order.getDepartureDateTime()));
            map.put(TrainUidOrderDetailEnum.DEPARTURESTATIONNAME.getCode(), MapperUtils.getValByLang(lang, order.getDepartureStationName(), order.getDepartureStationNameEn()));
            map.put(TrainUidOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getDepartureCityName(), order.getDepartureCityNameEn()));
            map.put(TrainUidOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrivalDateTime()));
            map.put(TrainUidOrderDetailEnum.ARRIVALSTATIONNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrivalStationName(), order.getArrivalStationNameEn()));
            map.put(TrainUidOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrivalCityName(), order.getArrivalCityNameEn()));
            map.put(TrainUidOrderDetailEnum.CHANGEBALANCE.getCode(), DigitBaseUtils.formatDigit(order.getChangebalance()));
            map.put(TrainUidOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(TrainUidOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccountCode()));
            map.put(TrainUidOrderDetailEnum.DELAY_RESCHEDULE_FEE.getCode(), DigitBaseUtils.formatDigit(order.getDelayRescheduleFee()));
            result.add(map);
        }
        log.info("TrainOrderSoaService.convertUidMapData:result={}", JsonUtils.toJsonString(data));
        return result;
    }

    protected List<Map<String, Object>> convertDetailMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportTrainOrderInfo> data = detailInfo.getTrainOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        log.info("TrainOrderSoaService.convertDetailMapData:data={}", JsonUtils.toJsonString(data));
        Map yesOrNotMap = BaseDataUtils.getYesOrNoMap(lang);
        Map orderStatusMap = initOrderStatus(lang);
        Map refundMap = initRefunstatusDes(lang);
        Map rebookMap = initReBooktatusDes(lang);
        Map ticketMap = initTicketTypeDes(lang);
        for (OnlineReportTrainOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(TrainOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZeroString(order.getOrderId()));
            map.put(TrainOrderDetailEnum.ORDERSTATUS.getCode(), getDesc(order.getOrderStatus(), orderStatusMap));
            map.put(TrainOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
//            map.put(TrainOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(TrainOrderDetailEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorpName()));
//            map.put(TrainOrderDetailEnum.COMPANYGROUP.getCode(), MapperUtils.trim(order.getCompanygroup()));
//            map.put(TrainOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
//            map.put(TrainOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getAccountId()));
//            map.put(TrainOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccountCode()));
//            map.put(TrainOrderDetailEnum.ACCOUNTNAME.getCode(), MapperUtils.trim(order.getAccountName()));
//            map.put(TrainOrderDetailEnum.SUBACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getSubAccountId()));
//            map.put(TrainOrderDetailEnum.SUBACCOUNTCODE.getCode(), MapperUtils.trim(order.getSubAccountCode()));
//            map.put(TrainOrderDetailEnum.SUBACCOUNTNAME.getCode(), MapperUtils.trim(order.getSubAccountName()));
//            map.put(TrainOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(TrainOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(TrainOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
//            map.put(TrainOrderDetailEnum.WORKCITY.getCode(), MapperUtils.trim(order.getWorkCity()));
//            map.put(TrainOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRankName()));
            map.put(TrainOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCostCenter1()));
//            map.put(TrainOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCostCenter2()));
//            map.put(TrainOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCostCenter3()));
//            map.put(TrainOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCostCenter4()));
//            map.put(TrainOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCostCenter5()));
//            map.put(TrainOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCostCenter6()));
            map.put(TrainOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
//            map.put(TrainOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
//            map.put(TrainOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
//            map.put(TrainOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
//            map.put(TrainOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
//            map.put(TrainOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
//            map.put(TrainOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
//            map.put(TrainOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
//            map.put(TrainOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
//            map.put(TrainOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(TrainOrderDetailEnum.ISONLINE.getCode(), MapperUtils.convertBookTypeSingle(order.getIsOnline()));
//            map.put(TrainOrderDetailEnum.FEETYPE.getCode(), MapperUtils.trim(order.getFeeType()));
            map.put(TrainOrderDetailEnum.PREPAYTYPE.getCode(), TrainOrderDetailService.convertPrepayType(MapperUtils.trim(order.getPrepayType())));
//            map.put(TrainOrderDetailEnum.ACBPREPAYTYPE.getCode(), MapperUtils.trim(order.getAcbPrepayType()));
//            map.put(TrainOrderDetailEnum.ISBOSS.getCode(), MapperUtils.trim(order.getBosstype()));
            map.put(TrainOrderDetailEnum.JOURNEYNO.getCode(), MapperUtils.trim(order.getTripId()));
            map.put(TrainOrderDetailEnum.TRIPID.getCode(), order.getJourneyNo());
//            map.put(TrainOrderDetailEnum.JOUNARYREASON.getCode(), MapperUtils.trim(order.getJourneyReason()));
//            map.put(TrainOrderDetailEnum.PROJECT.getCode(), MapperUtils.trim(order.getProject()));
//            map.put(TrainOrderDetailEnum.VERBALAUTHORIZE.getCode(), MapperUtils.convertTorF(order.getVerbalAuthorize(), yesOrNotMap));
//            map.put(TrainOrderDetailEnum.CONFIRMPERSON.getCode(), MapperUtils.trim(order.getConfirmPerson()));
//            map.put(TrainOrderDetailEnum.CONFIRMPTYPE.getCode(), MapperUtils.trim(order.getConfirmType()));
//            map.put(TrainOrderDetailEnum.CONFIRMPERSON2.getCode(), MapperUtils.trim(order.getConfirmPerson2()));
//            map.put(TrainOrderDetailEnum.CONFIRMPTYPE2.getCode(), MapperUtils.trim(order.getConfirmType2()));
            map.put(TrainOrderDetailEnum.GROUPMONTH.getCode(), MapperUtils.convertDigitToString(order.getGroupMonth()));
//            map.put(TrainOrderDetailEnum.USERDEFINEDRID.getCode(), MapperUtils.trim(order.getUserdefinedRid()));
//            map.put(TrainOrderDetailEnum.USERDEFINEDRC.getCode(), MapperUtils.trim(order.getUserdefinedRc()));
            //////////////////////////////////////////////////////////////////////////////////////////////////////////
            map.put(TrainOrderDetailEnum.PRINTTIME.getCode(), MapperUtils.trim(order.getPrintTime()));
            map.put(TrainOrderDetailEnum.PASSENGERNAME.getCode(), MapperUtils.trim(order.getPassengerName()));
            map.put(TrainOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            map.put(TrainOrderDetailEnum.CHANGEQUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getChangeQuantity()));
            map.put(TrainOrderDetailEnum.CHANGESTATUS.getCode(), getDesc(order.getChangeStatus(), rebookMap));
            map.put(TrainOrderDetailEnum.REFUNDSTATUS.getCode(), getDesc(order.getRefundStatus(), refundMap));
            map.put(TrainOrderDetailEnum.TICKETTYPE.getCode(), getDesc(order.getTicketType(), ticketMap));
            map.put(TrainOrderDetailEnum.TRAINNAME.getCode(), MapperUtils.trim(order.getTrainName()));
            map.put(TrainOrderDetailEnum.FIRSTSEATTYPENAME.getCode(), MapperUtils.getValByLang(lang, order.getFirstSeatTypeName(), order.getFirstSeatTypeNameEn()));
            map.put(TrainOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));
            map.put(TrainOrderDetailEnum.TICKETPRICE.getCode(), DigitBaseUtils.formatDigit(order.getTicketPrice()));
            map.put(TrainOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getServiceFee()));
//            map.put(TrainOrderDetailEnum.INSURANCEFEE.getCode(), DigitBaseUtils.formatDigit(order.getInsuranceFee()));
            map.put(TrainOrderDetailEnum.REFUNDTICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundTicketFee()));
//            map.put(TrainOrderDetailEnum.DELIVERFEE.getCode(), DigitBaseUtils.formatDigit(order.getDeliverFee()));
//            map.put(TrainOrderDetailEnum.PAPERTICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getPaperTicketFee()));
            map.put(TrainOrderDetailEnum.DEALCHANGESERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getDealChangeServiceFee()));
//            map.put(TrainOrderDetailEnum.GRABSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getGrabServiceFee()));
            map.put(TrainOrderDetailEnum.AFTERSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getAfterServiceFee()));
            map.put(TrainOrderDetailEnum.AFTERCHANGESERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getAfterchangeservicefee()));
//            map.put(TrainOrderDetailEnum.AFTERTAKETICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getAftertaketicketfee()));
//            map.put(TrainOrderDetailEnum.AFTERAFTERTAKETICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getAfteraftertaketicketfee()));
//            map.put(TrainOrderDetailEnum.OCURRENCY.getCode(), MapperUtils.trim(order.getOCurrency()));
//            map.put(TrainOrderDetailEnum.OEXCHANGERATE.getCode(), DigitBaseUtils.formatDigit(order.getOExchangerate().doubleValue(), FOUR_DIGIT_NUM));
            map.put(TrainOrderDetailEnum.LINECITY.getCode(), MapperUtils.getValByLang(lang, order.getLineCity(), order.getLineCityEn()));
            map.put(TrainOrderDetailEnum.DEPARTUREDATETIME.getCode(), MapperUtils.trim(order.getDepartureDateTime()));
            map.put(TrainOrderDetailEnum.DEPARTURESTATIONNAME.getCode(), MapperUtils.getValByLang(lang, order.getDepartureStationName(), order.getDepartureStationNameEn()));
            map.put(TrainOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getDepartureCityName(), order.getDepartureCityNameEn()));
            map.put(TrainOrderDetailEnum.DEPARTUREPROVINCENAME.getCode(), MapperUtils.getValByLang(lang, order.getDepartureProvinceName(), order.getDepartureProvinceNameEn()));
            map.put(TrainOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrivalDateTime()));
            map.put(TrainOrderDetailEnum.ARRIVALSTATIONNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrivalStationName(), order.getArrivalStationNameEn()));
            map.put(TrainOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrivalCityName(), order.getArrivalCityNameEn()));
            map.put(TrainOrderDetailEnum.ARRIVALPROVINCENAME.getCode(), MapperUtils.getValByLang(lang, order.getArrivalProvinceName(), order.getArrivalProvinceNameEn()));
            map.put(HotelOrderDetailEnum.ISRC.getCode(), MapperUtils.convertTorF(order.getIsRc(), yesOrNotMap));
            map.put(TrainOrderDetailEnum.SEATTYPERCCODENAME.getCode(), MapperUtils.trim(order.getSeattypeRccodename()));
//            map.put(TrainOrderDetailEnum.TICKETRCCODENAME.getCode(), MapperUtils.trim(order.getTicketRccodename()));
            map.put(TrainOrderDetailEnum.CHANGEBALANCE.getCode(), DigitBaseUtils.formatDigit(order.getChangebalance()));
//            map.put(TrainOrderDetailEnum.AUDITORID.getCode(), MapperUtils.trim(order.getAuditorid()));
//            map.put(TrainOrderDetailEnum.AUDITORID2.getCode(), MapperUtils.trim(order.getAuditorid2()));
//            map.put(TrainOrderDetailEnum.REFUND_RC.getCode(), MapperUtils.trim(order.getRefundRc()));
//            map.put(TrainOrderDetailEnum.REFUND_RC_DESC.getCode(), MapperUtils.trim(order.getRefundRcDesc()));
            map.put(TrainOrderDetailEnum.EST_FEE_12306.getCode(), DigitBaseUtils.formatDigit(order.getEstFee12306()));
//            map.put(TrainOrderDetailEnum.TAKETICKETSTATUS.getCode(), MapperUtils.trim(order.getTaketicketstatus()));
//            map.put(TrainOrderDetailEnum.DEFINDFLAG.getCode(), MapperUtils.trim(order.getDefineflag()));
//            map.put(TrainOrderDetailEnum.DEFINDFLAG2.getCode(), MapperUtils.trim(order.getDefineflag2()));
//            map.put(TrainOrderDetailEnum.TRAINCARBONS.getCode(), MapperUtils.convertDigitToString(order.getCarbonEmission()));
//            map.put(TrainOrderDetailEnum.TRAINMEDIANCARBONS.getCode(), MapperUtils.convertDigitToString(order.getMedianCarbons()));
//            map.put(TrainOrderDetailEnum.TRAINCARBONSAVE.getCode(), MapperUtils.convertDigitToString(order.getCarbonSave()));
//            map.put(TrainOrderDetailEnum.STDINDUSTRY1.getCode(), MapperUtils.trim(order.getStdIndustry1()));
//            map.put(TrainOrderDetailEnum.STDINDUSTRY2.getCode(), MapperUtils.trim(order.getStdIndustry2()));
            map.put(TrainOrderDetailEnum.PASSENGER_EID.getCode(), MapperUtils.trim(order.getPassengerEid()));
            map.put(TrainOrderDetailEnum.DELAY_RESCHEDULE_FEE.getCode(), DigitBaseUtils.formatDigit(order.getDelayRescheduleFee()));
            result.add(map);
        }
        log.info("TrainOrderSoaService.convertDetailMapData.result", JsonUtils.toJsonString(result));
        return result;
    }

    private String getDesc(String key, Map map) {
        if (StringUtils.isEmpty(key) || MapUtils.isEmpty(map)) {
            return StringUtils.EMPTY;
        }
        return (String) map.get(StringUtils.upperCase(key.trim()));
    }

    private Map initOrderStatus(String lang) {
        return TrainOrderStatusNewEnum.toMap();
    }

    private Map initRefunstatusDes(String lang) {
        return RefundOrderStatusEnum.toMap();
    }

    private Map initReBooktatusDes(String lang) {
        return RebookStatusNewEnum.toMap();
    }

    private Map initTicketTypeDes(String lang) {
        return ImmutableMap.builder()
                .put(TrainTicketTypeEnum.D.toString(), SharkUtils.get(TrainTicketTypeEnum.D.getName(), lang))
                .put(TrainTicketTypeEnum.C.toString(), SharkUtils.get(TrainTicketTypeEnum.C.getName(), lang))
                .build();
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.train;
    }
}
