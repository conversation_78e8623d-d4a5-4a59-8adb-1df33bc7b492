package com.corpgovernment.resource.schedule.onlinereport.reportlib.reportfactory;

import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.cglib.seriallizer.JacksonUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TimeFilterTypeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BasePermitVerfiyService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.OrderDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.TempFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/*
 * <AUTHOR>
 * @date 2020/9/28 17:44
 * @Desc 报告库
 */
@Service
@Slf4j
public class OrderDetailUidReport extends AbstractGenralExportDataService implements IReport {

    public final static String CORP_KEY = "corp";
    public final static String ACCOUNT_KEY = "account";
    public final static String DEPT_KEY = "dept";
    public final static String COST_CENTER_KEY = "costcenter";
    public final static String TIME_RANGE_FORMAT = "%s~%s";
    protected static final String LOG_TITLE = OrderDetailUidReport.class.getSimpleName();
    private final static int DEFAULT_PAGESIZE = 1000;
    //默认最大间隔天数，超过就要分批
    private final static int MAX_INTERVAL_DAYS_DEAFULT = 33;

    @Autowired
    private OrderDetailService orderDetailService;

    @Autowired
    private BasePermitVerfiyService basePermitVerfiyService;

    public Workbook create(TaskEntity taskEntity) throws Exception {
        Workbook workbook = null;
        try {
            String conditions = taskEntity.getConditions();
            log.info(LOG_TITLE, conditions);
            BaseQueryConditionBO baseQueryConditionBO = (BaseQueryConditionBO) JacksonUtil.deserialize(conditions, BaseQueryConditionBO.class);
            String uid = taskEntity.getUid();
            baseQueryConditionBO.getBaseQueryCondition().setUid(uid);
            String lang = taskEntity.getLang();
            String download_pageSize = QConfigUtils.getValue("download_pageSize");
            int pageSize = StringUtils.isEmpty(download_pageSize) ? DEFAULT_PAGESIZE : Integer.valueOf(download_pageSize);
            UserPermissionsBo userPermissionsBo = basePermitVerfiyService.vaildPermit(uid, baseQueryConditionBO, lang);
            BaseQueryConditionBO baseQueryCondition = basePermitVerfiyService.convertToBaseOrderQuery(userPermissionsBo, baseQueryConditionBO);
            Pager pager = new Pager(0l, pageSize, 1, 0);
            baseQueryCondition.setLang(lang);
            baseQueryCondition.setPager(pager);
            workbook = createBigData(baseQueryCondition, pageSize, uid, lang);
        } catch (Exception e) {
            log.error(LOG_TITLE, e);
            if (workbook != null) {
/*                if (workbook instanceof SXSSFWorkbook) {
                    ((SXSSFWorkbook) workbook).dispose();
                }
                workbook.close();*/
            }
            throw e;
        }
        return workbook;
    }

    private Workbook createBigData(BaseQueryConditionBO reportLibFilterBO, int pageSize, String uid, String lang) throws IOException, BusinessException {
        // 创建工作簿
        String tempPath = System.getProperty(TempFile.JAVA_IO_TMPDIR);
        log.info(LOG_TITLE, "createBigData tempPath:" + tempPath);
        return createBigDataMulitBatchByDate(reportLibFilterBO, pageSize, uid, lang);
    }

    /**
     * @param lang
     * @param baseQueryConditionBO
     * @param uid
     * @return
     */
    public List filllFilterContent(String lang, BaseQueryConditionBO baseQueryConditionBO, String uid) {
        BaseQueryCondition baseQueryCondition = baseQueryConditionBO.getBaseQueryCondition();
        String queryBu = baseQueryConditionBO.getQueryBu();
        List data = new ArrayList();
        data.addAll(getTimeRangeContent(lang, baseQueryCondition.getTimeFilterList(), queryBu, baseQueryCondition.getReportId()));
        Map map = getConditionContent(uid, baseQueryCondition, lang);
        data.addAll(getFilterContent(baseQueryConditionBO.getUsers(), baseQueryConditionBO.getEmployeIds(), lang));
        data.addAll(getDataRangeContent(lang, map));
        return data;
    }

    /**
     * 数据范围
     *
     * @param lang
     * @param map
     * @return
     */
    protected List getDataRangeContent(String lang, Map map) {
        return Arrays.asList(
                Arrays.asList(SharkUtils.get("Index.public", lang), (String) map.get(CORP_KEY)),
                Arrays.asList(SharkUtils.get("Public.account", lang), (String) map.get(ACCOUNT_KEY)),
                Arrays.asList(SharkUtils.get("Public.department", lang), (String) map.get(DEPT_KEY)),
                Arrays.asList(SharkUtils.get("Public.costcenter", lang), (String) map.get(COST_CENTER_KEY)));
    }

    /**
     * @param lang
     * @param timeFilterTypeInfoList
     * @param queryBu
     * @return
     */
    protected List getTimeRangeContent(String lang, List<TimeFilterTypeInfo> timeFilterTypeInfoList, String queryBu, String reportId) {
       /* List list = new ArrayList();
        list.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY));
        for (TimeFilterTypeInfo timeFilterTypeInfo : timeFilterTypeInfoList) {
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate")) {
                list.add(Arrays.asList(SharkUtils.get("Exceltopname.date", lang),
                        String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
            } else if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
                if (StringUtils.equalsIgnoreCase("DetailReport:FltTicketRefundDetails", reportId)) {
                    list.add(Arrays.asList(SharkUtils.get("Exceltopname.RefundTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase("DetailReport:FltTicketRebookDetails", reportId)) {
                    list.add(Arrays.asList(SharkUtils.get("Report.RebookTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase("DetailReport:UnUseFltTicketDetails", reportId)) {
                    list.add(Arrays.asList(SharkUtils.get("Exceltopname.TicketingTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else {
                    list.add(Arrays.asList(SharkUtils.get("Exceltopname.dealdate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                }
            } else if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "usedate")) {
                if (StringUtils.equalsIgnoreCase(queryBu, flight.toString())) {
                    list.add(Arrays.asList(SharkUtils.get("Report.TakeoffTime", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase(queryBu, hotel.toString())) {
                    list.add(Arrays.asList(SharkUtils.get("Exceltopname.livedate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase(queryBu, train.toString())) {
                    list.add(Arrays.asList(SharkUtils.get("Exceltopname.depaturedate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                } else if (StringUtils.equalsIgnoreCase(queryBu, QueryReportBuTypeEnum.car.toString())) {
                    list.add(Arrays.asList(SharkUtils.get("Report.begaindate", lang),
                            String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
                }
            }
        }
        return list;*/
        return null;
    }

    /**
     * @param users      预订人
     * @param passengers 出行人
     * @param lang       语言
     * @return
     */
    protected List getFilterContent(List<String> users, List<String> passengers, String lang) {
        List list = new ArrayList();
        String default_tip = SharkUtils.get("Report.SelectResult2", lang);
        list.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY));
        // 预订人
        list.add(Arrays.asList(SharkUtils.get("UserSurvey.Q1.Opt5", lang),
                CollectionUtils.isEmpty(users) ? default_tip : StringUtils.join(users, GlobalConst.SEPARATOR)));
        // 出行人
        list.add(Arrays.asList(SharkUtils.get("TravelPosition.passenger", lang),
                CollectionUtils.isEmpty(passengers) ? default_tip : StringUtils.join(passengers, GlobalConst.SEPARATOR)));
        return list;
    }

    /**
     * @param baseQueryConditionBO
     * @param pageSize
     * @param uid
     * @param lang
     * @return
     * @throws IOException
     * @throws BusinessException
     */
    private Workbook createBigDataMulitBatchByDate(BaseQueryConditionBO baseQueryConditionBO, int pageSize, String uid, String lang) throws IOException, BusinessException {/*
        Integer sheetLimit = QConfigUtils.getInterValue("sheetLimit");
        if (sheetLimit == null || sheetLimit <= 0) {
            sheetLimit = 10000;
        }
        List filterList = filllFilterContent(lang, baseQueryConditionBO, uid);

        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        SXSSFWorkbook workbook = new SXSSFWorkbook(hssfWorkbook, sheetLimit);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info(LOG_TITLE, "createBigDataMulitBatchByDate start");
        List<QueryReportBuTypeEnum> queryReportBuTypeEnums = Arrays.asList(flight, hotel,
                train, QueryReportBuTypeEnum.car);
        for (QueryReportBuTypeEnum queryReportBuTypeEnum : queryReportBuTypeEnums) {
            int pageIndex = 1;
            boolean isEmpty = false;//是否为空
            boolean isLastPage = false;//最后一页
            baseQueryConditionBO.setQueryBu(queryReportBuTypeEnum.toString());
            String sheetName = getSheetName(queryReportBuTypeEnum, lang);
            int startRow = 0;
            Map<Integer, Integer> maxWidth = new HashMap<>();
            while (!isLastPage) {
                List<List<Object>> dataSource = new ArrayList<>();
                Pager pager = baseQueryConditionBO.getPager();
                pager.setPageIndex(pageIndex);
                OrderDetailBO orderDetail = orderDetailService.queryDetail(uid, baseQueryConditionBO, true);
                if (pageIndex == 1) {
                    dataSource.add(new ArrayList<>(orderDetail.getTitle()));
                }
                pageIndex++;
                isEmpty = CollectionUtils.isEmpty(orderDetail.getData());
                isLastPage = isEmpty || orderDetail.getData().size() < pageSize;
                if (!isEmpty) {
                    dataSource.addAll(orderDetail.getData());
                }
                PoiCustomExcelUtils.createExcelBigDataMulitBatchObj(workbook, dataSource, startRow, sheetName, maxWidth);
                startRow += dataSource.size();
            }
            log.info(LOG_TITLE, String.format("createBigDataMulitBatchByDate end, data size : %d , take time : %s", startRow, stopWatch.getTime()));
        }
//        PoiCustomExcelUtils.createExcelMulitBatch(hssfWorkbook, filterList, 1, SharkUtils.get("Report.SelectResult1", lang),maxWidth);
        return workbook;*/
        return null;
    }

    private String getSheetName(QueryReportBuTypeEnum queryReportBuTypeEnum, String lang) {
        String sheetName = StringUtils.EMPTY;
        switch (queryReportBuTypeEnum) {
            case overview:
                sheetName = SharkUtils.get("Public.all", lang);
                break;
            case flight:
                sheetName = SharkUtils.get("Index.air", lang);
                break;
            case hotel:
                sheetName = SharkUtils.get("Index.hotel", lang);
                break;
            case train:
                sheetName = SharkUtils.get("Index.train", lang);
                break;
            case car:
                sheetName = SharkUtils.get("Index.car", lang);
                break;
            default:
                break;
        }
        return sheetName;
    }

}
