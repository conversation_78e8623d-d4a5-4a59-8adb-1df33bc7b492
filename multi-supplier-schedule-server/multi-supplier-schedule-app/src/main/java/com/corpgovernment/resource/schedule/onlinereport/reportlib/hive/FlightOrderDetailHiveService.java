package com.corpgovernment.resource.schedule.onlinereport.reportlib.hive;


import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportLibFilterBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib.ReportLibResultEntity;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.OrderDeatailService;
import org.springframework.stereotype.Service;

/**
 * Auther:abguo
 * Date:2019/8/21
 * Description:
 */
@Service
public class FlightOrderDetailHiveService extends OrderDeatailService {

    @Override
    public ReportLibResultEntity queryOrderdetails(ReportLibFilterBO reportLibFilterBO) {
        return null;
    }

}
