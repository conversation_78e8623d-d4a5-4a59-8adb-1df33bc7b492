package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.hive;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.CommonConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.customreport.ReportStaticDataQueryService;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TimeFilterTypeInfo;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.AbstractClickhouseBaseDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.CostcenterAndDeptFilter;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveFilter;
import com.corpgovernment.resource.schedule.onlinereport.enums.ClickHouseTable;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.OrderDeatailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-10-17 19:34
 * @desc
 */
@Slf4j
public abstract class AbstractOrderDetailHiveService extends AbstractClickhouseBaseDao implements OrderDetailHiveService {

    protected static final String PREFIX = OrderDeatailService.class.getSimpleName();
    @Autowired
    private ReportStaticDataQueryService reportStaticDataQueryService;

    /**
     * 机票订单明细
     *
     * @param baseQueryCondition
     */
    @Override
    public List<Map<String, Object>> queryOrderDetail(BaseQueryConditionBO baseQueryCondition) {
        HiveFilter hiveFilter = getHiveFilter(baseQueryCondition);
//        if (StringUtils.equalsIgnoreCase(baseQueryCondition.getQueryBu(), QueryReportBuTypeEnum .flight.name())){
        return this.queryOrderDetail(hiveFilter, baseQueryCondition.getLang(), getReportType(baseQueryCondition));
//        }else {
//            return this.queryOrderDetail(hiveFilter, baseQueryCondition.getLang());
//        }
    }

    /**
     * @param hiveFilter
     * @param lang
     * @return
     */
    protected List<Map<String, String>> queryOrderDetail(HiveFilter hiveFilter, String lang) {
        return null;
    }

    /**
     * @param hiveFilter
     * @param lang
     * @return
     */
    protected List<Map<String, Object>> queryOrderDetail(HiveFilter hiveFilter, String lang, String reportType) {
        return null;
    }



    protected HiveFilter getHiveFilter(BaseQueryConditionBO baseQueryConditionBO) {
        HiveFilter hiveFilter = new HiveFilter();
        BaseQueryCondition baseQueryCondition = baseQueryConditionBO.getBaseQueryCondition();
        hiveFilter.setEmployeIds(baseQueryConditionBO.getEmployeIds());
        hiveFilter.setOrderIds(Optional.ofNullable(baseQueryConditionBO.getOrderids()).orElse(new ArrayList<>()).stream().filter(i -> Objects.nonNull(i))
                .map(i -> Long.valueOf(StringUtils.trim(i))).collect(Collectors.toList()));
        hiveFilter.setCorpIds(baseQueryCondition.getCorpIds());
        hiveFilter.setAccountIds(Optional.ofNullable(baseQueryCondition.getAccountIds()).orElse(new ArrayList<>()).stream().filter(i -> Objects.nonNull(i))
                .map(i -> Long.valueOf(i)).collect(Collectors.toList()));
        List<TimeFilterTypeInfo> timeFilterTypeInfos = baseQueryCondition.getTimeFilterList();
        for (TimeFilterTypeInfo timeFilterTypeInfo : timeFilterTypeInfos) {
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    hiveFilter.setOrderStartTime(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    hiveFilter.setOrderEndTime(timeFilterTypeInfo.getEndTime());
                }
            }
            // 成交时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    hiveFilter.setDealStartTime(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    hiveFilter.setDealEndTime(timeFilterTypeInfo.getEndTime());
                }
            }

            // 使用时间
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "usedate")) {
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getStartTime())) {
                    hiveFilter.setUseStartTime(timeFilterTypeInfo.getStartTime());
                }
                if (StringUtils.isNotEmpty(timeFilterTypeInfo.getEndTime())) {
                    hiveFilter.setUseEndTime(timeFilterTypeInfo.getEndTime());
                }
            }
        }
        Pager pager = baseQueryConditionBO.getPager();
        hiveFilter.setPageNum(pager.getPageIndex());
        hiveFilter.setPageSize(pager.getPageSize());

        ClickHouseTable tableName = getTableName(baseQueryConditionBO.getQueryBu());

        String partition = getPartition(tableName);
        hiveFilter.setPartition(StringUtils.trim(partition));
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getCostCenterList())) {
            List<CostcenterAndDeptFilter> costcenterAndDeptFilters = new ArrayList<>();
            Optional.ofNullable(baseQueryCondition.getCostCenterList()).orElse(new ArrayList<>())
                    .stream().filter(i -> Objects.nonNull(i))
                    .forEach(i -> {
                        CostcenterAndDeptFilter filter = new CostcenterAndDeptFilter();
                        filter.setLevel(i.getKey());
                        filter.setInfo(i.getVals());
                        filter.setSelectAll(i.isSelectAll());
                        filter.setWay(i.getWay());
                        filter.setPermitVals(i.getPermitVals());
                        costcenterAndDeptFilters.add(filter);
                    });
            hiveFilter.setCostcenters(costcenterAndDeptFilters);
        }
        if (CollectionUtils.isNotEmpty(baseQueryCondition.getDeptList())) {
            List<CostcenterAndDeptFilter> costcenterAndDeptFilters = new ArrayList<>();
            Optional.ofNullable(baseQueryCondition.getDeptList()).orElse(new ArrayList<>())
                    .stream().filter(i -> Objects.nonNull(i))
                    .forEach(i -> {
                        CostcenterAndDeptFilter filter = new CostcenterAndDeptFilter();
                        filter.setLevel(i.getKey());
                        filter.setInfo(i.getVals());
                        filter.setSelectAll(i.isSelectAll());
                        filter.setWay(i.getWay());
                        filter.setPermitVals(i.getPermitVals());
                        costcenterAndDeptFilters.add(filter);
                    });
            hiveFilter.setDepts(costcenterAndDeptFilters);
        }
        hiveFilter.setExceedStandard(baseQueryConditionBO.getExceedStandard());
        hiveFilter.setUsers(baseQueryConditionBO.getUsers());
        hiveFilter.setPassengers(baseQueryConditionBO.getPassengers());
        hiveFilter.setOrderstatusList(convertOrderStatus(baseQueryConditionBO.getOrderstatusList()));
        hiveFilter.setExtData(baseQueryConditionBO.getExtData());
        // 切换sr和hive的时候可根据这个uid来灰度
        hiveFilter.setUid(baseQueryCondition.getUid());
        return hiveFilter;
    }

    private String getPartition(ClickHouseTable tableName) {
        return queryPartition(tableName);
    }

    private ClickHouseTable getTableName(String queryBu) {
        if (QueryReportBuTypeEnum.hotel.name().equals(queryBu)) {
            return ClickHouseTable.OLRPT_INDEXHOTELDOWNLOAD;
        }
        if (QueryReportBuTypeEnum.flight.name().equals(queryBu)) {
            return ClickHouseTable.OLRPT_INDEXFLIGHTDOWNLOAD;
        }
        if (QueryReportBuTypeEnum.train.name().equals(queryBu)) {
            return ClickHouseTable.OLRPT_INDEXTRAINDOWNLOAD;
        }
        if (QueryReportBuTypeEnum.car.name().equals(queryBu)) {
            return ClickHouseTable.OLRPT_INDEXCARDOWNLOAD;
        }
        return null;
    }

    protected List<String> convertOrderStatus(List<String> orderstatusList) {
        return orderstatusList;
    }

    protected String getReportType(BaseQueryConditionBO baseQueryCondition) {
        return Optional.ofNullable(baseQueryCondition.getExtData()).orElse(new HashMap<>()).get("reportType");
    }
}
