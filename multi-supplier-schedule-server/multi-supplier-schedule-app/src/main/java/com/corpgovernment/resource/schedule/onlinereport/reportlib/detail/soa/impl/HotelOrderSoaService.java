package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.soa.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.common.BaseDataUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportHotelOrderInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.HotelOrderDetailService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.soa.AbstractOrderDetailSoaService;
import onlinereport.enums.reportlib.HotelOrderDetailEnum;
import onlinereport.enums.reportlib.HotelOrderStatusEnum;
import onlinereport.enums.reportlib.HtlOrderStatusEnums;
import onlinereport.enums.reportlib.OrderDetailEnumerable;
import onlinereport.enums.reportlib.uid.HotelUidOrderDetailEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst.FOUR_DIGIT_NUM;


/**
 * <AUTHOR>
 * @date 2022-10-13 13:58
 * @desc
 */
@Service
public class HotelOrderSoaService extends AbstractOrderDetailSoaService {

    @Override
    public OrderDetailEnumerable[] getEnum(String reportType) {
        if (StringUtils.equalsIgnoreCase(reportType, "H_UID_DETAIL")) {
            return HotelUidOrderDetailEnum.values();
        } else {
            return HotelOrderDetailEnum.values();
        }
    }

    /**
     * 酒店订单明细
     *
     * @param detailInfo
     * @param lang
     * @param reportType
     * @return
     */
    public List<Map<String, Object>> convertMapData(OnlineReportOrderDetailInfo detailInfo, String lang, String reportType) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportHotelOrderInfo> data = detailInfo.getHtlOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        if (StringUtils.equalsIgnoreCase(reportType, "H_UID_DETAIL")) {
            return convertUidMapData(detailInfo, lang);
        } else {
            return convertDetailMapData(detailInfo, lang);
        }
    }

    protected List<Map<String, Object>> convertUidMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportHotelOrderInfo> data = detailInfo.getHtlOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        for (OnlineReportHotelOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(HotelUidOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrderId()));
            map.put(HotelUidOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrderStatus()));
            map.put(HotelUidOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(HotelUidOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(HotelUidOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
            map.put(HotelUidOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getAccountId()));
            map.put(HotelUidOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(HotelUidOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
            map.put(HotelUidOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRankName()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCostCenter1()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCostCenter2()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCostCenter3()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCostCenter4()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCostCenter5()));
            map.put(HotelUidOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCostCenter6()));
            map.put(HotelUidOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(HotelUidOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(HotelUidOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(HotelUidOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
            map.put(HotelUidOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
            map.put(HotelUidOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
            map.put(HotelUidOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
            map.put(HotelUidOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
            map.put(HotelUidOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
            map.put(HotelUidOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(HotelUidOrderDetailEnum.DEADPRICE.getCode(), DigitBaseUtils.formatDigit(order.getDeadPriceOnenight()));
            map.put(HotelUidOrderDetailEnum.STAR.getCode(), MapperUtils.trim(order.getStar()));
            map.put(HotelUidOrderDetailEnum.CITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getCityName(), order.getCityNameEn()));
            map.put(HotelUidOrderDetailEnum.PROVINCENAME.getCode(), MapperUtils.getValByLang(lang, order.getProvinceName(), order.getProvinceNameEn()));
            map.put(HotelUidOrderDetailEnum.COUNTRYNAME.getCode(), MapperUtils.getValByLang(lang, order.getCountryName(), order.getCountryNameEn()));
            map.put(HotelUidOrderDetailEnum.CLIENTNAME.getCode(), MapperUtils.trim(order.getClientName()));
            map.put(HotelUidOrderDetailEnum.ORDERTYPE.getCode(), MapperUtils.convertOrderType(order.getOrderType()));
            map.put(HotelUidOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            map.put(HotelUidOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrivalDateTime()));
            map.put(HotelUidOrderDetailEnum.DEPARTUREDATETIME.getCode(), MapperUtils.trim(order.getDepartureDateTime()));
            map.put(HotelUidOrderDetailEnum.ROOMPRICE.getCode(), DigitBaseUtils.formatDigit(order.getRoomPrice()));
            map.put(HotelUidOrderDetailEnum.AVGPRICE.getCode(), DigitBaseUtils.formatDigit(order.getAvgprice()));
            map.put(HotelUidOrderDetailEnum.CUSTOMEREVAL.getCode(), DigitBaseUtils.formatDigit(order.getCustomereval(), 1));
            map.put(HotelUidOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(HotelUidOrderDetailEnum.HOTELGROUPNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotelGroupName(), order.getHotelGroupNameEn()));
            map.put(HotelUidOrderDetailEnum.REALPAYWITHSERVICE.getCode(), MapperUtils.convertDigitToZero(DigitBaseUtils.formatDigit(order.getRealPayWithService())));
            map.put(HotelUidOrderDetailEnum.HOTELNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotelName(), order.getHotelNameEn()));
            map.put(HotelUidOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccountCode()));
            map.put(HotelUidOrderDetailEnum.BASICROOMTYPENAME.getCode(), MapperUtils.trim(order.getBasicRoomTypeName()));
            result.add(map);
        }
        return result;
    }


    protected List<Map<String, Object>> convertDetailMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportHotelOrderInfo> data = detailInfo.getHtlOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }

        Map yesOrNotMap = BaseDataUtils.getYesOrNoMap(lang);
        for (OnlineReportHotelOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(HotelOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrderId()));
            map.put(HotelOrderDetailEnum.ORDERSTATUS.getCode(), order.getOrderStatus());
            map.put(HotelOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(HotelOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(HotelOrderDetailEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorpName()));
            map.put(HotelOrderDetailEnum.CANCELREASON_DESC.getCode(), MapperUtils.trim(order.getCancelreason()));
            map.put(HotelOrderDetailEnum.CANCEL_ESTI_SAVE_AMOUNT.getCode(), MapperUtils.convertDigitToZero(order.getCancelEstiSaveAmount()));
//            map.put(HotelOrderDetailEnum.COMPANYGROUP.getCode(), MapperUtils.trim(order.getCompanygroup()));
//            map.put(HotelOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
//            map.put(HotelOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getAccountId()));
//            map.put(HotelOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccountCode()));
//            map.put(HotelOrderDetailEnum.ACCOUNTNAME.getCode(), MapperUtils.trim(order.getAccountName()));
//            map.put(HotelOrderDetailEnum.SUBACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getSubAccountId()));
//            map.put(HotelOrderDetailEnum.SUBACCOUNTCODE.getCode(), MapperUtils.trim(order.getSubAccountCode()));
//            map.put(HotelOrderDetailEnum.SUBACCOUNTNAME.getCode(), MapperUtils.trim(order.getSubAccountName()));
            map.put(HotelOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(HotelOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(HotelOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
//            map.put(HotelOrderDetailEnum.WORKCITY.getCode(), MapperUtils.trim(order.getWorkCity()));
            map.put(HotelOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRankName()));
            map.put(HotelOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCostCenter1()));
            map.put(HotelOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCostCenter2()));
            map.put(HotelOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCostCenter3()));
            map.put(HotelOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCostCenter4()));
            map.put(HotelOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCostCenter5()));
            map.put(HotelOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCostCenter6()));
            map.put(HotelOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(HotelOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(HotelOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(HotelOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
            map.put(HotelOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
            map.put(HotelOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
            map.put(HotelOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
            map.put(HotelOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
            map.put(HotelOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
            map.put(HotelOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(HotelOrderDetailEnum.ISONLINE.getCode(), MapperUtils.convertBookTypeSingle(order.getIsOnline()));
//            map.put(HotelOrderDetailEnum.FEETYPE.getCode(), MapperUtils.trim(order.getFeeType()));
            map.put(HotelOrderDetailEnum.PREPAYTYPE.getCode(), HotelOrderDetailService.convertPrepayType(MapperUtils.trim(order.getPrepayType())));
//            map.put(HotelOrderDetailEnum.ACBPREPAYTYPE.getCode(), MapperUtils.trim(order.getAcbPrepayType()));
//            map.put(HotelOrderDetailEnum.ISBOSS.getCode(), MapperUtils.trim(order.getBosstype()));
            map.put(HotelOrderDetailEnum.JOURNEYNO.getCode(), MapperUtils.trim(order.getJourneyNo()));
            map.put(HotelOrderDetailEnum.TRIPID.getCode(), MapperUtils.convertDigitToDash(order.getTripId()));
//            map.put(HotelOrderDetailEnum.JOUNARYREASON.getCode(), MapperUtils.trim(order.getJourneyReason()));
//            map.put(HotelOrderDetailEnum.PROJECT.getCode(), MapperUtils.trim(order.getProject()));
//            map.put(HotelOrderDetailEnum.VERBALAUTHORIZE.getCode(), MapperUtils.convertTorF(order.getVerbalAuthorize(), yesOrNotMap));
//            map.put(HotelOrderDetailEnum.CONFIRMPERSON.getCode(), MapperUtils.trim(order.getConfirmPerson()));
//            map.put(HotelOrderDetailEnum.CONFIRMPTYPE.getCode(), MapperUtils.trim(order.getConfirmType()));
//            map.put(HotelOrderDetailEnum.CONFIRMPERSON2.getCode(), MapperUtils.trim(order.getConfirmPerson2()));
//            map.put(HotelOrderDetailEnum.CONFIRMPTYPE2.getCode(), MapperUtils.trim(order.getConfirmType2()));
            map.put(HotelOrderDetailEnum.GROUPMONTH.getCode(), MapperUtils.convertDigitToString(order.getGroupMonth()));
            /////////////////////////////////////////////////////////////////////
            map.put(HotelOrderDetailEnum.DEALDATE.getCode(), MapperUtils.trim(order.getDealDate()));
            map.put(HotelOrderDetailEnum.DEADPRICE.getCode(), DigitBaseUtils.formatDigit(order.getDeadPriceOnenight()));
            map.put(HotelOrderDetailEnum.ISMIXPAYMENT.getCode(), MapperUtils.convertTorF(order.getIsMixPayment(), yesOrNotMap));
            map.put(HotelOrderDetailEnum.SETTLEMENTPERSONAMT.getCode(), DigitBaseUtils.formatDigit(order.getSettlementPersonAmt()));
            map.put(HotelOrderDetailEnum.SETTLEMENTACCNTAMT.getCode(), DigitBaseUtils.formatDigit(order.getSettlementAccntAmt()));
            map.put(HotelOrderDetailEnum.HOTELNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotelName(), order.getHotelNameEn()));
//            map.put(HotelOrderDetailEnum.HOTELGROUPNAME.getCode(), MapperUtils.getValByLang(lang, order.getHotelGroupName(), order.getHotelGroupNameEn()));
            map.put(HotelOrderDetailEnum.HOTELBRANDNAME.getCode(), MapperUtils.trim(order.getHotelBrandName()));
            map.put(HotelOrderDetailEnum.ISOVERSEA.getCode(), MapperUtils.convertIsOverSea(order.getIsOversea(), lang));
            map.put(HotelOrderDetailEnum.STAR.getCode(), MapperUtils.trim(order.getStar()));
//            map.put(HotelOrderDetailEnum.ZONE.getCode(), MapperUtils.trim(order.getZone()));
            map.put(HotelOrderDetailEnum.LOCATION.getCode(), convertLocation(MapperUtils.trim(order.getLocation())));
            map.put(HotelOrderDetailEnum.CITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getCityName(), order.getCityNameEn()));
//            map.put(HotelOrderDetailEnum.CITYLEVEL.getCode(), MapperUtils.trim(order.getPcitylevel())); // 使用父级城市id
            map.put(HotelOrderDetailEnum.PROVINCENAME.getCode(), MapperUtils.getValByLang(lang, order.getProvinceName(), order.getProvinceNameEn()));
            map.put(HotelOrderDetailEnum.COUNTRYNAME.getCode(), MapperUtils.getValByLang(lang, order.getCountryName(), order.getCountryNameEn()));
            map.put(HotelOrderDetailEnum.CLIENTNAME.getCode(), MapperUtils.trim(order.getClientName()));
            map.put(HotelOrderDetailEnum.ORDERTYPE.getCode(), MapperUtils.convertOrderType(order.getOrderType()));
            map.put(HotelOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            map.put(HotelOrderDetailEnum.ORDERROOMNUM.getCode(), MapperUtils.convertDigitToZero(order.getOrderRoomNum()));
            map.put(HotelOrderDetailEnum.PERSONS.getCode(), MapperUtils.convertDigitToZero(order.getPersons()));
            map.put(HotelOrderDetailEnum.DAYNUM.getCode(), MapperUtils.convertDigitToZero(order.getDayNum()));
            map.put(HotelOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrivalDateTime()));
            map.put(HotelOrderDetailEnum.DEPARTUREDATETIME.getCode(), MapperUtils.trim(order.getDepartureDateTime()));
//            map.put(HotelOrderDetailEnum.ADDBREAKFASTPRICE.getCode(), DigitBaseUtils.formatDigitV2(order.getAddBreakfast()));
            map.put(HotelOrderDetailEnum.POSTAMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getPostAmount()));
            map.put(HotelOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getServiceFee()));
            map.put(HotelOrderDetailEnum.REFUNDTIME.getCode(), MapperUtils.trim(order.getRefundTime()));
            map.put(HotelOrderDetailEnum.RFDAMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getRfdAmount()));
            map.put(HotelOrderDetailEnum.RFDQUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getRfdQuantity()));
            map.put(HotelOrderDetailEnum.BREAKFAST.getCode(), MapperUtils.convertDigitToZero(order.getBreakfast()));
//            map.put(HotelOrderDetailEnum.ISGDSORDER.getCode(), MapperUtils.convertTorF(order.getIsGdsOrder(), yesOrNotMap));
//            map.put(HotelOrderDetailEnum.ISCU.getCode(), MapperUtils.convertTorF(order.getIsCu(), yesOrNotMap));
//            map.put(HotelOrderDetailEnum.ISTMCPENJOY.getCode(), MapperUtils.convertTorF(order.getIsTmcpEnjoy(), yesOrNotMap));
            map.put(HotelOrderDetailEnum.ROOMNAME.getCode(), MapperUtils.trim(order.getRoomName()));
            map.put(HotelOrderDetailEnum.ISRC.getCode(), MapperUtils.convertTorF(order.getIsRc(), yesOrNotMap));
            map.put(HotelOrderDetailEnum.REASONCODE.getCode(), MapperUtils.trim(order.getReasonCode()));
            map.put(HotelOrderDetailEnum.LOWREASONINFO.getCode(), MapperUtils.getValByLang(lang, order.getLowReasoninfo(), order.getLowReasoninfoEn()));
            map.put(HotelOrderDetailEnum.LOWPRICEENVV.getCode(), MapperUtils.trim(order.getLowPriceEnVv()));
//            map.put(HotelOrderDetailEnum.MINPRICERC.getCode(), MapperUtils.trim(order.getMinPriceRc()));
//            map.put(HotelOrderDetailEnum.MINPRICERCVV.getCode(), MapperUtils.trim(order.getMinPriceRcVv()));
//            map.put(HotelOrderDetailEnum.MINPRICERCREASONINFO.getCode(), MapperUtils.getValByLang(lang, order.getMinPriceRcReasoninfo(), order.getMinPriceRcReasoninfoEn()));
//            map.put(HotelOrderDetailEnum.AGREEMENTRC.getCode(), MapperUtils.trim(order.getAgreementRc()));
//            map.put(HotelOrderDetailEnum.AGREEMENTRCVV.getCode(), MapperUtils.trim(order.getAgreementRcVv()));
//            map.put(HotelOrderDetailEnum.AGREEMENTREASONINFO.getCode(), MapperUtils.getValByLang(lang, order.getAgreementReasoninfo(), order.getAgreementReasoninfoEn()));
//            map.put(HotelOrderDetailEnum.OCURRENCY.getCode(), MapperUtils.trim(order.getOCurrency()));
//            map.put(HotelOrderDetailEnum.OEXCHANGERATE.getCode(), DigitBaseUtils.formatDigit(order.getOExchangerate(), FOUR_DIGIT_NUM));
            map.put(HotelOrderDetailEnum.POSTSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getPostservicefee()));
            map.put(HotelOrderDetailEnum.STARTTRUE.getCode(), MapperUtils.trim(order.getStarTrue()));
            map.put(HotelOrderDetailEnum.MASTERHOTELID.getCode(), MapperUtils.convertDigitToString(order.getMasterhotelid()));
            map.put(HotelOrderDetailEnum.ROOMPRICE.getCode(), DigitBaseUtils.formatDigit(order.getRoomPrice()));
//            map.put(HotelOrderDetailEnum.COUPONAMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getCouponamount()));
            map.put(HotelOrderDetailEnum.SERVERFROMTYPE.getCode(), MapperUtils.trim(order.getServerfromtype()));
//            map.put(HotelOrderDetailEnum.ZIPCODE.getCode(), MapperUtils.trim(order.getZipcode()));
            map.put(HotelOrderDetailEnum.ADDRESS.getCode(), MapperUtils.trim(order.getAddress()));
//            map.put(HotelOrderDetailEnum.ISSAMEDAY.getCode(), MapperUtils.trim(order.getIsSameday()));
//            map.put(HotelOrderDetailEnum.ISWORKTIME.getCode(), MapperUtils.trim(order.getIsWorktime()));
            map.put(HotelOrderDetailEnum.AVGPRICE.getCode(), DigitBaseUtils.formatDigit(order.getAvgprice()));
            map.put(HotelOrderDetailEnum.CORPREALPAY.getCode(), DigitBaseUtils.formatDigit(order.getCorpRealPay()));
            map.put(HotelOrderDetailEnum.REALPAYWITHSERVICE.getCode(), DigitBaseUtils.formatDigit(order.getRealPayWithService()));
            map.put(HotelOrderDetailEnum.HOTELPASSENGERID.getCode(), MapperUtils.trim(order.getHotelpassengerid()));
//            map.put(HotelOrderDetailEnum.DOUBLEBOOKEDORDERID.getCode(), MapperUtils.trim(order.getDoublebookedorderid()));
//            map.put(HotelOrderDetailEnum.DOUBLEBOOKEDRC.getCode(), MapperUtils.trim(order.getDoublebookedrc()));
//            map.put(HotelOrderDetailEnum.DOUBLEBOOKEDRCDETAIL.getCode(), MapperUtils.trim(order.getDoublebookedrcdetail()));
//            map.put(HotelOrderDetailEnum.DOUBLEBOOKEDRCREASON.getCode(), MapperUtils.trim(order.getDoublebookedrcreason()));
//            map.put(HotelOrderDetailEnum.AUDITORID.getCode(), MapperUtils.trim(order.getAuditorid()));
//            map.put(HotelOrderDetailEnum.AUDITORID2.getCode(), MapperUtils.trim(order.getAuditorid2()));
            map.put(HotelOrderDetailEnum.SAVE_AMOUNT_3C.getCode(), DigitBaseUtils.formatDigit(order.getSaveAmount3c()));
//            map.put(HotelOrderDetailEnum.SAVE_AMOUNT_PROMOTION.getCode(), DigitBaseUtils.formatDigit(order.getSaveAmountPromotion()));
//            map.put(HotelOrderDetailEnum.SAVE_AMOUNT_PREMINM.getCode(), DigitBaseUtils.formatDigit(order.getSaveAmountPremium()));
            map.put(HotelOrderDetailEnum.CUSTOMEREVAL.getCode(), DigitBaseUtils.formatDigit(order.getCustomereval(), 1));
            map.put(HotelOrderDetailEnum.CONTROL_SAVE.getCode(), DigitBaseUtils.formatDigit(order.getControlSave()).toString());
//            map.put(HotelOrderDetailEnum.DEFINDFLAG.getCode(), MapperUtils.trim(order.getDefineflag()));
//            map.put(HotelOrderDetailEnum.DEFINDFLAG2.getCode(), MapperUtils.trim(order.getDefineflag2()));
            map.put(HotelOrderDetailEnum.ORIGINROOMDISTRIBUTIONPRICES.getCode(), MapperUtils.trim(order.getOriginRoomDistributionPrices()));
            map.put(HotelOrderDetailEnum.STDINDUSTRY1.getCode(), MapperUtils.trim(order.getStdIndustry1()));
            map.put(HotelOrderDetailEnum.STDINDUSTRY2.getCode(), MapperUtils.trim(order.getStdIndustry2()));
//            map.put(HotelOrderDetailEnum.ALLOCATION_MODE.getCode(), MapperUtils.trim(order.getAllocationModeDesc()));
//            map.put(HotelOrderDetailEnum.HOUSESHARE_MODE_TYPE.getCode(), MapperUtils.trim(order.getHouseshareModeType()));
            result.add(map);
        }
        return result;
    }

    private String convertOrderStatus(String orderStauts) {
        Map<String, String> map = HotelOrderStatusEnum.toMap();

        return map.get(MapperUtils.trim(orderStauts));
    }

    private String convertLocation(String location) {
        if (StringUtils.equalsIgnoreCase("0", location)) {
            return GlobalConst.STRING_EMPTY;
        }
        return MapperUtils.trim(location);
    }


    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel;
    }
}
