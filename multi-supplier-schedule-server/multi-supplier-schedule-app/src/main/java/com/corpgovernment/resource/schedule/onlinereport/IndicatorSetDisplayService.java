package com.corpgovernment.resource.schedule.onlinereport;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.risk.RiskOrderReportBO;
import com.corpgovernment.resource.schedule.onlinereport.indicator.IndicatorSetDisplayAdaptor;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2022/10/19 14:25
 */

@Service
@Slf4j
public class IndicatorSetDisplayService {

    protected static final String LOG_TITLE = IndicatorSetDisplayService.class.getSimpleName();



    @Autowired
    private IndicatorSetDisplayAdaptor indicatorSetDisplayAdaptor;


    public Object query(RiskOrderReportBO request, String lang) throws BusinessException {
        if (request != null && StringUtils.isNotEmpty(request.getUid()) && StringUtils.isNotEmpty(request.getReportId())) {
//            UserPermissionsBo userPermissionsBo = userPermissionService.queryUserPermissions(request.getUid());
            return indicatorSetDisplayAdaptor.query(request, lang);
        } else {
            log.warn(LOG_TITLE, String.format("%s no data", request));
            throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
        }
    }

    public Object update(RiskOrderReportBO request) throws BusinessException {
        if(request != null && StringUtils.isNotEmpty(request.getUid()) && StringUtils.isNotEmpty(request.getReportId())){
            return indicatorSetDisplayAdaptor.update(request);
        }
        else {
            log.warn(LOG_TITLE,String.format("%s no data",request));
            throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
        }

    }
}
