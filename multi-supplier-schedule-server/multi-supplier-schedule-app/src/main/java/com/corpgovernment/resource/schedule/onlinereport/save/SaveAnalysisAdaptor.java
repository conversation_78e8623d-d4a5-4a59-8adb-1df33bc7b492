package com.corpgovernment.resource.schedule.onlinereport.save;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportTypeionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BuSaveDistributionInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.GeneralPotentialSaveInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.GeneralSaveDistributionInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.GeneralSaveInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPSaveProportionDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPSaveProportionDetailResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPotentialSaveLowRcResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPotentialSaveLowRclRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPromotionsSaveDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportPromotionsSaveDetailResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportProportion;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveGeneralRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveGeneralResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveProportionDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveProportionDetailResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveProportionRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportSaveProportionResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopPotentialSaveRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopPotentialSaveResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendLegend;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendPoint;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.PotentialSaveLowRcInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportSaveLossAnalyseRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportSaveLossAnalyseResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.CommonUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.OrpGsonUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.ExportConstant.TWO;


/**
 * <AUTHOR>
 * @date 2021/11/5 21:36
 * @Desc 节省分析 test
 */
@Service
@Slf4j
public class SaveAnalysisAdaptor extends BaseReportDataAdaptor<Object> {

    private final static String PERCENT_QUOTE = "%";

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private SaveAnalysisService saveAnalysisService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        log.info("SaveAnalysisAdaptor convert:{}", JsonUtils.toJsonString(baseCondition));

        Map resp = null;
        String reportId = baseCondition.getBaseQueryCondition().getReportId();
        String lang = baseCondition.getLang();
        String productType = baseCondition.getProductType();
        if (StringUtils.equalsIgnoreCase(reportId, "TravelAnalysis:SaveLoss:Save")) {
            if (StringUtils.equalsIgnoreCase(IndexEnum.GENERAL_SAVE.toString(), baseCondition.getIndex())
                    || StringUtils.equalsIgnoreCase(IndexEnum.GENERAL_SAVE_DISTRIBUTION.toString(), baseCondition.getIndex())) {
                OnlineReportSaveGeneralRequest request = new OnlineReportSaveGeneralRequest();
                if (StringUtils.isNotEmpty(baseCondition.getQueryBu())) {
                    request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                }
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                Map map = new HashMap();
                map.put("index", baseCondition.getIndex());
                // 概览节省分布不需要total信息
                map.put("needTotal", "F");
                request.setExtData(map);

                log.info("SaveAnalysisAdaptor convert request:{}", JsonUtils.toJsonString(request));

                OnlineReportSaveGeneralResponse responseType = corpOnlineReportPlatformService.querySaveGeneralInfo(request);

                log.info("SaveAnalysisAdaptor convert responseType:{}", JsonUtils.toJsonString(responseType));
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    if (StringUtils.equalsIgnoreCase(IndexEnum.GENERAL_SAVE.toString(), baseCondition.getIndex())) {
                        return responseType.getSaveInfo();
                    }
                    GeneralSaveDistributionInfo distributionInfo = Optional.ofNullable(responseType.getSaveDistributionInfo()).orElse(new GeneralSaveDistributionInfo());
                    if (StringUtils.equalsIgnoreCase(IndexEnum.GENERAL_SAVE_DISTRIBUTION.toString(), baseCondition.getIndex())) {
                        if (request.getQueryBu() != null) {
                            return convertSaveDistribution(distributionInfo, request.getQueryBu(), lang);
                        } else {
                            return convertSaveDistributionFrontPage(Optional.ofNullable(responseType.getBuSaveDisInfo()).orElse(new BuSaveDistributionInfo()), lang);
                        }
                    }
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(responseType)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                    }
                    throw businessException;
                }
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.PROMOTIONS_SAVE_DETAIL.toString(), baseCondition.getIndex())) {
                OnlineReportPromotionsSaveDetailRequest request = new OnlineReportPromotionsSaveDetailRequest();
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                OnlineReportPromotionsSaveDetailResponse responseType = corpOnlineReportPlatformService.queryPromotionsSaveDetail(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    if (org.apache.commons.collections4.CollectionUtils.isEmpty(responseType.getSavelist())) {
                        throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
                    }
                    return responseType.getSavelist();
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(responseType)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                    }
                    throw businessException;
                }
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_DISTRIBUTION.toString(), baseCondition.getIndex())) {
                OnlineReportSaveProportionRequest request = new OnlineReportSaveProportionRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                OnlineReportSaveProportionResponse response = corpOnlineReportPlatformService.querySaveProportion(request);
                if (response == null || response.getResponseCode() != 20000) {
                    throw new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                }
                List<OnlineReportProportion> proportions = response.getProportions();
                Map data = new HashMap();
                for (OnlineReportProportion proportion : proportions) {
                    data.put(proportion.getType(), proportion.getTypeNum());
                    data.put(proportion.getType() + "Percent", proportion.typeNumPercent);
                }
                List<Map> legends = new ArrayList<>();
                for (OnlineReportTrendLegend legend : response.getLegends()) {
                    Map var = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(legend), Map.class);
                    var.put("percentage", var.get("key") + "Percent");
                    legends.add(var);
                }
                resp = new HashMap();
                resp.put("data", data);
                resp.put("legends", legends);
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_DISTRIBUTION_DETAIL.toString(), baseCondition.getIndex())) {
                OnlineReportSaveProportionDetailRequest request = new OnlineReportSaveProportionDetailRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                OnlineReportSaveProportionDetailResponse response = corpOnlineReportPlatformService.querySaveProportionDetail(request);
                if (response == null || response.getResponseCode() != 20000) {
                    throw new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                }
                resp = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(response), Map.class);
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_TREND.toString(), baseCondition.getIndex())) {
                // 节省趋势
                OnlineReportTrendRequest request = new OnlineReportTrendRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
                if (StringUtils.isEmpty(baseCondition.getDateDimension())) {
                    request.setDateDimension(QueryReportAggDateDimensionEnum.month);
                } else {
                    request.setDateDimension(QueryReportAggDateDimensionEnum.valueOf(baseCondition.getDateDimension()));
                }
                request.setAggType(QueryReportAggTypeEnum.valueOf(baseCondition.getAggType()));
                baseCondition.getExtData().put("lang", baseCondition.getLang());
                request.setExtData(baseCondition.getExtData());
                request.setProductType(productType);
                OnlineReportTrendResponse response = corpOnlineReportPlatformService.queryReportSaveTrend(request);
                if (response == null || response.getResponseCode() != 20000) {
                    throw new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                }
                resp = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(response), Map.class); // todo 需要统一返回的数据结构
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_DETAIL.toString(), baseCondition.getIndex())) {
                // 节省趋势
                OnlineReportTrendRequest request = new OnlineReportTrendRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
                request.setDateDimension(QueryReportAggDateDimensionEnum.valueOf(baseCondition.getDateDimension()));
                request.setAggType(QueryReportAggTypeEnum.valueOf(baseCondition.getAggType()));
                baseCondition.getExtData().put("lang", baseCondition.getLang());
                request.setExtData(baseCondition.getExtData());
                request.setProductType(productType);
                OnlineReportTrendResponse response = corpOnlineReportPlatformService.queryReportSaveDetail(request);
                if (response == null || response.getResponseCode() != 20000) {
                    throw new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                }
                return convertDetail(response);
            }
        }
        if (StringUtils.equalsIgnoreCase(reportId, "TravelAnalysis:SaveLoss:SavePotential")) {
            if (StringUtils.equalsIgnoreCase(IndexEnum.GENERAL_SAVE_POTENTIAL.toString(), baseCondition.getIndex())) {
                OnlineReportSaveGeneralRequest request = new OnlineReportSaveGeneralRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                Map map = new HashMap();
                map.put("index", baseCondition.getIndex());
                request.setExtData(map);
                OnlineReportSaveGeneralResponse responseType = corpOnlineReportPlatformService.querySaveGeneralInfo(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    GeneralPotentialSaveInfo generalPotentialSaveInfo = Optional.ofNullable(responseType.getPotentialSaveInfo()).orElse(new GeneralPotentialSaveInfo());
                    return ImmutableMap.builder()
                            .put("potentialSaveAmount", MapperUtils.convertDigitToZero(generalPotentialSaveInfo.getPotentialSaveAmount()))
                            .put("overAmount", MapperUtils.convertDigitToZero(generalPotentialSaveInfo.getOverAmount()))
                            .put("rcTimes", MapperUtils.convertDigitToZero(generalPotentialSaveInfo.getRcTimes()))
                            .put("refundloss", MapperUtils.convertDigitToZero(generalPotentialSaveInfo.getRefundloss()))
                            .put("refundtkt", MapperUtils.convertDigitToZero(generalPotentialSaveInfo.getRefundtkt()))
                            .put("rebookloss", MapperUtils.convertDigitToZero(generalPotentialSaveInfo.getRebookloss()))
                            .put("rebooktkt", MapperUtils.convertDigitToZero(generalPotentialSaveInfo.getRebooktkt())).build();
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(responseType)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                    }
                    throw businessException;
                }
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.LOW_RC.toString(), baseCondition.getIndex())) {
                OnlineReportPotentialSaveLowRclRequest request = new OnlineReportPotentialSaveLowRclRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                OnlineReportPotentialSaveLowRcResponse responseType = corpOnlineReportPlatformService.queryPotentialSaveLowRcDetail(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                        return convertFlightLowRcMap(responseType.getLowRcList(), lang);
                    }
                    if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                        return convertHotelLowRcMap(responseType.getLowRcList(), lang);
                    }
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(responseType)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                    }
                    throw businessException;
                }
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.POTENTIAL_TOP_DEPT.toString(), baseCondition.getIndex())) {
                AnalysisObjectEnum analysisObjectEnum = StringUtils.isEmpty(baseCondition.getAnalysisObject()) ?
                        AnalysisObjectEnum.CORP : AnalysisObjectEnum.valueOf(baseCondition.getAnalysisObject().toUpperCase());
                OnlineReportTopPotentialSaveRequest request = new OnlineReportTopPotentialSaveRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setAnalysisObjectEnum(analysisObjectEnum);
                request.setPage(baseCondition.getPager());
                request.setProductType(productType);
                OnlineReportTopPotentialSaveResponse responseType = corpOnlineReportPlatformService.queryTopPotentialSaveDetail(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    List list = new ArrayList();
                    if (CollectionUtils.isNotEmpty(responseType.getDeptDetail())) {
                        for (String detail : responseType.getDeptDetail()) {
                            Map map = OrpGsonUtils.fromToJsonTypeTest(detail, Map.class);
                            for (Object key : map.keySet()) {
                                String keyString = (String) key;
                                if (keyString.endsWith("AMOUNT") || keyString.endsWith("LOSS")
                                        || keyString.endsWith("POTENTIAL") || keyString.endsWith("CORP_PRICE_ADJ")) {
                                    map.put(key, CommonUtils.fmtMicrometer(String.valueOf(map.get(key))));
                                    continue;
                                }
                                map.put(key, String.valueOf(map.get(key)));
                            }
                            list.add(map);
                        }
                    }
                    List<HeaderKeyValMap> headerKeyValMaps = Optional.ofNullable(responseType.getHeaderData()).orElse(new ArrayList<>());
                    int totalRecords = Optional.ofNullable(responseType.getTotalRecords()).orElse(0);
                    return ImmutableMap.builder()
                            .put("header", headerKeyValMaps)
                            .put("list", list)
                            .put("totalRecords", totalRecords).build();
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(responseType)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                    }
                    throw businessException;
                }
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.POTENTIAL_SAVE_DISTRIBUTION.toString(), baseCondition.getIndex())) {
                OnlineReportSaveProportionRequest request = new OnlineReportSaveProportionRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                OnlineReportSaveProportionResponse response = corpOnlineReportPlatformService.queryPSaveProportion(request);
                if (response == null || response.getResponseCode() != 20000) {
                    throw new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                }
                List<OnlineReportProportion> proportions = response.getProportions();
                Map data = new HashMap();
                for (OnlineReportProportion proportion : proportions) {
                    data.put(proportion.getType(), proportion.getTypeNum());
                    data.put(proportion.getType() + "Percent", proportion.typeNumPercent);
                }
                List<Map> legends = new ArrayList<>();
                for (OnlineReportTrendLegend legend : response.getLegends()) {
                    Map var = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(legend), Map.class);
                    var.put("percentage", var.get("key") + "Percent");
                    legends.add(var);
                }
                resp = new HashMap();
                resp.put("data", data);
                resp.put("legends", legends);
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.POTENTIAL_SAVE_DISTRIBUTION_DETAIL.toString(), baseCondition.getIndex())) {
                OnlineReportPSaveProportionDetailRequest request = new OnlineReportPSaveProportionDetailRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                OnlineReportPSaveProportionDetailResponse response = corpOnlineReportPlatformService.queryPSaveProportionDetail(request);
                if (response == null || response.getResponseCode() != 20000) {
                    throw new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                }
                resp = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(response), Map.class);// todo 需要统一返回的数据结构
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.POTENTIAL_SAVE_TREND.toString(), baseCondition.getIndex())) {
                // 潜在 节省趋势
                OnlineReportTrendRequest request = new OnlineReportTrendRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
                request.setDateDimension(QueryReportAggDateDimensionEnum.valueOf(baseCondition.getDateDimension()));
                request.setAggType(QueryReportAggTypeEnum.valueOf(baseCondition.getAggType()));
                baseCondition.getExtData().put("lang", baseCondition.getLang());
                request.setExtData(baseCondition.getExtData());
                request.setProductType(productType);
                OnlineReportTrendResponse response = corpOnlineReportPlatformService.queryReportPotentialSaveTrend(request);
                if (response == null || response.getResponseCode() != 20000) {
                    throw new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                }
                resp = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(response), Map.class); // todo 需要统一返回的数据结构
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.POTENTIAL_SAVE_DETAIL.toString(), baseCondition.getIndex())) {
                // 潜在 节省趋势
                OnlineReportTrendRequest request = new OnlineReportTrendRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
                request.setDateDimension(QueryReportAggDateDimensionEnum.valueOf(baseCondition.getDateDimension()));
                request.setAggType(QueryReportAggTypeEnum.valueOf(baseCondition.getAggType()));
                baseCondition.getExtData().put("lang", baseCondition.getLang());
                request.setExtData(baseCondition.getExtData());
                request.setProductType(productType);
                OnlineReportTrendResponse response = corpOnlineReportPlatformService.queryReportPotentialSaveDetail(request);
                if (response == null || response.getResponseCode() != 20000) {
                    throw new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                }
                return convertDetail(response);
            }
        }

        /**
         * 节省分析
         */
        if (StringUtils.equalsIgnoreCase(reportId, "TravelAnalysis:SaveLoss:SaveAnalysis")) {
            ReportSaveLossAnalyseResponse responseType = new ReportSaveLossAnalyseResponse();
            ReportSaveLossAnalyseRequest request = new ReportSaveLossAnalyseRequest();
            request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setProductType(productType);
            request.setStartCityId(baseCondition.getStartCityId());
            request.setArriveCityId(baseCondition.getArriveCityId());
            request.setFlightCity(baseCondition.getFlightCity());
            Map map = new HashMap();
            map.put("index", baseCondition.getIndex());
            request.setExtData(map);
            if (StringUtils.isEmpty(baseCondition.getIndex())) {
                responseType = corpOnlineReportPlatformService.querySaveLossAnalyse(request);
                // return responseType.getNotices();
            } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_PRE_ORDER.toString(), baseCondition.getIndex())) {
                responseType = corpOnlineReportPlatformService.querySaveLossPreOrderDate(request);
//                return responseType.getFlightBookDaysData();
            } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_POSITION_DIST.toString(), baseCondition.getIndex())) {
                responseType = corpOnlineReportPlatformService.querySaveLossPositionDist(request);
//                return responseType.getFlightPositionDist();
            } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_DISCOUNT_DIST.toString(), baseCondition.getIndex())) {
                responseType = corpOnlineReportPlatformService.querySaveLossDiscountDist(request);
//                return responseType.getFlightDiscountDist();
            } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_AVG_PRICE.toString(), baseCondition.getIndex())) {
                responseType = corpOnlineReportPlatformService.queryFlightAvgPriceDist(request);
//                return responseType.getFlightAvgTicketPriceDist();
            } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_AVG_MILEAGE.toString(), baseCondition.getIndex())) {
                responseType = corpOnlineReportPlatformService.queryFlightAvgMileageDist(request);
//                return responseType.getFlightAvgMileageDist();
            } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_STAR_DIST.toString(), baseCondition.getIndex())) {
                responseType = corpOnlineReportPlatformService.queryHotelStarDist(request);
//                return responseType.getHotelStarDist();
            } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_NIGHT_DIST.toString(), baseCondition.getIndex())) {
                responseType = corpOnlineReportPlatformService.queryHotelRoomNightDist(request);
//                return responseType.getHotelRoomNightDist();
            }
            if (Objects.isNull(responseType) || Objects.isNull(responseType.getResponseCode()) || responseType.getResponseCode() != 20000) {
                throw new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            }
            resp = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(responseType), Map.class);
        }
        return resp;
    }

    private Map convertDetail(OnlineReportTrendResponse response) {
        List<OnlineReportTrendLegend> legends = response.getData().getLegends();
        List<OnlineReportTrendPoint> data = response.getData().getData();
        List<Map> transformData = data.stream().map(
                point -> {
                    Map<String, Object> var = Maps.newHashMap();
                    Map<String, String> var2 = Maps.newHashMap();
                    for (Map.Entry<String, BigDecimal> entry : point.getData().entrySet()) {
                        if (entry.getKey().endsWith("Rate")) {
                            var2.put(entry.getKey(), entry.getValue().setScale(TWO).toPlainString() + "%");
                            continue;
                        }
                        if (entry.getKey().endsWith("Times")) {
                            var2.put(entry.getKey(), String.valueOf(entry.getValue().intValue()));
                            continue;
                        }
                        var2.put(entry.getKey(), entry.getValue().setScale(TWO).toPlainString());
                    }
                    var.put("axis", point.getAxis());
                    var.put("data", var2);
                    return var;
                }
        ).collect(Collectors.toList());
        return new HashMap<String, Object>() {{
            put("data", new HashMap<String, Object>() {{
                put("data", transformData);
                put("legends", OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(legends), List.class));
            }});
        }};
    }


    // 概览中的节省分布
    private Map convertSaveDistribution(GeneralSaveDistributionInfo saveDistributionInfo, QueryReportBuTypeEnum queryReportBuTypeEnum, String lang) {
        List legends = new ArrayList();
        Map legendItem1 = new HashMap() {{
            put("name", SharkUtils.get("Save.Agree", lang));
            put("key", "agree");
        }};
        legends.add(legendItem1);
        Map data = new HashMap() {
            {
                put("agree", MapperUtils.convertDigitToZero(saveDistributionInfo.getSaveAmount3c()));
            }
        };
        // 酒店去掉两方尊享
        if (saveDistributionInfo.getSaveAmount2c() != null && saveDistributionInfo.getSaveAmount2c().compareTo(BigDecimal.ZERO) > 0) {
            Map legendItem2 = new HashMap() {{
                put("name", SharkUtils.get("Save.TMC", lang));
                put("key", "tmc");
            }};
            legends.add(legendItem2);
            data.put("tmc", MapperUtils.convertDigitToZero(saveDistributionInfo.getSaveAmount2c()));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {

            // 酒店节省分布，去掉【商旅优惠活动】
            /*Map legendItem3 = new HashMap() {{
                put("name", SharkUtils.get("Save.HtlTrip", lang));
                put("key", "htlTrip");
            }};
            legends.add(legendItem3);
            data.put("htlTrip", MapperUtils.convertDigitToZero(saveDistributionInfo.getSaveAmountPromotion()));*/
        }
        Map legendItem4 = new HashMap() {{
            put("name", SharkUtils.get("savings.controlsavings", lang));
            put("key", "controlSave");
        }};
        legends.add(legendItem4);
        data.put("controlSave", MapperUtils.convertDigitToZero(saveDistributionInfo.getControlSave()));
        return ImmutableMap.builder()
                .put("legend", legends)
                .put("data", data).build();
    }

    private Map convertSaveDistributionFrontPage(BuSaveDistributionInfo buSaveDistributionInfo, String lang) {
        GeneralSaveDistributionInfo flt = Optional.ofNullable(buSaveDistributionInfo.getFlightSaveDis()).orElse(new GeneralSaveDistributionInfo());
        GeneralSaveDistributionInfo htl = Optional.ofNullable(buSaveDistributionInfo.getHotelSaveDis()).orElse(new GeneralSaveDistributionInfo());
        return ImmutableMap.builder()
                .put("flight", disTrend(flt, QueryReportBuTypeEnum.flight, lang))
                .put("hotel", disTrend(htl, QueryReportBuTypeEnum.hotel, lang)).build();
    }

    private Map disTrend(GeneralSaveDistributionInfo saveDistributionInfo, QueryReportBuTypeEnum queryReportBuTypeEnum, String lang) {
        List trendDataList = new ArrayList();
        Map thData = ImmutableMap.builder()
                .put("amount", MapperUtils.convertDigitToZero(saveDistributionInfo.getSaveAmount3c()))
                .put("amountPercent", MapperUtils.convertDigitToZero(saveDistributionInfo.getSaveAmount3cPercent())).build();
        trendDataList.add(ImmutableMap.builder()
                .put("axis", SharkUtils.get("Save.Agree", lang))
                .put("data", thData).build());
        // 酒店去掉两方尊享
        if (saveDistributionInfo.getSaveAmount2c() != null && saveDistributionInfo.getSaveAmount2c().compareTo(BigDecimal.ZERO) > 0) {
            Map twData = ImmutableMap.builder()
                    .put("amount", MapperUtils.convertDigitToZero(saveDistributionInfo.getSaveAmount2c()))
                    .put("amountPercent", MapperUtils.convertDigitToZero(saveDistributionInfo.getSaveAmount2cPercent())).build();
            trendDataList.add(ImmutableMap.builder()
                    .put("axis", SharkUtils.get("Save.TMC", lang))
                    .put("data", twData).build());
        }

        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            Map twData = ImmutableMap.builder()
                    .put("amount", MapperUtils.convertDigitToZero(saveDistributionInfo.getSaveAmountPromotion()))
                    .put("amountPercent", MapperUtils.convertDigitToZero(saveDistributionInfo.getSaveAmountPromotionPercent())).build();
            trendDataList.add(ImmutableMap.builder()
                    .put("axis", SharkUtils.get("Save.HtlTrip", lang))
                    .put("data", twData).build());
        }
        Map controlData = ImmutableMap.builder()
                .put("amount", MapperUtils.convertDigitToZero(saveDistributionInfo.getControlSave()))
                .put("amountPercent", MapperUtils.convertDigitToZero(saveDistributionInfo.getControlSavePercent())).build();
        trendDataList.add(ImmutableMap.builder()
                .put("axis", SharkUtils.get("savings.controlsavings", lang))
                .put("data", controlData).build());
        return ImmutableMap.builder()
                .put("totalSaveAmount", MapperUtils.convertDigitToZero(saveDistributionInfo.getSaveAmount()))
                .put("data", trendDataList).build();
    }

    private Map convertFlightLowRcMap(List<PotentialSaveLowRcInfo> saveLowRcInfos, String lang) {
        Map mapHeader1 = ImmutableMap.builder()
                .put("headerKey", "rcCode")
                .put("headerValue", "RC").build();
        Map mapHeader2 = ImmutableMap.builder()
                .put("headerKey", "rcTimes")
                .put("headerValue", SharkUtils.get("RCAnalysis.RCNum1", lang)).build();
        Map mapHeader3 = ImmutableMap.builder()
                .put("headerKey", "rcPercent")
                .put("headerValue", SharkUtils.get("ComplianceMonitor.numberpercentage", lang)).build();
        Map mapHeader4 = ImmutableMap.builder()
                .put("headerKey", "amount")
                .put("headerValue", SharkUtils.get("AirFeeDetail.netfare", lang)).build();
        Map mapHeader5 = ImmutableMap.builder()
                .put("headerKey", "corpPriceAdj")
                .put("headerValue", SharkUtils.get("Travelanalysis.lowest", lang)).build();
        Map mapHeader6 = ImmutableMap.builder()
                .put("headerKey", "potentialSaveAmount")
                .put("headerValue", SharkUtils.get("Save.AmtPotRate", lang)).build();
        Map mapHeader7 = ImmutableMap.builder()
                .put("headerKey", "potentialPercent")
                .put("headerValue", SharkUtils.get("Save.PotentialAmtPercent", lang)).build();
        List detailList = new ArrayList();
        if (CollectionUtils.isNotEmpty(saveLowRcInfos)) {
            for (PotentialSaveLowRcInfo saveLowRcInfo : saveLowRcInfos) {
                Map detail = ImmutableMap.builder()
                        .put("rcCode", StringUtils.isEmpty(saveLowRcInfo.getLowRcDesc()) ? saveLowRcInfo.getLowRcCode() : saveLowRcInfo.getLowRcCode().concat("-").concat(saveLowRcInfo.getLowRcDesc()))
                        .put("rcTimes", MapperUtils.convertDigitToZeroString(saveLowRcInfo.getRcTimes()))
                        .put("rcPercent", MapperUtils.convertDigitToZeroString(saveLowRcInfo.getRcPercent()).concat(PERCENT_QUOTE))
                        .put("amount", CommonUtils.fmtMicrometer(MapperUtils.convertDigitToZeroString(saveLowRcInfo.getAmount())))
                        .put("corpPriceAdj", CommonUtils.fmtMicrometer(MapperUtils.convertDigitToZeroString(saveLowRcInfo.getCorpPriceAdj())))
                        .put("potentialSaveAmount", CommonUtils.fmtMicrometer(MapperUtils.convertDigitToZeroString(saveLowRcInfo.getOverAmount())))
                        .put("potentialPercent", MapperUtils.convertDigitToZeroString(saveLowRcInfo.getOverAmountPercent()).concat(PERCENT_QUOTE)).build();
                detailList.add(detail);
            }
        }
        return ImmutableMap.builder()
                .put("header", Arrays.asList(mapHeader1, mapHeader2, mapHeader3, mapHeader4, mapHeader5, mapHeader6, mapHeader7))
                .put("list", detailList).build();
    }

    private Map convertHotelLowRcMap(List<PotentialSaveLowRcInfo> saveLowRcInfos, String lang) {
        Map mapHeader1 = ImmutableMap.builder()
                .put("headerKey", "rcCode")
                .put("headerValue", "RC").build();
        Map mapHeader2 = ImmutableMap.builder()
                .put("headerKey", "rcTimes")
                .put("headerValue", SharkUtils.get("RCAnalysis.RCNum1", lang)).build();
        Map mapHeader3 = ImmutableMap.builder()
                .put("headerKey", "rcPercent")
                .put("headerValue", SharkUtils.get("ComplianceMonitor.numberpercentage", lang)).build();
        Map mapHeader6 = ImmutableMap.builder()
                .put("headerKey", "potentialSaveAmount")
                .put("headerValue", SharkUtils.get("Save.AmtPotRate", lang)).build();
        Map mapHeader7 = ImmutableMap.builder()
                .put("headerKey", "potentialPercent")
                .put("headerValue", SharkUtils.get("Save.PotentialAmtPercent", lang)).build();
        List detailList = new ArrayList();
        if (CollectionUtils.isNotEmpty(saveLowRcInfos)) {
            for (PotentialSaveLowRcInfo saveLowRcInfo : saveLowRcInfos) {
                Map detail = ImmutableMap.builder()
                        .put("rcCode", StringUtils.isEmpty(saveLowRcInfo.getLowRcDesc()) ? saveLowRcInfo.getLowRcCode() : saveLowRcInfo.getLowRcCode().concat("-").concat(saveLowRcInfo.getLowRcDesc()))
                        .put("rcTimes", MapperUtils.convertDigitToZeroString(saveLowRcInfo.getRcTimes()))
                        .put("rcPercent", MapperUtils.convertDigitToZeroString(saveLowRcInfo.getRcPercent()).concat(PERCENT_QUOTE))
                        .put("potentialSaveAmount", CommonUtils.fmtMicrometer(MapperUtils.convertDigitToZeroString(saveLowRcInfo.getOverAmount())))
                        .put("potentialPercent", MapperUtils.convertDigitToZeroString(saveLowRcInfo.getOverAmountPercent()).concat(PERCENT_QUOTE)).build();
                detailList.add(detail);
            }
        }
        return ImmutableMap.builder()
                .put("header", Arrays.asList(mapHeader1, mapHeader2, mapHeader3, mapHeader6, mapHeader7))
                .put("list", detailList).build();
    }


    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        String reportId = baseCondition.getBaseQueryCondition().getReportId();
        String lang = baseCondition.getLang();
        String productType = baseCondition.getProductType();
        if (StringUtils.equalsIgnoreCase(reportId, "TravelAnalysis:SaveLoss:Save")) {
            if (StringUtils.equalsIgnoreCase(IndexEnum.GENERAL_SAVE.toString(), baseCondition.getIndex())
                    || StringUtils.equalsIgnoreCase(IndexEnum.GENERAL_SAVE_DISTRIBUTION.toString(), baseCondition.getIndex())) {
                OnlineReportSaveGeneralRequest request = new OnlineReportSaveGeneralRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                Map map = new HashMap();
                map.put("index", baseCondition.getIndex());
                // 概览节省分布不需要total信息
                map.put("needTotal", "F");
                request.setExtData(map);
                OnlineReportSaveGeneralResponse responseType = corpOnlineReportPlatformService.querySaveGeneralInfo(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    if (StringUtils.equalsIgnoreCase(IndexEnum.GENERAL_SAVE.toString(), baseCondition.getIndex())) {
                        GeneralSaveInfo saveInfo = Optional.ofNullable(responseType.getSaveInfo()).orElse(new GeneralSaveInfo());
                        if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                            chartExcelEntityList = saveAnalysisService.flightSaveGenralExcel(saveInfo, lang, baseCondition);
                        }
                        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                            chartExcelEntityList = saveAnalysisService.hotelSaveGenralExcel(saveInfo, lang, baseCondition);
                        }
                    }
                    if (StringUtils.equalsIgnoreCase(IndexEnum.GENERAL_SAVE_DISTRIBUTION.toString(), baseCondition.getIndex())) {
                        GeneralSaveDistributionInfo saveDistributionInfo = Optional.ofNullable(responseType.getSaveDistributionInfo()).orElse(new GeneralSaveDistributionInfo());
                        if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                            chartExcelEntityList = saveAnalysisService.flightSaveGenralDistributionExcel(saveDistributionInfo, lang, baseCondition);
                        }
                        if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                            chartExcelEntityList = saveAnalysisService.hotelSaveGenralDistributionExcel(saveDistributionInfo, lang, baseCondition);
                        }
                    }
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(responseType)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                    }
                    throw businessException;
                }
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.PROMOTIONS_SAVE_DETAIL.toString(), baseCondition.getIndex())) {
                OnlineReportPromotionsSaveDetailRequest request = new OnlineReportPromotionsSaveDetailRequest();
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                OnlineReportPromotionsSaveDetailResponse responseType = corpOnlineReportPlatformService.queryPromotionsSaveDetail(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    chartExcelEntityList = saveAnalysisService.hotelPromotionsSaveExcel(Optional.ofNullable(responseType.getSavelist()).orElse(new ArrayList<>()), baseCondition.getLang(), baseCondition);
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(responseType)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                    }
                    throw businessException;
                }
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_DISTRIBUTION.toString(), baseCondition.getIndex())) {
                OnlineReportSaveProportionRequest request = new OnlineReportSaveProportionRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                try {
                    OnlineReportSaveProportionResponse response = corpOnlineReportPlatformService.querySaveProportion(request);
                    if (response != null && response.getResponseCode() == 20000) {
                        return saveAnalysisService.SaveDistributionExcel(response, request, lang);
                    }
                } catch (Exception e) {
                    log.error(LOG_TITLE, e);
                }
            }
            if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_DISTRIBUTION_DETAIL.toString(), baseCondition.getIndex())) {
                OnlineReportSaveProportionDetailRequest request = new OnlineReportSaveProportionDetailRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                OnlineReportSaveProportionDetailResponse response;
                try {
                    try {
                        response = corpOnlineReportPlatformService.querySaveProportionDetail(request);
                        if (response != null && response.getResponseCode() == 20000) {
                            return saveAnalysisService.SaveDistributionDetailExcel(response, request, lang);
                        }
                    } catch (Exception e) {
                        log.error(LOG_TITLE, e);
                    }
                } catch (Exception e) {
                    log.error(LOG_TITLE, e);
                }
            }
            if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_TREND.toString(), baseCondition.getIndex())) {
                // 节省趋势
                OnlineReportTrendRequest request = new OnlineReportTrendRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
                request.setDateDimension(QueryReportAggDateDimensionEnum.valueOf(baseCondition.getDateDimension()));
                request.setAggType(QueryReportAggTypeEnum.valueOf(baseCondition.getAggType()));
                baseCondition.getExtData().put("lang", baseCondition.getLang());
                request.setExtData(baseCondition.getExtData());
                request.setProductType(productType);
                OnlineReportTrendResponse response;
                try {
                    response = corpOnlineReportPlatformService.queryReportSaveTrend(request);
                    if (response != null && response.getResponseCode() == 20000) {
                        return saveAnalysisService.SaveTrendExcel(response, request, lang);
                    }
                } catch (Exception e) {
                    log.error(LOG_TITLE, e);
                }
            }
            if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_DETAIL.toString(), baseCondition.getIndex())) {
                // 节省趋势
                OnlineReportTrendRequest request = new OnlineReportTrendRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
                request.setDateDimension(QueryReportAggDateDimensionEnum.valueOf(baseCondition.getDateDimension()));
                request.setAggType(QueryReportAggTypeEnum.valueOf(baseCondition.getAggType()));
                baseCondition.getExtData().put("lang", baseCondition.getLang());
                request.setExtData(baseCondition.getExtData());
                request.setProductType(productType);
                OnlineReportTrendResponse response;
                try {
                    response = corpOnlineReportPlatformService.queryReportSaveDetail(request);
                    if (response != null && response.getResponseCode() == 20000) {
                        return saveAnalysisService.SaveDetailExcel(response, request, lang);
                    }
                } catch (Exception e) {
                    log.error(LOG_TITLE, e);
                }
            }

        }
        if (StringUtils.equalsIgnoreCase(reportId, "TravelAnalysis:SaveLoss:SavePotential")) {
            if (StringUtils.equalsIgnoreCase(IndexEnum.GENERAL_SAVE_POTENTIAL.toString(), baseCondition.getIndex())) {
                OnlineReportSaveGeneralRequest request = new OnlineReportSaveGeneralRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                Map map = new HashMap();
                map.put("index", baseCondition.getIndex());
                request.setExtData(map);
                OnlineReportSaveGeneralResponse responseType = corpOnlineReportPlatformService.querySaveGeneralInfo(request);
                if (responseType != null && responseType.getResponseCode() == 20000) {
                    GeneralPotentialSaveInfo generalPotentialSaveInfo = Optional.ofNullable(responseType.getPotentialSaveInfo()).orElse(new GeneralPotentialSaveInfo());
                    if (request.getQueryBu() == QueryReportBuTypeEnum.flight) {
                        chartExcelEntityList = saveAnalysisService.flightSaveGenralPotentialExcel(generalPotentialSaveInfo, lang, baseCondition);
                    }
                    if (request.getQueryBu() == QueryReportBuTypeEnum.hotel) {
                        chartExcelEntityList = saveAnalysisService.hotelSaveGenralPotentialExcel(generalPotentialSaveInfo, lang, baseCondition);
                    }
                } else {
                    BusinessException businessException = null;
                    if (Objects.isNull(responseType)) {
                        businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                    } else {
                        businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                    }
                    throw businessException;
                }
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.LOW_RC.toString(), baseCondition.getIndex())) {
                chartExcelEntityList.add(saveAnalysisService.getSheetLowRcData(baseCondition, 0));
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.POTENTIAL_TOP_DEPT.toString(), baseCondition.getIndex())) {
                chartExcelEntityList.add(saveAnalysisService.getSheetTopDeptData(baseCondition, 0));
            }

            if (StringUtils.equalsIgnoreCase(IndexEnum.POTENTIAL_SAVE_DISTRIBUTION.toString(), baseCondition.getIndex())) {
                OnlineReportSaveProportionRequest request = new OnlineReportSaveProportionRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                OnlineReportSaveProportionResponse response;
                try {
                    response = corpOnlineReportPlatformService.queryPSaveProportion(request);
                    if (response != null && response.getResponseCode() == 20000) {
                        return saveAnalysisService.PotentialSaveDistributionExcel(response, request, lang);
                    }
                } catch (Exception e) {
                    log.error(LOG_TITLE, e);
                }
            }
            if (StringUtils.equalsIgnoreCase(IndexEnum.POTENTIAL_SAVE_DISTRIBUTION_DETAIL.toString(), baseCondition.getIndex())) {
                OnlineReportPSaveProportionDetailRequest request = new OnlineReportPSaveProportionDetailRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setLang(baseCondition.getLang());
                request.setProductType(productType);
                try {
                    OnlineReportPSaveProportionDetailResponse response = corpOnlineReportPlatformService.queryPSaveProportionDetail(request);
                    if (response != null && response.getResponseCode() == 20000) {
                        return saveAnalysisService.PotentialSaveDistributionDetailExcel(response, request, lang);
                    }
                } catch (Exception e) {
                    log.error(LOG_TITLE, e);
                }
            }
            if (StringUtils.equalsIgnoreCase(IndexEnum.POTENTIAL_SAVE_TREND.toString(), baseCondition.getIndex())) {
                // 潜在 节省趋势
                OnlineReportTrendRequest request = new OnlineReportTrendRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
                request.setDateDimension(QueryReportAggDateDimensionEnum.valueOf(baseCondition.getDateDimension()));
                request.setAggType(QueryReportAggTypeEnum.valueOf(baseCondition.getAggType()));
                baseCondition.getExtData().put("lang", baseCondition.getLang());
                request.setExtData(baseCondition.getExtData());
                request.setProductType(productType);
                OnlineReportTrendResponse response;
                try {
                    response = corpOnlineReportPlatformService.queryReportPotentialSaveTrend(request);
                    if (response != null && response.getResponseCode() == 20000) {
                        return saveAnalysisService.PotentialSaveTrendExcel(response, request, lang);
                    }
                } catch (Exception e) {
                    log.error(LOG_TITLE, e);
                }
            }
            if (StringUtils.equalsIgnoreCase(IndexEnum.POTENTIAL_SAVE_DETAIL.toString(), baseCondition.getIndex())) {
                // 潜在 节省趋势
                OnlineReportTrendRequest request = new OnlineReportTrendRequest();
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
                request.setBasecondition(baseCondition.getBaseQueryCondition());
                request.setQueryType(QueryReportTypeionEnum.valueOf(baseCondition.getQueryType()));
                request.setDateDimension(QueryReportAggDateDimensionEnum.valueOf(baseCondition.getDateDimension()));
                request.setAggType(QueryReportAggTypeEnum.valueOf(baseCondition.getAggType()));
                baseCondition.getExtData().put("lang", baseCondition.getLang());
                request.setExtData(baseCondition.getExtData());
                request.setProductType(productType);
                OnlineReportTrendResponse response;
                try {
                    response = corpOnlineReportPlatformService.queryReportPotentialSaveDetail(request);
                    if (response != null && response.getResponseCode() == 20000) {
                        return saveAnalysisService.PotentialSaveDetailExcel(response, request, lang);
                    }
                } catch (Exception e) {
                    log.error(LOG_TITLE, e);
                }
            }
        }
        /**
         * 节省分析
         */
        if (StringUtils.equalsIgnoreCase(reportId, "TravelAnalysis:SaveLoss:SaveAnalysis")) {
            ReportSaveLossAnalyseRequest request = new ReportSaveLossAnalyseRequest();
            request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setProductType(productType);
            request.setStartCityId(baseCondition.getStartCityId());
            request.setArriveCityId(baseCondition.getArriveCityId());
            request.setFlightCity(baseCondition.getFlightCity());
            try {
                if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_PRE_ORDER.toString(), baseCondition.getIndex())) {
                    ReportSaveLossAnalyseResponse responseType = corpOnlineReportPlatformService.querySaveLossPreOrderDate(request);
                    chartExcelEntityList = saveAnalysisService.flightPrdOrderDaysExcel(responseType, request, lang);
                } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_POSITION_DIST.toString(), baseCondition.getIndex())) {
                    ReportSaveLossAnalyseResponse responseType = corpOnlineReportPlatformService.querySaveLossPositionDist(request);
                    chartExcelEntityList = saveAnalysisService.flightPositionDistExcel(responseType, request, lang);
                } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_DISCOUNT_DIST.toString(), baseCondition.getIndex())) {
                    ReportSaveLossAnalyseResponse responseType = corpOnlineReportPlatformService.querySaveLossDiscountDist(request);
                    chartExcelEntityList = saveAnalysisService.flightDiscountDistExcel(responseType, request, lang);
                } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_AVG_PRICE.toString(), baseCondition.getIndex())) {
                    ReportSaveLossAnalyseResponse responseType = corpOnlineReportPlatformService.queryFlightAvgPriceDist(request);
                    chartExcelEntityList = saveAnalysisService.flightAvgPriceDistExcel(responseType, request, lang);
                } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_AVG_MILEAGE.toString(), baseCondition.getIndex())) {
                    ReportSaveLossAnalyseResponse responseType = corpOnlineReportPlatformService.queryFlightAvgMileageDist(request);
                    chartExcelEntityList = saveAnalysisService.flightAvgMileageDistExcel(responseType, request, lang);
                } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_STAR_DIST.toString(), baseCondition.getIndex())) {
                    ReportSaveLossAnalyseResponse responseType = corpOnlineReportPlatformService.queryHotelStarDist(request);
                    chartExcelEntityList = saveAnalysisService.hotelStarDistExcel(responseType, request, lang);
                } else if (StringUtils.equalsIgnoreCase(IndexEnum.SAVE_LOSS_NIGHT_DIST.toString(), baseCondition.getIndex())) {
                    ReportSaveLossAnalyseResponse responseType = corpOnlineReportPlatformService.queryHotelRoomNightDist(request);
                    chartExcelEntityList = saveAnalysisService.hotelNightDistExcel(responseType, request, lang);
                }
            } catch (Exception e) {
                log.error(LOG_TITLE, e);
            }
        }
        log.info(reportId, (String) OrpGsonUtils.toJsonStr(chartExcelEntityList));
        return chartExcelEntityList;
    }

    @Override
    public String buildExcelName(BaseQueryConditionBO request, String uid, String lang) {
        String middle = SharkUtils.get("Index.save", lang);
        String fileName_format = "%s_%s_%s_%s";
        IndexEnum indexEnum = IndexEnum.valueOf(request.getIndex());
        if (indexEnum == null) {
            return super.buildExcelName();
        }
        switch (indexEnum) {
            case GENERAL_SAVE:
                if (StringUtils.equalsIgnoreCase(request.getQueryBu(), QueryReportBuTypeEnum.flight.toString())) {
                    middle = SharkUtils.get("Save.FltAmtTotal", lang);
                } else if (StringUtils.equalsIgnoreCase(request.getQueryBu(), QueryReportBuTypeEnum.hotel.toString())) {
                    middle = SharkUtils.get("Save.HtlAmtTotal", lang);
                } else {
                    middle = SharkUtils.get("Save.AmtTotal", lang);
                }
                break;
            case GENERAL_SAVE_DISTRIBUTION:
                if (StringUtils.equalsIgnoreCase(request.getQueryBu(), QueryReportBuTypeEnum.flight.toString())) {
                    middle = SharkUtils.get("Save.FltAmtDis", lang);
                } else if (StringUtils.equalsIgnoreCase(request.getQueryBu(), QueryReportBuTypeEnum.hotel.toString())) {
                    middle = SharkUtils.get("Save.HtlAmtDis", lang);
                } else {
                    middle = SharkUtils.get("Save.App.SavePercent", lang);
                }
                break;
            case GENERAL_SAVE_POTENTIAL:
                if (StringUtils.equalsIgnoreCase(request.getQueryBu(), QueryReportBuTypeEnum.flight.toString())) {
                    middle = SharkUtils.get("Save.FltAmtPot", lang);
                } else if (StringUtils.equalsIgnoreCase(request.getQueryBu(), QueryReportBuTypeEnum.hotel.toString())) {
                    middle = SharkUtils.get("Save.HtlAmtPot", lang);
                } else {
                    middle = SharkUtils.get("Save.AmtPot", lang);
                }
                break;
            case LOW_RC:
                middle = SharkUtils.get("Save.RCResonDis", lang);
                break;
            case PROMOTIONS_SAVE_DETAIL:
                middle = SharkUtils.get("Save.HtlTripDtl", lang);
                break;
            case POTENTIAL_TOP_DEPT:
                middle = SharkUtils.get("Save.Dept", lang);
                break;
            case SAVE_DISTRIBUTION:
                middle = SharkUtils.get("Save.App.SavePercent", lang);
                break;
            case SAVE_DISTRIBUTION_DETAIL:
                middle = SharkUtils.get("Save.App.SavePercent", lang);
                break;
            case POTENTIAL_SAVE_DISTRIBUTION:
                middle = SharkUtils.get("Save.PotDis", lang);
                break;
            case POTENTIAL_SAVE_DISTRIBUTION_DETAIL:
                middle = SharkUtils.get("Save.PotDis", lang);
                break;
            case SAVE_TREND:
                middle = SharkUtils.get("Save.Trend", lang);
                break;
            case POTENTIAL_SAVE_TREND:
                middle = SharkUtils.get("Save.PotentialTrend", lang);
                break;
            case SAVE_DETAIL:
                middle = SharkUtils.get("Save.TableDetail", lang);
                break;
            case POTENTIAL_SAVE_DETAIL:
                middle = SharkUtils.get("Save.TableDetail", lang);
                break;
            /**
             * 节省分析
             */
            case SAVE_LOSS_PRE_ORDER:
                middle = SharkUtils.get("Save.PredateAys", lang);
                break;
            case SAVE_LOSS_POSITION_DIST:
                middle = SharkUtils.get("Save.FltClass", lang);
                break;
            case SAVE_LOSS_DISCOUNT_DIST:
                middle = SharkUtils.get("App.Behavior.PriceRate", lang);
                break;
            case SAVE_LOSS_AVG_PRICE:
                middle = SharkUtils.get("Save.AvgTkt", lang);
                break;
            case SAVE_LOSS_AVG_MILEAGE:
                middle = SharkUtils.get("Save.AvgTmps", lang);
                break;
            case SAVE_LOSS_STAR_DIST:
                middle = SharkUtils.get("Save.HtlStar", lang);
                break;
            case SAVE_LOSS_NIGHT_DIST:
                middle = SharkUtils.get("APP.Behavior.AvgNightPriceDist", lang);
                break;
        }
        String prefix = SharkUtils.get("Index.save", lang);
        if (StringUtils.equalsIgnoreCase(request.getBaseQueryCondition().getReportId(), "ravelAnalysis:SaveLoss:Save")) {
            prefix = SharkUtils.get("Index.save", lang);
        }
        if (StringUtils.equalsIgnoreCase(request.getBaseQueryCondition().getReportId(), "TravelAnalysis:SaveLoss:SavePotential")) {
            prefix = SharkUtils.get("Index.potentialsave", lang);
        }
        if (StringUtils.equalsIgnoreCase(request.getBaseQueryCondition().getReportId(), "TravelAnalysis:SaveLoss:SaveAnalysis")) {
            prefix = SharkUtils.get("Catalog.Save", lang);
        }
        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
        String name = String.format(fileName_format, prefix, middle, uid, str);
        log.info(request.getBaseQueryCondition().getReportId(), name);
        return name;
    }

    /**
     * GENERAL_SAVE:概览节省,GENERAL_SAVE_DISTRIBUTION:概览节省分布,GENERAL_SAVE_POTENTIAL:概览潜在节省,
     * LOW_RC:超标原因,PROMOTIONS_SAVE_DETAIL,酒店优惠明细,POTENTIAL_TOP_DEPT,部门节省对比
     */
    enum IndexEnum {
        GENERAL_SAVE,
        GENERAL_SAVE_DISTRIBUTION,
        GENERAL_SAVE_POTENTIAL,
        LOW_RC,
        PROMOTIONS_SAVE_DETAIL,
        POTENTIAL_TOP_DEPT,

        SAVE_DISTRIBUTION,

        SAVE_DISTRIBUTION_DETAIL,

        POTENTIAL_SAVE_DISTRIBUTION,

        POTENTIAL_SAVE_DISTRIBUTION_DETAIL,

        SAVE_TREND,

        POTENTIAL_SAVE_TREND,

        SAVE_DETAIL,

        POTENTIAL_SAVE_DETAIL,
        /**
         * 节省分析-提前预定天数
         */
        SAVE_LOSS_PRE_ORDER,
        /**
         * 节省分析-仓位分布
         */
        SAVE_LOSS_POSITION_DIST,
        /**
         * 节省分析-折扣分布
         */
        SAVE_LOSS_DISCOUNT_DIST,
        /**
         * 节省分析-平均票价分布
         */
        SAVE_LOSS_AVG_PRICE,
        /**
         * 节省分析-里程均价分布
         */
        SAVE_LOSS_AVG_MILEAGE,
        /**
         * 节省分析-星级分布
         */
        SAVE_LOSS_STAR_DIST,
        /**
         * 节省分析-间夜分布
         */
        SAVE_LOSS_NIGHT_DIST,

        /**
         * 大首页
         */
        FRONT_PAGE,
    }
}
