package com.corpgovernment.resource.schedule.onlinereport.riskorder;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.DeptUidStatisticalsEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpRiskDetailCustomColumnPO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.risk.RiskOrderReportBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.risk.CorpRiskOrderDetailCustomColumnDaoImpl;
import onlinereport.enums.reportlib.CarRiskOrderFieldEnum;
import onlinereport.enums.reportlib.FlightRiskOrderFieldEnum;
import onlinereport.enums.reportlib.HotelRiskOrderFieldEnum;
import onlinereport.enums.reportlib.HotelRiskUnderStayOrderFieldEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/6 18:20
 * @description：
 * @modified By：
 * @version: $
 */
@Service
public class RiskOrderDetailColumnService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    CorpRiskOrderDetailCustomColumnDaoImpl corpRiskOrderDetailCustomColumnDAO;

    // 查询自定字段, 未查询到，返回默认字段
    public List<String> queryCustomColumns(RiskOrderReportBO riskOrderReportBO) throws SQLException {
//        if(StringUtils.isNotBlank(riskOrderReportBO.getSubQueryBu())){
//            return queryCustomColumns(riskOrderReportBO.getUid(), riskOrderReportBO.getRiskSence(),riskOrderReportBO.getSubQueryBu());
//        }
        return queryCustomColumns(riskOrderReportBO.getUid(), riskOrderReportBO.getRiskSence());
    }



    // 查询自定字段, 未查询到，返回默认字段
    public List<String> queryCustomColumns(String uid, String riskScene) throws SQLException {
        List<CorpRiskDetailCustomColumnPO> pos = corpRiskOrderDetailCustomColumnDAO.queryByUIDAndScene(
                uid, riskScene);
        if (CollectionUtils.isNotEmpty(pos)) {
            List<String> columns = new ArrayList<>();
            CorpRiskDetailCustomColumnPO po = pos.get(0);
            List<String> customColumns = Arrays.asList(
                    StringUtils.splitByWholeSeparatorPreserveAllTokens(po.getCustomColumns(), ",")
            );
            if (! customColumns.contains(FlightRiskOrderFieldEnum.ORDER_ID.getName())) {
                columns.add(FlightRiskOrderFieldEnum.ORDER_ID.getName());
            }
            columns.addAll(customColumns);
            return columns;
        }
        return getDefaultColumns(riskScene);
    }

    // 查询自定字段, 未查询到，返回默认字段
    public List<String> queryCustomColumns(String uid, String riskScene,String subQueryBu) throws SQLException {
        List<CorpRiskDetailCustomColumnPO> pos = corpRiskOrderDetailCustomColumnDAO.queryByUIDAndScene(
                uid, riskScene);
        if (CollectionUtils.isNotEmpty(pos)) {
            List<String> columns = new ArrayList<>();
            CorpRiskDetailCustomColumnPO po = pos.get(0);
            List<String> customColumns = Arrays.asList(
                    StringUtils.splitByWholeSeparatorPreserveAllTokens(po.getCustomColumns(), ",")
            );
            columns.addAll(customColumns);
            return columns;
        }
        return getDefaultColumns(riskScene);
    }




    // 更新字段
    public int updateCustomColumns(RiskOrderReportBO riskOrderReportBO) throws Exception {
        if (riskOrderReportBO.getCustomColumns() == null) {
            return 0;
        }
        // 去掉订单号字段。订单号默认展示，在自定义展示指标里剔除
        else if (StringUtils.isNotBlank(riskOrderReportBO.getRiskSence())
                && (riskOrderReportBO.getRiskSence().equals("FLT_REFUND") || riskOrderReportBO.getRiskSence().equals("HTL_CASH_OUT")
                    || riskOrderReportBO.getRiskSence().equals("HTL_UNDER_STAY") || riskOrderReportBO.getRiskSence().equals("CAR_DOUBLE_BOOK"))
                && (riskOrderReportBO.getCustomColumns() != null)){
            riskOrderReportBO.getCustomColumns().remove(FlightRiskOrderFieldEnum.ORDER_ID.getName());
        }
        return corpRiskOrderDetailCustomColumnDAO.update(
                riskOrderReportBO.getUid(), riskOrderReportBO.getRiskSence(), StringUtils.join(riskOrderReportBO.getCustomColumns(), ",")
        );
    }

    private List flightDetailHeader(String lang) {
        FlightRiskOrderFieldEnum[] enums = FlightRiskOrderFieldEnum.values();
        return Arrays.stream(enums)
                .map(e -> new HashMap<String, Object>(){{
                    put("headerKey", e.getName());
                    put("headerValue", SharkUtils.get(e.getHeaderKey(), lang));
                    put("disabled", e.isMust());
                    }}
                )
                .collect(Collectors.toList());
    }

    private List hotelDetailHeader(String lang) {
        HotelRiskOrderFieldEnum[] enums = HotelRiskOrderFieldEnum.values();
        return Arrays.stream(enums)
                .map(e -> new HashMap<String, Object>(){{
                    put("headerKey", e.getName());
                    put("headerValue", SharkUtils.get(e.getHeaderKey(), lang));
                    put("disabled", e.isMust());
                }})
                .collect(Collectors.toList());
    }

    private List hotelUnderStayDetailHeader(String lang) {
        HotelRiskUnderStayOrderFieldEnum[] enums = HotelRiskUnderStayOrderFieldEnum.values();
        return Arrays.stream(enums)
                .map(e -> new HashMap<String, Object>(){{
                    put("headerKey", e.getName());
                    put("headerValue", SharkUtils.get(e.getHeaderKey(), lang));
                    put("disabled", e.isMust());
                }})
                .collect(Collectors.toList());
    }

    private List carDoubleBookDetailHeader(String lang) {
        CarRiskOrderFieldEnum[] enums = CarRiskOrderFieldEnum.values();
        return Arrays.stream(enums)
                .map(e -> new HashMap<String, Object>(){{
                    put("headerKey", e.getName());
                    put("headerValue", SharkUtils.get(e.getHeaderKey(), lang));
                    put("disabled", e.isMust());
                }})
                .collect(Collectors.toList());
    }

    /**
     * 自定义展示全部字段中，剔除必展示的订单号字段
     */
    public List getFullColumns(String scene, String lang) {
        List columns = new ArrayList<>();
        switch (scene.toUpperCase()) {
            case "FLT_REFUND":
                columns = flightDetailHeader(lang);
                break;
            case "HTL_CASH_OUT":
                columns = hotelDetailHeader(lang);
                break;
            case "HTL_UNDER_STAY":
                columns = hotelUnderStayDetailHeader(lang);
                break;
            case "CAR_DOUBLE_BOOK":
                columns = carDoubleBookDetailHeader(lang);
            default:
                break;
        }
        return columns;
    }

    /**
     * 自定义展示全部字段中
     */
    public List getFullUidColumns(String bu, String lang) {
        DeptUidStatisticalsEnum[] enums = DeptUidStatisticalsEnum.values();
        return Arrays.stream(enums).filter(e -> Objects.equals(e.getBizType(), bu))
                .map(e -> new HashMap<String, Object>(){{
                            put("headerKey", e.toString());
                            put("headerValue", SharkUtils.get(e.getSharkKey(), lang));
                        }}
                )
                .collect(Collectors.toList());
    }

//    /**
//     * 自定义展示全部字段中，剔除必展示的订单号字段
//     */
//    public List getFullDetailColumns(String queryBu, AnalysisObjectEnum analysisObjectEnum, String lang) {
//        List columns = new ArrayList<>();
//        OnlineReportDeptDetailAnalysisRequest request = new OnlineReportDeptDetailAnalysisRequest();
//        request.setSubQueryBu(Integer.parseInt(queryBu));
//        request.setAnalysisObjectEnum(analysisObjectEnum);
//        request.setLang(lang);
////        request.setAnalysisObjectEnum();
//        columns = corpOnlineReportPlatformService.queryDeptDetailHeader(request).getHeaderData();
//        return columns;
//    }

    /**
     * 默认展示字段 剔除必展示的订单号字段
     */
    private List<String> getDefaultColumns(String scene) {
        List<String> defaultColumns = new ArrayList<>();
        switch (scene.toUpperCase()) {
            case "FLT_REFUND":
                FlightRiskOrderFieldEnum[] enums = FlightRiskOrderFieldEnum.values();
                defaultColumns = Arrays.stream(enums)
                        .filter(FlightRiskOrderFieldEnum::isDefault)
                        // .filter(e -> e != FlightRiskOrderFieldEnum.ORDER_ID)
                        .map(FlightRiskOrderFieldEnum::getName)
                        .collect(Collectors.toList());
                break;
            case "HTL_CASH_OUT":
                HotelRiskOrderFieldEnum[] enums1 = HotelRiskOrderFieldEnum.values();
                defaultColumns = Arrays.stream(enums1)
                        .filter(HotelRiskOrderFieldEnum::isDefault)
                        // .filter(e -> e != HotelRiskOrderFieldEnum.ORDER_ID)
                        .map(HotelRiskOrderFieldEnum::getName)
                        .collect(Collectors.toList());
                break;
            case "HTL_UNDER_STAY":
                HotelRiskUnderStayOrderFieldEnum[] enums2 = HotelRiskUnderStayOrderFieldEnum.values();
                defaultColumns = Arrays.stream(enums2)
                        .filter(HotelRiskUnderStayOrderFieldEnum::isDefault)
                        // .filter(e -> e != HotelRiskUnderStayOrderFieldEnum.ORDER_ID)
                        .map(HotelRiskUnderStayOrderFieldEnum::getName)
                        .collect(Collectors.toList());
                break;
            case "CAR_DOUBLE_BOOK":
                CarRiskOrderFieldEnum[] enums3 = CarRiskOrderFieldEnum.values();
                defaultColumns = Arrays.stream(enums3)
                        .filter(CarRiskOrderFieldEnum::isDefault)
                        .map(CarRiskOrderFieldEnum::getName)
                        .collect(Collectors.toList());
            default:
                break;
        }
        return defaultColumns;
    }

}
