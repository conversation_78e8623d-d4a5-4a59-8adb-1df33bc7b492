package com.corpgovernment.resource.schedule.onlinereport.position;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TravelPositionStepEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopCityRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopCityResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopHotelRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTopHotelResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTravelPositionRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTravelPositionResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportTrendLegend;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TopHotelInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionCityData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionCityRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionCityResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionCountryData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionCountryRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionCountryResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionDetailInfoData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionDetailRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionDetailResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionProvinceData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionProvinceRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionProvinceResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.OrpGsonUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-08-22 18:18
 * @desc
 */
@Service
@Slf4j
public class TravelPositionAdaptor extends BaseReportDataAdaptor<Object> {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService ;

    @Autowired
    private TravelPositionExcelService travelPositionExcelService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String index = baseCondition.getIndex();
        if (StringUtils.equalsIgnoreCase(IndexEnum.POSITION.toString(), index)
                || StringUtils.equalsIgnoreCase(IndexEnum.POSITION_TRACK.toString(), index)
                || StringUtils.equalsIgnoreCase(IndexEnum.POSITION_HOTEL.toString(), index) || StringUtils.equalsIgnoreCase(IndexEnum.FRONT_PAGE.toString(), index)) {
            OnlineReportTravelPositionRequest request = new OnlineReportTravelPositionRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setProductType(baseCondition.getProductType());
            Map<String, String> map = Objects.isNull(baseCondition.getExtData()) ? new HashMap<>() : baseCondition.getExtData();
            request.setExtData(map);
            map.put("index", index);
            OnlineReportTravelPositionResponse responseType = corpOnlineReportPlatformService.queryTravelPositionInfo(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                if (StringUtils.equalsIgnoreCase(IndexEnum.POSITION.toString(), index)
                        || StringUtils.equalsIgnoreCase(IndexEnum.POSITION_HOTEL.toString(), index)
                        || StringUtils.equalsIgnoreCase(IndexEnum.FRONT_PAGE.toString(), index)) {
                    if (responseType.getPositionInfo() == null) {
                        throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
                    }
                    return responseType.getPositionInfo();
                }
                if (StringUtils.equalsIgnoreCase(IndexEnum.POSITION_TRACK.toString(), index)) {
                    if (responseType.getPositionTrackInfo() == null) {
                        throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
                    }
                    return responseType.getPositionTrackInfo();
                }
            } else {
                errorReturn(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        } else if (StringUtils.equalsIgnoreCase(IndexEnum.POSITION_CITY.toString(), index)) {
            TravelPositionCityRequest request = new TravelPositionCityRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setProductType(baseCondition.getProductType());
            request.setCityName(baseCondition.getCityName());
            TravelPositionCityResponse responseType = corpOnlineReportPlatformService.queryTravelPositionCity(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return Optional.ofNullable(responseType).map(t -> t.getCityData()).orElse(new TravelPositionCityData());
            } else {
                errorReturn(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        } else if (StringUtils.equalsIgnoreCase(IndexEnum.POSITION_PROVINCE.toString(), index)) {
            TravelPositionProvinceRequest request = new TravelPositionProvinceRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setProductType(baseCondition.getProductType());
            request.setProvinceName(baseCondition.getProvinceName());
            TravelPositionProvinceResponse responseType = corpOnlineReportPlatformService.queryTravelPositionProvince(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return Optional.ofNullable(responseType).map(t -> t.getProvinceData()).orElse(new TravelPositionProvinceData());
            } else {
                errorReturn(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        } else if (StringUtils.equalsIgnoreCase(IndexEnum.POSITION_COUNTRY.toString(), index)) {
            TravelPositionCountryRequest request = new TravelPositionCountryRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setProductType(baseCondition.getProductType());
            request.setCountryName(baseCondition.getCountryName());
            TravelPositionCountryResponse responseType = corpOnlineReportPlatformService.queryTravelPositionCountry(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return Optional.ofNullable(responseType).map(t -> t.getCountryData()).orElse(new TravelPositionCountryData());
            } else {
                errorReturn(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        } else if (StringUtils.equalsIgnoreCase(IndexEnum.POSITION_DETAIL.toString(), index)) {
            TravelPositionDetailRequest request = new TravelPositionDetailRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setTravelStep(baseCondition.getTravelStep());
            request.setQueryColumn(baseCondition.getQueryColumn());
            request.setQueryBus(baseCondition.getQueryBus());
            request.setProductType(baseCondition.getProductType());
            request.setPage(baseCondition.getPager());
            TravelPositionDetailResponse responseType = corpOnlineReportPlatformService.queryTravelPositionDetailByPage(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return responseType;
            } else {
                errorReturn(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        } else if (StringUtils.equalsIgnoreCase(IndexEnum.TOP_CITY.toString(), index)) {
            OnlineReportTopCityRequest request = new OnlineReportTopCityRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setProductType(baseCondition.getProductType());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            request.setExtData(extMap);
            OnlineReportTopCityResponse responseType = corpOnlineReportPlatformService.queryTopCity(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return responseType.getTopCityList();
            } else {
                errorReturn(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        } else if (StringUtils.equalsIgnoreCase(IndexEnum.TOP_HOTEL.toString(), index)) {
            OnlineReportTopHotelRequest request = new OnlineReportTopHotelRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setProductType(baseCondition.getProductType());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            request.setExtData(extMap);
            OnlineReportTopHotelResponse responseType = corpOnlineReportPlatformService.queryTopHotel(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                Map result = new HashMap<>();
                List<Map> headers = new ArrayList<>();
                TopHotelHeaderEnum[] hotelHeaderEnums = TopHotelHeaderEnum.values();
                for (TopHotelHeaderEnum topHotelHeaderEnum : hotelHeaderEnums) {
                    // 前端表格不需要显示城市名称
                    if (topHotelHeaderEnum == TopHotelHeaderEnum.CITY_NAME) {
                        continue;
                    }
                    Map header = new HashMap();
                    header.put("key", topHotelHeaderEnum.getCode());
                    header.put("val", SharkUtils.get(topHotelHeaderEnum.getSharkKey(), request.getLang()));
                    header.put("dataType", topHotelHeaderEnum.getDataType());
                    headers.add(header);
                }
                List<TopHotelInfo> topHotelInfos = responseType.getTopHotelList();
                List<Map> dataList = new ArrayList<>();
                for (TopHotelInfo item : topHotelInfos) {
                    Map data = new HashMap();
                    data.put(TopHotelHeaderEnum.STAR.code, MapperUtils.convertDigitToString(item.getStar()));
                    data.put(TopHotelHeaderEnum.HOTEL_NAME.code, MapperUtils.trim(item.getHotelName()));
                    data.put(TopHotelHeaderEnum.AMOUNT.code, MapperUtils.convertDigitToZero(item.getTotalAmount()));
                    data.put(TopHotelHeaderEnum.AMOUNT_PERCENT.code, MapperUtils.convertDigitToZeroString(item.getAmountPercent()).concat(GlobalConst.PERCENT_QUOTE));
                    data.put(TopHotelHeaderEnum.QUANTITY.code, MapperUtils.convertDigitToString(item.getTotalQuantity()));
                    data.put(TopHotelHeaderEnum.QUANTIY_PERCENT.code, MapperUtils.convertDigitToZeroString(item.getQuantityPercent()).concat(GlobalConst.PERCENT_QUOTE));
                    data.put(TopHotelHeaderEnum.AVG_PRICE.code, MapperUtils.convertDigitToString(item.getAvgPrice()));
                    dataList.add(data);
                }
                result.put("detail", dataList);
                result.put("headerData", headers);
                return result;
            } else {
                errorReturn(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        }
        return null;
    }

    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        if (StringUtils.equalsIgnoreCase(IndexEnum.POSITION_DETAIL.toString(), baseCondition.getIndex())) {
            TravelPositionDetailRequest request = new TravelPositionDetailRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setLang(baseCondition.getLang());
            request.setQueryColumn(baseCondition.getQueryColumn());
            request.setQueryBus(baseCondition.getQueryBus());
            request.setProductType(baseCondition.getProductType());
            Pager pager = Optional.ofNullable(baseCondition.getPager()).map(t -> {
                t.setPageSize(1000000);
                return t;
            }).orElse(new Pager(1000000L, 1000000, 1, 1000000));
            request.setPage(pager);
            try {
                request.setTravelStep(TravelPositionStepEnum.TRAVEL_ING);
                TravelPositionDetailResponse travelIngResponse = corpOnlineReportPlatformService.queryTravelPositionDetailByPage(request);
                request.setTravelStep(TravelPositionStepEnum.GOING);
                TravelPositionDetailResponse goingResponse = corpOnlineReportPlatformService.queryTravelPositionDetailByPage(request);
                request.setTravelStep(TravelPositionStepEnum.LEFTED);
                TravelPositionDetailResponse leftResponse = corpOnlineReportPlatformService.queryTravelPositionDetailByPage(request);
                return mapperExcel(travelIngResponse, goingResponse, leftResponse);
            } catch (Exception e) {
                log.error(LOG_TITLE, ExceptionUtils.getFullStackTrace(e));
            }
        }
        if (StringUtils.equalsIgnoreCase(IndexEnum.TOP_HOTEL.toString(), baseCondition.getIndex())) {
            return travelPositionExcelService.buildExcel(baseCondition);
        }
        return null;
    }

    List<ChartExcelEntity> mapperExcel(TravelPositionDetailResponse travelIngResp,
                                       TravelPositionDetailResponse goingResp,
                                       TravelPositionDetailResponse leftResp) {
        List<ChartExcelEntity> sheets = Lists.newArrayList();
        if (Objects.nonNull(travelIngResp) && travelIngResp.getResponseCode() == 20000) {
            sheets.add(setSheetData(travelIngResp.getNotLeftData(), GlobalConst.ZERO_DIGIT_NUM));
        }
        if (Objects.nonNull(goingResp) && goingResp.getResponseCode() == 20000) {
            sheets.add(setSheetData(goingResp.getGoingData(), GlobalConst.ONE_DIGIT_NUM));
        }
        if (Objects.nonNull(leftResp) && leftResp.getResponseCode() == 20000) {
            sheets.add(setSheetData(leftResp.getLeftData(), GlobalConst.NEW_DIGIT_NUM));
        }
        return sheets;
    }

    private ChartExcelEntity setSheetData(TravelPositionDetailInfo respData, Integer sheetNum) {
        ChartExcelEntity sheet = new ChartExcelEntity();
        List<List<String>> data = Lists.newArrayList();
        sheet.setSheetTitle(respData.getSubject());
        // header and body
        List<TravelPositionDetailInfoData> responseBodyList = Optional.ofNullable(respData.getData()).orElse(Lists.newArrayList());
        List<OnlineReportTrendLegend> headerMap = Optional.ofNullable(respData.getLegends()).orElse(Lists.newArrayList());
        List<String> headers = headerMap.stream().map(i -> i.getName()).collect(Collectors.toList());
        for (TravelPositionDetailInfoData detail : responseBodyList) {
            Map dataMap = OrpGsonUtils.fromToJsonTypeTest(OrpGsonUtils.toJsonStr(detail), Map.class);
            List<String> list = headerMap.stream().map(t -> String.valueOf(dataMap.get(t.getKey()))).collect(Collectors.toList());
            data.add(list);
        }
        sheet.setHeaders(headers);
        sheet.setSheetNum(sheetNum);
        sheet.setData(data);
        return sheet;
    }

    @Override
    public String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang) {
        /**
         * 差旅定位-差旅足迹明细-xxx-20220720
         */
        String fileName_format = "%s_%s_%s_%s";
        /**
         * 差旅定位
         */
        String prefix = SharkUtils.get("Catalog.TravelPosition", lang);
        /**
         * 差旅足迹明细
         */
        String middle = SharkUtils.get("TripMap.TripDetail", lang);
        if (StringUtils.equalsIgnoreCase(IndexEnum.POSITION_DETAIL.toString(), baseCondition.getIndex())) {
            middle = SharkUtils.get("TripMap.TripDetail", lang);
        }
        if (StringUtils.equalsIgnoreCase(IndexEnum.TOP_HOTEL.toString(), baseCondition.getIndex())) {
            middle = SharkUtils.get("INDEX.TOPCITYHOTEL", lang);
//            return "INDEX.TOPCITYHOTEL";
        }
        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);

        return String.format(fileName_format, prefix, middle, uid, str);
    }

    /**
     * POSITION:足迹, POSITION_TRACK:交通, POSITION_HOTEL：酒店,POSITION_CITY:城市查询,POSITION_PROVINCE:省份查询,
     * POSITION_COUNTRY:国家查询,POSITION_DETAIL:明细查询,FRONT_PAGE:大首页
     */
    enum IndexEnum {
        POSITION, POSITION_TRACK, POSITION_HOTEL, POSITION_CITY, POSITION_PROVINCE, POSITION_COUNTRY, POSITION_DETAIL, FRONT_PAGE, TOP_CITY, TOP_HOTEL
    }

    enum TopHotelHeaderEnum {
        CITY_NAME("cityName", "Exceltopname.city", -1),
        HOTEL_NAME("hotelName", "Index.hotelname", -1),
        STAR("star", "Index.star", -1),
        AMOUNT("amount", "Index.costmoney", 1),
        AMOUNT_PERCENT("amountPercent", "Index.moneyper", 3),
        QUANTITY("quantity", "Index.nightnum", 2),
        QUANTIY_PERCENT("quantityPercent", "Index.nightnumper", 3),
        AVG_PRICE("avgPrice", "Overview.HotelAvgNightPrice", 1);

        String code;
        String sharkKey;

        int dataType;

        TopHotelHeaderEnum(String c, String s, int d) {
            this.code = c;
            this.sharkKey = s;
            this.dataType = d;
        }

        public String getCode() {
            return code;
        }

        public String getSharkKey() {
            return sharkKey;
        }

        public int getDataType() {
            return dataType;
        }
    }
}
