package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.MixPaymentInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.MixPaymentTrendInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMixPaymentRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMixPaymentResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023-01-31 19:55
 * @desc
 */
@Service
public class MixPaymentBeahviorAnalysisService extends AbstractGenralExportDataService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    public List<ChartExcelEntity> buildExcel(BaseQueryConditionBO baseCondition) throws BusinessException {
        String reportId = baseCondition.getBaseQueryCondition().getReportId();
        String lang = baseCondition.getLang();
        Map map = getConditionContent(baseCondition.getBaseQueryCondition().getUid(), baseCondition.getBaseQueryCondition(), lang);
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HotelMixPayment", reportId) || StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:CarMixPayment", reportId)) {
            OnlineReportMixPaymentRequest request = new OnlineReportMixPaymentRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
            request.setProductType(baseCondition.getProductType());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            request.setExtData(extMap);
            OnlineReportMixPaymentResponse responseType = corpOnlineReportPlatformService.queryMixPaymentInfo(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                if (request.getQueryBu() == QueryReportBuTypeEnum.car) {
                    chartExcelEntityList = mixPayment(responseType.getMixPayment(), lang, request.getQueryBu(), map, (String) extMap.get("carOrderType"));
                } else {
                    chartExcelEntityList = mixPayment(responseType.getMixPayment(), lang, request.getQueryBu(), map, baseCondition.getProductType());
                }
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        }
        return chartExcelEntityList;
    }

    private List<ChartExcelEntity> mixPayment(MixPaymentInfo maxPayment, String lang, QueryReportBuTypeEnum queryReportBuTypeEnum, Map map, String type) {
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        maxPayment = Optional.ofNullable(maxPayment).orElse(new MixPaymentInfo());
        int sheetIndex = 0;
        chartExcelEntityList.add(mixPaymentTrend(maxPayment, lang, map, sheetIndex++, queryReportBuTypeEnum, type));
        chartExcelEntityList.add(mixPaymentDis(maxPayment, lang, map, sheetIndex++, queryReportBuTypeEnum, type));
        return chartExcelEntityList;
    }

    private ChartExcelEntity mixPaymentTrend(MixPaymentInfo maxPayment, String lang, Map map, int sheetIndex, QueryReportBuTypeEnum queryReportBuTypeEnum, String type) {
        List<MixPaymentTrendInfo> behaviorList = Optional.ofNullable(maxPayment.getMixPayMentTrendList()).orElse(new ArrayList<>());
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            sheet1.setHeaders(Arrays.asList(
                    SharkUtils.get("Index.month", lang),
                    SharkUtils.get("Index.nightnum", lang),
                    SharkUtils.get("Index.nightnumper", lang),
                    SharkUtils.get("Index.costmoney", lang),
                    SharkUtils.get("Index.costper", lang)));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.car) {
            sheet1.setHeaders(Arrays.asList(
                    SharkUtils.get("Index.month", lang),
                    SharkUtils.get("Index.ordernumber", lang),
                    SharkUtils.get("Index.orderpercentage", lang),
                    SharkUtils.get("Index.costmoney", lang),
                    SharkUtils.get("Index.costper", lang)));
        }
        List data = new ArrayList();
        for (MixPaymentTrendInfo trendInfo : behaviorList) {
            data.add(Arrays.asList(
                    MapperUtils.trim(trendInfo.getDim()),
                    MapperUtils.convertDigitToZeroString(trendInfo.getTotalQuantity()),
                    MapperUtils.convertDigitToZeroString(trendInfo.getQuantityPercent()).concat(GlobalConst.PERCENT_QUOTE),
                    MapperUtils.convertDigitToZeroString(trendInfo.getTotalAmount()),
                    MapperUtils.convertDigitToZeroString(trendInfo.getAmountPercent()).concat(GlobalConst.PERCENT_QUOTE)));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel) {
            data.add(Arrays.asList(SharkUtils.get("BehaviorAnalysis.HtlMixpay", lang)
                    .replace("{{value}}", MapperUtils.convertDigitToZeroString(maxPayment.getTotalQuanity()))
                    .replace("{{percent}}", MapperUtils.convertDigitToZeroString(maxPayment.getQuantityPercent())).concat(GlobalConst.PERCENT_QUOTE)));
            data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                    Arrays.asList(SharkUtils.get("Travelanalysis.hotelstyle", lang), getSheetNameByProductType(lang, type))));
        }
        if (queryReportBuTypeEnum == QueryReportBuTypeEnum.car) {
            data.add(Arrays.asList(SharkUtils.get("BehaviorAnalysis.CarMixpay", lang)
                    .replace("{{value}}", MapperUtils.convertDigitToZeroString(maxPayment.getTotalQuanity()))
                    .replace("{{percent}}", MapperUtils.convertDigitToZeroString(maxPayment.getQuantityPercent())).concat(GlobalConst.PERCENT_QUOTE)));
            data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                    Arrays.asList(SharkUtils.get("Exceltopname.usecartype", lang), getSheetNameByCarOrderType(lang, type))));
        }
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(SharkUtils.get("Index.Trend", lang));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }

    private ChartExcelEntity mixPaymentDis(MixPaymentInfo maxPayment, String lang, Map map, int sheetIndex, QueryReportBuTypeEnum queryBu, String productType) {
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(Arrays.asList(
                SharkUtils.get("Travelanalysis.CarPaybySelf", lang),
                SharkUtils.get("Travelanalysis.CarPaybyCompany", lang)));
        List data = new ArrayList();
        data.add(Arrays.asList(
                MapperUtils.convertDigitToZeroString(maxPayment.getTotalSettlementPersonamt()),
                MapperUtils.convertDigitToZeroString(maxPayment.getTotalSettlementAccntamt())));
        if (queryBu == QueryReportBuTypeEnum.hotel) {
            data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                    Arrays.asList(SharkUtils.get("Travelanalysis.hotelstyle", lang), getSheetNameByProductType(lang, productType))));
        }
        if (queryBu == QueryReportBuTypeEnum.car) {
            data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                    Arrays.asList(SharkUtils.get("Exceltopname.usecartype", lang), getSheetNameByCarOrderType(lang, productType))));
        }
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(SharkUtils.get("Index.Distribute", lang));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }

}
