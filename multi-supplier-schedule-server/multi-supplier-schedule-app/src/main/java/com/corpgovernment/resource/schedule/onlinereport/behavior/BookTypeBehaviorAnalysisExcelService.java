package com.corpgovernment.resource.schedule.onlinereport.behavior;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BookTypeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBookTypeRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBookTypeResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.onlinereport.enums.ProductTypeEnum;
import onlinereport.enums.BookTypeEnum;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022-11-18 14:53
 * @desc
 */
@Service
public class BookTypeBehaviorAnalysisExcelService extends AbstractGenralExportDataService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;


    public List<ChartExcelEntity> buildExcel(BaseQueryConditionBO baseCondition) throws BusinessException {
        ProductTypeEnum[] productTypeEnums = ProductTypeEnum.values();
        BookTypeEnum[] bookTypeEnums = BookTypeEnum.values();
        String lang = baseCondition.getLang();
        Map map = getConditionContent(baseCondition.getBaseQueryCondition().getUid(), baseCondition.getBaseQueryCondition(), lang);
        OnlineReportBookTypeRequest bookTypeRequest = new OnlineReportBookTypeRequest();
        bookTypeRequest.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
        bookTypeRequest.setBasecondition(baseCondition.getBaseQueryCondition());
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        for (ProductTypeEnum productTypeEnum : productTypeEnums) {
            bookTypeRequest.setProductType(productTypeEnum.toString());
            OnlineReportBookTypeResponse responseType = corpOnlineReportPlatformService.queryBookTypeAnalysis(bookTypeRequest);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                List<BookTypeInfo> bookTypeInfoList = Optional.ofNullable(responseType.getBookTypeList()).orElse(new ArrayList<>());
                ChartExcelEntity sheet1 = new ChartExcelEntity();
                sheet1.setHeaders(Arrays.asList(
                        SharkUtils.get("Travelanalysis.scheduledstyle", lang),
                        SharkUtils.get("Index.ordernumber", lang),
                        SharkUtils.get("Booking.Percentage", lang),
                        SharkUtils.get("Index.costmoney", lang),
                        SharkUtils.get("Index.costper", lang)));
                List data = new ArrayList();
                for (BookTypeEnum bookTypeEnum : bookTypeEnums) {
                    BookTypeInfo bookTypeInfo = bookTypeInfoList.stream()
                            .filter(i -> StringUtils.equalsIgnoreCase(i.getBookType(), bookTypeEnum.toString()))
                            .findFirst().orElse(new BookTypeInfo());
                    data.add(Arrays.asList(
                            bookTypeEnum.getName(),
                            MapperUtils.convertDigitToZeroString(bookTypeInfo.getTotalOrderCount()),
                            MapperUtils.convertDigitToZeroString(bookTypeInfo.getOrderPercent()).concat(GlobalConst.PERCENT_QUOTE),
                            MapperUtils.convertDigitToZeroString(bookTypeInfo.getTotalAmount()),
                            MapperUtils.convertDigitToZeroString(bookTypeInfo.getAmountPercent()).concat(GlobalConst.PERCENT_QUOTE)));
                }
                data.addAll(buttonContent(lang, map));
                sheet1.setSheetTitle(getSheetNameByProductType(lang, productTypeEnum.toString()));
                sheet1.setSheetNum(productTypeEnum.ordinal());
                sheet1.setData(data);
                chartExcelEntityList.add(sheet1);
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        }
        return chartExcelEntityList;
    }
}
