package com.corpgovernment.resource.schedule.onlinereport.reportlib;


import com.corpgovernment.resource.schedule.domain.onlinereport.common.ThreadContext;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportLibFilterBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib.ReportLibResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Auther:abguo
 * Date:2019/9/7
 * Description:
 * Project:onlinereportweb
 */
@Slf4j
public abstract class OrderDeatailService {

    protected static final String PREFIX = OrderDeatailService.class.getSimpleName();
    /**
     * 日记tag上下文
     */
    @Autowired
    protected ThreadContext logTagContext;


    protected abstract ReportLibResultEntity queryOrderdetails(ReportLibFilterBO reportLibFilterBO);

    public ReportLibResultEntity queryLimit(ReportLibFilterBO reportLibFilterBO) {
        log.info(PREFIX, "queryLimit", logTagContext.getMap());
        ReportLibResultEntity reportLibResultEntity = queryOrderdetails(reportLibFilterBO);
        return reportLibResultEntity;
    }
}
