package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.soa.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.common.BaseDataUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.ChineseLanguageConfig;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportFlightOrderInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DateUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.FlightOrderDetailService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.soa.AbstractOrderDetailSoaService;
import com.google.common.collect.ImmutableMap;
import onlinereport.enums.AgeTypeEnums;
import onlinereport.enums.FlightTicketStatusEnum;
import onlinereport.enums.OrderTypeEnum;
import onlinereport.enums.YesOrNotEnum;
import onlinereport.enums.reportlib.*;
import onlinereport.enums.reportlib.uid.FlightUidOrderDetailEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst.FOUR_DIGIT_NUM;


/**
 * <AUTHOR>
 * @date 2022-10-13 13:58
 * @desc
 */
@Service
public class FlightOrderSoaService extends AbstractOrderDetailSoaService {

    @Override
    public OrderDetailEnumerable[] getEnum(String reportType) {
        if (StringUtils.equalsIgnoreCase(reportType, "F_REFUND")) {
            return FlightRefundOrderDetailEnum.values();
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_REBOOK")) {
            return FlightChangeOrderDetailEnum.values();
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_UNUSE")) {
            return UnUseFlightOrderDetailEnum.values();
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_UID_DETAIL")) {
            return FlightUidOrderDetailEnum.values();
        } else {
            return FlightOrderDetailEnum.values();
        }
    }


    @Override
    protected List<Map<String, Object>> convertMapData(OnlineReportOrderDetailInfo detailInfo, String lang, String reportType) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportFlightOrderInfo> data = detailInfo.getFltOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        if (StringUtils.equalsIgnoreCase(reportType, "F_REFUND")) {
            return convertFlightRefundOrderMapData(detailInfo, lang);
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_REBOOK")) {
            return convertFlightRebookOrderMapData(detailInfo, lang);
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_UNUSE")) {
            return convertFlightUnuseOrderMapData(detailInfo, lang);
        } else if (StringUtils.equalsIgnoreCase(reportType, "F_UID_DETAIL")) {
            return convertFlightUidOrderMapData(detailInfo, lang);
        } else {
            return convertFlightOrderMapData(detailInfo, lang);
        }
    }

    /**
     * 订单明细
     *
     * @param detailInfo
     * @param lang
     * @return
     */
    public List<Map<String, Object>> convertFlightUidOrderMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportFlightOrderInfo> data = detailInfo.getFltOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        Map yesOrNotMap = BaseDataUtils.getYesOrNoMap(lang);
        for (OnlineReportFlightOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(FlightUidOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrderId()));
            map.put(FlightUidOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrderStatus()));
            map.put(FlightUidOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(FlightUidOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(FlightUidOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
            map.put(FlightUidOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.convertDigitToZero(order.getAccountId()));
            map.put(FlightUidOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(FlightUidOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
            map.put(FlightUidOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRankName()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCostCenter1()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCostCenter2()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCostCenter3()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCostCenter4()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCostCenter5()));
            map.put(FlightUidOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCostCenter6()));
            map.put(FlightUidOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(FlightUidOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(FlightUidOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(FlightUidOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
            map.put(FlightUidOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
            map.put(FlightUidOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
            map.put(FlightUidOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
            map.put(FlightUidOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
            map.put(FlightUidOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
            map.put(FlightUidOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(FlightUidOrderDetailEnum.PRINTTICKETTIME.getCode(), MapperUtils.trim(order.getPrintTicketTime()));
            map.put(FlightUidOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            map.put(FlightUidOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));
            map.put(FlightUidOrderDetailEnum.PRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrice()));
            map.put(FlightUidOrderDetailEnum.PRINTPRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrintPrice()));
            map.put(FlightUidOrderDetailEnum.PRICERATE.getCode(), DigitBaseUtils.formatDigit(order.getPriceRate()));
            map.put(FlightUidOrderDetailEnum.AGREEMENTTYPENAME.getCode(), MapperUtils.trim(order.getAgreementTypeName()));
            map.put(FlightUidOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassengerName()));
            map.put(FlightUidOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlightNo()));
            map.put(FlightUidOrderDetailEnum.SUBCLASS.getCode(), MapperUtils.trim(order.getSubClass()));
            map.put(FlightUidOrderDetailEnum.FLIGHTCITY.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getFlightCity()), MapperUtils.trim(order.getFlightCityEn())));
            map.put(FlightUidOrderDetailEnum.DEPARTUREPORTNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDeparturePortName()), MapperUtils.trim(order.getDeparturePortNameEn())));
            map.put(FlightUidOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDepartureCityName()), MapperUtils.trim(order.getDepartureCityNameEn())));
            map.put(FlightUidOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoffTime()));
            map.put(FlightUidOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getArrivalCityName()), MapperUtils.trim(order.getArrivalCityNameEn())));
            map.put(FlightUidOrderDetailEnum.ARRIVALPORTNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getArrivalPortName()), MapperUtils.trim(order.getArrivalPortNameEn())));
            map.put(FlightUidOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrivalDateTime()));
            map.put(FlightUidOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccountCode()));
            map.put(FlightUidOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(FlightUidOrderDetailEnum.ORDERTKT.getCode(), MapperUtils.convertDigitToZero(order.getOrdertkt()));
            map.put(FlightUidOrderDetailEnum.REFUNDTKT.getCode(), MapperUtils.convertDigitToZero(order.getRefundtkt()));
            map.put(FlightUidOrderDetailEnum.ISREFUND.getCode(), MapperUtils.convertTorF(order.getIsRefund(), yesOrNotMap));
            map.put(FlightUidOrderDetailEnum.ISREBOOK.getCode(), MapperUtils.convertTorF(order.getIsRebook(), yesOrNotMap));
            map.put(FlightUidOrderDetailEnum.CHANGETAKEOFFTIME.getCode(), MapperUtils.trim(order.getChangeTakeoffTime()));
            map.put(FlightUidOrderDetailEnum.CHANGEARRIVALTIME.getCode(), MapperUtils.trim(order.getChangeArrivalDatetime()));
            map.put(FlightUidOrderDetailEnum.FLIGHTCLASS.getCode(), FlightOrderDetailService.convertFlightClass(MapperUtils.trim(order.getFlightClass())));
            map.put(FlightUidOrderDetailEnum.AIRLINECN.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getAirlineCnName()), MapperUtils.trim(order.getAirlineEnName())));
            result.add(map);
        }
        return result;
    }

    protected List<Map<String, Object>> convertFlightOrderMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportFlightOrderInfo> data = detailInfo.getFltOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }

        Map yesOrNotMap = BaseDataUtils.getYesOrNoMap(lang);
        Map flightTicketStatusMap = initFlightTicketStatus(lang);
        for (OnlineReportFlightOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            // 基础信息
            map.put(FlightOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZeroString(order.getOrderId()));
            map.put(FlightOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrderStatus()));
            map.put(FlightOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(FlightOrderDetailEnum.ISONLINE.getCode(), MapperUtils.convertBookTypeSingle(order.getIsOnline()));
            map.put(FlightOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            // map.put(FlightOrderDetailEnum.FULLQUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getFullfaretkt()));
            map.put(FlightOrderDetailEnum.ORDERTKT.getCode(), MapperUtils.convertDigitToZero(order.getOrdertkt()));
            map.put(FlightOrderDetailEnum.REFUNDTKT.getCode(), MapperUtils.convertDigitToZero(order.getRefundtkt()));
            map.put(FlightOrderDetailEnum.PASSENGENO.getCode(), MapperUtils.trim(order.getPassengerNo()));
            map.put(FlightOrderDetailEnum.SEQUENCE.getCode(), MapperUtils.convertDigitToString(order.getSequence()));
            map.put(FlightOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlightNo()));
            map.put(FlightOrderDetailEnum.SUBCLASS.getCode(), MapperUtils.trim(order.getSubClass()));
            // map.put(FlightOrderDetailEnum.FLIGHTSTATUS.getCode(), MapperUtils.trim(order.getFlightStatus()));
            // map.put(FlightOrderDetailEnum.FLIGHTTIME.getCode(), DigitBaseUtils.formatDigit(order.getFlightTime()));
            map.put(FlightOrderDetailEnum.AIRLINE.getCode(), MapperUtils.trim(order.getAirline()));
            map.put(FlightOrderDetailEnum.AIRLINECN.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getAirlineCnName()), MapperUtils.trim(order.getAirlineEnName())));
            map.put(FlightOrderDetailEnum.TICKETNO.getCode(), MapperUtils.trim(order.getTicketNo()));
            if (null != order.getTicketNo()) {
                map.put(FlightOrderDetailEnum.TICKETSTATUS.getCode(), getDesc(order.getTicketStatus(), flightTicketStatusMap));
            } else {
                map.put(FlightOrderDetailEnum.TICKETSTATUS.getCode(), getDesc(0, flightTicketStatusMap));
            }
            map.put(FlightOrderDetailEnum.REALCLASS.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getRealClass()), MapperUtils.trim(order.getRealClassEn())));
            map.put(FlightOrderDetailEnum.FLIGHTCITYCODE.getCode(), MapperUtils.trim(order.getFlightCityCode()));
            map.put(FlightOrderDetailEnum.FLIGHTCITY.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getFlightCity()), MapperUtils.trim(order.getFlightCityEn())));
            map.put(FlightOrderDetailEnum.FLIGHTCITY2.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getFlightCity2()), MapperUtils.trim(order.getFlightCity2En())));
            map.put(FlightOrderDetailEnum.TPMS.getCode(), DigitBaseUtils.formatDigit(order.getTpms()));
            map.put(FlightOrderDetailEnum.TPMSEN.getCode(), DigitBaseUtils.formatDigit(order.getTpmsEn()));
            map.put(FlightOrderDetailEnum.DEPARTUREPORTNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDeparturePortName()), MapperUtils.trim(order.getDeparturePortNameEn())));
            map.put(FlightOrderDetailEnum.DPORTCODE.getCode(), MapperUtils.trim(order.getDportCode()));
            map.put(FlightOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDepartureCityName()), MapperUtils.trim(order.getDepartureCityNameEn())));
            map.put(FlightOrderDetailEnum.DEPARTURECITYCODE.getCode(), MapperUtils.trim(order.getDepartureCityCode()));
//            map.put(FlightOrderDetailEnum.DEPARTURECOUNTRY.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDepartureCountry()), MapperUtils.trim(order.getDepartureCountryEn())));
//            map.put(FlightOrderDetailEnum.DEPARTURECONTINENT.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDepartureContinent()), MapperUtils.trim(order.getDepartureContinentEn())));
            map.put(FlightOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoffTime()));
            map.put(FlightOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getArrivalCityName()), MapperUtils.trim(order.getArrivalCityNameEn())));
            map.put(FlightOrderDetailEnum.ARRIVALCITYCODE.getCode(), MapperUtils.trim(order.getArrivalCityCode()));
            map.put(FlightOrderDetailEnum.ARRIVALPORTNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getArrivalPortName()), MapperUtils.trim(order.getArrivalPortNameEn())));
            map.put(FlightOrderDetailEnum.APORTCODE.getCode(), MapperUtils.trim(order.getAportCode()));
            map.put(FlightOrderDetailEnum.ARRIVALCONUNTRY.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getArrivalCountry()), MapperUtils.trim(order.getArrivalCountryEn())));
            map.put(FlightOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrivalDateTime()));
            map.put(FlightOrderDetailEnum.DESTCITYNAME.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getDestCityName()), MapperUtils.trim(order.getDestCityNameEn())));
//            map.put(FlightOrderDetailEnum.AGETYPE.getCode(), convertAgeType(MapperUtils.trim(order.getAgeType())));//ADU, 成人票； BAB ,婴儿票；   CHI，儿童票
            map.put(FlightOrderDetailEnum.FLIGHTCLASS.getCode(), FlightOrderDetailService.convertFlightClass(MapperUtils.trim(order.getFlightClass())));

            // 详细信息
            map.put(FlightOrderDetailEnum.PREPAYTYPE.getCode(), FlightOrderDetailService.convertPrepayType(MapperUtils.trim(order.getPrepayType())));
            map.put(FlightOrderDetailEnum.TRIPID.getCode(), MapperUtils.convertDigitToDash(order.getTripId()));
            map.put(FlightOrderDetailEnum.GROUPMONTH.getCode(), MapperUtils.convertDigitToString(order.getGroupMonth()));

            map.put(FlightOrderDetailEnum.PRINTTICKETTIME.getCode(), convertPrintTicketTime(order));
            // map.put(FlightOrderDetailEnum.PROVIDEBILLTYPE.getCode(), MapperUtils.trim(order.getProvideBillType()));
            // map.put(FlightOrderDetailEnum.REBOOK_PREPAYTYPENAME.getCode(), MapperUtils.trim(order.getRebookPrepaytypename()));
            map.put(FlightOrderDetailEnum.LOWDTIME.getCode(), MapperUtils.trim(order.getLowDtime()));
            map.put(FlightOrderDetailEnum.CLASSRID.getCode(), MapperUtils.trim(order.getClassRid()));
            // map.put(FlightOrderDetailEnum.CLASSRC.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getClassRc()), MapperUtils.trim(order.getClassRcEn())));
            map.put(FlightOrderDetailEnum.AGREEMENTRID.getCode(), MapperUtils.trim(order.getAgreementRid()));
            // map.put(FlightOrderDetailEnum.AGREEMENTRC.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getAgreementRc()), MapperUtils.trim(order.getAgreementRcEn())));
            // map.put(FlightOrderDetailEnum.AGREEMENTRCVV.getCode(), MapperUtils.trim(order.getAgreementRcVv()));
            map.put(FlightOrderDetailEnum.LOWRID.getCode(), MapperUtils.trim(order.getLowRid()));
            // map.put(FlightOrderDetailEnum.LOWRC.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getLowRc()), MapperUtils.trim(order.getLowRcEn())));
            // map.put(FlightOrderDetailEnum.LOWPRICERCVV.getCode(), MapperUtils.trim(order.getLowpriceRcVv()));
            // map.put(FlightOrderDetailEnum.LOWRC_MARK.getCode(), MapperUtils.trim(order.getLowReasonRemarks()));
            map.put(FlightOrderDetailEnum.PRERID.getCode(), MapperUtils.trim(order.getPreRid()));
            // map.put(FlightOrderDetailEnum.PRERC.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getPreRc()), MapperUtils.trim(order.getPreRcEn())));
            map.put(FlightOrderDetailEnum.TIMERID.getCode(), MapperUtils.trim(order.getTimeRid()));
            // map.put(FlightOrderDetailEnum.TIMERC.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getTimeRc()), MapperUtils.trim(order.getTimeRcEn())));
            // map.put(FlightOrderDetailEnum.TIMERCVV.getCode(), MapperUtils.trim(order.getTimeRcVv()));
//            map.put(FlightOrderDetailEnum.DISTANCERCVV.getCode(), MapperUtils.trim(order.getDistanceRcVv()));
            map.put(FlightOrderDetailEnum.ISREFUND.getCode(), MapperUtils.convertTorF(order.getIsRefund(), yesOrNotMap));
            map.put(FlightOrderDetailEnum.REFUNDREASONDESC.getCode(), MapperUtils.trim(order.getRefundResonDesc()));
//             map.put(FlightOrderDetailEnum.REFUNDTYPE.getCode(), MapperUtils.trim(order.getFlightRefundType()));
//             map.put(FlightOrderDetailEnum.REFUNDRTIME.getCode(), MapperUtils.trim(order.getRefundAuditedTime()));
            map.put(FlightOrderDetailEnum.ISREBOOK.getCode(), MapperUtils.convertTorF(order.getIsRebook(), yesOrNotMap));
            map.put(FlightOrderDetailEnum.REBOOKREASONDESC.getCode(), MapperUtils.trim(order.getRebookResonDesc()));
            // map.put(FlightOrderDetailEnum.REBOOKTYPE.getCode(), MapperUtils.trim(order.getFlightRebookType()));
            map.put(FlightOrderDetailEnum.REBOOKTIME.getCode(), MapperUtils.trim(order.getRebookTime()));
            // map.put(FlightOrderDetailEnum.ORIGINALORDERID.getCode(), MapperUtils.convertDigitToString(order.getOriginalOrderId()));
            map.put(FlightOrderDetailEnum.CHANGEFLIGHTNO.getCode(), MapperUtils.trim(order.getChangeFlightNo()));
            map.put(FlightOrderDetailEnum.CHANGETAKEOFFTIME.getCode(), MapperUtils.trim(order.getChangeTakeoffTime()));
            map.put(FlightOrderDetailEnum.CHANGEARRIVALTIME.getCode(), MapperUtils.trim(order.getChangeArrivalDatetime()));

            // map.put(FlightOrderDetailEnum.BFRETURN.getCode(), MapperUtils.convertTorF(order.getBfReturn(), yesOrNotMap));
            map.put(FlightOrderDetailEnum.CONTRACTTYPE.getCode(), StringUtils.equalsIgnoreCase(order.getContractType(), "C") ? MapperUtils.convertTorF(YesOrNotEnum.T.toString(), yesOrNotMap) : MapperUtils.convertTorF(YesOrNotEnum.F.toString(), yesOrNotMap));
            map.put(FlightOrderDetailEnum.AGREEMENTTYPENAME.getCode(), MapperUtils.trim(order.getAgreementTypeName()));
            map.put(FlightOrderDetailEnum.AGREEMENTRATE.getCode(), DigitBaseUtils.formatDigit(order.getAgreementRate()));
            map.put(FlightOrderDetailEnum.PREORDERDATE.getCode(), ObjectUtils.defaultIfNull(order.getPreOrderDate(), 0).toString());
            map.put(FlightOrderDetailEnum.FLIGHTWAYDESC.getCode(),  MapperUtils.trim(order.getFlightWayDesc()));
            map.put(FlightOrderDetailEnum.CARRIERFLIGHTNO.getCode(), MapperUtils.trim(order.getCarrierflightno()));

            // 公司信息
//            map.put(FlightOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(FlightOrderDetailEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorpName()));
//            map.put(FlightOrderDetailEnum.STDINDUSTRY1.getCode(), MapperUtils.trim(order.getStdIndustry1()));
//            map.put(FlightOrderDetailEnum.STDINDUSTRY2.getCode(), MapperUtils.trim(order.getStdIndustry2()));


            // 预定人信息
//            map.put(FlightOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(FlightOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(FlightOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
//            map.put(FlightOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRankName()));
            map.put(FlightOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
//            map.put(FlightOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
//            map.put(FlightOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
//            map.put(FlightOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
//            map.put(FlightOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
//            map.put(FlightOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
//            map.put(FlightOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
//            map.put(FlightOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
//            map.put(FlightOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
//            map.put(FlightOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(FlightOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCostCenter1()));
//            map.put(FlightOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCostCenter2()));
//            map.put(FlightOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCostCenter3()));
//            map.put(FlightOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCostCenter4()));
//            map.put(FlightOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCostCenter5()));
//            map.put(FlightOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCostCenter6()));

            //订单钱款信息
            map.put(FlightOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));
            map.put(FlightOrderDetailEnum.PRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrice()));
            map.put(FlightOrderDetailEnum.NETFARE.getCode(), DigitBaseUtils.formatDigit(order.getNetfare()));
            map.put(FlightOrderDetailEnum.OILFEE.getCode(), DigitBaseUtils.formatDigit(order.getOilFee()));
            map.put(FlightOrderDetailEnum.TAX.getCode(), DigitBaseUtils.formatDigit(order.getTax()));
            map.put(FlightOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getServiceFee()));
//            map.put(FlightOrderDetailEnum.INSERANCEFEE.getCode(), DigitBaseUtils.formatDigit(order.getInsuranceFee()));
//            map.put(FlightOrderDetailEnum.BINDAMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getBindAmount()));
            map.put(FlightOrderDetailEnum.CHANGEFEE.getCode(), DigitBaseUtils.formatDigit(order.getChangeFee()));
            map.put(FlightOrderDetailEnum.REBOOKPRICEDIFFERENT.getCode(), DigitBaseUtils.formatDigit(order.getRebookPriceDifferential()));
            map.put(FlightOrderDetailEnum.REBOOKSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebookServiceFee()));
//            map.put(FlightOrderDetailEnum.TAX_DIFFERENTIAL.getCode(), DigitBaseUtils.formatDigit(order.getTaxDifferential()));
            map.put(FlightChangeOrderDetailEnum.REBOOKSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebookServiceFee()));
            map.put(FlightOrderDetailEnum.REFUNDFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundFee()));
            map.put(FlightOrderDetailEnum.REFUNDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundServiceFee()));
//            map.put(FlightOrderDetailEnum.SENDTICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getSendTicketFee()));
            map.put(FlightOrderDetailEnum.TICKETBEHINDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getTicketBehindServiceFee()));
            map.put(FlightOrderDetailEnum.REBOOKBEHINDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebookBehindServiceFee()));
            map.put(FlightOrderDetailEnum.REFUNDTBEHINDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundBehindServiceFee()));
//            map.put(FlightOrderDetailEnum.ISMIXPAYMENT.getCode(), MapperUtils.trim(order.getIsmixpayment()));
//            map.put(FlightOrderDetailEnum.SETTLEMENTACCNTAMT.getCode(), DigitBaseUtils.formatDigit(order.getAccntAmt()));
//            map.put(FlightOrderDetailEnum.SETTLEMENTPERSONAMT.getCode(), DigitBaseUtils.formatDigit(order.getPersonAmt()));
//            map.put(FlightOrderDetailEnum.STDPRICE.getCode(), DigitBaseUtils.formatDigit(order.getStdPrice()));
            map.put(FlightOrderDetailEnum.PRINTPRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrintPrice()));
//            map.put(FlightOrderDetailEnum.PUBLISHPRICE.getCode(), DigitBaseUtils.formatDigit(order.getPublishprice()));
            map.put(FlightOrderDetailEnum.PRICERATE.getCode(), DigitBaseUtils.formatDigit(order.getPriceRate()));
            map.put(FlightOrderDetailEnum.CORPPRICEADJ.getCode(), DigitBaseUtils.formatDigit(order.getCorpPriceAdj()));
            map.put(FlightOrderDetailEnum.SAVE_AMOUNT_3C.getCode(), DigitBaseUtils.formatDigit(order.getSaveAmount3c()));
//            map.put(FlightOrderDetailEnum.SAVE_AMOUNT_PREMIUM.getCode(), DigitBaseUtils.formatDigit(order.getSaveAmountPremium()));
//            map.put(FlightOrderDetailEnum.CONTROL_SAVE.getCode(), DigitBaseUtils.formatDigit(order.getControlSave()));

            map.put(FlightOrderDetailEnum.COUNTOFPASSENGERFLIGHT.getCode(), MapperUtils.convertDigitToZero(order.getCountofpassengerflight()));
            map.put(FlightOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassengerName()));
            map.put(FlightOrderDetailEnum.JOURNEYNO.getCode(), MapperUtils.trim(order.getJourneyNo()));

            result.add(map);
        }
        return result;
    }

    /**
     * 构建出退票时间 状态为RA时取退票申请时间
     * @param order
     * @return
     */
    private String convertPrintTicketTime(OnlineReportFlightOrderInfo order) {
        if (FlightOrderStatusNewEnum.isRA(MapperUtils.trim(order.getOrderStatus()))){
            return MapperUtils.trim(order.getRefundtime());
        }
        return MapperUtils.trim(order.getPrintTicketTime());
    }

    protected List<Map<String, Object>> convertFlightRebookOrderMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportFlightOrderInfo> data = detailInfo.getFltOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }

        for (OnlineReportFlightOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(FlightChangeOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(FlightChangeOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrderId()));
            map.put(FlightChangeOrderDetailEnum.PRINTTICKETTIME.getCode(), MapperUtils.trim(order.getPrintTicketTime()));
            map.put(FlightChangeOrderDetailEnum.REALCLASS.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getRealClass()), MapperUtils.trim(order.getRealClassEn())));
            map.put(FlightChangeOrderDetailEnum.SUBCLASS.getCode(), MapperUtils.trim(order.getSubClass()));
            map.put(FlightChangeOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(FlightChangeOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
            map.put(FlightChangeOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassengerName()));
            map.put(FlightChangeOrderDetailEnum.EMPLOYEEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(FlightChangeOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(FlightChangeOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(FlightChangeOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(FlightChangeOrderDetailEnum.FLIGHTCLASS.getCode(), FlightOrderDetailService.convertFlightClass(MapperUtils.trim(order.getFlightClass())));
            map.put(FlightChangeOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoffTime()));
            map.put(FlightChangeOrderDetailEnum.FLIGHTCITY2.getCode(), MapperUtils.getValByLang(lang, order.getFlightCity2(), order.getFlightCity2En()));
            map.put(FlightChangeOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getDepartureCityName(), order.getDepartureCityNameEn()));
            map.put(FlightChangeOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrivalCityName(), order.getArrivalCityNameEn()));

            map.put(FlightChangeOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlightNo()));
            map.put(FlightChangeOrderDetailEnum.AIRELINE.getCode(), MapperUtils.trim(order.getAirline()));
            map.put(FlightChangeOrderDetailEnum.STDPRICE.getCode(), getStdPrice(order.getFlightClass(), DigitBaseUtils.formatDigit(order.getStdPrice()).toString()));
            map.put(FlightChangeOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));

            map.put(FlightChangeOrderDetailEnum.PRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrice()));
            map.put(FlightChangeOrderDetailEnum.TAX.getCode(), DigitBaseUtils.formatDigit(order.getTax()));
            map.put(FlightChangeOrderDetailEnum.OILFEE.getCode(), DigitBaseUtils.formatDigit(order.getOilFee()));
            map.put(FlightChangeOrderDetailEnum.CHANGEFEE.getCode(), DigitBaseUtils.formatDigit(order.getChangeFee()));
            map.put(FlightChangeOrderDetailEnum.INSERANCEFEE.getCode(), DigitBaseUtils.formatDigit(order.getInsuranceFee()));
            map.put(FlightChangeOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getServiceFee()));
            map.put(FlightChangeOrderDetailEnum.REFUNDFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundFee()));
            map.put(FlightChangeOrderDetailEnum.REFUNDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundServiceFee()));
            map.put(FlightChangeOrderDetailEnum.REBOOKSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebookServiceFee()));
            map.put(FlightChangeOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));

            Set<String> rc = new HashSet<>();
            if (StringUtils.isNotEmpty(order.getPreRid())) {
                rc.add(order.getPreRid());
            }
            if (StringUtils.isNotEmpty(order.getLowRid())) {
                rc.add(order.getLowRid());
            }
            if (StringUtils.isNotEmpty(order.getClassRid())) {
                rc.add(order.getClassRid());
            }
            if (StringUtils.isNotEmpty(order.getAgreementRid())) {
                rc.add(order.getAgreementRid());
            }
            map.put(FlightChangeOrderDetailEnum.REASONCODE.getCode(), (rc.size() == 0) ? GlobalConst.STRING_EMPTY : StringUtils.join(rc.toArray(), GlobalConst.SEPARATOR));
            map.put(FlightChangeOrderDetailEnum.BOOKTYPE.getCode(), MapperUtils.convertBookTypeSingle(order.getIsOnline()));
            map.put(FlightChangeOrderDetailEnum.CORPPRICE.getCode(), DigitBaseUtils.formatDigit(order.getCorpPrice()));
            map.put(FlightChangeOrderDetailEnum.TICKETNO.getCode(), MapperUtils.trim(order.getTicketNo()));

            Set<String> rcDesc = new HashSet<>();
            if (StringUtils.isNotEmpty(order.getPreRid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getPreRc(), order.getPreRcEn()));
            }
            if (StringUtils.isNotEmpty(order.getLowRid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getLowRc(), order.getLowRcEn()));
            }
            if (StringUtils.isNotEmpty(order.getClassRid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getClassRc(), order.getClassRcEn()));
            }
            if (StringUtils.isNotEmpty(order.getAgreementRid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getAgreementRc(), order.getAgreementRcEn()));
            }
            map.put(FlightChangeOrderDetailEnum.REASON.getCode(), (rcDesc.size() == 0) ? GlobalConst.STRING_EMPTY : StringUtils.join(rcDesc.toArray(), GlobalConst.SEPARATOR));

            map.put(FlightChangeOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrivalDateTime()));
            map.put(FlightChangeOrderDetailEnum.AMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));
            map.put(FlightChangeOrderDetailEnum.DEPARTURECITYANDPORT.getCode(), MapperUtils.getValByLang(lang, order.getDepartureCityName() + "/" + order.getDeparturePortName(), order.getDepartureCityNameEn() + "/" + order.getDeparturePortNameEn()));
            map.put(FlightChangeOrderDetailEnum.ARRIVALCITYANDPORT.getCode(), MapperUtils.getValByLang(lang, order.getArrivalCityName() + "/" + order.getArrivalPortName(), order.getArrivalCityNameEn() + "/" + order.getArrivalPortNameEn()));
            map.put(FlightChangeOrderDetailEnum.PASSENGERNAMEPY.getCode(), MapperUtils.trim(order.getPassengerNamePy()));
            map.put(FlightChangeOrderDetailEnum.TPMS.getCode(), DigitBaseUtils.formatDigit(order.getTpms()));
            map.put(FlightChangeOrderDetailEnum.PREORDERDATE.getCode(), MapperUtils.convertDigitToZero(order.getPreOrderDate()));

            map.put(FlightChangeOrderDetailEnum.REBOOKTIMES.getCode(), MapperUtils.convertDigitToZero(order.getRebooktimes()));
            map.put(FlightChangeOrderDetailEnum.REBOOKPRICEDIFFERENTIAL.getCode(), DigitBaseUtils.formatDigit(order.getRebookPriceDifferential()));
            map.put(FlightChangeOrderDetailEnum.REBOOKSUBCLASS.getCode(), MapperUtils.trim(order.getRebookSubclass()));
            map.put(FlightChangeOrderDetailEnum.REBOOKREASONDESC.getCode(), MapperUtils.trim(order.getRebookResonDesc()));
            map.put(FlightChangeOrderDetailEnum.SUBSIDY.getCode(), org.apache.commons.lang3.StringUtils.EMPTY);
            result.add(map);
        }
        return result;
    }

    protected List<Map<String, Object>> convertFlightRefundOrderMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportFlightOrderInfo> data = detailInfo.getFltOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }

        for (OnlineReportFlightOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(FlightRefundOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(FlightRefundOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrderId()));
            map.put(FlightRefundOrderDetailEnum.PRINTTICKETTIME.getCode(), MapperUtils.trim(order.getPrintTicketTime()));
            map.put(FlightRefundOrderDetailEnum.REALCLASS.getCode(), MapperUtils.getValByLang(lang, MapperUtils.trim(order.getRealClass()), MapperUtils.trim(order.getRealClassEn())));
            map.put(FlightRefundOrderDetailEnum.SUBCLASS.getCode(), MapperUtils.trim(order.getSubClass()));
            map.put(FlightRefundOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(FlightRefundOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
            map.put(FlightRefundOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassengerName()));
            map.put(FlightRefundOrderDetailEnum.EMPLOYEEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(FlightRefundOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(FlightRefundOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(FlightRefundOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(FlightRefundOrderDetailEnum.FLIGHTCLASS.getCode(), OrderTypeEnum.getEnumByName(order.getFlightClass()).getDesCn());
            map.put(FlightRefundOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoffTime()));
            map.put(FlightRefundOrderDetailEnum.FLIGHTCITY2.getCode(), MapperUtils.getValByLang(lang, order.getFlightCity2(), order.getFlightCity2En()));
            map.put(FlightRefundOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlightNo()));

            map.put(FlightRefundOrderDetailEnum.PRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrice()));
            map.put(FlightRefundOrderDetailEnum.TAX.getCode(), DigitBaseUtils.formatDigit(order.getTax()));
            map.put(FlightRefundOrderDetailEnum.OILFEE.getCode(), DigitBaseUtils.formatDigit(order.getOilFee()));
            map.put(FlightRefundOrderDetailEnum.CHANGEFEE.getCode(), DigitBaseUtils.formatDigit(order.getChangeFee()));
            map.put(FlightRefundOrderDetailEnum.INSERANCEFEE.getCode(), DigitBaseUtils.formatDigit(order.getInsuranceFee()));
            map.put(FlightRefundOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getServiceFee()));
            map.put(FlightRefundOrderDetailEnum.REFUNDFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundFee()));
            map.put(FlightRefundOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));
            map.put(FlightRefundOrderDetailEnum.REFUNDSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefundServiceFee()));
            map.put(FlightRefundOrderDetailEnum.REBOOKSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getRebookServiceFee()));

            Set<String> rc = new HashSet<>();
            if (StringUtils.isNotEmpty(order.getPreRid())) {
                rc.add(order.getPreRid());
            }
            if (StringUtils.isNotEmpty(order.getLowRid())) {
                rc.add(order.getLowRid());
            }
            if (StringUtils.isNotEmpty(order.getClassRid())) {
                rc.add(order.getClassRid());
            }
            if (StringUtils.isNotEmpty(order.getAgreementRid())) {
                rc.add(order.getAgreementRid());
            }
            map.put(FlightRefundOrderDetailEnum.REASONCODE.getCode(), (rc.size() == 0) ? GlobalConst.STRING_EMPTY : StringUtils.join(rc.toArray(), GlobalConst.SEPARATOR));

            map.put(FlightRefundOrderDetailEnum.CORPPRICE.getCode(), DigitBaseUtils.formatDigit(order.getCorpPrice()));
            map.put(FlightRefundOrderDetailEnum.TICKETNO.getCode(), MapperUtils.trim(order.getTicketNo()));

            map.put(FlightRefundOrderDetailEnum.BOOKTYPE.getCode(), MapperUtils.convertBookTypeSingle(order.getIsOnline()));

            Set<String> rcDesc = new HashSet<>();
            if (StringUtils.isNotEmpty(order.getPreRid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getPreRc(), order.getPreRcEn()));
            }
            if (StringUtils.isNotEmpty(order.getLowRid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getLowRc(), order.getLowRcEn()));
            }
            if (StringUtils.isNotEmpty(order.getClassRid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getClassRc(), order.getLowRcEn()));
            }
            if (StringUtils.isNotEmpty(order.getAgreementRid())) {
                rcDesc.add(MapperUtils.getValByLang(lang, order.getAgreementRc(), order.getLowRcEn()));
            }
            map.put(FlightRefundOrderDetailEnum.REASON.getCode(), (rcDesc.size() == 0) ? GlobalConst.STRING_EMPTY : StringUtils.join(rcDesc.toArray(), GlobalConst.SEPARATOR));

            map.put(FlightRefundOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrivalDateTime()));
            map.put(FlightRefundOrderDetailEnum.AMOUNT.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));

            map.put(FlightRefundOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getDepartureCityName(), order.getDepartureCityNameEn()));
            map.put(FlightRefundOrderDetailEnum.DEPARTUREPORTNAME.getCode(), MapperUtils.getValByLang(lang, order.getDeparturePortName(), order.getDeparturePortNameEn()));
            map.put(FlightRefundOrderDetailEnum.DPORTCODE.getCode(), MapperUtils.trim(order.getDportCode()));
            map.put(FlightRefundOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrivalCityName(), order.getArrivalCityNameEn()));
            map.put(FlightRefundOrderDetailEnum.ARRIVALPORTNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrivalPortName(), order.getArrivalPortNameEn()));
            map.put(FlightRefundOrderDetailEnum.APORTCODE.getCode(), MapperUtils.trim(order.getAportCode()));
            map.put(FlightRefundOrderDetailEnum.PASSENGERNAMEPY.getCode(), MapperUtils.trim(order.getPassengerNamePy()));
            map.put(FlightRefundOrderDetailEnum.TPMS.getCode(), DigitBaseUtils.formatDigit(order.getTpms()));

            map.put(FlightRefundOrderDetailEnum.REFUNDTIME.getCode(), MapperUtils.trim(order.getRefundtime()));
            map.put(FlightRefundOrderDetailEnum.REFUNDREASONDESC.getCode(), MapperUtils.trim(order.getRefundResonDesc()));
            map.put(FlightRefundOrderDetailEnum.SUBSIDY.getCode(), org.apache.commons.lang3.StringUtils.EMPTY);
            result.add(map);
        }
        return result;
    }

    protected List<Map<String, Object>> convertFlightUnuseOrderMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportFlightOrderInfo> data = detailInfo.getFltOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        String segStatus = SharkUtils.get("Exceltopname.Unusedticket", lang);
        for (OnlineReportFlightOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(UnUseFlightOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(UnUseFlightOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZero(order.getOrderId()));
            map.put(UnUseFlightOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(UnUseFlightOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
            map.put(UnUseFlightOrderDetailEnum.PASSENGENAME.getCode(), MapperUtils.trim(order.getPassengerName()));
            map.put(UnUseFlightOrderDetailEnum.PRINTTICKETTIME.getCode(), MapperUtils.trim(order.getPrintTicketTime()));
            map.put(UnUseFlightOrderDetailEnum.TAKEOFFTIME.getCode(), MapperUtils.trim(order.getTakeoffTime()));
            map.put(UnUseFlightOrderDetailEnum.FLIGHTCITY.getCode(), MapperUtils.getValByLang(lang, order.getFlightCity(), order.getFlightCityEn()));
            map.put(UnUseFlightOrderDetailEnum.FLIGHTNO.getCode(), MapperUtils.trim(order.getFlightNo()));
            map.put(UnUseFlightOrderDetailEnum.SUBCLASS.getCode(), MapperUtils.trim(order.getSubClass()));
            map.put(UnUseFlightOrderDetailEnum.PRICERATE.getCode(), DigitBaseUtils.formatDigit(order.getPriceRate()));
            map.put(UnUseFlightOrderDetailEnum.PRINTPRICE.getCode(), DigitBaseUtils.formatDigit(order.getPrintPrice()));
            map.put(UnUseFlightOrderDetailEnum.TICKETNO.getCode(), MapperUtils.trim(order.getTicketNo()));
            map.put(UnUseFlightOrderDetailEnum.SEGSTATUS.getCode(), segStatus);
            map.put(UnUseFlightOrderDetailEnum.ISPERSONAL.getCode(), MapperUtils.trim(order.getFeeType()));
            map.put(UnUseFlightOrderDetailEnum.PAYMENTTYPE.getCode(), MapperUtils.trim(order.getPrepayType()));
            map.put(UnUseFlightOrderDetailEnum.WORKCITY.getCode(), MapperUtils.trim(order.getWorkCity()));
            map.put(UnUseFlightOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCostCenter1()));
            map.put(UnUseFlightOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCostCenter1()));
            map.put(UnUseFlightOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCostCenter1()));
            map.put(UnUseFlightOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(UnUseFlightOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(UnUseFlightOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(UnUseFlightOrderDetailEnum.FLIGHTCLASS.getCode(), FlightOrderDetailService.convertFlightClass(MapperUtils.trim(order.getFlightClass())));
            map.put(UnUseFlightOrderDetailEnum.EMPLOYEEID.getCode(), MapperUtils.trim(order.getEmployeId()));
            map.put(UnUseFlightOrderDetailEnum.TRIPID.getCode(), MapperUtils.convertDigitToDash(order.getTripId()));
            map.put(UnUseFlightOrderDetailEnum.EXPIREDATE.getCode(), DateUtils.getExpireDate(order.getPrintTicketTime()));
            map.put(UnUseFlightOrderDetailEnum.ORDERSTATUS.getCode(), convertOrderStatus(order.getOrderStatus()));
            map.put(UnUseFlightOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getRealPay()));
            map.put(UnUseFlightOrderDetailEnum.RANK.getCode(), MapperUtils.trim(order.getRankName()));
            result.add(map);
        }
        return result;
    }

    private String getStdPrice(String flightClass, String stdPrice) {
        if (StringUtils.equalsIgnoreCase(flightClass, OrderTypeEnum.N.toString())) {
            return DigitBaseUtils.formatDigit(stdPrice);
        } else {
            return GlobalConst.STRING_EMPTY;
        }
    }

    private String convertAgeType(String ageType) {
        if (StringUtils.isEmpty(ageType)) {
            return MapperUtils.trim(ageType);
        }
        if (StringUtils.equalsIgnoreCase(AgeTypeEnums.ADU.toString(), ageType.trim())) {
            return ChineseLanguageConfig.get("ageType_adu");
        } else if (StringUtils.equalsIgnoreCase(AgeTypeEnums.BAB.toString(), ageType.trim())) {
            return ChineseLanguageConfig.get("ageType_bab");
        } else if (StringUtils.equalsIgnoreCase(AgeTypeEnums.CHI.toString(), ageType.trim())) {
            return ChineseLanguageConfig.get("ageType_chi");
        }
        return MapperUtils.trim(ageType);
    }

    public String convertRefundcustomerstatus(String status) {
        if (StringUtils.isEmpty(status)) {
            return MapperUtils.trim(status);
        }
        if (StringUtils.equalsIgnoreCase("T", status.trim())) {
            return ChineseLanguageConfig.get("refund_customer_status_t");
        } else if (StringUtils.equalsIgnoreCase("F", status.trim())) {
            return ChineseLanguageConfig.get("refund_customer_status_f");
        } else {
            return MapperUtils.trim(status);
        }
    }

    private String getDesc(Integer key, Map map) {
        if (Objects.isNull(key) || Objects.isNull(map) || map.isEmpty()) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.isEmpty(StringUtils.trimToEmpty((String) map.get(key)))) {
            return String.valueOf(key);
        }
        return (String) map.get(key);
    }

    private Map initFlightTicketStatus(String lang) {
        return ImmutableMap.builder()
                .put(FlightTicketStatusEnum.Unknown_0.getValue(), SharkUtils.get(FlightTicketStatusEnum.Unknown_0.getDescription(), lang))
                .put(FlightTicketStatusEnum.Unknown_1.getValue(), SharkUtils.get(FlightTicketStatusEnum.Unknown_1.getDescription(), lang))
                .put(FlightTicketStatusEnum.Unknown_2.getValue(), SharkUtils.get(FlightTicketStatusEnum.Unknown_2.getDescription(), lang))
                .put(FlightTicketStatusEnum.Unknown_3.getValue(), SharkUtils.get(FlightTicketStatusEnum.Unknown_3.getDescription(), lang))
                .put(FlightTicketStatusEnum.UnUse.getValue(), SharkUtils.get(FlightTicketStatusEnum.UnUse.getDescription(), lang))
                .put(FlightTicketStatusEnum.Used.getValue(), SharkUtils.get(FlightTicketStatusEnum.Used.getDescription(), lang))
                .put(FlightTicketStatusEnum.CheckIn.getValue(), SharkUtils.get(FlightTicketStatusEnum.CheckIn.getDescription(), lang))
                .put(FlightTicketStatusEnum.OutGoing.getValue(), SharkUtils.get(FlightTicketStatusEnum.OutGoing.getDescription(), lang))
                .put(FlightTicketStatusEnum.Cancelled.getValue(), SharkUtils.get(FlightTicketStatusEnum.Cancelled.getDescription(), lang))
                .put(FlightTicketStatusEnum.Refunded.getValue(), SharkUtils.get(FlightTicketStatusEnum.Refunded.getDescription(), lang))
                .put(FlightTicketStatusEnum.Changed.getValue(), SharkUtils.get(FlightTicketStatusEnum.Changed.getDescription(), lang))
                .put(FlightTicketStatusEnum.Pending.getValue(), SharkUtils.get(FlightTicketStatusEnum.Pending.getDescription(), lang))
                .put(FlightTicketStatusEnum.Control9.getValue(), SharkUtils.get(FlightTicketStatusEnum.Control9.getDescription(), lang))
                .put(FlightTicketStatusEnum.Control10.getValue(), SharkUtils.get(FlightTicketStatusEnum.Control10.getDescription(), lang))
                .put(FlightTicketStatusEnum.Disabled.getValue(), SharkUtils.get(FlightTicketStatusEnum.Disabled.getDescription(), lang))
                .build();
    }

    private String convertOrderStatus(String orderStatus) {
        Map<String, String> map = FlightOrderStatusNewEnum.toMap();
        return map.get(MapperUtils.trim(orderStatus));
    }


    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.flight;
    }
}
