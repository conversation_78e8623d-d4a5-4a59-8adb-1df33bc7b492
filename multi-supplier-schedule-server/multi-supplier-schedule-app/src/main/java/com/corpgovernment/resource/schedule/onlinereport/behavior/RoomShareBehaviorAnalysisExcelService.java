package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportCostAllocationRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportCostAllocationResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportZoningPatternRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportZoningPatternResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportZoningPatternTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportZoningPatternTrendResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ZoningInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ZoningPatternInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ZoningTrendInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022-11-18 14:53
 * @desc
 */
@Service
public class RoomShareBehaviorAnalysisExcelService extends AbstractGenralExportDataService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        String reportId = baseCondition.getBaseQueryCondition().getReportId();
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();

        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HtlRoomShare", reportId) ||
                StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HtlRoomShareAnalysis", reportId) ||
                StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HtlCostAllocation", reportId)) {
            Map map = getConditionContent(baseCondition.getBaseQueryCondition().getUid(), baseCondition.getBaseQueryCondition(), lang);
            int sheetIndex = 0;
            chartExcelEntityList.add(buildExcel(baseCondition, sheetIndex++, "TravelAnalysis:Behavior:HtlRoomShare", map));
            chartExcelEntityList.add(buildExcel(baseCondition, sheetIndex++, "TravelAnalysis:Behavior:HtlRoomShareAnalysis", map));
            chartExcelEntityList.add(buildExcel(baseCondition, sheetIndex++, "TravelAnalysis:Behavior:HtlCostAllocation", map));
            return chartExcelEntityList;
        }
        return chartExcelEntityList;
    }


    public ChartExcelEntity buildExcel(BaseQueryConditionBO baseCondition, int sheetIndex, String reportId, Map map) throws BusinessException {
        String lang = baseCondition.getLang();
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HtlRoomShare", reportId)) {
            OnlineReportZoningPatternRequest request = new OnlineReportZoningPatternRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setProductType(baseCondition.getProductType());
            OnlineReportZoningPatternResponse responseType = corpOnlineReportPlatformService.queryHtlRoomShareAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return hotelRoomShare(responseType.getZoningInfoList(), lang, map, reportId, sheetIndex, baseCondition.getProductType());
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        }
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HtlRoomShareAnalysis", reportId)) {
            OnlineReportZoningPatternTrendRequest request = new OnlineReportZoningPatternTrendRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setProductType(baseCondition.getProductType());
            OnlineReportZoningPatternTrendResponse responseType = corpOnlineReportPlatformService.queryHtlRoomShareTrendAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return hotelRoomShareTrend(Optional.ofNullable(responseType.getZoningInfo()).orElse(new ZoningInfo()), lang, map, sheetIndex, baseCondition.getProductType());
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        }
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HtlCostAllocation", reportId)) {
            OnlineReportCostAllocationRequest request = new OnlineReportCostAllocationRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setProductType(baseCondition.getProductType());
            OnlineReportCostAllocationResponse responseType = corpOnlineReportPlatformService.queryHtlCostAllocationAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return hotelRoomShare(responseType.getZoningInfoList(), lang, map, reportId, sheetIndex, baseCondition.getProductType());
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        }
        return null;
    }

    private ChartExcelEntity hotelRoomShare(List<ZoningPatternInfo> patternInfos, String lang, Map map, String reportId, int sheetIndx, String productType) {
        patternInfos = Optional.ofNullable(patternInfos).orElse(new ArrayList<>());
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        String titleFirst = StringUtils.EMPTY;
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HtlCostAllocation", reportId)) {
            // 费用分摊模式分布
            titleFirst = SharkUtils.get("APP.Behavior.FareShareModePct", lang);
        }
        if (StringUtils.equalsIgnoreCase("TravelAnalysis:Behavior:HtlRoomShare", reportId)) {
            // 拼房模式
            titleFirst = SharkUtils.get("APP.Behavior.RoomShareModePct", lang);
        }
        sheet1.setHeaders(Arrays.asList(
                titleFirst,
                SharkUtils.get("Index.nightnum", lang),
                SharkUtils.get("Index.nightnumper", lang)));
        List data = new ArrayList();
        for (ZoningPatternInfo patternInfo : patternInfos) {
            data.add(Arrays.asList(
                    SharkUtils.get(patternInfo.getPattern(), lang),
                    MapperUtils.convertDigitToZero(patternInfo.getTotalQuantity()),
                    MapperUtils.convertDigitToZero(patternInfo.getQuantityPercent())));
        }
        data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                Arrays.asList(SharkUtils.get("Travelanalysis.hotelstyle", lang),
                        getSheetNameByProductType(lang, productType))));
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(titleFirst);
        sheet1.setSheetNum(sheetIndx);
        sheet1.setData(data);
        return sheet1;
    }

    private ChartExcelEntity hotelRoomShareTrend(ZoningInfo zoningInfo, String lang, Map map, int sheetIndex, String productType) {
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(Arrays.asList(
                SharkUtils.get("Index.month", lang),
                SharkUtils.get("Index.nightnum", lang),
                SharkUtils.get("Index.nightnumper", lang)));
        List data = new ArrayList();
        for (ZoningTrendInfo zoningTrendInfo : zoningInfo.getZoningTrendList()) {
            data.add(Arrays.asList(
                    zoningTrendInfo.getPoint(),
                    MapperUtils.convertDigitToZero(zoningTrendInfo.getTotalQuantity()),
                    MapperUtils.convertDigitToZero(zoningTrendInfo.getQuantityPercent())));
        }
        data.addAll(Arrays.asList(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY),
                Arrays.asList(SharkUtils.get("Travelanalysis.hotelstyle", lang),
                        getSheetNameByProductType(lang, productType))));
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(SharkUtils.get("APP.Behavior.RoomShareTrend", lang));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }
}
