//package com.corpgovernment.resource.schedule.onlinereport.reportlib;
//
//import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
//import com.corpgovernment.resource.schedule.domain.onlinereport.cglib.seriallizer.JacksonUtil;
//import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
//import com.corpgovernment.resource.schedule.domain.onlinereport.common.ThreadContext;
//import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
//import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpReportCustomColumnPO;
//import com.corpgovernment.resource.schedule.domain.onlinereport.entity.CostCenterAndDept;
//import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportLibFilterBO;
//import com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib.*;
//import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
//import com.corpgovernment.resource.schedule.domain.onlinereport.search.MainAccounts;
//import com.corpgovernment.resource.schedule.domain.onlinereport.utils.*;
//import com.corpgovernment.resource.schedule.onlinereport.IReportDataService;
//import com.corpgovernment.resource.schedule.onlinereport.enums.ManagerTypeEnum;
//import com.corpgovernment.resource.schedule.onlinereport.reportlib.hive.CarOrderDetailHiveService;
//import com.corpgovernment.resource.schedule.onlinereport.reportlib.hive.FlightOrderDetailHiveService;
//import com.corpgovernment.resource.schedule.onlinereport.reportlib.hive.HotelOrderDetailHiveService;
//import com.corpgovernment.resource.schedule.onlinereport.reportlib.hive.TrainOrderDetailHiveService;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import onlinereport.enums.BizTypeEnums;
//import onlinereport.enums.MonQurTypeEnum;
//import onlinereport.enums.OrderQueryEnum;
//import onlinereport.enums.reportlib.AbnormalFlightOrderDetailEnum;
//import onlinereport.enums.reportlib.CarOrderDetailEnum;
//import onlinereport.enums.reportlib.FlightChangeOrderDetailEnum;
//import onlinereport.enums.reportlib.FlightOrderDetailEnum;
//import onlinereport.enums.reportlib.FlightRefundOrderDetailEnum;
//import onlinereport.enums.reportlib.HotelOrderDetailEnum;
//import onlinereport.enums.reportlib.OrderDetailEnumerable;
//import onlinereport.enums.reportlib.TrainOrderDetailEnum;
//import onlinereport.enums.reportlib.UnUseFlightOrderDetailEnum;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.math.BigDecimal;
//import java.sql.SQLException;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Comparator;
//import java.util.HashMap;
//import java.util.HashSet;
//import java.util.Iterator;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.Set;
//import java.util.stream.Collectors;
//
//import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.LogonConst.ENV_DEV;
//
//
////*
//// * Auther:abguo
//// * Date:2019/9/7
//// * Description:
//// * Project:onlinereportweb
////
//
//@Service("reportLibReportDataService")
//@Slf4j
//public class ReportLibReportDataService implements IReportDataService<ReportLibFilterBO, ReportLibResultEntity> {
//
//    protected static final String PREFIX = ReportLibReportDataService.class.getSimpleName();
////*
////     * 日记tag上下文
//
//
//    @Autowired
//    protected ThreadContext logTagContext;
//    List<ManagerTypeEnum> excludes = Arrays.asList(ManagerTypeEnum.SUPERADMIN, ManagerTypeEnum.MANAGEMENT);
////    @Autowired
////    private UserPermissionService userPermissionService;
//    @Autowired
//    private RpcCorpOnlineReportService onlineReportService;
//    @Autowired
//    private CorpReportCustomColumnService reportColumnService;
//    private String language = "cn";
//
//
//    /**
//     *
//     * @param request
//     * @param depts 权限控制没有 传空list
//     * @param key
//     * @param limit
//     * @return
//     */
//    private static List<CostCenterAndDept> buildCostCenterAndDept(Map<String, Object> request, List<CostCenterAndDept> depts,
//                                                                  String key, int limit) {
//        List<CostCenterAndDept> deptSearchEntityList = new ArrayList<>();
//        if ((MapUtils.isEmpty(request) || !request.containsKey(key)) && CollectionUtils.isEmpty(depts)) {
//            return null;
//        }
//        if (CollectionUtils.isNotEmpty(depts) && depts.stream().anyMatch(i -> CollectionUtils.isNotEmpty(i.getInfo()))) {// 存在权限限制
//            if (request == null) {
//                return null;
//            }
//            request.get(key);
//            Map costcenterAndDeptMap = (request != null && request.get(key) != null) ? (Map) request.get(key) : new HashMap();
//            for (int i = 1; i <= limit; i++) {
//                int finalI = i;
//                if (depts.stream().anyMatch(j -> finalI == j.getLevel())) {
//                    CostCenterAndDept costCenterAndDept = depts.stream().filter(j -> finalI == j.getLevel()).findFirst().get();
//                    List<String> list1 = costCenterAndDept.getInfo();
//                    List<String> departs = (List<String>) costcenterAndDeptMap.get(key + i);
//                    Set<String> set1 = new HashSet<>(CollectionUtils.isEmpty(list1) ? new ArrayList<>() : list1);
//                    Set<String> set2 = new HashSet<>(CollectionUtils.isEmpty(departs) ? set1 : departs);
//                    set1.retainAll(set2);
//                    if (CollectionUtils.isNotEmpty(set1)) {
//                        CostCenterAndDept deptSearchEntity = new CostCenterAndDept();
//                        deptSearchEntity.setLevel(i);
//                        deptSearchEntity.setInfo(new ArrayList<>(set1));
//                        deptSearchEntityList.add(deptSearchEntity);
//                    }
//                }
//            }
//        } else {
//            Map costcenterAndDeptMap = (request != null && request.get(key) != null) ? (Map) request.get(key) : new HashMap();
//            for (int i = 1; i <= limit; i++) {
//                if (!costcenterAndDeptMap.containsKey(key + i)) {
//                    continue;
//                }
//                List<String> departs = (List<String>) costcenterAndDeptMap.get(key + i);
//                Set<String> set2 = new HashSet<>(CollectionUtils.isEmpty(departs) ? new ArrayList<>() : departs);
//                if (CollectionUtils.isNotEmpty(set2)) {
//                    CostCenterAndDept deptSearchEntity = new CostCenterAndDept();
//                    deptSearchEntity.setLevel(i);
//                    deptSearchEntity.setInfo(new ArrayList<>(set2));
//                    deptSearchEntityList.add(deptSearchEntity);
//                }
//            }
//        }
//
//        return deptSearchEntityList;
//    }
//
//
//    @Override
//    public ReportLibResultEntity query(ReportLibFilterBO request) {
//        log.info(PREFIX, "query", logTagContext.getMap());
//        return queryOrderDetail(request);
//    }
//
//    private ReportLibResultEntity queryOrderDetail(ReportLibFilterBO filterBO) {
//        language = filterBO.getLang();
//        ReportLibResultEntity reportLibResultEntity = new ReportLibResultEntity();
//        if (StringUtils.isEmpty(filterBO.getQueryType()) && StringUtils.isEmpty(filterBO.getBizType())) {
//            return reportLibResultEntity;
//        }
//        if (overMaxMonths(filterBO.getStartTime(), filterBO.getEndTime())) {
//            log.warn(PREFIX, "exceed max query time duration");
//            return reportLibResultEntity;
//        }
//        OrderQueryEnum orderQueryEnum = convertOrderQueryEnum(filterBO);
//        if (orderQueryEnum == null) {
//            return reportLibResultEntity;
//        }
//        filterBO.setCustomColumn(getCustomColumns(filterBO.getUid(), filterBO.getBizType()));
//        switch (orderQueryEnum) {
//            case CONSUME:
//                reportLibResultEntity.setRowList(queryData(filterBO));
//                break;
//            case DEPT:
//                if (null == filterBO.getSwitchType()) {
//                    reportLibResultEntity.setDeptData(queryDeptData(filterBO));
//                } else {
//                    //Excel下载
//                    reportLibResultEntity.setRowList(queryDeptExcelData(filterBO));
//                }
//                break;
//            default:
//                reportLibResultEntity = queryOrderDetail(filterBO, orderQueryEnum);
//        }
//        return reportLibResultEntity;
//    }
//
//    private OrderQueryEnum convertOrderQueryEnum(ReportLibFilterBO filterBO) {
//        OrderQueryEnum orderQueryEnum = null;
//        try {
//            if (StringUtils.isNotEmpty(filterBO.getBizType())) {
//                orderQueryEnum = OrderQueryEnum.valueOf(filterBO.getBizType().trim());
//            }
//            if (StringUtils.isNotEmpty(filterBO.getQueryType())) {
//                orderQueryEnum = OrderQueryEnum.valueOf(filterBO.getQueryType().trim());
//            }
//        } catch (Exception e) {
//            log.warn(PREFIX, e, logTagContext.getMap());
//        }
//        return orderQueryEnum;
//    }
//
//    private ReportLibResultEntity queryOrderDetail(ReportLibFilterBO filterBO, OrderQueryEnum orderQueryEnum) {
//        ReportLibResultEntity reportLibResultEntity = new ReportLibResultEntity();
//        log.info(PREFIX, "queryOrderDetail", logTagContext.getMap());
//        switch (orderQueryEnum) {
//            case F:
//                reportLibResultEntity = processFlighData(filterBO);
//                break;
//            case C:
//                reportLibResultEntity = processCarData(filterBO);
//                break;
//            case T:
//                reportLibResultEntity = processTrainData(filterBO);
//                break;
//            case H:
//                reportLibResultEntity = processHotelData(filterBO);
//                break;
//            case F_ABNORMAL:
//                reportLibResultEntity = processFlightAbnormalData(filterBO);
//                break;
//            case F_REFUND:
//                reportLibResultEntity = processFlighRefundData(filterBO);
//                break;
//            case F_CHANGE:
//                reportLibResultEntity = processFlighChangeData(filterBO);
//                break;
//            case F_UNUSE:
//                reportLibResultEntity = processFlighUnuseData(filterBO);
//                break;
//        }
//        if (reportLibResultEntity == null || CollectionUtils.isEmpty(reportLibResultEntity.getData())) {
//            log.info(PREFIX, " lists is null", logTagContext.getMap());
//            return reportLibResultEntity;
//        }
//
//        reportLibResultEntity.setTitleList(reportLibResultEntity.getData().remove(0));
//        reportLibResultEntity.setTotalCount(reportLibResultEntity.getData().size());
//        reportLibResultEntity.setRowList(reportLibResultEntity.getData());
//
//        return reportLibResultEntity;
//    }
//
//    private ReportLibResultEntity processFlighData(ReportLibFilterBO reportLibFilterBO) {
//        ReportLibResultEntity reportLibResultEntity = new ReportLibResultEntity();
//        if (reportLibFilterBO == null) {
//            return reportLibResultEntity;
//        }
//        if (StringUtils.equalsIgnoreCase(reportLibFilterBO.getSwitchType(), "download")) {
//            FlightOrderDetailHiveService flightOrderDetailHiveService = SpringUtil.getBean(FlightOrderDetailHiveService.class);
//            reportLibResultEntity = flightOrderDetailHiveService.queryLimit(reportLibFilterBO);
//        } else {
//            FlightOrderDetailService flightOrderDetailService = SpringUtil.getBean(FlightOrderDetailService.class);
//            reportLibResultEntity = flightOrderDetailService.queryLimit(reportLibFilterBO);
//        }
//        FlightOrderDetailEnum[] flightOrderDetailEnums = FlightOrderDetailEnum.values();
//        convertData(reportLibFilterBO, flightOrderDetailEnums, reportLibResultEntity);
//        return reportLibResultEntity;
//    }
//
//    private ReportLibResultEntity processHotelData(ReportLibFilterBO reportLibFilterBO) {
//        ReportLibResultEntity reportLibResultEntity = new ReportLibResultEntity();
//        if (reportLibFilterBO == null) {
//            return reportLibResultEntity;
//        }
//        if (StringUtils.equalsIgnoreCase(reportLibFilterBO.getSwitchType(), "download")) {
//            HotelOrderDetailHiveService hotelOrderDetailHiveService = SpringUtil.getBean(HotelOrderDetailHiveService.class);
//            reportLibResultEntity = hotelOrderDetailHiveService.queryLimit(reportLibFilterBO);
//        } else {
//            HotelOrderDetailService hotelOrderDetailService = SpringUtil.getBean(HotelOrderDetailService.class);
//            reportLibResultEntity = hotelOrderDetailService.queryLimit(reportLibFilterBO);
//        }
//        HotelOrderDetailEnum[] hotelOrderDetailEnums = HotelOrderDetailEnum.values();
//        convertData(reportLibFilterBO, hotelOrderDetailEnums, reportLibResultEntity);
//        return reportLibResultEntity;
//    }
//
//    private ReportLibResultEntity processTrainData(ReportLibFilterBO reportLibFilterBO) {
//        ReportLibResultEntity reportLibResultEntity = new ReportLibResultEntity();
//        if (reportLibFilterBO == null) {
//            return reportLibResultEntity;
//        }
//        if (StringUtils.equalsIgnoreCase(reportLibFilterBO.getSwitchType(), "download")) {
//            TrainOrderDetailHiveService trainOrderDetailHiveService = SpringUtil.getBean(TrainOrderDetailHiveService.class);
//            reportLibResultEntity = trainOrderDetailHiveService.queryLimit(reportLibFilterBO);
//        } else {
//            TrainOrderDetailService trainOrderDetailService = SpringUtil.getBean(TrainOrderDetailService.class);
//
//            reportLibResultEntity = trainOrderDetailService.queryLimit(reportLibFilterBO);
//        }
//        TrainOrderDetailEnum[] trainOrderDetailEnums = TrainOrderDetailEnum.values();
//        convertData(reportLibFilterBO, trainOrderDetailEnums, reportLibResultEntity);
//        return reportLibResultEntity;
//    }
//
//    private ReportLibResultEntity processCarData(ReportLibFilterBO reportLibFilterBO) {
//        ReportLibResultEntity reportLibResultEntity = new ReportLibResultEntity();
//        if (reportLibFilterBO == null) {
//            return reportLibResultEntity;
//        }
//        if (StringUtils.equalsIgnoreCase(reportLibFilterBO.getSwitchType(), "download")) {
//            CarOrderDetailHiveService carOrderDetailHiveService = SpringUtil.getBean(CarOrderDetailHiveService.class);
//            reportLibResultEntity = carOrderDetailHiveService.queryLimit(reportLibFilterBO);
//        } else {
//            CarOrderDetailService carOrderDetailService = SpringUtil.getBean(CarOrderDetailService.class);
//            reportLibResultEntity = carOrderDetailService.queryLimit(reportLibFilterBO);
//        }
//        CarOrderDetailEnum[] carOrderDetailEnums = CarOrderDetailEnum.values();
//        convertData(reportLibFilterBO, carOrderDetailEnums, reportLibResultEntity);
//        return reportLibResultEntity;
//    }
//
//    private ReportLibResultEntity processFlightAbnormalData(ReportLibFilterBO reportLibFilterBO) {
//        ReportLibResultEntity reportLibResultEntity = new ReportLibResultEntity();
//        if (reportLibFilterBO == null) {
//            return reportLibResultEntity;
//        }
//        if (StringUtils.equalsIgnoreCase(reportLibFilterBO.getSwitchType(), "download")) {
//            if (StringUtils.equalsIgnoreCase(QConfigUtils.getEnv(), ENV_DEV)) {
//                FlightOrderDetailService flightOrderDetailService = SpringUtil.getBean(FlightOrderDetailService.class);
//                reportLibFilterBO.setLimit(1000);
//                reportLibResultEntity = flightOrderDetailService.queryLimit(reportLibFilterBO);
//            } else {
//                FlightOrderDetailHiveService flightOrderDetailHiveService = SpringUtil.getBean(FlightOrderDetailHiveService.class);
//                reportLibResultEntity = flightOrderDetailHiveService.queryLimit(reportLibFilterBO);
//            }
//        } else {
//            FlightOrderDetailService flightOrderDetailService = SpringUtil.getBean(FlightOrderDetailService.class);
//            reportLibResultEntity = flightOrderDetailService.queryLimit(reportLibFilterBO);
//        }
//        AbnormalFlightOrderDetailEnum[] abnormalFlightOrderDetailEnums = AbnormalFlightOrderDetailEnum.values();
//        convertData(reportLibFilterBO, abnormalFlightOrderDetailEnums, reportLibResultEntity);
//        return reportLibResultEntity;
//    }
//
//    private ReportLibResultEntity processFlighRefundData(ReportLibFilterBO reportLibFilterBO) {
//        ReportLibResultEntity reportLibResultEntity = new ReportLibResultEntity();
//        if (reportLibFilterBO == null) {
//            return reportLibResultEntity;
//        }
//        if (StringUtils.equalsIgnoreCase(reportLibFilterBO.getSwitchType(), "download")) {
//            if (StringUtils.equalsIgnoreCase(QConfigUtils.getEnv(), ENV_DEV)) {
//                FlightOrderDetailService flightOrderDetailService = SpringUtil.getBean(FlightOrderDetailService.class);
//                reportLibFilterBO.setLimit(1000);
//                reportLibResultEntity = flightOrderDetailService.queryLimit(reportLibFilterBO);
//            } else {
//                FlightOrderDetailHiveService flightOrderDetailHiveService = SpringUtil.getBean(FlightOrderDetailHiveService.class);
//                reportLibResultEntity = flightOrderDetailHiveService.queryLimit(reportLibFilterBO);
//            }
//        } else {
//            FlightOrderDetailService flightOrderDetailService = SpringUtil.getBean(FlightOrderDetailService.class);
//            reportLibResultEntity = flightOrderDetailService.queryLimit(reportLibFilterBO);
//        }
//        FlightRefundOrderDetailEnum[] flightRefundOrderDetailEnums = FlightRefundOrderDetailEnum.values();
//        convertData(reportLibFilterBO, flightRefundOrderDetailEnums, reportLibResultEntity);
//        return reportLibResultEntity;
//    }
//
//    private ReportLibResultEntity processFlighChangeData(ReportLibFilterBO reportLibFilterBO) {
//        ReportLibResultEntity reportLibResultEntity = new ReportLibResultEntity();
//        if (reportLibFilterBO == null) {
//            return reportLibResultEntity;
//        }
//        if (StringUtils.equalsIgnoreCase(reportLibFilterBO.getSwitchType(), "download")) {
//            if (StringUtils.equalsIgnoreCase(QConfigUtils.getEnv(), ENV_DEV)) {
//                FlightOrderDetailService flightOrderDetailService = SpringUtil.getBean(FlightOrderDetailService.class);
//                reportLibFilterBO.setLimit(1000);
//                reportLibResultEntity = flightOrderDetailService.queryLimit(reportLibFilterBO);
//            } else {
//                FlightOrderDetailHiveService flightOrderDetailHiveService = SpringUtil.getBean(FlightOrderDetailHiveService.class);
//                reportLibResultEntity = flightOrderDetailHiveService.queryLimit(reportLibFilterBO);
//            }
//        } else {
//            FlightOrderDetailService flightOrderDetailService = SpringUtil.getBean(FlightOrderDetailService.class);
//            reportLibResultEntity = flightOrderDetailService.queryLimit(reportLibFilterBO);
//        }
//        FlightChangeOrderDetailEnum[] flightChangeOrderDetailEnums = FlightChangeOrderDetailEnum.values();
//        convertData(reportLibFilterBO, flightChangeOrderDetailEnums, reportLibResultEntity);
//        return reportLibResultEntity;
//    }
//
//    private ReportLibResultEntity processFlighUnuseData(ReportLibFilterBO reportLibFilterBO) {
//        ReportLibResultEntity reportLibResultEntity = new ReportLibResultEntity();
//        if (reportLibFilterBO == null) {
//            return reportLibResultEntity;
//        }
//        if (StringUtils.equalsIgnoreCase(reportLibFilterBO.getSwitchType(), "download")) {
//            if (StringUtils.equalsIgnoreCase(QConfigUtils.getEnv(), ENV_DEV)) {
//                FlightOrderDetailService flightOrderDetailService = SpringUtil.getBean(FlightOrderDetailService.class);
//                reportLibFilterBO.setLimit(1000);
//                reportLibResultEntity = flightOrderDetailService.queryLimit(reportLibFilterBO);
//            } else {
//                FlightOrderDetailHiveService flightOrderDetailHiveService = SpringUtil.getBean(FlightOrderDetailHiveService.class);
//                reportLibResultEntity = flightOrderDetailHiveService.queryLimit(reportLibFilterBO);
//            }
//        } else {
//            FlightOrderDetailService flightOrderDetailService = SpringUtil.getBean(FlightOrderDetailService.class);
//            reportLibResultEntity = flightOrderDetailService.queryLimit(reportLibFilterBO);
//        }
//        UnUseFlightOrderDetailEnum[] unUseFlightOrderDetailEnums = UnUseFlightOrderDetailEnum.values();
//        convertData(reportLibFilterBO, unUseFlightOrderDetailEnums, reportLibResultEntity);
//        return reportLibResultEntity;
//    }
//
//    public boolean validCorpFilter(ReportLibFilterBO reportLibFilterBO) {
////        UserPermissionsBo userPermissionsBo = userPermissionService.queryUserPermissions(reportLibFilterBO.getUid());
////        if (userPermissionsBo == null || StringUtils.isEmpty(userPermissionsBo.getUid())) {
////            log.warn(PREFIX + "validCorpFilter", "no permit");
////            return false;
////        }
////        if (userPermissionsBo.getManagerType() == ManagerTypeEnum.SUPERADMIN) {
////            log.warn(PREFIX + "validCorpFilter", "superadmin no permit");
////            return false;
////        }
//        //获取主账户权限集合
////        Set<Long> permissionMainAccountIds = new HashSet<>();
////        if (userPermissionsBo.getCorpAccountsMap() != null) {
////            userPermissionsBo.getCorpAccountsMap().forEach((k, v) -> {
////                List<MainAccounts> mainAccountsList = v.getAccounts();
////                if (mainAccountsList != null) {
////                    List<Long> tempAccountIds = mainAccountsList.stream().map(x -> Long.parseLong(x.getId())).collect(Collectors.toList());
////                    permissionMainAccountIds.addAll(tempAccountIds);
////                }
////            });
////        }
////        if (excludes.contains(userPermissionsBo.getManagerType()) || excludeManagement(userPermissionsBo.getUid())) {
////            reportLibFilterBO.setCorpIds(userPermissionsBo.getCorpIds());
////        }
////        //设置默认查询条件
////        if (userPermissionsBo.getManagerType() == ManagerTypeEnum.GROUPMANAGER || userPermissionsBo.getManagerType() == ManagerTypeEnum.CORPMANAGER) {
////            //集团管理员
////            //公司管理员
////            reportLibFilterBO.setCorpIds(userPermissionsBo.getCorpIds());
////        } else if (userPermissionsBo.getManagerType() == ManagerTypeEnum.COMMONUSER) {
////            //普通用户，如果普通用户的mainAccountIds为空，则说明拥有公司下的所有主账户权限，则只要根据corpIds进行查询即可
////            reportLibFilterBO.setCorpIds(userPermissionsBo.getCorpIds());
////            if (permissionMainAccountIds.size() > 0) {
////                //主账户集合不为空，说明对主账户做了权控
////                reportLibFilterBO.setAccountIds(new ArrayList<>(permissionMainAccountIds));
////            }
////        }
//        //用户的查询条件全不在权限范围内，直接抛异常
//        if (StringUtils.isEmpty(reportLibFilterBO.getGroupId()) && CollectionUtils.isEmpty(reportLibFilterBO.getCorpIds())
//                && CollectionUtils.isEmpty(reportLibFilterBO.getAccountIds())) {
//            throw new RuntimeException("User Try To Query No Permission Data");
//        }
//        //如果用户搜索带入了过滤条件，则根据过滤条件来进行查询
//        List<String> searchCorpIdList = null;
//        Map<String, List<String>> searchAccountIdList = null;
//        if (MapUtils.isNotEmpty(reportLibFilterBO.getFilterList())) {
//            searchCorpIdList = reportLibFilterBO.getFilterList().get("corp") == null ? null : new ArrayList<>((List<String>) reportLibFilterBO.getFilterList().get("corp"));
//            if (reportLibFilterBO.getFilterList().get("account") instanceof Map) {
//                searchAccountIdList = MapUtils.isEmpty((Map) reportLibFilterBO.getFilterList().get("account")) ? null : (Map<String, List<String>>) reportLibFilterBO.getFilterList().get("account");
//            }
//            //城市级别
//            reportLibFilterBO.setCityLevel(reportLibFilterBO.getFilterList().get("cityLevel") == null ? null : new ArrayList<>((List<String>) reportLibFilterBO.getFilterList().get("cityLevel")));
//            //酒店星级
//            reportLibFilterBO.setStar((reportLibFilterBO.getFilterList().get("star") == null ? null : (Integer) reportLibFilterBO.getFilterList().get("star")));
//            //酒店类别
//            reportLibFilterBO.setHotelType((reportLibFilterBO.getFilterList().get("hotelType") == null ? null : (String) reportLibFilterBO.getFilterList().get("hotelType")));
//            String bizType = Objects.toString(reportLibFilterBO.getFilterList().get("bizType"), null);
//            String queryType = Objects.toString(reportLibFilterBO.getFilterList().get("queryType"), null);
//            String startTime = Objects.toString(reportLibFilterBO.getFilterList().get("startTime"), null);
//            String endTime = Objects.toString(reportLibFilterBO.getFilterList().get("endTime"), null);
//            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotBlank(startTime)) {
//                long start = Long.parseLong(startTime);
//                if (start > 0) {
//                    reportLibFilterBO.setStartTime(start);
//                }
//            }
//            if (StringUtils.isNotEmpty(endTime) && StringUtils.isNotBlank(endTime)) {
//                long end = Long.parseLong(endTime);
//                if (end > 0) {
//                    reportLibFilterBO.setEndTime(end);
//                }
//            }
//            if (StringUtils.isNotEmpty(bizType) && StringUtils.isNotBlank(bizType)) {
//                reportLibFilterBO.setBizType(bizType.trim());
//            }
//            if (StringUtils.isNotEmpty(queryType) && StringUtils.isNotBlank(queryType)) {
//                reportLibFilterBO.setBizType(queryType.trim());
//            }
//        }
//
//        //公司
//        if (CollectionUtils.isNotEmpty(searchCorpIdList)) {
//            //移除不在权限范围内的CorpId
//            Iterator<String> corpIterator = searchCorpIdList.iterator();
////            while (corpIterator.hasNext()) {
////                String corpId = corpIterator.next();
////                if (userPermissionsBo.getCorpIds().stream().noneMatch(i -> StringUtils.equalsIgnoreCase(corpId, i))) {
////                    corpIterator.remove();
////                }
////            }
//            reportLibFilterBO.setCorpIds(searchCorpIdList);
//        }
//        //主账户
//        if (MapUtils.isNotEmpty(searchAccountIdList)) {
//            List<Long> accountIds = new ArrayList<>();
//            for (Map.Entry entry : searchAccountIdList.entrySet()) {
//                List<String> temp = (List<String>) entry.getValue();
//                for (String str : temp) {
//                    Long accountId = Long.parseLong(str);
//                    accountIds.add(new Long(accountId));
//                }
//            }
//            if (CollectionUtils.isNotEmpty(accountIds)) {
//                reportLibFilterBO.setAccountIds(accountIds);
//            }
//        }
//
//        //用户的查询条件全不在权限范围内，直接抛异常
//        if (StringUtils.isEmpty(reportLibFilterBO.getGroupId()) && CollectionUtils.isEmpty(reportLibFilterBO.getCorpIds())
//                && CollectionUtils.isEmpty(reportLibFilterBO.getAccountIds())) {
//            throw new RuntimeException("User Try To Query No Permission Data");
//        }
//
//        List<CostCenterAndDept> costcenters = buildCostCenterAndDept(reportLibFilterBO.getFilterList(), Lists.newArrayList(), "costCenter", 6);
//        reportLibFilterBO.setCostcenters(costcenters);
//        List<CostCenterAndDept> depts = buildCostCenterAndDept(reportLibFilterBO.getFilterList(), Lists.newArrayList(), "dept", 10);
//        reportLibFilterBO.setDepts(depts);
//        return true;
//    }
//
//
//
//    public boolean overMaxInvervalTime(String conditions, int invervalMonths) {
//        int interval = getInvervalTime(conditions);
//        return interval > invervalMonths;
//    }
//
//
//
//    private boolean overMaxMonths(long start, long end) {
//        int invervalMonths = QConfigUtils.getMaxInvervalMonth();
//        int diff = DateUtil.getIntervalMonths(start, end);
//        return diff > invervalMonths;
//    }
//
//
//
//    public List<String> processData(GeneralAndMonthDetailBONew generalAndMonthDetailBONew, MonQurTypeEnum monQurTypeEnum, List<Map<String, String>> list, GeneralAndMonthDetailBONew total) {
//        List<String> result = new ArrayList<>();
//        for (Map<String, String> map : list) {
//            boolean flag = false;
//            int month = Integer.valueOf(map.get("month"));
//            int year = Integer.valueOf(map.get("year"));
//            if (StringUtils.equalsIgnoreCase(monQurTypeEnum.toString(), MonQurTypeEnum.M.toString())) {
//                if (generalAndMonthDetailBONew != null && CollectionUtils.isNotEmpty(generalAndMonthDetailBONew.getMonthDetailBOList())) {
//                    List<MonthDetailBONew> listMonth = generalAndMonthDetailBONew.getMonthDetailBOList();
//                    if (CollectionUtils.isNotEmpty(listMonth)) {
//                        for (MonthDetailBONew monthDetailBONew : listMonth) {
//                            if (monthDetailBONew.getMonth() == month && monthDetailBONew.getYear() == year) {
//                                if (total != null) {
//                                    total(total, monthDetailBONew, monQurTypeEnum);
//                                }
//                                result.add(monthDetailBONew.getAmount());
//                                result.add(monthDetailBONew.getMonthOnMonth());
//                                result.add(monthDetailBONew.getYearOnYear());
//                                flag = true;
//                                break;
//                            }
//                        }
//                    }
//                }
//            }
//            if (StringUtils.equalsIgnoreCase(monQurTypeEnum.toString(), MonQurTypeEnum.Q.toString())) {
//                if (generalAndMonthDetailBONew != null && CollectionUtils.isNotEmpty(generalAndMonthDetailBONew.getQuarterDetailBOList())) {
//                    List<MonthDetailBONew> listQuarter = generalAndMonthDetailBONew.getQuarterDetailBOList();
//                    if (CollectionUtils.isNotEmpty(listQuarter)) {
//                        for (MonthDetailBONew monthDetailBONew : listQuarter) {
//                            if (monthDetailBONew.getMonth() == month && monthDetailBONew.getYear() == year) {
//                                if (total != null) {
//                                    total(total, monthDetailBONew, monQurTypeEnum);
//                                }
//                                result.add(monthDetailBONew.getAmount());
//                                result.add(monthDetailBONew.getMonthOnMonth());
//                                result.add(monthDetailBONew.getYearOnYear());
//                                flag = true;
//                                break;
//                            }
//                        }
//                    }
//                }
//            }
//            if (StringUtils.equalsIgnoreCase(monQurTypeEnum.toString(), MonQurTypeEnum.H.toString())) {
//                if (generalAndMonthDetailBONew != null && CollectionUtils.isNotEmpty(generalAndMonthDetailBONew.getHalfDetailBOList())) {
//                    List<MonthDetailBONew> listHalf = generalAndMonthDetailBONew.getHalfDetailBOList();
//                    if (CollectionUtils.isNotEmpty(listHalf)) {
//                        for (MonthDetailBONew monthDetailBONew : listHalf) {
//                            if (monthDetailBONew.getMonth() == month && monthDetailBONew.getYear() == year) {
//                                if (total != null) {
//                                    total(total, monthDetailBONew, monQurTypeEnum);
//                                }
//                                result.add(monthDetailBONew.getAmount());
//                                result.add(monthDetailBONew.getMonthOnMonth());
//                                result.add(monthDetailBONew.getYearOnYear());
//                                flag = true;
//                                break;
//                            }
//                        }
//                    }
//                }
//            }
//            if (!flag) {
//                result.add(GlobalConst.STRING_ZERO);
//                result.add(GlobalConst.STRING_ZERO);
//                result.add(GlobalConst.STRING_ZERO);
//            }
//        }
//        if (generalAndMonthDetailBONew != null) {
//            GeneralDetailBONew generalDetailBONew = generalAndMonthDetailBONew.getGeneralDetailBONew();
//            result.add(generalDetailBONew == null ? GlobalConst.STRING_ZERO : (StringUtils.isEmpty(generalDetailBONew.getAmount()) ? GlobalConst.STRING_ZERO : generalDetailBONew.getAmount()));
//        } else {
//            result.add(GlobalConst.STRING_ZERO);
//        }
//
//        return result;
//    }
////
////*
////     * 汇总
////     *
////     * @param totalGeneralAndMonthDetailBONew
////     * @param monthDetailBONew
////     * @param monQurTypeEnum
//
//
//    private void total(GeneralAndMonthDetailBONew totalGeneralAndMonthDetailBONew, MonthDetailBONew monthDetailBONew, MonQurTypeEnum monQurTypeEnum) {
//        boolean flag = true;
//        MonthDetailBONew target = BeanCopyUtil.copy(monthDetailBONew, new MonthDetailBONew());
//        if (totalGeneralAndMonthDetailBONew == null) {
//            totalGeneralAndMonthDetailBONew = new GeneralAndMonthDetailBONew();
//        }
//        if (totalGeneralAndMonthDetailBONew.getGeneralDetailBONew() == null) {
//            totalGeneralAndMonthDetailBONew.setGeneralDetailBONew(new GeneralDetailBONew());
//        }
//        if (StringUtils.equalsIgnoreCase(monQurTypeEnum.toString(), MonQurTypeEnum.M.toString())) {
//            if (CollectionUtils.isEmpty(totalGeneralAndMonthDetailBONew.getMonthDetailBOList())) {
//                totalGeneralAndMonthDetailBONew.setMonthDetailBOList(new ArrayList<>(Arrays.asList(target)));
//            } else {
//                for (MonthDetailBONew item : totalGeneralAndMonthDetailBONew.getMonthDetailBOList()) {
//                    flag = processDetail(item, target);
//                }
//                if (flag) {
//                    totalGeneralAndMonthDetailBONew.getMonthDetailBOList().add(target);
//                }
//            }
//        }
//        if (StringUtils.equalsIgnoreCase(monQurTypeEnum.toString(), MonQurTypeEnum.Q.toString())) {
//            if (CollectionUtils.isEmpty(totalGeneralAndMonthDetailBONew.getQuarterDetailBOList())) {
//                totalGeneralAndMonthDetailBONew.setQuarterDetailBOList(new ArrayList<>(Arrays.asList(target)));
//            } else {
//                for (MonthDetailBONew item : totalGeneralAndMonthDetailBONew.getQuarterDetailBOList()) {
//                    flag = processDetail(item, target);
//                }
//                if (flag) {
//                    totalGeneralAndMonthDetailBONew.getQuarterDetailBOList().add(target);
//                }
//            }
//        }
//        if (StringUtils.equalsIgnoreCase(monQurTypeEnum.toString(), MonQurTypeEnum.H.toString())) {
//            if (CollectionUtils.isEmpty(totalGeneralAndMonthDetailBONew.getHalfDetailBOList())) {
//                totalGeneralAndMonthDetailBONew.setHalfDetailBOList(new ArrayList<>(Arrays.asList(target)));
//            } else {
//                for (MonthDetailBONew item : totalGeneralAndMonthDetailBONew.getHalfDetailBOList()) {
//                    flag = processDetail(item, target);
//                }
//                if (flag) {
//                    totalGeneralAndMonthDetailBONew.getHalfDetailBOList().add(target);
//                }
//            }
//        }
//        if (StringUtils.equalsIgnoreCase(monQurTypeEnum.toString(), MonQurTypeEnum.Y.toString())) {
//            if (CollectionUtils.isEmpty(totalGeneralAndMonthDetailBONew.getHalfDetailBOList())) {
//                totalGeneralAndMonthDetailBONew.setYearDetailBOList(new ArrayList<>(Arrays.asList(target)));
//            } else {
//                for (MonthDetailBONew item : totalGeneralAndMonthDetailBONew.getYearDetailBOList()) {
//                    flag = processDetail(item, target);
//                }
//                if (flag) {
//                    totalGeneralAndMonthDetailBONew.getYearDetailBOList().add(target);
//                }
//            }
//        }
//        GeneralDetailBONew generalDetailBONew = totalGeneralAndMonthDetailBONew.getGeneralDetailBONew();
//        String originAmount = StringUtils.isEmpty(totalGeneralAndMonthDetailBONew.getGeneralDetailBONew().getAmount()) ? GlobalConst.STRING_ZERO : totalGeneralAndMonthDetailBONew.getGeneralDetailBONew().getAmount();
//        String originMonthAmount = StringUtils.isEmpty(totalGeneralAndMonthDetailBONew.getGeneralDetailBONew().getMonthOnMonthAmount()) ? GlobalConst.STRING_ZERO : totalGeneralAndMonthDetailBONew.getGeneralDetailBONew().getMonthOnMonthAmount();
//        String originYearAmount = StringUtils.isEmpty(totalGeneralAndMonthDetailBONew.getGeneralDetailBONew().getYearOnYearAmount()) ? GlobalConst.STRING_ZERO : totalGeneralAndMonthDetailBONew.getGeneralDetailBONew().getMonthOnMonthAmount();
//
//        generalDetailBONew.setAmount(DigitBaseUtils.formatDigit(DigitBaseUtils.add(DigitBaseUtils.convertStringToDouble(originAmount), DigitBaseUtils.convertStringToDouble(target.getAmount())).doubleValue()));
//        generalDetailBONew.setMonthOnMonthAmount(DigitBaseUtils.formatDigit(DigitBaseUtils.add(DigitBaseUtils.convertStringToDouble(originMonthAmount), DigitBaseUtils.convertStringToDouble(target.getMonthOnMonthAmount())).doubleValue()));
//        generalDetailBONew.setYearOnYearAmount(DigitBaseUtils.formatDigit(DigitBaseUtils.add(DigitBaseUtils.convertStringToDouble(originYearAmount), DigitBaseUtils.convertStringToDouble(target.getYearOnYearAmount())).doubleValue()));
//        generalDetailBONew.setMonthOnMonth(DigitBaseUtils.parsePercent(DigitBaseUtils.divide((DigitBaseUtils.convertStringToDouble(generalDetailBONew.getAmount()) - DigitBaseUtils.convertStringToDouble(generalDetailBONew.getMonthOnMonthAmount())), DigitBaseUtils.convertStringToDouble(generalDetailBONew.getMonthOnMonthAmount())).doubleValue()));
//        generalDetailBONew.setYearOnYear(DigitBaseUtils.parsePercent(DigitBaseUtils.divide((DigitBaseUtils.convertStringToDouble(generalDetailBONew.getAmount()) - DigitBaseUtils.convertStringToDouble(generalDetailBONew.getYearOnYearAmount())), DigitBaseUtils.convertStringToDouble(generalDetailBONew.getYearOnYearAmount())).doubleValue()));
//    }
//
//    private boolean processDetail(MonthDetailBONew item, MonthDetailBONew target) {
//        if (target.getMonth() != null && item.getMonth() != null && target.getYear() != null && item.getYear() != null) {
//            if (target.getMonth().intValue() == item.getMonth().intValue() && target.getYear().intValue() == item.getYear().intValue()) {
//                item.setAmount(DigitBaseUtils.formatDigit(DigitBaseUtils.add(DigitBaseUtils.convertStringToDouble(item.getAmount()), DigitBaseUtils.convertStringToDouble(target.getAmount())).doubleValue()));
//                item.setMonthOnMonthAmount(DigitBaseUtils.formatDigit(DigitBaseUtils.add(DigitBaseUtils.convertStringToDouble(item.getMonthOnMonthAmount()), DigitBaseUtils.convertStringToDouble(target.getMonthOnMonthAmount())).doubleValue()));
//                item.setYearOnYearAmount(DigitBaseUtils.formatDigit(DigitBaseUtils.add(DigitBaseUtils.convertStringToDouble(item.getYearOnYearAmount()), DigitBaseUtils.convertStringToDouble(target.getYearOnYearAmount())).doubleValue()));
//                item.setMonthOnMonth(DigitBaseUtils.parsePercent(DigitBaseUtils.divide(DigitBaseUtils.convertStringToDouble(item.getMonthOnMonthAmount()), DigitBaseUtils.convertStringToDouble(item.getAmount())).doubleValue()));
//                item.setYearOnYear(DigitBaseUtils.parsePercent(DigitBaseUtils.divide(DigitBaseUtils.convertStringToDouble(item.getYearOnYearAmount()), DigitBaseUtils.convertStringToDouble(item.getAmount())).doubleValue()));
//                return false;
//            }
//        }
//        return true;
//    }
//
////*
////     * @param reportLibFilterBO
////     * @return
//
//
//    private List<List<String>> queryData(ReportLibFilterBO reportLibFilterBO) {
//        List<List<String>> dataList = new ArrayList<>();
//        try {
//            TimeSum sum = onlineReportService.query(reportLibFilterBO);
//            if (null == sum) {
//                log.warn(PREFIX, "query data is null", logTagContext.getMap());
//                return dataList;
//            }
//            dataList = doSumData(sum, reportLibFilterBO.getBizTypes());
//        } catch (Exception e) {
//            log.error(PREFIX, e, logTagContext.getMap());
//        }
//        return dataList;
//    }
//
////*
////     * @param reportLibFilterBO
////     * @return
//
//
//    private List<Object> queryDeptData(ReportLibFilterBO reportLibFilterBO) {
//        List<Object> result = new ArrayList<>();
//        try {
//            result = onlineReportService.queryDept(reportLibFilterBO);
//            if (null == result) {
//                log.error(PREFIX, "query dept data is null", logTagContext.getMap());
//                return new ArrayList<>();
//            }
//        } catch (Exception e) {
//            log.error(PREFIX, e, logTagContext.getMap());
//        }
//        return result;
//    }
//
////*
////     * @param reportLibFilterBO
////     * @return
//
//
//    private List<List<String>> queryDeptExcelData(ReportLibFilterBO reportLibFilterBO) {
//        List<List<String>> dataList = new ArrayList<>();
//        try {
//            dataList = onlineReportService.queryDeptExcel(reportLibFilterBO);
//            if (null == dataList) {
//                log.warn(PREFIX, "query data is null", logTagContext.getMap());
//                return new ArrayList<>();
//            }
//        } catch (Exception e) {
//            log.error(PREFIX, e, logTagContext.getMap());
//        }
//        return dataList;
//    }
//
//    private List<List<String>> doSumData(TimeSum sum, List<String> bizTypes) {
//
//        List<List<String>> result = new ArrayList<>();
//
//        List<String> tiemTitle = Lists.newArrayList("");
//        List<String> defineTitle = Lists.newArrayList("");
//        List<String> iFligthRow = Lists.newArrayList(SharkUtils.get("Index.interair", language));
//        List<String> nFligthRow = Lists.newArrayList(SharkUtils.get("Index.domair", language));
//        List<String> aFligthRow = Lists.newArrayList(SharkUtils.get("Exceltopname.subtotalflight", language));
//        List<String> mHotelRow = Lists.newArrayList(SharkUtils.get("Supplier.memberhotel", language));
//        List<String> cHotelRow = Lists.newArrayList(SharkUtils.get("Supplier.pacthotel", language));
//        List<String> aHotelRow = Lists.newArrayList(SharkUtils.get("Exceltopname.subtotalhotel", language));
//        List<String> trainRow = Lists.newArrayList(SharkUtils.get("Index.train", language));
//        List<String> nCarRow = Lists.newArrayList(SharkUtils.get("Index.domairportpick", language));
//        List<String> iCarRow = Lists.newArrayList(SharkUtils.get("Index.intetrairportpick", language));
//        List<String> bCarRow = Lists.newArrayList(SharkUtils.get("Index.charcar", language));
//        List<String> rCarRow = Lists.newArrayList(SharkUtils.get("Index.rentcar", language));
//        List<String> oCarRow = Lists.newArrayList(SharkUtils.get("Index.tax", language));
//        List<String> aCarRow = Lists.newArrayList(SharkUtils.get("Exceltopname.subtotalcar", language));
//        List<String> allRow = Lists.newArrayList(SharkUtils.get("Index.sum", language));
//        result.add(tiemTitle);
//        result.add(defineTitle);
//        boolean andF = true;
//        boolean andH = true;
//        boolean andT = true;
//        boolean andC = true;
//        for (TimeSumEntry entry : sum.getTimeSumEntryList()) {
//            tiemTitle.add(entry.getTimeTitle());
//            tiemTitle.add(entry.getTimeTitle());
//            tiemTitle.add(entry.getTimeTitle());
//            defineTitle.add(SharkUtils.get("Index.costmoney", language));
//            defineTitle.add(SharkUtils.get("Index.radio", language));
//            defineTitle.add(SharkUtils.get("Index.rad", language));
//            if (bizTypes.contains(BizTypeEnums.F.toString())) {
//                iFligthRow.add(String.valueOf(entry.getIFligth().getPriceAmount()));
//                iFligthRow.add(entry.getIFligth().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                iFligthRow.add(entry.getIFligth().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                nFligthRow.add(String.valueOf(entry.getNFligth().getPriceAmount()));
//                nFligthRow.add(entry.getNFligth().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                nFligthRow.add(entry.getNFligth().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                aFligthRow.add(String.valueOf(entry.getAFligth().getPriceAmount()));
//                aFligthRow.add(entry.getAFligth().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                aFligthRow.add(entry.getAFligth().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                if (andF) {
//                    result.add(iFligthRow);
//                    result.add(nFligthRow);
//                    result.add(aFligthRow);
//                    andF = false;
//                }
//            }
//            if (bizTypes.contains(BizTypeEnums.H.toString())) {
//                mHotelRow.add(String.valueOf(entry.getMHotel().getPriceAmount()));
//                mHotelRow.add(entry.getMHotel().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                mHotelRow.add(entry.getMHotel().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                cHotelRow.add(String.valueOf(entry.getCHotel().getPriceAmount()));
//                cHotelRow.add(entry.getCHotel().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                cHotelRow.add(entry.getCHotel().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                aHotelRow.add(String.valueOf(entry.getAHotle().getPriceAmount()));
//                aHotelRow.add(entry.getAHotle().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                aHotelRow.add(entry.getAHotle().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                if (andH) {
//                    result.add(mHotelRow);
//                    result.add(cHotelRow);
//                    result.add(aHotelRow);
//                    andH = false;
//                }
//            }
//            if (bizTypes.contains(BizTypeEnums.C.toString())) {
//                nCarRow.add(String.valueOf(entry.getNCar().getPriceAmount()));
//                nCarRow.add(entry.getNCar().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                nCarRow.add(entry.getNCar().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                iCarRow.add(String.valueOf(entry.getICar().getPriceAmount()));
//                iCarRow.add(entry.getICar().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                iCarRow.add(entry.getICar().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                bCarRow.add(String.valueOf(entry.getBCar().getPriceAmount()));
//                bCarRow.add(entry.getBCar().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                bCarRow.add(entry.getBCar().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                rCarRow.add(String.valueOf(entry.getRCar().getPriceAmount()));
//                rCarRow.add(entry.getRCar().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                rCarRow.add(entry.getRCar().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                oCarRow.add(String.valueOf(entry.getOCar().getPriceAmount()));
//                oCarRow.add(entry.getOCar().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                oCarRow.add(entry.getOCar().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                aCarRow.add(String.valueOf(entry.getACar().getPriceAmount()));
//                aCarRow.add(entry.getACar().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                aCarRow.add(entry.getACar().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                if (andC) {
//                    result.add(nCarRow);
//                    result.add(iCarRow);
//                    result.add(bCarRow);
//                    result.add(rCarRow);
//                    result.add(oCarRow);
//                    result.add(aCarRow);
//                    andC = false;
//                }
//            }
//            if (bizTypes.contains(BizTypeEnums.T.toString())) {
//                trainRow.add(String.valueOf(entry.getTrain().getPriceAmount()));
//                trainRow.add(entry.getTrain().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                trainRow.add(entry.getTrain().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//                if (andT) {
//                    result.add(trainRow);
//                    andT = false;
//                }
//            }
//            //总计
//            allRow.add(String.valueOf(entry.getAll().getPriceAmount()));
//            allRow.add(entry.getAll().getQoq().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//            allRow.add(entry.getAll().getYoy().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
//        }
//        //消費總金額
//        iFligthRow.add(String.valueOf(sum.getIFligthSum()));
//        nFligthRow.add(String.valueOf(sum.getNFligthSum()));
//        aFligthRow.add(String.valueOf(sum.getAFligthSum()));
//        mHotelRow.add(String.valueOf(sum.getMHotelSum()));
//        cHotelRow.add(String.valueOf(sum.getCHotelSum()));
//        aHotelRow.add(String.valueOf(sum.getAHotleSum()));
//        trainRow.add(String.valueOf(sum.getTrainSum()));
//        nCarRow.add(String.valueOf(sum.getNCarSum()));
//        iCarRow.add(String.valueOf(sum.getICarSum()));
//        bCarRow.add(String.valueOf(sum.getBCarSum()));
//        rCarRow.add(String.valueOf(sum.getRCarSum()));
//        oCarRow.add(String.valueOf(sum.getOCarSum()));
//        aCarRow.add(String.valueOf(sum.getACarSum()));
//        allRow.add(String.valueOf(sum.getAllSum()));
//        result.add(allRow);
//        tiemTitle.add("");
//        defineTitle.add(SharkUtils.get("Supplier.totalcostamount", language));
//        return result;
//    }
//
//    private List getCustomColumns(ReportLibFilterBO reportLibFilterBO) {
//        String customColumn = reportLibFilterBO.getCustomColumn();
//        List<String> reportColumns = StringUtils.isEmpty(customColumn) ? new ArrayList<>() : Arrays.asList(customColumn.split(","));
//        return reportColumns;
//    }
//
//    private String getCustomColumns(String uid, String bizType) {
//        String reportColumns = null;
//        try {
//            String reportKey = "";
//            if (StringUtils.equalsIgnoreCase(bizType, OrderQueryEnum.F.toString())) {
//                reportKey = "DetailReport:FltOrderDetails";
//            } else if (StringUtils.equalsIgnoreCase(bizType, OrderQueryEnum.H.toString())) {
//                reportKey = "DetailReport:HotelOrderDetails";
//            } else if (StringUtils.equalsIgnoreCase(bizType, OrderQueryEnum.T.toString())) {
//                reportKey = "DetailReport:TrainOrderDetails";
//            } else if (StringUtils.equalsIgnoreCase(bizType, OrderQueryEnum.C.toString())) {
//                reportKey = "DetailReport:CarOrderDetails";
//            }
//            CorpReportCustomColumnPO corpReportCustomColumnPO = reportColumnService.getCustomColumn(uid, reportKey);
//            if (corpReportCustomColumnPO != null) {
//                reportColumns = corpReportCustomColumnPO.getCustomColumns();
//            }
//        } catch (SQLException e) {
//            log.error(PREFIX + "getCustomColumns", e);
//        }
//        return reportColumns;
//    }
//
//    private void convertData(ReportLibFilterBO reportLibFilterBO, OrderDetailEnumerable[] orderDetailEnumerables, ReportLibResultEntity reportLibResultEntity) {
//        String lang = reportLibFilterBO.getLang();
//        List<String> reportColumns = getCustomColumns(reportLibFilterBO);//自定义列
//        List<OrderDetailEnumerable> newList = Arrays.asList(orderDetailEnumerables);
//        if (CollectionUtils.isNotEmpty(reportColumns)) {
//            newList = newList.stream().filter(j -> reportColumns.stream().anyMatch(i -> StringUtils.equalsIgnoreCase(i, j.getCode()))).collect(Collectors.toList());
//        }
//        newList.sort(new Comparator<OrderDetailEnumerable>() {
//            @Override
//            public int compare(OrderDetailEnumerable o1, OrderDetailEnumerable o2) {
//                if (o1.getOrder() == o2.getOrder()) {
//                    return o1.getEnum().ordinal() - o2.getEnum().ordinal();
//                } else {
//                    return o1.getOrder() - o2.getOrder();
//                }
//            }
//        });
//        //标题
//        List<String> title = new ArrayList<>();
//        for (OrderDetailEnumerable item : newList) {
//            title.add(SharkUtils.get(item.getName(), item.getName(), lang));
//        }
//        reportLibResultEntity.setTitleList(title);
//
//        if (CollectionUtils.isEmpty(reportLibResultEntity.getResult())) {
//            return;
//        }
//        List<List<String>> result = new ArrayList<>();
//        result.add(title);
//        //数据
//        reportLibResultEntity.setData(result);
//        for (Map<String, String> map : reportLibResultEntity.getResult()) {
//            List<String> data = new ArrayList<>();
//            for (OrderDetailEnumerable item : newList) {
//                data.add(CommonUtils.fmtMicrometer(MapperUtils.trim(map.get(item.getCode()))));
//            }
//            result.add(data);
//        }
//    }
//
////*
////     * 查询条件时间间隔
////     *
////     * @param conditions
////     * @return
//
//
//    public int getInvervalTime(String conditions) {
//        ReportLibFilterBO reportLibFilterBO = (ReportLibFilterBO) JacksonUtil.deserialize(conditions, ReportLibFilterBO.class);
//        if (MapUtils.isNotEmpty(reportLibFilterBO.getFilterList())) {
//            String startTime = Objects.toString(reportLibFilterBO.getFilterList().get("startTime"), null);
//            String endTime = Objects.toString(reportLibFilterBO.getFilterList().get("endTime"), null);
//            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotBlank(startTime)) {
//                long start = Long.parseLong(startTime);
//                if (start > 0) {
//                    reportLibFilterBO.setStartTime(start);
//                }
//            }
//            if (StringUtils.isNotEmpty(endTime) && StringUtils.isNotBlank(endTime)) {
//                long end = Long.parseLong(endTime);
//                if (end > 0) {
//                    reportLibFilterBO.setEndTime(end);
//                }
//            }
//            if (reportLibFilterBO.getStartTime() == null && reportLibFilterBO.getEndTime() == null) {
//                throw new RuntimeException("Invalid Parameter");
//            }
//        }
//        int inverval = DateUtil.getIntervalMonths(reportLibFilterBO.getStartTime(), reportLibFilterBO.getEndTime());
//        return inverval;
//    }
//
//    private boolean excludeManagement(String uid) {
//        if (StringUtils.isEmpty(uid)) {
//            return false;
//        }
//        String management = QConfigUtils.uidOfCanViewCtripData();
//        if (management.contains(uid)) {
//            return true;
//        }
//        return false;
//    }
//}
