package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BalanceTypeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBalanceTypeRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBalanceTypeResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import onlinereport.enums.PageErrorCodeEnum;
import onlinereport.enums.ProductTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc
 */
@Service
public class HtlBalanceTypeBehaviorAnalysisExcelService extends AbstractGenralExportDataService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    public List<ChartExcelEntity> buildExcel(BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        Map map = getConditionContent(baseCondition.getBaseQueryCondition().getUid(), baseCondition.getBaseQueryCondition(), lang);
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        OnlineReportBalanceTypeRequest bookTypeRequest = new OnlineReportBalanceTypeRequest();
        bookTypeRequest.setBasecondition(baseCondition.getBaseQueryCondition());
        ProductTypeEnum[] productTypeEnums = ProductTypeEnum.values();
        for (ProductTypeEnum productTypeEnum : productTypeEnums) {
            bookTypeRequest.setProductType(productTypeEnum.toString());
            OnlineReportBalanceTypeResponse responseType = corpOnlineReportPlatformService.queryHtlBalanceTypeAnalysis(bookTypeRequest);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                chartExcelEntityList.add(hotelBalanceType(Optional.ofNullable(responseType.getBalanceTypeList()).orElse(new ArrayList<>()), lang, map, productTypeEnum.ordinal(),
                        productTypeEnum.toString()));
            } else {
                BusinessException businessException = null;
                if (Objects.isNull(responseType)) {
                    businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
                } else {
                    businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
                }
                throw businessException;
            }
        }
        return chartExcelEntityList;
    }

    private ChartExcelEntity hotelBalanceType(List<BalanceTypeInfo> balanceTypeInfoList, String lang, Map map, int sheetIndex, String productType) {
        List title = Arrays.asList(SharkUtils.get("Exceltopname.paytype", lang), SharkUtils.get("Index.nightnum", lang),
                SharkUtils.get("Index.nightnumper", lang), SharkUtils.get("Overview.HotelAvgNightPrice", lang), SharkUtils.get("Index.costmoney", lang));
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(title);
        List data = new ArrayList();
        HtlBalanceTypeBehaviorAnalysisAdaptor.HtlBalanceTypeEnum[] htlBalanceTypeEnums = HtlBalanceTypeBehaviorAnalysisAdaptor.HtlBalanceTypeEnum.values();
        for (HtlBalanceTypeBehaviorAnalysisAdaptor.HtlBalanceTypeEnum htlBalanceTypeEnum : htlBalanceTypeEnums) {
            BalanceTypeInfo balanceTypeInfo = balanceTypeInfoList.stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getBalanceType(), htlBalanceTypeEnum.getKey().toString()))
                    .findFirst().orElse(new BalanceTypeInfo());
            data.add(Arrays.asList(SharkUtils.get(htlBalanceTypeEnum.getSharkKey(), lang)
                    , MapperUtils.convertDigitToZero(balanceTypeInfo.getTotalQuantity())
                    , MapperUtils.convertDigitToZeroString(balanceTypeInfo.getQuantityPercent()).concat(GlobalConst.PERCENT_QUOTE)
                    , MapperUtils.convertDigitToZero(balanceTypeInfo.getAvgNightPrice())
                    , MapperUtils.convertDigitToZero(balanceTypeInfo.getTotalAmount())));
        }
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(getSheetNameByProductType(lang, productType));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }
}
