package com.corpgovernment.resource.schedule.onlinereport.reportlib.reportfactory;

import com.corpgovernment.resource.schedule.domain.onlinereport.customreport.CustomReportNewService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 * @date 2020/9/28 17:44
 * @Desc 自定义报表
 */
@Service
@Slf4j
public class CustormReport implements IReport {

    protected static final String LOG_TITLE = CustormReport.class.getSimpleName();
    @Autowired
    private CustomReportNewService service;

    //@Autowired
   // private CustomReportDataService customReportDataService;

    public Workbook create(TaskEntity taskEntity) throws Exception {
   /*     Workbook workbook = null;
        try {
            if (ReportTypeEnum.Custom.getValue() == taskEntity.getReportType()) {
                CustomRequest customRequest = new CustomRequest();
                if (StringUtils.isNotEmpty(taskEntity.getReportId())) {
                    CustomReportInfoNew customReportInfoNew = service.queryById(Long.valueOf(taskEntity.getReportId()));
                    customRequest.setReportJson(customReportInfoNew.getReportCondition());
                } else {
                    customRequest.setReportJson(taskEntity.getConditions());
                }
                customRequest.setUid(taskEntity.getUid());
                String sheetName = "sheet";
                ReportUserDefinedOutputNew output = customReportDataService.query(customRequest);
                workbook = PoiCustomExcelUtils.createExcel(output.getRowList(), output.getColumnCount(), output.getRowCount(), sheetName);
            }
        } catch (SQLException e) {
            log.error(LOG_TITLE, e);
            if (workbook != null) {
                workbook.close();
            }
            throw new Exception("create excel fail");
        } catch (IOException e) {
            log.error(LOG_TITLE, e);
            if (workbook != null) {
                workbook.close();
            }
            throw new Exception("create excel fail");
        }
        return workbook;*/
        return null;
    }
}
