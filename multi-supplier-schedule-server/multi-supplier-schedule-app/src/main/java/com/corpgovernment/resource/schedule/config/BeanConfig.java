package com.corpgovernment.resource.schedule.config;

import com.ctrip.corp.obt.generic.threadpool.core.spring.EnableDynamicTp;
import com.ctrip.corp.obt.generic.threadpool.core.support.DynamicTp;
import com.ctrip.corp.obt.generic.threadpool.core.support.ThreadPoolBuilder;
import com.ctrip.corp.obt.generic.threadpool.core.support.task.wrapper.TaskWrappers;
import com.google.common.collect.Sets;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.ctrip.corp.obt.generic.threadpool.common.em.QueueTypeEnum.ARRAY_BLOCKING_QUEUE;

/***
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/7
 **/
@Configuration
@EnableDynamicTp
public class BeanConfig {
    private static final Integer MAX_POOL_SIZE_THRESHOLD = 150;

    @Bean
    @DynamicTp("screenThreadPoolExecutor")
    public ThreadPoolExecutor screenThreadPoolExecutor() {
        int corePoolSize = 10;
        if (corePoolSize > MAX_POOL_SIZE_THRESHOLD) {
            corePoolSize = MAX_POOL_SIZE_THRESHOLD;
        }
        int maximumPoolSize = corePoolSize * 2;
        if (maximumPoolSize > MAX_POOL_SIZE_THRESHOLD) {
            maximumPoolSize = MAX_POOL_SIZE_THRESHOLD;
        }
        int threadQueueSize = 200;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .threadPoolName("screenThreadPoolExecutor")
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();

    }


    @Bean
    @DynamicTp("batchThreadPoolExecutor")
    public ThreadPoolExecutor batchThreadPoolExecutor() {
        int corePoolSize = 10;
        if (corePoolSize > MAX_POOL_SIZE_THRESHOLD) {
            corePoolSize = MAX_POOL_SIZE_THRESHOLD;
        }
        int maximumPoolSize = corePoolSize * 2;
        if (maximumPoolSize > MAX_POOL_SIZE_THRESHOLD) {
            maximumPoolSize = MAX_POOL_SIZE_THRESHOLD;
        }
        int threadQueueSize = 200;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .threadPoolName("batchThreadPoolExecutor")
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();

    }

    @Bean
    @DynamicTp("inheritableSupplierMonitorExecutePool")
    public ThreadPoolExecutor inheritableSupplierMonitorExecutePool() {
        int corePoolSize = 10;
        if (corePoolSize > MAX_POOL_SIZE_THRESHOLD) {
            corePoolSize = MAX_POOL_SIZE_THRESHOLD;
        }
        int maximumPoolSize = corePoolSize * 2;
        if (maximumPoolSize > MAX_POOL_SIZE_THRESHOLD) {
            maximumPoolSize = MAX_POOL_SIZE_THRESHOLD;
        }
        int threadQueueSize = 200;
        return ThreadPoolBuilder.newBuilder()
                .taskWrappers(TaskWrappers.getInstance().getByNames(Sets.newHashSet("ttl")))
                .corePoolSize(corePoolSize)
                .maximumPoolSize(maximumPoolSize)
                .threadPoolName("inheritableSupplierMonitorExecutePool")
                .keepAliveTime(1800L)
                .timeUnit(TimeUnit.SECONDS)
                .workQueue(ARRAY_BLOCKING_QUEUE.getName(), threadQueueSize)
                .rejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy())
                .build();

    }
}
