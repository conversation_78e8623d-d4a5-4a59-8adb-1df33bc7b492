package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.bluespace.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportHotelOrderInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportOrderDetailInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.bluespace.AbstractOrderDetailBlueSpaceService;
import onlinereport.enums.reportlib.OrderDetailEnumerable;
import onlinereport.enums.reportlib.bluespace.BsHotelOrderDetailEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-10-13 13:58
 * @desc
 */
@Service
public class HotelOrderBluespaceService extends AbstractOrderDetailBlueSpaceService {

    @Override
    public OrderDetailEnumerable[] getEnum(String reportType) {
        return BsHotelOrderDetailEnum.values();
    }


    protected List<Map<String, Object>> convertMapData(OnlineReportOrderDetailInfo detailInfo, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<OnlineReportHotelOrderInfo> data = detailInfo.getHtlOrderList();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }

        for (OnlineReportHotelOrderInfo order : data) {
            Map<String, Object> map = new HashMap<>();
            map.put(BsHotelOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToZeroString(order.getOrderId()));
            map.put(BsHotelOrderDetailEnum.ORDERSTATUS.getCode(), MapperUtils.trim(order.getOrderStatus()));
            map.put(BsHotelOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrderDate()));
            map.put(BsHotelOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorpCorporation()));
            map.put(BsHotelOrderDetailEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorpName()));
            map.put(BsHotelOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(BsHotelOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUserName()));
            /////////////////////////////////////////////////////////////////////
            map.put(BsHotelOrderDetailEnum.DEADPRICE.getCode(), DigitBaseUtils.formatDigit(order.getDeadPriceOnenight()));
            map.put(BsHotelOrderDetailEnum.HOTELNAME.getCode(), order.getHotelName());
            map.put(BsHotelOrderDetailEnum.HOTELGROUPNAME.getCode(), order.getHotelGroupName());
            map.put(BsHotelOrderDetailEnum.HOTELBRANDNAME.getCode(), MapperUtils.trim(order.getHotelBrandName()));
            map.put(BsHotelOrderDetailEnum.CITYNAME.getCode(), order.getCityName());
            map.put(BsHotelOrderDetailEnum.COUNTRYNAME.getCode(), order.getCountryName());
            map.put(BsHotelOrderDetailEnum.CLIENTNAME.getCode(), MapperUtils.trim(order.getClientName()));
            map.put(BsHotelOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            map.put(BsHotelOrderDetailEnum.ORDERROOMNUM.getCode(), MapperUtils.convertDigitToZero(order.getOrderRoomNum()));
            map.put(BsHotelOrderDetailEnum.PERSONS.getCode(), MapperUtils.convertDigitToZero(order.getPersons()));
            map.put(BsHotelOrderDetailEnum.DAYNUM.getCode(), MapperUtils.convertDigitToString(order.getDayNum()));
            map.put(BsHotelOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrivalDateTime()));
            map.put(BsHotelOrderDetailEnum.DEPARTUREDATETIME.getCode(), MapperUtils.trim(order.getDepartureDateTime()));
            map.put(BsHotelOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getServiceFee()).toString());
            map.put(BsHotelOrderDetailEnum.REFUNDTIME.getCode(), MapperUtils.trim(order.getRefundTime()));
            map.put(BsHotelOrderDetailEnum.RFDAMOUNT.getCode(), MapperUtils.convertDigitToZero(DigitBaseUtils.formatDigit(order.getRfdAmount())));
            map.put(BsHotelOrderDetailEnum.RFDQUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getRfdQuantity()));
            map.put(BsHotelOrderDetailEnum.BREAKFAST.getCode(), MapperUtils.convertDigitToZero(order.getBreakfast()));
            map.put(BsHotelOrderDetailEnum.ROOMNAME.getCode(), MapperUtils.trim(order.getRoomName()));
            map.put(BsHotelOrderDetailEnum.OCURRENCY.getCode(), MapperUtils.trim(order.getOCurrency()));
            map.put(BsHotelOrderDetailEnum.OEXCHANGERATE.getCode(), DigitBaseUtils.formatDigit(order.getOExchangerate()));
            map.put(BsHotelOrderDetailEnum.POSTSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getPostservicefee()));
            map.put(BsHotelOrderDetailEnum.ISREFUND.getCode(), MapperUtils.trim(order.getIsRefund()));
            map.put(BsHotelOrderDetailEnum.ROOMPRICE.getCode(), DigitBaseUtils.formatDigit(order.getRoomPrice()));
            map.put(BsHotelOrderDetailEnum.REALPAYWITHSERVICE.getCode(), DigitBaseUtils.formatDigit(order.getRealPayWithService()));
            result.add(map);
        }
        return result;
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.hotel;
    }
}
