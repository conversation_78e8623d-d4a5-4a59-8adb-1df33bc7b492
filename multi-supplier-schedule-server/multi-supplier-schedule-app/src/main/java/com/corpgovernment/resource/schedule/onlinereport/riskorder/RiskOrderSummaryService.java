//package com.corpgovernment.resource.schedule.onlinereport.riskorder;
//
//
//
//import com.ctrip.corp.onlinereportweb.commmon.enums.PageErrorCodeEnum;
//import com.ctrip.corp.onlinereportweb.commmon.enums.reportlib.FlightRiskWarningLevel;
//import com.ctrip.corp.onlinereportweb.commmon.enums.reportlib.HotelRiskWarningLevel;
//import com.ctrip.corp.onlinereportweb.commmon.helper.QConfigUtils;
//import com.ctrip.corp.onlinereportweb.commmon.utils.SharkUtils;
//import com.ctrip.corp.onlinereportweb.domain.bo.common.BusinessException;
//import com.ctrip.corp.onlinereportweb.domain.bo.entity.UserPermissionsBo;
//import com.ctrip.corp.onlinereportweb.domain.rpc.ICorpOnlineReportPlatformService;
//import com.ctrip.corp.onlinereportweb.domainreport.adaptor.biz.AbstractGenralExportDataService;
//import com.ctrip.corp.onlinereportweb.domainreport.bo.BaseQueryConditionBO;
//import com.ctrip.framework.clogging.agent.log.ILog;
//import com.ctrip.framework.clogging.agent.log.LogManager;
//import com.google.common.collect.Maps;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @date ：Created in 2022/4/12 14:36
// * @description：
// * @modified By：
// * @version: $
// */
//@Service
//public class RiskOrderSummaryService extends AbstractGenralExportDataService {
//
//    private ILog log = LogManager.getLogger(RiskOrderSummaryService.class);
//
//    private String LOG_TITLE = this.getClass().getSimpleName();
//
//    @Autowired
//    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;
//
//
//    /**
//     * 风险订单概览
//     *
//     * @param baseCondition
//     * @return
//     * @throws BusinessException
//     */
//    public Map getRiskOrderTotal(BaseQueryConditionBO baseCondition, UserPermissionsBo userPermissionsBo) throws BusinessException {
//        OnlineReportRiskOrderTotalRequest request = new OnlineReportRiskOrderTotalRequest();
//        request.setBasecondition(baseCondition.getBaseQueryCondition());
//        request.setLang(baseCondition.getLang());
//        OnlineReportRiskOrderTotalResponse responseType = getRiskTotal(request);
//        if (responseType != null && responseType.getResponseCode() == 20000) {
//            List<RiskOrderTotal> countSummary = responseType.getRiskOrderCount();
//            suppleBlankSummary(countSummary);
//            countSummary.forEach(
//                    t -> t.setHasPermission(
//                            userPermissionsBo.getResourceKeys().contains("ComplianceMonitor:RiskOrder:" + t.getRiskScene())
//                    )
//            );
//            return riskTotal(countSummary);
//        } else {
//            BusinessException businessException = null;
//            if (Objects.isNull(responseType)) {
//                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
//            } else {
//                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
//            }
//            throw businessException;
//        }
//    }
//
//    /**
//     * 风险订单概览
//     *
//     * @param baseCondition
//     * @return
//     * @throws BusinessException
//     */
//    public Map getRiskOrderSummary(BaseQueryConditionBO baseCondition) throws BusinessException {
//        OnlineReportRiskOrderSummaryRequest request = new OnlineReportRiskOrderSummaryRequest();
//        request.setBasecondition(baseCondition.getBaseQueryCondition());
//        // 区分 产线 及 场景
//        QueryReportRiskSceneTypeEnum sceneTypeEnum = QueryReportRiskSceneTypeEnum.valueOf(baseCondition.getRiskScene());
//        request.setScene(sceneTypeEnum);
//        request.setLang(baseCondition.getLang());
//        int pageSize = 5;
//        if (baseCondition.getPager() != null && baseCondition.getPager().getPageSize() != null) {
//            pageSize = baseCondition.getPager().getPageSize();
//        }
//        int limit = pageSize < 1 ? 5 : pageSize;
//        OnlineReportRiskOrderSummaryResponse responseType = getRiskSummary(request);
//        if (responseType != null && responseType.getResponseCode() == 20000) {
//            List<RiskOrderSummary> summaries = responseType.getRiskSummary();
//            if (CollectionUtils.isEmpty(summaries)){
//                throw  new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
//            }
//            List<RiskOrderSummary> flightSummaries = new ArrayList<>();
//            List<RiskOrderSummary> hotelSummaries = new ArrayList<>();
//            List<RiskOrderSummary> carSummaries = new ArrayList<>();
//            List<RiskOrderSummary> hotelUnderStaySummaries = new ArrayList<>();
//            for (RiskOrderSummary summary: summaries) {
//                switch (summary.type) {
//                    case "FLT_REFUND":
//                        flightSummaries.add(summary);
//                        break;
//                    case "HTL_CASH_OUT":
//                        hotelSummaries.add(summary);
//                        break;
//                    case "CAR_DOUBLE_BOOK":
//                        carSummaries.add(summary);
//                        break;
//                    case "HTL_UNDER_STAY":
//                        hotelUnderStaySummaries.add(summary);
//                        break;
//                    default:
//                        break;
//                }
//            }
//            Map<String, Object> resp = new HashMap<>();
//            if (flightSummaries.size() > 0) {
//                resp.put("FLT_REFUND", buildData(flightSummaries, limit, flightRiskWarningLevel(baseCondition.getLang()), sceneTypeEnum));
//            }
//            if (hotelSummaries.size() > 0) {
//                resp.put("HTL_CASH_OUT", buildData(hotelSummaries, limit, hotelRiskWarningLevel(baseCondition.getLang()), sceneTypeEnum));
//            }
//            if (carSummaries.size() > 0) {
//                resp.put("CAR_DOUBLE_BOOK", buildData(carSummaries, limit, emptyRiskWarningLevel(),sceneTypeEnum));
//            }
//            if (hotelUnderStaySummaries.size() > 0) {
//                resp.put("HTL_UNDER_STAY", buildData(hotelUnderStaySummaries, limit, emptyRiskWarningLevel(),sceneTypeEnum));
//            }
//            return resp;
//        } else {
//            BusinessException businessException = null;
//            if (Objects.isNull(responseType)) {
//                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
//            } else {
//                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
//            }
//            throw businessException;
//        }
//    }
//
//    public OnlineReportRiskOrderSummaryResponse getRiskSummary(OnlineReportRiskOrderSummaryRequest request) {
//        OnlineReportRiskOrderSummaryResponse response = null;
//        try {
//            log.info(LOG_TITLE, request.toString());
//            response = corpOnlineReportPlatformService.queryRiskOrderSummary(request);
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return response;
//    }
//
//    public OnlineReportRiskOrderTotalResponse getRiskTotal(OnlineReportRiskOrderTotalRequest request) {
//        OnlineReportRiskOrderTotalResponse response = null;
//        try {
//            log.info(LOG_TITLE, request.toString());
//            response = corpOnlineReportPlatformService.queryRiskOrderTotal(request);
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return response;
//    }
//
//    public Map buildData(List<RiskOrderSummary> summaries, int limit, Map<String, String> warningLevelInfo,
//                         QueryReportRiskSceneTypeEnum sceneTypeEnum) {
//        int count = 0;
//        BigDecimal amount = BigDecimal.ZERO;
//        Map<String, Object> result = new HashMap<>();
//        Map<String, Map<String, Object>> var = new HashMap<>();
//        for (RiskOrderSummary summary: summaries) {
//            count += summary.count;
//            amount = amount.add(Optional.ofNullable(summary.amount).orElse(BigDecimal.ZERO));
//            Map<String, Object> inner = var.computeIfAbsent(summary.uid, k -> new HashMap<>());
//            inner.put(summary.warnLevel, summary.count);
//            inner.put("name", summary.uidName);
//            inner.put("uid", summary.uid);
//            inner.put("count", (int) inner.getOrDefault("count", 0) + summary.count);
//            inner.put("amount", ((BigDecimal) inner.getOrDefault("amount", BigDecimal.ZERO)).add(Optional.ofNullable(summary.amount).orElse(BigDecimal.ZERO)));
//            inner.put("quantity",
//                    (int) inner.getOrDefault("quantity", 0) + Optional.ofNullable(summary.getQuantity()).orElse(0)
//            );
//        }
//        List<Map<String, Object>> data = new ArrayList<>(var.values());
//
//        // 先按风险订单排序，再按金额排序
//        if (QueryReportRiskSceneTypeEnum.FLT_REFUND == sceneTypeEnum) {
//            data.sort(
//                    Comparator.comparing(o -> (Integer) ((Map<String, Object>) o).getOrDefault(FlightRiskWarningLevel.WARNINGLEVEL3.getCode(), 0))
//                            .thenComparing(o -> (Integer) ((Map<String, Object>) o).getOrDefault(FlightRiskWarningLevel.WARNINGLEVEL1.getCode(), 0))
//                            .thenComparing(o -> (Integer) ((Map<String, Object>) o).getOrDefault(FlightRiskWarningLevel.WARNINGLEVEL2.getCode(), 0))
//                            .thenComparing(o -> (BigDecimal) ((Map<String, Object>) o).getOrDefault("amount", new BigDecimal("0"))).reversed()
//            );
//        }
//
//        if (QueryReportRiskSceneTypeEnum.HTL_CASH_OUT == sceneTypeEnum) {
//            data.sort(
//                    Comparator.comparing(o -> (Integer) ((Map<String, Object>) o).getOrDefault(HotelRiskWarningLevel.WARNINGLEVEL0.getCode(), 0))
//                            .thenComparing(o -> (Integer) ((Map<String, Object>) o).getOrDefault(HotelRiskWarningLevel.WARNINGLEVEL1_3.getCode(), 0))
//                            .thenComparing(o -> (BigDecimal) ((Map<String, Object>) o).getOrDefault("amount", new BigDecimal("0"))).reversed()
//            );
//        }
//
//        if (QueryReportRiskSceneTypeEnum.CAR_DOUBLE_BOOK == sceneTypeEnum) {
//            data.sort(
//                    Comparator.comparing(o -> (BigDecimal) ((Map<String, Object>) o).getOrDefault("amount", new BigDecimal("0"))).reversed()
//            );
//        }
//
//        if (QueryReportRiskSceneTypeEnum.HTL_UNDER_STAY == sceneTypeEnum) {
//            data.sort(
//                    Comparator.comparing(o -> (Integer) ((Map<String, Object>) o).getOrDefault("quantity", 0)).reversed()
//            );
//        }
//
//        data.forEach(c -> c.put("rank", data.indexOf(c) + 1));
//        result.put("count", count);
//        result.put("amount", amount);
//        result.put("data", data.size() > limit ? data.subList(0, limit) : data);
//        result.put("riskLevel", warningLevelInfo);
//        return result;
//    }
//
//    public Map<String, String> flightRiskWarningLevel(String lang) {
//        return new HashMap<String, String>(){{
//            put(FlightRiskWarningLevel.WARNINGLEVEL0.getCode(), SharkUtils.get(FlightRiskWarningLevel.WARNINGLEVEL0.getName(), lang));
//            put(FlightRiskWarningLevel.WARNINGLEVEL1.getCode(), SharkUtils.get(FlightRiskWarningLevel.WARNINGLEVEL1.getName(), lang));
//            put(FlightRiskWarningLevel.WARNINGLEVEL2.getCode(), SharkUtils.get(FlightRiskWarningLevel.WARNINGLEVEL2.getName(), lang));
//            put(FlightRiskWarningLevel.WARNINGLEVEL3.getCode(), SharkUtils.get(FlightRiskWarningLevel.WARNINGLEVEL3.getName(), lang));
//            put(FlightRiskWarningLevel.WARNINGLEVEL4.getCode(), SharkUtils.get(FlightRiskWarningLevel.WARNINGLEVEL4.getName(), lang));
//        }};
//    }
//
//    public Map<String, String> hotelRiskWarningLevel(String lang) {
//        return new HashMap<String, String>(){{
//            put(HotelRiskWarningLevel.WARNINGLEVEL0.getCode(), SharkUtils.get(HotelRiskWarningLevel.WARNINGLEVEL0.getName(), lang));
//            put(HotelRiskWarningLevel.WARNINGLEVEL1.getCode(), SharkUtils.get(HotelRiskWarningLevel.WARNINGLEVEL1.getName(), lang));
//            put(HotelRiskWarningLevel.WARNINGLEVEL2.getCode(), SharkUtils.get(HotelRiskWarningLevel.WARNINGLEVEL2.getName(), lang));
//            put(HotelRiskWarningLevel.WARNINGLEVEL3.getCode(), SharkUtils.get(HotelRiskWarningLevel.WARNINGLEVEL3.getName(), lang));
//            put(HotelRiskWarningLevel.WARNINGLEVEL1_3.getCode(), SharkUtils.get(HotelRiskWarningLevel.WARNINGLEVEL1_3.getName(), lang));
//        }};
//    }
//
//    public Map<String, String> emptyRiskWarningLevel() {
//        return Maps.newHashMap();
//    }
//
//    public Map riskTotal(List<RiskOrderTotal> riskOrderTotals) {
//        HashMap<String, Object> var1= new HashMap<>();
//        // 未处理0 + 已处理1
//        for (RiskOrderTotal riskOrderTotal: riskOrderTotals) {
//            Map tmp = (Map) var1.getOrDefault(riskOrderTotal.getRiskScene(), new HashMap<>());
//            tmp.put("riskScene", riskOrderTotal.getRiskScene());
//            tmp.put("amount", Optional.ofNullable(riskOrderTotal.getAmount()).orElse(BigDecimal.ZERO).add(
//                    (BigDecimal) tmp.getOrDefault("amount", BigDecimal.ZERO)));
//            tmp.put("count", Optional.ofNullable(riskOrderTotal.getCount()).orElse(0) +
//                    (Integer) tmp.getOrDefault("count", 0));
//            tmp.put("quantity", Optional.ofNullable(riskOrderTotal.getQuantity()).orElse(0) +
//                    (Integer) tmp.getOrDefault("quantity", 0));
//            tmp.put("hasPermission", riskOrderTotal.isHasPermission());
//            tmp.put("hasEnableOperation", checkOperation(riskOrderTotal.getRiskScene()));
//            // 未处理的数据
//            if (! tmp.containsKey("unHandleCount")) {
//                tmp.put("unHandleAmount", BigDecimal.ZERO);
//                tmp.put("unHandleCount", 0);
//                tmp.put("unHandleQuantity", 0);
//            }
//            if (riskOrderTotal.getOperateStatus() == 0) {
//                tmp.put("unHandleAmount", Optional.ofNullable(riskOrderTotal.getAmount()).orElse(BigDecimal.ZERO));
//                tmp.put("unHandleCount", Optional.ofNullable(riskOrderTotal.getCount()).orElse(0));
//                tmp.put("unHandleQuantity", Optional.ofNullable(riskOrderTotal.getQuantity()).orElse(0));
//            }
//            var1.put(riskOrderTotal.getRiskScene(), tmp);
//        }
//        Map data = new HashMap(){{
//            put("riskOrderCount", var1.values());
//        }};
//        return data;
//    }
//
//    private void suppleBlankSummary(List<RiskOrderTotal> rawTotal) {
//        List<String> scenes = rawTotal.stream().map(RiskOrderTotal::getRiskScene).collect(Collectors.toList());
//        if (! scenes.contains(QueryReportRiskSceneTypeEnum.FLT_REFUND.name())) {
//            RiskOrderTotal blank = new RiskOrderTotal();
//            blank.setRiskScene(QueryReportRiskSceneTypeEnum.FLT_REFUND.name());
//            blank.setOperateStatus(0);
//            rawTotal.add(blank);
//        }
//        if (! scenes.contains(QueryReportRiskSceneTypeEnum.HTL_CASH_OUT.name())) {
//            RiskOrderTotal blank = new RiskOrderTotal();
//            blank.setRiskScene(QueryReportRiskSceneTypeEnum.HTL_CASH_OUT.name());
//            blank.setOperateStatus(0);
//            rawTotal.add(blank);
//        }
//        if (! scenes.contains(QueryReportRiskSceneTypeEnum.HTL_UNDER_STAY.name())) {
//            RiskOrderTotal blank = new RiskOrderTotal();
//            blank.setRiskScene(QueryReportRiskSceneTypeEnum.HTL_UNDER_STAY.name());
//            blank.setOperateStatus(0);
//            rawTotal.add(blank);
//        }
//
//        // 去除用车疑似套现场景
//        // rawTotal.removeIf(d -> d.getRiskScene().equals(QueryReportRiskSceneTypeEnum.CAR_DOUBLE_BOOK.name()));
//
//        if (! scenes.contains(QueryReportRiskSceneTypeEnum.CAR_DOUBLE_BOOK.name())) {
//            RiskOrderTotal blank = new RiskOrderTotal();
//            blank.setRiskScene(QueryReportRiskSceneTypeEnum.CAR_DOUBLE_BOOK.name());
//            blank.setOperateStatus(0);
//            rawTotal.add(blank);
//        }
//    }
//
//    private boolean checkOperation(String riskScene) {
//        List<String> scenes = QConfigUtils.getOperationCloseRiskScene();
//        if (Optional.ofNullable(scenes).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).anyMatch(i-> StringUtils.equalsIgnoreCase(i, riskScene))) {
//            return false;
//        }
//        return true;
//    }
//
//}
