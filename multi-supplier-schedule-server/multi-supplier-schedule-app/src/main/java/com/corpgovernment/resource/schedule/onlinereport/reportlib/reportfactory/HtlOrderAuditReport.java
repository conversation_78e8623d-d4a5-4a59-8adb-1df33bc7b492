package com.corpgovernment.resource.schedule.onlinereport.reportlib.reportfactory;

import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.cglib.seriallizer.JacksonUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TimeFilterTypeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BasePermitVerfiyService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.htlaudit.HtlOrderAuditService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.TempFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/*
 * <AUTHOR>
 * @date 2020/9/28 17:44
 * @Desc 报告库
 */
@Service
@Slf4j
public class HtlOrderAuditReport extends AbstractGenralExportDataService implements IReport {

    public final static String CORP_KEY = "corp";
    public final static String ACCOUNT_KEY = "account";
    public final static String DEPT_KEY = "dept";
    public final static String COST_CENTER_KEY = "costcenter";
    public final static String TIME_RANGE_FORMAT = "%s~%s";
    protected static final String LOG_TITLE = HtlOrderAuditReport.class.getSimpleName();
    private final static int DEFAULT_PAGESIZE = 1000;
    //默认最大间隔天数，超过就要分批
    private final static int MAX_INTERVAL_DAYS_DEAFULT = 33;
    @Autowired
    private HtlOrderAuditService orderDetailService;
    @Autowired
    private BasePermitVerfiyService basePermitVerfiyService;

    public Workbook create(TaskEntity taskEntity) throws Exception {
        Workbook workbook = null;
        try {
            String conditions = taskEntity.getConditions();
            log.info(LOG_TITLE, conditions);
            BaseQueryConditionBO baseQueryConditionBO = (BaseQueryConditionBO) JacksonUtil.deserialize(conditions, BaseQueryConditionBO.class);
            String uid = taskEntity.getUid();
            baseQueryConditionBO.getBaseQueryCondition().setUid(uid);
            String lang = taskEntity.getLang();
            String download_pageSize = QConfigUtils.getValue("download_pageSize");
            int pageSize = StringUtils.isEmpty(download_pageSize) ? DEFAULT_PAGESIZE : Integer.valueOf(download_pageSize);
            UserPermissionsBo userPermissionsBo = basePermitVerfiyService.vaildPermit(uid, baseQueryConditionBO, lang);
            BaseQueryConditionBO baseQueryCondition = basePermitVerfiyService.convertToBaseOrderQuery(userPermissionsBo, baseQueryConditionBO);
            Pager pager = new Pager(0l, pageSize, 1, 0);
            baseQueryCondition.setLang(lang);
            baseQueryCondition.setPager(pager);
            workbook = createBigData(baseQueryCondition, pageSize, uid, lang);
        } catch (Exception e) {
            log.error(LOG_TITLE, e);
/*            if (workbook != null) {
                if (workbook instanceof SXSSFWorkbook) {
                    ((SXSSFWorkbook) workbook).dispose();
                }
                workbook.close();
            }*/
            throw e;
        }
        return workbook;
    }

    private Workbook createBigData(BaseQueryConditionBO reportLibFilterBO, int pageSize, String uid, String lang) throws IOException, BusinessException {
        // 创建工作簿
        String tempPath = System.getProperty(TempFile.JAVA_IO_TMPDIR);
        log.info(LOG_TITLE, "createBigData tempPath:" + tempPath);
        return createBigDataMulitBatchByDate(reportLibFilterBO, pageSize, uid, lang);
    }

    /**
     * @param lang
     * @param baseQueryConditionBO
     * @param uid
     * @return
     */
    public List filllFilterContent(String lang, BaseQueryConditionBO baseQueryConditionBO, String uid) {
        BaseQueryCondition baseQueryCondition = baseQueryConditionBO.getBaseQueryCondition();
        List data = new ArrayList();
        data.addAll(getTimeRangeContent(lang, baseQueryCondition.getTimeFilterList()));
        Map map = getConditionContent(uid, baseQueryCondition, lang);
        data.addAll(getFilterContent(baseQueryConditionBO.getUsers(), baseQueryConditionBO.getEmployeIds(), lang));
        data.addAll(getDataRangeContent(lang, map));
        return data;
    }

    /**
     * 数据范围
     *
     * @param lang
     * @param map
     * @return
     */
    protected List getDataRangeContent(String lang, Map map) {
        return Arrays.asList(
                Arrays.asList(SharkUtils.get("Index.public", lang), (String) map.get(CORP_KEY)),
                Arrays.asList(SharkUtils.get("Public.account", lang), (String) map.get(ACCOUNT_KEY)),
                Arrays.asList(SharkUtils.get("Public.department", lang), (String) map.get(DEPT_KEY)),
                Arrays.asList(SharkUtils.get("Public.costcenter", lang), (String) map.get(COST_CENTER_KEY)));
    }

    /**
     * @param lang
     * @param timeFilterTypeInfoList
     * @return
     */
    protected List getTimeRangeContent(String lang, List<TimeFilterTypeInfo> timeFilterTypeInfoList) {
        List list = new ArrayList();
        list.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY));
        for (TimeFilterTypeInfo timeFilterTypeInfo : timeFilterTypeInfoList) {
            if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "orderdate")) {
                list.add(Arrays.asList(SharkUtils.get("Exceltopname.date", lang),
                        String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
            } else if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "dealdate")) {
                list.add(Arrays.asList(SharkUtils.get("Exceltopname.dealdate", lang),
                        String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
            } else if (StringUtils.equalsIgnoreCase(timeFilterTypeInfo.getTimeType(), "usedate")) {
                list.add(Arrays.asList(SharkUtils.get("Exceltopname.livedate", lang),
                        String.format(TIME_RANGE_FORMAT, timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime())));
            }
        }
        return list;
    }

    /**
     * @param users      预订人
     * @param passengers 出行人
     * @param lang       语言
     * @return
     */
    protected List getFilterContent(List<String> users, List<String> passengers, String lang) {
        List list = new ArrayList();
        String default_tip = SharkUtils.get("Report.SelectResult2", lang);
        list.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY));
        // 预订人
        list.add(Arrays.asList(SharkUtils.get("UserSurvey.Q1.Opt5", lang),
                CollectionUtils.isEmpty(users) ? default_tip : StringUtils.join(users, GlobalConst.SEPARATOR)));
        // 出行人
        list.add(Arrays.asList(SharkUtils.get("TravelPosition.passenger", lang),
                CollectionUtils.isEmpty(passengers) ? default_tip : StringUtils.join(passengers, GlobalConst.SEPARATOR)));
        return list;
    }

    /**
     * @param baseQueryConditionBO
     * @param pageSize
     * @param uid
     * @param lang
     * @return
     * @throws IOException
     * @throws BusinessException
     */
    private Workbook createBigDataMulitBatchByDate(BaseQueryConditionBO baseQueryConditionBO, int pageSize, String uid, String lang) throws IOException, BusinessException {
 /*       Integer sheetLimit = QConfigUtils.getInterValue("sheetLimit");
        if (sheetLimit == null || sheetLimit <= 0) {
            sheetLimit = 10000;
        }
        List filterList = filllFilterContent(lang, baseQueryConditionBO, uid);

        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        SXSSFWorkbook workbook = new SXSSFWorkbook(hssfWorkbook, sheetLimit);
        int pageIndex = 1;
        boolean isEmpty = false;//是否为空
        boolean isLastPage = false;//最后一页
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info(LOG_TITLE, "createBigDataMulitBatchByDate start");
        int startRow = 0;
        List<TimeFilterTypeInfo> timeFilterTypeInfoList = baseQueryConditionBO.getBaseQueryCondition().getTimeFilterList();
        String sheetName = getSheetName(lang);
        Map<Integer, Integer> maxWidth = new HashMap<>();
        TimeFilterTypeInfo timeFilterTypeInfo = null;
        Optional optional1 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getTimeType(), "orderdate"))
                .findFirst();
        Optional optional2 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getTimeType(), "dealdate"))
                .findFirst();
        Optional optional3 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> org.apache.commons.lang.StringUtils.equalsIgnoreCase(i.getTimeType(), "usedate"))
                .findFirst();
        if (optional1.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional1.get();
        } else if (optional2.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional2.get();
        } else if (optional3.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional3.get();
        }
        long intervalDays = DateUtil.betweenDay(timeFilterTypeInfo.getStartTime(), timeFilterTypeInfo.getEndTime());
        if (intervalDays < MAX_INTERVAL_DAYS_DEAFULT) {
            while (!isLastPage) {
                List<List<Object>> dataSource = new ArrayList<>();
                Pager pager = baseQueryConditionBO.getPager();
                pager.setPageIndex(pageIndex);
                OrderDetailBO orderDetail = orderDetailService.queryDetail(uid, baseQueryConditionBO, true);
                if (pageIndex == 1) {
                    dataSource.add(new ArrayList<>(orderDetail.getTitle()));
                }
                pageIndex++;
                isEmpty = CollectionUtils.isEmpty(orderDetail.getData());
                isLastPage = isEmpty || orderDetail.getData().size() < pageSize;
                if (!isEmpty) {
                    dataSource.addAll(orderDetail.getData());
                }
                PoiCustomExcelUtils.createExcelBigDataMulitBatchObj(workbook, dataSource, startRow, sheetName, maxWidth);
                startRow += dataSource.size();
            }
        } else {
            boolean hasTitle = false;
            String startTime = timeFilterTypeInfo.getStartTime();
            String endTime = timeFilterTypeInfo.getEndTime();
            String startTimeTemp = startTime;
            String endTimeTemp = DateUtil.offsetTime(startTime, MAX_INTERVAL_DAYS_DEAFULT, Calendar.DAY_OF_YEAR);
            while (endTimeTemp.compareTo(endTime) <= 0 && startTimeTemp.compareTo(endTime) <= 0) {
                timeFilterTypeInfo.setStartTime(startTimeTemp);
                timeFilterTypeInfo.setEndTime(endTimeTemp);
                pageIndex = 1;
                isLastPage = false;//最后一页
                while (!isLastPage) {
                    List<List<Object>> dataSource = new ArrayList<>();
                    Pager pager = baseQueryConditionBO.getPager();
                    pager.setPageIndex(pageIndex);
                    OrderDetailBO orderDetail = orderDetailService.queryDetail(uid, baseQueryConditionBO, true);
                    if (pageIndex == 1 && !hasTitle) {
                        dataSource.add(new ArrayList<>(orderDetail.getTitle()));
                        hasTitle = true;
                    }
                    pageIndex++;
                    isEmpty = CollectionUtils.isEmpty(orderDetail.getData());
                    isLastPage = isEmpty || orderDetail.getData().size() < pageSize;
                    if (!isEmpty) {
                        dataSource.addAll(orderDetail.getData());
                    }
                    PoiCustomExcelUtils.createExcelBigDataMulitBatchObj(workbook, dataSource, startRow, sheetName, maxWidth);
                    startRow += dataSource.size();
                }
                startTimeTemp = DateUtil.offsetTime(endTimeTemp, 1, Calendar.DAY_OF_YEAR);
                endTimeTemp = DateUtil.offsetTime(endTimeTemp, MAX_INTERVAL_DAYS_DEAFULT, Calendar.DAY_OF_YEAR);
                if (endTimeTemp.compareTo(endTime) > 0 && startTimeTemp.compareTo(endTime) <= 0) {
                    startTimeTemp = startTimeTemp.compareTo(endTime) > 0 ? endTime : startTimeTemp;
                    endTimeTemp = endTimeTemp.compareTo(endTime) > 0 ? endTime : endTimeTemp;
                    timeFilterTypeInfo.setStartTime(startTimeTemp);
                    timeFilterTypeInfo.setEndTime(endTimeTemp);
                    pageIndex = 1;
                    isLastPage = false;//最后一页
                    while (!isLastPage) {
                        List<List<Object>> dataSource = new ArrayList<>();
                        Pager pager = baseQueryConditionBO.getPager();
                        pager.setPageIndex(pageIndex);
                        OrderDetailBO orderDetail = orderDetailService.queryDetail(uid, baseQueryConditionBO, true);
                        if (pageIndex == 1 && !hasTitle) {
                            dataSource.add(new ArrayList<>(orderDetail.getTitle()));
                            hasTitle = true;
                        }
                        pageIndex++;
                        isEmpty = CollectionUtils.isEmpty(orderDetail.getData());
                        isLastPage = isEmpty || orderDetail.getData().size() < pageSize;
                        if (!isEmpty) {
                            dataSource.addAll(orderDetail.getData());
                        }
                        PoiCustomExcelUtils.createExcelBigDataMulitBatchObj(workbook, dataSource, startRow, sheetName, maxWidth);
                        startRow += dataSource.size();
                    }
                    break;
                }
            }
        }
        PoiCustomExcelUtils.createExcelMulitBatch(hssfWorkbook, filterList, 1, SharkUtils.get("Report.SelectResult1", lang), maxWidth);
        log.info(LOG_TITLE, String.format("createBigDataMulitBatchByDate end, data size : %d , take time : %s", startRow, stopWatch.getTime()));
        return workbook;*/
        return null;
    }

    private String getSheetName(String lang) {
        return "酒店夜审数据";
    }

}
