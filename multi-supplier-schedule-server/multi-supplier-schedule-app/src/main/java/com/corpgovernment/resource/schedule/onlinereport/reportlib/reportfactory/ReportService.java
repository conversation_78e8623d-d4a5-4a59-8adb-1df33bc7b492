package com.corpgovernment.resource.schedule.onlinereport.reportlib.reportfactory;

import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.ReportBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.cglib.seriallizer.JacksonUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.CommonConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpreportDownloadTaskPO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TimeFilterTypeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.task.CorpreportDownloadTaskService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DateUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.JsonUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.ReportDataNewAdaptorService;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import onlinereport.enums.ReportTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.*;

/*
 * <AUTHOR>
 * @date 2020/9/30 16:55
 * @Desc
 */
@Service
@Slf4j
public class ReportService {

    @Autowired
    private IMyReportFactory myReportFactory;

    @Autowired
    private ReportDataNewAdaptorService adaptorService;

    public Workbook createWorkBook(TaskEntity taskEntity) throws Exception {
        long startTime = System.currentTimeMillis();
        IReport report = myReportFactory.createReportInstance(taskEntity.getReportType());
        log.info("exportReport执行createWorkBook方法myReportFactory.createReportInstance耗时：{}",System.currentTimeMillis()-startTime);
        startTime = System.currentTimeMillis();
        Workbook workbook = report.create(taskEntity);
        log.info("exportReport执行createWorkBook方法report.create耗时：{}",System.currentTimeMillis()-startTime);
        return workbook;
    }

    public void exportReport(BaseQueryConditionBO baseQueryConditionBO, HttpServletResponse response, BaseUserInfo baseUserInfo) throws Exception {
        long startTime = System.currentTimeMillis();
        String reportCondition = JsonUtils.objectToString(baseQueryConditionBO);
        String reportId = baseQueryConditionBO.getBaseQueryCondition() == null ? "" : baseQueryConditionBO.getBaseQueryCondition().getReportId();

        // 若是oneKeyPPT、oneKeyReport，则特殊处理请求参数
//        if (ReportTypeEnum.OneKeyExportPPT.equals(getReportType(reportId)) || ReportTypeEnum.OneKeyExportReportNew.equals(getReportType(reportId))) {
//            String handleEndTime = this.handleEndTime(baseQueryConditionBO.getBaseQueryCondition().getEndTime());
//            baseQueryConditionBO.getBaseQueryCondition().setEndTime(handleEndTime);
//        }
        ReportTypeEnum reportTypeEnum = getReportType(reportId);
        Map temp = new HashMap();
        Map<String, String> extData = baseQueryConditionBO.getExtData();
//        String reportPrefix = getNewReportNamePrefix(baseUserInfo.getUid(), baseQueryConditionBO, baseQueryConditionBO.getLang());
        if (MapUtils.isNotEmpty(extData) && StringUtils.isNotEmpty(extData.get(CommonConst.CARD_ID))) {
            temp.put(CommonConst.MODULE_CARD_NAME, SharkUtils.get(CommonConst.CARD_SHARK_CODE_PREFIX.concat(extData.get(CommonConst.CARD_ID)), baseQueryConditionBO.getLang()));
        }
        String exportFileName = StringUtils.EMPTY;
        if (ReportTypeEnum.listReportGroupExpectLib().stream().noneMatch(item -> item.equals(reportTypeEnum))) {
            // 参数及其他条件校验
            addTaskVerify(baseQueryConditionBO);

            log.info("exportReport执行整理参数及校验耗时：{}",System.currentTimeMillis()-startTime);
            startTime = System.currentTimeMillis();
            String reportPrefix = getNewReportNamePrefix(baseUserInfo.getUid(), baseQueryConditionBO, baseQueryConditionBO.getLang());
            log.info("exportReport执行getNewReportNamePrefix耗时：{}",System.currentTimeMillis()-startTime);
            temp.put(CommonConst.REPORTPREFIX_KEY, reportPrefix);
            exportFileName = reportPrefix;
        } else {
            log.info("exportReport执行整理参数耗时：{}",System.currentTimeMillis()-startTime);
            startTime = System.currentTimeMillis();
            String formFileName = adaptorService.getFileName(baseUserInfo.getUid(), baseQueryConditionBO, baseQueryConditionBO.getLang()) + ".xls";
            log.info("exportReport执行adaptorService.getFileName耗时：{}",System.currentTimeMillis()-startTime);
            temp.put(CommonConst.REPORT_FILE_NAME, formFileName);
            exportFileName = formFileName;
        }

        startTime = System.currentTimeMillis();
        TaskEntity taskEntity = new TaskEntity();
        taskEntity.setReportId(reportId);
        taskEntity.setReportType(reportTypeEnum.getValue());
        taskEntity.setUid(baseUserInfo.getUid());
        taskEntity.setConditionExt(JacksonUtil.serialize(temp));
        taskEntity.setConditions(reportCondition);
        taskEntity.setLang(baseQueryConditionBO.getLang());
        Workbook workbook = createWorkBook(taskEntity);
        log.info("exportReport执行createWorkBook耗时：{}",System.currentTimeMillis()-startTime);
        startTime = System.currentTimeMillis();
        // 浏览器下载
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(exportFileName + ".xls", "UTF-8"));

        try (OutputStream out = response.getOutputStream()) {
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            // Handle the exception appropriately
            throw new Exception("Error while writing the workbook to the output stream", e);
        } finally {
            log.info("exportReport执行createWorkBook耗时：{}",System.currentTimeMillis()-startTime);
            workbook.close();
        }

    }

    private void addTaskVerify(BaseQueryConditionBO baseQueryConditionBO) throws BusinessException {
        if (baseQueryConditionBO.getBaseQueryCondition() == null) {
            throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
        }
        timeVerify(baseQueryConditionBO.getBaseQueryCondition());
        paramVerify(baseQueryConditionBO);
    }

    /**
     * @param reportId
     * @return
     */
    private ReportTypeEnum getReportType(String reportId) {
        ReportTypeEnum reportTypeEnum = null;
        if (StringUtils.equalsIgnoreCase("DetailReport:FltOrderDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:UnUseFltTicketDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:FltTicketRefundDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:FltTicketRebookDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:HotelOrderDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:TrainOrderDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:CarOrderDetails", reportId)) {
            reportTypeEnum = ReportTypeEnum.ReportLibNew;
        } else if (StringUtils.equalsIgnoreCase("DetailReport:CityHotelAnalysis", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:ConsumeOverview", reportId)) {
            reportTypeEnum = ReportTypeEnum.ReportLibAgg;
        } else if (StringUtils.equalsIgnoreCase("DetailReport:OneKeyPPT", reportId)) {
            reportTypeEnum = ReportTypeEnum.OneKeyExportPPT;
        } else if (StringUtils.equalsIgnoreCase("DetailReport:OneKeyReport", reportId)) {
            reportTypeEnum = ReportTypeEnum.OneKeyExportReportNew;
        } else if (StringUtils.equalsIgnoreCase("DetailReport:UidOrderDetails", reportId)) {
            reportTypeEnum = ReportTypeEnum.ReportLibNewUid;
        } else if (StringUtils.equalsIgnoreCase("DetailReport:HotelOrderAuditDetails", reportId)) {
            reportTypeEnum = ReportTypeEnum.ReportLibHtlOrderAudit;
        }
        return reportTypeEnum;
    }

    public String getNewReportNamePrefix(String uid, BaseQueryConditionBO baseQueryConditionBO, String lang) {
        String reportPrefix = null;
        String pre_prefix = null;
        try {
            pre_prefix = TenantContext.getTenantId();
            String timeSufix = getReportTimeSufix(baseQueryConditionBO.getBaseQueryCondition(), baseQueryConditionBO.getBaseQueryCondition().getReportId());
            String reportName = CorpreportDownloadTaskService.getReportName(lang, baseQueryConditionBO.getBaseQueryCondition().getReportId());
            if (StringUtils.isNotEmpty(pre_prefix)) {
                reportPrefix = String.format("%s-%s_%s", pre_prefix.toUpperCase(), reportName, timeSufix);
            } else {
                reportPrefix = String.format("%s_%s", reportName, timeSufix);
            }
        } catch (Exception e) {
            log.warn("CorpreportDownloadTaskService " + "getReportName error", e);
        }
        return reportPrefix;
    }

    /**
     * 获得reporName时间后缀
     *
     * @param baseQueryCondition
     * @param reportId
     * @return
     */
    private String getReportTimeSufix(BaseQueryCondition baseQueryCondition, String reportId) {
        String reportTimeSufix = StringUtils.EMPTY;
        if (StringUtils.equalsIgnoreCase("DetailReport:ConsumeOverview", reportId)) {
            reportTimeSufix = getReportTimeSufix(baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime());
        } else if (StringUtils.equalsIgnoreCase("DetailReport:OneKeyPPT", reportId)) {
            reportTimeSufix = getReportPPTTimeSufix(baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime());
        } else if (StringUtils.equalsIgnoreCase("DetailReport:OneKeyReport", reportId)) {
            reportTimeSufix = getReportOneKeyReportTimeSufix(baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime());
        } else {
            reportTimeSufix = getReportTimeSufix(baseQueryCondition.getTimeFilterList());
        }
        return reportTimeSufix;
    }

    /**
     * 获得reporName时间后缀
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static String getReportTimeSufix(String startTime, String endTime) {
        String reportSufix = StringUtils.EMPTY;
        String startStr = startTime.replace("-", "");
        String endStr = endTime.replace("-", "");
        reportSufix = String.format("%s-%s", startStr, endStr);
        return reportSufix;
    }

    public static String getReportPPTTimeSufix(String startTime, String endTime) {
        String reportSufix = StringUtils.EMPTY;
        reportSufix = String.format("%s-%s", startTime, endTime);
        return reportSufix;
    }

    public static String getReportOneKeyReportTimeSufix(String startTime, String endTime) {
        return startTime.replace("-", "");
    }

    /**
     * 获得reporName时间后缀
     *
     * @param timeFilterTypeInfoList
     * @return
     */
    public static String getReportTimeSufix(List<TimeFilterTypeInfo> timeFilterTypeInfoList) {
        String reportSufix = StringUtils.EMPTY;
        TimeFilterTypeInfo timeFilterTypeInfo = null;
        Optional optional1 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "orderdate"))
                .findFirst();
        Optional optional2 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "dealdate"))
                .findFirst();
        Optional optional3 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "usedate"))
                .findFirst();
        if (optional2.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional2.get();
        } else if (optional1.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional1.get();
        } else if (optional3.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional3.get();
        }
        String startStr = timeFilterTypeInfo.getStartTime().replace("-", "");
        String endStr = timeFilterTypeInfo.getEndTime().replace("-", "");
        reportSufix = String.format("%s-%s", startStr, endStr);
        return reportSufix;
    }


    /**
     * 时间参数校验
     *
     * @param baseQueryCondition
     * @throws BusinessException
     */
    public static void timeVerify(BaseQueryCondition baseQueryCondition) throws BusinessException {
        if (StringUtils.equalsIgnoreCase("DetailReport:ConsumeOverview", baseQueryCondition.getReportId())
                || StringUtils.equalsIgnoreCase("DetailReport:OneKeyPPT", baseQueryCondition.getReportId())
                || StringUtils.equalsIgnoreCase("DetailReport:OneKeyReport", baseQueryCondition.getReportId())) {
            if (StringUtils.isEmpty(baseQueryCondition.getStartTime()) && StringUtils.isEmpty(baseQueryCondition.getEndTime())) {
                throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            }
            if (StringUtils.equalsIgnoreCase("DetailReport:OneKeyPPT", baseQueryCondition.getReportId())
                    || StringUtils.equalsIgnoreCase("DetailReport:OneKeyReport", baseQueryCondition.getReportId())) {
                int intervalMonths = DateUtil.getMonthNum(baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime());
                // DetailReport:OneKeyReport endTime可以为空，startTime不能为空，endTime为空时，startTime与当前时间差不能超过24月 todo
                if (intervalMonths + 1 > 24) {
                    throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
                }
            }
        } else {
            List<TimeFilterTypeInfo> timeFilterTypeInfoList = baseQueryCondition.getTimeFilterList();
            Optional optional1 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "orderdate"))
                    .findFirst();
            Optional optional2 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "dealdate"))
                    .findFirst();
            Optional optional3 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "usedate"))
                    .findFirst();
            if (!optional1.isPresent() && !optional2.isPresent() && !optional3.isPresent()) {
                throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            }
        }
    }

    public static void paramVerify(BaseQueryConditionBO baseQueryConditionBO) throws BusinessException {
        BaseQueryCondition baseQueryCondition = baseQueryConditionBO.getBaseQueryCondition();
        if (StringUtils.equalsIgnoreCase("DetailReport:UidOrderDetails", baseQueryCondition.getReportId())) {
            if (CollectionUtils.isEmpty(baseQueryConditionBO.getUsers()) && CollectionUtils.isEmpty(baseQueryConditionBO.getPassengers())) {
                throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            }
        }
    }
}
