//package com.corpgovernment.resource.schedule.onlinereport.riskorder;
//
//
//import com.ctrip.corp.onlinereportweb.commmon.constant.DateFormatConst;
//import com.ctrip.corp.onlinereportweb.commmon.enums.PageErrorCodeEnum;
//import com.ctrip.corp.onlinereportweb.commmon.enums.reportlib.*;
//import com.ctrip.corp.onlinereportweb.commmon.helper.ChineseLanguageConfig;
//import com.ctrip.corp.onlinereportweb.commmon.utils.OrpGsonUtils;
//import com.ctrip.corp.onlinereportweb.commmon.utils.SharkUtils;
//import com.ctrip.corp.onlinereportweb.domain.bo.common.BusinessException;
//import com.ctrip.corp.onlinereportweb.domain.rpc.ICorpOnlineReportPlatformService;
//import com.ctrip.corp.onlinereportweb.domainNew.entity.ChartExcelEntity;
//import com.ctrip.corp.onlinereportweb.domainreport.adaptor.biz.AbstractGenralExportDataService;
//import com.ctrip.corp.onlinereportweb.domainreport.bo.BaseQueryConditionBO;
//import com.ctrip.framework.clogging.agent.log.ILog;
//import com.ctrip.framework.clogging.agent.log.LogManager;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.time.DateFormatUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.stream.Collectors;
//
//import static java.math.BigDecimal.ROUND_HALF_UP;
//
///**
// * <AUTHOR>
// * @date ：Created in 2022/4/12 18:20
// * @description：
// * @modified By：
// * @version: $
// */
//@Service
//public class RiskOrderDetailService extends AbstractGenralExportDataService {
//
//    private ILog log = LogManager.getLogger(RiskOrderDetailService.class);
//
//    private String LOG_TITLE = this.getClass().getSimpleName();
//
//    private final int RISK_ORDER_DOWNLOAD_MAX_RECORDS = 100000;
//
//    @Autowired
//    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;
//
//    @Autowired
//    private RiskOrderDetailColumnService customColumnService;
//
//    /**
//     * 风险订单明细 机票
//     *
//     * @param baseCondition
//     * @return
//     * @throws BusinessException
//     */
//    public Map getRiskOrderDetail(BaseQueryConditionBO baseCondition) throws BusinessException {
//
//        OnlineReportRiskOrderDetailRequest request = mapRequest(baseCondition);
//        BusinessException businessException;
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "FLT_REFUND")) {
//            OnlineReportFlightRiskOrderDetailResponse responseType = getFlightDetail(request);
//            if (responseType != null && responseType.getResponseCode() == 20000) {
//                List<FlightRiskOrderDetail> orderDetails = responseType.getRiskOrders();
//                orderDetails.forEach(d -> {
//                    d.setWarningLevel(
//                            SharkUtils.get(
//                                    FlightRiskWarningLevel.getWarningLevelByCode(d.getWarningLevel()).getName(),
//                                    baseCondition.getLang())
//                    );
//                    d.setTicketStatus(
//                            SharkUtils.get(
//                                    FlightTicketStatusEnEnum.getEnumByCode(d.getTicketStatus()).getSharkKey(),
//                                    baseCondition.getLang())
//                    );
//                    d.setReasonType(
//                            SharkUtils.get("RiskOrder.FltReasonType1",
//                                    baseCondition.getLang())
//                    );
//                    if (StringUtils.isNotBlank(d.getUidIsvalid())) {
//                        d.setUidIsvalid(
//                                SharkUtils.get(d.getUidIsvalid().equalsIgnoreCase("f") ? "Users.ValidFalse" : "Users.ValidTrue",
//                                        baseCondition.getLang())
//                        );
//                    } else {
//                        d.setUidIsvalid("--");
//                    }
//
//                    if (StringUtils.isNotBlank(d.getPassengerIsvalid())) {
//                        d.setPassengerIsvalid(
//                                SharkUtils.get(d.getPassengerIsvalid().equalsIgnoreCase("f") ? "Users.ValidFalse" : "Users.ValidTrue",
//                                        baseCondition.getLang())
//                        );
//                    } else {
//                        d.setPassengerIsvalid("--");
//                    }
//                });
//                Map<String, Object> data = new HashMap<>();
//                data.put("bodyList", OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(orderDetails), List.class));
//                data.put("headerData", flightDetailHeader(baseCondition.getLang(), baseCondition.getBaseQueryCondition().getUid()));
//                data.put("pager", OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(responseType.page), Map.class));
//                return data;
//            }
//            if (Objects.isNull(responseType)) {
//                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
//            } else {
//                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
//            }
//            throw businessException;
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "HTL_CASH_OUT")) {
//            OnlineReportHotelRiskOrderDetailResponse responseType = getHotelDetail(request);
//            if (responseType != null && responseType.getResponseCode() == 20000) {
//                List<HotelRiskOrderDetail> orderDetails = responseType.getRiskOrders();
//
//                orderDetails.forEach(d -> {
//                    d.setReasonType(
//                            SharkUtils.get("RiskOrder.HtlCashout1",
//                                    baseCondition.getLang())
//                    );
//                    if (StringUtils.isNotEmpty(d.getWarningLevelV2())) {
//                        d.setWarningLevelV2(getHtlRiskLevelDesc(d.getWarningLevelV2(), baseCondition.getLang()));
//                    }
//                    if (StringUtils.isNotBlank(d.getUidIsvalid())) {
//                        d.setUidIsvalid(
//                                SharkUtils.get(d.getUidIsvalid().equalsIgnoreCase("f") ? "Users.ValidFalse" : "Users.ValidTrue",
//                                        baseCondition.getLang())
//                        );
//                    } else {
//                        d.setUidIsvalid("--");
//                    }
//                });
//
//                List bodyList = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(orderDetails), List.class);
//                bodyList.forEach(
//                        d -> {
//                            Double priceUseRatio = (Double) ((Map) d).get("priceUseRatio");
//                            Double roomQuantityContribution = (Double) ((Map) d).get("roomQuantityContribution");
//
//                            if (priceUseRatio == null) {
//                                ((Map) d).put("priceUseRatio", "--");
//                            } else {
//                                BigDecimal priceUseRatioValue = new BigDecimal(priceUseRatio);
//                                ((Map) d).put("priceUseRatio", priceUseRatioValue.movePointRight(2).setScale(2, ROUND_HALF_UP) + "%");
//                            }
//
//                            if (roomQuantityContribution == null) {
//                                ((Map) d).put("roomQuantityContribution", "--");
//                            } else {
//                                BigDecimal roomQuantityContributionValue = new BigDecimal(roomQuantityContribution);
//                                ((Map) d).put("roomQuantityContribution", roomQuantityContributionValue.movePointRight(2).setScale(2, ROUND_HALF_UP) + "%");
//                            }
//                        }
//                );
//
//                Map<String, Object> data = new HashMap<>();
//                data.put("bodyList", bodyList);
//                data.put("headerData", hotelDetailHeader(baseCondition.getLang(), baseCondition.getBaseQueryCondition().getUid()));
//                data.put("pager", OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(responseType.page), Map.class));
//                return data;
//            }
//            if (Objects.isNull(responseType)) {
//                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
//            } else {
//                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
//            }
//            throw businessException;
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "CAR_DOUBLE_BOOK")) {
//            OnlineReportCarRiskOrderDetailResponse responseType = getCarDetail(request);
//            if (responseType != null && responseType.getResponseCode() == 20000) {
//                List<CarRiskOrderDetail> orderDetails = responseType.getRiskOrders();
//                orderDetails.forEach(
//                        d -> {
//                            if (StringUtils.isNotBlank(d.getUidIsvalid())) {
//                                d.setUidIsvalid(
//                                        SharkUtils.get(d.getUidIsvalid().equalsIgnoreCase("f") ? "Users.ValidFalse" : "Users.ValidTrue",
//                                                baseCondition.getLang())
//                                );
//                            } else {
//                                d.setUidIsvalid("--");
//                            }
//
//                            d.setOtherUidAvgAmount(d.getOtherUidAvgAmount().setScale(2, ROUND_HALF_UP));
//                            d.setUidAmount(d.getUidAmount().setScale(2, ROUND_HALF_UP));
//                            d.setAmount(d.getAmount().setScale(2, ROUND_HALF_UP));
//                            d.setSameDriverAmount(d.getSameDriverAmount().setScale(2, ROUND_HALF_UP));
//                        }
//                );
//
//                List bodyList = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(orderDetails), List.class);
//                bodyList.forEach(
//                        d -> {
//                            Double highAmountCntRatio = (Double) ((Map) d).get("highAmountCntRatio");
//                            Double sameDriverUidRatio = (Double) ((Map) d).get("sameDriverUidRatio");
//                            Double sameUidDriverRatio = (Double) ((Map) d).get("sameUidDriverRatio");
//
//                            if (highAmountCntRatio == null) {
//                                ((Map) d).put("highAmountCntRatio", "--");
//                            } else {
//                                BigDecimal highAmountCntRatioValue = new BigDecimal(highAmountCntRatio);
//                                ((Map) d).put("highAmountCntRatio", highAmountCntRatioValue.movePointRight(2).setScale(2, ROUND_HALF_UP) + "%");
//                            }
//
//                            BigDecimal sameDriverUidRatioValue = new BigDecimal(sameDriverUidRatio);
//                            ((Map) d).put("sameDriverUidRatio", sameDriverUidRatioValue.movePointRight(2).setScale(2, ROUND_HALF_UP) + "%");
//
//                            BigDecimal sameUidDriverRatioValue = new BigDecimal(sameUidDriverRatio);
//                            ((Map) d).put("sameUidDriverRatio", sameUidDriverRatioValue.movePointRight(2).setScale(2, ROUND_HALF_UP) + "%");
//                        }
//                );
//
//                Map<String, Object> data = new HashMap<>();
//                data.put("bodyList", bodyList);
//                data.put("headerData", carDetailHeader(baseCondition.getLang()));
//                data.put("pager", OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(responseType.page), Map.class));
//                return data;
//            }
//            if (Objects.isNull(responseType)) {
//                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
//            } else {
//                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
//            }
//            throw businessException;
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "HTL_UNDER_STAY")) {
//            OnlineReportHotelUnderStayRiskOrderDetailResponse responseType = getHotelUnderStayDetail(request);
//            if (responseType != null && responseType.getResponseCode() == 20000) {
//                List<HotelUnderStayRiskOrderDetail> orderDetails = responseType.getRiskOrders();
//                orderDetails.forEach(
//                        d -> {
//                            if (StringUtils.isNotBlank(d.getUidIsvalid())) {
//                                d.setUidIsvalid(
//                                        SharkUtils.get(d.getUidIsvalid().equalsIgnoreCase("f") ? "Users.ValidFalse" : "Users.ValidTrue",
//                                                baseCondition.getLang())
//                                );
//                            } else {
//                                d.setUidIsvalid("--");
//                            }
//
//                            // 同住人是否离店
//                            d.setIsOtherClientUnderStay(
//                                    SharkUtils.get(
//                                            Integer.parseInt(d.getIsOtherClientUnderStay()) == 0 ? "lbl_F" : "lbl_T",
//                                            baseCondition.getLang()
//                                    )
//                            );
//                        }
//                );
//                Map<String, Object> data = new HashMap<>();
//                data.put("bodyList", OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(orderDetails), List.class));
//                data.put("headerData", hotelUnderStayDetailHeader(baseCondition.getLang(), baseCondition.getBaseQueryCondition().getUid()));
//                data.put("pager", OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(responseType.page), Map.class));
//                return data;
//            }
//            if (Objects.isNull(responseType)) {
//                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
//            } else {
//                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
//            }
//            throw businessException;
//        }
//
//        return null;
//    }
//
//    public List<ChartExcelEntity> getRiskOrderDetailExcel(BaseQueryConditionBO baseCondition) {
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "FLT_REFUND")) {
//            return getFlightDetailExcel(baseCondition);
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "HTL_CASH_OUT")) {
//            return getHotelDetailExcel(baseCondition);
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "CAR_DOUBLE_BOOK")) {
//            return getCarDetailExcel(baseCondition);
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "HTL_UNDER_STAY")) {
//            return getHtlUnderStayDetailExcel(baseCondition);
//        }
//        return new ArrayList<>();
//    }
//
//    public List<ChartExcelEntity> getFlightDetailExcel(BaseQueryConditionBO baseCondition) {
//        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
//        OnlineReportRiskOrderDetailRequest request = mapRequest(baseCondition);
//        request.page.setPageIndex(1);
//        request.page.setPageSize(RISK_ORDER_DOWNLOAD_MAX_RECORDS);
//        OnlineReportFlightRiskOrderDetailResponse responseType = getFlightDetail(request);
//        if (responseType != null && responseType.getResponseCode() == 20000) {
//            List<FlightRiskOrderDetail> orderDetails = responseType.getRiskOrders();
//            List<Map> headerKeys = flightDetailHeader(baseCondition.getLang(), baseCondition.getBaseQueryCondition().getUid());
//            // FlightRiskOrderFieldEnum[] enums = FlightRiskOrderFieldEnum.values();
//            List<String> columns = new ArrayList<>();
//            List<String> headers = new ArrayList<>();
//            // 未处理 - 已处理 - 无需处理
//            List<List<String>> unOperateData = new ArrayList();
//            List<List<String>> operatedData = new ArrayList();
//            List<List<String>> noNeedOperatedData = new ArrayList();
//            for (Map headerKey: headerKeys) {
//                columns.add((String) headerKey.get("headerKey"));
//                headers.add((String) headerKey.get("headerValue"));
//                // headers.add(SharkUtils.get(en.getHeaderKey(), baseCondition.getLang()));
//            }
//            for (FlightRiskOrderDetail detail: orderDetails) {
//                Map var1 = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(detail), Map.class);
//                List var2 = new ArrayList();
//                for (String column: columns) {
//                    // 风险标识
////                    if (column.equals(FlightRiskOrderFieldEnum.WARNING_LEVEL.getName())) {
////                        var2.add(
////                            SharkUtils.get(
////                                    FlightRiskWarningLevel.getWarningLevelByCode((String) var1.get(column)).getName(),
////                                    baseCondition.getLang()
////                            )
////                        );
////                        continue;
////                    }
//
//                    // 票号状态
//                    if (column.equals(FlightRiskOrderFieldEnum.TICKET_STATUS.getName())) {
//                        var2.add(
//                                SharkUtils.get(
//                                        FlightTicketStatusEnEnum.getEnumByCode((String) var1.get(column)).getSharkKey(),
//                                        baseCondition.getLang()
//                                )
//                        );
//                        continue;
//                    }
//
//                    // 处理状态
//                    if (column.equals(FlightRiskOrderFieldEnum.OPERATE_STATUS.getName())) {
//                        var2.add(
//                                SharkUtils.get(
//                                        operateStatus(((Double) var1.get(column)).intValue()),
//                                        baseCondition.getLang()
//                                )
//                        );
//                        continue;
//                    }
//
//                    // 预订人-机票乘机人
//                    if (column.equals(FlightRiskOrderFieldEnum.UID_ISVALID.getName())) {
//                        if (StringUtils.isNotBlank(((String) var1.get(column)))) {
//                            var2.add(
//                                    SharkUtils.get(
//                                            ((String) var1.get(column)).equalsIgnoreCase("f") ? "Users.ValidFalse" : "Users.ValidTrue",
//                                            baseCondition.getLang()
//                                    )
//                            );
//                        } else {
//                            var2.add("--");
//                        }
//                        continue;
//                    }
//
//                    if (column.equals(FlightRiskOrderFieldEnum.PASSENGER_ISVALID.getName())) {
//                        if (StringUtils.isNotBlank(((String) var1.get(column)))) {
//                            var2.add(
//                                    SharkUtils.get(
//                                            ((String) var1.get(column)).equalsIgnoreCase("f") ? "Users.ValidFalse" : "Users.ValidTrue",
//                                            baseCondition.getLang()
//                                    )
//                            );
//                        } else {
//                            var2.add("--");
//                        }
//                        continue;
//                    }
//                    if (column.equals(FlightRiskOrderFieldEnum.REASON_TYPE.getName())) {
//                        var2.add(
//                                SharkUtils.get("RiskOrder.FltReasonType1",
//                                        baseCondition.getLang()
//                                )
//                        );
//                        continue;
//                    }
//
//                    var2.add(var1.get(column) == null ? "" : var1.get(column));
//                }
//                if (detail.getOperateStatus() == 0) {
//                    unOperateData.add(var2);
//                } else if (detail.getOperateStatus() == 1) {
//                    operatedData.add(var2);
//                } else {
//                    noNeedOperatedData.add(var2);
//                }
//            }
//            Map map = getConditionContent(request.getBasecondition().getUid(), request.getBasecondition(), baseCondition.getLang());
//            unOperateData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//            operatedData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//            noNeedOperatedData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//
//            unOperateData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//            operatedData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//            noNeedOperatedData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//
//            unOperateData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//            operatedData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//            noNeedOperatedData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//
//            ChartExcelEntity unOperateSheet = new ChartExcelEntity();
//            unOperateSheet.setSheetNum(0);
//            unOperateSheet.setHeaders(headers);
//            unOperateSheet.setSheetTitle(SharkUtils.get("RiskOrder.OperateNot", baseCondition.getLang()));
//            unOperateSheet.setData(unOperateData);
//            chartExcelEntityList.add(unOperateSheet);
//
//            ChartExcelEntity operatedSheet = new ChartExcelEntity();
//            operatedSheet.setSheetNum(1);
//            operatedSheet.setHeaders(headers);
//            operatedSheet.setSheetTitle(SharkUtils.get("RiskOrder.OperateDone", baseCondition.getLang()));
//            operatedSheet.setData(operatedData);
//            chartExcelEntityList.add(operatedSheet);
//
//            ChartExcelEntity noNeedOperatedSheet = new ChartExcelEntity();
//            noNeedOperatedSheet.setSheetNum(2);
//            noNeedOperatedSheet.setHeaders(headers);
//            noNeedOperatedSheet.setSheetTitle(SharkUtils.get("RiskOrder.OperateIgnore", baseCondition.getLang()));
//            noNeedOperatedSheet.setData(noNeedOperatedData);
//            chartExcelEntityList.add(noNeedOperatedSheet);
//        }
//        return chartExcelEntityList;
//    }
//
//    public List<ChartExcelEntity> getHotelDetailExcel(BaseQueryConditionBO baseCondition) {
//        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
//        OnlineReportRiskOrderDetailRequest request = mapRequest(baseCondition);
//        request.page.setPageIndex(1);
//        request.page.setPageSize(RISK_ORDER_DOWNLOAD_MAX_RECORDS);
//        OnlineReportHotelRiskOrderDetailResponse responseType = getHotelDetail(request);
//        if (responseType != null && responseType.getResponseCode() == 20000) {
//            List<HotelRiskOrderDetail> orderDetails = responseType.getRiskOrders();
//            // HotelRiskOrderFieldEnum[] enums = HotelRiskOrderFieldEnum.values();
//            List<Map> headerKeys = hotelDetailHeader(baseCondition.getLang(), baseCondition.getBaseQueryCondition().getUid());
//            List<String> columns = new ArrayList<>();
//            List<String> headers = new ArrayList<>();
//            // 未处理 - 已处理 - 无需处理
//            List<List<String>> unOperateData = new ArrayList();
//            List<List<String>> operatedData = new ArrayList();
//            List<List<String>> noNeedOperatedData = new ArrayList();
//            for (Map headerKey: headerKeys) {
//                columns.add((String) headerKey.get("headerKey"));
//                headers.add((String) headerKey.get("headerValue"));
//                // headers.add(SharkUtils.get(en.getHeaderKey(), baseCondition.getLang()));
//            }
//            for (HotelRiskOrderDetail detail: orderDetails) {
//                Map var1 = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(detail), Map.class);
//                List var2 = new ArrayList();
//                for (String column: columns) {
//                    // 处理状态
//                    if (column.equals(HotelRiskOrderFieldEnum.OPERATE_STATUS.getName())) {
//                        var2.add(
//                                SharkUtils.get(
//                                        operateStatus(((Double) var1.get(column)).intValue()),
//                                        baseCondition.getLang()
//                                )
//                        );
//                        continue;
//                    }
//
//                    // 预订人是否在职
//                    if (column.equals(HotelRiskOrderFieldEnum.UID_ISVALID.getName())) {
//                        if (StringUtils.isNotBlank(((String) var1.get(column)))) {
//                            var2.add(
//                                    SharkUtils.get(
//                                            ((String) var1.get(column)).equalsIgnoreCase("f") ? "Users.ValidFalse" : "Users.ValidTrue",
//                                            baseCondition.getLang()
//                                    )
//                            );
//                        } else {
//                            var2.add("--");
//                        }
//                        continue;
//                    }
//
//                    // 风险等级
//                    if (column.equals(HotelRiskOrderFieldEnum.WARNING_LEVEL_V2.getName())) {
//                        if (StringUtils.isNotBlank(((String) var1.get(column)))) {
//                            var2.add(
//                                    getHtlRiskLevelDesc((String) var1.get(column),baseCondition.getLang())
//                            );
//                        } else {
//                            var2.add("--");
//                        }
//                        continue;
//                    }
//
//                    if (column.equals(FlightRiskOrderFieldEnum.REASON_TYPE.getName())) {
//                        var2.add(
//                                SharkUtils.get("RiskOrder.HtlCashout1",
//                                        baseCondition.getLang()
//                                )
//                        );
//                        continue;
//                    }
//
//                    // 差标使用率展示百分数
//                    if (column.equals(HotelRiskOrderFieldEnum.PRICE_USE_RATIO.getName())
//                        || column.equals(HotelRiskOrderFieldEnum.ROOM_QUANTITY_CONTRIBUTION.getName())
//                    ) {
//                        if ((var1.get(column)) != null) {
//                            var2.add(
//                                    BigDecimal.valueOf((Double) var1.get(column)).movePointRight(2).setScale(2, ROUND_HALF_UP) + "%"
//                            );
//                        } else {
//                            var2.add("--");
//                        }
//                        continue;
//                    }
//
//                    var2.add(var1.get(column) == null ? "" : var1.get(column));
//                }
//                if (detail.getOperateStatus() == 0) {
//                    unOperateData.add(var2);
//                } else if (detail.getOperateStatus() == 1) {
//                    operatedData.add(var2);
//                } else {
//                    noNeedOperatedData.add(var2);
//                }
//            }
//
//            // 后续填充
//            Map map = getConditionContent(request.getBasecondition().getUid(), request.getBasecondition(), baseCondition.getLang());
//            unOperateData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//            operatedData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//            noNeedOperatedData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//
//            unOperateData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//            operatedData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//            noNeedOperatedData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//
//            unOperateData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//            operatedData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//            noNeedOperatedData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//
//            ChartExcelEntity sheet = new ChartExcelEntity();
//            sheet.setSheetNum(0);
//            sheet.setHeaders(headers);
//            sheet.setSheetTitle(SharkUtils.get("RiskOrder.OperateNot", baseCondition.getLang()));
//            sheet.setData(unOperateData);
//            chartExcelEntityList.add(sheet);
//
//            ChartExcelEntity sheet1 = new ChartExcelEntity();
//            sheet1.setSheetNum(1);
//            sheet1.setHeaders(headers);
//            sheet1.setSheetTitle(SharkUtils.get("RiskOrder.OperateDone", baseCondition.getLang()));
//            sheet1.setData(operatedData);
//            chartExcelEntityList.add(sheet1);
//
//            ChartExcelEntity sheet2 = new ChartExcelEntity();
//            sheet2.setSheetNum(2);
//            sheet2.setHeaders(headers);
//            sheet2.setSheetTitle(SharkUtils.get("RiskOrder.OperateIgnore", baseCondition.getLang()));
//            sheet2.setData(noNeedOperatedData);
//            chartExcelEntityList.add(sheet2);
//        }
//        return chartExcelEntityList;
//    }
//
//    public List<ChartExcelEntity> getCarDetailExcel(BaseQueryConditionBO baseCondition) {
//        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
//        OnlineReportRiskOrderDetailRequest request = mapRequest(baseCondition);
//        request.page.setPageIndex(1);
//        request.page.setPageSize(RISK_ORDER_DOWNLOAD_MAX_RECORDS);
//        OnlineReportCarRiskOrderDetailResponse responseType = getCarDetail(request);
//        if (responseType != null && responseType.getResponseCode() == 20000) {
//            List<CarRiskOrderDetail> orderDetails = responseType.getRiskOrders();
//            CarRiskOrderFieldEnum[] enums = CarRiskOrderFieldEnum.values();
//            List<String> columns = new ArrayList<>();
//            List<String> headers = new ArrayList<>();
//            // 未处理 - 已处理 - 无需处理
//            List<List<String>> unOperateData = new ArrayList();
//            List<List<String>> operatedData = new ArrayList();
//            List<List<String>> noNeedOperatedData = new ArrayList();
//
//            for (CarRiskOrderFieldEnum en: enums) {
//                columns.add(en.getName());
//                headers.add(SharkUtils.get(en.getHeaderKey(), baseCondition.getLang()));
//            }
//            for (CarRiskOrderDetail detail: orderDetails) {
//                detail.setOtherUidAvgAmount(detail.getOtherUidAvgAmount().setScale(2, ROUND_HALF_UP));
//                detail.setUidAmount(detail.getUidAmount().setScale(2, ROUND_HALF_UP));
//                detail.setAmount(detail.getAmount().setScale(2, ROUND_HALF_UP));
//                detail.setSameDriverAmount(detail.getSameDriverAmount().setScale(2, ROUND_HALF_UP));
//
//                Map var1 = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(detail), Map.class);
//                List var2 = new ArrayList();
//                for (String column: columns) {
//                    // 处理状态
//                    if (column.equals(CarRiskOrderFieldEnum.OPERATE_STATUS.getName())) {
//                        var2.add(
//                                SharkUtils.get(
//                                        operateStatus(((Double) var1.get(column)).intValue()),
//                                        baseCondition.getLang()
//                                )
//                        );
//                        continue;
//                    }
//
//                    // 预订人是否在职
//                    if (column.equals(CarRiskOrderFieldEnum.UID_ISVALID.getName())) {
//                        if (StringUtils.isNotBlank(((String) var1.get(column)))) {
//                            var2.add(
//                                    SharkUtils.get(
//                                            ((String) var1.get(column)).equalsIgnoreCase("f") ? "Users.ValidFalse" : "Users.ValidTrue",
//                                            baseCondition.getLang()
//                                    )
//                            );
//                        } else {
//                            var2.add("--");
//                        }
//                        continue;
//                    }
//
//                    if (column.equals(CarRiskOrderFieldEnum.HIGH_AMOUNT_COUNT_RATIO.getName())
//                        || column.equals(CarRiskOrderFieldEnum.SAME_DRIVER_UID_RATIO.getName())
//                        || column.equals(CarRiskOrderFieldEnum.SAME_UID_DRIVER_RATIO.getName())
//                    ) {
//                        Double value = (Double) var1.get(column);
//                        var2.add(
//                            new BigDecimal(value).movePointRight(2).setScale(2, ROUND_HALF_UP) + "%"
//                        );
//                        continue;
//                    }
//
//                    var2.add(var1.get(column) == null ? "" : var1.get(column));
//                }
//
//                if (detail.getOperateStatus() == 0) {
//                    unOperateData.add(var2);
//                } else if (detail.getOperateStatus() == 1) {
//                    operatedData.add(var2);
//                } else {
//                    noNeedOperatedData.add(var2);
//                }
//            }
//
//            // 后续填充
//            Map map = getConditionContent(request.getBasecondition().getUid(), request.getBasecondition(), baseCondition.getLang());
//            unOperateData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//            operatedData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//            noNeedOperatedData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//
//            unOperateData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//            operatedData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//            noNeedOperatedData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//
//            unOperateData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//            operatedData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//            noNeedOperatedData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//
//            ChartExcelEntity sheet = new ChartExcelEntity();
//            sheet.setSheetNum(0);
//            sheet.setHeaders(headers);
//            sheet.setSheetTitle(SharkUtils.get("RiskOrder.OperateNot", baseCondition.getLang()));
//            sheet.setData(unOperateData);
//            chartExcelEntityList.add(sheet);
//
//            ChartExcelEntity sheet1 = new ChartExcelEntity();
//            sheet1.setSheetNum(1);
//            sheet1.setHeaders(headers);
//            sheet1.setSheetTitle(SharkUtils.get("RiskOrder.OperateDone", baseCondition.getLang()));
//            sheet1.setData(operatedData);
//            chartExcelEntityList.add(sheet1);
//
//            ChartExcelEntity sheet2 = new ChartExcelEntity();
//            sheet2.setSheetNum(2);
//            sheet2.setHeaders(headers);
//            sheet2.setSheetTitle(SharkUtils.get("RiskOrder.OperateIgnore", baseCondition.getLang()));
//            sheet2.setData(noNeedOperatedData);
//            chartExcelEntityList.add(sheet2);
//        }
//        return chartExcelEntityList;
//    }
//
//    public List<ChartExcelEntity> getHtlUnderStayDetailExcel(BaseQueryConditionBO baseCondition) {
//        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
//        OnlineReportRiskOrderDetailRequest request = mapRequest(baseCondition);
//        request.page.setPageIndex(1);
//        request.page.setPageSize(RISK_ORDER_DOWNLOAD_MAX_RECORDS);
//        OnlineReportHotelUnderStayRiskOrderDetailResponse responseType = getHotelUnderStayDetail(request);
//        if (responseType != null && responseType.getResponseCode() == 20000) {
//            List<HotelUnderStayRiskOrderDetail> orderDetails = responseType.getRiskOrders();
//            // HotelRiskUnderStayOrderFieldEnum[] enums = HotelRiskUnderStayOrderFieldEnum.values();
//            List<Map> headerKeys = hotelUnderStayDetailHeader(baseCondition.getLang(), baseCondition.getBaseQueryCondition().getUid());
//            List<String> columns = new ArrayList<>();
//            List<String> headers = new ArrayList<>();
//            // 未处理 - 已处理 - 无需处理
//            List<List<String>> unOperateData = new ArrayList();
//            List<List<String>> operatedData = new ArrayList();
//            List<List<String>> noNeedOperatedData = new ArrayList();
//            for (Map headerKey: headerKeys) {
//                columns.add((String) headerKey.get("headerKey"));
//                headers.add((String) headerKey.get("headerValue"));
//                // headers.add(SharkUtils.get(en.getHeaderKey(), baseCondition.getLang()));
//            }
//            for (HotelUnderStayRiskOrderDetail detail: orderDetails) {
//                Map var1 = OrpGsonUtils.fromToJsonType(OrpGsonUtils.toJsonStr(detail), Map.class);
//                List var2 = new ArrayList();
//                for (String column: columns) {
//                    // 处理状态
//                    if (column.equals(HotelRiskUnderStayOrderFieldEnum.OPERATE_STATUS.getName())) {
//                        var2.add(
//                                SharkUtils.get(
//                                        operateStatus(((Double) var1.get(column)).intValue()),
//                                        baseCondition.getLang()
//                                )
//                        );
//                        continue;
//                    }
//
//                    // 同住人是否离店
//                    if (column.equals(HotelRiskUnderStayOrderFieldEnum.IS_OTHER_CLIENT_UNDER_STAY.getName())) {
//                        var2.add(
//                                SharkUtils.get(
//                                        Integer.parseInt((String) var1.get(column)) == 0 ? "lbl_F" : "lbl_T",
//                                        baseCondition.getLang()
//                                )
//                        );
//                        continue;
//                    }
//
//                    // 预订人是否在职
//                    if (column.equals(HotelRiskUnderStayOrderFieldEnum.UID_ISVALID.getName())) {
//                        if (StringUtils.isNotBlank(((String) var1.get(column)))) {
//                            var2.add(
//                                    SharkUtils.get(
//                                            ((String) var1.get(column)).equalsIgnoreCase("f") ? "Users.ValidFalse" : "Users.ValidTrue",
//                                            baseCondition.getLang()
//                                    )
//                            );
//                        } else {
//                            var2.add("--");
//                        }
//                        continue;
//                    }
//
//                    var2.add(var1.get(column) == null ? "" : var1.get(column));
//                }
//                if (detail.getOperateStatus() == 0) {
//                    unOperateData.add(var2);
//                } else if (detail.getOperateStatus() == 1) {
//                    operatedData.add(var2);
//                } else {
//                    noNeedOperatedData.add(var2);
//                }
//            }
//
//            // 后续填充
//            Map map = getConditionContent(request.getBasecondition().getUid(), request.getBasecondition(), baseCondition.getLang());
//            unOperateData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//            operatedData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//            noNeedOperatedData.add(Arrays.asList(org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY, org.apache.commons.lang.StringUtils.EMPTY));
//
//            unOperateData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//            operatedData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//            noNeedOperatedData.add(Arrays.asList(
//                    SharkUtils.get("Index.TimeRange", baseCondition.getLang()),
//                    request.basecondition.startTime + "~" + request.basecondition.endTime)
//            );
//
//            unOperateData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//            operatedData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//            noNeedOperatedData.addAll(getDataRangeContent(baseCondition.getLang(), map));
//
//            ChartExcelEntity unOperateSheet = new ChartExcelEntity();
//            unOperateSheet.setSheetNum(0);
//            unOperateSheet.setHeaders(headers);
//            unOperateSheet.setSheetTitle(SharkUtils.get("RiskOrder.OperateNot", baseCondition.getLang()));
//            unOperateSheet.setData(unOperateData);
//            chartExcelEntityList.add(unOperateSheet);
//
//            ChartExcelEntity operatedSheet = new ChartExcelEntity();
//            operatedSheet.setSheetNum(1);
//            operatedSheet.setHeaders(headers);
//            operatedSheet.setSheetTitle(SharkUtils.get("RiskOrder.OperateDone", baseCondition.getLang()));
//            operatedSheet.setData(operatedData);
//            chartExcelEntityList.add(operatedSheet);
//
//            ChartExcelEntity noNeedOperatedSheet = new ChartExcelEntity();
//            noNeedOperatedSheet.setSheetNum(2);
//            noNeedOperatedSheet.setHeaders(headers);
//            noNeedOperatedSheet.setSheetTitle(SharkUtils.get("RiskOrder.OperateIgnore", baseCondition.getLang()));
//            noNeedOperatedSheet.setData(noNeedOperatedData);
//            chartExcelEntityList.add(noNeedOperatedSheet);
//        }
//        return chartExcelEntityList;
//    }
//
//    public OnlineReportFlightRiskOrderDetailResponse getFlightDetail(OnlineReportRiskOrderDetailRequest request) {
//        OnlineReportFlightRiskOrderDetailResponse response = null;
//        try {
//            log.info(LOG_TITLE + "-flight", request.toString());
//            response = corpOnlineReportPlatformService.queryRiskFlightOrderDetail(request);
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return response;
//    }
//
//    public OnlineReportHotelRiskOrderDetailResponse getHotelDetail(OnlineReportRiskOrderDetailRequest request) {
//        OnlineReportHotelRiskOrderDetailResponse response = null;
//        try {
//            log.info(LOG_TITLE + "-hotel", request.toString());
//            response = corpOnlineReportPlatformService.queryRiskHotelOrderDetail(request);
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return response;
//    }
//
//    public OnlineReportHotelUnderStayRiskOrderDetailResponse getHotelUnderStayDetail(OnlineReportRiskOrderDetailRequest request) {
//        OnlineReportHotelUnderStayRiskOrderDetailResponse response = null;
//        try {
//            log.info(LOG_TITLE + "-hotel-under-stay", request.toString());
//            response = corpOnlineReportPlatformService.queryRiskHotelUnderStayOrderDetail(request);
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return response;
//    }
//
//    public OnlineReportCarRiskOrderDetailResponse getCarDetail(OnlineReportRiskOrderDetailRequest request) {
//        OnlineReportCarRiskOrderDetailResponse response = null;
//        try {
//            log.info(LOG_TITLE + "-car", request.toString());
//            response = corpOnlineReportPlatformService.queryRiskCarOrderDetail(request);
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return response;
//    }
//
//    public List<Map> flightDetailHeader(String lang, String uid) {
//        List<Map> columns = new ArrayList();
//        try {
//            List<String> customColumns = customColumnService.queryCustomColumns(uid, "FLT_REFUND");
//            FlightRiskOrderFieldEnum[] enums = FlightRiskOrderFieldEnum.values();
//            columns = Arrays.stream(enums)
//                    .filter(e -> customColumns.contains(e.getName()))
//                    .map(e -> new HashMap<String, String>(){{
//                        put("headerKey", e.getName());
//                        put("headerValue", SharkUtils.get(e.getHeaderKey(), lang));}})
//                    .collect(Collectors.toList());
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return columns;
//    }
//
//    public List<Map> hotelDetailHeader(String lang, String uid) {
//        List<Map> columns = new ArrayList();
//        try {
//            List<String> customColumns = customColumnService.queryCustomColumns(uid, "HTL_CASH_OUT");
//            HotelRiskOrderFieldEnum[] enums = HotelRiskOrderFieldEnum.values();
//            columns = Arrays.stream(enums)
//                    .filter(e -> customColumns.contains(e.getName()))
//                    .map(e -> new HashMap<String, String>(){{
//                        put("headerKey", e.getName());
//                        put("headerValue", SharkUtils.get(e.getHeaderKey(), lang));}})
//                    .collect(Collectors.toList());
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return columns;
//    }
//
//    public List<Map> hotelUnderStayDetailHeader(String lang, String uid) {
//        List<Map> columns = new ArrayList();
//        try {
//            List<String> customColumns = customColumnService.queryCustomColumns(uid, "HTL_UNDER_STAY");
//            HotelRiskUnderStayOrderFieldEnum[] enums = HotelRiskUnderStayOrderFieldEnum.values();
//            columns = Arrays.stream(enums)
//                    .filter(e -> customColumns.contains(e.getName()))
//                    .map(e -> new HashMap<String, String>(){{
//                        put("headerKey", e.getName());
//                        put("headerValue", SharkUtils.get(e.getHeaderKey(), lang));}})
//                    .collect(Collectors.toList());
//        } catch (Exception e) {
//            log.error(LOG_TITLE, e);
//        }
//        return columns;
//    }
//
//    public List carDetailHeader(String lang) {
//        CarRiskOrderFieldEnum[] enums = CarRiskOrderFieldEnum.values();
//        return Arrays.stream(enums)
//                .map(e -> new HashMap<String, String>(){{
//                    put("headerKey", e.getName());
//                    put("headerValue", SharkUtils.get(e.getHeaderKey(), lang));}})
//                .collect(Collectors.toList());
//    }
//
//    public String getFileName(BaseQueryConditionBO baseCondition, String uid, String lang) {
//        String name = "%s-%s-%s-%s";
//        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "FLT_REFUND")) {
//            return String.format(name,
//                    SharkUtils.get("Catalog.ComplianceMonitor", lang),
//                    SharkUtils.get("RiskOrder.FltScene1", lang),
//                    uid,
//                    str
//            );
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "HTL_CASH_OUT")) {
//            return String.format(name,
//                    SharkUtils.get("Catalog.ComplianceMonitor", lang),
//                    SharkUtils.get("RiskOrder.HtlScene1", lang),
//                    uid,
//                    str
//            );
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "CAR_DOUBLE_BOOK")) {
//            return String.format(name,
//                    SharkUtils.get("Catalog.ComplianceMonitor", lang),
//                    SharkUtils.get("RiskOrder.CarScene1", lang),
//                    uid,
//                    str
//            );
//        }
//
//        if (StringUtils.equalsIgnoreCase(baseCondition.getRiskScene(), "HTL_UNDER_STAY")) {
//            return String.format(name,
//                    SharkUtils.get("Catalog.ComplianceMonitor", lang),
//                    SharkUtils.get("RiskOrder.HtlScene2", lang),
//                    uid,
//                    str
//            );
//        }
//        return "";
//    }
//
//    private String operateStatus(int status) {
//        switch (status) {
//            case 0:
//                return "RiskOrder.OperateNot";
//            case 1:
//                return "RiskOrder.OperateDone";
//            case 2:
//                return "RiskOrder.OperateIgnore";
//            default:
//                return "Exceltopname.unknown";
//        }
//    }
//
//
//    public OnlineReportRiskOrderDetailRequest mapRequest(BaseQueryConditionBO baseCondition) {
//        OnlineReportRiskOrderDetailRequest request = new OnlineReportRiskOrderDetailRequest();
//        request.setBasecondition(baseCondition.getBaseQueryCondition());
//        request.setOrderIds(baseCondition.getOrderids());
//        request.setUid(baseCondition.getUids());
//        request.setPage(baseCondition.getPager());
//        request.setSortKey(baseCondition.getSortKey());
//        request.setAsc(baseCondition.isAsc());
//        request.setLang(baseCondition.getLang());
//        request.setOperatorStatus(baseCondition.getOperatorStatus());
//        if (baseCondition.getRiskReasons() != null && baseCondition.getRiskReasons().size() > 0) {
//            List<String> riskReasons = new ArrayList<>();
//            for (String riskCode: baseCondition.getRiskReasons()) {
//                riskReasons.add(ChineseLanguageConfig.get(riskCode));
//            }
//            request.setRiskReasons(riskReasons);
//        }
//        return request;
//    }
//
//    private String getHtlRiskLevelDesc(String code, String lang){
//        if (StringUtils.isEmpty(code)){
//            return "";
//        }
//        int level = Integer.valueOf(code);
//        if (level == 1){
//            return SharkUtils.get("RiskOrder.LowRisk", lang);
//        }else if (level >= 2 && level <= 5){
//            return SharkUtils.get("Exceltopname.highrisk", lang);
//        }else {
//            return code;
//        }
//    }
//}
