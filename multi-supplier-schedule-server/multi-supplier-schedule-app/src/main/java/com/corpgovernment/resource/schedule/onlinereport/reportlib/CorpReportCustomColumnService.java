package com.corpgovernment.resource.schedule.onlinereport.reportlib;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpReportCustomColumnPO;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.dao.CorpReportCustomColumnDAOExt;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.List;

/**
 * Auther:abguo
 * Date:2019/9/7
 * Description:
 * Project:onlinereportweb
 */
@Service
public class CorpReportCustomColumnService {

    @Autowired
    private CorpReportCustomColumnDAOExt reportColumnDAOExt;

    public long insertReportColumn(String uid, String reportType, String reportColumns) throws SQLException {
        List<CorpReportCustomColumnPO> list = reportColumnDAOExt.queryColumnsByUidAndReportType(uid, reportType);
        if (CollectionUtils.isNotEmpty(list)) {
            return 0;
        }
        return reportColumnDAOExt.insert(uid, reportType, reportColumns);
    }

    public int updateReportColumn(Long id, String reportColumns) throws SQLException {
        return reportColumnDAOExt.update(id, reportColumns);
    }

    public CorpReportCustomColumnPO getCustomColumn(String uid, String reportType) throws SQLException {
        List<CorpReportCustomColumnPO> list = reportColumnDAOExt.queryColumnsByUidAndReportType(uid, reportType);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    public void insert(String uid, String reportKey, String reportColumns) throws SQLException, BusinessException {
        CorpReportCustomColumnPO corpReportCustomColumnPO = getCustomColumn(uid, reportKey);
        if (corpReportCustomColumnPO == null) {
            if (insertReportColumn(uid, reportKey, reportColumns) < 1) {
                throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), "");
            }
        } else {
            if (updateReportColumn(corpReportCustomColumnPO.getCustomColumnId(), reportColumns) < 0) {
                throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), "");
            }
        }
    }
}
