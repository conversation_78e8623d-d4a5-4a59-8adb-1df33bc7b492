//package com.corpgovernment.resource.schedule.onlinereport.riskorder;
//
//import com.ctrip.corp.onlinereportweb.commmon.helper.ChineseLanguageConfig;
//import com.ctrip.corp.onlinereportweb.commmon.utils.MD5Util;
//import com.ctrip.corp.onlinereportweb.dataaccess.PO.CorpRiskOrderReportTaskPO;
//import com.ctrip.corp.onlinereportweb.dataaccess.dao.CorpRiskOrderReportTaskDAO;
//import com.ctrip.corp.onlinereportweb.domain.cglib.seriallizer.JacksonUtil;
//import com.ctrip.corp.onlinereportweb.domainreport.bo.risk.RiskOrderReportBO;
//import com.ctrip.corp.onlinereportweb.domainreport.bo.risk.RiskOrderReportHotelCashOutConditionB0;
//import com.ctrip.corp.onlinereportweb.domainreport.bo.risk.RiskOrderReportHotelCashOutConditionUnionB0;
//import com.ctrip.framework.clogging.agent.log.ILog;
//import com.ctrip.framework.clogging.agent.log.LogManager;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.sql.SQLException;
//import java.sql.Timestamp;
//import java.util.*;
//
///**
// * <AUTHOR>
// * @date ：Created in 2022/4/12 18:20
// * @description：
// * @modified By：
// * @version: $
// */
//@Service
//public class RiskOrderReportTaskService {
//
//    private static final ILog LOGGER = LogManager.getLogger(RiskOrderReportTaskService.class);
//    protected static final String PREFIX = RiskOrderReportTaskService.class.getSimpleName();
//
//    @Autowired
//    CorpRiskOrderReportTaskDAO corpRiskOrderReportTaskDAO;
//
//    // 添加任务 - 有重复则不添加
//    public int addTask(RiskOrderReportBO riskOrderReportBO) throws SQLException {
//        List<RiskOrderReportHotelCashOutConditionB0> tasks = unionForOneTask(riskOrderReportBO.getReportCondition());
//        List<RiskOrderReportHotelCashOutConditionB0> noSameTasks = new ArrayList<>();
//        for (RiskOrderReportHotelCashOutConditionB0 task: tasks) {
//            List<CorpRiskOrderReportTaskPO> sameTask = corpRiskOrderReportTaskDAO.querySameTask(riskOrderReportBO.getUid(),
//                    riskOrderReportBO.getStartTime(), riskOrderReportBO.getEndTime(),
//                    MD5Util.md5Encrypt32Upper(JacksonUtil.serialize(task))
//            );
//            if (sameTask.size() == 0) {
//                noSameTasks.add(task);
//            }
//        }
//        return insertTask(riskOrderReportBO.getRiskSence(), riskOrderReportBO.getUid(),
//                riskOrderReportBO.getStartTime(), riskOrderReportBO.getEndTime(), riskOrderReportBO.getChooseAll(), noSameTasks);
//    }
//
//    private int insertTask(String riskScene, String uid,
//                           String startTime, String endTime, Integer chooseAll,
//                           List<RiskOrderReportHotelCashOutConditionB0> bos) throws SQLException {
//        List<CorpRiskOrderReportTaskPO> insertTasks = new ArrayList<>();
//        for (RiskOrderReportHotelCashOutConditionB0 task: bos) {
//            CorpRiskOrderReportTaskPO po = new CorpRiskOrderReportTaskPO();
//            String reportConditionContent = JacksonUtil.serialize(task);
//            po.setRiskSence(riskScene);
//            po.setUid(uid);
//            po.setStartTime(startTime);
//            po.setEndTime(endTime);
//            po.setReportCondition(reportConditionContent);
//            po.setTaskStatus(0); // 初始状态为0
//            po.setReportConditionMD5(MD5Util.md5Encrypt32Upper(reportConditionContent));
//            if (chooseAll == 1) {
//                po.setChooseAll(Boolean.TRUE);
//            }
//            po.setValid(Boolean.TRUE);
//            // 生成文件名 CTRIP员工协同酒店舞弊套现分析2022-01-01-2022-08-10.pdf
//            po.setDownloadUrl(
//                    String.format("%s%s%s-%s.pdf", task.getCorporation(), ChineseLanguageConfig.get("risk_report_cash_out"),
//                    startTime, endTime)
//            );
//            insertTasks.add(po);
//        }
//        int[] insertResults = corpRiskOrderReportTaskDAO.insert(insertTasks);
//        return Arrays.stream(insertResults).sum();
//    }
//
//    // 查询该卡号下的所有报告生成任务
//    public List<CorpRiskOrderReportTaskPO> queryTasksByUid(String uid, Timestamp timestamp) throws SQLException {
//        return corpRiskOrderReportTaskDAO.queryTaskByUID(uid, timestamp);
//    }
//
//    // 取消任务5, 且把该记录置为无效 - 重跑任务0
//    public int updateTask(int taskId, int taskStatus, String uid) throws SQLException {
//        CorpRiskOrderReportTaskPO po = corpRiskOrderReportTaskDAO.queryByPk(taskId);
//        if (po.getUid().equals(uid)) {
//            po.setTaskStatus(taskStatus);
//            if (taskStatus == 5) {
//                po.setValid(Boolean.FALSE);
//            }
//            return corpRiskOrderReportTaskDAO.update(po);
//        }
//        return 0;
//    }
//
//    public CorpRiskOrderReportTaskPO queryByTaskId(long taskId) {
//        try {
//            return corpRiskOrderReportTaskDAO.queryByPk(taskId);
//        } catch (SQLException e) {
//            LOGGER.error(PREFIX + "queryByPk", e);
//        }
//        return null;
//    }
//
//    public List<CorpRiskOrderReportTaskPO> queryTasksByStatus(List<Integer> status) throws SQLException {
//        return corpRiskOrderReportTaskDAO.queryTaskByStatus(status);
//    }
//
//    public List<CorpRiskOrderReportTaskPO> queryTasksByIds(List<Long> ids) throws SQLException {
//        return corpRiskOrderReportTaskDAO.queryTaskByIds(ids);
//    }
//
//    public int updateTasks(List<CorpRiskOrderReportTaskPO> tasks) throws SQLException {
//        return Arrays.stream(corpRiskOrderReportTaskDAO.update(tasks)).sum();
//    }
//
//    public ArrayList<RiskOrderReportHotelCashOutConditionB0> unionForOneTask(List<RiskOrderReportHotelCashOutConditionB0> rawCondition) {
//        HashMap<String, RiskOrderReportHotelCashOutConditionUnionB0> union = new HashMap<>();
//        for (RiskOrderReportHotelCashOutConditionB0 bo: rawCondition) {
//            if (! union.containsKey(bo.getCorporation())) {
//                RiskOrderReportHotelCashOutConditionUnionB0 unionBO = new RiskOrderReportHotelCashOutConditionUnionB0();
//                unionBO.setCorporation(bo.getCorporation());
//                addNoEmpty(unionBO.getAccountId(), bo.getAccountId(), "$");
//                addNoEmpty(unionBO.getMasterHotelId(), bo.getMasterHotelId(), ",");
//                addNoEmpty(unionBO.getMasterHotelName(), bo.getMasterHotelName(), ",");
//                addNoEmpty(unionBO.getCostcenter1(), bo.getCostcenter1(), "$");
//                addNoEmpty(unionBO.getCostcenter2(), bo.getCostcenter2(), "$");
//                addNoEmpty(unionBO.getCostcenter3(), bo.getCostcenter3(), "$");
//                addNoEmpty(unionBO.getCostcenter4(), bo.getCostcenter4(), "$");
//                addNoEmpty(unionBO.getCostcenter5(), bo.getCostcenter5(), "$");
//                addNoEmpty(unionBO.getCostcenter6(), bo.getCostcenter6(), "$");
//                addNoEmpty(unionBO.getDept1(), bo.getDept1(), "$");
//                addNoEmpty(unionBO.getDept2(), bo.getDept2(), "$");
//                addNoEmpty(unionBO.getDept3(), bo.getDept3(), "$");
//                addNoEmpty(unionBO.getDept4(), bo.getDept4(), "$");
//                addNoEmpty(unionBO.getDept5(), bo.getDept5(), "$");
//                addNoEmpty(unionBO.getDept6(), bo.getDept6(), "$");
//                addNoEmpty(unionBO.getDept7(), bo.getDept7(), "$");
//                addNoEmpty(unionBO.getDept8(), bo.getDept8(), "$");
//                addNoEmpty(unionBO.getDept9(), bo.getDept9(), "$");
//                addNoEmpty(unionBO.getDept10(), bo.getDept10(), "$");
//                union.put(bo.getCorporation(), unionBO);
//                continue;
//            }
//            RiskOrderReportHotelCashOutConditionUnionB0 existBo = union.get(bo.getCorporation());
//            addNoEmpty(existBo.getAccountId(), bo.getAccountId(), "$");
//            addNoEmpty(existBo.getMasterHotelId(), bo.getMasterHotelId(), ",");
//            addNoEmpty(existBo.getMasterHotelName(), bo.getMasterHotelName(), ",");
//            addNoEmpty(existBo.getCostcenter1(), bo.getCostcenter1(), "$");
//            addNoEmpty(existBo.getCostcenter2(), bo.getCostcenter2(), "$");
//            addNoEmpty(existBo.getCostcenter3(), bo.getCostcenter3(), "$");
//            addNoEmpty(existBo.getCostcenter4(), bo.getCostcenter4(), "$");
//            addNoEmpty(existBo.getCostcenter5(), bo.getCostcenter5(), "$");
//            addNoEmpty(existBo.getCostcenter6(), bo.getCostcenter6(), "$");
//            addNoEmpty(existBo.getDept1(), bo.getDept1(), "$");
//            addNoEmpty(existBo.getDept2(), bo.getDept2(), "$");
//            addNoEmpty(existBo.getDept3(), bo.getDept3(), "$");
//            addNoEmpty(existBo.getDept4(), bo.getDept4(), "$");
//            addNoEmpty(existBo.getDept5(), bo.getDept5(), "$");
//            addNoEmpty(existBo.getDept6(), bo.getDept6(), "$");
//            addNoEmpty(existBo.getDept7(), bo.getDept7(), "$");
//            addNoEmpty(existBo.getDept8(), bo.getDept8(), "$");
//            addNoEmpty(existBo.getDept9(), bo.getDept9(), "$");
//            addNoEmpty(existBo.getDept10(), bo.getDept10(), "$");
//        }
//        ArrayList<RiskOrderReportHotelCashOutConditionB0> unionConditions = new ArrayList<>();
//        for (RiskOrderReportHotelCashOutConditionUnionB0 bo: union.values()) {
//            RiskOrderReportHotelCashOutConditionB0 joinBo = new RiskOrderReportHotelCashOutConditionB0();
//            joinBo.setCorporation(bo.getCorporation());
//            bo.getMasterHotelId().sort(Comparator.comparing(Integer::valueOf));
//            joinBo.setMasterHotelId(StringUtils.join(bo.getMasterHotelId(), ","));
//            bo.getMasterHotelName().sort(Comparator.comparing(String::hashCode));
//            joinBo.setMasterHotelName(StringUtils.join(bo.getMasterHotelName(), ","));
//            bo.getAccountId().sort(Comparator.comparing(Integer::valueOf));
//            joinBo.setAccountId(StringUtils.join(bo.getAccountId(), "$"));
//
//            bo.getCostcenter1().sort(Comparator.comparing(String::hashCode));
//            joinBo.setCostcenter1(StringUtils.join(bo.getCostcenter1(), "$"));
//            bo.getCostcenter2().sort(Comparator.comparing(String::hashCode));
//            joinBo.setCostcenter2(StringUtils.join(bo.getCostcenter2(), "$"));
//            bo.getCostcenter3().sort(Comparator.comparing(String::hashCode));
//            joinBo.setCostcenter3(StringUtils.join(bo.getCostcenter3(), "$"));
//            bo.getCostcenter4().sort(Comparator.comparing(String::hashCode));
//            joinBo.setCostcenter4(StringUtils.join(bo.getCostcenter4(), "$"));
//            bo.getCostcenter5().sort(Comparator.comparing(String::hashCode));
//            joinBo.setCostcenter5(StringUtils.join(bo.getCostcenter5(), "$"));
//            bo.getCostcenter6().sort(Comparator.comparing(String::hashCode));
//            joinBo.setCostcenter6(StringUtils.join(bo.getCostcenter6(), "$"));
//
//            bo.getDept1().sort(Comparator.comparing(String::hashCode));
//            joinBo.setDept1(StringUtils.join(bo.getDept1(), "$"));
//            bo.getDept2().sort(Comparator.comparing(String::hashCode));
//            joinBo.setDept2(StringUtils.join(bo.getDept2(), "$"));
//            bo.getDept3().sort(Comparator.comparing(String::hashCode));
//            joinBo.setDept3(StringUtils.join(bo.getDept3(), "$"));
//            bo.getDept4().sort(Comparator.comparing(String::hashCode));
//            joinBo.setDept4(StringUtils.join(bo.getDept4(), "$"));
//            bo.getDept5().sort(Comparator.comparing(String::hashCode));
//            joinBo.setDept5(StringUtils.join(bo.getDept5(), "$"));
//
//            bo.getDept6().sort(Comparator.comparing(String::hashCode));
//            joinBo.setDept6(StringUtils.join(bo.getDept6(), "$"));
//            bo.getDept7().sort(Comparator.comparing(String::hashCode));
//            joinBo.setDept7(StringUtils.join(bo.getDept7(), "$"));
//            bo.getDept8().sort(Comparator.comparing(String::hashCode));
//            joinBo.setDept8(StringUtils.join(bo.getDept8(), "$"));
//            bo.getDept9().sort(Comparator.comparing(String::hashCode));
//            joinBo.setDept9(StringUtils.join(bo.getDept9(), "$"));
//            bo.getDept10().sort(Comparator.comparing(String::hashCode));
//            joinBo.setDept10(StringUtils.join(bo.getDept10(), "$"));
//            unionConditions.add(joinBo);
//        }
//        return unionConditions;
//    }
//
//    public void addNoEmpty(List<String> union, String content, String separator) {
//        if (StringUtils.isBlank(content)) {
//            return;
//        }
//        for (String splitContent: StringUtils.splitByWholeSeparatorPreserveAllTokens(content, separator)) {
//            if (! union.contains(splitContent)) {
//                union.add(splitContent);
//            }
//        }
//    }
//
//}
