package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AvgNightPriceOverviewInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AvgNightPriceRangeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HotelBehaviorInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.MarkMetricInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportAvgNightPriceRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportAvgNightPriceResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMarkMetricTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportMarkMetricTrendResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc
 */
@Service
public class HotelAvgPriceBehaviorAnalysisAdaptor extends BaseReportDataAdaptor<Object> {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private HotelAvgPriceBehaviorAnalysisExcelService hotelAvgPriceBehaviorAnalysisExcelService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        String index = baseCondition.getIndex();
        if (StringUtils.equalsIgnoreCase(index, "trend_compare_corp_industry")) {
            OnlineReportMarkMetricTrendRequest request = new OnlineReportMarkMetricTrendRequest();
            request.setProductType(baseCondition.getProductType());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            request.setQueryBu(QueryReportBuTypeEnum.hotel);
            extMap.put("metric", "avg_night_price_trend");
            request.setExtData(extMap);
            OnlineReportMarkMetricTrendResponse responseType = corpOnlineReportPlatformService.queryMarkMetricTrend(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return metricTrend(Optional.ofNullable(responseType.getMarkMetricTrend()).orElse(new ArrayList<>()), lang);
            } else {
                erroThrow(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        } else if (StringUtils.equalsIgnoreCase(index, "trend_compare_mc")) {
            OnlineReportBehaviorAnalysisRequest request = new OnlineReportBehaviorAnalysisRequest();
            request.setProductType(baseCondition.getProductType());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            request.setQueryBu(QueryReportBuTypeEnum.hotel);
            request.setExtData(extMap);
            OnlineReportBehaviorAnalysisResponse responseType = corpOnlineReportPlatformService.queryBehaviorAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return metricTrendMC(Optional.ofNullable(responseType.getHotelBehaviorList()).orElse(new ArrayList<>()), lang);
            } else {
                erroThrow(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        } else if (StringUtils.equalsIgnoreCase(index, "dis") || StringUtils.equalsIgnoreCase(index, "overview")) {
            OnlineReportAvgNightPriceRequest request = new OnlineReportAvgNightPriceRequest();
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setProductType(baseCondition.getProductType());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            // 需要查询行业数据
            extMap.put("needIndustry", "T");
            extMap.put("index", index);
            request.setExtData(extMap);
            OnlineReportAvgNightPriceResponse responseType = corpOnlineReportPlatformService.queryHtlAvgNightPriceAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                if (StringUtils.equalsIgnoreCase(index, "dis")) {
                    return hotelAvgNightPriceRange(Optional.ofNullable(responseType.getRangeList()).orElse(new ArrayList<>()), lang);
                }
                if (StringUtils.equalsIgnoreCase(index, "overview")) {
                    return hotelAvgNightPriceOverview(Optional.ofNullable(responseType.getOverviewInfo()).orElse(new AvgNightPriceOverviewInfo()));
                }
            } else {
                erroThrow(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        }
        return null;
    }


    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        return hotelAvgPriceBehaviorAnalysisExcelService.buildExcel(userPermissionsBo, baseCondition);
    }

    @Override
    public String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang) {
        String fileName_format = "%s_%s_%s_%s";
        String middle = SharkUtils.get("Travelanalysis.averagehotel", lang);
        String prefix = SharkUtils.get("Catalog.BehaviorAnalysis", lang);
        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
        return String.format(fileName_format, prefix, middle, uid, str);
    }


    private Map metricTrend(List<MarkMetricInfo> markMetricInfoList, String lang) {
        List trendLegends = new ArrayList();
        Map trendLegendCompany = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.public", lang))
                .put("key", "company").build();
        trendLegends.add(trendLegendCompany);
        Map trendLegendIndustry = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.industry", lang))
                .put("key", "industry").build();
        trendLegends.add(trendLegendIndustry);
        Map trendLegendCorp = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.tmc", lang))
                .put("key", "corp").build();
        trendLegends.add(trendLegendCorp);
        List trendDataList = new ArrayList();
        for (MarkMetricInfo markMetricInfo : markMetricInfoList) {
            Map trendData = ImmutableMap.builder()
                    .put("company", MapperUtils.convertDigitToZero(markMetricInfo.getCompanyMetric()))
                    .put("industry", MapperUtils.convertDigitToZero(markMetricInfo.getIndustryMetric()))
                    .put("corp", MapperUtils.convertDigitToZero(markMetricInfo.getCorpMetric())).build();
            trendDataList.add(ImmutableMap.builder()
                    .put("axis", markMetricInfo.getDim())
                    .put("data", trendData).build());
        }
        return ImmutableMap.builder()
                .put("legends", trendLegends)
                .put("data", trendDataList).build();
    }

    private Map metricTrendMC(List<HotelBehaviorInfo> behaviorInfos, String lang) {
        List trendLegends = new ArrayList();
        Map trendLegendCompany = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.NonAgreement", lang))
                .put("key", "amountM").build();
        trendLegends.add(trendLegendCompany);
        Map trendLegendIndustry = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.pact", lang))
                .put("key", "amountC").build();
        trendLegends.add(trendLegendIndustry);
        List trendDataList = new ArrayList();
        for (HotelBehaviorInfo behaviorInfo : behaviorInfos) {
            Map trendData = ImmutableMap.builder()
                    .put("amountC", MapperUtils.convertDigitToZero(behaviorInfo.getAvgNightPriceC()))
                    .put("amountM", MapperUtils.convertDigitToZero(behaviorInfo.getAvgNightPriceM())).build();
            trendDataList.add(ImmutableMap.builder()
                    .put("axis", behaviorInfo.getDim())
                    .put("data", trendData).build());
        }
        return ImmutableMap.builder()
                .put("legends", trendLegends)
                .put("data", trendDataList).build();
    }

    private Map hotelAvgNightPriceRange(List<AvgNightPriceRangeInfo> avgNightPriceRangeInfoList, String lang) {
        List legends = new ArrayList();
        HtlAvgNightPriceRangeEnum[] avgNightPriceRangeEnums = HtlAvgNightPriceRangeEnum.values();
        Map rangeLegend1 = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.nightnum", lang))
                .put("key", "quantity")
                .put("percentage", "quantityPercent")
                .put("corpPercentage", "corpQuantityPercent")
                .put("industryPercentage", "industryQuantityPercent").build();
        legends.add(rangeLegend1);
        Map rangeLegend2 = ImmutableMap.builder()
                .put("name", SharkUtils.get("Index.costmoney", lang))
                .put("key", "amount")
                .put("percentage", "amountPercent")
                .put("corpPercentage", "corpAmountPercent")
                .put("industryPercentage", "industryAmountPercent").build();
        legends.add(rangeLegend2);
        List data = new ArrayList();
        for (HtlAvgNightPriceRangeEnum rangeEnum : avgNightPriceRangeEnums) {
            AvgNightPriceRangeInfo avgNightPriceRangeInfo = avgNightPriceRangeInfoList.stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getRange(), rangeEnum.getKey().toString()))
                    .findFirst().orElse(new AvgNightPriceRangeInfo());
            Map trendData = ImmutableMap.builder()
                    .put("quantity", MapperUtils.convertDigitToZero(avgNightPriceRangeInfo.getTotalQuantity()))
                    .put("quantityPercent", MapperUtils.convertDigitToZero(avgNightPriceRangeInfo.getQuantityPercent()))
                    .put("corpQuantityPercent", MapperUtils.convertDigitToZero(avgNightPriceRangeInfo.getCorpQuantityPercent()))
                    .put("industryQuantityPercent", MapperUtils.convertDigitToZero(avgNightPriceRangeInfo.getIndustryQuantityPercent()))
                    .put("amount", MapperUtils.convertDigitToZero(avgNightPriceRangeInfo.getTotalAmount()))
                    .put("amountPercent", MapperUtils.convertDigitToZero(avgNightPriceRangeInfo.getAmountPercent()))
                    .put("corpAmountPercent", MapperUtils.convertDigitToZero(avgNightPriceRangeInfo.getCorpAmountPercent()))
                    .put("industryAmountPercent", MapperUtils.convertDigitToZero(avgNightPriceRangeInfo.getIndustryAmountPercent())).build();
            data.add(ImmutableMap.builder()
                    .put("axis", rangeEnum == HtlAvgNightPriceRangeEnum.RANGE13 ?
                            String.format("%s%s%s", SharkUtils.get("App.General.GreaterThan", lang), rangeEnum.getKey(), SharkUtils.get("Unit.Yuan", lang)) :
                            String.format("%s%s", rangeEnum.getKey(), SharkUtils.get("Unit.Yuan", lang)))
                    .put("data", trendData).build());
        }
        return ImmutableMap.builder()
                .put("legends", legends)
                .put("data", data).build();
    }

    private Map hotelAvgNightPriceOverview(AvgNightPriceOverviewInfo avgNightPriceOverviewInfo) {
        return ImmutableMap.builder()
                .put("companyAvgNightpRice", MapperUtils.convertDigitToZero(avgNightPriceOverviewInfo.getCompanyAvgNightPrice()))
                .put("corpAvgNightpRice", MapperUtils.convertDigitToZero(avgNightPriceOverviewInfo.getCorpAvgNightPrice()))
                .put("industryAvgNightpRice", MapperUtils.convertDigitToZero(avgNightPriceOverviewInfo.getIndustryAvgNightPrice())).build();
    }

    enum HtlAvgNightPriceRangeEnum {
        RANGE1("0-100", "0-100"),
        RANGE2("100-200", "100-200"),
        RANGE3("200-300", "200-300"),
        RANGE4("300-400", "300-400"),
        RANGE5("400-500", "400-500"),
        RANGE6("500-600", "500-600"),
        RANGE7("600-700", "600-700"),
        RANGE8("700-800", "700-800"),
        RANGE9("800-900", "800-900"),
        RANGE10("900-1000", "900-1000"),
        RANGE11("900-1500", "900-1500"),
        RANGE12("1500-2000", "1500-2000"),
        RANGE13("2000", "2000"),
        ;
        String key;
        String sharkKey;

        HtlAvgNightPriceRangeEnum(String s, String m) {
            this.key = s;
            this.sharkKey = m;
        }

        public String getKey() {
            return key;
        }

        public String getSharkKey() {
            return sharkKey;
        }
    }
}
