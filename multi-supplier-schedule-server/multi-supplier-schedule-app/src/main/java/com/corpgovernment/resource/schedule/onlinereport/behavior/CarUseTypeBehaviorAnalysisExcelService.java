package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.CarBehaviorInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz.AbstractGenralExportDataService;
import onlinereport.enums.PageErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2022/4/22 15:50
 * @Desc 用车同城\跨域分析
 */
@Service
public class CarUseTypeBehaviorAnalysisExcelService extends AbstractGenralExportDataService {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    public List<ChartExcelEntity> buildExcel(BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        Map map = getConditionContent(baseCondition.getBaseQueryCondition().getUid(), baseCondition.getBaseQueryCondition(), lang);
        int sheetIndex = 0;
        List<ChartExcelEntity> chartExcelEntityList = new ArrayList<>();
        OnlineReportBehaviorAnalysisRequest request = new OnlineReportBehaviorAnalysisRequest();
        request.setBasecondition(baseCondition.getBaseQueryCondition());
        request.setQueryBu(QueryReportBuTypeEnum.car);
        request.setProductType(baseCondition.getProductType());
        Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
        extMap.put("dim", "USE_CAR_TYPE");
        request.setExtData(extMap);
        OnlineReportBehaviorAnalysisResponse responseType = corpOnlineReportPlatformService.queryBehaviorAnalysis(request);
        if (responseType != null && responseType.getResponseCode() == 20000) {
            chartExcelEntityList.add(carBehavior(Optional.ofNullable(responseType.getCarBehaviorList()).orElse(new ArrayList<>()), lang, map, sheetIndex,
                    (String) extMap.get("carOrderType")));
        } else {
            BusinessException businessException = null;
            if (Objects.isNull(responseType)) {
                businessException = new BusinessException(PageErrorCodeEnum.InterfaceError.getValue(), PageErrorCodeEnum.InterfaceError.getDescription());
            } else {
                businessException = new BusinessException(responseType.getResponseCode(), responseType.getResponseDesc());
            }
            throw businessException;
        }
        return chartExcelEntityList;
    }

    private ChartExcelEntity carBehavior(List<CarBehaviorInfo> list, String lang, Map map, int sheetIndex, String carOrderType) {
        CarUseTypeBehaviorAnalysisAdaptor.UseCarTypeEnum[] useCarTypeEnums = CarUseTypeBehaviorAnalysisAdaptor.UseCarTypeEnum.values();
        ChartExcelEntity sheet1 = new ChartExcelEntity();
        sheet1.setHeaders(Arrays.asList(StringUtils.EMPTY, SharkUtils.get("Index.ordernumber", lang),
                SharkUtils.get("Index.orderpercentage", lang), SharkUtils.get("Overview.CarAvgPrice", lang), SharkUtils.get("Supplier.totalcostamount", lang)));
        List data = new ArrayList();
        for (CarUseTypeBehaviorAnalysisAdaptor.UseCarTypeEnum useCarTypeEnum : useCarTypeEnums) {
            CarBehaviorInfo carBehaviorInfo = list.stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), useCarTypeEnum.name()))
                    .findFirst().orElse(new CarBehaviorInfo());
            data.add(Arrays.asList(SharkUtils.get(useCarTypeEnum.getSharkKey(), lang)
                    , MapperUtils.convertDigitToZero(carBehaviorInfo.getTotalOrderCount())
                    , MapperUtils.convertDigitToZeroString(carBehaviorInfo.getOrderCountPercent()).concat(GlobalConst.PERCENT_QUOTE)
                    , MapperUtils.convertDigitToZero(carBehaviorInfo.getAvgPrice())
                    , MapperUtils.convertDigitToZero(carBehaviorInfo.getTotalAmount())));
        }
        data.addAll(buttonContent(lang, map));
        sheet1.setSheetTitle(getSheetNameByCarOrderType(lang, carOrderType));
        sheet1.setSheetNum(sheetIndex);
        sheet1.setData(data);
        return sheet1;
    }
}
