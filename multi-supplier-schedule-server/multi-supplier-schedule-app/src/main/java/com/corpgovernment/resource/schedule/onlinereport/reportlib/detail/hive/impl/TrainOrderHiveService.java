package com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.hive.impl;


import com.corpgovernment.resource.schedule.domain.onlinereport.common.BaseDataUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DigitBaseUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.HiveFilter;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.SrDao;
import com.corpgovernment.resource.schedule.onlinereport.clickhouse.hive.TrainEntity;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.SrSwitchConfig;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.TrainOrderDetailService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.detail.hive.AbstractOrderDetailHiveService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.reportlib.HotelOrderDetailEnum;
import onlinereport.enums.reportlib.OrderDetailEnumerable;
import onlinereport.enums.reportlib.TrainChangeStatusEnum;
import onlinereport.enums.reportlib.TrainOrderDetailEnum;
import onlinereport.enums.reportlib.TrainOrderStatusEnum;
import onlinereport.enums.reportlib.TrainRefundStatusEnum;
import onlinereport.enums.reportlib.TrainTicketTypeEnum;
import onlinereport.enums.reportlib.uid.TrainUidOrderDetailEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst.FOUR_DIGIT_NUM;


/**
 * Auther:abguo
 * Date:2019/8/21
 * Description:
 */
@Service
@Slf4j
public class TrainOrderHiveService extends AbstractOrderDetailHiveService {

    @Autowired
    private HiveDao hiveDao;

    @Autowired
    private SrSwitchConfig srSwitchConfig;

    @Autowired
    private SrDao srDao;

    @Override
    public OrderDetailEnumerable[] getEnum(String reportType) {
        if (StringUtils.equalsIgnoreCase(reportType, "T_UID_DETAIL")) {
            return TrainUidOrderDetailEnum.values();
        } else {
            return TrainOrderDetailEnum.values();
        }
    }

    public List<Map<String, Object>> queryOrderDetail(HiveFilter hiveFilter, String lang, String reportType) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<TrainEntity> trainEntityList = null;
        if (srSwitchConfig.isOpenSrSwitch(hiveFilter.getUid(), "train")) {
            trainEntityList = srDao.searchTrainOrderdetail(hiveFilter);
        } else {
            trainEntityList = hiveDao.searchTrainOrderdetail(hiveFilter);
        }
        if (CollectionUtils.isEmpty(trainEntityList)) {
            return result;
        }
        if (StringUtils.equalsIgnoreCase(reportType, "T_UID_DETAIL")) {
            return queryTrainUidOrderDetail(trainEntityList, lang);
        } else {
            return queryTrainOrderDetail(trainEntityList, lang);
        }
    }

    /**
     * 火车订单明细
     *
     * @param trainEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> queryTrainUidOrderDetail(List<TrainEntity> trainEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(trainEntityList)) {
            return result;
        }
        Map orderStatusMap = initOrderStatus(lang);
        Map refundMap = initRefunstatusDes(lang);
        Map rebookMap = initReBooktatusDes(lang);
        for (TrainEntity order : trainEntityList) {
            Map<String, Object> map = new HashMap<>();
            map.put(TrainUidOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToString(order.getOrder_id()));
            map.put(TrainUidOrderDetailEnum.ORDERSTATUS.getCode(), getDesc(order.getOrder_status(), orderStatusMap));
            map.put(TrainUidOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
            map.put(TrainUidOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorp_corporation()));
            map.put(TrainUidOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
            map.put(TrainUidOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.trim(order.getAccount_id()));
            map.put(TrainUidOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(TrainUidOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
            map.put(TrainUidOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRank_name()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCost_center1()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCost_center2()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCost_center3()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCost_center4()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCost_center5()));
            map.put(TrainUidOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCost_center6()));
            map.put(TrainUidOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
            map.put(TrainUidOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
            map.put(TrainUidOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
            map.put(TrainUidOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
            map.put(TrainUidOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
            map.put(TrainUidOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
            map.put(TrainUidOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
            map.put(TrainUidOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
            map.put(TrainUidOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
            map.put(TrainUidOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(TrainUidOrderDetailEnum.PRINTTIME.getCode(), MapperUtils.trim(order.getPrint_time()));
            map.put(TrainUidOrderDetailEnum.PASSENGERNAME.getCode(), MapperUtils.trim(order.getPassenger_name()));
            map.put(TrainUidOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            map.put(TrainUidOrderDetailEnum.CHANGEQUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getChange_quantity()));
            map.put(TrainUidOrderDetailEnum.CHANGESTATUS.getCode(), getDesc(order.getChange_status(), rebookMap));
            map.put(TrainUidOrderDetailEnum.REFUNDSTATUS.getCode(), getDesc(order.getRefund_status(), refundMap));
            map.put(TrainUidOrderDetailEnum.TRAINNAME.getCode(), MapperUtils.trim(order.getTrain_name()));
            map.put(TrainUidOrderDetailEnum.FIRSTSEATTYPENAME.getCode(), MapperUtils.getValByLang(lang, order.getFirst_seat_type_name(), order.getFirst_seat_type_name_en()));
            map.put(TrainUidOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));
            map.put(TrainUidOrderDetailEnum.TICKETPRICE.getCode(), DigitBaseUtils.formatDigit(order.getTicket_price()));
            map.put(TrainUidOrderDetailEnum.DEPARTUREDATETIME.getCode(), MapperUtils.trim(order.getDeparture_date_time()));
            map.put(TrainUidOrderDetailEnum.DEPARTURESTATIONNAME.getCode(), MapperUtils.getValByLang(lang, order.getDeparture_station_name(), order.getDeparture_station_name_en()));
            map.put(TrainUidOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getDeparture_city_name(), order.getDeparture_city_name_en()));
            map.put(TrainUidOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrival_date_time()));
            map.put(TrainUidOrderDetailEnum.ARRIVALSTATIONNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrival_station_name(), order.getArrival_station_name_en()));
            map.put(TrainUidOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrival_city_name(), order.getArrival_city_name_en()));
            map.put(TrainUidOrderDetailEnum.CHANGEBALANCE.getCode(), DigitBaseUtils.formatDigit(order.getChangebalance()));
            map.put(TrainUidOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
            map.put(TrainUidOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccount_code()));
            map.put(TrainUidOrderDetailEnum.DELAY_RESCHEDULE_FEE.getCode(), DigitBaseUtils.formatDigit(order.getDelay_reschedule_fee()));
            result.add(map);
        }
        return result;
    }

    /**
     * 火车订单明细
     *
     * @param trainEntityList
     * @param lang
     * @return
     */
    public List<Map<String, Object>> queryTrainOrderDetail(List<TrainEntity> trainEntityList, String lang) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(trainEntityList)) {
            return result;
        }
        Map yesOrNotMap = BaseDataUtils.getYesOrNoMap(lang);
        Map orderStatusMap = initOrderStatus(lang);
        Map refundMap = initRefunstatusDes(lang);
        Map rebookMap = initReBooktatusDes(lang);
        Map ticketMap = initTicketTypeDes(lang);
        for (TrainEntity order : trainEntityList) {
            Map<String, Object> map = new HashMap<>();
            map.put(TrainOrderDetailEnum.ORDERID.getCode(), MapperUtils.convertDigitToString(order.getOrder_id()));
            map.put(TrainOrderDetailEnum.ORDERSTATUS.getCode(), getDesc(order.getOrder_status(), orderStatusMap));
            map.put(TrainOrderDetailEnum.ORDERDATE.getCode(), MapperUtils.trim(order.getOrder_date()));
//            map.put(TrainOrderDetailEnum.CORP_CORPORATION.getCode(), MapperUtils.trim(order.getCorp_corporation()));
            map.put(TrainOrderDetailEnum.CORPNAME.getCode(), MapperUtils.trim(order.getCorp_name()));
//            map.put(TrainOrderDetailEnum.COMPANYGROUP.getCode(), MapperUtils.trim(order.getCompanygroup()));
//            map.put(TrainOrderDetailEnum.COMPANYGROUPID.getCode(), MapperUtils.trim(order.getCompanygroupid()));
//            map.put(TrainOrderDetailEnum.ACCOUNTID.getCode(), MapperUtils.trim(order.getAccount_id()));
//            map.put(TrainOrderDetailEnum.ACCOUNTCODE.getCode(), MapperUtils.trim(order.getAccount_code()));
//            map.put(TrainOrderDetailEnum.ACCOUNTNAME.getCode(), MapperUtils.trim(order.getAccount_name()));
//            map.put(TrainOrderDetailEnum.SUBACCOUNTID.getCode(), MapperUtils.trim(order.getSub_account_id()));
//            map.put(TrainOrderDetailEnum.SUBACCOUNTCODE.getCode(), MapperUtils.trim(order.getSub_account_code()));
//            map.put(TrainOrderDetailEnum.SUBACCOUNTNAME.getCode(), MapperUtils.trim(order.getSub_account_name()));
//            map.put(TrainOrderDetailEnum.UID.getCode(), MapperUtils.trim(order.getUid()));
            map.put(TrainOrderDetailEnum.EMPLOYEID.getCode(), MapperUtils.trim(order.getEmploye_id()));
            map.put(TrainOrderDetailEnum.USERNAME.getCode(), MapperUtils.trim(order.getUser_name()));
//            map.put(TrainOrderDetailEnum.RANKNAME.getCode(), MapperUtils.trim(order.getRank_name()));
//            map.put(TrainOrderDetailEnum.WORKCITY.getCode(), MapperUtils.trim(order.getWork_city()));
            map.put(TrainOrderDetailEnum.COSTCENTER1.getCode(), MapperUtils.trim(order.getCost_center1()));
//            map.put(TrainOrderDetailEnum.COSTCENTER2.getCode(), MapperUtils.trim(order.getCost_center2()));
//            map.put(TrainOrderDetailEnum.COSTCENTER3.getCode(), MapperUtils.trim(order.getCost_center3()));
//            map.put(TrainOrderDetailEnum.COSTCENTER4.getCode(), MapperUtils.trim(order.getCost_center4()));
//            map.put(TrainOrderDetailEnum.COSTCENTER5.getCode(), MapperUtils.trim(order.getCost_center5()));
//            map.put(TrainOrderDetailEnum.COSTCENTER6.getCode(), MapperUtils.trim(order.getCost_center6()));
            map.put(TrainOrderDetailEnum.DEPT1.getCode(), MapperUtils.trim(order.getDept1()));
//            map.put(TrainOrderDetailEnum.DEPT2.getCode(), MapperUtils.trim(order.getDept2()));
//            map.put(TrainOrderDetailEnum.DEPT3.getCode(), MapperUtils.trim(order.getDept3()));
//            map.put(TrainOrderDetailEnum.DEPT4.getCode(), MapperUtils.trim(order.getDept4()));
//            map.put(TrainOrderDetailEnum.DEPT5.getCode(), MapperUtils.trim(order.getDept5()));
//            map.put(TrainOrderDetailEnum.DEPT6.getCode(), MapperUtils.trim(order.getDept6()));
//            map.put(TrainOrderDetailEnum.DEPT7.getCode(), MapperUtils.trim(order.getDept7()));
//            map.put(TrainOrderDetailEnum.DEPT8.getCode(), MapperUtils.trim(order.getDept8()));
//            map.put(TrainOrderDetailEnum.DEPT9.getCode(), MapperUtils.trim(order.getDept9()));
//            map.put(TrainOrderDetailEnum.DEPT10.getCode(), MapperUtils.trim(order.getDept10()));
            map.put(TrainOrderDetailEnum.ISONLINE.getCode(), MapperUtils.convertBookTypeSingle(order.getIs_online()));
//            map.put(TrainOrderDetailEnum.FEETYPE.getCode(), MapperUtils.trim(order.getFee_type()));
            map.put(TrainOrderDetailEnum.PREPAYTYPE.getCode(), TrainOrderDetailService.convertPrepayType(MapperUtils.trim(order.getPrepay_type())));
//            map.put(TrainOrderDetailEnum.ACBPREPAYTYPE.getCode(), MapperUtils.trim(order.getAcb_prepay_type()));
//            map.put(TrainOrderDetailEnum.ISBOSS.getCode(), MapperUtils.trim(order.getBosstype()));
            map.put(TrainOrderDetailEnum.JOURNEYNO.getCode(), MapperUtils.trim(order.getJourney_no()));
            map.put(TrainOrderDetailEnum.TRIPID.getCode(), MapperUtils.convertStrToDash(order.getTrip_id()));
//            map.put(TrainOrderDetailEnum.JOUNARYREASON.getCode(), MapperUtils.trim(order.getJourney_reason()));
//            map.put(TrainOrderDetailEnum.PROJECT.getCode(), MapperUtils.trim(order.getProject()));
//            map.put(TrainOrderDetailEnum.VERBALAUTHORIZE.getCode(), MapperUtils.convertTorF(order.getVerbal_authorize(), yesOrNotMap));
//            map.put(TrainOrderDetailEnum.CONFIRMPERSON.getCode(), MapperUtils.trim(order.getConfirm_person()));
//            map.put(TrainOrderDetailEnum.CONFIRMPTYPE.getCode(), MapperUtils.trim(order.getConfirm_type()));
//            map.put(TrainOrderDetailEnum.CONFIRMPERSON2.getCode(), MapperUtils.trim(order.getConfirm_person2()));
//            map.put(TrainOrderDetailEnum.CONFIRMPTYPE2.getCode(), MapperUtils.trim(order.getConfirm_type2()));
            map.put(TrainOrderDetailEnum.GROUPMONTH.getCode(), MapperUtils.convertDigitToString(order.getGroup_month()));
//            map.put(TrainOrderDetailEnum.USERDEFINEDRID.getCode(), MapperUtils.trim(order.getUserdefined_rid()));
//            map.put(TrainOrderDetailEnum.USERDEFINEDRC.getCode(), MapperUtils.trim(order.getUserdefined_rc()));
            //////////////////////////////////////////////////////////////////////////////////////////////////////////
            map.put(TrainOrderDetailEnum.PRINTTIME.getCode(), MapperUtils.trim(order.getPrint_time()));
            map.put(TrainOrderDetailEnum.PASSENGERNAME.getCode(), MapperUtils.trim(order.getPassenger_name()));
            map.put(TrainOrderDetailEnum.QUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getQuantity()));
            map.put(TrainOrderDetailEnum.CHANGEQUANTITY.getCode(), MapperUtils.convertDigitToZero(order.getChange_quantity()));
            map.put(TrainOrderDetailEnum.CHANGESTATUS.getCode(), getDesc(order.getChange_status(), rebookMap));
            map.put(TrainOrderDetailEnum.REFUNDSTATUS.getCode(), getDesc(order.getRefund_status(), refundMap));
            map.put(TrainOrderDetailEnum.TICKETTYPE.getCode(), getDesc(order.getTicket_type(), ticketMap));
            map.put(TrainOrderDetailEnum.TRAINNAME.getCode(), MapperUtils.trim(order.getTrain_name()));
            map.put(TrainOrderDetailEnum.FIRSTSEATTYPENAME.getCode(), MapperUtils.getValByLang(lang, order.getFirst_seat_type_name(), order.getFirst_seat_type_name_en()));
            map.put(TrainOrderDetailEnum.REALPAY.getCode(), DigitBaseUtils.formatDigit(order.getReal_pay()));
            map.put(TrainOrderDetailEnum.TICKETPRICE.getCode(), DigitBaseUtils.formatDigit(order.getTicket_price()));
            map.put(TrainOrderDetailEnum.SERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getService_fee()));
//            map.put(TrainOrderDetailEnum.INSURANCEFEE.getCode(), DigitBaseUtils.formatDigit(order.getInsurance_fee()));
            map.put(TrainOrderDetailEnum.REFUNDTICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getRefund_ticket_fee()));
//            map.put(TrainOrderDetailEnum.DELIVERFEE.getCode(), DigitBaseUtils.formatDigit(order.getDeliver_fee()));
//            map.put(TrainOrderDetailEnum.PAPERTICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getPaper_ticket_fee()));
            map.put(TrainOrderDetailEnum.DEALCHANGESERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getDeal_change_service_fee()));
//            map.put(TrainOrderDetailEnum.GRABSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getGrab_service_fee()));
            map.put(TrainOrderDetailEnum.AFTERSERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getAfter_service_fee()));
            map.put(TrainOrderDetailEnum.AFTERCHANGESERVICEFEE.getCode(), DigitBaseUtils.formatDigit(order.getAfterchangeservicefee()));
//            map.put(TrainOrderDetailEnum.AFTERTAKETICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getAftertaketicketfee()));
//            map.put(TrainOrderDetailEnum.AFTERAFTERTAKETICKETFEE.getCode(), DigitBaseUtils.formatDigit(order.getAfteraftertaketicketfee()));
//            map.put(TrainOrderDetailEnum.OCURRENCY.getCode(), MapperUtils.trim(order.getO_currency()));
//            map.put(TrainOrderDetailEnum.OEXCHANGERATE.getCode(), DigitBaseUtils.formatDigit(order.getO_exchangerate(), FOUR_DIGIT_NUM));
            map.put(TrainOrderDetailEnum.LINECITY.getCode(), MapperUtils.getValByLang(lang, order.getLine_city(), order.getLine_city_en()));
            map.put(TrainOrderDetailEnum.DEPARTUREDATETIME.getCode(), MapperUtils.trim(order.getDeparture_date_time()));
            map.put(TrainOrderDetailEnum.DEPARTURESTATIONNAME.getCode(), MapperUtils.getValByLang(lang, order.getDeparture_station_name(), order.getDeparture_station_name_en()));
            map.put(TrainOrderDetailEnum.DEPARTURECITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getDeparture_city_name(), order.getDeparture_city_name_en()));
            map.put(TrainOrderDetailEnum.DEPARTUREPROVINCENAME.getCode(), MapperUtils.getValByLang(lang, order.getDeparture_province_name(), order.getDeparture_province_name_en()));
            map.put(TrainOrderDetailEnum.ARRIVALDATETIME.getCode(), MapperUtils.trim(order.getArrival_date_time()));
            map.put(TrainOrderDetailEnum.ARRIVALSTATIONNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrival_station_name(), order.getArrival_station_name_en()));
            map.put(TrainOrderDetailEnum.ARRIVALCITYNAME.getCode(), MapperUtils.getValByLang(lang, order.getArrival_city_name(), order.getArrival_city_name_en()));
            map.put(TrainOrderDetailEnum.ARRIVALPROVINCENAME.getCode(), MapperUtils.getValByLang(lang, order.getArrival_province_name(), order.getArrival_province_name_en()));
            map.put(HotelOrderDetailEnum.ISRC.getCode(), MapperUtils.convertTorF(order.getIs_rc(), yesOrNotMap));
            map.put(TrainOrderDetailEnum.SEATTYPERCCODENAME.getCode(), MapperUtils.trim(order.getSeattype_rccodeid()));
//            map.put(TrainOrderDetailEnum.TICKETRCCODENAME.getCode(), MapperUtils.trim(order.getTicket_rccodename()));
            map.put(TrainOrderDetailEnum.CHANGEBALANCE.getCode(), DigitBaseUtils.formatDigit(order.getChangebalance()));
//            map.put(TrainOrderDetailEnum.AUDITORID.getCode(), MapperUtils.trim(order.getAuditorid()));
//            map.put(TrainOrderDetailEnum.AUDITORID2.getCode(), MapperUtils.trim(order.getAuditorid2()));
//            map.put(TrainOrderDetailEnum.REFUND_RC.getCode(), MapperUtils.trim(order.getRefund_rc()));
//            map.put(TrainOrderDetailEnum.REFUND_RC_DESC.getCode(), MapperUtils.trim(order.getRefund_rc_desc()));
            map.put(TrainOrderDetailEnum.EST_FEE_12306.getCode(), DigitBaseUtils.formatDigit(order.getEst_fee_12306()));
//            map.put(TrainOrderDetailEnum.TAKETICKETSTATUS.getCode(), MapperUtils.trim(order.getTaketicketstatus()));
//            map.put(TrainOrderDetailEnum.DEFINDFLAG.getCode(), MapperUtils.trim(order.getDefineflag()));
//            map.put(TrainOrderDetailEnum.DEFINDFLAG2.getCode(), MapperUtils.trim(order.getDefineflag2()));
//            map.put(TrainOrderDetailEnum.TRAINCARBONS.getCode(), MapperUtils.convertDigitToString(convertUnit(order.getCarbon_emission())));
//            map.put(TrainOrderDetailEnum.TRAINMEDIANCARBONS.getCode(), MapperUtils.convertDigitToString(convertUnit(order.getMedian_carbon_emission())));
//            map.put(TrainOrderDetailEnum.TRAINCARBONSAVE.getCode(), MapperUtils.convertDigitToString(calSaveCarbon(order.getCarbon_emission(), order.getMedian_carbon_emission())));
//            map.put(TrainOrderDetailEnum.STDINDUSTRY1.getCode(), MapperUtils.trim(order.getStd_industry1()));
//            map.put(TrainOrderDetailEnum.STDINDUSTRY2.getCode(), MapperUtils.trim(order.getStd_industry2()));
            map.put(TrainOrderDetailEnum.PASSENGER_EID.getCode(), MapperUtils.trim(order.getPassenger_employeeid()));
            map.put(TrainOrderDetailEnum.DELAY_RESCHEDULE_FEE.getCode(), DigitBaseUtils.formatDigit(order.getDelay_reschedule_fee()));
            result.add(map);
        }
        log.info("train order detail-queryTrainOrderDetail: {}", JsonUtils.toJsonString( result));
        return result;
    }

    private Double calSaveCarbon(Integer carbon, Integer medianCarbon) {
        if (carbon == null || medianCarbon == null || carbon == 0 || medianCarbon == 0) {
            return 0d;
        }
        return DigitBaseUtils.divide(medianCarbon - carbon, 1000).doubleValue();
    }

    ;

    private Double convertUnit(Integer carbonEmission) {
        if (carbonEmission == null || carbonEmission == 0) {
            return 0d;
        }
        return DigitBaseUtils.divide(carbonEmission, 1000).doubleValue();
    }


    private String getDesc(String key, Map map) {
        if (StringUtils.isEmpty(key) || MapUtils.isEmpty(map)) {
            return StringUtils.EMPTY;
        }
        return (String) map.get(StringUtils.upperCase(key.trim()));
    }

    private Map initOrderStatus(String lang) {
        return ImmutableMap.builder()
                .put(TrainOrderStatusEnum.N.toString(), SharkUtils.get(TrainOrderStatusEnum.N.getName(), lang))
                .put(TrainOrderStatusEnum.WP.toString(), SharkUtils.get(TrainOrderStatusEnum.WP.getName(), lang))
                .put(TrainOrderStatusEnum.PP.toString(), SharkUtils.get(TrainOrderStatusEnum.PP.getName(), lang))
                .put(TrainOrderStatusEnum.PF.toString(), SharkUtils.get(TrainOrderStatusEnum.PF.getName(), lang))
                .put(TrainOrderStatusEnum.WA.toString(), SharkUtils.get(TrainOrderStatusEnum.WA.getName(), lang))
                .put(TrainOrderStatusEnum.AR.toString(), SharkUtils.get(TrainOrderStatusEnum.AR.getName(), lang))
                .put(TrainOrderStatusEnum.WT.toString(), SharkUtils.get(TrainOrderStatusEnum.WT.getName(), lang))
                .put(TrainOrderStatusEnum.TP.toString(), SharkUtils.get(TrainOrderStatusEnum.TP.getName(), lang))
                .put(TrainOrderStatusEnum.TD.toString(), SharkUtils.get(TrainOrderStatusEnum.TD.getName(), lang))
                .put(TrainOrderStatusEnum.TF.toString(), SharkUtils.get(TrainOrderStatusEnum.TF.getName(), lang))
                .put(TrainOrderStatusEnum.C.toString(), SharkUtils.get(TrainOrderStatusEnum.C.getName(), lang))
                .build();
    }

    private Map initRefunstatusDes(String lang) {
        return ImmutableMap.builder()
                .put(TrainRefundStatusEnum.F.toString(), SharkUtils.get(TrainRefundStatusEnum.F.getName(), lang))
                .put(TrainRefundStatusEnum.P.toString(), SharkUtils.get(TrainRefundStatusEnum.P.getName(), lang))
                .put(TrainRefundStatusEnum.S.toString(), SharkUtils.get(TrainRefundStatusEnum.S.getName(), lang))
                .build();
    }

    private Map initReBooktatusDes(String lang) {
        return ImmutableMap.builder()
                .put(TrainChangeStatusEnum.F.toString(), SharkUtils.get(TrainChangeStatusEnum.F.getName(), lang))
                .put(TrainChangeStatusEnum.P.toString(), SharkUtils.get(TrainChangeStatusEnum.P.getName(), lang))
                .put(TrainChangeStatusEnum.S.toString(), SharkUtils.get(TrainChangeStatusEnum.S.getName(), lang))
                .build();
    }

    private Map initTicketTypeDes(String lang) {
        return ImmutableMap.builder()
                .put(TrainTicketTypeEnum.D.toString(), SharkUtils.get(TrainTicketTypeEnum.D.getName(), lang))
                .put(TrainTicketTypeEnum.C.toString(), SharkUtils.get(TrainTicketTypeEnum.C.getName(), lang))
                .build();
    }

    @Override
    public boolean matching(QueryReportBuTypeEnum queryReportBuTypeEnum) {
        return queryReportBuTypeEnum == QueryReportBuTypeEnum.train;
    }
}
