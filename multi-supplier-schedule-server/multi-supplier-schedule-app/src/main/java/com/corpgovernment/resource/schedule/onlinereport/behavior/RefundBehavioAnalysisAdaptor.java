package com.corpgovernment.resource.schedule.onlinereport.behavior;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserPermissionsBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.DateFormatConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.FlightBehaviorInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportBehaviorAnalysisResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportRefundTrendRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportRefundTrendResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RefundInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RefundPercentInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.RefundTrendInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.corpgovernment.resource.schedule.onlinereport.BaseReportDataAdaptor;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022-11-18 14:53
 * @desc
 */
@Service
public class RefundBehavioAnalysisAdaptor extends BaseReportDataAdaptor<Object> {

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;

    @Autowired
    private RefundBehavioAnalysisExcelService refundBehavioAnalysisExcelService;

    @Override
    public Object convert(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        String lang = baseCondition.getLang();
        String index = baseCondition.getIndex();
        if (StringUtils.equalsIgnoreCase(RefundIndexEnum.REfund_TYPE_DIS.toString(), index)) {
            OnlineReportBehaviorAnalysisRequest request = new OnlineReportBehaviorAnalysisRequest();
            request.setProductType(baseCondition.getProductType());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            // 和app的退区分
            extMap.put("dim", index);
            request.setExtData(extMap);
            OnlineReportBehaviorAnalysisResponse responseType = corpOnlineReportPlatformService.queryBehaviorAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                return refundDis(Optional.ofNullable(responseType.getFlightBehaviorList()).orElse(new ArrayList<>()), lang);
            } else {
                erroThrow(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        } else {
            OnlineReportRefundTrendRequest request = new OnlineReportRefundTrendRequest();
            request.setProductType(baseCondition.getProductType());
            request.setBasecondition(baseCondition.getBaseQueryCondition());
            Map extMap = Optional.ofNullable(baseCondition.getExtData()).orElse(new HashMap());
            // 和app的退区分
            extMap.put("from", "PC");
            extMap.put("index", index);
            request.setExtData(extMap);
            if (StringUtils.equalsIgnoreCase(RefundIndexEnum.ALL.toString(), baseCondition.getIndex())) {
                request.setQueryBu(QueryReportBuTypeEnum.overview);
            } else {
                request.setQueryBu(QueryReportBuTypeEnum.valueOf(baseCondition.getQueryBu()));
            }
            OnlineReportRefundTrendResponse responseType = corpOnlineReportPlatformService.queryRefundTrendAnalysis(request);
            if (responseType != null && responseType.getResponseCode() == 20000) {
                if (StringUtils.equalsIgnoreCase(RefundIndexEnum.ALL.toString(), index)) {
                    return refundALL(Optional.ofNullable(responseType.getRefundPercentInfo()).orElse(new RefundPercentInfo()));
                } else if (StringUtils.equalsIgnoreCase(RefundIndexEnum.overview.toString(), index)) {
                    return refundOverview(Optional.ofNullable(responseType.getRefundInfo()).orElse(new RefundInfo()));
                } else if (StringUtils.equalsIgnoreCase(RefundIndexEnum.trend.toString(), index)) {
                    return refund(Optional.ofNullable(responseType.getRefundInfo()).orElse(new RefundInfo()), lang);
                }
            } else {
                erroThrow(responseType, responseType.getResponseCode(), responseType.getResponseDesc());
            }
        }
        return null;
    }

    @Override
    public List<ChartExcelEntity> buildExcel(UserPermissionsBo userPermissionsBo, BaseQueryConditionBO baseCondition) throws BusinessException {
        return refundBehavioAnalysisExcelService.buildExcel(userPermissionsBo, baseCondition);
    }

    @Override
    public String buildExcelName(BaseQueryConditionBO baseCondition, String uid, String lang) {
        String fileName_format = "%s_%s_%s_%s";
        String middle = SharkUtils.get("Travelanalysis.refundanalysis", lang);
        String prefix = SharkUtils.get("Catalog.BehaviorAnalysis", lang);
        String str = DateFormatUtils.format(new Date(), DateFormatConst.DATE_FORMAT4);
        return String.format(fileName_format, prefix, middle, uid, str);
    }

    private Map refundALL(RefundPercentInfo refundPercentInfo) {
        return ImmutableMap.builder()
                .put("flightRefundPercent", MapperUtils.convertDigitToZero(refundPercentInfo.getFlightRefundPercent()))
                .put("hotelRefundPercent", MapperUtils.convertDigitToZero(refundPercentInfo.getHotelRefundPercent()))
                .put("trainRefundQuantity", MapperUtils.convertDigitToZero(refundPercentInfo.getTrainRefundPercent())).build();
    }

    private Map refund(RefundInfo refundInfo, String lang) {
        List trendLegends = new ArrayList();
        Map trendLegendQuantity = ImmutableMap.builder()
                .put("name", SharkUtils.get("App.index.num", lang))
                .put("key", "refundQuantity")
                .put("percentage", "refundPercent").build();
        trendLegends.add(trendLegendQuantity);

        List trendDataList = new ArrayList();
        List<RefundTrendInfo> rebookTrendInfoList = refundInfo.getRefundTrendList();
        for (RefundTrendInfo trendInfo : rebookTrendInfoList) {
            Map trendData = ImmutableMap.builder()
                    .put("refundQuantity", MapperUtils.convertDigitToZero(trendInfo.getTotalRefundQuantity()))
                    .put("refundPercent", MapperUtils.convertDigitToZero(trendInfo.getCompanyRefundPercent())).build();
            trendDataList.add(ImmutableMap.builder()
                    .put("axis", trendInfo.getPoint())
                    .put("data", trendData).build());
        }
        return ImmutableMap.builder()
                .put("legends", trendLegends)
                .put("data", trendDataList).build();
    }

    private Map refundOverview(RefundInfo refundInfo) {
        Map percentData = new HashMap();
        percentData.put("refundPercent", MapperUtils.convertDigitToZero(refundInfo.getRefundQuantityPercent()));
        percentData.put("refundQuanttiy", MapperUtils.convertDigitToZero(refundInfo.getTotalRefundQuantity()));
        percentData.put("loss", MapperUtils.convertDigitToZero(refundInfo.getTotalRefundLoss()));
        percentData.put("corpRefundPercent", MapperUtils.convertDigitToZero(refundInfo.getCorpRefundtktPercent()));
        percentData.put("industryRefundPercent", MapperUtils.convertDigitToZero(refundInfo.getIndustryRefundtktPercent()));
        return percentData;
    }

    private Object refundDis(List<FlightBehaviorInfo> list, String lang) {
        List trendDataList = new ArrayList();
        FlightRefundTypeEnum[] flightRefundTypeEnums = FlightRefundTypeEnum.values();
        for (FlightRefundTypeEnum flightRefundTypeEnum : flightRefundTypeEnums) {
            FlightBehaviorInfo trendInfo = list.stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getDim(), flightRefundTypeEnum.toString()))
                    .findFirst().orElse(new FlightBehaviorInfo());
            Map trendData = ImmutableMap.builder()
                    .put("refundQuantity", MapperUtils.convertDigitToZero(trendInfo.getTotalRefundQuantity()))
                    .put("refundPercent", MapperUtils.convertDigitToZero(trendInfo.getRefundQuantityPercent())).build();
            trendDataList.add(ImmutableMap.builder()
                    .put("axis", SharkUtils.get(flightRefundTypeEnum.getSharkKey(), lang))
                    .put("data", trendData).build());
        }
        return trendDataList;
    }

    /**
     * ALL:退票占比概览
     */
    enum RefundIndexEnum {
        ALL, overview, trend, REfund_TYPE_DIS
    }

    /**
     * SPECIAL:特殊事件,VOLUNTARY:自愿,INVOLUNTARY:航变,OTHER:其他
     */
    enum FlightRefundTypeEnum {
        VOLUNTARY("Travelanalysis.Reson1"),
        INVOLUNTARY("Travelanalysis.Reson2"),
        SPECIAL("Travelanalysis.Reson3"),
        OTHER("Index.others"),
        ;
        private String sharkKey;

        FlightRefundTypeEnum(String s) {
            this.sharkKey = s;
        }

        public String getSharkKey() {
            return sharkKey;
        }
    }
}
