# response error content
ResponseEnums.FAILED=\u7CFB\u7EDF\u5F02\u5E38
ResponseEnums.SERVER_ERROR=\u670D\u52A1\u5F02\u5E38
ResponseEnums.NULL_REQUEST=\u8BF7\u6C42\u4E0D\u80FD\u4E3A\u7A7A
ResponseEnums.PARAMETER_FAIL=\u8BF7\u6C42\u53C2\u6570\u5F02\u5E38
ResponseEnums.NULL_RESPONSE=\u54CD\u5E94\u4E0D\u80FD\u4E3A\u7A7A

OnlineReportBuEnum.overview=\u6982\u89C8
OnlineReportBuEnum.flight=\u673A\u7968
OnlineReportBuEnum.hotel=\u9152\u5E97
OnlineReportBuEnum.train=\u706B\u8F66
OnlineReportBuEnum.car=\u7528\u8F66

DetailReportTypeEnum.detailAmountReport=\u91D1\u989D\u62A5\u8868
DetailReportTypeEnum.detailTicketReport=\u884C\u7A0B\u62A5\u8868

## \u6982\u89C8
ReportDetailHeaderEnum.flightV=Index.air
ReportDetailHeaderEnum.hotelV=Index.hotel
ReportDetailHeaderEnum.trainV=Index.train
ReportDetailHeaderEnum.carV=Index.car
ReportDetailHeaderEnum.busV=Overview.Bus
ReportDetailHeaderEnum.addV=Overview.VAS

## flight
# flight-\u6210\u4EA4\u51C0\u4EF7\uFF08\u4E0D\u542B\u6539\u7B7E\u5DEE\u4EF7\uFF09
ReportDetailHeaderEnum.flightNetPrice=AirFeeDetail.netfare
# flight-\u673A\u5EFA\u7A0E
ReportDetailHeaderEnum.flightMachineConstructionTax=AirFeeDetail.tax
# flight-\u71C3\u6CB9\u8D39
ReportDetailHeaderEnum.flightFuelCosts=AirFeeDetail.oil_fee
# flight-\u57FA\u7840\u5546\u65C5\u7BA1\u7406\u670D\u52A1
ReportDetailHeaderEnum.flightServiceFee=AirFeeDetail.service_fee
# flight-\u4FDD\u9669
ReportDetailHeaderEnum.flightInsurance=AirFeeDetail.insurance_fee
# flight-\u914D\u9001
ReportDetailHeaderEnum.flightSendTicketFee=AirFeeDetail.send_ticket_fee
# flight-\u589E\u503C\u670D\u52A1\u5305\u8D39
ReportDetailHeaderEnum.flightServicePackageFee=AirFeeDetail.servicepackage_fee
# flight-\u7ED1\u5B9A\u9152\u5E97\u4F18\u60E0\u5238
ReportDetailHeaderEnum.flightBindHotelCoupons=AirFeeDetail.bind_amount
# flight-\u6539\u7B7E\u8D39
ReportDetailHeaderEnum.flightChangeFee=AirFeeDetail.change_fee
# flight-\u6539\u7B7E\u5DEE\u4EF7
ReportDetailHeaderEnum.flightRebookPriceDifferential=AirFeeDetail.rebook_price_differential
# flight-\u6539\u7B7E\u5546\u65C5\u7BA1\u7406\u670D\u52A1\u8D39
ReportDetailHeaderEnum.flightChangeTravelManagementServiceFee=AirFeeDetail.rebook_service_fee
# flight-\u9000\u7968\u8D39
ReportDetailHeaderEnum.flightRefundFee=AirFeeDetail.refund_fee
# flight-\u9000\u7968\u5546\u65C5\u7BA1\u7406\u670D\u52A1\u8D39
ReportDetailHeaderEnum.flightRefundManagementServiceFee=AirFeeDetail.refund_service_fee
# flight-\u9000\u7968\u884C\u7A0B\u5355\u5546\u65C5\u7BA1\u7406\u670D\u52A1\u8D39
ReportDetailHeaderEnum.flightRefundTravelManagementServiceFee=AirFeeDetail.refund_itinerary_fee

# flight-\u540E\u6536\u5546\u65C5\u7BA1\u7406\u670D\u52A1\u8D39
ReportDetailHeaderEnum.ticketBehindServicefee=Exceltopname.postservicefee
# flight-\u540E\u6536\u6539\u7B7E\u5546\u65C5\u7BA1\u7406\u670D\u52A1\u8D39
ReportDetailHeaderEnum.rebookBehindServiceFee=Exceltopname.laterchangefee
# flight-\u540E\u6536\u9000\u7968\u5546\u65C5\u7BA1\u7406\u670D\u52A1\u8D39
ReportDetailHeaderEnum.refundBehindServiceFee=Exceltopname.refundlaterfee

## flight-first header name
ReportDetailHeaderEnum.flightOverallConsumerDetails=ReportDetailHeaderEnum.flightOverallConsumerDetails
ReportDetailHeaderEnum.domesticFlightConsumptionDetails=ReportDetailHeaderEnum.domesticFlightConsumptionDetails
ReportDetailHeaderEnum.internationalFlightConsumptionDetails=ReportDetailHeaderEnum.internationalFlightConsumptionDetails
ReportDetailHeaderEnum.agreementFlightConsumptionDetails=ReportDetailHeaderEnum.agreementFlightConsumptionDetails
ReportDetailHeaderEnum.nonAgreementFlightConsumptionDetails=ReportDetailHeaderEnum.nonAgreementFlightConsumptionDetails


## hotel
ReportDetailHeaderEnum.hotelRomPrice=HotelFeeDetail.room_price
ReportDetailHeaderEnum.hotelServiceFee=HotelFeeDetail.pre_service_fee
ReportDetailHeaderEnum.hotelCoupon=HotelFeeDetail.couponamount
ReportDetailHeaderEnum.hotelPostServiceFee=HotelFeeDetail.post_service_fee

### hotel-first header name
# \u9152\u5E97\u6574\u4F53\u6D88\u8D39\u660E\u7EC6
ReportDetailHeaderEnum.hotelOverallConsumerDetails=ReportDetailHeaderEnum.hotelOverallConsumerDetails
# \u56FD\u5185\u9152\u5E97\u6D88\u8D39\u660E\u7EC6
ReportDetailHeaderEnum.domesticHotelConsumptionDetails=ReportDetailHeaderEnum.domesticHotelConsumptionDetails
# \u56FD\u9645\u9152\u5E97\u6D88\u8D39\u660E\u7EC6
ReportDetailHeaderEnum.internationalHotelConsumptionDetails=ReportDetailHeaderEnum.internationalHotelConsumptionDetails
# \u534F\u8BAE\u9152\u5E97\u6D88\u8D39\u660E\u7EC6
ReportDetailHeaderEnum.agreementHotelConsumptionDetails=ReportDetailHeaderEnum.agreementHotelConsumptionDetails
# \u975E\u534F\u8BAE\u9152\u5E97\u6D88\u8D39\u660E\u7EC6
ReportDetailHeaderEnum.nonAgreementHotelConsumptionDetails=ReportDetailHeaderEnum.nonAgreementHotelConsumptionDetails


## train
ReportDetailHeaderEnum.trainTicketPrice=TrainFeeDetail.ticket_price
ReportDetailHeaderEnum.trainServiceFee=TrainFeeDetail.service_fee
ReportDetailHeaderEnum.trainChangebalance=TrainFeeDetail.changebalance
ReportDetailHeaderEnum.trainInsuranceFee=TrainFeeDetail.insurance_fee
ReportDetailHeaderEnum.trainDeliverFee=TrainFeeDetail.deliver_fee
ReportDetailHeaderEnum.trainGrabServiceFee=TrainFeeDetail.grab_service_fee
ReportDetailHeaderEnum.trainPaperTicketFee=TrainFeeDetail.paper_ticket_fee
ReportDetailHeaderEnum.trainRefundTicketFee=TrainFeeDetail.refund_ticket_fee
ReportDetailHeaderEnum.trainReplacementOfLaborCosts=TrainFeeDetail.aftertaketicketfee
ReportDetailHeaderEnum.trainDealChangeServiceFee=TrainFeeDetail.deal_change_service_fee
ReportDetailHeaderEnum.trainAfterServiceFee=TrainFeeDetail.after_service_fee
ReportDetailHeaderEnum.trainAfterchangeservicefee=TrainFeeDetail.afterChangeServiceFee
ReportDetailHeaderEnum.trainAfteraftertaketicketfee=TrainFeeDetail.afterAfterTakeTicketFee

### train-first header name
ReportDetailHeaderEnum.trainOverallConsumerDetails=ReportDetailHeaderEnum.trainOverallConsumerDetails


## car
ReportDetailHeaderEnum.basicFee=CarFeeDetail.basic_fee
ReportDetailHeaderEnum.carServiceFee=CarFeeDetail.service_fee
ReportDetailHeaderEnum.refundAmount=CarFeeDetail.refund_amount
ReportDetailHeaderEnum.takeCarV=index.domtaxi
ReportDetailHeaderEnum.takeCarVInter=index.inttaxi
ReportDetailHeaderEnum.transferV=Index.domairportpick
ReportDetailHeaderEnum.transferVInter=Index.intetrairportpick
ReportDetailHeaderEnum.rentalCarV=Index.rentcar
ReportDetailHeaderEnum.rentalCarVInter=Exceltopname.intcarrental
ReportDetailHeaderEnum.charterCarV=Index.CharteredCar

### car-first header name
ReportDetailHeaderEnum.carConsumptionDetails=ReportDetailHeaderEnum.carConsumptionDetails
ReportDetailHeaderEnum.takeCarConsumptionDetails=ReportDetailHeaderEnum.takeCarConsumptionDetails
ReportDetailHeaderEnum.transferConsumptionDetails=ReportDetailHeaderEnum.transferConsumptionDetails
ReportDetailHeaderEnum.rentalCarConsumptionDetails=ReportDetailHeaderEnum.rentalCarConsumptionDetails
ReportDetailHeaderEnum.charterCarConsumptionDetails=ReportDetailHeaderEnum.charterCarConsumptionDetails

## \u5171\u7528
ReportDetailHeaderEnum.common.serviceFee=\u5546\u65C5\u7BA1\u7406\u670D\u52A1\u8D39
ReportDetailHeaderEnum.common.insurance=\u4FDD\u9669\u8D39
ReportDetailHeaderEnum.common.deliveryFee=\u914D\u9001\u8D39
ReportDetailHeaderEnum.common.changePriceDiff=\u6539\u7B7E\u65F6\u7968\u9762\u5DEE\u4EF7


ReportDetailHeaderEnum.train.serviceFee=Train.service_fee

## \u5171\u7528-\u540C\u6BD4
ReportDetailHeaderEnum.common.yoy=Index.radio
## \u5171\u7528-\u73AF\u6BD4
ReportDetailHeaderEnum.common.mom=Index.rad
## \u5171\u7528-\u56FD\u5185
ReportDetailHeaderEnum.common.domestic=lbl_Domestic
## \u5171\u7528-\u56FD\u9645
ReportDetailHeaderEnum.common.international=lbl_International
## \u5171\u7528-\u534F\u8BAE
ReportDetailHeaderEnum.common.agreement=Index.Agreement
## \u5171\u7528-\u975E\u534F\u8BAE
ReportDetailHeaderEnum.common.nonAgreement=Index.NonAgreement
# \u516C\u5171-\u5C0F\u8BA1
ReportDetailHeaderEnum.common.subtotal=Index.total
# \u603B\u8BA1
ReportDetailHeaderEnum.totalV=Index.sum
# \u6708\u4EFD
ReportDetailHeaderEnum.dimension=Index.month

#\u53C2\u6570\u6821\u9A8C
OnlineReportDetailHandler.startTime=\u5F00\u59CB\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A
OnlineReportDetailHandler.endTime=\u7ED3\u675F\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A
OnlineReportDetailHandler.time.format=\u5F00\u59CB\u65F6\u95F4\u5FC5\u987B\u5C0F\u4E8E\u7ED3\u675F\u65F6\u95F4
OnlineReportDetailHandler.bu=\u67E5\u8BE2\u4EA7\u7EBF\u4E0D\u80FD\u4E3A\u7A7A
OnlineReportDetailHandler.baseCondition=\u57FA\u7840\u67E5\u8BE2\u6761\u4EF6\u4E0D\u80FD\u4E3A\u7A7A
OnlineReportDetailHandler.uId=uId\u4E0D\u80FD\u4E3A\u7A7A
OnlineReportDetailHandler.yoyType=\u540C\u6BD4\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A

ProductTypeEnum.dom=\u56FD\u5185
ProductTypeEnum.inter=\u56FD\u9645


Report.hotel.orderstatus=\u5DF2\u5B8C\u6210

Report.other=\u5176\u4ED6

Report.hotel.roomshare.room=\u540C\u4F4F
Report.hotel.roomshare.travel=\u540C\u884C
Report.hotel.roomshare.all=\u540C\u4F4F+\u540C\u884C

Report.hotel.roomshare.single=\u5355\u4EBA


Report.threeAgreement=\u4E09\u65B9\u534F\u8BAE
Report.Premium=\u4E24\u65B9\u5C0A\u4EAB

Report.Member=\u4F1A\u5458

Report.corpPayType=\u56E0\u516C

Report.Deal=\u6210\u4EA4


Report.FlightRefundtype.Special=\u7279\u6B8A\u4E8B\u4EF6
Report.FlightRefundtype.Voluntary=\u81EA\u613F
Report.FlightRefundtype.Involuntary=\u822A\u53D8
Report.FlightRefundtype.Other=\u5176\u4ED6

Report.CarMixpayment=\u662F


Report.country.china=\u4E2D\u56FD

Report.firstTier=\u4E00\u7EBF

Report.ReportLib.Contient.Asia=\u4E9A\u6D32
Report.ReportLib.Contient.Europe=\u6B27\u6D32
Report.ReportLib.Contient.Oceania=\u5927\u6D0B\u6D32
Report.ReportLib.Contient.NorthAmerica=\u5317\u7F8E\u6D32
Report.ReportLib.Contient.SouthAmerica=\u5357\u7F8E\u6D32
Report.ReportLib.Contient.Africa=\u975E\u6D32
Report.ReportLib.Contient.Antarctica=\u5357\u6781\u6D32
Report.ReportLib.flt.mixpayment=\u6DF7\u4ED8
Report.ReportLib.PrepayType.accnt=\u516C\u53F8\u8D26\u6237
Report.ReportLib.rebookPrepaytypename.accnt=\u516C\u8D26\u652F\u4ED8
Report.ReportLib.rebookPrepaytypename.UnionPay=\u94F6\u8054\u516C\u8D26\u652F\u4ED8



