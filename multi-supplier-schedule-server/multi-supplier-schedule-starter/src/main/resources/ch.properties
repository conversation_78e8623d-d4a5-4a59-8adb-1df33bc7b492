# response error content
ResponseEnums.FAILED=系统异常
ResponseEnums.SERVER_ERROR=服务异常
ResponseEnums.NULL_REQUEST=请求不能为空
ResponseEnums.PARAMETER_FAIL=请求参数异常
ResponseEnums.NULL_RESPONSE=响应不能为空

OnlineReportBuEnum.overview=概览
OnlineReportBuEnum.flight=机票
OnlineReportBuEnum.hotel=酒店
OnlineReportBuEnum.train=火车
OnlineReportBuEnum.car=用车

DetailReportTypeEnum.detailAmountReport=金额报表
DetailReportTypeEnum.detailTicketReport=行程报表

## 概览
ReportDetailHeaderEnum.flightV=Index.air
ReportDetailHeaderEnum.hotelV=Index.hotel
ReportDetailHeaderEnum.trainV=Index.train
ReportDetailHeaderEnum.carV=Index.car
ReportDetailHeaderEnum.busV=Overview.Bus
ReportDetailHeaderEnum.addV=Overview.VAS

## flight
# flight-成交净价（不含改签差价）
ReportDetailHeaderEnum.flightNetPrice=AirFeeDetail.netfare
# flight-机建税
ReportDetailHeaderEnum.flightMachineConstructionTax=AirFeeDetail.tax
# flight-燃油费
ReportDetailHeaderEnum.flightFuelCosts=AirFeeDetail.oil_fee
# flight-基础商旅管理服务
ReportDetailHeaderEnum.flightServiceFee=AirFeeDetail.service_fee
# flight-保险
ReportDetailHeaderEnum.flightInsurance=AirFeeDetail.insurance_fee
# flight-配送
ReportDetailHeaderEnum.flightSendTicketFee=AirFeeDetail.send_ticket_fee
# flight-增值服务包费
ReportDetailHeaderEnum.flightServicePackageFee=AirFeeDetail.servicepackage_fee
# flight-绑定酒店优惠券
ReportDetailHeaderEnum.flightBindHotelCoupons=AirFeeDetail.bind_amount
# flight-改签费
ReportDetailHeaderEnum.flightChangeFee=AirFeeDetail.change_fee
# flight-改签差价
ReportDetailHeaderEnum.flightRebookPriceDifferential=AirFeeDetail.rebook_price_differential
# flight-改签商旅管理服务费
ReportDetailHeaderEnum.flightChangeTravelManagementServiceFee=AirFeeDetail.rebook_service_fee
# flight-退票费
ReportDetailHeaderEnum.flightRefundFee=AirFeeDetail.refund_fee
# flight-退票商旅管理服务费
ReportDetailHeaderEnum.flightRefundManagementServiceFee=AirFeeDetail.refund_service_fee
# flight-退票行程单商旅管理服务费
ReportDetailHeaderEnum.flightRefundTravelManagementServiceFee=AirFeeDetail.refund_itinerary_fee

# flight-后收商旅管理服务费
ReportDetailHeaderEnum.ticketBehindServicefee=Exceltopname.postservicefee
# flight-后收改签商旅管理服务费
ReportDetailHeaderEnum.rebookBehindServiceFee=Exceltopname.laterchangefee
# flight-后收退票商旅管理服务费
ReportDetailHeaderEnum.refundBehindServiceFee=Exceltopname.refundlaterfee

## flight-first header name
ReportDetailHeaderEnum.flightOverallConsumerDetails=ReportDetailHeaderEnum.flightOverallConsumerDetails
ReportDetailHeaderEnum.domesticFlightConsumptionDetails=ReportDetailHeaderEnum.domesticFlightConsumptionDetails
ReportDetailHeaderEnum.internationalFlightConsumptionDetails=ReportDetailHeaderEnum.internationalFlightConsumptionDetails
ReportDetailHeaderEnum.agreementFlightConsumptionDetails=ReportDetailHeaderEnum.agreementFlightConsumptionDetails
ReportDetailHeaderEnum.nonAgreementFlightConsumptionDetails=ReportDetailHeaderEnum.nonAgreementFlightConsumptionDetails


## hotel
ReportDetailHeaderEnum.hotelRomPrice=HotelFeeDetail.room_price
ReportDetailHeaderEnum.hotelServiceFee=HotelFeeDetail.pre_service_fee
ReportDetailHeaderEnum.hotelCoupon=HotelFeeDetail.couponamount
ReportDetailHeaderEnum.hotelPostServiceFee=HotelFeeDetail.post_service_fee

### hotel-first header name
# 酒店整体消费明细
ReportDetailHeaderEnum.hotelOverallConsumerDetails=ReportDetailHeaderEnum.hotelOverallConsumerDetails
# 国内酒店消费明细
ReportDetailHeaderEnum.domesticHotelConsumptionDetails=ReportDetailHeaderEnum.domesticHotelConsumptionDetails
# 国际酒店消费明细
ReportDetailHeaderEnum.internationalHotelConsumptionDetails=ReportDetailHeaderEnum.internationalHotelConsumptionDetails
# 协议酒店消费明细
ReportDetailHeaderEnum.agreementHotelConsumptionDetails=ReportDetailHeaderEnum.agreementHotelConsumptionDetails
# 非协议酒店消费明细
ReportDetailHeaderEnum.nonAgreementHotelConsumptionDetails=ReportDetailHeaderEnum.nonAgreementHotelConsumptionDetails


## train
ReportDetailHeaderEnum.trainTicketPrice=TrainFeeDetail.ticket_price
ReportDetailHeaderEnum.trainServiceFee=TrainFeeDetail.service_fee
ReportDetailHeaderEnum.trainChangebalance=TrainFeeDetail.changebalance
ReportDetailHeaderEnum.trainInsuranceFee=TrainFeeDetail.insurance_fee
ReportDetailHeaderEnum.trainDeliverFee=TrainFeeDetail.deliver_fee
ReportDetailHeaderEnum.trainGrabServiceFee=TrainFeeDetail.grab_service_fee
ReportDetailHeaderEnum.trainPaperTicketFee=TrainFeeDetail.paper_ticket_fee
ReportDetailHeaderEnum.trainRefundTicketFee=TrainFeeDetail.refund_ticket_fee
ReportDetailHeaderEnum.trainReplacementOfLaborCosts=TrainFeeDetail.aftertaketicketfee
ReportDetailHeaderEnum.trainDealChangeServiceFee=TrainFeeDetail.deal_change_service_fee
ReportDetailHeaderEnum.trainAfterServiceFee=TrainFeeDetail.after_service_fee
ReportDetailHeaderEnum.trainAfterchangeservicefee=TrainFeeDetail.afterChangeServiceFee
ReportDetailHeaderEnum.trainAfteraftertaketicketfee=TrainFeeDetail.afterAfterTakeTicketFee

### train-first header name
ReportDetailHeaderEnum.trainOverallConsumerDetails=ReportDetailHeaderEnum.trainOverallConsumerDetails


## car
ReportDetailHeaderEnum.basicFee=CarFeeDetail.basic_fee
ReportDetailHeaderEnum.carServiceFee=CarFeeDetail.service_fee
ReportDetailHeaderEnum.refundAmount=CarFeeDetail.refund_amount
ReportDetailHeaderEnum.takeCarV=index.domtaxi
ReportDetailHeaderEnum.takeCarVInter=index.inttaxi
ReportDetailHeaderEnum.transferV=Index.domairportpick
ReportDetailHeaderEnum.transferVInter=Index.intetrairportpick
ReportDetailHeaderEnum.rentalCarV=Index.rentcar
ReportDetailHeaderEnum.rentalCarVInter=Exceltopname.intcarrental
ReportDetailHeaderEnum.charterCarV=Index.CharteredCar

### car-first header name
ReportDetailHeaderEnum.carConsumptionDetails=ReportDetailHeaderEnum.carConsumptionDetails
ReportDetailHeaderEnum.takeCarConsumptionDetails=ReportDetailHeaderEnum.takeCarConsumptionDetails
ReportDetailHeaderEnum.transferConsumptionDetails=ReportDetailHeaderEnum.transferConsumptionDetails
ReportDetailHeaderEnum.rentalCarConsumptionDetails=ReportDetailHeaderEnum.rentalCarConsumptionDetails
ReportDetailHeaderEnum.charterCarConsumptionDetails=ReportDetailHeaderEnum.charterCarConsumptionDetails

## 共用
ReportDetailHeaderEnum.common.serviceFee=商旅管理服务费
ReportDetailHeaderEnum.common.insurance=保险费
ReportDetailHeaderEnum.common.deliveryFee=配送费
ReportDetailHeaderEnum.common.changePriceDiff=改签时票面差价

## 共用-同比
ReportDetailHeaderEnum.common.yoy=Index.radio
## 共用-环比
ReportDetailHeaderEnum.common.mom=Index.rad
## 共用-国内
ReportDetailHeaderEnum.common.domestic=lbl_Domestic
## 共用-国际
ReportDetailHeaderEnum.common.international=lbl_International
## 共用-协议
ReportDetailHeaderEnum.common.agreement=Index.Agreement
## 共用-非协议
ReportDetailHeaderEnum.common.nonAgreement=Index.NonAgreement
# 公共-小计
ReportDetailHeaderEnum.common.subtotal=Index.total
# 总计
ReportDetailHeaderEnum.totalV=Index.sum
# 月份
ReportDetailHeaderEnum.dimension=Index.month

#参数校验
OnlineReportDetailHandler.startTime=开始时间不能为空
OnlineReportDetailHandler.endTime=结束时间不能为空
OnlineReportDetailHandler.time.format=开始时间必须小于结束时间
OnlineReportDetailHandler.bu=查询产线不能为空
OnlineReportDetailHandler.baseCondition=基础查询条件不能为空
OnlineReportDetailHandler.uId=uId不能为空
OnlineReportDetailHandler.yoyType=同比类型不能为空

ProductTypeEnum.dom=国内
ProductTypeEnum.inter=国际


Report.hotel.orderstatus=已完成

Report.other=其他

Report.hotel.roomshare.room=同住
Report.hotel.roomshare.travel=同行
Report.hotel.roomshare.all=同住+同行

Report.hotel.roomshare.single=单人


Report.threeAgreement=三方协议
Report.Premium=两方尊享

Report.Member=会员

Report.corpPayType=因公

Report.Deal=成交


Report.FlightRefundtype.Special=特殊事件
Report.FlightRefundtype.Voluntary=自愿
Report.FlightRefundtype.Involuntary=航变
Report.FlightRefundtype.Other=其他

Report.CarMixpayment=是


Report.country.china=中国

Report.firstTier=一线

Report.ReportLib.Contient.Asia=亚洲
Report.ReportLib.Contient.Europe=欧洲
Report.ReportLib.Contient.Oceania=大洋洲
Report.ReportLib.Contient.NorthAmerica=北美洲
Report.ReportLib.Contient.SouthAmerica=南美洲
Report.ReportLib.Contient.Africa=非洲
Report.ReportLib.Contient.Antarctica=南极洲
Report.ReportLib.flt.mixpayment=混付
Report.ReportLib.PrepayType.accnt=公司账户
Report.ReportLib.rebookPrepaytypename.accnt=公账支付
Report.ReportLib.rebookPrepaytypename.UnionPay=银联公账支付



