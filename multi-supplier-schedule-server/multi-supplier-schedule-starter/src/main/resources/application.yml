spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: multi-supplier-schedule
app:
  id: multi-supplier-schedule-new
apollo:
  meta: http://**************:51000
  bootstrap:
    enabled: true
    namespaces: application,corpgovernment.multi-supplier,corpgovernment.common,corpgovernment.field-dictionary,corpgovernment.mss.BYD,corpgovernment.mss.XGJT,corpgovernment.mss.COFCO,corpgovernment.mss.SEC,corpgovernment.mss.CETC,corpgovernment.mss.CTRIP,corpgovernment.mss.DP,corpgovernment.tenant.dictionary
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
