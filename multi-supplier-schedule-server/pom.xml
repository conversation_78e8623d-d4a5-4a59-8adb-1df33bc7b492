<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.corpgovernment</groupId>
        <artifactId>multi-supplier-schedule-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>multi-supplier-schedule-server</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>multi-supplier-schedule-starter</module>
        <module>multi-supplier-schedule-infrastructure</module>
        <module>multi-supplier-schedule-domain</module>
        <module>multi-supplier-schedule-app</module>
        <module>multi-supplier-schedule-web</module>
    </modules>

    <properties>
        <redis-handler.version>2.0.0</redis-handler.version>
        <hutool.version>4.6.3</hutool.version>
        <multi-supplier-core-sdk.version>1.0.0-SNAPSHOT</multi-supplier-core-sdk.version>
        <arch-platform-api.version>1.0.2</arch-platform-api.version>
        <arch.generic.version>1.0.8</arch.generic.version>
        <discovery.version>1.0.6.6</discovery.version>
        <skw.version>8.7.0</skw.version>
        <arch-async-redis.version>1.0.5.11</arch-async-redis.version>
        <booking-core-service-sdk.version>1.0.6-SNAPSHOT</booking-core-service-sdk.version>

    </properties>

    <dependencies>
        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>booking-core-service-sdk</artifactId>
            <version>${booking-core-service-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>booking-core-service-dto</artifactId>
            <version>${booking-core-service-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>multi-supplier-core-sdk</artifactId>
            <version>${multi-supplier-core-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>basic-manage-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>flight-booking-api</artifactId>
            <version>${api-version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-logging</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>service-default</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>service-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>service-mysql</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>arch-shard-starter</artifactId>
                    <groupId>com.ctrip.corp.obt</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.corpgovernment</groupId>
            <artifactId>redis-handler</artifactId>
            <version>${redis-handler.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-async-redis</artifactId>
            <version>${arch-async-redis.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-shard-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-platform-api</artifactId>
            <version>${arch-platform-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-core</artifactId>
            <version>${arch.generic.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-event</artifactId>
            <version>${arch.generic.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ctrip.corp.obt</groupId>
            <artifactId>arch-discovery</artifactId>
            <version>${discovery.version}</version>
        </dependency>
        <!-- 为啥启动失败了 -->
    </dependencies>

</project>
