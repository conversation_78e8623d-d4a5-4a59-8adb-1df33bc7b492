package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "name",
    "totalCarbon",
    "saveCarbon",
    "avgCarbon",
    "greenRate"
})
public class FootprintCarbonEmissionInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public FootprintCarbonEmissionInfo(
        String name,
        BigDecimal totalCarbon,
        BigDecimal saveCarbon,
        BigDecimal avgCarbon,
        BigDecimal greenRate) {
        this.name = name;
        this.totalCarbon = totalCarbon;
        this.saveCarbon = saveCarbon;
        this.avgCarbon = avgCarbon;
        this.greenRate = greenRate;
    }

    public FootprintCarbonEmissionInfo() {
    }

    @JsonProperty("name")
    public String name;

    /**
     * 累计碳排
     */
    @JsonProperty("totalCarbon")
    public BigDecimal totalCarbon;

    /**
     * 节省碳排
     */
    @JsonProperty("saveCarbon")
    public BigDecimal saveCarbon;

    /**
     * 平均碳排
     */
    @JsonProperty("avgCarbon")
    public BigDecimal avgCarbon;

    /**
     * 绿色占比
     */
    @JsonProperty("greenRate")
    public BigDecimal greenRate;

    public String getName() {
        return name;
    }

    public void setName(final String name) {
        this.name = name;
    }

    /**
     * 累计碳排
     */
    public BigDecimal getTotalCarbon() {
        return totalCarbon;
    }

    /**
     * 累计碳排
     */
    public void setTotalCarbon(final BigDecimal totalCarbon) {
        this.totalCarbon = totalCarbon;
    }

    /**
     * 节省碳排
     */
    public BigDecimal getSaveCarbon() {
        return saveCarbon;
    }

    /**
     * 节省碳排
     */
    public void setSaveCarbon(final BigDecimal saveCarbon) {
        this.saveCarbon = saveCarbon;
    }

    /**
     * 平均碳排
     */
    public BigDecimal getAvgCarbon() {
        return avgCarbon;
    }

    /**
     * 平均碳排
     */
    public void setAvgCarbon(final BigDecimal avgCarbon) {
        this.avgCarbon = avgCarbon;
    }

    /**
     * 绿色占比
     */
    public BigDecimal getGreenRate() {
        return greenRate;
    }

    /**
     * 绿色占比
     */
    public void setGreenRate(final BigDecimal greenRate) {
        this.greenRate = greenRate;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FootprintCarbonEmissionInfo other = (FootprintCarbonEmissionInfo)obj;
        return
            Objects.equal(this.name, other.name) &&
            Objects.equal(this.totalCarbon, other.totalCarbon) &&
            Objects.equal(this.saveCarbon, other.saveCarbon) &&
            Objects.equal(this.avgCarbon, other.avgCarbon) &&
            Objects.equal(this.greenRate, other.greenRate);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.name == null ? 0 : this.name.hashCode());
        result = 31 * result + (this.totalCarbon == null ? 0 : this.totalCarbon.hashCode());
        result = 31 * result + (this.saveCarbon == null ? 0 : this.saveCarbon.hashCode());
        result = 31 * result + (this.avgCarbon == null ? 0 : this.avgCarbon.hashCode());
        result = 31 * result + (this.greenRate == null ? 0 : this.greenRate.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("name", name)
            .add("totalCarbon", totalCarbon)
            .add("saveCarbon", saveCarbon)
            .add("avgCarbon", avgCarbon)
            .add("greenRate", greenRate)
            .toString();
    }
}
