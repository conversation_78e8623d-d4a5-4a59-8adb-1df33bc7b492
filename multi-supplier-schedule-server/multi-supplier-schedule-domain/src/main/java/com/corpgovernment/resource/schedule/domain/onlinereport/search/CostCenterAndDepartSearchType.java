package com.corpgovernment.resource.schedule.domain.onlinereport.search;

public enum CostCenterAndDepartSearchType {

    COSTCENTER(0),
    DEPARTMENT(1),
    CORP(3);

    private final int value;

    CostCenterAndDepartSearchType(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public int getValue() {
        return value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public static CostCenterAndDepartSearchType findByValue(int value) {
        switch (value) {
            case 0:
                return COSTCENTER;
            case 1:
                return DEPARTMENT;
            default:
                return null;
        }
    }
}
