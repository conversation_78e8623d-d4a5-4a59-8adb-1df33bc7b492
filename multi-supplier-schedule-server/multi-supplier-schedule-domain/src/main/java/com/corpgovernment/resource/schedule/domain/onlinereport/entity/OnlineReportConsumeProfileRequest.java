package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * **消费概况***************
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "lang",
    "basecondition",
    "queryBus",
    "dateDimension",
    "extData"
})
public class OnlineReportConsumeProfileRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportConsumeProfileRequest(
        String lang,
        BaseQueryCondition basecondition,
        List<QueryReportBuTypeEnum> queryBus,
        QueryReportAggDateDimensionEnum dateDimension,
        Map<String, String> extData) {
        this.lang = lang;
        this.basecondition = basecondition;
        this.queryBus = queryBus;
        this.dateDimension = dateDimension;
        this.extData = extData;
    }

    public OnlineReportConsumeProfileRequest() {
    }

    /**
     * 语言
     */
    @JsonProperty("lang")
    public String lang;

    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    /**
     * 机酒火
     */
    @JsonProperty("queryBus")
    public List<QueryReportBuTypeEnum> queryBus;

    /**
     * 聚合日期维度：月-季度-半年-年
     */
    @JsonProperty("dateDimension")
    public QueryReportAggDateDimensionEnum dateDimension;

    /**
     * 扩展字段
     */
    @JsonProperty("extData")
    public Map<String, String> extData;

    /**
     * 语言
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * 机酒火
     */
    public List<QueryReportBuTypeEnum> getQueryBus() {
        return queryBus;
    }

    /**
     * 机酒火
     */
    public void setQueryBus(final List<QueryReportBuTypeEnum> queryBus) {
        this.queryBus = queryBus;
    }

    /**
     * 聚合日期维度：月-季度-半年-年
     */
    public QueryReportAggDateDimensionEnum getDateDimension() {
        return dateDimension;
    }

    /**
     * 聚合日期维度：月-季度-半年-年
     */
    public void setDateDimension(final QueryReportAggDateDimensionEnum dateDimension) {
        this.dateDimension = dateDimension;
    }

    /**
     * 扩展字段
     */
    public Map<String, String> getExtData() {
        return extData;
    }

    /**
     * 扩展字段
     */
    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportConsumeProfileRequest other = (OnlineReportConsumeProfileRequest)obj;
        return
            Objects.equal(this.lang, other.lang) &&
            Objects.equal(this.basecondition, other.basecondition) &&
            Objects.equal(this.queryBus, other.queryBus) &&
            Objects.equal(this.dateDimension, other.dateDimension) &&
            Objects.equal(this.extData, other.extData);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.queryBus == null ? 0 : this.queryBus.hashCode());
        result = 31 * result + (this.dateDimension == null ? 0 : this.dateDimension.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("lang", lang)
            .add("basecondition", basecondition)
            .add("queryBus", queryBus)
            .add("dateDimension", dateDimension)
            .add("extData", extData)
            .toString();
    }
}
