package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 行为分析-机票折扣分布
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "range",
    "totalQuantity",
    "quantityPercent",
    "avgPreOrderDate",
    "totalAmount",
    "amountPercent",
    "totalPrice"
})
public class FltDiscountRangeInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    

    

    public FltDiscountRangeInfo(
        String range,
        Integer totalQuantity,
        Double quantityPercent,
        Integer avgPreOrderDate,
        BigDecimal totalAmount,
        Double amountPercent,
        BigDecimal totalPrice) {
        this.range = range;
        this.totalQuantity = totalQuantity;
        this.quantityPercent = quantityPercent;
        this.avgPreOrderDate = avgPreOrderDate;
        this.totalAmount = totalAmount;
        this.amountPercent = amountPercent;
        this.totalPrice = totalPrice;
    }

    public FltDiscountRangeInfo() {
    }

    /**
     * 折扣区间
     */
    @JsonProperty("range")
    public String range;

    /**
     * 票张数
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;

    /**
     * 张数占比
     */
    @JsonProperty("quantityPercent")
    public Double quantityPercent;

    /**
     * 平均提前预定天数
     */
    @JsonProperty("avgPreOrderDate")
    public Integer avgPreOrderDate;

    /**
     * 消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;

    /**
     * 金额占比
     */
    @JsonProperty("amountPercent")
    public Double amountPercent;

    /**
     * 成交净价
     */
    @JsonProperty("totalPrice")
    public BigDecimal totalPrice;

    /**
     * 折扣区间
     */
    public String getRange() {
        return range;
    }

    /**
     * 折扣区间
     */
    public void setRange(final String range) {
        this.range = range;
    }

    /**
     * 票张数
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 票张数
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 张数占比
     */
    public Double getQuantityPercent() {
        return quantityPercent;
    }

    /**
     * 张数占比
     */
    public void setQuantityPercent(final Double quantityPercent) {
        this.quantityPercent = quantityPercent;
    }

    /**
     * 平均提前预定天数
     */
    public Integer getAvgPreOrderDate() {
        return avgPreOrderDate;
    }

    /**
     * 平均提前预定天数
     */
    public void setAvgPreOrderDate(final Integer avgPreOrderDate) {
        this.avgPreOrderDate = avgPreOrderDate;
    }

    /**
     * 消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 金额占比
     */
    public Double getAmountPercent() {
        return amountPercent;
    }

    /**
     * 金额占比
     */
    public void setAmountPercent(final Double amountPercent) {
        this.amountPercent = amountPercent;
    }

    /**
     * 成交净价
     */
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    /**
     * 成交净价
     */
    public void setTotalPrice(final BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FltDiscountRangeInfo other = (FltDiscountRangeInfo)obj;
        return
            Objects.equal(this.range, other.range) &&
            Objects.equal(this.totalQuantity, other.totalQuantity) &&
            Objects.equal(this.quantityPercent, other.quantityPercent) &&
            Objects.equal(this.avgPreOrderDate, other.avgPreOrderDate) &&
            Objects.equal(this.totalAmount, other.totalAmount) &&
            Objects.equal(this.amountPercent, other.amountPercent) &&
            Objects.equal(this.totalPrice, other.totalPrice);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.range == null ? 0 : this.range.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.quantityPercent == null ? 0 : this.quantityPercent.hashCode());
        result = 31 * result + (this.avgPreOrderDate == null ? 0 : this.avgPreOrderDate.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.amountPercent == null ? 0 : this.amountPercent.hashCode());
        result = 31 * result + (this.totalPrice == null ? 0 : this.totalPrice.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("range", range)
            .add("totalQuantity", totalQuantity)
            .add("quantityPercent", quantityPercent)
            .add("avgPreOrderDate", avgPreOrderDate)
            .add("totalAmount", totalAmount)
            .add("amountPercent", amountPercent)
            .add("totalPrice", totalPrice)
            .toString();
    }
}
