//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2015.07.02 at 11:10:02 AM CST
//


package com.corpgovernment.resource.schedule.domain.onlinereport.types;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.ctriposs.baiji.rpc.common.types package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 *
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _CheckHealthRequest_QNAME = new QName("http://soa.ctrip.com/common/types/v1", "CheckHealthRequest");
    private final static QName _CheckHealthResponse_QNAME = new QName("http://soa.ctrip.com/common/types/v1", "CheckHealthResponse");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.ctriposs.baiji.rpc.common.types
     *
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType }
     *
     */
    public CheckHealthRequestType createCheckHealthRequestType() {
        return new CheckHealthRequestType();
    }

    /**
     * Create an instance of {@link com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType }
     *
     */
    public CheckHealthResponseType createCheckHealthResponseType() {
        return new CheckHealthResponseType();
    }

    /**
     * Create an instance of {@link com.ctriposs.baiji.rpc.common.types.ErrorFieldType }
     *
     */
    public ErrorFieldType createErrorFieldType() {
        return new ErrorFieldType();
    }

    /**
     * Create an instance of {@link com.ctriposs.baiji.rpc.common.types.PaginationOutputType }
     *
     */
    public PaginationOutputType createPaginationOutputType() {
        return new PaginationOutputType();
    }

    /**
     * Create an instance of {@link com.ctriposs.baiji.rpc.common.types.ResponseStatusType }
     *
     */
    public ResponseStatusType createResponseStatusType() {
        return new ResponseStatusType();
    }

    /**
     * Create an instance of {@link com.ctriposs.baiji.rpc.common.types.GenericErrorResponseType }
     *
     */
    public GenericErrorResponseType createGenericErrorResponseType() {
        return new GenericErrorResponseType();
    }

    /**
     * Create an instance of {@link com.ctriposs.baiji.rpc.common.types.ErrorDataType }
     *
     */
    public ErrorDataType createErrorDataType() {
        return new ErrorDataType();
    }

    /**
     * Create an instance of {@link com.ctriposs.baiji.rpc.common.types.ExtensionType }
     *
     */
    public ExtensionType createExtensionType() {
        return new ExtensionType();
    }

    /**
     * Create an instance of {@link com.ctriposs.baiji.rpc.common.types.CommonRequestType }
     *
     */
    public CommonRequestType createCommonRequestType() {
        return new CommonRequestType();
    }

    /**
     * Create an instance of {@link com.ctriposs.baiji.rpc.common.types.PaginationInputType }
     *
     */
    public PaginationInputType createPaginationInputType() {
        return new PaginationInputType();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType }{@code >}}
     *
     */
    @XmlElementDecl(namespace = "http://soa.ctrip.com/common/types/v1", name = "CheckHealthRequest")
    public JAXBElement<CheckHealthRequestType> createCheckHealthRequest(CheckHealthRequestType value) {
        return new JAXBElement<CheckHealthRequestType>(_CheckHealthRequest_QNAME, CheckHealthRequestType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType }{@code >}}
     *
     */
    @XmlElementDecl(namespace = "http://soa.ctrip.com/common/types/v1", name = "CheckHealthResponse")
    public JAXBElement<CheckHealthResponseType> createCheckHealthResponse(CheckHealthResponseType value) {
        return new JAXBElement<CheckHealthResponseType>(_CheckHealthResponse_QNAME, CheckHealthResponseType.class, null, value);
    }

}
