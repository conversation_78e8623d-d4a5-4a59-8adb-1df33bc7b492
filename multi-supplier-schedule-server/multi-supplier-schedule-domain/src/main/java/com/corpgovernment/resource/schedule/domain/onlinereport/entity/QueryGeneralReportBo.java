package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import java.util.List;
import java.util.Map;

/**
 * @Auther: he_chen
 * @Date: 2019/8/13 10:52
 * @Description: 查询总概报表参数
 */
public class QueryGeneralReportBo {
	/**
	 * 报告ID
	 */
	private String reportId;

	/**
	 * 报表名称
	 */
	private String reportName;

	/**
	 * 筛选项
	 */
	private Map<String, Object> filterList;
	/**
	 * 维度
	 */
	private List<String> dimensionList;
	/**
	 * 统计项
	 */
	private List<String> statisticalList;

	public Map<String, Object> getFilterList() {
		return filterList;
	}

	public void setFilterList(Map<String, Object> filterList) {
		this.filterList = filterList;
	}

	public List<String> getDimensionList() {
		return dimensionList;
	}

	public void setDimensionList(List<String> dimensionList) {
		this.dimensionList = dimensionList;
	}

	public List<String> getStatisticalList() {
		return statisticalList;
	}

	public void setStatisticalList(List<String> statisticalList) {
		this.statisticalList = statisticalList;
	}

	public String getReportId() {
		return reportId;
	}

	public void setReportId(String reportId) {
		this.reportId = reportId;
	}

	public String getReportName() {
		return reportName;
	}

	public void setReportName(String reportName) {
		this.reportName = reportName;
	}
}
