package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 机票提前预定天数分布
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "title",
    "preOrderNotice",
    "headerData",
    "bodyList"
})
public class FlightAdvanceBookDaysData implements Serializable {
    private static final long serialVersionUID = 1L;





    public FlightAdvanceBookDaysData(
        String title,
        String preOrderNotice,
        List<HeaderKeyValMap> headerData,
        List<FlightAdvanceBookDaysValue> bodyList) {
        this.title = title;
        this.preOrderNotice = preOrderNotice;
        this.headerData = headerData;
        this.bodyList = bodyList;
    }

    public FlightAdvanceBookDaysData() {
    }

    /**
     * 标题
     */
    @JsonProperty("title")
    public String title;

    /**
     * 提前预定通知文案
     */
    @JsonProperty("preOrderNotice")
    public String preOrderNotice;

    @JsonProperty("headerData")
    public List<HeaderKeyValMap> headerData;

    /**
     * 明细-数据
     */
    @JsonProperty("bodyList")
    public List<FlightAdvanceBookDaysValue> bodyList;

    /**
     * 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 标题
     */
    public void setTitle(final String title) {
        this.title = title;
    }

    /**
     * 提前预定通知文案
     */
    public String getPreOrderNotice() {
        return preOrderNotice;
    }

    /**
     * 提前预定通知文案
     */
    public void setPreOrderNotice(final String preOrderNotice) {
        this.preOrderNotice = preOrderNotice;
    }
    public List<HeaderKeyValMap> getHeaderData() {
        return headerData;
    }

    public void setHeaderData(final List<HeaderKeyValMap> headerData) {
        this.headerData = headerData;
    }

    /**
     * 明细-数据
     */
    public List<FlightAdvanceBookDaysValue> getBodyList() {
        return bodyList;
    }

    /**
     * 明细-数据
     */
    public void setBodyList(final List<FlightAdvanceBookDaysValue> bodyList) {
        this.bodyList = bodyList;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightAdvanceBookDaysData other = (FlightAdvanceBookDaysData)obj;
        return
            Objects.equal(this.title, other.title) &&
            Objects.equal(this.preOrderNotice, other.preOrderNotice) &&
            Objects.equal(this.headerData, other.headerData) &&
            Objects.equal(this.bodyList, other.bodyList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.title == null ? 0 : this.title.hashCode());
        result = 31 * result + (this.preOrderNotice == null ? 0 : this.preOrderNotice.hashCode());
        result = 31 * result + (this.headerData == null ? 0 : this.headerData.hashCode());
        result = 31 * result + (this.bodyList == null ? 0 : this.bodyList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("title", title)
            .add("preOrderNotice", preOrderNotice)
            .add("headerData", headerData)
            .add("bodyList", bodyList)
            .toString();
    }
}
