package com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib;

/**
 * <AUTHOR>
 * @Date 2020/10/23
 * @Description： 常量
 */
public class ReportLibCommonEntity {
    /**
     * 会员酒店
     */
    public static final String MEMBER_HOTEL = "M";
    /**
     * 协议酒店
     */
    public static final String CONTRACT_HOTEL = "C";
    /**
     * 国内酒店
     */
    public static final String DOMESTIC_HOTEL = "F";
    /**
     * 海外酒店
     */
    public static final String FOREIGN_HOTEL = "T";
    /**
     * 港澳台酒店
     */
    public static final String OVERSEA_HOTEL = "O";
    /**
     * 预定方式-APP
     */
    public static final String APP = "M";
    /**
     * 预定方式-Online
     */
    public static final String ONLINE = "T";
    /**
     * 预定方式-Offline
     */
    public static final String OFFLINE = "F";
    /**
     * KPI-COUNT
     */
    public static final Integer KPI_COUNT = 14;
    /**
     * TOP5
     */
    public static final Integer TOP_FIVE = 5;
    /**
     * TOP10
     */
    public static final Integer TOP_TEN = 10;
    /**
     * TOP100
     */
    public static final Integer TOP_ONE_HUNDRED = 100;
    /**
     * 12个月
     */
    public static final Integer MONTH_TWELVE = 12;
    /**
     * 损失明细-TOP13
     */
    public static final Integer LOSS_DETAIL_COUNT = 13;
    /**
     * 航线和提前预定天数-TOP10
     */
    public static final Integer FLIGHT_CITY_AND_PRE_ORDER_COUNT = 10;
    /**
     * 承运商-TOP5
     */
    public static final Integer FLIGHT_CARRIER_INT_START_ROW_COUNT = 5;
    /**
     * 航线-TOP5
     */
    public static final Integer FLIGHT_CITY_COUNT = 5;
    /**
     * 协议航空采购-TOP40
     */
    public static final Integer FLIGHT_AGREEMENT_AIR_PURCHASE_COUNT = 40;
    /**
     * 部门酒店消费-TOP101
     */
    public static final Integer HOTEL_DEPT_START_ROW_COUNT = 101;
}
