package com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 差标类型枚举
 * @create 2024-12-23 15:03
 */
@Getter
@AllArgsConstructor
public enum TravelStandardStrategyEnum {
    
    ORDER("order", "按订单管控"),
    ROOM("room", "按房间管控")
    ;
    
    private final String code;
    
    private final String info;
    
    private static final Map<String, TravelStandardStrategyEnum> map = new HashMap<>();
    
    static {
        for (TravelStandardStrategyEnum tmpEnum : values()) {
            map.put(tmpEnum.getCode(), tmpEnum);
            map.put(tmpEnum.getInfo(), tmpEnum);
        }
    }
    
    public static TravelStandardStrategyEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return map.get(key);
    }
    
}
