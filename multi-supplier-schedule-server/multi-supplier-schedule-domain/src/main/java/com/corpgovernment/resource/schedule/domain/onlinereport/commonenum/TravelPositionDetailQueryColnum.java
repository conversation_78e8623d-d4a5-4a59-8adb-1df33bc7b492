package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;

/**
 * 差旅定位-足迹明细查询字段
 */
public enum TravelPositionDetailQueryColnum {

    /**
     * 预订人
     */
    BOOKER(0),

    /**
     * 出行人
     */
    TRAVELER(1),

    /**
     * 航班号
     */
    FLIGHT_NO(2),

    /**
     * 火车班次
     */
    TRAIN_NO(3),

    /**
     * 酒店名称
     */
    HOTEL_NAME(4);

    private final int value;

    TravelPositionDetailQueryColnum(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public int getValue() {
        return value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public static TravelPositionDetailQueryColnum findByValue(int value) {
        switch (value) {
            case 0:
                return BOOKER;
            case 1:
                return TRAVELER;
            case 2:
                return FLIGHT_NO;
            case 3:
                return TRAIN_NO;
            case 4:
                return HOTEL_NAME;
            default:
                return null;
        }
    }
}
