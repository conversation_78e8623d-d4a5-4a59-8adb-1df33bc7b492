package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 评分指标
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "yoy",
    "mom"
})
public class MarkMetricInfoYoyAndMon implements Serializable {
    private static final long serialVersionUID = 1L;





    public MarkMetricInfoYoyAndMon(
        MarkMetricInfo yoy,
        MarkMetricInfo mom) {
        this.yoy = yoy;
        this.mom = mom;
    }

    public MarkMetricInfoYoyAndMon() {
    }

    /**
     * 同比
     */
    @JsonProperty("yoy")
    public MarkMetricInfo yoy;

    /**
     * 环比
     */
    @JsonProperty("mom")
    public MarkMetricInfo mom;

    /**
     * 同比
     */
    public MarkMetricInfo getYoy() {
        return yoy;
    }

    /**
     * 同比
     */
    public void setYoy(final MarkMetricInfo yoy) {
        this.yoy = yoy;
    }

    /**
     * 环比
     */
    public MarkMetricInfo getMom() {
        return mom;
    }

    /**
     * 环比
     */
    public void setMom(final MarkMetricInfo mom) {
        this.mom = mom;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final MarkMetricInfoYoyAndMon other = (MarkMetricInfoYoyAndMon)obj;
        return
            Objects.equal(this.yoy, other.yoy) &&
            Objects.equal(this.mom, other.mom);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.yoy == null ? 0 : this.yoy.hashCode());
        result = 31 * result + (this.mom == null ? 0 : this.mom.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("yoy", yoy)
            .add("mom", mom)
            .toString();
    }
}
