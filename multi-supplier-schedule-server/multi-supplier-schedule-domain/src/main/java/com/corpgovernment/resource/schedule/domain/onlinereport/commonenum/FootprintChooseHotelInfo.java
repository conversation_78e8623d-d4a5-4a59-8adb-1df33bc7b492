package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "name",
    "quantity",
    "amount",
    "htlMostHotelgroupname",
    "htlMostCity",
    "htlStar",
    "htlQtyMostHotelgroupname",
    "htlQtyRate"
})
public class FootprintChooseHotelInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public FootprintChooseHotelInfo(
        String name,
        Integer quantity,
        BigDecimal amount,
        String htlMostHotelgroupname,
        String htlMostCity,
        Integer htlStar,
        String htlQtyMostHotelgroupname,
        BigDecimal htlQtyRate) {
        this.name = name;
        this.quantity = quantity;
        this.amount = amount;
        this.htlMostHotelgroupname = htlMostHotelgroupname;
        this.htlMostCity = htlMostCity;
        this.htlStar = htlStar;
        this.htlQtyMostHotelgroupname = htlQtyMostHotelgroupname;
        this.htlQtyRate = htlQtyRate;
    }

    public FootprintChooseHotelInfo() {
    }

    /**
     * 名称
     */
    @JsonProperty("name")
    public String name;

    /**
     * 张、夜、次
     */
    @JsonProperty("quantity")
    public Integer quantity;

    /**
     * 金额
     */
    @JsonProperty("amount")
    public BigDecimal amount;

    /**
     * 最多夜数酒店集团名称
     */
    @JsonProperty("htlMostHotelgroupname")
    public String htlMostHotelgroupname;

    /**
     * 最多夜数酒店所在城市
     */
    @JsonProperty("htlMostCity")
    public String htlMostCity;

    /**
     * 最多住宿酒店星级
     */
    @JsonProperty("htlStar")
    public Integer htlStar;

    /**
     * 最多夜数酒店集团名称的住宿夜数
     */
    @JsonProperty("htlQtyMostHotelgroupname")
    public String htlQtyMostHotelgroupname;

    /**
     * 酒店夜数公司排行超越人数百分比
     */
    @JsonProperty("htlQtyRate")
    public BigDecimal htlQtyRate;

    /**
     * 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 名称
     */
    public void setName(final String name) {
        this.name = name;
    }

    /**
     * 张、夜、次
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 张、夜、次
     */
    public void setQuantity(final Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * 金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 金额
     */
    public void setAmount(final BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 最多夜数酒店集团名称
     */
    public String getHtlMostHotelgroupname() {
        return htlMostHotelgroupname;
    }

    /**
     * 最多夜数酒店集团名称
     */
    public void setHtlMostHotelgroupname(final String htlMostHotelgroupname) {
        this.htlMostHotelgroupname = htlMostHotelgroupname;
    }

    /**
     * 最多夜数酒店所在城市
     */
    public String getHtlMostCity() {
        return htlMostCity;
    }

    /**
     * 最多夜数酒店所在城市
     */
    public void setHtlMostCity(final String htlMostCity) {
        this.htlMostCity = htlMostCity;
    }

    /**
     * 最多住宿酒店星级
     */
    public Integer getHtlStar() {
        return htlStar;
    }

    /**
     * 最多住宿酒店星级
     */
    public void setHtlStar(final Integer htlStar) {
        this.htlStar = htlStar;
    }

    /**
     * 最多夜数酒店集团名称的住宿夜数
     */
    public String getHtlQtyMostHotelgroupname() {
        return htlQtyMostHotelgroupname;
    }

    /**
     * 最多夜数酒店集团名称的住宿夜数
     */
    public void setHtlQtyMostHotelgroupname(final String htlQtyMostHotelgroupname) {
        this.htlQtyMostHotelgroupname = htlQtyMostHotelgroupname;
    }

    /**
     * 酒店夜数公司排行超越人数百分比
     */
    public BigDecimal getHtlQtyRate() {
        return htlQtyRate;
    }

    /**
     * 酒店夜数公司排行超越人数百分比
     */
    public void setHtlQtyRate(final BigDecimal htlQtyRate) {
        this.htlQtyRate = htlQtyRate;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FootprintChooseHotelInfo other = (FootprintChooseHotelInfo)obj;
        return
            Objects.equal(this.name, other.name) &&
            Objects.equal(this.quantity, other.quantity) &&
            Objects.equal(this.amount, other.amount) &&
            Objects.equal(this.htlMostHotelgroupname, other.htlMostHotelgroupname) &&
            Objects.equal(this.htlMostCity, other.htlMostCity) &&
            Objects.equal(this.htlStar, other.htlStar) &&
            Objects.equal(this.htlQtyMostHotelgroupname, other.htlQtyMostHotelgroupname) &&
            Objects.equal(this.htlQtyRate, other.htlQtyRate);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.name == null ? 0 : this.name.hashCode());
        result = 31 * result + (this.quantity == null ? 0 : this.quantity.hashCode());
        result = 31 * result + (this.amount == null ? 0 : this.amount.hashCode());
        result = 31 * result + (this.htlMostHotelgroupname == null ? 0 : this.htlMostHotelgroupname.hashCode());
        result = 31 * result + (this.htlMostCity == null ? 0 : this.htlMostCity.hashCode());
        result = 31 * result + (this.htlStar == null ? 0 : this.htlStar.hashCode());
        result = 31 * result + (this.htlQtyMostHotelgroupname == null ? 0 : this.htlQtyMostHotelgroupname.hashCode());
        result = 31 * result + (this.htlQtyRate == null ? 0 : this.htlQtyRate.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("name", name)
            .add("quantity", quantity)
            .add("amount", amount)
            .add("htlMostHotelgroupname", htlMostHotelgroupname)
            .add("htlMostCity", htlMostCity)
            .add("htlStar", htlStar)
            .add("htlQtyMostHotelgroupname", htlQtyMostHotelgroupname)
            .add("htlQtyRate", htlQtyRate)
            .toString();
    }
}
