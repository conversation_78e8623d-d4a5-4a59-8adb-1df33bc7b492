package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 实时数量数据分析
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "point",
    "currentQuantity",
    "preQuantity"
})
public class RealTimeQuantityTrendInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public RealTimeQuantityTrendInfo(
        String point,
        Integer currentQuantity,
        Integer preQuantity) {
        this.point = point;
        this.currentQuantity = currentQuantity;
        this.preQuantity = preQuantity;
    }

    public RealTimeQuantityTrendInfo() {
    }

    @JsonProperty("point")
    public String point;

    /**
     * 当前数量
     */
    @JsonProperty("currentQuantity")
    public Integer currentQuantity;

    /**
     * 昨日/上周同期数量
     */
    @JsonProperty("preQuantity")
    public Integer preQuantity;

    public String getPoint() {
        return point;
    }

    public void setPoint(final String point) {
        this.point = point;
    }

    /**
     * 当前数量
     */
    public Integer getCurrentQuantity() {
        return currentQuantity;
    }

    /**
     * 当前数量
     */
    public void setCurrentQuantity(final Integer currentQuantity) {
        this.currentQuantity = currentQuantity;
    }

    /**
     * 昨日/上周同期数量
     */
    public Integer getPreQuantity() {
        return preQuantity;
    }

    /**
     * 昨日/上周同期数量
     */
    public void setPreQuantity(final Integer preQuantity) {
        this.preQuantity = preQuantity;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final RealTimeQuantityTrendInfo other = (RealTimeQuantityTrendInfo)obj;
        return
            Objects.equal(this.point, other.point) &&
            Objects.equal(this.currentQuantity, other.currentQuantity) &&
            Objects.equal(this.preQuantity, other.preQuantity);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.point == null ? 0 : this.point.hashCode());
        result = 31 * result + (this.currentQuantity == null ? 0 : this.currentQuantity.hashCode());
        result = 31 * result + (this.preQuantity == null ? 0 : this.preQuantity.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("point", point)
            .add("currentQuantity", currentQuantity)
            .add("preQuantity", preQuantity)
            .toString();
    }
}
