package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 差旅足迹明细
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "subject",
    "totalCount",
    "legends",
    "data"
})
public class TravelPositionDetailInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public TravelPositionDetailInfo(
        String subject,
        Integer totalCount,
        List<OnlineReportTrendLegend> legends,
        List<TravelPositionDetailInfoData> data) {
        this.subject = subject;
        this.totalCount = totalCount;
        this.legends = legends;
        this.data = data;
    }

    public TravelPositionDetailInfo() {
    }

    /**
     * 主题
     */
    @JsonProperty("subject")
    public String subject;

    /**
     * 总数量
     */
    @JsonProperty("totalCount")
    public Integer totalCount;

    @JsonProperty("legends")
    public List<OnlineReportTrendLegend> legends;

    @JsonProperty("data")
    public List<TravelPositionDetailInfoData> data;

    /**
     * 主题
     */
    public String getSubject() {
        return subject;
    }

    /**
     * 主题
     */
    public void setSubject(final String subject) {
        this.subject = subject;
    }

    /**
     * 总数量
     */
    public Integer getTotalCount() {
        return totalCount;
    }

    /**
     * 总数量
     */
    public void setTotalCount(final Integer totalCount) {
        this.totalCount = totalCount;
    }
    public List<OnlineReportTrendLegend> getLegends() {
        return legends;
    }

    public void setLegends(final List<OnlineReportTrendLegend> legends) {
        this.legends = legends;
    }
    public List<TravelPositionDetailInfoData> getData() {
        return data;
    }

    public void setData(final List<TravelPositionDetailInfoData> data) {
        this.data = data;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TravelPositionDetailInfo other = (TravelPositionDetailInfo)obj;
        return
            Objects.equal(this.subject, other.subject) &&
            Objects.equal(this.totalCount, other.totalCount) &&
            Objects.equal(this.legends, other.legends) &&
            Objects.equal(this.data, other.data);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.subject == null ? 0 : this.subject.hashCode());
        result = 31 * result + (this.totalCount == null ? 0 : this.totalCount.hashCode());
        result = 31 * result + (this.legends == null ? 0 : this.legends.hashCode());
        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("subject", subject)
            .add("totalCount", totalCount)
            .add("legends", legends)
            .add("data", data)
            .toString();
    }
}
