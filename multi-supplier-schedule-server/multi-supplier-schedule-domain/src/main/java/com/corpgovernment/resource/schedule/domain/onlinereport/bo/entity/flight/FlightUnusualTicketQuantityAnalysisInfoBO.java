package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

/**
 * @Description: 异常机票票张分析详情
 * <AUTHOR>
 * @Date 2019/5/31
 */
public class FlightUnusualTicketQuantityAnalysisInfoBO {
    /*总张数*/
    private Integer quantity;
    /*同比总张数*/
    private Integer yearOnYearQuantity;
    /*环比总张数*/
    private Integer monthOnMonthQuantity;

    public FlightUnusualTicketQuantityAnalysisInfoBO() {
        this.quantity=0;
        this.yearOnYearQuantity=0;
        this.monthOnMonthQuantity=0;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getYearOnYearQuantity() {
        return yearOnYearQuantity;
    }

    public void setYearOnYearQuantity(Integer yearOnYearQuantity) {
        this.yearOnYearQuantity = yearOnYearQuantity;
    }

    public Integer getMonthOnMonthQuantity() {
        return monthOnMonthQuantity;
    }

    public void setMonthOnMonthQuantity(Integer monthOnMonthQuantity) {
        this.monthOnMonthQuantity = monthOnMonthQuantity;
    }
}
