package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 心程贝数据
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "baseCondition"
})
public class OnlineReportWelfareStockDataRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportWelfareStockDataRequest(
        BaseQueryCondition baseCondition) {
        this.baseCondition = baseCondition;
    }

    public OnlineReportWelfareStockDataRequest() {
    }

    /**
     * 基本查询条件
     */
    @JsonProperty("baseCondition")
    public BaseQueryCondition baseCondition;

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBaseCondition() {
        return baseCondition;
    }

    /**
     * 基本查询条件
     */
    public void setBaseCondition(final BaseQueryCondition baseCondition) {
        this.baseCondition = baseCondition;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportWelfareStockDataRequest other = (OnlineReportWelfareStockDataRequest)obj;
        return
            Objects.equal(this.baseCondition, other.baseCondition);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.baseCondition == null ? 0 : this.baseCondition.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("baseCondition", baseCondition)
            .toString();
    }
}
