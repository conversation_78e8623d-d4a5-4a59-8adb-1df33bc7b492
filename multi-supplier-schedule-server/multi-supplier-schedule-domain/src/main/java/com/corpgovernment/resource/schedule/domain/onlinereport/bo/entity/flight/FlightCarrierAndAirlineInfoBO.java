package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;



import java.io.Serializable;
import java.util.List;
/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2019/6/10 13:30
 */
public class FlightCarrierAndAirlineInfoBO implements Serializable{

    private static final long serialVersionUID = 1L;
    /*全部*/
    List<FlightCarrierAndAirlineBasicBO> fltCarrierAndAirlineListA;

    /*国内*/
    List<FlightCarrierAndAirlineBasicBO> fltCarrierAndAirlineListN;

    /*国际*/
    List<FlightCarrierAndAirlineBasicBO> fltCarrierAndAirlineListI;

    public List<FlightCarrierAndAirlineBasicBO> getFltCarrierAndAirlineListA() {
        return fltCarrierAndAirlineListA;
    }

    public void setFltCarrierAndAirlineListA(List<FlightCarrierAndAirlineBasicBO> fltCarrierAndAirlineListA) {
        this.fltCarrierAndAirlineListA = fltCarrierAndAirlineListA;
    }

    public List<FlightCarrierAndAirlineBasicBO> getFltCarrierAndAirlineListN() {
        return fltCarrierAndAirlineListN;
    }

    public void setFltCarrierAndAirlineListN(List<FlightCarrierAndAirlineBasicBO> fltCarrierAndAirlineListN) {
        this.fltCarrierAndAirlineListN = fltCarrierAndAirlineListN;
    }

    public List<FlightCarrierAndAirlineBasicBO> getFltCarrierAndAirlineListI() {
        return fltCarrierAndAirlineListI;
    }

    public void setFltCarrierAndAirlineListI(List<FlightCarrierAndAirlineBasicBO> fltCarrierAndAirlineListI) {
        this.fltCarrierAndAirlineListI = fltCarrierAndAirlineListI;
    }
}
