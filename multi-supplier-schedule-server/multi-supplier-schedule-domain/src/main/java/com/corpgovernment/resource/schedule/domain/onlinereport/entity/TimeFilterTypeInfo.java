package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 时间类型
 * ****************************************报告库订单明细s******************************************
 * ****************************************绿野仙踪-碳排放e******************************************
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "timeType",
    "startTime",
    "endTime"
})
public class TimeFilterTypeInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public TimeFilterTypeInfo(
        String timeType,
        String startTime,
        String endTime) {
        this.timeType = timeType;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public TimeFilterTypeInfo() {
    }

    /**
     * *
     * * 时间类型：
     * * orderDate -预订时间；
     * * dealDate -成交时间；
     * * useDate -使用时间（对应机票的第一程起飞时间；酒店入住时间；火车票出发时间；用车的使用时间；汽车票的出发时间）
     */
    @JsonProperty("timeType")
    public String timeType;

    /**
     * *
     * * 起始时间（格式yyyy-MM-dd）
     */
    @JsonProperty("startTime")
    public String startTime;

    /**
     * *
     * * 截止时间（格式yyyy-MM-dd）
     */
    @JsonProperty("endTime")
    public String endTime;

    /**
     * *
     * * 时间类型：
     * * orderDate -预订时间；
     * * dealDate -成交时间；
     * * useDate -使用时间（对应机票的第一程起飞时间；酒店入住时间；火车票出发时间；用车的使用时间；汽车票的出发时间）
     */
    public String getTimeType() {
        return timeType;
    }

    /**
     * *
     * * 时间类型：
     * * orderDate -预订时间；
     * * dealDate -成交时间；
     * * useDate -使用时间（对应机票的第一程起飞时间；酒店入住时间；火车票出发时间；用车的使用时间；汽车票的出发时间）
     */
    public void setTimeType(final String timeType) {
        this.timeType = timeType;
    }

    /**
     * *
     * * 起始时间（格式yyyy-MM-dd）
     */
    public String getStartTime() {
        return startTime;
    }

    /**
     * *
     * * 起始时间（格式yyyy-MM-dd）
     */
    public void setStartTime(final String startTime) {
        this.startTime = startTime;
    }

    /**
     * *
     * * 截止时间（格式yyyy-MM-dd）
     */
    public String getEndTime() {
        return endTime;
    }

    /**
     * *
     * * 截止时间（格式yyyy-MM-dd）
     */
    public void setEndTime(final String endTime) {
        this.endTime = endTime;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TimeFilterTypeInfo other = (TimeFilterTypeInfo)obj;
        return
            Objects.equal(this.timeType, other.timeType) &&
            Objects.equal(this.startTime, other.startTime) &&
            Objects.equal(this.endTime, other.endTime);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.timeType == null ? 0 : this.timeType.hashCode());
        result = 31 * result + (this.startTime == null ? 0 : this.startTime.hashCode());
        result = 31 * result + (this.endTime == null ? 0 : this.endTime.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("timeType", timeType)
            .add("startTime", startTime)
            .add("endTime", endTime)
            .toString();
    }
}
