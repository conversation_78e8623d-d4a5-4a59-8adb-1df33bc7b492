package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "city",
    "cityName",
    "sumRealPay",
    "sumQuantity",
    "avgPrice",
    "sumRealPayC",
    "sumQuantityC",
    "avgPriceC",
    "avgPriceCCorp",
    "avgPriceCIndustry",
    "sumRealPayM",
    "sumQuantityM",
    "avgPriceM",
    "avgPriceMCorp",
    "avgPriceMIndustry"
})
public class CityHotelInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public CityHotelInfo(
        Long city,
        String cityName,
        BigDecimal sumRealPay,
        Integer sumQuantity,
        BigDecimal avgPrice,
        BigDecimal sumRealPayC,
        Integer sumQuantityC,
        BigDecimal avgPriceC,
        BigDecimal avgPriceCCorp,
        BigDecimal avgPriceCIndustry,
        BigDecimal sumRealPayM,
        Integer sumQuantityM,
        BigDecimal avgPriceM,
        BigDecimal avgPriceMCorp,
        BigDecimal avgPriceMIndustry) {
        this.city = city;
        this.cityName = cityName;
        this.sumRealPay = sumRealPay;
        this.sumQuantity = sumQuantity;
        this.avgPrice = avgPrice;
        this.sumRealPayC = sumRealPayC;
        this.sumQuantityC = sumQuantityC;
        this.avgPriceC = avgPriceC;
        this.avgPriceCCorp = avgPriceCCorp;
        this.avgPriceCIndustry = avgPriceCIndustry;
        this.sumRealPayM = sumRealPayM;
        this.sumQuantityM = sumQuantityM;
        this.avgPriceM = avgPriceM;
        this.avgPriceMCorp = avgPriceMCorp;
        this.avgPriceMIndustry = avgPriceMIndustry;
    }

    public CityHotelInfo() {
    }

    @JsonProperty("city")
    public Long city;

    @JsonProperty("cityName")
    public String cityName;

    @JsonProperty("sumRealPay")
    public BigDecimal sumRealPay;

    @JsonProperty("sumQuantity")
    public Integer sumQuantity;

    @JsonProperty("avgPrice")
    public BigDecimal avgPrice;

    @JsonProperty("sumRealPayC")
    public BigDecimal sumRealPayC;

    @JsonProperty("sumQuantityC")
    public Integer sumQuantityC;

    @JsonProperty("avgPriceC")
    public BigDecimal avgPriceC;

    @JsonProperty("avgPriceCCorp")
    public BigDecimal avgPriceCCorp;

    @JsonProperty("avgPriceCIndustry")
    public BigDecimal avgPriceCIndustry;

    @JsonProperty("sumRealPayM")
    public BigDecimal sumRealPayM;

    @JsonProperty("sumQuantityM")
    public Integer sumQuantityM;

    @JsonProperty("avgPriceM")
    public BigDecimal avgPriceM;

    @JsonProperty("avgPriceMCorp")
    public BigDecimal avgPriceMCorp;

    @JsonProperty("avgPriceMIndustry")
    public BigDecimal avgPriceMIndustry;

    public Long getCity() {
        return city;
    }

    public void setCity(final Long city) {
        this.city = city;
    }
    public String getCityName() {
        return cityName;
    }

    public void setCityName(final String cityName) {
        this.cityName = cityName;
    }
    public BigDecimal getSumRealPay() {
        return sumRealPay;
    }

    public void setSumRealPay(final BigDecimal sumRealPay) {
        this.sumRealPay = sumRealPay;
    }
    public Integer getSumQuantity() {
        return sumQuantity;
    }

    public void setSumQuantity(final Integer sumQuantity) {
        this.sumQuantity = sumQuantity;
    }
    public BigDecimal getAvgPrice() {
        return avgPrice;
    }

    public void setAvgPrice(final BigDecimal avgPrice) {
        this.avgPrice = avgPrice;
    }
    public BigDecimal getSumRealPayC() {
        return sumRealPayC;
    }

    public void setSumRealPayC(final BigDecimal sumRealPayC) {
        this.sumRealPayC = sumRealPayC;
    }
    public Integer getSumQuantityC() {
        return sumQuantityC;
    }

    public void setSumQuantityC(final Integer sumQuantityC) {
        this.sumQuantityC = sumQuantityC;
    }
    public BigDecimal getAvgPriceC() {
        return avgPriceC;
    }

    public void setAvgPriceC(final BigDecimal avgPriceC) {
        this.avgPriceC = avgPriceC;
    }
    public BigDecimal getAvgPriceCCorp() {
        return avgPriceCCorp;
    }

    public void setAvgPriceCCorp(final BigDecimal avgPriceCCorp) {
        this.avgPriceCCorp = avgPriceCCorp;
    }
    public BigDecimal getAvgPriceCIndustry() {
        return avgPriceCIndustry;
    }

    public void setAvgPriceCIndustry(final BigDecimal avgPriceCIndustry) {
        this.avgPriceCIndustry = avgPriceCIndustry;
    }
    public BigDecimal getSumRealPayM() {
        return sumRealPayM;
    }

    public void setSumRealPayM(final BigDecimal sumRealPayM) {
        this.sumRealPayM = sumRealPayM;
    }
    public Integer getSumQuantityM() {
        return sumQuantityM;
    }

    public void setSumQuantityM(final Integer sumQuantityM) {
        this.sumQuantityM = sumQuantityM;
    }
    public BigDecimal getAvgPriceM() {
        return avgPriceM;
    }

    public void setAvgPriceM(final BigDecimal avgPriceM) {
        this.avgPriceM = avgPriceM;
    }
    public BigDecimal getAvgPriceMCorp() {
        return avgPriceMCorp;
    }

    public void setAvgPriceMCorp(final BigDecimal avgPriceMCorp) {
        this.avgPriceMCorp = avgPriceMCorp;
    }
    public BigDecimal getAvgPriceMIndustry() {
        return avgPriceMIndustry;
    }

    public void setAvgPriceMIndustry(final BigDecimal avgPriceMIndustry) {
        this.avgPriceMIndustry = avgPriceMIndustry;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final CityHotelInfo other = (CityHotelInfo)obj;
        return
            Objects.equal(this.city, other.city) &&
            Objects.equal(this.cityName, other.cityName) &&
            Objects.equal(this.sumRealPay, other.sumRealPay) &&
            Objects.equal(this.sumQuantity, other.sumQuantity) &&
            Objects.equal(this.avgPrice, other.avgPrice) &&
            Objects.equal(this.sumRealPayC, other.sumRealPayC) &&
            Objects.equal(this.sumQuantityC, other.sumQuantityC) &&
            Objects.equal(this.avgPriceC, other.avgPriceC) &&
            Objects.equal(this.avgPriceCCorp, other.avgPriceCCorp) &&
            Objects.equal(this.avgPriceCIndustry, other.avgPriceCIndustry) &&
            Objects.equal(this.sumRealPayM, other.sumRealPayM) &&
            Objects.equal(this.sumQuantityM, other.sumQuantityM) &&
            Objects.equal(this.avgPriceM, other.avgPriceM) &&
            Objects.equal(this.avgPriceMCorp, other.avgPriceMCorp) &&
            Objects.equal(this.avgPriceMIndustry, other.avgPriceMIndustry);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.city == null ? 0 : this.city.hashCode());
        result = 31 * result + (this.cityName == null ? 0 : this.cityName.hashCode());
        result = 31 * result + (this.sumRealPay == null ? 0 : this.sumRealPay.hashCode());
        result = 31 * result + (this.sumQuantity == null ? 0 : this.sumQuantity.hashCode());
        result = 31 * result + (this.avgPrice == null ? 0 : this.avgPrice.hashCode());
        result = 31 * result + (this.sumRealPayC == null ? 0 : this.sumRealPayC.hashCode());
        result = 31 * result + (this.sumQuantityC == null ? 0 : this.sumQuantityC.hashCode());
        result = 31 * result + (this.avgPriceC == null ? 0 : this.avgPriceC.hashCode());
        result = 31 * result + (this.avgPriceCCorp == null ? 0 : this.avgPriceCCorp.hashCode());
        result = 31 * result + (this.avgPriceCIndustry == null ? 0 : this.avgPriceCIndustry.hashCode());
        result = 31 * result + (this.sumRealPayM == null ? 0 : this.sumRealPayM.hashCode());
        result = 31 * result + (this.sumQuantityM == null ? 0 : this.sumQuantityM.hashCode());
        result = 31 * result + (this.avgPriceM == null ? 0 : this.avgPriceM.hashCode());
        result = 31 * result + (this.avgPriceMCorp == null ? 0 : this.avgPriceMCorp.hashCode());
        result = 31 * result + (this.avgPriceMIndustry == null ? 0 : this.avgPriceMIndustry.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("city", city)
            .add("cityName", cityName)
            .add("sumRealPay", sumRealPay)
            .add("sumQuantity", sumQuantity)
            .add("avgPrice", avgPrice)
            .add("sumRealPayC", sumRealPayC)
            .add("sumQuantityC", sumQuantityC)
            .add("avgPriceC", avgPriceC)
            .add("avgPriceCCorp", avgPriceCCorp)
            .add("avgPriceCIndustry", avgPriceCIndustry)
            .add("sumRealPayM", sumRealPayM)
            .add("sumQuantityM", sumQuantityM)
            .add("avgPriceM", avgPriceM)
            .add("avgPriceMCorp", avgPriceMCorp)
            .add("avgPriceMIndustry", avgPriceMIndustry)
            .toString();
    }
}
