package com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity;

import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardSourceEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardStrategyEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 差标项
 * @create 2024-12-23 14:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TravelStandardItem {
    
    // 索引
    private Integer index;
    
    // 管控价格上限
    private BigDecimal maxPrice;
    
    // 管控价格下限
    private BigDecimal minPrice;

    /**
     * 浮动金额上限
     */
    private BigDecimal floatMaxPrice;
    
    // 管控星级
    private List<Integer> starList;
    
}
