package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "headerData",
    "bodyList"
})
public class DetailData implements Serializable {
    private static final long serialVersionUID = 1L;



    

    public DetailData(
        List<HeaderKeyValMap> headerData,
        List<ReprotBodyData> bodyList) {
        this.headerData = headerData;
        this.bodyList = bodyList;
    }

    public DetailData() {
    }

    /**
     * header
     */
    @JsonProperty("headerData")
    public List<HeaderKeyValMap> headerData;

    /**
     * 明细-数据
     */
    @JsonProperty("bodyList")
    public List<ReprotBodyData> bodyList;

    /**
     * header
     */
    public List<HeaderKeyValMap> getHeaderData() {
        return headerData;
    }

    /**
     * header
     */
    public void setHeaderData(final List<HeaderKeyValMap> headerData) {
        this.headerData = headerData;
    }

    /**
     * 明细-数据
     */
    public List<ReprotBodyData> getBodyList() {
        return bodyList;
    }

    /**
     * 明细-数据
     */
    public void setBodyList(final List<ReprotBodyData> bodyList) {
        this.bodyList = bodyList;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final DetailData other = (DetailData)obj;
        return
            Objects.equal(this.headerData, other.headerData) &&
            Objects.equal(this.bodyList, other.bodyList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.headerData == null ? 0 : this.headerData.hashCode());
        result = 31 * result + (this.bodyList == null ? 0 : this.bodyList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("headerData", headerData)
            .add("bodyList", bodyList)
            .toString();
    }
}
