package com.corpgovernment.resource.schedule.domain.onlinereport.types;

import com.fasterxml.jackson.annotation.JsonValue;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * (out) The request was processed successfully, but something occurred that may affect your application or the user.
 * For example, Baiji service may have changed a value the user sent in.
 * In this case, Baiji service returns a normal, successful response and also returns the warning.
 */
@XmlType(name = "ErrorClassificationCodeType")
@XmlEnum
public enum ErrorClassificationCodeType {

    /**
     * Indicates that an error has occurred in the service implementation,
     * such as business logic error or other backend error.
     */
    @XmlEnumValue("ServiceError")
    ServiceError(0, "ServiceError"),

    /**
     * Indicates that an error has occurred because of framework-level request validation failure.
     * This is usually because client consumer has attempted to submit invalid data (or missing data)
     * in the request when making API call.
     */
    @XmlEnumValue("ValidationError")
    ValidationError(1, "ValidationError"),

    /**
     * Indicates that an error has occurred in the Baiji soa framework (Baiji RPC),
     * such as a serialization/deserialization failure.
     */
    @XmlEnumValue("FrameworkError")
    FrameworkError(2, "FrameworkError"),

    /**
     * Indicates that a Baiji service is unable to meet a specified service level agreement.
     * Typical cases that will cause this error including:
     * 1) continues high service call latency;
     * 2) continues high service call error rate.
     * In these cases, to avoid further service deterioration, the service framework will enter into a self-protecting mode,
     * by tripping the service call circuit and return SLAError to clients.
     * Later, when the situation improves, the service framework will close the service call circuit again and continue to serve the clients.
     */
    @XmlEnumValue("SLAError")
    SLAError(3, "SLAError"),

    /**
     * Indicates that a request failed to pass the service security check.
     */
    @XmlEnumValue("SecurityError")
    SecurityError(4, "SecurityError");

    private final int intValue;
    private final String value;

    ErrorClassificationCodeType(int intValue, String value) {
        this.intValue = intValue;
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public int getValue() {
        return intValue;
    }

    @JsonValue
    public String value() {
        return value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    @Deprecated
    public static ErrorClassificationCodeType findByValue(int value) {
        switch (value) {
            case 0:
                return ServiceError;
            case 1:
                return ValidationError;
            case 2:
                return FrameworkError;
            case 3:
                return SLAError;
            case 4:
                return SecurityError;
            default:
                return null;
        }
    }

    public static ErrorClassificationCodeType fromValue(String v) {
        for (ErrorClassificationCodeType c: ErrorClassificationCodeType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
