package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.hotel;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 城市预定详情
 * <AUTHOR>
 * @Date 2019/4/10
 */
public class CityBookingDetailBO {
    private Integer cityID;
    private String cityName;
    private Integer bookingNumber;
    private Integer agreementHotelNumber;
    private Integer noBookingNumber;

    public Integer getCityID() {
        return cityID;
    }

    public void setCityID(Integer cityID) {
        this.cityID = cityID;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getBookingNumber() {
        return bookingNumber;
    }

    public void setBookingNumber(Integer bookingNumber) {
        this.bookingNumber = bookingNumber;
    }

    public Integer getAgreementHotelNumber() {
        return agreementHotelNumber;
    }

    public Integer getNoBookingNumber() {
        return noBookingNumber;
    }

    public void setNoBookingNumber(Integer noBookingNumber) {
        this.noBookingNumber = noBookingNumber;
    }

    public void setAgreementHotelNumber(Integer agreementHotelNumber) {
        this.agreementHotelNumber = agreementHotelNumber;
    }

    public  CityBookingDetailBO(){
        this.cityID=0;
        this.agreementHotelNumber=0;
        this.bookingNumber=0;
        this.noBookingNumber=0;
        this.cityName= StringUtils.EMPTY;
    }
}
