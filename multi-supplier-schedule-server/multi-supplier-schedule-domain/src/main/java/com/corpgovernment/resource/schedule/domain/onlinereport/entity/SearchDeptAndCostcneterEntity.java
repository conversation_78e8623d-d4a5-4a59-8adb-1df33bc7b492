package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 部门成本中心搜索条件
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "key",
    "vals",
    "selectAll",
    "permitVals",
        "orgList",
        "currentList",
    "way"
})
public class SearchDeptAndCostcneterEntity implements Serializable {
    private static final long serialVersionUID = 1L;





    public SearchDeptAndCostcneterEntity(
        Integer key,
        List<String> vals,
        Boolean selectAll,
        List<String> permitVals,
        List<String> orgList,
        List<String> currentList,
        Integer way) {
        this.key = key;
        this.vals = vals;
        this.selectAll = selectAll;
        this.permitVals = permitVals;
        this.orgList = orgList;
        this.currentList = currentList;
        this.way = way;
    }

    public SearchDeptAndCostcneterEntity() {
    }

    /**
     * 搜索Key
     */
    @JsonProperty("key")
    public Integer key;

    /**
     * 搜索Value
     */
    @JsonProperty("vals")
    public List<String> vals;

    /**
     * 是否全选
     */
    @JsonProperty("selectAll")
    public Boolean selectAll;

    /**
     * 角色用户可能存在的单独的权限限制集合
     */
    @JsonProperty("permitVals")
    public List<String> permitVals;

    @JsonProperty("orgList")
    private List<String> orgList;
    @JsonProperty("currentList")
    private List<String> currentList;

    /**
     * 1:包含，2：剔除
     */
    @JsonProperty("way")
    public Integer way;

    /**
     * 搜索Key
     */
    public Integer getKey() {
        return key;
    }

    /**
     * 搜索Key
     */
    public void setKey(final Integer key) {
        this.key = key;
    }

    /**
     * 搜索Value
     */
    public List<String> getVals() {
        return vals;
    }

    /**
     * 搜索Value
     */
    public void setVals(final List<String> vals) {
        this.vals = vals;
    }

    /**
     * 是否全选
     */
    public Boolean isSelectAll() {
        return selectAll;
    }

    /**
     * 是否全选
     */
    public void setSelectAll(final Boolean selectAll) {
        this.selectAll = selectAll;
    }

    /**
     * 角色用户可能存在的单独的权限限制集合
     */
    public List<String> getPermitVals() {
        return permitVals;
    }

    /**
     * 角色用户可能存在的单独的权限限制集合
     */
    public void setPermitVals(final List<String> permitVals) {
        this.permitVals = permitVals;
    }

    /**
     * 1:包含，2：剔除
     */
    public Integer getWay() {
        return way;
    }

    /**
     * 1:包含，2：剔除
     */
    public void setWay(final Integer way) {
        this.way = way;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    public List<String> getOrgList() {
        return orgList;
    }

    public void setOrgList(List<String> orgList) {
        this.orgList = orgList;
    }

    public List<String> getCurrentList() {
        return currentList;
    }

    public void setCurrentList(List<String> currentList) {
        this.currentList = currentList;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final SearchDeptAndCostcneterEntity other = (SearchDeptAndCostcneterEntity)obj;
        return
            Objects.equal(this.key, other.key) &&
            Objects.equal(this.vals, other.vals) &&
            Objects.equal(this.selectAll, other.selectAll) &&
            Objects.equal(this.permitVals, other.permitVals) &&
            Objects.equal(this.orgList, other.orgList) &&
            Objects.equal(this.currentList, other.currentList) &&
            Objects.equal(this.way, other.way);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.key == null ? 0 : this.key.hashCode());
        result = 31 * result + (this.vals == null ? 0 : this.vals.hashCode());
        result = 31 * result + (this.selectAll == null ? 0 : this.selectAll.hashCode());
        result = 31 * result + (this.permitVals == null ? 0 : this.permitVals.hashCode());
        result = 31 * result + (this.orgList == null ? 0 : this.orgList.hashCode());
        result = 31 * result + (this.currentList == null ? 0 : this.currentList.hashCode());
        result = 31 * result + (this.way == null ? 0 : this.way.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("key", key)
            .add("vals", vals)
            .add("selectAll", selectAll)
            .add("permitVals", permitVals)
            .add("orgList", orgList)
            .add("currentList", currentList)
            .add("way", way)
            .toString();
    }
}
