package com.corpgovernment.resource.schedule.domain.onlinereport.rpc;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.ReportLibTakeawayIndicatorConfigBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.*;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.CostCenterAndDepartmentSearchRequestType;

import java.util.List;

/*
 * <AUTHOR>
 * @date 2020/7/6 16:29
 * @Desc
 */
public interface ICorpOnlineReportPlatformService {

    OnlineReportConsumeResponse queryReportConsume(OnlineReportConsumeRequest var1) throws Exception;

    OnlineReportConsumeOrdercntResponse queryReportConsumeOrdercnt(OnlineReportConsumeOrdercntRequest var1) throws Exception;

    OnlineReportDetailResponse queryReportDetail(OnlineReportDetailRequest var1) throws Exception;

    OnlineReportTrendResponse queryReportTrend(OnlineReportTrendRequest var1) throws Exception;

    OnlineReportHtlCityResponse queryHtlCityList(OnlineReportHtlCityRequest var1);

    // 差旅评分指标
    OnlineReportMarkMetricTrendResponse queryMarkMetricTrend(OnlineReportMarkMetricTrendRequest request);

    // 机票折扣
    OnlineReportFltDiscountRangeResponse queryFltDiscountRangeAnalysis(OnlineReportFltDiscountRangeRequest request);

    // 退票\退订趋势
    OnlineReportRefundTrendResponse queryRefundTrendAnalysis(OnlineReportRefundTrendRequest request);

    OnlineReportDeptUidConsumeDetailResponse queryDeptUidConsumeDetail(OnlineReportDeptUidConsumeDetailRequest request);
/*



    OnlineReportNoteResponse updateReportNote(OnlineReportNoteRequest var1) throws Exception;

    OnlineReportNoteListResponse queryReportNote(OnlineReportNoteListRequest var1) throws Exception;*/

    OnlineReportTopFiveDeptConsumeResponse queryTopFiveDeptConsume(OnlineReportTopFiveDeptConsumeRequest var1) throws Exception;

    OnlineReportTopFiveDeptAnalysisResponse queryTopFiveDeptAnalysis(OnlineReportTopFiveDeptAnalysisRequest var1) throws Exception;

    OnlineReportDeptDetailAnalysisResponse queryDeptDetailAnalysis(OnlineReportDeptDetailAnalysisRequest var1);

  /*  OnlineReportDeptDetailAnalysisResponse queryDeptDetailHeader(OnlineReportDeptDetailAnalysisRequest var1) throws Exception;

    OnlineReportRiskOrderSummaryResponse queryRiskOrderSummary(OnlineReportRiskOrderSummaryRequest var1) throws Exception;

    OnlineReportFlightRiskOrderDetailResponse queryRiskFlightOrderDetail(OnlineReportRiskOrderDetailRequest var1) throws Exception;

    OnlineReportHotelRiskOrderDetailResponse queryRiskHotelOrderDetail(OnlineReportRiskOrderDetailRequest var1) throws Exception;

    OnlineReportCarRiskOrderDetailResponse queryRiskCarOrderDetail(OnlineReportRiskOrderDetailRequest var1) throws Exception;

    OnlineReportHotelUnderStayRiskOrderDetailResponse queryRiskHotelUnderStayOrderDetail(OnlineReportRiskOrderDetailRequest var1) throws Exception;*/

    OnlineReportRcViewResponse queryReportRcViewData(OnlineReportRcViewRequest var1) throws BusinessException;

    OnlineReportRcTrendResponse queryReportRcTrend(OnlineReportRcTrendRequest var1);

    OnlineReportRcViewCountResponse queryReportRcViewCountData(OnlineReportRcViewRequest var1);

    OnlineReportRcViewReasonResponse queryReportRcViewReasonData(OnlineReportRcViewRequest var1);

    OnlineReportTopRcAnalysisResponse queryReportTopRcAnalysis(OnlineReportTopRcAnalysisRequest var1);


    // 提前预订天数
    OnlineReportPreOrderDateResponse queryPreOrderDateAnalysis(OnlineReportPreOrderDateRequest request);

    // 行为分析
    OnlineReportBehaviorAnalysisResponse queryBehaviorAnalysis(OnlineReportBehaviorAnalysisRequest request);

    // 预订方式
    OnlineReportBookTypeResponse queryBookTypeAnalysis(OnlineReportBookTypeRequest request);

    // 改签趋势
    OnlineReportRebookTrendResponse queryRebookTrendAnalysis(OnlineReportRebookTrendRequest request);

    OnlineReportSaveGeneralResponse querySaveGeneralInfo(OnlineReportSaveGeneralRequest request);

    OnlineReportPromotionsSaveDetailResponse queryPromotionsSaveDetail(OnlineReportPromotionsSaveDetailRequest request);

    OnlineReportSaveProportionResponse querySaveProportion(OnlineReportSaveProportionRequest request);

    OnlineReportSaveProportionDetailResponse querySaveProportionDetail(OnlineReportSaveProportionDetailRequest request);

    OnlineReportTrendResponse queryReportSaveTrend(OnlineReportTrendRequest request);

    OnlineReportTrendResponse queryReportSaveDetail(OnlineReportTrendRequest request);

    OnlineReportPotentialSaveLowRcResponse queryPotentialSaveLowRcDetail(OnlineReportPotentialSaveLowRclRequest request);

    OnlineReportTopPotentialSaveResponse queryTopPotentialSaveDetail(OnlineReportTopPotentialSaveRequest request);

    OnlineReportSaveProportionResponse queryPSaveProportion(OnlineReportSaveProportionRequest request);

    OnlineReportPSaveProportionDetailResponse queryPSaveProportionDetail(OnlineReportPSaveProportionDetailRequest request);

    OnlineReportTrendResponse queryReportPotentialSaveTrend(OnlineReportTrendRequest request);

    OnlineReportTrendResponse queryReportPotentialSaveDetail(OnlineReportTrendRequest request);

    ReportSaveLossAnalyseResponse querySaveLossAnalyse(ReportSaveLossAnalyseRequest request);

    ReportSaveLossAnalyseResponse querySaveLossPreOrderDate(ReportSaveLossAnalyseRequest request);

    ReportSaveLossAnalyseResponse querySaveLossPositionDist(ReportSaveLossAnalyseRequest request);

    ReportSaveLossAnalyseResponse querySaveLossDiscountDist(ReportSaveLossAnalyseRequest request);

    ReportSaveLossAnalyseResponse queryFlightAvgPriceDist(ReportSaveLossAnalyseRequest request);

    ReportSaveLossAnalyseResponse queryFlightAvgMileageDist(ReportSaveLossAnalyseRequest request);

    ReportSaveLossAnalyseResponse queryHotelStarDist(ReportSaveLossAnalyseRequest request);

    ReportSaveLossAnalyseResponse queryHotelRoomNightDist(ReportSaveLossAnalyseRequest request);

    // 报告库-订单明细
    OnlineReportOrderDetailResponse queryOrderDetail(OnlineReportOrderDetailRequest request);

    OnlineReportConsumeProfileResponse queryConsumeProfile(OnlineReportConsumeProfileRequest request);

    // 报告库-城市酒店
    OnlineReportCityHotelResponse queryCityHotelInfo(OnlineReportCityHotelRequest request);

    // 差旅分析--足迹图/交通图/酒店图
    OnlineReportTravelPositionResponse queryTravelPositionInfo(OnlineReportTravelPositionRequest request);


    /*
       OnlineReportRiskOrderTotalResponse queryRiskOrderTotal(OnlineReportRiskOrderTotalRequest var1) throws Exception;

       OnlineReportFlightRiskOrderOperatorResponse updateReportRiskFlightOrderOperatorLog(OnlineReportFlightRiskOrderOperatorRequest request) throws Exception;

       OnlineReportHotelRiskOrderOperatorResponse updateReportRiskHotelOrderOperatorLog(OnlineReportHotelRiskOrderOperatorRequest request) throws Exception;

       OnlineReportCarRiskOrderOperatorResponse updateReportRiskCarOrderOperatorLog(OnlineReportCarRiskOrderOperatorRequest request) throws Exception;

       OnlineReportRiskHotelCashOutReportHotelResponse queryReportRiskHotelCashOutReportHotel(OnlineReportRiskHotelCashOutReportHotelRequest request) throws Exception;






    *//*机酒节省损失分析--热门城市航班*//*
    ReportSaveLossAnalyseResponse queryFlightHotCity(ReportSaveLossAnalyseRequest request);







    *//*供应商监测*//*
    OnlineReportAgreementConsumeResponse queryAgreementConsume(OnlineReportAgreementConsumeRequest request);

    *//*供应商监测*//*
    OnlineReportAgreementDetailResponse queryAgreementDetail(OnlineReportAgreementDetailRequest request);

    *//*供应商监测*//*
    OnlineReportHotAanlysisResponse queryHotAnalysis(OnlineReportHotAanlysisRequest request);

    *//*供应商监测*//*
    OnlineReportSupplierTrendResponse querySupplierTrend(OnlineReportSupplierTrendRequest request);

    *//*供应商监测*//*
    OnlineReportSupplierTopResponse querySupplierTop(OnlineReportSupplierTopRequest request);

    *//*供应商监测*//*
    OnlineReportAgreementBaseDataResponse queryAgreementBaseData(OnlineReportAgreementBaseDataRequest request);

    *//*碳排概览*//*
    OnlineReportCarbonsViewResponse queryCarbonsView(OnlineReportCarbonsViewRequest request);

    *//*碳排排名*//*
    OnlineReportCarbonsTopResponse queryCarbonsTop(OnlineReportCarbonsTopRequest request);

    *//*碳排部门明细*//*
    OnlineReportCarbonsDeptDetailResponse queryCarbonsDeptDetail(OnlineReportCarbonsDeptDetailRequest request);







 /*




























*/
    //*差旅定位-top城市酒店*//*
    OnlineReportTopCityResponse queryTopCity(OnlineReportTopCityRequest request);

    //*差旅定位-top酒店城市
    OnlineReportTopHotelResponse queryTopHotel(OnlineReportTopHotelRequest request);

    // 差旅分析--明细查询
    TravelPositionDetailResponse queryTravelPositionDetailByPage(TravelPositionDetailRequest request);

    // 差旅分析--国家查询
    TravelPositionCountryResponse queryTravelPositionCountry(TravelPositionCountryRequest request);

    // 差旅分析--省份查询
    TravelPositionProvinceResponse queryTravelPositionProvince(TravelPositionProvinceRequest request);

    // 差旅分析--城市查询
    TravelPositionCityResponse queryTravelPositionCity(TravelPositionCityRequest request);

    // 酒店支付方式
    OnlineReportBalanceTypeResponse queryHtlBalanceTypeAnalysis(OnlineReportBalanceTypeRequest request);

    // 酒店拼房是否有数据
    OnlineReportZoningOpenResponse queryHtlRoomShareOpenAnalysis(OnlineReportZoningOpenRequest request);

    // 酒店拼房费用分摊模式
    OnlineReportCostAllocationResponse queryHtlCostAllocationAnalysis(OnlineReportCostAllocationRequest request);

    // 酒店拼房模式
    OnlineReportZoningPatternResponse queryHtlRoomShareAnalysis(OnlineReportZoningPatternRequest request);

    // 随心订
    OnlineReportMixPaymentResponse queryMixPaymentInfo(OnlineReportMixPaymentRequest request);

    /*一键导出report-部门消费top明细*/
    OnlineReportTopDeptConsumeDetailResponse queryTopDeptConsumeDetail(OnlineReportTopDeptConsumeDetailRequest request);

    OnlineReportQueryHtlOrderAuditResponse queryHtlOrderAuditInfo(OnlineReportQueryHtlOrderAuditRequest request);

  /*

    OnlineReportConsumeTrendResponse queryReportConsumeTrend(OnlineReportConsumeTrendRequest request);

    OnlineReportAvgPricePredictTrendResponse queryAvgPricePredictTrend(OnlineReportAvgPricePredictTrendRequest request);

    OnlineReportCarbonsViewV2Response queryCarbonsViewV2(OnlineReportCarbonsViewV2Request request);

    OnlineReportCarbonSaveResponse queryCarbonSave(OnlineReportCarbonSaveRequest request);

    OnlineReportCarbonsDeptDetailV2Response queryCarbonsDeptDetailV2(OnlineReportCarbonsDeptDetailV2Request request);

    OnlineReportCarbonsTrendResponse queryCarbonsTrend(OnlineReportCarbonsTrendRequest request);

    OnlineReportCarbonsDeptDetailV2Response queryCarbonsDeptDetailV2Header(OnlineReportCarbonsDeptDetailV2Request var1);

    OnlineReportQueryDeptByUidResponse queryDeptByUid(OnlineReportQueryDeptByUidRequest request);

    OnlineReportQueryIndustryListResponse queryIndustryList(OnlineReportQueryIndustryListRequest request);

    OnlineReportTravelStandardCityResponse queryTravelStandardCity(OnlineReportTravelStandardCityRequest request);

    OnlineReportTravelStandardRankResponse queryTravelStandardRank(OnlineReportTravelStandardRankRequest request);

    OnlineReportTravelStandardAdviceResponse queryTravelStandardAdvice(OnlineReportTravelStandardAdviceRequest request);

    OnlineReportTravelStandardTopCityAdviceResponse queryTravelStandardTopCityAdvice(OnlineReportTravelStandardTopCityAdviceRequest request);

    OnlineReportTravelStandardTrendResponse queryTravelStandardTrend(OnlineReportTravelStandardTrendRequest request);


    OnlineReportPreviewDataResponse queryReportPreviewData(OnlineReportPreviewDataRequest request);*/

    OnlineReportQueryUpdateTimeInfoResponse queryUpdateTimeInfo(OnlineReportQueryUpdateTimeInfoRequest request);

    // 酒店拼房间夜数趋势
    OnlineReportZoningPatternTrendResponse queryHtlRoomShareTrendAnalysis(OnlineReportZoningPatternTrendRequest request);

    // 酒店酒店星级分析
    OnlineReportHotelStarResponse queryHotelStarAnalysis(OnlineReportHotelStarRequest request);

    // 酒店间夜均价分布
    OnlineReportAvgNightPriceResponse queryHtlAvgNightPriceAnalysis(OnlineReportAvgNightPriceRequest request);

    OnlineReportSettlementBillingDataResponse querySettlementBillingData(OnlineReportSettlementBillingDataRequest request);

    OnlineReportWelfareStockDataResponse queryWelfareStockData(OnlineReportWelfareStockDataRequest request);

    String queryBaseDataLastUpdateTime();


    OnlineReportHotAanlysisResponse queryHotAnalysis(OnlineReportHotAanlysisRequest request);

    OnlineReportAgreementConsumeResponse queryAgreementConsume(OnlineReportAgreementConsumeRequest request);

    OnlineReportAgreementDetailResponse queryAgreementDetail(OnlineReportAgreementDetailRequest request);

    OnlineReportSupplierTrendResponse querySupplierTrend(OnlineReportSupplierTrendRequest request);

    OnlineReportCarbonsDeptDetailV2Response queryCarbonsDeptDetailV2Header(OnlineReportCarbonsDeptDetailV2Request request);

    OnlineReportDeptDetailAnalysisResponse queryDeptDetailHeader(OnlineReportDeptDetailAnalysisRequest request);

    ReportLibTakeawayIndicatorConfigBo queryIndicatorConfig(String cardNo);

    List<String> queryCostCenterOrDepartmentOrCorpId(CostCenterAndDepartmentSearchRequestType requestType) throws Exception;
}
