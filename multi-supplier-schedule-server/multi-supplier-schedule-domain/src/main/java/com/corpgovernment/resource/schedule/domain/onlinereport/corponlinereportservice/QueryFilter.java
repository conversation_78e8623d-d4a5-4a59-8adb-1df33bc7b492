/*
package com.corpgovernment.resource.schedule.domain.onlinereport.corponlinereportservice;

import com.ctriposs.baiji.exception.RuntimeException;
import com.ctriposs.baiji.schema.*;
import com.ctriposs.baiji.specific.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "filterName",
    "filterValueList",
    "filterType"
})
public class QueryFilter implements  Serializable {
    private static final long serialVersionUID = 1L;

    @JsonIgnore
    public static final transient Schema SCHEMA = Schema.parse("{\"type\":\"record\",\"name\":\"QueryFilter\",\"namespace\":\"" + QueryFilter.class.getPackage().getName() + "\",\"doc\":null,\"fields\":[{\"name\":\"filterName\",\"type\":[\"string\",\"null\"]},{\"name\":\"filterValueList\",\"type\":[{\"type\":\"array\",\"items\":\"string\"},\"null\"]},{\"name\":\"filterType\",\"type\":[\"string\",\"null\"]}]}");



    public QueryFilter(
        String filterName,
        List<String> filterValueList,
        String filterType) {
        this.filterName = filterName;
        this.filterValueList = filterValueList;
        this.filterType = filterType;
    }

    public QueryFilter() {
    }

    */
/**
     * 筛选列(枚举值)
     *//*

    @JsonProperty("filterName")
    public String filterName;

    */
/**
     * 筛选值列表
     *//*

    @JsonProperty("filterValueList")
    public List<String> filterValueList;

    */
/**
     * 类型(机票、酒店、火车票、用车)
     *//*

    @JsonProperty("filterType")
    public String filterType;

    */
/**
     * 筛选列(枚举值)
     *//*

    public String getFilterName() {
        return filterName;
    }

    */
/**
     * 筛选列(枚举值)
     *//*

    public void setFilterName(final String filterName) {
        this.filterName = filterName;
    }

    */
/**
     * 筛选值列表
     *//*

    public List<String> getFilterValueList() {
        return filterValueList;
    }

    */
/**
     * 筛选值列表
     *//*

    public void setFilterValueList(final List<String> filterValueList) {
        this.filterValueList = filterValueList;
    }

    */
/**
     * 类型(机票、酒店、火车票、用车)
     *//*

    public String getFilterType() {
        return filterType;
    }

    */
/**
     * 类型(机票、酒店、火车票、用车)
     *//*

    public void setFilterType(final String filterType) {
        this.filterType = filterType;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0: return this.filterName;
            case 1: return this.filterValueList;
            case 2: return this.filterType;
            default: throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value="unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0: this.filterName = (String)fieldValue; break;
            case 1: this.filterValueList = (List<String>)fieldValue; break;
            case 2: this.filterType = (String)fieldValue; break;
            default: throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final QueryFilter other = (QueryFilter)obj;
        return
            Objects.equal(this.filterName, other.filterName) &&
            Objects.equal(this.filterValueList, other.filterValueList) &&
            Objects.equal(this.filterType, other.filterType);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.filterName == null ? 0 : this.filterName.hashCode());
        result = 31 * result + (this.filterValueList == null ? 0 : this.filterValueList.hashCode());
        result = 31 * result + (this.filterType == null ? 0 : this.filterType.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("filterName", filterName)
            .add("filterValueList", filterValueList)
            .add("filterType", filterType)
            .toString();
    }
}
*/
