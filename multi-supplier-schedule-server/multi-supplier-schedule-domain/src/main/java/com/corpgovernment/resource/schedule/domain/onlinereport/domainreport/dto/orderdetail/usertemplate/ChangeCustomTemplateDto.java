package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.dto.orderdetail.usertemplate;

import com.corpgovernment.resource.schedule.domain.onlinereport.constant.SharkKeyConst;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Data
public class ChangeCustomTemplateDto {
    /**
     * 模板编号
     */
    private String templateNo;
    /**
     * 模板名称
     */
    @Length(max = 10, message = SharkKeyConst.REPORT_TEMPLATE_NAME_IS_TOO_LONG)
    private String templateName;
    /**
     * 报表key
     */
    @NotEmpty(message = SharkKeyConst.REPORT_TEMPLATE_REPORT_KEY_NOT_NULL)
    private String reportKey;
    /**
     * 自定义模板字段
     */
    private Map<String, Object> templateFields;
    /**
     * 自定义模版条件
     */
    private Map<String, Object> templateConditions;

}
