package com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 差标类型枚举
 * @create 2024-12-23 14:49
 */
@AllArgsConstructor
@Getter
public enum TravelStandardTypeEnum {

    STANDARD_TRAVEL_STANDARD("standardTravelStandard", "标准差标"),
    LADDER_TRAVEL_STANDARD("ladderTravelStandard", "阶梯差标"),
    COHABIT_TRAVEL_STANDARD("cohabitTravelStandard", "同住差标"),
    FLOAT_TRAVEL_STANDARD("floatTravelStandard", "浮动差标"),
    ;
    
    private final String code;
    
    private final String info;

}
