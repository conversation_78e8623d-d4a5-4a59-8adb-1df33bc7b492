package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.positiontracking;

/**
 * @Description: 定位跟踪城市订单数据
 * <AUTHOR>
 * @Date 2019/6/28
 */
public class PositionTrackingOrderDetailsBO {

    //订单号
    private String orderid;
    //卡号
    private String uid;
    //出行人
    private String passengerName;
    //订单联系人手机
    private String mobilePhone;
    //订单联系人
    private String contactName;
    //订单联系人邮箱
    private String contactEmail;
    //航班号/酒店名称
    private String info;
    //出发时间/入住时间
    private String arrival;
    //到达时间/离店时间
    private String departure;
    //出发城市
    private String acity;
    //到达城市/酒店所在城市
    private String dcity;
    //部门1
    private String dept1;
    //部门2
    private String dept2;
    //部门3
    private String dept3;
    //订单类型
    private String orderType;
    //描述/查询类别
    private String desInfo;

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getPassengerName() {
        return passengerName;
    }

    public void setPassengerName(String passengerName) {
        this.passengerName = passengerName;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getArrival() {
        return arrival;
    }

    public void setArrival(String arrival) {
        this.arrival = arrival;
    }

    public String getDeparture() {
        return departure;
    }

    public void setDeparture(String departure) {
        this.departure = departure;
    }

    public String getAcity() {
        return acity;
    }

    public void setAcity(String acity) {
        this.acity = acity;
    }

    public String getDcity() {
        return dcity;
    }

    public void setDcity(String dcity) {
        this.dcity = dcity;
    }

    public String getDept1() {
        return dept1;
    }

    public void setDept1(String dept1) {
        this.dept1 = dept1;
    }

    public String getDept2() {
        return dept2;
    }

    public void setDept2(String dept2) {
        this.dept2 = dept2;
    }

    public String getDept3() {
        return dept3;
    }

    public void setDept3(String dept3) {
        this.dept3 = dept3;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getDesInfo() {
        return desInfo;
    }

    public void setDesInfo(String desInfo) {
        this.desInfo = desInfo;
    }
}
