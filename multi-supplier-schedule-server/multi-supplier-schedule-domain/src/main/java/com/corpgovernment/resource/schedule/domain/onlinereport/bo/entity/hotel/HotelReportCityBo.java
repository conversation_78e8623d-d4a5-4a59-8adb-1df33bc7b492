package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.hotel;

/**
 * @Auther: ddzhan
 * @Date: 2019/4/15
 * @Description:
 */
public class HotelReportCityBo implements  Comparable<HotelReportCityBo> {

    private Double htlMemberAmount;
    private Integer htlMemberQuantity;

    private Double htlAgreementAmount;
    private Integer htlAgreementQuantity;

    private String htlCity;
    private Integer htlCityId;
    private String htlOrdertype;
    private Integer htlStar;

    public Integer getHtlStar() {
        return htlStar;
    }

    public void setHtlStar(Integer htlStar) {
        this.htlStar = htlStar;
    }

    public Double getHtlMemberAmount() {
        return htlMemberAmount;
    }

    public void setHtlMemberAmount(Double htlMemberAmount) {
        this.htlMemberAmount = htlMemberAmount;
    }

    public Integer getHtlMemberQuantity() {
        return htlMemberQuantity;
    }

    public void setHtlMemberQuantity(Integer htlMemberQuantity) {
        this.htlMemberQuantity = htlMemberQuantity;
    }

    public Double getHtlAgreementAmount() {
        return htlAgreementAmount;
    }

    public void setHtlAgreementAmount(Double htlAgreementAmount) {
        this.htlAgreementAmount = htlAgreementAmount;
    }

    public Integer getHtlAgreementQuantity() {
        return htlAgreementQuantity;
    }

    public void setHtlAgreementQuantity(Integer htlAgreementQuantity) {
        this.htlAgreementQuantity = htlAgreementQuantity;
    }

    public String getHtlCity() {
        return htlCity;
    }

    public void setHtlCity(String htlCity) {
        this.htlCity = htlCity;
    }

    public Integer getHtlCityId() {
        return htlCityId;
    }

    public void setHtlCityId(Integer htlCityId) {
        this.htlCityId = htlCityId;
    }

    public String getHtlOrdertype() {
        return htlOrdertype;
    }

    public void setHtlOrdertype(String htlOrdertype) {
        this.htlOrdertype = htlOrdertype;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public int compareTo(HotelReportCityBo o) {
        if(o.equals(this))
            return 0;
        return (o.getHtlAgreementQuantity() + o.getHtlMemberQuantity()) - (this.getHtlAgreementQuantity()+  this.getHtlMemberQuantity());
    }

}
