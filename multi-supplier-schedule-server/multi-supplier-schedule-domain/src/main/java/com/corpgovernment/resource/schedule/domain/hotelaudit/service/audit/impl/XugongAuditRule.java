package com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.impl;

import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.HotelAuditItem;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.HotelAuditResult;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.OrderInfo;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.TokenInfo;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.TravelApplication;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.TravelStandard;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.HotelAuditRuleEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.ProductTypeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelModeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardTypeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.IHotelAuditRule;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-25 11:08
 */
@Slf4j
@Service
public class XugongAuditRule implements IHotelAuditRule {
    
    private final static String XUGONG_TENANT_ID = "XGJT";
    
    @Override
    public HotelAuditResult audit(HotelAuditItem hotelAuditItem) {
        String tenantId = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getTenantId)
                .orElse(null);
        ProductTypeEnum productTypeEnum = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getOrderInfo)
                .map(OrderInfo::getProductTypeEnum)
                .orElse(null);
        // 校验前提：徐工、国内酒店、因公
        if (!StringUtils.equalsIgnoreCase(tenantId, XUGONG_TENANT_ID) || !ProductTypeEnum.HOTEL.equals(productTypeEnum)) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                    .auditDescList(null)
                    .auditPass(true)
                    .build();
        }
        
        List<String> auditDescList = new ArrayList<>();
        // 出差申请单校验
        TravelApplication travelApplication = Optional.ofNullable(hotelAuditItem.getTokenInfo())
                .map(TokenInfo::getTravelApplication)
                .orElse(null);
        if (travelApplication == null) {
            auditDescList.add("出差申请单为空");
        } else {
            if (StringUtils.isBlank(travelApplication.getTravelId()) || StringUtils.isBlank(travelApplication.getTravelApplicationId())) {
                auditDescList.add("出差申请单ID为空");
            }
            if (StringUtils.isBlank(travelApplication.getBookAddress()) || travelApplication.getBookLat() == null || travelApplication.getBookLon() == null) {
                auditDescList.add("出差申请单地址为空");
            }
        }
        
        // 差标校验
        TravelStandard travelStandard = Optional.ofNullable(hotelAuditItem.getTokenInfo())
                .map(TokenInfo::getTravelStandard)
                .orElse(null);
        if (travelStandard == null
                || travelStandard.getTravelStandardTypeEnumList() == null
                || !travelStandard.getTravelStandardTypeEnumList().contains(TravelStandardTypeEnum.LADDER_TRAVEL_STANDARD)) {
            auditDescList.add("使用的不是阶梯差标");
        }
        
        // 阶梯差标等级校验
        if (travelStandard == null || travelStandard.getLadderLevel() == null) {
            auditDescList.add("阶梯差标等级为空");
        }
        
        if (CollectionUtils.isNotEmpty(auditDescList)) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                    .auditDescList(auditDescList)
                    .auditPass(false)
                    .errorLabel(getHotelAuditRuleEnum().getErrorLabel())
                    .build();
        }
        return HotelAuditResult.builder()
                .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                .auditDescList(null)
                .auditPass(true)
                .build();
    }
    
    @Override
    public HotelAuditRuleEnum getHotelAuditRuleEnum() {
        return HotelAuditRuleEnum.XUGONG;
    }
    
    @Override
    public List<TravelModeEnum> getSupportTravelModeEnumList() {
        return Collections.singletonList(TravelModeEnum.PUB);
    }
    
}
