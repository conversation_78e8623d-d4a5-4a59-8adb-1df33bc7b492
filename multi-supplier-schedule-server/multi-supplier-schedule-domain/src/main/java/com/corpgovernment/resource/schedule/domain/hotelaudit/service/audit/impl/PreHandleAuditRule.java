package com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.impl;

import cn.hutool.core.date.DateUtil;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.HotelAuditItem;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.HotelAuditResult;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.OrderInfo;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.TokenInfo;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.TravelStandard;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.HotelAuditRuleEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.OrderSourceEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelModeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.IHotelAuditRule;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-24 15:13
 */
@Slf4j
@Service
public class PreHandleAuditRule implements IHotelAuditRule {
    
    @Override
    public HotelAuditResult audit(HotelAuditItem hotelAuditItem) {
        // 订单信息为空
        OrderInfo orderInfo = hotelAuditItem.getOrderInfo();
        if (orderInfo == null) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(HotelAuditRuleEnum.CONTEXT_INFO_LOSS)
                    .auditDescList(Collections.singletonList("orderInfo为空"))
                    .auditPass(false)
                    .errorLabel(HotelAuditRuleEnum.CONTEXT_INFO_LOSS.getErrorLabel())
                    .build();
        }
        
        // 补录订单
        if (Objects.equals(orderInfo.getOrderSourceEnum(), OrderSourceEnum.SUPPLEMENT)) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(HotelAuditRuleEnum.SUPPLEMENT_ORDER)
                    .auditDescList(Collections.singletonList(HotelAuditRuleEnum.SUPPLEMENT_ORDER.getInfo()))
                    .auditPass(false)
                    .errorLabel(HotelAuditRuleEnum.SUPPLEMENT_ORDER.getErrorLabel())
                    .build();
        }
        
        // 无token订单
        if (StringUtils.isBlank(orderInfo.getToken())) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(HotelAuditRuleEnum.NO_TOKEN_ORDER)
                    .auditDescList(Collections.singletonList(HotelAuditRuleEnum.NO_TOKEN_ORDER.getInfo()))
                    .auditPass(false)
                    .errorLabel(HotelAuditRuleEnum.NO_TOKEN_ORDER.getErrorLabel())
                    .build();
        }
        
        // token信息为空
        TokenInfo tokenInfo = hotelAuditItem.getTokenInfo();
        if (tokenInfo == null) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(HotelAuditRuleEnum.CONTEXT_INFO_LOSS)
                    .auditDescList(Collections.singletonList("tokenInfo为空"))
                    .auditPass(false)
                    .errorLabel(HotelAuditRuleEnum.CONTEXT_INFO_LOSS.getErrorLabel())
                    .build();
        }
        
        return HotelAuditResult.builder()
                .auditPass(true)
                .build();
    }
    
    @Override
    public HotelAuditRuleEnum getHotelAuditRuleEnum() {
        return null;
    }
    
    @Override
    public List<TravelModeEnum> getSupportTravelModeEnumList() {
        return null;
    }
    
}
