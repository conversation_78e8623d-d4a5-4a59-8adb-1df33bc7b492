package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

import org.apache.commons.lang3.StringUtils;

public class FlightPreOrderDateBO {
    public Integer quantity;
    public Integer fullQuantity;
    public Double yPriceRate;
    public Integer yQuantity;
    public Double nonFullPriceRate;
    public Double price;
    public Double priceAll;
    public Double tpm;
    public String preOrderDate;

    public FlightPreOrderDateBO(){
        this.quantity = 0;
        this.fullQuantity = 0;
        this.yPriceRate = 0d;
        this.yQuantity = 0;
        this.nonFullPriceRate = 0d;
        this.price = 0d;
        this.priceAll = 0d;
        this.tpm = 0d;
        this.preOrderDate = StringUtils.EMPTY;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getFullQuantity() {
        return fullQuantity;
    }

    public void setFullQuantity(Integer fullQuantity) {
        this.fullQuantity = fullQuantity;
    }

    public Double getyPriceRate() {
        return yPriceRate;
    }

    public void setyPriceRate(Double yPriceRate) {
        this.yPriceRate = yPriceRate;
    }

    public Integer getyQuantity() {
        return yQuantity;
    }

    public void setyQuantity(Integer yQuantity) {
        this.yQuantity = yQuantity;
    }

    public Double getNonFullPriceRate() {
        return nonFullPriceRate;
    }

    public void setNonFullPriceRate(Double nonFullPriceRate) {
        this.nonFullPriceRate = nonFullPriceRate;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getPriceAll() {
        return priceAll;
    }

    public void setPriceAll(Double priceAll) {
        this.priceAll = priceAll;
    }

    public Double getTpm() {
        return tpm;
    }

    public void setTpm(Double tpm) {
        this.tpm = tpm;
    }

    public String getPreOrderDate() {
        return preOrderDate;
    }

    public void setPreOrderDate(String preOrderDate) {
        this.preOrderDate = preOrderDate;
    }
}
