package com.corpgovernment.resource.schedule.domain.onlinereport.mapper;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.DataQueryRange;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.FlightOrder;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.QConfigKeyConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.business.BusinessConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.google.common.primitives.Doubles;
import onlinereport.enums.BizTypeEnums;
import onlinereport.enums.BookTypeEnum;
import onlinereport.enums.CarOrderStatusEnum;
import onlinereport.enums.DimensionEnum;
import onlinereport.enums.FlightContinentEnum;
import onlinereport.enums.FlightContractEnum;
import onlinereport.enums.HtlTypeEnum;
import onlinereport.enums.OrderTypeEnum;
import onlinereport.enums.StatisticalEnum;
import onlinereport.enums.TrainStatusEnum;
import onlinereport.enums.YesOrNotEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public final class MapperUtils {

    /**
     * 根据bookTpye 转换线上、线下类型
     *
     * @param bookType
     * @return
     */
    public static String convertBookType(String bookType) {
        bookType = StringUtils.trimToEmpty(bookType);
        if (BookTypeEnum.F.toString().equalsIgnoreCase(bookType)) {
            //offline->线下(offline)
            return BookTypeEnum.F.getVal();
        }
        //online(T)、app(M)->线上(online)
        return BookTypeEnum.T.getVal();
    }

    /**
     * 根据bookTpye 转换线上、线下,app类型。
     *
     * @param bookType
     * @return
     */
    public static String convertBookTypeSingle(String bookType) {
        bookType = StringUtils.trimToEmpty(bookType);
        if (BookTypeEnum.F.toString().equalsIgnoreCase(bookType)) {
            //offline->线下(offline)
            return BookTypeEnum.F.getVal();
        } else if (BookTypeEnum.T.toString().equalsIgnoreCase(bookType)) {
            return BookTypeEnum.T.getVal();
        }
        //如果bookType为空默认转换为online类型
        else if (StringUtils.isEmpty(bookType)) {
            return BookTypeEnum.T.getVal();
        }
        return BookTypeEnum.M.getVal();
    }

    /**
     * 根据月份获取季度
     *
     * @param month
     * @return
     */
    public static int convertQuarter(int month) {
        return (month - 1) / 3 + 1;
    }

    /**
     * 根据月份获半年度
     *
     * @param month
     * @return
     */
    public static String convertHalfYear(int month) {
        if (month <= 6) {
            return "上";
        } else {
            return "下";
        }
    }

    /**
     * 根据月份获半年度
     *
     * @param month
     * @return
     */
    public static String convertHalfYear(String month) {
        return Integer.parseInt(month) <= 6 ? "1" : "2";
    }

    /**
     * 航线,出发城市->到达城市
     *
     * @param order
     * @return
     */
    public static String convertAirRoute(FlightOrder order) {
        return String.format("%s-%s", order.getDepartureCity(), order.getArrivalCity());
    }

    /**
     * 转化是否协议航空
     *
     * @param order
     * @return
     */
    public static String convertAgreementAir(FlightOrder order) {
        if (StringUtils.isNotEmpty(order.getCustomerID())
                && order.getCustomerID().contains(FlightContractEnum.TRIPLE.getDes())
                && YesOrNotEnum.T.toString().equalsIgnoreCase(order.getBfReturn())) {
            return YesOrNotEnum.T.toString();
        }
        return YesOrNotEnum.F.toString();
    }

    /**
     * 是否海外
     *
     * @param htlType
     * @return
     */
    public static String convertIsOversea(String htlType) {
        if (YesOrNotEnum.T.toString().equalsIgnoreCase(htlType)) {
            //海外 T - > T
            return YesOrNotEnum.T.toString();
        }
        //国内 F,港澳台 O ->F
        return YesOrNotEnum.F.toString();
    }

    /**
     * Integer 转成 String，为null 则返回""
     *
     * @param origin
     * @return
     */
    public static String convertDigitToString(Integer origin) {
        return origin == null ? GlobalConst.STRING_EMPTY : origin.toString();
    }

    /**
     * Integer 转成 String，为null 则返回""
     *
     * @param origin
     * @return
     */
    public static String convertDigitToString(BigDecimal origin) {
        return origin == null ? GlobalConst.STRING_EMPTY : origin.toString();
    }

    /**
     * Integer 转成 String，为null 则返回"0"
     *
     * @param origin
     * @return
     */
    public static String convertDigitToZeroString(Integer origin) {
        return origin == null ? GlobalConst.STRING_ZERO : origin.toString();
    }

    /**
     * BigDecimal 为null 则返回0
     *
     * @param origin
     * @return
     */
    public static BigDecimal convertDigitToZero(BigDecimal origin) {
        return origin == null ? BigDecimal.ZERO : origin;
    }

    /**
     * Integer 为null 则返回0
     *
     * @param origin
     * @return
     */
    public static Integer convertDigitToZero(Integer origin) {
        return origin == null ? 0 : origin;
    }

    /**
     * Long 为null 则返回0
     *
     * @param origin
     * @return
     */
    public static Long convertDigitToZero(Long origin) {
        return origin == null ? 0 : origin;
    }

    /**
     * BigDecimal 转成 String，为null 则返回"0"
     *
     * @param origin
     * @return
     */
    public static String convertDigitToZeroString(BigDecimal origin) {
        return origin == null ? GlobalConst.STRING_ZERO : origin.toString();
    }

    /**
     * Long 转成 String，为null 则返回""
     *
     * @param origin
     * @return
     */
    public static String convertDigitToString(Long origin) {
        return origin == null ? GlobalConst.STRING_EMPTY : origin.toString();
    }

    /**
     * Long 转成 String，为null 则返回"-"
     *
     * @param origin
     * @return
     */
    public static String convertDigitToDash(Long origin) {
        return origin == null || origin == 0l ? "-" : origin.toString();
    }

    /**
     * Long 转成 String，为null 则返回"-"
     *
     * @param origin
     * @return
     */
    public static String convertStrToDash(String origin) {
        return StringUtils.isEmpty(origin) || StringUtils.equalsIgnoreCase(origin, "0") ? "-" : StringUtils.trimToEmpty(origin);
    }

    /**
     * Long 转成 String，为null 则返回"0"
     *
     * @param origin
     * @return
     */
    public static String convertDigitToZeroString(Long origin) {
        return origin == null ? GlobalConst.STRING_ZERO : origin.toString();
    }

    /**
     * Float 转成 String，为null 则返回"0"
     *
     * @param origin
     * @return
     */
    public static String convertDigitToZeroString(Float origin) {
        return origin == null ? GlobalConst.STRING_ZERO : origin.toString();
    }

    /**
     * Float 转成 String，为null 则返回""
     *
     * @param origin
     * @return
     */
    public static String convertDigitToString(Float origin) {
        return origin == null ? GlobalConst.STRING_EMPTY : origin.toString();
    }

    /**
     * Double 转成 String，为null 则返回""
     *
     * @param origin
     * @return
     */
    public static String convertDigitToString(Double origin) {
        return origin == null ? GlobalConst.STRING_EMPTY : origin.toString();
    }

    /**
     * Double 转成 String，为null 则返回"0"
     *
     * @param origin
     * @return
     */
    public static String convertDigitToZeroString(Double origin) {
        return origin == null ? GlobalConst.STRING_ZERO : origin.toString();
    }

    /**
     * 为null 则返回"0"
     *
     * @param origin
     * @return
     */
    public static double convertDigitToZero(Double origin) {
        return origin == null ? 0 : origin.doubleValue();
    }

    /**
     * 数字字符串，判断如果为空则变为"0"
     *
     * @param origin
     * @return
     */
    public static String convertDefaultDigit(String origin) {
        if (StringUtils.isEmpty(origin)) {
            return GlobalConst.STRING_ZERO;
        }
        try {
            Double.parseDouble(origin);
            return origin;
        } catch (Exception ex) {
            return GlobalConst.STRING_ZERO;
        }
    }

    /**
     * 部门成本中心查询限制、默认10000
     *
     * @return
     */
    public static Integer getESOrganizationQueryLimit() {
        String limitVal = QConfigUtils.getValue(QConfigKeyConst.ES_ORGANIZATION_QUERY_LIMIT);
        Integer limit = 10000;
        if (StringUtils.isNotEmpty(limitVal)) {
            limit = Integer.parseInt(limitVal);
        }
        return limit;
    }

    /**
     * 字符串小写去空格
     *
     * @param origin
     * @return
     */
    public static String trimString(String origin) {
        if (StringUtils.isEmpty(origin)) {
            return GlobalConst.STRING_EMPTY;
        }
        return origin.toLowerCase().trim();
    }

    /**
     * 字符串去空格
     *
     * @param origin
     * @return
     */
    public static String trim(String origin) {
        if (StringUtils.isEmpty(origin)) {
            return GlobalConst.STRING_EMPTY;
        }
        return origin.trim();
    }

    /**
     * 国际航班类型
     *
     * @param origin
     * @return
     */
    public static String getFlightContinent(String origin) {
        String desc = GlobalConst.STRING_EMPTY;
        if (StringUtils.isEmpty(origin)) {
            return desc;
        }
        if (StringUtils.equalsIgnoreCase(origin.trim(), FlightContinentEnum.A.toString())) {
            desc = "洲内";
        } else if (StringUtils.equalsIgnoreCase(origin.trim(), FlightContinentEnum.N.toString())) {
            desc = "国内";
        } else if (StringUtils.equalsIgnoreCase(origin.trim(), FlightContinentEnum.I.toString())) {
            desc = "洲际";
        } else {
            desc = origin;
        }
        return desc;
    }

    /**
     * 获得产线名称
     *
     * @param bizTypeEnums
     * @return
     */
    public static String getProductLine(BizTypeEnums bizTypeEnums) {
        String desc = "";
        switch (bizTypeEnums) {
            case F:
                desc = "机票";
                break;
            case T:
                desc = "火车";
                break;
            case C:
                desc = "用车";
                break;
            case H:
                desc = "酒店";
                break;
            default:
                desc = "";
                break;
        }
        return desc;
    }

/*    public static List<Filter> mapTo(List<QueryFilter> queryFilterList) {
        return BeanCopyUtil.copyList(queryFilterList, Filter.class,
                MapperFactory.getCopyByRefMapper().mapClass(QueryFilter.class, Filter.class)
                        .field("filterName", "name")
                        .field("filterValueList", "value")
                        .field("filterType", "filterType"));
    }*/

    /**
     * 判断是否成交成人数据
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isADUAndDealedData(Map<String, String> row) {
        return BusinessConst.FLIGHT_STATUS_CJ.equalsIgnoreCase(row.get(DimensionEnum.FLT_FLIGHTSTATUS.getName()))
                && BusinessConst.AGETYPE_ADU.equalsIgnoreCase(row.get(DimensionEnum.FLT_AGETYPE.getName()));
    }

    /**
     * 判断是否成交数据
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isDealedData(Map<String, String> row) {
        return BusinessConst.FLIGHT_STATUS_CJ.equalsIgnoreCase(row.get(DimensionEnum.FLT_FLIGHTSTATUS.getName()));
    }

    /**
     * 是否国内机票数据
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isDomData(Map<String, String> row) {
        return OrderTypeEnum.N.toString().equalsIgnoreCase(row.get(DimensionEnum.FLT_FLIGHTCLASS.getName()));
    }

    /**
     * 是否退票
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isRefund(Map<String, String> row) {
        return YesOrNotEnum.T.toString().equalsIgnoreCase(row.get(DimensionEnum.FLT_ISREFUND.getName()));
    }

    /**
     * 是否改签
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isRebook(Map<String, String> row) {
        return YesOrNotEnum.T.toString().equalsIgnoreCase(row.get(DimensionEnum.FLT_IS_REBOOK.getName()));
    }

    /**
     * 是否经济舱机票数据
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isEconomyClass(Map<String, String> row) {
        String realClass = row.get(DimensionEnum.FLT_REALCLASS.getName());
        return StringUtils.isNotEmpty(realClass) && realClass.indexOf(BusinessConst.FLIGHT_REALCLASS_ECONOMY) >= 0;
    }

    /**
     * 是否包含 头等舱、公务舱、全价经济舱
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isEconomyTopAllPriceClass(Map<String, String> row) {
        String realClass = row.get(DimensionEnum.FLT_REALCLASS.getName());
        if (StringUtils.isEmpty(realClass)) {
            return false;
        }
        return realClass.indexOf(BusinessConst.FLIGHT_REALCLASS_BUSINESS) >= 0 || realClass.indexOf(BusinessConst.FLIGHT_REALCLASS_ECONOMYSTANDARD) >= 0
                || realClass.indexOf(BusinessConst.FLIGHT_REALCLASS_FIRST) >= 0;
    }

    /**
     * 判断是否成交成人数据以及经济舱
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isADUAndDealedEconomyData(Map<String, String> row) {
        String realClass = row.get(DimensionEnum.FLT_REALCLASS.getName());
        return BusinessConst.FLIGHT_STATUS_CJ.equalsIgnoreCase(row.get(DimensionEnum.FLT_FLIGHTSTATUS.getName()))
                && BusinessConst.AGETYPE_ADU.equalsIgnoreCase(row.get(DimensionEnum.FLT_AGETYPE.getName())) && StringUtils.isNotEmpty(realClass) && realClass.indexOf(BusinessConst.FLIGHT_REALCLASS_ECONOMY) >= 0;
    }

    /**
     * 判断是否成成人数据以及经济舱
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isADUAndEconomyData(Map<String, String> row) {
        String realClass = row.get(DimensionEnum.FLT_REALCLASS.getName());
        return BusinessConst.AGETYPE_ADU.equalsIgnoreCase(row.get(DimensionEnum.FLT_AGETYPE.getName())) && StringUtils.isNotEmpty(realClass) && realClass.indexOf(BusinessConst.FLIGHT_REALCLASS_ECONOMY) >= 0;
    }

    /**
     * 是否协议航空
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isAgreementAir(Map<String, String> row) {
        String result = row.get(DimensionEnum.FLT_AGREEMENTAIR.getName());
        return result.equals(YesOrNotEnum.T.name());
    }

    /**
     * 【Class='Y' 这块在isEconomyClass过滤】
     * and 【IsFullPrice='F' 折扣率不等于1】- 和产品确认这块不过滤
     * and  【BfReturn='T' and CustomerID='三方协议' 这块在isAgreementAir已经过滤】
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isYBefore(Map<String, String> row) {
        //成交成人放到数据源筛选
        //折扣率不等于1
        /*if(DigitParseUtils.tryParseDouble(row.get(StatisticalEnum.FLT_PRICERATE.getName())) != 1D ){
            isFullPrice = true;
        }*/
        if (isAgreementAir(row) && isEconomyClass(row)) {
            return true;
        }
        return false;
    }


    public static String getDiscountRange(String discountRate) {
        Double discount = Doubles.tryParse(discountRate);
        if (null == discount) return StringUtils.EMPTY;
        if (Doubles.compare(discount, 1D) == 0) return "1.00 - 1.00";
        if (Doubles.compare(discount, 0.9D) >= 0) return "0.90 - 0.99";
        if (Doubles.compare(discount, 0.8D) >= 0) return "0.80 - 0.89";
        if (Doubles.compare(discount, 0.7D) >= 0) return "0.70 - 0.79";
        if (Doubles.compare(discount, 0.6D) >= 0) return "0.60 - 0.69";
        if (Doubles.compare(discount, 0.5D) >= 0) return "0.50 - 0.59";
        if (Doubles.compare(discount, 0.4D) >= 0) return "0.40 - 0.49";
        if (Doubles.compare(discount, 0.3D) >= 0) return "0.30 - 0.39";
        if (Doubles.compare(discount, 0.2D) >= 0) return "0.20 - 0.29";
        if (Doubles.compare(discount, 0.1D) >= 0) return "0.10 - 0.19";
        if (Doubles.compare(discount, 0D) >= 0) return "0.00 - 0.09";
        return StringUtils.EMPTY;
    }

    public static String getTakeOffTimeRange(Long takeOffTime) {
        if (null == takeOffTime) return StringUtils.EMPTY;
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(takeOffTime);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        if (hour >= 0 && hour < 6) return "0:00 - 06:00";
        if (hour >= 6 && hour < 8) return "06:00 - 08:00";
        if (hour >= 8 && hour < 10) return "08:00 - 10:00";
        if (hour >= 10 && hour < 12) return "10:00 - 12:00";
        if (hour >= 12 && hour < 14) return "12:00 - 14:00";
        if (hour >= 14 && hour < 16) return "14:00 - 16:00";
        if (hour >= 16 && hour < 18) return "16:00 - 18:00";
        if (hour >= 18 && hour < 20) return "18:00 - 20:00";
        if (hour >= 20 && hour < 22) return "20:00 - 22:00";
        if (hour >= 22 && hour < 24) return "22:00 - 24:00";
        return StringUtils.EMPTY;
    }


    /**
     * 判断是否成人数据
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isADUData(Map<String, String> row) {
        return BusinessConst.AGETYPE_ADU.equalsIgnoreCase(row.get(DimensionEnum.FLT_AGETYPE.getName()));
    }

    /**
     * 根据物理舱位转换舱等
     *
     * @param realClass
     * @return
     */
    public static String convertFlightClassID(String realClass) {
        if (StringUtils.isEmpty(realClass)) {
            return "Y";
        }
        if (realClass.contains(BusinessConst.FLIGHT_REALCLASS_FIRST)) {
            return "F";
        } else if (realClass.contains(BusinessConst.FLIGHT_REALCLASS_BUSINESS)) {
            return "C";
        } else {
            return "Y";
        }
    }

    /**
     * 判断火车票是否改签
     * quantity为0就是退票
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isTrainChange(Map<String, String> row) {
        int quantity = Integer.parseInt(row.get(StatisticalEnum.TRAIN_QUANTITY.getName()));
        if (quantity == 0) {
            return true;
        }
        return false;
        //return TrainStatusEnum.S.toString().equalsIgnoreCase(row.get(DimensionEnum.TRAIN_ISCHANGE.getName()));
    }

    /**
     * 判断火车票是否成交(废弃)
     *
     * @param row
     * @return true 是，false 否
     */
    @Deprecated
    public static boolean isTrainDealedData(Map<String, String> row) {
        return TrainStatusEnum.D.toString().equalsIgnoreCase(row.get(DimensionEnum.TRAIN_TICKETTYPE.getName()));
    }

    /**
     * 判断用车订单状态是否成交数据
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isCarOrderSuccessful(Map<String, String> row) {
        return CarOrderStatusEnum.Successful.toString().equalsIgnoreCase(row.get(DimensionEnum.CAR_ORDERSTATUS.getName()));
    }

    /**
     * 判断火车票是否出票状态
     * 判斷quantity>0
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isTrainSuccess(Map<String, String> row) {
        int quantity = Integer.parseInt(row.get(StatisticalEnum.TRAIN_QUANTITY.getName()));
        if (quantity > 0) {
            return true;
        }
        return false;
    }


    /**
     * 判断火车票是否退票
     * 判斷quantity小于0退票
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isTrainRefund(Map<String, String> row) {
        int quantity = Integer.parseInt(row.get(StatisticalEnum.TRAIN_QUANTITY.getName()));
        if (quantity < 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否online数据
     *
     * @param row
     * @return true 是，false 否
     */
    public static boolean isOnlineData(Map<String, String> row) {
        return BookTypeEnum.T.getVal().equalsIgnoreCase(row.get(DimensionEnum.BOOKTYPE_SINGLE.getName()));
    }


    //region 过滤组织部门

    /**
     * 过滤组织部门
     *
     * @param sourceList
     * @param queryRange
     * @return
     */
    public static List<Map<String, String>> filterOrg(List<Map<String, String>> sourceList, DataQueryRange queryRange) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return sourceList;
        }
        //是否根据组织过滤数据
        boolean needFilterOrg = (queryRange.getDeptList() != null && !queryRange.getDeptList().isEmpty())
                || (queryRange.getCostCenterList() != null && !queryRange.getCostCenterList().isEmpty());

        if (!needFilterOrg) {
            return sourceList;
        }

        List<Map<String, String>> tempList = new ArrayList<>();
        for (Map<String, String> row :
                sourceList) {
            if (!filterOrg(queryRange, row)) {
                continue;
            }
            tempList.add(row);
        }
        return tempList;
    }

    /**
     * 过滤部门
     *
     * @param dataQueryRange
     * @param row
     * @return
     */
    public static boolean filterOrg(DataQueryRange dataQueryRange, Map<String, String> row) {
        if (!filterDept(dataQueryRange, row)) {
            return false;
        }
        if (!filterCostCenter(dataQueryRange, row)) {
            return false;
        }
        return true;
    }

    /**
     * 筛选部门
     *
     * @param dataQueryRange
     * @param row
     * @return
     */
    public static boolean filterDept(DataQueryRange dataQueryRange, Map<String, String> row) {
        if (dataQueryRange.getDeptList() == null) {
            return true;
        }
        if (!filterOrg(1, dataQueryRange.getDeptList(), row.get(DimensionEnum.DEPT1.getName()))) {
            return false;
        }
        if (!filterOrg(2, dataQueryRange.getDeptList(), row.get(DimensionEnum.DEPT2.getName()))) {
            return false;
        }
        if (!filterOrg(3, dataQueryRange.getDeptList(), row.get(DimensionEnum.DEPT3.getName()))) {
            return false;
        }
        if (!filterOrg(4, dataQueryRange.getDeptList(), row.get(DimensionEnum.DEPT4.getName()))) {
            return false;
        }
        if (!filterOrg(5, dataQueryRange.getDeptList(), row.get(DimensionEnum.DEPT5.getName()))) {
            return false;
        }
        if (!filterOrg(6, dataQueryRange.getDeptList(), row.get(DimensionEnum.DEPT6.getName()))) {
            return false;
        }
        return true;
    }

    /**
     * 筛选成本中心
     *
     * @param dataQueryRange
     * @param row
     * @return
     */
    public static boolean filterCostCenter(DataQueryRange dataQueryRange, Map<String, String> row) {
        if (dataQueryRange.getCostCenterList() == null) {
            return true;
        }
        if (!filterOrg(1, dataQueryRange.getCostCenterList(), row.get(DimensionEnum.COSTCENTER1.getName()))) {
            return false;
        }
        if (!filterOrg(2, dataQueryRange.getCostCenterList(), row.get(DimensionEnum.COSTCENTER2.getName()))) {
            return false;
        }
        if (!filterOrg(3, dataQueryRange.getCostCenterList(), row.get(DimensionEnum.COSTCENTER3.getName()))) {
            return false;
        }
        if (!filterOrg(4, dataQueryRange.getCostCenterList(), row.get(DimensionEnum.COSTCENTER4.getName()))) {
            return false;
        }
        if (!filterOrg(5, dataQueryRange.getCostCenterList(), row.get(DimensionEnum.COSTCENTER5.getName()))) {
            return false;
        }
        if (!filterOrg(6, dataQueryRange.getCostCenterList(), row.get(DimensionEnum.COSTCENTER6.getName()))) {
            return false;
        }
        return true;
    }


    /**
     * 判断orgMap 是否包含orgVal
     *
     * @param key
     * @param orgMap
     * @param orgVal
     * @return
     */
    public static boolean filterOrg(int key, Map<Integer, HashSet<String>> orgMap, String orgVal) {
        if (orgMap == null || !orgMap.containsKey(key) || orgMap.get(key) == null || orgMap.get(key).isEmpty()) {
            return true;
        }

        if (StringUtils.isNotEmpty(orgVal)) {
            return orgMap.get(key).contains(orgVal.toLowerCase().trim());
        } else {
            return orgMap.get(key).contains(GlobalConst.STRING_EMPTY);
        }
    }
    //endregion


    public static String convertDone(String done) {
        if (StringUtils.isEmpty(done) || StringUtils.isBlank(done)) {
            return GlobalConst.STRING_EMPTY;
        }
        if (StringUtils.equalsIgnoreCase(done, "TD")) {
            return "成交";
        } else if (StringUtils.equalsIgnoreCase(done, "TC")) {
            return "退订";
        } else {
            return "其它";
        }
    }

    public static String convertOrderType(String orderType) {
        if (StringUtils.isEmpty(orderType)) {
            return GlobalConst.STRING_EMPTY;
        }
        if (OrderTypeEnum.M.toString().equalsIgnoreCase(orderType)) {
            return "会员";
        } else if (OrderTypeEnum.C.toString().equalsIgnoreCase(orderType)) {
            return "协议";
        } else {
            return GlobalConst.STRING_EMPTY;
        }
    }

    public static String convertIsOverSea(String isOverSea, String lang) {
        if (StringUtils.isEmpty(isOverSea)) {
            return GlobalConst.STRING_EMPTY;
        }
        if (HtlTypeEnum.F.toString().equalsIgnoreCase(isOverSea)) {
            return SharkUtils.get("lbl_HtlOverseaF", lang);
        } else if (HtlTypeEnum.O.toString().equalsIgnoreCase(isOverSea)) {
            return SharkUtils.get("index.hkmctw", lang);
        } else if (HtlTypeEnum.T.toString().equalsIgnoreCase(isOverSea)) {
            return SharkUtils.get("lbl_HtlOverseaT", lang);
        } else {
            return StringUtils.trimToEmpty(isOverSea);
        }
    }

    public static String convertTorF(Integer str, Map sourceMap) {
        if (Objects.isNull(str) || Objects.isNull(sourceMap)) {
            return StringUtils.EMPTY;
        }
        if (str == 0) {
            return (String) sourceMap.get(YesOrNotEnum.F.toString());
        } else if (str == 1) {
            return (String) sourceMap.get(YesOrNotEnum.T.toString());
        }
        return StringUtils.EMPTY;
    }

    public static String convertTorF(String str, Map sourceMap) {
        if (StringUtils.equalsIgnoreCase(YesOrNotEnum.T.toString().trim(), trim(str))) {
            return (String) sourceMap.get(YesOrNotEnum.T.toString());
        } else if (StringUtils.equalsIgnoreCase(YesOrNotEnum.F.toString().trim(), trim(str))) {
            return (String) sourceMap.get(YesOrNotEnum.F.toString());
        } else if (StringUtils.equalsIgnoreCase("N".trim(), trim(str))) {
            return (String) sourceMap.get(YesOrNotEnum.F.toString());
        } else if (StringUtils.equalsIgnoreCase("Y", trim(str))) {
            return (String) sourceMap.get(YesOrNotEnum.T.toString());
        }
        return trim(str);
    }

    public static String convertPatternType(String patternType, String lang) {
        if (StringUtils.equalsIgnoreCase(patternType, "17")) {
            return SharkUtils.get("index.fromairport", lang);
        } else if (StringUtils.equalsIgnoreCase(patternType, "18")) {
            return SharkUtils.get("index.toairport", lang);
        }
        return StringUtils.trimToEmpty(patternType);
    }

    /**
     * @param realClass
     * @return
     */
    public static String convertClass(String realClass, String flightClass) {
        if (OrderTypeEnum.N.toString().equalsIgnoreCase(flightClass)) {
            if (StringUtils.isNotEmpty(realClass)) {
                if (realClass.contains(BusinessConst.FLIGHT_REALCLASS_ECONOMY) && realClass.length() > 3) {
                    return BusinessConst.FLIGHT_REALCLASS_ECONOMYSUPER;
                }
            }
        }
        return MapperUtils.trim(realClass);
    }

    public static String getValByLang(String lang, String cnVal, String enVal) {
        if (StringUtils.isEmpty(lang) || "zh-cn".equalsIgnoreCase(lang)) {
            return trim(cnVal);
        } else if (StringUtils.equalsIgnoreCase("en", lang) || StringUtils.equalsIgnoreCase("en-us", lang)) {
            return trim(enVal);
        }
        return trim(cnVal);
    }

    /**
     * @param time yyyy-mm-dd HH:MM:SS
     * @return
     */
    public static String subStrTime(String time) {
        if (StringUtils.isNotEmpty(time)) {
            return time.substring(0, 10);
        }
        return StringUtils.EMPTY;
    }

}
