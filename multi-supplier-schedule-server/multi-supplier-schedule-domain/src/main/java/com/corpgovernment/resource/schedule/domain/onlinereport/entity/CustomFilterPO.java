package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import org.springframework.beans.BeanUtils;

/**
 * Auther:abguo
 * Date:2019/8/13
 * Description:
 * Project:onlinereportweb
 */
public class CustomFilterPO {
    private CustomCommonFilterPO customCommonFilterPO;
    private CustomFlightFilterPO customFlightFilterPO;
    private CustomHotelFilterPO customHotelFilterPO;
    private CustomTrainFilterPO customTrainFilterPO;

    private String lang;

    private String loginUid;

    public CustomCommonFilterPO getCustomCommonFilterPO() {
        return customCommonFilterPO;
    }

    public void setCustomCommonFilterPO(CustomCommonFilterPO customCommonFilterPO) {
        this.customCommonFilterPO = customCommonFilterPO;
    }

    public CustomFlightFilterPO getCustomFlightFilterPO() {
        return customFlightFilterPO;
    }

    public void setCustomFlightFilterPO(CustomFlightFilterPO customFlightFilterPO) {
        this.customFlightFilterPO = customFlightFilterPO;
    }

    public CustomHotelFilterPO getCustomHotelFilterPO() {
        return customHotelFilterPO;
    }

    public void setCustomHotelFilterPO(CustomHotelFilterPO customHotelFilterPO) {
        this.customHotelFilterPO = customHotelFilterPO;
    }

    public CustomTrainFilterPO getCustomTrainFilterPO() {
        return customTrainFilterPO;
    }

    public void setCustomTrainFilterPO(CustomTrainFilterPO customTrainFilterPO) {
        this.customTrainFilterPO = customTrainFilterPO;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getLoginUid() {
        return loginUid;
    }

    public void setLoginUid(String loginUid) {
        this.loginUid = loginUid;
    }

    /**
     * clone
     * @return
     */
    public CustomFilterPO copyEntity() {
        try {
            CustomFilterPO other = new CustomFilterPO();
            BeanUtils.copyProperties(this, other);
            return other;
        } catch (Exception ex) {
            return null;
        }
    }
}
