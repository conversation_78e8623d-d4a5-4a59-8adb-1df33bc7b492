package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 基本统计查询条件
 */
@SuppressWarnings("all")
@Data
public class BaseQueryCondition implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 报告id
     */
    @JsonProperty("reportId")
    public String reportId;
    /**
     * 登录用户id，用于日志记录
     */
    @JsonProperty("uid")
    public String uid;
    /**
     * 登录用户id所属公司
     */
    @JsonProperty("userCorpId")
    public String userCorpId;
    /**
     * 登录用户公司所属行业，用于查询行业数据
     */
    @JsonProperty("industryType")
    public String industryType;
    /**
     * 集团id
     */
    @JsonProperty("groupId")
    public String groupId;
    /**
     * 查询开始时间,必传
     */
    @JsonProperty("startTime")
    public String startTime;
    /**
     * 查询结束时间,必传
     */
    @JsonProperty("endTime")
    public String endTime;
    /**
     * 同比-去年/前年-(detail使用)
     */
    @JsonProperty("yoyType")
    public String yoyType;
    /**
     * 预订方式：T online; F offline; M app;
     */
    @JsonProperty("orderChannel")
    public List<String> orderChannel;
    /**
     * 公司id，corpIds和accountIds不能同时为空
     */
    @JsonProperty("corpIds")
    public List<String> corpIds;

    /**
     * 主账户，corpIds和accountIds不能同时为空
     */
    @JsonProperty("accountIds")
    public List<String> accountIds;
    /**
     * 部门列表
     */
    @JsonProperty("deptList")
    public List<SearchDeptAndCostcneterEntity> deptList;
    /**
     * 成本中心列表
     */
    @JsonProperty("costCenterList")
    public List<SearchDeptAndCostcneterEntity> costCenterList;
    /**
     * 支持多种不同时间类型
     */
    @JsonProperty("timeFilterList")
    public List<TimeFilterTypeInfo> timeFilterList;
    /**
     * 航司
     */
    @JsonProperty("airLines")
    public List<String> airLines;
    /**
     * 酒店集团
     */
    @JsonProperty("hotelGroups")
    public List<String> hotelGroups;

    public List<String> hotelIds;
    /**
     * 城市id
     */
    @JsonProperty("cityIds")
    public List<Integer> cityIds;
    /**
     * 省份id
     */
    @JsonProperty("provinceIds")
    public List<Integer> provinceIds;
    /**
     * 国家id
     */
    @JsonProperty("countryIds")
    public List<Integer> countryIds;
    /**
     * 酒店类型,会员:M,协议：C
     */
    @JsonProperty("htlOrderTypes")
    public List<String> htlOrderTypes;
    /**
     * 公司的地区POS站点 默认值为zh-CN，目前含：zh-CN中国、ja-JP日本
     */
    @JsonProperty("pos")
    public String pos;
    /**
     * 数据统计口径，成交/预订
     */
    @JsonProperty("statisticalCaliber")
    public String statisticalCaliber;
    /**
     * 是否蓝色空间， T: 是, F:否
     */
    @JsonProperty("blueSpace")
    public String blueSpace;
    /**
     * 下钻部门
     */
    @JsonProperty("deptEntity")
    public SearchDeptAndCostcneterEntity deptEntity;
    /**
     * 下钻成本中心
     */
    @JsonProperty("costCenterEntity")
    public SearchDeptAndCostcneterEntity costCenterEntity;
    /**
     * 比较消费同级公司，行业数据
     */
    @JsonProperty("compareSameLevel")
    public String compareSameLevel;
    /**
     * 消费等级
     */
    @JsonProperty("consumptionLevel")
    public String consumptionLevel;
    /**
     * 比较消费同级公司，商旅数据
     */
    @JsonProperty("compareCorpSameLevel")
    public String compareCorpSameLevel;
    /**
     * 币种
     */
    @JsonProperty("currency")
    public String currency;
    /**
     * 使用启用StarRocks
     */
    @JsonProperty("useStarRocks")
    public Boolean useStarRocks;


}
