package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

/**
 * @Description: 异常机票风险级别分析详情
 * <AUTHOR>
 * @Date 2019/5/31
 */
public class FlightUnusualTicketWarningLevelAnalysisInfoBO {
    /**
     * 风险级别
     */
    private Integer warningLevel;

    /**
     * 票张
     */
    private Integer quantity;

    public FlightUnusualTicketWarningLevelAnalysisInfoBO(){
        this.warningLevel=0;
        this.quantity=0;
    }

    public Integer getWarningLevel() {
        return warningLevel;
    }

    public void setWarningLevel(Integer warningLevel) {
        this.warningLevel = warningLevel;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
}
