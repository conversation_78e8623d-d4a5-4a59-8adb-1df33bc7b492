package com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-23 21:01
 */
@AllArgsConstructor
@Getter
public enum TravelModeEnum {
    
    PUB("pub", "因公"),
    OWN("own", "因私");
    
    private final String code;
    
    private final String info;
    
    private static final Map<String, TravelModeEnum> map = new HashMap<>();
    
    static {
        for (TravelModeEnum tmpEnum : values()) {
            map.put(tmpEnum.getCode(), tmpEnum);
            map.put(tmpEnum.getInfo(), tmpEnum);
            map.put(tmpEnum.name(), tmpEnum);
        }
    }
    
    public static TravelModeEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return map.get(key);
    }
    
}
