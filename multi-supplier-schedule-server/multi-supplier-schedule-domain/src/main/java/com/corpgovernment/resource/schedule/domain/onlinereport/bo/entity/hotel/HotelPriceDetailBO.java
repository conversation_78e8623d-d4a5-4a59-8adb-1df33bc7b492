package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.hotel;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2019/5/7
 */
public class HotelPriceDetailBO {

    //城市
    private int city;
    //城市名称
    private String cityName;

    //城市英文名
    private String cityEngName;
    //酒店类型
    private String orderType;
    //星级
    private int star;
    //间夜
    private int quantity;
    //金额
    private double amount;
    //间夜-会员
    private int quantityM;
    //金额-会员
    private double amountM;
    //间夜-协议
    private int quantityC;
    //金额-协议
    private double amountC;
    //T 海外，F国内，O港澳台
    private String isOversea;

    //商旅间夜-会员
    private int quantityCorpM;
    //商旅金额-会员
    private double amountCorpM;
    //商旅间夜-协议
    private int quantityCorpC;
    //商旅金额-协议
    private double amountCorpC;

    public int getCity() {
        return city;
    }

    public void setCity(int city) {
        this.city = city;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public int getStar() {
        return star;
    }

    public void setStar(int star) {
        this.star = star;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public int getQuantityM() {
        return quantityM;
    }

    public void setQuantityM(int quantityM) {
        this.quantityM = quantityM;
    }

    public double getAmountM() {
        return amountM;
    }

    public void setAmountM(double amountM) {
        this.amountM = amountM;
    }

    public int getQuantityC() {
        return quantityC;
    }

    public void setQuantityC(int quantityC) {
        this.quantityC = quantityC;
    }

    public double getAmountC() {
        return amountC;
    }

    public void setAmountC(double amountC) {
        this.amountC = amountC;
    }

    public String getIsOversea() {
        return isOversea;
    }

    public void setIsOversea(String isOversea) {
        this.isOversea = isOversea;
    }

    public String getCityEngName() {
        return cityEngName;
    }

    public void setCityEngName(String cityEngName) {
        this.cityEngName = cityEngName;
    }

    public int getQuantityCorpM() {
        return quantityCorpM;
    }

    public void setQuantityCorpM(int quantityCorpM) {
        this.quantityCorpM = quantityCorpM;
    }

    public double getAmountCorpM() {
        return amountCorpM;
    }

    public void setAmountCorpM(double amountCorpM) {
        this.amountCorpM = amountCorpM;
    }

    public int getQuantityCorpC() {
        return quantityCorpC;
    }

    public void setQuantityCorpC(int quantityCorpC) {
        this.quantityCorpC = quantityCorpC;
    }

    public double getAmountCorpC() {
        return amountCorpC;
    }

    public void setAmountCorpC(double amountCorpC) {
        this.amountCorpC = amountCorpC;
    }
}
