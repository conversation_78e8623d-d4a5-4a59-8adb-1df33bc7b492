package com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/4/15 14:28
 * @description
 */
@Data
public class SumEntry {
    @JsonProperty("priceAmount")
    public BigDecimal priceAmount;
    @JsonProperty("qoqAmount")
    public BigDecimal qoqAmount;
    @JsonProperty("yoyAmount")
    public BigDecimal yoyAmount;
    @JsonProperty("qoq")
    public BigDecimal qoq;
    @JsonProperty("yoy")
    public BigDecimal yoy;
}
