package com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.impl;

import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.HotelAuditItem;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.HotelAuditResult;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.TravelStandardItem;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.HotelAuditRuleEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelModeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.IHotelAuditDomainService;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.IHotelAuditRule;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-24 14:09
 */
@Slf4j
@Service
public class MaxPriceUnLimitAuditRule implements IHotelAuditRule {
    
    @Resource
    private IHotelAuditDomainService hotelAuditDomainService;
    
    @Override
    public HotelAuditResult audit(HotelAuditItem hotelAuditItem) {
        List<TravelStandardItem> usedTravelStandardItemList = hotelAuditDomainService.getUsedTravelStandardItemList(hotelAuditItem);
        if (CollectionUtils.isEmpty(usedTravelStandardItemList)) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                    .auditDescList(Collections.singletonList("无任何差标"))
                    .auditPass(false)
                    .errorLabel(getHotelAuditRuleEnum().getErrorLabel())
                    .build();
        }
        // 校验
        List<String> auditDescList = new ArrayList<>();
        for (TravelStandardItem travelStandardItem : usedTravelStandardItemList) {
            BigDecimal maxPrice = Optional.ofNullable(travelStandardItem)
                    .map(TravelStandardItem::getMaxPrice)
                    .orElse(null);
            Integer index = Optional.ofNullable(travelStandardItem)
                    .map(TravelStandardItem::getIndex)
                    .orElse(null);
            if (maxPrice == null) {
                auditDescList.add(String.format("索引%s，价格上限差标为不限", index));
            }
        }
        
        if (CollectionUtils.isNotEmpty(auditDescList)) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                    .auditDescList(auditDescList)
                    .auditPass(false)
                    .errorLabel(getHotelAuditRuleEnum().getErrorLabel())
                    .build();
        }
        return HotelAuditResult.builder()
                .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                .auditDescList(null)
                .auditPass(true)
                .build();
    }
    
    @Override
    public HotelAuditRuleEnum getHotelAuditRuleEnum() {
        return HotelAuditRuleEnum.MAX_PRICE_UN_LIMIT;
    }
    
    @Override
    public List<TravelModeEnum> getSupportTravelModeEnumList() {
        return Collections.singletonList(TravelModeEnum.PUB);
    }
    
}
