package com.corpgovernment.resource.schedule.domain.accesslog.model.resp;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class Top5InfoExcel {

    /**
     * 排行
     */
    private Integer rank;

    /**
     * 产线。机票、酒店、火车票、公共
     */
    private String trafficType;

    /**
     * 问题描述
     */
    private String description;

    /**
     * 问题回复
     */
    private String answer;

    /**
     * 整体浏览量（PV）
     */
    private Integer pv;

    /**
     * PC浏览量（PV）
     */
    private Integer pcPv;

    /**
     * 移动端浏览量（PV）
     */
    private Integer mobilePv;
}
