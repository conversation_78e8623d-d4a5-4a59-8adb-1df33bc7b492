package com.corpgovernment.resource.schedule.domain.onlinereport.entity;



import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 查询 - 按手否生效状态查询
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "uid",
    "status",
    "id",
    "page"
})
public class OnlineReportNoteListRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportNoteListRequest(
        String uid,
        List<Integer> status,
        Integer id,
        Pager page) {
        this.uid = uid;
        this.status = status;
        this.id = id;
        this.page = page;
    }

    public OnlineReportNoteListRequest() {
    }

    /**
     * 用户uid
     */
    @JsonProperty("uid")
    public String uid;

    /**
     * 查询的状态
     */
    @JsonProperty("status")
    public List<Integer> status;

    /**
     * 按id查询
     */
    @JsonProperty("id")
    public Integer id;

    /**
     * page翻页
     */
    @JsonProperty("page")
    public Pager page;

    /**
     * 用户uid
     */
    public String getUid() {
        return uid;
    }

    /**
     * 用户uid
     */
    public void setUid(final String uid) {
        this.uid = uid;
    }

    /**
     * 查询的状态
     */
    public List<Integer> getStatus() {
        return status;
    }

    /**
     * 查询的状态
     */
    public void setStatus(final List<Integer> status) {
        this.status = status;
    }

    /**
     * 按id查询
     */
    public Integer getId() {
        return id;
    }

    /**
     * 按id查询
     */
    public void setId(final Integer id) {
        this.id = id;
    }

    /**
     * page翻页
     */
    public Pager getPage() {
        return page;
    }

    /**
     * page翻页
     */
    public void setPage(final Pager page) {
        this.page = page;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportNoteListRequest other = (OnlineReportNoteListRequest)obj;
        return
            Objects.equal(this.uid, other.uid) &&
            Objects.equal(this.status, other.status) &&
            Objects.equal(this.id, other.id) &&
            Objects.equal(this.page, other.page);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.uid == null ? 0 : this.uid.hashCode());
        result = 31 * result + (this.status == null ? 0 : this.status.hashCode());
        result = 31 * result + (this.id == null ? 0 : this.id.hashCode());
        result = 31 * result + (this.page == null ? 0 : this.page.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("uid", uid)
            .add("status", status)
            .add("id", id)
            .add("page", page)
            .toString();
    }
}
