package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;

/**
 * 查询聚合维度-日期
 */
public enum QueryReportAggDateDimensionEnum {

    /**
     * 月
     */
    month(0),

    /**
     * 季度
     */
    quarter(1),

    /**
     * 半年
     */
    half(2),

    /**
     * 按日查询
     */
    day(3),

    /**
     * 年
     */
    year(4),

    /**
     * 周
     */
    week(5);

    private final int value;

    QueryReportAggDateDimensionEnum(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public int getValue() {
        return value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public static QueryReportAggDateDimensionEnum findByValue(int value) {
        switch (value) {
            case 0:
                return month;
            case 1:
                return quarter;
            case 2:
                return half;
            case 3:
                return day;
            case 4:
                return year;
            case 5:
                return week;
            default:
                return null;
        }
    }
}
