package com.corpgovernment.resource.schedule.domain.onlinereport.enums;


import com.corpgovernment.resource.schedule.domain.onlinereport.constant.RedisKeyConst;

/**
 * 基础数据类型
 */
public enum BaseDataTypeEnum {


    //主账户
    FundAccount(RedisKeyConst.COIREPORT_BASEDATA_FUNDACCOUNT),
    //公司
    CustomerMass(RedisKeyConst.COIREPORT_BASEDATA_CUSTOMERMASS),
    //上去
    Zone(RedisKeyConst.COIREPORT_BASEDATA_ZONE),
    //洲
    Continent(RedisKeyConst.COIREPORT_BASEDATA_CONTINENT),
    //城市
    City(RedisKeyConst.COIREPORT_BASEDATA_CITY),
    //航线
    FltAirline(RedisKeyConst.COIREPORT_BASEDATA_AIRLINE),
    //火车站
    TrainStation(RedisKeyConst.COIREPORT_BASEDATA_TRAINSTATION),
    //行政区
    Location(RedisKeyConst.COIREPORT_BASEDATA_LOCATION),
    //酒店
    Hotel(RedisKeyConst.COIREPORT_BASEDATA_HOTEL),
    //错误队列
    Error(""),

    /**
     * 商旅用户
     */
    CorpUser(RedisKeyConst.COIREPORT_BASEDATA_CORPUSER),
    /**
     * 商旅主账户信息
     */
    CorpAccount(RedisKeyConst.COIREPORT_BASEDATA_CORPACCOUNT),

    /**
     * 商旅用户uid-主账户accountid关系
     */
    CorpUserAccount(RedisKeyConst.COIREPORT_BASEDATA_CORPUSERACCOUNT),

    /**
     * 用户职级
     */
    CorpUserRankInfo(RedisKeyConst.COIREPORT_BASEDATA_USERRANK),
    /**
     * 公司职级List
     */
    CorpRankInfo(RedisKeyConst.COIREPORT_BASEDATA_CORPRANK),

    /**
     * 部门、成本中心
     */
    CorpOrganization(RedisKeyConst.COIREPORT_BASEDATA_ORGANIZATION),

    PROVINCE(RedisKeyConst.COIREPORT_BASEDATA_PROVINCE),

    COUNTRY(RedisKeyConst.COIREPORT_BASEDATA_COUNTRY),

    /*
     * 机票reasoncode
     */
    FLIGHTREASONCODEINFO(RedisKeyConst.COIREPORT_BASEDATA_FLIGHT_REASONCODE);

    private String redisKey;

    private BaseDataTypeEnum(String redisKey) {
        this.redisKey = redisKey;
    }

    public String getRedisKey() {
        return redisKey;
    }
}
