package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.Map;

/**
 * **查询卡号所属成本中心根据最近一次下单***************
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "lang",
    "basecondition",
    "uid",
    "eid",
    "extData"
})
public class OnlineReportQueryDeptByUidRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportQueryDeptByUidRequest(
        String lang,
        BaseQueryCondition basecondition,
        String uid,
        String eid,
        Map<String, String> extData) {
        this.lang = lang;
        this.basecondition = basecondition;
        this.uid = uid;
        this.eid = eid;
        this.extData = extData;
    }

    public OnlineReportQueryDeptByUidRequest() {
    }

    /**
     * 语言
     */
    @JsonProperty("lang")
    public String lang;

    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    @JsonProperty("uid")
    public String uid;

    @JsonProperty("eid")
    public String eid;

    /**
     * 扩展字段
     */
    @JsonProperty("extData")
    public Map<String, String> extData;

    /**
     * 语言
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }
    public String getUid() {
        return uid;
    }

    public void setUid(final String uid) {
        this.uid = uid;
    }
    public String getEid() {
        return eid;
    }

    public void setEid(final String eid) {
        this.eid = eid;
    }

    /**
     * 扩展字段
     */
    public Map<String, String> getExtData() {
        return extData;
    }

    /**
     * 扩展字段
     */
    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportQueryDeptByUidRequest other = (OnlineReportQueryDeptByUidRequest)obj;
        return
            Objects.equal(this.lang, other.lang) &&
            Objects.equal(this.basecondition, other.basecondition) &&
            Objects.equal(this.uid, other.uid) &&
            Objects.equal(this.eid, other.eid) &&
            Objects.equal(this.extData, other.extData);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.uid == null ? 0 : this.uid.hashCode());
        result = 31 * result + (this.eid == null ? 0 : this.eid.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("lang", lang)
            .add("basecondition", basecondition)
            .add("uid", uid)
            .add("eid", eid)
            .add("extData", extData)
            .toString();
    }
}
