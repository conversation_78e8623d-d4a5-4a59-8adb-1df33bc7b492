package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "hotCity",
    "cityList"
})
public class TravelPositionCityData implements Serializable {
    private static final long serialVersionUID = 1L;





    public TravelPositionCityData(
        List<TravelPositionCityInfo> hotCity,
        List<TravelPositionCityInfo> cityList) {
        this.hotCity = hotCity;
        this.cityList = cityList;
    }

    public TravelPositionCityData() {
    }

    @JsonProperty("hotCity")
    public List<TravelPositionCityInfo> hotCity;

    @JsonProperty("cityList")
    public List<TravelPositionCityInfo> cityList;

    public List<TravelPositionCityInfo> getHotCity() {
        return hotCity;
    }

    public void setHotCity(final List<TravelPositionCityInfo> hotCity) {
        this.hotCity = hotCity;
    }
    public List<TravelPositionCityInfo> getCityList() {
        return cityList;
    }

    public void setCityList(final List<TravelPositionCityInfo> cityList) {
        this.cityList = cityList;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TravelPositionCityData other = (TravelPositionCityData)obj;
        return
            Objects.equal(this.hotCity, other.hotCity) &&
            Objects.equal(this.cityList, other.cityList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.hotCity == null ? 0 : this.hotCity.hashCode());
        result = 31 * result + (this.cityList == null ? 0 : this.cityList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("hotCity", hotCity)
            .add("cityList", cityList)
            .toString();
    }
}
