package com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz;


import com.corpgovernment.resource.schedule.domain.onlinereport.constant.ExportConstant;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailAmountDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.OnlineReportData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportDataDetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReprotBodyData;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.CommonUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: com.ctrip.corp.onlinereportweb.domainreport.adaptor.biz
 * @description:
 * @author: Chris Yu
 * @create: 2021-11-15 15:31
 **/
@Service
public class CarReportDetailService {

    /**
     * 用车-整体
     *
     * @param carDetailData
     * @return
     */
    public List<ChartExcelEntity> carAmountAllBodyData(DetailData carDetailData) {
        return carAmountDetail(carDetailData, ExportConstant.ZERO);
    }

    /**
     * 分产线
     *
     * @param reportData
     * @return
     */
    public List<ChartExcelEntity> carSubProductBodyData(OnlineReportData reportData) {
        List<ChartExcelEntity> carSubProductList = new ArrayList<>();
        DetailAmountDto carAmount = reportData.getDetailAmountData();
        if (Objects.isNull(carAmount)) {
            return Lists.newArrayList();
        }
        List<ChartExcelEntity> takeTaxiExcelEntityList = carAmountDetail(carAmount.getTakeTaxiDetail(), ExportConstant.ZERO);
        List<ChartExcelEntity> takeTaxiInterExcelEntityList = carAmountDetail(carAmount.getTakeTaxiDetailInter(), ExportConstant.ONE);
        List<ChartExcelEntity> airportPickUpExcelEntityList = carAmountDetail(carAmount.getAirportPickUpDetail(), ExportConstant.TWO);
        List<ChartExcelEntity> airportPickUpInterExcelEntityList = carAmountDetail(carAmount.getAirportPickUpDetailInter(), ExportConstant.THREE);
        List<ChartExcelEntity> rentalCarExcelEntityList = carAmountDetail(carAmount.getRentalCarDetail(), ExportConstant.FOUR);
//        List<ChartExcelEntity> rentalCarInterExcelEntityList = carAmountDetail(carAmount.getRentalCarDetailInter(), ExportConstant.FIVE);
        List<ChartExcelEntity> charteredCarExcelEntityList = carAmountDetail(carAmount.getCharteredCarDetail(), ExportConstant.FIVE);

        carSubProductList.addAll(takeTaxiExcelEntityList);
        carSubProductList.addAll(takeTaxiInterExcelEntityList);
        carSubProductList.addAll(airportPickUpExcelEntityList);
        carSubProductList.addAll(airportPickUpInterExcelEntityList);
        carSubProductList.addAll(rentalCarExcelEntityList);
//        carSubProductList.addAll(rentalCarInterExcelEntityList);
        carSubProductList.addAll(charteredCarExcelEntityList);
        return carSubProductList;
    }


    private List<ChartExcelEntity> carAmountDetail(DetailData carDetailData, int sheetNum) {
        if (Objects.isNull(carDetailData)) {
            return Lists.newArrayList();
        }
        ChartExcelEntity amountAllEntity = new ChartExcelEntity();
        // title
        amountAllEntity.setSheetTitle(carDetailData.getHeaderData().get(ExportConstant.ZERO).getHeaderValue());
        // header
        amountAllEntity.setHeaders(carDetailData.getHeaderData().stream().map(HeaderKeyValMap::getHeaderValue).collect(Collectors.toList()));
        amountAllEntity.setSheetNum(sheetNum);
        List<List<String>> excelData = Lists.newArrayList();
        List<String> carExcelBodyData = null;
        for (ReprotBodyData bodyData : carDetailData.getBodyList()) {
            carExcelBodyData = Lists.newArrayList();
            carExcelBodyData.add(bodyData.getDimension());
            ReportDataDetailData hotelBodyData = bodyData.getDetailData();
            carExcelBodyData.add(hotelBodyData.getBasicFee());
            carExcelBodyData.add(hotelBodyData.getServiceFee());
            carExcelBodyData.add(hotelBodyData.getRefundAmount());
            carExcelBodyData.add(hotelBodyData.getTotalV());
            // 叠加数据
            excelData.add(carExcelBodyData);
        }
        amountAllEntity.setData(excelData);
        return Lists.newArrayList(amountAllEntity);
    }


    /**
     * 用车-整体
     *
     * @param carDetailData
     * @return
     */
    public List<ChartExcelEntity> carAmountTicketBodyData(String sheetTitle, DetailData carDetailData) {
        if (Objects.isNull(carDetailData)) {
            return Lists.newArrayList();
        }
        ChartExcelEntity amountAllEntity = new ChartExcelEntity();
        // title
        amountAllEntity.setSheetTitle(sheetTitle);
        // header
        amountAllEntity.setHeaders(carDetailData.getHeaderData().stream().map(HeaderKeyValMap::getHeaderValue).collect(Collectors.toList()));
        amountAllEntity.setSheetNum(ExportConstant.ZERO);
        List<List<String>> excelData = Lists.newArrayList();
        List<String> carExcelBodyData = null;
        for (ReprotBodyData bodyData : carDetailData.getBodyList()) {
            carExcelBodyData = Lists.newArrayList();
            carExcelBodyData.add(bodyData.getDimension());
            ReportDataDetailData carBodyData = bodyData.getDetailData();
            carExcelBodyData.add(carBodyData.getTakeCarV());
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getTakeCarYoy()));
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getTakeCarMom()));

            carExcelBodyData.add(carBodyData.getTakeCarVInter());
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getTakeCarYoyInter()));
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getTakeCarMomInter()));

            carExcelBodyData.add(carBodyData.getTransferV());
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getTransferYoy()));
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getTransferMom()));

            carExcelBodyData.add(carBodyData.getTransferVInter());
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getTransferYoyInter()));
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getTransferMomInter()));

            carExcelBodyData.add(carBodyData.getRentalCarV());
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getRentalCarYoy()));
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getRentalCarMom()));

            carExcelBodyData.add(carBodyData.getRentalCarVInter());
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getRentalCarYoyInter()));
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getRentalCarMomInter()));

            carExcelBodyData.add(carBodyData.getCharterCarV());
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getCharterCarYoy()));
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getCharterCarMom()));

            carExcelBodyData.add(carBodyData.getTotalV());
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getTotalYoy()));
            carExcelBodyData.add(CommonUtils.cancelSymbol(carBodyData.getTotalMom()));
            // 叠加数据
            excelData.add(carExcelBodyData);
        }
        amountAllEntity.setData(excelData);
        return Lists.newArrayList(amountAllEntity);
    }
}
