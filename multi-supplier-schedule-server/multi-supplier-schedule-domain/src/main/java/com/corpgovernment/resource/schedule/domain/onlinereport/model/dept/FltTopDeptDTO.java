package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FltTopDeptDTO {

    @Column(name = "aggId")
    @Type(value = Types.VARCHAR)
    private String aggId;

    @Column(name = "aggType")
    @Type(value = Types.VARCHAR)
    private String aggType;

    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;

    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    @Column(name = "totalEconmyPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalEconmyPrice;

    @Column(name = "totalEconmyTpms")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalEconmyTpms;

    @Column(name = "totalEconmyQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalEconmyQuantity;

    @Column(name = "totalDomEconmyDiscount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalDomEconmyDiscount;

    @Column(name = "totalDomEconmyQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalDomEconmyQuantity;

    @Column(name = "totalFullfaretkt")
    @Type(value = Types.INTEGER)
    private Integer totalFullfaretkt;

    @Column(name = "totalDomEconmyFullfaretkt")
    @Type(value = Types.INTEGER)
    private Integer totalDomEconmyFullfaretkt;

    @Column(name = "totalDomEconmyOrdertkt")
    @Type(value = Types.INTEGER)
    private Integer totalDomEconmyOrdertkt;

    @Column(name = "totalRefundFee")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRefundFee;

    @Column(name = "totalRefundtkt")
    @Type(value = Types.INTEGER)
    private Integer totalRefundtkt;

    @Column(name = "totalRebookFee")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRebookFee;

    @Column(name = "totalRebooktkt")
    @Type(value = Types.INTEGER)
    private Integer totalRebooktkt;

    @Column(name = "totalOrdertkt")
    @Type(value = Types.INTEGER)
    private Integer totalOrdertkt;

    @Column(name = "totalOverAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalOverAmount;

    @Column(name = "totalRcTimes")
    @Type(value = Types.INTEGER)
    private Integer totalRcTimes;

    @Column(name = "totalOrderCount")
    @Type(value = Types.INTEGER)
    private Integer totalOrderCount;

    @Column(name = "totalSaveAmount3c")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalSaveAmount3c;

    @Column(name = "totalNetfare3c")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalNetfare3c;

    @Column(name = "totalSaveAmountPremium")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalSaveAmountPremium;

    @Column(name = "totalNetfarePremium")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalNetfarePremium;

    @Column(name = "totalControlSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalControlSave;

    @Column(name = "totalControlNetfare")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalControlNetfare;

    @Column(name = "totalCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCarbons;

    @Column(name = "totalMedianCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalMedianCarbons;

    @Column(name = "totalCarbonSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCarbonSave;

    @Column(name = "totalRefundloss")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRefundloss;

    @Column(name = "totalRebookloss")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRebookloss;

    @Column(name = "hotFlightCity")
    @Type(value = Types.VARCHAR)
    private String hotFlightCity;

    @Column(name = "totalPreOrderDate")
    @Type(value = Types.INTEGER)
    private Integer totalPreOrderDate;

    @Column(name = "totalPreOrderDateQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalPreOrderDateQuantity;

    @Column(name = "totalCarbonsTpms")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCarbonsTpms;

    @Column(name = "totalAllOrderCount")
    @Type(value = Types.INTEGER)
    private Integer totalAllOrderCount;
}
