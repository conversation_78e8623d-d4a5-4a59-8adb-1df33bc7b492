package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;

/**
 * 查询指标类型
 */
public enum QueryReportTypeionEnum {

    /**
     * 金额统计
     */
    amount(0),

    /**
     * 行程数统计
     */
    numberOfTrips(1),

    /**
     * 金额-明细
     */
    amountInfo(2),

    /**
     * 协议
     */
    agreement(3),

    /**
     * 非协议
     */
    nonAgreement(4),

    /**
     * 国内
     */
    domestic(5),

    /**
     * 国际
     */
    international(6),

    /**
     * 分产品
     */
    subProduct(7);

    private final int value;

    QueryReportTypeionEnum(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public int getValue() {
        return value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public static QueryReportTypeionEnum findByValue(int value) {
        switch (value) {
            case 0:
                return amount;
            case 1:
                return numberOfTrips;
            case 2:
                return amountInfo;
            case 3:
                return agreement;
            case 4:
                return nonAgreement;
            case 5:
                return domestic;
            case 6:
                return international;
            case 7:
                return subProduct;
            default:
                return null;
        }
    }
}
