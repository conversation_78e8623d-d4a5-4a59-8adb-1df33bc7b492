package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 酒店行为分析
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dim",
    "totalQuantity",
    "quantityPercent",
    "totalAmount",
    "amountPercent",
    "totalAmountM",
    "totalAmountC",
    "avgNightPriceM",
    "avgNightPriceC"
})
public class HotelBehaviorInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public HotelBehaviorInfo(
        String dim,
        Integer totalQuantity,
        Double quantityPercent,
        BigDecimal totalAmount,
        Double amountPercent,
        BigDecimal totalAmountM,
        BigDecimal totalAmountC,
        BigDecimal avgNightPriceM,
        BigDecimal avgNightPriceC) {
        this.dim = dim;
        this.totalQuantity = totalQuantity;
        this.quantityPercent = quantityPercent;
        this.totalAmount = totalAmount;
        this.amountPercent = amountPercent;
        this.totalAmountM = totalAmountM;
        this.totalAmountC = totalAmountC;
        this.avgNightPriceM = avgNightPriceM;
        this.avgNightPriceC = avgNightPriceC;
    }

    public HotelBehaviorInfo() {
    }

    /**
     * 维度
     */
    @JsonProperty("dim")
    public String dim;

    /**
     * 间夜数
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;

    /**
     * 间夜数占比
     */
    @JsonProperty("quantityPercent")
    public Double quantityPercent;

    /**
     * 消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;

    /**
     * 金额占比
     */
    @JsonProperty("amountPercent")
    public Double amountPercent;

    /**
     * 协议消费金额
     */
    @JsonProperty("totalAmountM")
    public BigDecimal totalAmountM;

    /**
     * 会员消费金额
     */
    @JsonProperty("totalAmountC")
    public BigDecimal totalAmountC;

    /**
     * 协议消费金额
     */
    @JsonProperty("avgNightPriceM")
    public BigDecimal avgNightPriceM;

    /**
     * 会员消费金额
     */
    @JsonProperty("avgNightPriceC")
    public BigDecimal avgNightPriceC;

    /**
     * 维度
     */
    public String getDim() {
        return dim;
    }

    /**
     * 维度
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 间夜数
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 间夜数
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 间夜数占比
     */
    public Double getQuantityPercent() {
        return quantityPercent;
    }

    /**
     * 间夜数占比
     */
    public void setQuantityPercent(final Double quantityPercent) {
        this.quantityPercent = quantityPercent;
    }

    /**
     * 消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 金额占比
     */
    public Double getAmountPercent() {
        return amountPercent;
    }

    /**
     * 金额占比
     */
    public void setAmountPercent(final Double amountPercent) {
        this.amountPercent = amountPercent;
    }

    /**
     * 协议消费金额
     */
    public BigDecimal getTotalAmountM() {
        return totalAmountM;
    }

    /**
     * 协议消费金额
     */
    public void setTotalAmountM(final BigDecimal totalAmountM) {
        this.totalAmountM = totalAmountM;
    }

    /**
     * 会员消费金额
     */
    public BigDecimal getTotalAmountC() {
        return totalAmountC;
    }

    /**
     * 会员消费金额
     */
    public void setTotalAmountC(final BigDecimal totalAmountC) {
        this.totalAmountC = totalAmountC;
    }

    /**
     * 协议消费金额
     */
    public BigDecimal getAvgNightPriceM() {
        return avgNightPriceM;
    }

    /**
     * 协议消费金额
     */
    public void setAvgNightPriceM(final BigDecimal avgNightPriceM) {
        this.avgNightPriceM = avgNightPriceM;
    }

    /**
     * 会员消费金额
     */
    public BigDecimal getAvgNightPriceC() {
        return avgNightPriceC;
    }

    /**
     * 会员消费金额
     */
    public void setAvgNightPriceC(final BigDecimal avgNightPriceC) {
        this.avgNightPriceC = avgNightPriceC;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final HotelBehaviorInfo other = (HotelBehaviorInfo)obj;
        return
            Objects.equal(this.dim, other.dim) &&
            Objects.equal(this.totalQuantity, other.totalQuantity) &&
            Objects.equal(this.quantityPercent, other.quantityPercent) &&
            Objects.equal(this.totalAmount, other.totalAmount) &&
            Objects.equal(this.amountPercent, other.amountPercent) &&
            Objects.equal(this.totalAmountM, other.totalAmountM) &&
            Objects.equal(this.totalAmountC, other.totalAmountC) &&
            Objects.equal(this.avgNightPriceM, other.avgNightPriceM) &&
            Objects.equal(this.avgNightPriceC, other.avgNightPriceC);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.quantityPercent == null ? 0 : this.quantityPercent.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.amountPercent == null ? 0 : this.amountPercent.hashCode());
        result = 31 * result + (this.totalAmountM == null ? 0 : this.totalAmountM.hashCode());
        result = 31 * result + (this.totalAmountC == null ? 0 : this.totalAmountC.hashCode());
        result = 31 * result + (this.avgNightPriceM == null ? 0 : this.avgNightPriceM.hashCode());
        result = 31 * result + (this.avgNightPriceC == null ? 0 : this.avgNightPriceC.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dim", dim)
            .add("totalQuantity", totalQuantity)
            .add("quantityPercent", quantityPercent)
            .add("totalAmount", totalAmount)
            .add("amountPercent", amountPercent)
            .add("totalAmountM", totalAmountM)
            .add("totalAmountC", totalAmountC)
            .add("avgNightPriceM", avgNightPriceM)
            .add("avgNightPriceC", avgNightPriceC)
            .toString();
    }
}
