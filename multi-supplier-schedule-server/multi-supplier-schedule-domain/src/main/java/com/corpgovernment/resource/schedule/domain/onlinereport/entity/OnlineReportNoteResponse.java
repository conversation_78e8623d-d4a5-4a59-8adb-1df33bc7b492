package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "success",
    "fail"
})
public class OnlineReportNoteResponse implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportNoteResponse(
        Integer responseCode,
        String responseDesc,
        Integer success,
        Integer fail) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.success = success;
        this.fail = fail;
        
    }

    public OnlineReportNoteResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    /**
     * 成功数量
     */
    @JsonProperty("success")
    public Integer success;

    /**
     * 失败数量
     */
    @JsonProperty("fail")
    public Integer fail;



    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }

    /**
     * 成功数量
     */
    public Integer getSuccess() {
        return success;
    }

    /**
     * 成功数量
     */
    public void setSuccess(final Integer success) {
        this.success = success;
    }

    /**
     * 失败数量
     */
    public Integer getFail() {
        return fail;
    }

    /**
     * 失败数量
     */
    public void setFail(final Integer fail) {
        this.fail = fail;
    }



    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportNoteResponse other = (OnlineReportNoteResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc) &&
            Objects.equal(this.success, other.success) &&
            Objects.equal(this.fail, other.fail) ;
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.success == null ? 0 : this.success.hashCode());
        result = 31 * result + (this.fail == null ? 0 : this.fail.hashCode());
        

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            .add("success", success)
            .add("fail", fail)
            
            .toString();
    }
}
