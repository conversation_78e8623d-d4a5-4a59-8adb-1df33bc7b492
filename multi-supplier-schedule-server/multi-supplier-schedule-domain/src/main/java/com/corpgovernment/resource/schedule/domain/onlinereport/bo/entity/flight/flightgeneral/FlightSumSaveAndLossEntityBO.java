package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight.flightgeneral;

import org.apache.commons.lang.StringUtils;

public class FlightSumSaveAndLossEntityBO {
    private Integer year;
    private Integer month;
    private String name;
    private Double save;
    private Double loss;
    private Double maxSave;

    public FlightSumSaveAndLossEntityBO(){
        this.year = 1970;
        this.month = 1;
        this.name = StringUtils.EMPTY;
        this.save = 0d;
        this.loss = 0d;
        this.maxSave = 0d;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getSave() {
        return save;
    }

    public void setSave(Double save) {
        this.save = save;
    }

    public Double getLoss() {
        return loss;
    }

    public void setLoss(Double loss) {
        this.loss = loss;
    }

    public Double getMaxSave() {
        return maxSave;
    }

    public void setMaxSave(Double maxSave) {
        this.maxSave = maxSave;
    }
}
