package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
public class CarTopDeptDTO {

    @Column(name = "aggId")
    @Type(value = Types.VARCHAR)
    private String aggId;

    @Column(name = "aggType")
    @Type(value = Types.VARCHAR)
    private String aggType;

    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;

    @Column(name = "totalAmountAirportpickDom")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmountAirportpickDom;

    @Column(name = "totalAmountAirportpickInter")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmountAirportpickInter;

    @Column(name = "totalAmountCharter")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmountCharter;

    @Column(name = "totalAmountRent")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmountRent;

    @Column(name = "totalAmountTax")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmountTax;

    @Column(name = "totalOrderCount")
    @Type(value = Types.INTEGER)
    private Integer totalOrderCount;

    @Column(name = "totalCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCarbons;

    @Column(name = "totalCarbonsTpms")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCarbonsTpms;

    @Column(name = "totalNormalDistance")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalNormalDistance;
}
