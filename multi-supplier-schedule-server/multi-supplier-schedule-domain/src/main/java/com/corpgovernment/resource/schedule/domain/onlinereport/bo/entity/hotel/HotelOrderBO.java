package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.hotel;

/**
 * @Description: 酒店订单详情
 * <AUTHOR>
 * @Date 2019/3/29
 */
public class HotelOrderBO {
    private Long id;
    private Long orderId;
    private Integer printYear;
    private Integer printMonth;
    private Long dateTime;
    private String corpCorporation;
    private Long accountId;
    private String orderType;
    private Long hotel;
    private Long star;
    private String isCU;
    private String cityId;
    private String orderStatus;
    private String clientName;
    private String isOnline;
    private String uid;
    private String userName;
    private String reasonCode;
    private String deadPrice;
    private Integer quantity;
    private String amount;
    private Integer dayNum;
    private Integer persons;
    private String prepayType;
    private String isBreakfast;
    private String dept1;
    private String dept2;
    private String dept3;
    private String dept4;
    private String dept5;
    private String dept6;
    private String dept7;
    private String dept8;
    private String dept9;
    private String dept10;
    private String costCenter1;
    private String costCenter2;
    private String costCenter3;
    private String costCenter4;
    private String costCenter5;
    private String costCenter6;
    private String projectCode;
    private String project;
    private String journeyReasonCode;
    private String journeyReason;
    private String defineValue1;
    private String defineValue2;
    private String journeyNo;
    private String workCity;
    private Long rcTime;
    private String isRefund;
    private String accountCodeName;
    private Long tripId;
    private String lowReasonInfo;
    private String lowReasonInfoEN;
    private String agreementRC;
    private String agreementReasonInfo;
    private String agreementReasonInfoEN;
    private String industryType;
    private Long arrivalDateTime;
    private Long departureDateTime;
    private String isOversea;
    private Long orderDate;
    private String orderRoomNum;
    private String unitPrice;
    private String isMixPayment;
    private Integer roomId;
    private String roomName;
    private String roomNameEN;
    private String basicRoomTypeName;
    private String basicRoomTypeEnName;
    private String payType;
    private String settlementPersonAmt;
    private String settlementACCNTAmt;
    private String minPriceRC;
    private String minPriceRCVV;
    private String lowPriceRCVV;
    private String agreementRCVV;
    /**
     * 商区名称
     */
    private String locationName;
    /**
     * 行政区名称
     */
    private String zoneName;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 城市名称(英文)
     */
    private String cityNameEN;
    /**
     * 酒店名称
     */
    private String hotelName;
    /**
     * 酒店名称(英文)
     */
    private String hotelNameEN;

    /**
     * 国家id
     */
    private Integer countryID;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 省份id
     */
    private Integer provinceID;

    /**
     * 省份名称
     */
    private String provinceName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getPrintYear() {
        return printYear;
    }

    public void setPrintYear(Integer printYear) {
        this.printYear = printYear;
    }

    public Integer getPrintMonth() {
        return printMonth;
    }

    public void setPrintMonth(Integer printMonth) {
        this.printMonth = printMonth;
    }

    public Long getDateTime() {
        return dateTime;
    }

    public void setDateTime(Long dateTime) {
        this.dateTime = dateTime;
    }

    public String getCorpCorporation() {
        return corpCorporation;
    }

    public void setCorpCorporation(String corpCorporation) {
        this.corpCorporation = corpCorporation;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Long getHotel() {
        return hotel;
    }

    public void setHotel(Long hotel) {
        this.hotel = hotel;
    }

    public Long getStar() {
        return star;
    }

    public void setStar(Long star) {
        this.star = star;
    }

    public String getIsCU() {
        return isCU;
    }

    public void setIsCU(String isCU) {
        this.isCU = isCU;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(String isOnline) {
        this.isOnline = isOnline;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getDeadPrice() {
        return deadPrice;
    }

    public void setDeadPrice(String deadPrice) {
        this.deadPrice = deadPrice;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public Integer getDayNum() {
        return dayNum;
    }

    public void setDayNum(Integer dayNum) {
        this.dayNum = dayNum;
    }

    public Integer getPersons() {
        return persons;
    }

    public void setPersons(Integer persons) {
        this.persons = persons;
    }

    public String getPrepayType() {
        return prepayType;
    }

    public void setPrepayType(String prepayType) {
        this.prepayType = prepayType;
    }

    public String getIsBreakfast() {
        return isBreakfast;
    }

    public void setIsBreakfast(String isBreakfast) {
        this.isBreakfast = isBreakfast;
    }

    public String getDept1() {
        return dept1;
    }

    public void setDept1(String dept1) {
        this.dept1 = dept1;
    }

    public String getDept2() {
        return dept2;
    }

    public void setDept2(String dept2) {
        this.dept2 = dept2;
    }

    public String getDept3() {
        return dept3;
    }

    public void setDept3(String dept3) {
        this.dept3 = dept3;
    }

    public String getDept4() {
        return dept4;
    }

    public void setDept4(String dept4) {
        this.dept4 = dept4;
    }

    public String getDept5() {
        return dept5;
    }

    public void setDept5(String dept5) {
        this.dept5 = dept5;
    }

    public String getDept6() {
        return dept6;
    }

    public void setDept6(String dept6) {
        this.dept6 = dept6;
    }

    public String getDept7() {
        return dept7;
    }

    public void setDept7(String dept7) {
        this.dept7 = dept7;
    }

    public String getDept8() {
        return dept8;
    }

    public void setDept8(String dept8) {
        this.dept8 = dept8;
    }

    public String getDept9() {
        return dept9;
    }

    public void setDept9(String dept9) {
        this.dept9 = dept9;
    }

    public String getDept10() {
        return dept10;
    }

    public void setDept10(String dept10) {
        this.dept10 = dept10;
    }

    public String getCostCenter1() {
        return costCenter1;
    }

    public void setCostCenter1(String costCenter1) {
        this.costCenter1 = costCenter1;
    }

    public String getCostCenter2() {
        return costCenter2;
    }

    public void setCostCenter2(String costCenter2) {
        this.costCenter2 = costCenter2;
    }

    public String getCostCenter3() {
        return costCenter3;
    }

    public void setCostCenter3(String costCenter3) {
        this.costCenter3 = costCenter3;
    }

    public String getCostCenter4() {
        return costCenter4;
    }

    public void setCostCenter4(String costCenter4) {
        this.costCenter4 = costCenter4;
    }

    public String getCostCenter5() {
        return costCenter5;
    }

    public void setCostCenter5(String costCenter5) {
        this.costCenter5 = costCenter5;
    }

    public String getCostCenter6() {
        return costCenter6;
    }

    public void setCostCenter6(String costCenter6) {
        this.costCenter6 = costCenter6;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getJourneyReasonCode() {
        return journeyReasonCode;
    }

    public void setJourneyReasonCode(String journeyReasonCode) {
        this.journeyReasonCode = journeyReasonCode;
    }

    public String getJourneyReason() {
        return journeyReason;
    }

    public void setJourneyReason(String journeyReason) {
        this.journeyReason = journeyReason;
    }

    public String getDefineValue1() {
        return defineValue1;
    }

    public void setDefineValue1(String defineValue1) {
        this.defineValue1 = defineValue1;
    }

    public String getDefineValue2() {
        return defineValue2;
    }

    public void setDefineValue2(String defineValue2) {
        this.defineValue2 = defineValue2;
    }

    public String getJourneyNo() {
        return journeyNo;
    }

    public void setJourneyNo(String journeyNo) {
        this.journeyNo = journeyNo;
    }

    public String getWorkCity() {
        return workCity;
    }

    public void setWorkCity(String workCity) {
        this.workCity = workCity;
    }

    public Long getRcTime() {
        return rcTime;
    }

    public void setRcTime(Long rcTime) {
        this.rcTime = rcTime;
    }

    public String getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(String isRefund) {
        this.isRefund = isRefund;
    }

    public String getAccountCodeName() {
        return accountCodeName;
    }

    public void setAccountCodeName(String accountCodeName) {
        this.accountCodeName = accountCodeName;
    }

    public Long getTripId() {
        return tripId;
    }

    public void setTripId(Long tripId) {
        this.tripId = tripId;
    }

    public String getLowReasonInfo() {
        return lowReasonInfo;
    }

    public void setLowReasonInfo(String lowReasonInfo) {
        this.lowReasonInfo = lowReasonInfo;
    }

    public String getLowReasonInfoEN() {
        return lowReasonInfoEN;
    }

    public void setLowReasonInfoEN(String lowReasonInfoEN) {
        this.lowReasonInfoEN = lowReasonInfoEN;
    }

    public String getAgreementRC() {
        return agreementRC;
    }

    public void setAgreementRC(String agreementRC) {
        this.agreementRC = agreementRC;
    }

    public String getAgreementReasonInfo() {
        return agreementReasonInfo;
    }

    public void setAgreementReasonInfo(String agreementReasonInfo) {
        this.agreementReasonInfo = agreementReasonInfo;
    }

    public String getAgreementReasonInfoEN() {
        return agreementReasonInfoEN;
    }

    public void setAgreementReasonInfoEN(String agreementReasonInfoEN) {
        this.agreementReasonInfoEN = agreementReasonInfoEN;
    }

    public String getIndustryType() {
        return industryType;
    }

    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }

    public Long getArrivalDateTime() {
        return arrivalDateTime;
    }

    public void setArrivalDateTime(Long arrivalDateTime) {
        this.arrivalDateTime = arrivalDateTime;
    }

    public Long getDepartureDateTime() {
        return departureDateTime;
    }

    public void setDepartureDateTime(Long departureDateTime) {
        this.departureDateTime = departureDateTime;
    }

    public String getIsOversea() {
        return isOversea;
    }

    public void setIsOversea(String isOversea) {
        this.isOversea = isOversea;
    }

    public Long getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Long orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderRoomNum() {
        return orderRoomNum;
    }

    public void setOrderRoomNum(String orderRoomNum) {
        this.orderRoomNum = orderRoomNum;
    }

    public String getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(String unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getIsMixPayment() {
        return isMixPayment;
    }

    public void setIsMixPayment(String isMixPayment) {
        this.isMixPayment = isMixPayment;
    }

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getRoomNameEN() {
        return roomNameEN;
    }

    public void setRoomNameEN(String roomNameEN) {
        this.roomNameEN = roomNameEN;
    }

    public String getBasicRoomTypeName() {
        return basicRoomTypeName;
    }

    public void setBasicRoomTypeName(String basicRoomTypeName) {
        this.basicRoomTypeName = basicRoomTypeName;
    }

    public String getBasicRoomTypeEnName() {
        return basicRoomTypeEnName;
    }

    public void setBasicRoomTypeEnName(String basicRoomTypeEnName) {
        this.basicRoomTypeEnName = basicRoomTypeEnName;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getSettlementPersonAmt() {
        return settlementPersonAmt;
    }

    public void setSettlementPersonAmt(String settlementPersonAmt) {
        this.settlementPersonAmt = settlementPersonAmt;
    }

    public String getSettlementACCNTAmt() {
        return settlementACCNTAmt;
    }

    public void setSettlementACCNTAmt(String settlementACCNTAmt) {
        this.settlementACCNTAmt = settlementACCNTAmt;
    }

    public String getMinPriceRC() {
        return minPriceRC;
    }

    public void setMinPriceRC(String minPriceRC) {
        this.minPriceRC = minPriceRC;
    }

    public String getMinPriceRCVV() {
        return minPriceRCVV;
    }

    public void setMinPriceRCVV(String minPriceRCVV) {
        this.minPriceRCVV = minPriceRCVV;
    }

    public String getLowPriceRCVV() {
        return lowPriceRCVV;
    }

    public void setLowPriceRCVV(String lowPriceRCVV) {
        this.lowPriceRCVV = lowPriceRCVV;
    }

    public String getAgreementRCVV() {
        return agreementRCVV;
    }

    public void setAgreementRCVV(String agreementRCVV) {
        this.agreementRCVV = agreementRCVV;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityNameEN() {
        return cityNameEN;
    }

    public void setCityNameEN(String cityNameEN) {
        this.cityNameEN = cityNameEN;
    }

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public String getHotelNameEN() {
        return hotelNameEN;
    }

    public void setHotelNameEN(String hotelNameEN) {
        this.hotelNameEN = hotelNameEN;
    }

    public Integer getCountryID() {
        return countryID;
    }

    public void setCountryID(Integer countryID) {
        this.countryID = countryID;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public Integer getProvinceID() {
        return provinceID;
    }

    public void setProvinceID(Integer provinceID) {
        this.provinceID = provinceID;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }
}
