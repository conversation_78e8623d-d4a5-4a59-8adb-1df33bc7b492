package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
public class DeptConsume {

    @Column(name = "aggId")
    @Type(value = Types.VARCHAR)
    private String aggId;

    @Column(name = "aggType")
    @Type(value = Types.VARCHAR)
    private String aggType;

    @Column(name = "realPay")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPay;

    @Column(name = "yoy")
    @Type(value = Types.DECIMAL)
    private BigDecimal yoy;

    @Column(name = "yoyBeforeLast")
    @Type(value = Types.DECIMAL)
    private BigDecimal yoyBeforeLast;

    @Column(name = "mom")
    @Type(value = Types.DECIMAL)
    private BigDecimal mom;

    @Column(name = "realPayPercent")
    @Type(value = Types.DECIMAL)
    private BigDecimal realPayPercent;

    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;
}
