package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import com.corpgovernment.resource.schedule.domain.onlinereport.types.ResponseStatusType;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.Map;

/**
 * 部门uid消费明细
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "responseCode",
        "responseDesc",
        "deptUidConsumeDetailInfo",
        "extData",
        "responseStatus"
})
public class OnlineReportDeptUidConsumeDetailResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;
    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;
    @JsonProperty("deptUidConsumeDetailInfo")
    public OnlineReportDeptUidConsumeDetailInfo deptUidConsumeDetailInfo;
    @JsonProperty("extData")
    public Map<String, String> extData;
    @JsonProperty("ResponseStatus")
    public ResponseStatusType responseStatus;

    public OnlineReportDeptUidConsumeDetailResponse(
            Integer responseCode,
            String responseDesc,
            OnlineReportDeptUidConsumeDetailInfo deptUidConsumeDetailInfo,
            Map<String, String> extData,
            ResponseStatusType responseStatus) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.deptUidConsumeDetailInfo = deptUidConsumeDetailInfo;
        this.extData = extData;
        this.responseStatus = responseStatus;
    }

    public OnlineReportDeptUidConsumeDetailResponse() {
    }

    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }

    public OnlineReportDeptUidConsumeDetailInfo getDeptUidConsumeDetailInfo() {
        return deptUidConsumeDetailInfo;
    }

    public void setDeptUidConsumeDetailInfo(final OnlineReportDeptUidConsumeDetailInfo deptUidConsumeDetailInfo) {
        this.deptUidConsumeDetailInfo = deptUidConsumeDetailInfo;
    }

    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    public ResponseStatusType getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(final ResponseStatusType responseStatus) {
        this.responseStatus = responseStatus;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.responseCode;
            case 1:
                return this.responseDesc;
            case 2:
                return this.deptUidConsumeDetailInfo;
            case 3:
                return this.extData;
            case 4:
                return this.responseStatus;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.responseCode = (Integer) fieldValue;
                break;
            case 1:
                this.responseDesc = (String) fieldValue;
                break;
            case 2:
                this.deptUidConsumeDetailInfo = (OnlineReportDeptUidConsumeDetailInfo) fieldValue;
                break;
            case 3:
                this.extData = (Map<String, String>) fieldValue;
                break;
            case 4:
                this.responseStatus = (ResponseStatusType) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportDeptUidConsumeDetailResponse other = (OnlineReportDeptUidConsumeDetailResponse) obj;
        return
                Objects.equal(this.responseCode, other.responseCode) &&
                        Objects.equal(this.responseDesc, other.responseDesc) &&
                        Objects.equal(this.deptUidConsumeDetailInfo, other.deptUidConsumeDetailInfo) &&
                        Objects.equal(this.extData, other.extData) &&
                        Objects.equal(this.responseStatus, other.responseStatus);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.deptUidConsumeDetailInfo == null ? 0 : this.deptUidConsumeDetailInfo.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());
        result = 31 * result + (this.responseStatus == null ? 0 : this.responseStatus.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("responseCode", responseCode)
                .add("responseDesc", responseDesc)
                .add("deptUidConsumeDetailInfo", deptUidConsumeDetailInfo)
                .add("extData", extData)
                .add("responseStatus", responseStatus)
                .toString();
    }
}
