package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 提前预定明细
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "preOrderDate",
    "potentialSavAmt",
    "quantity",
    "quantityRate",
    "fullFaretkt",
    "netFare",
    "avgDiscount"
})
public class FlightAdvanceBookDaysValue implements Serializable {
    private static final long serialVersionUID = 1L;





    public FlightAdvanceBookDaysValue(
        String preOrderDate,
        String potentialSavAmt,
        Integer quantity,
        String quantityRate,
        String fullFaretkt,
        String netFare,
        String avgDiscount) {
        this.preOrderDate = preOrderDate;
        this.potentialSavAmt = potentialSavAmt;
        this.quantity = quantity;
        this.quantityRate = quantityRate;
        this.fullFaretkt = fullFaretkt;
        this.netFare = netFare;
        this.avgDiscount = avgDiscount;
    }

    public FlightAdvanceBookDaysValue() {
    }

    /**
     * 提前预定天数
     */
    @JsonProperty("preOrderDate")
    public String preOrderDate;

    /**
     * 潜在节省金额
     */
    @JsonProperty("potentialSavAmt")
    public String potentialSavAmt;

    /**
     * 票张数
     */
    @JsonProperty("quantity")
    public Integer quantity;

    /**
     * 票张数占比
     */
    @JsonProperty("quantityRate")
    public String quantityRate;

    /**
     * 全价票张数
     */
    @JsonProperty("fullFaretkt")
    public String fullFaretkt;

    /**
     * 成交净价
     */
    @JsonProperty("netFare")
    public String netFare;

    /**
     * 平均折扣
     */
    @JsonProperty("avgDiscount")
    public String avgDiscount;

    /**
     * 提前预定天数
     */
    public String getPreOrderDate() {
        return preOrderDate;
    }

    /**
     * 提前预定天数
     */
    public void setPreOrderDate(final String preOrderDate) {
        this.preOrderDate = preOrderDate;
    }

    /**
     * 潜在节省金额
     */
    public String getPotentialSavAmt() {
        return potentialSavAmt;
    }

    /**
     * 潜在节省金额
     */
    public void setPotentialSavAmt(final String potentialSavAmt) {
        this.potentialSavAmt = potentialSavAmt;
    }

    /**
     * 票张数
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 票张数
     */
    public void setQuantity(final Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * 票张数占比
     */
    public String getQuantityRate() {
        return quantityRate;
    }

    /**
     * 票张数占比
     */
    public void setQuantityRate(final String quantityRate) {
        this.quantityRate = quantityRate;
    }

    /**
     * 全价票张数
     */
    public String getFullFaretkt() {
        return fullFaretkt;
    }

    /**
     * 全价票张数
     */
    public void setFullFaretkt(final String fullFaretkt) {
        this.fullFaretkt = fullFaretkt;
    }

    /**
     * 成交净价
     */
    public String getNetFare() {
        return netFare;
    }

    /**
     * 成交净价
     */
    public void setNetFare(final String netFare) {
        this.netFare = netFare;
    }

    /**
     * 平均折扣
     */
    public String getAvgDiscount() {
        return avgDiscount;
    }

    /**
     * 平均折扣
     */
    public void setAvgDiscount(final String avgDiscount) {
        this.avgDiscount = avgDiscount;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightAdvanceBookDaysValue other = (FlightAdvanceBookDaysValue)obj;
        return
            Objects.equal(this.preOrderDate, other.preOrderDate) &&
            Objects.equal(this.potentialSavAmt, other.potentialSavAmt) &&
            Objects.equal(this.quantity, other.quantity) &&
            Objects.equal(this.quantityRate, other.quantityRate) &&
            Objects.equal(this.fullFaretkt, other.fullFaretkt) &&
            Objects.equal(this.netFare, other.netFare) &&
            Objects.equal(this.avgDiscount, other.avgDiscount);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.preOrderDate == null ? 0 : this.preOrderDate.hashCode());
        result = 31 * result + (this.potentialSavAmt == null ? 0 : this.potentialSavAmt.hashCode());
        result = 31 * result + (this.quantity == null ? 0 : this.quantity.hashCode());
        result = 31 * result + (this.quantityRate == null ? 0 : this.quantityRate.hashCode());
        result = 31 * result + (this.fullFaretkt == null ? 0 : this.fullFaretkt.hashCode());
        result = 31 * result + (this.netFare == null ? 0 : this.netFare.hashCode());
        result = 31 * result + (this.avgDiscount == null ? 0 : this.avgDiscount.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("preOrderDate", preOrderDate)
            .add("potentialSavAmt", potentialSavAmt)
            .add("quantity", quantity)
            .add("quantityRate", quantityRate)
            .add("fullFaretkt", fullFaretkt)
            .add("netFare", netFare)
            .add("avgDiscount", avgDiscount)
            .toString();
    }
}
