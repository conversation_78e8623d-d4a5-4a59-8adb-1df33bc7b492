package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

import java.util.List;
import java.util.Map;

/**
 * @Description: 机票损失明细
 * <AUTHOR>
 * @Date 2019/5/27
 */
public class FlightDeptLossDetailBO {
      private List<FlightLossDetailBO> sumList;
      private Map<String,FlightDeptListBO> deptMap;

    public List<FlightLossDetailBO> getSumList() {
        return sumList;
    }

    public void setSumList(List<FlightLossDetailBO> sumList) {
        this.sumList = sumList;
    }

    public Map<String, FlightDeptListBO> getDeptMap() {
        return deptMap;
    }

    public void setDeptMap(Map<String, FlightDeptListBO> deptMap) {
        this.deptMap = deptMap;
    }
}

