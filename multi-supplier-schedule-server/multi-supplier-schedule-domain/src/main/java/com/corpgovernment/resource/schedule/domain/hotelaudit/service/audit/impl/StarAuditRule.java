package com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.impl;

import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.HotelAuditItem;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.HotelAuditResult;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.OrderInfo;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.TravelStandardItem;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.HotelAuditRuleEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelModeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.IHotelAuditDomainService;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.IHotelAuditRule;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @create 2025-01-23 14:29
 */
@Slf4j
@Service
public class StarAuditRule implements IHotelAuditRule {
    
    @Resource
    private IHotelAuditDomainService hotelAuditDomainService;
    
    @Override
    public HotelAuditResult audit(HotelAuditItem hotelAuditItem) {
        Boolean overLimit = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getOrderInfo)
                .map(OrderInfo::getOverLimit)
                .orElse(null);
        Integer hotelStar = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getOrderInfo)
                .map(OrderInfo::getStar)
                .orElse(null);
        Boolean hotelIsStarLicence = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getOrderInfo)
                .map(OrderInfo::getIsStarLicence)
                .orElse(null);
        List<TravelStandardItem> usedTravelStandardItemList = hotelAuditDomainService.getUsedTravelStandardItemList(hotelAuditItem);
        
        // 订单酒店星级数据
        if (hotelStar == null || hotelIsStarLicence == null) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                    .auditDescList(Collections.singletonList("订单无酒店星级数据"))
                    .auditPass(false)
                    .build();
        }
        
        // 钻级不管控
        if (Boolean.FALSE.equals(hotelIsStarLicence) || CollectionUtils.isEmpty(usedTravelStandardItemList)) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                    .auditDescList(null)
                    .auditPass(true)
                    .build();
        }
        
        for (TravelStandardItem travelStandardItem : usedTravelStandardItemList) {
            List<Integer> starList = Optional.ofNullable(travelStandardItem)
                    .map(TravelStandardItem::getStarList)
                    .orElse(null);
            // 空为不限
            if (CollectionUtils.isNotEmpty(starList)) {
                // 审计超标但是订单没超标
                if (!starList.contains(hotelStar) && Boolean.FALSE.equals(overLimit)) {
                    return HotelAuditResult.builder()
                            .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                            .auditDescList(Collections.singletonList("审计超标但订单没有超标"))
                            .auditPass(false)
                            .build();
                }
            }
        }
        
        return HotelAuditResult.builder()
                .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                .auditDescList(null)
                .auditPass(true)
                .build();
    }
    
    @Override
    public HotelAuditRuleEnum getHotelAuditRuleEnum() {
        return HotelAuditRuleEnum.STAR;
    }
    
    @Override
    public List<TravelModeEnum> getSupportTravelModeEnumList() {
        return Collections.singletonList(TravelModeEnum.PUB);
    }
}
