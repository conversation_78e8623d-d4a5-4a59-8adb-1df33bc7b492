package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.budget;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class AvgPricePredictDTO {

    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;

    @Column(name = "forward_3m")
    @Type(value = Types.DECIMAL)
    private BigDecimal forward3m;

    @Column(name = "forward_2m")
    @Type(value = Types.DECIMAL)
    private BigDecimal forward2m;

    @Column(name = "forward_1m")
    @Type(value = Types.DECIMAL)
    private BigDecimal forward1m;

    @Column(name = "current_m")
    @Type(value = Types.DECIMAL)
    private BigDecimal currentm;

    @Column(name = "next_m")
    @Type(value = Types.DECIMAL)
    private BigDecimal nextm;

    @Column(name = "next_next_m")
    @Type(value = Types.DECIMAL)
    private BigDecimal nextNextm;

    @Column(name = "rio_recent_3m")
    @Type(value = Types.DECIMAL)
    private BigDecimal rio_recent_3m;

    @Column(name = "corpCount")
    @Type(value = Types.INTEGER)
    private Integer corpCount;
}
