package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportRiskSceneTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.List;

/**
 * 风险订单明细-request
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "orderIds",
        "uid",
        "riskOrderDetailSearch",
        "lang",
        "sortKey",
        "asc",
        "queryBu",
        "scene",
        "basecondition",
        "page",
        "operatorStatus",
        "riskReasons"
})
public class OnlineReportRiskOrderDetailRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    public OnlineReportRiskOrderDetailRequest(
            List<String> orderIds,
            List<String> uid,
            List<String> riskOrderDetailSearch,
            String lang,
            String sortKey,
            Boolean asc,
            QueryReportBuTypeEnum queryBu,
            QueryReportRiskSceneTypeEnum scene,
            BaseQueryCondition basecondition,
            Pager page,
            List<Integer> operatorStatus,
            List<String> riskReasons) {
        this.orderIds = orderIds;
        this.uid = uid;
        this.riskOrderDetailSearch = riskOrderDetailSearch;
        this.lang = lang;
        this.sortKey = sortKey;
        this.asc = asc;
        this.queryBu = queryBu;
        this.scene = scene;
        this.basecondition = basecondition;
        this.page = page;
        this.operatorStatus = operatorStatus;
        this.riskReasons = riskReasons;
    }

    public OnlineReportRiskOrderDetailRequest() {
    }

    /**
     * 筛选的订单
     */
    @JsonProperty("orderIds")
    public List<String> orderIds;

    /**
     * 筛选的用户id
     */
    @JsonProperty("uid")
    public List<String> uid;

    /**
     * app搜索不区分订单用户
     */
    @JsonProperty("riskOrderDetailSearch")
    public List<String> riskOrderDetailSearch;

    /**
     * 语言
     */
    @JsonProperty("lang")
    public String lang;

    /**
     * 排序key
     */
    @JsonProperty("sortKey")
    public String sortKey;

    /**
     * 升序
     */
    @JsonProperty("asc")
    public Boolean asc;

    /**
     * 机票-酒店 风险订单明细
     */
    @JsonProperty("queryBu")
    public QueryReportBuTypeEnum queryBu;

    /**
     * 查询场景
     */
    @JsonProperty("scene")
    public QueryReportRiskSceneTypeEnum scene;

    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    /**
     * page翻页
     */
    @JsonProperty("page")
    public Pager page;

    /**
     * 查询的操作状态
     */
    @JsonProperty("operatorStatus")
    public List<Integer> operatorStatus;

    /**
     * 风险场景命中原因
     */
    @JsonProperty("riskReasons")
    public List<String> riskReasons;

    /**
     * 筛选的订单
     */
    public List<String> getOrderIds() {
        return orderIds;
    }

    /**
     * 筛选的订单
     */
    public void setOrderIds(final List<String> orderIds) {
        this.orderIds = orderIds;
    }

    /**
     * 筛选的用户id
     */
    public List<String> getUid() {
        return uid;
    }

    /**
     * 筛选的用户id
     */
    public void setUid(final List<String> uid) {
        this.uid = uid;
    }

    /**
     * app搜索不区分订单用户
     */
    public List<String> getRiskOrderDetailSearch() {
        return riskOrderDetailSearch;
    }

    /**
     * app搜索不区分订单用户
     */
    public void setRiskOrderDetailSearch(final List<String> riskOrderDetailSearch) {
        this.riskOrderDetailSearch = riskOrderDetailSearch;
    }

    /**
     * 语言
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }

    /**
     * 排序key
     */
    public String getSortKey() {
        return sortKey;
    }

    /**
     * 排序key
     */
    public void setSortKey(final String sortKey) {
        this.sortKey = sortKey;
    }

    /**
     * 升序
     */
    public Boolean isAsc() {
        return asc;
    }

    /**
     * 升序
     */
    public void setAsc(final Boolean asc) {
        this.asc = asc;
    }

    /**
     * 机票-酒店 风险订单明细
     */
    public QueryReportBuTypeEnum getQueryBu() {
        return queryBu;
    }

    /**
     * 机票-酒店 风险订单明细
     */
    public void setQueryBu(final QueryReportBuTypeEnum queryBu) {
        this.queryBu = queryBu;
    }

    /**
     * 查询场景
     */
    public QueryReportRiskSceneTypeEnum getScene() {
        return scene;
    }

    /**
     * 查询场景
     */
    public void setScene(final QueryReportRiskSceneTypeEnum scene) {
        this.scene = scene;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * page翻页
     */
    public Pager getPage() {
        return page;
    }

    /**
     * page翻页
     */
    public void setPage(final Pager page) {
        this.page = page;
    }

    /**
     * 查询的操作状态
     */
    public List<Integer> getOperatorStatus() {
        return operatorStatus;
    }

    /**
     * 查询的操作状态
     */
    public void setOperatorStatus(final List<Integer> operatorStatus) {
        this.operatorStatus = operatorStatus;
    }

    /**
     * 风险场景命中原因
     */
    public List<String> getRiskReasons() {
        return riskReasons;
    }

    /**
     * 风险场景命中原因
     */
    public void setRiskReasons(final List<String> riskReasons) {
        this.riskReasons = riskReasons;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.orderIds = (List<String>) fieldValue;
                break;
            case 1:
                this.uid = (List<String>) fieldValue;
                break;
            case 2:
                this.riskOrderDetailSearch = (List<String>) fieldValue;
                break;
            case 3:
                this.lang = (String) fieldValue;
                break;
            case 4:
                this.sortKey = (String) fieldValue;
                break;
            case 5:
                this.asc = (Boolean) fieldValue;
                break;
            case 6:
                this.queryBu = (QueryReportBuTypeEnum) fieldValue;
                break;
            case 7:
                this.scene = (QueryReportRiskSceneTypeEnum) fieldValue;
                break;
            case 8:
                this.basecondition = (BaseQueryCondition) fieldValue;
                break;
            case 9:
                this.page = (Pager) fieldValue;
                break;
            case 10:
                this.operatorStatus = (List<Integer>) fieldValue;
                break;
            case 11:
                this.riskReasons = (List<String>) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportRiskOrderDetailRequest other = (OnlineReportRiskOrderDetailRequest) obj;
        return
                Objects.equal(this.orderIds, other.orderIds) &&
                        Objects.equal(this.uid, other.uid) &&
                        Objects.equal(this.riskOrderDetailSearch, other.riskOrderDetailSearch) &&
                        Objects.equal(this.lang, other.lang) &&
                        Objects.equal(this.sortKey, other.sortKey) &&
                        Objects.equal(this.asc, other.asc) &&
                        Objects.equal(this.queryBu, other.queryBu) &&
                        Objects.equal(this.scene, other.scene) &&
                        Objects.equal(this.basecondition, other.basecondition) &&
                        Objects.equal(this.page, other.page) &&
                        Objects.equal(this.operatorStatus, other.operatorStatus) &&
                        Objects.equal(this.riskReasons, other.riskReasons);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.orderIds == null ? 0 : this.orderIds.hashCode());
        result = 31 * result + (this.uid == null ? 0 : this.uid.hashCode());
        result = 31 * result + (this.riskOrderDetailSearch == null ? 0 : this.riskOrderDetailSearch.hashCode());
        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.sortKey == null ? 0 : this.sortKey.hashCode());
        result = 31 * result + (this.asc == null ? 0 : this.asc.hashCode());
        result = 31 * result + (this.queryBu == null ? 0 : this.queryBu.hashCode());
        result = 31 * result + (this.scene == null ? 0 : this.scene.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.page == null ? 0 : this.page.hashCode());
        result = 31 * result + (this.operatorStatus == null ? 0 : this.operatorStatus.hashCode());
        result = 31 * result + (this.riskReasons == null ? 0 : this.riskReasons.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("orderIds", orderIds)
                .add("uid", uid)
                .add("riskOrderDetailSearch", riskOrderDetailSearch)
                .add("lang", lang)
                .add("sortKey", sortKey)
                .add("asc", asc)
                .add("queryBu", queryBu)
                .add("scene", scene)
                .add("basecondition", basecondition)
                .add("page", page)
                .add("operatorStatus", operatorStatus)
                .add("riskReasons", riskReasons)
                .toString();
    }
}
