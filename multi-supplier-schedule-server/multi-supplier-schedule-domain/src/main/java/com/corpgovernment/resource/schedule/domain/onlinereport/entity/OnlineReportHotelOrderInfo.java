package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@Data
public class OnlineReportHotelOrderInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("orderId")
    public Long orderId;

    @JsonProperty("orderStatus")
    public String orderStatus;

    @JsonProperty("orderDate")
    public String orderDate;

    @JsonProperty("corpCorporation")
    public String corpCorporation;

    @JsonProperty("corpName")
    public String corpName;

    @JsonProperty("companygroupid")
    public String companygroupid;

    @JsonProperty("companygroup")
    public String companygroup;

    @JsonProperty("accountId")
    public Long accountId;

    @JsonProperty("accountCode")
    public String accountCode;

    @JsonProperty("accountName")
    public String accountName;

    @JsonProperty("subAccountId")
    public Long subAccountId;

    @JsonProperty("subAccountCode")
    public String subAccountCode;

    @JsonProperty("subAccountName")
    public String subAccountName;

    @JsonProperty("industryType")
    public String industryType;

    @JsonProperty("industryTypeName")
    public String industryTypeName;

    @JsonProperty("uid")
    public String uid;

    @JsonProperty("userName")
    public String userName;

    @JsonProperty("employeId")
    public String employeId;

    @JsonProperty("workCity")
    public String workCity;

    @JsonProperty("rankName")
    public String rankName;

    @JsonProperty("costCenter1")
    public String costCenter1;

    @JsonProperty("costCenter2")
    public String costCenter2;

    @JsonProperty("costCenter3")
    public String costCenter3;

    @JsonProperty("costCenter4")
    public String costCenter4;

    @JsonProperty("costCenter5")
    public String costCenter5;

    @JsonProperty("costCenter6")
    public String costCenter6;

    @JsonProperty("dept1")
    public String dept1;

    @JsonProperty("dept2")
    public String dept2;

    @JsonProperty("dept3")
    public String dept3;

    @JsonProperty("dept4")
    public String dept4;

    @JsonProperty("dept5")
    public String dept5;

    @JsonProperty("dept6")
    public String dept6;

    @JsonProperty("dept7")
    public String dept7;

    @JsonProperty("dept8")
    public String dept8;

    @JsonProperty("dept9")
    public String dept9;

    @JsonProperty("dept10")
    public String dept10;

    @JsonProperty("feeType")
    public String feeType;

    @JsonProperty("isOnline")
    public String isOnline;

    @JsonProperty("prepayType")
    public String prepayType;

    @JsonProperty("acbPrepayType")
    public String acbPrepayType;

    @JsonProperty("bosstype")
    public String bosstype;

    @JsonProperty("tripId")
    public Long tripId;

    @JsonProperty("journeyNo")
    public String journeyNo;

    @JsonProperty("journeyReason")
    public String journeyReason;

    @JsonProperty("journeyReasonCode")
    public String journeyReasonCode;

    @JsonProperty("projectCode")
    public String projectCode;

    @JsonProperty("project")
    public String project;

    @JsonProperty("verbalAuthorize")
    public String verbalAuthorize;

    @JsonProperty("confirmPerson")
    public String confirmPerson;

    @JsonProperty("confirmType")
    public String confirmType;

    @JsonProperty("confirmPerson2")
    public String confirmPerson2;

    @JsonProperty("confirmType2")
    public String confirmType2;

    @JsonProperty("approvalpasstime")
    public String approvalpasstime;

    @JsonProperty("actionname")
    public String actionname;

    @JsonProperty("defineflag")
    public String defineflag;

    @JsonProperty("defineflag2")
    public String defineflag2;

    @JsonProperty("confirmtimepoint")
    public String confirmtimepoint;

    @JsonProperty("confirmtimepoint2")
    public String confirmtimepoint2;

    @JsonProperty("auditorid")
    public String auditorid;

    @JsonProperty("auditorid2")
    public String auditorid2;

    @JsonProperty("dealDate")
    public String dealDate;

    @JsonProperty("groupMonth")
    public Integer groupMonth;

    @JsonProperty("printMonth")
    public Integer printMonth;

    @JsonProperty("printYear")
    public Integer printYear;

    @JsonProperty("deadPrice")
    public BigDecimal deadPrice;

    @JsonProperty("settlementAccntAmt")
    public BigDecimal settlementAccntAmt;

    @JsonProperty("settlementPersonAmt")
    public BigDecimal settlementPersonAmt;

    @JsonProperty("hotel")
    public Long hotel;

    @JsonProperty("hotelName")
    public String hotelName;

    @JsonProperty("hotelGroupName")
    public String hotelGroupName;

    @JsonProperty("hotelBrandName")
    public String hotelBrandName;

    @JsonProperty("isOversea")
    public String isOversea;

    @JsonProperty("star")
    public String star;

    @JsonProperty("location")
    public String location;

    @JsonProperty("zone")
    public String zone;

    @JsonProperty("city")
    public Integer city;

    @JsonProperty("cityLevel")
    public String cityLevel;

    @JsonProperty("cityName")
    public String cityName;

    @JsonProperty("provinceName")
    public String provinceName;

    @JsonProperty("countryName")
    public String countryName;

    @JsonProperty("clientName")
    public String clientName;

    @JsonProperty("orderType")
    public String orderType;

    @JsonProperty("quantity")
    public Integer quantity;

    @JsonProperty("orderRoomNum")
    public Integer orderRoomNum;

    @JsonProperty("persons")
    public Integer persons;

    @JsonProperty("dayNum")
    public Integer dayNum;

    @JsonProperty("arrivalDateTime")
    public String arrivalDateTime;

    @JsonProperty("departureDateTime")
    public String departureDateTime;

    @JsonProperty("isBreakfast")
    public String isBreakfast;

    @JsonProperty("addBreakfast")
    public String addBreakfast;

    @JsonProperty("realPay")
    public BigDecimal realPay;

    @JsonProperty("addBreakfastPrice")
    public BigDecimal addBreakfastPrice;

    @JsonProperty("postAmount")
    public BigDecimal postAmount;

    @JsonProperty("serviceFee")
    public BigDecimal serviceFee;

    @JsonProperty("isRefund")
    public String isRefund;

    @JsonProperty("refundId")
    public String refundId;

    @JsonProperty("refundTime")
    public String refundTime;

    @JsonProperty("rfdAmount")
    public BigDecimal rfdAmount;

    @JsonProperty("rfdQuantity")
    public Integer rfdQuantity;

    @JsonProperty("gdsOrder")
    public String gdsOrder;

    @JsonProperty("isGdsOrder")
    public String isGdsOrder;

    @JsonProperty("isCu")
    public String isCu;

    @JsonProperty("isMixPayment")
    public String isMixPayment;

    @JsonProperty("isTmcpEnjoy")
    public String isTmcpEnjoy;

    @JsonProperty("basicRoomTypeName")
    public String basicRoomTypeName;

    @JsonProperty("roomId")
    public String roomId;

    @JsonProperty("roomName")
    public String roomName;

    @JsonProperty("isRc")
    public String isRc;

    @JsonProperty("reasonCode")
    public String reasonCode;

    @JsonProperty("lowReasoninfo")
    public String lowReasoninfo;

    @JsonProperty("lowPriceEnVv")
    public String lowPriceEnVv;

    @JsonProperty("minPriceRc")
    public String minPriceRc;

    @JsonProperty("minPriceRcVv")
    public String minPriceRcVv;

    @JsonProperty("agreementRc")
    public String agreementRc;

    @JsonProperty("agreementRcVv")
    public String agreementRcVv;

    @JsonProperty("agreementReasoninfo")
    public String agreementReasoninfo;

    @JsonProperty("userdefinedRid")
    public String userdefinedRid;

    @JsonProperty("userdefinedRc")
    public String userdefinedRc;

    @JsonProperty("masterhotelid")
    public Integer masterhotelid;

    @JsonProperty("breakfast")
    public Long breakfast;

    @JsonProperty("oCurrency")
    public String oCurrency;

    @JsonProperty("oExchangerate")
    public BigDecimal oExchangerate;

    @JsonProperty("pcitylevel")
    public String pcitylevel;

    @JsonProperty("hotelNameEn")
    public String hotelNameEn;

    @JsonProperty("hotelGroupNameEn")
    public String hotelGroupNameEn;

    @JsonProperty("cityNameEn")
    public String cityNameEn;

    @JsonProperty("provinceNameEn")
    public String provinceNameEn;

    @JsonProperty("countryNameEn")
    public String countryNameEn;

    @JsonProperty("lowReasoninfoEn")
    public String lowReasoninfoEn;

    @JsonProperty("agreementReasoninfoEn")
    public String agreementReasoninfoEn;

    @JsonProperty("postservicefee")
    public BigDecimal postservicefee;

    @JsonProperty("balancetype")
    public String balancetype;

    @JsonProperty("balancetypename")
    public String balancetypename;

    @JsonProperty("starTrue")
    public String starTrue;

    @JsonProperty("roomPrice")
    public BigDecimal roomPrice;

    @JsonProperty("couponamount")
    public BigDecimal couponamount;

    @JsonProperty("serverfromtype")
    public String serverfromtype;

    @JsonProperty("zipcode")
    public String zipcode;

    @JsonProperty("address")
    public String address;

    @JsonProperty("isSameday")
    public String isSameday;

    @JsonProperty("isWorktime")
    public String isWorktime;

    @JsonProperty("avgprice")
    public BigDecimal avgprice;

    @JsonProperty("corpRealPay")
    public BigDecimal corpRealPay;

    @JsonProperty("minPriceRcReasoninfo")
    public String minPriceRcReasoninfo;

    @JsonProperty("minPriceRcReasoninfoEn")
    public String minPriceRcReasoninfoEn;

    @JsonProperty("deadPriceOnenight")
    public BigDecimal deadPriceOnenight;

    @JsonProperty("hotelpassengerid")
    public String hotelpassengerid;

    @JsonProperty("doublebookedorderid")
    public String doublebookedorderid;

    @JsonProperty("doublebookedrc")
    public String doublebookedrc;

    @JsonProperty("doublebookedrcdetail")
    public String doublebookedrcdetail;

    @JsonProperty("doublebookedrcreason")
    public String doublebookedrcreason;

    @JsonProperty("saveAmount3c")
    public BigDecimal saveAmount3c;

    @JsonProperty("saveAmountPromotion")
    public BigDecimal saveAmountPromotion;

    @JsonProperty("saveAmountPremium")
    public BigDecimal saveAmountPremium;

    @JsonProperty("realPayWithService")
    public BigDecimal realPayWithService;

    @JsonProperty("customereval")
    public BigDecimal customereval;

    @JsonProperty("controlSave")
    public BigDecimal controlSave;

    @JsonProperty("originRoomDistributionPrices")
    public String originRoomDistributionPrices;

    @JsonProperty("stdIndustry1")
    public String stdIndustry1;

    @JsonProperty("stdIndustry2")
    public String stdIndustry2;

    @JsonProperty("houseshareModeType")
    public String houseshareModeType;

    @JsonProperty("allocationMode")
    public String allocationMode;

    @JsonProperty("allocationModeDesc")
    public String allocationModeDesc;

    @JsonProperty("accntAmt")
    public BigDecimal accntAmt;

    @JsonProperty("personAmt")
    public BigDecimal personAmt;
    // 取消原因
    private String cancelreason;

    private BigDecimal cancelEstiSaveAmount;
}
