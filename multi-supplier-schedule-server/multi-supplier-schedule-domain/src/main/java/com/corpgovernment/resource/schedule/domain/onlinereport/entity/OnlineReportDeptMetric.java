package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 部门指标
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dim",
    "metric",
    "loss"
})
public class OnlineReportDeptMetric implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportDeptMetric(
        String dim,
        Integer metric,
        BigDecimal loss) {
        this.dim = dim;
        this.metric = metric;
        this.loss = loss;
    }

    public OnlineReportDeptMetric() {
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    @JsonProperty("dim")
    public String dim;

    /**
     * 指标值
     */
    @JsonProperty("metric")
    public Integer metric;

    /**
     * 损失
     */
    @JsonProperty("loss")
    public BigDecimal loss;

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public String getDim() {
        return dim;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 指标值
     */
    public Integer getMetric() {
        return metric;
    }

    /**
     * 指标值
     */
    public void setMetric(final Integer metric) {
        this.metric = metric;
    }

    /**
     * 损失
     */
    public BigDecimal getLoss() {
        return loss;
    }

    /**
     * 损失
     */
    public void setLoss(final BigDecimal loss) {
        this.loss = loss;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportDeptMetric other = (OnlineReportDeptMetric)obj;
        return
            Objects.equal(this.dim, other.dim) &&
            Objects.equal(this.metric, other.metric) &&
            Objects.equal(this.loss, other.loss);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.metric == null ? 0 : this.metric.hashCode());
        result = 31 * result + (this.loss == null ? 0 : this.loss.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dim", dim)
            .add("metric", metric)
            .add("loss", loss)
            .toString();
    }
}
