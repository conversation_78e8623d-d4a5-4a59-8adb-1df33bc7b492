package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 城市事件
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "provinceId",
    "provinceName",
    "cityId",
    "cityName",
    "eventId",
    "eventTime",
    "eventName",
    "eventContent",
    "hCount",
    "mCount",
    "wCount"
})
public class EventCityInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public EventCityInfo(
        Integer provinceId,
        String provinceName,
        Integer cityId,
        String cityName,
        Long eventId,
        String eventTime,
        String eventName,
        String eventContent,
        Integer hCount,
        Integer mCount,
        Integer wCount) {
        this.provinceId = provinceId;
        this.provinceName = provinceName;
        this.cityId = cityId;
        this.cityName = cityName;
        this.eventId = eventId;
        this.eventTime = eventTime;
        this.eventName = eventName;
        this.eventContent = eventContent;
        this.hCount = hCount;
        this.mCount = mCount;
        this.wCount = wCount;
    }

    public EventCityInfo() {
    }

    /**
     * 省份ID
     */
    @JsonProperty("provinceId")
    public Integer provinceId;

    /**
     * 省份名称
     */
    @JsonProperty("provinceName")
    public String provinceName;

    /**
     * 城市ID
     */
    @JsonProperty("cityId")
    public Integer cityId;

    /**
     * 城市名称
     */
    @JsonProperty("cityName")
    public String cityName;

    /**
     * 事件ID
     */
    @JsonProperty("eventId")
    public Long eventId;

    /**
     * 时间
     */
    @JsonProperty("eventTime")
    public String eventTime;

    /**
     * 事件名称
     */
    @JsonProperty("eventName")
    public String eventName;

    /**
     * 事件名称
     */
    @JsonProperty("eventContent")
    public String eventContent;

    /**
     * 可能到过
     */
    @JsonProperty("hCount")
    public Integer hCount;

    /**
     * 正在出差
     */
    @JsonProperty("mCount")
    public Integer mCount;

    /**
     * 将要去
     */
    @JsonProperty("wCount")
    public Integer wCount;

    /**
     * 省份ID
     */
    public Integer getProvinceId() {
        return provinceId;
    }

    /**
     * 省份ID
     */
    public void setProvinceId(final Integer provinceId) {
        this.provinceId = provinceId;
    }

    /**
     * 省份名称
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     * 省份名称
     */
    public void setProvinceName(final String provinceName) {
        this.provinceName = provinceName;
    }

    /**
     * 城市ID
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 城市ID
     */
    public void setCityId(final Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 城市名称
     */
    public String getCityName() {
        return cityName;
    }

    /**
     * 城市名称
     */
    public void setCityName(final String cityName) {
        this.cityName = cityName;
    }

    /**
     * 事件ID
     */
    public Long getEventId() {
        return eventId;
    }

    /**
     * 事件ID
     */
    public void setEventId(final Long eventId) {
        this.eventId = eventId;
    }

    /**
     * 时间
     */
    public String getEventTime() {
        return eventTime;
    }

    /**
     * 时间
     */
    public void setEventTime(final String eventTime) {
        this.eventTime = eventTime;
    }

    /**
     * 事件名称
     */
    public String getEventName() {
        return eventName;
    }

    /**
     * 事件名称
     */
    public void setEventName(final String eventName) {
        this.eventName = eventName;
    }

    /**
     * 事件名称
     */
    public String getEventContent() {
        return eventContent;
    }

    /**
     * 事件名称
     */
    public void setEventContent(final String eventContent) {
        this.eventContent = eventContent;
    }

    /**
     * 可能到过
     */
    public Integer getHCount() {
        return hCount;
    }

    /**
     * 可能到过
     */
    public void setHCount(final Integer hCount) {
        this.hCount = hCount;
    }

    /**
     * 正在出差
     */
    public Integer getMCount() {
        return mCount;
    }

    /**
     * 正在出差
     */
    public void setMCount(final Integer mCount) {
        this.mCount = mCount;
    }

    /**
     * 将要去
     */
    public Integer getWCount() {
        return wCount;
    }

    /**
     * 将要去
     */
    public void setWCount(final Integer wCount) {
        this.wCount = wCount;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final EventCityInfo other = (EventCityInfo)obj;
        return
            Objects.equal(this.provinceId, other.provinceId) &&
            Objects.equal(this.provinceName, other.provinceName) &&
            Objects.equal(this.cityId, other.cityId) &&
            Objects.equal(this.cityName, other.cityName) &&
            Objects.equal(this.eventId, other.eventId) &&
            Objects.equal(this.eventTime, other.eventTime) &&
            Objects.equal(this.eventName, other.eventName) &&
            Objects.equal(this.eventContent, other.eventContent) &&
            Objects.equal(this.hCount, other.hCount) &&
            Objects.equal(this.mCount, other.mCount) &&
            Objects.equal(this.wCount, other.wCount);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.provinceId == null ? 0 : this.provinceId.hashCode());
        result = 31 * result + (this.provinceName == null ? 0 : this.provinceName.hashCode());
        result = 31 * result + (this.cityId == null ? 0 : this.cityId.hashCode());
        result = 31 * result + (this.cityName == null ? 0 : this.cityName.hashCode());
        result = 31 * result + (this.eventId == null ? 0 : this.eventId.hashCode());
        result = 31 * result + (this.eventTime == null ? 0 : this.eventTime.hashCode());
        result = 31 * result + (this.eventName == null ? 0 : this.eventName.hashCode());
        result = 31 * result + (this.eventContent == null ? 0 : this.eventContent.hashCode());
        result = 31 * result + (this.hCount == null ? 0 : this.hCount.hashCode());
        result = 31 * result + (this.mCount == null ? 0 : this.mCount.hashCode());
        result = 31 * result + (this.wCount == null ? 0 : this.wCount.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("provinceId", provinceId)
            .add("provinceName", provinceName)
            .add("cityId", cityId)
            .add("cityName", cityName)
            .add("eventId", eventId)
            .add("eventTime", eventTime)
            .add("eventName", eventName)
            .add("eventContent", eventContent)
            .add("hCount", hCount)
            .add("mCount", mCount)
            .add("wCount", wCount)
            .toString();
    }
}
