package com.corpgovernment.resource.schedule.domain.onlinereport.utils;

import com.corpgovernment.resource.schedule.domain.onlinereport.common.ChineseLanguageConfig;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Slf4j
/**
 * Auther:abguo
 * Date:2019/8/15
 * Description:
 * Project:onlinereportweb
 */
public class PoiCustomExcelUtils {



    public static Workbook createExcel(List<List<String>> ts, int col, int row, String sheetName) throws IOException {
        String fontname = ChineseLanguageConfig.get("fontname");
        // 创建工作簿
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        try {
            XSSFColor backgroundcolor = new XSSFColor(new java.awt.Color(50, 120, 180),new DefaultIndexedColorMap());

            // 列标题样式
            XSSFCellStyle colHeadStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,true,true, false,false,backgroundcolor,HSSFColor.HSSFColorPredefined.WHITE.getIndex());
            // 行标题样式
            XSSFCellStyle rowHeadStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 内容样式
            XSSFCellStyle contentStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 内容样式
            XSSFCellStyle numStyle1 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            XSSFCellStyle numStyle2 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 创建工作表
            XSSFSheet sheet = hssfWorkbook.createSheet(sheetName);
            // 填充sheet内容
            fillSheetContent(sheet,ts,numStyle1,numStyle2,contentStyle,colHeadStyle,rowHeadStyle,row,col);
        } catch (Exception ex) {
            log.error("createExcel error:",ex);
            if (hssfWorkbook != null){
                hssfWorkbook.close();
            }
        }
        return  hssfWorkbook;
    }

    /**
     * 填充内容
     * @param workbook
     * @param fontName
     * @param fontSize
     * @param fontBold
     * @param borderFlag
     * @param alignmentCenter
     * @param verticalAlignmentCenter
     * @param alignmentRight
     * @param alignmentLeft
     * @param backgroundColor
     * @param fontColor
     * @return
     */
    private static XSSFCellStyle createHSSFCellStyle(XSSFWorkbook workbook, String fontName, short fontSize,
                                                     boolean fontBold, boolean borderFlag, boolean alignmentCenter, boolean verticalAlignmentCenter,
                                                     boolean alignmentRight ,boolean alignmentLeft ,XSSFColor backgroundColor,short fontColor) {
        XSSFCellStyle hssfCellStyle = workbook.createCellStyle();
        // --水平居中
        if (alignmentCenter) {
            hssfCellStyle.setAlignment(HorizontalAlignment.CENTER);
        }
        // --右对齐
        if (alignmentRight) {
            hssfCellStyle.setAlignment(HorizontalAlignment.RIGHT);
        }
        // --左对齐
        if (alignmentLeft) {
            hssfCellStyle.setAlignment(HorizontalAlignment.LEFT);
        }
        // --垂直居中
        if (verticalAlignmentCenter) {
            hssfCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        }
        // --创建边框
        if (true == borderFlag) {
            hssfCellStyle.setBorderTop(BorderStyle.THIN);
            hssfCellStyle.setBorderRight(BorderStyle.THIN);
            hssfCellStyle.setBorderBottom(BorderStyle.THIN);
            hssfCellStyle.setBorderLeft(BorderStyle.THIN);
            hssfCellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            hssfCellStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        }
        if (backgroundColor != null){
            hssfCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            hssfCellStyle.setFillForegroundColor(backgroundColor);
        }
        // --创建字体
        XSSFFont font = workbook.createFont();
        font.setFontHeightInPoints(fontSize);
        // --设置字体名称
        font.setFontName(fontName);
        if (null == fontName || fontName.trim().equals("")) {
            font.setFontName("宋体");
        }
        font.setColor(fontColor);

        // --设置是否加粗
        font.setBold(fontBold);
        // 加载字体
        hssfCellStyle.setFont(font);

        return hssfCellStyle;
    }


    /**
     * 填充sheet内容
     *
     * @param sheet
     * @param ts
     */
    protected static void fillSheetContent(XSSFSheet sheet, List<List<String>> ts, XSSFCellStyle numStyle1, XSSFCellStyle numStyle2, XSSFCellStyle contentStyle,XSSFCellStyle colStyle,XSSFCellStyle rowStyle,int col,int row)
            throws IllegalAccessException {
        Map<Integer, Integer> maxWidth = new HashMap<>();
        if (ts != null) {
            for (int j = 0; j < ts.size(); j++) {
                XSSFRow row3 = sheet.createRow(j);
                List<String> temp = ts.get(j);
                for (int i = 0; i < temp.size(); i++) {
//                    sheet.autoSizeColumn(i);
//                    sheet.setColumnWidth(i,sheet.getColumnWidth(i)*17/10);
                    // 创建数据行,前面有两行,头标题行和列标题行
                    XSSFCell cell = row3.createCell(i);
                    if (j<col){
                        cell.setCellStyle(colStyle);
                    }else if (i<row){
                        cell.setCellStyle(rowStyle);
                    }else{
                        cell.setCellStyle(contentStyle);
                    }
                    int length = 15000;
                    if (null != temp.get(i)) {
                        if (CommonUtils.isNumeric(temp.get(i)) && i != 0 && CommonUtils.getNumberLen(temp.get(i)) < 16) {
                            String content = temp.get(i);
                            double cellDoubleValue = Double.parseDouble(content);
                            numStyle1.setDataFormat((short) 0);
                            cell.setCellStyle(numStyle1);
                            cell.setCellValue(cellDoubleValue);
                            length = temp.get(i).getBytes().length * 256;
                        } else if (CommonUtils.isNum1(temp.get(i)) && i != 0 && CommonUtils.getNumberLen(temp.get(i)) < 16) {
                            //千分位处理
                            String tempContent = temp.get(i);
                            double cellDoubleValue = Double.parseDouble(tempContent);
                            if (tempContent.length()-tempContent.indexOf(".")==5){
                                numStyle2.setDataFormat((short) 4);
                            }else {
                                numStyle2.setDataFormat((short) 2);
                            }
                            cell.setCellStyle(numStyle2);
                            cell.setCellValue(cellDoubleValue);
                            length = temp.get(i).getBytes().length * 256;
                        }else{
                            cell.setCellValue(temp.get(i));
                            length = temp.get(i).getBytes().length * 256;
                        }
                    }else{
                        cell.setCellValue("");
                        length = cell.getStringCellValue().getBytes().length * 256;
                    }//这里把宽度最大限制到15000
                    if (length > 15000) {
                        length = 15000;
                    }
                    if (maxWidth.size()==0||maxWidth.get(i)==null){
                        maxWidth.put(i,length);
                    }else{
                        maxWidth.put(i,Math.max(length,maxWidth.get(i)));
                    }
                }
            }
            for (int i = 0; i < maxWidth.size(); i++) {
                sheet.setColumnWidth(i, maxWidth.get(i));
            }
        }
    }



    /**
     * 创建数据excel
     * @param ts
     * @param sheetName
     * @return
     * @throws IOException
     */
    public static Workbook createExcel(List<List<String>> ts, String sheetName) throws IOException {
        String fontname = ChineseLanguageConfig.get("fontname");

        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        try {
            XSSFColor backgroundcolor = new XSSFColor(new java.awt.Color(50, 120, 180),new DefaultIndexedColorMap());

            // 列标题样式
            XSSFCellStyle headStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,true,true, false,false,backgroundcolor,HSSFColor.HSSFColorPredefined.WHITE.getIndex());
            // 内容样式
            XSSFCellStyle contentStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 内容样式
            XSSFCellStyle numStyle1 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            XSSFCellStyle numStyle2 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 创建工作表
            XSSFSheet sheet = hssfWorkbook.createSheet(sheetName);
            Map<Integer, Integer> maxWidth = new HashMap<>();
            // 填充sheet内容
            fillSheetContent(sheet,ts,numStyle1,numStyle2,contentStyle,headStyle,0,maxWidth);
        } catch (Exception ex) {
            log.error("createExcel error",ex);
            if (hssfWorkbook != null){
                hssfWorkbook.close();
            }
        }
        return  hssfWorkbook;
    }

    /**
     * 分批创建数据excel
     * @param hssfWorkbook
     * @param ts
     * @param startRow
     * @param sheetName
     * @return
     * @throws IOException
     */
    public static Workbook createExcelMulitBatch(XSSFWorkbook hssfWorkbook, List<List<String>> ts, int startRow, String sheetName,Map<Integer, Integer> maxWidth) throws IOException {
        String fontname = ChineseLanguageConfig.get("fontname");
        try {
            XSSFColor backgroundcolor = new XSSFColor(new java.awt.Color(50, 120, 180),new DefaultIndexedColorMap());

            // 列标题样式
            XSSFCellStyle headStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,true,true, false,false,backgroundcolor,HSSFColor.HSSFColorPredefined.WHITE.getIndex());
            // 内容样式
            XSSFCellStyle contentStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 内容样式
            XSSFCellStyle numStyle1 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            XSSFCellStyle numStyle2 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 创建工作表

            XSSFSheet sheet = hssfWorkbook.getSheet(sheetName) == null ? hssfWorkbook.createSheet(sheetName) : hssfWorkbook.getSheet(sheetName);
            // 填充sheet内容
            fillSheetContent(sheet,ts,numStyle1,numStyle2,contentStyle,headStyle,startRow,maxWidth);
        } catch (Exception ex) {
            log.error("createExcelMulitBatch error",ex);
            if (hssfWorkbook != null){
                hssfWorkbook.close();
            }
        }
        return  hssfWorkbook;
    }

    /**
     * 填充sheet内容
     * @param sheet sheet 也
     * @param ts 数据源
     * @param numStyle1 数值格式
     * @param numStyle2 数值格式
     * @param contentStyle 内容格式
     * @param headStyle 标题格式
     * @param startRow 开始行
     * @throws IllegalAccessException
     */
    protected static void fillSheetContent(XSSFSheet sheet, List<List<String>> ts, XSSFCellStyle numStyle1, XSSFCellStyle numStyle2, XSSFCellStyle contentStyle,XSSFCellStyle headStyle,int startRow,Map<Integer, Integer> maxWidth)
            throws IllegalAccessException {
        numStyle1.setDataFormat((short) 0);
        numStyle2.setDataFormat((short) 2);
        if (ts != null) {
            for (int j = startRow; j < ts.size()+startRow; j++) {
                XSSFRow row = sheet.createRow(j);
                List<String> temp = ts.get(j-startRow);
                for (int i = 0; i < temp.size(); i++) {
                    XSSFCell cell = row.createCell(i);
                    if (j > 0){
                        cell.setCellStyle(contentStyle);
                    }else {
                        cell.setCellStyle(headStyle);
                    }
                    int length = 15000;
                    if (null != temp.get(i)) {
                        if (CommonUtils.isNumeric(temp.get(i)) && i != 0 && CommonUtils.getNumberLen(temp.get(i)) < 16) {
                            String content = temp.get(i).replaceAll(",", "");
                            double cellDoubleValue = Double.parseDouble(content);
                            numStyle1.setDataFormat((short) 0);
                            cell.setCellStyle(numStyle1);
                            cell.setCellValue(cellDoubleValue);
                            length = temp.get(i).getBytes().length * 256;
                        } else if (CommonUtils.isNum1(temp.get(i)) && i != 0 && CommonUtils.getNumberLen(temp.get(i)) < 16) {
                            //千分位处理
                            String tempContent = temp.get(i);
                            double cellDoubleValue = Double.parseDouble(tempContent);
                            cell.setCellStyle(numStyle2);
                            cell.setCellValue(cellDoubleValue);
                            length = temp.get(i).getBytes().length * 256;
                        }else{
                            cell.setCellValue(temp.get(i));
                            length = temp.get(i).getBytes().length * 256;
                        }
                    }else{
                        cell.setCellValue("");
                        length = cell.getStringCellValue().getBytes().length * 256;
                    }//这里把宽度最大限制到15000
                    if (length > 15000) {
                        length = 15000;
                    }
                    if (maxWidth.size()==0||maxWidth.get(i)==null){
                        maxWidth.put(i,length);
                    }else{
                        maxWidth.put(i,Math.max(length,maxWidth.get(i)));
                    }
                }
            }
            for (int i = 0; i < maxWidth.size(); i++) {
                sheet.setColumnWidth(i, maxWidth.get(i));
            }
        }
    }


    /**
     * 创建大数据excel
     * @param ts
     * @param sheetName
     * @return
     * @throws IOException
     */
    public static Workbook createExcelBigData(List<List<String>> ts, String sheetName) throws IOException {
        String fontname = ChineseLanguageConfig.get("fontname");
        // 创建工作簿
        Integer sheetLimit = QConfigUtils.getInterValue("sheetLimit");
        if (sheetLimit == null || sheetLimit <= 0){
            sheetLimit = 10000;
        }
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        SXSSFWorkbook wb = new SXSSFWorkbook(hssfWorkbook,sheetLimit);
        wb.setCompressTempFiles(true); // 压缩临时文件
        try {
            XSSFColor backgroundcolor = new XSSFColor(new java.awt.Color(50, 120, 180),new DefaultIndexedColorMap());

            // 列标题样式
            XSSFCellStyle headStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,true,true, false,false,backgroundcolor,HSSFColor.HSSFColorPredefined.WHITE.getIndex());
            // 内容样式
            XSSFCellStyle contentStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 内容样式
            XSSFCellStyle numStyle1 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            XSSFCellStyle numStyle2 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 创建工作表
            SXSSFSheet sheet = wb.createSheet(sheetName);
            Map<Integer, Integer> maxWidth = new HashMap<>();
            // 填充sheet内容
            fillSheetContent(sheet,ts,numStyle1,numStyle2,contentStyle,headStyle,0,maxWidth);
        } catch (Exception ex) {
            log.error("createExcelBigData error",ex);
            if (wb != null){
                if (wb instanceof SXSSFWorkbook){
                    ((SXSSFWorkbook) wb).dispose();
                }
                wb.close();
            }
        }
        return  wb;
    }

    /**
     * 分批创建大数据excel
     * @param wb
     * @param ts
     * @param startRow
     * @param sheetName
     * @return
     * @throws IOException
     */
    public static Workbook createExcelBigDataMulitBatch(SXSSFWorkbook wb, List<List<String>> ts, int startRow, String sheetName,Map<Integer, Integer> maxWidth) throws IOException {
        String fontname = ChineseLanguageConfig.get("fontname");
        if (wb == null){
            return null;
        }
        XSSFWorkbook hssfWorkbook =  wb.getXSSFWorkbook();
        wb.setCompressTempFiles(true); // 压缩临时文件
        try {
            XSSFColor backgroundcolor = new XSSFColor(new java.awt.Color(50, 120, 180),new DefaultIndexedColorMap());

            // 列标题样式
            XSSFCellStyle headStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,true,true, false,false,backgroundcolor,HSSFColor.HSSFColorPredefined.WHITE.getIndex());
            // 内容样式
            XSSFCellStyle contentStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 内容样式
            XSSFCellStyle numStyle1 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            XSSFCellStyle numStyle2 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 创建工作表
            SXSSFSheet sheet = wb.getSheet(sheetName) == null ? wb.createSheet(sheetName) : wb.getSheet(sheetName);
            // 填充sheet内容
            fillSheetContent(sheet,ts,numStyle1,numStyle2,contentStyle,headStyle,startRow,maxWidth);
        } catch (Exception ex) {
            log.error("createExcelBigDataMulitBatch error",ex);
            if (wb instanceof SXSSFWorkbook){
                ((SXSSFWorkbook) wb).dispose();
            }
            wb.close();
        }
        return  wb;
    }

    /**
     * 填充sheet内容
     * @param sheet sheet 也
     * @param ts 数据源
     * @param numStyle1 数值格式
     * @param numStyle2 数值格式
     * @param contentStyle 内容格式
     * @param headStyle 标题格式
     * @param startRow 开始行
     * @throws IllegalAccessException
     */
    protected static void fillSheetContent(SXSSFSheet sheet, List<List<String>> ts, XSSFCellStyle numStyle1, XSSFCellStyle numStyle2, XSSFCellStyle contentStyle,XSSFCellStyle headStyle,int startRow,Map<Integer, Integer> maxWidth)
            throws IllegalAccessException {
        numStyle1.setDataFormat((short) 0);
        numStyle2.setDataFormat((short) 2);
        if (ts != null) {
            for (int j = startRow; j < ts.size()+startRow; j++) {
                SXSSFRow row = sheet.createRow(j);
                List<String> temp = ts.get(j-startRow);
                for (int i = 0; i < temp.size(); i++) {
                    SXSSFCell cell = row.createCell(i);
                    if (j > 0){
                        cell.setCellStyle(contentStyle);
                    }else {
                        cell.setCellStyle(headStyle);
                    }
                    int length = 15000;
                    if (null != temp.get(i)) {
                        if (CommonUtils.isNumeric(temp.get(i))&&i!=0){//整数
                            String content = temp.get(i);
                            //excel最多支持的数值型数据的长度是15位。当数据长度到过15位时，从第16位开始，后面的数字全部被默认修改为0,当数值的长度大于15的时候设置单元格为文本格式
                            if (content.length() <15){
                                double cellDoubleValue = Double.parseDouble(content);
                                numStyle1.setDataFormat((short) 0);
                                cell.setCellStyle(numStyle1);
                                cell.setCellValue(cellDoubleValue);
                                length = temp.get(i).getBytes().length * 256;
                            }else {
                                cell.setCellValue(temp.get(i));
                                length = temp.get(i).getBytes().length * 256;
                            }
                        } else if (CommonUtils.isNum1(temp.get(i))&&i!=0){//数值
                            //千分位处理
                            String tempContent = temp.get(i).replaceAll(",", "");
                            if (tempContent.length() <15){
                                double cellDoubleValue = Double.parseDouble(tempContent);
                                cell.setCellStyle(numStyle2);
                                cell.setCellValue(cellDoubleValue);
                                length = temp.get(i).getBytes().length * 256;
                            }else {
                                cell.setCellValue(temp.get(i));
                                length = temp.get(i).getBytes().length * 256;
                            }
                        }else{
                            cell.setCellValue(temp.get(i));
                            length = temp.get(i).getBytes().length * 256;
                        }
                    }else{
                        cell.setCellValue("");
                        length = cell.getStringCellValue().getBytes().length * 256;
                    }//这里把宽度最大限制到15000
                    if (length > 15000) {
                        length = 15000;
                    }
                    if (maxWidth.size()==0||maxWidth.get(i)==null){
                        maxWidth.put(i,length);
                    }else{
                        maxWidth.put(i,Math.max(length,maxWidth.get(i)));
                    }
                }
            }
            for (int i = 0; i < maxWidth.size(); i++) {
                sheet.setColumnWidth(i, maxWidth.get(i));
            }
        }
    }

    /**
     * 分批创建大数据excel
     * @param wb
     * @param ts
     * @param startRow
     * @param sheetName
     * @return
     * @throws IOException
     */
    public static Workbook createExcelBigDataMulitBatchObj(SXSSFWorkbook wb, List<List<Object>> ts, int startRow, String sheetName,
                                                           Map<Integer, Integer> maxWidth, List<Integer> highlightTitle) throws IOException {
        String fontname = ChineseLanguageConfig.get("fontname");
        if (wb == null){
            return null;
        }
        XSSFWorkbook hssfWorkbook =  wb.getXSSFWorkbook();
        wb.setCompressTempFiles(true); // 压缩临时文件
        try {
            XSSFColor backgroundcolor = new XSSFColor(new java.awt.Color(50, 120, 180), new DefaultIndexedColorMap());
            XSSFColor specialBackGroundColor = new XSSFColor(new java.awt.Color(220, 20, 60), new DefaultIndexedColorMap());

            // 列标题样式
            XSSFCellStyle headStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,true,true, false,false,backgroundcolor,HSSFColor.HSSFColorPredefined.WHITE.getIndex());
            // 列标题样式
            XSSFCellStyle specialHeadStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false, true, true, false, false, specialBackGroundColor, HSSFColor.HSSFColorPredefined.WHITE.getIndex());
            // 内容样式
            XSSFCellStyle contentStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 内容样式
            XSSFCellStyle numStyle1 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            XSSFCellStyle numStyle2 = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, false,
                    false,false,false,false, true,null,HSSFColor.HSSFColorPredefined.BLACK.getIndex());
            // 创建工作表
            SXSSFSheet sheet = wb.getSheet(sheetName) == null ? wb.createSheet(sheetName) : wb.getSheet(sheetName);
            // 填充sheet内容
            fillSheetContentObj(sheet, ts, numStyle1, numStyle2, contentStyle, headStyle, startRow, maxWidth, highlightTitle, specialHeadStyle);
        } catch (Exception ex) {
            log.error("createExcelBigDataMulitBatchObj error",ex);
            if (wb instanceof SXSSFWorkbook){
                ((SXSSFWorkbook) wb).dispose();
            }
            wb.close();
        }
        return  wb;
    }

    /**
     * 填充sheet内容
     * @param sheet sheet 也
     * @param ts 数据源
     * @param numStyle1 数值格式
     * @param numStyle2 数值格式
     * @param contentStyle 内容格式
     * @param headStyle 标题格式
     * @param startRow 开始行
     * @throws IllegalAccessException
     */
    protected static void fillSheetContentObj(SXSSFSheet sheet, List<List<Object>> ts, XSSFCellStyle numStyle1, XSSFCellStyle numStyle2,
                                              XSSFCellStyle contentStyle, XSSFCellStyle headStyle, int startRow, Map<Integer, Integer> maxWidth,
                                              List<Integer> highLightHeadCells, XSSFCellStyle highLightHeadStyle)
            throws IllegalAccessException {
        numStyle1.setDataFormat((short) 0);
        numStyle2.setDataFormat((short) 2);
        if (ts != null) {
            for (int j = startRow; j < ts.size()+startRow; j++) {
                SXSSFRow row = sheet.createRow(j);
                List<Object> temp = ts.get(j-startRow);
                for (int i = 0; i < temp.size(); i++) {
                    SXSSFCell cell = row.createCell(i);
                    if (j > 0){
                        cell.setCellStyle(contentStyle);
                    }else {
                        if (CollectionUtils.isNotEmpty(highLightHeadCells) && highLightHeadCells.contains(i)) {
                            cell.setCellStyle(highLightHeadStyle);
                        } else {
                            cell.setCellStyle(headStyle);
                        }
                    }
                    int length = 15000;
                    Object object = temp.get(i);
                    if (null != object) {
                        String dataStr = String.valueOf(object);
                        // 第一列是订单号，显示成文本格式
                        if (i != 0){
                            if (object instanceof String){
                                cell.setCellValue(dataStr);
                                length = dataStr.getBytes().length * 256;
                            }else if (object instanceof Integer || object instanceof Long) {
                                double cellDoubleValue = Double.parseDouble(dataStr);
                                cell.setCellStyle(numStyle1);
                                cell.setCellValue(cellDoubleValue);
                                length = dataStr.getBytes().length * 256;
                            }else if (object instanceof Double || object instanceof Float || object instanceof BigDecimal) {
                                double cellDoubleValue = Double.parseDouble(dataStr);
                                cell.setCellStyle(numStyle2);
                                cell.setCellValue(cellDoubleValue);
                                length = dataStr.getBytes().length * 256;
                            }else {
                                cell.setCellValue(dataStr);
                                length = dataStr.getBytes().length * 256;
                            }
                        }else {
                            cell.setCellValue(dataStr);
                            length = dataStr.getBytes().length * 256;
                        }
                    }else{
                        cell.setCellValue("");
                        length = cell.getStringCellValue().getBytes().length * 256;
                    }//这里把宽度最大限制到15000
                    if (length > 15000) {
                        length = 15000;
                    }
                    if (maxWidth.size()==0||maxWidth.get(i)==null){
                        maxWidth.put(i,length);
                    }else{
                        maxWidth.put(i,Math.max(length,maxWidth.get(i)));
                    }
                }
            }
            for (int i = 0; i < maxWidth.size(); i++) {
                sheet.setColumnWidth(i, maxWidth.get(i));
            }
            // 设置卡片携带字段的汇总

        }
    }

    public static void sumColAndFilledInRear(SXSSFWorkbook workbook, String sheetName, List<Integer> highlightTitle) {
        SXSSFSheet sheet = workbook.getSheet(sheetName);
        if (null == sheet) {
            log.error("sumColAndFilledInRear", String.format("can not obtain sheet, sheetName:%s", sheetName));
            return;
        }
        // 汇总卡片携带字段到excel最后一行
        // 单元格
        Cell cell;
        // 长度转成ABC后的列
        String colString;
        // 求和公式
        String sumString;
        if (sheet.getLastRowNum() > 0 && CollectionUtils.isNotEmpty(highlightTitle)) {
            SXSSFRow row = sheet.createRow(sheet.getLastRowNum() + 1);
            // 设置汇总名称
            for (Integer colNo : highlightTitle) {
                // 列标题样式
                String fontname = ChineseLanguageConfig.get("fontname");
                XSSFWorkbook hssfWorkbook = workbook.getXSSFWorkbook();
                XSSFCellStyle specialHeadStyle = createHSSFCellStyle(hssfWorkbook, fontname, (short) 10, true,
                        false, false, false, true, false,
                        null, HSSFColor.HSSFColorPredefined.BLACK.getIndex());
                cell = row.createCell(colNo);
                cell.setCellStyle(specialHeadStyle);
                colString = CellReference.convertNumToColString(colNo);  //长度转成ABC列
                //求和公式 求i9至i12单元格的总和
                sumString = "SUM(" + colString + "2:" + colString + sheet.getLastRowNum() + ")";
                // 把公式塞入合计列
                cell.setCellFormula(sumString);
            }
        }
    }

}
