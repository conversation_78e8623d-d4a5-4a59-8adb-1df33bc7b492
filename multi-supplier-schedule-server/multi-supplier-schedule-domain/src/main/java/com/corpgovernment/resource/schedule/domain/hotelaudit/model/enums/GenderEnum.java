package com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 性别枚举
 * @create 2024-09-10 15:14
 */
@AllArgsConstructor
@Getter
public enum GenderEnum {
    
    MALE("male", "男", "M"),
    FEMALE("female", "女", "F"),
    ;
    
    private final String code;
    
    private final String info;
    
    private final String shortCode;
    
    private static final Map<String, GenderEnum> map = new HashMap<>();
    
    static {
        for (GenderEnum tmpEnum : values()) {
            map.put(tmpEnum.getCode(), tmpEnum);
            map.put(tmpEnum.getInfo(), tmpEnum);
            map.put(tmpEnum.getShortCode(), tmpEnum);
        }
    }
    
    public static GenderEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return map.get(key);
    }
    
}
