package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * * 个人足迹 年度报告
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "queryYear",
    "uid"
})
public class FootprintPreviewRequestType implements Serializable {
    private static final long serialVersionUID = 1L;





    public FootprintPreviewRequestType(
        String queryYear,
        String uid) {
        this.queryYear = queryYear;
        this.uid = uid;
    }

    public FootprintPreviewRequestType() {
    }

    /**
     * 年份
     */
    @JsonProperty("queryYear")
    public String queryYear;

    @JsonProperty("uid")
    public String uid;

    /**
     * 年份
     */
    public String getQueryYear() {
        return queryYear;
    }

    /**
     * 年份
     */
    public void setQueryYear(final String queryYear) {
        this.queryYear = queryYear;
    }
    public String getUid() {
        return uid;
    }

    public void setUid(final String uid) {
        this.uid = uid;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FootprintPreviewRequestType other = (FootprintPreviewRequestType)obj;
        return
            Objects.equal(this.queryYear, other.queryYear) &&
            Objects.equal(this.uid, other.uid);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.queryYear == null ? 0 : this.queryYear.hashCode());
        result = 31 * result + (this.uid == null ? 0 : this.uid.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("queryYear", queryYear)
            .add("uid", uid)
            .toString();
    }
}
