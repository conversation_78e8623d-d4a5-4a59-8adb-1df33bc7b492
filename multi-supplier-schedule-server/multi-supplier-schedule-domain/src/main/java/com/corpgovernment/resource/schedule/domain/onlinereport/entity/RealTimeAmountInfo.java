package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 实时金额数据分析
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "totalAmount",
    "preDayPercent",
    "preWeekPercent",
    "trendInfoList"
})
public class RealTimeAmountInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public RealTimeAmountInfo(
        BigDecimal totalAmount,
        Double preDayPercent,
        Double preWeekPercent,
        List<RealTimeAmountTrendInfo> trendInfoList) {
        this.totalAmount = totalAmount;
        this.preDayPercent = preDayPercent;
        this.preWeekPercent = preWeekPercent;
        this.trendInfoList = trendInfoList;
    }

    public RealTimeAmountInfo() {
    }

    /**
     * 当前金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;

    /**
     * 昨日同期
     */
    @JsonProperty("preDayPercent")
    public Double preDayPercent;

    /**
     * 上周同期
     */
    @JsonProperty("preWeekPercent")
    public Double preWeekPercent;

    /**
     * 趋势信息
     */
    @JsonProperty("trendInfoList")
    public List<RealTimeAmountTrendInfo> trendInfoList;

    /**
     * 当前金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 当前金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 昨日同期
     */
    public Double getPreDayPercent() {
        return preDayPercent;
    }

    /**
     * 昨日同期
     */
    public void setPreDayPercent(final Double preDayPercent) {
        this.preDayPercent = preDayPercent;
    }

    /**
     * 上周同期
     */
    public Double getPreWeekPercent() {
        return preWeekPercent;
    }

    /**
     * 上周同期
     */
    public void setPreWeekPercent(final Double preWeekPercent) {
        this.preWeekPercent = preWeekPercent;
    }

    /**
     * 趋势信息
     */
    public List<RealTimeAmountTrendInfo> getTrendInfoList() {
        return trendInfoList;
    }

    /**
     * 趋势信息
     */
    public void setTrendInfoList(final List<RealTimeAmountTrendInfo> trendInfoList) {
        this.trendInfoList = trendInfoList;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final RealTimeAmountInfo other = (RealTimeAmountInfo)obj;
        return
            Objects.equal(this.totalAmount, other.totalAmount) &&
            Objects.equal(this.preDayPercent, other.preDayPercent) &&
            Objects.equal(this.preWeekPercent, other.preWeekPercent) &&
            Objects.equal(this.trendInfoList, other.trendInfoList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.preDayPercent == null ? 0 : this.preDayPercent.hashCode());
        result = 31 * result + (this.preWeekPercent == null ? 0 : this.preWeekPercent.hashCode());
        result = 31 * result + (this.trendInfoList == null ? 0 : this.trendInfoList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("totalAmount", totalAmount)
            .add("preDayPercent", preDayPercent)
            .add("preWeekPercent", preWeekPercent)
            .add("trendInfoList", trendInfoList)
            .toString();
    }
}
