package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 热门航段
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "data"
})
public class FlightHotCity implements Serializable {
    private static final long serialVersionUID = 1L;





    public FlightHotCity(
        List<FlightHotCityEntity> data) {
        this.data = data;
    }

    public FlightHotCity() {
    }

    @JsonProperty("data")
    public List<FlightHotCityEntity> data;

    public List<FlightHotCityEntity> getData() {
        return data;
    }

    public void setData(final List<FlightHotCityEntity> data) {
        this.data = data;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightHotCity other = (FlightHotCity)obj;
        return
            Objects.equal(this.data, other.data);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("data", data)
            .toString();
    }
}
