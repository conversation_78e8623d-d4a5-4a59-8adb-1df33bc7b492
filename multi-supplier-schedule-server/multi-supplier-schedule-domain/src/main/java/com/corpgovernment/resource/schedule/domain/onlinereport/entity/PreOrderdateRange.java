package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;

/**
 * 提前条数区间
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "start",
        "end"
})
public class PreOrderdateRange implements Serializable {
    private static final long serialVersionUID = 1L;


    public PreOrderdateRange(
            Integer start,
            Integer end) {
        this.start = start;
        this.end = end;
    }

    public PreOrderdateRange() {
    }

    @JsonProperty("start")
    public Integer start;

    @JsonProperty("end")
    public Integer end;

    public Integer getStart() {
        return start;
    }

    public void setStart(final Integer start) {
        this.start = start;
    }

    public Integer getEnd() {
        return end;
    }

    public void setEnd(final Integer end) {
        this.end = end;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final PreOrderdateRange other = (PreOrderdateRange) obj;
        return
                Objects.equal(this.start, other.start) &&
                        Objects.equal(this.end, other.end);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.start == null ? 0 : this.start.hashCode());
        result = 31 * result + (this.end == null ? 0 : this.end.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("start", start)
                .add("end", end)
                .toString();
    }
}
