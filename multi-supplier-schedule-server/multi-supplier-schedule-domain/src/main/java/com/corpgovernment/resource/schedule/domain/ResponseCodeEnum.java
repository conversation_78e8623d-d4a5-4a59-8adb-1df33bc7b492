package com.corpgovernment.resource.schedule.domain;

import com.ctrip.corp.obt.generic.exception.ResponseStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/10/11
 */
@Getter
@ToString
@AllArgsConstructor
public enum ResponseCodeEnum implements ResponseStatus {
    RPC_IS_ERROR(61001, "RPC调用异常"),

    /**
     * 获取租户信息失败
     */
    GET_TENANT_INFO_ERROR(61002, "获取租户信息失败"),

    EMAIL_SUBJECT_MISSING(10120118, "邮件主题缺失"),
    EMAIL_RECEIVER_MISSING(10120119, "邮件接收人邮箱缺失")

    ;

    private final int code;
    private final String message;

    @Override
    public int code() {
        return code;
    }

    @Override
    public String message() {
        return message;
    }
    
}
