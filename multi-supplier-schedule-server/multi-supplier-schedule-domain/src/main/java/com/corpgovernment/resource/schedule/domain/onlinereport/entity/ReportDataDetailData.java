package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "flightV",
    "flightYoy",
    "flightMom",
    "hotelV",
    "hotelYoy",
    "hotelMom",
    "trainV",
    "trainYoy",
    "trainMom",
    "carV",
    "carYoy",
    "carMom",
    "busV",
    "busYoy",
    "busMom",
    "addV",
    "addYoy",
    "addMom",
    "totalV",
    "totalYoy",
    "totalMom",
    "agreementV",
    "agreementYoy",
    "agreementMom",
    "unAgreementV",
    "unAgreementYoy",
    "unAgreementMom",
    "domesticV",
    "domesticYoy",
    "domesticMom",
    "internationalV",
    "internationalYoy",
    "internationalMom",
    "takeCarV",
    "takeCarYoy",
    "takeCarMom",
    "takeCarVInter",
    "takeCarYoyInter",
    "takeCarMomInter",
    "transferV",
    "transferYoy",
    "transferMom",
    "transferVInter",
    "transferYoyInter",
    "transferMomInter",
    "rentalCarV",
    "rentalCarYoy",
    "rentalCarMom",
    "rentalCarVInter",
    "rentalCarYoyInter",
    "rentalCarMomInter",
    "charterCarV",
    "charterCarYoy",
    "charterCarMom",
    "netfare",
    "tax",
    "oilFee",
    "sendTicketFee",
    "servicepackageFee",
    "bindAmount",
    "changeFee",
    "oilfeedifferential",
    "taxDifferential",
    "rebookPriceDifferential",
    "rebookServiceFee",
    "refundFee",
    "refundServiceFee",
    "refundItineraryFee",
    "ticketBehindServicefee",
    "rebookBehindServiceFee",
    "refundBehindServiceFee",
    "roomPrice",
    "couponAmount",
    "hotelPostServiceFee",
    "ticketPrice",
    "deliverFee",
    "grabServiceFee",
    "paperTicketFee",
    "afterServiceFee",
    "afterChangeServiceFee",
    "afterTakeTicketFee",
    "basicFee",
    "refundAmount",
    "serviceFee",
    "insuranceFee",
    "quantity"
})
public class ReportDataDetailData implements Serializable {
    private static final long serialVersionUID = 1L;





    public ReportDataDetailData(
        String flightV,
        String flightYoy,
        String flightMom,
        String hotelV,
        String hotelYoy,
        String hotelMom,
        String trainV,
        String trainYoy,
        String trainMom,
        String carV,
        String carYoy,
        String carMom,
        String busV,
        String busYoy,
        String busMom,
        String addV,
        String addYoy,
        String addMom,
        String totalV,
        String totalYoy,
        String totalMom,
        String agreementV,
        String agreementYoy,
        String agreementMom,
        String unAgreementV,
        String unAgreementYoy,
        String unAgreementMom,
        String domesticV,
        String domesticYoy,
        String domesticMom,
        String internationalV,
        String internationalYoy,
        String internationalMom,
        String takeCarV,
        String takeCarYoy,
        String takeCarMom,
        String takeCarVInter,
        String takeCarYoyInter,
        String takeCarMomInter,
        String transferV,
        String transferYoy,
        String transferMom,
        String transferVInter,
        String transferYoyInter,
        String transferMomInter,
        String rentalCarV,
        String rentalCarYoy,
        String rentalCarMom,
        String rentalCarVInter,
        String rentalCarYoyInter,
        String rentalCarMomInter,
        String charterCarV,
        String charterCarYoy,
        String charterCarMom,
        String netfare,
        String tax,
        String oilFee,
        String sendTicketFee,
        String servicepackageFee,
        String bindAmount,
        String changeFee,
        String oilfeedifferential,
        String taxDifferential,
        String rebookPriceDifferential,
        String rebookServiceFee,
        String refundFee,
        String refundServiceFee,
        String refundItineraryFee,
        String ticketBehindServicefee,
        String rebookBehindServiceFee,
        String refundBehindServiceFee,
        String roomPrice,
        String couponAmount,
        String hotelPostServiceFee,
        String ticketPrice,
        String deliverFee,
        String grabServiceFee,
        String paperTicketFee,
        String afterServiceFee,
        String afterChangeServiceFee,
        String afterTakeTicketFee,
        String basicFee,
        String refundAmount,
        String serviceFee,
        String insuranceFee,
        String quantity) {
        this.flightV = flightV;
        this.flightYoy = flightYoy;
        this.flightMom = flightMom;
        this.hotelV = hotelV;
        this.hotelYoy = hotelYoy;
        this.hotelMom = hotelMom;
        this.trainV = trainV;
        this.trainYoy = trainYoy;
        this.trainMom = trainMom;
        this.carV = carV;
        this.carYoy = carYoy;
        this.carMom = carMom;
        this.busV = busV;
        this.busYoy = busYoy;
        this.busMom = busMom;
        this.addV = addV;
        this.addYoy = addYoy;
        this.addMom = addMom;
        this.totalV = totalV;
        this.totalYoy = totalYoy;
        this.totalMom = totalMom;
        this.agreementV = agreementV;
        this.agreementYoy = agreementYoy;
        this.agreementMom = agreementMom;
        this.unAgreementV = unAgreementV;
        this.unAgreementYoy = unAgreementYoy;
        this.unAgreementMom = unAgreementMom;
        this.domesticV = domesticV;
        this.domesticYoy = domesticYoy;
        this.domesticMom = domesticMom;
        this.internationalV = internationalV;
        this.internationalYoy = internationalYoy;
        this.internationalMom = internationalMom;
        this.takeCarV = takeCarV;
        this.takeCarYoy = takeCarYoy;
        this.takeCarMom = takeCarMom;
        this.takeCarVInter = takeCarVInter;
        this.takeCarYoyInter = takeCarYoyInter;
        this.takeCarMomInter = takeCarMomInter;
        this.transferV = transferV;
        this.transferYoy = transferYoy;
        this.transferMom = transferMom;
        this.transferVInter = transferVInter;
        this.transferYoyInter = transferYoyInter;
        this.transferMomInter = transferMomInter;
        this.rentalCarV = rentalCarV;
        this.rentalCarYoy = rentalCarYoy;
        this.rentalCarMom = rentalCarMom;
        this.rentalCarVInter = rentalCarVInter;
        this.rentalCarYoyInter = rentalCarYoyInter;
        this.rentalCarMomInter = rentalCarMomInter;
        this.charterCarV = charterCarV;
        this.charterCarYoy = charterCarYoy;
        this.charterCarMom = charterCarMom;
        this.netfare = netfare;
        this.tax = tax;
        this.oilFee = oilFee;
        this.sendTicketFee = sendTicketFee;
        this.servicepackageFee = servicepackageFee;
        this.bindAmount = bindAmount;
        this.changeFee = changeFee;
        this.oilfeedifferential = oilfeedifferential;
        this.taxDifferential = taxDifferential;
        this.rebookPriceDifferential = rebookPriceDifferential;
        this.rebookServiceFee = rebookServiceFee;
        this.refundFee = refundFee;
        this.refundServiceFee = refundServiceFee;
        this.refundItineraryFee = refundItineraryFee;
        this.ticketBehindServicefee = ticketBehindServicefee;
        this.rebookBehindServiceFee = rebookBehindServiceFee;
        this.refundBehindServiceFee = refundBehindServiceFee;
        this.roomPrice = roomPrice;
        this.couponAmount = couponAmount;
        this.hotelPostServiceFee = hotelPostServiceFee;
        this.ticketPrice = ticketPrice;
        this.deliverFee = deliverFee;
        this.grabServiceFee = grabServiceFee;
        this.paperTicketFee = paperTicketFee;
        this.afterServiceFee = afterServiceFee;
        this.afterChangeServiceFee = afterChangeServiceFee;
        this.afterTakeTicketFee = afterTakeTicketFee;
        this.basicFee = basicFee;
        this.refundAmount = refundAmount;
        this.serviceFee = serviceFee;
        this.insuranceFee = insuranceFee;
        this.quantity = quantity;
    }

    public ReportDataDetailData() {
    }

    /**
     * 概览-机票
     */
    @JsonProperty("flightV")
    public String flightV;

    @JsonProperty("flightYoy")
    public String flightYoy;

    @JsonProperty("flightMom")
    public String flightMom;

    /**
     * 概览-酒店
     */
    @JsonProperty("hotelV")
    public String hotelV;

    @JsonProperty("hotelYoy")
    public String hotelYoy;

    @JsonProperty("hotelMom")
    public String hotelMom;

    /**
     * 概览-火车
     */
    @JsonProperty("trainV")
    public String trainV;

    @JsonProperty("trainYoy")
    public String trainYoy;

    @JsonProperty("trainMom")
    public String trainMom;

    /**
     * 概览-用车
     */
    @JsonProperty("carV")
    public String carV;

    @JsonProperty("carYoy")
    public String carYoy;

    @JsonProperty("carMom")
    public String carMom;

    /**
     * 概览-汽车
     */
    @JsonProperty("busV")
    public String busV;

    @JsonProperty("busYoy")
    public String busYoy;

    @JsonProperty("busMom")
    public String busMom;

    /**
     * 概览-增值
     */
    @JsonProperty("addV")
    public String addV;

    @JsonProperty("addYoy")
    public String addYoy;

    @JsonProperty("addMom")
    public String addMom;

    /**
     * 总计
     */
    @JsonProperty("totalV")
    public String totalV;

    @JsonProperty("totalYoy")
    public String totalYoy;

    @JsonProperty("totalMom")
    public String totalMom;

    /**
     * 协议
     */
    @JsonProperty("agreementV")
    public String agreementV;

    @JsonProperty("agreementYoy")
    public String agreementYoy;

    @JsonProperty("agreementMom")
    public String agreementMom;

    /**
     * 非协议
     */
    @JsonProperty("unAgreementV")
    public String unAgreementV;

    @JsonProperty("unAgreementYoy")
    public String unAgreementYoy;

    @JsonProperty("unAgreementMom")
    public String unAgreementMom;

    /**
     * 国内
     */
    @JsonProperty("domesticV")
    public String domesticV;

    @JsonProperty("domesticYoy")
    public String domesticYoy;

    @JsonProperty("domesticMom")
    public String domesticMom;

    /**
     * 国际
     */
    @JsonProperty("internationalV")
    public String internationalV;

    @JsonProperty("internationalYoy")
    public String internationalYoy;

    @JsonProperty("internationalMom")
    public String internationalMom;

    /**
     * 用车
     */
    @JsonProperty("takeCarV")
    public String takeCarV;

    /**
     * 用车
     */
    @JsonProperty("takeCarYoy")
    public String takeCarYoy;

    @JsonProperty("takeCarMom")
    public String takeCarMom;

    /**
     * 国际用车
     */
    @JsonProperty("takeCarVInter")
    public String takeCarVInter;

    /**
     * 国际用车
     */
    @JsonProperty("takeCarYoyInter")
    public String takeCarYoyInter;

    @JsonProperty("takeCarMomInter")
    public String takeCarMomInter;

    /**
     * 接送机
     */
    @JsonProperty("transferV")
    public String transferV;

    /**
     * 接送机
     */
    @JsonProperty("transferYoy")
    public String transferYoy;

    @JsonProperty("transferMom")
    public String transferMom;

    /**
     * 国际接送机
     */
    @JsonProperty("transferVInter")
    public String transferVInter;

    /**
     * 国际接送机
     */
    @JsonProperty("transferYoyInter")
    public String transferYoyInter;

    @JsonProperty("transferMomInter")
    public String transferMomInter;

    /**
     * 租车
     */
    @JsonProperty("rentalCarV")
    public String rentalCarV;

    /**
     * 租车
     */
    @JsonProperty("rentalCarYoy")
    public String rentalCarYoy;

    @JsonProperty("rentalCarMom")
    public String rentalCarMom;

    /**
     * 国际租车
     */
    @JsonProperty("rentalCarVInter")
    public String rentalCarVInter;

    @JsonProperty("rentalCarYoyInter")
    public String rentalCarYoyInter;

    @JsonProperty("rentalCarMomInter")
    public String rentalCarMomInter;

    /**
     * 包车
     */
    @JsonProperty("charterCarV")
    public String charterCarV;

    /**
     * 包车
     */
    @JsonProperty("charterCarYoy")
    public String charterCarYoy;

    @JsonProperty("charterCarMom")
    public String charterCarMom;

    /**
     * 机票-成交净价
     */
    @JsonProperty("netfare")
    public String netfare;

    /**
     * 机票-机建税
     */
    @JsonProperty("tax")
    public String tax;

    /**
     * 机票-燃油费
     */
    @JsonProperty("oilFee")
    public String oilFee;

    /**
     * 机票-配送费
     */
    @JsonProperty("sendTicketFee")
    public String sendTicketFee;

    /**
     * 机票-增值服务包费
     */
    @JsonProperty("servicepackageFee")
    public String servicepackageFee;

    /**
     * 机票-绑定酒店优惠券
     */
    @JsonProperty("bindAmount")
    public String bindAmount;

    /**
     * 机票-改签费
     */
    @JsonProperty("changeFee")
    public String changeFee;

    /**
     * 机票-改签燃油差
     */
    @JsonProperty("oilfeedifferential")
    public String oilfeedifferential;

    /**
     * 机票-税差
     */
    @JsonProperty("taxDifferential")
    public String taxDifferential;

    /**
     * 机票-改签差价
     */
    @JsonProperty("rebookPriceDifferential")
    public String rebookPriceDifferential;

    /**
     * 机票-改签商旅管理服务费
     */
    @JsonProperty("rebookServiceFee")
    public String rebookServiceFee;

    /**
     * 机票-退票费
     */
    @JsonProperty("refundFee")
    public String refundFee;

    /**
     * 机票-退票商旅管理服务费
     */
    @JsonProperty("refundServiceFee")
    public String refundServiceFee;

    /**
     * 机票-退票行程单商旅管理服务费
     */
    @JsonProperty("refundItineraryFee")
    public String refundItineraryFee;

    /**
     * 机票-后收商旅管理服务费
     */
    @JsonProperty("ticketBehindServicefee")
    public String ticketBehindServicefee;

    /**
     * 机票-后收改签商旅管理服务费
     */
    @JsonProperty("rebookBehindServiceFee")
    public String rebookBehindServiceFee;

    /**
     * 机票-后收退票商旅管理服务费
     */
    @JsonProperty("refundBehindServiceFee")
    public String refundBehindServiceFee;

    /**
     * 酒店-房价
     */
    @JsonProperty("roomPrice")
    public String roomPrice;

    /**
     * 酒店-酒店优惠券
     */
    @JsonProperty("couponAmount")
    public String couponAmount;

    /**
     * 酒店-后收商旅管理服务费
     */
    @JsonProperty("hotelPostServiceFee")
    public String hotelPostServiceFee;

    /**
     * 原始出票金额
     */
    @JsonProperty("ticketPrice")
    public String ticketPrice;

    /**
     * 配送费
     * 改签差价
     * 基础商旅管理服务费
     */
    @JsonProperty("deliverFee")
    public String deliverFee;

    /**
     * 抢票费
     */
    @JsonProperty("grabServiceFee")
    public String grabServiceFee;

    /**
     * 纸质出票费
     */
    @JsonProperty("paperTicketFee")
    public String paperTicketFee;

    /**
     * 后收商旅管理服务费
     * 改签商旅管理服务费
     * 代取人工费
     * 退票金额
     */
    @JsonProperty("afterServiceFee")
    public String afterServiceFee;

    /**
     * 后收改签商旅管理服务费
     */
    @JsonProperty("afterChangeServiceFee")
    public String afterChangeServiceFee;

    /**
     * 后收代取人工费
     */
    @JsonProperty("afterTakeTicketFee")
    public String afterTakeTicketFee;

    /**
     * 用车-基础费用
     */
    @JsonProperty("basicFee")
    public String basicFee;

    /**
     * 用车-退款金额
     */
    @JsonProperty("refundAmount")
    public String refundAmount;

    /**
     * 机票/酒店/火车/用车-公用字段-商旅管理服务费
     */
    @JsonProperty("serviceFee")
    public String serviceFee;

    /**
     * 机票/火车-公用字段-保险费
     */
    @JsonProperty("insuranceFee")
    public String insuranceFee;

    /**
     * 机票/酒店/火车/用车-公用字段-票张
     */
    @JsonProperty("quantity")
    public String quantity;

    /**
     * 概览-机票
     */
    public String getFlightV() {
        return flightV;
    }

    /**
     * 概览-机票
     */
    public void setFlightV(final String flightV) {
        this.flightV = flightV;
    }
    public String getFlightYoy() {
        return flightYoy;
    }

    public void setFlightYoy(final String flightYoy) {
        this.flightYoy = flightYoy;
    }
    public String getFlightMom() {
        return flightMom;
    }

    public void setFlightMom(final String flightMom) {
        this.flightMom = flightMom;
    }

    /**
     * 概览-酒店
     */
    public String getHotelV() {
        return hotelV;
    }

    /**
     * 概览-酒店
     */
    public void setHotelV(final String hotelV) {
        this.hotelV = hotelV;
    }
    public String getHotelYoy() {
        return hotelYoy;
    }

    public void setHotelYoy(final String hotelYoy) {
        this.hotelYoy = hotelYoy;
    }
    public String getHotelMom() {
        return hotelMom;
    }

    public void setHotelMom(final String hotelMom) {
        this.hotelMom = hotelMom;
    }

    /**
     * 概览-火车
     */
    public String getTrainV() {
        return trainV;
    }

    /**
     * 概览-火车
     */
    public void setTrainV(final String trainV) {
        this.trainV = trainV;
    }
    public String getTrainYoy() {
        return trainYoy;
    }

    public void setTrainYoy(final String trainYoy) {
        this.trainYoy = trainYoy;
    }
    public String getTrainMom() {
        return trainMom;
    }

    public void setTrainMom(final String trainMom) {
        this.trainMom = trainMom;
    }

    /**
     * 概览-用车
     */
    public String getCarV() {
        return carV;
    }

    /**
     * 概览-用车
     */
    public void setCarV(final String carV) {
        this.carV = carV;
    }
    public String getCarYoy() {
        return carYoy;
    }

    public void setCarYoy(final String carYoy) {
        this.carYoy = carYoy;
    }
    public String getCarMom() {
        return carMom;
    }

    public void setCarMom(final String carMom) {
        this.carMom = carMom;
    }

    /**
     * 概览-汽车
     */
    public String getBusV() {
        return busV;
    }

    /**
     * 概览-汽车
     */
    public void setBusV(final String busV) {
        this.busV = busV;
    }
    public String getBusYoy() {
        return busYoy;
    }

    public void setBusYoy(final String busYoy) {
        this.busYoy = busYoy;
    }
    public String getBusMom() {
        return busMom;
    }

    public void setBusMom(final String busMom) {
        this.busMom = busMom;
    }

    /**
     * 概览-增值
     */
    public String getAddV() {
        return addV;
    }

    /**
     * 概览-增值
     */
    public void setAddV(final String addV) {
        this.addV = addV;
    }
    public String getAddYoy() {
        return addYoy;
    }

    public void setAddYoy(final String addYoy) {
        this.addYoy = addYoy;
    }
    public String getAddMom() {
        return addMom;
    }

    public void setAddMom(final String addMom) {
        this.addMom = addMom;
    }

    /**
     * 总计
     */
    public String getTotalV() {
        return totalV;
    }

    /**
     * 总计
     */
    public void setTotalV(final String totalV) {
        this.totalV = totalV;
    }
    public String getTotalYoy() {
        return totalYoy;
    }

    public void setTotalYoy(final String totalYoy) {
        this.totalYoy = totalYoy;
    }
    public String getTotalMom() {
        return totalMom;
    }

    public void setTotalMom(final String totalMom) {
        this.totalMom = totalMom;
    }

    /**
     * 协议
     */
    public String getAgreementV() {
        return agreementV;
    }

    /**
     * 协议
     */
    public void setAgreementV(final String agreementV) {
        this.agreementV = agreementV;
    }
    public String getAgreementYoy() {
        return agreementYoy;
    }

    public void setAgreementYoy(final String agreementYoy) {
        this.agreementYoy = agreementYoy;
    }
    public String getAgreementMom() {
        return agreementMom;
    }

    public void setAgreementMom(final String agreementMom) {
        this.agreementMom = agreementMom;
    }

    /**
     * 非协议
     */
    public String getUnAgreementV() {
        return unAgreementV;
    }

    /**
     * 非协议
     */
    public void setUnAgreementV(final String unAgreementV) {
        this.unAgreementV = unAgreementV;
    }
    public String getUnAgreementYoy() {
        return unAgreementYoy;
    }

    public void setUnAgreementYoy(final String unAgreementYoy) {
        this.unAgreementYoy = unAgreementYoy;
    }
    public String getUnAgreementMom() {
        return unAgreementMom;
    }

    public void setUnAgreementMom(final String unAgreementMom) {
        this.unAgreementMom = unAgreementMom;
    }

    /**
     * 国内
     */
    public String getDomesticV() {
        return domesticV;
    }

    /**
     * 国内
     */
    public void setDomesticV(final String domesticV) {
        this.domesticV = domesticV;
    }
    public String getDomesticYoy() {
        return domesticYoy;
    }

    public void setDomesticYoy(final String domesticYoy) {
        this.domesticYoy = domesticYoy;
    }
    public String getDomesticMom() {
        return domesticMom;
    }

    public void setDomesticMom(final String domesticMom) {
        this.domesticMom = domesticMom;
    }

    /**
     * 国际
     */
    public String getInternationalV() {
        return internationalV;
    }

    /**
     * 国际
     */
    public void setInternationalV(final String internationalV) {
        this.internationalV = internationalV;
    }
    public String getInternationalYoy() {
        return internationalYoy;
    }

    public void setInternationalYoy(final String internationalYoy) {
        this.internationalYoy = internationalYoy;
    }
    public String getInternationalMom() {
        return internationalMom;
    }

    public void setInternationalMom(final String internationalMom) {
        this.internationalMom = internationalMom;
    }

    /**
     * 用车
     */
    public String getTakeCarV() {
        return takeCarV;
    }

    /**
     * 用车
     */
    public void setTakeCarV(final String takeCarV) {
        this.takeCarV = takeCarV;
    }

    /**
     * 用车
     */
    public String getTakeCarYoy() {
        return takeCarYoy;
    }

    /**
     * 用车
     */
    public void setTakeCarYoy(final String takeCarYoy) {
        this.takeCarYoy = takeCarYoy;
    }
    public String getTakeCarMom() {
        return takeCarMom;
    }

    public void setTakeCarMom(final String takeCarMom) {
        this.takeCarMom = takeCarMom;
    }

    /**
     * 国际用车
     */
    public String getTakeCarVInter() {
        return takeCarVInter;
    }

    /**
     * 国际用车
     */
    public void setTakeCarVInter(final String takeCarVInter) {
        this.takeCarVInter = takeCarVInter;
    }

    /**
     * 国际用车
     */
    public String getTakeCarYoyInter() {
        return takeCarYoyInter;
    }

    /**
     * 国际用车
     */
    public void setTakeCarYoyInter(final String takeCarYoyInter) {
        this.takeCarYoyInter = takeCarYoyInter;
    }
    public String getTakeCarMomInter() {
        return takeCarMomInter;
    }

    public void setTakeCarMomInter(final String takeCarMomInter) {
        this.takeCarMomInter = takeCarMomInter;
    }

    /**
     * 接送机
     */
    public String getTransferV() {
        return transferV;
    }

    /**
     * 接送机
     */
    public void setTransferV(final String transferV) {
        this.transferV = transferV;
    }

    /**
     * 接送机
     */
    public String getTransferYoy() {
        return transferYoy;
    }

    /**
     * 接送机
     */
    public void setTransferYoy(final String transferYoy) {
        this.transferYoy = transferYoy;
    }
    public String getTransferMom() {
        return transferMom;
    }

    public void setTransferMom(final String transferMom) {
        this.transferMom = transferMom;
    }

    /**
     * 国际接送机
     */
    public String getTransferVInter() {
        return transferVInter;
    }

    /**
     * 国际接送机
     */
    public void setTransferVInter(final String transferVInter) {
        this.transferVInter = transferVInter;
    }

    /**
     * 国际接送机
     */
    public String getTransferYoyInter() {
        return transferYoyInter;
    }

    /**
     * 国际接送机
     */
    public void setTransferYoyInter(final String transferYoyInter) {
        this.transferYoyInter = transferYoyInter;
    }
    public String getTransferMomInter() {
        return transferMomInter;
    }

    public void setTransferMomInter(final String transferMomInter) {
        this.transferMomInter = transferMomInter;
    }

    /**
     * 租车
     */
    public String getRentalCarV() {
        return rentalCarV;
    }

    /**
     * 租车
     */
    public void setRentalCarV(final String rentalCarV) {
        this.rentalCarV = rentalCarV;
    }

    /**
     * 租车
     */
    public String getRentalCarYoy() {
        return rentalCarYoy;
    }

    /**
     * 租车
     */
    public void setRentalCarYoy(final String rentalCarYoy) {
        this.rentalCarYoy = rentalCarYoy;
    }
    public String getRentalCarMom() {
        return rentalCarMom;
    }

    public void setRentalCarMom(final String rentalCarMom) {
        this.rentalCarMom = rentalCarMom;
    }

    /**
     * 国际租车
     */
    public String getRentalCarVInter() {
        return rentalCarVInter;
    }

    /**
     * 国际租车
     */
    public void setRentalCarVInter(final String rentalCarVInter) {
        this.rentalCarVInter = rentalCarVInter;
    }
    public String getRentalCarYoyInter() {
        return rentalCarYoyInter;
    }

    public void setRentalCarYoyInter(final String rentalCarYoyInter) {
        this.rentalCarYoyInter = rentalCarYoyInter;
    }
    public String getRentalCarMomInter() {
        return rentalCarMomInter;
    }

    public void setRentalCarMomInter(final String rentalCarMomInter) {
        this.rentalCarMomInter = rentalCarMomInter;
    }

    /**
     * 包车
     */
    public String getCharterCarV() {
        return charterCarV;
    }

    /**
     * 包车
     */
    public void setCharterCarV(final String charterCarV) {
        this.charterCarV = charterCarV;
    }

    /**
     * 包车
     */
    public String getCharterCarYoy() {
        return charterCarYoy;
    }

    /**
     * 包车
     */
    public void setCharterCarYoy(final String charterCarYoy) {
        this.charterCarYoy = charterCarYoy;
    }
    public String getCharterCarMom() {
        return charterCarMom;
    }

    public void setCharterCarMom(final String charterCarMom) {
        this.charterCarMom = charterCarMom;
    }

    /**
     * 机票-成交净价
     */
    public String getNetfare() {
        return netfare;
    }

    /**
     * 机票-成交净价
     */
    public void setNetfare(final String netfare) {
        this.netfare = netfare;
    }

    /**
     * 机票-机建税
     */
    public String getTax() {
        return tax;
    }

    /**
     * 机票-机建税
     */
    public void setTax(final String tax) {
        this.tax = tax;
    }

    /**
     * 机票-燃油费
     */
    public String getOilFee() {
        return oilFee;
    }

    /**
     * 机票-燃油费
     */
    public void setOilFee(final String oilFee) {
        this.oilFee = oilFee;
    }

    /**
     * 机票-配送费
     */
    public String getSendTicketFee() {
        return sendTicketFee;
    }

    /**
     * 机票-配送费
     */
    public void setSendTicketFee(final String sendTicketFee) {
        this.sendTicketFee = sendTicketFee;
    }

    /**
     * 机票-增值服务包费
     */
    public String getServicepackageFee() {
        return servicepackageFee;
    }

    /**
     * 机票-增值服务包费
     */
    public void setServicepackageFee(final String servicepackageFee) {
        this.servicepackageFee = servicepackageFee;
    }

    /**
     * 机票-绑定酒店优惠券
     */
    public String getBindAmount() {
        return bindAmount;
    }

    /**
     * 机票-绑定酒店优惠券
     */
    public void setBindAmount(final String bindAmount) {
        this.bindAmount = bindAmount;
    }

    /**
     * 机票-改签费
     */
    public String getChangeFee() {
        return changeFee;
    }

    /**
     * 机票-改签费
     */
    public void setChangeFee(final String changeFee) {
        this.changeFee = changeFee;
    }

    /**
     * 机票-改签燃油差
     */
    public String getOilfeedifferential() {
        return oilfeedifferential;
    }

    /**
     * 机票-改签燃油差
     */
    public void setOilfeedifferential(final String oilfeedifferential) {
        this.oilfeedifferential = oilfeedifferential;
    }

    /**
     * 机票-税差
     */
    public String getTaxDifferential() {
        return taxDifferential;
    }

    /**
     * 机票-税差
     */
    public void setTaxDifferential(final String taxDifferential) {
        this.taxDifferential = taxDifferential;
    }

    /**
     * 机票-改签差价
     */
    public String getRebookPriceDifferential() {
        return rebookPriceDifferential;
    }

    /**
     * 机票-改签差价
     */
    public void setRebookPriceDifferential(final String rebookPriceDifferential) {
        this.rebookPriceDifferential = rebookPriceDifferential;
    }

    /**
     * 机票-改签商旅管理服务费
     */
    public String getRebookServiceFee() {
        return rebookServiceFee;
    }

    /**
     * 机票-改签商旅管理服务费
     */
    public void setRebookServiceFee(final String rebookServiceFee) {
        this.rebookServiceFee = rebookServiceFee;
    }

    /**
     * 机票-退票费
     */
    public String getRefundFee() {
        return refundFee;
    }

    /**
     * 机票-退票费
     */
    public void setRefundFee(final String refundFee) {
        this.refundFee = refundFee;
    }

    /**
     * 机票-退票商旅管理服务费
     */
    public String getRefundServiceFee() {
        return refundServiceFee;
    }

    /**
     * 机票-退票商旅管理服务费
     */
    public void setRefundServiceFee(final String refundServiceFee) {
        this.refundServiceFee = refundServiceFee;
    }

    /**
     * 机票-退票行程单商旅管理服务费
     */
    public String getRefundItineraryFee() {
        return refundItineraryFee;
    }

    /**
     * 机票-退票行程单商旅管理服务费
     */
    public void setRefundItineraryFee(final String refundItineraryFee) {
        this.refundItineraryFee = refundItineraryFee;
    }

    /**
     * 机票-后收商旅管理服务费
     */
    public String getTicketBehindServicefee() {
        return ticketBehindServicefee;
    }

    /**
     * 机票-后收商旅管理服务费
     */
    public void setTicketBehindServicefee(final String ticketBehindServicefee) {
        this.ticketBehindServicefee = ticketBehindServicefee;
    }

    /**
     * 机票-后收改签商旅管理服务费
     */
    public String getRebookBehindServiceFee() {
        return rebookBehindServiceFee;
    }

    /**
     * 机票-后收改签商旅管理服务费
     */
    public void setRebookBehindServiceFee(final String rebookBehindServiceFee) {
        this.rebookBehindServiceFee = rebookBehindServiceFee;
    }

    /**
     * 机票-后收退票商旅管理服务费
     */
    public String getRefundBehindServiceFee() {
        return refundBehindServiceFee;
    }

    /**
     * 机票-后收退票商旅管理服务费
     */
    public void setRefundBehindServiceFee(final String refundBehindServiceFee) {
        this.refundBehindServiceFee = refundBehindServiceFee;
    }

    /**
     * 酒店-房价
     */
    public String getRoomPrice() {
        return roomPrice;
    }

    /**
     * 酒店-房价
     */
    public void setRoomPrice(final String roomPrice) {
        this.roomPrice = roomPrice;
    }

    /**
     * 酒店-酒店优惠券
     */
    public String getCouponAmount() {
        return couponAmount;
    }

    /**
     * 酒店-酒店优惠券
     */
    public void setCouponAmount(final String couponAmount) {
        this.couponAmount = couponAmount;
    }

    /**
     * 酒店-后收商旅管理服务费
     */
    public String getHotelPostServiceFee() {
        return hotelPostServiceFee;
    }

    /**
     * 酒店-后收商旅管理服务费
     */
    public void setHotelPostServiceFee(final String hotelPostServiceFee) {
        this.hotelPostServiceFee = hotelPostServiceFee;
    }

    /**
     * 原始出票金额
     */
    public String getTicketPrice() {
        return ticketPrice;
    }

    /**
     * 原始出票金额
     */
    public void setTicketPrice(final String ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    /**
     * 配送费
     * 改签差价
     * 基础商旅管理服务费
     */
    public String getDeliverFee() {
        return deliverFee;
    }

    /**
     * 配送费
     * 改签差价
     * 基础商旅管理服务费
     */
    public void setDeliverFee(final String deliverFee) {
        this.deliverFee = deliverFee;
    }

    /**
     * 抢票费
     */
    public String getGrabServiceFee() {
        return grabServiceFee;
    }

    /**
     * 抢票费
     */
    public void setGrabServiceFee(final String grabServiceFee) {
        this.grabServiceFee = grabServiceFee;
    }

    /**
     * 纸质出票费
     */
    public String getPaperTicketFee() {
        return paperTicketFee;
    }

    /**
     * 纸质出票费
     */
    public void setPaperTicketFee(final String paperTicketFee) {
        this.paperTicketFee = paperTicketFee;
    }

    /**
     * 后收商旅管理服务费
     * 改签商旅管理服务费
     * 代取人工费
     * 退票金额
     */
    public String getAfterServiceFee() {
        return afterServiceFee;
    }

    /**
     * 后收商旅管理服务费
     * 改签商旅管理服务费
     * 代取人工费
     * 退票金额
     */
    public void setAfterServiceFee(final String afterServiceFee) {
        this.afterServiceFee = afterServiceFee;
    }

    /**
     * 后收改签商旅管理服务费
     */
    public String getAfterChangeServiceFee() {
        return afterChangeServiceFee;
    }

    /**
     * 后收改签商旅管理服务费
     */
    public void setAfterChangeServiceFee(final String afterChangeServiceFee) {
        this.afterChangeServiceFee = afterChangeServiceFee;
    }

    /**
     * 后收代取人工费
     */
    public String getAfterTakeTicketFee() {
        return afterTakeTicketFee;
    }

    /**
     * 后收代取人工费
     */
    public void setAfterTakeTicketFee(final String afterTakeTicketFee) {
        this.afterTakeTicketFee = afterTakeTicketFee;
    }

    /**
     * 用车-基础费用
     */
    public String getBasicFee() {
        return basicFee;
    }

    /**
     * 用车-基础费用
     */
    public void setBasicFee(final String basicFee) {
        this.basicFee = basicFee;
    }

    /**
     * 用车-退款金额
     */
    public String getRefundAmount() {
        return refundAmount;
    }

    /**
     * 用车-退款金额
     */
    public void setRefundAmount(final String refundAmount) {
        this.refundAmount = refundAmount;
    }

    /**
     * 机票/酒店/火车/用车-公用字段-商旅管理服务费
     */
    public String getServiceFee() {
        return serviceFee;
    }

    /**
     * 机票/酒店/火车/用车-公用字段-商旅管理服务费
     */
    public void setServiceFee(final String serviceFee) {
        this.serviceFee = serviceFee;
    }

    /**
     * 机票/火车-公用字段-保险费
     */
    public String getInsuranceFee() {
        return insuranceFee;
    }

    /**
     * 机票/火车-公用字段-保险费
     */
    public void setInsuranceFee(final String insuranceFee) {
        this.insuranceFee = insuranceFee;
    }

    /**
     * 机票/酒店/火车/用车-公用字段-票张
     */
    public String getQuantity() {
        return quantity;
    }

    /**
     * 机票/酒店/火车/用车-公用字段-票张
     */
    public void setQuantity(final String quantity) {
        this.quantity = quantity;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final ReportDataDetailData other = (ReportDataDetailData)obj;
        return
            Objects.equal(this.flightV, other.flightV) &&
            Objects.equal(this.flightYoy, other.flightYoy) &&
            Objects.equal(this.flightMom, other.flightMom) &&
            Objects.equal(this.hotelV, other.hotelV) &&
            Objects.equal(this.hotelYoy, other.hotelYoy) &&
            Objects.equal(this.hotelMom, other.hotelMom) &&
            Objects.equal(this.trainV, other.trainV) &&
            Objects.equal(this.trainYoy, other.trainYoy) &&
            Objects.equal(this.trainMom, other.trainMom) &&
            Objects.equal(this.carV, other.carV) &&
            Objects.equal(this.carYoy, other.carYoy) &&
            Objects.equal(this.carMom, other.carMom) &&
            Objects.equal(this.busV, other.busV) &&
            Objects.equal(this.busYoy, other.busYoy) &&
            Objects.equal(this.busMom, other.busMom) &&
            Objects.equal(this.addV, other.addV) &&
            Objects.equal(this.addYoy, other.addYoy) &&
            Objects.equal(this.addMom, other.addMom) &&
            Objects.equal(this.totalV, other.totalV) &&
            Objects.equal(this.totalYoy, other.totalYoy) &&
            Objects.equal(this.totalMom, other.totalMom) &&
            Objects.equal(this.agreementV, other.agreementV) &&
            Objects.equal(this.agreementYoy, other.agreementYoy) &&
            Objects.equal(this.agreementMom, other.agreementMom) &&
            Objects.equal(this.unAgreementV, other.unAgreementV) &&
            Objects.equal(this.unAgreementYoy, other.unAgreementYoy) &&
            Objects.equal(this.unAgreementMom, other.unAgreementMom) &&
            Objects.equal(this.domesticV, other.domesticV) &&
            Objects.equal(this.domesticYoy, other.domesticYoy) &&
            Objects.equal(this.domesticMom, other.domesticMom) &&
            Objects.equal(this.internationalV, other.internationalV) &&
            Objects.equal(this.internationalYoy, other.internationalYoy) &&
            Objects.equal(this.internationalMom, other.internationalMom) &&
            Objects.equal(this.takeCarV, other.takeCarV) &&
            Objects.equal(this.takeCarYoy, other.takeCarYoy) &&
            Objects.equal(this.takeCarMom, other.takeCarMom) &&
            Objects.equal(this.takeCarVInter, other.takeCarVInter) &&
            Objects.equal(this.takeCarYoyInter, other.takeCarYoyInter) &&
            Objects.equal(this.takeCarMomInter, other.takeCarMomInter) &&
            Objects.equal(this.transferV, other.transferV) &&
            Objects.equal(this.transferYoy, other.transferYoy) &&
            Objects.equal(this.transferMom, other.transferMom) &&
            Objects.equal(this.transferVInter, other.transferVInter) &&
            Objects.equal(this.transferYoyInter, other.transferYoyInter) &&
            Objects.equal(this.transferMomInter, other.transferMomInter) &&
            Objects.equal(this.rentalCarV, other.rentalCarV) &&
            Objects.equal(this.rentalCarYoy, other.rentalCarYoy) &&
            Objects.equal(this.rentalCarMom, other.rentalCarMom) &&
            Objects.equal(this.rentalCarVInter, other.rentalCarVInter) &&
            Objects.equal(this.rentalCarYoyInter, other.rentalCarYoyInter) &&
            Objects.equal(this.rentalCarMomInter, other.rentalCarMomInter) &&
            Objects.equal(this.charterCarV, other.charterCarV) &&
            Objects.equal(this.charterCarYoy, other.charterCarYoy) &&
            Objects.equal(this.charterCarMom, other.charterCarMom) &&
            Objects.equal(this.netfare, other.netfare) &&
            Objects.equal(this.tax, other.tax) &&
            Objects.equal(this.oilFee, other.oilFee) &&
            Objects.equal(this.sendTicketFee, other.sendTicketFee) &&
            Objects.equal(this.servicepackageFee, other.servicepackageFee) &&
            Objects.equal(this.bindAmount, other.bindAmount) &&
            Objects.equal(this.changeFee, other.changeFee) &&
            Objects.equal(this.oilfeedifferential, other.oilfeedifferential) &&
            Objects.equal(this.taxDifferential, other.taxDifferential) &&
            Objects.equal(this.rebookPriceDifferential, other.rebookPriceDifferential) &&
            Objects.equal(this.rebookServiceFee, other.rebookServiceFee) &&
            Objects.equal(this.refundFee, other.refundFee) &&
            Objects.equal(this.refundServiceFee, other.refundServiceFee) &&
            Objects.equal(this.refundItineraryFee, other.refundItineraryFee) &&
            Objects.equal(this.ticketBehindServicefee, other.ticketBehindServicefee) &&
            Objects.equal(this.rebookBehindServiceFee, other.rebookBehindServiceFee) &&
            Objects.equal(this.refundBehindServiceFee, other.refundBehindServiceFee) &&
            Objects.equal(this.roomPrice, other.roomPrice) &&
            Objects.equal(this.couponAmount, other.couponAmount) &&
            Objects.equal(this.hotelPostServiceFee, other.hotelPostServiceFee) &&
            Objects.equal(this.ticketPrice, other.ticketPrice) &&
            Objects.equal(this.deliverFee, other.deliverFee) &&
            Objects.equal(this.grabServiceFee, other.grabServiceFee) &&
            Objects.equal(this.paperTicketFee, other.paperTicketFee) &&
            Objects.equal(this.afterServiceFee, other.afterServiceFee) &&
            Objects.equal(this.afterChangeServiceFee, other.afterChangeServiceFee) &&
            Objects.equal(this.afterTakeTicketFee, other.afterTakeTicketFee) &&
            Objects.equal(this.basicFee, other.basicFee) &&
            Objects.equal(this.refundAmount, other.refundAmount) &&
            Objects.equal(this.serviceFee, other.serviceFee) &&
            Objects.equal(this.insuranceFee, other.insuranceFee) &&
            Objects.equal(this.quantity, other.quantity);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.flightV == null ? 0 : this.flightV.hashCode());
        result = 31 * result + (this.flightYoy == null ? 0 : this.flightYoy.hashCode());
        result = 31 * result + (this.flightMom == null ? 0 : this.flightMom.hashCode());
        result = 31 * result + (this.hotelV == null ? 0 : this.hotelV.hashCode());
        result = 31 * result + (this.hotelYoy == null ? 0 : this.hotelYoy.hashCode());
        result = 31 * result + (this.hotelMom == null ? 0 : this.hotelMom.hashCode());
        result = 31 * result + (this.trainV == null ? 0 : this.trainV.hashCode());
        result = 31 * result + (this.trainYoy == null ? 0 : this.trainYoy.hashCode());
        result = 31 * result + (this.trainMom == null ? 0 : this.trainMom.hashCode());
        result = 31 * result + (this.carV == null ? 0 : this.carV.hashCode());
        result = 31 * result + (this.carYoy == null ? 0 : this.carYoy.hashCode());
        result = 31 * result + (this.carMom == null ? 0 : this.carMom.hashCode());
        result = 31 * result + (this.busV == null ? 0 : this.busV.hashCode());
        result = 31 * result + (this.busYoy == null ? 0 : this.busYoy.hashCode());
        result = 31 * result + (this.busMom == null ? 0 : this.busMom.hashCode());
        result = 31 * result + (this.addV == null ? 0 : this.addV.hashCode());
        result = 31 * result + (this.addYoy == null ? 0 : this.addYoy.hashCode());
        result = 31 * result + (this.addMom == null ? 0 : this.addMom.hashCode());
        result = 31 * result + (this.totalV == null ? 0 : this.totalV.hashCode());
        result = 31 * result + (this.totalYoy == null ? 0 : this.totalYoy.hashCode());
        result = 31 * result + (this.totalMom == null ? 0 : this.totalMom.hashCode());
        result = 31 * result + (this.agreementV == null ? 0 : this.agreementV.hashCode());
        result = 31 * result + (this.agreementYoy == null ? 0 : this.agreementYoy.hashCode());
        result = 31 * result + (this.agreementMom == null ? 0 : this.agreementMom.hashCode());
        result = 31 * result + (this.unAgreementV == null ? 0 : this.unAgreementV.hashCode());
        result = 31 * result + (this.unAgreementYoy == null ? 0 : this.unAgreementYoy.hashCode());
        result = 31 * result + (this.unAgreementMom == null ? 0 : this.unAgreementMom.hashCode());
        result = 31 * result + (this.domesticV == null ? 0 : this.domesticV.hashCode());
        result = 31 * result + (this.domesticYoy == null ? 0 : this.domesticYoy.hashCode());
        result = 31 * result + (this.domesticMom == null ? 0 : this.domesticMom.hashCode());
        result = 31 * result + (this.internationalV == null ? 0 : this.internationalV.hashCode());
        result = 31 * result + (this.internationalYoy == null ? 0 : this.internationalYoy.hashCode());
        result = 31 * result + (this.internationalMom == null ? 0 : this.internationalMom.hashCode());
        result = 31 * result + (this.takeCarV == null ? 0 : this.takeCarV.hashCode());
        result = 31 * result + (this.takeCarYoy == null ? 0 : this.takeCarYoy.hashCode());
        result = 31 * result + (this.takeCarMom == null ? 0 : this.takeCarMom.hashCode());
        result = 31 * result + (this.takeCarVInter == null ? 0 : this.takeCarVInter.hashCode());
        result = 31 * result + (this.takeCarYoyInter == null ? 0 : this.takeCarYoyInter.hashCode());
        result = 31 * result + (this.takeCarMomInter == null ? 0 : this.takeCarMomInter.hashCode());
        result = 31 * result + (this.transferV == null ? 0 : this.transferV.hashCode());
        result = 31 * result + (this.transferYoy == null ? 0 : this.transferYoy.hashCode());
        result = 31 * result + (this.transferMom == null ? 0 : this.transferMom.hashCode());
        result = 31 * result + (this.transferVInter == null ? 0 : this.transferVInter.hashCode());
        result = 31 * result + (this.transferYoyInter == null ? 0 : this.transferYoyInter.hashCode());
        result = 31 * result + (this.transferMomInter == null ? 0 : this.transferMomInter.hashCode());
        result = 31 * result + (this.rentalCarV == null ? 0 : this.rentalCarV.hashCode());
        result = 31 * result + (this.rentalCarYoy == null ? 0 : this.rentalCarYoy.hashCode());
        result = 31 * result + (this.rentalCarMom == null ? 0 : this.rentalCarMom.hashCode());
        result = 31 * result + (this.rentalCarVInter == null ? 0 : this.rentalCarVInter.hashCode());
        result = 31 * result + (this.rentalCarYoyInter == null ? 0 : this.rentalCarYoyInter.hashCode());
        result = 31 * result + (this.rentalCarMomInter == null ? 0 : this.rentalCarMomInter.hashCode());
        result = 31 * result + (this.charterCarV == null ? 0 : this.charterCarV.hashCode());
        result = 31 * result + (this.charterCarYoy == null ? 0 : this.charterCarYoy.hashCode());
        result = 31 * result + (this.charterCarMom == null ? 0 : this.charterCarMom.hashCode());
        result = 31 * result + (this.netfare == null ? 0 : this.netfare.hashCode());
        result = 31 * result + (this.tax == null ? 0 : this.tax.hashCode());
        result = 31 * result + (this.oilFee == null ? 0 : this.oilFee.hashCode());
        result = 31 * result + (this.sendTicketFee == null ? 0 : this.sendTicketFee.hashCode());
        result = 31 * result + (this.servicepackageFee == null ? 0 : this.servicepackageFee.hashCode());
        result = 31 * result + (this.bindAmount == null ? 0 : this.bindAmount.hashCode());
        result = 31 * result + (this.changeFee == null ? 0 : this.changeFee.hashCode());
        result = 31 * result + (this.oilfeedifferential == null ? 0 : this.oilfeedifferential.hashCode());
        result = 31 * result + (this.taxDifferential == null ? 0 : this.taxDifferential.hashCode());
        result = 31 * result + (this.rebookPriceDifferential == null ? 0 : this.rebookPriceDifferential.hashCode());
        result = 31 * result + (this.rebookServiceFee == null ? 0 : this.rebookServiceFee.hashCode());
        result = 31 * result + (this.refundFee == null ? 0 : this.refundFee.hashCode());
        result = 31 * result + (this.refundServiceFee == null ? 0 : this.refundServiceFee.hashCode());
        result = 31 * result + (this.refundItineraryFee == null ? 0 : this.refundItineraryFee.hashCode());
        result = 31 * result + (this.ticketBehindServicefee == null ? 0 : this.ticketBehindServicefee.hashCode());
        result = 31 * result + (this.rebookBehindServiceFee == null ? 0 : this.rebookBehindServiceFee.hashCode());
        result = 31 * result + (this.refundBehindServiceFee == null ? 0 : this.refundBehindServiceFee.hashCode());
        result = 31 * result + (this.roomPrice == null ? 0 : this.roomPrice.hashCode());
        result = 31 * result + (this.couponAmount == null ? 0 : this.couponAmount.hashCode());
        result = 31 * result + (this.hotelPostServiceFee == null ? 0 : this.hotelPostServiceFee.hashCode());
        result = 31 * result + (this.ticketPrice == null ? 0 : this.ticketPrice.hashCode());
        result = 31 * result + (this.deliverFee == null ? 0 : this.deliverFee.hashCode());
        result = 31 * result + (this.grabServiceFee == null ? 0 : this.grabServiceFee.hashCode());
        result = 31 * result + (this.paperTicketFee == null ? 0 : this.paperTicketFee.hashCode());
        result = 31 * result + (this.afterServiceFee == null ? 0 : this.afterServiceFee.hashCode());
        result = 31 * result + (this.afterChangeServiceFee == null ? 0 : this.afterChangeServiceFee.hashCode());
        result = 31 * result + (this.afterTakeTicketFee == null ? 0 : this.afterTakeTicketFee.hashCode());
        result = 31 * result + (this.basicFee == null ? 0 : this.basicFee.hashCode());
        result = 31 * result + (this.refundAmount == null ? 0 : this.refundAmount.hashCode());
        result = 31 * result + (this.serviceFee == null ? 0 : this.serviceFee.hashCode());
        result = 31 * result + (this.insuranceFee == null ? 0 : this.insuranceFee.hashCode());
        result = 31 * result + (this.quantity == null ? 0 : this.quantity.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("flightV", flightV)
            .add("flightYoy", flightYoy)
            .add("flightMom", flightMom)
            .add("hotelV", hotelV)
            .add("hotelYoy", hotelYoy)
            .add("hotelMom", hotelMom)
            .add("trainV", trainV)
            .add("trainYoy", trainYoy)
            .add("trainMom", trainMom)
            .add("carV", carV)
            .add("carYoy", carYoy)
            .add("carMom", carMom)
            .add("busV", busV)
            .add("busYoy", busYoy)
            .add("busMom", busMom)
            .add("addV", addV)
            .add("addYoy", addYoy)
            .add("addMom", addMom)
            .add("totalV", totalV)
            .add("totalYoy", totalYoy)
            .add("totalMom", totalMom)
            .add("agreementV", agreementV)
            .add("agreementYoy", agreementYoy)
            .add("agreementMom", agreementMom)
            .add("unAgreementV", unAgreementV)
            .add("unAgreementYoy", unAgreementYoy)
            .add("unAgreementMom", unAgreementMom)
            .add("domesticV", domesticV)
            .add("domesticYoy", domesticYoy)
            .add("domesticMom", domesticMom)
            .add("internationalV", internationalV)
            .add("internationalYoy", internationalYoy)
            .add("internationalMom", internationalMom)
            .add("takeCarV", takeCarV)
            .add("takeCarYoy", takeCarYoy)
            .add("takeCarMom", takeCarMom)
            .add("takeCarVInter", takeCarVInter)
            .add("takeCarYoyInter", takeCarYoyInter)
            .add("takeCarMomInter", takeCarMomInter)
            .add("transferV", transferV)
            .add("transferYoy", transferYoy)
            .add("transferMom", transferMom)
            .add("transferVInter", transferVInter)
            .add("transferYoyInter", transferYoyInter)
            .add("transferMomInter", transferMomInter)
            .add("rentalCarV", rentalCarV)
            .add("rentalCarYoy", rentalCarYoy)
            .add("rentalCarMom", rentalCarMom)
            .add("rentalCarVInter", rentalCarVInter)
            .add("rentalCarYoyInter", rentalCarYoyInter)
            .add("rentalCarMomInter", rentalCarMomInter)
            .add("charterCarV", charterCarV)
            .add("charterCarYoy", charterCarYoy)
            .add("charterCarMom", charterCarMom)
            .add("netfare", netfare)
            .add("tax", tax)
            .add("oilFee", oilFee)
            .add("sendTicketFee", sendTicketFee)
            .add("servicepackageFee", servicepackageFee)
            .add("bindAmount", bindAmount)
            .add("changeFee", changeFee)
            .add("oilfeedifferential", oilfeedifferential)
            .add("taxDifferential", taxDifferential)
            .add("rebookPriceDifferential", rebookPriceDifferential)
            .add("rebookServiceFee", rebookServiceFee)
            .add("refundFee", refundFee)
            .add("refundServiceFee", refundServiceFee)
            .add("refundItineraryFee", refundItineraryFee)
            .add("ticketBehindServicefee", ticketBehindServicefee)
            .add("rebookBehindServiceFee", rebookBehindServiceFee)
            .add("refundBehindServiceFee", refundBehindServiceFee)
            .add("roomPrice", roomPrice)
            .add("couponAmount", couponAmount)
            .add("hotelPostServiceFee", hotelPostServiceFee)
            .add("ticketPrice", ticketPrice)
            .add("deliverFee", deliverFee)
            .add("grabServiceFee", grabServiceFee)
            .add("paperTicketFee", paperTicketFee)
            .add("afterServiceFee", afterServiceFee)
            .add("afterChangeServiceFee", afterChangeServiceFee)
            .add("afterTakeTicketFee", afterTakeTicketFee)
            .add("basicFee", basicFee)
            .add("refundAmount", refundAmount)
            .add("serviceFee", serviceFee)
            .add("insuranceFee", insuranceFee)
            .add("quantity", quantity)
            .toString();
    }
}
