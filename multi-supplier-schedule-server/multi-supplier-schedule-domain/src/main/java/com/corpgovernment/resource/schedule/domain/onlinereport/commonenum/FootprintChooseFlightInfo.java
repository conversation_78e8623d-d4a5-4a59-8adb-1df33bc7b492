package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "name",
    "quantity",
    "amount",
    "fltMostAirline",
    "fltMostDcityacity",
    "fltMostTimerange",
    "fltQtyMostAirline",
    "fltQtyRate"
})
public class FootprintChooseFlightInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public FootprintChooseFlightInfo(
        String name,
        Integer quantity,
        BigDecimal amount,
        String fltMostAirline,
        String fltMostDcityacity,
        String fltMostTimerange,
        String fltQtyMostAirline,
        BigDecimal fltQtyRate) {
        this.name = name;
        this.quantity = quantity;
        this.amount = amount;
        this.fltMostAirline = fltMostAirline;
        this.fltMostDcityacity = fltMostDcityacity;
        this.fltMostTimerange = fltMostTimerange;
        this.fltQtyMostAirline = fltQtyMostAirline;
        this.fltQtyRate = fltQtyRate;
    }

    public FootprintChooseFlightInfo() {
    }

    /**
     * 名称
     */
    @JsonProperty("name")
    public String name;

    /**
     * 张、夜、次
     */
    @JsonProperty("quantity")
    public Integer quantity;

    /**
     * 金额
     */
    @JsonProperty("amount")
    public BigDecimal amount;

    /**
     * 偏爱航司(最常乘坐的航司)
     */
    @JsonProperty("fltMostAirline")
    public String fltMostAirline;

    /**
     * 最常乘坐的航线
     */
    @JsonProperty("fltMostDcityacity")
    public String fltMostDcityacity;

    /**
     * 偏爱时段(最常出行的时段)
     */
    @JsonProperty("fltMostTimerange")
    public String fltMostTimerange;

    /**
     * 偏爱航司次数(最常乘坐的航司乘坐次数)
     */
    @JsonProperty("fltQtyMostAirline")
    public String fltQtyMostAirline;

    /**
     * 机票张数数公司排行超越人数百分比
     */
    @JsonProperty("fltQtyRate")
    public BigDecimal fltQtyRate;

    /**
     * 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 名称
     */
    public void setName(final String name) {
        this.name = name;
    }

    /**
     * 张、夜、次
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 张、夜、次
     */
    public void setQuantity(final Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * 金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 金额
     */
    public void setAmount(final BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 偏爱航司(最常乘坐的航司)
     */
    public String getFltMostAirline() {
        return fltMostAirline;
    }

    /**
     * 偏爱航司(最常乘坐的航司)
     */
    public void setFltMostAirline(final String fltMostAirline) {
        this.fltMostAirline = fltMostAirline;
    }

    /**
     * 最常乘坐的航线
     */
    public String getFltMostDcityacity() {
        return fltMostDcityacity;
    }

    /**
     * 最常乘坐的航线
     */
    public void setFltMostDcityacity(final String fltMostDcityacity) {
        this.fltMostDcityacity = fltMostDcityacity;
    }

    /**
     * 偏爱时段(最常出行的时段)
     */
    public String getFltMostTimerange() {
        return fltMostTimerange;
    }

    /**
     * 偏爱时段(最常出行的时段)
     */
    public void setFltMostTimerange(final String fltMostTimerange) {
        this.fltMostTimerange = fltMostTimerange;
    }

    /**
     * 偏爱航司次数(最常乘坐的航司乘坐次数)
     */
    public String getFltQtyMostAirline() {
        return fltQtyMostAirline;
    }

    /**
     * 偏爱航司次数(最常乘坐的航司乘坐次数)
     */
    public void setFltQtyMostAirline(final String fltQtyMostAirline) {
        this.fltQtyMostAirline = fltQtyMostAirline;
    }

    /**
     * 机票张数数公司排行超越人数百分比
     */
    public BigDecimal getFltQtyRate() {
        return fltQtyRate;
    }

    /**
     * 机票张数数公司排行超越人数百分比
     */
    public void setFltQtyRate(final BigDecimal fltQtyRate) {
        this.fltQtyRate = fltQtyRate;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FootprintChooseFlightInfo other = (FootprintChooseFlightInfo)obj;
        return
            Objects.equal(this.name, other.name) &&
            Objects.equal(this.quantity, other.quantity) &&
            Objects.equal(this.amount, other.amount) &&
            Objects.equal(this.fltMostAirline, other.fltMostAirline) &&
            Objects.equal(this.fltMostDcityacity, other.fltMostDcityacity) &&
            Objects.equal(this.fltMostTimerange, other.fltMostTimerange) &&
            Objects.equal(this.fltQtyMostAirline, other.fltQtyMostAirline) &&
            Objects.equal(this.fltQtyRate, other.fltQtyRate);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.name == null ? 0 : this.name.hashCode());
        result = 31 * result + (this.quantity == null ? 0 : this.quantity.hashCode());
        result = 31 * result + (this.amount == null ? 0 : this.amount.hashCode());
        result = 31 * result + (this.fltMostAirline == null ? 0 : this.fltMostAirline.hashCode());
        result = 31 * result + (this.fltMostDcityacity == null ? 0 : this.fltMostDcityacity.hashCode());
        result = 31 * result + (this.fltMostTimerange == null ? 0 : this.fltMostTimerange.hashCode());
        result = 31 * result + (this.fltQtyMostAirline == null ? 0 : this.fltQtyMostAirline.hashCode());
        result = 31 * result + (this.fltQtyRate == null ? 0 : this.fltQtyRate.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("name", name)
            .add("quantity", quantity)
            .add("amount", amount)
            .add("fltMostAirline", fltMostAirline)
            .add("fltMostDcityacity", fltMostDcityacity)
            .add("fltMostTimerange", fltMostTimerange)
            .add("fltQtyMostAirline", fltQtyMostAirline)
            .add("fltQtyRate", fltQtyRate)
            .toString();
    }
}
