package com.corpgovernment.resource.schedule.domain.onlinereport.compliance;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2022/4/8 10:44
 * @Desc
 */
@Data
public class RcDetailDTO {
    @Column(name = "rcTimes")
    @Type(value = Types.INTEGER)
    private Integer rcTimes;
    @Column(name = "rcCode")
    @Type(value = Types.VARCHAR)
    private String rcCode;
    @Column(name = "rcDesc")
    @Type(value = Types.VARCHAR)
    private String rcDesc;
}
