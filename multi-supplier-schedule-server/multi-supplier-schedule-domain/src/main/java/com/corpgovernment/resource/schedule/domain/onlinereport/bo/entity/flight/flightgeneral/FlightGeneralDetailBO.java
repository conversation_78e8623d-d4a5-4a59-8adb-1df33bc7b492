package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight.flightgeneral;

/**
 * @Auther: zzd
 * @Date: 2019/6/6
 * @Description: 机票总概
 */
public class FlightGeneralDetailBO {

    public String amount;
    public String yearOnYear;
    public String monthOnMonth;
    public String domAvgPrice;
    public Integer domQuantity;
    public String intAvgPrice;
    public Integer intQuantity;
    public String domMileage;
    public String intMileage;
    public String mileage;

    public String getAmount() {
        return this.amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getYearOnYear() {
        return this.yearOnYear;
    }

    public void setYearOnYear(String yearOnYear) {
        this.yearOnYear = yearOnYear;
    }

    public String getMonthOnMonth() {
        return this.monthOnMonth;
    }

    public void setMonthOnMonth(String monthOnMonth) {
        this.monthOnMonth = monthOnMonth;
    }

    public String getDomAvgPrice() {
        return this.domAvgPrice;
    }

    public void setDomAvgPrice(String domAvgPrice) {
        this.domAvgPrice = domAvgPrice;
    }

    public Integer getDomQuantity() {
        return this.domQuantity;
    }

    public void setDomQuantity(Integer domQuantity) {
        this.domQuantity = domQuantity;
    }

    public String getIntAvgPrice() {
        return this.intAvgPrice;
    }

    public void setIntAvgPrice(String intAvgPrice) {
        this.intAvgPrice = intAvgPrice;
    }

    public Integer getIntQuantity() {
        return this.intQuantity;
    }

    public void setIntQuantity(Integer intQuantity) {
        this.intQuantity = intQuantity;
    }

    public String getDomMileage() {
        return this.domMileage;
    }

    public void setDomMileage(String domMileage) {
        this.domMileage = domMileage;
    }

    public String getIntMileage() {
        return this.intMileage;
    }

    public void setIntMileage(String intMileage) {
        this.intMileage = intMileage;
    }

    public String getMileage() {
        return this.mileage;
    }

    public void setMileage(String mileage) {
        this.mileage = mileage;
    }
}
