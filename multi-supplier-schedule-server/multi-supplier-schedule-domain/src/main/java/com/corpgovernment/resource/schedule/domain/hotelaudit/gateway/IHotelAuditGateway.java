package com.corpgovernment.resource.schedule.domain.hotelaudit.gateway;

import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-23 16:21
 */
public interface IHotelAuditGateway {
    
    List<OrderInfo> getHotelOrderInfoList(String startTime, String endTime);
    
    List<OrderInfo> getHotelIntlOrderInfoList(String startTime, String endTime);
    
    List<OrderInfo> getHotelOrderInfoList(List<String> orderIdList);
    
    List<OrderInfo> getHotelIntlOrderInfoList(List<String> orderIdList);
    
    TravelApplication getTravelApplication(String token);
    
    TravelStandard getTravelStandard(String token);
    
    List<GuestInfo> getGuestInfoList(String token);
    
    GuestInfo getPolicyExecutor(String token);
    
    HotelRoomInfo getHotelRoomInfo(String token);

    OrderTravelStandardResult getOrderTravelStandardResult(String token);
    
}
