package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 节省
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "totalCarbonSave",
    "avgMonthCarbonSave",
    "yoy",
    "mom",
    "fltCarbonSave",
    "fltGreenPercentage",
    "fltSaveRate",
    "fltCorpGreenPercentage",
    "fltCorpSaveRate",
    "fltIndustryGreenPercentage",
    "fltIndustrySaveRate",
    "htlCarbonSave",
    "htlGreenPercentage",
    "htlSaveRate",
    "htlCorpGreenPercentage",
    "htlCorpSaveRate",
    "htlIndustryGreenPercentage",
    "htlIndustrySaveRate",
    "trainCarbonSave",
    "trainGreenPercentage",
    "trainSaveRate",
    "trainCorpGreenPercentage",
    "trainCorpSaveRate",
    "trainIndustryGreenPercentage",
    "trainIndustrySaveRate",
    "carCarbonSave",
    "carGreenPercentage",
    "carSaveRate",
    "carAvgMileCarbons",
    "carCorpGreenPercentage",
    "carCorpSaveRate",
    "carCorpAvgMileCarbons",
    "carIndustryGreenPercentage",
    "carIndustrySaveRate",
    "carIndustryAvgMileCarbons"
})
public class CarbonSaveInfo implements Serializable {
    private static final long serialVersionUID = 1L;



    

    public CarbonSaveInfo(
        Double totalCarbonSave,
        Double avgMonthCarbonSave,
        Double yoy,
        Double mom,
        Double fltCarbonSave,
        Double fltGreenPercentage,
        Double fltSaveRate,
        Double fltCorpGreenPercentage,
        Double fltCorpSaveRate,
        Double fltIndustryGreenPercentage,
        Double fltIndustrySaveRate,
        Double htlCarbonSave,
        Double htlGreenPercentage,
        Double htlSaveRate,
        Double htlCorpGreenPercentage,
        Double htlCorpSaveRate,
        Double htlIndustryGreenPercentage,
        Double htlIndustrySaveRate,
        Double trainCarbonSave,
        Double trainGreenPercentage,
        Double trainSaveRate,
        Double trainCorpGreenPercentage,
        Double trainCorpSaveRate,
        Double trainIndustryGreenPercentage,
        Double trainIndustrySaveRate,
        Double carCarbonSave,
        Double carGreenPercentage,
        Double carSaveRate,
        Double carAvgMileCarbons,
        Double carCorpGreenPercentage,
        Double carCorpSaveRate,
        Double carCorpAvgMileCarbons,
        Double carIndustryGreenPercentage,
        Double carIndustrySaveRate,
        Double carIndustryAvgMileCarbons) {
        this.totalCarbonSave = totalCarbonSave;
        this.avgMonthCarbonSave = avgMonthCarbonSave;
        this.yoy = yoy;
        this.mom = mom;
        this.fltCarbonSave = fltCarbonSave;
        this.fltGreenPercentage = fltGreenPercentage;
        this.fltSaveRate = fltSaveRate;
        this.fltCorpGreenPercentage = fltCorpGreenPercentage;
        this.fltCorpSaveRate = fltCorpSaveRate;
        this.fltIndustryGreenPercentage = fltIndustryGreenPercentage;
        this.fltIndustrySaveRate = fltIndustrySaveRate;
        this.htlCarbonSave = htlCarbonSave;
        this.htlGreenPercentage = htlGreenPercentage;
        this.htlSaveRate = htlSaveRate;
        this.htlCorpGreenPercentage = htlCorpGreenPercentage;
        this.htlCorpSaveRate = htlCorpSaveRate;
        this.htlIndustryGreenPercentage = htlIndustryGreenPercentage;
        this.htlIndustrySaveRate = htlIndustrySaveRate;
        this.trainCarbonSave = trainCarbonSave;
        this.trainGreenPercentage = trainGreenPercentage;
        this.trainSaveRate = trainSaveRate;
        this.trainCorpGreenPercentage = trainCorpGreenPercentage;
        this.trainCorpSaveRate = trainCorpSaveRate;
        this.trainIndustryGreenPercentage = trainIndustryGreenPercentage;
        this.trainIndustrySaveRate = trainIndustrySaveRate;
        this.carCarbonSave = carCarbonSave;
        this.carGreenPercentage = carGreenPercentage;
        this.carSaveRate = carSaveRate;
        this.carAvgMileCarbons = carAvgMileCarbons;
        this.carCorpGreenPercentage = carCorpGreenPercentage;
        this.carCorpSaveRate = carCorpSaveRate;
        this.carCorpAvgMileCarbons = carCorpAvgMileCarbons;
        this.carIndustryGreenPercentage = carIndustryGreenPercentage;
        this.carIndustrySaveRate = carIndustrySaveRate;
        this.carIndustryAvgMileCarbons = carIndustryAvgMileCarbons;
    }

    public CarbonSaveInfo() {
    }

    @JsonProperty("totalCarbonSave")
    public Double totalCarbonSave;

    @JsonProperty("avgMonthCarbonSave")
    public Double avgMonthCarbonSave;

    @JsonProperty("yoy")
    public Double yoy;

    @JsonProperty("mom")
    public Double mom;

    @JsonProperty("fltCarbonSave")
    public Double fltCarbonSave;

    @JsonProperty("fltGreenPercentage")
    public Double fltGreenPercentage;

    @JsonProperty("fltSaveRate")
    public Double fltSaveRate;

    @JsonProperty("fltCorpGreenPercentage")
    public Double fltCorpGreenPercentage;

    @JsonProperty("fltCorpSaveRate")
    public Double fltCorpSaveRate;

    @JsonProperty("fltIndustryGreenPercentage")
    public Double fltIndustryGreenPercentage;

    @JsonProperty("fltIndustrySaveRate")
    public Double fltIndustrySaveRate;

    @JsonProperty("htlCarbonSave")
    public Double htlCarbonSave;

    @JsonProperty("htlGreenPercentage")
    public Double htlGreenPercentage;

    @JsonProperty("htlSaveRate")
    public Double htlSaveRate;

    @JsonProperty("htlCorpGreenPercentage")
    public Double htlCorpGreenPercentage;

    @JsonProperty("htlCorpSaveRate")
    public Double htlCorpSaveRate;

    @JsonProperty("htlIndustryGreenPercentage")
    public Double htlIndustryGreenPercentage;

    @JsonProperty("htlIndustrySaveRate")
    public Double htlIndustrySaveRate;

    @JsonProperty("trainCarbonSave")
    public Double trainCarbonSave;

    @JsonProperty("trainGreenPercentage")
    public Double trainGreenPercentage;

    @JsonProperty("trainSaveRate")
    public Double trainSaveRate;

    @JsonProperty("trainCorpGreenPercentage")
    public Double trainCorpGreenPercentage;

    @JsonProperty("trainCorpSaveRate")
    public Double trainCorpSaveRate;

    @JsonProperty("trainIndustryGreenPercentage")
    public Double trainIndustryGreenPercentage;

    @JsonProperty("trainIndustrySaveRate")
    public Double trainIndustrySaveRate;

    @JsonProperty("carCarbonSave")
    public Double carCarbonSave;

    @JsonProperty("carGreenPercentage")
    public Double carGreenPercentage;

    @JsonProperty("carSaveRate")
    public Double carSaveRate;

    @JsonProperty("carAvgMileCarbons")
    public Double carAvgMileCarbons;

    @JsonProperty("carCorpGreenPercentage")
    public Double carCorpGreenPercentage;

    @JsonProperty("carCorpSaveRate")
    public Double carCorpSaveRate;

    @JsonProperty("carCorpAvgMileCarbons")
    public Double carCorpAvgMileCarbons;

    @JsonProperty("carIndustryGreenPercentage")
    public Double carIndustryGreenPercentage;

    @JsonProperty("carIndustrySaveRate")
    public Double carIndustrySaveRate;

    @JsonProperty("carIndustryAvgMileCarbons")
    public Double carIndustryAvgMileCarbons;

    public Double getTotalCarbonSave() {
        return totalCarbonSave;
    }

    public void setTotalCarbonSave(final Double totalCarbonSave) {
        this.totalCarbonSave = totalCarbonSave;
    }
    public Double getAvgMonthCarbonSave() {
        return avgMonthCarbonSave;
    }

    public void setAvgMonthCarbonSave(final Double avgMonthCarbonSave) {
        this.avgMonthCarbonSave = avgMonthCarbonSave;
    }
    public Double getYoy() {
        return yoy;
    }

    public void setYoy(final Double yoy) {
        this.yoy = yoy;
    }
    public Double getMom() {
        return mom;
    }

    public void setMom(final Double mom) {
        this.mom = mom;
    }
    public Double getFltCarbonSave() {
        return fltCarbonSave;
    }

    public void setFltCarbonSave(final Double fltCarbonSave) {
        this.fltCarbonSave = fltCarbonSave;
    }
    public Double getFltGreenPercentage() {
        return fltGreenPercentage;
    }

    public void setFltGreenPercentage(final Double fltGreenPercentage) {
        this.fltGreenPercentage = fltGreenPercentage;
    }
    public Double getFltSaveRate() {
        return fltSaveRate;
    }

    public void setFltSaveRate(final Double fltSaveRate) {
        this.fltSaveRate = fltSaveRate;
    }
    public Double getFltCorpGreenPercentage() {
        return fltCorpGreenPercentage;
    }

    public void setFltCorpGreenPercentage(final Double fltCorpGreenPercentage) {
        this.fltCorpGreenPercentage = fltCorpGreenPercentage;
    }
    public Double getFltCorpSaveRate() {
        return fltCorpSaveRate;
    }

    public void setFltCorpSaveRate(final Double fltCorpSaveRate) {
        this.fltCorpSaveRate = fltCorpSaveRate;
    }
    public Double getFltIndustryGreenPercentage() {
        return fltIndustryGreenPercentage;
    }

    public void setFltIndustryGreenPercentage(final Double fltIndustryGreenPercentage) {
        this.fltIndustryGreenPercentage = fltIndustryGreenPercentage;
    }
    public Double getFltIndustrySaveRate() {
        return fltIndustrySaveRate;
    }

    public void setFltIndustrySaveRate(final Double fltIndustrySaveRate) {
        this.fltIndustrySaveRate = fltIndustrySaveRate;
    }
    public Double getHtlCarbonSave() {
        return htlCarbonSave;
    }

    public void setHtlCarbonSave(final Double htlCarbonSave) {
        this.htlCarbonSave = htlCarbonSave;
    }
    public Double getHtlGreenPercentage() {
        return htlGreenPercentage;
    }

    public void setHtlGreenPercentage(final Double htlGreenPercentage) {
        this.htlGreenPercentage = htlGreenPercentage;
    }
    public Double getHtlSaveRate() {
        return htlSaveRate;
    }

    public void setHtlSaveRate(final Double htlSaveRate) {
        this.htlSaveRate = htlSaveRate;
    }
    public Double getHtlCorpGreenPercentage() {
        return htlCorpGreenPercentage;
    }

    public void setHtlCorpGreenPercentage(final Double htlCorpGreenPercentage) {
        this.htlCorpGreenPercentage = htlCorpGreenPercentage;
    }
    public Double getHtlCorpSaveRate() {
        return htlCorpSaveRate;
    }

    public void setHtlCorpSaveRate(final Double htlCorpSaveRate) {
        this.htlCorpSaveRate = htlCorpSaveRate;
    }
    public Double getHtlIndustryGreenPercentage() {
        return htlIndustryGreenPercentage;
    }

    public void setHtlIndustryGreenPercentage(final Double htlIndustryGreenPercentage) {
        this.htlIndustryGreenPercentage = htlIndustryGreenPercentage;
    }
    public Double getHtlIndustrySaveRate() {
        return htlIndustrySaveRate;
    }

    public void setHtlIndustrySaveRate(final Double htlIndustrySaveRate) {
        this.htlIndustrySaveRate = htlIndustrySaveRate;
    }
    public Double getTrainCarbonSave() {
        return trainCarbonSave;
    }

    public void setTrainCarbonSave(final Double trainCarbonSave) {
        this.trainCarbonSave = trainCarbonSave;
    }
    public Double getTrainGreenPercentage() {
        return trainGreenPercentage;
    }

    public void setTrainGreenPercentage(final Double trainGreenPercentage) {
        this.trainGreenPercentage = trainGreenPercentage;
    }
    public Double getTrainSaveRate() {
        return trainSaveRate;
    }

    public void setTrainSaveRate(final Double trainSaveRate) {
        this.trainSaveRate = trainSaveRate;
    }
    public Double getTrainCorpGreenPercentage() {
        return trainCorpGreenPercentage;
    }

    public void setTrainCorpGreenPercentage(final Double trainCorpGreenPercentage) {
        this.trainCorpGreenPercentage = trainCorpGreenPercentage;
    }
    public Double getTrainCorpSaveRate() {
        return trainCorpSaveRate;
    }

    public void setTrainCorpSaveRate(final Double trainCorpSaveRate) {
        this.trainCorpSaveRate = trainCorpSaveRate;
    }
    public Double getTrainIndustryGreenPercentage() {
        return trainIndustryGreenPercentage;
    }

    public void setTrainIndustryGreenPercentage(final Double trainIndustryGreenPercentage) {
        this.trainIndustryGreenPercentage = trainIndustryGreenPercentage;
    }
    public Double getTrainIndustrySaveRate() {
        return trainIndustrySaveRate;
    }

    public void setTrainIndustrySaveRate(final Double trainIndustrySaveRate) {
        this.trainIndustrySaveRate = trainIndustrySaveRate;
    }
    public Double getCarCarbonSave() {
        return carCarbonSave;
    }

    public void setCarCarbonSave(final Double carCarbonSave) {
        this.carCarbonSave = carCarbonSave;
    }
    public Double getCarGreenPercentage() {
        return carGreenPercentage;
    }

    public void setCarGreenPercentage(final Double carGreenPercentage) {
        this.carGreenPercentage = carGreenPercentage;
    }
    public Double getCarSaveRate() {
        return carSaveRate;
    }

    public void setCarSaveRate(final Double carSaveRate) {
        this.carSaveRate = carSaveRate;
    }
    public Double getCarAvgMileCarbons() {
        return carAvgMileCarbons;
    }

    public void setCarAvgMileCarbons(final Double carAvgMileCarbons) {
        this.carAvgMileCarbons = carAvgMileCarbons;
    }
    public Double getCarCorpGreenPercentage() {
        return carCorpGreenPercentage;
    }

    public void setCarCorpGreenPercentage(final Double carCorpGreenPercentage) {
        this.carCorpGreenPercentage = carCorpGreenPercentage;
    }
    public Double getCarCorpSaveRate() {
        return carCorpSaveRate;
    }

    public void setCarCorpSaveRate(final Double carCorpSaveRate) {
        this.carCorpSaveRate = carCorpSaveRate;
    }
    public Double getCarCorpAvgMileCarbons() {
        return carCorpAvgMileCarbons;
    }

    public void setCarCorpAvgMileCarbons(final Double carCorpAvgMileCarbons) {
        this.carCorpAvgMileCarbons = carCorpAvgMileCarbons;
    }
    public Double getCarIndustryGreenPercentage() {
        return carIndustryGreenPercentage;
    }

    public void setCarIndustryGreenPercentage(final Double carIndustryGreenPercentage) {
        this.carIndustryGreenPercentage = carIndustryGreenPercentage;
    }
    public Double getCarIndustrySaveRate() {
        return carIndustrySaveRate;
    }

    public void setCarIndustrySaveRate(final Double carIndustrySaveRate) {
        this.carIndustrySaveRate = carIndustrySaveRate;
    }
    public Double getCarIndustryAvgMileCarbons() {
        return carIndustryAvgMileCarbons;
    }

    public void setCarIndustryAvgMileCarbons(final Double carIndustryAvgMileCarbons) {
        this.carIndustryAvgMileCarbons = carIndustryAvgMileCarbons;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final CarbonSaveInfo other = (CarbonSaveInfo)obj;
        return
            Objects.equal(this.totalCarbonSave, other.totalCarbonSave) &&
            Objects.equal(this.avgMonthCarbonSave, other.avgMonthCarbonSave) &&
            Objects.equal(this.yoy, other.yoy) &&
            Objects.equal(this.mom, other.mom) &&
            Objects.equal(this.fltCarbonSave, other.fltCarbonSave) &&
            Objects.equal(this.fltGreenPercentage, other.fltGreenPercentage) &&
            Objects.equal(this.fltSaveRate, other.fltSaveRate) &&
            Objects.equal(this.fltCorpGreenPercentage, other.fltCorpGreenPercentage) &&
            Objects.equal(this.fltCorpSaveRate, other.fltCorpSaveRate) &&
            Objects.equal(this.fltIndustryGreenPercentage, other.fltIndustryGreenPercentage) &&
            Objects.equal(this.fltIndustrySaveRate, other.fltIndustrySaveRate) &&
            Objects.equal(this.htlCarbonSave, other.htlCarbonSave) &&
            Objects.equal(this.htlGreenPercentage, other.htlGreenPercentage) &&
            Objects.equal(this.htlSaveRate, other.htlSaveRate) &&
            Objects.equal(this.htlCorpGreenPercentage, other.htlCorpGreenPercentage) &&
            Objects.equal(this.htlCorpSaveRate, other.htlCorpSaveRate) &&
            Objects.equal(this.htlIndustryGreenPercentage, other.htlIndustryGreenPercentage) &&
            Objects.equal(this.htlIndustrySaveRate, other.htlIndustrySaveRate) &&
            Objects.equal(this.trainCarbonSave, other.trainCarbonSave) &&
            Objects.equal(this.trainGreenPercentage, other.trainGreenPercentage) &&
            Objects.equal(this.trainSaveRate, other.trainSaveRate) &&
            Objects.equal(this.trainCorpGreenPercentage, other.trainCorpGreenPercentage) &&
            Objects.equal(this.trainCorpSaveRate, other.trainCorpSaveRate) &&
            Objects.equal(this.trainIndustryGreenPercentage, other.trainIndustryGreenPercentage) &&
            Objects.equal(this.trainIndustrySaveRate, other.trainIndustrySaveRate) &&
            Objects.equal(this.carCarbonSave, other.carCarbonSave) &&
            Objects.equal(this.carGreenPercentage, other.carGreenPercentage) &&
            Objects.equal(this.carSaveRate, other.carSaveRate) &&
            Objects.equal(this.carAvgMileCarbons, other.carAvgMileCarbons) &&
            Objects.equal(this.carCorpGreenPercentage, other.carCorpGreenPercentage) &&
            Objects.equal(this.carCorpSaveRate, other.carCorpSaveRate) &&
            Objects.equal(this.carCorpAvgMileCarbons, other.carCorpAvgMileCarbons) &&
            Objects.equal(this.carIndustryGreenPercentage, other.carIndustryGreenPercentage) &&
            Objects.equal(this.carIndustrySaveRate, other.carIndustrySaveRate) &&
            Objects.equal(this.carIndustryAvgMileCarbons, other.carIndustryAvgMileCarbons);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.totalCarbonSave == null ? 0 : this.totalCarbonSave.hashCode());
        result = 31 * result + (this.avgMonthCarbonSave == null ? 0 : this.avgMonthCarbonSave.hashCode());
        result = 31 * result + (this.yoy == null ? 0 : this.yoy.hashCode());
        result = 31 * result + (this.mom == null ? 0 : this.mom.hashCode());
        result = 31 * result + (this.fltCarbonSave == null ? 0 : this.fltCarbonSave.hashCode());
        result = 31 * result + (this.fltGreenPercentage == null ? 0 : this.fltGreenPercentage.hashCode());
        result = 31 * result + (this.fltSaveRate == null ? 0 : this.fltSaveRate.hashCode());
        result = 31 * result + (this.fltCorpGreenPercentage == null ? 0 : this.fltCorpGreenPercentage.hashCode());
        result = 31 * result + (this.fltCorpSaveRate == null ? 0 : this.fltCorpSaveRate.hashCode());
        result = 31 * result + (this.fltIndustryGreenPercentage == null ? 0 : this.fltIndustryGreenPercentage.hashCode());
        result = 31 * result + (this.fltIndustrySaveRate == null ? 0 : this.fltIndustrySaveRate.hashCode());
        result = 31 * result + (this.htlCarbonSave == null ? 0 : this.htlCarbonSave.hashCode());
        result = 31 * result + (this.htlGreenPercentage == null ? 0 : this.htlGreenPercentage.hashCode());
        result = 31 * result + (this.htlSaveRate == null ? 0 : this.htlSaveRate.hashCode());
        result = 31 * result + (this.htlCorpGreenPercentage == null ? 0 : this.htlCorpGreenPercentage.hashCode());
        result = 31 * result + (this.htlCorpSaveRate == null ? 0 : this.htlCorpSaveRate.hashCode());
        result = 31 * result + (this.htlIndustryGreenPercentage == null ? 0 : this.htlIndustryGreenPercentage.hashCode());
        result = 31 * result + (this.htlIndustrySaveRate == null ? 0 : this.htlIndustrySaveRate.hashCode());
        result = 31 * result + (this.trainCarbonSave == null ? 0 : this.trainCarbonSave.hashCode());
        result = 31 * result + (this.trainGreenPercentage == null ? 0 : this.trainGreenPercentage.hashCode());
        result = 31 * result + (this.trainSaveRate == null ? 0 : this.trainSaveRate.hashCode());
        result = 31 * result + (this.trainCorpGreenPercentage == null ? 0 : this.trainCorpGreenPercentage.hashCode());
        result = 31 * result + (this.trainCorpSaveRate == null ? 0 : this.trainCorpSaveRate.hashCode());
        result = 31 * result + (this.trainIndustryGreenPercentage == null ? 0 : this.trainIndustryGreenPercentage.hashCode());
        result = 31 * result + (this.trainIndustrySaveRate == null ? 0 : this.trainIndustrySaveRate.hashCode());
        result = 31 * result + (this.carCarbonSave == null ? 0 : this.carCarbonSave.hashCode());
        result = 31 * result + (this.carGreenPercentage == null ? 0 : this.carGreenPercentage.hashCode());
        result = 31 * result + (this.carSaveRate == null ? 0 : this.carSaveRate.hashCode());
        result = 31 * result + (this.carAvgMileCarbons == null ? 0 : this.carAvgMileCarbons.hashCode());
        result = 31 * result + (this.carCorpGreenPercentage == null ? 0 : this.carCorpGreenPercentage.hashCode());
        result = 31 * result + (this.carCorpSaveRate == null ? 0 : this.carCorpSaveRate.hashCode());
        result = 31 * result + (this.carCorpAvgMileCarbons == null ? 0 : this.carCorpAvgMileCarbons.hashCode());
        result = 31 * result + (this.carIndustryGreenPercentage == null ? 0 : this.carIndustryGreenPercentage.hashCode());
        result = 31 * result + (this.carIndustrySaveRate == null ? 0 : this.carIndustrySaveRate.hashCode());
        result = 31 * result + (this.carIndustryAvgMileCarbons == null ? 0 : this.carIndustryAvgMileCarbons.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("totalCarbonSave", totalCarbonSave)
            .add("avgMonthCarbonSave", avgMonthCarbonSave)
            .add("yoy", yoy)
            .add("mom", mom)
            .add("fltCarbonSave", fltCarbonSave)
            .add("fltGreenPercentage", fltGreenPercentage)
            .add("fltSaveRate", fltSaveRate)
            .add("fltCorpGreenPercentage", fltCorpGreenPercentage)
            .add("fltCorpSaveRate", fltCorpSaveRate)
            .add("fltIndustryGreenPercentage", fltIndustryGreenPercentage)
            .add("fltIndustrySaveRate", fltIndustrySaveRate)
            .add("htlCarbonSave", htlCarbonSave)
            .add("htlGreenPercentage", htlGreenPercentage)
            .add("htlSaveRate", htlSaveRate)
            .add("htlCorpGreenPercentage", htlCorpGreenPercentage)
            .add("htlCorpSaveRate", htlCorpSaveRate)
            .add("htlIndustryGreenPercentage", htlIndustryGreenPercentage)
            .add("htlIndustrySaveRate", htlIndustrySaveRate)
            .add("trainCarbonSave", trainCarbonSave)
            .add("trainGreenPercentage", trainGreenPercentage)
            .add("trainSaveRate", trainSaveRate)
            .add("trainCorpGreenPercentage", trainCorpGreenPercentage)
            .add("trainCorpSaveRate", trainCorpSaveRate)
            .add("trainIndustryGreenPercentage", trainIndustryGreenPercentage)
            .add("trainIndustrySaveRate", trainIndustrySaveRate)
            .add("carCarbonSave", carCarbonSave)
            .add("carGreenPercentage", carGreenPercentage)
            .add("carSaveRate", carSaveRate)
            .add("carAvgMileCarbons", carAvgMileCarbons)
            .add("carCorpGreenPercentage", carCorpGreenPercentage)
            .add("carCorpSaveRate", carCorpSaveRate)
            .add("carCorpAvgMileCarbons", carCorpAvgMileCarbons)
            .add("carIndustryGreenPercentage", carIndustryGreenPercentage)
            .add("carIndustrySaveRate", carIndustrySaveRate)
            .add("carIndustryAvgMileCarbons", carIndustryAvgMileCarbons)
            .toString();
    }
}
