/*
package com.corpgovernment.resource.schedule.domain.onlinereport.customreport;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.CustomRequest;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.ReportUserDefinedOutput;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.RedisKeyConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.business.BusinessConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportUserDefinedOutputNew;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.CommonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.BizTypeEnums;
import onlinereport.enums.DimensionEnum;
import onlinereport.enums.FieldEnum;
import onlinereport.enums.ReportDeptEnum;
import onlinereport.enums.SharkLocaleEnum;
import onlinereport.enums.TrainSeatTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

*/
/**
 * Auther:abguo
 * Date:2019/8/15
 * Description:
 * Project:onlinereportweb
 *//*

@Service("customReportDataService")
@Slf4j
public class CustomReportDataService  implements IReportDataService<CustomRequest,ReportUserDefinedOutputNew> {
    protected static final String LOG_TITLE = CustomReportDataService.class.getSimpleName();

    @Autowired
    private CommonStatisticsService commonStatisticsService;

    @Autowired
    private CorpReportSetService corpReportSetService;

    @Override
    public ReportUserDefinedOutputNew query(CustomRequest request) throws JsonProcessingException {
        String uid = request.getUid();
        String reportJson = request.getReportJson();
        ReportUserDefinedInput input = parseInputToFilter(reportJson);
        if (input==null){
            return null;
        }
        if (!validTime(input.getFilterList())){
            log.warn(LOG_TITLE+"query","time error");
            return null;
        }
        ReportUserDefinedOutputNew outputNew =getResultFromRedis(request,uid);
        if (outputNew != null){
            return outputNew;
        }
        outputNew = new ReportUserDefinedOutputNew();
        ReportUserDefinedOutput output = commonStatisticsService.statistics(input,uid);
        List<Map<String, String>> reportData = output.getRowList();//createdataTest(json);createdataTest(json);
        if (CollectionUtils.isEmpty(reportData)){
            return outputNew;
        }
        saveResultToRedis(output,request,uid);
        String simlpeExchange = QConfigUtils.getValue("simlpeExchange");
        if (StringUtils.equalsIgnoreCase(simlpeExchange,"F")){
            outputNew = complexExchange(reportJson,reportData);
        }else {
            outputNew = simpleExchange(reportJson,reportData);
        }
        return  outputNew;
    }
//
    */
/**
     *json转换为filter
     * @param reportJson
     * @return
     *//*

    private ReportUserDefinedInput  parseInputToFilter(String reportJson) throws JsonProcessingException {
        ReportUserDefinedInput input = new ReportUserDefinedInput();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> jsonObject = objectMapper.readValue(reportJson, new TypeReference<Map<String, Object>>() {});
        if (jsonObject.containsKey("bizType")){
            BizTypeEnums bizTypeEnums = BizTypeEnums.valueOf((String) jsonObject.get("bizType"));
            input.setBizTypeEnums(bizTypeEnums);
        }else {
            return null;
        }
        if (!jsonObject.containsKey("dimensionList")||CollectionUtils.isEmpty((Collection) jsonObject.get("dimensionList"))){
                return null;
        }
        if (!jsonObject.containsKey("statisticalList")||CollectionUtils.isEmpty((Collection) jsonObject.get("statisticalList"))){
                return null;
        }

        List dimisionArray = (List) jsonObject.get("dimensionList");//维度
        List statisticalList = (List) jsonObject.get("statisticalList");//度量项
//        List<String> statisticalList = statisticArray.toJavaList(String.class);
        Set<String> dimensionSet = new HashSet<>();
        List<Filter> filterList = new ArrayList<>();
        filterList.addAll(speicalFilter(jsonObject));
        for (int i = 0; i<dimisionArray.size(); i++){
            Map json = (Map) dimisionArray.get(i);
            String name = (String) json.get("name");
            if (StringUtils.isNotEmpty(name)&&StringUtils.isNotBlank(name)){
                if (StringUtils.equalsIgnoreCase(name,"time")){//时间维度
                    dimensionSet.add(DimensionEnum.TIME.getName());
                    if (json.containsKey("value")){
                        List jsonArray = (List) json.get("value");
                        if (jsonArray!=null&&jsonArray.size()>0){
                            String time = (String) jsonArray.get(0);
                            if (StringUtils.equalsIgnoreCase(time, DimensionEnum.YEAR.getName())){
                                dimensionSet.add(DimensionEnum.YEAR.getName());
                            }else if (StringUtils.equalsIgnoreCase(time, DimensionEnum.HALFYEAR.getName())){
                                dimensionSet.add(DimensionEnum.HALFYEAR.getName());
                            }else if (StringUtils.equalsIgnoreCase(time, DimensionEnum.QUARTER.getName())){
                                dimensionSet.add(DimensionEnum.QUARTER.getName());
                            }else {
                                dimensionSet.add(DimensionEnum.MONTH.getName());
                            }
                        }
                    }
                }else if (validCostcenterOrDept(name)){
                    dimensionSet.add(name);
                    if ((!json.containsKey("isAll")||!(Boolean) json.get("isAll"))){
                        Filter filter =parseFilterItem(json);
                        if (filter!=null){
                            filterList.add(filter);
                        }
                    }
                } else{
                    dimensionSet.add(name);
                    Filter filter =parseFilterItem(json);
                    if (filter!=null){
                        filterList.add(filter);
                    }
                }
            }
        }
        input.setFilterList(filterList);
        input.setDimensionList(new ArrayList<>(dimensionSet));
        input.setStatisticalList(statisticalList);
        return input;
    }

    private boolean validCostcenterOrDept(String name){
        ReportDeptEnum reportDeptEnum = ReportDeptEnum.getReportDeptEnumByCode(name);
        if (reportDeptEnum==null){
            return false;
        }else {
            return true;
        }

    }

//    private  boolean validCorpFilter(List<Filter> filterList,String uid){
//        if (filterList.size() == 0||filterList.stream().noneMatch(i->i.getName().equalsIgnoreCase(DimensionEnum.CORP.getName())&&i.getValue()!=null&&i.getValue().size()>0)){
//            Filter corpFilter = new Filter();
//            corpFilter.setName(DimensionEnum.CORP.getName());
//            List<CorpAndAccountInfoBO> corpAndAccountInfos = corpReportSetService.getCorpAccountByUid(uid, new HashMap<>());
//            if (corpAndAccountInfos==null||corpAndAccountInfos.size()==0){
//                log.warn(LOG_TITLE+"validCorpFilter","  uid:"+uid+",没有配置公司");
//                return false;
//            }else{
//                List<String> corpIds = corpAndAccountInfos.stream().map(i->i.getCorpId()).collect(Collectors.toList());
//                corpFilter.setValue(corpIds);
//                filterList.add(corpFilter);
//                return true;
//            }
//        }else{
//            return true;
//        }
//    }

    private boolean validTime(List<Filter> filterList){
        String maxTime = QConfigUtils.getValue(GlobalConst.MAX_DAYS);
        if (StringUtils.isNotEmpty(maxTime)&&!StringUtils.equalsIgnoreCase(maxTime,GlobalConst.STRING_ZERO)){
            log.info("maxTime","switch maxTime turn on");
            if (CollectionUtils.isNotEmpty(filterList)&&(filterList.stream().anyMatch(i->i.getName().equalsIgnoreCase("startTime")&&i.getValue()!=null&&i.getValue().size()>0)
                    &&filterList.stream().anyMatch(i->i.getName().equalsIgnoreCase("endTime")&&i.getValue()!=null&&i.getValue().size()>0))){
                Filter startFilter = filterList.stream().filter(i->i.getName().equalsIgnoreCase("startTime")).findFirst().get();
                Filter endFilter = filterList.stream().filter(i->i.getName().equalsIgnoreCase("endTime")).findFirst().get();
                long  startTime = Long.parseLong(startFilter.getValue().get(0));
                long  endTime = Long.parseLong(endFilter.getValue().get(0));
                if (!ValidateUtils.validMaxDays(startTime,endTime)){
                    return false;
                }
                return true;
            }else{
                return false;
            }
        }else {
            log.info("maxTime","switch maxTime turn off");
            return true;
        }

    }

    public List<Filter> speicalFilter(Map json) {
        List<Filter> result = new ArrayList<>();
        if (!json.containsKey("startTime") || !json.containsKey("endTime")) {
            return result;
        }

        Filter startFilter = new Filter();
        startFilter.setName("startTime");
        startFilter.setValue(Arrays.asList(String.valueOf(json.get("startTime"))));
        result.add(startFilter);

        Filter endFilter = new Filter();
        endFilter.setName("endTime");
        endFilter.setValue(Arrays.asList(String.valueOf(json.get("endTime"))));
        result.add(endFilter);


        Filter langFilter = new Filter();
        langFilter.setName("lang");
        if (!json.containsKey("languange")||
                StringUtils.isEmpty((String) json.get("languange"))){
            langFilter.setValue(Arrays.asList(SharkLocaleEnum.ZH_CN.getLoacal()));
        }else{
            langFilter.setValue(Arrays.asList((String) json.get("languange")));

        }
        result.add(langFilter);

        return result;
    }


    public  Filter  parseFilterItem(Map json){
        String name = (String) json.get("name");
        if (json.containsKey("value")){
            List jsonArray = (List) json.get("value");
            if (jsonArray!=null&&jsonArray.size()>0){
                Filter filter = new Filter();
                filter.setName(name);
                if (StringUtils.equalsIgnoreCase(name,"train_seat_type")){
                    filter.setValue(convertSeatType(jsonArray));
                }else if (StringUtils.equalsIgnoreCase(name,"flt_realclass")){
                    filter.setValue(convertRealClass(jsonArray));
                }else if (StringUtils.equalsIgnoreCase(name,"flt_flightstatus")){
                    filter.setValue(convertFlightStatus(jsonArray));
                }else if (StringUtils.equalsIgnoreCase(name,"flt_agreementair")){
                    filter.setValue(convertFlightAgreementAir(jsonArray));
                }else if (StringUtils.equalsIgnoreCase(name,"htl_star")){
                    filter.setValue(convertStar(jsonArray));
                }else{
                    filter.setValue(jsonArray);
                }
                return filter;
            }else{
                return  setDefaultFilterValue(name);
            }
        }else{
            return  setDefaultFilterValue(name);
        }
    }

    private Filter setDefaultFilterValue(String name){
        if (StringUtils.equalsIgnoreCase(name,"booktype")){
            Filter filter = new Filter();
            filter.setValue(Arrays.asList("T","F","M"));
            filter.setName(name);
            return filter;
        }else if (StringUtils.equalsIgnoreCase(name,"flt_realclass")){
            Filter filter = new Filter();
            filter.setValue(Arrays.asList("Y","F","C","S"));
            filter.setName(name);
            return filter;
        }else if (StringUtils.equalsIgnoreCase(name,"flt_agreementair")){
            Filter filter = new Filter();
            filter.setValue(Arrays.asList("NA","YA"));
            filter.setName(name);
            return filter;
        }else if (StringUtils.equalsIgnoreCase(name,"flt_flightclass")){
            Filter filter = new Filter();
            filter.setValue(Arrays.asList("I","N"));
            filter.setName(name);
            return filter;
        }else if (StringUtils.equalsIgnoreCase(name,"flt_flightstatus")){
            Filter filter = new Filter();
            filter.setValue(Arrays.asList(BusinessConst.FLIGHT_STATUS_CJ, BusinessConst.FLIGHT_STATUS_TP));
            filter.setName(name);
            return filter;
        }else if (StringUtils.equalsIgnoreCase(name,"htl_isoversea")){
            Filter filter = new Filter();
            filter.setValue(Arrays.asList("F","O","T"));
            filter.setName(name);
            return filter;
        }else if (StringUtils.equalsIgnoreCase(name,"htl_ordertype")){
            Filter filter = new Filter();
            filter.setValue(Arrays.asList("M","C"));
            filter.setName(name);
            return filter;
        }else if (StringUtils.equalsIgnoreCase(name,"htl_ordertype_id")){
            Filter filter = new Filter();
            filter.setValue(Arrays.asList("M","C"));
            filter.setName(name);
            return filter;
        } else if (StringUtils.equalsIgnoreCase(name,"htl_star")){
            Filter filter = new Filter();
            filter.setValue(Arrays.asList("0","1","2","3","4","5"));
            filter.setName(name);
            return filter;
        }
        return null;
    }

    */
/**
     * 是否协议航空
     * @param resource
     * @return
     *//*

    private List<String> convertFlightAgreementAir(List<String> resource){
        List<String> result = new ArrayList<>();
        if (resource.contains("YA")){
            result.add("C");
        }
        if (resource.contains("NA")){
            result.add("NC");
        }
        return result;
    }

    */
/**
     * 航段状态
     * @param resource
     * @return
     *//*

    private List<String> convertFlightStatus(List<String> resource){
        List<String> result = new ArrayList<>();
        if (resource.contains("D")){
            result.add(BusinessConst.FLIGHT_STATUS_CJ);
        }
        if (resource.contains("R")){
            result.add(BusinessConst.FLIGHT_STATUS_TP);
        }
        return result;
    }
    */
/**
     * 星级
     * @param resource
     * @return
     *//*

    private List<String> convertStar(List<String> resource){
        if (resource.contains("2")){
            resource.addAll(Arrays.asList("0","1"));
        }
        return resource;
    }

    */
/**
     * 舱位
     * @param resource
     * @return
     *//*

    private List<String> convertRealClass(List<String> resource){
        List<String> result = new ArrayList<>();
        if (resource.contains("Y")){
            result.add("Y");
        }
        if (resource.contains("F")){
            result.add("F");
        }
        if (resource.contains("C")){
            result.add("C");
        }
        if (resource.contains("S")){
            result.add("S");
        }
        return result;
    }

    */
/**
     * 坐席
     * @param resource
     * @return
     *//*

    private List<String> convertSeatType(List<String> resource){
        List<String> result = new ArrayList<>();
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_1ST_CLASS_SEAT.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_1ST_CLASS_SEAT.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_2ND_CLASS_SEAT.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_2ND_CLASS_SEAT.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_PREMIER_SEAT.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_PREMIER_SEAT.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_BUSINESS_CLASS_SEAT.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_BUSINESS_CLASS_SEAT.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_HARD_SEAT.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_HARD_SEAT.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_HARD_SLEEPER.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_HARD_SLEEPER.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_SLEEPER.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_SLEEPER.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_DELUXE_SOFT.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_DELUXE_SOFT.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_SOFT_SLEEPER.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_SOFT_SLEEPER.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_STANDING.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_STANDING.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_1ST_DOUBLE_SLEEPER.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_1ST_DOUBLE_SLEEPER.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_2ND_DOUBLE_SLEEPER.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_2ND_DOUBLE_SLEEPER.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_1_BED_SOFT_SLEEPER.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_1_BED_SOFT_SLEEPER.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_OTHERS.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_OTHERS.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_LIE_GENERATION.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_LIE_GENERATION.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_HIGH_GRADE_SOFT_SLEEPER.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_HIGH_GRADE_SOFT_SLEEPER.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_1ND_ClASS_SOFT_SEAT.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_1ND_ClASS_SOFT_SEAT.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_2ND_ClASS_SOFT_SEAT.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_2ND_ClASS_SOFT_SEAT.getName());
        }
        if (resource.contains(String.valueOf(TrainSeatTypeEnum.TRAIN_SEAT_HIGH_GRADE_MOTORTRAIN_SLEEPER.getCode()))){
            result.add(TrainSeatTypeEnum.TRAIN_SEAT_HIGH_GRADE_MOTORTRAIN_SLEEPER.getName());
        }
        return result;
    }

    */
/**
     * 简单行列互换
     * @param reportJson
     * @param reportData
     * @return
     *//*

    public ReportUserDefinedOutputNew simpleExchange(String reportJson,List<Map<String, String>> reportData) throws JsonProcessingException {
        ReportUserDefinedOutputNew outputNew = new ReportUserDefinedOutputNew();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> jsonObject = objectMapper.readValue(reportJson, new TypeReference<Map<String, Object>>() {});
        List dimisionArray = (List) jsonObject.get("dimensionList");//维度
        List statisticArray = (List) jsonObject.get("statisticalList");//度量项
        Map reportStyleJson = (Map) jsonObject.get("reportStyle");//报表行列值
        Map rowJson = (Map) reportStyleJson.get("row");
//        Map columnJson = (Map) reportStyleJson.get("column");
        List<String> diminsionList = new ArrayList<>();
        List<String> sortedFields = new ArrayList<>();
        List<String> titleList = new ArrayList<>();

        for (int i = 0 ; i < dimisionArray.size();i++){
            diminsionList.add((String) ((Map)dimisionArray.get(i)).get("name"));
            sortedFields.add((String) ((Map)dimisionArray.get(i)).get("name"));
        }
        titleList.addAll(diminsionList);
        titleList.addAll(statisticArray);
        List<List<String>> list= new ArrayList<>();
        for (Map<String,String> map : reportData){
            List<String> item = new ArrayList<>();
            for (String key : titleList){
                item.add(CommonUtils.fmtMicrometer(MapperUtils.trim(map.get(key))));
            }
            list.add(item);
        }
        List<String> titles = list.remove(0);
        sorted(list,sortedFields.size());
        list.add(0,titles);
        if (rowJson.containsKey("statistic")&&CollectionUtils.isNotEmpty((Collection) rowJson.get("statistic"))){
            outputNew.setColumnCount(dimisionArray.size());
            outputNew.setRowCount(1);
            outputNew.setRowList(list);
            outputNew.setTotalCount(list.size());
        }
        if (rowJson.containsKey("dimension")&&CollectionUtils.isNotEmpty((Collection) rowJson.get("dimension"))){
            List<List<String>> result = new ArrayList<>();
            for (int i = 0; i<list.get(0).size(); i++){
                List<String> temp = new ArrayList<>();
                for (int j = 0;j < list.size(); j++ ){
                    temp.add(list.get(j).get(i));
                }
                result.add(temp);
            }
            outputNew.setColumnCount(1);
            outputNew.setRowCount(dimisionArray.size());
            outputNew.setRowList(result);
            outputNew.setTotalCount(result.size());
        }
        return outputNew;
    }


    */
/**
     * 复杂行列互换
     * @param reportJson
     * @param reportData
     * @return
     *//*

    public ReportUserDefinedOutputNew complexExchange(String reportJson,List<Map<String, String>> reportData) throws JsonProcessingException {
        ReportUserDefinedOutputNew outputNew = new ReportUserDefinedOutputNew();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> jsonObject = objectMapper.readValue(reportJson, new TypeReference<Map<String, Object>>() {});
        List dimisionArray = (List) jsonObject.get("dimensionList");//维度
        List statisticArray = (List) jsonObject.get("statisticalList");//度量项
        Map reportStyleJson = (Map) jsonObject.get("reportStyle");//报表行列值
        Map rowJson = (Map) reportStyleJson.get("row");
        Map columnJson = (Map) reportStyleJson.get("column");
        List<String> diminsionList = new ArrayList<>();
        List<String> sortedFields = new ArrayList<>();
//        List<String> titleList = new ArrayList<>();

        for (int i = 0 ; i < dimisionArray.size();i++){
            diminsionList.add((String) ((Map)dimisionArray.get(i)).get("name"));
            sortedFields.add((String) ((Map)dimisionArray.get(i)).get("name"));
        }

        Map<String, Set<String>> dimisionValMap = new HashMap<>();
        for (String str : diminsionList) {
            dimisionValMap.put(str, new HashSet<>());
        }
        for (Map<String, String> map : reportData) {
            for (String str : diminsionList) {
                dimisionValMap.get(str).add(map.get(str));
            }
        }

//        Map<String, Set<String>> statisticValMap = new HashMap<>();
//        List<String> statisticsList = statisticArray.toJavaList(String.class);
//
//        Set<String> statisticSet = new HashSet<>();
//        statisticSet.addAll(statisticsList);
//        statisticValMap.put("statistic",statisticSet);

        List rowStatisticArray = null;
        List columnStatisticArray = null;
        List rowDimisionArray = null;
        List columnDimisionArray = null;
        if (rowJson.containsKey("statistic")&&CollectionUtils.isNotEmpty((Collection) rowJson.get("statistic"))){
            rowStatisticArray = (List) rowJson.get("statistic");
        }else{
            columnStatisticArray = (List) columnJson.get("statistic");
        }
        if (rowJson.containsKey("dimension")&&CollectionUtils.isNotEmpty((Collection) rowJson.get("dimension"))){
            rowDimisionArray = (List) rowJson.get("dimension");
        }
        if (columnJson.containsKey("dimension")&&CollectionUtils.isNotEmpty((Collection) columnJson.get("dimension"))){
            columnDimisionArray = (List) columnJson.get("dimension");
        }
        List columns = crossJoin(getTitleVal(columnStatisticArray,columnDimisionArray,reportData));
        List rows = crossJoin(getTitleVal(rowStatisticArray,rowDimisionArray,reportData));
        List<List<String>> rowList = fillData(columns,rows,diminsionList,statisticArray,reportData);
        outputNew.setColumnCount(rows.size());
        outputNew.setRowCount(columns.size());
        outputNew.setRowList(rowList);
        return outputNew;
    }

    */
/**
     * List<Map<String,String>>Map<String,String>
     * @param dimension 维度值
     * @param statistics 度量值
     * @param datas 数据集
     * @return
     *//*

    public Map<String,String> converListToMap(List<String> dimension, List<String> statistics, List<Map<String,String>> datas ){
        Map<String,String> newData = new HashMap<>();
        if (datas==null||datas.size()<=0){
            return newData;
        }
        for (Map<String,String> map : datas){
            for (String statistic : statistics){
                List<String> key = new ArrayList<>();
                if (dimension!=null&&dimension.size()>0){
                    for (String dimision : dimension ){
                        key.add(map.get(dimision));
                    }
                }
                key.add(FieldEnum.valueOf(statistic.toUpperCase()).getName());
                newData.put(StringUtils.join(key,"#"),map.get(statistic));
            }
        }
        return newData;
    }

    */
/**
     * 行列组合的可能
     * @param mapVal
     * @return
     *//*

    public List<List<String>> crossJoin(Map<String,Set<String>> mapVal){
        List<List<String>> result = new ArrayList<>();
        if (mapVal==null||mapVal.size()==0){
            return result;
        }
        int totalColumn = 1;
        for (Set<String> set : mapVal.values()){
            totalColumn *= set.size();
        }
        int temp = 1;
        for (Set<String> set : mapVal.values()){
            temp *= set.size();
            List<String> tempList = new ArrayList<>();
            //元素循环次数=总记录数/(元素个数*后续集合的笛卡尔积个数)
            int loopCount = totalColumn/(set.size()*totalColumn/temp);
            for (int i = 0;i<loopCount;i++){
                for (String str: set){
                    for (int j=0 ; j<(totalColumn/temp);j++ ){
                        tempList.add(str);
                    }
                }
            }
            result.add(tempList);
        }
        return  result;
    }

    */
/**
     * 获得标题值
     * @param statisticsArr 行或列的度量项
     * @param dimisionsArr  行或列的维度
     * @param rowList 数据集
     * @return
     *//*

    public Map<String, Set<String>>  getTitleVal(List statisticsArr, List dimisionsArr , List<Map<String,String>> rowList){
        Map<String,Set<String>> result = new HashMap<>();

        if (statisticsArr!=null&&statisticsArr.size()>0){
            Map<String, Set<String>> statisticValMap = new HashMap<>();
            Set<String> statisticSet = new HashSet<>();
            for (Object str : statisticsArr) {
                statisticSet.add(rowList.get(0).get(str));
            }
            statisticValMap.put("statistic",statisticSet);
            result.putAll(statisticValMap);
        }
        if (dimisionsArr!=null&&dimisionsArr.size()>0){
            Map<String, Set<String>> dimisionValMap = new HashMap<>();

            for (Object str : dimisionsArr) {
                dimisionValMap.put((String) str, new HashSet<>());
            }
            rowList.remove(0);
            for (Map<String, String> map : rowList) {
                for (Object str : dimisionsArr) {
                    dimisionValMap.get(str).add(map.get(str));
                }
            }
            result.putAll(dimisionValMap);
        }
        return  result;
    }

    */
/**
     * 填充数据
     * @param columns 行标题
     * @param rows 列标题
     * @param dimension 维度值
     * @param statistics 度量项值
     * @param datas
     *//*

    public List<List<String>>  fillData(List<List<String>> columns,List<List<String>> rows,List<String> dimension, List<String> statistics,List<Map<String,String>> datas ){
        Map<String,String> mapData = converListToMap(dimension,statistics,datas);
        List<List<String>> result = new ArrayList<>();
        if (rows!=null&&rows.size()>0){
            List<String> emptyData = new ArrayList<>();
            for (int a = 0 ; a < columns.size() ; a++){
                emptyData.add(GlobalConst.STRING_EMPTY);
            }
            for (int a = 0 ; a < rows.size() ; a++){
                List<String> temp = new ArrayList<>();
                temp.addAll(emptyData);
                temp.addAll(rows.get(a));
                result.add(temp);
            }
            for (int count = 0;count<columns.get(0).size();count++){
                List<String> item = new ArrayList<>();
                for (int l = 0; l<columns.size();l++){
                    item.add(columns.get(l).get(count));
                }
                for (int i = 0; i<rows.get(0).size();i++){
                    List<String> temp = new ArrayList<>();
                    for (int k = 0;k<rows.size();k++){
                        temp.add(rows.get(k).get(i));
                    }
                    for (int l = 0; l<columns.size();l++){
                        temp.add(columns.get(l).get(count));
                    }
                    item.add(CommonUtils.fmtMicrometer(getCellData(temp,mapData)));
                    result.add(item);
                }
            }
        }
        return result;
    }

    */
/**
     * 获得单元格值
     * @param keys
     * @param targetData
     * @return
     *//*

    public String getCellData(List<String> keys,Map<String,String> targetData) {
        for (String targetKey : targetData.keySet()){
            List<String> temp = Arrays.asList(targetKey.split("#"));
            if (compareList(temp,keys)){
                return targetData.get(targetKey);
            }
        }
        return null;
    }

    public boolean compareList(List<String> targetList,List<String> sourceList){
        return  targetList.containsAll(sourceList);
    }

    private void sorted(List<List<String>> list,int length){
        Comparator<List<String>> c = (p, o) -> p.get(0).compareTo(o.get(0));
        c = getComparator(c,1,length);
        list.sort(c);
    }
    private Comparator<List<String>> getComparator(Comparator<List<String>> c,int index,int length){
        if (index<(length-1)){
            Comparator<List<String>> a = c.thenComparing((p, o) ->{
                if (p.get(index).length() == o.get(index).length()){
                    return p.get(index).compareTo(o.get(index));
                }else{
                    return p.get(index).length()>o.get(index).length()?1:-1;
                }
            });
            return getComparator(a,index+1,length);
        }else{
            return c.thenComparing((p, o) ->{
                if (p.get(index).length() == o.get(index).length()){
                    return p.get(index).compareTo(o.get(index));
                }else{
                    return p.get(index).length()>o.get(index).length()?1:-1;
                }
            });
        }
    }

    public static void main(String[] args) {
//        String str1 = "11";
//        String str2 = "1";
//        String str3 = "7";
//        String str4 = "7";
//        String str5 = "8";
//        List<String> test = Arrays.asList(str1,str2,str3,str4,str5);
//        Collections.sort(test);
//        System.out.println(StringUtils.join(test,";"));
//        Collections.sort(test, new Comparator<String>() {
//            @Override
//            public int compare(String o1, String o2) {
//                return o1.compareTo(o2);
//            }
//        });
//        System.out.println(FieldEnum.valueOf("time".toUpperCase()).getName());
//        System.out.println("htl_hotelName".toLowerCase());

//        System.out.println(CommonUtils.fmtMicrometer("12312312312.12"));

//        String text = "{\"bizType\":\"A\",\"customreportinfoName\":\"CUSTOMREPORT\",\"dimensionList\":[{\"name\":\"corp\"},{\"name\":\"project\"},{\"name\":\"username\"}],\"statisticalList\":[\"realpay\",\"rcrate\",\"rctimes\"],\"reportStyle\":{\"row\":{\"dimension\":[\"corp\",\"project\",\"username\"],\"statistic\":[]},\"column\":{\"dimension\":[],\"statistic\":[\"realpay\",\"rcrate\",\"rctimes\"]}},\"startTime\":1604740752566,\"endTime\":1607332752566}";
//
//        ObjectMapper objectMapper = new ObjectMapper();
//        try {
//            Map<String, Object> jsonObject = objectMapper.readValue(text, new TypeReference<Map<String, Object>>() {});
//            System.out.println(jsonObject);
//        } catch (JsonProcessingException e) {
//            e.printStackTrace();
//        }
    }

    */
/**
     * 将自定义查询结果缓存到redis
     * @param output
     * @param request
     * @param logonUID
     *//*

    protected void saveResultToRedis(ReportUserDefinedOutput output,CustomRequest request,String logonUID) {
        if (output == null || output.getFilterList() == null || output.getFilterList().isEmpty()) {
            return;
        }
        String expire = QConfigUtils.getNullDefaultValue(RedisKeyConst.ONLINEREPORT_CUSTOMREPORT_OUTPUT_EXPIRETIME_KEY,String.valueOf(RedisKeyConst.ONLINEREPORT_CUSTOMREPORT_OUTPUT_DEFAULT_EXPIRETIME));
        int expireTime = Integer.valueOf(expire);
        String key = String.format(RedisKeyConst.ONLINEREPORT_CUSTOMREPORT_OUTPUT, logonUID.trim().toLowerCase(), JacksonUtil.serialize(request));
        RedisCacheUtils.setValueExpire(key, JacksonUtil.serialize(output),expireTime);
    }

    */
/**
     * 从redis中查询缓存结果
     * @param request
     * @param logonUID
     * @return
     *//*

    protected ReportUserDefinedOutputNew getResultFromRedis(CustomRequest request,String logonUID) {
        ReportUserDefinedOutputNew result = null;
        String key = String.format(RedisKeyConst.ONLINEREPORT_CUSTOMREPORT_OUTPUT, logonUID.trim().toLowerCase(), JacksonUtil.serialize(request));
        String val = RedisCacheUtils.get(key);
        if (StringUtils.isNotEmpty(val)){
            result = (ReportUserDefinedOutputNew) JacksonUtil.deserialize(val,ReportUserDefinedOutputNew.class);
        }
        return result;
    }

}
*/
