package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 拼房数量
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "zoningTrendList",
    "totalQuantity",
    "quantityPercent",
    "totalAmount"
})
public class ZoningInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public ZoningInfo(
        List<ZoningTrendInfo> zoningTrendList,
        Integer totalQuantity,
        Double quantityPercent,
        BigDecimal totalAmount) {
        this.zoningTrendList = zoningTrendList;
        this.totalQuantity = totalQuantity;
        this.quantityPercent = quantityPercent;
        this.totalAmount = totalAmount;
    }

    public ZoningInfo() {
    }

    @JsonProperty("zoningTrendList")
    public List<ZoningTrendInfo> zoningTrendList;

    /**
     * 拼房酒店间夜
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;

    @JsonProperty("quantityPercent")
    public Double quantityPercent;

    /**
     * 拼房酒店消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;

    public List<ZoningTrendInfo> getZoningTrendList() {
        return zoningTrendList;
    }

    public void setZoningTrendList(final List<ZoningTrendInfo> zoningTrendList) {
        this.zoningTrendList = zoningTrendList;
    }

    /**
     * 拼房酒店间夜
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 拼房酒店间夜
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }
    public Double getQuantityPercent() {
        return quantityPercent;
    }

    public void setQuantityPercent(final Double quantityPercent) {
        this.quantityPercent = quantityPercent;
    }

    /**
     * 拼房酒店消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 拼房酒店消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final ZoningInfo other = (ZoningInfo)obj;
        return
            Objects.equal(this.zoningTrendList, other.zoningTrendList) &&
            Objects.equal(this.totalQuantity, other.totalQuantity) &&
            Objects.equal(this.quantityPercent, other.quantityPercent) &&
            Objects.equal(this.totalAmount, other.totalAmount);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.zoningTrendList == null ? 0 : this.zoningTrendList.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.quantityPercent == null ? 0 : this.quantityPercent.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("zoningTrendList", zoningTrendList)
            .add("totalQuantity", totalQuantity)
            .add("quantityPercent", quantityPercent)
            .add("totalAmount", totalAmount)
            .toString();
    }
}
