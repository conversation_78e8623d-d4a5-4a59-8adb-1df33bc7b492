package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.Map;

/**
 * 供应商监测-top
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "fltTopInfo",
    "htlTopInfo",
    "trainTopInfo",
    "carTopInfo",
    "totalRecords",
    "extData"
})
public class OnlineReportSupplierTopResponse implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportSupplierTopResponse(
        Integer responseCode,
        String responseDesc,
        SupplierFltTopInfo fltTopInfo,
        SupplierHtlTopInfo htlTopInfo,
        SupplierTrainTopInfo trainTopInfo,
        SupplierCarTopInfo carTopInfo,
        Integer totalRecords,
        Map<String, String> extData) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.fltTopInfo = fltTopInfo;
        this.htlTopInfo = htlTopInfo;
        this.trainTopInfo = trainTopInfo;
        this.carTopInfo = carTopInfo;
        this.totalRecords = totalRecords;
        this.extData = extData;
        
    }

    public OnlineReportSupplierTopResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    /**
     * 机票top数据
     */
    @JsonProperty("fltTopInfo")
    public SupplierFltTopInfo fltTopInfo;

    /**
     * 机票top数据
     */
    @JsonProperty("htlTopInfo")
    public SupplierHtlTopInfo htlTopInfo;

    /**
     * 机票top数据
     */
    @JsonProperty("trainTopInfo")
    public SupplierTrainTopInfo trainTopInfo;

    /**
     * 机票top数据
     */
    @JsonProperty("carTopInfo")
    public SupplierCarTopInfo carTopInfo;

    /**
     * 数据总条数
     */
    @JsonProperty("totalRecords")
    public Integer totalRecords;

    @JsonProperty("extData")
    public Map<String, String> extData;



    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }

    /**
     * 机票top数据
     */
    public SupplierFltTopInfo getFltTopInfo() {
        return fltTopInfo;
    }

    /**
     * 机票top数据
     */
    public void setFltTopInfo(final SupplierFltTopInfo fltTopInfo) {
        this.fltTopInfo = fltTopInfo;
    }

    /**
     * 机票top数据
     */
    public SupplierHtlTopInfo getHtlTopInfo() {
        return htlTopInfo;
    }

    /**
     * 机票top数据
     */
    public void setHtlTopInfo(final SupplierHtlTopInfo htlTopInfo) {
        this.htlTopInfo = htlTopInfo;
    }

    /**
     * 机票top数据
     */
    public SupplierTrainTopInfo getTrainTopInfo() {
        return trainTopInfo;
    }

    /**
     * 机票top数据
     */
    public void setTrainTopInfo(final SupplierTrainTopInfo trainTopInfo) {
        this.trainTopInfo = trainTopInfo;
    }

    /**
     * 机票top数据
     */
    public SupplierCarTopInfo getCarTopInfo() {
        return carTopInfo;
    }

    /**
     * 机票top数据
     */
    public void setCarTopInfo(final SupplierCarTopInfo carTopInfo) {
        this.carTopInfo = carTopInfo;
    }

    /**
     * 数据总条数
     */
    public Integer getTotalRecords() {
        return totalRecords;
    }

    /**
     * 数据总条数
     */
    public void setTotalRecords(final Integer totalRecords) {
        this.totalRecords = totalRecords;
    }
    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }



    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportSupplierTopResponse other = (OnlineReportSupplierTopResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc) &&
            Objects.equal(this.fltTopInfo, other.fltTopInfo) &&
            Objects.equal(this.htlTopInfo, other.htlTopInfo) &&
            Objects.equal(this.trainTopInfo, other.trainTopInfo) &&
            Objects.equal(this.carTopInfo, other.carTopInfo) &&
            Objects.equal(this.totalRecords, other.totalRecords) &&
            Objects.equal(this.extData, other.extData) ;
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.fltTopInfo == null ? 0 : this.fltTopInfo.hashCode());
        result = 31 * result + (this.htlTopInfo == null ? 0 : this.htlTopInfo.hashCode());
        result = 31 * result + (this.trainTopInfo == null ? 0 : this.trainTopInfo.hashCode());
        result = 31 * result + (this.carTopInfo == null ? 0 : this.carTopInfo.hashCode());
        result = 31 * result + (this.totalRecords == null ? 0 : this.totalRecords.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());
        

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            .add("fltTopInfo", fltTopInfo)
            .add("htlTopInfo", htlTopInfo)
            .add("trainTopInfo", trainTopInfo)
            .add("carTopInfo", carTopInfo)
            .add("totalRecords", totalRecords)
            .add("extData", extData)
            
            .toString();
    }
}
