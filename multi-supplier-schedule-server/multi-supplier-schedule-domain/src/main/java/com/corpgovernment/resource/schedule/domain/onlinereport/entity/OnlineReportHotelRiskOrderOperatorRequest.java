package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 风险订单酒店-操作请求request
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "corpId",
    "operatorList"
})
public class OnlineReportHotelRiskOrderOperatorRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportHotelRiskOrderOperatorRequest(
        String corpId,
        List<HotelRiskOrderOperator> operatorList) {
        this.corpId = corpId;
        this.operatorList = operatorList;
    }

    public OnlineReportHotelRiskOrderOperatorRequest() {
    }

    @JsonProperty("corpId")
    public String corpId;

    /**
     * 操作的记录
     */
    @JsonProperty("operatorList")
    public List<HotelRiskOrderOperator> operatorList;

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(final String corpId) {
        this.corpId = corpId;
    }

    /**
     * 操作的记录
     */
    public List<HotelRiskOrderOperator> getOperatorList() {
        return operatorList;
    }

    /**
     * 操作的记录
     */
    public void setOperatorList(final List<HotelRiskOrderOperator> operatorList) {
        this.operatorList = operatorList;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportHotelRiskOrderOperatorRequest other = (OnlineReportHotelRiskOrderOperatorRequest)obj;
        return
            Objects.equal(this.corpId, other.corpId) &&
            Objects.equal(this.operatorList, other.operatorList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.corpId == null ? 0 : this.corpId.hashCode());
        result = 31 * result + (this.operatorList == null ? 0 : this.operatorList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("corpId", corpId)
            .add("operatorList", operatorList)
            .toString();
    }
}
