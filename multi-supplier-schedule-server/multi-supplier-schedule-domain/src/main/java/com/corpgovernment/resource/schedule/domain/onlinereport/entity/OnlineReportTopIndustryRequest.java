package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.Map;

/**
 * **行业排名***************
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "lang",
    "topLimit",
    "extData"
})
public class OnlineReportTopIndustryRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportTopIndustryRequest(
        String lang,
        Integer topLimit,
        Map<String, String> extData) {
        this.lang = lang;
        this.topLimit = topLimit;
        this.extData = extData;
    }

    public OnlineReportTopIndustryRequest() {
    }

    /**
     * 语言
     */
    @JsonProperty("lang")
    public String lang;

    @JsonProperty("topLimit")
    public Integer topLimit;

    /**
     * 扩展字段
     */
    @JsonProperty("extData")
    public Map<String, String> extData;

    /**
     * 语言
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }
    public Integer getTopLimit() {
        return topLimit;
    }

    public void setTopLimit(final Integer topLimit) {
        this.topLimit = topLimit;
    }

    /**
     * 扩展字段
     */
    public Map<String, String> getExtData() {
        return extData;
    }

    /**
     * 扩展字段
     */
    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTopIndustryRequest other = (OnlineReportTopIndustryRequest)obj;
        return
            Objects.equal(this.lang, other.lang) &&
            Objects.equal(this.topLimit, other.topLimit) &&
            Objects.equal(this.extData, other.extData);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.topLimit == null ? 0 : this.topLimit.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("lang", lang)
            .add("topLimit", topLimit)
            .add("extData", extData)
            .toString();
    }
}
