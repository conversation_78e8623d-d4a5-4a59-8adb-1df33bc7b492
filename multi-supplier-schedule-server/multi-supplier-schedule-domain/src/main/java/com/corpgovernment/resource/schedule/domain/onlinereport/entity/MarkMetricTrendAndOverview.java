package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 评分指标
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "markMetricTrend",
    "markMetricInfo"
})
public class MarkMetricTrendAndOverview implements Serializable {
    private static final long serialVersionUID = 1L;





    public MarkMetricTrendAndOverview(
        List<MarkMetricInfo> markMetricTrend,
        MarkMetricInfo markMetricInfo) {
        this.markMetricTrend = markMetricTrend;
        this.markMetricInfo = markMetricInfo;
    }

    public MarkMetricTrendAndOverview() {
    }

    /**
     * 趋势
     */
    @JsonProperty("markMetricTrend")
    public List<MarkMetricInfo> markMetricTrend;

    /**
     * 汇总
     */
    @JsonProperty("markMetricInfo")
    public MarkMetricInfo markMetricInfo;

    /**
     * 趋势
     */
    public List<MarkMetricInfo> getMarkMetricTrend() {
        return markMetricTrend;
    }

    /**
     * 趋势
     */
    public void setMarkMetricTrend(final List<MarkMetricInfo> markMetricTrend) {
        this.markMetricTrend = markMetricTrend;
    }

    /**
     * 汇总
     */
    public MarkMetricInfo getMarkMetricInfo() {
        return markMetricInfo;
    }

    /**
     * 汇总
     */
    public void setMarkMetricInfo(final MarkMetricInfo markMetricInfo) {
        this.markMetricInfo = markMetricInfo;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final MarkMetricTrendAndOverview other = (MarkMetricTrendAndOverview)obj;
        return
            Objects.equal(this.markMetricTrend, other.markMetricTrend) &&
            Objects.equal(this.markMetricInfo, other.markMetricInfo);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.markMetricTrend == null ? 0 : this.markMetricTrend.hashCode());
        result = 31 * result + (this.markMetricInfo == null ? 0 : this.markMetricInfo.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("markMetricTrend", markMetricTrend)
            .add("markMetricInfo", markMetricInfo)
            .toString();
    }
}
