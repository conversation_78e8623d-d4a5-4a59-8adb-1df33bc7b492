package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 行为分析-酒店间夜均价分布
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "range",
    "totalQuantity",
    "quantityPercent",
    "corpQuantityPercent",
    "industryQuantityPercent",
    "totalAmount",
    "amountPercent",
    "corpAmountPercent",
    "industryAmountPercent"
})
public class AvgNightPriceRangeInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public AvgNightPriceRangeInfo(
        String range,
        Integer totalQuantity,
        Double quantityPercent,
        Double corpQuantityPercent,
        Double industryQuantityPercent,
        BigDecimal totalAmount,
        Double amountPercent,
        Double corpAmountPercent,
        Double industryAmountPercent) {
        this.range = range;
        this.totalQuantity = totalQuantity;
        this.quantityPercent = quantityPercent;
        this.corpQuantityPercent = corpQuantityPercent;
        this.industryQuantityPercent = industryQuantityPercent;
        this.totalAmount = totalAmount;
        this.amountPercent = amountPercent;
        this.corpAmountPercent = corpAmountPercent;
        this.industryAmountPercent = industryAmountPercent;
    }

    public AvgNightPriceRangeInfo() {
    }

    /**
     * 间夜均价区间
     */
    @JsonProperty("range")
    public String range;

    /**
     * 间夜数
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;

    /**
     * 间夜占比
     */
    @JsonProperty("quantityPercent")
    public Double quantityPercent;

    @JsonProperty("corpQuantityPercent")
    public Double corpQuantityPercent;

    @JsonProperty("industryQuantityPercent")
    public Double industryQuantityPercent;

    /**
     * 消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;

    /**
     * 金额占比
     */
    @JsonProperty("amountPercent")
    public Double amountPercent;

    @JsonProperty("corpAmountPercent")
    public Double corpAmountPercent;

    @JsonProperty("industryAmountPercent")
    public Double industryAmountPercent;

    /**
     * 间夜均价区间
     */
    public String getRange() {
        return range;
    }

    /**
     * 间夜均价区间
     */
    public void setRange(final String range) {
        this.range = range;
    }

    /**
     * 间夜数
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 间夜数
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 间夜占比
     */
    public Double getQuantityPercent() {
        return quantityPercent;
    }

    /**
     * 间夜占比
     */
    public void setQuantityPercent(final Double quantityPercent) {
        this.quantityPercent = quantityPercent;
    }
    public Double getCorpQuantityPercent() {
        return corpQuantityPercent;
    }

    public void setCorpQuantityPercent(final Double corpQuantityPercent) {
        this.corpQuantityPercent = corpQuantityPercent;
    }
    public Double getIndustryQuantityPercent() {
        return industryQuantityPercent;
    }

    public void setIndustryQuantityPercent(final Double industryQuantityPercent) {
        this.industryQuantityPercent = industryQuantityPercent;
    }

    /**
     * 消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 金额占比
     */
    public Double getAmountPercent() {
        return amountPercent;
    }

    /**
     * 金额占比
     */
    public void setAmountPercent(final Double amountPercent) {
        this.amountPercent = amountPercent;
    }
    public Double getCorpAmountPercent() {
        return corpAmountPercent;
    }

    public void setCorpAmountPercent(final Double corpAmountPercent) {
        this.corpAmountPercent = corpAmountPercent;
    }
    public Double getIndustryAmountPercent() {
        return industryAmountPercent;
    }

    public void setIndustryAmountPercent(final Double industryAmountPercent) {
        this.industryAmountPercent = industryAmountPercent;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final AvgNightPriceRangeInfo other = (AvgNightPriceRangeInfo)obj;
        return
            Objects.equal(this.range, other.range) &&
            Objects.equal(this.totalQuantity, other.totalQuantity) &&
            Objects.equal(this.quantityPercent, other.quantityPercent) &&
            Objects.equal(this.corpQuantityPercent, other.corpQuantityPercent) &&
            Objects.equal(this.industryQuantityPercent, other.industryQuantityPercent) &&
            Objects.equal(this.totalAmount, other.totalAmount) &&
            Objects.equal(this.amountPercent, other.amountPercent) &&
            Objects.equal(this.corpAmountPercent, other.corpAmountPercent) &&
            Objects.equal(this.industryAmountPercent, other.industryAmountPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.range == null ? 0 : this.range.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.quantityPercent == null ? 0 : this.quantityPercent.hashCode());
        result = 31 * result + (this.corpQuantityPercent == null ? 0 : this.corpQuantityPercent.hashCode());
        result = 31 * result + (this.industryQuantityPercent == null ? 0 : this.industryQuantityPercent.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.amountPercent == null ? 0 : this.amountPercent.hashCode());
        result = 31 * result + (this.corpAmountPercent == null ? 0 : this.corpAmountPercent.hashCode());
        result = 31 * result + (this.industryAmountPercent == null ? 0 : this.industryAmountPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("range", range)
            .add("totalQuantity", totalQuantity)
            .add("quantityPercent", quantityPercent)
            .add("corpQuantityPercent", corpQuantityPercent)
            .add("industryQuantityPercent", industryQuantityPercent)
            .add("totalAmount", totalAmount)
            .add("amountPercent", amountPercent)
            .add("corpAmountPercent", corpAmountPercent)
            .add("industryAmountPercent", industryAmountPercent)
            .toString();
    }
}
