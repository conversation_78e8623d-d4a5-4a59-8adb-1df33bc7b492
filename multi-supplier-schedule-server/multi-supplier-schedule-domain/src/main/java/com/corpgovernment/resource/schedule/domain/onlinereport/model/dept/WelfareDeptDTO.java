package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2025/1/8
 */
@Data
public class WelfareDeptDTO {
    private String aggId;
    private String aggType;
    /**
     * 心程贝发放金额
     */
    @Column(name = "allotAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal allotAmount;
    /**
     * 心程贝回收金额
     */
    @Column(name = "recycleAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal returnAmount;
    /**
     * 心程贝退还金额
     */
    @Column(name = "returnAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal refundAmount;
    /**
     * 心程贝扣减金额
     */
    @Column(name = "deductAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal deductAmount;
    /**
     * 心程贝余额（个人账户）
     */
    private BigDecimal totalBalance;

}
