package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 行为分析-预订方式
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "bookType",
    "totalOrderCount",
    "orderPercent",
    "totalAmount",
    "amountPercent"
})
public class BookTypeInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public BookTypeInfo(
        String bookType,
        Integer totalOrderCount,
        Double orderPercent,
        BigDecimal totalAmount,
        Double amountPercent) {
        this.bookType = bookType;
        this.totalOrderCount = totalOrderCount;
        this.orderPercent = orderPercent;
        this.totalAmount = totalAmount;
        this.amountPercent = amountPercent;
    }

    public BookTypeInfo() {
    }

    /**
     * 预订方式
     */
    @JsonProperty("bookType")
    public String bookType;

    /**
     * 订单数量
     */
    @JsonProperty("totalOrderCount")
    public Integer totalOrderCount;

    /**
     * 订单占比
     */
    @JsonProperty("orderPercent")
    public Double orderPercent;

    /**
     * 消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;

    /**
     * 金额占比
     */
    @JsonProperty("amountPercent")
    public Double amountPercent;

    /**
     * 预订方式
     */
    public String getBookType() {
        return bookType;
    }

    /**
     * 预订方式
     */
    public void setBookType(final String bookType) {
        this.bookType = bookType;
    }

    /**
     * 订单数量
     */
    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }

    /**
     * 订单数量
     */
    public void setTotalOrderCount(final Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }

    /**
     * 订单占比
     */
    public Double getOrderPercent() {
        return orderPercent;
    }

    /**
     * 订单占比
     */
    public void setOrderPercent(final Double orderPercent) {
        this.orderPercent = orderPercent;
    }

    /**
     * 消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 金额占比
     */
    public Double getAmountPercent() {
        return amountPercent;
    }

    /**
     * 金额占比
     */
    public void setAmountPercent(final Double amountPercent) {
        this.amountPercent = amountPercent;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final BookTypeInfo other = (BookTypeInfo)obj;
        return
            Objects.equal(this.bookType, other.bookType) &&
            Objects.equal(this.totalOrderCount, other.totalOrderCount) &&
            Objects.equal(this.orderPercent, other.orderPercent) &&
            Objects.equal(this.totalAmount, other.totalAmount) &&
            Objects.equal(this.amountPercent, other.amountPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.bookType == null ? 0 : this.bookType.hashCode());
        result = 31 * result + (this.totalOrderCount == null ? 0 : this.totalOrderCount.hashCode());
        result = 31 * result + (this.orderPercent == null ? 0 : this.orderPercent.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.amountPercent == null ? 0 : this.amountPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("bookType", bookType)
            .add("totalOrderCount", totalOrderCount)
            .add("orderPercent", orderPercent)
            .add("totalAmount", totalAmount)
            .add("amountPercent", amountPercent)
            .toString();
    }
}
