package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OnlineReportTrainOrderInfo implements Serializable {
    private static final long serialVersionUID = 1L;




    @JsonProperty("orderId")
    public Long orderId;

    @JsonProperty("orderStatus")
    public String orderStatus;

    @JsonProperty("orderDate")
    public String orderDate;

    @JsonProperty("corpCorporation")
    public String corpCorporation;

    @JsonProperty("corpName")
    public String corpName;

    @JsonProperty("companygroupid")
    public String companygroupid;

    @JsonProperty("companygroup")
    public String companygroup;

    @JsonProperty("accountId")
    public Long accountId;

    @JsonProperty("accountCode")
    public String accountCode;

    @JsonProperty("accountName")
    public String accountName;

    @JsonProperty("subAccountId")
    public Long subAccountId;

    @JsonProperty("subAccountCode")
    public String subAccountCode;

    @JsonProperty("subAccountName")
    public String subAccountName;

    @JsonProperty("industryType")
    public String industryType;

    @JsonProperty("industryTypeName")
    public String industryTypeName;

    @JsonProperty("uid")
    public String uid;

    @JsonProperty("userName")
    public String userName;

    @JsonProperty("employeId")
    public String employeId;

    @JsonProperty("workCity")
    public String workCity;

    @JsonProperty("rankName")
    public String rankName;

    @JsonProperty("costCenter1")
    public String costCenter1;

    @JsonProperty("costCenter2")
    public String costCenter2;

    @JsonProperty("costCenter3")
    public String costCenter3;

    @JsonProperty("costCenter4")
    public String costCenter4;

    @JsonProperty("costCenter5")
    public String costCenter5;

    @JsonProperty("costCenter6")
    public String costCenter6;

    @JsonProperty("dept1")
    public String dept1;

    @JsonProperty("dept2")
    public String dept2;

    @JsonProperty("dept3")
    public String dept3;

    @JsonProperty("dept4")
    public String dept4;

    @JsonProperty("dept5")
    public String dept5;

    @JsonProperty("dept6")
    public String dept6;

    @JsonProperty("dept7")
    public String dept7;

    @JsonProperty("dept8")
    public String dept8;

    @JsonProperty("dept9")
    public String dept9;

    @JsonProperty("dept10")
    public String dept10;

    @JsonProperty("feeType")
    public String feeType;

    @JsonProperty("isOnline")
    public String isOnline;

    @JsonProperty("prepayType")
    public String prepayType;

    @JsonProperty("acbPrepayType")
    public String acbPrepayType;

    @JsonProperty("bosstype")
    public String bosstype;

    @JsonProperty("tripId")
    public String tripId;

    @JsonProperty("journeyNo")
    public String journeyNo;

    @JsonProperty("journeyReason")
    public String journeyReason;

    @JsonProperty("journeyReasonCode")
    public String journeyReasonCode;

    @JsonProperty("projectCode")
    public String projectCode;

    @JsonProperty("project")
    public String project;

    @JsonProperty("verbalAuthorize")
    public String verbalAuthorize;

    @JsonProperty("confirmPerson")
    public String confirmPerson;

    @JsonProperty("confirmType")
    public String confirmType;

    @JsonProperty("confirmPerson2")
    public String confirmPerson2;

    @JsonProperty("confirmType2")
    public String confirmType2;

    @JsonProperty("approvalpasstime")
    public String approvalpasstime;

    @JsonProperty("actionname")
    public String actionname;

    @JsonProperty("defineflag")
    public String defineflag;

    @JsonProperty("defineflag2")
    public String defineflag2;

    @JsonProperty("confirmtimepoint")
    public String confirmtimepoint;

    @JsonProperty("confirmtimepoint2")
    public String confirmtimepoint2;

    @JsonProperty("auditorid")
    public String auditorid;

    @JsonProperty("auditorid2")
    public String auditorid2;

    @JsonProperty("groupMonth")
    public Integer groupMonth;

    @JsonProperty("printMonth")
    public Integer printMonth;

    @JsonProperty("printYear")
    public Integer printYear;

    @JsonProperty("printTime")
    public String printTime;

    @JsonProperty("passengerId")
    public String passengerId;

    @JsonProperty("passengerName")
    public String passengerName;

    @JsonProperty("passengerNamePy")
    public String passengerNamePy;

    @JsonProperty("persons")
    public Integer persons;

    @JsonProperty("printTicketType")
    public String printTicketType;

    @JsonProperty("quantity")
    public Integer quantity;

    @JsonProperty("changeQuantity")
    public Integer changeQuantity;

    @JsonProperty("changeStatus")
    public String changeStatus;

    @JsonProperty("refundStatus")
    public String refundStatus;

    @JsonProperty("ticketType")
    public String ticketType;

    @JsonProperty("trainName")
    public String trainName;

    @JsonProperty("firstSeatTypeName")
    public String firstSeatTypeName;

    @JsonProperty("seatType")
    public String seatType;

    @JsonProperty("realPay")
    public BigDecimal realPay;

    @JsonProperty("ticketPrice")
    public BigDecimal ticketPrice;

    @JsonProperty("insuranceFee")
    public BigDecimal insuranceFee;

    @JsonProperty("serviceFee")
    public BigDecimal serviceFee;

    @JsonProperty("refundTicketFee")
    public BigDecimal refundTicketFee;

    @JsonProperty("deliverFee")
    public BigDecimal deliverFee;

    @JsonProperty("paperTicketFee")
    public BigDecimal paperTicketFee;

    @JsonProperty("dealChangeServiceFee")
    public BigDecimal dealChangeServiceFee;

    @JsonProperty("grabServiceFee")
    public BigDecimal grabServiceFee;

    @JsonProperty("afterServiceFee")
    public BigDecimal afterServiceFee;

    @JsonProperty("afterchangeservicefee")
    public BigDecimal afterchangeservicefee;

    @JsonProperty("aftertaketicketfee")
    public BigDecimal aftertaketicketfee;

    @JsonProperty("afteraftertaketicketfee")
    public BigDecimal afteraftertaketicketfee;

    @JsonProperty("changebalance")
    public BigDecimal changebalance;

    @JsonProperty("departureDateTime")
    public String departureDateTime;

    @JsonProperty("departureStationName")
    public String departureStationName;

    @JsonProperty("departureCityId")
    public String departureCityId;

    @JsonProperty("departureCityName")
    public String departureCityName;

    @JsonProperty("departureProvinceName")
    public String departureProvinceName;

    @JsonProperty("arrivalDateTime")
    public String arrivalDateTime;

    @JsonProperty("arrivalStationName")
    public String arrivalStationName;

    @JsonProperty("arrivalCityId")
    public String arrivalCityId;

    @JsonProperty("arrivalCityName")
    public String arrivalCityName;

    @JsonProperty("arrivalProvinceName")
    public String arrivalProvinceName;

    @JsonProperty("lineCity")
    public String lineCity;

    @JsonProperty("isRc")
    public String isRc;

    @JsonProperty("seattypeRccodeid")
    public String seattypeRccodeid;

    @JsonProperty("seattypeRccodename")
    public String seattypeRccodename;

    @JsonProperty("ticketRccodeid")
    public String ticketRccodeid;

    @JsonProperty("ticketRccodename")
    public String ticketRccodename;

    @JsonProperty("userdefinedRid")
    public String userdefinedRid;

    @JsonProperty("userdefinedRc")
    public String userdefinedRc;

    @JsonProperty("oCurrency")
    public String oCurrency;

    @JsonProperty("oExchangerate")
    public BigDecimal oExchangerate;

    @JsonProperty("firstSeatTypeNameEn")
    public String firstSeatTypeNameEn;

    @JsonProperty("departureStationNameEn")
    public String departureStationNameEn;

    @JsonProperty("departureCityNameEn")
    public String departureCityNameEn;

    @JsonProperty("departureProvinceNameEn")
    public String departureProvinceNameEn;

    @JsonProperty("arrivalStationNameEn")
    public String arrivalStationNameEn;

    @JsonProperty("arrivalCityNameEn")
    public String arrivalCityNameEn;

    @JsonProperty("arrivalProvinceNameEn")
    public String arrivalProvinceNameEn;

    @JsonProperty("lineCityEn")
    public String lineCityEn;

    @JsonProperty("refundRc")
    public String refundRc;

    @JsonProperty("refundRcDesc")
    public String refundRcDesc;

    @JsonProperty("estFee12306")
    public BigDecimal estFee12306;

    @JsonProperty("taketicketstatus")
    public String taketicketstatus;

    @JsonProperty("refundQuantity")
    public Integer refundQuantity;

    @JsonProperty("carbonEmission")
    public Double carbonEmission;

    @JsonProperty("medianCarbons")
    public Double medianCarbons;

    @JsonProperty("carbonSave")
    public Double carbonSave;

    @JsonProperty("stdIndustry1")
    public String stdIndustry1;

    @JsonProperty("stdIndustry2")
    public String stdIndustry2;

    @JsonProperty("passengerEid")
    public String passengerEid;

}
