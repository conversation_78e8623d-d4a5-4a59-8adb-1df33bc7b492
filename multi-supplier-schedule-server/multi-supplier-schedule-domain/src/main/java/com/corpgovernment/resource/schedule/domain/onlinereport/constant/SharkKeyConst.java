package com.corpgovernment.resource.schedule.domain.onlinereport.constant;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
public interface SharkKeyConst {
    /**
     * 报表key不能为空
     */
    String REPORT_TEMPLATE_REPORT_KEY_NOT_NULL = "Report.Template.ReportKey";
    /**
     * 模板名称已存在
     */
    String REPORT_TEMPLATE_NAME_HAS_EXIST = "report_template_notice";
    /**
     * 模板名称过长（建议不超过10个字符）
     */
    String REPORT_TEMPLATE_NAME_IS_TOO_LONG = "report_template";

    /**
     * 模版编号列表长度必须大于0
     */
    String REPORT_TEMPLATE_NO_LIST_SIZE_NOT_BE_EMPTY = "Report.TemplateNoList.Size";
}
