package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 火车票费用维度
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "afteraftertaketicketfee",
    "afterchangeservicefee",
    "aftertaketicketfee",
    "afterServiceFee",
    "changebalance",
    "delayRescheduleFee",
    "estFee12306",
    "dealChangeServiceFee",
    "sendTicketFee",
    "grabServiceFee",
    "insuranceFee",
    "paperTicketFee",
    "refundTicketFee",
    "serviceFee",
    "ticketPrice",
    "totalV"
})
public class TrainDataDetailData implements Serializable {
    private static final long serialVersionUID = 1L;




    public TrainDataDetailData(
        String afteraftertaketicketfee,
        String afterchangeservicefee,
        String aftertaketicketfee,
        String afterServiceFee,
        String changebalance,
        String delayRescheduleFee,
        String estFee12306,
        String dealChangeServiceFee,
        String sendTicketFee,
        String grabServiceFee,
        String insuranceFee,
        String paperTicketFee,
        String refundTicketFee,
        String serviceFee,
        String ticketPrice,
        String totalV) {
        this.afteraftertaketicketfee = afteraftertaketicketfee;
        this.afterchangeservicefee = afterchangeservicefee;
        this.aftertaketicketfee = aftertaketicketfee;
        this.afterServiceFee = afterServiceFee;
        this.changebalance = changebalance;
        this.delayRescheduleFee = delayRescheduleFee;
        this.changebalance = changebalance;
        this.dealChangeServiceFee = dealChangeServiceFee;
        this.sendTicketFee = sendTicketFee;
        this.grabServiceFee = grabServiceFee;
        this.insuranceFee = insuranceFee;
        this.paperTicketFee = paperTicketFee;
        this.refundTicketFee = refundTicketFee;
        this.serviceFee = serviceFee;
        this.ticketPrice = ticketPrice;
        this.totalV = totalV;
    }

    public TrainDataDetailData() {
    }

    /**
     * 后收后取票服务费
     */
    @JsonProperty("afteraftertaketicketfee")
    public String afteraftertaketicketfee;

    /**
     * 后收改签服务费
     */
    @JsonProperty("afterchangeservicefee")
    public String afterchangeservicefee;

    /**
     * 代取票人工费
     */
    @JsonProperty("aftertaketicketfee")
    public String aftertaketicketfee;

    /**
     * 后收服务费
     */
    @JsonProperty("afterServiceFee")
    public String afterServiceFee;

    /**
     * 改签时票面差价
     */
    @JsonProperty("changebalance")
    public String changebalance;

    /**
     * 改签费
     */
    @JsonProperty("delayRescheduleFee")
    public String delayRescheduleFee;

    /**
     * 改签退款手续费
     */
    @JsonProperty("estFee12306")
    public String estFee12306;

    /**
     * 改签时商旅收取的管理服务费
     */
    @JsonProperty("dealChangeServiceFee")
    public String dealChangeServiceFee;

    /**
     * 配送费
     */
    @JsonProperty("sendTicketFee")
    public String sendTicketFee;

    /**
     * 抢票费
     */
    @JsonProperty("grabServiceFee")
    public String grabServiceFee;

    /**
     * 保险费
     */
    @JsonProperty("insuranceFee")
    public String insuranceFee;

    /**
     * 纸质出票费
     */
    @JsonProperty("paperTicketFee")
    public String paperTicketFee;

    /**
     * 退票时退补给客户的金额
     */
    @JsonProperty("refundTicketFee")
    public String refundTicketFee;

    /**
     * 基础服务费
     */
    @JsonProperty("serviceFee")
    public String serviceFee;

    /**
     * 原始出票金额(票价)
     */
    @JsonProperty("ticketPrice")
    public String ticketPrice;

    /**
     * 总计
     */
    @JsonProperty("totalV")
    public String totalV;

    public String getDelayRescheduleFee() {
        return delayRescheduleFee;
    }

    public void setDelayRescheduleFee(String delayRescheduleFee) {
        this.delayRescheduleFee = delayRescheduleFee;
    }

    public String getEstFee12306() {
        return estFee12306;
    }

    public void setEstFee12306(String estFee12306) {
        this.estFee12306 = estFee12306;
    }

    /**
     * 后收后取票服务费
     */
    public String getAfteraftertaketicketfee() {
        return afteraftertaketicketfee;
    }

    /**
     * 后收后取票服务费
     */
    public void setAfteraftertaketicketfee(final String afteraftertaketicketfee) {
        this.afteraftertaketicketfee = afteraftertaketicketfee;
    }

    /**
     * 后收改签服务费
     */
    public String getAfterchangeservicefee() {
        return afterchangeservicefee;
    }

    /**
     * 后收改签服务费
     */
    public void setAfterchangeservicefee(final String afterchangeservicefee) {
        this.afterchangeservicefee = afterchangeservicefee;
    }

    /**
     * 代取票人工费
     */
    public String getAftertaketicketfee() {
        return aftertaketicketfee;
    }

    /**
     * 代取票人工费
     */
    public void setAftertaketicketfee(final String aftertaketicketfee) {
        this.aftertaketicketfee = aftertaketicketfee;
    }

    /**
     * 后收服务费
     */
    public String getAfterServiceFee() {
        return afterServiceFee;
    }

    /**
     * 后收服务费
     */
    public void setAfterServiceFee(final String afterServiceFee) {
        this.afterServiceFee = afterServiceFee;
    }

    /**
     * 改签时票面差价
     */
    public String getChangebalance() {
        return changebalance;
    }

    /**
     * 改签时票面差价
     */
    public void setChangebalance(final String changebalance) {
        this.changebalance = changebalance;
    }

    /**
     * 改签时商旅收取的管理服务费
     */
    public String getDealChangeServiceFee() {
        return dealChangeServiceFee;
    }

    /**
     * 改签时商旅收取的管理服务费
     */
    public void setDealChangeServiceFee(final String dealChangeServiceFee) {
        this.dealChangeServiceFee = dealChangeServiceFee;
    }

    /**
     * 配送费
     */
    public String getSendTicketFee() {
        return sendTicketFee;
    }

    /**
     * 配送费
     */
    public void setSendTicketFee(final String sendTicketFee) {
        this.sendTicketFee = sendTicketFee;
    }

    /**
     * 抢票费
     */
    public String getGrabServiceFee() {
        return grabServiceFee;
    }

    /**
     * 抢票费
     */
    public void setGrabServiceFee(final String grabServiceFee) {
        this.grabServiceFee = grabServiceFee;
    }

    /**
     * 保险费
     */
    public String getInsuranceFee() {
        return insuranceFee;
    }

    /**
     * 保险费
     */
    public void setInsuranceFee(final String insuranceFee) {
        this.insuranceFee = insuranceFee;
    }

    /**
     * 纸质出票费
     */
    public String getPaperTicketFee() {
        return paperTicketFee;
    }

    /**
     * 纸质出票费
     */
    public void setPaperTicketFee(final String paperTicketFee) {
        this.paperTicketFee = paperTicketFee;
    }

    /**
     * 退票时退补给客户的金额
     */
    public String getRefundTicketFee() {
        return refundTicketFee;
    }

    /**
     * 退票时退补给客户的金额
     */
    public void setRefundTicketFee(final String refundTicketFee) {
        this.refundTicketFee = refundTicketFee;
    }

    /**
     * 基础服务费
     */
    public String getServiceFee() {
        return serviceFee;
    }

    /**
     * 基础服务费
     */
    public void setServiceFee(final String serviceFee) {
        this.serviceFee = serviceFee;
    }

    /**
     * 原始出票金额(票价)
     */
    public String getTicketPrice() {
        return ticketPrice;
    }

    /**
     * 原始出票金额(票价)
     */
    public void setTicketPrice(final String ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    /**
     * 总计
     */
    public String getTotalV() {
        return totalV;
    }

    /**
     * 总计
     */
    public void setTotalV(final String totalV) {
        this.totalV = totalV;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TrainDataDetailData other = (TrainDataDetailData)obj;
        return
            Objects.equal(this.afteraftertaketicketfee, other.afteraftertaketicketfee) &&
            Objects.equal(this.afterchangeservicefee, other.afterchangeservicefee) &&
            Objects.equal(this.aftertaketicketfee, other.aftertaketicketfee) &&
            Objects.equal(this.afterServiceFee, other.afterServiceFee) &&
            Objects.equal(this.changebalance, other.changebalance) &&
            Objects.equal(this.delayRescheduleFee, other.delayRescheduleFee) &&
            Objects.equal(this.estFee12306, other.estFee12306) &&
            Objects.equal(this.dealChangeServiceFee, other.dealChangeServiceFee) &&
            Objects.equal(this.sendTicketFee, other.sendTicketFee) &&
            Objects.equal(this.grabServiceFee, other.grabServiceFee) &&
            Objects.equal(this.insuranceFee, other.insuranceFee) &&
            Objects.equal(this.paperTicketFee, other.paperTicketFee) &&
            Objects.equal(this.refundTicketFee, other.refundTicketFee) &&
            Objects.equal(this.serviceFee, other.serviceFee) &&
            Objects.equal(this.ticketPrice, other.ticketPrice) &&
            Objects.equal(this.totalV, other.totalV);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.afteraftertaketicketfee == null ? 0 : this.afteraftertaketicketfee.hashCode());
        result = 31 * result + (this.afterchangeservicefee == null ? 0 : this.afterchangeservicefee.hashCode());
        result = 31 * result + (this.aftertaketicketfee == null ? 0 : this.aftertaketicketfee.hashCode());
        result = 31 * result + (this.afterServiceFee == null ? 0 : this.afterServiceFee.hashCode());
        result = 31 * result + (this.changebalance == null ? 0 : this.changebalance.hashCode());
        result = 31 * result + (this.delayRescheduleFee == null ? 0 : this.delayRescheduleFee.hashCode());
        result = 31 * result + (this.estFee12306 == null ? 0 : this.estFee12306.hashCode());
        result = 31 * result + (this.dealChangeServiceFee == null ? 0 : this.dealChangeServiceFee.hashCode());
        result = 31 * result + (this.sendTicketFee == null ? 0 : this.sendTicketFee.hashCode());
        result = 31 * result + (this.grabServiceFee == null ? 0 : this.grabServiceFee.hashCode());
        result = 31 * result + (this.insuranceFee == null ? 0 : this.insuranceFee.hashCode());
        result = 31 * result + (this.paperTicketFee == null ? 0 : this.paperTicketFee.hashCode());
        result = 31 * result + (this.refundTicketFee == null ? 0 : this.refundTicketFee.hashCode());
        result = 31 * result + (this.serviceFee == null ? 0 : this.serviceFee.hashCode());
        result = 31 * result + (this.ticketPrice == null ? 0 : this.ticketPrice.hashCode());
        result = 31 * result + (this.totalV == null ? 0 : this.totalV.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("afteraftertaketicketfee", afteraftertaketicketfee)
            .add("afterchangeservicefee", afterchangeservicefee)
            .add("aftertaketicketfee", aftertaketicketfee)
            .add("afterServiceFee", afterServiceFee)
            .add("changebalance", changebalance)
            .add("delayRescheduleFee", delayRescheduleFee)
            .add("estFee12306", estFee12306)
            .add("dealChangeServiceFee", dealChangeServiceFee)
            .add("sendTicketFee", sendTicketFee)
            .add("grabServiceFee", grabServiceFee)
            .add("insuranceFee", insuranceFee)
            .add("paperTicketFee", paperTicketFee)
            .add("refundTicketFee", refundTicketFee)
            .add("serviceFee", serviceFee)
            .add("ticketPrice", ticketPrice)
            .add("totalV", totalV)
            .toString();
    }
}
