package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

import java.io.Serializable;

/**
 * @Description: 折扣分布BO
 * <AUTHOR>
 * @Date 2019/5/23 15:57
 */
public class FlightRateInfoBO implements Serializable{

    private static final long serialVersionUID = 1L;


    String range;

    Integer number;

    String percent;

    Double ratePreOrderdate;


    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getPercent() {
        return percent;
    }

    public void setPercent(String percent) {
        this.percent = percent;
    }

    public Double getRatePreOrderdate() {
        return ratePreOrderdate;
    }

    public void setRatePreOrderdate(Double ratePreOrderdate) {
        this.ratePreOrderdate = ratePreOrderdate;
    }
}
