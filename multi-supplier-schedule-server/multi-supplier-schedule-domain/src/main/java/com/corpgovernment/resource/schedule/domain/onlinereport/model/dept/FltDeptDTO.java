package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import lombok.Data;

import java.math.BigDecimal;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
public class FltDeptDTO {

    private String aggId;
    private String aggType;
    private BigDecimal totalAmount;
    private Integer totalQuantity;
    private BigDecimal totalEconmyPrice;
    private BigDecimal totalEconmyTpms;
    private Integer totalEconmyQuantity;
    private BigDecimal totalDomEconmyDiscount;
    private Integer totalDomEconmyQuantity;
    private Integer totalFullfaretkt;
    private Integer totalDomEconmyFullfaretkt;
    private Integer totalDomEconmyOrdertkt;
    private BigDecimal totalRefundFee;
    private Integer totalRefundtkt;
    private BigDecimal totalRebookFee;
    private Integer totalRebooktkt;
    private Integer totalOrdertkt;
    private BigDecimal totalOverAmount;
    private Integer totalRcTimes;
    private Integer totalOrderCount;
    private BigDecimal totalSaveAmount3c;
    private BigDecimal totalNetfare3c;
    private BigDecimal totalSaveAmountPremium;
    private BigDecimal totalNetfarePremium;
    private BigDecimal totalControlSave;
    private BigDecimal totalControlNetfare;
    private BigDecimal totalCarbons;
    private BigDecimal totalMedianCarbons;
    private BigDecimal avgTpmsPrice;
    private BigDecimal avgPrice;
    private BigDecimal avgDiscount;
    private BigDecimal fullfaretktPercent;
    private BigDecimal saveRate;
    private BigDecimal refundRate;
    private BigDecimal rebookRate;
    private BigDecimal rcPercent;
    private BigDecimal saveAmount3cRate;
    private BigDecimal saveAmount2cRate;
    private BigDecimal controlSaveRate;
    private BigDecimal carbonSaveRate;
    private BigDecimal totalCarbonSave;

    private BigDecimal totalRefundloss;

    private BigDecimal totalRebookloss;

    private String hotFlightCity;

    private Integer totalPreOrderDate;

    private Integer totalPreOrderDateQuantity;

    private BigDecimal totalCarbonsTpms;

    private Integer totalAllOrderCount;
}
