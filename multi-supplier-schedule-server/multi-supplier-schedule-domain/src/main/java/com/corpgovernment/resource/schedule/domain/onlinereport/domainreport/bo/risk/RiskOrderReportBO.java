package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.risk;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 * @date 2022/8/8 11:01
 * @Desc
 */
@Setter
@Getter
public class RiskOrderReportBO {



    // 分析对象
    private String analysisObject;

    // 子产线
    private String subQueryBu;

    // 用户选择模块记录
    public String reportId;

    // 酒店协同套现 选择的酒店
    private List<RiskOrderReportHotelCashOutConditionB0> reportCondition;

    private String uid;

    private String startTime;

    private String endTime;

    private String riskSence;

    private Integer chooseAll;

    private List<String> customColumns;

    private Map<String, List<String>> customStatistics;
    // 产线
    private String queryBu;
}
