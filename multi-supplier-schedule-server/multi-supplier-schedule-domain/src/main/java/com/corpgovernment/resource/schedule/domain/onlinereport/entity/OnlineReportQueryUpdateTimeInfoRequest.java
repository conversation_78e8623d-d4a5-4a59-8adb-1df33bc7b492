package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 数据更新时间
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "moduleCode"
})
public class OnlineReportQueryUpdateTimeInfoRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportQueryUpdateTimeInfoRequest(
        String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public OnlineReportQueryUpdateTimeInfoRequest() {
    }

    /**
     * 模块code
     */
    @JsonProperty("moduleCode")
    public String moduleCode;

    /**
     * 模块code
     */
    public String getModuleCode() {
        return moduleCode;
    }

    /**
     * 模块code
     */
    public void setModuleCode(final String moduleCode) {
        this.moduleCode = moduleCode;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportQueryUpdateTimeInfoRequest other = (OnlineReportQueryUpdateTimeInfoRequest)obj;
        return
            Objects.equal(this.moduleCode, other.moduleCode);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.moduleCode == null ? 0 : this.moduleCode.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("moduleCode", moduleCode)
            .toString();
    }
}
