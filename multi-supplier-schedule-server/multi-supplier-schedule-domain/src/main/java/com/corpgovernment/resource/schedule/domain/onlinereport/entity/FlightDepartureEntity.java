package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "departureCityId",
    "departureCityName",
    "departureCityCode"
})
public class FlightDepartureEntity implements Serializable {
    private static final long serialVersionUID = 1L;





    public FlightDepartureEntity(
        Integer departureCityId,
        String departureCityName,
        String departureCityCode) {
        this.departureCityId = departureCityId;
        this.departureCityName = departureCityName;
        this.departureCityCode = departureCityCode;
    }

    public FlightDepartureEntity() {
    }

    @JsonProperty("departureCityId")
    public Integer departureCityId;

    @JsonProperty("departureCityName")
    public String departureCityName;

    @JsonProperty("departureCityCode")
    public String departureCityCode;

    public Integer getDepartureCityId() {
        return departureCityId;
    }

    public void setDepartureCityId(final Integer departureCityId) {
        this.departureCityId = departureCityId;
    }
    public String getDepartureCityName() {
        return departureCityName;
    }

    public void setDepartureCityName(final String departureCityName) {
        this.departureCityName = departureCityName;
    }
    public String getDepartureCityCode() {
        return departureCityCode;
    }

    public void setDepartureCityCode(final String departureCityCode) {
        this.departureCityCode = departureCityCode;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightDepartureEntity other = (FlightDepartureEntity)obj;
        return
            Objects.equal(this.departureCityId, other.departureCityId) &&
            Objects.equal(this.departureCityName, other.departureCityName) &&
            Objects.equal(this.departureCityCode, other.departureCityCode);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.departureCityId == null ? 0 : this.departureCityId.hashCode());
        result = 31 * result + (this.departureCityName == null ? 0 : this.departureCityName.hashCode());
        result = 31 * result + (this.departureCityCode == null ? 0 : this.departureCityCode.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("departureCityId", departureCityId)
            .add("departureCityName", departureCityName)
            .add("departureCityCode", departureCityCode)
            .toString();
    }
}
