package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import java.util.List;

/**
 * Auther:abguo
 * Date:2019/8/13
 * Description:
 * Project:onlinereportweb
 */
public class CustomCommonFilterPO {

    public String groupId;
    public List<String> corpIdList;
    public List<Long> accountIdList;
    public String bizType;
    public Integer year;
    public Integer month;
    public Long startTime;
    public Long endTime;
    public List<SearchEntityPO> deptList;
    public List<SearchEntityPO> costCenterList;
    public List<String> bookingChannelList;
    public List<String> rankIDList;
    public List<String> projects;
    public List<String> journeyReasons;

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public List<String> getCorpIdList() {
        return corpIdList;
    }

    public void setCorpIdList(List<String> corpIdList) {
        this.corpIdList = corpIdList;
    }

    public List<Long> getAccountIdList() {
        return accountIdList;
    }

    public void setAccountIdList(List<Long> accountIdList) {
        this.accountIdList = accountIdList;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public List<SearchEntityPO> getDeptList() {
        return deptList;
    }

    public void setDeptList(List<SearchEntityPO> deptList) {
        this.deptList = deptList;
    }

    public List<SearchEntityPO> getCostCenterList() {
        return costCenterList;
    }

    public void setCostCenterList(List<SearchEntityPO> costCenterList) {
        this.costCenterList = costCenterList;
    }

    public List<String> getBookingChannelList() {
        return bookingChannelList;
    }

    public void setBookingChannelList(List<String> bookingChannelList) {
        this.bookingChannelList = bookingChannelList;
    }

    public List<String> getRankIDList() {
        return rankIDList;
    }

    public void setRankIDList(List<String> rankIDList) {
        this.rankIDList = rankIDList;
    }

    public List<String> getProjects() {
        return projects;
    }

    public void setProjects(List<String> projects) {
        this.projects = projects;
    }

    public List<String> getJourneyReasons() {
        return journeyReasons;
    }

    public void setJourneyReasons(List<String> journeyReasons) {
        this.journeyReasons = journeyReasons;
    }
}
