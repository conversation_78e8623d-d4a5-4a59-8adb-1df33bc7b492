package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.dao;

import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpReportCustomColumnPO;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.List;

/*
 * <AUTHOR>
 * @date 2019/11/28 13:08
 * @Desc
 */
@Service
public class CorpReportCustomColumnDAOExt extends CorpReportCustomColumnDAO {

    public CorpReportCustomColumnDAOExt() throws SQLException {
    }

    /**
     * 查询条件
     *
     * @param uid
     * @param reportId
     * @return
     * @throws SQLException
     */
    public List<CorpReportCustomColumnPO> queryColumnsByUidAndReportType(String uid, String reportId) throws SQLException {
/*        SelectSqlBuilder builder = new SelectSqlBuilder();
        DalHints newHints = new DalHints();
        builder.selectAll();
        builder.equalNullable("uid", uid, Types.VARCHAR);
        builder.and();
        builder.equalNullable("report_key", reportId, Types.VARCHAR);
        return queryAll(builder, newHints);*/
        return null;
    }

    /**
     * 插入自定义字段
     *
     * @param uid
     * @param reportKey
     * @return 记录id
     * @throws SQLException
     */
    public long insert(String uid, String reportKey, String reportColumns) throws SQLException {
/*        CorpReportCustomColumnPO corpReportColumnPO = new CorpReportCustomColumnPO();
        corpReportColumnPO.setUid(uid);
        corpReportColumnPO.setReportKey(reportKey);
        corpReportColumnPO.setCustomColumns(reportColumns);
        KeyHolder keyHolder = new KeyHolder();
        int result = insertWithKeyHolder(keyHolder, corpReportColumnPO);
        if (result > 0 && keyHolder.getKey() != null) {
            return keyHolder.getKey().longValue();
        }*/
        return 0;
    }

    /**
     * 更新自定义字段
     *
     * @param id
     * @param reportColumns
     * @return
     * @throws SQLException
     */
    public int update(long id, String reportColumns) throws SQLException {
   /*     CorpReportCustomColumnPO po = queryByPk(id);
        po.setCustomColumns(reportColumns);
        return update(po);*/

        return 0;
    }
}
