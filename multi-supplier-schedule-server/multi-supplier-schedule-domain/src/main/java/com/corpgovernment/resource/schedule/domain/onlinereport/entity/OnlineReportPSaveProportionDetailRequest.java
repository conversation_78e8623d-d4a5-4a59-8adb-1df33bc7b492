package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "eId",
        "lang",
        "queryBu",
        "basecondition",
        "productType"
})
public class OnlineReportPSaveProportionDetailRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    public OnlineReportPSaveProportionDetailRequest(
            String eId,
            String lang,
            QueryReportBuTypeEnum queryBu,
            BaseQueryCondition basecondition,
            String productType) {
        this.eId = eId;
        this.lang = lang;
        this.queryBu = queryBu;
        this.basecondition = basecondition;
        this.productType = productType;
    }

    public OnlineReportPSaveProportionDetailRequest() {
    }

    @JsonProperty("eId")
    public String eId;

    /**
     * 语言环境
     */
    @JsonProperty("lang")
    public String lang;

    /**
     * 查询产线
     */
    @JsonProperty("queryBu")
    public QueryReportBuTypeEnum queryBu;

    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    /**
     * 国内dom、国际inter、全部all
     */
    @JsonProperty("productType")
    public String productType;

    public String getEId() {
        return eId;
    }

    public void setEId(final String eId) {
        this.eId = eId;
    }

    /**
     * 语言环境
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言环境
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }

    /**
     * 查询产线
     */
    public QueryReportBuTypeEnum getQueryBu() {
        return queryBu;
    }

    /**
     * 查询产线
     */
    public void setQueryBu(final QueryReportBuTypeEnum queryBu) {
        this.queryBu = queryBu;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * 国内dom、国际inter、全部all
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 国内dom、国际inter、全部all
     */
    public void setProductType(final String productType) {
        this.productType = productType;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportPSaveProportionDetailRequest other = (OnlineReportPSaveProportionDetailRequest) obj;
        return
                Objects.equal(this.eId, other.eId) &&
                        Objects.equal(this.lang, other.lang) &&
                        Objects.equal(this.queryBu, other.queryBu) &&
                        Objects.equal(this.basecondition, other.basecondition) &&
                        Objects.equal(this.productType, other.productType);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eId == null ? 0 : this.eId.hashCode());
        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.queryBu == null ? 0 : this.queryBu.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.productType == null ? 0 : this.productType.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("eId", eId)
                .add("lang", lang)
                .add("queryBu", queryBu)
                .add("basecondition", basecondition)
                .add("productType", productType)
                .toString();
    }
}
