package com.corpgovernment.resource.schedule.domain.onlinereport.rpc.impl;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.corpgovernment.api.organization.model.org.request.ListAllDepartmentsAndCostCenterByParentIdReq;
import com.corpgovernment.api.organization.soa.IOrganizationClient;

import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.domain.onlinereport.business.RoleBusiness;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.ChineseLanguageConfig;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.ConstDefine;
import com.corpgovernment.resource.schedule.domain.onlinereport.constants.OrpConstants;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPlatformService;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPrivilegeService;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.*;
import com.corpgovernment.resource.schedule.domain.onlinereport.types.ResponseStatusType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/*
 * <AUTHOR>
 * @date 2020/7/6 16:29
 * @Desc
 */
@Service
@Slf4j
public class CorpOnlineReportPrivilegeServiceImpl implements ICorpOnlineReportPrivilegeService {
    @Autowired
    private RoleBusiness roleBusiness;

    @Resource
    private IOrganizationClient organizationClient;

//    @Autowired
//    private OnlineReportClient onlineReportClient;

    @Autowired
    private ICorpOnlineReportPlatformService corpOnlineReportPlatformService;


    @Override
    public OperatorCorpRangeResponseType operatorCorpRange(OperatorCorpRangeRequestType requestType) throws Exception {
        OperatorCorpRangeResponseType responseType = new OperatorCorpRangeResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS));
        responseType.setResultStatus(resultStatus);
        try {
            responseType = roleBusiness.operatorCorpRange(requestType);
        } catch (Exception e) {
            log.error("error", e);
            ResultStatus errorStatus = new ResultStatus(ConstDefine.SYTEMERROR_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SYSTEM_ERROR));
            responseType.setResultStatus(errorStatus);
        }
        return responseType;
    }


    @Override
    public GetMainAccountIdsByCorpIdsResponseType getMainAccountIdsByCorpIds(GetMainAccountIdsByCorpIdsRequestType requestType) throws Exception {
        GetMainAccountIdsByCorpIdsResponseType responseType = new GetMainAccountIdsByCorpIdsResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS));
        responseType.setResultStatus(resultStatus);
        try {
            responseType = roleBusiness.getMainAccountIdsByCorpIds(requestType);
        } catch (Exception e) {
            log.error("error", e);
            ResultStatus errorStatus = new ResultStatus(ConstDefine.SYTEMERROR_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SYSTEM_ERROR));
            responseType.setResultStatus(errorStatus);
        }
        return responseType;
    }

    @Override
    public CostCenterAndDepartmentSearchResponseType costCenterAndDepartSearch(CostCenterAndDepartmentSearchRequestType requestType) throws Exception {
        ListAllDepartmentsAndCostCenterByParentIdReq req = new ListAllDepartmentsAndCostCenterByParentIdReq();
        req.setParentIds(requestType.getCorpIds());
        req.setSearchType(requestType.getSearchType().toString());
        req.setPageSize(requestType.getPageSize());

        log.info("查询部门和成本中心请求参数:{}", req);
        // 部门查询、成本中心查询
        // 1. 查询总记录数
        JSONResult<Integer> countResult = organizationClient.listAllDepartmentsAndCostCenterByParentIdCount(req);
        log.info("查询部门和成本中心总记录数结果:{}", countResult);
        if (!countResult.isSUCCESS()) {
            log.error("查询部门和成本中心总记录数失败,请求参数:{}", req);
            return new CostCenterAndDepartmentSearchResponseType();
        }

        Integer totalCount = countResult.getData();
        if (totalCount == null || totalCount <= 0) {
            return new CostCenterAndDepartmentSearchResponseType();
        }

        // 2. 查询部门和成本中心列表
        // 计算offset
        int pageSize = requestType.getPageSize();
        int pageIndex = requestType.getPageIndex();
        int offset = (pageIndex - 1) * pageSize;
        req.setPageIndex(offset);
        log.info("查询部门和成本中心列表请求参数:{}", req);
        JSONResult<List<String>> result = organizationClient.listAllDepartmentsAndCostCenterByParentId(req);
        log.info("查询部门和成本中心列表结果:{}", result);
        if (!result.isSUCCESS()) {
            log.error("查询部门和成本中心列表失败,请求参数:{}", req);
            return new CostCenterAndDepartmentSearchResponseType();
        }
        List<String> costCenterAndDepartments = result.getData();

        CostCenterAndDepartmentSearchResponseType responseType = new CostCenterAndDepartmentSearchResponseType();
        responseType.setResultStatus(new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS)));

        LevelRecords levelRecords = new LevelRecords();
        levelRecords.setTotalRecords(totalCount);
        levelRecords.setRecords(costCenterAndDepartments.stream().map(this::decrypt).map(Object::toString).collect(Collectors.toList()));
        responseType.setLevel1(levelRecords);
        responseType.setResponseStatus(new ResponseStatusType());

        return responseType;
    }

    @Override
    public CostCenterAndDepartmentSearchResponseType costCenterAndDepartSearchByCk(CostCenterAndDepartmentSearchRequestType requestType) throws Exception {

        // 只用返回name
        List<String> result =  corpOnlineReportPlatformService.queryCostCenterOrDepartmentOrCorpId(requestType);

        Set<String> allName = new HashSet<>();
        // 判断是否包含,分割
        Optional.ofNullable(result).orElse(Collections.emptyList()).stream().filter(StringUtils::isNotBlank).forEach(t ->{
            if (t.contains(OrpConstants.COMMA)) {
                allName.addAll(Arrays.asList(t.split(OrpConstants.COMMA)));
            }else{
                allName.add(t);
            }
        });
        CostCenterAndDepartmentSearchResponseType responseType = new CostCenterAndDepartmentSearchResponseType();
        responseType.setResultStatus(new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS)));
        responseType.setResponseStatus(new ResponseStatusType());
        responseType.setLevel1(new LevelRecords(allName.size(), allName.stream().map(this::decrypt).map(Object::toString).collect(Collectors.toList())));
        return responseType;
    }

    /**
     * 解密
     */
    private Object decrypt(Object value) {
        if (Objects.isNull(value)) {
            return OrpConstants.EMPTY;
        }

        // 类型不是String直接返回
        if (!(value instanceof String)) {
            return value.toString();
        }

        SymmetricCrypto sm4 = SmUtil.sm4("1234567890123456".getBytes());

        try {
            return sm4.decryptStr((String) value, CharsetUtil.CHARSET_UTF_8);
        } catch (Exception e) {
            return value;
        }
    }


}
