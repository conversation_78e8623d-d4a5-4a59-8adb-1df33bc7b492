package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * Rc原因
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "rcTimes",
    "rcCode",
    "rcDesc",
    "rcPercent"
})
public class RcViewReasonDetail implements Serializable {
    private static final long serialVersionUID = 1L;





    public RcViewReasonDetail(
        Integer rcTimes,
        String rcCode,
        String rcDesc,
        Double rcPercent) {
        this.rcTimes = rcTimes;
        this.rcCode = rcCode;
        this.rcDesc = rcDesc;
        this.rcPercent = rcPercent;
    }

    public RcViewReasonDetail() {
    }

    /**
     * 超标次数
     */
    @JsonProperty("rcTimes")
    public Integer rcTimes;

    /**
     * rc code
     */
    @JsonProperty("rcCode")
    public String rcCode;

    /**
     * rc 描述
     */
    @JsonProperty("rcDesc")
    public String rcDesc;

    /**
     * rc占比
     */
    @JsonProperty("rcPercent")
    public Double rcPercent;

    /**
     * 超标次数
     */
    public Integer getRcTimes() {
        return rcTimes;
    }

    /**
     * 超标次数
     */
    public void setRcTimes(final Integer rcTimes) {
        this.rcTimes = rcTimes;
    }

    /**
     * rc code
     */
    public String getRcCode() {
        return rcCode;
    }

    /**
     * rc code
     */
    public void setRcCode(final String rcCode) {
        this.rcCode = rcCode;
    }

    /**
     * rc 描述
     */
    public String getRcDesc() {
        return rcDesc;
    }

    /**
     * rc 描述
     */
    public void setRcDesc(final String rcDesc) {
        this.rcDesc = rcDesc;
    }

    /**
     * rc占比
     */
    public Double getRcPercent() {
        return rcPercent;
    }

    /**
     * rc占比
     */
    public void setRcPercent(final Double rcPercent) {
        this.rcPercent = rcPercent;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final RcViewReasonDetail other = (RcViewReasonDetail)obj;
        return
            Objects.equal(this.rcTimes, other.rcTimes) &&
            Objects.equal(this.rcCode, other.rcCode) &&
            Objects.equal(this.rcDesc, other.rcDesc) &&
            Objects.equal(this.rcPercent, other.rcPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.rcTimes == null ? 0 : this.rcTimes.hashCode());
        result = 31 * result + (this.rcCode == null ? 0 : this.rcCode.hashCode());
        result = 31 * result + (this.rcDesc == null ? 0 : this.rcDesc.hashCode());
        result = 31 * result + (this.rcPercent == null ? 0 : this.rcPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("rcTimes", rcTimes)
            .add("rcCode", rcCode)
            .add("rcDesc", rcDesc)
            .add("rcPercent", rcPercent)
            .toString();
    }
}
