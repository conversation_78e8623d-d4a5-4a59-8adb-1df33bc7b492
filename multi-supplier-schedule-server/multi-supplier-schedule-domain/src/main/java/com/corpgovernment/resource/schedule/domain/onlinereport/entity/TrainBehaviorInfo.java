package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 火车行为分析
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dim",
    "totalQuantity",
    "quantityPercent",
    "totalRefundQuantity",
    "totalRefundLoss",
    "totalPotentialSaveAmount"
})
public class TrainBehaviorInfo implements Serializable {
    private static final long serialVersionUID = 1L;



    

    public TrainBehaviorInfo(
        String dim,
        Integer totalQuantity,
        Double quantityPercent,
        Integer totalRefundQuantity,
        BigDecimal totalRefundLoss,
        BigDecimal totalPotentialSaveAmount) {
        this.dim = dim;
        this.totalQuantity = totalQuantity;
        this.quantityPercent = quantityPercent;
        this.totalRefundQuantity = totalRefundQuantity;
        this.totalRefundLoss = totalRefundLoss;
        this.totalPotentialSaveAmount = totalPotentialSaveAmount;
    }

    public TrainBehaviorInfo() {
    }

    /**
     * 维度
     */
    @JsonProperty("dim")
    public String dim;

    /**
     * 票张数
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;

    /**
     * 票张数占比
     */
    @JsonProperty("quantityPercent")
    public Double quantityPercent;

    /**
     * 退票张数
     */
    @JsonProperty("totalRefundQuantity")
    public Integer totalRefundQuantity;

    /**
     * 退票损失
     */
    @JsonProperty("totalRefundLoss")
    public BigDecimal totalRefundLoss;

    /**
     * 潜在节省
     */
    @JsonProperty("totalPotentialSaveAmount")
    public BigDecimal totalPotentialSaveAmount;

    /**
     * 维度
     */
    public String getDim() {
        return dim;
    }

    /**
     * 维度
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 票张数
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 票张数
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 票张数占比
     */
    public Double getQuantityPercent() {
        return quantityPercent;
    }

    /**
     * 票张数占比
     */
    public void setQuantityPercent(final Double quantityPercent) {
        this.quantityPercent = quantityPercent;
    }

    /**
     * 退票张数
     */
    public Integer getTotalRefundQuantity() {
        return totalRefundQuantity;
    }

    /**
     * 退票张数
     */
    public void setTotalRefundQuantity(final Integer totalRefundQuantity) {
        this.totalRefundQuantity = totalRefundQuantity;
    }

    /**
     * 退票损失
     */
    public BigDecimal getTotalRefundLoss() {
        return totalRefundLoss;
    }

    /**
     * 退票损失
     */
    public void setTotalRefundLoss(final BigDecimal totalRefundLoss) {
        this.totalRefundLoss = totalRefundLoss;
    }

    /**
     * 潜在节省
     */
    public BigDecimal getTotalPotentialSaveAmount() {
        return totalPotentialSaveAmount;
    }

    /**
     * 潜在节省
     */
    public void setTotalPotentialSaveAmount(final BigDecimal totalPotentialSaveAmount) {
        this.totalPotentialSaveAmount = totalPotentialSaveAmount;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TrainBehaviorInfo other = (TrainBehaviorInfo)obj;
        return
            Objects.equal(this.dim, other.dim) &&
            Objects.equal(this.totalQuantity, other.totalQuantity) &&
            Objects.equal(this.quantityPercent, other.quantityPercent) &&
            Objects.equal(this.totalRefundQuantity, other.totalRefundQuantity) &&
            Objects.equal(this.totalRefundLoss, other.totalRefundLoss) &&
            Objects.equal(this.totalPotentialSaveAmount, other.totalPotentialSaveAmount);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.quantityPercent == null ? 0 : this.quantityPercent.hashCode());
        result = 31 * result + (this.totalRefundQuantity == null ? 0 : this.totalRefundQuantity.hashCode());
        result = 31 * result + (this.totalRefundLoss == null ? 0 : this.totalRefundLoss.hashCode());
        result = 31 * result + (this.totalPotentialSaveAmount == null ? 0 : this.totalPotentialSaveAmount.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dim", dim)
            .add("totalQuantity", totalQuantity)
            .add("quantityPercent", quantityPercent)
            .add("totalRefundQuantity", totalRefundQuantity)
            .add("totalRefundLoss", totalRefundLoss)
            .add("totalPotentialSaveAmount", totalPotentialSaveAmount)
            .toString();
    }
}
