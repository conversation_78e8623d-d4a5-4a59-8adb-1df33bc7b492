package com.corpgovernment.resource.schedule.domain.onlinereport.compliance;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2022/4/8 10:44
 * @Desc
 */
@Data
public class HotelRcTimesDTO {
    @Column(name = "reasonRcTimes")
    @Type(value = Types.INTEGER)
    private Integer lowRcTimes;
    @Column(name = "minRcTimes")
    @Type(value = Types.INTEGER)
    private Integer minRcTimes;
    @Column(name = "agreementRcTimes")
    @Type(value = Types.INTEGER)
    private Integer agreementRcTimes;
    @Column(name = "orderCount")
    @Type(value = Types.INTEGER)
    private Integer orderCount;
    @Column(name = "rcTimes")
    @Type(value = Types.INTEGER)
    private Integer rcTimes;
}
