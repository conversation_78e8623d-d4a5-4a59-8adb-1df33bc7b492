package com.corpgovernment.resource.schedule.domain.onlinereport.types;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

/**
 * Generic error response container. Only used by Baiji RPC framework internally.
 * Please don't use this type in your service implementation.
 */
@SuppressWarnings("all")
public class GenericErrorResponseType implements Serializable {
    private static final long serialVersionUID = 1L;


    public GenericErrorResponseType(
            ResponseStatusType responseStatus) {
        this.responseStatus = responseStatus;
    }

    public GenericErrorResponseType() {
    }

    @XmlElement(name = "ResponseStatus", required = true)
    @JsonProperty("ResponseStatus")
    public ResponseStatusType responseStatus;

    public ResponseStatusType getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(final ResponseStatusType responseStatus) {
        this.responseStatus = responseStatus;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.responseStatus;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.responseStatus = (ResponseStatusType) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final GenericErrorResponseType other = (GenericErrorResponseType) obj;
        return
                Objects.equal(this.responseStatus, other.responseStatus);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseStatus == null ? 0 : this.responseStatus.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("responseStatus", responseStatus)
                .toString();
    }
}
