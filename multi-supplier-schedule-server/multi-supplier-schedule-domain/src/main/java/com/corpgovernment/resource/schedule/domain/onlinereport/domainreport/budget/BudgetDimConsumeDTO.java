package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.budget;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @className BudgetDimConsumeDTO
 * @date 2024/6/3
 */
@Data
public class BudgetDimConsumeDTO {

    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;

    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;

}
