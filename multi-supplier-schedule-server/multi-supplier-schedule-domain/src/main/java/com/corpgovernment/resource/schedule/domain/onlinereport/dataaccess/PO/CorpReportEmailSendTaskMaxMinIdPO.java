package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/27 10:16
 * @description：
 * @modified By：
 * @version: $
 */
@Entity
//@Database(name = "CorpReportDB")
@Table(name = "report_email_sendtask")
@Data
public class CorpReportEmailSendTaskMaxMinIdPO {

    /**
     * minId
     */
    @Column(name = "minId")
    @Type(value = Types.BIGINT)
    private Long minId;

    /**
     * 用户卡号
     */
    @Column(name = "maxId")
    @Type(value = Types.BIGINT)
    private Long maxId;

}
