package com.corpgovernment.resource.schedule.domain.screen.model;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TotalCountBO {

    /**
     * 中文行程类型
     */
    @Column(name = "type")
    @Type(value = Types.VARCHAR)
    public String type;

    /**
     * 总金额
     */
    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;


    /**
     * 总数量
     */
    @Column(name = "totalQuantity")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalQuantity;

}
