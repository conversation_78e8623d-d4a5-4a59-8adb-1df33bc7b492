package com.corpgovernment.resource.schedule.domain.accesslog.service;

import com.corpgovernment.resource.schedule.domain.accesslog.gateway.AccessLogGateway;
import com.corpgovernment.resource.schedule.domain.accesslog.model.BookingFaq;
import com.corpgovernment.resource.schedule.domain.accesslog.model.Top5Info;
import com.corpgovernment.resource.schedule.domain.accesslog.model.Top5InfoTraffic;
import com.corpgovernment.resource.schedule.domain.accesslog.model.req.ReportQueryReq;
import com.corpgovernment.resource.schedule.domain.accesslog.model.resp.ReportQueryResp;
import com.corpgovernment.resource.schedule.domain.accesslog.model.resp.Top5InfoExcel;
import com.corpgovernment.resource.schedule.domain.accesslog.model.resp.Top5InfoResp;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR> Smith
 */
@Component
@Slf4j
public class AccessLogService {

    @Resource
    private AccessLogGateway accessLogGateway;

    /**
     * 查询高频问题访问日志
     */
    public ReportQueryResp query(@Valid ReportQueryReq reportQueryReq) {
        log.info("query top5 info, req:{}", reportQueryReq);

        Config appConfig = ConfigService.getAppConfig();
        // 判断当前环境是否为程曦环境（dataSources.front.loadDataSource = true）
        Boolean booleanProperty = appConfig.getBooleanProperty("dataSources.front.loadDataSource", false);
        log.info("current environment is :{}", booleanProperty);
        // 走程曦环境查询逻辑
        if (booleanProperty) {
            return getTop5InfoFront(reportQueryReq);
        }


        Top5Info top5Info = accessLogGateway.queryBYD(reportQueryReq);

        // 获取问题信息
        Set<Integer> questionIds = Lists.newArrayList(top5Info.getFlightTop5(), top5Info.getTrainTop5(), top5Info.getHotelTop5(), top5Info.getCommonTop5())
                .stream()
                .flatMap(Collection::stream)
                .map(Top5InfoTraffic::getQuestionId)
                .collect(Collectors.toSet());

        List<BookingFaq> questionFaqs = accessLogGateway.queryQuestion(questionIds);

        Map<Integer, BookingFaq> questionFaqsMap = questionFaqs.stream().collect(Collectors.toMap(BookingFaq::getId, t -> t));


        List<Top5InfoResp> flightTop5Resp = getTop5Resp(top5Info.getFlightTop5(), questionFaqsMap);
        List<Top5InfoResp> trainTop5Resp = getTop5Resp(top5Info.getTrainTop5(), questionFaqsMap);
        List<Top5InfoResp> hotelTop5Resp = getTop5Resp(top5Info.getHotelTop5(), questionFaqsMap);
        List<Top5InfoResp> publicTop5Resp = getTop5Resp(top5Info.getCommonTop5(), questionFaqsMap);

        ReportQueryResp reportQueryResp = new ReportQueryResp();
        reportQueryResp.setFlightTop5(flightTop5Resp);
        reportQueryResp.setTrainTicketTop5(trainTop5Resp);
        reportQueryResp.setHotelTop5(hotelTop5Resp);
        reportQueryResp.setPublicTop5(publicTop5Resp);

        return reportQueryResp;
    }


    /**
     * 查询高频问题访问日志
     */
    public List<Top5InfoResp> queryExcel(@Valid ReportQueryReq reportQueryReq) {
        log.info("query top5 info, req:{}", reportQueryReq);

        Config appConfig = ConfigService.getAppConfig();
        // 判断当前环境是否为程曦环境（dataSources.front.loadDataSource = true）
        Boolean booleanProperty = appConfig.getBooleanProperty("dataSources.front.loadDataSource", false);
        log.info("current environment is :{}", booleanProperty);
        // 走程曦环境查询逻辑
        if (booleanProperty) {
            return getTop5InfoFrontExcel(reportQueryReq);
        }


        List<Top5InfoTraffic> top5 = accessLogGateway.queryExcelBYD(reportQueryReq);

        // 获取问题信息
        Set<Integer> questionIds = top5.stream().map(Top5InfoTraffic::getQuestionId).collect(Collectors.toSet());

        List<BookingFaq> questionFaqs = accessLogGateway.queryQuestion(questionIds);

        Map<Integer, BookingFaq> questionFaqsMap = questionFaqs.stream().collect(Collectors.toMap(BookingFaq::getId, t -> t));


        return getTop5Resp(top5, questionFaqsMap);
    }

    private ReportQueryResp getTop5InfoFront(ReportQueryReq reportQueryReq) {
        List<Top5InfoResp> flightTop5Resp = accessLogGateway.query(reportQueryReq, "flight");
        List<Top5InfoResp> trainTop5Resp = accessLogGateway.query(reportQueryReq, "train");
        List<Top5InfoResp> hotelTop5Resp = accessLogGateway.query(reportQueryReq, "hotel");
        List<Top5InfoResp> publicTop5Resp = accessLogGateway.query(reportQueryReq, "common");

        ReportQueryResp reportQueryResp = new ReportQueryResp();
        reportQueryResp.setFlightTop5(flightTop5Resp);
        reportQueryResp.setTrainTicketTop5(trainTop5Resp);
        reportQueryResp.setHotelTop5(hotelTop5Resp);
        reportQueryResp.setPublicTop5(publicTop5Resp);

        return reportQueryResp;
    }

    private List<Top5InfoResp> getTop5InfoFrontExcel(ReportQueryReq reportQueryReq) {
        return accessLogGateway.query(reportQueryReq, null);
    }

    private List<Top5InfoResp> getTop5Resp(List<Top5InfoTraffic> top5, Map<Integer, BookingFaq> questionFaqsMap) {
        return IntStream.range(0, top5.size())
                .mapToObj(m -> {
                    Top5InfoTraffic top5InfoTraffic = top5.get(m);
                    Top5InfoResp top5InfoResp = new Top5InfoResp();
                    top5InfoResp.setRank(m + 1);

                    BookingFaq mapOrDefault = questionFaqsMap.getOrDefault(top5InfoTraffic.getQuestionId(), new BookingFaq());
                    top5InfoResp.setDescription(mapOrDefault.getQuestion());
                    top5InfoResp.setAnswer(mapOrDefault.getAnswer());

                    top5InfoResp.setPv(top5InfoTraffic.getPv());
                    top5InfoResp.setAppPv(top5InfoTraffic.getAppPv());
                    top5InfoResp.setWebPv(top5InfoTraffic.getWebPv());

                    return top5InfoResp;
                })
                .collect(Collectors.toList());
    }

    public void export(@Valid ReportQueryReq reportQueryReq, HttpServletResponse response) {
        log.info("export report data, req:{}", JsonUtils.toJsonString(reportQueryReq));

        ReportQueryResp reportQueryResp = query(reportQueryReq);
        if (reportQueryResp == null) {
            log.error("query report data is null");
            throw new RuntimeException("query report data is null");
        }

        List<Top5InfoResp> flightTop5 = Optional.ofNullable(reportQueryResp.getFlightTop5()).orElse(Lists.newArrayList());
        List<Top5InfoResp> trainTicketTop5 = Optional.ofNullable(reportQueryResp.getTrainTicketTop5()).orElse(Lists.newArrayList());
        List<Top5InfoResp> hotelTop5 = Optional.ofNullable(reportQueryResp.getHotelTop5()).orElse(Lists.newArrayList());
        List<Top5InfoResp> publicTop5 = Optional.ofNullable(reportQueryResp.getPublicTop5()).orElse(Lists.newArrayList());

        String source = reportQueryReq.getSource();

        List<Top5InfoExcel> flightTop5Excel = flightTop5.stream().map(m -> getTop5InfoExcel(m, "机票")).collect(Collectors.toList());
        List<Top5InfoExcel> trainTicketTop5Excel = trainTicketTop5.stream().map(m -> getTop5InfoExcel(m, "火车票")).collect(Collectors.toList());
        List<Top5InfoExcel> hotelTop5Excel = hotelTop5.stream().map(m -> getTop5InfoExcel(m, "酒店")).collect(Collectors.toList());
        List<Top5InfoExcel> publicTop5Excel = publicTop5.stream().map(m -> getTop5InfoExcel(m, "公共")).collect(Collectors.toList());

        AtomicInteger rank = new AtomicInteger(1);
        List<Top5InfoExcel> collect = Stream.of(flightTop5Excel, trainTicketTop5Excel, hotelTop5Excel, publicTop5Excel)
                // 重新根据pv排序
                .flatMap(List::stream)
                .sorted((o1, o2) -> o2.getPv().compareTo(o1.getPv()))
                // 重新设置排名（通过排序后的顺序）
                .peek(m -> m.setRank(rank.getAndIncrement()))
                .collect(Collectors.toList());

        log.info("export report data, resp:{}", JsonUtils.toJsonString(collect));

        //导出
        try {
            exportTop5InfoToExcel(collect, response);
        } catch (IOException e) {

            log.error("export excel error:", e);
        }


    }


    /**
     * <AUTHOR>
     */
    private void exportTop5InfoToExcel(List<Top5InfoExcel> data, HttpServletResponse response)
            throws IOException {

        // 创建一个新的 Excel 工作簿
        Workbook workbook = new HSSFWorkbook();

        // 创建一个名为 "Top5 Info" 的工作表
        Sheet sheet = workbook.createSheet("常见问题分析");

        // 设置表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        // 创建表头行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"排序", "产线", "问题", "回复", "PV（整体）", "PV（PC端）", "PV（移动端）"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 填充数据
        int rowNum = 1;
        for (Top5InfoExcel top5Info : data) {
            Row row = sheet.createRow(rowNum);
            rowNum++;
            row.createCell(0).setCellValue(top5Info.getRank());
            row.createCell(1).setCellValue(top5Info.getTrafficType());
            row.createCell(2).setCellValue(top5Info.getDescription());
            row.createCell(3).setCellValue(top5Info.getAnswer());
            row.createCell(4).setCellValue(top5Info.getPv());
            row.createCell(5).setCellValue(top5Info.getPcPv());
            row.createCell(6).setCellValue(top5Info.getMobilePv());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=常见问题分析.xls");

        // 将工作簿写入输出流
        try (OutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
        }

        // 关闭工作簿
        workbook.close();
    }

    private Top5InfoExcel getTop5InfoExcel(Top5InfoResp m, String trafficType) {
        Top5InfoExcel top5InfoExcel = new Top5InfoExcel();
        top5InfoExcel.setRank(m.getRank());
        top5InfoExcel.setTrafficType(trafficType);
        top5InfoExcel.setDescription(m.getDescription());
        // 回复
        top5InfoExcel.setAnswer(m.getAnswer());
        // pv 整体
        top5InfoExcel.setPv(m.getPv());
        // pv PC端
        top5InfoExcel.setPcPv(m.getWebPv());
        // pv 移动端
        top5InfoExcel.setMobilePv(m.getAppPv());
        return top5InfoExcel;
    }

}
