package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight.flightsaveandloss;

/**
 * @Description: 预订机票占比预测
 * <AUTHOR>
 * @Date 2019/6/8
 */
public class FlightForecastDataPercentBO {
    //我司提前四天以上预订机票占比
    private Double pre4Percent;
    //行业提前四天以上预订机票占比
    private Double pre4PercentIndustry;
    //我司最低票价预订机票占比
    private Double lowPercent;
    //行业最低票价预订机票占比
    private Double lowPercentIndustry;

    public Double getPre4Percent() {
        return pre4Percent;
    }

    public void setPre4Percent(Double pre4Percent) {
        this.pre4Percent = pre4Percent;
    }

    public Double getPre4PercentIndustry() {
        return pre4PercentIndustry;
    }

    public void setPre4PercentIndustry(Double pre4PercentIndustry) {
        this.pre4PercentIndustry = pre4PercentIndustry;
    }

    public Double getLowPercent() {
        return lowPercent;
    }

    public void setLowPercent(Double lowPercent) {
        this.lowPercent = lowPercent;
    }

    public Double getLowPercentIndustry() {
        return lowPercentIndustry;
    }

    public void setLowPercentIndustry(Double lowPercentIndustry) {
        this.lowPercentIndustry = lowPercentIndustry;
    }
}
