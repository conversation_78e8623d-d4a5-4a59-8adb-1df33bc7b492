package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 拼房模式
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "pattern",
    "totalQuantity",
    "quantityPercent"
})
public class ZoningPatternInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public ZoningPatternInfo(
        String pattern,
        Integer totalQuantity,
        Double quantityPercent) {
        this.pattern = pattern;
        this.totalQuantity = totalQuantity;
        this.quantityPercent = quantityPercent;
    }

    public ZoningPatternInfo() {
    }

    /**
     * 拼房模式
     */
    @JsonProperty("pattern")
    public String pattern;

    /**
     * 间夜数
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;

    /**
     * 间夜占比
     */
    @JsonProperty("quantityPercent")
    public Double quantityPercent;

    /**
     * 拼房模式
     */
    public String getPattern() {
        return pattern;
    }

    /**
     * 拼房模式
     */
    public void setPattern(final String pattern) {
        this.pattern = pattern;
    }

    /**
     * 间夜数
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 间夜数
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 间夜占比
     */
    public Double getQuantityPercent() {
        return quantityPercent;
    }

    /**
     * 间夜占比
     */
    public void setQuantityPercent(final Double quantityPercent) {
        this.quantityPercent = quantityPercent;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final ZoningPatternInfo other = (ZoningPatternInfo)obj;
        return
            Objects.equal(this.pattern, other.pattern) &&
            Objects.equal(this.totalQuantity, other.totalQuantity) &&
            Objects.equal(this.quantityPercent, other.quantityPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.pattern == null ? 0 : this.pattern.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.quantityPercent == null ? 0 : this.quantityPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("pattern", pattern)
            .add("totalQuantity", totalQuantity)
            .add("quantityPercent", quantityPercent)
            .toString();
    }
}
