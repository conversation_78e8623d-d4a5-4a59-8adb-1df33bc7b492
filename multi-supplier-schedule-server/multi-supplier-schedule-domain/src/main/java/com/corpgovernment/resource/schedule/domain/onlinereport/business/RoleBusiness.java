package com.corpgovernment.resource.schedule.domain.onlinereport.business;


import com.corpgovernment.resource.schedule.domain.onlinereport.common.ChineseLanguageConfig;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.ConstDefine;
import com.corpgovernment.resource.schedule.domain.onlinereport.config.ApplicationConfig;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.GetMainAccountIdsByCorpIdsRequestType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.GetMainAccountIdsByCorpIdsResponseType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.MainAccountsList;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.OperatorCorpRangeRequestType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.OperatorCorpRangeResponseType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.ResultStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/24 13:26
 */
@Service
@Slf4j
public class RoleBusiness {


  /*  @Autowired
    private TbPrivilegeRoleDTOMapper tbPrivilegeRoleDTOMapper;

    @Autowired
    private TbPrivilegeRoleDataPermissionDTOMapper tbPrivilegeRoleDataPermissionDTOMapper;

    @Autowired
    private TbPrivilegeRoleResourcePermissionDTOMapper tbPrivilegeRoleResourcePermissionDTOMapper;

    @Autowired
    private TbPrivilegeRoleCorpDTOMapper tbPrivilegeRoleCorpDTOMapper;

    @Autowired
    private TbPrivilegeRoleAccountDTOMapper tbPrivilegeRoleAccountDTOMapper;

    @Autowired
    private TbPrivilegeUserDTOMapper tbPrivilegeUserDTOMapper;

    @Autowired
    private TbPrivilegeResourceDTOMapper tbPrivilegeResourceDTOMapper;

    @Autowired
    private TbPrivilegeCorpPackageDTOMapper tbPrivilegeCorpPackageDTOMapper;

    @Autowired
    private TbPrivilegePackageResourceDTOMapper tbPrivilegePackageResourceDTOMapper;

    @Autowired
    private TbPrivilegeRoleCostcenterAndDeptDTOMapper tbPrivilegeCostAndDeptDTOMapper;

    @Autowired
    private PrivilegeUserBusiness userBusiness;

    @Autowired
    private OperationLogBusiness operationLogBusiness;

    @Autowired
    private CostCenterAndDepartmentSearchBusicess costCenterAndDepartmentSearchBusicess;

    @Autowired
    private AppManagerServiceRpc appManagerServiceRpc;

    @Autowired
    private Corp4jServiceRpc corp4jServiceRpc;

    @Autowired
    private CorpUserInfoServiceRpc corpUserInfoServiceRpc;

    @Autowired
    private Group4jServiceRpc group4jServiceRpc;

    @Autowired
    private OrgInfoServiceRpc orgInfoServiceRpc;

    @Autowired
    private BaseBusiness baseBusiness;*/

    private final static String COSTCENTERANDDEPT_KEY = "costcenterAnddept_limit";

    private final static int COSTCENTERANDDEPT_DEFAULT_LIMIT = 100;

    public static final String ROLENAMECANNOTEMPTY = "RoleNameCanNotEmpty";
    public static final String ROLENAMETOOLONG = "RoleNameTooLong";
    public static final String ROLENOTEXIST = "RoleNotExist";
    public static final String NOPERMISSIONTOPERATEROLE = "NoPermissionToOperateRole";
    public static final String SUPERADMINCANNOTCREATEROLE = "SuperAdminCanNotCreateRole";
    public static final String CTRIPADMINCANNOTCREATEROLE = "CtripAdminCanNotCreateRole";
    public static final String ROLENAMEEXIST = "RoleNameExist";
    public static final String ROLEHASBINDEDUSER = "RoleHasBindedUser";

    public static final String PAGERESOURCECANNOTEMPTY = "PageResourceCanNotEmpty";
    public static final String ROLEALLOCATEPERMISSION_001 = "RoleBusiness.roleAllocatePermissions-001";
    public static final String CORPDONOTHAVERESOURCEPERMISSION = "CorpDoNotHaveResourcePermission";
    public static final String DATARANGECANNOTEMPTY = "DataRangeCanNotEmpty";
    public static final String ROLEALLOCATEPERMISSION_002 = "RoleBusiness.roleAllocatePermissions-002";
    public static final String ROLEALLOCATEPERMISSION_003 = "RoleBusiness.roleAllocatePermissions-003";
    public static final String MAINACCOUNTCANNOTEMPTY = "MainAccountCanNotEmpty";

    public static final String PAGERESOURCE = "PageResource";
    public static final String NOTHING = "Nothing";
    public static final String ALLCORPANDMAINACCOUNTSPERMISSION = "AllCorpAndMainAccountsPermission";
    public static final String PRODUCTLINE = "ProductLine";
    public static final String CORPANDMAINACCOUNTSPERMISSIONRANGE = "CorpAndMainAccountsPermissionRange";
    public static final String CORPID = "CorpId";
    public static final String MAINACCOUNTID = "MainAccountId";
    public static final String YES = "Yes";
    public static final String NO = "No";

    public static final String USERIDCANNOTEMPTY = "UserIdCanNotEmpty";
    public static final String NOPERMISSIONTOOPERATEROLE = "NoPermissionToOperateRole";
    public static final String USERHASALLOCATEROLE = "UserHasAllocateRole";
    public static final String USERNOTBELONGGROUP = "UserNotBelongGroup";
    public static final String USERNOTBELONGCORP = "UserNotBelongCorp";
    public static final String BINBUSINESS = "BindSuccess";
    public static final String ROLEBINDUSER_001 = "RoleBusiness.roleUnBindUser-001";

    public static final String PACKAGERESOURCEISNOTINCORPPACKAGE = "PageResourceIsNotInCorpPackage";

    public static final String ADDROLE = "AddRole";

    public static final String EDITROLENAME = "EditRoleName";

    public static final String ROLEID = "RoleId";

    public static final String ROLENAME = "RoleName";

    /**
     * 添加角色
     *
     * @param requestType 入参
     * @return 返回值
     */
/*    public AddRoleResponseType addRole(AddRoleRequestType requestType) {
        AddRoleResponseType responseType = new AddRoleResponseType();
        ResultStatus resultStatus = new ResultStatus();
        resultStatus.setCode(ConstDefine.FAIL_STATUS_COE);
        responseType.setResultStatus(resultStatus);
        String roleName = requestType.getRoleName();

        if (StringUtils.isEmpty(roleName)) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ROLENAMECANNOTEMPTY));
            return responseType;
        }
        roleName = roleName.trim();
        if (roleName.length() > 60) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ROLENAMETOOLONG));
            return responseType;
        }

        //如果是修改角色，先判断角色是否存在
        TbPrivilegeRoleDTO role = tbPrivilegeRoleDTOMapper.selectByPrimaryKey(requestType.getRoleId());
        if (requestType.getRoleId() != null) {
            if (role == null) {
                resultStatus.setMessage(ChineseLanguageConfig.get(ROLENOTEXIST));
                return responseType;
            }
            //判断是否拥有修改该角色的权限
            Boolean hasRolePermission = validUserHasRolePermission(requestType.getOperator(), requestType.getRoleId());
            if (!hasRolePermission) {
                resultStatus.setMessage(ChineseLanguageConfig.get(NOPERMISSIONTOPERATEROLE));
                return responseType;
            }
            //如果角色名称没有变动，则直接返回成功
            if (requestType.getRoleId() != null && roleName.equals(role.getName())) {
                resultStatus.setCode(ConstDefine.SUCESS_STATUS_COE);
                resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.SUCESS));
                return responseType;
            }
        }
        //判断角色名称是否重复，集团管理员创建的角色同一集团下不能重复，公司管理员创建的角色同一公司下不能重复
        //集团管理员创建的角色是集团范畴的，公司管理员创建的角色是公司范畴的；
        String operator = requestType.getOperator();
        TbPrivilegeUserDTO operatorInfo = tbPrivilegeUserDTOMapper.getByUserId(operator);
        if (operatorInfo == null || UserTypeEnum.COMMONUSER.getUserType().equals(operatorInfo.getUserType())) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.NO_AUTH));
            return responseType;
        } else if (UserTypeEnum.SUPERADMIN.getUserType().equals(operatorInfo.getUserType())) {
            resultStatus.setMessage(ChineseLanguageConfig.get(SUPERADMINCANNOTCREATEROLE));
            return responseType;
        } else if (UserTypeEnum.CTRIPADMIN.getUserType().equals(operatorInfo.getUserType()) || UserTypeEnum.CUSTOMERASSISTANT.getUserType().equals(operatorInfo.getUserType())) {
            resultStatus.setMessage(ChineseLanguageConfig.get(CTRIPADMINCANNOTCREATEROLE));
            return responseType;
        }
        TbPrivilegeRoleDTO existRoleNameDTO = null;
        if (UserTypeEnum.GROUPADMIN.getUserType().equals(operatorInfo.getUserType())) {
            existRoleNameDTO = tbPrivilegeRoleDTOMapper.getRoleByGroupIdAndName(operatorInfo.getGroupId(), roleName);
        } else if (UserTypeEnum.CORPADMIN.getUserType().equals(operatorInfo.getUserType())) {
            existRoleNameDTO = tbPrivilegeRoleDTOMapper.getRoleByCorpIdAndName(operatorInfo.getCorpId(), roleName);
        }
        if ((requestType.getRoleId() == null && existRoleNameDTO != null)
                || (requestType.getRoleId() != null && existRoleNameDTO != null && !existRoleNameDTO.getId().equals(requestType.getRoleId()))) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ROLENAMEEXIST));
            return responseType;
        }
        //公司管理员创建的角色，role_type = 1;集团管理员创建的角色，role_type = 2;
        RoleTypeEnum roleType = UserTypeEnum.GROUPADMIN.getUserType().equals(operatorInfo.getUserType()) ? RoleTypeEnum.GROUP_ROLE : RoleTypeEnum.CORP_ROLE;
        Integer roleId = addRole(requestType.getRoleId(), roleType.getRoleType(), requestType.getRoleName(), operator, operatorInfo.getCorpId(),
                operatorInfo.getGroupId());
        resultStatus.setCode(ConstDefine.SUCESS_STATUS_COE);
        resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.SUCESS));
        responseType.setRoleId(roleId);

        //插入操作日志
        StringBuilder logSb = new StringBuilder();
        if (requestType.getRoleId() == null) {
            logSb.append(ChineseLanguageConfig.get(ADDROLE)).append(",");
        } else {
            logSb.append(ChineseLanguageConfig.get(EDITROLENAME)).append(",");
        }
        logSb.append(ChineseLanguageConfig.get(ROLEID)).append(":").append(roleId);
        logSb.append(ChineseLanguageConfig.get(ROLENAME)).append(":");
        if (role != null) {
            logSb.append(role.getName()).append("->");
        }
        logSb.append(requestType.getRoleName());
        OperationType operationType = requestType.getRoleId() == null ? OperationType.ADD_ROLE : OperationType.UPDATE_ROLE;
        operationLogBusiness.insertLog(operator, null, operatorInfo.getCorpId(), operationType, logSb.toString());
        return responseType;
    }*/

    /**
     * 删除角色
     *
     * @param requestType 入参
     * @return 返回值
     */
    /*public DeleteRoleResponseType deleteRole(DeleteRoleRequestType requestType) {
        DeleteRoleResponseType responseType = new DeleteRoleResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.FAIL_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.FAIL));
        responseType.setResultStatus(resultStatus);
        String operator = requestType.getOperator();
        Integer roleId = requestType.getRoleId();
        TbPrivilegeRoleDTO roleDTO = tbPrivilegeRoleDTOMapper.selectByPrimaryKey(roleId);
        boolean hasPermission = validUserHasRolePermission(operator, roleId);
        if (!hasPermission) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.NO_AUTH));
            return responseType;
        }
        //判断角色是否有用户绑定
        int userCounts = tbPrivilegeUserDTOMapper.countUsersByRoleId(roleId);
        if (userCounts > 0) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ROLEHASBINDEDUSER));
            return responseType;
        }

        TbPrivilegeRoleDTO deleteRole = new TbPrivilegeRoleDTO();
        deleteRole.setId(roleId);
        deleteRole.setIsDel(new Byte("1"));
        int count = tbPrivilegeRoleDTOMapper.updateByPrimaryKeySelective(deleteRole);
        if (count > 0) {
            resultStatus.setCode(ConstDefine.SUCESS_STATUS_COE);
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.SUCESS));
            responseType.setResultStatus(resultStatus);
        }
        //插入操作日志
        String deleteRoleLogFormat = ChineseLanguageConfig.get("DeleteRoleLogFormat");
        operationLogBusiness.insertLog(operator, null, roleDTO.getCorpId(), OperationType.DEL_ROLE, String.format(deleteRoleLogFormat, roleId, roleDTO.getName()));
        return responseType;
    }*/

    /**
     * 角色分配权限
     *
     * @param requestType 入参
     * @return 返回值
     */
  /*  public RoleAllocatePermissionsResponseType roleAllocatePermissions(RoleAllocatePermissionsRequestType requestType) {

        RoleAllocatePermissionsResponseType responseType = new RoleAllocatePermissionsResponseType();
        ;
        ResultStatus resultStatus = new ResultStatus(ConstDefine.FAIL_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.FAIL));
        responseType.setResultStatus(resultStatus);

        String operator = requestType.getOperator();
        Integer roleId = requestType.getRoleId();
        Boolean hasAllCorpPermissions = requestType.isHasAllCorpPermissions();
        List<CorpDataPermission> corpDataPermissionList = requestType.getCorpDataPermissions();
        List<CostCenterAndDept> costCenters = requestType.getCostcenters();
        List<CostCenterAndDept> depts = requestType.getDepts();
        List<Integer> pageResources = requestType.getPageResources();
        List<com.ctrip.corp.onlinereportprivilegeservice.service.ProductLineEnum> productLineEnumList = requestType.getProductLines();
        //判断是否拥有修改该角色的权限
        Boolean hasRolePermission = validUserHasRolePermission(operator, roleId);
        if (!hasRolePermission) {
            resultStatus.setMessage(ChineseLanguageConfig.get(NOPERMISSIONTOPERATEROLE));
            return responseType;
        }
        if (hasAllCorpPermissions == null) {
            resultStatus.setMessage("HasAllCorpPermission is Empty");
            return responseType;
        }
        //判断页面资源是否在公司套餐范围内
        if (CollectionUtils.isEmpty(pageResources)) {
            resultStatus.setMessage(ChineseLanguageConfig.get(PAGERESOURCECANNOTEMPTY));
            return responseType;
        }
        if (isGteLimt(costCenters, depts)) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ROLEALLOCATEPERMISSION_001));
            return responseType;
        }

        TbPrivilegeRoleDTO roleDTO = tbPrivilegeRoleDTOMapper.selectByPrimaryKey(roleId);
        if (roleDTO == null) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ROLENOTEXIST));
            return responseType;
        }
        TbPrivilegeUserDTO operatorInfo = tbPrivilegeUserDTOMapper.getByUserId(operator);
        if (operatorInfo == null || UserTypeEnum.COMMONUSER.getUserType().equals(operatorInfo.getUserType())) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.NO_AUTH));
            return responseType;
        } else if (UserTypeEnum.SUPERADMIN.getUserType().equals(operatorInfo.getUserType())) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.NO_AUTH));
            return responseType;
        } else if (UserTypeEnum.CTRIPADMIN.getUserType().equals(operatorInfo.getUserType()) || UserTypeEnum.CUSTOMERASSISTANT.getUserType().equals(operatorInfo.getUserType())) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.NO_AUTH));
            return responseType;
        }
        if (RoleTypeEnum.CORP_ROLE.getRoleType().equals(roleDTO.getRoleType())) {
            //公司管理员能操作公司角色
            if (!UserTypeEnum.CORPADMIN.getUserType().equals(operatorInfo.getUserType())) {
                resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.NO_AUTH));
                return responseType;
            }
        }
        if (RoleTypeEnum.GROUP_ROLE.getRoleType().equals(roleDTO.getRoleType())) {
            //集团管理员能操作集团角色
            if (!UserTypeEnum.GROUPADMIN.getUserType().equals(operatorInfo.getUserType())) {
                resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.NO_AUTH));
                return responseType;
            }
        }
        List<TbPrivilegeResourceDTO> corpOwnResources = null;
        if (StringUtils.isNotEmpty(operatorInfo.getGroupId())) {
            corpOwnResources = tbPrivilegeResourceDTOMapper.getResourcesByCorpId(operatorInfo.getGroupId(), CorpTypeEnum.GRUOP.getCorpType());
        } else {
            corpOwnResources = tbPrivilegeResourceDTOMapper.getResourcesByCorpId(operatorInfo.getCorpId(), CorpTypeEnum.CORP.getCorpType());
        }
        if (corpOwnResources == null) {
            resultStatus.setMessage(ChineseLanguageConfig.get(CORPDONOTHAVERESOURCEPERMISSION));
            return responseType;
        }
        List<Integer> corpOwnResourceIds = corpOwnResources.stream().filter(Objects::nonNull).filter(x -> Objects.nonNull(x.getId())).map(x -> x.getId()).collect(Collectors.toList());
        for (Integer pageResource : pageResources) {
            if (!corpOwnResourceIds.contains(pageResource)) {
                resultStatus.setMessage(ChineseLanguageConfig.get(PACKAGERESOURCEISNOTINCORPPACKAGE));
                return responseType;
            }
        }

        //校验公司，主账户
        if (!hasAllCorpPermissions && CollectionUtils.isEmpty(corpDataPermissionList)) {
            resultStatus.setMessage(ChineseLanguageConfig.get(DATARANGECANNOTEMPTY));
            return responseType;
        }
        List<TbPrivilegeRoleAccountDTO> accountDTOList = new ArrayList<>();
        List<TbPrivilegeRoleCorpDTO> corpDTOList = new ArrayList<>();
        for (CorpDataPermission corpDataPermission : corpDataPermissionList) {
            String corpId = corpDataPermission.getCorpId();
            List<String> mainAccountIds = corpDataPermission.getMainAccountIds();
            //判断是否有该公司的管理权限
            boolean hasCorpPermission = convertCorpDTO(operatorInfo, corpId, roleId, corpDTOList);
            if (!hasCorpPermission) {
                resultStatus.setMessage(String.format(ChineseLanguageConfig.get(ROLEALLOCATEPERMISSION_002), corpId));
                return responseType;
            }
            //判断主账户是否合法
            boolean hasAccountPermission = convertAccountDTO(mainAccountIds, corpId, roleId, accountDTOList);
            if (!hasAccountPermission) {
                resultStatus.setMessage(ChineseLanguageConfig.get(ROLEALLOCATEPERMISSION_003));
                return responseType;
            }
        }
        //校验公司，主账户
        if (!hasAllCorpPermissions && CollectionUtils.isEmpty(corpDTOList) && CollectionUtils.isEmpty(accountDTOList)) {
            resultStatus.setMessage(ChineseLanguageConfig.get(MAINACCOUNTCANNOTEMPTY));
            return responseType;
        }
        // productline 默认全选
        List<ProductLineEnum> productLines = Arrays.stream(ProductLineEnum.values()).collect(Collectors.toList());
//        for (int i = 0; productLineEnumList != null && i < productLineEnumList.size(); i++) {
//            ProductLineEnum productLineEnum = ProductLineEnum.valueOf(productLineEnumList.get(i).name());
//            productLines.add(productLineEnum);
//        }

        allocateRolePermissions(roleId, pageResources, hasAllCorpPermissions, accountDTOList, corpDTOList, costCenters, depts, productLines);

        resultStatus.setCode(ConstDefine.SUCESS_STATUS_COE);
        resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.SUCESS));

        //插入操作日志，耗时操作，异步执行
        new Thread(() -> {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                StringBuilder logSb = new StringBuilder();
                List<TbPrivilegeResourceDTO> resourceDTOS = tbPrivilegeResourceDTOMapper.selectByIds(pageResources);
                if (!CollectionUtils.isEmpty(resourceDTOS)) {
                    List<String> resourceNames = resourceDTOS.stream().map(x -> x.getResourceName()).collect(Collectors.toList());
                    logSb.append(ChineseLanguageConfig.get(PAGERESOURCE)).append(":").
                            append(org.apache.commons.lang3.StringUtils.join(resourceNames, ",")).
                            append(System.lineSeparator());
                } else {
                    logSb.append(ChineseLanguageConfig.get(PAGERESOURCE)).append(":").append(ChineseLanguageConfig.get(NOTHING)).append(System.lineSeparator());
                }
                logSb.append(ChineseLanguageConfig.get(ALLCORPANDMAINACCOUNTSPERMISSION)).append(":").
                        append(hasAllCorpPermissions ? ChineseLanguageConfig.get(YES) : ChineseLanguageConfig.get(NO)).append(System.lineSeparator());
                logSb.append(ChineseLanguageConfig.get(PRODUCTLINE)).append(":").
                        append(org.apache.commons.lang3.StringUtils.join(productLineEnumList, ",")).append(System.lineSeparator());
                if (hasAllCorpPermissions == false) {
                    logSb.append(ChineseLanguageConfig.get(CORPANDMAINACCOUNTSPERMISSIONRANGE)).
                            append(":").append(objectMapper.writeValueAsString(corpDataPermissionList));
                    for (int i = 0; corpDataPermissionList != null && i < corpDataPermissionList.size(); i++) {
                        logSb.append("[");
                        CorpDataPermission corpDataPermission = corpDataPermissionList.get(i);
                        logSb.append(ChineseLanguageConfig.get(CORPID)).append(":").append(corpDataPermission.getCorpId());
                        logSb.append(ChineseLanguageConfig.get(MAINACCOUNTID)).append(":").
                                append(org.apache.commons.lang3.StringUtils.join(corpDataPermission.getMainAccountIds(), ","));
                        corpDataPermission.getMainAccountIds();
                        logSb.append("]");
                    }
                }
                operationLogBusiness.insertLog(operator, null, roleDTO.getCorpId(), OperationType.ROLE_ALLOCATE_PERMISSION, logSb.toString());
            } catch (Exception e) {
                log.error("Insert Operate Log Error", e);
            }
        }).start();

        return responseType;
    }*/

    /**
     * 转DTO并校验operator是否有操作当前corpId的权限
     *
     * @param operatorInfo 在线报告权限对象
     * @param corpId       公司id
     * @param roleId       角色id
     * @param corpDTOList  公司角色对象
     * @return
     */
/*    private boolean convertCorpDTO(TbPrivilegeUserDTO operatorInfo, String corpId, Integer roleId, List<TbPrivilegeRoleCorpDTO> corpDTOList) {
        boolean hasCorpPermission = true;
        if (StringUtils.isNotEmpty(corpId)) {
            GetCorpInfoResponseType corpInfoResponseType = corp4jServiceRpc.getCorpInfoById(corpId);
            if (UserTypeEnum.GROUPADMIN.getUserType().equals(operatorInfo.getUserType())) {
                hasCorpPermission = StringUtils.isNotEmpty(operatorInfo.getGroupId());
                if (hasCorpPermission) {
                    String groupId = null;
                    if (corpInfoResponseType != null && corpInfoResponseType.getResponseStatus().getAck() == AckCodeType.Success) {
                        groupId = corpInfoResponseType.getCorpGroupId();
                    }
                    hasCorpPermission = StringUtils.equalsIgnoreCase(operatorInfo.getGroupId(), groupId);
                }
            } else if (UserTypeEnum.CORPADMIN.getUserType().equals(operatorInfo.getUserType())) {
                hasCorpPermission = StringUtils.equalsIgnoreCase(operatorInfo.getCorpId(), corpId);
            }
            TbPrivilegeRoleCorpDTO tbPrivilegeRoleCorpDTO = new TbPrivilegeRoleCorpDTO();
            tbPrivilegeRoleCorpDTO.setCorpId(corpId);
            String corpName = corpInfoResponseType.getCorporationName();
            tbPrivilegeRoleCorpDTO.setCorpName(corpName);
            tbPrivilegeRoleCorpDTO.setRoleId(roleId);
            corpDTOList.add(tbPrivilegeRoleCorpDTO);
        }
        return hasCorpPermission;
    }*/

    /**
     * 转DTO并校验 mainAccountIds和corpId下面主账户的关系
     *
     * @param mainAccountIds 主账户id
     * @param corpId         公司id
     * @param roleId         角色id
     * @param accountDTOList 主账户角色对象
     * @return 返回值
     */
/*    private boolean convertAccountDTO(List<String> mainAccountIds, String corpId, Integer roleId, List<TbPrivilegeRoleAccountDTO> accountDTOList) {
        List<AccountStruct> accountStructList = appManagerServiceRpc.getAccountStructList(corpId);
        List<String> validAccountIds = accountStructList.stream().map(x -> String.valueOf(x.getAccountID())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mainAccountIds)) {
            for (String accountId : mainAccountIds) {
                if (!validAccountIds.contains(accountId)) {
                    return false;
                }
                AccountStruct accountStruct = accountStructList.stream().filter(i -> i.getAccountID().equals(Integer.valueOf(accountId))).findFirst().get();
                if (accountStruct != null) {
                    TbPrivilegeRoleAccountDTO tbPrivilegeRoleAccountDTO = new TbPrivilegeRoleAccountDTO();
                    tbPrivilegeRoleAccountDTO.setAccountId(Integer.valueOf(accountId));
                    tbPrivilegeRoleAccountDTO.setAccountName((String) ObjectUtils.defaultIfNull(accountStruct.getAccountName(), StringUtils.EMPTY));
                    tbPrivilegeRoleAccountDTO.setCorpId(corpId);
                    tbPrivilegeRoleAccountDTO.setRoleId(roleId);
                    accountDTOList.add(tbPrivilegeRoleAccountDTO);
                } else {
                    return false;
                }
            }
        }
        return true;
    }*/

    /**
     * 是否超过限制大小
     *
     * @param costCenters 成本中心
     * @param depts       部门
     * @return 是/否
     */
  /*  private boolean isGteLimt(List<CostCenterAndDept> costCenters, List<CostCenterAndDept> depts) {
        String limit = ApplicationConfig.getNullDefaultValue(COSTCENTERANDDEPT_KEY, String.valueOf(COSTCENTERANDDEPT_DEFAULT_LIMIT));
        int costcentAndDept_limit = Integer.valueOf(limit);
        int totalCount = 0;
        if (CollectionUtils.isNotEmpty(costCenters)) {
            for (CostCenterAndDept item : costCenters) {
                totalCount += item.getInfo().size();
            }
        }
        if (costcentAndDept_limit < totalCount) {
            return true;
        }
        totalCount = 0;
        if (CollectionUtils.isNotEmpty(depts)) {
            for (CostCenterAndDept item : depts) {
                totalCount += item.getInfo().size();
            }
        }
        if (costcentAndDept_limit < totalCount) {
            return true;
        }
        return false;
    }*/

    /**
     * 角赋权
     *
     * @param roleId              角色id
     * @param pageResources       页面资源
     * @param allCorpPermissions  是否有所有数据权限
     * @param accountDTOList      主账户
     * @param corpDTOList         公司
     * @param costcenters         成本中心
     * @param depts               部门
     * @param productLineEnumList 产线
     */
/*    @Transactional
    public void allocateRolePermissions(Integer roleId, List<Integer> pageResources, Boolean allCorpPermissions,
                                        List<TbPrivilegeRoleAccountDTO> accountDTOList,
                                        List<TbPrivilegeRoleCorpDTO> corpDTOList, List<CostCenterAndDept> costcenters,
                                        List<CostCenterAndDept> depts, List<ProductLineEnum> productLineEnumList) {
        //删除Redis缓存
        //删除公司和主账户缓存
        RedisCacheUtils.delKey(CacheKeyService.getCorpAllMainAccountsCacheKey(UserTypeEnum.COMMONUSER.getUserType(),
                null, null, roleId), false);
        //删除页面资源权限
        RedisCacheUtils.delKey(CacheKeyService.getUserPageResourceKeysCacheKey(UserTypeEnum.COMMONUSER.getUserType(),
                null, null, roleId), false);

        tbPrivilegeRoleDataPermissionDTOMapper.deleteByRoleId(roleId);//数据权限
        tbPrivilegeRoleResourcePermissionDTOMapper.deleteByRoleId(roleId);//资源权限
        tbPrivilegeRoleCorpDTOMapper.deleteByRoleId(roleId);//公司
        tbPrivilegeRoleAccountDTOMapper.deleteByRoleId(roleId);//主账户
        tbPrivilegeCostAndDeptDTOMapper.deleteByRoleId(roleId);//成本中心和部门

        //插入页面资源权限
        for (Integer pageResource : pageResources) {
            TbPrivilegeRoleResourcePermissionDTO tbPrivilegeRoleResourcePermissionDTO = new TbPrivilegeRoleResourcePermissionDTO();
            tbPrivilegeRoleResourcePermissionDTO.setRoleId(roleId);
            tbPrivilegeRoleResourcePermissionDTO.setResourceId(pageResource);
            tbPrivilegeRoleResourcePermissionDTO.setDatachangeCreatetime(new Date());
            tbPrivilegeRoleResourcePermissionDTO.setDatachangeLasttime(new Date());
            tbPrivilegeRoleResourcePermissionDTOMapper.insert(tbPrivilegeRoleResourcePermissionDTO);
        }

        TbPrivilegeRoleDataPermissionDTO allCorpPermissionDTO = new TbPrivilegeRoleDataPermissionDTO();
        allCorpPermissionDTO.setRoleId(roleId);
        allCorpPermissionDTO.setCorpId("");
        allCorpPermissionDTO.setDataType(PermissionDataTypeEnum.ALL_CORP_ACCOUNT.getDataType());
        allCorpPermissionDTO.setData(allCorpPermissions ? "1" : "0");
        allCorpPermissionDTO.setDatachangeCreatetime(new Date());
        allCorpPermissionDTO.setDatachangeLasttime(new Date());
        tbPrivilegeRoleDataPermissionDTOMapper.insert(allCorpPermissionDTO);
        if (!allCorpPermissions) {
            //公司权限
            if (CollectionUtils.isNotEmpty(corpDTOList)) {
                tbPrivilegeRoleCorpDTOMapper.insertbatch(corpDTOList);
            }
            //主账户权限
            if (CollectionUtils.isNotEmpty(accountDTOList)) {
                tbPrivilegeRoleAccountDTOMapper.insertbatch(accountDTOList);
            }
        }
        List<TbPrivilegeRoleCostcenterAndDeptDTO> costCenterList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(costcenters)) {
            for (CostCenterAndDept item : costcenters) {
                for (String info : item.getInfo()) {
                    TbPrivilegeRoleCostcenterAndDeptDTO costcenterAndDeptDTO = new TbPrivilegeRoleCostcenterAndDeptDTO();
                    costcenterAndDeptDTO.setInfo(info);
                    costcenterAndDeptDTO.setLevel(item.getLevel());
                    costcenterAndDeptDTO.setRoleid(roleId);
                    costcenterAndDeptDTO.setInfoType(CostCenterAndDepartSearchType.COSTCENTER.getValue());
                    costCenterList.add(costcenterAndDeptDTO);
                }
            }
            if (CollectionUtils.isNotEmpty(costCenterList)) {
                tbPrivilegeCostAndDeptDTOMapper.insertbatch(costCenterList);
            }
        }
        if (CollectionUtils.isNotEmpty(depts)) {
            List<TbPrivilegeRoleCostcenterAndDeptDTO> deptList = new ArrayList<>();
            for (CostCenterAndDept item : depts) {
                for (String info : item.getInfo()) {
                    TbPrivilegeRoleCostcenterAndDeptDTO costcenterAndDeptDTO = new TbPrivilegeRoleCostcenterAndDeptDTO();
                    costcenterAndDeptDTO.setInfo(info);
                    costcenterAndDeptDTO.setLevel(item.getLevel());
                    costcenterAndDeptDTO.setRoleid(roleId);
                    costcenterAndDeptDTO.setInfoType(CostCenterAndDepartSearchType.DEPARTMENT.getValue());
                    deptList.add(costcenterAndDeptDTO);
                }
            }
            if (CollectionUtils.isNotEmpty(deptList)) {
                tbPrivilegeCostAndDeptDTOMapper.insertbatch(deptList);
            }
        }
        for (ProductLineEnum productLineEnum : productLineEnumList) {
            TbPrivilegeRoleDataPermissionDTO tbPrivilegeRoleDataPermissionDTO = new TbPrivilegeRoleDataPermissionDTO();
            tbPrivilegeRoleDataPermissionDTO.setRoleId(roleId);
            tbPrivilegeRoleDataPermissionDTO.setCorpId("");
            tbPrivilegeRoleDataPermissionDTO.setData(productLineEnum.name());
            tbPrivilegeRoleDataPermissionDTO.setDataType(PermissionDataTypeEnum.PRODUCT_LINE.getDataType());
            tbPrivilegeRoleDataPermissionDTO.setDatachangeCreatetime(new Date());
            tbPrivilegeRoleDataPermissionDTO.setDatachangeLasttime(new Date());
            tbPrivilegeRoleDataPermissionDTOMapper.insert(tbPrivilegeRoleDataPermissionDTO);
        }
    }*/

    /**
     * 获取角色详情
     *
     * @param requestType 入参
     * @return 返回值
     */
 /*   public GetRoleDetailResponseType getRoleDetail(GetRoleDetailRequestType requestType) {
        GetRoleDetailResponseType responseType = new GetRoleDetailResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS));
        responseType.setResultStatus(resultStatus);
        String operatorId = requestType.getOperator();
        Integer roleId = requestType.getRoleId();
        //获取当前用户信息
        TbPrivilegeUserDTO operatorInfo = tbPrivilegeUserDTOMapper.getByUserId(operatorId);
        //判断是否有查看这个角色的权限
        if (operatorInfo == null) {
            resultStatus.setCode(ConstDefine.FAIL_STATUS_COE);
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.USERNOTEXIST));
            return responseType;
        }
        TbPrivilegeRoleDTO tbPrivilegeRoleDTO = tbPrivilegeRoleDTOMapper.selectByPrimaryKey(roleId);
        if (tbPrivilegeRoleDTO == null) {
            resultStatus.setCode(ConstDefine.FAIL_STATUS_COE);
            resultStatus.setMessage(ChineseLanguageConfig.get(ROLENOTEXIST));
            return responseType;
        }

        Boolean hasRolePermission = validUserHasRolePermission(operatorId, roleId);
        if (!hasRolePermission) {
            resultStatus.setCode(ConstDefine.FAIL_STATUS_COE);
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.NO_AUTH));
            return responseType;
        }

        responseType.setRoleId(tbPrivilegeRoleDTO.getId());
        responseType.setRoleName(tbPrivilegeRoleDTO.getName());

        List<TbPrivilegeRoleDataPermissionDTO> roleDataPermissionDTOList = tbPrivilegeRoleDataPermissionDTOMapper.getRoleDataPermissionByRoleId(roleId);

        if (CollectionUtils.isNotEmpty(roleDataPermissionDTOList)) {
            //获取产线权限
            responseType.setProductLines(new ArrayList<>(8));
            roleDataPermissionDTOList.stream().filter(x -> PermissionDataTypeEnum.PRODUCT_LINE.getDataType().equals(x.getDataType())).forEach(x -> {
                responseType.getProductLines().add(com.ctrip.corp.onlinereportprivilegeservice.service.ProductLineEnum.valueOf(x.getData()));
            });

            //是否有所有公司的查看权限
            responseType.setHasAllCorpPermissions(false);
            Optional<TbPrivilegeRoleDataPermissionDTO> allCorpPermissionOptional = roleDataPermissionDTOList.stream().filter(x ->
                    PermissionDataTypeEnum.ALL_CORP_ACCOUNT.getDataType().equals(x.getDataType())).findFirst();
            if (allCorpPermissionOptional.isPresent()) {
                String data = allCorpPermissionOptional.get().getData();
                if ("1".equals(data)) {
                    responseType.setHasAllCorpPermissions(true);
                }
            }

        }
        //获取公司数据范围权限
//        Set<String> corpIds = roleDataPermissionDTOList.stream().filter(x -> StringUtils.isNotEmpty(x.getCorpId())).map(x -> x.getCorpId()).collect(Collectors.toSet());

        List<TbPrivilegeRoleCorpDTO> tbPrivilegeRoleCorpDTOList = tbPrivilegeRoleCorpDTOMapper.selectByRoleId(roleId);
        List<TbPrivilegeRoleAccountDTO> tbPrivilegeRoleAccountDTOList = tbPrivilegeRoleAccountDTOMapper.selectByRoleId(roleId);
        if (CollectionUtils.isNotEmpty(tbPrivilegeRoleCorpDTOList)) {
            responseType.setCorpDataPermissions(new ArrayList<>());
            for (TbPrivilegeRoleCorpDTO roleCorpDTO : tbPrivilegeRoleCorpDTOList) {
                List<String> mainAccountIds = tbPrivilegeRoleAccountDTOList.stream().filter(x ->
                                StringUtils.equalsIgnoreCase(roleCorpDTO.getCorpId(), x.getCorpId()))
                        .map(x -> String.valueOf(x.getAccountId()))
                        .collect(Collectors.toList());
                CorpDataPermission corpDataPermission = new CorpDataPermission();
                corpDataPermission.setCorpId(StringUtils.upperCase(roleCorpDTO.getCorpId()));
                corpDataPermission.setMainAccountIds(mainAccountIds);
                responseType.getCorpDataPermissions().add(corpDataPermission);
            }
        }
        List<CostCenterAndDept> costcenters = new ArrayList<>();
        for (int i = 1; i <= 6; i++) {
            List<String> infos = costCenterAndDepartmentSearchBusicess.queryCostCenterOrDepartment(roleId, i, CostCenterAndDepartSearchType.COSTCENTER);
            CostCenterAndDept dept = new CostCenterAndDept();
            dept.setLevel(i);
            dept.setInfo(infos);
            costcenters.add(dept);
        }
        responseType.setCostcenters(costcenters);
        List<CostCenterAndDept> depts = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            List<String> infos = costCenterAndDepartmentSearchBusicess.queryCostCenterOrDepartment(roleId, i, CostCenterAndDepartSearchType.DEPARTMENT);
            CostCenterAndDept dept = new CostCenterAndDept();
            dept.setLevel(i);
            dept.setInfo(infos);
            depts.add(dept);
        }
        responseType.setDepts(depts);
        //获取页面资源权限
        List<Integer> pageResources = getResourceIdsByRoleId(roleId);
        responseType.setPageResources(pageResources);

        return responseType;
    }*/

    /**
     * 获取角色列表
     *
     * @param requestType 入参
     * @return 返回值
     */
    /*public RoleListResponseType getRoleList(RoleListRequestType requestType) {
        RoleListResponseType responseType = new RoleListResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS));
        responseType.setResultStatus(resultStatus);
        responseType.setTotalRecords(0);
        String operator = requestType.getOperator();
        String groupId = requestType.getGroupId();
        String corpId = requestType.getCorpId();
        String roleName = StringUtils.isEmpty(requestType.getRoleName()) ? null : requestType.getRoleName().trim();
        Integer pageIndex = requestType.getPageIndex() == null ? 1 : requestType.getPageIndex();
        Integer pageSize = Math.min(requestType.getPageSize() == null ? 20 : requestType.getPageSize(), ConstDefine.MAX_PAGESIZE);
        TbPrivilegeUserDTO operatorInfo = tbPrivilegeUserDTOMapper.getByUserId(operator);
        if (operatorInfo == null) {
            resultStatus.setCode(ConstDefine.FAIL_STATUS_COE);
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.USERNOTEXIST));
            return responseType;
        }

        Integer userType = operatorInfo.getUserType();

        int totalRecords = 0;
        List<RoleListInfoDTO> roles = new ArrayList<>(pageSize);
        Integer roleType = null;
        // 超管会返回当前卡号所属公司
        List<String> corpIds = getOperatorCorpRange(operator, false);
        if (CollectionUtils.isEmpty(corpIds) && !UserTypeEnum.SUPERADMIN.getUserType().equals(userType)) {
            return responseType;
        }
        List<String> finalList = new ArrayList<>();
        String finalCorpId = corpId;
        finalList = corpIds;
        if (StringUtils.isNotEmpty(corpId)) {
            finalList = corpIds.stream().filter(Objects::nonNull).filter(x -> StringUtils.equalsIgnoreCase(x, finalCorpId)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(finalList)) {
            return responseType;
        }
        if (UserTypeEnum.SUPERADMIN.getUserType().equals(userType)) {
            roleType = null;
        } else if (UserTypeEnum.GROUPADMIN.getUserType().equals(userType)) {
            //集团管理员，获取该集团下面的所有角色
//            groupId = operatorInfo.getGroupId();
            roleType = RoleTypeEnum.GROUP_ROLE.getRoleType();
        } else if (UserTypeEnum.CORPADMIN.getUserType().equals(userType)) {
            //公司管理员，获取该公司下的所有角色
//            corpId = operatorInfo.getCorpId();
            roleType = RoleTypeEnum.CORP_ROLE.getRoleType();
        }
        if (CollectionUtils.isNotEmpty(finalList)) {
            totalRecords = tbPrivilegeRoleDTOMapper.countRoles(finalList, roleName, roleType);
            if (totalRecords > 0) {
                roles = tbPrivilegeRoleDTOMapper.searchRoles(finalList, roleName, roleType, new Pagination(pageIndex, pageSize));
            }
        }
        responseType.setTotalRecords(totalRecords);

        if (CollectionUtils.isNotEmpty(roles)) {
            List<RoleListBaseInfo> roleListBaseInfoList = new ArrayList<>(roles.size());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (RoleListInfoDTO role : roles) {
                RoleListBaseInfo roleListBaseInfo = new RoleListBaseInfo();
                roleListBaseInfo.setRoleId(role.getId());
                roleListBaseInfo.setRoleName(role.getName());
                roleListBaseInfo.setBindUsers(role.getBindUserCount());
                roleListBaseInfo.setCreator(role.getCreateUserId());
                roleListBaseInfo.setCreateTime(sdf.format(role.getDatachangeCreatetime()));
                roleListBaseInfoList.add(roleListBaseInfo);
            }
            //批量替换creator
            Set<String> createIdsSet = roleListBaseInfoList.stream().map(x -> x.getCreator()).collect(Collectors.toSet());
            List<String> createIds = createIdsSet == null ? new ArrayList<>() : new ArrayList<>(createIdsSet);
            List<GetCorpUserInfoResponseType> getCorpUserInfoResponseTypeList = corpUserInfoServiceRpc.getCorpUserInfos(createIds);
            for (int i = 0; getCorpUserInfoResponseTypeList != null && i < getCorpUserInfoResponseTypeList.size(); i++) {
                GetCorpUserInfoResponseType getCorpUserInfoResponseType = getCorpUserInfoResponseTypeList.get(i);
                if (getCorpUserInfoResponseType != null && getCorpUserInfoResponseType.getRetCode() != null
                        && getCorpUserInfoResponseType.getRetCode() == 0) {
                    String creatorName = getCorpUserInfoResponseType.getName();
                    String uid = getCorpUserInfoResponseType.getUID();
                    roleListBaseInfoList.stream().filter(x -> uid.equals(x.getCreator())).forEach(x -> x.setCreator(creatorName));
                }
            }
            responseType.setRoleList(roleListBaseInfoList);
        }
        return responseType;
    }*/


    /**
     * 获取用户的公司权限范围
     *
     * @param requestType 入参
     * @return 返回值
     */
    public OperatorCorpRangeResponseType operatorCorpRange(OperatorCorpRangeRequestType requestType) {
        OperatorCorpRangeResponseType corpRangeResponseType = new OperatorCorpRangeResponseType();
        corpRangeResponseType.setResultStatus(new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS)));
        corpRangeResponseType.setCorpList(new ArrayList<>());
        String operator = requestType.getOperator();
/*        List<String> corpIds = getOperatorCorpRange(operator, BooleanUtils.isTrue(requestType.isDefaultCorpAdmin()));

        List<GetCorpInfoResponseType> getCorpInfoResponseTypeList = corp4jServiceRpc.getCorpInfoByIds(corpIds);
        if (CollectionUtils.isNotEmpty(getCorpInfoResponseTypeList)) {
            for (GetCorpInfoResponseType getCorpInfoResponseType : getCorpInfoResponseTypeList) {
                if (getCorpInfoResponseType != null && getCorpInfoResponseType.getResponseStatus().getAck() == AckCodeType.Success) {
                    String corpId = StringUtils.upperCase(getCorpInfoResponseType.getCorpCorporation());
                    String corpName = getCorpInfoResponseType.getCorporationName();
                    corpRangeResponseType.getCorpList().add(new CorpBaseInfo(corpId, corpName));
                }
            }
        }*/
        return corpRangeResponseType;
    }

    /**
     * 获取用户的公司范围权限
     *
     * @param uid
     * @param isDefaultCorpAdmin 当前卡号默认当做公司管理员处理
     * @return
     */
/*    public List<String> getOperatorCorpRange(String uid, boolean isDefaultCorpAdmin) {
        TbPrivilegeUserDTO operatorDTO = tbPrivilegeUserDTOMapper.getByUserId(uid);
        return getOperatorCorpRange(operatorDTO, uid, isDefaultCorpAdmin);
    }*/

    /**
     * @param operatorDTO
     * @param uid
     * @param defaultCorpAdmin 默认公司管理员
     * @return
     */
/*    public List<String> getOperatorCorpRange(TbPrivilegeUserDTO operatorDTO, String uid, boolean defaultCorpAdmin) {
        List<String> corpIds = new ArrayList<>();
        if (operatorDTO == null && !defaultCorpAdmin) {
            return corpIds;
        }
        //获取用户信息
        GetCorpUserInfoResponseType getCorpUserInfoResponseType = corpUserInfoServiceRpc.getCorpUserInfo(uid);
        String corpId = Optional.ofNullable(getCorpUserInfoResponseType).orElse(new GetCorpUserInfoResponseType()).getCorpCorporation();
        if (operatorDTO == null && defaultCorpAdmin) {
            // 沒有开通权限，但是需要默认公司管理员的权限
            operatorDTO = new TbPrivilegeUserDTO();
            operatorDTO.setUid(uid);
            operatorDTO.setUserType(UserTypeEnum.CORPADMIN.getUserType());
            operatorDTO.setCorpId(corpId);
        }
        if (operatorDTO != null && defaultCorpAdmin) {
            // 沒有开通权限，corpId只能通过接口查询
            operatorDTO.setCorpId(corpId);
        }
        String needCheckCorp = ApplicationConfig.getNullDefaultValue("need_check_corp_change", StringUtils.EMPTY);
        // 需要校验corpid,并且corpId发生了变化
        if (StringUtils.equalsIgnoreCase(needCheckCorp, "T") && !StringUtils.equalsIgnoreCase(StringUtils.trim(corpId), StringUtils.trim(operatorDTO.getCorpId()))) {
            return corpIds;
        }
        String valid = Optional.ofNullable(getCorpUserInfoResponseType).orElse(new GetCorpUserInfoResponseType()).getValid();
        String needCheckUidValid = ApplicationConfig.getNullDefaultValue("need_check_uid_valid", StringUtils.EMPTY);
        // 需要校验uid，并且uid无效直接返回
        if (StringUtils.equalsIgnoreCase(needCheckUidValid, "T") && !StringUtils.equalsIgnoreCase(valid, "T")) {
            return corpIds;
        }
        return getOperatorCorpRange(operatorDTO);
    }*/

    /**
     * 获取用户的公司范围权限
     *
     * @param operatorDTO 卡号
     * @return 返回值
     */
    /*public List<String> getOperatorCorpRange(TbPrivilegeUserDTO operatorDTO) {
        List<String> corpIds = new ArrayList<>();
        if (operatorDTO == null) {
            return corpIds;
        }
        //从缓存中获取数据
        String cacheKey = String.format(RedisKeyConst.Privilege.OperatorCorpRange, operatorDTO.getUid());
        Set<String> corpRanges = RedisCacheUtils.smembers(cacheKey);
        if (corpRanges != null && corpRanges.size() > 0) {
            return new ArrayList<>(corpRanges);
        }
        Integer userType = operatorDTO.getUserType();
        String corpId = operatorDTO.getCorpId();
        if (UserTypeEnum.GROUPADMIN.getUserType().equals(userType)) {
            //如果是集团管理员，获取集团下面所有的公司
            corpIds = getGroupCorpIdsByCorpId(corpId);
        } else if (UserTypeEnum.CORPADMIN.getUserType().equals(userType)) {
            corpIds.add(corpId);
        } else if (UserTypeEnum.COMMONUSER.getUserType().equals(userType)) {
            Integer roleId = operatorDTO.getRoleId();
            List<TbPrivilegeRoleDataPermissionDTO> dataPermissionDTOS = tbPrivilegeRoleDataPermissionDTOMapper.getRoleDataPermissionByRoleId(roleId);
            //判断用户是否拥有所有权限：全部公司+全部主账户
            boolean allCorpPermission = dataPermissionDTOS.stream().anyMatch(x ->
                    x.getDataType().equals(PermissionDataTypeEnum.ALL_CORP_ACCOUNT.getDataType()) && StringUtils.equalsIgnoreCase(x.getData(), "1"));
            // 默认是当前公司
            List<String> allCorpIds = Lists.newArrayList(corpId);
            // 如果存在集团
            if (StringUtils.isNotEmpty(operatorDTO.getGroupId())) {
                TbPrivilegeRoleDTO roleDTO = tbPrivilegeRoleDTOMapper.selectByPrimaryKey(roleId);
                // 并且是集团角色
                if (Objects.nonNull(roleDTO) && Objects.nonNull(roleDTO.getRoleType()) && roleDTO.getRoleType().compareTo(RoleTypeEnum.GROUP_ROLE.getRoleType()) == 0) {
                    //集团所有公司
                    allCorpIds = getGroupCorpIdsByCorpId(corpId);
                }
            }
            //拥有公司或集团下的所有权限
            if (allCorpPermission) {
                corpIds = allCorpIds;
            } else {
                List<TbPrivilegeRoleCorpDTO> permissionDTOS = tbPrivilegeRoleCorpDTOMapper.selectByRoleId(roleId);
                if (CollectionUtils.isNotEmpty(permissionDTOS) && CollectionUtils.isNotEmpty(allCorpIds)) {
                    // 取交集
                    corpIds = allCorpIds.stream().filter(i -> permissionDTOS.stream().anyMatch(x -> StringUtils.equalsIgnoreCase(i, x.getCorpId()))).collect(Collectors.toList());
                }
            }
        } else if (excludeUserType(userType) || excludeManagement(operatorDTO.getUid())) {
            // 超管人员，管理人员默认返回自己公司id
            corpIds.add(corpId);
        }
        if (CollectionUtils.isNotEmpty(corpIds)) {
            RedisCacheUtils.sadd(cacheKey, corpIds.toArray(new String[corpIds.size()]));
            //一小时过期
            RedisCacheUtils.expire(cacheKey, 60 * 5, false);
            corpIds = corpIds.stream().map(i -> StringUtils.upperCase(i)).collect(Collectors.toList());
        }
        return corpIds;
    }*/

    /**
     * 根据集团下的某个公司id获取集团下的所有公司id
     *
     * @param corpId 公司id
     * @return 返回值
     */
/*    private List<String> getGroupCorpIdsByCorpId(String corpId) {
        List<String> corpIds = new ArrayList<>();
        GetCorpInfoResponseType getCorpInfoResponseType = corp4jServiceRpc.getCorpInfoById(corpId);
        if (getCorpInfoResponseType != null && getCorpInfoResponseType.getResponseStatus().getAck() == AckCodeType.Success) {
            String groupName = getCorpInfoResponseType.getCorpGroupName();
            GetCorpIDByGroupNameResponseType getCorpIDByGroupNameResponseType = orgInfoServiceRpc.getCorpIdByGroupName(groupName);
            if (getCorpIDByGroupNameResponseType != null && getCorpIDByGroupNameResponseType.getResponseStatus().getAck() == AckCodeType.Success) {
                corpIds = getCorpIDByGroupNameResponseType.getCorpIDList();
            }
        }
        return corpIds;
    }*/

    /**
     * 根据公司id或者主账户列表
     * 如果是集团管理员，获取公司id下的所有主账户
     * 如果是公司管理员，获取公司id下的所有主账户
     * 如果是普通用户，需要读取角色权限表
     *
     * @param requestType 入参
     * @return 返回值
     */
    public GetMainAccountIdsByCorpIdsResponseType getMainAccountIdsByCorpIds(GetMainAccountIdsByCorpIdsRequestType requestType) {
        GetMainAccountIdsByCorpIdsResponseType responseType = new GetMainAccountIdsByCorpIdsResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS));
        responseType.setResultStatus(resultStatus);

        List<String> corpIds = requestType.getCorpIds();
        if (CollectionUtils.isEmpty(corpIds)) {
            return responseType;
        }
        //去重
        corpIds = new ArrayList<>(new HashSet<>(corpIds));

        String operator = requestType.getOperator();
 /*       Map<String, List<CorpMainAccountInfo>> corpMainAccountInfos = getAllCorpMainAccountInfos(operator, BooleanUtils.isTrue(requestType.isDefaultCorpAdmin()));

        if (corpMainAccountInfos == null) {
            return responseType;
        }*/

        Map<String, MainAccountsList> corpAccountsMap = new HashMap<>();

/*        Map<String, String> corpIdNameMap = corp4jServiceRpc.getCorpNamesByIds(corpIds);
        for (String corpId : corpIds) {
            List<CorpMainAccountInfo> corpMainAccountInfoList = corpMainAccountInfos.get(corpId);
            if (CollectionUtils.isNotEmpty(corpMainAccountInfoList)) {
                MainAccountsList mainAccountsList = new MainAccountsList();
                List<MainAccounts> mainAccounts = corpMainAccountInfoList.stream().map(x ->
                        new MainAccounts(String.valueOf(x.getId()), x.getAccountName(), x.getCompanyName())).collect(Collectors.toList());
                mainAccountsList.setAccounts(mainAccounts);
                mainAccountsList.setCorpId(StringUtils.upperCase(corpId));
                mainAccountsList.setCorpName(corpIdNameMap.get(corpId) == null ? corpId : corpIdNameMap.get(corpId));
                corpAccountsMap.put(StringUtils.upperCase(corpId), mainAccountsList);
            }
        }*/

        responseType.setCorpAccountsMap(corpAccountsMap);
        return responseType;
    }

    /**
     * 根据userId获取用户所有的corpId和mainAccountId集合
     *
     * @param uid 卡号
     * @return 返回值
     */
/*    public Map<String, List<CorpMainAccountInfo>> getAllCorpMainAccountInfos(String uid, boolean isDefaultCorpAdmin) {
        TbPrivilegeUserDTO operatorInfo = userBusiness.getPrivilegeUserDTO(uid);
        return getAllCorpMainAccountInfos(operatorInfo, uid, isDefaultCorpAdmin);
    }*/

    /**
     * 根据userId获取用户所有的corpId和mainAccountId集合
     *
     * @param operatorInfo 操作人
     * @return 返回值
     */
    /*public Map<String, List<CorpMainAccountInfo>> getAllCorpMainAccountInfos(TbPrivilegeUserDTO operatorInfo, String uid, boolean isDefaultCorpAdmin) {
        Map<String, List<CorpMainAccountInfo>> result = new HashMap<>();
        if (operatorInfo == null && isDefaultCorpAdmin) {
            // 沒有开通权限，但是需要默认公司管理员的权限
            operatorInfo = new TbPrivilegeUserDTO();
            operatorInfo.setUid(uid);
            operatorInfo.setUserType(UserTypeEnum.CORPADMIN.getUserType());
        }
        if (operatorInfo == null) {
            return result;
        }
        List<String> corpIds = getOperatorCorpRange(operatorInfo, uid, isDefaultCorpAdmin);
        if (CollectionUtils.isEmpty(corpIds)) {
            return result;
        }

        if (UserTypeEnum.GROUPADMIN.getUserType().equals(operatorInfo.getUserType())
                || UserTypeEnum.CORPADMIN.getUserType().equals(operatorInfo.getUserType())
                || UserTypeEnum.COMMONUSER.getUserType().equals(operatorInfo.getUserType()) || excludeUserType(operatorInfo.getUserType())) {
            corpIds.forEach(corpId -> {
                List<AccountStruct> accountStructList = appManagerServiceRpc.getAccountStructList(corpId);
                if (CollectionUtils.isNotEmpty(accountStructList)) {
                    List<CorpMainAccountInfo> corpMainAccountInfos = accountStructList.stream().map(x ->
                            new CorpMainAccountInfo(x.getAccountID(), x.getAccountName(), x.getCompanyName())).collect(Collectors.toList());
                    result.put(StringUtils.upperCase(corpId), corpMainAccountInfos);
                }
            });
        }

        if (UserTypeEnum.COMMONUSER.getUserType().equals(operatorInfo.getUserType())) {
            Integer roleId = operatorInfo.getRoleId();
            List<TbPrivilegeRoleAccountDTO> rolePermissions = tbPrivilegeRoleAccountDTOMapper.selectByRoleId(roleId);
            List<TbPrivilegeRoleDataPermissionDTO> dataPermissionDTOS = tbPrivilegeRoleDataPermissionDTOMapper.getRoleDataPermissionByRoleId(roleId);
            boolean allCorpPermission = dataPermissionDTOS.stream().anyMatch(x ->
                    x.getDataType().equals(PermissionDataTypeEnum.ALL_CORP_ACCOUNT.getDataType())
                            && StringUtils.equalsIgnoreCase(x.getData(), "1"));
            if (!allCorpPermission) {
                //如果普通用户不拥有所有主账户权限，获取有权限的主账户id
                List<String> hasPermissionAccountIds = rolePermissions.stream().map(x -> String.valueOf(x.getAccountId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(hasPermissionAccountIds)) {
                    Set<String> keys = result.keySet();
                    keys.forEach(key -> {
                        List<CorpMainAccountInfo> filterMainAccountInfos = result.get(key).stream().filter(x ->
                                hasPermissionAccountIds.contains(String.valueOf(x.getId()))).collect(Collectors.toList());
                        result.put(StringUtils.upperCase(key), filterMainAccountInfos);
                    });
                }
            }
        }
        return result;
    }*/


/*    public boolean excludeUserType(Integer userType) {
        if (userType == null) {
            return false;
        }
        if (UserTypeEnum.SUPERADMIN.getUserType().equals(userType)
                || UserTypeEnum.MANAGEMENT.getUserType().equals(userType)) {
            // 超管人员，管理人员默认返回自己公司id
            return true;
        }
        return false;
    }*/

    public boolean excludeManagement(String uid) {
        if (StringUtils.isEmpty(uid)) {
            return false;
        }
        String management = ApplicationConfig.uidOfCanViewCtripData();
        if (management.contains(uid)) {
            return true;
        }
        return false;
    }

/*    private boolean excludeManagerType(ManagerTypeEnum managerType) {
        if (managerType == null) {
            return false;
        }
        return excludeUserType(managerType.getValue() + 1);
    }*/

    /**
     * 添加角色
     *
     * @param roleId   角色id
     * @param roleType 角色类型
     * @param roleName 角色名称
     * @param operator 操作人
     * @param corpId   公司id
     * @param groupId  集团id
     * @return 返回值
     */
/*    @Transactional
    public int addRole(Integer roleId, Integer roleType, String roleName, String operator, String corpId, String groupId) {
        TbPrivilegeRoleDTO tbPrivilegeRoleDTO = new TbPrivilegeRoleDTO();
        if (roleId == null) {
            //新增角色
            tbPrivilegeRoleDTO.setName(roleName);
            tbPrivilegeRoleDTO.setCorpId(corpId);
            tbPrivilegeRoleDTO.setGroupId(groupId);
            tbPrivilegeRoleDTO.setRoleType(roleType);
            tbPrivilegeRoleDTO.setCreateUserId(operator);
            tbPrivilegeRoleDTO.setIsDel(new Byte("0"));
            tbPrivilegeRoleDTO.setDatachangeCreatetime(new Date());
            tbPrivilegeRoleDTOMapper.insertSelective(tbPrivilegeRoleDTO);
            roleId = tbPrivilegeRoleDTO.getId();
        } else {
            //修改角色
            TbPrivilegeRoleDTO roleDTO = tbPrivilegeRoleDTOMapper.selectByPrimaryKey(roleId);
            if (roleDTO != null) {
                roleDTO.setName(roleName);
                roleDTO.setDatachangeLasttime(new Date());
                tbPrivilegeRoleDTOMapper.updateByPrimaryKeySelective(roleDTO);
            }
        }
        return roleId;

    }*/

    /**
     * 角色绑定用户
     *
     * @param requestType 入参
     * @return 返回值
     */
 /*   public RoleBindUsersResponseType roleBindUser(RoleBindUsersRequestType requestType) {
        RoleBindUsersResponseType responseType = new RoleBindUsersResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.FAIL_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.FAIL));
        responseType.setResultStatus(resultStatus);
        Integer roleId = requestType.getRoleId();
        String operator = requestType.getOperator();
        List<String> userIds = requestType.getUserIds();
        TbPrivilegeUserDTO operatorInfo = tbPrivilegeUserDTOMapper.getByUserId(operator);
        TbPrivilegeRoleDTO tbPrivilegeRoleDTO = tbPrivilegeRoleDTOMapper.selectByPrimaryKey(roleId);
        if (CollectionUtils.isEmpty(userIds)) {
            resultStatus.setMessage(ChineseLanguageConfig.get(USERIDCANNOTEMPTY));
            return responseType;
        }
        if (tbPrivilegeRoleDTO == null) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ROLENOTEXIST));
            return responseType;
        }
        Boolean hasRolePermission = validUserHasRolePermission(operator, roleId);
        if (!hasRolePermission) {
            resultStatus.setMessage(ChineseLanguageConfig.get(NOPERMISSIONTOOPERATEROLE));
            return responseType;
        }

        //验证uid是否已经分配有角色
        for (String userId : userIds) {
            TbPrivilegeUserDTO tbPrivilegeUserDTO = tbPrivilegeUserDTOMapper.getByUserId(userId);
            if (tbPrivilegeUserDTO != null) {
                resultStatus.setMessage(String.format(ChineseLanguageConfig.get(USERHASALLOCATEROLE), userId));
                return responseType;
            }
        }

        //判断传入的uid是否是该公司或集团下的
        List<TbPrivilegeUserDTO> newUsers = new ArrayList<>();
        List<GetCorpUserInfoResponseType> corpUserInfoResponseTypeList = corpUserInfoServiceRpc.getCorpUserInfos(userIds);
        if (CollectionUtils.isNotEmpty(corpUserInfoResponseTypeList)) {
            for (int i = 0; i < corpUserInfoResponseTypeList.size(); i++) {
                GetCorpUserInfoResponseType getCorpUserInfoResponseType = corpUserInfoResponseTypeList.get(i);
                if (getCorpUserInfoResponseType != null && getCorpUserInfoResponseType.getRetCode() != null && getCorpUserInfoResponseType.getRetCode() == 0) {
                    String corpId = getCorpUserInfoResponseType.getCorpCorporation();

                    TbPrivilegeUserDTO newUser = new TbPrivilegeUserDTO();
                    newUser.setUid(getCorpUserInfoResponseType.getUID());
                    newUser.setUserName(StringUtils.trimToEmpty(getCorpUserInfoResponseType.getName()));
                    newUser.setCorpId(corpId);
                    newUser.setUserType(UserTypeEnum.COMMONUSER.getUserType());
                    newUser.setRoleId(roleId);
                    newUser.setStatus(AdminStatusEnum.NORMAL.getStatus());
                    newUser.setCreateUser(operator);
                    newUser.setIsDel(new Byte("0"));
                    newUser.setDatachangeCreatetime(new Date());
                    newUser.setDatachangeLasttime(new Date());

                    //判断是否属于同一个集团
                    GetCorpInfoResponseType getCorpInfoResponseType = corp4jServiceRpc.getCorpInfoById(corpId);
                    if (getCorpInfoResponseType != null && getCorpUserInfoResponseType.getRetCode() != null && getCorpInfoResponseType.getRetCode() == 0) {
                        String groupId = getCorpInfoResponseType.getCorpGroupId();

                        if (!((StringUtils.isEmpty(operatorInfo.getGroupId()) && StringUtils.isEmpty(groupId))
                                || StringUtils.equalsIgnoreCase(operatorInfo.getGroupId(), groupId))) {
                            resultStatus.setMessage(String.format(ChineseLanguageConfig.get("UserGroupChange"), getCorpUserInfoResponseType.getUID()));
                            return responseType;
                        }
                        newUser.setGroupId(groupId);
                        newUser.setCorpName(getCorpInfoResponseType.getCorporationName());
                    }

                    if (UserTypeEnum.CORPADMIN.getUserType().equals(operatorInfo.getUserType())) {
                        //如果是公司管理员，需要判断员工所在的公司和公司管理员的公司是否是同一个
                        if (!StringUtils.equalsIgnoreCase(tbPrivilegeRoleDTO.getCorpId(), corpId)) {
                            resultStatus.setMessage(String.format(ChineseLanguageConfig.get("UserCorpChange"), getCorpUserInfoResponseType.getUID()));
                            return responseType;
                        }
                    }
                    newUsers.add(newUser);
                }
            }
        }

        //验证通过，添加用户，绑定角色
        for (int i = 0; i < newUsers.size(); i++) {
            tbPrivilegeUserDTOMapper.insert(newUsers.get(i));
            baseBusiness.autoAddCrmManger(newUsers.get(i).getCorpId(), operator, "roleBindUser");
        }

        resultStatus.setCode(ConstDefine.SUCESS_STATUS_COE);
        resultStatus.setMessage(ChineseLanguageConfig.get(BINBUSINESS));

        //插入日志
        operationLogBusiness.insertLog(operator, null, tbPrivilegeRoleDTO.getCorpId(),
                OperationType.ROLE_BIND_USER, String.format("roleId:%d,roleName:%s,userIds:%s",
                        roleId, tbPrivilegeRoleDTO.getName(), org.apache.commons.lang3.StringUtils.join(userIds, ",")));

        return responseType;
    }
*/

    /**
     * 角色绑定的用户列表
     *
     * @param requestType 入参
     * @return 返回值
     */
/*    public RoleBindUserListResponseType roleBindUserList(RoleBindUserListRequestType requestType) {
        RoleBindUserListResponseType responseType = new RoleBindUserListResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS));
        responseType.setResultStatus(resultStatus);

        String operator = requestType.getOperator();
        Integer roleId = requestType.getRoleId();
        Boolean hasRolePermission = validUserHasRolePermission(operator, roleId);
        if (!hasRolePermission) {
            resultStatus.setCode(ConstDefine.FAIL_STATUS_COE);
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.NO_AUTH));
            return responseType;
        }
        int totalRecords = tbPrivilegeUserDTOMapper.countUsersByRoleId(roleId);
        responseType.setTotalRecords(totalRecords);
        responseType.setRoleBindUsers(new ArrayList<>());
        if (totalRecords > 0) {
            int pageIndex = requestType.getPageIndex() == null ? 1 : requestType.getPageIndex();
            int pageSize = Math.min(requestType.getPageSize() == null ? 20 : requestType.getPageSize(), ConstDefine.MAX_PAGESIZE);
            List<TbPrivilegeUserDTO> tbPrivilegeUserDTOList = tbPrivilegeUserDTOMapper.searchUsersByRoleId(roleId, new Pagination(pageIndex, pageSize));
            if (CollectionUtils.isNotEmpty(tbPrivilegeUserDTOList)) {
                List<String> uids = tbPrivilegeUserDTOList.stream().map(x -> x.getUid()).collect(Collectors.toList());
                List<GetCorpUserInfoResponseType> getCorpUserInfoResponseTypeList = corpUserInfoServiceRpc.getCorpUserInfos(uids);
                GetBaseAccountInfoListResponseType getBaseAccountInfoListResponseType = group4jServiceRpc.getBaseAccountInfoList(uids);
                List<AccountBaseInfo> accountBaseInfoList = null;
                if (getBaseAccountInfoListResponseType != null && getBaseAccountInfoListResponseType.getResponseStatus().getAck() == AckCodeType.Success) {
                    accountBaseInfoList = getBaseAccountInfoListResponseType.getInfos();
                }

                for (String uid : uids) {
                    RoleBindUserInfo roleBindUserInfo = new RoleBindUserInfo();
                    TbPrivilegeUserDTO tbPrivilegeUserDTO = tbPrivilegeUserDTOList.stream().filter(x -> uid.equals(x.getUid())).findFirst().get();
                    Optional<GetCorpUserInfoResponseType> getCorpUserInfoResponseType = getCorpUserInfoResponseTypeList.stream().filter(x ->
                            uid.equals(x.getUID())).findFirst();
                    Optional<AccountBaseInfo> accountBaseInfoOptional = null;
                    if (CollectionUtils.isNotEmpty(accountBaseInfoList)) {
                        accountBaseInfoOptional = accountBaseInfoList.stream().filter(x -> x.getUid().equals(uid)).findFirst();
                    }
                    roleBindUserInfo.setId(tbPrivilegeUserDTO.getId());
                    roleBindUserInfo.setUid(uid);
                    if (getCorpUserInfoResponseType.isPresent() && getCorpUserInfoResponseType.get().getRetCode() != null &&
                            getCorpUserInfoResponseType.get().getRetCode() == 0) {
                        roleBindUserInfo.setUserName(getCorpUserInfoResponseType.get().getName());
                    }
                    if (accountBaseInfoOptional != null && accountBaseInfoOptional.isPresent()) {
                        roleBindUserInfo.setMainAccount(accountBaseInfoOptional.get().getAccountName());
                        roleBindUserInfo.setSubAccount(accountBaseInfoOptional.get().getSubAccountName());
                    }
                    responseType.getRoleBindUsers().add(roleBindUserInfo);
                }
            }
        }
        return responseType;
    }*/

    /**
     * 角色解绑用户
     *
     * @param requestType 入参
     * @return 返回值
     */
    /*public RoleUnbindUserResponseType roleUnBindUser(RoleUnbindUserRequestType requestType) {
        RoleUnbindUserResponseType responseType = new RoleUnbindUserResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.FAIL_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.FAIL));
        responseType.setResultStatus(resultStatus);
        String operator = requestType.getOperator();
        Integer roleId = requestType.getRoleId();
        String userId = requestType.getUserId();

        if (roleId == null || userId == null) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.PARAM_ERROR));
            return responseType;
        }

        Boolean hasRolePermission = validUserHasRolePermission(operator, roleId);
        if (!hasRolePermission) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.NO_AUTH));
            return responseType;
        }
        TbPrivilegeUserDTO tbPrivilegeUserDTO = tbPrivilegeUserDTOMapper.getByUserId(userId);
        if (tbPrivilegeUserDTO == null) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.USERNOTEXIST));
            return responseType;
        }
        if (!tbPrivilegeUserDTO.getRoleId().equals(roleId)) {
            resultStatus.setMessage(ChineseLanguageConfig.get(ROLEBINDUSER_001));
            return responseType;
        }
        tbPrivilegeUserDTO.setIsDel(new Byte("1"));
        int count = tbPrivilegeUserDTOMapper.updateByPrimaryKey(tbPrivilegeUserDTO);
        if (count > 0) {
            resultStatus.setCode(ConstDefine.SUCESS_STATUS_COE);
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.SUCESS));
            return responseType;
        }

        //删除tb_privilege_user表缓存信息
        RedisCacheUtils.delKey(String.format(RedisKeyConst.Privilege.UserDbInfo, userId), false);

        //日志记录
        operationLogBusiness.insertLog(operator, null, tbPrivilegeUserDTO.getCorpId(), OperationType.ROLE_UNBIND_USER,
                String.format("roleId:%d,userId:%s", roleId, userId));

        return responseType;
    }*/

    /**
     * 查询用户
     *
     * @param requestType 入参
     * @return 返回值
     */
 /*   public QueryCorpUserResponseType queryCorpUser(QueryCorpUserRequestType requestType) {
        QueryCorpUserResponseType responseType = new QueryCorpUserResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS));
        responseType.setResultStatus(resultStatus);
        String operator = requestType.getOperator();
        String keyword = requestType.getSearchKeyword();
        TbPrivilegeUserDTO operatorInfo = tbPrivilegeUserDTOMapper.getByUserId(operator);
        if (operatorInfo == null) {
            resultStatus.setCode(ConstDefine.FAIL_STATUS_COE);
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.USERNOTEXIST));
            return responseType;
        }
        String corpId = operatorInfo.getCorpId();
        Integer searchType = requestType.getSearchType();
        Integer pageIndex = requestType.getPageIndex() == null ? 1 : requestType.getPageIndex();
        Integer pageSize = Math.min(requestType.getPageSize() == null ? 20 : requestType.getPageSize(), ConstDefine.MAX_PAGESIZE);
        CorpUserQueryType corpUserQueryType = CorpUserQueryType.getByValue(searchType);
        List<String> uids = new ArrayList<>();
        if (corpUserQueryType == CorpUserQueryType.UID) {
            uids.add(keyword);
        } else {
            GetCorpUserListResponseType getCorpUserListResponseType = appManagerServiceRpc.getCorpUserList(corpId, corpUserQueryType, keyword, pageIndex, pageSize);
            if (getCorpUserListResponseType != null && getCorpUserListResponseType.getResponseStatus().getAck() == AckCodeType.Success
                    && getCorpUserListResponseType.getRetCode() == 200) {
                responseType.setTotalRecords(getCorpUserListResponseType.getTotalNum());
                List<CorpUserInfo> corpUserInfoList = getCorpUserListResponseType.getCorpUserInfoList();
                if (CollectionUtils.isNotEmpty(corpUserInfoList)) {
                    for (CorpUserInfo corpuserInfo : corpUserInfoList) {
                        uids.add(corpuserInfo.getUid());
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(uids)) {
            List<GetCorpUserInfoResponseType> getCorpUserInfoResponseTypeList = corpUserInfoServiceRpc.getCorpUserInfos(uids);
            GetBaseAccountInfoListResponseType getBaseAccountInfoListResponseType = group4jServiceRpc.getBaseAccountInfoList(uids);
            List<AccountBaseInfo> accountBaseInfos = null;
            if (getBaseAccountInfoListResponseType != null && getBaseAccountInfoListResponseType.getResponseStatus().getAck() == AckCodeType.Success) {
                accountBaseInfos = getBaseAccountInfoListResponseType.getInfos();
            }

            List<CorpUserBaseInfo> corpUserBaseInfoList = new ArrayList<>();

            for (String uid : uids) {
                final String trimUid = uid.trim();
                Optional<GetCorpUserInfoResponseType> corpUserInfoResponseTypeOptional = null;
                Optional<AccountBaseInfo> accountBaseInfoOptional = null;
                if (CollectionUtils.isNotEmpty(getCorpUserInfoResponseTypeList)) {
                    corpUserInfoResponseTypeOptional = getCorpUserInfoResponseTypeList.stream().filter(x -> x.getResponseStatus()
                            != null && x.getResponseStatus().getAck() == AckCodeType.Success
                            && trimUid.equals(x.getUID())).findFirst();
                }
                if (CollectionUtils.isNotEmpty(accountBaseInfos)) {
                    accountBaseInfoOptional = accountBaseInfos.stream().filter(x -> trimUid.equals(x.getUid())).findFirst();
                }
                if (corpUserInfoResponseTypeOptional != null && corpUserInfoResponseTypeOptional.isPresent()
                        && accountBaseInfoOptional != null && accountBaseInfoOptional.isPresent()) {
                    GetCorpUserInfoResponseType getCorpUserInfoResponseType = corpUserInfoResponseTypeOptional.get();
                    AccountBaseInfo accountBaseInfo = accountBaseInfoOptional.get();
                    CorpUserBaseInfo corpUserBaseInfo = new CorpUserBaseInfo();
                    corpUserBaseInfo.setUid(trimUid);
                    corpUserBaseInfo.setName(getCorpUserInfoResponseType.getName());
                    corpUserBaseInfo.setMainAccount(accountBaseInfo.getAccountName());
                    corpUserBaseInfo.setSubAccount(accountBaseInfo.getSubAccountName());
                    corpUserBaseInfoList.add(corpUserBaseInfo);
                }

            }
            responseType.setCorpUserBaseInfos(corpUserBaseInfoList);
        }

        if (corpUserQueryType == CorpUserQueryType.UID) {
            responseType.setTotalRecords(responseType.getCorpUserBaseInfos() == null ? 0 : responseType.getCorpUserBaseInfos().size());
        }

        return responseType;
    }*/

    /**
     * 查询用户权限
     *
     * @param uid
     * @param serverFromApp 是否来自APP
     * @return
     */
/*    public QueryUserPermissionsResponseType queryUserPermissions(String uid, boolean serverFromApp, boolean isDefaultCorpAdmin) {
        QueryUserPermissionsResponseType responseType = new QueryUserPermissionsResponseType();
        ResultStatus resultStatus = new ResultStatus(ConstDefine.SUCESS_STATUS_COE, ChineseLanguageConfig.get(ConstDefine.SUCESS));
        responseType.setResultStatus(resultStatus);
        TbPrivilegeUserDTO operatorInfo = userBusiness.getPrivilegeUserDTO(uid);
        if (operatorInfo == null && BooleanUtils.isTrue(isDefaultCorpAdmin)) {
            // 沒有开通权限，但是需要默认公司管理员的权限
            operatorInfo = new TbPrivilegeUserDTO();
            operatorInfo.setUid(uid);
            operatorInfo.setUserType(UserTypeEnum.CORPADMIN.getUserType());
        }
        if (operatorInfo == null) {
            resultStatus.setCode(ConstDefine.FAIL_STATUS_COE);
            resultStatus.setMessage(ChineseLanguageConfig.get(ConstDefine.USERNOTEXIST));
            return responseType;
        }
        responseType.setUid(uid);
        responseType.setManagerType(ManagerTypeEnum.findByValue(operatorInfo.getUserType() - 1));

        responseType.setGroupId(StringUtils.upperCase(operatorInfo.getGroupId()));
        if (serverFromApp && (responseType.getManagerType() == ManagerTypeEnum.GROUPMANAGER
                || responseType.getManagerType() == ManagerTypeEnum.CORPMANAGER)) {
            List<String> corpIds = getOperatorCorpRange(operatorInfo, uid, isDefaultCorpAdmin);
            if (CollectionUtils.isNotEmpty(corpIds)) {
                responseType.setCorpIds(corpIds.stream().map(i -> StringUtils.upperCase(Optional.ofNullable(i).orElse(StringUtils.EMPTY))).collect(Collectors.toList()));
            }
        } else if (excludeManagerType(responseType.getManagerType()) || excludeManagement(uid)) {
            // 超管人员，管理人员默认返回自己公司id
            responseType.setCorpIds(Arrays.asList(StringUtils.upperCase(operatorInfo.getCorpId())));
        } else {
            //获取所有可以操作的公司和主账户
            Map<String, List<CorpMainAccountInfo>> corpMainAccountInfos = getAllCorpMainAccountInfos(operatorInfo, uid, isDefaultCorpAdmin);
            if (corpMainAccountInfos != null && corpMainAccountInfos.size() > 0) {
                Map<String, MainAccountsList> mainAccountsListMap = new HashMap<>();
                corpMainAccountInfos.forEach((corpId, corpMainAccountInfoList) -> {
                    MainAccountsList mainAccountsList = new MainAccountsList();
                    mainAccountsList.setAccounts(
                            corpMainAccountInfoList.stream()
                                    .map(x -> new MainAccounts(String.valueOf(x.getId()), x.getAccountName(), x.getCompanyName()))
                                    .collect(Collectors.toList())
                    );
                    mainAccountsListMap.put(StringUtils.upperCase(corpId), mainAccountsList);
                });
                responseType.setCorpIds(new ArrayList<>(mainAccountsListMap.keySet()));
                responseType.setCorpAccountsMap(mainAccountsListMap);
            }
        }

        Integer roleId = operatorInfo.getRoleId();
        List<CostCenterAndDept> costcenters = getCostcenter(roleId);
        responseType.setCostcenters(costcenters);
        List<CostCenterAndDept> depts = getDept(roleId);
        responseType.setDepts(depts);
        if (!serverFromApp) {
            GetCorpInfoResponseType getCorpInfoResponseType = corp4jServiceRpc.getCorpInfoById(operatorInfo.getCorpId());
            String orderNeedHotelVerify = "F";
            if (getCorpInfoResponseType != null && getCorpInfoResponseType.getRetCode() != null && getCorpInfoResponseType.getRetCode() == 0) {
                // 是否开通住店审核功能
                orderNeedHotelVerify = baseBusiness.getCorpColSetKeyValue(getCorpInfoResponseType.getCorpColSetKeyValues(), "orderNeedHotelVerify");
            }
            //获取用户页面资源
            UserPermissions userPermissions = getUserPageResources(operatorInfo, isDefaultCorpAdmin, orderNeedHotelVerify);
            if (userPermissions == null || CollectionUtils.isEmpty(userPermissions.getPageResourceKeys())) {
                if (excludeUserType(operatorInfo.getUserType()) || excludeManagement(uid)) {
                    userPermissions = getUserPageResourcesByCorp(operatorInfo);
                }
            }
            responseType.setResourceKeys(userPermissions.getPageResourceKeys());
            List<String> productLines = userPermissions.getProductLines();
            if (CollectionUtils.isNotEmpty(productLines)) {
                List<com.ctrip.corp.onlinereportprivilegeservice.service.ProductLineEnum> productLineEnumList = new ArrayList<>(productLines.size());
                for (String productLine : productLines) {
                    com.ctrip.corp.onlinereportprivilegeservice.service.ProductLineEnum productLineEnum
                            = com.ctrip.corp.onlinereportprivilegeservice.service.ProductLineEnum.valueOf(productLine);
                    productLineEnumList.add(productLineEnum);
                }
                responseType.setProductLines(productLineEnumList);
            }
        }
        return responseType;
    }*/

/*    private List<CostCenterAndDept> getCostcenter(Integer roleId) {
        List<CostCenterAndDept> costcenters = new ArrayList<>();
        for (int i = 1; i <= 6; i++) {
            List<String> infos = costCenterAndDepartmentSearchBusicess.queryCostCenterOrDepartment(roleId, i, CostCenterAndDepartSearchType.COSTCENTER);
            CostCenterAndDept dept = new CostCenterAndDept();
            dept.setLevel(i);
            dept.setInfo(infos);
            costcenters.add(dept);
        }
        return costcenters;
    }

    private List<CostCenterAndDept> getDept(Integer roleId) {
        List<CostCenterAndDept> costcenters = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            List<String> infos = costCenterAndDepartmentSearchBusicess.queryCostCenterOrDepartment(roleId, i, CostCenterAndDepartSearchType.DEPARTMENT);
            CostCenterAndDept dept = new CostCenterAndDept();
            dept.setLevel(i);
            dept.setInfo(infos);
            costcenters.add(dept);
        }
        return costcenters;
    }*/

    /**
     * 获取用户的页面资源权限
     *
     * @param operatorInfo 操作对象
     * @return 返回值
     */
/*    public UserPermissions getUserPageResources(TbPrivilegeUserDTO operatorInfo, boolean isDefaultCorpAdmin, String orderNeedHotelVerify) {
        if (operatorInfo == null) {
            return null;
        }
        UserPermissions userPermissions = new UserPermissions();
        List<String> productLines = new ArrayList<>();
        List<String> pageResources = new ArrayList<>();
        if (UserTypeEnum.GROUPADMIN.getUserType().equals(operatorInfo.getUserType())
                || UserTypeEnum.CORPADMIN.getUserType().equals(operatorInfo.getUserType())) {
            //集团管理员 或 公司管理员有所有产线权限
            productLines = Arrays.asList(ProductLineEnum.HOTEL.name(), ProductLineEnum.FLIGHT.name(),
                    ProductLineEnum.CAR.name(), ProductLineEnum.TRAIN.name());
            String corpId = null;
            String groupId = operatorInfo.getGroupId();
            if (StringUtils.isEmpty(groupId)) {
                corpId = operatorInfo.getCorpId();
            }
            if (isDefaultCorpAdmin) {
                // isDefaultCorpAdmin为true约定为国际站，默认查询日本套餐
                String packageId = ApplicationConfig.get("PACKAGE_INT_POS_POS_JP");
                pageResources = tbPrivilegePackageResourceDTOMapper.getResourceKeysByPackageId(Integer.valueOf(packageId.trim()));
            } else {
                TbPrivilegeCorpPackageDTO packageDTO = tbPrivilegeCorpPackageDTOMapper.selectByCorpOrGroup(corpId, groupId);
                if (packageDTO == null) {
                    return userPermissions;
                }
                pageResources = tbPrivilegePackageResourceDTOMapper.getResourceKeysByPackageId(packageDTO.getPackageId());
            }
        } else if (UserTypeEnum.COMMONUSER.getUserType().equals(operatorInfo.getUserType())) {
            //普通用户
            if (operatorInfo.getRoleId() == null) {
                return userPermissions;
            }
            List<TbPrivilegeRoleDataPermissionDTO> roleDataPermissionDTOList = tbPrivilegeRoleDataPermissionDTOMapper.getRoleDataPermissionByRoleId(operatorInfo.getRoleId());
            if (CollectionUtils.isEmpty(roleDataPermissionDTOList)) {
                return userPermissions;
            }
            productLines = roleDataPermissionDTOList.stream().filter(x -> PermissionDataTypeEnum.PRODUCT_LINE.getDataType().equals(x.getDataType())).map(x -> x.getData()).collect(Collectors.toList());
            pageResources = tbPrivilegeRoleDTOMapper.getRolePageResourceKeysByRoleId(operatorInfo.getRoleId());
        }
        // 未开通夜审的不能查看夜审数据
        if (CollectionUtils.isNotEmpty(pageResources) && !org.apache.commons.lang3.StringUtils.equalsIgnoreCase(orderNeedHotelVerify, "T")) {
            pageResources.remove("DetailReport:HotelOrderAuditDetails");
        }
        userPermissions.setProductLines(productLines);
        userPermissions.setPageResourceKeys(pageResources);
        return userPermissions;
    }*/

    /**
     * 获取用户的页面资源权限
     *
     * @param operatorInfo 操作对象
     * @return 返回值
     */
/*    public UserPermissions getUserPageResourcesByCorp(TbPrivilegeUserDTO operatorInfo) {
        if (operatorInfo == null) {
            return null;
        }
        UserPermissions userPermissions = new UserPermissions();

        List<String> productLines = Arrays.asList(ProductLineEnum.HOTEL.name(), ProductLineEnum.FLIGHT.name(),
                ProductLineEnum.CAR.name(), ProductLineEnum.TRAIN.name());
        String corpId = null;
        String groupId = operatorInfo.getGroupId();
        if (StringUtils.isEmpty(groupId)) {
            corpId = operatorInfo.getCorpId();
        }
        TbPrivilegeCorpPackageDTO packageDTO = tbPrivilegeCorpPackageDTOMapper.selectByCorpOrGroup(corpId, groupId);
        if (packageDTO == null) {
            return userPermissions;
        }
        List<String> pageResources = tbPrivilegePackageResourceDTOMapper.getResourceKeysByPackageId(packageDTO.getPackageId());
        userPermissions.setProductLines(productLines);
        userPermissions.setPageResourceKeys(pageResources);
        return userPermissions;
    }*/

    /**
     * 根据角色Id获取页面资源id
     *
     * @param roleId 角色id
     * @return 返回值
     */
/*    public List<Integer> getResourceIdsByRoleId(Integer roleId) {
        List<TbPrivilegeRoleResourcePermissionDTO> roleResources = tbPrivilegeRoleResourcePermissionDTOMapper.getResourcesByRoleId(roleId);
        if (CollectionUtils.isNotEmpty(roleResources)) {
            List<Integer> resourceIds = roleResources.stream().map(x -> x.getResourceId()).collect(Collectors.toList());
            return resourceIds;
        }
        return new ArrayList<>();
    }*/

    /**
     * 校验卡号是否有操作角色的权限
     *
     * @param userId 卡号
     * @param roleId 角色id
     * @return 返回值
     */
/*    private Boolean validUserHasRolePermission(String userId, Integer roleId) {
        //获取当前用户信息
        TbPrivilegeUserDTO operatorInfo = tbPrivilegeUserDTOMapper.getByUserId(userId);
        //判断是否有查看这个角色的权限
        if (operatorInfo == null) {
            return false;
        }
        TbPrivilegeRoleDTO tbPrivilegeRoleDTO = tbPrivilegeRoleDTOMapper.selectByPrimaryKey(roleId);
        if (tbPrivilegeRoleDTO == null) {
            return false;
        }
        if (UserTypeEnum.GROUPADMIN.getUserType().equals(operatorInfo.getUserType()) && RoleTypeEnum.GROUP_ROLE.getRoleType().equals(tbPrivilegeRoleDTO.getRoleType())) {
            List<String> corpIds = getOperatorCorpRange(userId, false);
            // 校验角色所属公式是否是否再有权限的公司范围内
            return Optional.ofNullable(corpIds).orElse(Lists.newArrayList()).stream().anyMatch(x -> StringUtils.equalsIgnoreCase(x, tbPrivilegeRoleDTO.getCorpId()));
        }
        if (UserTypeEnum.CORPADMIN.getUserType().equals(operatorInfo.getUserType())) {
            if (StringUtils.equalsIgnoreCase(operatorInfo.getCorpId(), tbPrivilegeRoleDTO.getCorpId())
                    && RoleTypeEnum.CORP_ROLE.getRoleType().equals(tbPrivilegeRoleDTO.getRoleType())) {
                return true;
            }
        }
        return false;
    }*/

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorpMainAccountInfo {
        private Integer id;
        private String accountName;
        private String companyName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserPermissions {
        List<String> productLines;
        List<String> pageResourceKeys;
    }

}
