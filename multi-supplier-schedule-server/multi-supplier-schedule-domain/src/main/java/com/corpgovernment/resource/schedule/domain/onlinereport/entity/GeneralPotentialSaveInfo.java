package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 概览-潜在节省
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "reportDate",
    "totalPotentialSaveAmount",
    "totalOverAmount",
    "totalRefundloss",
    "totalRebookloss",
    "potentialSaveAmount",
    "overAmount",
    "rcTimes",
    "rcPercent",
    "refundloss",
    "refundtkt",
    "refundPercent",
    "rebookloss",
    "rebooktkt",
    "rebookPercent"
})
public class GeneralPotentialSaveInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public GeneralPotentialSaveInfo(
        String reportDate,
        BigDecimal totalPotentialSaveAmount,
        BigDecimal totalOverAmount,
        BigDecimal totalRefundloss,
        BigDecimal totalRebookloss,
        BigDecimal potentialSaveAmount,
        BigDecimal overAmount,
        Integer rcTimes,
        BigDecimal rcPercent,
        BigDecimal refundloss,
        Integer refundtkt,
        BigDecimal refundPercent,
        BigDecimal rebookloss,
        Integer rebooktkt,
        BigDecimal rebookPercent) {
        this.reportDate = reportDate;
        this.totalPotentialSaveAmount = totalPotentialSaveAmount;
        this.totalOverAmount = totalOverAmount;
        this.totalRefundloss = totalRefundloss;
        this.totalRebookloss = totalRebookloss;
        this.potentialSaveAmount = potentialSaveAmount;
        this.overAmount = overAmount;
        this.rcTimes = rcTimes;
        this.rcPercent = rcPercent;
        this.refundloss = refundloss;
        this.refundtkt = refundtkt;
        this.refundPercent = refundPercent;
        this.rebookloss = rebookloss;
        this.rebooktkt = rebooktkt;
        this.rebookPercent = rebookPercent;
    }

    public GeneralPotentialSaveInfo() {
    }

    /**
     * 消费记录开始时间
     */
    @JsonProperty("reportDate")
    public String reportDate;

    /**
     * 总潜在节省（从有消费记录开始）
     */
    @JsonProperty("totalPotentialSaveAmount")
    public BigDecimal totalPotentialSaveAmount;

    /**
     * 总超标损失（从有消费记录开始）
     */
    @JsonProperty("totalOverAmount")
    public BigDecimal totalOverAmount;

    /**
     * 总退票损失（从有消费记录开始）
     */
    @JsonProperty("totalRefundloss")
    public BigDecimal totalRefundloss;

    /**
     * 总改签损失（从有消费记录开始）
     */
    @JsonProperty("totalRebookloss")
    public BigDecimal totalRebookloss;

    /**
     * 潜在节省(超标损失+退改损失)
     */
    @JsonProperty("potentialSaveAmount")
    public BigDecimal potentialSaveAmount;

    /**
     * 超标损失
     */
    @JsonProperty("overAmount")
    public BigDecimal overAmount;

    /**
     * 超标次数
     */
    @JsonProperty("rcTimes")
    public Integer rcTimes;

    /**
     * 超标占比
     */
    @JsonProperty("rcPercent")
    public BigDecimal rcPercent;

    /**
     * 退票损失既退票潜在节省
     */
    @JsonProperty("refundloss")
    public BigDecimal refundloss;

    /**
     * 退票次数
     */
    @JsonProperty("refundtkt")
    public Integer refundtkt;

    /**
     * 退票占比
     */
    @JsonProperty("refundPercent")
    public BigDecimal refundPercent;

    /**
     * 改签损失既改签潜在节省
     */
    @JsonProperty("rebookloss")
    public BigDecimal rebookloss;

    /**
     * 改签次数
     */
    @JsonProperty("rebooktkt")
    public Integer rebooktkt;

    /**
     * 改签占比
     */
    @JsonProperty("rebookPercent")
    public BigDecimal rebookPercent;

    /**
     * 消费记录开始时间
     */
    public String getReportDate() {
        return reportDate;
    }

    /**
     * 消费记录开始时间
     */
    public void setReportDate(final String reportDate) {
        this.reportDate = reportDate;
    }

    /**
     * 总潜在节省（从有消费记录开始）
     */
    public BigDecimal getTotalPotentialSaveAmount() {
        return totalPotentialSaveAmount;
    }

    /**
     * 总潜在节省（从有消费记录开始）
     */
    public void setTotalPotentialSaveAmount(final BigDecimal totalPotentialSaveAmount) {
        this.totalPotentialSaveAmount = totalPotentialSaveAmount;
    }

    /**
     * 总超标损失（从有消费记录开始）
     */
    public BigDecimal getTotalOverAmount() {
        return totalOverAmount;
    }

    /**
     * 总超标损失（从有消费记录开始）
     */
    public void setTotalOverAmount(final BigDecimal totalOverAmount) {
        this.totalOverAmount = totalOverAmount;
    }

    /**
     * 总退票损失（从有消费记录开始）
     */
    public BigDecimal getTotalRefundloss() {
        return totalRefundloss;
    }

    /**
     * 总退票损失（从有消费记录开始）
     */
    public void setTotalRefundloss(final BigDecimal totalRefundloss) {
        this.totalRefundloss = totalRefundloss;
    }

    /**
     * 总改签损失（从有消费记录开始）
     */
    public BigDecimal getTotalRebookloss() {
        return totalRebookloss;
    }

    /**
     * 总改签损失（从有消费记录开始）
     */
    public void setTotalRebookloss(final BigDecimal totalRebookloss) {
        this.totalRebookloss = totalRebookloss;
    }

    /**
     * 潜在节省(超标损失+退改损失)
     */
    public BigDecimal getPotentialSaveAmount() {
        return potentialSaveAmount;
    }

    /**
     * 潜在节省(超标损失+退改损失)
     */
    public void setPotentialSaveAmount(final BigDecimal potentialSaveAmount) {
        this.potentialSaveAmount = potentialSaveAmount;
    }

    /**
     * 超标损失
     */
    public BigDecimal getOverAmount() {
        return overAmount;
    }

    /**
     * 超标损失
     */
    public void setOverAmount(final BigDecimal overAmount) {
        this.overAmount = overAmount;
    }

    /**
     * 超标次数
     */
    public Integer getRcTimes() {
        return rcTimes;
    }

    /**
     * 超标次数
     */
    public void setRcTimes(final Integer rcTimes) {
        this.rcTimes = rcTimes;
    }

    /**
     * 超标占比
     */
    public BigDecimal getRcPercent() {
        return rcPercent;
    }

    /**
     * 超标占比
     */
    public void setRcPercent(final BigDecimal rcPercent) {
        this.rcPercent = rcPercent;
    }

    /**
     * 退票损失既退票潜在节省
     */
    public BigDecimal getRefundloss() {
        return refundloss;
    }

    /**
     * 退票损失既退票潜在节省
     */
    public void setRefundloss(final BigDecimal refundloss) {
        this.refundloss = refundloss;
    }

    /**
     * 退票次数
     */
    public Integer getRefundtkt() {
        return refundtkt;
    }

    /**
     * 退票次数
     */
    public void setRefundtkt(final Integer refundtkt) {
        this.refundtkt = refundtkt;
    }

    /**
     * 退票占比
     */
    public BigDecimal getRefundPercent() {
        return refundPercent;
    }

    /**
     * 退票占比
     */
    public void setRefundPercent(final BigDecimal refundPercent) {
        this.refundPercent = refundPercent;
    }

    /**
     * 改签损失既改签潜在节省
     */
    public BigDecimal getRebookloss() {
        return rebookloss;
    }

    /**
     * 改签损失既改签潜在节省
     */
    public void setRebookloss(final BigDecimal rebookloss) {
        this.rebookloss = rebookloss;
    }

    /**
     * 改签次数
     */
    public Integer getRebooktkt() {
        return rebooktkt;
    }

    /**
     * 改签次数
     */
    public void setRebooktkt(final Integer rebooktkt) {
        this.rebooktkt = rebooktkt;
    }

    /**
     * 改签占比
     */
    public BigDecimal getRebookPercent() {
        return rebookPercent;
    }

    /**
     * 改签占比
     */
    public void setRebookPercent(final BigDecimal rebookPercent) {
        this.rebookPercent = rebookPercent;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final GeneralPotentialSaveInfo other = (GeneralPotentialSaveInfo)obj;
        return
            Objects.equal(this.reportDate, other.reportDate) &&
            Objects.equal(this.totalPotentialSaveAmount, other.totalPotentialSaveAmount) &&
            Objects.equal(this.totalOverAmount, other.totalOverAmount) &&
            Objects.equal(this.totalRefundloss, other.totalRefundloss) &&
            Objects.equal(this.totalRebookloss, other.totalRebookloss) &&
            Objects.equal(this.potentialSaveAmount, other.potentialSaveAmount) &&
            Objects.equal(this.overAmount, other.overAmount) &&
            Objects.equal(this.rcTimes, other.rcTimes) &&
            Objects.equal(this.rcPercent, other.rcPercent) &&
            Objects.equal(this.refundloss, other.refundloss) &&
            Objects.equal(this.refundtkt, other.refundtkt) &&
            Objects.equal(this.refundPercent, other.refundPercent) &&
            Objects.equal(this.rebookloss, other.rebookloss) &&
            Objects.equal(this.rebooktkt, other.rebooktkt) &&
            Objects.equal(this.rebookPercent, other.rebookPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.reportDate == null ? 0 : this.reportDate.hashCode());
        result = 31 * result + (this.totalPotentialSaveAmount == null ? 0 : this.totalPotentialSaveAmount.hashCode());
        result = 31 * result + (this.totalOverAmount == null ? 0 : this.totalOverAmount.hashCode());
        result = 31 * result + (this.totalRefundloss == null ? 0 : this.totalRefundloss.hashCode());
        result = 31 * result + (this.totalRebookloss == null ? 0 : this.totalRebookloss.hashCode());
        result = 31 * result + (this.potentialSaveAmount == null ? 0 : this.potentialSaveAmount.hashCode());
        result = 31 * result + (this.overAmount == null ? 0 : this.overAmount.hashCode());
        result = 31 * result + (this.rcTimes == null ? 0 : this.rcTimes.hashCode());
        result = 31 * result + (this.rcPercent == null ? 0 : this.rcPercent.hashCode());
        result = 31 * result + (this.refundloss == null ? 0 : this.refundloss.hashCode());
        result = 31 * result + (this.refundtkt == null ? 0 : this.refundtkt.hashCode());
        result = 31 * result + (this.refundPercent == null ? 0 : this.refundPercent.hashCode());
        result = 31 * result + (this.rebookloss == null ? 0 : this.rebookloss.hashCode());
        result = 31 * result + (this.rebooktkt == null ? 0 : this.rebooktkt.hashCode());
        result = 31 * result + (this.rebookPercent == null ? 0 : this.rebookPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("reportDate", reportDate)
            .add("totalPotentialSaveAmount", totalPotentialSaveAmount)
            .add("totalOverAmount", totalOverAmount)
            .add("totalRefundloss", totalRefundloss)
            .add("totalRebookloss", totalRebookloss)
            .add("potentialSaveAmount", potentialSaveAmount)
            .add("overAmount", overAmount)
            .add("rcTimes", rcTimes)
            .add("rcPercent", rcPercent)
            .add("refundloss", refundloss)
            .add("refundtkt", refundtkt)
            .add("refundPercent", refundPercent)
            .add("rebookloss", rebookloss)
            .add("rebooktkt", rebooktkt)
            .add("rebookPercent", rebookPercent)
            .toString();
    }
}
