package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

/**
 * @Description: 提前预订天数节省分析
 * <AUTHOR>
 * @Date 2019/6/18
 */
public class FlightPreorderDayDetailBO {

    //提前天数
    private String nameId;
    //提前天数
    private String name;
    //提前预定区间开始天数
    private int orderRangeStart;
    //张数
    private int quantity;
    //张数占比
    private double quantityPercent;
    //全价票张数
    private int fullQuantity;
    //平均折扣
    private double avgDiscount;
    //不含全价票平均折扣
    private double noFullAvgDiscount;
    //潜在节省
    private double latentSave;
    //里程均价
    private double milAvgPrice;
    //Y舱折扣
    private double priceRate;
    //Y舱里程
    private double tpmSY;
    //Y舱净价
    private double yPrice;
    //Y舱票张数
    private int yQuantity;
    //不含全价票折扣
    private double noFullPriceRate;

    public String getNameId() {
        return nameId;
    }

    public void setNameId(String nameId) {
        this.nameId = nameId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getOrderRangeStart() {
        return orderRangeStart;
    }

    public void setOrderRangeStart(int orderRangeStart) {
        this.orderRangeStart = orderRangeStart;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public double getQuantityPercent() {
        return quantityPercent;
    }

    public void setQuantityPercent(double quantityPercent) {
        this.quantityPercent = quantityPercent;
    }

    public int getFullQuantity() {
        return fullQuantity;
    }

    public void setFullQuantity(int fullQuantity) {
        this.fullQuantity = fullQuantity;
    }

    public double getAvgDiscount() {
        return avgDiscount;
    }

    public void setAvgDiscount(double avgDiscount) {
        this.avgDiscount = avgDiscount;
    }

    public double getNoFullAvgDiscount() {
        return noFullAvgDiscount;
    }

    public void setNoFullAvgDiscount(double noFullAvgDiscount) {
        this.noFullAvgDiscount = noFullAvgDiscount;
    }

    public double getLatentSave() {
        return latentSave;
    }

    public void setLatentSave(double latentSave) {
        this.latentSave = latentSave;
    }

    public double getMilAvgPrice() {
        return milAvgPrice;
    }

    public void setMilAvgPrice(double milAvgPrice) {
        this.milAvgPrice = milAvgPrice;
    }

    public double getPriceRate() {
        return priceRate;
    }

    public void setPriceRate(double priceRate) {
        this.priceRate = priceRate;
    }

    public double getTpmSY() {
        return tpmSY;
    }

    public void setTpmSY(double tpmSY) {
        this.tpmSY = tpmSY;
    }

    public double getyPrice() {
        return yPrice;
    }

    public void setyPrice(double yPrice) {
        this.yPrice = yPrice;
    }

    public int getyQuantity() {
        return yQuantity;
    }

    public void setyQuantity(int yQuantity) {
        this.yQuantity = yQuantity;
    }

    public double getNoFullPriceRate() {
        return noFullPriceRate;
    }

    public void setNoFullPriceRate(double noFullPriceRate) {
        this.noFullPriceRate = noFullPriceRate;
    }
}
