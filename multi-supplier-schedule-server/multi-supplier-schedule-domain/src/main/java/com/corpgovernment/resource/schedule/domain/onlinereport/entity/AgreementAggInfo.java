package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 协议消费信息聚合
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "totalPriceDomTa",
    "totalPriceDomNta",
    "totalPriceInterTa",
    "totalPriceInterNta",
    "totalQuantityDomTa",
    "totalQuantityDomNta",
    "totalQuantityInterTa",
    "totalQuantityInterNta",
    "totalPriceTa",
    "totalPriceNta",
    "totalQuantityTa",
    "totalQuantityNta"
})
public class AgreementAggInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public AgreementAggInfo(
        BigDecimal totalPriceDomTa,
        BigDecimal totalPriceDomNta,
        BigDecimal totalPriceInterTa,
        BigDecimal totalPriceInterNta,
        Integer totalQuantityDomTa,
        Integer totalQuantityDomNta,
        Integer totalQuantityInterTa,
        Integer totalQuantityInterNta,
        BigDecimal totalPriceTa,
        BigDecimal totalPriceNta,
        Integer totalQuantityTa,
        Integer totalQuantityNta) {
        this.totalPriceDomTa = totalPriceDomTa;
        this.totalPriceDomNta = totalPriceDomNta;
        this.totalPriceInterTa = totalPriceInterTa;
        this.totalPriceInterNta = totalPriceInterNta;
        this.totalQuantityDomTa = totalQuantityDomTa;
        this.totalQuantityDomNta = totalQuantityDomNta;
        this.totalQuantityInterTa = totalQuantityInterTa;
        this.totalQuantityInterNta = totalQuantityInterNta;
        this.totalPriceTa = totalPriceTa;
        this.totalPriceNta = totalPriceNta;
        this.totalQuantityTa = totalQuantityTa;
        this.totalQuantityNta = totalQuantityNta;
    }

    public AgreementAggInfo() {
    }

    /**
     * 国内三方成交净价
     */
    @JsonProperty("totalPriceDomTa")
    public BigDecimal totalPriceDomTa;

    /**
     * 国内非三方成交净价
     */
    @JsonProperty("totalPriceDomNta")
    public BigDecimal totalPriceDomNta;

    /**
     * 国际三方成交净价
     */
    @JsonProperty("totalPriceInterTa")
    public BigDecimal totalPriceInterTa;

    /**
     * 国际非三方成交净价
     */
    @JsonProperty("totalPriceInterNta")
    public BigDecimal totalPriceInterNta;

    /**
     * 国内三方票张数
     */
    @JsonProperty("totalQuantityDomTa")
    public Integer totalQuantityDomTa;

    /**
     * 国内非三方票张数
     */
    @JsonProperty("totalQuantityDomNta")
    public Integer totalQuantityDomNta;

    /**
     * 国际三方票张数
     */
    @JsonProperty("totalQuantityInterTa")
    public Integer totalQuantityInterTa;

    /**
     * 国际非三方票张数
     */
    @JsonProperty("totalQuantityInterNta")
    public Integer totalQuantityInterNta;

    /**
     * 三方成交净价
     */
    @JsonProperty("totalPriceTa")
    public BigDecimal totalPriceTa;

    /**
     * 非三方成交净价
     */
    @JsonProperty("totalPriceNta")
    public BigDecimal totalPriceNta;

    /**
     * 三方票张数
     */
    @JsonProperty("totalQuantityTa")
    public Integer totalQuantityTa;

    /**
     * 非三方票张数
     */
    @JsonProperty("totalQuantityNta")
    public Integer totalQuantityNta;

    /**
     * 国内三方成交净价
     */
    public BigDecimal getTotalPriceDomTa() {
        return totalPriceDomTa;
    }

    /**
     * 国内三方成交净价
     */
    public void setTotalPriceDomTa(final BigDecimal totalPriceDomTa) {
        this.totalPriceDomTa = totalPriceDomTa;
    }

    /**
     * 国内非三方成交净价
     */
    public BigDecimal getTotalPriceDomNta() {
        return totalPriceDomNta;
    }

    /**
     * 国内非三方成交净价
     */
    public void setTotalPriceDomNta(final BigDecimal totalPriceDomNta) {
        this.totalPriceDomNta = totalPriceDomNta;
    }

    /**
     * 国际三方成交净价
     */
    public BigDecimal getTotalPriceInterTa() {
        return totalPriceInterTa;
    }

    /**
     * 国际三方成交净价
     */
    public void setTotalPriceInterTa(final BigDecimal totalPriceInterTa) {
        this.totalPriceInterTa = totalPriceInterTa;
    }

    /**
     * 国际非三方成交净价
     */
    public BigDecimal getTotalPriceInterNta() {
        return totalPriceInterNta;
    }

    /**
     * 国际非三方成交净价
     */
    public void setTotalPriceInterNta(final BigDecimal totalPriceInterNta) {
        this.totalPriceInterNta = totalPriceInterNta;
    }

    /**
     * 国内三方票张数
     */
    public Integer getTotalQuantityDomTa() {
        return totalQuantityDomTa;
    }

    /**
     * 国内三方票张数
     */
    public void setTotalQuantityDomTa(final Integer totalQuantityDomTa) {
        this.totalQuantityDomTa = totalQuantityDomTa;
    }

    /**
     * 国内非三方票张数
     */
    public Integer getTotalQuantityDomNta() {
        return totalQuantityDomNta;
    }

    /**
     * 国内非三方票张数
     */
    public void setTotalQuantityDomNta(final Integer totalQuantityDomNta) {
        this.totalQuantityDomNta = totalQuantityDomNta;
    }

    /**
     * 国际三方票张数
     */
    public Integer getTotalQuantityInterTa() {
        return totalQuantityInterTa;
    }

    /**
     * 国际三方票张数
     */
    public void setTotalQuantityInterTa(final Integer totalQuantityInterTa) {
        this.totalQuantityInterTa = totalQuantityInterTa;
    }

    /**
     * 国际非三方票张数
     */
    public Integer getTotalQuantityInterNta() {
        return totalQuantityInterNta;
    }

    /**
     * 国际非三方票张数
     */
    public void setTotalQuantityInterNta(final Integer totalQuantityInterNta) {
        this.totalQuantityInterNta = totalQuantityInterNta;
    }

    /**
     * 三方成交净价
     */
    public BigDecimal getTotalPriceTa() {
        return totalPriceTa;
    }

    /**
     * 三方成交净价
     */
    public void setTotalPriceTa(final BigDecimal totalPriceTa) {
        this.totalPriceTa = totalPriceTa;
    }

    /**
     * 非三方成交净价
     */
    public BigDecimal getTotalPriceNta() {
        return totalPriceNta;
    }

    /**
     * 非三方成交净价
     */
    public void setTotalPriceNta(final BigDecimal totalPriceNta) {
        this.totalPriceNta = totalPriceNta;
    }

    /**
     * 三方票张数
     */
    public Integer getTotalQuantityTa() {
        return totalQuantityTa;
    }

    /**
     * 三方票张数
     */
    public void setTotalQuantityTa(final Integer totalQuantityTa) {
        this.totalQuantityTa = totalQuantityTa;
    }

    /**
     * 非三方票张数
     */
    public Integer getTotalQuantityNta() {
        return totalQuantityNta;
    }

    /**
     * 非三方票张数
     */
    public void setTotalQuantityNta(final Integer totalQuantityNta) {
        this.totalQuantityNta = totalQuantityNta;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final AgreementAggInfo other = (AgreementAggInfo)obj;
        return
            Objects.equal(this.totalPriceDomTa, other.totalPriceDomTa) &&
            Objects.equal(this.totalPriceDomNta, other.totalPriceDomNta) &&
            Objects.equal(this.totalPriceInterTa, other.totalPriceInterTa) &&
            Objects.equal(this.totalPriceInterNta, other.totalPriceInterNta) &&
            Objects.equal(this.totalQuantityDomTa, other.totalQuantityDomTa) &&
            Objects.equal(this.totalQuantityDomNta, other.totalQuantityDomNta) &&
            Objects.equal(this.totalQuantityInterTa, other.totalQuantityInterTa) &&
            Objects.equal(this.totalQuantityInterNta, other.totalQuantityInterNta) &&
            Objects.equal(this.totalPriceTa, other.totalPriceTa) &&
            Objects.equal(this.totalPriceNta, other.totalPriceNta) &&
            Objects.equal(this.totalQuantityTa, other.totalQuantityTa) &&
            Objects.equal(this.totalQuantityNta, other.totalQuantityNta);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.totalPriceDomTa == null ? 0 : this.totalPriceDomTa.hashCode());
        result = 31 * result + (this.totalPriceDomNta == null ? 0 : this.totalPriceDomNta.hashCode());
        result = 31 * result + (this.totalPriceInterTa == null ? 0 : this.totalPriceInterTa.hashCode());
        result = 31 * result + (this.totalPriceInterNta == null ? 0 : this.totalPriceInterNta.hashCode());
        result = 31 * result + (this.totalQuantityDomTa == null ? 0 : this.totalQuantityDomTa.hashCode());
        result = 31 * result + (this.totalQuantityDomNta == null ? 0 : this.totalQuantityDomNta.hashCode());
        result = 31 * result + (this.totalQuantityInterTa == null ? 0 : this.totalQuantityInterTa.hashCode());
        result = 31 * result + (this.totalQuantityInterNta == null ? 0 : this.totalQuantityInterNta.hashCode());
        result = 31 * result + (this.totalPriceTa == null ? 0 : this.totalPriceTa.hashCode());
        result = 31 * result + (this.totalPriceNta == null ? 0 : this.totalPriceNta.hashCode());
        result = 31 * result + (this.totalQuantityTa == null ? 0 : this.totalQuantityTa.hashCode());
        result = 31 * result + (this.totalQuantityNta == null ? 0 : this.totalQuantityNta.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("totalPriceDomTa", totalPriceDomTa)
            .add("totalPriceDomNta", totalPriceDomNta)
            .add("totalPriceInterTa", totalPriceInterTa)
            .add("totalPriceInterNta", totalPriceInterNta)
            .add("totalQuantityDomTa", totalQuantityDomTa)
            .add("totalQuantityDomNta", totalQuantityDomNta)
            .add("totalQuantityInterTa", totalQuantityInterTa)
            .add("totalQuantityInterNta", totalQuantityInterNta)
            .add("totalPriceTa", totalPriceTa)
            .add("totalPriceNta", totalPriceNta)
            .add("totalQuantityTa", totalQuantityTa)
            .add("totalQuantityNta", totalQuantityNta)
            .toString();
    }
}
