package com.corpgovernment.resource.schedule.domain.onlinereport.consume;

import lombok.Data;

import java.math.BigDecimal;

/*
 * <AUTHOR>
 * @date 2021/12/8 20:31
 * @Desc
 */
@Data
public class OnlineReportConsumeBO {


    private BigDecimal totalAmount = BigDecimal.ZERO;

    private BigDecimal totalOneAmount = BigDecimal.ZERO;

    private BigDecimal totalTwoAmount = BigDecimal.ZERO;

    private BigDecimal totalThreeAmount = BigDecimal.ZERO;

    private BigDecimal totalFourAmount = BigDecimal.ZERO;

    private BigDecimal totalFiveAmount = BigDecimal.ZERO;

    private BigDecimal totalSixAmount = BigDecimal.ZERO;

    private BigDecimal totalSevenAmount = BigDecimal.ZERO;

    private BigDecimal avgPrice = BigDecimal.ZERO;

    private BigDecimal avgOtherPrice = BigDecimal.ZERO;

    private BigDecimal totalCorpServiceFee = BigDecimal.ZERO;

    private Integer totalCntOrder = 0;


    private Integer totalQuantity = 0;

    private Integer totalOneQuantity = 0;

    private Integer totalTwoQuantity = 0;

    private Integer totalThreeQuantity = 0;

    private Integer totalFourQuantity = 0;

    private Integer totalFiveQuantity = 0;

    private Integer totalSixQuantity = 0;

    private Integer totalSevenQuantity = 0;


    private Integer totalRefundQuantity = 0;

    private Integer totalRebookQuantity = 0;

}
