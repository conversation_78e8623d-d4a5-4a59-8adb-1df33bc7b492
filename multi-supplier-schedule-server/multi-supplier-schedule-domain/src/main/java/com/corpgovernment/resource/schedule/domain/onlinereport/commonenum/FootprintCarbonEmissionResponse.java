package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * * 个人足迹碳排 返回
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "data",
    "allTotalCarbon",
    "allSaveCarbon"
})
public class FootprintCarbonEmissionResponse implements Serializable {
    private static final long serialVersionUID = 1L;





    public FootprintCarbonEmissionResponse(
        Integer responseCode,
        String responseDesc,
        List<FootprintCarbonEmissionInfo> data,
        BigDecimal allTotalCarbon,
        BigDecimal allSaveCarbon) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        
        this.data = data;
        this.allTotalCarbon = allTotalCarbon;
        this.allSaveCarbon = allSaveCarbon;
    }

    public FootprintCarbonEmissionResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    

    @JsonProperty("data")
    public List<FootprintCarbonEmissionInfo> data;

    @JsonProperty("allTotalCarbon")
    public BigDecimal allTotalCarbon;

    @JsonProperty("allSaveCarbon")
    public BigDecimal allSaveCarbon;

    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }
    

    public List<FootprintCarbonEmissionInfo> getData() {
        return data;
    }

    public void setData(final List<FootprintCarbonEmissionInfo> data) {
        this.data = data;
    }
    public BigDecimal getAllTotalCarbon() {
        return allTotalCarbon;
    }

    public void setAllTotalCarbon(final BigDecimal allTotalCarbon) {
        this.allTotalCarbon = allTotalCarbon;
    }
    public BigDecimal getAllSaveCarbon() {
        return allSaveCarbon;
    }

    public void setAllSaveCarbon(final BigDecimal allSaveCarbon) {
        this.allSaveCarbon = allSaveCarbon;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FootprintCarbonEmissionResponse other = (FootprintCarbonEmissionResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc)  &&
            Objects.equal(this.data, other.data) &&
            Objects.equal(this.allTotalCarbon, other.allTotalCarbon) &&
            Objects.equal(this.allSaveCarbon, other.allSaveCarbon);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        
        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());
        result = 31 * result + (this.allTotalCarbon == null ? 0 : this.allTotalCarbon.hashCode());
        result = 31 * result + (this.allSaveCarbon == null ? 0 : this.allSaveCarbon.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            
            .add("data", data)
            .add("allTotalCarbon", allTotalCarbon)
            .add("allSaveCarbon", allSaveCarbon)
            .toString();
    }
}
