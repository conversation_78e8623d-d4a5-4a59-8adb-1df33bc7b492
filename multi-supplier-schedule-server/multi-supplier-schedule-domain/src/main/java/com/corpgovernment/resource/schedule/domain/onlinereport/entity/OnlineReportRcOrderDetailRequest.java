package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;

/**
 * 风险订单-明细查询request
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "queryBu",
        "orderId"
})
public class OnlineReportRcOrderDetailRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    public OnlineReportRcOrderDetailRequest(
            QueryReportBuTypeEnum queryBu,
            Long orderId) {
        this.queryBu = queryBu;
        this.orderId = orderId;
    }

    public OnlineReportRcOrderDetailRequest() {
    }

    /**
     * 查询产线
     */
    @JsonProperty("queryBu")
    public QueryReportBuTypeEnum queryBu;

    /**
     * 订单号
     */
    @JsonProperty("orderId")
    public Long orderId;

    /**
     * 查询产线
     */
    public QueryReportBuTypeEnum getQueryBu() {
        return queryBu;
    }

    /**
     * 查询产线
     */
    public void setQueryBu(final QueryReportBuTypeEnum queryBu) {
        this.queryBu = queryBu;
    }

    /**
     * 订单号
     */
    public Long getOrderId() {
        return orderId;
    }

    /**
     * 订单号
     */
    public void setOrderId(final Long orderId) {
        this.orderId = orderId;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportRcOrderDetailRequest other = (OnlineReportRcOrderDetailRequest) obj;
        return
                Objects.equal(this.queryBu, other.queryBu) &&
                        Objects.equal(this.orderId, other.orderId);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.queryBu == null ? 0 : this.queryBu.hashCode());
        result = 31 * result + (this.orderId == null ? 0 : this.orderId.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("queryBu", queryBu)
                .add("orderId", orderId)
                .toString();
    }
}
