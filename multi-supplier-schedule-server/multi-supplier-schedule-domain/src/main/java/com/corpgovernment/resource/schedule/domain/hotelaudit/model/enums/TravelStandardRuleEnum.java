package com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-24 00:04
 */
@AllArgsConstructor
@Getter
public enum TravelStandardRuleEnum {
    
    COHABIT_RULE("CohabitRule"),
    PRICE_RULE("PriceRule"),
    OFF_PEAK_SEASON_RULE("OffPeakSeasonRule"),
    HOTEL_STEP_RULE("HotelStepRule"),
    FLOAT_PRICE_RULE("FloatPriceRule"),
    ;
    
    private final String code;
    
}
