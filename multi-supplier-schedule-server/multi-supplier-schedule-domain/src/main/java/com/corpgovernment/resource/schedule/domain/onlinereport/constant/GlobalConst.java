package com.corpgovernment.resource.schedule.domain.onlinereport.constant;

/**
 * @Description: 通用常量
 * <AUTHOR>
 * @Date 2019/3/1
 */
public class GlobalConst {
    public static final String APPID = "100015078";
    public static final int APPID_150902 = 150902;

    public static final int SHARK_REFERENCE_APPID = 150901;
    public static final String STRING_ZERO = "0";
    public static final String STRING_EMPTY = "";
    public static final String UUID = "uuid";
    public static final String CORPID = "corpId";

    public static final String REPORTID = "reportid";


    public static final String SEPARATOR = ",";


    public final static String JOB_PARAMETER_NAME = "JOB_PARAMETER";


    public static final String JOB_NAME_PREFIX = "ctrip.corp.onlinereport.";

    /**
     * 每个卡号自定义报表默认最大值
     */
    public static final int MAXCOUNT_NEWCUXTOMREPORT_DEFAULT = 10;

    /**
     * 小数位数
     */
    public static final int ZERO_DIGIT_NUM = 0;
    public static final int ONE_DIGIT_NUM = 1;
    public static final int NEW_DIGIT_NUM = 2;
    public static final int THREE_DIGIT_NUM = 3;
    public static final int FOUR_DIGIT_NUM = 4;

    public static final String MAX_DAYS = "maxDays";

    public static final int MAX_DAYS_DEFAULT = 366;

    public static final String MAX_INVERVAL_MONTHS = "maxIntervalsMonths";

    public static final int MAX_INVERVAL_MONTHS_DEFAULT = 13;

    public static final String EMAIL_SENDER_DEFAULT = "<EMAIL>";
    public static final String EMAIL_SENDERNAME_DEFAULT = "BASE DATA GROUP";
    public static final String EMAIL_CHARSET = "GB2312";
    public static final String EMAIL_SENDCODE = "12010017";
    public static final int EMAIL_TEMPLATEID = 12010017;
    public static final String EMAIL_MESSAGETYPE = "N";
    public static final String EMAIL_CLIENTTYPE = "PU";
    public static final String EAMIL_SERVICEFROM = "100023325";
    public static final String EMAIL_OPERATOR_DEFAULT = "system";

    public static final int RESULT_EXPIRE = 3600;

    //下载任务
    public final static int SAMEREPORTMAXTASKCOUNT_DEFAULT = 5;//同一个report默认最大下载任务数

    public final static String SAMEREPORTMAXTASKCOUNTTIPS = "SameReportMaxTaskCountTips";

    public final static String SAMEREPORTMAXTASKCOUNTTCOUNT= "SameReportMaxTaskCount";

    public final static String SAMEREPORTTASKRUNNING= "SameReportTaskRunning";


    public final static int TRIGGER_WORK_DAY = 2;//默认发送邮件的工作日数

    public final static String  SEMICOLON_SEPARATOR = ";";//分号分隔符


   //发送任务
    public final static int SAMEREPORTMAXSENDTASKCOUNT_DEFAULT = 1;//同一个report默认最大发送任务数

    public final static String SAMEREPORTMAXSENDTASKCOUNTTIPS = "SameReportMaxSendTaskCountTips";

    public final static String SAMEREPORTMAXSENDTASKCOUNTTCOUNT= "SameReportMaxSendTaskCount";

    public final static String SAMEREPORTSENDTASKRUNNING= "SameReportSendTaskRunning";

    public final static String PERCENT_QUOTE = "%";

    public final static String HOTEL_ISAUDIT_KEY= "HOTEL_ISAUDIT";

}

