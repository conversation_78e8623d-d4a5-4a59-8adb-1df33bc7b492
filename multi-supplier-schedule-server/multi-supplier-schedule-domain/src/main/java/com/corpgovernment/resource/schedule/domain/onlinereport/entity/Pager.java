package com.corpgovernment.resource.schedule.domain.onlinereport.entity;



import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "dataCount",
        "pageSize",
        "pageIndex",
        "pageTotal"
})
public class Pager implements Serializable {
    private static final long serialVersionUID = 1L;





    public Pager(
            Long dataCount,
            Integer pageSize,
            Integer pageIndex,
            Integer pageTotal) {
        this.dataCount = dataCount;
        this.pageSize = pageSize;
        this.pageIndex = pageIndex;
        this.pageTotal = pageTotal;
    }

    public Pager() {
    }

    /**
     * 总数据数
     */
    @JsonProperty("dataCount")
    public Long dataCount;

    /**
     * 页面数据数
     */
    @JsonProperty("pageSize")
    public Integer pageSize;

    /**
     * 页数
     */
    @JsonProperty("pageIndex")
    public Integer pageIndex;

    /**
     * 总页数
     */
    @JsonProperty("pageTotal")
    public Integer pageTotal;

    /**
     * 总数据数
     */
    public Long getDataCount() {
        return dataCount;
    }

    /**
     * 总数据数
     */
    public void setDataCount(final Long dataCount) {
        this.dataCount = dataCount;
    }

    /**
     * 页面数据数
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 页面数据数
     */
    public void setPageSize(final Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 页数
     */
    public Integer getPageIndex() {
        return pageIndex;
    }

    /**
     * 页数
     */
    public void setPageIndex(final Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    /**
     * 总页数
     */
    public Integer getPageTotal() {
        return pageTotal;
    }

    /**
     * 总页数
     */
    public void setPageTotal(final Integer pageTotal) {
        this.pageTotal = pageTotal;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final Pager other = (Pager)obj;
        return
                Objects.equal(this.dataCount, other.dataCount) &&
                        Objects.equal(this.pageSize, other.pageSize) &&
                        Objects.equal(this.pageIndex, other.pageIndex) &&
                        Objects.equal(this.pageTotal, other.pageTotal);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dataCount == null ? 0 : this.dataCount.hashCode());
        result = 31 * result + (this.pageSize == null ? 0 : this.pageSize.hashCode());
        result = 31 * result + (this.pageIndex == null ? 0 : this.pageIndex.hashCode());
        result = 31 * result + (this.pageTotal == null ? 0 : this.pageTotal.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("dataCount", dataCount)
                .add("pageSize", pageSize)
                .add("pageIndex", pageIndex)
                .add("pageTotal", pageTotal)
                .toString();
    }
}
