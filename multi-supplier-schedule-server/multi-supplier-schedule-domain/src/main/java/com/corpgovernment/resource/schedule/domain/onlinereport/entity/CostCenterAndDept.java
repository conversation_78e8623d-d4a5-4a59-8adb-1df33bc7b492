package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.List;

/**
 * 获取角色详情-返回体
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "level",
        "info"
})
public class CostCenterAndDept implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 层级
     */
    @JsonProperty("level")
    public Integer level;
    /**
     * 具体的成本中心或部门
     */
    @JsonProperty("info")
    public List<String> info;

    public CostCenterAndDept(
            Integer level,
            List<String> info) {
        this.level = level;
        this.info = info;
    }

    public CostCenterAndDept() {
    }

    /**
     * 层级
     */
    public Integer getLevel() {
        return level;
    }

    /**
     * 层级
     */
    public void setLevel(final Integer level) {
        this.level = level;
    }

    /**
     * 具体的成本中心或部门
     */
    public List<String> getInfo() {
        return info;
    }

    /**
     * 具体的成本中心或部门
     */
    public void setInfo(final List<String> info) {
        this.info = info;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.level;
            case 1:
                return this.info;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.level = (Integer) fieldValue;
                break;
            case 1:
                this.info = (List<String>) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final CostCenterAndDept other = (CostCenterAndDept) obj;
        return
                Objects.equal(this.level, other.level) &&
                        Objects.equal(this.info, other.info);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.level == null ? 0 : this.level.hashCode());
        result = 31 * result + (this.info == null ? 0 : this.info.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("level", level)
                .add("info", info)
                .toString();
    }
}
