package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 机票-里程均价分布
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "title",
    "avgMileageNotice",
    "legends",
    "data"
})
public class FlightAvgMileageDist implements Serializable {
    private static final long serialVersionUID = 1L;

    

    

    public FlightAvgMileageDist(
        String title,
        String avgMileageNotice,
        List<OnlineReportTrendLegend> legends,
        List<OnlineReportTrendPoint> data) {
        this.title = title;
        this.avgMileageNotice = avgMileageNotice;
        this.legends = legends;
        this.data = data;
    }

    public FlightAvgMileageDist() {
    }

    /**
     * 标题
     */
    @JsonProperty("title")
    public String title;

    /**
     * 文案
     */
    @JsonProperty("avgMileageNotice")
    public String avgMileageNotice;

    @JsonProperty("legends")
    public List<OnlineReportTrendLegend> legends;

    @JsonProperty("data")
    public List<OnlineReportTrendPoint> data;

    /**
     * 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 标题
     */
    public void setTitle(final String title) {
        this.title = title;
    }

    /**
     * 文案
     */
    public String getAvgMileageNotice() {
        return avgMileageNotice;
    }

    /**
     * 文案
     */
    public void setAvgMileageNotice(final String avgMileageNotice) {
        this.avgMileageNotice = avgMileageNotice;
    }
    public List<OnlineReportTrendLegend> getLegends() {
        return legends;
    }

    public void setLegends(final List<OnlineReportTrendLegend> legends) {
        this.legends = legends;
    }
    public List<OnlineReportTrendPoint> getData() {
        return data;
    }

    public void setData(final List<OnlineReportTrendPoint> data) {
        this.data = data;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightAvgMileageDist other = (FlightAvgMileageDist)obj;
        return
            Objects.equal(this.title, other.title) &&
            Objects.equal(this.avgMileageNotice, other.avgMileageNotice) &&
            Objects.equal(this.legends, other.legends) &&
            Objects.equal(this.data, other.data);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.title == null ? 0 : this.title.hashCode());
        result = 31 * result + (this.avgMileageNotice == null ? 0 : this.avgMileageNotice.hashCode());
        result = 31 * result + (this.legends == null ? 0 : this.legends.hashCode());
        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("title", title)
            .add("avgMileageNotice", avgMileageNotice)
            .add("legends", legends)
            .add("data", data)
            .toString();
    }
}
