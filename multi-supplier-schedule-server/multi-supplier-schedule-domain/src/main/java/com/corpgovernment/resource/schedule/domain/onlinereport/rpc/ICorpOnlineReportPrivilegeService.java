package com.corpgovernment.resource.schedule.domain.onlinereport.rpc;


import com.corpgovernment.resource.schedule.domain.onlinereport.search.CostCenterAndDepartmentSearchRequestType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.CostCenterAndDepartmentSearchResponseType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.GetMainAccountIdsByCorpIdsRequestType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.GetMainAccountIdsByCorpIdsResponseType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.OperatorCorpRangeRequestType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.OperatorCorpRangeResponseType;

/*
 * <AUTHOR>
 * @date 2020/7/6 16:29
 * @Desc
 */
public interface ICorpOnlineReportPrivilegeService {


    OperatorCorpRangeResponseType operatorCorpRange(OperatorCorpRangeRequestType var1) throws Exception;


    GetMainAccountIdsByCorpIdsResponseType getMainAccountIdsByCorpIds(GetMainAccountIdsByCorpIdsRequestType requestType) throws Exception;


    CostCenterAndDepartmentSearchResponseType costCenterAndDepartSearch(CostCenterAndDepartmentSearchRequestType requestType) throws Exception;

    CostCenterAndDepartmentSearchResponseType costCenterAndDepartSearchByCk(CostCenterAndDepartmentSearchRequestType requestType) throws Exception;
}
