package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
//@Database(name = "CorpReportDB")
@Table(name = "corpreportsendtask")
public class CorpreportSendTaskPO {

    /**
     * 主键Id
     */
    @Id
	@Column(name = "taskId")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long taskId;

    /**
     * 任务状态
     */
	@Column(name = "taskStatus")
	@Type(value = Types.INTEGER)
	private Integer taskStatus;

    /**
     * 报表表示
     */
	@Column(name = "reportId")
	@Type(value = Types.VARCHAR)
	private String reportId;

    /**
     * 是否有效
     */
	@Column(name = "isValid")
	@Type(value = Types.BIT)
	private Boolean isValid;


	/**
	 * 收件人
	 */
	@Column(name = "reciverEmails")
	@Type(value = Types.VARCHAR)
	private String reciverEmails;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 最后修改时间
     */
	@Column(name = "DataChange_LastTime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	/**
	 * 用户卡号
	 */
	@Column(name = "uid")
	@Type(value = Types.VARCHAR)
	private String uid;

	/**
	 * 邮件标题
	 */
	@Column(name = "title")
	@Type(value = Types.VARCHAR)
	private String title;


	/**
	 * 邮件内容
	 */
	@Column(name = "content")
	@Type(value = Types.VARCHAR)
	private String content;


	/**
	 * 邮件内容
	 */
	@Column(name = "conditions")
	@Type(value = Types.VARCHAR)
	private String conditions;

	/**
	 * 报表类型
	 */
	@Column(name = "reportType")
	@Type(value = Types.TINYINT)
	private Integer reportType;

	/**
	 * 语言
	 */
	@Column(name = "lang")
	@Type(value = Types.VARCHAR)
	private String lang;


	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}

	public Integer getTaskStatus() {
		return taskStatus;
	}

	public void setTaskStatus(Integer taskStatus) {
		this.taskStatus = taskStatus;
	}

	public String getReportId() {
		return reportId;
	}

	public void setReportId(String reportId) {
		this.reportId = reportId;
	}

	public String getReciverEmails() {
		return reciverEmails;
	}

	public void setReciverEmails(String reciverEmails) {
		this.reciverEmails = reciverEmails;
	}


	public Boolean getValid() {
		return isValid;
	}

	public void setValid(Boolean valid) {
		isValid = valid;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}


	public String getConditions() {
		return conditions;
	}

	public void setConditions(String conditions) {
		this.conditions = conditions;
	}

	public Integer getReportType() {
		return reportType;
	}

	public void setReportType(Integer reportType) {
		this.reportType = reportType;
	}

	public String getLang() {
		return lang;
	}

	public void setLang(String lang) {
		this.lang = lang;
	}
}
