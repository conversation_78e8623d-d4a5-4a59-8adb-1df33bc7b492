package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 用车部门消费
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "topList",
    "otherConsume",
    "sumConsume",
    "corpConsume",
    "industryConsume"
})
public class CarTopDeptConsumeInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public CarTopDeptConsumeInfo(
        List<OnlineReportCarTopDeptConsume> topList,
        OnlineReportCarTopDeptConsume otherConsume,
        OnlineReportCarTopDeptConsume sumConsume,
        OnlineReportCarTopDeptConsume corpConsume,
        OnlineReportCarTopDeptConsume industryConsume) {
        this.topList = topList;
        this.otherConsume = otherConsume;
        this.sumConsume = sumConsume;
        this.corpConsume = corpConsume;
        this.industryConsume = industryConsume;
    }

    public CarTopDeptConsumeInfo() {
    }

    @JsonProperty("topList")
    public List<OnlineReportCarTopDeptConsume> topList;

    @JsonProperty("otherConsume")
    public OnlineReportCarTopDeptConsume otherConsume;

    @JsonProperty("sumConsume")
    public OnlineReportCarTopDeptConsume sumConsume;

    @JsonProperty("corpConsume")
    public OnlineReportCarTopDeptConsume corpConsume;

    @JsonProperty("industryConsume")
    public OnlineReportCarTopDeptConsume industryConsume;

    public List<OnlineReportCarTopDeptConsume> getTopList() {
        return topList;
    }

    public void setTopList(final List<OnlineReportCarTopDeptConsume> topList) {
        this.topList = topList;
    }
    public OnlineReportCarTopDeptConsume getOtherConsume() {
        return otherConsume;
    }

    public void setOtherConsume(final OnlineReportCarTopDeptConsume otherConsume) {
        this.otherConsume = otherConsume;
    }
    public OnlineReportCarTopDeptConsume getSumConsume() {
        return sumConsume;
    }

    public void setSumConsume(final OnlineReportCarTopDeptConsume sumConsume) {
        this.sumConsume = sumConsume;
    }
    public OnlineReportCarTopDeptConsume getCorpConsume() {
        return corpConsume;
    }

    public void setCorpConsume(final OnlineReportCarTopDeptConsume corpConsume) {
        this.corpConsume = corpConsume;
    }
    public OnlineReportCarTopDeptConsume getIndustryConsume() {
        return industryConsume;
    }

    public void setIndustryConsume(final OnlineReportCarTopDeptConsume industryConsume) {
        this.industryConsume = industryConsume;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final CarTopDeptConsumeInfo other = (CarTopDeptConsumeInfo)obj;
        return
            Objects.equal(this.topList, other.topList) &&
            Objects.equal(this.otherConsume, other.otherConsume) &&
            Objects.equal(this.sumConsume, other.sumConsume) &&
            Objects.equal(this.corpConsume, other.corpConsume) &&
            Objects.equal(this.industryConsume, other.industryConsume);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.topList == null ? 0 : this.topList.hashCode());
        result = 31 * result + (this.otherConsume == null ? 0 : this.otherConsume.hashCode());
        result = 31 * result + (this.sumConsume == null ? 0 : this.sumConsume.hashCode());
        result = 31 * result + (this.corpConsume == null ? 0 : this.corpConsume.hashCode());
        result = 31 * result + (this.industryConsume == null ? 0 : this.industryConsume.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("topList", topList)
            .add("otherConsume", otherConsume)
            .add("sumConsume", sumConsume)
            .add("corpConsume", corpConsume)
            .add("industryConsume", industryConsume)
            .toString();
    }
}
