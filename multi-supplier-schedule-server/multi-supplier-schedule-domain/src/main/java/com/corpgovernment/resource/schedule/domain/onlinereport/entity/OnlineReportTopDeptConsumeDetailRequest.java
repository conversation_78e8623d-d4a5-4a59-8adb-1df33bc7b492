package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.Map;

/**
 * 各产线部门top消费明细
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "eId",
        "lang",
        "basecondition",
        "analysisObjectEnum",
        "topLimit",
        "queryBu",
        "productType",
        "extData",
        "analysisObjectOrgInfo"
})
public class OnlineReportTopDeptConsumeDetailRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonProperty("eId")
    public String eId;
    /**
     * 语言
     */
    @JsonProperty("lang")
    public String lang;
    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;
    /**
     * 分析对象
     */
    @JsonProperty("analysisObjectEnum")
    public AnalysisObjectEnum analysisObjectEnum;
    /**
     * top限制
     */
    @JsonProperty("topLimit")
    public Integer topLimit;
    /**
     * 查询产线
     */
    @JsonProperty("queryBu")
    public QueryReportBuTypeEnum queryBu;
    /**
     * 国内、国际
     */
    @JsonProperty("productType")
    public String productType;
    @JsonProperty("extData")
    public Map<String, String> extData;
    /**
     * 分析对象组织信息
     */
    @JsonProperty("analysisObjectOrgInfo")
    public AnalysisObjectOrgInfo analysisObjectOrgInfo;
    /**
     * 下钻对象
     */
    @JsonProperty("drillDownObjectEnum")
    public AnalysisObjectEnum drillDownObjectEnum;
    /**
     * 下钻目标值
     */
    @JsonProperty("drillDownVal")
    public String drillDownVal;

    public OnlineReportTopDeptConsumeDetailRequest(
            String eId,
            String lang,
            BaseQueryCondition basecondition,
            AnalysisObjectEnum analysisObjectEnum,
            Integer topLimit,
            QueryReportBuTypeEnum queryBu,
            String productType,
            Map<String, String> extData,
            AnalysisObjectOrgInfo analysisObjectOrgInfo,
            AnalysisObjectEnum drillDownObjectEnum,
            String drillDownVal) {
        this.eId = eId;
        this.lang = lang;
        this.basecondition = basecondition;
        this.analysisObjectEnum = analysisObjectEnum;
        this.topLimit = topLimit;
        this.queryBu = queryBu;
        this.productType = productType;
        this.extData = extData;
        this.analysisObjectOrgInfo = analysisObjectOrgInfo;
        this.drillDownObjectEnum = drillDownObjectEnum;
        this.drillDownVal = drillDownVal;
    }

    public OnlineReportTopDeptConsumeDetailRequest() {
    }

    public String getEId() {
        return eId;
    }

    public void setEId(final String eId) {
        this.eId = eId;
    }

    /**
     * 语言
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * 分析对象
     */
    public AnalysisObjectEnum getAnalysisObjectEnum() {
        return analysisObjectEnum;
    }

    /**
     * 分析对象
     */
    public void setAnalysisObjectEnum(final AnalysisObjectEnum analysisObjectEnum) {
        this.analysisObjectEnum = analysisObjectEnum;
    }

    /**
     * top限制
     */
    public Integer getTopLimit() {
        return topLimit;
    }

    /**
     * top限制
     */
    public void setTopLimit(final Integer topLimit) {
        this.topLimit = topLimit;
    }

    /**
     * 查询产线
     */
    public QueryReportBuTypeEnum getQueryBu() {
        return queryBu;
    }

    /**
     * 查询产线
     */
    public void setQueryBu(final QueryReportBuTypeEnum queryBu) {
        this.queryBu = queryBu;
    }

    /**
     * 国内、国际
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 国内、国际
     */
    public void setProductType(final String productType) {
        this.productType = productType;
    }

    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    /**
     * 分析对象组织信息
     */
    public AnalysisObjectOrgInfo getAnalysisObjectOrgInfo() {
        return analysisObjectOrgInfo;
    }

    /**
     * 分析对象组织信息
     */
    public void setAnalysisObjectOrgInfo(final AnalysisObjectOrgInfo analysisObjectOrgInfo) {
        this.analysisObjectOrgInfo = analysisObjectOrgInfo;
    }

    /**
     * 下钻对象
     */
    public AnalysisObjectEnum getDrillDownObjectEnum() {
        return drillDownObjectEnum;
    }

    /**
     * 下钻对象
     */
    public void setDrillDownObjectEnum(final AnalysisObjectEnum drillDownObjectEnum) {
        this.drillDownObjectEnum = drillDownObjectEnum;
    }

    /**
     * 下钻目标值
     */
    public String getDrillDownVal() {
        return drillDownVal;
    }

    /**
     * 下钻目标值
     */
    public void setDrillDownVal(final String drillDownVal) {
        this.drillDownVal = drillDownVal;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.eId;
            case 1:
                return this.lang;
            case 2:
                return this.basecondition;
            case 3:
                return this.analysisObjectEnum;
            case 4:
                return this.topLimit;
            case 5:
                return this.queryBu;
            case 6:
                return this.productType;
            case 7:
                return this.extData;
            case 8:
                return this.analysisObjectOrgInfo;
            case 9:
                return this.drillDownObjectEnum;
            case 10:
                return this.drillDownVal;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.eId = (String) fieldValue;
                break;
            case 1:
                this.lang = (String) fieldValue;
                break;
            case 2:
                this.basecondition = (BaseQueryCondition) fieldValue;
                break;
            case 3:
                this.analysisObjectEnum = (AnalysisObjectEnum) fieldValue;
                break;
            case 4:
                this.topLimit = (Integer) fieldValue;
                break;
            case 5:
                this.queryBu = (QueryReportBuTypeEnum) fieldValue;
                break;
            case 6:
                this.productType = (String) fieldValue;
                break;
            case 7:
                this.extData = (Map<String, String>) fieldValue;
                break;
            case 8:
                this.analysisObjectOrgInfo = (AnalysisObjectOrgInfo) fieldValue;
                break;
            case 9:
                this.drillDownObjectEnum = (AnalysisObjectEnum) fieldValue;
                break;
            case 10:
                this.drillDownVal = (String) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTopDeptConsumeDetailRequest other = (OnlineReportTopDeptConsumeDetailRequest) obj;
        return
                Objects.equal(this.eId, other.eId) &&
                        Objects.equal(this.lang, other.lang) &&
                        Objects.equal(this.basecondition, other.basecondition) &&
                        Objects.equal(this.analysisObjectEnum, other.analysisObjectEnum) &&
                        Objects.equal(this.topLimit, other.topLimit) &&
                        Objects.equal(this.queryBu, other.queryBu) &&
                        Objects.equal(this.productType, other.productType) &&
                        Objects.equal(this.extData, other.extData) &&
                        Objects.equal(this.analysisObjectOrgInfo, other.analysisObjectOrgInfo) &&
                        Objects.equal(this.drillDownObjectEnum, other.drillDownObjectEnum) &&
                        Objects.equal(this.drillDownVal, other.drillDownVal);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eId == null ? 0 : this.eId.hashCode());
        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.analysisObjectEnum == null ? 0 : this.analysisObjectEnum.hashCode());
        result = 31 * result + (this.topLimit == null ? 0 : this.topLimit.hashCode());
        result = 31 * result + (this.queryBu == null ? 0 : this.queryBu.hashCode());
        result = 31 * result + (this.productType == null ? 0 : this.productType.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());
        result = 31 * result + (this.analysisObjectOrgInfo == null ? 0 : this.analysisObjectOrgInfo.hashCode());
        result = 31 * result + (this.drillDownObjectEnum == null ? 0 : this.drillDownObjectEnum.hashCode());
        result = 31 * result + (this.drillDownVal == null ? 0 : this.drillDownVal.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("eId", eId)
                .add("lang", lang)
                .add("basecondition", basecondition)
                .add("analysisObjectEnum", analysisObjectEnum)
                .add("topLimit", topLimit)
                .add("queryBu", queryBu)
                .add("productType", productType)
                .add("extData", extData)
                .add("analysisObjectOrgInfo", analysisObjectOrgInfo)
                .add("drillDownObjectEnum", drillDownObjectEnum)
                .add("drillDownVal", drillDownVal)
                .toString();
    }
}
