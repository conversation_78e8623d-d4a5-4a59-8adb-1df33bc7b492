package com.corpgovernment.resource.schedule.domain.accesslog.model;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.sql.Types;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class Top5InfoTraffic {


    /**
     * 问题ID
     */
    @Column(name = "question_id")
    @Type(Types.INTEGER)
    private Integer questionId;

    /**
     * 浏览量（PV）
     */
    @Column(name = "pv")
    @Type(Types.INTEGER)
    private Integer pv;

    /**
     * app pv
     */
    @Column(name = "app_pv")
    @Type(Types.INTEGER)
    private Integer appPv;

    /**
     * web pv
     */
    @Column(name = "web_pv")
    @Type(Types.INTEGER)
    private Integer webPv;

}
