package com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.factory;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.HotelAuditItem;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.HotelAuditResult;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.OrderInfo;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.HotelAuditRuleEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.ProductTypeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelModeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.IHotelAuditRule;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.impl.PreHandleAuditRule;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-24 15:07
 */
@Service
@Slf4j
public class HotelAuditRuleFactory {
    
    @Resource
    private PreHandleAuditRule preHandleAuditRule;
    
    private final List<IHotelAuditRule> list = new ArrayList<>();
    
    @BusinessBehaviorMonitor
    public List<HotelAuditResult> audit(HotelAuditItem hotelAuditItem) {
        if (hotelAuditItem == null) {
            return null;
        }
        
        // 基础信息填充
        hotelAuditItem.setAuditTime(DateUtil.now());
        hotelAuditItem.setRequestId(UUID.randomUUID().toString());
        TraceContext.setRequestId(hotelAuditItem.getRequestId());
        
        // 前置校验
        HotelAuditResult hotelAuditResult = preHandleAuditRule.audit(hotelAuditItem);
        Boolean auditPass = Optional.ofNullable(hotelAuditResult)
                .map(HotelAuditResult::getAuditPass)
                .orElse(null);
        // 前置校验不通过
        if (Boolean.FALSE.equals(auditPass)) {
            return Collections.singletonList(hotelAuditResult);
        }
        
        // 差旅模式：因公因私订单
        TravelModeEnum travelModeEnum = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getOrderInfo)
                .map(OrderInfo::getTravelModeEnum)
                .orElse(null);
        
        // 校验所有规则
        List<HotelAuditResult> hotelAuditResultList = new ArrayList<>();
        for (IHotelAuditRule hotelAuditRule : list) {
            if (hotelAuditRule == null) {
                continue;
            }
            
            // 规则差旅模式限制
            List<TravelModeEnum> supportTravelModeEnumList = hotelAuditRule.getSupportTravelModeEnumList();
            
            // 差旅模式是否符合
            if (supportTravelModeEnumList != null
                    && supportTravelModeEnumList.stream().noneMatch(item -> Objects.equals(item, travelModeEnum))) {
                continue;
            }
            
            try {
                HotelAuditResult result = hotelAuditRule.audit(hotelAuditItem);
                hotelAuditResultList.add(result);
            } catch (Exception exception) {
                log.error(StrUtil.format("审计规则异常 hotelAuditRuleEnum={}", hotelAuditRule.getHotelAuditRuleEnum()), exception);
            }
        }
        return hotelAuditResultList;
    }
    
    @Autowired
    public void setList(List<IHotelAuditRule> hotelAuditRuleList) {
        if (CollectionUtils.isEmpty(hotelAuditRuleList)) {
            return;
        }
        
        for (IHotelAuditRule hotelAuditRule : hotelAuditRuleList) {
            if (hotelAuditRule == null || hotelAuditRule.getHotelAuditRuleEnum() == null) {
                continue;
            }
            list.add(hotelAuditRule);
        }
    }

}
