package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "hotList",
    "countryList"
})
public class TravelPositionCountryData implements Serializable {
    private static final long serialVersionUID = 1L;





    public TravelPositionCountryData(
        List<TravelPositionCountryInfo> hotList,
        List<TravelPositionCountryInfo> countryList) {
        this.hotList = hotList;
        this.countryList = countryList;
    }

    public TravelPositionCountryData() {
    }

    @JsonProperty("hotList")
    public List<TravelPositionCountryInfo> hotList;

    @JsonProperty("countryList")
    public List<TravelPositionCountryInfo> countryList;

    public List<TravelPositionCountryInfo> getHotList() {
        return hotList;
    }

    public void setHotList(final List<TravelPositionCountryInfo> hotList) {
        this.hotList = hotList;
    }
    public List<TravelPositionCountryInfo> getCountryList() {
        return countryList;
    }

    public void setCountryList(final List<TravelPositionCountryInfo> countryList) {
        this.countryList = countryList;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TravelPositionCountryData other = (TravelPositionCountryData)obj;
        return
            Objects.equal(this.hotList, other.hotList) &&
            Objects.equal(this.countryList, other.countryList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.hotList == null ? 0 : this.hotList.hashCode());
        result = 31 * result + (this.countryList == null ? 0 : this.countryList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("hotList", hotList)
            .add("countryList", countryList)
            .toString();
    }
}
