package com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity;

import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.HotelAuditRuleEnum;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-23 16:12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HotelAuditResult {
    
    // 酒店审计枚举
    private HotelAuditRuleEnum hotelAuditRuleEnum;
    
    // 审计描述
    private List<String> auditDescList;
    
    // 审计通过
    private Boolean auditPass;
    
    // 审计异常标签
    private String errorLabel;
    
    public static HotelAuditResult buildHotelAuditResult(HotelAuditRuleEnum hotelAuditRuleEnum, List<String> errorDescList) {
        if (CollectionUtils.isEmpty(errorDescList)) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(hotelAuditRuleEnum)
                    .auditPass(true)
                    .build();
        }
        
        return HotelAuditResult.builder()
                .hotelAuditRuleEnum(hotelAuditRuleEnum)
                .auditDescList(errorDescList)
                .auditPass(false)
                .errorLabel(Optional.ofNullable(hotelAuditRuleEnum)
                        .map(HotelAuditRuleEnum::getErrorLabel)
                        .orElse(null))
                .build();
    }
    
}
