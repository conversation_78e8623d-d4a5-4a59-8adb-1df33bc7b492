package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;

import java.util.ArrayList;
import java.util.List;

/*
 * <AUTHOR>
 *
 * @date 2021/12/24 15:31
 *
 * @Desc部门分析指标UID维度
 */
public enum DeptUidStatisticalsEnum {

    /***************************************** 公共 *********************************************/
    // 整体消费金额
    TOTAL_AMOUNT("overview", "Overview.TotalAmount", 2, false, ""),
    // 整体消费金额占比
    AMOUNT_PERCENT("overview", "Overview.TotalAmountPer", 2, true, ""),
    // 全产线订单数
    TOTAL_ORDER_COUNT("overview", "Index.ordernumberAll", 2, true, "1,2,3,4,5"),
    // 总节省金额
    TOTAL_SAVE_AMOUNT("overview", "Save.AmtTotal", 2, true, "1,2,3,4,5"),
    // 总潜在节省金额
    TOTAL_SAVE_POTENTIAL("overview", "Save.AmtPot", 2, true, "1,2,3,4,5"),
    // top目的地destination
    TOP_DESTINATION("overview", "dept.topDestination", 2, true, "1,2,3,4,5"),

    /***************************************** 机票 *********************************************/
    // 消费金额
    FLT_AMOUNT("flight", "Index.costmoney", 0, false, "1,2,3,4,5"),
    // 消费金额占比
    FLT_AMOUNT_PERCENT("flight", "Index.costper", 2, true, "1,2,3,4,5"),
    // 订单数
    FLT_ORDER_COUNT("flight", "Index.ordernumber", 0, false, "1,2,3,4,5"),
    // 节省金额
    FLT_SAVE_AMOUNT("flight", "Exceltopname.savemoney", 0, false, "1,2,3,4,5"),
    // dept.saveAmountPotential
    FLT_SAVE_POTENTIAL("flight", "dept.saveAmountPotential", 0, false, "1,2,4,5"),
    // Top航线
    FLT_TOP_FLIGHT_CITY("flight", "dept.topFlightCity", 2, true, "1,2,4,5"),
    // 提前预订天数
    FLT_AVG_PRE_ORDER_DATE("flight", "Travelanalysis.advancedbooking", 2, false, "1,2,3,4,5"),
    // 平均折扣
    FLT_AVG_DISCOUNT("flight", "Index.avgdiscount", 2, false, "1,2,4,5"),
    // 里程均价
    FLT_AVG_MILE_PRICE("flight", "Exceltopname.avgmilprice", 2, false, "1,2,3,4,5"),
    // 机票均价
    FLT_AVG_PRICE("flight", "Overview.FlightAvgTktPrice", 2, false, "1,2,4,5"),
    // 碳排放量 - 已删除，不再显示碳排放相关字段
    // FLT_CARBONS("flight", "Exceltopname.carbonEmission", 2, true, "1,2,4,5"),
    // 里程碳排 - 已删除，不再显示碳排放相关字段
    // FLT_AVG_MILE_CARBONS("flight", "index.milecarbon", 0, false, "1,2,3,4,5"),
    // RC订单数
    FLT_RC_TIMES("flight", "RCAnalysis.OverBudget", 0, false, "1,2,3,4,5"),
    // RC占比
    FLT_RC_PERCENT("flight", "Exceltopname.RCPercentage", 0, false, "1,2,3,4,5"),
    // 退票率
    FLT_REFUND_RATE("flight", "Exceltopname.refundrate", 0, false, "1,2,3,4,5"),
    // 改签率
    FLT_REBOOK_RATE("flight", "Exceltopname.changerate", 0, false, "1,2,3,4,5"),

    /***************************************** 酒店 *********************************************/
    // 消费金额
    HTL_AMOUNT("hotel", "Index.costmoney", 2, false, ""),
    // 消费金额占比
    HTL_AMOUNT_PERCENT("hotel", "Index.costper", 2, true, ""),
    // 订单数
    HTL_ORDER_COUNT("hotel", "Index.ordernumber", 0, false, ""),
    // 间夜数
    HTL_QUANTITY("hotel", "Index.nightnum", 2, true, ""),
    // 节省金额
    HTL_SAVE_AMOUNT("hotel", "Exceltopname.savemoney", 2, false, ""),
    //dept.saveAmountPotential
    HTL_SAVE_POTENTIAL("hotel", "dept.saveAmountPotential", 2, false, ""),
    // Top酒店城市
    HTL_TOP_CITY("hotel", "dept.topHotelCity", 2, false, ""),
    // 最常入住星级
    HTL_TOP_STAR("hotel", "dept.topHotelStar", 2, true, ""),
    // 差标使用率
    HTL_USAGE("hotel", "DeadPriceAnalysis.DeadPriceUsage", 2, false, ""),
    // 间夜均价
    HTL_AVG_PRICE("hotel", "Overview.HotelAvgNightPrice", 2, true, ""),
    //酒店房价
    HTL_ROOM_PRICE("hotel", "HotelFeeDetail.room_price", 2, true, ""),
    //RC订单数
    HTL_RC_TIMES("hotel", "RCAnalysis.OverBudget", 2, true, ""),
    //RC占比
    HTL_RC_PERCENT("hotel", "Exceltopname.RCPercentage", 2, true, ""),
    // 取消率
    HTL_CANCEL_RATE("hotel", "Exceltopname.refundrate", 2, true, ""),

    /***************************************** 火车 *********************************************/
    // 消费金额
    TRAIN_AMOUNT("train", "Index.costmoney", 0, false, ""),
    // 消费金额占比
    TRAIN_AMOUNT_PERCENT("train", "Index.costper", 2, true, ""),
    // 订单数
    TRAIN_ORDER_COUNT("train", "Index.ordernumber", 2, false, ""),
    // 张数
    TRAIN_QUANTITY("train", "Index.number", 2, true, ""),
    // Top线路
    TRAIN_TOP_LINE("train", "dept.topTrainLine", 2, true, ""),
    // 最常乘坐坐席
    TRAIN_TOP_SEAT("train", "dept.topTrainSeat", 0, false, ""),
    // 碳排放量 - 已删除，不再显示碳排放相关字段
    // TRAIN_CARBONS("train", "Exceltopname.carbonEmission", 0, false, ""),
    // 退票率
    TRAIN_REFUND_RATE("train", "Exceltopname.refundrate", 0, false, ""),
    // 改签率
    TRAIN_REBOOK_RATE("train", "Exceltopname.changerate", 0, false, ""),
    /***************************************** 用车 *********************************************/
    // 消费金额
    CAR_AMOUNT("car", "Index.costmoney", 0, false, ""),
    // 消费金额占比
    CAR_AMOUNT_PERCENT("car", "Index.costper", 0, false, ""),
    // 订单数
    CAR_ORDER_COUNT("car", "Index.ordernumber", 2, true, ""),
    // 里程均价 - 已删除，不再显示里程均价字段
    CAR_AVG_MILE_PRICE("car", "Exceltopname.avgmilprice", 2, false, ""),
    // 碳排放量 - 已删除，不再显示碳排放相关字段
    // CAR_CARBONS("car", "Exceltopname.carbonEmission", 2, false, ""),

    // 里程碳排 - 已删除，不再显示碳排放相关字段
    // CAR_AVG_MILE_CARBONS("car", "index.emissionper", 2, false, ""),



    /***************************************** 汽车票*********************************************/
    // 消费金额
    BUS_AMOUNT("bus","Index.costmoney",0,false,""),

    // 消费金额占比
    BUS_AMOUNT_PERCENT("bus","Index.costper",2,true,""),
    // 订单数
    BUS_ORDER_COUNT("bus","Index.ordernumber",2,true,""),

    /***************************************** 增值*********************************************/
    // 消费金额
    VASO_AMOUNT("vaso","Index.costmoney",0,false,""),
    // 消费金额占比
    VASO_AMOUNT_PERCENT("vaso","Index.costper",2,true,""),
    // 订单数
    VASO_ORDER_COUNT("vaso", "Index.ordernumber", 2, true, ""),

    /***************************************** 心程贝 *********************************************/
    // 心程贝被发放金额
    WELFARE_ALLOT_AMOUNT("welfare", "Dept.Uid.Welfare.AllotAmount", 2, true, ""),
    // 心程贝被发放金额占比
    WELFARE_ALLOT_AMOUNT_PERCENT("welfare", "Dept.Uid.Welfare.AllotAmountPercent", 2, true, ""),
    // 心程贝被回收金额
    WELFARE_RECYCLE_AMOUNT("welfare", "Dept.Uid.Welfare.RecycleAmount", 2, true, ""),
    // 心程贝被退还金额
    WELFARE_RETURN_AMOUNT("welfare", "Dept.Uid.Welfare.ReturnAmount", 2, true, ""),
    // 心程贝被扣减金额
    WELFARE_DEDUCT_AMOUNT("welfare", "Dept.Uid.Welfare.DeductAmount", 2, true, ""),
    // 心程贝余额
    WELFARE_BALANCE("welfare", "Overview.XCB.Balance", 2, true, ""),
    ;



    private String bizType;

    private String sharkKey;

    private int num;

    private boolean isPercent;

    private String subQueryBu;

    public String getBizType() {
        return bizType;
    }

    public String getSharkKey() {
        return sharkKey;
    }

    public int getNum() {
        return num;
    }

    public boolean isPercent() {
        return isPercent;
    }

    public String getSubQueryBu() {
        return subQueryBu;
    }

    DeptUidStatisticalsEnum(String s, String r, int a, boolean flag, String subQueryBu) {
        this.bizType = s;
        this.sharkKey = r;
        this.num = a;
        this.isPercent = flag;
        this.subQueryBu = subQueryBu;
    }

    public static List getUidStatisticals(String bizType){
        DeptUidStatisticalsEnum[] values = DeptUidStatisticalsEnum.values();
        List<DeptUidStatisticalsEnum> list = new ArrayList<>();
        for (DeptUidStatisticalsEnum value : values) {
            if (value.getBizType().equals(bizType)) {
                list.add(value);
            }
        }
        // Log: Removed carbon emission related fields from employee detail analysis:
        // - Flight: FLT_CARBONS, FLT_AVG_MILE_CARBONS
        // - Train: TRAIN_CARBONS
        // - Car: CAR_CARBONS, CAR_AVG_MILE_CARBONS, CAR_AVG_MILE_PRICE
        return list;

    }
}
