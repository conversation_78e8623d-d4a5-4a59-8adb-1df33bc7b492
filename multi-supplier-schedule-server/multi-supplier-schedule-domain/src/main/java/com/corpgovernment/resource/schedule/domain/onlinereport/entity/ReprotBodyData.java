package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dimension",
    "detailData"
})
public class ReprotBodyData implements Serializable {
    private static final long serialVersionUID = 1L;





    public ReprotBodyData(
        String dimension,
        ReportDataDetailData detailData) {
        this.dimension = dimension;
        this.detailData = detailData;
    }

    public ReprotBodyData() {
    }

    /**
     * 列维度 含-总计/小计
     */
    @JsonProperty("dimension")
    public String dimension;

    /**
     * 列其它数据
     */
    @JsonProperty("detailData")
    public ReportDataDetailData detailData;

    /**
     * 列维度 含-总计/小计
     */
    public String getDimension() {
        return dimension;
    }

    /**
     * 列维度 含-总计/小计
     */
    public void setDimension(final String dimension) {
        this.dimension = dimension;
    }

    /**
     * 列其它数据
     */
    public ReportDataDetailData getDetailData() {
        return detailData;
    }

    /**
     * 列其它数据
     */
    public void setDetailData(final ReportDataDetailData detailData) {
        this.detailData = detailData;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final ReprotBodyData other = (ReprotBodyData)obj;
        return
            Objects.equal(this.dimension, other.dimension) &&
            Objects.equal(this.detailData, other.detailData);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dimension == null ? 0 : this.dimension.hashCode());
        result = 31 * result + (this.detailData == null ? 0 : this.detailData.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dimension", dimension)
            .add("detailData", detailData)
            .toString();
    }
}
