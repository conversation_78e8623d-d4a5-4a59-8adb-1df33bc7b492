package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2022/1/5 10:16
 * @description：
 * @modified By：
 * @version: $
 */
@Entity
//@Database(name = "CorpReportDB")
@Table(name = "report_email_sendtask")
public class CorpReportEmailSendTaskPO {

    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 用户卡号
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    /**
     * 公司id
     */
    @Column(name = "corpId")
    @Type(value = Types.VARCHAR)
    private String corpId;

    /**
     * 公司类型
     */
    @Column(name = "corpType")
    @Type(value = Types.VARCHAR)
    private String corpType;

    /**
     * 邮箱
     */
    @Column(name = "email")
    @Type(value = Types.VARCHAR)
    private String email;

    /**
     * 报表类型
     */
    @Column(name = "reportType")
    @Type(value = Types.VARCHAR)
    private String reportType;

    /**
     * 首日
     */
    @Column(name = "firstDay")
    @Type(value = Types.VARCHAR)
    private String firstDay;

    /**
     * 结束日
     */
    @Column(name = "endDay")
    @Type(value = Types.VARCHAR)
    private String endDay;

    /**
     * 是否有消费
     */
    @Column(name = "hasConsume")
    @Type(value = Types.TINYINT)
    private int hasConsume;

    /**
     * 是否已发送 0-未发送 1-已发送 2-发送失败
     */
    @Column(name = "hasSend")
    @Type(value = Types.TINYINT)
    private int hasSend;

    /**
     * 记录创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 最后修改时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 公司名称
     */
    @Column(name = "corpName")
    @Type(value = Types.VARCHAR)
    private String corpName;

    /**
     * 卡号角色
     */
    @Column(name = "userType")
    @Type(value = Types.INTEGER)
    private int userType;

    /**
     * 唯一标识
     */
    @Column(name = "emailId")
    @Type(value = Types.VARCHAR)
    private String emailId;

    /**
     * 异常订单显示
     */
    @Column(name = "showRiskOrder")
    @Type(value = Types.TINYINT)
    private int  showRiskOrder;

    /**
     * 邮件语种
     */
    @Column(name = "local")
    @Type(value = Types.VARCHAR)
    private String local;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getCorpType() {
        return corpType;
    }

    public void setCorpType(String corpType) {
        this.corpType = corpType;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public String getFirstDay() {
        return firstDay;
    }

    public void setFirstDay(String firstDay) {
        this.firstDay = firstDay;
    }

    public String getEndDay() {
        return endDay;
    }

    public void setEndDay(String endDay) {
        this.endDay = endDay;
    }

    public int getHasConsume() {
        return hasConsume;
    }

    public void setHasConsume(int hasConsume) {
        this.hasConsume = hasConsume;
    }

    public int getHasSend() {
        return hasSend;
    }

    public void setHasSend(int hasSend) {
        this.hasSend = hasSend;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getCorpName() {
        return corpName;
    }

    public void setCorpName(String corpName) {
        this.corpName = corpName;
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public int getShowRiskOrder() {
        return showRiskOrder;
    }

    public void setShowRiskOrder(int showRiskOrder) {
        this.showRiskOrder = showRiskOrder;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    @Override
    public String toString() {
        return "CorpReportEmailSendTaskPO{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", corpId='" + corpId + '\'' +
                ", corpType='" + corpType + '\'' +
                ", email='" + email + '\'' +
                ", reportType='" + reportType + '\'' +
                ", firstDay='" + firstDay + '\'' +
                ", endDay='" + endDay + '\'' +
                ", hasConsume=" + hasConsume +
                ", hasSend=" + hasSend +
                ", datachangeCreatetime=" + datachangeCreatetime +
                ", datachangeLasttime=" + datachangeLasttime +
                ", corpName='" + corpName + '\'' +
                ", userType=" + userType +
                ", emailId='" + emailId + '\'' +
                ", showRiskOrder=" + showRiskOrder + '\'' +
                ", local=" + local +
                '}';
    }
}
