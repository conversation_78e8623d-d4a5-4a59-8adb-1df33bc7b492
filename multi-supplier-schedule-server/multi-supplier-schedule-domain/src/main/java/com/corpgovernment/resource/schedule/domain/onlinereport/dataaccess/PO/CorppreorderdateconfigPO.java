package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.sql.Types;
/**
 * @Description: Corppreorderdateconfig po
 * <AUTHOR>
 * @Date 2019/4/11
 */
@Entity
//@Database(name = "CorpReportDB")
@Table(name = "corppreorderdateconfig")
public class CorppreorderdateconfigPO {



    /**
     * uid
     */
    @Column(name = "UID")
    @Type(value = Types.VARCHAR)
    private String uID;

    /**
     * 开始
     */
    @Column(name = "start")
    @Type(value = Types.INTEGER)
    private Integer start;

    /**
     * 结束
     */
    @Column(name = "end")
    @Type(value = Types.INTEGER)
    private Integer end;



    public String getUID() {
        return uID;
    }

    public void setUID(String uID) {
        this.uID = uID;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getEnd() {
        return end;
    }

    public void setEnd(Integer end) {
        this.end = end;
    }


}
