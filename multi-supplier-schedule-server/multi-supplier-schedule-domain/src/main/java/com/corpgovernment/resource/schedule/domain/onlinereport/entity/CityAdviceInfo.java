package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "cityTypeName",
    "cityTypeId",
    "cityName",
    "rankGroup",
    "rankIdGroup",
    "avgPrice",
    "travelStandard",
    "travelStandardCNY",
    "price",
    "currency",
    "roomNights",
    "marketPrice",
    "circlePrice",
    "adviceCode",
    "uuid",
    "advicePrice",
    "advicePriceCNY",
    "exchangeRate"
})
public class CityAdviceInfo implements Serializable {
    private static final long serialVersionUID = 1L;



    

    public CityAdviceInfo(
        String cityTypeName,
        Integer cityTypeId,
        String cityName,
        String rankGroup,
        String rankIdGroup,
        Double avgPrice,
        Double travelStandard,
        Double travelStandardCNY,
        Double price,
        String currency,
        Integer roomNights,
        Double marketPrice,
        Double circlePrice,
        String adviceCode,
        String uuid,
        Double advicePrice,
        Double advicePriceCNY,
        Double exchangeRate) {
        this.cityTypeName = cityTypeName;
        this.cityTypeId = cityTypeId;
        this.cityName = cityName;
        this.rankGroup = rankGroup;
        this.rankIdGroup = rankIdGroup;
        this.avgPrice = avgPrice;
        this.travelStandard = travelStandard;
        this.travelStandardCNY = travelStandardCNY;
        this.price = price;
        this.currency = currency;
        this.roomNights = roomNights;
        this.marketPrice = marketPrice;
        this.circlePrice = circlePrice;
        this.adviceCode = adviceCode;
        this.uuid = uuid;
        this.advicePrice = advicePrice;
        this.advicePriceCNY = advicePriceCNY;
        this.exchangeRate = exchangeRate;
    }

    public CityAdviceInfo() {
    }

    /**
     * 城市类别名称
     */
    @JsonProperty("cityTypeName")
    public String cityTypeName;

    /**
     * 区域id
     */
    @JsonProperty("cityTypeId")
    public Integer cityTypeId;

    /**
     * 城市名称
     */
    @JsonProperty("cityName")
    public String cityName;

    /**
     * 职级组
     */
    @JsonProperty("rankGroup")
    public String rankGroup;

    /**
     * 职级组ID
     */
    @JsonProperty("rankIdGroup")
    public String rankIdGroup;

    /**
     * 近180天预订间夜均价
     */
    @JsonProperty("avgPrice")
    public Double avgPrice;

    /**
     * 原差标
     */
    @JsonProperty("travelStandard")
    public Double travelStandard;

    /**
     * 人民币差标
     */
    @JsonProperty("travelStandardCNY")
    public Double travelStandardCNY;

    /**
     * 当前差标
     */
    @JsonProperty("price")
    public Double price;

    /**
     * 差标币种
     */
    @JsonProperty("currency")
    public String currency;

    /**
     * 近180天预订间夜数
     */
    @JsonProperty("roomNights")
    public Integer roomNights;

    /**
     * 市场价
     */
    @JsonProperty("marketPrice")
    public Double marketPrice;

    /**
     * 常订酒店圈酒店价格
     */
    @JsonProperty("circlePrice")
    public Double circlePrice;

    /**
     * 预订建议
     */
    @JsonProperty("adviceCode")
    public String adviceCode;

    /**
     * 趋势图uuid
     */
    @JsonProperty("uuid")
    public String uuid;

    /**
     * 建议差标同原差标货币
     */
    @JsonProperty("advicePrice")
    public Double advicePrice;

    /**
     * 建议差标-CNY
     */
    @JsonProperty("advicePriceCNY")
    public Double advicePriceCNY;

    /**
     * 当日汇率
     */
    @JsonProperty("exchangeRate")
    public Double exchangeRate;

    /**
     * 城市类别名称
     */
    public String getCityTypeName() {
        return cityTypeName;
    }

    /**
     * 城市类别名称
     */
    public void setCityTypeName(final String cityTypeName) {
        this.cityTypeName = cityTypeName;
    }

    /**
     * 区域id
     */
    public Integer getCityTypeId() {
        return cityTypeId;
    }

    /**
     * 区域id
     */
    public void setCityTypeId(final Integer cityTypeId) {
        this.cityTypeId = cityTypeId;
    }

    /**
     * 城市名称
     */
    public String getCityName() {
        return cityName;
    }

    /**
     * 城市名称
     */
    public void setCityName(final String cityName) {
        this.cityName = cityName;
    }

    /**
     * 职级组
     */
    public String getRankGroup() {
        return rankGroup;
    }

    /**
     * 职级组
     */
    public void setRankGroup(final String rankGroup) {
        this.rankGroup = rankGroup;
    }

    /**
     * 职级组ID
     */
    public String getRankIdGroup() {
        return rankIdGroup;
    }

    /**
     * 职级组ID
     */
    public void setRankIdGroup(final String rankIdGroup) {
        this.rankIdGroup = rankIdGroup;
    }

    /**
     * 近180天预订间夜均价
     */
    public Double getAvgPrice() {
        return avgPrice;
    }

    /**
     * 近180天预订间夜均价
     */
    public void setAvgPrice(final Double avgPrice) {
        this.avgPrice = avgPrice;
    }

    /**
     * 原差标
     */
    public Double getTravelStandard() {
        return travelStandard;
    }

    /**
     * 原差标
     */
    public void setTravelStandard(final Double travelStandard) {
        this.travelStandard = travelStandard;
    }

    /**
     * 人民币差标
     */
    public Double getTravelStandardCNY() {
        return travelStandardCNY;
    }

    /**
     * 人民币差标
     */
    public void setTravelStandardCNY(final Double travelStandardCNY) {
        this.travelStandardCNY = travelStandardCNY;
    }

    /**
     * 当前差标
     */
    public Double getPrice() {
        return price;
    }

    /**
     * 当前差标
     */
    public void setPrice(final Double price) {
        this.price = price;
    }

    /**
     * 差标币种
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 差标币种
     */
    public void setCurrency(final String currency) {
        this.currency = currency;
    }

    /**
     * 近180天预订间夜数
     */
    public Integer getRoomNights() {
        return roomNights;
    }

    /**
     * 近180天预订间夜数
     */
    public void setRoomNights(final Integer roomNights) {
        this.roomNights = roomNights;
    }

    /**
     * 市场价
     */
    public Double getMarketPrice() {
        return marketPrice;
    }

    /**
     * 市场价
     */
    public void setMarketPrice(final Double marketPrice) {
        this.marketPrice = marketPrice;
    }

    /**
     * 常订酒店圈酒店价格
     */
    public Double getCirclePrice() {
        return circlePrice;
    }

    /**
     * 常订酒店圈酒店价格
     */
    public void setCirclePrice(final Double circlePrice) {
        this.circlePrice = circlePrice;
    }

    /**
     * 预订建议
     */
    public String getAdviceCode() {
        return adviceCode;
    }

    /**
     * 预订建议
     */
    public void setAdviceCode(final String adviceCode) {
        this.adviceCode = adviceCode;
    }

    /**
     * 趋势图uuid
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * 趋势图uuid
     */
    public void setUuid(final String uuid) {
        this.uuid = uuid;
    }

    /**
     * 建议差标同原差标货币
     */
    public Double getAdvicePrice() {
        return advicePrice;
    }

    /**
     * 建议差标同原差标货币
     */
    public void setAdvicePrice(final Double advicePrice) {
        this.advicePrice = advicePrice;
    }

    /**
     * 建议差标-CNY
     */
    public Double getAdvicePriceCNY() {
        return advicePriceCNY;
    }

    /**
     * 建议差标-CNY
     */
    public void setAdvicePriceCNY(final Double advicePriceCNY) {
        this.advicePriceCNY = advicePriceCNY;
    }

    /**
     * 当日汇率
     */
    public Double getExchangeRate() {
        return exchangeRate;
    }

    /**
     * 当日汇率
     */
    public void setExchangeRate(final Double exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final CityAdviceInfo other = (CityAdviceInfo)obj;
        return
            Objects.equal(this.cityTypeName, other.cityTypeName) &&
            Objects.equal(this.cityTypeId, other.cityTypeId) &&
            Objects.equal(this.cityName, other.cityName) &&
            Objects.equal(this.rankGroup, other.rankGroup) &&
            Objects.equal(this.rankIdGroup, other.rankIdGroup) &&
            Objects.equal(this.avgPrice, other.avgPrice) &&
            Objects.equal(this.travelStandard, other.travelStandard) &&
            Objects.equal(this.travelStandardCNY, other.travelStandardCNY) &&
            Objects.equal(this.price, other.price) &&
            Objects.equal(this.currency, other.currency) &&
            Objects.equal(this.roomNights, other.roomNights) &&
            Objects.equal(this.marketPrice, other.marketPrice) &&
            Objects.equal(this.circlePrice, other.circlePrice) &&
            Objects.equal(this.adviceCode, other.adviceCode) &&
            Objects.equal(this.uuid, other.uuid) &&
            Objects.equal(this.advicePrice, other.advicePrice) &&
            Objects.equal(this.advicePriceCNY, other.advicePriceCNY) &&
            Objects.equal(this.exchangeRate, other.exchangeRate);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.cityTypeName == null ? 0 : this.cityTypeName.hashCode());
        result = 31 * result + (this.cityTypeId == null ? 0 : this.cityTypeId.hashCode());
        result = 31 * result + (this.cityName == null ? 0 : this.cityName.hashCode());
        result = 31 * result + (this.rankGroup == null ? 0 : this.rankGroup.hashCode());
        result = 31 * result + (this.rankIdGroup == null ? 0 : this.rankIdGroup.hashCode());
        result = 31 * result + (this.avgPrice == null ? 0 : this.avgPrice.hashCode());
        result = 31 * result + (this.travelStandard == null ? 0 : this.travelStandard.hashCode());
        result = 31 * result + (this.travelStandardCNY == null ? 0 : this.travelStandardCNY.hashCode());
        result = 31 * result + (this.price == null ? 0 : this.price.hashCode());
        result = 31 * result + (this.currency == null ? 0 : this.currency.hashCode());
        result = 31 * result + (this.roomNights == null ? 0 : this.roomNights.hashCode());
        result = 31 * result + (this.marketPrice == null ? 0 : this.marketPrice.hashCode());
        result = 31 * result + (this.circlePrice == null ? 0 : this.circlePrice.hashCode());
        result = 31 * result + (this.adviceCode == null ? 0 : this.adviceCode.hashCode());
        result = 31 * result + (this.uuid == null ? 0 : this.uuid.hashCode());
        result = 31 * result + (this.advicePrice == null ? 0 : this.advicePrice.hashCode());
        result = 31 * result + (this.advicePriceCNY == null ? 0 : this.advicePriceCNY.hashCode());
        result = 31 * result + (this.exchangeRate == null ? 0 : this.exchangeRate.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("cityTypeName", cityTypeName)
            .add("cityTypeId", cityTypeId)
            .add("cityName", cityName)
            .add("rankGroup", rankGroup)
            .add("rankIdGroup", rankIdGroup)
            .add("avgPrice", avgPrice)
            .add("travelStandard", travelStandard)
            .add("travelStandardCNY", travelStandardCNY)
            .add("price", price)
            .add("currency", currency)
            .add("roomNights", roomNights)
            .add("marketPrice", marketPrice)
            .add("circlePrice", circlePrice)
            .add("adviceCode", adviceCode)
            .add("uuid", uuid)
            .add("advicePrice", advicePrice)
            .add("advicePriceCNY", advicePriceCNY)
            .add("exchangeRate", exchangeRate)
            .toString();
    }
}
