package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;

/**
 * **在线报告部门分析前5部门消费****************************
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "eId",
        "basecondition",
        "analysisObjectEnum",
        "page",
        "queryBu"
})
public class OnlineReportTopFiveDeptConsumeRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonProperty("eId")
    public String eId;
    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;
    /**
     * 分析对象
     */
    @JsonProperty("analysisObjectEnum")
    public AnalysisObjectEnum analysisObjectEnum;
    /**
     * page翻页
     */
    @JsonProperty("page")
    public Pager page;
    /**
     * 查询产线
     */
    @JsonProperty("queryBu")
    public QueryReportBuTypeEnum queryBu;
    @JsonProperty("analysisObjectVal")
    public String analysisObjectVal;

    public OnlineReportTopFiveDeptConsumeRequest(
            String eId,
            BaseQueryCondition basecondition,
            AnalysisObjectEnum analysisObjectEnum,
            Pager page,
            QueryReportBuTypeEnum queryBu,
            String analysisObjectVal) {
        this.eId = eId;
        this.basecondition = basecondition;
        this.analysisObjectEnum = analysisObjectEnum;
        this.page = page;
        this.queryBu = queryBu;
        this.analysisObjectVal = analysisObjectVal;
    }

    public OnlineReportTopFiveDeptConsumeRequest() {
    }

    public String getEId() {
        return eId;
    }

    public void setEId(final String eId) {
        this.eId = eId;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * 分析对象
     */
    public AnalysisObjectEnum getAnalysisObjectEnum() {
        return analysisObjectEnum;
    }

    /**
     * 分析对象
     */
    public void setAnalysisObjectEnum(final AnalysisObjectEnum analysisObjectEnum) {
        this.analysisObjectEnum = analysisObjectEnum;
    }

    /**
     * page翻页
     */
    public Pager getPage() {
        return page;
    }

    /**
     * page翻页
     */
    public void setPage(final Pager page) {
        this.page = page;
    }

    /**
     * 查询产线
     */
    public QueryReportBuTypeEnum getQueryBu() {
        return queryBu;
    }

    /**
     * 查询产线
     */
    public void setQueryBu(final QueryReportBuTypeEnum queryBu) {
        this.queryBu = queryBu;
    }

    public String getAnalysisObjectVal() {
        return analysisObjectVal;
    }

    public void setAnalysisObjectVal(final String analysisObjectVal) {
        this.analysisObjectVal = analysisObjectVal;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.eId;
            case 1:
                return this.basecondition;
            case 2:
                return this.analysisObjectEnum;
            case 3:
                return this.page;
            case 4:
                return this.queryBu;
            case 5:
                return this.analysisObjectVal;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.eId = (String) fieldValue;
                break;
            case 1:
                this.basecondition = (BaseQueryCondition) fieldValue;
                break;
            case 2:
                this.analysisObjectEnum = (AnalysisObjectEnum) fieldValue;
                break;
            case 3:
                this.page = (Pager) fieldValue;
                break;
            case 4:
                this.queryBu = (QueryReportBuTypeEnum) fieldValue;
                break;
            case 5:
                this.analysisObjectVal = (String) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTopFiveDeptConsumeRequest other = (OnlineReportTopFiveDeptConsumeRequest) obj;
        return
                Objects.equal(this.eId, other.eId) &&
                        Objects.equal(this.basecondition, other.basecondition) &&
                        Objects.equal(this.analysisObjectEnum, other.analysisObjectEnum) &&
                        Objects.equal(this.page, other.page) &&
                        Objects.equal(this.queryBu, other.queryBu) &&
                        Objects.equal(this.analysisObjectVal, other.analysisObjectVal);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eId == null ? 0 : this.eId.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.analysisObjectEnum == null ? 0 : this.analysisObjectEnum.hashCode());
        result = 31 * result + (this.page == null ? 0 : this.page.hashCode());
        result = 31 * result + (this.queryBu == null ? 0 : this.queryBu.hashCode());
        result = 31 * result + (this.analysisObjectVal == null ? 0 : this.analysisObjectVal.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("eId", eId)
                .add("basecondition", basecondition)
                .add("analysisObjectEnum", analysisObjectEnum)
                .add("page", page)
                .add("queryBu", queryBu)
                .add("analysisObjectVal", analysisObjectVal)
                .toString();
    }
}
