package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;


/**
 * @Description: 机票部门消费
 * <AUTHOR>
 * @Date 2019/5/31
 */
public class FlightDeptConsumPtionReportBO implements Comparable<FlightDeptConsumPtionReportBO> {

    public String deptName;
    public String amount;
    public Integer quantity;
    public Integer fullQuantity;
    public String avgPrice;
    public String avgDiscount;
    public String fullQuantityPerent;
    public String save;
    public String saveRate;
    public String loss;
    public String lossRate;

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getFullQuantity() {
        return fullQuantity;
    }

    public void setFullQuantity(Integer fullQuantity) {
        this.fullQuantity = fullQuantity;
    }

    public String getAvgPrice() {
        return avgPrice;
    }

    public void setAvgPrice(String avgPrice) {
        this.avgPrice = avgPrice;
    }

    public String getAvgDiscount() {
        return avgDiscount;
    }

    public void setAvgDiscount(String avgDiscount) {
        this.avgDiscount = avgDiscount;
    }

    public String getFullQuantityPerent() {
        return fullQuantityPerent;
    }

    public void setFullQuantityPerent(String fullQuantityPerent) {
        this.fullQuantityPerent = fullQuantityPerent;
    }

    public String getSave() {
        return save;
    }

    public void setSave(String save) {
        this.save = save;
    }

    public String getSaveRate() {
        return saveRate;
    }

    public void setSaveRate(String saveRate) {
        this.saveRate = saveRate;
    }

    public String getLoss() {
        return loss;
    }

    public void setLoss(String loss) {
        this.loss = loss;
    }

    public String getLossRate() {
        return lossRate;
    }

    public void setLossRate(String lossRate) {
        this.lossRate = lossRate;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }


    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public int compareTo(FlightDeptConsumPtionReportBO o) {
        if (o.equals(this))
            return 0;
        return  (int)(Double.parseDouble(o.getAmount()) - Double.parseDouble(this.getAmount()));
    }


}
