package com.corpgovernment.resource.schedule.domain.onlinereport.constant;


/**
 * @Description: QConfig相关配置的key和default value
 * <AUTHOR>
 * @Date 2019/3/1
 */
public class QConfigKeyConst {
    /**
     * 自定义报表最大数量
     */
    public static final String MAXCOUNT_NEWCUSTOM_REPORT = "Max_NewCustom_Reoprt";

    /**
     * 是否开始分段查询
     */
    public static final String QUERY_MULTI_THREAD = "Query_Multi_Thread";

    /**
     * 按照间隔时间查询COI
     */
    public static final String QUERRY_SERVICE_INTERVAL_TIME = "Query_Service_Interval_Time";
    public static final String QUERRY_SERVICE_DEFAULT_INTERVAL_TIME = "30";
    /**
     * 部门、成本中心生效时间
     */
    public static final String COIREPORT_BASEDATA_DEPTANDCOSTCENTER_TIME = "CoiReport_Basedata_DeptAndCostCenter_Time";
    /**
     * Qconfig主账户名称redis存储时间的key
     */
    public static final String ACCOUNT_NAME_REDIS_LIMIT = "Account_Name_Redis_Limit";

    /**
     * 查询es部门筛选数量上限
     */
    public static final String ES_ORGANIZATION_QUERY_LIMIT = "ES_Organization_Query_Limit";

    /**
     * 城市搜索地址
     */
    public static final String SEARCHCITYPATH = "SearchCityPath";
    /**
     * 查询COI ES SOA  单批次orderId限制
     */
    public static final String COIES_SEARCH_ORDERLIMIT = "COIES_SEARCH_ORDERLIMIT";

    public static final String TRIGGER_WORK_DAY_KEY = "TRIGGER_WORK_DAY";

    /**
     * 月报
     */
    public static final String MONTHREPORT_TRIGGER_WORK_DAY_KEY = "MONTHREPORT_MTRIGGER_WORK_DAY";
    /**
     * 季报
     */
    public static final String QUARTERREPORT_TRIGGER_WORK_DAY_KEY = "QUARTERREPORT_TRIGGER_WORK_DAY";
    /**
     * 半年报
     */
    public static final String HALFREPORT_TRIGGER_WORK_DAY_KEY = "HALFREPORT_TRIGGER_WORK_DAY";
    /**
     * 年报
     */
    public static final String YEARREPORT_TRIGGER_WORK_DAY_KEY = "YEARREPORT_TRIGGER_WORK_DAY";

}
