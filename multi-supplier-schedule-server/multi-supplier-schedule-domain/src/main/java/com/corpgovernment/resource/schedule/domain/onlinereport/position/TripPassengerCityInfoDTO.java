package com.corpgovernment.resource.schedule.domain.onlinereport.position;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-08-18 11:27
 */
@Data
public class TripPassengerCityInfoDTO {
    // 城市id
    @Column(name = "sub_trip_city_id")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    // 城市名称
    @Column(name = "subTripCityName")
    @Type(value = Types.VARCHAR)
    private String cityName;

    // 人次
    @Column(name = "passenger_cnt")
    @Type(value = Types.INTEGER)
    private Integer passengerCount;

    // 行程
    @Column(name = "trip_cnt")
    @Type(value = Types.INTEGER)
    private Integer tripCount;
}
