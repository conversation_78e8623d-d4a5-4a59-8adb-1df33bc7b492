package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

/**
 * @Auther: ddzhan
 * @Date: 2019/5/14
 * @Description:
 */
public class FlightCarriersReportBO implements  Comparable<FlightCarriersReportBO> {

   private int fltQuantity;
   private double fltPrice;
   private String fltFlightclass;
   private String fltFlightclassId;
   private String fltAirline;//承运商


    public String getFltAirline() {
        return fltAirline;
    }

    public void setFltAirline(String fltAirline) {
        this.fltAirline = fltAirline;
    }

    public int getFltQuantity() {
        return fltQuantity;
    }

    public void setFltQuantity(int fltQuantity) {
        this.fltQuantity = fltQuantity;
    }

    public double getFltPrice() {
        return fltPrice;
    }

    public void setFltPrice(double fltPrice) {
        this.fltPrice = fltPrice;
    }

    public String getFltFlightclass() {
        return fltFlightclass;
    }

    public void setFltFlightclass(String fltFlightclass) {
        this.fltFlightclass = fltFlightclass;
    }



    public String getFltFlightclassId() {
        return fltFlightclassId;
    }

    public void setFltFlightclassId(String fltFlightclassId) {
        this.fltFlightclassId = fltFlightclassId;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }



    @Override
    public int hashCode() {
        return super.hashCode();
    }



    @Override
    public int compareTo(FlightCarriersReportBO o) {
        if(o.equals(this))
            return 0;
        return (int)((o.getFltPrice()) - (this.getFltPrice()));
    }

}
