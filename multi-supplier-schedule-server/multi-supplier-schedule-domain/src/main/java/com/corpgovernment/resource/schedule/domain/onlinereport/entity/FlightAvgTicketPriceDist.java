package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.List;

/**
 * 机票平均票价分布
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "title",
        "avgTicketPriceNotice",
        "legends",
        "data"
})
public class FlightAvgTicketPriceDist implements Serializable {
    private static final long serialVersionUID = 1L;


    public FlightAvgTicketPriceDist(
            String title,
            String avgTicketPriceNotice,
            List<OnlineReportTrendLegend> legends,
            List<OnlineReportTrendPoint> data) {
        this.title = title;
        this.avgTicketPriceNotice = avgTicketPriceNotice;
        this.legends = legends;
        this.data = data;
    }

    public FlightAvgTicketPriceDist() {
    }

    /**
     * 标题
     */
    @JsonProperty("title")
    public String title;

    /**
     * 文案
     */
    @JsonProperty("avgTicketPriceNotice")
    public String avgTicketPriceNotice;

    @JsonProperty("legends")
    public List<OnlineReportTrendLegend> legends;

    @JsonProperty("data")
    public List<OnlineReportTrendPoint> data;

    /**
     * 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 标题
     */
    public void setTitle(final String title) {
        this.title = title;
    }

    /**
     * 文案
     */
    public String getAvgTicketPriceNotice() {
        return avgTicketPriceNotice;
    }

    /**
     * 文案
     */
    public void setAvgTicketPriceNotice(final String avgTicketPriceNotice) {
        this.avgTicketPriceNotice = avgTicketPriceNotice;
    }

    public List<OnlineReportTrendLegend> getLegends() {
        return legends;
    }

    public void setLegends(final List<OnlineReportTrendLegend> legends) {
        this.legends = legends;
    }

    public List<OnlineReportTrendPoint> getData() {
        return data;
    }

    public void setData(final List<OnlineReportTrendPoint> data) {
        this.data = data;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightAvgTicketPriceDist other = (FlightAvgTicketPriceDist) obj;
        return
                Objects.equal(this.title, other.title) &&
                        Objects.equal(this.avgTicketPriceNotice, other.avgTicketPriceNotice) &&
                        Objects.equal(this.legends, other.legends) &&
                        Objects.equal(this.data, other.data);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.title == null ? 0 : this.title.hashCode());
        result = 31 * result + (this.avgTicketPriceNotice == null ? 0 : this.avgTicketPriceNotice.hashCode());
        result = 31 * result + (this.legends == null ? 0 : this.legends.hashCode());
        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("title", title)
                .add("avgTicketPriceNotice", avgTicketPriceNotice)
                .add("legends", legends)
                .add("data", data)
                .toString();
    }
}
