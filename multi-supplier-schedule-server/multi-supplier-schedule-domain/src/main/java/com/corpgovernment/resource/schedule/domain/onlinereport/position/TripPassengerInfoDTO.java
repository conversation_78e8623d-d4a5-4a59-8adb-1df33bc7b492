package com.corpgovernment.resource.schedule.domain.onlinereport.position;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-08-18 11:27
 */
@Data
public class TripPassengerInfoDTO {
    // 出行人uid
    @Column(name = "passengeruid")
    @Type(value = Types.VARCHAR)
    private String passengeruid;

    // 用户姓名（拼音）
    @Column(name = "pin_yin_user_name")
    @Type(value = Types.VARCHAR)
    private String pinYinUseName;

    // 用户姓名（处理后的）
    @Column(name = "processed_user_name")
    @Type(value = Types.VARCHAR)
    private String processedUserName;

}
