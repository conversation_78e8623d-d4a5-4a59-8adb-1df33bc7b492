package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

/**
 * @Description: 机票部门分析明细
 * <AUTHOR>
 * @Date 2019/6/1
 */
public class DeptAnysisDetailBO {


    // 部门id(名称/id)
    String deptId;
    // 部门名称
    String departName;
    // 价格
    double price;
    // 张数
    int quantity;
    // 出票张数
    int draftQuantity;
    // 平均折扣
    double avgDiscount;
    // 全票价比例
    double fullPerc;
    // 节省
    double save;
    // 节省率
    double saveRate;
    // 损失
    double loss;
    // 损失率
    double lossRate;
    //  国际里程平均价 //国内也通用
    double intMilAvgPrice;
    // 未执行差旅政策占比
    double rcPercentage;
    //  平均提前预订天数
    double preOrderdate;
    //  经济舱价格
    double yPrice;
    //  经济舱数量
    double yQuantity;
    // RC违反次数
    double rcTimes ;
    // RC违反率
    double rcRate ;
    // Online占比
    double onlinePerc ;
    // 退票费
    double reFundFee ;
    // 退票率
    double reFundRate ;
    // 改签费
    double reBookFee ;
    // 改签率
    double reBookRate ;
    // 折扣
    double priceRate ;
    // 里程
    double tpm_s_i ;
    //全价
    double stdPrice ;
    // online订单数
    int olQuantity ;
    // 全票价数量
    double fullcount ;
    // 退票费数量
    double reFundQuantity ;
    // 改签费数量
    double reBookQuantity ;
    // 提前预定天数
    double preOrderDay ;
    // 部门数
    int dn ;
    //订单数
    int ordNum;


    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public int getDraftQuantity() {
        return draftQuantity;
    }

    public void setDraftQuantity(int draftQuantity) {
        this.draftQuantity = draftQuantity;
    }

    public double getAvgDiscount() {
        return avgDiscount;
    }

    public void setAvgDiscount(double avgDiscount) {
        this.avgDiscount = avgDiscount;
    }

    public double getFullPerc() {
        return fullPerc;
    }

    public void setFullPerc(double fullPerc) {
        this.fullPerc = fullPerc;
    }

    public double getSave() {
        return save;
    }

    public void setSave(double save) {
        this.save = save;
    }

    public double getSaveRate() {
        return saveRate;
    }

    public void setSaveRate(double saveRate) {
        this.saveRate = saveRate;
    }

    public double getLoss() {
        return loss;
    }

    public void setLoss(double loss) {
        this.loss = loss;
    }

    public double getLossRate() {
        return lossRate;
    }

    public void setLossRate(double lossRate) {
        this.lossRate = lossRate;
    }

    public double getIntMilAvgPrice() {
        return intMilAvgPrice;
    }

    public void setIntMilAvgPrice(double intMilAvgPrice) {
        this.intMilAvgPrice = intMilAvgPrice;
    }

    public double getRcPercentage() {
        return rcPercentage;
    }

    public void setRcPercentage(double rcPercentage) {
        this.rcPercentage = rcPercentage;
    }

    public double getPreOrderdate() {
        return preOrderdate;
    }

    public void setPreOrderdate(double preOrderdate) {
        this.preOrderdate = preOrderdate;
    }

    public double getyPrice() {
        return yPrice;
    }

    public void setyPrice(double yPrice) {
        this.yPrice = yPrice;
    }

    public double getyQuantity() {
        return yQuantity;
    }

    public void setyQuantity(double yQuantity) {
        this.yQuantity = yQuantity;
    }

    public double getRcTimes() {
        return rcTimes;
    }

    public void setRcTimes(double rcTimes) {
        this.rcTimes = rcTimes;
    }

    public double getRcRate() {
        return rcRate;
    }

    public void setRcRate(double rcRate) {
        this.rcRate = rcRate;
    }

    public double getOnlinePerc() {
        return onlinePerc;
    }

    public void setOnlinePerc(double onlinePerc) {
        this.onlinePerc = onlinePerc;
    }

    public double getReFundFee() {
        return reFundFee;
    }

    public void setReFundFee(double reFundFee) {
        this.reFundFee = reFundFee;
    }

    public double getReFundRate() {
        return reFundRate;
    }

    public void setReFundRate(double reFundRate) {
        this.reFundRate = reFundRate;
    }

    public double getReBookFee() {
        return reBookFee;
    }

    public void setReBookFee(double reBookFee) {
        this.reBookFee = reBookFee;
    }

    public double getReBookRate() {
        return reBookRate;
    }

    public void setReBookRate(double reBookRate) {
        this.reBookRate = reBookRate;
    }

    public double getPriceRate() {
        return priceRate;
    }

    public void setPriceRate(double priceRate) {
        this.priceRate = priceRate;
    }

    public double getTpm_s_i() {
        return tpm_s_i;
    }

    public void setTpm_s_i(double tpm_s_i) {
        this.tpm_s_i = tpm_s_i;
    }

    public double getStdPrice() {
        return stdPrice;
    }

    public void setStdPrice(double stdPrice) {
        this.stdPrice = stdPrice;
    }

    public int getOlQuantity() {
        return olQuantity;
    }

    public void setOlQuantity(int olQuantity) {
        this.olQuantity = olQuantity;
    }

    public double getFullcount() {
        return fullcount;
    }

    public void setFullcount(double fullcount) {
        this.fullcount = fullcount;
    }

    public double getReFundQuantity() {
        return reFundQuantity;
    }

    public void setReFundQuantity(double reFundQuantity) {
        this.reFundQuantity = reFundQuantity;
    }

    public double getReBookQuantity() {
        return reBookQuantity;
    }

    public void setReBookQuantity(double reBookQuantity) {
        this.reBookQuantity = reBookQuantity;
    }

    public double getPreOrderDay() {
        return preOrderDay;
    }

    public void setPreOrderDay(double preOrderDay) {
        this.preOrderDay = preOrderDay;
    }

    public int getDn() {
        return dn;
    }

    public void setDn(int dn) {
        this.dn = dn;
    }

    public int getOrdNum() {
        return ordNum;
    }

    public void setOrdNum(int ordNum) {
        this.ordNum = ordNum;
    }
}
