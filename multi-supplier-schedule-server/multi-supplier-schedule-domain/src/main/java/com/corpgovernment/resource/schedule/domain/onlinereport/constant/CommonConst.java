package com.corpgovernment.resource.schedule.domain.onlinereport.constant;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: 公共常量类
 * <AUTHOR>
 * @Date 2019/4/2
 */
public class CommonConst {

    public static final String SERVICENAME = "ServiceName";
    public static final String SERVICECODE_VAL = "100014047";
    public static final String SERVICECODE = "ServiceCode";
    public static final String SERVICEERROR_CODE = "18501001";
    public static final String VALIDAT_EERROR_CODE = "18501002";
    //    public static final String VALIDAT_EERROR = "请求参数错误";
    //静态资源包PackageName
    public static final String STATIC_DATA_NAME = "Data";

    public static final String LOG_GUID_KEY = "LogGuidKey";
    public static final String LOG_DBNAME = "DBName";

    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_FORMAT2 = "yyyy-MM-dd";

    public static final String DATE_FORMAT3 = "%s-%s-%s %s:%s:%s";

    public static final String DATE_FORMAT4 = "%s-%s-%s";

    public static final String DATE_FORMAT5 = "yyyyMM";


//    public static final String ACCNT_TYPE = "月结";
//
//    public static final String PRE_TYPE = "现付";

    public static final String FLIGHT_STATUS_CJ = "成交";
    public static final String FLIGHT_STATUS_CR = "ADU";

    public static final String LANG_RES_URL = "langResUrl";
    public static final String GET_ACCOUNT_RC_CONFIG = "GetAccountInfoList";
    public static final String RC_CONFIG_LOWRC = "ReasonCode";
    public static final String RC_CONFIG_LOWRC_I = "ReasonCodeI";
    public static final String RC_CONFIG_PRERC_N = "FltPreBookRC";
    public static final String RC_CONFIG_PRERC_I = "IntlFltPreBookRC";


    public static final String ZH_CN = "zh-cn";
    public static final String EN = "en";

    //CRedis Name
    public static final String CREDIS_NAME = "Corp_OpenAPIAccessControl";

    public static final String CREDIS_HASH_KEY = "100014026_CREDIS_HASH_KEY";

    //ModelName
    public static final String VEHICLE_DOWNLOAD = "VehicleDownload";
    public static final String TRAIN_DOWNLOAD = "TrainDownload";
    public static final String HOTEL_DOWNLOAD = "HotelDownload";
    public static final String FLIGHT_DOWNLOAD = "FlightDownload";
    public static final String All_MIN_DATADATE = "ALLMinDataDate";

    public static final String WHIPPLETREE = "--";
    public static final double HUNDRED = 100;

    public static final String REPORTPREFIX_KEY = "reportPrefix";

    public static final String SEARCHE_TYPE_M = "M"; //可能在

    public static final String SEARCHE_TYPE_H = "H";//曾到过

    public static final String SEARCHE_TYPE_W = "W";//将要去

    public static final String COMMON_NOLOGIN_KEY = "common-nologin";
    public static final String COMMON_PARAMERROR_KEY = "common-paramerror";
    public static final String COMMON_NOPERMIT_KEY = "common-nopermit";
    public static final String COMMON_SYSTEMERROR_KEY = "common-systemerror";

    public static final String COMMON_NODATA_KEY = "common-nodata";
    public static final String COMMON_SUCESS_KEY = "common-sucess";
    public static final String COMMON_FAIL_KEY = "common-fail";
    public static final String OVERMAXCUSTOMREPORTCOUNT_KEY = "overmaxcustomreportcount";
    public static final String RETRY_LOGIN_OR_RUNASLOGIN_KEY = "RetryLoginOrRunAsLogin";

    //cookie的语言key
    public static final String CORP_RES_LANG = "Corp_ResLang";

    // 新版
    public static final String ONLINE_REPORT_MODULE_ALIAS = "onlineReport";

    // 老版
    public static final String OLD_ONLINE_REPORT_MODULE_ALIAS = "oldOnlineReport";

    public static final String DEFAULT_PLUS = "+";

    public static final String DEFAULT_MINUS = "-";

    public static final String DEFAULT_TILDE = "~";

    public static final String DEFAULT_COLON = ":";

    public static final String PACKAGE_TYPE_NEW_SSVIP = "new_ssvip";

    public static final String PACKAGE_TYPE_NEW_SVIP = "new_svip";

    public static final String PACKAGE_TYPE_NEW_COMMON = "new_common";

    public static final String EMAIL = "email";

    public static final String CONFIG = "config";

    public static final String MONTHLY = "monthly";

    public static final String ANNUAL = "annual";

    public static final String QUARTERLY = "quarterly";

    public static final String ERROR = "error";

    public static final List<String> ALL_REPORTS = Arrays.asList(MONTHLY, ANNUAL, QUARTERLY);

    public static final String BIG = "B";

    public static final String PLT = "P";

    public static final String UNKNOWN = "U";

    public static final String ONE_STR = "1";

    public static final String ZERO_STR = "0";

    public static final int ZERO = 0;

    public static final String COMMA = ",";

    public static final String SEMICOLON = ";";

    public static final String FALSE = "false";

    public static final String TRUE = "true";

    public static final String CHINESE = "zh-CN";

    public static final String ENGLISH = "en-US";

    /**
     * 携带条件的卡片
     */
    public static final String CARD_ID = "cardId";
    /**
     * 卡片shark code前缀
     */
    public static final String CARD_SHARK_CODE_PREFIX = "Reprot.Card.Detail.Preview.";
    public static final String MODULE_CARD_NAME = "moduleCardName";
    public static final String REPORT_FILE_NAME = "reportFileName";}
