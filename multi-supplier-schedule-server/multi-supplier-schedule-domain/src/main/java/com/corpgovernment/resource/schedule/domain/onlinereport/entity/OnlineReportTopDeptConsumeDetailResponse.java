package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.types.ResponseStatusType;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.Map;

/**
 * 各产线部门top消费明细
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "fltTopConsumeInfo",
    "htlTopConsumeInfo",
    "trainTopConsumeInfo",
    "carTopConsumeInfo",
    "extData",
    "totalRecords",
    "overviewTopConsumeInfo"
})
public class OnlineReportTopDeptConsumeDetailResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    public OnlineReportTopDeptConsumeDetailResponse(
            Integer responseCode,
            String responseDesc,
            FltTopDeptConsumeInfo fltTopConsumeInfo,
            HtlTopDeptConsumeInfo htlTopConsumeInfo,
            TrainTopDeptConsumeInfo trainTopConsumeInfo,
            CarTopDeptConsumeInfo carTopConsumeInfo,
            WelfareTopDeptConsumeInfo welfareTopConsumeInfo,
            OverviewTopDeptConsumeInfo overviewTopConsumeInfo,
            Map<String, String> extData,
            Integer totalRecords,
            ResponseStatusType responseStatus) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.fltTopConsumeInfo = fltTopConsumeInfo;
        this.htlTopConsumeInfo = htlTopConsumeInfo;
        this.trainTopConsumeInfo = trainTopConsumeInfo;
        this.carTopConsumeInfo = carTopConsumeInfo;
        this.welfareTopConsumeInfo = welfareTopConsumeInfo;
        this.overviewTopConsumeInfo = overviewTopConsumeInfo;
        this.extData = extData;
        this.totalRecords = totalRecords;
        this.responseStatus = responseStatus;
    }

    public OnlineReportTopDeptConsumeDetailResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    @JsonProperty("fltTopConsumeInfo")
    public FltTopDeptConsumeInfo fltTopConsumeInfo;

    @JsonProperty("htlTopConsumeInfo")
    public HtlTopDeptConsumeInfo htlTopConsumeInfo;

    @JsonProperty("trainTopConsumeInfo")
    public TrainTopDeptConsumeInfo trainTopConsumeInfo;

    @JsonProperty("carTopConsumeInfo")
    public CarTopDeptConsumeInfo carTopConsumeInfo;

    @JsonProperty("welfareTopConsumeInfo")
    public WelfareTopDeptConsumeInfo welfareTopConsumeInfo;

    @JsonProperty("overviewTopConsumeInfo")
    public OverviewTopDeptConsumeInfo overviewTopConsumeInfo;

    @JsonProperty("extData")
    public Map<String, String> extData;

    /**
     * 数据总条数
     */
    @JsonProperty("totalRecords")
    public Integer totalRecords;

    @JsonProperty("ResponseStatus")
    public ResponseStatusType responseStatus;

    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }
    public FltTopDeptConsumeInfo getFltTopConsumeInfo() {
        return fltTopConsumeInfo;
    }

    public void setFltTopConsumeInfo(final FltTopDeptConsumeInfo fltTopConsumeInfo) {
        this.fltTopConsumeInfo = fltTopConsumeInfo;
    }
    public HtlTopDeptConsumeInfo getHtlTopConsumeInfo() {
        return htlTopConsumeInfo;
    }

    public void setHtlTopConsumeInfo(final HtlTopDeptConsumeInfo htlTopConsumeInfo) {
        this.htlTopConsumeInfo = htlTopConsumeInfo;
    }
    public TrainTopDeptConsumeInfo getTrainTopConsumeInfo() {
        return trainTopConsumeInfo;
    }

    public void setTrainTopConsumeInfo(final TrainTopDeptConsumeInfo trainTopConsumeInfo) {
        this.trainTopConsumeInfo = trainTopConsumeInfo;
    }
    public CarTopDeptConsumeInfo getCarTopConsumeInfo() {
        return carTopConsumeInfo;
    }

    public void setCarTopConsumeInfo(final CarTopDeptConsumeInfo carTopConsumeInfo) {
        this.carTopConsumeInfo = carTopConsumeInfo;
    }
    public WelfareTopDeptConsumeInfo getWelfareTopConsumeInfo() {
        return welfareTopConsumeInfo;
    }

    public void setWelfareTopConsumeInfo(final WelfareTopDeptConsumeInfo welfareTopConsumeInfo) {
        this.welfareTopConsumeInfo = welfareTopConsumeInfo;
    }
    public OverviewTopDeptConsumeInfo getOverviewTopConsumeInfo() {
        return overviewTopConsumeInfo;
    }

    public void setOverviewTopConsumeInfo(final OverviewTopDeptConsumeInfo overviewTopConsumeInfo) {
        this.overviewTopConsumeInfo = overviewTopConsumeInfo;
    }
    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    /**
     * 数据总条数
     */
    public Integer getTotalRecords() {
        return totalRecords;
    }

    /**
     * 数据总条数
     */
    public void setTotalRecords(final Integer totalRecords) {
        this.totalRecords = totalRecords;
    }
    public ResponseStatusType getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(final ResponseStatusType responseStatus) {
        this.responseStatus = responseStatus;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0: return this.responseCode;
            case 1: return this.responseDesc;
            case 2: return this.fltTopConsumeInfo;
            case 3: return this.htlTopConsumeInfo;
            case 4: return this.trainTopConsumeInfo;
            case 5: return this.carTopConsumeInfo;
            case 6: return this.welfareTopConsumeInfo;
            case 7: return this.overviewTopConsumeInfo;
            case 8: return this.extData;
            case 9: return this.totalRecords;
            case 10: return this.responseStatus;
            default: throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value="unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0: this.responseCode = (Integer)fieldValue; break;
            case 1: this.responseDesc = (String)fieldValue; break;
            case 2: this.fltTopConsumeInfo = (FltTopDeptConsumeInfo)fieldValue; break;
            case 3: this.htlTopConsumeInfo = (HtlTopDeptConsumeInfo)fieldValue; break;
            case 4: this.trainTopConsumeInfo = (TrainTopDeptConsumeInfo)fieldValue; break;
            case 5: this.carTopConsumeInfo = (CarTopDeptConsumeInfo)fieldValue; break;
            case 6: this.welfareTopConsumeInfo = (WelfareTopDeptConsumeInfo)fieldValue; break;
            case 7: this.overviewTopConsumeInfo = (OverviewTopDeptConsumeInfo)fieldValue; break;
            case 8: this.extData = (Map<String, String>)fieldValue; break;
            case 9: this.totalRecords = (Integer)fieldValue; break;
            case 10: this.responseStatus = (ResponseStatusType)fieldValue; break;
            default: throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTopDeptConsumeDetailResponse other = (OnlineReportTopDeptConsumeDetailResponse)obj;
        return
                Objects.equal(this.responseCode, other.responseCode) &&
                        Objects.equal(this.responseDesc, other.responseDesc) &&
                        Objects.equal(this.fltTopConsumeInfo, other.fltTopConsumeInfo) &&
                        Objects.equal(this.htlTopConsumeInfo, other.htlTopConsumeInfo) &&
                        Objects.equal(this.trainTopConsumeInfo, other.trainTopConsumeInfo) &&
                        Objects.equal(this.carTopConsumeInfo, other.carTopConsumeInfo) &&
                        Objects.equal(this.welfareTopConsumeInfo, other.welfareTopConsumeInfo) &&
                        Objects.equal(this.overviewTopConsumeInfo, other.overviewTopConsumeInfo) &&
                        Objects.equal(this.extData, other.extData) &&
                        Objects.equal(this.totalRecords, other.totalRecords) &&
                        Objects.equal(this.responseStatus, other.responseStatus);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.fltTopConsumeInfo == null ? 0 : this.fltTopConsumeInfo.hashCode());
        result = 31 * result + (this.htlTopConsumeInfo == null ? 0 : this.htlTopConsumeInfo.hashCode());
        result = 31 * result + (this.trainTopConsumeInfo == null ? 0 : this.trainTopConsumeInfo.hashCode());
        result = 31 * result + (this.carTopConsumeInfo == null ? 0 : this.carTopConsumeInfo.hashCode());
        result = 31 * result + (this.welfareTopConsumeInfo == null ? 0 : this.welfareTopConsumeInfo.hashCode());
        result = 31 * result + (this.overviewTopConsumeInfo == null ? 0 : this.overviewTopConsumeInfo.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());
        result = 31 * result + (this.totalRecords == null ? 0 : this.totalRecords.hashCode());
        result = 31 * result + (this.responseStatus == null ? 0 : this.responseStatus.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("responseCode", responseCode)
                .add("responseDesc", responseDesc)
                .add("fltTopConsumeInfo", fltTopConsumeInfo)
                .add("htlTopConsumeInfo", htlTopConsumeInfo)
                .add("trainTopConsumeInfo", trainTopConsumeInfo)
                .add("carTopConsumeInfo", carTopConsumeInfo)
                .add("welfareTopConsumeInfo", welfareTopConsumeInfo)
                .add("overviewTopConsumeInfo", overviewTopConsumeInfo)
                .add("extData", extData)
                .add("totalRecords", totalRecords)
                .add("responseStatus", responseStatus)
                .toString();
    }
}
