package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 概览-节省分布
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "reportDate",
    "totalSaveAmount3c",
    "saveAmount3c",
    "saveAmount3cPercent",
    "totalSaveAmount2c",
    "saveAmount2c",
    "saveAmount2cPercent",
    "totalSaveAmountPromotion",
    "saveAmountPromotion",
    "saveAmountPromotionPercent",
    "saveAmount",
    "totalControlSave",
    "controlSave",
    "controlSavePercent"
})
public class GeneralSaveDistributionInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public GeneralSaveDistributionInfo(
        String reportDate,
        BigDecimal totalSaveAmount3c,
        BigDecimal saveAmount3c,
        BigDecimal saveAmount3cPercent,
        BigDecimal totalSaveAmount2c,
        BigDecimal saveAmount2c,
        BigDecimal saveAmount2cPercent,
        BigDecimal totalSaveAmountPromotion,
        BigDecimal saveAmountPromotion,
        BigDecimal saveAmountPromotionPercent,
        BigDecimal saveAmount,
        BigDecimal totalControlSave,
        BigDecimal controlSave,
        BigDecimal controlSavePercent) {
        this.reportDate = reportDate;
        this.totalSaveAmount3c = totalSaveAmount3c;
        this.saveAmount3c = saveAmount3c;
        this.saveAmount3cPercent = saveAmount3cPercent;
        this.totalSaveAmount2c = totalSaveAmount2c;
        this.saveAmount2c = saveAmount2c;
        this.saveAmount2cPercent = saveAmount2cPercent;
        this.totalSaveAmountPromotion = totalSaveAmountPromotion;
        this.saveAmountPromotion = saveAmountPromotion;
        this.saveAmountPromotionPercent = saveAmountPromotionPercent;
        this.saveAmount = saveAmount;
        this.totalControlSave = totalControlSave;
        this.controlSave = controlSave;
        this.controlSavePercent = controlSavePercent;
    }

    public GeneralSaveDistributionInfo() {
    }

    /**
     * 消费记录开始时间
     */
    @JsonProperty("reportDate")
    public String reportDate;

    /**
     * 总三方节省（从有消费记录开始）
     */
    @JsonProperty("totalSaveAmount3c")
    public BigDecimal totalSaveAmount3c;

    /**
     * 三方节省
     */
    @JsonProperty("saveAmount3c")
    public BigDecimal saveAmount3c;

    /**
     * 三方节省占比
     */
    @JsonProperty("saveAmount3cPercent")
    public BigDecimal saveAmount3cPercent;

    /**
     * 总两方节省（从有消费记录开始）
     */
    @JsonProperty("totalSaveAmount2c")
    public BigDecimal totalSaveAmount2c;

    /**
     * 两方节省
     */
    @JsonProperty("saveAmount2c")
    public BigDecimal saveAmount2c;

    /**
     * 两方节省占比
     */
    @JsonProperty("saveAmount2cPercent")
    public BigDecimal saveAmount2cPercent;

    /**
     * 总优惠节省（从有消费记录开始）
     */
    @JsonProperty("totalSaveAmountPromotion")
    public BigDecimal totalSaveAmountPromotion;

    /**
     * 优惠节省
     */
    @JsonProperty("saveAmountPromotion")
    public BigDecimal saveAmountPromotion;

    /**
     * 优惠节省占比
     */
    @JsonProperty("saveAmountPromotionPercent")
    public BigDecimal saveAmountPromotionPercent;

    /**
     * 节省金额
     */
    @JsonProperty("saveAmount")
    public BigDecimal saveAmount;

    /**
     * 总管控节省（从有消费记录开始）
     */
    @JsonProperty("totalControlSave")
    public BigDecimal totalControlSave;

    /**
     * 管控节省
     */
    @JsonProperty("controlSave")
    public BigDecimal controlSave;

    /**
     * 管控节省占比
     */
    @JsonProperty("controlSavePercent")
    public BigDecimal controlSavePercent;

    /**
     * 消费记录开始时间
     */
    public String getReportDate() {
        return reportDate;
    }

    /**
     * 消费记录开始时间
     */
    public void setReportDate(final String reportDate) {
        this.reportDate = reportDate;
    }

    /**
     * 总三方节省（从有消费记录开始）
     */
    public BigDecimal getTotalSaveAmount3c() {
        return totalSaveAmount3c;
    }

    /**
     * 总三方节省（从有消费记录开始）
     */
    public void setTotalSaveAmount3c(final BigDecimal totalSaveAmount3c) {
        this.totalSaveAmount3c = totalSaveAmount3c;
    }

    /**
     * 三方节省
     */
    public BigDecimal getSaveAmount3c() {
        return saveAmount3c;
    }

    /**
     * 三方节省
     */
    public void setSaveAmount3c(final BigDecimal saveAmount3c) {
        this.saveAmount3c = saveAmount3c;
    }

    /**
     * 三方节省占比
     */
    public BigDecimal getSaveAmount3cPercent() {
        return saveAmount3cPercent;
    }

    /**
     * 三方节省占比
     */
    public void setSaveAmount3cPercent(final BigDecimal saveAmount3cPercent) {
        this.saveAmount3cPercent = saveAmount3cPercent;
    }

    /**
     * 总两方节省（从有消费记录开始）
     */
    public BigDecimal getTotalSaveAmount2c() {
        return totalSaveAmount2c;
    }

    /**
     * 总两方节省（从有消费记录开始）
     */
    public void setTotalSaveAmount2c(final BigDecimal totalSaveAmount2c) {
        this.totalSaveAmount2c = totalSaveAmount2c;
    }

    /**
     * 两方节省
     */
    public BigDecimal getSaveAmount2c() {
        return saveAmount2c;
    }

    /**
     * 两方节省
     */
    public void setSaveAmount2c(final BigDecimal saveAmount2c) {
        this.saveAmount2c = saveAmount2c;
    }

    /**
     * 两方节省占比
     */
    public BigDecimal getSaveAmount2cPercent() {
        return saveAmount2cPercent;
    }

    /**
     * 两方节省占比
     */
    public void setSaveAmount2cPercent(final BigDecimal saveAmount2cPercent) {
        this.saveAmount2cPercent = saveAmount2cPercent;
    }

    /**
     * 总优惠节省（从有消费记录开始）
     */
    public BigDecimal getTotalSaveAmountPromotion() {
        return totalSaveAmountPromotion;
    }

    /**
     * 总优惠节省（从有消费记录开始）
     */
    public void setTotalSaveAmountPromotion(final BigDecimal totalSaveAmountPromotion) {
        this.totalSaveAmountPromotion = totalSaveAmountPromotion;
    }

    /**
     * 优惠节省
     */
    public BigDecimal getSaveAmountPromotion() {
        return saveAmountPromotion;
    }

    /**
     * 优惠节省
     */
    public void setSaveAmountPromotion(final BigDecimal saveAmountPromotion) {
        this.saveAmountPromotion = saveAmountPromotion;
    }

    /**
     * 优惠节省占比
     */
    public BigDecimal getSaveAmountPromotionPercent() {
        return saveAmountPromotionPercent;
    }

    /**
     * 优惠节省占比
     */
    public void setSaveAmountPromotionPercent(final BigDecimal saveAmountPromotionPercent) {
        this.saveAmountPromotionPercent = saveAmountPromotionPercent;
    }

    /**
     * 节省金额
     */
    public BigDecimal getSaveAmount() {
        return saveAmount;
    }

    /**
     * 节省金额
     */
    public void setSaveAmount(final BigDecimal saveAmount) {
        this.saveAmount = saveAmount;
    }

    /**
     * 总管控节省（从有消费记录开始）
     */
    public BigDecimal getTotalControlSave() {
        return totalControlSave;
    }

    /**
     * 总管控节省（从有消费记录开始）
     */
    public void setTotalControlSave(final BigDecimal totalControlSave) {
        this.totalControlSave = totalControlSave;
    }

    /**
     * 管控节省
     */
    public BigDecimal getControlSave() {
        return controlSave;
    }

    /**
     * 管控节省
     */
    public void setControlSave(final BigDecimal controlSave) {
        this.controlSave = controlSave;
    }

    /**
     * 管控节省占比
     */
    public BigDecimal getControlSavePercent() {
        return controlSavePercent;
    }

    /**
     * 管控节省占比
     */
    public void setControlSavePercent(final BigDecimal controlSavePercent) {
        this.controlSavePercent = controlSavePercent;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final GeneralSaveDistributionInfo other = (GeneralSaveDistributionInfo)obj;
        return
            Objects.equal(this.reportDate, other.reportDate) &&
            Objects.equal(this.totalSaveAmount3c, other.totalSaveAmount3c) &&
            Objects.equal(this.saveAmount3c, other.saveAmount3c) &&
            Objects.equal(this.saveAmount3cPercent, other.saveAmount3cPercent) &&
            Objects.equal(this.totalSaveAmount2c, other.totalSaveAmount2c) &&
            Objects.equal(this.saveAmount2c, other.saveAmount2c) &&
            Objects.equal(this.saveAmount2cPercent, other.saveAmount2cPercent) &&
            Objects.equal(this.totalSaveAmountPromotion, other.totalSaveAmountPromotion) &&
            Objects.equal(this.saveAmountPromotion, other.saveAmountPromotion) &&
            Objects.equal(this.saveAmountPromotionPercent, other.saveAmountPromotionPercent) &&
            Objects.equal(this.saveAmount, other.saveAmount) &&
            Objects.equal(this.totalControlSave, other.totalControlSave) &&
            Objects.equal(this.controlSave, other.controlSave) &&
            Objects.equal(this.controlSavePercent, other.controlSavePercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.reportDate == null ? 0 : this.reportDate.hashCode());
        result = 31 * result + (this.totalSaveAmount3c == null ? 0 : this.totalSaveAmount3c.hashCode());
        result = 31 * result + (this.saveAmount3c == null ? 0 : this.saveAmount3c.hashCode());
        result = 31 * result + (this.saveAmount3cPercent == null ? 0 : this.saveAmount3cPercent.hashCode());
        result = 31 * result + (this.totalSaveAmount2c == null ? 0 : this.totalSaveAmount2c.hashCode());
        result = 31 * result + (this.saveAmount2c == null ? 0 : this.saveAmount2c.hashCode());
        result = 31 * result + (this.saveAmount2cPercent == null ? 0 : this.saveAmount2cPercent.hashCode());
        result = 31 * result + (this.totalSaveAmountPromotion == null ? 0 : this.totalSaveAmountPromotion.hashCode());
        result = 31 * result + (this.saveAmountPromotion == null ? 0 : this.saveAmountPromotion.hashCode());
        result = 31 * result + (this.saveAmountPromotionPercent == null ? 0 : this.saveAmountPromotionPercent.hashCode());
        result = 31 * result + (this.saveAmount == null ? 0 : this.saveAmount.hashCode());
        result = 31 * result + (this.totalControlSave == null ? 0 : this.totalControlSave.hashCode());
        result = 31 * result + (this.controlSave == null ? 0 : this.controlSave.hashCode());
        result = 31 * result + (this.controlSavePercent == null ? 0 : this.controlSavePercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("reportDate", reportDate)
            .add("totalSaveAmount3c", totalSaveAmount3c)
            .add("saveAmount3c", saveAmount3c)
            .add("saveAmount3cPercent", saveAmount3cPercent)
            .add("totalSaveAmount2c", totalSaveAmount2c)
            .add("saveAmount2c", saveAmount2c)
            .add("saveAmount2cPercent", saveAmount2cPercent)
            .add("totalSaveAmountPromotion", totalSaveAmountPromotion)
            .add("saveAmountPromotion", saveAmountPromotion)
            .add("saveAmountPromotionPercent", saveAmountPromotionPercent)
            .add("saveAmount", saveAmount)
            .add("totalControlSave", totalControlSave)
            .add("controlSave", controlSave)
            .add("controlSavePercent", controlSavePercent)
            .toString();
    }
}
