package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.car;

/**
 * @Description: 用车订单明细
 * <AUTHOR>
 * @Date 2019/6/9
 */
public class CarOrderBO {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 公司ID
     */
    private String corpCorporation;

    /**
     * 主账户代号
     */
    private String accountId;

    /**
     * 出票年份
     */
    private Integer printYear;

    /**
     * 出票月份
     */
    private Integer printMonth;

    /**
     * uid
     */
    private String uid;

    /**
     * 乘客姓名
     */
    private String passengerName;

    /**
     * 持卡人
     */
    private String userName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 工作城市
     */
    private String workCity;

    /**
     * 部门1
     */
    private String dept1;

    /**
     * 部门2
     */
    private String dept2;

    /**
     * 部门3
     */
    private String dept3;

    /**
     * 部门4
     */
    private String dept4;

    /**
     * 部门5
     */
    private String dept5;

    /**
     * 部门6
     */
    private String dept6;

    /**
     * 部门7
     */
    private String dept7;

    /**
     * 部门8
     */
    private String dept8;

    /**
     * 部门9
     */
    private String dept9;

    /**
     * 部门10
     */
    private String dept10;

    /**
     * 成本中心1
     */
    private String costCenter1;

    /**
     * 成本中心2
     */
    private String costCenter2;

    /**
     * 成本中心3
     */
    private String costCenter3;

    /**
     * 成本中心4
     */
    private String costCenter4;

    /**
     * 成本中心5
     */
    private String costCenter5;

    /**
     * 成本中心6
     */
    private String costCenter6;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String project;

    /**
     * 出行目的编号
     */
    private String journeyReasonCode;

    /**
     * 出行目的名称
     */
    private String journeyReason;

    /**
     * 自定义字段1
     */
    private String defineValue1;

    /**
     * 自定义字段2
     */
    private String defineValue2;

    /**
     * 关联行程单号
     */
    private String journeyNo;

    /**
     * 主账户名称
     */
    private String accountCodeName;

    /**
     * 所属行业
     */
    private String industryType;

    /**
     * 订单类型(用车类型) 1   国内代驾 2 国际代驾 ;4  国内自驾;代驾=接送机 ，自驾=租车，暂无国际租车
     */
    private Integer orderType;

    /**
     * 预定日期
     */
    private Long orderDate;

    /**
     * 订单状态（ 已提交：Submitted 处理中：Processing 已确认：Confirmed 取消中：Canceling 已取消： Canceled 已成交：Successful）
     */
    private String orderStatus;

    /**
     * 订车数量，目前恒等于1
     */
    private Integer quantity;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 出发时间 租车：出发时间、还车时间根据租车起始和结束时间来显示yyyy-mm-dd hh:mm:ss,接送机：仅填写出发时间
     */
    private Long startTime;

    /**
     * 城市，租车=借车城市、接送机=出发城市
     */
    private String departureCityName;

    /**
     * 接送机机场
     */
    private String fixedLocationName;

    /**
     * 接送机地点
     */
    private String address;

    /**
     * 接送机行为 17 接 18送
     */
    private Integer patternType;

    /**
     * 还车城市，仅租车
     */
    private String arrivalCityName;

    /**
     * 还车时间,接送机无
     */
    private Long endTime;

    /**
     * 接送机航班
     */
    private String flightTrainNum;

    /**
     * 车型品牌
     */
    private String vehicleType;

    /**
     * 基础费用
     */
    private String basicFee;

    /**
     * 增值费用
     */
    private String valueAddFee;

    /**
     * 服务费
     */
    private String serviceFee;

    /**
     * 快递费
     */
    private String deliveryFee;

    /**
     * 违约金
     */
    private String penaltyFee;

    /**
     * 总金额=基础费用+增值费用+服务费+快递费+违约金
     */
    private String price;

    /**
     * 预订方式（channeltype ：208 online ， 408 app）Online、Offline、App ,用车暂无offline
     */
    private Integer bookType;

    /**
     * 支付方式（ACCNT:公司账户 CCARD:信用卡 CASH:现付）
     */
    private String paymentType;

    /**
     * 所属行程号
     */
    private Long tripId;

    /**
     * VehicleId = 1  快车  else 专车
     */
    private Integer vehicleId;

    /**
     * 1  市内, 2  跨城
     */
    private Integer useType;

    /**
     * 出发地点
     */
    private String startAddress;

    /**
     * 出发地点-详细地址
     */
    private String startAddressDes;

    /**
     * 目的地点
     */
    private String endAddress;

    /**
     * 目的地点-详细地址
     */
    private String endAddressDes;

    /**
     * 预计里程-公里数
     */
    private String estimateDistance;

    /**
     * 实际里程-公里
     */
    private String normalDistance;

    /**
     * 供应商id
     */
    private String vendorId;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 供应商英文名
     */
    private String vendorNameEn;

    /**
     * 出发城市Id
     */
    private Integer departureCityId;

    /**
     * 到达城市Id
     */
    private Integer arrivalCityId;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCorpCorporation() {
        return corpCorporation;
    }

    public void setCorpCorporation(String corpCorporation) {
        this.corpCorporation = corpCorporation;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public Integer getPrintYear() {
        return printYear;
    }

    public void setPrintYear(Integer printYear) {
        this.printYear = printYear;
    }

    public Integer getPrintMonth() {
        return printMonth;
    }

    public void setPrintMonth(Integer printMonth) {
        this.printMonth = printMonth;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getPassengerName() {
        return passengerName;
    }

    public void setPassengerName(String passengerName) {
        this.passengerName = passengerName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getWorkCity() {
        return workCity;
    }

    public void setWorkCity(String workCity) {
        this.workCity = workCity;
    }

    public String getDept1() {
        return dept1;
    }

    public void setDept1(String dept1) {
        this.dept1 = dept1;
    }

    public String getDept2() {
        return dept2;
    }

    public void setDept2(String dept2) {
        this.dept2 = dept2;
    }

    public String getDept3() {
        return dept3;
    }

    public void setDept3(String dept3) {
        this.dept3 = dept3;
    }

    public String getDept4() {
        return dept4;
    }

    public void setDept4(String dept4) {
        this.dept4 = dept4;
    }

    public String getDept5() {
        return dept5;
    }

    public void setDept5(String dept5) {
        this.dept5 = dept5;
    }

    public String getDept6() {
        return dept6;
    }

    public void setDept6(String dept6) {
        this.dept6 = dept6;
    }

    public String getDept7() {
        return dept7;
    }

    public void setDept7(String dept7) {
        this.dept7 = dept7;
    }

    public String getDept8() {
        return dept8;
    }

    public void setDept8(String dept8) {
        this.dept8 = dept8;
    }

    public String getDept9() {
        return dept9;
    }

    public void setDept9(String dept9) {
        this.dept9 = dept9;
    }

    public String getDept10() {
        return dept10;
    }

    public void setDept10(String dept10) {
        this.dept10 = dept10;
    }

    public String getCostCenter1() {
        return costCenter1;
    }

    public void setCostCenter1(String costCenter1) {
        this.costCenter1 = costCenter1;
    }

    public String getCostCenter2() {
        return costCenter2;
    }

    public void setCostCenter2(String costCenter2) {
        this.costCenter2 = costCenter2;
    }

    public String getCostCenter3() {
        return costCenter3;
    }

    public void setCostCenter3(String costCenter3) {
        this.costCenter3 = costCenter3;
    }

    public String getCostCenter4() {
        return costCenter4;
    }

    public void setCostCenter4(String costCenter4) {
        this.costCenter4 = costCenter4;
    }

    public String getCostCenter5() {
        return costCenter5;
    }

    public void setCostCenter5(String costCenter5) {
        this.costCenter5 = costCenter5;
    }

    public String getCostCenter6() {
        return costCenter6;
    }

    public void setCostCenter6(String costCenter6) {
        this.costCenter6 = costCenter6;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getJourneyReasonCode() {
        return journeyReasonCode;
    }

    public void setJourneyReasonCode(String journeyReasonCode) {
        this.journeyReasonCode = journeyReasonCode;
    }

    public String getJourneyReason() {
        return journeyReason;
    }

    public void setJourneyReason(String journeyReason) {
        this.journeyReason = journeyReason;
    }

    public String getDefineValue1() {
        return defineValue1;
    }

    public void setDefineValue1(String defineValue1) {
        this.defineValue1 = defineValue1;
    }

    public String getDefineValue2() {
        return defineValue2;
    }

    public void setDefineValue2(String defineValue2) {
        this.defineValue2 = defineValue2;
    }

    public String getJourneyNo() {
        return journeyNo;
    }

    public void setJourneyNo(String journeyNo) {
        this.journeyNo = journeyNo;
    }

    public String getAccountCodeName() {
        return accountCodeName;
    }

    public void setAccountCodeName(String accountCodeName) {
        this.accountCodeName = accountCodeName;
    }

    public String getIndustryType() {
        return industryType;
    }

    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Long getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Long orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public String getDepartureCityName() {
        return departureCityName;
    }

    public void setDepartureCityName(String departureCityName) {
        this.departureCityName = departureCityName;
    }

    public String getFixedLocationName() {
        return fixedLocationName;
    }

    public void setFixedLocationName(String fixedLocationName) {
        this.fixedLocationName = fixedLocationName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getPatternType() {
        return patternType;
    }

    public void setPatternType(Integer patternType) {
        this.patternType = patternType;
    }

    public String getArrivalCityName() {
        return arrivalCityName;
    }

    public void setArrivalCityName(String arrivalCityName) {
        this.arrivalCityName = arrivalCityName;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public String getFlightTrainNum() {
        return flightTrainNum;
    }

    public void setFlightTrainNum(String flightTrainNum) {
        this.flightTrainNum = flightTrainNum;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getBasicFee() {
        return basicFee;
    }

    public void setBasicFee(String basicFee) {
        this.basicFee = basicFee;
    }

    public String getValueAddFee() {
        return valueAddFee;
    }

    public void setValueAddFee(String valueAddFee) {
        this.valueAddFee = valueAddFee;
    }

    public String getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(String serviceFee) {
        this.serviceFee = serviceFee;
    }

    public String getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(String deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public String getPenaltyFee() {
        return penaltyFee;
    }

    public void setPenaltyFee(String penaltyFee) {
        this.penaltyFee = penaltyFee;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Integer getBookType() {
        return bookType;
    }

    public void setBookType(Integer bookType) {
        this.bookType = bookType;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public Long getTripId() {
        return tripId;
    }

    public void setTripId(Long tripId) {
        this.tripId = tripId;
    }

    public Integer getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Integer vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Integer getUseType() {
        return useType;
    }

    public void setUseType(Integer useType) {
        this.useType = useType;
    }

    public String getStartAddress() {
        return startAddress;
    }

    public void setStartAddress(String startAddress) {
        this.startAddress = startAddress;
    }

    public String getStartAddressDes() {
        return startAddressDes;
    }

    public void setStartAddressDes(String startAddressDes) {
        this.startAddressDes = startAddressDes;
    }

    public String getEndAddress() {
        return endAddress;
    }

    public void setEndAddress(String endAddress) {
        this.endAddress = endAddress;
    }

    public String getEndAddressDes() {
        return endAddressDes;
    }

    public void setEndAddressDes(String endAddressDes) {
        this.endAddressDes = endAddressDes;
    }

    public String getEstimateDistance() {
        return estimateDistance;
    }

    public void setEstimateDistance(String estimateDistance) {
        this.estimateDistance = estimateDistance;
    }

    public String getNormalDistance() {
        return normalDistance;
    }

    public void setNormalDistance(String normalDistance) {
        this.normalDistance = normalDistance;
    }

    public String getVendorId() {
        return vendorId;
    }

    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getVendorNameEn() {
        return vendorNameEn;
    }

    public void setVendorNameEn(String vendorNameEn) {
        this.vendorNameEn = vendorNameEn;
    }

    public Integer getDepartureCityId() {
        return departureCityId;
    }

    public void setDepartureCityId(Integer departureCityId) {
        this.departureCityId = departureCityId;
    }

    public Integer getArrivalCityId() {
        return arrivalCityId;
    }

    public void setArrivalCityId(Integer arrivalCityId) {
        this.arrivalCityId = arrivalCityId;
    }
}
