package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "fltOrderList",
    "htlOrderList",
    "trainOrderList",
    "carOrderList"
})
public class OnlineReportOrderDetailInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportOrderDetailInfo(
        List<OnlineReportFlightOrderInfo> fltOrderList,
        List<OnlineReportHotelOrderInfo> htlOrderList,
        List<OnlineReportTrainOrderInfo> trainOrderList,
        List<OnlineReportCarOrderInfo> carOrderList) {
        this.fltOrderList = fltOrderList;
        this.htlOrderList = htlOrderList;
        this.trainOrderList = trainOrderList;
        this.carOrderList = carOrderList;
    }

    public OnlineReportOrderDetailInfo() {
    }

    @JsonProperty("fltOrderList")
    public List<OnlineReportFlightOrderInfo> fltOrderList;

    @JsonProperty("htlOrderList")
    public List<OnlineReportHotelOrderInfo> htlOrderList;

    @JsonProperty("trainOrderList")
    public List<OnlineReportTrainOrderInfo> trainOrderList;

    @JsonProperty("carOrderList")
    public List<OnlineReportCarOrderInfo> carOrderList;

    public List<OnlineReportFlightOrderInfo> getFltOrderList() {
        return fltOrderList;
    }

    public void setFltOrderList(final List<OnlineReportFlightOrderInfo> fltOrderList) {
        this.fltOrderList = fltOrderList;
    }
    public List<OnlineReportHotelOrderInfo> getHtlOrderList() {
        return htlOrderList;
    }

    public void setHtlOrderList(final List<OnlineReportHotelOrderInfo> htlOrderList) {
        this.htlOrderList = htlOrderList;
    }
    public List<OnlineReportTrainOrderInfo> getTrainOrderList() {
        return trainOrderList;
    }

    public void setTrainOrderList(final List<OnlineReportTrainOrderInfo> trainOrderList) {
        this.trainOrderList = trainOrderList;
    }
    public List<OnlineReportCarOrderInfo> getCarOrderList() {
        return carOrderList;
    }

    public void setCarOrderList(final List<OnlineReportCarOrderInfo> carOrderList) {
        this.carOrderList = carOrderList;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportOrderDetailInfo other = (OnlineReportOrderDetailInfo)obj;
        return
            Objects.equal(this.fltOrderList, other.fltOrderList) &&
            Objects.equal(this.htlOrderList, other.htlOrderList) &&
            Objects.equal(this.trainOrderList, other.trainOrderList) &&
            Objects.equal(this.carOrderList, other.carOrderList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.fltOrderList == null ? 0 : this.fltOrderList.hashCode());
        result = 31 * result + (this.htlOrderList == null ? 0 : this.htlOrderList.hashCode());
        result = 31 * result + (this.trainOrderList == null ? 0 : this.trainOrderList.hashCode());
        result = 31 * result + (this.carOrderList == null ? 0 : this.carOrderList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("fltOrderList", fltOrderList)
            .add("htlOrderList", htlOrderList)
            .add("trainOrderList", trainOrderList)
            .add("carOrderList", carOrderList)
            .toString();
    }
}
