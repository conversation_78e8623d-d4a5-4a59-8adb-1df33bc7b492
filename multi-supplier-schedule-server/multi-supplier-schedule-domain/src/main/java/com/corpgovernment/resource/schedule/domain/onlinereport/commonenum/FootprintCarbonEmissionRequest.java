package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * * 个人足迹 碳排 请求
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "queryTime",
    "uid"
})
public class FootprintCarbonEmissionRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public FootprintCarbonEmissionRequest(
        String queryTime,
        String uid) {
        this.queryTime = queryTime;
        this.uid = uid;
    }

    public FootprintCarbonEmissionRequest() {
    }

    /**
     * 全部ALL 本年THIS_YEAR 本月THIS_MONTH
     */
    @JsonProperty("queryTime")
    public String queryTime;

    @JsonProperty("uid")
    public String uid;

    /**
     * 全部ALL 本年THIS_YEAR 本月THIS_MONTH
     */
    public String getQueryTime() {
        return queryTime;
    }

    /**
     * 全部ALL 本年THIS_YEAR 本月THIS_MONTH
     */
    public void setQueryTime(final String queryTime) {
        this.queryTime = queryTime;
    }
    public String getUid() {
        return uid;
    }

    public void setUid(final String uid) {
        this.uid = uid;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FootprintCarbonEmissionRequest other = (FootprintCarbonEmissionRequest)obj;
        return
            Objects.equal(this.queryTime, other.queryTime) &&
            Objects.equal(this.uid, other.uid);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.queryTime == null ? 0 : this.queryTime.hashCode());
        result = 31 * result + (this.uid == null ? 0 : this.uid.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("queryTime", queryTime)
            .add("uid", uid)
            .toString();
    }
}
