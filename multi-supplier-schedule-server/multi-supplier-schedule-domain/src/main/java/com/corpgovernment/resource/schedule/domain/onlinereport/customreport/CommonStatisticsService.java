/*
package com.corpgovernment.resource.schedule.domain.onlinereport.customreport;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.ReportUserDefinedOutput;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserDefinedFilter;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.business.BusinessConst;
import onlinereport.enums.BizTypeEnums;
import onlinereport.enums.DimensionEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

*/
/**
 * Auther:abguo
 * Date:2019/8/1
 * Description:
 * Project:onlinereportweb
 *//*

@Service
public class CommonStatisticsService extends StatisticsReportService {
    @Override
    protected CustomStatisticsDataHandler getStatisticsDataHandler() {
        return null;
    }

    protected CustomStatisticsDataHandler getStatisticsDataHandler(BizTypeEnums bizTypeEnums) {
        CustomStatisticsDataHandler customStatisticsDataHandler = null;
        switch (bizTypeEnums) {
            case F:
                customStatisticsDataHandler = SpringUtil.getBean(StatisticsFlightDataHandler.class);
                break;
            case T:
                customStatisticsDataHandler = SpringUtil.getBean(StatisticsTrainDataHandler.class);
                break;
            case H:
                customStatisticsDataHandler = SpringUtil.getBean(StatisticsHotelDataHandler.class);
                break;
            case C:
                customStatisticsDataHandler = SpringUtil.getBean(StatisticsCarDataHandler.class);
                break;
            default:
                customStatisticsDataHandler = SpringUtil.getBean(StatisticsCommonDataHandler.class);
        }

        return customStatisticsDataHandler;
    }

    @Override
    protected void addFilterValueFromSource(List<Map<String, String>> dataSourceList, HashSet<String> dimensionSet, Map<String, List<UserDefinedFilter>> mapResult) {

    }

    @Override
    protected void convertIDToName(Map<String, List<UserDefinedFilter>> mapResult, ReportUserDefinedOutput result, ReportUserDefinedInput input) {

    }

    */
/**
     * 统计数据
     *
     * @param input
     * @return
     *//*

    public ReportUserDefinedOutput statistics(ReportUserDefinedInput input, String uid) {
        setBizType(input);
        if (StringUtils.equalsIgnoreCase(input.getBizTypeEnums().toString(), BizTypeEnums.A.toString())) {
            setBizTypeFilter(input, BizTypeEnums.A);
        }
        setDefaultFlightStatus(input);
        CustomStatisticsDataHandler handler = getStatisticsDataHandler(input.getBizTypeEnums());
        Map<String, String> mapLog = logTagContext.getMap();
        ReportUserDefinedOutput output = handler.statistics(input, uid, mapLog);
        return output;
    }

    private void setDefaultFlightStatus(ReportUserDefinedInput input) {
        List<Filter> filterList = input.getFilterList();
        //默认成交
        if (CollectionUtils.isNotEmpty(filterList)) {
            if (filterList.stream().anyMatch(i -> i.getName().equalsIgnoreCase(DimensionEnum.PRODUCTLINE.getName()) && i.getValue() != null && i.getValue().contains(BizTypeEnums.F.toString()))
                    || filterList.stream().anyMatch(i -> i.getName().equalsIgnoreCase("bizType") && i.getValue() != null && i.getValue().contains(BizTypeEnums.F.toString()))) {
                if (filterList.stream().noneMatch(i -> i.getName().equalsIgnoreCase(DimensionEnum.FLT_FLIGHTSTATUS.getName()))) {
                    Filter flightStatusFilter = new Filter();
                    flightStatusFilter.setName(DimensionEnum.FLT_FLIGHTSTATUS.getName());
                    flightStatusFilter.setValue(Arrays.asList(BusinessConst.FLIGHT_STATUS_CJ));
                    filterList.add(flightStatusFilter);
                }
            }
        }

    }

    private void setBizType(ReportUserDefinedInput input) {
        List<String> dimensionList = input.getDimensionList();
        List<String> statisticList = input.getStatisticalList();
        List<String> temp = new ArrayList();
        temp.addAll(dimensionList);
        temp.addAll(statisticList);
        for (String key : temp) {
            if (key.startsWith("htl")) {
                setBizTypeFilter(input, BizTypeEnums.H);
                input.setBizTypeEnums(BizTypeEnums.H);
                break;
            } else if (key.startsWith("flt")) {
                setBizTypeFilter(input, BizTypeEnums.F);
                input.setBizTypeEnums(BizTypeEnums.F);
                break;
            } else if (key.startsWith("train")) {
                setBizTypeFilter(input, BizTypeEnums.T);
                input.setBizTypeEnums(BizTypeEnums.T);
                break;
            } else if (key.startsWith("car")) {
                setBizTypeFilter(input, BizTypeEnums.C);
                input.setBizTypeEnums(BizTypeEnums.C);
                break;
            } else {
                input.setBizTypeEnums(BizTypeEnums.A);
                continue;
            }
        }
    }


    private void setBizTypeFilter(ReportUserDefinedInput input, BizTypeEnums bizTypeEnums) {
        if (StringUtils.equalsIgnoreCase(bizTypeEnums.toString(), BizTypeEnums.A.toString())) {
            Filter filter = new Filter();
            filter.setName("bizType");
            if (input.getFilterList().stream().anyMatch(i -> StringUtils.equalsIgnoreCase(i.getName(), DimensionEnum.PRODUCTLINE.getName()))) {
                filter.setValue(input.getFilterList().stream().filter(i -> StringUtils.equalsIgnoreCase(i.getName(), DimensionEnum.PRODUCTLINE.getName())).findFirst().get().getValue());
            } else {
                filter.setValue(Arrays.asList(BizTypeEnums.F.toString(), BizTypeEnums.T.toString(), BizTypeEnums.C.toString(), BizTypeEnums.H.toString()));
            }
            if (CollectionUtils.isNotEmpty(input.getFilterList())) {
                input.getFilterList().add(filter);
            } else {
                List<Filter> list = new ArrayList<>();
                list.add(filter);
                input.setFilterList(list);
            }
        } else {
            if (CollectionUtils.isNotEmpty(input.getFilterList())) {
                Filter filter = new Filter();
                filter.setName("bizType");
                filter.setValue(Arrays.asList(bizTypeEnums.toString()));
                input.getFilterList().add(filter);
            } else {
                List<Filter> list = new ArrayList<>();
                Filter filter = new Filter();
                filter.setName("bizType");
                filter.setValue(Arrays.asList(bizTypeEnums.toString()));
                list.add(filter);
                input.setFilterList(list);
            }
        }

    }

}
*/
