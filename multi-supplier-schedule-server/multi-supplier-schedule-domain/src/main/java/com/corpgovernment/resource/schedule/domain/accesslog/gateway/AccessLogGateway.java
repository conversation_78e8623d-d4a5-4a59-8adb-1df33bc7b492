package com.corpgovernment.resource.schedule.domain.accesslog.gateway;

import com.corpgovernment.resource.schedule.domain.accesslog.model.BookingFaq;
import com.corpgovernment.resource.schedule.domain.accesslog.model.Top5Info;
import com.corpgovernment.resource.schedule.domain.accesslog.model.Top5InfoTraffic;
import com.corpgovernment.resource.schedule.domain.accesslog.model.req.ReportQueryReq;
import com.corpgovernment.resource.schedule.domain.accesslog.model.resp.Top5InfoResp;

import java.util.Collection;
import java.util.List;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface AccessLogGateway {

    Top5Info queryBYD(@Valid ReportQueryReq reportQueryReq);

    List<BookingFaq> queryQuestion(Collection<Integer> questionIds);

    List<Top5InfoResp> query(ReportQueryReq reportQueryReq, String flight);

    List<Top5InfoTraffic> queryExcelBYD(@Valid ReportQueryReq reportQueryReq);
}
