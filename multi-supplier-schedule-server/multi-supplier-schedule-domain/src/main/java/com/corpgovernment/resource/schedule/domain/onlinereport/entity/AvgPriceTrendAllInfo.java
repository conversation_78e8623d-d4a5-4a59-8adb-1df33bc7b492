package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "avgPriceTrendInfoDom",
    "avgPriceTrendInfoInter"
})
public class AvgPriceTrendAllInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public AvgPriceTrendAllInfo(
        AvgPriceTrendInfo avgPriceTrendInfoDom,
        AvgPriceTrendInfo avgPriceTrendInfoInter) {
        this.avgPriceTrendInfoDom = avgPriceTrendInfoDom;
        this.avgPriceTrendInfoInter = avgPriceTrendInfoInter;
    }

    public AvgPriceTrendAllInfo() {
    }

    /**
     * 国内
     */
    @JsonProperty("avgPriceTrendInfoDom")
    public AvgPriceTrendInfo avgPriceTrendInfoDom;

    /**
     * 国际
     */
    @JsonProperty("avgPriceTrendInfoInter")
    public AvgPriceTrendInfo avgPriceTrendInfoInter;

    /**
     * 国内
     */
    public AvgPriceTrendInfo getAvgPriceTrendInfoDom() {
        return avgPriceTrendInfoDom;
    }

    /**
     * 国内
     */
    public void setAvgPriceTrendInfoDom(final AvgPriceTrendInfo avgPriceTrendInfoDom) {
        this.avgPriceTrendInfoDom = avgPriceTrendInfoDom;
    }

    /**
     * 国际
     */
    public AvgPriceTrendInfo getAvgPriceTrendInfoInter() {
        return avgPriceTrendInfoInter;
    }

    /**
     * 国际
     */
    public void setAvgPriceTrendInfoInter(final AvgPriceTrendInfo avgPriceTrendInfoInter) {
        this.avgPriceTrendInfoInter = avgPriceTrendInfoInter;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final AvgPriceTrendAllInfo other = (AvgPriceTrendAllInfo)obj;
        return
            Objects.equal(this.avgPriceTrendInfoDom, other.avgPriceTrendInfoDom) &&
            Objects.equal(this.avgPriceTrendInfoInter, other.avgPriceTrendInfoInter);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.avgPriceTrendInfoDom == null ? 0 : this.avgPriceTrendInfoDom.hashCode());
        result = 31 * result + (this.avgPriceTrendInfoInter == null ? 0 : this.avgPriceTrendInfoInter.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("avgPriceTrendInfoDom", avgPriceTrendInfoDom)
            .add("avgPriceTrendInfoInter", avgPriceTrendInfoInter)
            .toString();
    }
}
