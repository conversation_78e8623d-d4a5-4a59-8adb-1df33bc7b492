package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import lombok.Data;

import java.math.BigDecimal;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
public class TrainDeptDTO {

    private String aggId;

    private String aggType;

    private BigDecimal totalAmount;

    private Integer totalQuantity;

    private Integer totalRefundtkt;

    private Integer totalRebooktkt;

    private Integer totalOrdertkt;

    private BigDecimal avgPrice;

    private BigDecimal rebookRate;

    private BigDecimal refundRate;

    private BigDecimal totalPrice;

    private Integer totalAllOrderCount;

    private String hotLine;

    private String hotSeat;

    private BigDecimal totalCarbons;
}
