package com.corpgovernment.resource.schedule.domain.onlinereport.utils;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/*
 * <AUTHOR>
 * @date 2020/10/9 11:42
 * @Desc
 */
@Component
public final class SpringUtil implements ApplicationContextAware {

    /**
     * Spring application context
     */
    private static volatile ApplicationContext applicationContext;

    private SpringUtil() {
    }

    /**
     * 创建spring工具类的实例
     *
     * @param applicationContext
     * @return
     */
    public static void initializeSpringApplicationContext(ApplicationContext applicationContext) {
        SpringUtil.initApplicationContext(applicationContext);
    }

    /**
     * 根据类型获取实例, initializeSpringApplicationContext(ApplicationContext applicationContext)初始化spring容器
     *
     * @param type
     * @return
     */
    public static <T> T getBean(Class<T> type) {
        if (SpringUtil.applicationContext == null) {
            throw new IllegalArgumentException("SpringUtil.getBean(Class<T> type) exception.");
        }

        return applicationContext.getBean(type);
    }


    /**
     * Set the ApplicationContext that this object runs in.
     *
     * @param applicationContext the ApplicationContext object to be used by this object
     * @throws BeansException if thrown by application context methods
     * @see BeanInitializationException
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtil.initApplicationContext(applicationContext);
    }

    /**
     * 初始化spring上下文
     *
     * @param applicationContext
     */
    private static void initApplicationContext(ApplicationContext applicationContext) {
        if (SpringUtil.applicationContext == null) {
            synchronized (SpringUtil.class) {
                if (SpringUtil.applicationContext == null) {
                    SpringUtil.applicationContext = applicationContext;
                }
            }
        }
    }
}
