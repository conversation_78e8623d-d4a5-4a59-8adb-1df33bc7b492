package com.corpgovernment.resource.schedule.domain.onlinereport.config;

import com.corpgovernment.resource.schedule.domain.hotel.model.SupplierControl;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/3 15:56
 * @description
 */
@Configuration
@Data
@Slf4j
public class CommonConfig {

    @Value("${supplier.monitor:}")
    private String supplierMonitorJson;

    @Bean
    public SupplierMonitorDto getSupplierMonitor() {
        if (StringUtils.isBlank(supplierMonitorJson)) {
            return new SupplierMonitorDto();
        }
        try {
            return JsonUtils.parse(supplierMonitorJson, new TypeReference<SupplierMonitorDto>() {});
        } catch (Exception e) {
            log.error("getSupplierControlMap失败", e);
            return new SupplierMonitorDto();
        }
    }
}
