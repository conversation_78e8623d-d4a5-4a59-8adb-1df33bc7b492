package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "trendInfos",
    "totalPersons"
})
public class OnlineReportCorpCustInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportCorpCustInfo(
        List<OnlineReportCorpCustTrendInfo> trendInfos,
        Integer totalPersons) {
        this.trendInfos = trendInfos;
        this.totalPersons = totalPersons;
    }

    public OnlineReportCorpCustInfo() {
    }

    @JsonProperty("trendInfos")
    public List<OnlineReportCorpCustTrendInfo> trendInfos;

    @JsonProperty("totalPersons")
    public Integer totalPersons;

    public List<OnlineReportCorpCustTrendInfo> getTrendInfos() {
        return trendInfos;
    }

    public void setTrendInfos(final List<OnlineReportCorpCustTrendInfo> trendInfos) {
        this.trendInfos = trendInfos;
    }
    public Integer getTotalPersons() {
        return totalPersons;
    }

    public void setTotalPersons(final Integer totalPersons) {
        this.totalPersons = totalPersons;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportCorpCustInfo other = (OnlineReportCorpCustInfo)obj;
        return
            Objects.equal(this.trendInfos, other.trendInfos) &&
            Objects.equal(this.totalPersons, other.totalPersons);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.trendInfos == null ? 0 : this.trendInfos.hashCode());
        result = 31 * result + (this.totalPersons == null ? 0 : this.totalPersons.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("trendInfos", trendInfos)
            .add("totalPersons", totalPersons)
            .toString();
    }
}
