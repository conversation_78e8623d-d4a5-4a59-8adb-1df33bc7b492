package com.corpgovernment.resource.schedule.domain.onlinereport.task;

import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.ReportBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.cglib.seriallizer.JacksonUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.CommonConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpReportConditionPO;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpreportDownloadTaskPO;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.dao.CorpreportDownloadTaskDAOExt;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.dao.ReportConditionDAOExt;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TimeFilterTypeInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.DateUtil;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.JsonUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.ConditionType;
import onlinereport.enums.PageErrorCodeEnum;
import onlinereport.enums.ReportTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/*
 * <AUTHOR>
 * @date 2019/11/28 13:11
 * @Desc
 */
@Service
@Slf4j
public class CorpreportDownloadTaskService {

    protected static final String PREFIX = CorpreportDownloadTaskService.class.getSimpleName();
    @Resource
    private CorpreportDownloadTaskDAOExt corpreportDownloadTaskDAOExt;

    @Resource
    private ReportConditionDAOExt reportConditionDAOExt;

  /*  @Resource
    private TbPrivilegeUserDAOExt tbPrivilegeUserDAOExt;*/


    /**
     * 获得reporName
     *
     * @param lang
     * @param reportId
     * @return
     */
    public static String getReportName(String lang, String reportId) {
        String reportName = null;
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:FltOrderDetails", reportId)) {
            reportName = SharkUtils.get("Report.airticketorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:UnUseFltTicketDetails", reportId)) {
            reportName = SharkUtils.get("Report.nouseairticket", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:FltTicketRefundDetails", reportId)) {
            reportName = SharkUtils.get("Report.refundticketorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:FltTicketRebookDetails", reportId)) {
            reportName = SharkUtils.get("Report.changeticketorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:HotelOrderDetails", reportId)) {
            reportName = SharkUtils.get("Report.hotelorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:TrainOrderDetails", reportId)) {
            reportName = SharkUtils.get("Report.trainorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:CarOrderDetails", reportId)) {
            reportName = SharkUtils.get("Report.carorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:CityHotelAnalysis", reportId)) {
            reportName = SharkUtils.get("Report.cityhotel", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:ConsumeOverview", reportId)) {
            reportName = SharkUtils.get("Report.cost", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:OneKeyPPT", reportId)) {
            reportName = SharkUtils.get("PPT.reportname", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:OneKeyReport", reportId)) {
            reportName = "ExcelReport";
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:UidOrderDetails", reportId)) {
            reportName = SharkUtils.get("index.libemployee", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:HotelOrderAuditDetails", reportId)) {
            reportName = SharkUtils.get("reportlib.hotelaudit", lang);
        }
        if (StringUtils.isEmpty(reportName)) {
            reportName = "orderdetail";
        }
        //文件名中的特殊字符
        if (reportName.contains("/")) {
            reportName = reportName.replace("/", "_");
        }
        if (reportName.contains(" ")) {
            reportName = reportName.replace(" ", "");
        }
        return reportName;
    }

    /**
     * 获得reporName
     *
     * @param lang
     * @param reportId
     * @return
     */
    public static String getReportNameBluespace(String lang, String reportId) {
        String reportName = null;
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:FltOrderDetails", reportId)) {
            reportName = SharkUtils.getBluespace("Report.airticketorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:UnUseFltTicketDetails", reportId)) {
            reportName = SharkUtils.getBluespace("Report.nouseairticket", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:FltTicketRefundDetails", reportId)) {
            reportName = SharkUtils.getBluespace("Report.refundticketorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:FltTicketRebookDetails", reportId)) {
            reportName = SharkUtils.getBluespace("Report.changeticketorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:HotelOrderDetails", reportId)) {
            reportName = SharkUtils.getBluespace("Report.hotelorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:TrainOrderDetails", reportId)) {
            reportName = SharkUtils.getBluespace("Report.trainorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:CarOrderDetails", reportId)) {
            reportName = SharkUtils.getBluespace("Report.carorder", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:CityHotelAnalysis", reportId)) {
            reportName = SharkUtils.getBluespace("Report.cityhotel", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:ConsumeOverview", reportId)) {
            reportName = SharkUtils.getBluespace("Report.cost", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:OneKeyPPT", reportId)) {
            reportName = SharkUtils.getBluespace("PPT.reportname", lang);
        } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase("DetailReport:OneKeyReport", reportId)) {
            reportName = "ExcelReport";
        }
        if (StringUtils.isEmpty(reportName)) {
            reportName = "orderdetail";
        }
        //文件名中的特殊字符
        if (reportName.contains("/")) {
            reportName = reportName.replace("/", "_");
        }
        if (reportName.contains(" ")) {
            reportName = reportName.replace(" ", "");
        }
        return reportName;
    }

    /**
     * 获得reporName时间后缀
     *
     * @param baseQueryCondition
     * @param reportId
     * @return
     */
    public static String getReportTimeSufix(BaseQueryCondition baseQueryCondition, String reportId) {
        String reportTimeSufix = StringUtils.EMPTY;
        if (StringUtils.equalsIgnoreCase("DetailReport:ConsumeOverview", reportId)) {
            reportTimeSufix = getReportTimeSufix(baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime());
        } else if (StringUtils.equalsIgnoreCase("DetailReport:OneKeyPPT", reportId)) {
            reportTimeSufix = getReportPPTTimeSufix(baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime());
        } else if (StringUtils.equalsIgnoreCase("DetailReport:OneKeyReport", reportId)) {
            reportTimeSufix = getReportOneKeyReportTimeSufix(baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime());
        } else {
            reportTimeSufix = getReportTimeSufix(baseQueryCondition.getTimeFilterList());
        }
        return reportTimeSufix;
    }

    /**
     * 获得reporName时间后缀
     *
     * @param timeFilterTypeInfoList
     * @return
     */
    public static String getReportTimeSufix(List<TimeFilterTypeInfo> timeFilterTypeInfoList) {
        String reportSufix = StringUtils.EMPTY;
        TimeFilterTypeInfo timeFilterTypeInfo = null;
        Optional optional1 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "orderdate"))
                .findFirst();
        Optional optional2 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "dealdate"))
                .findFirst();
        Optional optional3 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "usedate"))
                .findFirst();
        if (optional2.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional2.get();
        } else if (optional1.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional1.get();
        } else if (optional3.isPresent()) {
            timeFilterTypeInfo = (TimeFilterTypeInfo) optional3.get();
        }
        String startStr = timeFilterTypeInfo.getStartTime().replace("-", "");
        String endStr = timeFilterTypeInfo.getEndTime().replace("-", "");
        reportSufix = String.format("%s-%s", startStr, endStr);
        return reportSufix;
    }

    /**
     * 获得reporName时间后缀
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static String getReportTimeSufix(String startTime, String endTime) {
        String reportSufix = StringUtils.EMPTY;
        String startStr = startTime.replace("-", "");
        String endStr = endTime.replace("-", "");
        reportSufix = String.format("%s-%s", startStr, endStr);
        return reportSufix;
    }

    public static String getReportPPTTimeSufix(String startTime, String endTime) {
        String reportSufix = StringUtils.EMPTY;
        reportSufix = String.format("%s-%s", startTime, endTime);
        return reportSufix;
    }

    public static String getReportOneKeyReportTimeSufix(String startTime, String endTime) {
        return startTime.replace("-", "");
    }

    /**
     * 时间参数校验
     *
     * @param baseQueryCondition
     * @throws BusinessException
     */
    public static void timeVerify(BaseQueryCondition baseQueryCondition) throws BusinessException {
        if (StringUtils.equalsIgnoreCase("DetailReport:ConsumeOverview", baseQueryCondition.getReportId())
                || StringUtils.equalsIgnoreCase("DetailReport:OneKeyPPT", baseQueryCondition.getReportId())
                || StringUtils.equalsIgnoreCase("DetailReport:OneKeyReport", baseQueryCondition.getReportId())) {
            if (StringUtils.isEmpty(baseQueryCondition.getStartTime()) && StringUtils.isEmpty(baseQueryCondition.getEndTime())) {
                throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            }
            if (StringUtils.equalsIgnoreCase("DetailReport:OneKeyPPT", baseQueryCondition.getReportId())
                    || StringUtils.equalsIgnoreCase("DetailReport:OneKeyReport", baseQueryCondition.getReportId())) {
                int intervalMonths = DateUtil.getMonthNum(baseQueryCondition.getStartTime(), baseQueryCondition.getEndTime());
                // DetailReport:OneKeyReport endTime可以为空，startTime不能为空，endTime为空时，startTime与当前时间差不能超过24月 todo
                if (intervalMonths + 1 > 24) {
                    throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
                }
            }
        } else {
            List<TimeFilterTypeInfo> timeFilterTypeInfoList = baseQueryCondition.getTimeFilterList();
            Optional optional1 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "orderdate"))
                    .findFirst();
            Optional optional2 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "dealdate"))
                    .findFirst();
            Optional optional3 = Optional.ofNullable(timeFilterTypeInfoList).orElse(new ArrayList<>()).stream()
                    .filter(i -> StringUtils.equalsIgnoreCase(i.getTimeType(), "usedate"))
                    .findFirst();
            if (!optional1.isPresent() && !optional2.isPresent() && !optional3.isPresent()) {
                throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            }
        }
    }

    public static void paramVerify(BaseQueryConditionBO baseQueryConditionBO) throws BusinessException {
        BaseQueryCondition baseQueryCondition = baseQueryConditionBO.getBaseQueryCondition();
        if (StringUtils.equalsIgnoreCase("DetailReport:UidOrderDetails", baseQueryCondition.getReportId())) {
            if (CollectionUtils.isEmpty(baseQueryConditionBO.getUsers()) && CollectionUtils.isEmpty(baseQueryConditionBO.getPassengers())) {
                throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            }
        }
    }

    /**
     * @param reportId
     * @return
     */
    public static ReportTypeEnum getReportType(String reportId) {
        ReportTypeEnum reportTypeEnum = null;
        if (StringUtils.equalsIgnoreCase("DetailReport:FltOrderDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:UnUseFltTicketDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:FltTicketRefundDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:FltTicketRebookDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:HotelOrderDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:TrainOrderDetails", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:CarOrderDetails", reportId)) {
            reportTypeEnum = ReportTypeEnum.ReportLibNew;
        } else if (StringUtils.equalsIgnoreCase("DetailReport:CityHotelAnalysis", reportId) ||
                StringUtils.equalsIgnoreCase("DetailReport:ConsumeOverview", reportId)) {
            reportTypeEnum = ReportTypeEnum.ReportLibAgg;
        } else if (StringUtils.equalsIgnoreCase("DetailReport:OneKeyPPT", reportId)) {
            reportTypeEnum = ReportTypeEnum.OneKeyExportPPT;
        } else if (StringUtils.equalsIgnoreCase("DetailReport:OneKeyReport", reportId)) {
            reportTypeEnum = ReportTypeEnum.OneKeyExportReportNew;
        } else if (StringUtils.equalsIgnoreCase("DetailReport:UidOrderDetails", reportId)) {
            reportTypeEnum = ReportTypeEnum.ReportLibNewUid;
        } else if (StringUtils.equalsIgnoreCase("DetailReport:HotelOrderAuditDetails", reportId)) {
            reportTypeEnum = ReportTypeEnum.ReportLibHtlOrderAudit;
        }
        return reportTypeEnum;
    }

    public List<CorpreportDownloadTaskPO> queryBy(CorpreportDownloadTaskPO sample) {
 /*       List<CorpreportDownloadTaskPO> result = new ArrayList<>();
        try {

            result = corpreportDownloadTaskDAOExt.queryBy(sample);
        } catch (SQLException e) {
            log.error(PREFIX + "queryBy", e);
        }
        return result;*/
        return null;
    }

    public CorpreportDownloadTaskPO queryByPk(long taskId) {
/*        try {
            CorpreportDownloadTaskPO result = corpreportDownloadTaskDAOExt.queryByPk(taskId);
            return result;
        } catch (SQLException e) {
            log.error(PREFIX + "queryByPk", e);
        }*/
        return null;
    }

    /**
     * 下载任务更新（不包括condition）
     *
     * @param sample
     * @return
     * @throws SQLException
     */
    public int update(CorpreportDownloadTaskPO sample) throws SQLException {
        //return corpreportDownloadTaskDAOExt.update(sample);

        return 0;

    }

    /**
     * * 新增下载任务更新（包括condition）
     *
     * @param reportBO
     * @return taskId 任务id
     * @throws SQLException
     */
    public Long addTask(ReportBO reportBO, String lang) throws SQLException {
        int count = 0;
        CorpreportDownloadTaskPO sample = new CorpreportDownloadTaskPO();
        sample.setTaskStatus(0);
        sample.setValid(true);
        sample.setReportType(reportBO.getReportType());
        sample.setUid(reportBO.getUid());
        sample.setConditions(reportBO.getReportConditionExt());
        sample.setLang(lang);
/*        KeyHolder keyHolder = new KeyHolder();
        count = corpreportDownloadTaskDAOExt.insertWithKeyHolder(keyHolder, sample);
        Long taskId = keyHolder.getKey().longValue();
        reportConditionDAOExt.insertBatch(taskId, ConditionType.DOWNLOADTASK.getCode(), reportBO.getReportCondition());
        return taskId;*/
        return null;
    }

    public List<CorpreportDownloadTaskPO> queryTask(String uid, Timestamp timestamp) throws SQLException {
        List<CorpreportDownloadTaskPO> corpreportDownloadTaskPOList = corpreportDownloadTaskDAOExt.queryTaskByUID(uid, timestamp);
        return corpreportDownloadTaskPOList;
    }

    /**
     * 根据taskStatus,reportType查询当天的下载任务
     *
     * @param taskStatusList
     * @param reportTypeList
     * @return
     * @throws SQLException
     */
    public List<CorpreportDownloadTaskPO> queryCurrentTaskByTaskStatus(List<Integer> taskStatusList, List<Integer> reportTypeList) throws SQLException {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Timestamp timestamp = new Timestamp(calendar.getTimeInMillis());
        List<CorpreportDownloadTaskPO> corpreportDownloadTaskPOList = corpreportDownloadTaskDAOExt.queryTaskByTaskStatus(taskStatusList, reportTypeList, timestamp);
        return corpreportDownloadTaskPOList;
    }

    /**
     * 根据taskStatus查询24小时内的下载任务
     *
     * @param taskStatusList
     * @return
     * @throws SQLException
     */
    public List<CorpreportDownloadTaskPO> queryTaskByTaskStatus(List<Integer> taskStatusList) throws SQLException {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -24);
        Timestamp timestamp = new Timestamp(calendar.getTimeInMillis());
        List<CorpreportDownloadTaskPO> corpreportDownloadTaskPOList = corpreportDownloadTaskDAOExt.queryTaskByTaskStatus(taskStatusList, null, timestamp);
        return corpreportDownloadTaskPOList;
    }

    /**
     * 是否有条件相同的有效的任务在下载
     *
     * @param uid        卡号
     * @param conditions 条件
     * @return
     * @throws SQLException
     */
    public long hasSameTaskRunning(String uid, String conditions) throws SQLException {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 1);
        Timestamp timestamp = new Timestamp(calendar.getTimeInMillis());
        long reportId = 0;
        List<CorpreportDownloadTaskPO> corpreportDownloadTaskPOList = corpreportDownloadTaskDAOExt.queryTaskByUID(uid, timestamp);
        for (CorpreportDownloadTaskPO item : corpreportDownloadTaskPOList) {
            long taskId = item.getTaskId();
            List<CorpReportConditionPO> reportConditionPOList = reportConditionDAOExt.queryCondiotionByTaskAndType(taskId, ConditionType.DOWNLOADTASK.getCode());
            if (CollectionUtils.isNotEmpty(reportConditionPOList)) {
                StringBuilder sb = new StringBuilder();
                for (CorpReportConditionPO corpReportConditionPO : reportConditionPOList) {
                    sb.append(corpReportConditionPO.getReportCondition());
                }
                if (StringUtils.equalsIgnoreCase(conditions, sb.toString()) && item.getTaskStatus() == 1) {
                    reportId = item.getTaskId();
                    return reportId;
                }
            }
        }
        return reportId;
    }

    /**
     * 条件相同的有效的任务数量
     *
     * @param uid
     * @param conditions
     * @return
     * @throws SQLException
     */
    public int queryTaskCount(String uid, String conditions) throws SQLException {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 1);
        Timestamp timestamp = new Timestamp(calendar.getTimeInMillis());
        int result = 0;
        List<CorpreportDownloadTaskPO> corpreportDownloadTaskPOList = corpreportDownloadTaskDAOExt.queryTaskByUID(uid, timestamp);
        for (CorpreportDownloadTaskPO item : corpreportDownloadTaskPOList) {
            // 下载失败
            if (item.getTaskStatus() == 3) {
                continue;
            }
            long taskId = item.getTaskId();
            List<CorpReportConditionPO> reportConditionPOList = reportConditionDAOExt.queryCondiotionByTaskAndType(taskId, ConditionType.DOWNLOADTASK.getCode());
            if (CollectionUtils.isNotEmpty(reportConditionPOList)) {
                StringBuilder sb = new StringBuilder();
                for (CorpReportConditionPO corpReportConditionPO : reportConditionPOList) {
                    sb.append(corpReportConditionPO.getReportCondition());
                }
                if (StringUtils.equalsIgnoreCase(conditions, sb.toString())) {
                    result++;
                }
            }
        }
        return result;
    }

    public String getReportNameByPrefix(String reportCondition, long taskId, int reportType, long createTime) {
        String fileName = null;
        String suffix = String.valueOf(taskId);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readValue(reportCondition, JsonNode.class);
            JsonNode jsonNode1 = jsonNode.get(CommonConst.REPORTPREFIX_KEY);
            String reportPrefix = jsonNode1.asText();
            String randomStr = getRandomStr(createTime);
            if (StringUtils.isNotEmpty(randomStr) && StringUtils.isNotBlank(randomStr)) {
                suffix = String.format("%s%d", randomStr, taskId);
            }
            if (StringUtils.isNotEmpty(reportPrefix)) {
                if (reportType == ReportTypeEnum.OneKeyExportReport.getValue() || reportType == ReportTypeEnum.ReportLibAgg.getValue()
                        || reportType == ReportTypeEnum.OneKeyExportReportNew.getValue()) {
                    fileName = String.format("%s_%s.xls", reportPrefix, suffix);
                } else if (reportType == ReportTypeEnum.OneKeyExportPPT.getValue()) {
                    //公司ID_差旅分析报告_查询开始时间-结束时间
                    fileName = reportPrefix;
                } else {
                    fileName = String.format("%s_%s.xlsx", reportPrefix, suffix);
                }
            }
        } catch (Exception e) {
            log.warn(PREFIX + "getReportNameByPrefix", e);
        }
        if (StringUtils.isEmpty(fileName)) {
            if (reportType == ReportTypeEnum.OneKeyExportReport.getValue() || reportType == ReportTypeEnum.ReportLibAgg.getValue()) {
                fileName = String.format("%s.xls", suffix);
            } else if (reportType == ReportTypeEnum.OneKeyExportPPT.getValue()) {
                fileName = String.format("%s", suffix);
            } else {
                fileName = String.format("%s.xlsx", suffix);
            }
        }
        return fileName;
    }

    public String getReportNamePrefix(String uid, int reportType, String reportCondition, String lang) {
/*        String reportPrefix = null;
        String pre_prefix = null;
        try {
            pre_prefix = getCorpId(uid);
            if (ReportTypeEnum.ReportLib.getValue() == reportType || ReportTypeEnum.ReportLibNew.getValue() == reportType) {
                ReportLibFilterBO reportLibFilterBO = (ReportLibFilterBO) JacksonUtil.deserialize(reportCondition, ReportLibFilterBO.class);
                if (StringUtils.isNotEmpty(reportLibFilterBO.getBizType()) || StringUtils.isNotEmpty(reportLibFilterBO.getQueryType())) {
                    String bizType = reportLibFilterBO.getBizType();
                    String queryType = reportLibFilterBO.getQueryType();
                    long startTime = reportLibFilterBO.getStartTime();
                    long endTime = reportLibFilterBO.getEndTime();
                    String startStr = DateFormatUtils.format(startTime, "yyyyMMdd");
                    String endStr = DateFormatUtils.format(endTime, "yyyyMMdd");
                    String reportName = ExcelSheetNameUtil.getReportName(bizType, queryType, lang);
                    if (StringUtils.isNotEmpty(pre_prefix)) {
                        reportPrefix = String.format("%s%s%s%s", pre_prefix.toUpperCase(), reportName, startStr, endStr);
                    } else {
                        reportPrefix = String.format("%s%s%s", reportName, startStr, endStr);
                    }
                }
            }
            if (ReportTypeEnum.OneKeyExportReport.getValue() == reportType) {
                QueryGeneralReportBo queryGeneralReportBo = (QueryGeneralReportBo) JacksonUtil.deserialize(reportCondition, QueryGeneralReportBo.class);
                String startTime = Objects.toString(queryGeneralReportBo.getFilterList().get("startTime"), null);
                if (StringUtils.isNotEmpty(startTime)) {
                    String startStr = DateFormatUtils.format(Long.valueOf(startTime), "yyyyMMdd");
                    reportPrefix = String.format("%s%s", "ExcelReport", startStr);
                } else {
                    reportPrefix = String.format("%s", "ExcelReport");
                }
            }
        } catch (Exception e) {
            log.warn(PREFIX + "getReportName", e);
        }
        return reportPrefix;*/
        return null;
    }

    public String getNewReportNamePrefix(String uid, BaseQueryConditionBO baseQueryConditionBO, String lang) {
        String reportPrefix = null;
        String pre_prefix = null;
        try {
            pre_prefix = getCorpId(uid);
            String timeSufix = getReportTimeSufix(baseQueryConditionBO.getBaseQueryCondition(), baseQueryConditionBO.getBaseQueryCondition().getReportId());
            String reportName = CorpreportDownloadTaskService.getReportName(lang, baseQueryConditionBO.getBaseQueryCondition().getReportId());
            if (StringUtils.isNotEmpty(pre_prefix)) {
                reportPrefix = String.format("%s-%s_%s", pre_prefix.toUpperCase(), reportName, timeSufix);
            } else {
                reportPrefix = String.format("%s_%s", reportName, timeSufix);
            }
        } catch (Exception e) {
            log.warn(PREFIX + "getReportName", e);
        }
        return reportPrefix;
    }

    /**
     * 根据time获得一个字符串
     *
     * @param time
     * @return
     */
    public String getRandomStr(long time) {
        String numStr = null;
        String str = String.valueOf(time);
        int length = str.length();
        numStr = str;
        if (length > 2) {
            numStr = str.substring(length - 2, length);
        }
        char[] buffer = new char[4];
        for (int i = 0; i < 4; i++) {
            time--;
            buffer[i] = (char) (96 + time % 26 + 1);
        }
        return new String(buffer) + numStr;
    }

    public String getCorpId(String uid) {
/*        String pre_prefix = null;
        try {
            List<TbPrivilegeUserPO> tbPrivilegeUserPOList = tbPrivilegeUserDAOExt.queryUserInfoByUid(uid);
            if (CollectionUtils.isNotEmpty(tbPrivilegeUserPOList)) {
                TbPrivilegeUserPO tbPrivilegeUserPO = tbPrivilegeUserPOList.get(0);
                pre_prefix = StringUtils.isEmpty(tbPrivilegeUserPO.getCorp_id()) ? StringUtils.EMPTY : tbPrivilegeUserPO.getCorp_id().trim();
            }
        } catch (Exception e) {
            log.warn(PREFIX + "getCorpId", e);
        }
        return pre_prefix;*/
        return null;
    }

    /**
     * 更新任务状态为失败
     *
     * @param ids
     * @return
     * @throws SQLException
     */
    public void delete(List<Long> ids) throws SQLException {
        for (long id : ids) {
            delete(id);
        }
    }

    /**
     * 更新任务状态为失败
     *
     * @param id
     * @return
     * @throws SQLException
     */
    public int delete(Long id) throws SQLException {
/*        CorpreportDownloadTaskPO sample = corpreportDownloadTaskDAOExt.queryByPk(id);
        if (sample == null) {
            return 0;
        }
        sample.setValid(false);
        sample.setDatachangeLasttime(new Timestamp(new Date().getTime()));
        return corpreportDownloadTaskDAOExt.update(sample);*/

        return 0;
    }

    /**
     * 删除任务
     *
     * @param taskId
     * @param uid
     * @return
     * @throws BusinessException
     * @throws SQLException
     */
    public boolean delete(Long taskId, String uid) throws BusinessException, SQLException {
        CorpreportDownloadTaskPO corpreportDownloadTaskPO = queryByPk(taskId);
        if (corpreportDownloadTaskPO == null) {
            throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
        }
        if (!StringUtils.equalsIgnoreCase(uid, corpreportDownloadTaskPO.getUid())) {
            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), PageErrorCodeEnum.InvalidOperation.getDescription());
        }
        //运行状态是（0：准备下载，3：下载失败）可以删除
        if (corpreportDownloadTaskPO.getTaskStatus() == 0 || corpreportDownloadTaskPO.getTaskStatus() == 3) {
            return delete(taskId) > 0;
        }
        return false;
    }

    /**
     * current是否是最小时间间隔
     *
     * @param list
     * @param current
     * @return
     */
    public boolean isMinInvervalTime(List<CorpreportDownloadTaskPO> list, CorpreportDownloadTaskPO current) {
        int interval = getInterval(current.getConditions());
        for (CorpreportDownloadTaskPO item : list) {
            int tmp = getInterval(item.getConditions());
            if (interval > tmp) {
                return false;
            }
        }
        return true;
    }

    private int getInterval(String conditions) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Map<String, Object> jsonObject = objectMapper.readValue(conditions, new TypeReference<Map<String, Object>>() {
            });
            String startTime = Objects.toString(jsonObject.get("startTime"), null);
            String endTime = Objects.toString(jsonObject.get("endTime"), null);
            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                long start = Long.parseLong(startTime);
                long end = Long.parseLong(endTime);
                int interval = DateUtil.getIntervalMonths(start, end);
                return interval;
            }
        } catch (Exception e) {
            log.info(PREFIX + "isMinInvervalTime", e);
        }
        return 0;
    }

    /**
     * * 新增下载任务更新（包括condition）
     *
     * @param baseQueryConditionBO
     * @return taskId 任务id
     * @throws SQLException
     */
    public Long addTask(BaseQueryConditionBO baseQueryConditionBO, String lang, String uid) throws SQLException, JsonProcessingException, BusinessException {
        String reportCondition = JsonUtils.objectToString(baseQueryConditionBO);
        // 权限校验
        //basePermitVerfiyService.vaildPermit(uid, baseQueryConditionBO, lang);
        // 参数及其他条件校验
        addTaskVerify(uid, reportCondition, baseQueryConditionBO);
        baseQueryConditionBO.setLang(lang);
        Map temp = new HashMap();
        String reportPrefix = getNewReportNamePrefix(uid, baseQueryConditionBO, baseQueryConditionBO.getLang());
        temp.put(CommonConst.REPORTPREFIX_KEY, reportPrefix);
        ReportBO reportBO = new ReportBO();
        reportBO.setUid(uid);
        ReportTypeEnum reportTypeEnum = getReportType(baseQueryConditionBO.getBaseQueryCondition().getReportId());
        reportBO.setReportType(reportTypeEnum.getValue());
        reportBO.setReportCondition(reportCondition);
        reportBO.setLanguage(lang);
        reportBO.setReportConditionExt(JacksonUtil.serialize(temp));
        return addTask(reportBO, lang);
    }

    private void addTaskVerify(String uid, String reportCondition, BaseQueryConditionBO baseQueryConditionBO) throws BusinessException, SQLException {
        if (baseQueryConditionBO.getBaseQueryCondition() == null) {
            throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
        }
        timeVerify(baseQueryConditionBO.getBaseQueryCondition());
        paramVerify(baseQueryConditionBO);

        long reportId = hasSameTaskRunning(uid, StringUtils.trimToEmpty(reportCondition));
        if (reportId > 0) {
            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), SharkUtils.get("Report.Tip9", baseQueryConditionBO.getLang()));
        }
//        int maxCount = QConfigUtils.getMaxTaskCount();
//        long count = queryTaskCount(uid, StringUtils.trimToEmpty(reportCondition));
//        if (count >= maxCount) {//同一个报表当前不能超过3个任务
//            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), getMaxTaskTip(maxCount, baseQueryConditionBO.getLang()));
//        }
    }

    /**
     * 超过最大任务数量提示
     *
     * @param maxCount
     * @return
     */
    private String getMaxTaskTip(int maxCount, String lang) {
//        String tip = ChineseLanguageConfig.get(GlobalConst.SAMEREPORTMAXTASKCOUNTTIPS);
//        return String.format(tip,maxCount);

        return SharkUtils.get("Report.Tip9", lang);
    }


}
