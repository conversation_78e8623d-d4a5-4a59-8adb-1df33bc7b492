package com.corpgovernment.resource.schedule.domain.cleanup.gateway;

import com.corpgovernment.resource.schedule.domain.cleanup.model.TableInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DataCleanGateway {

    /**
     * 获取表数据
     */
    List<Map<String, Object>> getData(TableInfo tableInfo);


    void updateData(TableInfo table, List<Map<String, Object>> data);

    void log(TableInfo table, List<Map<String, Object>> oldData, List<Map<String, Object>> newData);
}
