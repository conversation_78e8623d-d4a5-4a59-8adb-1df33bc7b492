package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.hotel;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 酒店城市入住情况分析
 * <AUTHOR>
 * @Date 2019/4/3
 */
public class HotelCityCheckInAnalysisBO {
    /**
     * 所有
     */
    private List<HotelCityCheckInDetailBO> allAnalysisInfo;
    /**
     * 国内
     */
    private List<HotelCityCheckInDetailBO> domesticAnalysisInfo;
    /**
     * 港澳台
     */
    private List<HotelCityCheckInDetailBO> hongkongAnalysisInfo;
    /**
     * 海外
     */
    private List<HotelCityCheckInDetailBO> overseaAnalysisInfo;

    public List<HotelCityCheckInDetailBO> getAllAnalysisInfo() {
        return allAnalysisInfo;
    }

    public void setAllAnalysisInfo(List<HotelCityCheckInDetailBO> allAnalysisInfo) {
        this.allAnalysisInfo = allAnalysisInfo;
    }

    public List<HotelCityCheckInDetailBO> getDomesticAnalysisInfo() {
        return domesticAnalysisInfo;
    }

    public void setDomesticAnalysisInfo(List<HotelCityCheckInDetailBO> domesticAnalysisInfo) {
        this.domesticAnalysisInfo = domesticAnalysisInfo;
    }

    public List<HotelCityCheckInDetailBO> getHongkongAnalysisInfo() {
        return hongkongAnalysisInfo;
    }

    public void setHongkongAnalysisInfo(List<HotelCityCheckInDetailBO> hongkongAnalysisInfo) {
        this.hongkongAnalysisInfo = hongkongAnalysisInfo;
    }

    public List<HotelCityCheckInDetailBO> getOverseaAnalysisInfo() {
        return overseaAnalysisInfo;
    }

    public void setOverseaAnalysisInfo(List<HotelCityCheckInDetailBO> overseaAnalysisInfo) {
        this.overseaAnalysisInfo = overseaAnalysisInfo;
    }

    public HotelCityCheckInAnalysisBO(){
        allAnalysisInfo=new ArrayList<>();
        domesticAnalysisInfo=new ArrayList<>();
        hongkongAnalysisInfo=new ArrayList<>();
        overseaAnalysisInfo=new ArrayList<>();
    }
}
