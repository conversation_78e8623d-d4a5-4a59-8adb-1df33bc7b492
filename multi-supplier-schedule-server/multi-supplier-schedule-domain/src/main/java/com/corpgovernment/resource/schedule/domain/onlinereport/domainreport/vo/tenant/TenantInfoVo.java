package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.vo.tenant;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/9 22:23
 * @description
 */
@Data
public class TenantInfoVo {

    /**
     * 租户名字
     */
    private String tenantName;
    /**
     * 行业大类
     */
    private String stdIndustry1;
    /**
     * 行业小类
     */
    private String stdIndustry2;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    private String remark;
    /**
     * 租户id
     */
    private String tenantId;
}
