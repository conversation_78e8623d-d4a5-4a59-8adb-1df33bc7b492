package com.corpgovernment.resource.schedule.domain.onlinereport.travelposition;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.travelposition
 * @description:
 * @author: md_wang
 * @create: 2022-08-19 15:54
 **/
@Data
public class TravelPositionCityDTO {

    /**
     * 当前子行程城市
     */
    @Column(name = "sub_trip_city_id")
    @Type(value = Types.INTEGER)
    private Integer subTripCityId;
    @Column(name = "subTripCityName")
    @Type(value = Types.VARCHAR)
    private String subTripCityName;

    /**
     * 当前行程出发城市id
     */
    @Column(name = "start_trip_city_id")
    @Type(value = Types.INTEGER)
    private Integer startTripCityId;
    @Column(name = "startTripCityName")
    @Type(value = Types.VARCHAR)
    private String startTripCityName;

    /**
     * 当前行程最终到达城市
     */
    @Column(name = "end_trip_city_id")
    @Type(value = Types.INTEGER)
    private Integer endTripCityId;
    @Column(name = "end_trip_city_name")
    @Type(value = Types.VARCHAR)
    private String endTripCityName;

    /**
     * 一次行程子行程id
     */
    @Column(name = "sub_one_trip_id")
    @Type(value = Types.VARCHAR)
    private String subOneTripId;


    /**
     * 出发省份
     */
    @Column(name = "start_province_name")
    @Type(value = Types.VARCHAR)
    private String startProvinceName;

    /**
     * 到达省份
     */
    @Column(name = "end_province_name")
    @Type(value = Types.VARCHAR)
    private String endProvinceName;

    @Column(name = "end_province_id")
    @Type(value = Types.INTEGER)
    private Integer endProvinceId;


    /**
     * 当前子行程到达省份
     */
    @Column(name = "subTripProvinceName")
    @Type(value = Types.VARCHAR)
    private String subTripProvinceName;
    @Column(name = "sub_trip_province_id")
    @Type(value = Types.INTEGER)
    private Integer subTripProvinceId;

    /**
     * 工作省份
     */
    @Column(name = "corp_province_id")
    @Type(value = Types.INTEGER)
    private Integer corpProvinceId;

    @Column(name = "corp_province")
    @Type(value = Types.VARCHAR)
    private String corpProvince;

    /**
     * 一次行程子行程数量
     */
    @Column(name = "subTripTripCount")
    @Type(value = Types.INTEGER)
    private Integer subTripTripCount;



    /**
     * 当前子行程到达国家id
     */
    @Column(name = "sub_trip_city_countryid")
    @Type(value = Types.INTEGER)
    private Integer subTripCityCountryid;
    @Column(name = "subTripCityCountryname")
    @Type(value = Types.VARCHAR)
    private String subTripCityCountryname;
}
