package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 酒店-酒店星级分布
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "title",
    "starDistNotice",
    "legends",
    "data"
})
public class HotelStarDist implements Serializable {
    private static final long serialVersionUID = 1L;





    public HotelStarDist(
        String title,
        String starDistNotice,
        List<OnlineReportTrendLegend> legends,
        List<OnlineReportTrendPoint> data) {
        this.title = title;
        this.starDistNotice = starDistNotice;
        this.legends = legends;
        this.data = data;
    }

    public HotelStarDist() {
    }

    /**
     * 标题
     */
    @JsonProperty("title")
    public String title;

    /**
     * 文案
     */
    @JsonProperty("starDistNotice")
    public String starDistNotice;

    @JsonProperty("legends")
    public List<OnlineReportTrendLegend> legends;

    @JsonProperty("data")
    public List<OnlineReportTrendPoint> data;

    /**
     * 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 标题
     */
    public void setTitle(final String title) {
        this.title = title;
    }

    /**
     * 文案
     */
    public String getStarDistNotice() {
        return starDistNotice;
    }

    /**
     * 文案
     */
    public void setStarDistNotice(final String starDistNotice) {
        this.starDistNotice = starDistNotice;
    }
    public List<OnlineReportTrendLegend> getLegends() {
        return legends;
    }

    public void setLegends(final List<OnlineReportTrendLegend> legends) {
        this.legends = legends;
    }
    public List<OnlineReportTrendPoint> getData() {
        return data;
    }

    public void setData(final List<OnlineReportTrendPoint> data) {
        this.data = data;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final HotelStarDist other = (HotelStarDist)obj;
        return
            Objects.equal(this.title, other.title) &&
            Objects.equal(this.starDistNotice, other.starDistNotice) &&
            Objects.equal(this.legends, other.legends) &&
            Objects.equal(this.data, other.data);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.title == null ? 0 : this.title.hashCode());
        result = 31 * result + (this.starDistNotice == null ? 0 : this.starDistNotice.hashCode());
        result = 31 * result + (this.legends == null ? 0 : this.legends.hashCode());
        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("title", title)
            .add("starDistNotice", starDistNotice)
            .add("legends", legends)
            .add("data", data)
            .toString();
    }
}
