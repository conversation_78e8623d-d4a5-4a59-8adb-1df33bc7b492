package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.car;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 用车城市分析详情
 * <AUTHOR>
 * @Date 2019/6/18
 */
public class VehicleCityAnalysisInfoBO {

    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 城市英文名称
     */
    private String cityNameEn;
    /**
     * 订单数量
     */
    private Integer orderNumber;

    /**
     * 总金额
     */
    private String totalAmount;
    /**
     * 前5部门名称
     */
    private String top5Depart;

    /**
     * 部门分析
     */
    private List<VehicleDeptAnalysisInfoBO> deptAnalysisInfoBOList;

    public VehicleCityAnalysisInfoBO(){
        this.cityId=0;
        this.cityName="";
        this.cityNameEn="";
        this.orderNumber=0;
        this.totalAmount="0";
        this.top5Depart="";
        this.deptAnalysisInfoBOList=new ArrayList<>(0);
    }

    public List<VehicleDeptAnalysisInfoBO> getDeptAnalysisInfoBOList() {
        return deptAnalysisInfoBOList;
    }

    public void setDeptAnalysisInfoBOList(List<VehicleDeptAnalysisInfoBO> deptAnalysisInfoBOList) {
        this.deptAnalysisInfoBOList = deptAnalysisInfoBOList;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityNameEn() {
        return cityNameEn;
    }

    public void setCityNameEn(String cityNameEn) {
        this.cityNameEn = cityNameEn;
    }

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Integer orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getTop5Depart() {
        return top5Depart;
    }

    public void setTop5Depart(String top5Depart) {
        this.top5Depart = top5Depart;
    }
}
