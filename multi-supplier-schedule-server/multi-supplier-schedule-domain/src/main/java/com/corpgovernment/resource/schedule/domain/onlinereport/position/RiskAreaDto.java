package com.corpgovernment.resource.schedule.domain.onlinereport.position;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2024-06-26
 */
@Data
public class RiskAreaDto {
    /**
     * 风险类型（1-高风险；2-中高风险；3-中风险）
     */
    @Column(name = "risk_type")
    @Type(value = Types.INTEGER)
    private Integer riskType;
    /**
     * 国家名称
     */
    @Column(name = "country_name")
    @Type(value = Types.VARCHAR)
    private String countryName;
    /**
     * 城市名称
     */
    @Column(name = "city_name")
    @Type(value = Types.VARCHAR)
    private String cityName;
    /**
     * 国家ID
     */
    @Column(name = "country_id")
    @Type(value = Types.INTEGER)
    private Integer countryId;
    /**
     * 城市ID
     */
    @Column(name = "city_id")
    @Type(value = Types.INTEGER)
    private Integer cityId;
}
