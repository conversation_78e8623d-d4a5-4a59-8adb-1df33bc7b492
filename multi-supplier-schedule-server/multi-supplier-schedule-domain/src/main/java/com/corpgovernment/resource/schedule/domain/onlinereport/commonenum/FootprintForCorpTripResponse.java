package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * * 差旅足迹
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "corpTripData",
    "bookListData",
    "chooseFlightData",
    "chooseHotelData",
    "chooseTrainData",
    "chooseCarData"
})
public class FootprintForCorpTripResponse implements Serializable {
    private static final long serialVersionUID = 1L;





    public FootprintForCorpTripResponse(
        Integer responseCode,
        String responseDesc,
        FootprintForCorpTripInfo corpTripData,
        FootprintForBookListInfo bookListData,
        FootprintChooseFlightInfo chooseFlightData,
        FootprintChooseHotelInfo chooseHotelData,
        FootprintChooseTrainInfo chooseTrainData,
        FootprintChooseCarInfo chooseCarData) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;

        this.corpTripData = corpTripData;
        this.bookListData = bookListData;
        this.chooseFlightData = chooseFlightData;
        this.chooseHotelData = chooseHotelData;
        this.chooseTrainData = chooseTrainData;
        this.chooseCarData = chooseCarData;
    }

    public FootprintForCorpTripResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;



    /**
     * 差旅足迹数据
     */
    @JsonProperty("corpTripData")
    public FootprintForCorpTripInfo corpTripData;

    /**
     * 我的榜单数据
     */
    @JsonProperty("bookListData")
    public FootprintForBookListInfo bookListData;

    /**
     * 我的选择-机票数据
     */
    @JsonProperty("chooseFlightData")
    public FootprintChooseFlightInfo chooseFlightData;

    /**
     * 我的选择-酒店数据
     */
    @JsonProperty("chooseHotelData")
    public FootprintChooseHotelInfo chooseHotelData;

    /**
     * 我的选择-火车票数据
     */
    @JsonProperty("chooseTrainData")
    public FootprintChooseTrainInfo chooseTrainData;

    /**
     * 我的选择-用车数据
     */
    @JsonProperty("chooseCarData")
    public FootprintChooseCarInfo chooseCarData;

    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }



    /**
     * 差旅足迹数据
     */
    public FootprintForCorpTripInfo getCorpTripData() {
        return corpTripData;
    }

    /**
     * 差旅足迹数据
     */
    public void setCorpTripData(final FootprintForCorpTripInfo corpTripData) {
        this.corpTripData = corpTripData;
    }

    /**
     * 我的榜单数据
     */
    public FootprintForBookListInfo getBookListData() {
        return bookListData;
    }

    /**
     * 我的榜单数据
     */
    public void setBookListData(final FootprintForBookListInfo bookListData) {
        this.bookListData = bookListData;
    }

    /**
     * 我的选择-机票数据
     */
    public FootprintChooseFlightInfo getChooseFlightData() {
        return chooseFlightData;
    }

    /**
     * 我的选择-机票数据
     */
    public void setChooseFlightData(final FootprintChooseFlightInfo chooseFlightData) {
        this.chooseFlightData = chooseFlightData;
    }

    /**
     * 我的选择-酒店数据
     */
    public FootprintChooseHotelInfo getChooseHotelData() {
        return chooseHotelData;
    }

    /**
     * 我的选择-酒店数据
     */
    public void setChooseHotelData(final FootprintChooseHotelInfo chooseHotelData) {
        this.chooseHotelData = chooseHotelData;
    }

    /**
     * 我的选择-火车票数据
     */
    public FootprintChooseTrainInfo getChooseTrainData() {
        return chooseTrainData;
    }

    /**
     * 我的选择-火车票数据
     */
    public void setChooseTrainData(final FootprintChooseTrainInfo chooseTrainData) {
        this.chooseTrainData = chooseTrainData;
    }

    /**
     * 我的选择-用车数据
     */
    public FootprintChooseCarInfo getChooseCarData() {
        return chooseCarData;
    }

    /**
     * 我的选择-用车数据
     */
    public void setChooseCarData(final FootprintChooseCarInfo chooseCarData) {
        this.chooseCarData = chooseCarData;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FootprintForCorpTripResponse other = (FootprintForCorpTripResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc)  &&
            Objects.equal(this.corpTripData, other.corpTripData) &&
            Objects.equal(this.bookListData, other.bookListData) &&
            Objects.equal(this.chooseFlightData, other.chooseFlightData) &&
            Objects.equal(this.chooseHotelData, other.chooseHotelData) &&
            Objects.equal(this.chooseTrainData, other.chooseTrainData) &&
            Objects.equal(this.chooseCarData, other.chooseCarData);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        
        result = 31 * result + (this.corpTripData == null ? 0 : this.corpTripData.hashCode());
        result = 31 * result + (this.bookListData == null ? 0 : this.bookListData.hashCode());
        result = 31 * result + (this.chooseFlightData == null ? 0 : this.chooseFlightData.hashCode());
        result = 31 * result + (this.chooseHotelData == null ? 0 : this.chooseHotelData.hashCode());
        result = 31 * result + (this.chooseTrainData == null ? 0 : this.chooseTrainData.hashCode());
        result = 31 * result + (this.chooseCarData == null ? 0 : this.chooseCarData.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            
            .add("corpTripData", corpTripData)
            .add("bookListData", bookListData)
            .add("chooseFlightData", chooseFlightData)
            .add("chooseHotelData", chooseHotelData)
            .add("chooseTrainData", chooseTrainData)
            .add("chooseCarData", chooseCarData)
            .toString();
    }
}
