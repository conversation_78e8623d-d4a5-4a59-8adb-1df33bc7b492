package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "aggType",
    "totalCurrentAmount",
    "yoy",
    "mom",
    "totalYoyAmount",
    "totalMomAmount"
})
public class ConsumeAggInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public ConsumeAggInfo(
        String aggType,
        BigDecimal totalCurrentAmount,
        BigDecimal yoy,
        BigDecimal mom,
        BigDecimal totalYoyAmount,
        BigDecimal totalMomAmount) {
        this.aggType = aggType;
        this.totalCurrentAmount = totalCurrentAmount;
        this.yoy = yoy;
        this.mom = mom;
        this.totalYoyAmount = totalYoyAmount;
        this.totalMomAmount = totalMomAmount;
    }

    public ConsumeAggInfo() {
    }

    @JsonProperty("aggType")
    public String aggType;

    @JsonProperty("totalCurrentAmount")
    public BigDecimal totalCurrentAmount;

    @JsonProperty("yoy")
    public BigDecimal yoy;

    @JsonProperty("mom")
    public BigDecimal mom;

    @JsonProperty("totalYoyAmount")
    public BigDecimal totalYoyAmount;

    @JsonProperty("totalMomAmount")
    public BigDecimal totalMomAmount;

    public String getAggType() {
        return aggType;
    }

    public void setAggType(final String aggType) {
        this.aggType = aggType;
    }
    public BigDecimal getTotalCurrentAmount() {
        return totalCurrentAmount;
    }

    public void setTotalCurrentAmount(final BigDecimal totalCurrentAmount) {
        this.totalCurrentAmount = totalCurrentAmount;
    }
    public BigDecimal getYoy() {
        return yoy;
    }

    public void setYoy(final BigDecimal yoy) {
        this.yoy = yoy;
    }
    public BigDecimal getMom() {
        return mom;
    }

    public void setMom(final BigDecimal mom) {
        this.mom = mom;
    }
    public BigDecimal getTotalYoyAmount() {
        return totalYoyAmount;
    }

    public void setTotalYoyAmount(final BigDecimal totalYoyAmount) {
        this.totalYoyAmount = totalYoyAmount;
    }
    public BigDecimal getTotalMomAmount() {
        return totalMomAmount;
    }

    public void setTotalMomAmount(final BigDecimal totalMomAmount) {
        this.totalMomAmount = totalMomAmount;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final ConsumeAggInfo other = (ConsumeAggInfo)obj;
        return
            Objects.equal(this.aggType, other.aggType) &&
            Objects.equal(this.totalCurrentAmount, other.totalCurrentAmount) &&
            Objects.equal(this.yoy, other.yoy) &&
            Objects.equal(this.mom, other.mom) &&
            Objects.equal(this.totalYoyAmount, other.totalYoyAmount) &&
            Objects.equal(this.totalMomAmount, other.totalMomAmount);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.aggType == null ? 0 : this.aggType.hashCode());
        result = 31 * result + (this.totalCurrentAmount == null ? 0 : this.totalCurrentAmount.hashCode());
        result = 31 * result + (this.yoy == null ? 0 : this.yoy.hashCode());
        result = 31 * result + (this.mom == null ? 0 : this.mom.hashCode());
        result = 31 * result + (this.totalYoyAmount == null ? 0 : this.totalYoyAmount.hashCode());
        result = 31 * result + (this.totalMomAmount == null ? 0 : this.totalMomAmount.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("aggType", aggType)
            .add("totalCurrentAmount", totalCurrentAmount)
            .add("yoy", yoy)
            .add("mom", mom)
            .add("totalYoyAmount", totalYoyAmount)
            .add("totalMomAmount", totalMomAmount)
            .toString();
    }
}
