package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.budget;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class FltConsumeTopDTO {

    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;

    @Column(name = "dimEn")
    @Type(value = Types.VARCHAR)
    private String dimEn;

    @Column(name = "date")
    @Type(value = Types.VARCHAR)
    private String date;

    @Column(name = "sumPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumPrice;

    @Column(name = "sumQuantity")
    @Type(value = Types.INTEGER)
    private Integer sumQuantity;

    @Column(name = "sumPriceEconomy")
    @Type(value = Types.DECIMAL)
    private BigDecimal sumPriceEconomy;

    @Column(name = "sumQuantityEconomy")
    @Type(value = Types.INTEGER)
    private Integer sumQuantityEconomy;
}
