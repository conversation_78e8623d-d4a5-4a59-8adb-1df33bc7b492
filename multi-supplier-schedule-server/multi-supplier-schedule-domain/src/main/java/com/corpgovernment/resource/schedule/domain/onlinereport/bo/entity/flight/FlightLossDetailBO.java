package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;


import org.apache.commons.lang.StringUtils;

/**
 * @Description: 机票损失详情
 * <AUTHOR>
 * @Date 2019/5/28
 */
public class FlightLossDetailBO implements Comparable<FlightLossDetailBO> {
    /**
     * RC名称
     */
    String rcName;
    /**
     * 说明
     */
    String remark;
    /**
     * 说明-英文
     */
    String remarkEN;
    /**
     * 张数
     */
    String quantity;
    /**
     * 票价
     */
    String price;
    /**
     * 最低航班价
     */
    String lowPrice;
    /**
     * 部门名称
     */
    String dept;


    public String getRcName() {
        return rcName;
    }

    public void setRcName(String rcName) {
        this.rcName = rcName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemarkEN() {
        return remarkEN;
    }

    public void setRemarkEN(String remarkEN) {
        this.remarkEN = remarkEN;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getLowPrice() {
        return lowPrice;
    }

    public void setLowPrice(String lowPrice) {
        this.lowPrice = lowPrice;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }


    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public int compareTo(FlightLossDetailBO o) {
        if (o.equals(this))
            return 0;
        return StringUtils.equalsIgnoreCase(o.getQuantity(),this.getQuantity())?0:1;
    }
}
