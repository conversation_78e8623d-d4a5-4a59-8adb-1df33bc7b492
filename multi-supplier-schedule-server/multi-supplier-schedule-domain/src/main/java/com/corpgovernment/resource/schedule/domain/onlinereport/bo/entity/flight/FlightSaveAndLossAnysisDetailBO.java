package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

/**
 * @Description: 节省与损失月度季度分析
 * <AUTHOR>
 * @Date 2019/6/11
 */
public class FlightSaveAndLossAnysisDetailBO {
    //年
    private int year;
    //月
    private int month;
    //成交净价
    private double price;
    //当前潜在节省
    private double latentSave;
    //当前潜在节省率
    private double latentSaveRate;
    //预测潜在节省
    private double forecastLatentSave;
    //预测潜在节省率
    private double forecastLatentSaveRate;
    //预订渠道潜在节省
    private double channelLatenSave;
    //最低价潜在节省
    private double lowPriceLatentSave;
    //提前0-3天潜在节省
    private double pre3LatentSave;
    //app+online张数
    private int olQuantity;
    //最低价张数
    private int lowQuantity;
    //提前4天张数
    private int pre4Quantity;
    //张数
    private int quantity;
    //全价
    private double stdPrice;
    //节省
    private double save;
    //节省率
    private double saveRate;

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public double getLatentSave() {
        return latentSave;
    }

    public void setLatentSave(double latentSave) {
        this.latentSave = latentSave;
    }

    public double getLatentSaveRate() {
        return latentSaveRate;
    }

    public void setLatentSaveRate(double latentSaveRate) {
        this.latentSaveRate = latentSaveRate;
    }

    public double getForecastLatentSave() {
        return forecastLatentSave;
    }

    public void setForecastLatentSave(double forecastLatentSave) {
        this.forecastLatentSave = forecastLatentSave;
    }

    public double getForecastLatentSaveRate() {
        return forecastLatentSaveRate;
    }

    public void setForecastLatentSaveRate(double forecastLatentSaveRate) {
        this.forecastLatentSaveRate = forecastLatentSaveRate;
    }

    public double getChannelLatenSave() {
        return channelLatenSave;
    }

    public void setChannelLatenSave(double channelLatenSave) {
        this.channelLatenSave = channelLatenSave;
    }

    public double getLowPriceLatentSave() {
        return lowPriceLatentSave;
    }

    public void setLowPriceLatentSave(double lowPriceLatentSave) {
        this.lowPriceLatentSave = lowPriceLatentSave;
    }

    public double getPre3LatentSave() {
        return pre3LatentSave;
    }

    public void setPre3LatentSave(double pre3LatentSave) {
        this.pre3LatentSave = pre3LatentSave;
    }

    public int getOlQuantity() {
        return olQuantity;
    }

    public void setOlQuantity(int olQuantity) {
        this.olQuantity = olQuantity;
    }

    public int getLowQuantity() {
        return lowQuantity;
    }

    public void setLowQuantity(int lowQuantity) {
        this.lowQuantity = lowQuantity;
    }

    public int getPre4Quantity() {
        return pre4Quantity;
    }

    public void setPre4Quantity(int pre4Quantity) {
        this.pre4Quantity = pre4Quantity;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public double getStdPrice() {
        return stdPrice;
    }

    public void setStdPrice(double stdPrice) {
        this.stdPrice = stdPrice;
    }

    public double getSave() {
        return save;
    }

    public void setSave(double save) {
        this.save = save;
    }

    public double getSaveRate() {
        return saveRate;
    }

    public void setSaveRate(double saveRate) {
        this.saveRate = saveRate;
    }
}
