package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;

/**
 * 酒店名称
 */
public enum TravelPositionStepEnum {

    /**
     * 出差中
     */
    TRAVEL_ING(0),

    /**
     * 将要去
     */
    GOING(1),

    /**
     * 已离开
     */
    LEFTED(2);

    private final int value;

    TravelPositionStepEnum(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public int getValue() {
        return value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public static TravelPositionStepEnum findByValue(int value) {
        switch (value) {
            case 0:
                return TRAVEL_ING;
            case 1:
                return GOING;
            case 2:
                return LEFTED;
            default:
                return null;
        }
    }
}
