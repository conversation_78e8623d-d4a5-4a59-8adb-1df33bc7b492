package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "name",
    "quantity",
    "amount",
    "carMaxtime",
    "carQtyRate"
})
public class FootprintChooseCarInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public FootprintChooseCarInfo(
        String name,
        Integer quantity,
        BigDecimal amount,
        String carMaxtime,
        BigDecimal carQtyRate) {
        this.name = name;
        this.quantity = quantity;
        this.amount = amount;
        this.carMaxtime = carMaxtime;
        this.carQtyRate = carQtyRate;
    }

    public FootprintChooseCarInfo() {
    }

    /**
     * 名称
     */
    @JsonProperty("name")
    public String name;

    /**
     * 张、夜、次
     */
    @JsonProperty("quantity")
    public Integer quantity;

    /**
     * 金额
     */
    @JsonProperty("amount")
    public BigDecimal amount;

    /**
     * 最晚用车时间
     */
    @JsonProperty("carMaxtime")
    public String carMaxtime;

    /**
     * 用车次数公司排行超越人数百分比
     */
    @JsonProperty("carQtyRate")
    public BigDecimal carQtyRate;

    /**
     * 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 名称
     */
    public void setName(final String name) {
        this.name = name;
    }

    /**
     * 张、夜、次
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 张、夜、次
     */
    public void setQuantity(final Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * 金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 金额
     */
    public void setAmount(final BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 最晚用车时间
     */
    public String getCarMaxtime() {
        return carMaxtime;
    }

    /**
     * 最晚用车时间
     */
    public void setCarMaxtime(final String carMaxtime) {
        this.carMaxtime = carMaxtime;
    }

    /**
     * 用车次数公司排行超越人数百分比
     */
    public BigDecimal getCarQtyRate() {
        return carQtyRate;
    }

    /**
     * 用车次数公司排行超越人数百分比
     */
    public void setCarQtyRate(final BigDecimal carQtyRate) {
        this.carQtyRate = carQtyRate;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FootprintChooseCarInfo other = (FootprintChooseCarInfo)obj;
        return
            Objects.equal(this.name, other.name) &&
            Objects.equal(this.quantity, other.quantity) &&
            Objects.equal(this.amount, other.amount) &&
            Objects.equal(this.carMaxtime, other.carMaxtime) &&
            Objects.equal(this.carQtyRate, other.carQtyRate);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.name == null ? 0 : this.name.hashCode());
        result = 31 * result + (this.quantity == null ? 0 : this.quantity.hashCode());
        result = 31 * result + (this.amount == null ? 0 : this.amount.hashCode());
        result = 31 * result + (this.carMaxtime == null ? 0 : this.carMaxtime.hashCode());
        result = 31 * result + (this.carQtyRate == null ? 0 : this.carQtyRate.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("name", name)
            .add("quantity", quantity)
            .add("amount", amount)
            .add("carMaxtime", carMaxtime)
            .add("carQtyRate", carQtyRate)
            .toString();
    }
}
