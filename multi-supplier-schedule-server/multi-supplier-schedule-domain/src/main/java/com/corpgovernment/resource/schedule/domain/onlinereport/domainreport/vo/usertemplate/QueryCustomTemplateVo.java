package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.vo.usertemplate;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Data
public class QueryCustomTemplateVo {
    /**
     * 用户id
     */
    private String uid;
    /**
     * 模板编号
     */
    private String templateNo;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 报表key
     */
    private String reportKey;
    /**
     * 自定义模板字段
     */
    private Map<String, Object> templateFields;
    /**
     * 自定义模版条件
     */
    private Map<String, Object> templateConditions;
}
