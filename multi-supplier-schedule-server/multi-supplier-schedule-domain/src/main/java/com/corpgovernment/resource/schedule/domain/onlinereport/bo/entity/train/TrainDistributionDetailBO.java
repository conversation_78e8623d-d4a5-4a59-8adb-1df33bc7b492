package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.train;

/**
 * @Description: 车型分布
 * <AUTHOR>
 * @Date 2019/6/25
 */
public class TrainDistributionDetailBO {
    /*  高铁票金额 */
    private String crhAmount;
    /*  高铁票张数 */
    private int crhQuantity;
    /*  高铁票票价 */
    private String crhTicketPrice;
    /*  动车票金额 */
    private String emuAmount;
    /*  动车票张数 */
    private int emuQuantity;
    /*  动车票票价 */
    private String emuTicketPrice;
    /* 普通票金额  */
    private String ordinaryAmount;
    /* 普通票张数  */
    private int ordinaryQuantity;
    /*  普通票票价 */
    private String ordinaryTicketPrice;

    public String getCrhAmount() {
        return crhAmount;
    }

    public void setCrhAmount(String crhAmount) {
        this.crhAmount = crhAmount;
    }

    public int getCrhQuantity() {
        return crhQuantity;
    }

    public void setCrhQuantity(int crhQuantity) {
        this.crhQuantity = crhQuantity;
    }

    public String getCrhTicketPrice() {
        return crhTicketPrice;
    }

    public void setCrhTicketPrice(String crhTicketPrice) {
        this.crhTicketPrice = crhTicketPrice;
    }

    public String getEmuAmount() {
        return emuAmount;
    }

    public void setEmuAmount(String emuAmount) {
        this.emuAmount = emuAmount;
    }

    public int getEmuQuantity() {
        return emuQuantity;
    }

    public void setEmuQuantity(int emuQuantity) {
        this.emuQuantity = emuQuantity;
    }

    public String getEmuTicketPrice() {
        return emuTicketPrice;
    }

    public void setEmuTicketPrice(String emuTicketPrice) {
        this.emuTicketPrice = emuTicketPrice;
    }

    public String getOrdinaryAmount() {
        return ordinaryAmount;
    }

    public void setOrdinaryAmount(String ordinaryAmount) {
        this.ordinaryAmount = ordinaryAmount;
    }

    public int getOrdinaryQuantity() {
        return ordinaryQuantity;
    }

    public void setOrdinaryQuantity(int ordinaryQuantity) {
        this.ordinaryQuantity = ordinaryQuantity;
    }

    public String getOrdinaryTicketPrice() {
        return ordinaryTicketPrice;
    }

    public void setOrdinaryTicketPrice(String ordinaryTicketPrice) {
        this.ordinaryTicketPrice = ordinaryTicketPrice;
    }
}
