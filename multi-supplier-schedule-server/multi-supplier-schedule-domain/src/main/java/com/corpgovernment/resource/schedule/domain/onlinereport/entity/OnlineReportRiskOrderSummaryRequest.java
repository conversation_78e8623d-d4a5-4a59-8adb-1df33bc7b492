package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportRiskSceneTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;

/**
 * 风险订单概览-request
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "eId",
        "lang",
        "scene",
        "basecondition"
})
public class OnlineReportRiskOrderSummaryRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    public OnlineReportRiskOrderSummaryRequest(
            String eId,
            String lang,
            QueryReportRiskSceneTypeEnum scene,
            BaseQueryCondition basecondition) {
        this.eId = eId;
        this.lang = lang;
        this.scene = scene;
        this.basecondition = basecondition;
    }

    public OnlineReportRiskOrderSummaryRequest() {
    }

    @JsonProperty("eId")
    public String eId;

    /**
     * 语言
     */
    @JsonProperty("lang")
    public String lang;

    /**
     * 查询场景
     */
    @JsonProperty("scene")
    public QueryReportRiskSceneTypeEnum scene;

    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    public String getEId() {
        return eId;
    }

    public void setEId(final String eId) {
        this.eId = eId;
    }

    /**
     * 语言
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }

    /**
     * 查询场景
     */
    public QueryReportRiskSceneTypeEnum getScene() {
        return scene;
    }

    /**
     * 查询场景
     */
    public void setScene(final QueryReportRiskSceneTypeEnum scene) {
        this.scene = scene;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportRiskOrderSummaryRequest other = (OnlineReportRiskOrderSummaryRequest) obj;
        return
                Objects.equal(this.eId, other.eId) &&
                        Objects.equal(this.lang, other.lang) &&
                        Objects.equal(this.scene, other.scene) &&
                        Objects.equal(this.basecondition, other.basecondition);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eId == null ? 0 : this.eId.hashCode());
        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.scene == null ? 0 : this.scene.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("eId", eId)
                .add("lang", lang)
                .add("scene", scene)
                .add("basecondition", basecondition)
                .toString();
    }
}
