package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportTypeionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryYoyTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * **在线报告明细列表****************************
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "eId",
        "lang",
        "basecondition",
        "yoyType",
        "queryBu",
        "queryType",
        "extData"
})
public class OnlineReportDetailRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    public OnlineReportDetailRequest(
            String eId,
            String lang,
            BaseQueryCondition basecondition,
            QueryYoyTypeEnum yoyType,
            QueryReportBuTypeEnum queryBu,
            List<QueryReportTypeionEnum> queryType,
            Map<String, String> extData) {
        this.eId = eId;
        this.lang = lang;
        this.basecondition = basecondition;
        this.yoyType = yoyType;
        this.queryBu = queryBu;
        this.queryType = queryType;
        this.extData = extData;
    }

    public OnlineReportDetailRequest() {
    }

    @JsonProperty("eId")
    public String eId;

    /**
     * 语言环境
     */
    @JsonProperty("lang")
    public String lang;

    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    /**
     * 同比-去年/前年
     */
    @JsonProperty("yoyType")
    public QueryYoyTypeEnum yoyType;

    /**
     * 查询产线
     */
    @JsonProperty("queryBu")
    public QueryReportBuTypeEnum queryBu;

    /**
     * 查询类型-金额-行程数
     * list<ReportCommonEnum.QueryReportDimensionEnum> queryDimension;
     * 查询维度--同比-环比
     */
    @JsonProperty("queryType")
    public List<QueryReportTypeionEnum> queryType;

    @JsonProperty("extData")
    public Map<String, String> extData;

    public String getEId() {
        return eId;
    }

    public void setEId(final String eId) {
        this.eId = eId;
    }

    /**
     * 语言环境
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言环境
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * 同比-去年/前年
     */
    public QueryYoyTypeEnum getYoyType() {
        return yoyType;
    }

    /**
     * 同比-去年/前年
     */
    public void setYoyType(final QueryYoyTypeEnum yoyType) {
        this.yoyType = yoyType;
    }

    /**
     * 查询产线
     */
    public QueryReportBuTypeEnum getQueryBu() {
        return queryBu;
    }

    /**
     * 查询产线
     */
    public void setQueryBu(final QueryReportBuTypeEnum queryBu) {
        this.queryBu = queryBu;
    }

    /**
     * 查询类型-金额-行程数
     * list<ReportCommonEnum.QueryReportDimensionEnum> queryDimension;
     * 查询维度--同比-环比
     */
    public List<QueryReportTypeionEnum> getQueryType() {
        return queryType;
    }

    /**
     * 查询类型-金额-行程数
     * list<ReportCommonEnum.QueryReportDimensionEnum> queryDimension;
     * 查询维度--同比-环比
     */
    public void setQueryType(final List<QueryReportTypeionEnum> queryType) {
        this.queryType = queryType;
    }

    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportDetailRequest other = (OnlineReportDetailRequest) obj;
        return
                Objects.equal(this.eId, other.eId) &&
                        Objects.equal(this.lang, other.lang) &&
                        Objects.equal(this.basecondition, other.basecondition) &&
                        Objects.equal(this.yoyType, other.yoyType) &&
                        Objects.equal(this.queryBu, other.queryBu) &&
                        Objects.equal(this.queryType, other.queryType) &&
                        Objects.equal(this.extData, other.extData);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eId == null ? 0 : this.eId.hashCode());
        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.yoyType == null ? 0 : this.yoyType.hashCode());
        result = 31 * result + (this.queryBu == null ? 0 : this.queryBu.hashCode());
        result = 31 * result + (this.queryType == null ? 0 : this.queryType.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("eId", eId)
                .add("lang", lang)
                .add("basecondition", basecondition)
                .add("yoyType", yoyType)
                .add("queryBu", queryBu)
                .add("queryType", queryType)
                .add("extData", extData)
                .toString();
    }
}
