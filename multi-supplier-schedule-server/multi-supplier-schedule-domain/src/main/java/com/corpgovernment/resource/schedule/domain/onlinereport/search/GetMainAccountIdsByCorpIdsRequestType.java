package com.corpgovernment.resource.schedule.domain.onlinereport.search;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.List;

/**
 * 根据公司id获取公司下的主账户列表-请求体
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "operator",
        "corpIds",
        "defaultCorpAdmin"
})
public class GetMainAccountIdsByCorpIdsRequestType implements Serializable {
    private static final long serialVersionUID = 1L;


    public GetMainAccountIdsByCorpIdsRequestType(
            String operator,
            List<String> corpIds,
            Boolean defaultCorpAdmin) {
        this.operator = operator;
        this.corpIds = corpIds;
        this.defaultCorpAdmin = defaultCorpAdmin;
    }

    public GetMainAccountIdsByCorpIdsRequestType() {
    }

    /**
     * 操作人
     */
    @JsonProperty("operator")
    public String operator;

    /**
     * 公司ids
     */
    @JsonProperty("corpIds")
    public List<String> corpIds;

    /**
     * 当前operator是否当做公司管理角色处理
     */
    @JsonProperty("defaultCorpAdmin")
    public Boolean defaultCorpAdmin;

    /**
     * 操作人
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作人
     */
    public void setOperator(final String operator) {
        this.operator = operator;
    }

    /**
     * 公司ids
     */
    public List<String> getCorpIds() {
        return corpIds;
    }

    /**
     * 公司ids
     */
    public void setCorpIds(final List<String> corpIds) {
        this.corpIds = corpIds;
    }

    /**
     * 当前operator是否当做公司管理角色处理
     */
    public Boolean isDefaultCorpAdmin() {
        return defaultCorpAdmin;
    }

    /**
     * 当前operator是否当做公司管理角色处理
     */
    public void setDefaultCorpAdmin(final Boolean defaultCorpAdmin) {
        this.defaultCorpAdmin = defaultCorpAdmin;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.operator;
            case 1:
                return this.corpIds;
            case 2:
                return this.defaultCorpAdmin;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.operator = (String) fieldValue;
                break;
            case 1:
                this.corpIds = (List<String>) fieldValue;
                break;
            case 2:
                this.defaultCorpAdmin = (Boolean) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final GetMainAccountIdsByCorpIdsRequestType other = (GetMainAccountIdsByCorpIdsRequestType) obj;
        return
                Objects.equal(this.operator, other.operator) &&
                        Objects.equal(this.corpIds, other.corpIds) &&
                        Objects.equal(this.defaultCorpAdmin, other.defaultCorpAdmin);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.operator == null ? 0 : this.operator.hashCode());
        result = 31 * result + (this.corpIds == null ? 0 : this.corpIds.hashCode());
        result = 31 * result + (this.defaultCorpAdmin == null ? 0 : this.defaultCorpAdmin.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("operator", operator)
                .add("corpIds", corpIds)
                .add("defaultCorpAdmin", defaultCorpAdmin)
                .toString();
    }
}
