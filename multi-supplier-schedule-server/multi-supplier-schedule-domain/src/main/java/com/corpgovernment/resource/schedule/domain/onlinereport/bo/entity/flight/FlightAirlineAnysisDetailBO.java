package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;


import java.io.Serializable;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2019/5/31 14:12
 */
public class FlightAirlineAnysisDetailBO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 航线
     */
    public String name;

    /**
     * 成交净价
     */
    public Double price;

    /**
     * 张数
     */
    public Integer quantity;

    /**
     * 平均票价
     */
    public Double avgPrice;

    /**
     * 平均折扣
     */
    public Double avgDiscount;

    /**
     * 商旅ADR
     */
    public Double busADR;

    /**
     * 行业ADR
     */
    public Double indADR;

    /**
     * 商旅平均票价
     */
    public Double busAvg;

    /**
     * 行业平均票价
     */
    public Double indAvg;


    /**
     * 里程均价
     */
    public Double mileAvgPrice;

    /**
     * 商旅里程均价
     */
    public Double corpMileAvgPrice;

    /**
     * 行业里程均价
     */
    public Double industryMileAvgPrice;


    public Double getMileAvgPrice() {
        return mileAvgPrice;
    }

    public void setMileAvgPrice(Double mileAvgPrice) {
        this.mileAvgPrice = mileAvgPrice;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Double getAvgPrice() {
        return avgPrice;
    }

    public void setAvgPrice(Double avgPrice) {
        this.avgPrice = avgPrice;
    }

    public Double getAvgDiscount() {
        return avgDiscount;
    }

    public void setAvgDiscount(Double avgDiscount) {
        this.avgDiscount = avgDiscount;
    }

    public Double getBusADR() {
        return busADR;
    }

    public void setBusADR(Double busADR) {
        this.busADR = busADR;
    }

    public Double getIndADR() {
        return indADR;
    }

    public void setIndADR(Double indADR) {
        this.indADR = indADR;
    }

    public Double getBusAvg() {
        return busAvg;
    }

    public void setBusAvg(Double busAvg) {
        this.busAvg = busAvg;
    }

    public Double getIndAvg() {
        return indAvg;
    }

    public void setIndAvg(Double indAvg) {
        this.indAvg = indAvg;
    }

    public Double getCorpMileAvgPrice() {
        return corpMileAvgPrice;
    }

    public void setCorpMileAvgPrice(Double corpMileAvgPrice) {
        this.corpMileAvgPrice = corpMileAvgPrice;
    }

    public Double getIndustryMileAvgPrice() {
        return industryMileAvgPrice;
    }

    public void setIndustryMileAvgPrice(Double industryMileAvgPrice) {
        this.industryMileAvgPrice = industryMileAvgPrice;
    }
}
