package com.corpgovernment.resource.schedule.domain.onlinereport.search;

import com.corpgovernment.resource.schedule.domain.onlinereport.types.ResponseStatusType;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import lombok.Data;

import java.io.Serializable;

/**
 * 成本中心和部门查询返回体
 */
@SuppressWarnings("all")
@Data
public class CostCenterAndDepartmentSearchResponseType implements  Serializable {

    private static final long serialVersionUID = 1L;
    @JsonProperty("ResponseStatus")
    public ResponseStatusType responseStatus;
    @JsonProperty("resultStatus")
    public ResultStatus resultStatus;
    @JsonProperty("level1")
    public LevelRecords level1;
    @JsonProperty("level2")
    public LevelRecords level2;
    @JsonProperty("level3")
    public LevelRecords level3;
    @JsonProperty("level4")
    public LevelRecords level4;
    @JsonProperty("level5")
    public LevelRecords level5;
    @JsonProperty("level6")
    public LevelRecords level6;
    @JsonProperty("level7")
    public LevelRecords level7;
    @JsonProperty("level8")
    public LevelRecords level8;
    @JsonProperty("level9")
    public LevelRecords level9;
    @JsonProperty("level10")
    public LevelRecords level10;


}
