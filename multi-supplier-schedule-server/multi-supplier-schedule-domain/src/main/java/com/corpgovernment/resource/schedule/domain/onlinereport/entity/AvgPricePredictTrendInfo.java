package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dim",
    "avgPriceTrendList",
    "avgPricePredictList"
})
public class AvgPricePredictTrendInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public AvgPricePredictTrendInfo(
        String dim,
        List<BudgetConsumeInfo> avgPriceTrendList,
        List<BudgetConsumeInfo> avgPricePredictList) {
        this.dim = dim;
        this.avgPriceTrendList = avgPriceTrendList;
        this.avgPricePredictList = avgPricePredictList;
    }

    public AvgPricePredictTrendInfo() {
    }

    @JsonProperty("dim")
    public String dim;

    @JsonProperty("avgPriceTrendList")
    public List<BudgetConsumeInfo> avgPriceTrendList;

    @JsonProperty("avgPricePredictList")
    public List<BudgetConsumeInfo> avgPricePredictList;

    public String getDim() {
        return dim;
    }

    public void setDim(final String dim) {
        this.dim = dim;
    }
    public List<BudgetConsumeInfo> getAvgPriceTrendList() {
        return avgPriceTrendList;
    }

    public void setAvgPriceTrendList(final List<BudgetConsumeInfo> avgPriceTrendList) {
        this.avgPriceTrendList = avgPriceTrendList;
    }
    public List<BudgetConsumeInfo> getAvgPricePredictList() {
        return avgPricePredictList;
    }

    public void setAvgPricePredictList(final List<BudgetConsumeInfo> avgPricePredictList) {
        this.avgPricePredictList = avgPricePredictList;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final AvgPricePredictTrendInfo other = (AvgPricePredictTrendInfo)obj;
        return
            Objects.equal(this.dim, other.dim) &&
            Objects.equal(this.avgPriceTrendList, other.avgPriceTrendList) &&
            Objects.equal(this.avgPricePredictList, other.avgPricePredictList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.avgPriceTrendList == null ? 0 : this.avgPriceTrendList.hashCode());
        result = 31 * result + (this.avgPricePredictList == null ? 0 : this.avgPricePredictList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dim", dim)
            .add("avgPriceTrendList", avgPriceTrendList)
            .add("avgPricePredictList", avgPricePredictList)
            .toString();
    }
}
