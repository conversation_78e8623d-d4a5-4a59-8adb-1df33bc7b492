package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

/**
 * @Description: 机票主账户相关配置信息-来源徐舰接口
 * <AUTHOR>
 * @Date 2019/6/8
 */
public class FlightAccountConfigInfoBO {
    /**
     * 国内低价RC管控
     */
    private String reasonCode;
    /**
     * 国际低价RC管控
     */
    private String reasonCodeI;
    /**
     * 国内提前RC管控
     */
    private String fltPreBookRC;
    /**
     * 国际提前RC管控
     */
    private String intlFltPreBookRC;

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getReasonCodeI() {
        return reasonCodeI;
    }

    public void setReasonCodeI(String reasonCodeI) {
        this.reasonCodeI = reasonCodeI;
    }

    public String getFltPreBookRC() {
        return fltPreBookRC;
    }

    public void setFltPreBookRC(String fltPreBookRC) {
        this.fltPreBookRC = fltPreBookRC;
    }

    public String getIntlFltPreBookRC() {
        return intlFltPreBookRC;
    }

    public void setIntlFltPreBookRC(String intlFltPreBookRC) {
        this.intlFltPreBookRC = intlFltPreBookRC;
    }
}
