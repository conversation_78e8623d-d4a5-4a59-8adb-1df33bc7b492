package com.corpgovernment.resource.schedule.domain.onlinereport.search;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 成本中心和部门查询请求体
 */
@SuppressWarnings("all")
@Data
public class CostCenterAndDepartmentSearchRequestType implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 操作人
     */
    @JsonProperty("operator")
    public String operator;
    /**
     * 公司id，不可为空
     */
    @JsonProperty("corpIds")
    public List<String> corpIds;
    /**
     * 主账户id，不可为空
     */
    @JsonProperty("accountIds")
    public List<Integer> accountIds;
    /**
     * 搜索关键字，可为空
     */
    @JsonProperty("keyword")
    public String keyword;
    /**
     * 层级，不传可查询所有层级
     */
    @JsonProperty("level")
    public Integer level;
    /**
     * 查询类型，不可为空
     */
    @JsonProperty("searchType")
    public CostCenterAndDepartSearchType searchType;
    /**
     * 分页起始页，不可为空，起始页为1
     */
    @JsonProperty("pageIndex")
    public Integer pageIndex;
    /**
     * 分页大小，默认20；最大不超过50
     */
    @JsonProperty("pageSize")
    public Integer pageSize;
    /**
     * 当前operator是否当做公司管理角色处理
     */
    @JsonProperty("defaultCorpAdmin")
    public Boolean defaultCorpAdmin;

    /**
     * 是否查询ck表中的公司或部门
     */
    @JsonProperty("searchCk")
    public Boolean searchCk = false;


}
