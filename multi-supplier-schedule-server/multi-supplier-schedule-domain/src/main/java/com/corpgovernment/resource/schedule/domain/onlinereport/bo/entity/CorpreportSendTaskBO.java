package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity;

/**
 * Auther:abguo
 * Date:2019/7/25
 * Description:
 * Project:onlinereportweb
 */
public class CorpreportSendTaskBO {

    private  long taskId;
    private  Integer taskStatus;
    private  String reportId;
    private  String content;
    private  String title;
    private  String reportCondition;
    private  boolean isValid;
    private  String reciverEmails;
    private  String uid;
    private  Integer reportType;

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getReportCondition() {
        return reportCondition;
    }

    public void setReportCondition(String reportCondition) {
        this.reportCondition = reportCondition;
    }

    public boolean isValid() {
        return isValid;
    }

    public void setValid(boolean valid) {
        isValid = valid;
    }

    public String getReciverEmails() {
        return reciverEmails;
    }

    public void setReciverEmails(String reciverEmails) {
        this.reciverEmails = reciverEmails;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getReportType() {
        return reportType;
    }

    public void setReportType(Integer reportType) {
        this.reportType = reportType;
    }
}
