package com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 出差申请单信息
 * @create 2024-07-22 23:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TravelApplication {
    
    // 申请单ID
    private String travelApplicationId;
    
    // 行程ID
    private String travelId;
    
    // 预定纬度
    private Double bookLat;
    
    // 预定经度
    private Double bookLon;
    
    // 预定地址
    private String bookAddress;

}
