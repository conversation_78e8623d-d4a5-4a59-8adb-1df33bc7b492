package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 位置
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "countryId",
    "counttryName",
    "provinceId",
    "provinceName",
    "cityId",
    "cityName",
    "provinceGlat",
    "provinceGlon",
    "cityGlat",
    "cityGlon"
})
public class PositionInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public PositionInfo(
        Integer countryId,
        String counttryName,
        Integer provinceId,
        String provinceName,
        Integer cityId,
        String cityName,
        String provinceGlat,
        String provinceGlon,
        String cityGlat,
        String cityGlon) {
        this.countryId = countryId;
        this.counttryName = counttryName;
        this.provinceId = provinceId;
        this.provinceName = provinceName;
        this.cityId = cityId;
        this.cityName = cityName;
        this.provinceGlat = provinceGlat;
        this.provinceGlon = provinceGlon;
        this.cityGlat = cityGlat;
        this.cityGlon = cityGlon;
    }

    public PositionInfo() {
    }

    /**
     * 国家id
     */
    @JsonProperty("countryId")
    public Integer countryId;

    /**
     * 国家名称
     */
    @JsonProperty("counttryName")
    public String counttryName;

    /**
     * 省份id
     */
    @JsonProperty("provinceId")
    public Integer provinceId;

    /**
     * 省份名称
     */
    @JsonProperty("provinceName")
    public String provinceName;

    /**
     * 城市id
     */
    @JsonProperty("cityId")
    public Integer cityId;

    /**
     * 城市名称
     */
    @JsonProperty("cityName")
    public String cityName;

    /**
     * 省份纬度,国内高德,海外谷歌
     */
    @JsonProperty("provinceGlat")
    public String provinceGlat;

    /**
     * 省份经度,国内高德,海外谷歌
     */
    @JsonProperty("provinceGlon")
    public String provinceGlon;

    /**
     * 城市纬度,国内高德,海外谷歌
     */
    @JsonProperty("cityGlat")
    public String cityGlat;

    /**
     * 城市经度,国内高德,海外谷歌
     */
    @JsonProperty("cityGlon")
    public String cityGlon;

    /**
     * 国家id
     */
    public Integer getCountryId() {
        return countryId;
    }

    /**
     * 国家id
     */
    public void setCountryId(final Integer countryId) {
        this.countryId = countryId;
    }

    /**
     * 国家名称
     */
    public String getCounttryName() {
        return counttryName;
    }

    /**
     * 国家名称
     */
    public void setCounttryName(final String counttryName) {
        this.counttryName = counttryName;
    }

    /**
     * 省份id
     */
    public Integer getProvinceId() {
        return provinceId;
    }

    /**
     * 省份id
     */
    public void setProvinceId(final Integer provinceId) {
        this.provinceId = provinceId;
    }

    /**
     * 省份名称
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     * 省份名称
     */
    public void setProvinceName(final String provinceName) {
        this.provinceName = provinceName;
    }

    /**
     * 城市id
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 城市id
     */
    public void setCityId(final Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 城市名称
     */
    public String getCityName() {
        return cityName;
    }

    /**
     * 城市名称
     */
    public void setCityName(final String cityName) {
        this.cityName = cityName;
    }

    /**
     * 省份纬度,国内高德,海外谷歌
     */
    public String getProvinceGlat() {
        return provinceGlat;
    }

    /**
     * 省份纬度,国内高德,海外谷歌
     */
    public void setProvinceGlat(final String provinceGlat) {
        this.provinceGlat = provinceGlat;
    }

    /**
     * 省份经度,国内高德,海外谷歌
     */
    public String getProvinceGlon() {
        return provinceGlon;
    }

    /**
     * 省份经度,国内高德,海外谷歌
     */
    public void setProvinceGlon(final String provinceGlon) {
        this.provinceGlon = provinceGlon;
    }

    /**
     * 城市纬度,国内高德,海外谷歌
     */
    public String getCityGlat() {
        return cityGlat;
    }

    /**
     * 城市纬度,国内高德,海外谷歌
     */
    public void setCityGlat(final String cityGlat) {
        this.cityGlat = cityGlat;
    }

    /**
     * 城市经度,国内高德,海外谷歌
     */
    public String getCityGlon() {
        return cityGlon;
    }

    /**
     * 城市经度,国内高德,海外谷歌
     */
    public void setCityGlon(final String cityGlon) {
        this.cityGlon = cityGlon;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final PositionInfo other = (PositionInfo)obj;
        return
            Objects.equal(this.countryId, other.countryId) &&
            Objects.equal(this.counttryName, other.counttryName) &&
            Objects.equal(this.provinceId, other.provinceId) &&
            Objects.equal(this.provinceName, other.provinceName) &&
            Objects.equal(this.cityId, other.cityId) &&
            Objects.equal(this.cityName, other.cityName) &&
            Objects.equal(this.provinceGlat, other.provinceGlat) &&
            Objects.equal(this.provinceGlon, other.provinceGlon) &&
            Objects.equal(this.cityGlat, other.cityGlat) &&
            Objects.equal(this.cityGlon, other.cityGlon);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.countryId == null ? 0 : this.countryId.hashCode());
        result = 31 * result + (this.counttryName == null ? 0 : this.counttryName.hashCode());
        result = 31 * result + (this.provinceId == null ? 0 : this.provinceId.hashCode());
        result = 31 * result + (this.provinceName == null ? 0 : this.provinceName.hashCode());
        result = 31 * result + (this.cityId == null ? 0 : this.cityId.hashCode());
        result = 31 * result + (this.cityName == null ? 0 : this.cityName.hashCode());
        result = 31 * result + (this.provinceGlat == null ? 0 : this.provinceGlat.hashCode());
        result = 31 * result + (this.provinceGlon == null ? 0 : this.provinceGlon.hashCode());
        result = 31 * result + (this.cityGlat == null ? 0 : this.cityGlat.hashCode());
        result = 31 * result + (this.cityGlon == null ? 0 : this.cityGlon.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("countryId", countryId)
            .add("counttryName", counttryName)
            .add("provinceId", provinceId)
            .add("provinceName", provinceName)
            .add("cityId", cityId)
            .add("cityName", cityName)
            .add("provinceGlat", provinceGlat)
            .add("provinceGlon", provinceGlon)
            .add("cityGlat", cityGlat)
            .add("cityGlon", cityGlon)
            .toString();
    }
}
