package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "industry",
    "totalPersons",
    "personsPercent",
    "totalPersonTimesRecent12Month",
    "totalPersonTimesRecent3Month"
})
public class OnlineReportIndustryInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    

    

    public OnlineReportIndustryInfo(
        String industry,
        Integer totalPersons,
        Double personsPercent,
        Integer totalPersonTimesRecent12Month,
        Integer totalPersonTimesRecent3Month) {
        this.industry = industry;
        this.totalPersons = totalPersons;
        this.personsPercent = personsPercent;
        this.totalPersonTimesRecent12Month = totalPersonTimesRecent12Month;
        this.totalPersonTimesRecent3Month = totalPersonTimesRecent3Month;
    }

    public OnlineReportIndustryInfo() {
    }

    /**
     * 行业
     */
    @JsonProperty("industry")
    public String industry;

    /**
     * 总人数
     */
    @JsonProperty("totalPersons")
    public Integer totalPersons;

    /**
     * 人数占比
     */
    @JsonProperty("personsPercent")
    public Double personsPercent;

    /**
     * 最近一年出行人次
     */
    @JsonProperty("totalPersonTimesRecent12Month")
    public Integer totalPersonTimesRecent12Month;

    /**
     * 最近3个月出行人次
     */
    @JsonProperty("totalPersonTimesRecent3Month")
    public Integer totalPersonTimesRecent3Month;

    /**
     * 行业
     */
    public String getIndustry() {
        return industry;
    }

    /**
     * 行业
     */
    public void setIndustry(final String industry) {
        this.industry = industry;
    }

    /**
     * 总人数
     */
    public Integer getTotalPersons() {
        return totalPersons;
    }

    /**
     * 总人数
     */
    public void setTotalPersons(final Integer totalPersons) {
        this.totalPersons = totalPersons;
    }

    /**
     * 人数占比
     */
    public Double getPersonsPercent() {
        return personsPercent;
    }

    /**
     * 人数占比
     */
    public void setPersonsPercent(final Double personsPercent) {
        this.personsPercent = personsPercent;
    }

    /**
     * 最近一年出行人次
     */
    public Integer getTotalPersonTimesRecent12Month() {
        return totalPersonTimesRecent12Month;
    }

    /**
     * 最近一年出行人次
     */
    public void setTotalPersonTimesRecent12Month(final Integer totalPersonTimesRecent12Month) {
        this.totalPersonTimesRecent12Month = totalPersonTimesRecent12Month;
    }

    /**
     * 最近3个月出行人次
     */
    public Integer getTotalPersonTimesRecent3Month() {
        return totalPersonTimesRecent3Month;
    }

    /**
     * 最近3个月出行人次
     */
    public void setTotalPersonTimesRecent3Month(final Integer totalPersonTimesRecent3Month) {
        this.totalPersonTimesRecent3Month = totalPersonTimesRecent3Month;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportIndustryInfo other = (OnlineReportIndustryInfo)obj;
        return
            Objects.equal(this.industry, other.industry) &&
            Objects.equal(this.totalPersons, other.totalPersons) &&
            Objects.equal(this.personsPercent, other.personsPercent) &&
            Objects.equal(this.totalPersonTimesRecent12Month, other.totalPersonTimesRecent12Month) &&
            Objects.equal(this.totalPersonTimesRecent3Month, other.totalPersonTimesRecent3Month);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.industry == null ? 0 : this.industry.hashCode());
        result = 31 * result + (this.totalPersons == null ? 0 : this.totalPersons.hashCode());
        result = 31 * result + (this.personsPercent == null ? 0 : this.personsPercent.hashCode());
        result = 31 * result + (this.totalPersonTimesRecent12Month == null ? 0 : this.totalPersonTimesRecent12Month.hashCode());
        result = 31 * result + (this.totalPersonTimesRecent3Month == null ? 0 : this.totalPersonTimesRecent3Month.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("industry", industry)
            .add("totalPersons", totalPersons)
            .add("personsPercent", personsPercent)
            .add("totalPersonTimesRecent12Month", totalPersonTimesRecent12Month)
            .add("totalPersonTimesRecent3Month", totalPersonTimesRecent3Month)
            .toString();
    }
}
