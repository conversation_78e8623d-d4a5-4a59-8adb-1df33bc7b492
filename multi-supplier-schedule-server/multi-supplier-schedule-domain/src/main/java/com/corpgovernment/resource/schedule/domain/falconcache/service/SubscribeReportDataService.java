package com.corpgovernment.resource.schedule.domain.falconcache.service;


import com.alicp.jetcache.support.JavaValueDecoder;
import com.ctrip.corp.obt.falconcache.core.config.FalconConfig;
import com.ctrip.corp.obt.falconcache.core.dto.BatchReportMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.Topic;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2024/4/8
 */
@Slf4j
@Component
public class SubscribeReportDataService {
    private  byte[] reportHotDatachannel;

    @Autowired
    @Qualifier("falconRedisMessageListenerContainer")
    private  RedisMessageListenerContainer falconRedisMessageListenerContainer;
    @Autowired
    private CalculateHotKeyService calculateHotKeyService;

    private final ReentrantLock reentrantLock = new ReentrantLock();
    private final MessageListener reportDataListener = this::processReportData;

    @PostConstruct
    public void startSubscribe() {
        reentrantLock.lock();
        try {
            String reportDataChannel = FalconConfig.getReportDataChannel();
            Topic reportDataTopic = new ChannelTopic(reportDataChannel);
            this.falconRedisMessageListenerContainer.addMessageListener(reportDataListener, reportDataTopic);
            log.info("subscribe falconcache invalidate notification. clusterChannel={} hotKeyChannel={} ");
        }finally {
            reentrantLock.unlock();
        }
    }
    private void processReportData(Message message, @Nullable byte[] var2){
        byte[] messageBody = message.getBody();
        Object object = JavaValueDecoder.INSTANCE.apply(messageBody);
        if (object == null) {
            log.error("report data is null");
            return;
        }
        if (object instanceof BatchReportMessage){
            calculateHotKeyService.accumulateHotKey((BatchReportMessage)object);
        } else {
            log.error("the message is not instance of BatchReportMessage, class={}", object.getClass());
        }
    }
}
