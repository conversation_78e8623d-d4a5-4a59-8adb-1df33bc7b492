package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * Rc占比趋势
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "point",
    "rcTimes",
    "rcPercent",
    "orderCount",
    "corpRcTimes",
    "corpOrderCount",
    "corpRcPercent",
    "industryRcTimes",
    "industryOrderCount",
    "industryRcPercent"
})
public class RcTrend implements Serializable {
    private static final long serialVersionUID = 1L;





    public RcTrend(
        String point,
        Integer rcTimes,
        Double rcPercent,
        Integer orderCount,
        Integer corpRcTimes,
        Integer corpOrderCount,
        Double corpRcPercent,
        Integer industryRcTimes,
        Integer industryOrderCount,
        Double industryRcPercent) {
        this.point = point;
        this.rcTimes = rcTimes;
        this.rcPercent = rcPercent;
        this.orderCount = orderCount;
        this.corpRcTimes = corpRcTimes;
        this.corpOrderCount = corpOrderCount;
        this.corpRcPercent = corpRcPercent;
        this.industryRcTimes = industryRcTimes;
        this.industryOrderCount = industryOrderCount;
        this.industryRcPercent = industryRcPercent;
    }

    public RcTrend() {
    }

    /**
     * 时间维度
     */
    @JsonProperty("point")
    public String point;

    /**
     * 超标次数
     */
    @JsonProperty("rcTimes")
    public Integer rcTimes;

    /**
     * 超标次数占比
     */
    @JsonProperty("rcPercent")
    public Double rcPercent;

    /**
     * 订单数
     */
    @JsonProperty("orderCount")
    public Integer orderCount;

    /**
     * 商旅超标次数
     */
    @JsonProperty("corpRcTimes")
    public Integer corpRcTimes;

    /**
     * 商旅订单数
     */
    @JsonProperty("corpOrderCount")
    public Integer corpOrderCount;

    /**
     * 商旅超标占比
     */
    @JsonProperty("corpRcPercent")
    public Double corpRcPercent;

    /**
     * 行业超标次数
     */
    @JsonProperty("industryRcTimes")
    public Integer industryRcTimes;

    /**
     * 行业订单数
     */
    @JsonProperty("industryOrderCount")
    public Integer industryOrderCount;

    /**
     * 行业超标占比
     */
    @JsonProperty("industryRcPercent")
    public Double industryRcPercent;

    /**
     * 时间维度
     */
    public String getPoint() {
        return point;
    }

    /**
     * 时间维度
     */
    public void setPoint(final String point) {
        this.point = point;
    }

    /**
     * 超标次数
     */
    public Integer getRcTimes() {
        return rcTimes;
    }

    /**
     * 超标次数
     */
    public void setRcTimes(final Integer rcTimes) {
        this.rcTimes = rcTimes;
    }

    /**
     * 超标次数占比
     */
    public Double getRcPercent() {
        return rcPercent;
    }

    /**
     * 超标次数占比
     */
    public void setRcPercent(final Double rcPercent) {
        this.rcPercent = rcPercent;
    }

    /**
     * 订单数
     */
    public Integer getOrderCount() {
        return orderCount;
    }

    /**
     * 订单数
     */
    public void setOrderCount(final Integer orderCount) {
        this.orderCount = orderCount;
    }

    /**
     * 商旅超标次数
     */
    public Integer getCorpRcTimes() {
        return corpRcTimes;
    }

    /**
     * 商旅超标次数
     */
    public void setCorpRcTimes(final Integer corpRcTimes) {
        this.corpRcTimes = corpRcTimes;
    }

    /**
     * 商旅订单数
     */
    public Integer getCorpOrderCount() {
        return corpOrderCount;
    }

    /**
     * 商旅订单数
     */
    public void setCorpOrderCount(final Integer corpOrderCount) {
        this.corpOrderCount = corpOrderCount;
    }

    /**
     * 商旅超标占比
     */
    public Double getCorpRcPercent() {
        return corpRcPercent;
    }

    /**
     * 商旅超标占比
     */
    public void setCorpRcPercent(final Double corpRcPercent) {
        this.corpRcPercent = corpRcPercent;
    }

    /**
     * 行业超标次数
     */
    public Integer getIndustryRcTimes() {
        return industryRcTimes;
    }

    /**
     * 行业超标次数
     */
    public void setIndustryRcTimes(final Integer industryRcTimes) {
        this.industryRcTimes = industryRcTimes;
    }

    /**
     * 行业订单数
     */
    public Integer getIndustryOrderCount() {
        return industryOrderCount;
    }

    /**
     * 行业订单数
     */
    public void setIndustryOrderCount(final Integer industryOrderCount) {
        this.industryOrderCount = industryOrderCount;
    }

    /**
     * 行业超标占比
     */
    public Double getIndustryRcPercent() {
        return industryRcPercent;
    }

    /**
     * 行业超标占比
     */
    public void setIndustryRcPercent(final Double industryRcPercent) {
        this.industryRcPercent = industryRcPercent;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final RcTrend other = (RcTrend)obj;
        return
            Objects.equal(this.point, other.point) &&
            Objects.equal(this.rcTimes, other.rcTimes) &&
            Objects.equal(this.rcPercent, other.rcPercent) &&
            Objects.equal(this.orderCount, other.orderCount) &&
            Objects.equal(this.corpRcTimes, other.corpRcTimes) &&
            Objects.equal(this.corpOrderCount, other.corpOrderCount) &&
            Objects.equal(this.corpRcPercent, other.corpRcPercent) &&
            Objects.equal(this.industryRcTimes, other.industryRcTimes) &&
            Objects.equal(this.industryOrderCount, other.industryOrderCount) &&
            Objects.equal(this.industryRcPercent, other.industryRcPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.point == null ? 0 : this.point.hashCode());
        result = 31 * result + (this.rcTimes == null ? 0 : this.rcTimes.hashCode());
        result = 31 * result + (this.rcPercent == null ? 0 : this.rcPercent.hashCode());
        result = 31 * result + (this.orderCount == null ? 0 : this.orderCount.hashCode());
        result = 31 * result + (this.corpRcTimes == null ? 0 : this.corpRcTimes.hashCode());
        result = 31 * result + (this.corpOrderCount == null ? 0 : this.corpOrderCount.hashCode());
        result = 31 * result + (this.corpRcPercent == null ? 0 : this.corpRcPercent.hashCode());
        result = 31 * result + (this.industryRcTimes == null ? 0 : this.industryRcTimes.hashCode());
        result = 31 * result + (this.industryOrderCount == null ? 0 : this.industryOrderCount.hashCode());
        result = 31 * result + (this.industryRcPercent == null ? 0 : this.industryRcPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("point", point)
            .add("rcTimes", rcTimes)
            .add("rcPercent", rcPercent)
            .add("orderCount", orderCount)
            .add("corpRcTimes", corpRcTimes)
            .add("corpOrderCount", corpOrderCount)
            .add("corpRcPercent", corpRcPercent)
            .add("industryRcTimes", industryRcTimes)
            .add("industryOrderCount", industryOrderCount)
            .add("industryRcPercent", industryRcPercent)
            .toString();
    }
}
