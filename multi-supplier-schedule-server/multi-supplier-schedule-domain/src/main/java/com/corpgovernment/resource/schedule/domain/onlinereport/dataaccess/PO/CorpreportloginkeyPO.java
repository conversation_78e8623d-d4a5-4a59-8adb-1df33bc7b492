package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * @Description: offline登录ticket存储表
 * <AUTHOR>
 * @Date 2019/3/12
 */
@Entity
//@Database(name = "CorpReportDB")
@Table(name = "corpreportloginkey")
public class CorpreportloginkeyPO {

    /**
     * uid，主键
     */
    @Id
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    /**
     * 公司
     */
    @Column(name = "corp_corporation")
    @Type(value = Types.VARCHAR)
    private String corpCorporation;

    /**
     * ticket
     */
    @Column(name = "ticket")
    @Type(value = Types.VARCHAR)
    private String ticket;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 最后修改时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getCorpCorporation() {
        return corpCorporation;
    }

    public void setCorpCorporation(String corpCorporation) {
        this.corpCorporation = corpCorporation;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}
