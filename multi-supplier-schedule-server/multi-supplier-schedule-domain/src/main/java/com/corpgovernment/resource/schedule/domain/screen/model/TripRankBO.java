package com.corpgovernment.resource.schedule.domain.screen.model;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.sql.Types;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TripRankBO {

    @Column(name = "route")
    @Type(value = Types.VARCHAR)
    private String route;

    /**
     * 行程段。如：北京-上海
     */
    @Column(name = "routeName")
    @Type(value = Types.VARCHAR)
    private String routeName;

    /**
     * 行程订单量
     */
    @Column(name = "count")
    @Type(value = Types.INTEGER)
    private Integer count;

}
