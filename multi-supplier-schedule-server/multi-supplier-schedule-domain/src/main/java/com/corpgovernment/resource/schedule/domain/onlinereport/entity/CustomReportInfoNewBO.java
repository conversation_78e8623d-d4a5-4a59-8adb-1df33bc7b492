package com.corpgovernment.resource.schedule.domain.onlinereport.entity;



/**
 * Auther:abguo
 * Date:2019/8/13
 * Description:
 * Project:onlinereportweb
 */
public class CustomReportInfoNewBO {
    /**
     * key
     */
    private Long customreportinfoId;

    /**
     * 报表名称
     */
    private String customreportinfoName;

    /**
     * 导出文件名称
     */
    private String exportFileName;

    /**
     * 所属UID
     */
    private String uid;

    /**
     * 报表
     */
    private String reportCondition;

    /**
     * 报表
     */
    private Boolean isValid;

    /**
     * 报表描述
     */
    private String remark;

    public Long getCustomreportinfoId() {
        return customreportinfoId;
    }

    public void setCustomreportinfoId(Long customreportinfoId) {
        this.customreportinfoId = customreportinfoId;
    }

    public String getCustomreportinfoName() {
        return customreportinfoName;
    }

    public void setCustomreportinfoName(String customreportinfoName) {
        this.customreportinfoName = customreportinfoName;
    }

    public String getExportFileName() {
        return exportFileName;
    }

    public void setExportFileName(String exportFileName) {
        this.exportFileName = exportFileName;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getReportCondition() {
        return reportCondition;
    }

    public void setReportCondition(String reportCondition) {
        this.reportCondition = reportCondition;
    }

    public Boolean getValid() {
        return isValid;
    }

    public void setValid(Boolean valid) {
        isValid = valid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
