package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2022/09/08 13:04
 * @Desc
 */
@Entity
//@Database(name = "CorpReportDB")
@Table(name = "riskorderdetailcolumn")
@Data
public class CorpRiskDetailCustomColumnPO {

    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 用户卡号
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    /**
     * 风险场景
     */
    @Column(name = "riskSence")
    @Type(value = Types.VARCHAR)
    private String riskSence;

    /**
     * 自定义字段
     */
    @Column(name = "customColumns")
    @Type(value = Types.VARCHAR)
    private String customColumns;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 最后修改时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;
}
