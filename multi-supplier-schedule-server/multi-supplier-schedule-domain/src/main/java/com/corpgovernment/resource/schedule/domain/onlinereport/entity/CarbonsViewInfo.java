package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 概览
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "totalCarbons",
    "avgCarbons",
    "mom",
    "yoy"
})
public class CarbonsViewInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    

    

    public CarbonsViewInfo(
        BigDecimal totalCarbons,
        BigDecimal avgCarbons,
        BigDecimal mom,
        BigDecimal yoy) {
        this.totalCarbons = totalCarbons;
        this.avgCarbons = avgCarbons;
        this.mom = mom;
        this.yoy = yoy;
    }

    public CarbonsViewInfo() {
    }

    /**
     * 总计
     */
    @JsonProperty("totalCarbons")
    public BigDecimal totalCarbons;

    /**
     * 单程
     */
    @JsonProperty("avgCarbons")
    public BigDecimal avgCarbons;

    /**
     * 环比
     */
    @JsonProperty("mom")
    public BigDecimal mom;

    /**
     * 同比
     */
    @JsonProperty("yoy")
    public BigDecimal yoy;

    /**
     * 总计
     */
    public BigDecimal getTotalCarbons() {
        return totalCarbons;
    }

    /**
     * 总计
     */
    public void setTotalCarbons(final BigDecimal totalCarbons) {
        this.totalCarbons = totalCarbons;
    }

    /**
     * 单程
     */
    public BigDecimal getAvgCarbons() {
        return avgCarbons;
    }

    /**
     * 单程
     */
    public void setAvgCarbons(final BigDecimal avgCarbons) {
        this.avgCarbons = avgCarbons;
    }

    /**
     * 环比
     */
    public BigDecimal getMom() {
        return mom;
    }

    /**
     * 环比
     */
    public void setMom(final BigDecimal mom) {
        this.mom = mom;
    }

    /**
     * 同比
     */
    public BigDecimal getYoy() {
        return yoy;
    }

    /**
     * 同比
     */
    public void setYoy(final BigDecimal yoy) {
        this.yoy = yoy;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final CarbonsViewInfo other = (CarbonsViewInfo)obj;
        return
            Objects.equal(this.totalCarbons, other.totalCarbons) &&
            Objects.equal(this.avgCarbons, other.avgCarbons) &&
            Objects.equal(this.mom, other.mom) &&
            Objects.equal(this.yoy, other.yoy);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.totalCarbons == null ? 0 : this.totalCarbons.hashCode());
        result = 31 * result + (this.avgCarbons == null ? 0 : this.avgCarbons.hashCode());
        result = 31 * result + (this.mom == null ? 0 : this.mom.hashCode());
        result = 31 * result + (this.yoy == null ? 0 : this.yoy.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("totalCarbons", totalCarbons)
            .add("avgCarbons", avgCarbons)
            .add("mom", mom)
            .add("yoy", yoy)
            .toString();
    }
}
