package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight.flightgeneral;

/**
 * @Auther: ddzhan
 * @Date: 2019/6/6
 */
public class FlightGeneralClassStatInfoAnysisBO {

    /* 经济舱 */
    int economyClass;
    /* 折扣经济舱 */
    int discEconomyClass;
    /* 全价经济舱 */
    int FullEconomyClass;
    /* 公务舱 */
    int businessClass;
    /* 头等舱 */
    int firstClass;
    /*  张数 */
    int quantity;
    /* N:国内 I:国际 */
    String type;

    public int getEconomyClass() {
        return economyClass;
    }

    public void setEconomyClass(int economyClass) {
        this.economyClass = economyClass;
    }

    public int getDiscEconomyClass() {
        return discEconomyClass;
    }

    public void setDiscEconomyClass(int discEconomyClass) {
        this.discEconomyClass = discEconomyClass;
    }

    public int getFullEconomyClass() {
        return FullEconomyClass;
    }

    public void setFullEconomyClass(int fullEconomyClass) {
        FullEconomyClass = fullEconomyClass;
    }

    public int getBusinessClass() {
        return businessClass;
    }

    public void setBusinessClass(int businessClass) {
        this.businessClass = businessClass;
    }

    public int getFirstClass() {
        return firstClass;
    }

    public void setFirstClass(int firstClass) {
        this.firstClass = firstClass;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
