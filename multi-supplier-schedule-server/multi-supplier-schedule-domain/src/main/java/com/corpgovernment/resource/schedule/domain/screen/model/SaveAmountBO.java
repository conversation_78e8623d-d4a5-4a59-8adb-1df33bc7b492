package com.corpgovernment.resource.schedule.domain.screen.model;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SaveAmountBO {

    /**
     * 三方协议的节省金额（=会员划线后最低房价A-协议下单实际房价B；B=折前房价-促销金额；仅房价，不含前后收服务费，未扣除优惠券）
     */
    @Column(name = "saveAmount3c")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveAmount3c;

    /**
     * 促销优惠活动的节省金额，字段上线时间：2019-02-02
     */
    @Column(name = "saveAmountPromotion")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveAmountPromotion;

    /**
     * 两方尊享的节省金额（=会员划线后最低房价A-尊享下单实际房价B；B=折前房价-促销金额；仅房价，不含前后收服务费，未扣除优惠券
     */
    @Column(name = "saveAmountPremium")
    @Type(value = Types.DECIMAL)
    private BigDecimal saveAmountPremium;

    /**
     * 节省金额
     */
    @Column(name = "controlSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal controlSave;

    public BigDecimal getFlightSaveAmount() {
        return saveAmount3c.add(saveAmountPremium).add(controlSave);
    }

    public BigDecimal getHotelSaveAmount() {
        return saveAmount3c.add(saveAmountPromotion).add(saveAmountPremium).add(controlSave);
    }
}
