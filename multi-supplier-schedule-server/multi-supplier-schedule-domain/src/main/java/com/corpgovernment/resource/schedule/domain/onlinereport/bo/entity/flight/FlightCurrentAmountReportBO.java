package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

import java.util.List;

/**
 * @Auther: ddzhan
 * @Date: 2019/6/14 15:01
 * @Description:
 */
public class FlightCurrentAmountReportBO {

    public String flightclass;
    public String isreFund;
    public String realPay;
    public String price;
    public String tax;
    public String oilFee;
    public String serviceFee;
    public String insuranceFee;
    public String reFundFee;
    public String changeFee;
    public String bindAmount;
    public String sendticketfee;
    public String quantity;
    public String type;

    List<FlightCurrentAmountReportBO> sumData;

    public List<FlightCurrentAmountReportBO> getSumData() {
        return sumData;
    }

    public void setSumData(List<FlightCurrentAmountReportBO> sumData) {
        this.sumData = sumData;
    }

    public String getFlightclass() {
        return flightclass;
    }

    public void setFlightclass(String flightclass) {
        this.flightclass = flightclass;
    }

    public String getIsreFund() {
        return isreFund;
    }

    public void setIsreFund(String isreFund) {
        this.isreFund = isreFund;
    }

    public String getRealPay() {
        return realPay;
    }

    public void setRealPay(String realPay) {
        this.realPay = realPay;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getTax() {
        return tax;
    }

    public void setTax(String tax) {
        this.tax = tax;
    }

    public String getOilFee() {
        return oilFee;
    }

    public void setOilFee(String oilFee) {
        this.oilFee = oilFee;
    }

    public String getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(String serviceFee) {
        this.serviceFee = serviceFee;
    }

    public String getInsuranceFee() {
        return insuranceFee;
    }

    public void setInsuranceFee(String insuranceFee) {
        this.insuranceFee = insuranceFee;
    }

    public String getReFundFee() {
        return reFundFee;
    }

    public void setReFundFee(String reFundFee) {
        this.reFundFee = reFundFee;
    }

    public String getChangeFee() {
        return changeFee;
    }

    public void setChangeFee(String changeFee) {
        this.changeFee = changeFee;
    }

    public String getBindAmount() {
        return bindAmount;
    }

    public void setBindAmount(String bindAmount) {
        this.bindAmount = bindAmount;
    }

    public String getSendticketfee() {
        return sendticketfee;
    }

    public void setSendticketfee(String sendticketfee) {
        this.sendticketfee = sendticketfee;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
