package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2019-02-27
 */
public class CustomReportInfoNew {
    /**
     * key
     */
    private Long id;

    /**
     * 报表名称
     */
    private String name;

    /**
     * 所属UID
     */
    private String uid;

    /**
     * 报表类型，对应机票、酒店或者机酒等
     */
    private String reportCondition;

    /**
     * 报表序号
     */
    private boolean isValid;
    /**
     * 报表描述
     */
    private String remark;

    /**
     * 报表类型
     */
    private Integer reportType;

    /**
     * 创建时间
     */
    private Timestamp dataChangeCreatetime;

    /**
     * 修改时间
     */
    private Timestamp dataChangeLasttime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getReportCondition() {
        return this.reportCondition;
    }

    public void setReportCondition(String reportCondition) {
        this.reportCondition = reportCondition;
    }

    public boolean isValid() {
        return isValid;
    }

    public void setValid(boolean valid) {
        isValid = valid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Timestamp getDataChangeCreatetime() {
        return dataChangeCreatetime;
    }

    public void setDataChangeCreatetime(Timestamp dataChangeCreatetime) {
        this.dataChangeCreatetime = dataChangeCreatetime;
    }

    public Timestamp getDataChangeLasttime() {
        return dataChangeLasttime;
    }

    public void setDataChangeLasttime(Timestamp dataChangeLasttime) {
        this.dataChangeLasttime = dataChangeLasttime;
    }

    public Integer getReportType() {
        return reportType;
    }

    public void setReportType(Integer reportType) {
        this.reportType = reportType;
    }
}
