package com.corpgovernment.resource.schedule.domain.onlinereport.search;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "id",
        "accountName",
        "companyName"
})
public class MainAccounts implements Serializable {
    private static final long serialVersionUID = 1L;


    public MainAccounts(
            String id,
            String accountName,
            String companyName) {
        this.id = id;
        this.accountName = accountName;
        this.companyName = companyName;
    }

    public MainAccounts() {
    }

    @JsonProperty("id")
    public String id;

    @JsonProperty("accountName")
    public String accountName;

    @JsonProperty("companyName")
    public String companyName;

    public String getId() {
        return id;
    }

    public void setId(final String id) {
        this.id = id;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(final String accountName) {
        this.accountName = accountName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(final String companyName) {
        this.companyName = companyName;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.id;
            case 1:
                return this.accountName;
            case 2:
                return this.companyName;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.id = (String) fieldValue;
                break;
            case 1:
                this.accountName = (String) fieldValue;
                break;
            case 2:
                this.companyName = (String) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final MainAccounts other = (MainAccounts) obj;
        return
                Objects.equal(this.id, other.id) &&
                        Objects.equal(this.accountName, other.accountName) &&
                        Objects.equal(this.companyName, other.companyName);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.id == null ? 0 : this.id.hashCode());
        result = 31 * result + (this.accountName == null ? 0 : this.accountName.hashCode());
        result = 31 * result + (this.companyName == null ? 0 : this.companyName.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("id", id)
                .add("accountName", accountName)
                .add("companyName", companyName)
                .toString();
    }
}
