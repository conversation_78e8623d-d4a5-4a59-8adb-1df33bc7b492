package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 前5部门5部门指标
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "deptMetricList",
    "extData",
    "totalRecords"
})
public class OnlineReportTopFiveDeptMetricResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    

    

    public OnlineReportTopFiveDeptMetricResponse(
        Integer responseCode,
        String responseDesc,
        List<OnlineReportDeptMetric> deptMetricList,
        Map<String, String> extData,
        Integer totalRecords) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.deptMetricList = deptMetricList;
        this.extData = extData;
        this.totalRecords = totalRecords;
        
    }

    public OnlineReportTopFiveDeptMetricResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    @JsonProperty("deptMetricList")
    public List<OnlineReportDeptMetric> deptMetricList;

    @JsonProperty("extData")
    public Map<String, String> extData;

    /**
     * 数据总条数
     */
    @JsonProperty("totalRecords")
    public Integer totalRecords;

    

    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }
    public List<OnlineReportDeptMetric> getDeptMetricList() {
        return deptMetricList;
    }

    public void setDeptMetricList(final List<OnlineReportDeptMetric> deptMetricList) {
        this.deptMetricList = deptMetricList;
    }
    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    /**
     * 数据总条数
     */
    public Integer getTotalRecords() {
        return totalRecords;
    }

    /**
     * 数据总条数
     */
    public void setTotalRecords(final Integer totalRecords) {
        this.totalRecords = totalRecords;
    }
    


    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTopFiveDeptMetricResponse other = (OnlineReportTopFiveDeptMetricResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc) &&
            Objects.equal(this.deptMetricList, other.deptMetricList) &&
            Objects.equal(this.extData, other.extData) &&
            Objects.equal(this.totalRecords, other.totalRecords) ;
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.deptMetricList == null ? 0 : this.deptMetricList.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());
        result = 31 * result + (this.totalRecords == null ? 0 : this.totalRecords.hashCode());
        

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            .add("deptMetricList", deptMetricList)
            .add("extData", extData)
            .add("totalRecords", totalRecords)
            
            .toString();
    }
}
