package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 行为分析-酒店支付方式
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "balanceType",
    "totalQuantity",
    "quantityPercent",
    "avgNightPrice",
    "totalAmount",
    "amountPercent"
})
public class BalanceTypeInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public BalanceTypeInfo(
        String balanceType,
        Integer totalQuantity,
        Double quantityPercent,
        BigDecimal avgNightPrice,
        BigDecimal totalAmount,
        Double amountPercent) {
        this.balanceType = balanceType;
        this.totalQuantity = totalQuantity;
        this.quantityPercent = quantityPercent;
        this.avgNightPrice = avgNightPrice;
        this.totalAmount = totalAmount;
        this.amountPercent = amountPercent;
    }

    public BalanceTypeInfo() {
    }

    /**
     * 支付方式
     */
    @JsonProperty("balanceType")
    public String balanceType;

    /**
     * 间夜数
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;

    /**
     * 间夜占比
     */
    @JsonProperty("quantityPercent")
    public Double quantityPercent;

    /**
     * 间夜均价
     */
    @JsonProperty("avgNightPrice")
    public BigDecimal avgNightPrice;

    /**
     * 消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;

    /**
     * 金额占比
     */
    @JsonProperty("amountPercent")
    public Double amountPercent;

    /**
     * 支付方式
     */
    public String getBalanceType() {
        return balanceType;
    }

    /**
     * 支付方式
     */
    public void setBalanceType(final String balanceType) {
        this.balanceType = balanceType;
    }

    /**
     * 间夜数
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 间夜数
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 间夜占比
     */
    public Double getQuantityPercent() {
        return quantityPercent;
    }

    /**
     * 间夜占比
     */
    public void setQuantityPercent(final Double quantityPercent) {
        this.quantityPercent = quantityPercent;
    }

    /**
     * 间夜均价
     */
    public BigDecimal getAvgNightPrice() {
        return avgNightPrice;
    }

    /**
     * 间夜均价
     */
    public void setAvgNightPrice(final BigDecimal avgNightPrice) {
        this.avgNightPrice = avgNightPrice;
    }

    /**
     * 消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 金额占比
     */
    public Double getAmountPercent() {
        return amountPercent;
    }

    /**
     * 金额占比
     */
    public void setAmountPercent(final Double amountPercent) {
        this.amountPercent = amountPercent;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final BalanceTypeInfo other = (BalanceTypeInfo)obj;
        return
            Objects.equal(this.balanceType, other.balanceType) &&
            Objects.equal(this.totalQuantity, other.totalQuantity) &&
            Objects.equal(this.quantityPercent, other.quantityPercent) &&
            Objects.equal(this.avgNightPrice, other.avgNightPrice) &&
            Objects.equal(this.totalAmount, other.totalAmount) &&
            Objects.equal(this.amountPercent, other.amountPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.balanceType == null ? 0 : this.balanceType.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.quantityPercent == null ? 0 : this.quantityPercent.hashCode());
        result = 31 * result + (this.avgNightPrice == null ? 0 : this.avgNightPrice.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.amountPercent == null ? 0 : this.amountPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("balanceType", balanceType)
            .add("totalQuantity", totalQuantity)
            .add("quantityPercent", quantityPercent)
            .add("avgNightPrice", avgNightPrice)
            .add("totalAmount", totalAmount)
            .add("amountPercent", amountPercent)
            .toString();
    }
}
