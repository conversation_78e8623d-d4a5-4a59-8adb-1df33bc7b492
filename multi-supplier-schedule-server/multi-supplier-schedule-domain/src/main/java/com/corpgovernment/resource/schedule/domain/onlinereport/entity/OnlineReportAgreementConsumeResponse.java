package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.Map;

/**
 * 差旅出行-出行人resp
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "agreementViewInfo",
    "agreementAggInfo",
    "extData"
})
public class OnlineReportAgreementConsumeResponse implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportAgreementConsumeResponse(
        Integer responseCode,
        String responseDesc,
        AgreementViewInfo agreementViewInfo,
        AgreementAggInfo agreementAggInfo,
        Map<String, String> extData) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.agreementViewInfo = agreementViewInfo;
        this.agreementAggInfo = agreementAggInfo;
        this.extData = extData;
        
    }

    public OnlineReportAgreementConsumeResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    /**
     * 概览
     */
    @JsonProperty("agreementViewInfo")
    public AgreementViewInfo agreementViewInfo;

    /**
     * 聚合
     */
    @JsonProperty("agreementAggInfo")
    public AgreementAggInfo agreementAggInfo;

    /**
     * 聚合
     */
    @JsonProperty("extData")
    public Map<String, String> extData;



    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }

    /**
     * 概览
     */
    public AgreementViewInfo getAgreementViewInfo() {
        return agreementViewInfo;
    }

    /**
     * 概览
     */
    public void setAgreementViewInfo(final AgreementViewInfo agreementViewInfo) {
        this.agreementViewInfo = agreementViewInfo;
    }

    /**
     * 聚合
     */
    public AgreementAggInfo getAgreementAggInfo() {
        return agreementAggInfo;
    }

    /**
     * 聚合
     */
    public void setAgreementAggInfo(final AgreementAggInfo agreementAggInfo) {
        this.agreementAggInfo = agreementAggInfo;
    }

    /**
     * 聚合
     */
    public Map<String, String> getExtData() {
        return extData;
    }

    /**
     * 聚合
     */
    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }



    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportAgreementConsumeResponse other = (OnlineReportAgreementConsumeResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc) &&
            Objects.equal(this.agreementViewInfo, other.agreementViewInfo) &&
            Objects.equal(this.agreementAggInfo, other.agreementAggInfo) &&
            Objects.equal(this.extData, other.extData) ;
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.agreementViewInfo == null ? 0 : this.agreementViewInfo.hashCode());
        result = 31 * result + (this.agreementAggInfo == null ? 0 : this.agreementAggInfo.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());
        

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            .add("agreementViewInfo", agreementViewInfo)
            .add("agreementAggInfo", agreementAggInfo)
            .add("extData", extData)
            
            .toString();
    }
}
