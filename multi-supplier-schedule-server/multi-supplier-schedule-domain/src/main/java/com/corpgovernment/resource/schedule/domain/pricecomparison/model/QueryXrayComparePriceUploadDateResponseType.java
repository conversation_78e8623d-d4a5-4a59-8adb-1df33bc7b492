package com.corpgovernment.resource.schedule.domain.pricecomparison.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * http://contract.mobile.flight.ctripcorp.com/#/operation-detail/10939/27/queryXrayComparePriceUploadDate?lang=zh-CN
 */
@NoArgsConstructor
@Data
public class QueryXrayComparePriceUploadDateResponseType {
    @JsonProperty("responseStatus")
    private ResponseStatusDTO responseStatus;
    @JsonProperty("responseCode")
    private Integer responseCode;
    @JsonProperty("responseDesc")
    private String responseDesc;
    @JsonProperty("records")
    private List<RecordsDTO> records;

    @NoArgsConstructor
    @Data
    public static class ResponseStatusDTO {
        @JsonProperty("Timestamp")
        private String timestamp;
        @JsonProperty("Ack")
        private Object ack;
        @JsonProperty("Errors")
        private List<ErrorsDTO> errors;
        @JsonProperty("Build")
        private String build;
        @JsonProperty("Version")
        private String version;
        @JsonProperty("startTime")
        private String startTime;
        @JsonProperty("Extension")
        private List<ExtensionDTO> extension;
        @JsonProperty("responseDesc")
        private String responseDesc;
        @JsonProperty("userID")
        private String userID;
        @JsonProperty("msg")
        private String msg;
        @JsonProperty("ResponseCode")
        private Integer responseCode;
        @JsonProperty("code")
        private String code;
        @JsonProperty("reason")
        private String reason;

        @NoArgsConstructor
        @Data
        public static class ErrorsDTO {
            @JsonProperty("Message")
            private String message;
            @JsonProperty("ErrorCode")
            private String errorCode;
            @JsonProperty("StackTrace")
            private String stackTrace;
            @JsonProperty("SeverityCode")
            private Object severityCode;
            @JsonProperty("ErrorFields")
            private ErrorFieldsDTO errorFields;
            @JsonProperty("ErrorClassification")
            private Object errorClassification;

            @NoArgsConstructor
            @Data
            public static class ErrorFieldsDTO {
                @JsonProperty("FieldName")
                private String fieldName;
                @JsonProperty("ErrorCode")
                private String errorCode;
                @JsonProperty("Message")
                private String message;
            }
        }

        @NoArgsConstructor
        @Data
        public static class ExtensionDTO {
            @JsonProperty("Id")
            private String id;
            @JsonProperty("Version")
            private String version;
            @JsonProperty("ContentType")
            private String contentType;
            @JsonProperty("Value")
            private String value;
        }
    }

    @NoArgsConstructor
    @Data
    public static class RecordsDTO {
        @JsonProperty("taskId")
        private String taskId;
        @JsonProperty("productType")
        private Integer productType;
        @JsonProperty("compareType")
        private Integer compareType;
        @JsonProperty("task_status")
        private Integer taskStatus;
        @JsonProperty("createEid")
        private String createEid;
        @JsonProperty("createName")
        private String createName;
        @JsonProperty("datachangeCreatetime")
        private Integer datachangeCreatetime;
        @JsonProperty("hotelData")
        private List<HotelDataDTO> hotelData;
        @JsonProperty("flightData")
        private List<FlightDataDTO> flightData;

        @NoArgsConstructor
        @Data
        public static class HotelDataDTO {
            @JsonProperty("id")
            private Integer id;
            @JsonProperty("masterhotelid")
            private Integer masterhotelid;
            @JsonProperty("bizUid")
            private String bizUid;
            @JsonProperty("compareUid")
            private String compareUid;
            @JsonProperty("corpId")
            private String corpId;
            @JsonProperty("checkinDate")
            private String checkinDate;
            @JsonProperty("pos")
            private String pos;
            @JsonProperty("compareDate")
            private String compareDate;
            @JsonProperty("compareType")
            private Integer compareType;
            @JsonProperty("countryType")
            private String countryType;
        }

        @NoArgsConstructor
        @Data
        public static class FlightDataDTO {
            @JsonProperty("id")
            private Integer id;
            @JsonProperty("departureCityId")
            private Integer departureCityId;
            @JsonProperty("departureCityName")
            private String departureCityName;
            @JsonProperty("departureCityCode")
            private String departureCityCode;
            @JsonProperty("arrivalCityId")
            private Integer arrivalCityId;
            @JsonProperty("arrivalCityName")
            private String arrivalCityName;
            @JsonProperty("arrivalCityCode")
            private String arrivalCityCode;
            @JsonProperty("flightClass")
            private String flightClass;
            @JsonProperty("compareUid")
            private String compareUid;
            @JsonProperty("corpId")
            private String corpId;
            @JsonProperty("departureDate")
            private String departureDate;
            @JsonProperty("compareType")
            private Integer compareType;
        }
    }
}
