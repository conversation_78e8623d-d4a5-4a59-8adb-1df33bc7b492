package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggDateDimensionEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportAggTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TrendDimensionTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.Map;

/**
 * 趋势
 * ****************************************绿野仙踪-碳排放s******************************************
 * ****************************************供应商监测******************************************
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "eId",
        "lang",
        "basecondition",
        "queryBu",
        "dateDimension",
        "aggType",
        "dimensionType",
        "extData",
        "productType"
})
public class FltCarbonEmissionTrendRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    public FltCarbonEmissionTrendRequest(
            String eId,
            String lang,
            BaseQueryCondition basecondition,
            QueryReportBuTypeEnum queryBu,
            QueryReportAggDateDimensionEnum dateDimension,
            QueryReportAggTypeEnum aggType,
            TrendDimensionTypeEnum dimensionType,
            Map<String, String> extData,
            String productType) {
        this.eId = eId;
        this.lang = lang;
        this.basecondition = basecondition;
        this.queryBu = queryBu;
        this.dateDimension = dateDimension;
        this.aggType = aggType;
        this.dimensionType = dimensionType;
        this.extData = extData;
        this.productType = productType;
    }

    public FltCarbonEmissionTrendRequest() {
    }

    @JsonProperty("eId")
    public String eId;

    /**
     * 语言环境
     */
    @JsonProperty("lang")
    public String lang;

    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    /**
     * 查询产线
     */
    @JsonProperty("queryBu")
    public QueryReportBuTypeEnum queryBu;

    /**
     * 聚合日期维度：月-季度-半年
     */
    @JsonProperty("dateDimension")
    public QueryReportAggDateDimensionEnum dateDimension;

    /**
     * 聚合方式：当期-累计
     */
    @JsonProperty("aggType")
    public QueryReportAggTypeEnum aggType;

    /**
     * 聚合方式：总计-单程
     */
    @JsonProperty("dimensionType")
    public TrendDimensionTypeEnum dimensionType;

    @JsonProperty("extData")
    public Map<String, String> extData;

    /**
     * 国内dom、国际inter、全部all
     */
    @JsonProperty("productType")
    public String productType;

    public String getEId() {
        return eId;
    }

    public void setEId(final String eId) {
        this.eId = eId;
    }

    /**
     * 语言环境
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言环境
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * 查询产线
     */
    public QueryReportBuTypeEnum getQueryBu() {
        return queryBu;
    }

    /**
     * 查询产线
     */
    public void setQueryBu(final QueryReportBuTypeEnum queryBu) {
        this.queryBu = queryBu;
    }

    /**
     * 聚合日期维度：月-季度-半年
     */
    public QueryReportAggDateDimensionEnum getDateDimension() {
        return dateDimension;
    }

    /**
     * 聚合日期维度：月-季度-半年
     */
    public void setDateDimension(final QueryReportAggDateDimensionEnum dateDimension) {
        this.dateDimension = dateDimension;
    }

    /**
     * 聚合方式：当期-累计
     */
    public QueryReportAggTypeEnum getAggType() {
        return aggType;
    }

    /**
     * 聚合方式：当期-累计
     */
    public void setAggType(final QueryReportAggTypeEnum aggType) {
        this.aggType = aggType;
    }

    /**
     * 聚合方式：总计-单程
     */
    public TrendDimensionTypeEnum getDimensionType() {
        return dimensionType;
    }

    /**
     * 聚合方式：总计-单程
     */
    public void setDimensionType(final TrendDimensionTypeEnum dimensionType) {
        this.dimensionType = dimensionType;
    }

    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    /**
     * 国内dom、国际inter、全部all
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 国内dom、国际inter、全部all
     */
    public void setProductType(final String productType) {
        this.productType = productType;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FltCarbonEmissionTrendRequest other = (FltCarbonEmissionTrendRequest) obj;
        return
                Objects.equal(this.eId, other.eId) &&
                        Objects.equal(this.lang, other.lang) &&
                        Objects.equal(this.basecondition, other.basecondition) &&
                        Objects.equal(this.queryBu, other.queryBu) &&
                        Objects.equal(this.dateDimension, other.dateDimension) &&
                        Objects.equal(this.aggType, other.aggType) &&
                        Objects.equal(this.dimensionType, other.dimensionType) &&
                        Objects.equal(this.extData, other.extData) &&
                        Objects.equal(this.productType, other.productType);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eId == null ? 0 : this.eId.hashCode());
        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.queryBu == null ? 0 : this.queryBu.hashCode());
        result = 31 * result + (this.dateDimension == null ? 0 : this.dateDimension.hashCode());
        result = 31 * result + (this.aggType == null ? 0 : this.aggType.hashCode());
        result = 31 * result + (this.dimensionType == null ? 0 : this.dimensionType.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());
        result = 31 * result + (this.productType == null ? 0 : this.productType.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("eId", eId)
                .add("lang", lang)
                .add("basecondition", basecondition)
                .add("queryBu", queryBu)
                .add("dateDimension", dateDimension)
                .add("aggType", aggType)
                .add("dimensionType", dimensionType)
                .add("extData", extData)
                .add("productType", productType)
                .toString();
    }
}
