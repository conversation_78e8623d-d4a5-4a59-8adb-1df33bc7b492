package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 热门城市
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dataTop",
    "data"
})
public class HotelHotCity implements Serializable {
    private static final long serialVersionUID = 1L;

    

    

    public HotelHotCity(
        List<HotelHotCityEntity> dataTop,
        List<HotelHotCityEntity> data) {
        this.dataTop = dataTop;
        this.data = data;
    }

    public HotelHotCity() {
    }

    @JsonProperty("dataTop")
    public List<HotelHotCityEntity> dataTop;

    @JsonProperty("data")
    public List<HotelHotCityEntity> data;

    public List<HotelHotCityEntity> getDataTop() {
        return dataTop;
    }

    public void setDataTop(final List<HotelHotCityEntity> dataTop) {
        this.dataTop = dataTop;
    }
    public List<HotelHotCityEntity> getData() {
        return data;
    }

    public void setData(final List<HotelHotCityEntity> data) {
        this.data = data;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final HotelHotCity other = (HotelHotCity)obj;
        return
            Objects.equal(this.dataTop, other.dataTop) &&
            Objects.equal(this.data, other.data);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dataTop == null ? 0 : this.dataTop.hashCode());
        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dataTop", dataTop)
            .add("data", data)
            .toString();
    }
}
