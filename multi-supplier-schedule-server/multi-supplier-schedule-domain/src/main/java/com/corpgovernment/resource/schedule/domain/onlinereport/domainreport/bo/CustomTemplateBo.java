package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/1 20:43
 * @description
 */
@Data
public class CustomTemplateBo {
    /**
     * 模版编号(必传)
     */
    @JsonProperty("templateNo")
    public String templateNo;

    /**
     * 模版名称
     */
    @JsonProperty("templateName")
    public String templateName;

    /**
     * uid（必传）
     */
    @JsonProperty("uid")
    public String uid;

    /**
     * 报告key
     */
    @JsonProperty("reportKey")
    public String reportKey;

    /**
     * 自定义字段编号
     */
    @JsonProperty("customColumnNo")
    public String customColumnNo;

    /**
     * 自定义条件编号
     */
    @JsonProperty("customConditionNo")
    public String customConditionNo;

    /**
     * 自定义字段集合
     */
    @JsonProperty("customColumnInfoBoList")
    public List<CustomContentInfoBo> customColumnInfoBoList;

    /**
     * 自定义条件集合
     */
    @JsonProperty("customConditionInfoBoList")
    public List<CustomContentInfoBo> customConditionInfoBoList;


}
