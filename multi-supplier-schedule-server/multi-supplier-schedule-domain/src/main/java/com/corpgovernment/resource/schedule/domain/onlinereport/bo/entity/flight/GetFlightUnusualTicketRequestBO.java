package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

import java.util.List;

/**
 * @Description: 异常机票查询、总概分析request
 * <AUTHOR>
 * @Date 2019/5/31
 */
public class GetFlightUnusualTicketRequestBO {
    /**
     * 主账户id列表
     */
    private List<Long> accountIDList;
    /**
     * uid列表
     */
    private List<String> uIDList;
    /**
     * 订单id列表
     */
    private List<Long> orderIDList;

    /**
     * 公司id列表
     */
    private List<String> corpIDList;

    /**
     * 风险级别列表
     */
    private List<Integer> warningLevelList;

    /**
     * 乘客姓名列表
     */
    private List<String> passengerNameList;

    /**
     * 出票开始时间
     */
    private Long startPrintTicketTime;

    /**
     * 出票截止时间
     */
    private Long endPrintTicketTime;

    /**
     * 语言版本
     */
    private String lang;

    /**
     * 登录UID
     */
    private String logOnUID;

    public List<Long> getAccountIDList() {
        return accountIDList;
    }

    public void setAccountIDList(List<Long> accountIDList) {
        this.accountIDList = accountIDList;
    }

    public List<String> getuIDList() {
        return uIDList;
    }

    public void setuIDList(List<String> uIDList) {
        this.uIDList = uIDList;
    }

    public List<Long> getOrderIDList() {
        return orderIDList;
    }

    public void setOrderIDList(List<Long> orderIDList) {
        this.orderIDList = orderIDList;
    }

    public List<String> getCorpIDList() {
        return corpIDList;
    }

    public void setCorpIDList(List<String> corpIDList) {
        this.corpIDList = corpIDList;
    }

    public List<Integer> getWarningLevelList() {
        return warningLevelList;
    }

    public void setWarningLevelList(List<Integer> warningLevelList) {
        this.warningLevelList = warningLevelList;
    }

    public List<String> getPassengerNameList() {
        return passengerNameList;
    }

    public void setPassengerNameList(List<String> passengerNameList) {
        this.passengerNameList = passengerNameList;
    }

    public Long getStartPrintTicketTime() {
        return startPrintTicketTime;
    }

    public void setStartPrintTicketTime(Long startPrintTicketTime) {
        this.startPrintTicketTime = startPrintTicketTime;
    }

    public Long getEndPrintTicketTime() {
        return endPrintTicketTime;
    }

    public void setEndPrintTicketTime(Long endPrintTicketTime) {
        this.endPrintTicketTime = endPrintTicketTime;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getLogOnUID() {
        return logOnUID;
    }

    public void setLogOnUID(String logOnUID) {
        this.logOnUID = logOnUID;
    }
}
