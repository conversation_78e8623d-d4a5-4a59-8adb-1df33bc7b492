package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight.flightgeneral;


import java.util.List;

/**
 * @Auther: ddzhan
 * @Date: 2019/6/5
 * @Description: 机票总概top分析 BO
 */
public class FlightGeneralTopAnysisBO {

    List<FlightGeneralDeptTopAnysisBO> flightGeneralDeptTopAnysisList;

    List<FlightGeneralCityTopAnysisBO> flightGeneralCityTopAnysisList;

    public List<FlightGeneralDeptTopAnysisBO> getFlightGeneralDeptTopAnysisList() {
        return flightGeneralDeptTopAnysisList;
    }

    public void setFlightGeneralDeptTopAnysisList(List<FlightGeneralDeptTopAnysisBO> flightGeneralDeptTopAnysisList) {
        this.flightGeneralDeptTopAnysisList = flightGeneralDeptTopAnysisList;
    }

    public List<FlightGeneralCityTopAnysisBO> getFlightGeneralCityTopAnysisList() {
        return flightGeneralCityTopAnysisList;
    }

    public void setFlightGeneralCityTopAnysisList(List<FlightGeneralCityTopAnysisBO> flightGeneralCityTopAnysisList) {
        this.flightGeneralCityTopAnysisList = flightGeneralCityTopAnysisList;
    }
}
