package com.corpgovernment.resource.schedule.domain.onlinereport.customreport;

import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.CustomReportInfoNew;
import com.corpgovernment.resource.schedule.domain.onlinereport.dao.CustomReportInfoNewDAOExt;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.CustomReportInfoNewMapper;
import com.corpgovernment.resource.schedule.domain.onlinereport.po.CustomReportInfoNewPO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Auther:abguo
 * Date:2019/8/17
 * Description:
 * Project:onlinereportweb
 */
@Service(value = "customNewService")
public class CustomReportNewService {
    @Resource
    private CustomReportInfoNewDAOExt customReportInfoNewDAOExt;
    @Autowired
    private CustomReportInfoNewMapper mapper;

    public List<CustomReportInfoNew> queryByIds(List<Long> ids) throws SQLException {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        List<List<Long>> list = new ArrayList<>();
        int batchSize = 50;
        if (ids.size() > batchSize) {
            list = Lists.partition(ids, batchSize);
        } else {
            list.add(ids);
        }

        List<CustomReportInfoNew> result = new ArrayList<>();
        for (List<Long> item : list) {
            List<CustomReportInfoNewPO> entitys = customReportInfoNewDAOExt.queryByIds(item);
            if (CollectionUtils.isEmpty(entitys)) {
                return null;
            }
            result.addAll(entitys.stream().map(p -> mapper.mapper(p)).collect(Collectors.toList()));
        }
        return result;
    }

    public CustomReportInfoNew queryById(Long id) throws SQLException {
/*        CustomReportInfoNewPO entity = customReportInfoNewDAOExt.queryByPk(id);
        if (entity == null) {
            return null;
        }
        return mapper.mapper(entity);*/
        return null;
    }

/*    public int count(CustomReportInfoNew info) throws SQLException {
        CustomReportInfoNewPO po = mapper.mapper(info);
        List<CustomReportInfoNewPO> list = customReportInfoNewDAOExt.queryBy(po);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        } else {
            return list.size();
        }
    }

    public List<CustomReportInfoNew> query(CustomReportInfoNew info) throws SQLException {
        CustomReportInfoNewPO po = mapper.mapper(info);
        List<CustomReportInfoNewPO> list = customReportInfoNewDAOExt.queryBy(po);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(mapper::mapper).collect(Collectors.toList());
    }*/

    public List<CustomReportInfoNew> queryByUid(String uid) throws SQLException {
        List<CustomReportInfoNewPO> list = customReportInfoNewDAOExt.queryByUID(uid);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(mapper::mapper).collect(Collectors.toList());
    }

/*    public int insert(CustomReportInfoNew info) throws SQLException {
        CustomReportInfoNewPO po = mapper.mapper(info);
        if (po == null) {
            return 0;
        }
        KeyHolder keyHolder = new KeyHolder();
        int result = customReportInfoNewDAOExt.insertWithKeyHolder(keyHolder, po);
        if (result > 0 && keyHolder.getKey() != null) {
            return keyHolder.getKey().intValue();
        }
        return 0;
    }*/

 /*   public int update(CustomReportInfoNew info) throws SQLException {
        CustomReportInfoNewPO po = mapper.mapper(info);
        if (po == null) {
            return 0;
        }

        return customReportInfoNewDAOExt.update(po);
    }

    public int delete(CustomReportInfoNew info) throws SQLException {
        CustomReportInfoNewPO po = mapper.mapper(info);
        if (po == null) {
            return 0;
        }

        return customReportInfoNewDAOExt.delete(po);
    }

    public boolean save(CustomReportInfoNew info) throws Exception {
        CustomReportInfoNewPO po = mapper.mapper(info);
        if (po == null) {
            return false;
        }
        CustomReportInfoNewPO item = queryByUid(info.getUid(), info.getReportType());
        if (item == null) {
            return customReportInfoNewDAOExt.insert(po) > 0;
        } else {
            po.setId(item.getId());
            return customReportInfoNewDAOExt.update(po) > 0;
        }
    }*/

    public CustomReportInfoNewPO queryByUid(String uid, int reportType) throws SQLException {
        List<CustomReportInfoNewPO> list = customReportInfoNewDAOExt.queryByUID(uid, reportType);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
