package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "areaCount",
    "noNeedChangeAreaCount",
    "needChangeAreaCount",
    "upperSuggestCount",
    "upperSuggestAreas",
    "downSuggestCount",
    "downSuggestAreas",
    "quantity"
})
public class AdviceOverview implements Serializable {
    private static final long serialVersionUID = 1L;





    public AdviceOverview(
        Integer areaCount,
        Integer noNeedChangeAreaCount,
        Integer needChangeAreaCount,
        Integer upperSuggestCount,
        List<String> upperSuggestAreas,
        Integer downSuggestCount,
        List<String> downSuggestAreas,
        Integer quantity) {
        this.areaCount = areaCount;
        this.noNeedChangeAreaCount = noNeedChangeAreaCount;
        this.needChangeAreaCount = needChangeAreaCount;
        this.upperSuggestCount = upperSuggestCount;
        this.upperSuggestAreas = upperSuggestAreas;
        this.downSuggestCount = downSuggestCount;
        this.downSuggestAreas = downSuggestAreas;
        this.quantity = quantity;
    }

    public AdviceOverview() {
    }

    /**
     * 设置差标地区数
     */
    @JsonProperty("areaCount")
    public Integer areaCount;

    /**
     * 差标设置合理数
     */
    @JsonProperty("noNeedChangeAreaCount")
    public Integer noNeedChangeAreaCount;

    /**
     * 差标可适当调整数
     */
    @JsonProperty("needChangeAreaCount")
    public Integer needChangeAreaCount;

    /**
     * 差标可适当调高数
     */
    @JsonProperty("upperSuggestCount")
    public Integer upperSuggestCount;

    /**
     * 差标可适当调高差标的Top3地区
     */
    @JsonProperty("upperSuggestAreas")
    public List<String> upperSuggestAreas;

    /**
     * 差标可适当调低数
     */
    @JsonProperty("downSuggestCount")
    public Integer downSuggestCount;

    /**
     * 差标可适当调低差标的Top3地区
     */
    @JsonProperty("downSuggestAreas")
    public List<String> downSuggestAreas;

    /**
     * 酒店消费间夜量
     */
    @JsonProperty("quantity")
    public Integer quantity;

    /**
     * 设置差标地区数
     */
    public Integer getAreaCount() {
        return areaCount;
    }

    /**
     * 设置差标地区数
     */
    public void setAreaCount(final Integer areaCount) {
        this.areaCount = areaCount;
    }

    /**
     * 差标设置合理数
     */
    public Integer getNoNeedChangeAreaCount() {
        return noNeedChangeAreaCount;
    }

    /**
     * 差标设置合理数
     */
    public void setNoNeedChangeAreaCount(final Integer noNeedChangeAreaCount) {
        this.noNeedChangeAreaCount = noNeedChangeAreaCount;
    }

    /**
     * 差标可适当调整数
     */
    public Integer getNeedChangeAreaCount() {
        return needChangeAreaCount;
    }

    /**
     * 差标可适当调整数
     */
    public void setNeedChangeAreaCount(final Integer needChangeAreaCount) {
        this.needChangeAreaCount = needChangeAreaCount;
    }

    /**
     * 差标可适当调高数
     */
    public Integer getUpperSuggestCount() {
        return upperSuggestCount;
    }

    /**
     * 差标可适当调高数
     */
    public void setUpperSuggestCount(final Integer upperSuggestCount) {
        this.upperSuggestCount = upperSuggestCount;
    }

    /**
     * 差标可适当调高差标的Top3地区
     */
    public List<String> getUpperSuggestAreas() {
        return upperSuggestAreas;
    }

    /**
     * 差标可适当调高差标的Top3地区
     */
    public void setUpperSuggestAreas(final List<String> upperSuggestAreas) {
        this.upperSuggestAreas = upperSuggestAreas;
    }

    /**
     * 差标可适当调低数
     */
    public Integer getDownSuggestCount() {
        return downSuggestCount;
    }

    /**
     * 差标可适当调低数
     */
    public void setDownSuggestCount(final Integer downSuggestCount) {
        this.downSuggestCount = downSuggestCount;
    }

    /**
     * 差标可适当调低差标的Top3地区
     */
    public List<String> getDownSuggestAreas() {
        return downSuggestAreas;
    }

    /**
     * 差标可适当调低差标的Top3地区
     */
    public void setDownSuggestAreas(final List<String> downSuggestAreas) {
        this.downSuggestAreas = downSuggestAreas;
    }

    /**
     * 酒店消费间夜量
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 酒店消费间夜量
     */
    public void setQuantity(final Integer quantity) {
        this.quantity = quantity;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final AdviceOverview other = (AdviceOverview)obj;
        return
            Objects.equal(this.areaCount, other.areaCount) &&
            Objects.equal(this.noNeedChangeAreaCount, other.noNeedChangeAreaCount) &&
            Objects.equal(this.needChangeAreaCount, other.needChangeAreaCount) &&
            Objects.equal(this.upperSuggestCount, other.upperSuggestCount) &&
            Objects.equal(this.upperSuggestAreas, other.upperSuggestAreas) &&
            Objects.equal(this.downSuggestCount, other.downSuggestCount) &&
            Objects.equal(this.downSuggestAreas, other.downSuggestAreas) &&
            Objects.equal(this.quantity, other.quantity);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.areaCount == null ? 0 : this.areaCount.hashCode());
        result = 31 * result + (this.noNeedChangeAreaCount == null ? 0 : this.noNeedChangeAreaCount.hashCode());
        result = 31 * result + (this.needChangeAreaCount == null ? 0 : this.needChangeAreaCount.hashCode());
        result = 31 * result + (this.upperSuggestCount == null ? 0 : this.upperSuggestCount.hashCode());
        result = 31 * result + (this.upperSuggestAreas == null ? 0 : this.upperSuggestAreas.hashCode());
        result = 31 * result + (this.downSuggestCount == null ? 0 : this.downSuggestCount.hashCode());
        result = 31 * result + (this.downSuggestAreas == null ? 0 : this.downSuggestAreas.hashCode());
        result = 31 * result + (this.quantity == null ? 0 : this.quantity.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("areaCount", areaCount)
            .add("noNeedChangeAreaCount", noNeedChangeAreaCount)
            .add("needChangeAreaCount", needChangeAreaCount)
            .add("upperSuggestCount", upperSuggestCount)
            .add("upperSuggestAreas", upperSuggestAreas)
            .add("downSuggestCount", downSuggestCount)
            .add("downSuggestAreas", downSuggestAreas)
            .add("quantity", quantity)
            .toString();
    }
}
