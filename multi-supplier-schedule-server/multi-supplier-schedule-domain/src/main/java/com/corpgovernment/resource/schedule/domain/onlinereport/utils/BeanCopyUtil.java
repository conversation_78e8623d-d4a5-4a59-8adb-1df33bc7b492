package com.corpgovernment.resource.schedule.domain.onlinereport.utils;

import com.baidu.unbiz.easymapper.ClassMapBuilder;
import com.baidu.unbiz.easymapper.MapperFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * java bean copy工具类
 */
public final class BeanCopyUtil {

    private BeanCopyUtil() {
    }

    /**
     * 源实例Copy至目标实例
     *
     * @param sourceInstance 源实例
     * @param targetType     目标类型class
     * @param <S>            源类型
     * @param <T>            目标类型
     * @return 目标实例
     */
    public static <S, T> T copy(S sourceInstance, Class<T> targetType) {
        try {
            return MapperFactory.getCopyByRefMapper().mapClass(sourceInstance.getClass(), targetType).register()
                    .map(sourceInstance, targetType);
        } catch (Exception e) {
            throw new RuntimeException("copy error", e);
        }
    }

    /**
     * 源实例Copy至目标实例
     *
     * @param sourceInstance 源实例
     * @param targetType     目标类型class
     * @param mapBuilder     类型因映射构建器 (eg:MapperFactory.getCopyByRefMapper().mapClass(clazzS, clazzT))
     * @param <S>            源类型
     * @param <T>            目标类型
     * @return
     */
    public static <S, T> T copy(S sourceInstance, Class<T> targetType, ClassMapBuilder<S, T> mapBuilder) {
        try {
            return mapBuilder.register().map(sourceInstance, targetType);
        } catch (Exception e) {
            throw new RuntimeException("copy error", e);
        }
    }

    /**
     * 源实例Copy至目标实例
     *
     * @param sourceInstance 源实例
     * @param targetInstance 目标实例
     * @param <S>            源类型
     * @param <T>            目标类型
     * @return 目标实例
     */
    public static <S, T> T copy(S sourceInstance, T targetInstance) {
        try {
            return MapperFactory.getCopyByRefMapper().mapClass(sourceInstance.getClass(), targetInstance.getClass())
                    .register().map(sourceInstance, targetInstance);
        } catch (Exception e) {
            throw new RuntimeException("copy error", e);
        }
    }

    /**
     * 源实例Copy至目标实例
     *
     * @param sourceInstance 源实例
     * @param targetInstance 目标实例
     * @param mapBuilder     类型因映射构建器 (eg:MapperFactory.getCopyByRefMapper().mapClass(clazzS, clazzT))
     * @param <S>            源类型
     * @param <T>            目标类型
     * @return 目标实例
     */
    public static <S, T> T copy(S sourceInstance, T targetInstance, ClassMapBuilder<S, T> mapBuilder) {
        try {
            return mapBuilder.register().map(sourceInstance, targetInstance);
        } catch (Exception e) {
            throw new RuntimeException("copy error", e);
        }
    }

    /**
     * 源实例列表Copy至目标实例列表
     *
     * @param sourceInstances 源实例列表
     * @param targetType      目标类型class
     * @param <S>             源类型
     * @param <T>             目标类型
     * @return 目标实例列表
     */
    public static <S, T> List<T> copyList(List<S> sourceInstances, Class<T> targetType) {
        List<T> results = new ArrayList<>();
        for (S sourceInstance : sourceInstances) {
            results.add(copy(sourceInstance, targetType));
        }
        return results;
    }

    /**
     * 源实例列表Copy至目标实例列表
     *
     * @param sourceInstances 源实例列表
     * @param targetType      目标类型class
     * @param mapBuilder      类型因映射构建器 (eg:MapperFactory.getCopyByRefMapper().mapClass(clazzS, clazzT))
     * @param <S>             源类型
     * @param <T>             目标类型
     * @return 目标实例列表
     */
    public static <S, T> List<T> copyList(List<S> sourceInstances, Class<T> targetType,
                                          ClassMapBuilder<S, T> mapBuilder) {
        List<T> results = new ArrayList<>();
        for (S sourceInstance : sourceInstances) {
            results.add(copy(sourceInstance, targetType, mapBuilder));
        }
        return results;
    }
}
