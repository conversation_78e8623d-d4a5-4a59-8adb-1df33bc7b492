package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "sumInfo",
    "otherInfo",
    "topList"
})
public class SupplierTrainTopInfo implements Serializable {
    private static final long serialVersionUID = 1L;



    

    public SupplierTrainTopInfo(
        OnlineReportSupplierTrainTop sumInfo,
        OnlineReportSupplierTrainTop otherInfo,
        List<OnlineReportSupplierTrainTop> topList) {
        this.sumInfo = sumInfo;
        this.otherInfo = otherInfo;
        this.topList = topList;
    }

    public SupplierTrainTopInfo() {
    }

    /**
     * sum
     */
    @JsonProperty("sumInfo")
    public OnlineReportSupplierTrainTop sumInfo;

    /**
     * other
     */
    @JsonProperty("otherInfo")
    public OnlineReportSupplierTrainTop otherInfo;

    /**
     * top
     */
    @JsonProperty("topList")
    public List<OnlineReportSupplierTrainTop> topList;

    /**
     * sum
     */
    public OnlineReportSupplierTrainTop getSumInfo() {
        return sumInfo;
    }

    /**
     * sum
     */
    public void setSumInfo(final OnlineReportSupplierTrainTop sumInfo) {
        this.sumInfo = sumInfo;
    }

    /**
     * other
     */
    public OnlineReportSupplierTrainTop getOtherInfo() {
        return otherInfo;
    }

    /**
     * other
     */
    public void setOtherInfo(final OnlineReportSupplierTrainTop otherInfo) {
        this.otherInfo = otherInfo;
    }

    /**
     * top
     */
    public List<OnlineReportSupplierTrainTop> getTopList() {
        return topList;
    }

    /**
     * top
     */
    public void setTopList(final List<OnlineReportSupplierTrainTop> topList) {
        this.topList = topList;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final SupplierTrainTopInfo other = (SupplierTrainTopInfo)obj;
        return
            Objects.equal(this.sumInfo, other.sumInfo) &&
            Objects.equal(this.otherInfo, other.otherInfo) &&
            Objects.equal(this.topList, other.topList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.sumInfo == null ? 0 : this.sumInfo.hashCode());
        result = 31 * result + (this.otherInfo == null ? 0 : this.otherInfo.hashCode());
        result = 31 * result + (this.topList == null ? 0 : this.topList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("sumInfo", sumInfo)
            .add("otherInfo", otherInfo)
            .add("topList", topList)
            .toString();
    }
}
