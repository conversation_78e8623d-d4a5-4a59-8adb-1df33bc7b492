package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * @Description: 自定义报表持久实体
 * <AUTHOR>
 * @Date 2019/2/27
 */
@Entity
//@Database(name = "CorpReportDB")
@Table(name = "corpreportmypage")
public class CorpReportMyPagePO {

    /**
     * key
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 内容类型
     * ReportStructure：图表，ReportLayout：页面结构
     */
    @Column(name = "contentType")
    @Type(value = Types.VARCHAR)
    private String contentType;

    /**
     * 所属UID
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    /**
     * 具体内容
     */
    @Column(name = "pageContent")
    @Type(value = Types.VARCHAR)
    private String pageContent;

    /**
     * 是否有效
     */
    @Column(name = "isValid")
    @Type(value = Types.BIT)
    private Boolean isValid;


    /**
     * 创建时间
     */
    @Column(name = "dataChange_createTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 修改时间
     */
    @Column(name = "dataChange_lastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }



    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }


    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Boolean getValid() {
        return isValid;
    }

    public void setValid(Boolean valid) {
        isValid = valid;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getPageContent() {
        return pageContent;
    }

    public void setPageContent(String pageContent) {
        this.pageContent = pageContent;
    }
}
