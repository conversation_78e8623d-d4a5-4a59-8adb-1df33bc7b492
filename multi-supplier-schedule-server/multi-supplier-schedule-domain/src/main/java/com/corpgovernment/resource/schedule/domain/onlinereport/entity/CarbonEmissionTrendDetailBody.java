package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "date",
    "carbons",
    "chainCarbons",
    "yoyCarbons"
})
public class CarbonEmissionTrendDetailBody implements Serializable {
    private static final long serialVersionUID = 1L;





    public CarbonEmissionTrendDetailBody(
        String date,
        String carbons,
        String chainCarbons,
        String yoyCarbons) {
        this.date = date;
        this.carbons = carbons;
        this.chainCarbons = chainCarbons;
        this.yoyCarbons = yoyCarbons;
    }

    public CarbonEmissionTrendDetailBody() {
    }

    /**
     * 月份
     */
    @JsonProperty("date")
    public String date;

    /**
     * 碳排放量
     */
    @JsonProperty("carbons")
    public String carbons;

    @JsonProperty("chainCarbons")
    public String chainCarbons;

    @JsonProperty("yoyCarbons")
    public String yoyCarbons;

    /**
     * 月份
     */
    public String getDate() {
        return date;
    }

    /**
     * 月份
     */
    public void setDate(final String date) {
        this.date = date;
    }

    /**
     * 碳排放量
     */
    public String getCarbons() {
        return carbons;
    }

    /**
     * 碳排放量
     */
    public void setCarbons(final String carbons) {
        this.carbons = carbons;
    }
    public String getChainCarbons() {
        return chainCarbons;
    }

    public void setChainCarbons(final String chainCarbons) {
        this.chainCarbons = chainCarbons;
    }
    public String getYoyCarbons() {
        return yoyCarbons;
    }

    public void setYoyCarbons(final String yoyCarbons) {
        this.yoyCarbons = yoyCarbons;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final CarbonEmissionTrendDetailBody other = (CarbonEmissionTrendDetailBody)obj;
        return
            Objects.equal(this.date, other.date) &&
            Objects.equal(this.carbons, other.carbons) &&
            Objects.equal(this.chainCarbons, other.chainCarbons) &&
            Objects.equal(this.yoyCarbons, other.yoyCarbons);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.date == null ? 0 : this.date.hashCode());
        result = 31 * result + (this.carbons == null ? 0 : this.carbons.hashCode());
        result = 31 * result + (this.chainCarbons == null ? 0 : this.chainCarbons.hashCode());
        result = 31 * result + (this.yoyCarbons == null ? 0 : this.yoyCarbons.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("date", date)
            .add("carbons", carbons)
            .add("chainCarbons", chainCarbons)
            .add("yoyCarbons", yoyCarbons)
            .toString();
    }
}
