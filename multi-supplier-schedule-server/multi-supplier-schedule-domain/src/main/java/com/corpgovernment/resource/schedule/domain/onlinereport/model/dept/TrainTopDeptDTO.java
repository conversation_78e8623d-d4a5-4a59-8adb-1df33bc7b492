package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
public class TrainTopDeptDTO {

    @Column(name = "aggId")
    @Type(value = Types.VARCHAR)
    private String aggId;

    @Column(name = "aggType")
    @Type(value = Types.VARCHAR)
    private String aggType;

    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;

    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    @Column(name = "totalRefundtkt")
    @Type(value = Types.INTEGER)
    private Integer totalRefundtkt;

    @Column(name = "totalRebooktkt")
    @Type(value = Types.INTEGER)
    private Integer totalRebooktkt;

    @Column(name = "totalOrdertkt")
    @Type(value = Types.INTEGER)
    private Integer totalOrdertkt;

    @Column(name = "totalPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalPrice;

    @Column(name = "totalAllOrderCount")
    @Type(value = Types.INTEGER)
    private Integer totalAllOrderCount;

    @Column(name = "hotLine")
    @Type(value = Types.VARCHAR)
    private String hotLine;

    @Column(name = "hotSeat")
    @Type(value = Types.VARCHAR)
    private String hotSeat;

    @Column(name = "totalCarbons")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCarbons;

    @Column(name = "totalRcTimes")
    @Type(value = Types.INTEGER)
    private Integer totalRcTimes;

    @Column(name = "totalOrderCount")
    @Type(value = Types.INTEGER)
    private Integer totalOrderCount;
}
