package com.corpgovernment.resource.schedule.domain.pricecomparison.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class QueryXrayComparePriceUploadDateRequestType {
    @ApiModelProperty("数据上传日期范围开始时间")
    @JsonProperty("startDate")
    private Integer startDate;
    @ApiModelProperty("数据上传日期范围结束时间")
    @JsonProperty("endDate")
    private Integer endDate;
    @ApiModelProperty("产品类型 1， 酒店比价； 2 机票比价")
    @JsonProperty("productType")
    private Integer productType;
    @ApiModelProperty("corpID")
    private String corpID;
}
