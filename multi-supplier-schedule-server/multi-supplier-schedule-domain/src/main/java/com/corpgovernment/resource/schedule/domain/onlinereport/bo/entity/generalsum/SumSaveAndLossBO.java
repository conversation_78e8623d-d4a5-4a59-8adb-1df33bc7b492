package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.generalsum;

import java.util.List;

/**
 * @Description: 首页节省与损失
 * <AUTHOR>
 * @Date 2019/6/25
 */
public class SumSaveAndLossBO {

    //总节省
    private double totalSave;
    //总损失
    private double totalLoss;
    //机票节省
    private List<ReportGeneralDetailBO> flightSave;
    //机票损失
    private List<ReportGeneralDetailBO> flightLoss;
    //酒店节省
    private List<ReportGeneralDetailBO> hotelSave;
    //酒店损失
    private List<ReportGeneralDetailBO> hotelLoss;

    public double getTotalSave() {
        return totalSave;
    }

    public void setTotalSave(double totalSave) {
        this.totalSave = totalSave;
    }

    public double getTotalLoss() {
        return totalLoss;
    }

    public void setTotalLoss(double totalLoss) {
        this.totalLoss = totalLoss;
    }

    public List<ReportGeneralDetailBO> getFlightSave() {
        return flightSave;
    }

    public void setFlightSave(List<ReportGeneralDetailBO> flightSave) {
        this.flightSave = flightSave;
    }

    public List<ReportGeneralDetailBO> getFlightLoss() {
        return flightLoss;
    }

    public void setFlightLoss(List<ReportGeneralDetailBO> flightLoss) {
        this.flightLoss = flightLoss;
    }

    public List<ReportGeneralDetailBO> getHotelSave() {
        return hotelSave;
    }

    public void setHotelSave(List<ReportGeneralDetailBO> hotelSave) {
        this.hotelSave = hotelSave;
    }

    public List<ReportGeneralDetailBO> getHotelLoss() {
        return hotelLoss;
    }

    public void setHotelLoss(List<ReportGeneralDetailBO> hotelLoss) {
        this.hotelLoss = hotelLoss;
    }
}
