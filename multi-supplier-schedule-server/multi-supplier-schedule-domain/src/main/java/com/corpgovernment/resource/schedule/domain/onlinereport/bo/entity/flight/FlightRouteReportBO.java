package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

/**
 * @Auther: ddzhan
 * @Date: 2019/5/14
 * @Description:
 */
public class FlightRouteReportBO implements  Comparable<FlightRouteReportBO> {

   private int fltQuantity;
   private String fltPrice;
   private String fltFlightclass;
    private String fltFlightclassId;
   private String fltAirroute2;
    private String fltAirroute;
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getFltQuantity() {
        return fltQuantity;
    }

    public void setFltQuantity(int fltQuantity) {
        this.fltQuantity = fltQuantity;
    }

    public String getFltPrice() {
        return fltPrice;
    }

    public void setFltPrice(String fltPrice) {
        this.fltPrice = fltPrice;
    }

    public String getFltFlightclass() {
        return fltFlightclass;
    }

    public void setFltFlightclass(String fltFlightclass) {
        this.fltFlightclass = fltFlightclass;
    }

    public String getFltAirroute2() {
        return fltAirroute2;
    }

    public String getFltAirroute() {
        return fltAirroute;
    }

    public void setFltAirroute(String fltAirroute) {
        this.fltAirroute = fltAirroute;
    }

    public void setFltAirroute2(String fltAirroute2) {
        this.fltAirroute2 = fltAirroute2;
    }

    public String getFltFlightclassId() {
        return fltFlightclassId;
    }

    public void setFltFlightclassId(String fltFlightclassId) {
        this.fltFlightclassId = fltFlightclassId;
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }



    @Override
    public int hashCode() {
        return super.hashCode();
    }



    @Override
    public int compareTo(FlightRouteReportBO o) {
        if(o.equals(this))
            return 0;
        return (o.getFltQuantity()) - (this.getFltQuantity());
    }

}
