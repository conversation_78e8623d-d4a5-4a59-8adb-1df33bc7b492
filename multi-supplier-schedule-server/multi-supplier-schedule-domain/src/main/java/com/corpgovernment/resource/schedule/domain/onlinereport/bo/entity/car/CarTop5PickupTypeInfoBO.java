package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.car;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 前5接送机车型分析详情
 * <AUTHOR>
 * @Date 2019/6/19 11:20
 */
public class CarTop5PickupTypeInfoBO {

    public CarTop5PickupTypeInfoBO(){
        this.domTypes=new ArrayList<>(0);
        this.interTypes=new ArrayList<>(0);
    }

    /*国内接送机--车型*/
    private List<CarTypeInfoBO> domTypes;
    /*国际接送机--车型 */
    private List<CarTypeInfoBO> interTypes;

    public List<CarTypeInfoBO> getDomTypes() {
        return domTypes;
    }

    public void setDomTypes(List<CarTypeInfoBO> domTypes) {
        this.domTypes = domTypes;
    }

    public List<CarTypeInfoBO> getInterTypes() {
        return interTypes;
    }

    public void setInterTypes(List<CarTypeInfoBO> interTypes) {
        this.interTypes = interTypes;
    }
}
