package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;

import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/1/22
 */
public enum AgreementStatusLabelEnum {
    /**
     * 协议签约中
     */
    SIGNING("SIGNING", "supplierMonitor.agreement.status.signing"),
    /**
     * 协议生效中
     */
    SIGNED("SIGNED", "supplierMonitor.agreement.status.signed"),
    /**
     * 协议即将到期
     */
    EXPIRING("EXPIRING", "supplierMonitor.agreement.status.expiring"),
    /**
     * 协议已到期
     */
    EXPIRED("EXPIRED", "supplierMonitor.agreement.status.expired"),
    /**
     * 未签署协议
     */
    DEFAULT("DEFAULT", "supplierMonitor.agreement.status.unsigned");

    private String code;
    private String sharkKey;

    public static String getNameByCode(String code, String lang) {
        if (StringUtils.isEmpty(code)) {
            return SharkUtils.get(DEFAULT.getSharkKey(), lang);
        }
        for (AgreementStatusLabelEnum agreementStatusLabelEnum : AgreementStatusLabelEnum.values()) {
            if (agreementStatusLabelEnum.getCode().equals(code)) {
                return SharkUtils.get(agreementStatusLabelEnum.getSharkKey(), lang);
            }
        }
        return StringUtils.EMPTY;
    }


    AgreementStatusLabelEnum(String code, String sharkKey) {
        this.code = code;
        this.sharkKey = sharkKey;
    }

    public String getCode() {
        return code;
    }

    public String getSharkKey() {
        return sharkKey;
    }
}
