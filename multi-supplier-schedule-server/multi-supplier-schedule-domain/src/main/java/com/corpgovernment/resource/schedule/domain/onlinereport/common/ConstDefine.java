package com.corpgovernment.resource.schedule.domain.onlinereport.common;


import com.corpgovernment.resource.schedule.domain.onlinereport.config.ApplicationConfig;

/**
 * @Description: 关常量
 * <AUTHOR>
 * @Date 2020/5/1
 */
public class ConstDefine {

    /**
     * Es 類型定義
     */
    public static final String BIZ_FLIGHT = "F";
    public static final String BIZ_HOTEL = "H";
    public static final String BIZ_TRAINCAR = "A";
    public static final String BIZ_BASE = "B";

   /* public static final String ES_FLIGHT_SHORT = "bi_flight_short";
    public static final String ES_HOTEL_SHORT = "bi_hotel_short";
    public static final String ES_TRAIN_SHORT = "bi_train_short";
    public static final String ES_CAR_SHORT = "bi_car_short";*/
    public static final String ES_FLIGHT_SHORT = ApplicationConfig.get("flight_short_index");
    public static final String ES_HOTEL_SHORT = ApplicationConfig.get("hotel_short_index");
    public static final String ES_TRAIN_SHORT = ApplicationConfig.get("train_short_index");
    public static final String ES_CAR_SHORT = ApplicationConfig.get("car_short_index");

    public static final int FLIGHT_INDEX = 1;
    public static final int HOTEL_INDEX = 2;
    public static final int TRAIN_INDEX = 3;
    public static final int CAR_INDEX= 4;

    public static final String  FLIGHT_STATUS = "done";


    public static final Integer  MAX_PAGESIZE = 50;

    public static final Integer NEGATIVE_ONE = -1;

    public static final Integer SUCESS_STATUS_COE = 0;
    public static final Integer FAIL_STATUS_COE = 1;
    public static final Integer SYTEMERROR_STATUS_COE = 2;

    public static final String ONE = "1";

    public static final String CTRIP_CORP = "ctrip";

    public static final String CTRIP_GROUP = "Gr_00000168";
    // 老版平台权益套餐
    public static final String PACKAGE_SVIP_PLATFORM_KEY = "PACKAGE_SVIP_PLATFORM";
    // 老版平台默认套餐
    public static final String PACKAGE_DEFAULT_PLATFORM_KEY = "PACKAGE_DEFAULT_PLATFORM";
    // 老版大客默认套餐
    public static final String PACKAGE_DEFAULT_CTRIP_KEY = "PACKAGE_DEFAULT_CTRIP";

    public static final String SYSTEM_ERROR = "CommonInfo-sytemerror";

    public static final String PARAM_ERROR = "CommonInfo-paramerror";

    public static final String NO_AUTH = "CommonInfo-noauth";

    public static final String FAIL = "CommonInfo-fail";

    public static final String SUCESS = "CommonInfo-sucess";

    public static final String USERNOTEXIST = "UserNotExist";

    public static final String PACKAGENOTEXIST = "PackageNotExist";

    public static final String NO_DATA = "CommonInfo-nodata";

    // 新版专业版
    public static final String PACKAGE_NEW_SVIP_PLATFORM_KEY = "PACKAGE_NEW_SVIP_PLATFORM";

    // 新版专家版
    public static final String PACKAGE_NEW_SSVIP_PLATFORM_KEY = "PACKAGE_NEW_SSVIP_PLATFORM";

    // 新版普通版
    public static final String PACKAGE_NEW_DEFAULT_PLATFORM_KEY = "PACKAGE_NEW_DEFAULT_PLATFORM";

   // 国际站点package
   public static final String PACKAGE_INT_POS_FORMAT = "PACKAGE_INT_POS_%s";

   public static final String USER_IS_NOT_VAILD = "UserIsNotVaild";

   // 超过最大绑定数量
   public static final String OVER_MAX_MAPPING_COUNT = "OverMaxMappingCount";


   //针对华圣客户，不开放在线报告权限
    public static final String HUASEHNG_FORBID_MESSAGE = "HUASEHNG_FORBID_MESSAGE";


    public static final String SHARK_PARAM_ERROR_KEY = "soa.param.verify.tip1";

    public static final String SHARK_AUTH_ERROR_KEY = "soa.param.verify.tip2";

    public static final String CHECK_POS_EMPTY = "CHECK_POS_EMPTY";

    public static final String QUERY_BASE_DATA_FROM_CK = "QUERY_BASE_DATA_FROM_CK";

    // 华圣默认套餐
    public static final String PACKAGE_DEFAULT_HUASHENG_KEY = "PACKAGE_DEFAULT_HUASHENG";

    // 客户经理助理
    public static final String ASSISTANT_CN = "ASSISTANT";

    // tripal通知销售经理 标题
    public static final String TRIPAL_SALES_MANAGER_NOTIFICATION_TITLE = "TripalSalesManagerNoteTitle";

    // tripal通知销售经理 描述
    public static final String TRIPAL_SALES_MANAGER_NOTIFICATION_DESCRIBE = "TripalSalesManagerNoteDesc";

    // tripal通知销售经理详情链接
    public static final String TRIPAL_SALES_MANAGER_NOTIFICATION_URL = "TripalSalesManagerNoteUrl";

    // tripal通知销售经理 卡号
    public static final String TRIPAL_SALES_MANAGER_NOTIFICATION_CARD = "TripalSalesManagerNoteCard";

}
