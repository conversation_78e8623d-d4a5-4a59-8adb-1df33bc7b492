package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 差标计算器城市类别-城市联动筛选
 * ****************************************差标计算器*********************************************
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "corpIds",
    "cityTypes",
    "productType",
    "searchScope"
})
public class OnlineReportTravelStandardCityRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportTravelStandardCityRequest(
        List<String> corpIds,
        List<Integer> cityTypes,
        String productType,
        Integer searchScope) {
        this.corpIds = corpIds;
        this.cityTypes = cityTypes;
        this.productType = productType;
        this.searchScope = searchScope;
    }

    public OnlineReportTravelStandardCityRequest() {
    }

    /**
     * 公司
     */
    @JsonProperty("corpIds")
    public List<String> corpIds;

    /**
     * 城市类别
     */
    @JsonProperty("cityTypes")
    public List<Integer> cityTypes;

    /**
     * 国内dom、国际inter、全部all
     */
    @JsonProperty("productType")
    public String productType;

    /**
     * 0: 城市类别, 1: 城市
     */
    @JsonProperty("searchScope")
    public Integer searchScope;

    /**
     * 公司
     */
    public List<String> getCorpIds() {
        return corpIds;
    }

    /**
     * 公司
     */
    public void setCorpIds(final List<String> corpIds) {
        this.corpIds = corpIds;
    }

    /**
     * 城市类别
     */
    public List<Integer> getCityTypes() {
        return cityTypes;
    }

    /**
     * 城市类别
     */
    public void setCityTypes(final List<Integer> cityTypes) {
        this.cityTypes = cityTypes;
    }

    /**
     * 国内dom、国际inter、全部all
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 国内dom、国际inter、全部all
     */
    public void setProductType(final String productType) {
        this.productType = productType;
    }

    /**
     * 0: 城市类别, 1: 城市
     */
    public Integer getSearchScope() {
        return searchScope;
    }

    /**
     * 0: 城市类别, 1: 城市
     */
    public void setSearchScope(final Integer searchScope) {
        this.searchScope = searchScope;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTravelStandardCityRequest other = (OnlineReportTravelStandardCityRequest)obj;
        return
            Objects.equal(this.corpIds, other.corpIds) &&
            Objects.equal(this.cityTypes, other.cityTypes) &&
            Objects.equal(this.productType, other.productType) &&
            Objects.equal(this.searchScope, other.searchScope);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.corpIds == null ? 0 : this.corpIds.hashCode());
        result = 31 * result + (this.cityTypes == null ? 0 : this.cityTypes.hashCode());
        result = 31 * result + (this.productType == null ? 0 : this.productType.hashCode());
        result = 31 * result + (this.searchScope == null ? 0 : this.searchScope.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("corpIds", corpIds)
            .add("cityTypes", cityTypes)
            .add("productType", productType)
            .add("searchScope", searchScope)
            .toString();
    }
}
