package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 航线以及承运商分析
 * <AUTHOR>
 * @Date 2019/5/23 15:55
 */
public class FlightCarriersAndAirLineAnysisBO implements Serializable{

    private static final long serialVersionUID = 1L;

    //折扣分布

    List<FlightRateInfoBO> flightRateInfoList;
    //起飞时间段分析

    List<FlightTakeOffTimeBO> flightTakeOffTimeList;

    //承运商分析
    FlightCarrierAnalysisInfoBO flightCarrierAnalysisInfoBO;

    //航线分析
    FlightAirlineAnysisResponseTypeBO fltAirlineAnysisResponseTypeBO;


    public List<FlightRateInfoBO> getFlightRateInfoList() {
        return flightRateInfoList;
    }

    public void setFlightRateInfoList(List<FlightRateInfoBO> flightRateInfoList) {
        this.flightRateInfoList = flightRateInfoList;
    }

    public List<FlightTakeOffTimeBO> getFlightTakeOffTimeList() {
        return flightTakeOffTimeList;
    }

    public void setFlightTakeOffTimeList(List<FlightTakeOffTimeBO> flightTakeOffTimeList) {
        this.flightTakeOffTimeList = flightTakeOffTimeList;
    }

    public FlightCarrierAnalysisInfoBO getFlightCarrierAnalysisInfoBO() {
        return flightCarrierAnalysisInfoBO;
    }

    public void setFlightCarrierAnalysisInfoBO(FlightCarrierAnalysisInfoBO flightCarrierAnalysisInfoBO) {
        this.flightCarrierAnalysisInfoBO = flightCarrierAnalysisInfoBO;
    }
}
