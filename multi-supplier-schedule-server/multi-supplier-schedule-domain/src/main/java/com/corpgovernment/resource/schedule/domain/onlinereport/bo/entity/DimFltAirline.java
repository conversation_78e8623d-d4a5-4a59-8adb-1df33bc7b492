package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity;

/**
 * @Description: 航司信息
 * <AUTHOR>
 * @Date 2019/3/22
 */
public class DimFltAirline {

    //航司二字码
    private String airLineCode2;
    //航司三字码
    private String airLineCode3;
    //中文名
    private String airLine;
    //中文名
    private String airLineSN;
    //
    private String flightClassId;
    //
    private String currentFlag;
    //英文名
    private String airlineNameEng;
    //tel
    private String tel;

    public String getAirLineCode2() {
        return airLineCode2;
    }

    public void setAirLineCode2(String airLineCode2) {
        this.airLineCode2 = airLineCode2;
    }

    public String getAirLineCode3() {
        return airLineCode3;
    }

    public void setAirLineCode3(String airLineCode3) {
        this.airLineCode3 = airLineCode3;
    }

    public String getAirLine() {
        return airLine;
    }

    public void setAirLine(String airLine) {
        this.airLine = airLine;
    }

    public String getAirLineSN() {
        return airLineSN;
    }

    public void setAirLineSN(String airLineSN) {
        this.airLineSN = airLineSN;
    }

    public String getFlightClassId() {
        return flightClassId;
    }

    public void setFlightClassId(String flightClassId) {
        this.flightClassId = flightClassId;
    }

    public String getCurrentFlag() {
        return currentFlag;
    }

    public void setCurrentFlag(String currentFlag) {
        this.currentFlag = currentFlag;
    }

    public String getAirlineNameEng() {
        return airlineNameEng;
    }

    public void setAirlineNameEng(String airlineNameEng) {
        this.airlineNameEng = airlineNameEng;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }
}
