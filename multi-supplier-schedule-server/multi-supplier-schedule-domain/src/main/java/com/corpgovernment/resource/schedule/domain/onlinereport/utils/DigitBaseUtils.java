package com.corpgovernment.resource.schedule.domain.onlinereport.utils;


import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Optional;

/**
 * Auther:abguo
 * Date:2019/9/1
 * Description:
 * Project:onlinereportweb
 */
public class DigitBaseUtils {

    public static final String STRING_ZERO = "0";


    /**
     * 除法运算
     * @param dumerator   分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal divide(Double dumerator, Double denominator) {
        return divide(dumerator,denominator, GlobalConst.NEW_DIGIT_NUM);
    }

    /**
     * 除法运算
     * @param dumerator   分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal divide(BigDecimal dumerator, BigDecimal denominator) {
        if (denominator==null) {
            return BigDecimal.ZERO;
        }
        if (dumerator==null) {
            return BigDecimal.ZERO;
        }
        return divide(dumerator.doubleValue(),denominator.doubleValue(), GlobalConst.NEW_DIGIT_NUM);
    }

    /**
     * 除法运算
     * @param dumerator   分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal divide(Integer dumerator, Integer denominator) {
        if (denominator==null) {
            return BigDecimal.ZERO;
        }
        if (dumerator==null) {
            return BigDecimal.ZERO;
        }
        return divide(new BigDecimal(dumerator),new BigDecimal(denominator), GlobalConst.NEW_DIGIT_NUM);
    }

    /**
     * 除法运算
     * @param dumerator   分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal divide(Integer dumerator, Integer denominator, int digit_num) {
        if (denominator==null) {
            return BigDecimal.ZERO;
        }
        if (dumerator==null) {
            return BigDecimal.ZERO;
        }
        return divide(new BigDecimal(dumerator),new BigDecimal(denominator), digit_num);
    }

    /**
     * 除法运算
     * @param dumerator   分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal divide(BigDecimal dumerator, BigDecimal denominator, int size) {
        if (denominator==null) {
            return BigDecimal.ZERO;
        }
        if (dumerator==null) {
            return BigDecimal.ZERO;
        }
        return divide(dumerator.doubleValue(),denominator.doubleValue(), size);
    }

    /**
     * 除法运算
     * @param dumerator 分子
     * @param denominator 分母
     * @param digit_num 保留小数位数
     * @return
     */
    public static BigDecimal divide(Double dumerator, Double denominator,int digit_num) {
        if (denominator==null||denominator==0) {
            return BigDecimal.ZERO;
        }
        if (dumerator==null||dumerator == 0) {
            return BigDecimal.ZERO;
        }
        if (digit_num <= 0){
            digit_num = GlobalConst.NEW_DIGIT_NUM;
        }
        BigDecimal bgNumerator = new BigDecimal(dumerator);
        BigDecimal bgDenominator = new BigDecimal(denominator);
        return bgNumerator.divide(bgDenominator, digit_num, RoundingMode.HALF_UP);
    }

    /**
     * 减法运算
     * @param minuend    被减数
     * @param subtrahend 减数
     * @return
     */
    public static BigDecimal subtract(Double minuend, Double subtrahend) {
        if (minuend==null) {
            minuend = new Double(0);
        }
        if (subtrahend==null) {
            subtrahend = new Double(0);
        }
        BigDecimal bgMinuend = new BigDecimal(minuend);
        BigDecimal bgSubtrahend = new BigDecimal(subtrahend);
        return bgMinuend.subtract(bgSubtrahend);
    }

    /**
     * 减法运算
     * @param minuend    被减数
     * @param subtrahend 减数
     * @return
     */
    public static BigDecimal add(Double minuend, Double subtrahend) {
        if (minuend==null) {
            minuend = new Double(0);
        }
        if (subtrahend==null) {
            subtrahend = new Double(0);
        }
        BigDecimal bgMinuend = new BigDecimal(minuend);
        BigDecimal bgSubtrahend = new BigDecimal(subtrahend);
        return bgMinuend.add(bgSubtrahend);
    }

    /**
     * 减法运算
     * @param minuend    被减数
     * @param subtrahend 减数
     * @return
     */
    public static BigDecimal add(BigDecimal minuend, BigDecimal subtrahend) {
        if (minuend==null) {
            minuend = BigDecimal.ZERO;
        }
        if (subtrahend==null) {
            subtrahend = BigDecimal.ZERO;
        }
        return minuend.add(subtrahend);
    }

    /**
     * 乘法运算
     * @param minuend
     * @param subtrahend
     * @return
     */
    public static BigDecimal multiply(BigDecimal minuend, BigDecimal subtrahend) {
        if (minuend==null) {
            minuend = BigDecimal.ZERO;
        }
        if (subtrahend==null) {
            subtrahend = BigDecimal.ZERO;
        }
        return minuend.multiply(subtrahend);
    }

    public static String parsePercent(Double target) {
        return parsePercent(target,2);
    }
    public static String parsePercent(Double target,int num) {
        if (target==null) {
            target = new Double(0);
        }
        //modify by Jason Ding 2020/07/29
        //target = Math.abs(target);
        NumberFormat nf = NumberFormat.getPercentInstance();
        nf.setMinimumFractionDigits(num);
        nf.setGroupingUsed(false);
        return nf.format(target);
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static String formatDigit(String target) {
        return formatDigit(target,GlobalConst.NEW_DIGIT_NUM);
    }
    /**
     * 格式化
     * @param target
     * @return
     */
    public static BigDecimal formatDigitV2(String target) {
        if (StringUtils.isEmpty(target)||StringUtils.isBlank(target)) {
            target = GlobalConst.STRING_ZERO;
        }
        BigDecimal bgDenominator = new BigDecimal(target);
        return formatDigit(bgDenominator);
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static String formatDigit(String target,int digit_num) {
        if (StringUtils.isEmpty(target)||StringUtils.isBlank(target)) {
            target = GlobalConst.STRING_ZERO;
        }
        if (digit_num <= 0){
            digit_num = GlobalConst.NEW_DIGIT_NUM;
        }
        BigDecimal bgDenominator = new BigDecimal(target);
        return formatDigit(bgDenominator.doubleValue(),digit_num);
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static String formatDigit(Double target) {
        return formatDigit(target,GlobalConst.NEW_DIGIT_NUM);
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static BigDecimal formatDigitV2(Double target) {
        target = Optional.ofNullable(target).orElse(0d);
        return formatDigit(new BigDecimal(target));
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static BigDecimal convertFormatDigit(Double target) {
        if (target==null) {
            target = new Double(0);
        }
        return formatDigit(new BigDecimal(target));
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static BigDecimal convertFormatDigit(BigDecimal target, int digit_num) {
        if (target==null) {
            target = BigDecimal.ZERO;
        }
        if (digit_num < 0){
            digit_num = 0;
        }
        return target.setScale(digit_num,BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static BigDecimal convertFormatDigit(Double target, int digit_num) {
        if (target==null) {
            target = 0d;
        }
        if (digit_num <= 0){
            digit_num = GlobalConst.NEW_DIGIT_NUM;
        }
        return new BigDecimal(target).setScale(digit_num,BigDecimal.ROUND_HALF_UP);
    }
    /**
     * 格式化
     * @param target
     * @return
     */
    public static BigDecimal formatDigit(Float target) {
        return formatDigitBigDecimal(target, GlobalConst.NEW_DIGIT_NUM);
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static BigDecimal formatDigitBigDecimal(Float target,int digit_num) {
        if (target==null) {
            target = new Float(0);
        }
        if (digit_num <= 0){
            digit_num = GlobalConst.NEW_DIGIT_NUM;
        }
        BigDecimal bgDenominator = new BigDecimal(target);
        return bgDenominator.setScale(digit_num,BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static BigDecimal formatDigit(Float target,int digit_num) {
        if (target==null) {
            target = new Float(0);
        }
        if (digit_num <= 0){
            digit_num = GlobalConst.NEW_DIGIT_NUM;
        }
        BigDecimal bgDenominator = new BigDecimal(target);
        return bgDenominator.setScale(digit_num,BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static String formatDigit(Double target,int digit_num) {
        if (target==null) {
            target = new Double(0);
        }
        if (digit_num <= 0){
            digit_num = GlobalConst.NEW_DIGIT_NUM;
        }
        BigDecimal bgDenominator = new BigDecimal(target);
        return bgDenominator.setScale(digit_num,BigDecimal.ROUND_HALF_UP).toString();
    }

    public static String formatDigit(BigDecimal target,int digit_num) {
        if (target==null) {
            target = BigDecimal.ZERO;
        }
        if (digit_num <= 0){
            digit_num = GlobalConst.NEW_DIGIT_NUM;
        }
        return target.setScale(digit_num,BigDecimal.ROUND_HALF_UP).toString();
    }

    public static BigDecimal formatDigit1(BigDecimal target,int digit_num) {
        if (target==null) {
            target = BigDecimal.ZERO;
        }
        if (digit_num <= 0){
            digit_num = GlobalConst.NEW_DIGIT_NUM;
        }
        return target.setScale(digit_num,BigDecimal.ROUND_HALF_UP);
    }

    public static double convertStringToDouble(String target){
        double data = 0;
        if (StringUtils.isEmpty(target)||StringUtils.isBlank(target)) {
            return data;
        }
        try {
            return Double.valueOf(target);
        }catch (Exception e){
            data = 0;

        }
        return data;
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static BigDecimal formatDigit(BigDecimal target) {
        if (target==null) {
            target = BigDecimal.ZERO;
        }
        return formatBigDecimal(target.doubleValue(),GlobalConst.NEW_DIGIT_NUM);
    }

    /**
     * 格式化
     * @param target
     * @return
     */
    public static BigDecimal formatBigDecimal(Double target,int digit_num) {
        if (target==null) {
            target = new Double(0);
        }
        if (digit_num <= 0){
            digit_num = GlobalConst.NEW_DIGIT_NUM;
        }
        BigDecimal bgDenominator = new BigDecimal(target);
        return bgDenominator.setScale(digit_num,BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 默认值
     * @param target
     * @return
     */
    public static int intgerDefault(Integer target) {
        if (target==null) {
            target = 0;
        }
        return target;
    }

    /**
     * 默认值
     * @param target
     * @return
     */
    public static long intgerToLong(Integer target) {
        if (target==null) {
            target = 0;
        }
        return target.longValue();
    }

    public static void main(String[] args) {
        System.out.println(DigitBaseUtils.formatDigit(DigitBaseUtils.divide(3728254.00,3677969.59).doubleValue()));
    }
}
