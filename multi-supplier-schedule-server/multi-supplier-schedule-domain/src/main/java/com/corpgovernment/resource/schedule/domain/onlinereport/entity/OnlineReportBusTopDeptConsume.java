package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 汽车
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "dimId",
        "dim",
        "totalAmount",
        "totalOrderCount",
        "amountPercent"
})
public class OnlineReportBusTopDeptConsume implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    @JsonProperty("dimId")
    public String dimId;
    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    @JsonProperty("dim")
    public String dim;
    /**
     * 消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;
    /**
     * 订单数
     */
    @JsonProperty("totalOrderCount")
    public Integer totalOrderCount;
    /**
     * 消费金额占比
     */
    @JsonProperty("amountPercent")
    public BigDecimal amountPercent;

    public OnlineReportBusTopDeptConsume(
            String dimId,
            String dim,
            BigDecimal totalAmount,
            Integer totalOrderCount,
            BigDecimal amountPercent) {
        this.dimId = dimId;
        this.dim = dim;
        this.totalAmount = totalAmount;
        this.totalOrderCount = totalOrderCount;
        this.amountPercent = amountPercent;
    }

    public OnlineReportBusTopDeptConsume() {
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public String getDimId() {
        return dimId;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public void setDimId(final String dimId) {
        this.dimId = dimId;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public String getDim() {
        return dim;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 订单数
     */
    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }

    /**
     * 订单数
     */
    public void setTotalOrderCount(final Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }

    /**
     * 消费金额占比
     */
    public BigDecimal getAmountPercent() {
        return amountPercent;
    }

    /**
     * 消费金额占比
     */
    public void setAmountPercent(final BigDecimal amountPercent) {
        this.amountPercent = amountPercent;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.dimId;
            case 1:
                return this.dim;
            case 2:
                return this.totalAmount;
            case 3:
                return this.totalOrderCount;
            case 4:
                return this.amountPercent;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.dimId = (String) fieldValue;
                break;
            case 1:
                this.dim = (String) fieldValue;
                break;
            case 2:
                this.totalAmount = (BigDecimal) fieldValue;
                break;
            case 3:
                this.totalOrderCount = (Integer) fieldValue;
                break;
            case 4:
                this.amountPercent = (BigDecimal) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportBusTopDeptConsume other = (OnlineReportBusTopDeptConsume) obj;
        return
                Objects.equal(this.dimId, other.dimId) &&
                        Objects.equal(this.dim, other.dim) &&
                        Objects.equal(this.totalAmount, other.totalAmount) &&
                        Objects.equal(this.totalOrderCount, other.totalOrderCount) &&
                        Objects.equal(this.amountPercent, other.amountPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dimId == null ? 0 : this.dimId.hashCode());
        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.totalOrderCount == null ? 0 : this.totalOrderCount.hashCode());
        result = 31 * result + (this.amountPercent == null ? 0 : this.amountPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("dimId", dimId)
                .add("dim", dim)
                .add("totalAmount", totalAmount)
                .add("totalOrderCount", totalOrderCount)
                .add("amountPercent", amountPercent)
                .toString();
    }
}
