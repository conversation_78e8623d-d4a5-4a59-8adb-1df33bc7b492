package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "totalOrdercnt",
        "avgMonthOrdercnt",
        "yoyOrdercntLast",
        "yoyOrdercntBeforeLast",
        "momOrdercnt",
        "yoyLastRatio",
        "yoyBeforeLastRatio",
        "momRatio"
})
public class OnlineReportConsumeOrdercnt implements Serializable {
    private static final long serialVersionUID = 1L;


    public OnlineReportConsumeOrdercnt(
            Integer totalOrdercnt,
            BigDecimal avgMonthOrdercnt,
            Integer yoyOrdercntLast,
            Integer yoyOrdercntBeforeLast,
            Integer momOrdercnt,
            BigDecimal yoyLastRatio,
            BigDecimal yoyBeforeLastRatio,
            BigDecimal momRatio) {
        this.totalOrdercnt = totalOrdercnt;
        this.avgMonthOrdercnt = avgMonthOrdercnt;
        this.yoyOrdercntLast = yoyOrdercntLast;
        this.yoyOrdercntBeforeLast = yoyOrdercntBeforeLast;
        this.momOrdercnt = momOrdercnt;
        this.yoyLastRatio = yoyLastRatio;
        this.yoyBeforeLastRatio = yoyBeforeLastRatio;
        this.momRatio = momRatio;
    }

    public OnlineReportConsumeOrdercnt() {
    }

    /**
     * 总 订单
     */
    @JsonProperty("totalOrdercnt")
    public Integer totalOrdercnt;

    /**
     * 月均 订单数
     */
    @JsonProperty("avgMonthOrdercnt")
    public BigDecimal avgMonthOrdercnt;

    /**
     * 同比去年 订单数
     */
    @JsonProperty("yoyOrdercntLast")
    public Integer yoyOrdercntLast;

    /**
     * 同比前年行程/订单数
     */
    @JsonProperty("yoyOrdercntBeforeLast")
    public Integer yoyOrdercntBeforeLast;

    /**
     * 环比行程/订单数
     */
    @JsonProperty("momOrdercnt")
    public Integer momOrdercnt;

    /**
     * 同比去年 订单比值
     */
    @JsonProperty("yoyLastRatio")
    public BigDecimal yoyLastRatio;

    /**
     * 同比前年行程/订单比值
     */
    @JsonProperty("yoyBeforeLastRatio")
    public BigDecimal yoyBeforeLastRatio;

    /**
     * 环比行程/订单比值
     */
    @JsonProperty("momRatio")
    public BigDecimal momRatio;

    /**
     * 总 订单
     */
    public Integer getTotalOrdercnt() {
        return totalOrdercnt;
    }

    /**
     * 总 订单
     */
    public void setTotalOrdercnt(final Integer totalOrdercnt) {
        this.totalOrdercnt = totalOrdercnt;
    }

    /**
     * 月均 订单数
     */
    public BigDecimal getAvgMonthOrdercnt() {
        return avgMonthOrdercnt;
    }

    /**
     * 月均 订单数
     */
    public void setAvgMonthOrdercnt(final BigDecimal avgMonthOrdercnt) {
        this.avgMonthOrdercnt = avgMonthOrdercnt;
    }

    /**
     * 同比去年 订单数
     */
    public Integer getYoyOrdercntLast() {
        return yoyOrdercntLast;
    }

    /**
     * 同比去年 订单数
     */
    public void setYoyOrdercntLast(final Integer yoyOrdercntLast) {
        this.yoyOrdercntLast = yoyOrdercntLast;
    }

    /**
     * 同比前年行程/订单数
     */
    public Integer getYoyOrdercntBeforeLast() {
        return yoyOrdercntBeforeLast;
    }

    /**
     * 同比前年行程/订单数
     */
    public void setYoyOrdercntBeforeLast(final Integer yoyOrdercntBeforeLast) {
        this.yoyOrdercntBeforeLast = yoyOrdercntBeforeLast;
    }

    /**
     * 环比行程/订单数
     */
    public Integer getMomOrdercnt() {
        return momOrdercnt;
    }

    /**
     * 环比行程/订单数
     */
    public void setMomOrdercnt(final Integer momOrdercnt) {
        this.momOrdercnt = momOrdercnt;
    }

    /**
     * 同比去年 订单比值
     */
    public BigDecimal getYoyLastRatio() {
        return yoyLastRatio;
    }

    /**
     * 同比去年 订单比值
     */
    public void setYoyLastRatio(final BigDecimal yoyLastRatio) {
        this.yoyLastRatio = yoyLastRatio;
    }

    /**
     * 同比前年行程/订单比值
     */
    public BigDecimal getYoyBeforeLastRatio() {
        return yoyBeforeLastRatio;
    }

    /**
     * 同比前年行程/订单比值
     */
    public void setYoyBeforeLastRatio(final BigDecimal yoyBeforeLastRatio) {
        this.yoyBeforeLastRatio = yoyBeforeLastRatio;
    }

    /**
     * 环比行程/订单比值
     */
    public BigDecimal getMomRatio() {
        return momRatio;
    }

    /**
     * 环比行程/订单比值
     */
    public void setMomRatio(final BigDecimal momRatio) {
        this.momRatio = momRatio;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportConsumeOrdercnt other = (OnlineReportConsumeOrdercnt) obj;
        return
                Objects.equal(this.totalOrdercnt, other.totalOrdercnt) &&
                        Objects.equal(this.avgMonthOrdercnt, other.avgMonthOrdercnt) &&
                        Objects.equal(this.yoyOrdercntLast, other.yoyOrdercntLast) &&
                        Objects.equal(this.yoyOrdercntBeforeLast, other.yoyOrdercntBeforeLast) &&
                        Objects.equal(this.momOrdercnt, other.momOrdercnt) &&
                        Objects.equal(this.yoyLastRatio, other.yoyLastRatio) &&
                        Objects.equal(this.yoyBeforeLastRatio, other.yoyBeforeLastRatio) &&
                        Objects.equal(this.momRatio, other.momRatio);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.totalOrdercnt == null ? 0 : this.totalOrdercnt.hashCode());
        result = 31 * result + (this.avgMonthOrdercnt == null ? 0 : this.avgMonthOrdercnt.hashCode());
        result = 31 * result + (this.yoyOrdercntLast == null ? 0 : this.yoyOrdercntLast.hashCode());
        result = 31 * result + (this.yoyOrdercntBeforeLast == null ? 0 : this.yoyOrdercntBeforeLast.hashCode());
        result = 31 * result + (this.momOrdercnt == null ? 0 : this.momOrdercnt.hashCode());
        result = 31 * result + (this.yoyLastRatio == null ? 0 : this.yoyLastRatio.hashCode());
        result = 31 * result + (this.yoyBeforeLastRatio == null ? 0 : this.yoyBeforeLastRatio.hashCode());
        result = 31 * result + (this.momRatio == null ? 0 : this.momRatio.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("totalOrdercnt", totalOrdercnt)
                .add("avgMonthOrdercnt", avgMonthOrdercnt)
                .add("yoyOrdercntLast", yoyOrdercntLast)
                .add("yoyOrdercntBeforeLast", yoyOrdercntBeforeLast)
                .add("momOrdercnt", momOrdercnt)
                .add("yoyLastRatio", yoyLastRatio)
                .add("yoyBeforeLastRatio", yoyBeforeLastRatio)
                .add("momRatio", momRatio)
                .toString();
    }
}
