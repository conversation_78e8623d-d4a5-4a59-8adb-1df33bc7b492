package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 热门目的地城市
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "hotDomDestInfo",
    "hotInterDestInfo",
    "hotContinentDestInfo"
})
public class OnlineReportHotDestinationInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportHotDestinationInfo(
        List<OnlineReportDestinationInfo> hotDomDestInfo,
        List<OnlineReportDestinationInfo> hotInterDestInfo,
        List<OnlineReportDestinationInfo> hotContinentDestInfo) {
        this.hotDomDestInfo = hotDomDestInfo;
        this.hotInterDestInfo = hotInterDestInfo;
        this.hotContinentDestInfo = hotContinentDestInfo;
    }

    public OnlineReportHotDestinationInfo() {
    }

    /**
     * 国内热门目的地城市
     */
    @JsonProperty("hotDomDestInfo")
    public List<OnlineReportDestinationInfo> hotDomDestInfo;

    /**
     * 国际热门目的地城市
     */
    @JsonProperty("hotInterDestInfo")
    public List<OnlineReportDestinationInfo> hotInterDestInfo;

    /**
     * 大洲热门目的地城市
     */
    @JsonProperty("hotContinentDestInfo")
    public List<OnlineReportDestinationInfo> hotContinentDestInfo;

    /**
     * 国内热门目的地城市
     */
    public List<OnlineReportDestinationInfo> getHotDomDestInfo() {
        return hotDomDestInfo;
    }

    /**
     * 国内热门目的地城市
     */
    public void setHotDomDestInfo(final List<OnlineReportDestinationInfo> hotDomDestInfo) {
        this.hotDomDestInfo = hotDomDestInfo;
    }

    /**
     * 国际热门目的地城市
     */
    public List<OnlineReportDestinationInfo> getHotInterDestInfo() {
        return hotInterDestInfo;
    }

    /**
     * 国际热门目的地城市
     */
    public void setHotInterDestInfo(final List<OnlineReportDestinationInfo> hotInterDestInfo) {
        this.hotInterDestInfo = hotInterDestInfo;
    }

    /**
     * 大洲热门目的地城市
     */
    public List<OnlineReportDestinationInfo> getHotContinentDestInfo() {
        return hotContinentDestInfo;
    }

    /**
     * 大洲热门目的地城市
     */
    public void setHotContinentDestInfo(final List<OnlineReportDestinationInfo> hotContinentDestInfo) {
        this.hotContinentDestInfo = hotContinentDestInfo;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportHotDestinationInfo other = (OnlineReportHotDestinationInfo)obj;
        return
            Objects.equal(this.hotDomDestInfo, other.hotDomDestInfo) &&
            Objects.equal(this.hotInterDestInfo, other.hotInterDestInfo) &&
            Objects.equal(this.hotContinentDestInfo, other.hotContinentDestInfo);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.hotDomDestInfo == null ? 0 : this.hotDomDestInfo.hashCode());
        result = 31 * result + (this.hotInterDestInfo == null ? 0 : this.hotInterDestInfo.hashCode());
        result = 31 * result + (this.hotContinentDestInfo == null ? 0 : this.hotContinentDestInfo.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("hotDomDestInfo", hotDomDestInfo)
            .add("hotInterDestInfo", hotInterDestInfo)
            .add("hotContinentDestInfo", hotContinentDestInfo)
            .toString();
    }
}
