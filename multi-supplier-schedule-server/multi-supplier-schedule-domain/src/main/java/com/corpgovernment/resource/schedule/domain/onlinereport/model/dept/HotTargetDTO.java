package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
public class HotTargetDTO implements Comparable<HotTargetDTO> {

    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;
    @Column(name = "topTarget")
    @Type(value = Types.VARCHAR)
    private String topTarget;
    @Column(name = "countSort")
    @Type(value = Types.INTEGER)
    private Integer countSort;

    @Override
    public int compareTo(HotTargetDTO other) {
        return Integer.compare(this.countSort, other.countSort);
    }
}
