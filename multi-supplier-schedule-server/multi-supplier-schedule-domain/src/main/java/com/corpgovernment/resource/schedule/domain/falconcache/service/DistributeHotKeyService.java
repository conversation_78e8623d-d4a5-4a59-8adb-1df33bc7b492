package com.corpgovernment.resource.schedule.domain.falconcache.service;

import com.alicp.jetcache.support.JavaValueEncoder;
import com.corpgovernment.resource.schedule.domain.falconcache.model.KeyModel;
import com.ctrip.corp.obt.falconcache.core.config.FalconConfig;
import com.ctrip.corp.obt.falconcache.core.dto.AccessAmountDTO;
import com.ctrip.corp.obt.falconcache.core.dto.HotKeyDistributeMessage;
import com.ctrip.corp.obt.falconcache.core.util.CKUtils;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.Conditional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/8
 */
@Component
public class DistributeHotKeyService {
    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    private  Map<String, byte[]> channelMap = new ConcurrentHashMap<>();

    public void distributeHotKey(Map<String, List<KeyModel>> hotKeyList){
        if (CollectionUtils.isEmpty(hotKeyList)){
            return;
        } RedisConnection connection = redisConnectionFactory.getConnection();

        hotKeyList.forEach((k,v) -> {

            Map<String, List<KeyModel>> keyPrefix = v.stream().collect(Collectors.groupingBy(KeyModel::getKeyPrefix));
            keyPrefix.forEach((k1,v1)->{
                String channelName = String.format("%s:%s:%s",FalconConfig.getHotKeyListenChannel(), k, k1);
                HotKeyDistributeMessage hotKeyDistributeMessage = new HotKeyDistributeMessage();
                List<AccessAmountDTO> accessAmountDTOList = v1.stream().map(t->{
                    AccessAmountDTO accessAmountDTO = new AccessAmountDTO();
                    accessAmountDTO.setKeyPrefix(t.getKeyPrefix());
                    accessAmountDTO.setKeyName(t.getKey());
                    return accessAmountDTO;
                }).collect(Collectors.toList());
                hotKeyDistributeMessage.setHotKeyList(accessAmountDTOList);
                byte[] body = JavaValueEncoder.INSTANCE.apply(hotKeyDistributeMessage);
                byte[] channel = channelMap.computeIfAbsent(String.format(channelName), t-> channelName.getBytes(StandardCharsets.UTF_8));
                // 下发 热key到服务应用
                connection.publish(channel, body);
            });

        });
    }
}
