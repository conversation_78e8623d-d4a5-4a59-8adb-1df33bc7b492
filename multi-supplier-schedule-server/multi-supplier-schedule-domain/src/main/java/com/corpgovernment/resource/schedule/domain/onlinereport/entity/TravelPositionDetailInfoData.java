package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "isSelf",
    "orderType",
    "orderId",
    "passengerName",
    "processedUserName",
    "uid",
    "uidName",
    "flightnoTrainnoHotelname",
    "startProvinceName",
    "startCityName",
    "endProvinceName",
    "endCityName",
    "startSubTripDate",
    "endSubTripDate",
    "startTime",
    "endTime",
    "dept1",
    "dept2",
    "dept3",
    "dept4",
    "dept5",
    "dept6",
    "dept7",
    "dept8",
    "dept9",
    "dept10",
    "costcenter1",
    "costcenter2",
    "costcenter3",
    "costcenter4",
    "costcenter5",
    "costcenter6"
})
public class TravelPositionDetailInfoData implements Serializable {
    private static final long serialVersionUID = 1L;





    public TravelPositionDetailInfoData(
        String isSelf,
        String orderType,
        Long orderId,
        String passengerName,
        String processedUserName,
        String uid,
        String uidName,
        String flightnoTrainnoHotelname,
        String startProvinceName,
        String startCityName,
        String endProvinceName,
        String endCityName,
        String startSubTripDate,
        String endSubTripDate,
        String startTime,
        String endTime,
        String dept1,
        String dept2,
        String dept3,
        String dept4,
        String dept5,
        String dept6,
        String dept7,
        String dept8,
        String dept9,
        String dept10,
        String costcenter1,
        String costcenter2,
        String costcenter3,
        String costcenter4,
        String costcenter5,
        String costcenter6) {
        this.isSelf = isSelf;
        this.orderType = orderType;
        this.orderId = orderId;
        this.passengerName = passengerName;
        this.processedUserName = processedUserName;
        this.uid = uid;
        this.uidName = uidName;
        this.flightnoTrainnoHotelname = flightnoTrainnoHotelname;
        this.startProvinceName = startProvinceName;
        this.startCityName = startCityName;
        this.endProvinceName = endProvinceName;
        this.endCityName = endCityName;
        this.startSubTripDate = startSubTripDate;
        this.endSubTripDate = endSubTripDate;
        this.startTime = startTime;
        this.endTime = endTime;
        this.dept1 = dept1;
        this.dept2 = dept2;
        this.dept3 = dept3;
        this.dept4 = dept4;
        this.dept5 = dept5;
        this.dept6 = dept6;
        this.dept7 = dept7;
        this.dept8 = dept8;
        this.dept9 = dept9;
        this.dept10 = dept10;
        this.costcenter1 = costcenter1;
        this.costcenter2 = costcenter2;
        this.costcenter3 = costcenter3;
        this.costcenter4 = costcenter4;
        this.costcenter5 = costcenter5;
        this.costcenter6 = costcenter6;
    }

    public TravelPositionDetailInfoData() {
    }

    /**
     * *
     * * 因公 因私
     */
    @JsonProperty("isSelf")
    public String isSelf;

    /**
     * *
     * * 业务类型
     */
    @JsonProperty("orderType")
    public String orderType;

    /**
     * *
     * * 订单id
     */
    @JsonProperty("orderId")
    public Long orderId;

    /**
     * *
     * * 出行人姓名
     */
    @JsonProperty("passengerName")
    public String passengerName;

    /**
     * *
     * * 用户姓名（处理后的）
     */
    @JsonProperty("processedUserName")
    public String processedUserName;

    /**
     * *
     * * 预订人id，已经删去空值
     */
    @JsonProperty("uid")
    public String uid;

    /**
     * *
     * * 预订人姓名
     */
    @JsonProperty("uidName")
    public String uidName;

    /**
     * *
     * * 航班号/火车车次/酒店名称
     */
    @JsonProperty("flightnoTrainnoHotelname")
    public String flightnoTrainnoHotelname;

    /**
     * *
     * * 出发省份
     */
    @JsonProperty("startProvinceName")
    public String startProvinceName;

    /**
     * *
     * * 出发城市
     */
    @JsonProperty("startCityName")
    public String startCityName;

    /**
     * *
     * * 到达省份
     */
    @JsonProperty("endProvinceName")
    public String endProvinceName;

    /**
     * *
     * * 到达城市
     */
    @JsonProperty("endCityName")
    public String endCityName;

    /**
     * 子行程开始 结束时间
     */
    @JsonProperty("startSubTripDate")
    public String startSubTripDate;

    @JsonProperty("endSubTripDate")
    public String endSubTripDate;

    /**
     * 出发时间/入住时间
     */
    @JsonProperty("startTime")
    public String startTime;

    /**
     * 到达时间/离店时间
     */
    @JsonProperty("endTime")
    public String endTime;

    /**
     * 部门
     */
    @JsonProperty("dept1")
    public String dept1;

    @JsonProperty("dept2")
    public String dept2;

    @JsonProperty("dept3")
    public String dept3;

    @JsonProperty("dept4")
    public String dept4;

    @JsonProperty("dept5")
    public String dept5;

    @JsonProperty("dept6")
    public String dept6;

    @JsonProperty("dept7")
    public String dept7;

    @JsonProperty("dept8")
    public String dept8;

    @JsonProperty("dept9")
    public String dept9;

    @JsonProperty("dept10")
    public String dept10;

    /**
     * 成本中心
     */
    @JsonProperty("costcenter1")
    public String costcenter1;

    @JsonProperty("costcenter2")
    public String costcenter2;

    @JsonProperty("costcenter3")
    public String costcenter3;

    @JsonProperty("costcenter4")
    public String costcenter4;

    @JsonProperty("costcenter5")
    public String costcenter5;

    @JsonProperty("costcenter6")
    public String costcenter6;

    /**
     * *
     * * 因公 因私
     */
    public String getIsSelf() {
        return isSelf;
    }

    /**
     * *
     * * 因公 因私
     */
    public void setIsSelf(final String isSelf) {
        this.isSelf = isSelf;
    }

    /**
     * *
     * * 业务类型
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     * *
     * * 业务类型
     */
    public void setOrderType(final String orderType) {
        this.orderType = orderType;
    }

    /**
     * *
     * * 订单id
     */
    public Long getOrderId() {
        return orderId;
    }

    /**
     * *
     * * 订单id
     */
    public void setOrderId(final Long orderId) {
        this.orderId = orderId;
    }

    /**
     * *
     * * 出行人姓名
     */
    public String getPassengerName() {
        return passengerName;
    }

    /**
     * *
     * * 出行人姓名
     */
    public void setPassengerName(final String passengerName) {
        this.passengerName = passengerName;
    }

    /**
     * *
     * * 用户姓名（处理后的）
     */
    public String getProcessedUserName() {
        return processedUserName;
    }

    /**
     * *
     * * 用户姓名（处理后的）
     */
    public void setProcessedUserName(final String processedUserName) {
        this.processedUserName = processedUserName;
    }

    /**
     * *
     * * 预订人id，已经删去空值
     */
    public String getUid() {
        return uid;
    }

    /**
     * *
     * * 预订人id，已经删去空值
     */
    public void setUid(final String uid) {
        this.uid = uid;
    }

    /**
     * *
     * * 预订人姓名
     */
    public String getUidName() {
        return uidName;
    }

    /**
     * *
     * * 预订人姓名
     */
    public void setUidName(final String uidName) {
        this.uidName = uidName;
    }

    /**
     * *
     * * 航班号/火车车次/酒店名称
     */
    public String getFlightnoTrainnoHotelname() {
        return flightnoTrainnoHotelname;
    }

    /**
     * *
     * * 航班号/火车车次/酒店名称
     */
    public void setFlightnoTrainnoHotelname(final String flightnoTrainnoHotelname) {
        this.flightnoTrainnoHotelname = flightnoTrainnoHotelname;
    }

    /**
     * *
     * * 出发省份
     */
    public String getStartProvinceName() {
        return startProvinceName;
    }

    /**
     * *
     * * 出发省份
     */
    public void setStartProvinceName(final String startProvinceName) {
        this.startProvinceName = startProvinceName;
    }

    /**
     * *
     * * 出发城市
     */
    public String getStartCityName() {
        return startCityName;
    }

    /**
     * *
     * * 出发城市
     */
    public void setStartCityName(final String startCityName) {
        this.startCityName = startCityName;
    }

    /**
     * *
     * * 到达省份
     */
    public String getEndProvinceName() {
        return endProvinceName;
    }

    /**
     * *
     * * 到达省份
     */
    public void setEndProvinceName(final String endProvinceName) {
        this.endProvinceName = endProvinceName;
    }

    /**
     * *
     * * 到达城市
     */
    public String getEndCityName() {
        return endCityName;
    }

    /**
     * *
     * * 到达城市
     */
    public void setEndCityName(final String endCityName) {
        this.endCityName = endCityName;
    }

    /**
     * 子行程开始 结束时间
     */
    public String getStartSubTripDate() {
        return startSubTripDate;
    }

    /**
     * 子行程开始 结束时间
     */
    public void setStartSubTripDate(final String startSubTripDate) {
        this.startSubTripDate = startSubTripDate;
    }
    public String getEndSubTripDate() {
        return endSubTripDate;
    }

    public void setEndSubTripDate(final String endSubTripDate) {
        this.endSubTripDate = endSubTripDate;
    }

    /**
     * 出发时间/入住时间
     */
    public String getStartTime() {
        return startTime;
    }

    /**
     * 出发时间/入住时间
     */
    public void setStartTime(final String startTime) {
        this.startTime = startTime;
    }

    /**
     * 到达时间/离店时间
     */
    public String getEndTime() {
        return endTime;
    }

    /**
     * 到达时间/离店时间
     */
    public void setEndTime(final String endTime) {
        this.endTime = endTime;
    }

    /**
     * 部门
     */
    public String getDept1() {
        return dept1;
    }

    /**
     * 部门
     */
    public void setDept1(final String dept1) {
        this.dept1 = dept1;
    }
    public String getDept2() {
        return dept2;
    }

    public void setDept2(final String dept2) {
        this.dept2 = dept2;
    }
    public String getDept3() {
        return dept3;
    }

    public void setDept3(final String dept3) {
        this.dept3 = dept3;
    }
    public String getDept4() {
        return dept4;
    }

    public void setDept4(final String dept4) {
        this.dept4 = dept4;
    }
    public String getDept5() {
        return dept5;
    }

    public void setDept5(final String dept5) {
        this.dept5 = dept5;
    }
    public String getDept6() {
        return dept6;
    }

    public void setDept6(final String dept6) {
        this.dept6 = dept6;
    }
    public String getDept7() {
        return dept7;
    }

    public void setDept7(final String dept7) {
        this.dept7 = dept7;
    }
    public String getDept8() {
        return dept8;
    }

    public void setDept8(final String dept8) {
        this.dept8 = dept8;
    }
    public String getDept9() {
        return dept9;
    }

    public void setDept9(final String dept9) {
        this.dept9 = dept9;
    }
    public String getDept10() {
        return dept10;
    }

    public void setDept10(final String dept10) {
        this.dept10 = dept10;
    }

    /**
     * 成本中心
     */
    public String getCostcenter1() {
        return costcenter1;
    }

    /**
     * 成本中心
     */
    public void setCostcenter1(final String costcenter1) {
        this.costcenter1 = costcenter1;
    }
    public String getCostcenter2() {
        return costcenter2;
    }

    public void setCostcenter2(final String costcenter2) {
        this.costcenter2 = costcenter2;
    }
    public String getCostcenter3() {
        return costcenter3;
    }

    public void setCostcenter3(final String costcenter3) {
        this.costcenter3 = costcenter3;
    }
    public String getCostcenter4() {
        return costcenter4;
    }

    public void setCostcenter4(final String costcenter4) {
        this.costcenter4 = costcenter4;
    }
    public String getCostcenter5() {
        return costcenter5;
    }

    public void setCostcenter5(final String costcenter5) {
        this.costcenter5 = costcenter5;
    }
    public String getCostcenter6() {
        return costcenter6;
    }

    public void setCostcenter6(final String costcenter6) {
        this.costcenter6 = costcenter6;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TravelPositionDetailInfoData other = (TravelPositionDetailInfoData)obj;
        return
            Objects.equal(this.isSelf, other.isSelf) &&
            Objects.equal(this.orderType, other.orderType) &&
            Objects.equal(this.orderId, other.orderId) &&
            Objects.equal(this.passengerName, other.passengerName) &&
            Objects.equal(this.processedUserName, other.processedUserName) &&
            Objects.equal(this.uid, other.uid) &&
            Objects.equal(this.uidName, other.uidName) &&
            Objects.equal(this.flightnoTrainnoHotelname, other.flightnoTrainnoHotelname) &&
            Objects.equal(this.startProvinceName, other.startProvinceName) &&
            Objects.equal(this.startCityName, other.startCityName) &&
            Objects.equal(this.endProvinceName, other.endProvinceName) &&
            Objects.equal(this.endCityName, other.endCityName) &&
            Objects.equal(this.startSubTripDate, other.startSubTripDate) &&
            Objects.equal(this.endSubTripDate, other.endSubTripDate) &&
            Objects.equal(this.startTime, other.startTime) &&
            Objects.equal(this.endTime, other.endTime) &&
            Objects.equal(this.dept1, other.dept1) &&
            Objects.equal(this.dept2, other.dept2) &&
            Objects.equal(this.dept3, other.dept3) &&
            Objects.equal(this.dept4, other.dept4) &&
            Objects.equal(this.dept5, other.dept5) &&
            Objects.equal(this.dept6, other.dept6) &&
            Objects.equal(this.dept7, other.dept7) &&
            Objects.equal(this.dept8, other.dept8) &&
            Objects.equal(this.dept9, other.dept9) &&
            Objects.equal(this.dept10, other.dept10) &&
            Objects.equal(this.costcenter1, other.costcenter1) &&
            Objects.equal(this.costcenter2, other.costcenter2) &&
            Objects.equal(this.costcenter3, other.costcenter3) &&
            Objects.equal(this.costcenter4, other.costcenter4) &&
            Objects.equal(this.costcenter5, other.costcenter5) &&
            Objects.equal(this.costcenter6, other.costcenter6);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.isSelf == null ? 0 : this.isSelf.hashCode());
        result = 31 * result + (this.orderType == null ? 0 : this.orderType.hashCode());
        result = 31 * result + (this.orderId == null ? 0 : this.orderId.hashCode());
        result = 31 * result + (this.passengerName == null ? 0 : this.passengerName.hashCode());
        result = 31 * result + (this.processedUserName == null ? 0 : this.processedUserName.hashCode());
        result = 31 * result + (this.uid == null ? 0 : this.uid.hashCode());
        result = 31 * result + (this.uidName == null ? 0 : this.uidName.hashCode());
        result = 31 * result + (this.flightnoTrainnoHotelname == null ? 0 : this.flightnoTrainnoHotelname.hashCode());
        result = 31 * result + (this.startProvinceName == null ? 0 : this.startProvinceName.hashCode());
        result = 31 * result + (this.startCityName == null ? 0 : this.startCityName.hashCode());
        result = 31 * result + (this.endProvinceName == null ? 0 : this.endProvinceName.hashCode());
        result = 31 * result + (this.endCityName == null ? 0 : this.endCityName.hashCode());
        result = 31 * result + (this.startSubTripDate == null ? 0 : this.startSubTripDate.hashCode());
        result = 31 * result + (this.endSubTripDate == null ? 0 : this.endSubTripDate.hashCode());
        result = 31 * result + (this.startTime == null ? 0 : this.startTime.hashCode());
        result = 31 * result + (this.endTime == null ? 0 : this.endTime.hashCode());
        result = 31 * result + (this.dept1 == null ? 0 : this.dept1.hashCode());
        result = 31 * result + (this.dept2 == null ? 0 : this.dept2.hashCode());
        result = 31 * result + (this.dept3 == null ? 0 : this.dept3.hashCode());
        result = 31 * result + (this.dept4 == null ? 0 : this.dept4.hashCode());
        result = 31 * result + (this.dept5 == null ? 0 : this.dept5.hashCode());
        result = 31 * result + (this.dept6 == null ? 0 : this.dept6.hashCode());
        result = 31 * result + (this.dept7 == null ? 0 : this.dept7.hashCode());
        result = 31 * result + (this.dept8 == null ? 0 : this.dept8.hashCode());
        result = 31 * result + (this.dept9 == null ? 0 : this.dept9.hashCode());
        result = 31 * result + (this.dept10 == null ? 0 : this.dept10.hashCode());
        result = 31 * result + (this.costcenter1 == null ? 0 : this.costcenter1.hashCode());
        result = 31 * result + (this.costcenter2 == null ? 0 : this.costcenter2.hashCode());
        result = 31 * result + (this.costcenter3 == null ? 0 : this.costcenter3.hashCode());
        result = 31 * result + (this.costcenter4 == null ? 0 : this.costcenter4.hashCode());
        result = 31 * result + (this.costcenter5 == null ? 0 : this.costcenter5.hashCode());
        result = 31 * result + (this.costcenter6 == null ? 0 : this.costcenter6.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("isSelf", isSelf)
            .add("orderType", orderType)
            .add("orderId", orderId)
            .add("passengerName", passengerName)
            .add("processedUserName", processedUserName)
            .add("uid", uid)
            .add("uidName", uidName)
            .add("flightnoTrainnoHotelname", flightnoTrainnoHotelname)
            .add("startProvinceName", startProvinceName)
            .add("startCityName", startCityName)
            .add("endProvinceName", endProvinceName)
            .add("endCityName", endCityName)
            .add("startSubTripDate", startSubTripDate)
            .add("endSubTripDate", endSubTripDate)
            .add("startTime", startTime)
            .add("endTime", endTime)
            .add("dept1", dept1)
            .add("dept2", dept2)
            .add("dept3", dept3)
            .add("dept4", dept4)
            .add("dept5", dept5)
            .add("dept6", dept6)
            .add("dept7", dept7)
            .add("dept8", dept8)
            .add("dept9", dept9)
            .add("dept10", dept10)
            .add("costcenter1", costcenter1)
            .add("costcenter2", costcenter2)
            .add("costcenter3", costcenter3)
            .add("costcenter4", costcenter4)
            .add("costcenter5", costcenter5)
            .add("costcenter6", costcenter6)
            .toString();
    }
}
