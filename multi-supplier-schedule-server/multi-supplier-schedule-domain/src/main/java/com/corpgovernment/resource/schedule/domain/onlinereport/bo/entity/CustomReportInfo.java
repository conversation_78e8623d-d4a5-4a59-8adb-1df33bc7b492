package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2019-02-27
 */
public class CustomReportInfo {
    /**
     * key
     */
    private Long id;

    /**
     * 报表名称
     */
    private String name;

    /**
     * 所属UID
     */
    private String uID;

    /**
     * 报表类型，对应机票、酒店或者机酒等
     */
    private String type;

    /**
     * 报表序号
     */
    private Integer seqNo;

    /**
     * 过滤条件（JSON数据: {Name: price, Contrast: 1, Value: 50}, 其中Contrast对比类型）
     */
    private String filter;

    /**
     * 维度
     */
    private String dimension;

    /**
     * 列信息（JSON数据：{Code: price, Name: price, Seq: 1, Style: , Format: }）
     */
    private String field;

    /**
     * 排序规则，排序字段名，暂只支持单列排序
     */
    private String sort;

    /**
     * 限制，用于分页，存储结构为：｛起始行｝,｛返回行数｝，例：50,10
     */
    private String limitRow;

    /**
     * 场景（使用场景）
     */
    private String scene;

    /**
     * 报表描述
     */
    private String remark;

    /**
     * 创建时间
     */
    private Timestamp dataChangeCreatetime;

    /**
     * 修改时间
     */
    private Timestamp dataChangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getuID() {
        return uID;
    }

    public void setuID(String uID) {
        this.uID = uID;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(Integer seqNo) {
        this.seqNo = seqNo;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getLimitRow() {
        return limitRow;
    }

    public void setLimitRow(String limitRow) {
        this.limitRow = limitRow;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Timestamp getDataChangeCreatetime() {
        return dataChangeCreatetime;
    }

    public void setDataChangeCreatetime(Timestamp dataChangeCreatetime) {
        this.dataChangeCreatetime = dataChangeCreatetime;
    }

    public Timestamp getDataChangeLasttime() {
        return dataChangeLasttime;
    }

    public void setDataChangeLasttime(Timestamp dataChangeLasttime) {
        this.dataChangeLasttime = dataChangeLasttime;
    }

}
