package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 供应商监测-趋势
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "trends",
    "totalRecords",
    "extData"
})
public class OnlineReportSupplierTrendResponse implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportSupplierTrendResponse(
        Integer responseCode,
        String responseDesc,
        List<OnlineReportSupplierTrendInfo> trends,
        Integer totalRecords,
        Map<String, String> extData) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.trends = trends;
        this.totalRecords = totalRecords;
        this.extData = extData;
        
    }

    public OnlineReportSupplierTrendResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    /**
     * 明细数据
     */
    @JsonProperty("trends")
    public List<OnlineReportSupplierTrendInfo> trends;

    /**
     * 数据总条数
     */
    @JsonProperty("totalRecords")
    public Integer totalRecords;

    @JsonProperty("extData")
    public Map<String, String> extData;



    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }

    /**
     * 明细数据
     */
    public List<OnlineReportSupplierTrendInfo> getTrends() {
        return trends;
    }

    /**
     * 明细数据
     */
    public void setTrends(final List<OnlineReportSupplierTrendInfo> trends) {
        this.trends = trends;
    }

    /**
     * 数据总条数
     */
    public Integer getTotalRecords() {
        return totalRecords;
    }

    /**
     * 数据总条数
     */
    public void setTotalRecords(final Integer totalRecords) {
        this.totalRecords = totalRecords;
    }
    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }



    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportSupplierTrendResponse other = (OnlineReportSupplierTrendResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc) &&
            Objects.equal(this.trends, other.trends) &&
            Objects.equal(this.totalRecords, other.totalRecords) &&
            Objects.equal(this.extData, other.extData) ;
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.trends == null ? 0 : this.trends.hashCode());
        result = 31 * result + (this.totalRecords == null ? 0 : this.totalRecords.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());
        

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            .add("trends", trends)
            .add("totalRecords", totalRecords)
            .add("extData", extData)
            
            .toString();
    }
}
