package com.corpgovernment.resource.schedule.domain.execution.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.cron.pattern.CronPatternUtil;
import com.corpgovernment.common.businessmonitor.annotation.BusinessBehaviorMonitor;
import com.corpgovernment.resource.core.hotelv2.domain.enums.TaskEnum;
import com.corpgovernment.resource.core.hotelv2.domain.enums.TaskStartModeEnum;
import com.corpgovernment.resource.schedule.domain.execution.gateway.IExecutionGateway;
import com.corpgovernment.resource.schedule.domain.execution.model.ExecutionConfig;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-06 01:00
 */
@Service
@Slf4j
public class ExecutionDomainService implements IExecutionDomainService {

    @Resource
    private IExecutionGateway scheduleGateway;

    @Override
    public  List<ExecutionConfig> getExecutionConfigList() {
        return scheduleGateway.getExecutionConfigList();
    }

    @Override
    @BusinessBehaviorMonitor
    public List<ExecutionConfig.ExecutionNodeConfig> getNeedWorkExecutionNodeConfigList(List<ExecutionConfig> executionConfigList) {
        if (CollectionUtils.isEmpty(executionConfigList)) {
            return null;
        }

        List<ExecutionConfig.ExecutionNodeConfig> resultList = new ArrayList<>();
        // 遍历执行配置列表
        for (ExecutionConfig executionConfig : executionConfigList) {
            if (executionConfig == null || CollectionUtils.isEmpty(executionConfig.getExecutionNodeConfigList())) {
                continue;
            }

            // 遍历执行节点配置列表
            for (ExecutionConfig.ExecutionNodeConfig executionNodeConfig : executionConfig.getExecutionNodeConfigList()) {
                if (executionNodeConfig == null || executionNodeConfig.getFlowRate() == null || executionNodeConfig.getFlowRate() <= 0) {
                    continue;
                }

                resultList.add(executionNodeConfig);
            }
        }
        return resultList;
    }

    @Override
    @BusinessBehaviorMonitor
    public List<ExecutionConfig.TaskConfig> getNeedWorkTaskConfigList(List<ExecutionConfig> executionConfigList) {
        if (CollectionUtils.isEmpty(executionConfigList)) {
            return null;
        }

        // 获取当前时间，忽略秒
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        String now = dateFormat.format(new Date());

        List<ExecutionConfig.TaskConfig> resultList = new ArrayList<>();

        for (ExecutionConfig executionConfig : executionConfigList) {
            if (executionConfig == null || CollectionUtils.isEmpty(executionConfig.getTaskConfigList())) {
                continue;
            }

            // 遍历任务配置列表
            for (ExecutionConfig.TaskConfig taskConfig : executionConfig.getTaskConfigList()) {
                if (taskConfig == null || !Boolean.TRUE.equals(taskConfig.getEnable())) {
                    continue;
                }

                try {
                    // 获取执行时间点，忽略秒
                    List<Date> dateList = CronPatternUtil.matchedDates(
                            taskConfig.getCronExpression(),
                            DateUtil.beginOfDay(DateUtil.date()),
                            DateUtil.endOfDay(DateUtil.date()),
                            1000,
                            false);
                    log.info("now={} cronExpression={} dateList={}", now, taskConfig.getCronExpression(), JsonUtils.toJsonString(dateList));

                    if (CollectionUtils.isNotEmpty(dateList) && dateList.stream().map(dateFormat::format).anyMatch(item -> StringUtils.equalsIgnoreCase(item, now))) {
                        resultList.add(taskConfig);
                    }
                } catch (Exception exception) {
                    log.error("cron表达式解析失败，cron表达式：{}", taskConfig.getCronExpression(), exception);
                }
            }
        }

        return resultList;
    }
    
    @Override
    public List<ExecutionConfig.TaskConfig> getTaskConfigList(String param) {
        if (StringUtils.isBlank(param)) {
            return null;
        }
        
        // 转成list
        List<String> taskConfigStrList = JsonUtils.parse(param, new TypeReference<List<String>>() {});
        if (CollectionUtils.isEmpty(taskConfigStrList)) {
            return null;
        }
        
        // 解析
        List<ExecutionConfig.TaskConfig> taskConfigList = new ArrayList<>();
        for (String taskConfigStr : taskConfigStrList) {
            if (StringUtils.isBlank(taskConfigStr)) {
                continue;
            }
            
            String[] taskConfigArray = taskConfigStr.split(" ");
            if (taskConfigArray.length != 5) {
                continue;
            }
            
            taskConfigList.add(ExecutionConfig.TaskConfig.builder()
                    .supplierCode(taskConfigArray[0])
                    .taskEnum(TaskEnum.getTaskEnum(taskConfigArray[1]))
                    .enable(true)
                    .taskStartModeEnum(TaskStartModeEnum.getTaskStartModeEnum(taskConfigArray[2]))
                    .operation(taskConfigArray[3])
                    .priority(taskConfigArray[4] == null ? 5 : Integer.parseInt(taskConfigArray[4]))
                    .cronExpression(null)
                    .build());
        }
        return taskConfigList;
    }
    
}
