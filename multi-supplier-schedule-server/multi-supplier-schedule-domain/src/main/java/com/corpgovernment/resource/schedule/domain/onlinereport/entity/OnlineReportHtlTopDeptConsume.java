package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 酒店部门消费
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "dimId",
        "dim",
        "totalAmount",
        "totalQuantity",
        "avgPrice",
        "totalAmountTa",
        "totalQuantityTa",
        "avgPriceTa",
        "totalAmountNta",
        "totalQuantityNta",
        "avgPriceNta",
        "totalAmountDom",
        "totalQuantityDom",
        "avgPriceDom",
        "totalAmountInter",
        "totalQuantityInter",
        "avgPriceInter",
        "rcPercent",
        "totalOverAmount",
        "saveAmount3cRate",
        "saveAmount2cRate",
        "saveAmountPromotionRate",
        "controlSaveRate"
})
public class OnlineReportHtlTopDeptConsume implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    @JsonProperty("dimId")
    public String dimId;
    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    @JsonProperty("dim")
    public String dim;
    /**
     * 消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;
    /**
     * 间夜
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;
    /**
     * 间夜均价
     */
    @JsonProperty("avgPrice")
    public BigDecimal avgPrice;
    /**
     * 协议消费金额
     */
    @JsonProperty("totalAmountTa")
    public BigDecimal totalAmountTa;
    /**
     * 协议间夜
     */
    @JsonProperty("totalQuantityTa")
    public Integer totalQuantityTa;
    /**
     * 协议间夜均价
     */
    @JsonProperty("avgPriceTa")
    public BigDecimal avgPriceTa;
    /**
     * 非协议消费金额
     */
    @JsonProperty("totalAmountNta")
    public BigDecimal totalAmountNta;
    /**
     * 非协议间夜
     */
    @JsonProperty("totalQuantityNta")
    public Integer totalQuantityNta;
    /**
     * 非协议间夜均价
     */
    @JsonProperty("avgPriceNta")
    public BigDecimal avgPriceNta;
    /**
     * 国内消费金额
     */
    @JsonProperty("totalAmountDom")
    public BigDecimal totalAmountDom;
    /**
     * 国内间夜
     */
    @JsonProperty("totalQuantityDom")
    public Integer totalQuantityDom;
    /**
     * 国内间夜均价
     */
    @JsonProperty("avgPriceDom")
    public BigDecimal avgPriceDom;
    /**
     * 国际消费金额
     */
    @JsonProperty("totalAmountInter")
    public BigDecimal totalAmountInter;
    /**
     * 国际间夜
     */
    @JsonProperty("totalQuantityInter")
    public Integer totalQuantityInter;
    /**
     * 国际间夜均价
     */
    @JsonProperty("avgPriceInter")
    public BigDecimal avgPriceInter;
    /**
     * rc次数占比
     */
    @JsonProperty("rcPercent")
    public BigDecimal rcPercent;
    /**
     * 超标损失
     */
    @JsonProperty("totalOverAmount")
    public BigDecimal totalOverAmount;
    /**
     * 三方节省率
     */
    @JsonProperty("saveAmount3cRate")
    public BigDecimal saveAmount3cRate;
    /**
     * 两方节省率
     */
    @JsonProperty("saveAmount2cRate")
    public BigDecimal saveAmount2cRate;
    /**
     * 优惠活动节省率
     */
    @JsonProperty("saveAmountPromotionRate")
    public BigDecimal saveAmountPromotionRate;
    /**
     * 管控节省率
     */
    @JsonProperty("controlSaveRate")
    public BigDecimal controlSaveRate;
    /**
     * 消费金额占比
     */
    @JsonProperty("amountPercent")
    public BigDecimal amountPercent;
    /**
     * 订单数
     */
    @JsonProperty("totalAllOrderCount")
    public Integer totalAllOrderCount;
    /**
     * 节省金额
     */
    @JsonProperty("totalSaveAmount")
    public BigDecimal totalSaveAmount;
    /**
     * 潜在节省金额（退、改、RC）
     */
    @JsonProperty("totalSavePotential")
    public BigDecimal totalSavePotential;
    /**
     * Top酒店城市
     */
    @JsonProperty("topCity")
    public String topCity;
    /**
     * 最常入住星级
     */
    @JsonProperty("topStar")
    public String topStar;
    /**
     * 差标使用率
     */
    @JsonProperty("usage")
    public BigDecimal usage;
    /**
     * 房价
     */
    @JsonProperty("roomPrice")
    public BigDecimal roomPrice;
    /**
     * RC订单数
     */
    @JsonProperty("rcTimes")
    public Integer rcTimes;
    /**
     * 取消率
     */
    @JsonProperty("cancelRate")
    public BigDecimal cancelRate;

    public OnlineReportHtlTopDeptConsume(
            String dimId,
            String dim,
            BigDecimal totalAmount,
            Integer totalQuantity,
            BigDecimal avgPrice,
            BigDecimal totalAmountTa,
            Integer totalQuantityTa,
            BigDecimal avgPriceTa,
            BigDecimal totalAmountNta,
            Integer totalQuantityNta,
            BigDecimal avgPriceNta,
            BigDecimal totalAmountDom,
            Integer totalQuantityDom,
            BigDecimal avgPriceDom,
            BigDecimal totalAmountInter,
            Integer totalQuantityInter,
            BigDecimal avgPriceInter,
            BigDecimal rcPercent,
            BigDecimal totalOverAmount,
            BigDecimal saveAmount3cRate,
            BigDecimal saveAmount2cRate,
            BigDecimal saveAmountPromotionRate,
            BigDecimal controlSaveRate,
            BigDecimal amountPercent,
            Integer totalAllOrderCount,
            BigDecimal totalSaveAmount,
            BigDecimal totalSavePotential,
            String topCity,
            String topStar,
            BigDecimal usage,
            BigDecimal roomPrice,
            Integer rcTimes,
            BigDecimal cancelRate) {
        this.dimId = dimId;
        this.dim = dim;
        this.totalAmount = totalAmount;
        this.totalQuantity = totalQuantity;
        this.avgPrice = avgPrice;
        this.totalAmountTa = totalAmountTa;
        this.totalQuantityTa = totalQuantityTa;
        this.avgPriceTa = avgPriceTa;
        this.totalAmountNta = totalAmountNta;
        this.totalQuantityNta = totalQuantityNta;
        this.avgPriceNta = avgPriceNta;
        this.totalAmountDom = totalAmountDom;
        this.totalQuantityDom = totalQuantityDom;
        this.avgPriceDom = avgPriceDom;
        this.totalAmountInter = totalAmountInter;
        this.totalQuantityInter = totalQuantityInter;
        this.avgPriceInter = avgPriceInter;
        this.rcPercent = rcPercent;
        this.totalOverAmount = totalOverAmount;
        this.saveAmount3cRate = saveAmount3cRate;
        this.saveAmount2cRate = saveAmount2cRate;
        this.saveAmountPromotionRate = saveAmountPromotionRate;
        this.controlSaveRate = controlSaveRate;
        this.amountPercent = amountPercent;
        this.totalAllOrderCount = totalAllOrderCount;
        this.totalSaveAmount = totalSaveAmount;
        this.totalSavePotential = totalSavePotential;
        this.topCity = topCity;
        this.topStar = topStar;
        this.usage = usage;
        this.roomPrice = roomPrice;
        this.rcTimes = rcTimes;
        this.cancelRate = cancelRate;
    }

    public OnlineReportHtlTopDeptConsume() {
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public String getDimId() {
        return dimId;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public void setDimId(final String dimId) {
        this.dimId = dimId;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public String getDim() {
        return dim;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 间夜
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 间夜
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 间夜均价
     */
    public BigDecimal getAvgPrice() {
        return avgPrice;
    }

    /**
     * 间夜均价
     */
    public void setAvgPrice(final BigDecimal avgPrice) {
        this.avgPrice = avgPrice;
    }

    /**
     * 协议消费金额
     */
    public BigDecimal getTotalAmountTa() {
        return totalAmountTa;
    }

    /**
     * 协议消费金额
     */
    public void setTotalAmountTa(final BigDecimal totalAmountTa) {
        this.totalAmountTa = totalAmountTa;
    }

    /**
     * 协议间夜
     */
    public Integer getTotalQuantityTa() {
        return totalQuantityTa;
    }

    /**
     * 协议间夜
     */
    public void setTotalQuantityTa(final Integer totalQuantityTa) {
        this.totalQuantityTa = totalQuantityTa;
    }

    /**
     * 协议间夜均价
     */
    public BigDecimal getAvgPriceTa() {
        return avgPriceTa;
    }

    /**
     * 协议间夜均价
     */
    public void setAvgPriceTa(final BigDecimal avgPriceTa) {
        this.avgPriceTa = avgPriceTa;
    }

    /**
     * 非协议消费金额
     */
    public BigDecimal getTotalAmountNta() {
        return totalAmountNta;
    }

    /**
     * 非协议消费金额
     */
    public void setTotalAmountNta(final BigDecimal totalAmountNta) {
        this.totalAmountNta = totalAmountNta;
    }

    /**
     * 非协议间夜
     */
    public Integer getTotalQuantityNta() {
        return totalQuantityNta;
    }

    /**
     * 非协议间夜
     */
    public void setTotalQuantityNta(final Integer totalQuantityNta) {
        this.totalQuantityNta = totalQuantityNta;
    }

    /**
     * 非协议间夜均价
     */
    public BigDecimal getAvgPriceNta() {
        return avgPriceNta;
    }

    /**
     * 非协议间夜均价
     */
    public void setAvgPriceNta(final BigDecimal avgPriceNta) {
        this.avgPriceNta = avgPriceNta;
    }

    /**
     * 国内消费金额
     */
    public BigDecimal getTotalAmountDom() {
        return totalAmountDom;
    }

    /**
     * 国内消费金额
     */
    public void setTotalAmountDom(final BigDecimal totalAmountDom) {
        this.totalAmountDom = totalAmountDom;
    }

    /**
     * 国内间夜
     */
    public Integer getTotalQuantityDom() {
        return totalQuantityDom;
    }

    /**
     * 国内间夜
     */
    public void setTotalQuantityDom(final Integer totalQuantityDom) {
        this.totalQuantityDom = totalQuantityDom;
    }

    /**
     * 国内间夜均价
     */
    public BigDecimal getAvgPriceDom() {
        return avgPriceDom;
    }

    /**
     * 国内间夜均价
     */
    public void setAvgPriceDom(final BigDecimal avgPriceDom) {
        this.avgPriceDom = avgPriceDom;
    }

    /**
     * 国际消费金额
     */
    public BigDecimal getTotalAmountInter() {
        return totalAmountInter;
    }

    /**
     * 国际消费金额
     */
    public void setTotalAmountInter(final BigDecimal totalAmountInter) {
        this.totalAmountInter = totalAmountInter;
    }

    /**
     * 国际间夜
     */
    public Integer getTotalQuantityInter() {
        return totalQuantityInter;
    }

    /**
     * 国际间夜
     */
    public void setTotalQuantityInter(final Integer totalQuantityInter) {
        this.totalQuantityInter = totalQuantityInter;
    }

    /**
     * 国际间夜均价
     */
    public BigDecimal getAvgPriceInter() {
        return avgPriceInter;
    }

    /**
     * 国际间夜均价
     */
    public void setAvgPriceInter(final BigDecimal avgPriceInter) {
        this.avgPriceInter = avgPriceInter;
    }

    /**
     * rc次数占比
     */
    public BigDecimal getRcPercent() {
        return rcPercent;
    }

    /**
     * rc次数占比
     */
    public void setRcPercent(final BigDecimal rcPercent) {
        this.rcPercent = rcPercent;
    }

    /**
     * 超标损失
     */
    public BigDecimal getTotalOverAmount() {
        return totalOverAmount;
    }

    /**
     * 超标损失
     */
    public void setTotalOverAmount(final BigDecimal totalOverAmount) {
        this.totalOverAmount = totalOverAmount;
    }

    /**
     * 三方节省率
     */
    public BigDecimal getSaveAmount3cRate() {
        return saveAmount3cRate;
    }

    /**
     * 三方节省率
     */
    public void setSaveAmount3cRate(final BigDecimal saveAmount3cRate) {
        this.saveAmount3cRate = saveAmount3cRate;
    }

    /**
     * 两方节省率
     */
    public BigDecimal getSaveAmount2cRate() {
        return saveAmount2cRate;
    }

    /**
     * 两方节省率
     */
    public void setSaveAmount2cRate(final BigDecimal saveAmount2cRate) {
        this.saveAmount2cRate = saveAmount2cRate;
    }

    /**
     * 优惠活动节省率
     */
    public BigDecimal getSaveAmountPromotionRate() {
        return saveAmountPromotionRate;
    }

    /**
     * 优惠活动节省率
     */
    public void setSaveAmountPromotionRate(final BigDecimal saveAmountPromotionRate) {
        this.saveAmountPromotionRate = saveAmountPromotionRate;
    }

    /**
     * 管控节省率
     */
    public BigDecimal getControlSaveRate() {
        return controlSaveRate;
    }

    /**
     * 管控节省率
     */
    public void setControlSaveRate(final BigDecimal controlSaveRate) {
        this.controlSaveRate = controlSaveRate;
    }

    /**
     * 消费金额占比
     */
    public BigDecimal getAmountPercent() {
        return amountPercent;
    }

    /**
     * 消费金额占比
     */
    public void setAmountPercent(final BigDecimal amountPercent) {
        this.amountPercent = amountPercent;
    }

    /**
     * 订单数
     */
    public Integer getTotalAllOrderCount() {
        return totalAllOrderCount;
    }

    /**
     * 订单数
     */
    public void setTotalAllOrderCount(final Integer totalAllOrderCount) {
        this.totalAllOrderCount = totalAllOrderCount;
    }

    /**
     * 节省金额
     */
    public BigDecimal getTotalSaveAmount() {
        return totalSaveAmount;
    }

    /**
     * 节省金额
     */
    public void setTotalSaveAmount(final BigDecimal totalSaveAmount) {
        this.totalSaveAmount = totalSaveAmount;
    }

    /**
     * 潜在节省金额（退、改、RC）
     */
    public BigDecimal getTotalSavePotential() {
        return totalSavePotential;
    }

    /**
     * 潜在节省金额（退、改、RC）
     */
    public void setTotalSavePotential(final BigDecimal totalSavePotential) {
        this.totalSavePotential = totalSavePotential;
    }

    /**
     * Top酒店城市
     */
    public String getTopCity() {
        return topCity;
    }

    /**
     * Top酒店城市
     */
    public void setTopCity(final String topCity) {
        this.topCity = topCity;
    }

    /**
     * 最常入住星级
     */
    public String getTopStar() {
        return topStar;
    }

    /**
     * 最常入住星级
     */
    public void setTopStar(final String topStar) {
        this.topStar = topStar;
    }

    /**
     * 差标使用率
     */
    public BigDecimal getUsage() {
        return usage;
    }

    /**
     * 差标使用率
     */
    public void setUsage(final BigDecimal usage) {
        this.usage = usage;
    }

    /**
     * 房价
     */
    public BigDecimal getRoomPrice() {
        return roomPrice;
    }

    /**
     * 房价
     */
    public void setRoomPrice(final BigDecimal roomPrice) {
        this.roomPrice = roomPrice;
    }

    /**
     * RC订单数
     */
    public Integer getRcTimes() {
        return rcTimes;
    }

    /**
     * RC订单数
     */
    public void setRcTimes(final Integer rcTimes) {
        this.rcTimes = rcTimes;
    }

    /**
     * 取消率
     */
    public BigDecimal getCancelRate() {
        return cancelRate;
    }

    /**
     * 取消率
     */
    public void setCancelRate(final BigDecimal cancelRate) {
        this.cancelRate = cancelRate;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.dimId;
            case 1:
                return this.dim;
            case 2:
                return this.totalAmount;
            case 3:
                return this.totalQuantity;
            case 4:
                return this.avgPrice;
            case 5:
                return this.totalAmountTa;
            case 6:
                return this.totalQuantityTa;
            case 7:
                return this.avgPriceTa;
            case 8:
                return this.totalAmountNta;
            case 9:
                return this.totalQuantityNta;
            case 10:
                return this.avgPriceNta;
            case 11:
                return this.totalAmountDom;
            case 12:
                return this.totalQuantityDom;
            case 13:
                return this.avgPriceDom;
            case 14:
                return this.totalAmountInter;
            case 15:
                return this.totalQuantityInter;
            case 16:
                return this.avgPriceInter;
            case 17:
                return this.rcPercent;
            case 18:
                return this.totalOverAmount;
            case 19:
                return this.saveAmount3cRate;
            case 20:
                return this.saveAmount2cRate;
            case 21:
                return this.saveAmountPromotionRate;
            case 22:
                return this.controlSaveRate;
            case 23:
                return this.amountPercent;
            case 24:
                return this.totalAllOrderCount;
            case 25:
                return this.totalSaveAmount;
            case 26:
                return this.totalSavePotential;
            case 27:
                return this.topCity;
            case 28:
                return this.topStar;
            case 29:
                return this.usage;
            case 30:
                return this.roomPrice;
            case 31:
                return this.rcTimes;
            case 32:
                return this.cancelRate;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.dimId = (String) fieldValue;
                break;
            case 1:
                this.dim = (String) fieldValue;
                break;
            case 2:
                this.totalAmount = (BigDecimal) fieldValue;
                break;
            case 3:
                this.totalQuantity = (Integer) fieldValue;
                break;
            case 4:
                this.avgPrice = (BigDecimal) fieldValue;
                break;
            case 5:
                this.totalAmountTa = (BigDecimal) fieldValue;
                break;
            case 6:
                this.totalQuantityTa = (Integer) fieldValue;
                break;
            case 7:
                this.avgPriceTa = (BigDecimal) fieldValue;
                break;
            case 8:
                this.totalAmountNta = (BigDecimal) fieldValue;
                break;
            case 9:
                this.totalQuantityNta = (Integer) fieldValue;
                break;
            case 10:
                this.avgPriceNta = (BigDecimal) fieldValue;
                break;
            case 11:
                this.totalAmountDom = (BigDecimal) fieldValue;
                break;
            case 12:
                this.totalQuantityDom = (Integer) fieldValue;
                break;
            case 13:
                this.avgPriceDom = (BigDecimal) fieldValue;
                break;
            case 14:
                this.totalAmountInter = (BigDecimal) fieldValue;
                break;
            case 15:
                this.totalQuantityInter = (Integer) fieldValue;
                break;
            case 16:
                this.avgPriceInter = (BigDecimal) fieldValue;
                break;
            case 17:
                this.rcPercent = (BigDecimal) fieldValue;
                break;
            case 18:
                this.totalOverAmount = (BigDecimal) fieldValue;
                break;
            case 19:
                this.saveAmount3cRate = (BigDecimal) fieldValue;
                break;
            case 20:
                this.saveAmount2cRate = (BigDecimal) fieldValue;
                break;
            case 21:
                this.saveAmountPromotionRate = (BigDecimal) fieldValue;
                break;
            case 22:
                this.controlSaveRate = (BigDecimal) fieldValue;
                break;
            case 23:
                this.amountPercent = (BigDecimal) fieldValue;
                break;
            case 24:
                this.totalAllOrderCount = (Integer) fieldValue;
                break;
            case 25:
                this.totalSaveAmount = (BigDecimal) fieldValue;
                break;
            case 26:
                this.totalSavePotential = (BigDecimal) fieldValue;
                break;
            case 27:
                this.topCity = (String) fieldValue;
                break;
            case 28:
                this.topStar = (String) fieldValue;
                break;
            case 29:
                this.usage = (BigDecimal) fieldValue;
                break;
            case 30:
                this.roomPrice = (BigDecimal) fieldValue;
                break;
            case 31:
                this.rcTimes = (Integer) fieldValue;
                break;
            case 32:
                this.cancelRate = (BigDecimal) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportHtlTopDeptConsume other = (OnlineReportHtlTopDeptConsume) obj;
        return
                Objects.equal(this.dimId, other.dimId) &&
                        Objects.equal(this.dim, other.dim) &&
                        Objects.equal(this.totalAmount, other.totalAmount) &&
                        Objects.equal(this.totalQuantity, other.totalQuantity) &&
                        Objects.equal(this.avgPrice, other.avgPrice) &&
                        Objects.equal(this.totalAmountTa, other.totalAmountTa) &&
                        Objects.equal(this.totalQuantityTa, other.totalQuantityTa) &&
                        Objects.equal(this.avgPriceTa, other.avgPriceTa) &&
                        Objects.equal(this.totalAmountNta, other.totalAmountNta) &&
                        Objects.equal(this.totalQuantityNta, other.totalQuantityNta) &&
                        Objects.equal(this.avgPriceNta, other.avgPriceNta) &&
                        Objects.equal(this.totalAmountDom, other.totalAmountDom) &&
                        Objects.equal(this.totalQuantityDom, other.totalQuantityDom) &&
                        Objects.equal(this.avgPriceDom, other.avgPriceDom) &&
                        Objects.equal(this.totalAmountInter, other.totalAmountInter) &&
                        Objects.equal(this.totalQuantityInter, other.totalQuantityInter) &&
                        Objects.equal(this.avgPriceInter, other.avgPriceInter) &&
                        Objects.equal(this.rcPercent, other.rcPercent) &&
                        Objects.equal(this.totalOverAmount, other.totalOverAmount) &&
                        Objects.equal(this.saveAmount3cRate, other.saveAmount3cRate) &&
                        Objects.equal(this.saveAmount2cRate, other.saveAmount2cRate) &&
                        Objects.equal(this.saveAmountPromotionRate, other.saveAmountPromotionRate) &&
                        Objects.equal(this.controlSaveRate, other.controlSaveRate) &&
                        Objects.equal(this.amountPercent, other.amountPercent) &&
                        Objects.equal(this.totalAllOrderCount, other.totalAllOrderCount) &&
                        Objects.equal(this.totalSaveAmount, other.totalSaveAmount) &&
                        Objects.equal(this.totalSavePotential, other.totalSavePotential) &&
                        Objects.equal(this.topCity, other.topCity) &&
                        Objects.equal(this.topStar, other.topStar) &&
                        Objects.equal(this.usage, other.usage) &&
                        Objects.equal(this.roomPrice, other.roomPrice) &&
                        Objects.equal(this.rcTimes, other.rcTimes) &&
                        Objects.equal(this.cancelRate, other.cancelRate);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dimId == null ? 0 : this.dimId.hashCode());
        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.avgPrice == null ? 0 : this.avgPrice.hashCode());
        result = 31 * result + (this.totalAmountTa == null ? 0 : this.totalAmountTa.hashCode());
        result = 31 * result + (this.totalQuantityTa == null ? 0 : this.totalQuantityTa.hashCode());
        result = 31 * result + (this.avgPriceTa == null ? 0 : this.avgPriceTa.hashCode());
        result = 31 * result + (this.totalAmountNta == null ? 0 : this.totalAmountNta.hashCode());
        result = 31 * result + (this.totalQuantityNta == null ? 0 : this.totalQuantityNta.hashCode());
        result = 31 * result + (this.avgPriceNta == null ? 0 : this.avgPriceNta.hashCode());
        result = 31 * result + (this.totalAmountDom == null ? 0 : this.totalAmountDom.hashCode());
        result = 31 * result + (this.totalQuantityDom == null ? 0 : this.totalQuantityDom.hashCode());
        result = 31 * result + (this.avgPriceDom == null ? 0 : this.avgPriceDom.hashCode());
        result = 31 * result + (this.totalAmountInter == null ? 0 : this.totalAmountInter.hashCode());
        result = 31 * result + (this.totalQuantityInter == null ? 0 : this.totalQuantityInter.hashCode());
        result = 31 * result + (this.avgPriceInter == null ? 0 : this.avgPriceInter.hashCode());
        result = 31 * result + (this.rcPercent == null ? 0 : this.rcPercent.hashCode());
        result = 31 * result + (this.totalOverAmount == null ? 0 : this.totalOverAmount.hashCode());
        result = 31 * result + (this.saveAmount3cRate == null ? 0 : this.saveAmount3cRate.hashCode());
        result = 31 * result + (this.saveAmount2cRate == null ? 0 : this.saveAmount2cRate.hashCode());
        result = 31 * result + (this.saveAmountPromotionRate == null ? 0 : this.saveAmountPromotionRate.hashCode());
        result = 31 * result + (this.controlSaveRate == null ? 0 : this.controlSaveRate.hashCode());
        result = 31 * result + (this.amountPercent == null ? 0 : this.amountPercent.hashCode());
        result = 31 * result + (this.totalAllOrderCount == null ? 0 : this.totalAllOrderCount.hashCode());
        result = 31 * result + (this.totalSaveAmount == null ? 0 : this.totalSaveAmount.hashCode());
        result = 31 * result + (this.totalSavePotential == null ? 0 : this.totalSavePotential.hashCode());
        result = 31 * result + (this.topCity == null ? 0 : this.topCity.hashCode());
        result = 31 * result + (this.topStar == null ? 0 : this.topStar.hashCode());
        result = 31 * result + (this.usage == null ? 0 : this.usage.hashCode());
        result = 31 * result + (this.roomPrice == null ? 0 : this.roomPrice.hashCode());
        result = 31 * result + (this.rcTimes == null ? 0 : this.rcTimes.hashCode());
        result = 31 * result + (this.cancelRate == null ? 0 : this.cancelRate.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("dimId", dimId)
                .add("dim", dim)
                .add("totalAmount", totalAmount)
                .add("totalQuantity", totalQuantity)
                .add("avgPrice", avgPrice)
                .add("totalAmountTa", totalAmountTa)
                .add("totalQuantityTa", totalQuantityTa)
                .add("avgPriceTa", avgPriceTa)
                .add("totalAmountNta", totalAmountNta)
                .add("totalQuantityNta", totalQuantityNta)
                .add("avgPriceNta", avgPriceNta)
                .add("totalAmountDom", totalAmountDom)
                .add("totalQuantityDom", totalQuantityDom)
                .add("avgPriceDom", avgPriceDom)
                .add("totalAmountInter", totalAmountInter)
                .add("totalQuantityInter", totalQuantityInter)
                .add("avgPriceInter", avgPriceInter)
                .add("rcPercent", rcPercent)
                .add("totalOverAmount", totalOverAmount)
                .add("saveAmount3cRate", saveAmount3cRate)
                .add("saveAmount2cRate", saveAmount2cRate)
                .add("saveAmountPromotionRate", saveAmountPromotionRate)
                .add("controlSaveRate", controlSaveRate)
                .add("amountPercent", amountPercent)
                .add("totalAllOrderCount", totalAllOrderCount)
                .add("totalSaveAmount", totalSaveAmount)
                .add("totalSavePotential", totalSavePotential)
                .add("topCity", topCity)
                .add("topStar", topStar)
                .add("usage", usage)
                .add("roomPrice", roomPrice)
                .add("rcTimes", rcTimes)
                .add("cancelRate", cancelRate)
                .toString();
    }
}
