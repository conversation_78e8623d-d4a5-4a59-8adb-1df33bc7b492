package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 用车部门消费
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "topList",
    "otherConsume",
    "sumConsume",
    "corpConsume",
    "industryConsume"
})
public class OverviewTopDeptConsumeInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    

    

    public OverviewTopDeptConsumeInfo(
        List<OnlineReportOverviewTopDeptConsume> topList,
        OnlineReportOverviewTopDeptConsume otherConsume,
        OnlineReportOverviewTopDeptConsume sumConsume,
        OnlineReportOverviewTopDeptConsume corpConsume,
        OnlineReportOverviewTopDeptConsume industryConsume) {
        this.topList = topList;
        this.otherConsume = otherConsume;
        this.sumConsume = sumConsume;
        this.corpConsume = corpConsume;
        this.industryConsume = industryConsume;
    }

    public OverviewTopDeptConsumeInfo() {
    }

    @JsonProperty("topList")
    public List<OnlineReportOverviewTopDeptConsume> topList;

    @JsonProperty("otherConsume")
    public OnlineReportOverviewTopDeptConsume otherConsume;

    @JsonProperty("sumConsume")
    public OnlineReportOverviewTopDeptConsume sumConsume;

    @JsonProperty("corpConsume")
    public OnlineReportOverviewTopDeptConsume corpConsume;

    @JsonProperty("industryConsume")
    public OnlineReportOverviewTopDeptConsume industryConsume;

    public List<OnlineReportOverviewTopDeptConsume> getTopList() {
        return topList;
    }

    public void setTopList(final List<OnlineReportOverviewTopDeptConsume> topList) {
        this.topList = topList;
    }
    public OnlineReportOverviewTopDeptConsume getOtherConsume() {
        return otherConsume;
    }

    public void setOtherConsume(final OnlineReportOverviewTopDeptConsume otherConsume) {
        this.otherConsume = otherConsume;
    }
    public OnlineReportOverviewTopDeptConsume getSumConsume() {
        return sumConsume;
    }

    public void setSumConsume(final OnlineReportOverviewTopDeptConsume sumConsume) {
        this.sumConsume = sumConsume;
    }
    public OnlineReportOverviewTopDeptConsume getCorpConsume() {
        return corpConsume;
    }

    public void setCorpConsume(final OnlineReportOverviewTopDeptConsume corpConsume) {
        this.corpConsume = corpConsume;
    }
    public OnlineReportOverviewTopDeptConsume getIndustryConsume() {
        return industryConsume;
    }

    public void setIndustryConsume(final OnlineReportOverviewTopDeptConsume industryConsume) {
        this.industryConsume = industryConsume;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OverviewTopDeptConsumeInfo other = (OverviewTopDeptConsumeInfo)obj;
        return
            Objects.equal(this.topList, other.topList) &&
            Objects.equal(this.otherConsume, other.otherConsume) &&
            Objects.equal(this.sumConsume, other.sumConsume) &&
            Objects.equal(this.corpConsume, other.corpConsume) &&
            Objects.equal(this.industryConsume, other.industryConsume);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.topList == null ? 0 : this.topList.hashCode());
        result = 31 * result + (this.otherConsume == null ? 0 : this.otherConsume.hashCode());
        result = 31 * result + (this.sumConsume == null ? 0 : this.sumConsume.hashCode());
        result = 31 * result + (this.corpConsume == null ? 0 : this.corpConsume.hashCode());
        result = 31 * result + (this.industryConsume == null ? 0 : this.industryConsume.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("topList", topList)
            .add("otherConsume", otherConsume)
            .add("sumConsume", sumConsume)
            .add("corpConsume", corpConsume)
            .add("industryConsume", industryConsume)
            .toString();
    }
}
