package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import java.sql.Types;

/**
 * @Description: 机票-节省与损失计算规则配置信息
 * <AUTHOR>
 * @Date 2019/5/22
 */
public class FltComputationRulePO {


    /**
     * 用户ID
     */
    @Column(name="UID")
    @Type(value= Types.VARCHAR)
    private String uId;

    /**
     * 公司ID
     */
    @Column(name="Corp_Corporation")
    @Type(value=Types.VARCHAR)
    private String corp_corporation;

    /**
     * 计算规则：0 全价,1 里程均价,2 平均票价
     */
    @Column(name="ComputationRule")
    @Type(value=Types.INTEGER)
    private int computationRule;

    /**
     * 对比对象：0 公司同比,1 商旅,2 行业
     */
    @Column(name="CompareObj")
    @Type(value=Types.INTEGER)
    private int compareObj;

    public String getuId() {
        return uId;
    }

    public void setuId(String uId) {
        this.uId = uId;
    }

    public String getCorp_corporation() {
        return corp_corporation;
    }

    public void setCorp_corporation(String corp_corporation) {
        this.corp_corporation = corp_corporation;
    }

    public int getComputationRule() {
        return computationRule;
    }

    public void setComputationRule(int computationRule) {
        this.computationRule = computationRule;
    }

    public int getCompareObj() {
        return compareObj;
    }

    public void setCompareObj(int compareObj) {
        this.compareObj = compareObj;
    }
}
