package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.corpgovernment.resource.schedule.domain.onlinereport.constant.CommonConst.CARD_SHARK_CODE_PREFIX;


/**
 * <AUTHOR>
 * @date 2024/8/20
 */
public enum CardTakeawayIndicatorEnum {
    /**
     * 部门与员工分析-机票
     */
    DEPTANALYSIS_FLIGHT_AMOUNT_CONSUMPTION("deptanalyse_flight_amount_consumption", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_flight_amount_consumption")),
    DEPTANALYSIS_FLIGHT_TICKETS("deptanalyse_flight_tickets", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_flight_tickets")),
    DEPTANALYSIS_FLIGHT_AMOUNT_LOSS_RC("deptanalyse_flight_amount_loss_RC", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_flight_amount_loss_RC")),
    DEPTANALYSIS_FLIGHT_CARBON_EMISSIONS("deptanalyse_flight_carbon_emissions", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_flight_carbon_emissions")),
    /**
     * 部门与员工分析-酒店
     */
    DEPTANALYSIS_HOTEL_AMOUNT_CONSUMPTION("deptanalyse_hotel_amount_consumption", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_hotel_amount_consumption")),
    DEPTANALYSIS_HOTEL_ROOM_NIGHTS("deptanalyse_hotel_room_nights", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_hotel_room_nights")),
    DEPTANALYSIS_HOTEL_AMOUNT_LOSS_RC("deptanalyse_hotel_amount_loss_RC", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_hotel_amount_loss_RC")),
    /**
     * 部门与员工分析-火车
     */
    DEPTANALYSIS_TRAIN_AMOUNT_CONSUMPTION("deptanalyse_train_amount_consumption", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_train_amount_consumption")),
    DEPTANALYSIS_TRAIN_TICKETS("deptanalyse_train_tickets", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_train_tickets")),
    /**
     * 部门与员工分析-用车
     */
    DEPTANALYSIS_CAR_AMOUNT_CONSUMPTION("deptanalyse_car_amount_consumption", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_car_amount_consumption")),
    DEPTANALYSIS_CAR_ORDERS("deptanalyse_car_orders", CARD_SHARK_CODE_PREFIX.concat("deptanalyse_car_orders")),
    ;

    public static List<String> listCardAboutTopData() {
        return Arrays.stream(values()).map(CardTakeawayIndicatorEnum::getCode).collect(Collectors.toList());
    }

    private String code;
    private String sharkCode;

    CardTakeawayIndicatorEnum(String code, String sharkCode) {
        this.code = code;
        this.sharkCode = sharkCode;
    }

    public String getCode() {
        return code;
    }

    public String getSharkCode() {
        return sharkCode;
    }
}
