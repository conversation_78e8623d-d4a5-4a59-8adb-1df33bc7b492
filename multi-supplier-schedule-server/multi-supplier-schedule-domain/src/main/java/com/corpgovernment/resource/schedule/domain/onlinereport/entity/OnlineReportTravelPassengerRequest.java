package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.Map;

/**
 * 差旅出行-出行人req
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "eId",
        "lang",
        "queryBu",
        "extData",
        "basecondition",
        "productType"
})
public class OnlineReportTravelPassengerRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    public OnlineReportTravelPassengerRequest(
            String eId,
            String lang,
            QueryReportBuTypeEnum queryBu,
            Map<String, String> extData,
            BaseQueryCondition basecondition,
            String productType) {
        this.eId = eId;
        this.lang = lang;
        this.queryBu = queryBu;
        this.extData = extData;
        this.basecondition = basecondition;
        this.productType = productType;
    }

    public OnlineReportTravelPassengerRequest() {
    }

    @JsonProperty("eId")
    public String eId;

    /**
     * 语言
     */
    @JsonProperty("lang")
    public String lang;

    /**
     * 查询产线
     */
    @JsonProperty("queryBu")
    public QueryReportBuTypeEnum queryBu;

    /**
     * 扩展字段
     */
    @JsonProperty("extData")
    public Map<String, String> extData;

    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    /**
     * 国内：dom，国际：inter
     */
    @JsonProperty("productType")
    public String productType;

    public String getEId() {
        return eId;
    }

    public void setEId(final String eId) {
        this.eId = eId;
    }

    /**
     * 语言
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }

    /**
     * 查询产线
     */
    public QueryReportBuTypeEnum getQueryBu() {
        return queryBu;
    }

    /**
     * 查询产线
     */
    public void setQueryBu(final QueryReportBuTypeEnum queryBu) {
        this.queryBu = queryBu;
    }

    /**
     * 扩展字段
     */
    public Map<String, String> getExtData() {
        return extData;
    }

    /**
     * 扩展字段
     */
    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * 国内：dom，国际：inter
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 国内：dom，国际：inter
     */
    public void setProductType(final String productType) {
        this.productType = productType;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTravelPassengerRequest other = (OnlineReportTravelPassengerRequest) obj;
        return
                Objects.equal(this.eId, other.eId) &&
                        Objects.equal(this.lang, other.lang) &&
                        Objects.equal(this.queryBu, other.queryBu) &&
                        Objects.equal(this.extData, other.extData) &&
                        Objects.equal(this.basecondition, other.basecondition) &&
                        Objects.equal(this.productType, other.productType);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eId == null ? 0 : this.eId.hashCode());
        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.queryBu == null ? 0 : this.queryBu.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.productType == null ? 0 : this.productType.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("eId", eId)
                .add("lang", lang)
                .add("queryBu", queryBu)
                .add("extData", extData)
                .add("basecondition", basecondition)
                .add("productType", productType)
                .toString();
    }
}
