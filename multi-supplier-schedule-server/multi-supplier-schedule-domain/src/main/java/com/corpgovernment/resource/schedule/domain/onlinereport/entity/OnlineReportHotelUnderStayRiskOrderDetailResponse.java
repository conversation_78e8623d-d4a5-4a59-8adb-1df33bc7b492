package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.List;

/**
 * 风险订单明细酒店场景2 - 提前离店-response
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "riskOrders",
    "page"
})
public class OnlineReportHotelUnderStayRiskOrderDetailResponse implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportHotelUnderStayRiskOrderDetailResponse(
        Integer responseCode,
        String responseDesc,
        List<HotelUnderStayRiskOrderDetail> riskOrders,
        Pager page) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.riskOrders = riskOrders;
        this.page = page;
        
    }

    public OnlineReportHotelUnderStayRiskOrderDetailResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    @JsonProperty("riskOrders")
    public List<HotelUnderStayRiskOrderDetail> riskOrders;

    @JsonProperty("page")
    public Pager page;



    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }
    public List<HotelUnderStayRiskOrderDetail> getRiskOrders() {
        return riskOrders;
    }

    public void setRiskOrders(final List<HotelUnderStayRiskOrderDetail> riskOrders) {
        this.riskOrders = riskOrders;
    }
    public Pager getPage() {
        return page;
    }

    public void setPage(final Pager page) {
        this.page = page;
    }



    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportHotelUnderStayRiskOrderDetailResponse other = (OnlineReportHotelUnderStayRiskOrderDetailResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc) &&
            Objects.equal(this.riskOrders, other.riskOrders) &&
            Objects.equal(this.page, other.page) ;
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.riskOrders == null ? 0 : this.riskOrders.hashCode());
        result = 31 * result + (this.page == null ? 0 : this.page.hashCode());
        

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            .add("riskOrders", riskOrders)
            .add("page", page)
            
            .toString();
    }
}
