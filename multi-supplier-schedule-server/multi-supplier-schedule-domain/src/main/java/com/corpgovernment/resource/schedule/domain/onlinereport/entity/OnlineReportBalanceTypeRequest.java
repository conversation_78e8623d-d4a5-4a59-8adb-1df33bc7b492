package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 酒店支付方式
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "eId",
    "basecondition",
    "productType"
})
public class OnlineReportBalanceTypeRequest implements Serializable {
    private static final long serialVersionUID = 1L;



    

    public OnlineReportBalanceTypeRequest(
        String eId,
        BaseQueryCondition basecondition,
        String productType) {
        this.eId = eId;
        this.basecondition = basecondition;
        this.productType = productType;
    }

    public OnlineReportBalanceTypeRequest() {
    }

    @JsonProperty("eId")
    public String eId;

    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    /**
     * 国内：dom，国际：inter
     */
    @JsonProperty("productType")
    public String productType;

    public String getEId() {
        return eId;
    }

    public void setEId(final String eId) {
        this.eId = eId;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * 国内：dom，国际：inter
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 国内：dom，国际：inter
     */
    public void setProductType(final String productType) {
        this.productType = productType;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportBalanceTypeRequest other = (OnlineReportBalanceTypeRequest)obj;
        return
            Objects.equal(this.eId, other.eId) &&
            Objects.equal(this.basecondition, other.basecondition) &&
            Objects.equal(this.productType, other.productType);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eId == null ? 0 : this.eId.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.productType == null ? 0 : this.productType.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("eId", eId)
            .add("basecondition", basecondition)
            .add("productType", productType)
            .toString();
    }
}
