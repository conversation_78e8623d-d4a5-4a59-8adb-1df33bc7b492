package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 风险订单概览详情
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "uid",
    "uidName",
    "warnLevel",
    "count",
    "amount",
    "type",
    "quantity"
})
public class RiskOrderSummary implements Serializable {
    private static final long serialVersionUID = 1L;





    public RiskOrderSummary(
        String uid,
        String uidName,
        String warnLevel,
        Integer count,
        BigDecimal amount,
        String type,
        Integer quantity) {
        this.uid = uid;
        this.uidName = uidName;
        this.warnLevel = warnLevel;
        this.count = count;
        this.amount = amount;
        this.type = type;
        this.quantity = quantity;
    }

    public RiskOrderSummary() {
    }

    /**
     * 卡号
     */
    @JsonProperty("uid")
    public String uid;

    /**
     * 持卡人
     */
    @JsonProperty("uidName")
    public String uidName;

    /**
     * 风险级别
     */
    @JsonProperty("warnLevel")
    public String warnLevel;

    /**
     * 订单数
     */
    @JsonProperty("count")
    public Integer count;

    /**
     * 金额
     */
    @JsonProperty("amount")
    public BigDecimal amount;

    /**
     * 订单产线 机票 or 酒店
     */
    @JsonProperty("type")
    public String type;

    /**
     * 间夜数
     */
    @JsonProperty("quantity")
    public Integer quantity;

    /**
     * 卡号
     */
    public String getUid() {
        return uid;
    }

    /**
     * 卡号
     */
    public void setUid(final String uid) {
        this.uid = uid;
    }

    /**
     * 持卡人
     */
    public String getUidName() {
        return uidName;
    }

    /**
     * 持卡人
     */
    public void setUidName(final String uidName) {
        this.uidName = uidName;
    }

    /**
     * 风险级别
     */
    public String getWarnLevel() {
        return warnLevel;
    }

    /**
     * 风险级别
     */
    public void setWarnLevel(final String warnLevel) {
        this.warnLevel = warnLevel;
    }

    /**
     * 订单数
     */
    public Integer getCount() {
        return count;
    }

    /**
     * 订单数
     */
    public void setCount(final Integer count) {
        this.count = count;
    }

    /**
     * 金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 金额
     */
    public void setAmount(final BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 订单产线 机票 or 酒店
     */
    public String getType() {
        return type;
    }

    /**
     * 订单产线 机票 or 酒店
     */
    public void setType(final String type) {
        this.type = type;
    }

    /**
     * 间夜数
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 间夜数
     */
    public void setQuantity(final Integer quantity) {
        this.quantity = quantity;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final RiskOrderSummary other = (RiskOrderSummary)obj;
        return
            Objects.equal(this.uid, other.uid) &&
            Objects.equal(this.uidName, other.uidName) &&
            Objects.equal(this.warnLevel, other.warnLevel) &&
            Objects.equal(this.count, other.count) &&
            Objects.equal(this.amount, other.amount) &&
            Objects.equal(this.type, other.type) &&
            Objects.equal(this.quantity, other.quantity);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.uid == null ? 0 : this.uid.hashCode());
        result = 31 * result + (this.uidName == null ? 0 : this.uidName.hashCode());
        result = 31 * result + (this.warnLevel == null ? 0 : this.warnLevel.hashCode());
        result = 31 * result + (this.count == null ? 0 : this.count.hashCode());
        result = 31 * result + (this.amount == null ? 0 : this.amount.hashCode());
        result = 31 * result + (this.type == null ? 0 : this.type.hashCode());
        result = 31 * result + (this.quantity == null ? 0 : this.quantity.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("uid", uid)
            .add("uidName", uidName)
            .add("warnLevel", warnLevel)
            .add("count", count)
            .add("amount", amount)
            .add("type", type)
            .add("quantity", quantity)
            .toString();
    }
}
