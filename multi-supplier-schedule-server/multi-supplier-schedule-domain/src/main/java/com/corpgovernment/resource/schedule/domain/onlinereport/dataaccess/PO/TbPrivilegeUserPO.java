package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Types;

/**
 * @Description: 权限配置
 * <AUTHOR>
 * @Date 2019/4/2
 */
@Entity
//@Database(name="CorpReportDB")
@Table(name="tb_privilege_user")
public class TbPrivilegeUserPO {

    // 主键
    @Id
    @Column(name="id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value= Types.BIGINT)
    private Long id;

    // 卡号
    @Column(name="uid")
    @Type(value=Types.VARCHAR)
    private String uid;

    // 公司id
    @Column(name="corp_id")
    @Type(value=Types.VARCHAR)
    private String corp_id;

    // 集团id
    @Column(name="group_id")
    @Type(value=Types.VARCHAR)
    private String group_id;

    // 角色id
    @Column(name="role_id")
    @Type(value=Types.INTEGER)
    private Integer role_id;

    // 状态：1 正常; 2 禁用;
    @Column(name="status")
    @Type(value=Types.INTEGER)
    private Integer status;

    // 创建人
    @Column(name="create_user")
    @Type(value=Types.VARCHAR)
    private String create_user;

    // 是否删除
    @Column(name="is_del")
    @Type(value=Types.TINYINT)
    private Integer is_del;

    // 用户名称
    @Column(name="user_name")
    @Type(value=Types.VARCHAR)
    private String userName;

    // 用户名称
    @Column(name="corp_name")
    @Type(value=Types.VARCHAR)
    private String corpName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getCorp_id() {
        return corp_id;
    }

    public void setCorp_id(String corp_id) {
        this.corp_id = corp_id;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public Integer getRole_id() {
        return role_id;
    }

    public void setRole_id(Integer role_id) {
        this.role_id = role_id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public Integer getIs_del() {
        return is_del;
    }

    public void setIs_del(Integer is_del) {
        this.is_del = is_del;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCorpName() {
        return corpName;
    }

    public void setCorpName(String corpName) {
        this.corpName = corpName;
    }
}
