package com.corpgovernment.resource.schedule.domain.execution.model;

import com.corpgovernment.resource.core.hotelv2.domain.enums.ExecutionNodeEnum;
import com.corpgovernment.resource.core.hotelv2.domain.enums.TaskEnum;
import com.corpgovernment.resource.core.hotelv2.domain.enums.TaskStartModeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-06 00:48
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionConfig {

    private String supplierCode;

    private List<TaskConfig> taskConfigList;

    private List<ExecutionNodeConfig> executionNodeConfigList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaskConfig {

        private String supplierCode;

        private TaskEnum taskEnum;

        private Boolean enable;

        private TaskStartModeEnum taskStartModeEnum;

        private String operation;

        private Integer priority;

        private String cronExpression;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExecutionNodeConfig {

        private String supplierCode;

        private ExecutionNodeEnum executionNodeEnum;

        private Integer flowRate;

        private List<NextExecutionNodeConfig> nextExecutionNodeConfigList;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NextExecutionNodeConfig {

        private ExecutionNodeEnum executionNodeEnum;

        private Integer priority;

        private String supplierCode;

    }

}
