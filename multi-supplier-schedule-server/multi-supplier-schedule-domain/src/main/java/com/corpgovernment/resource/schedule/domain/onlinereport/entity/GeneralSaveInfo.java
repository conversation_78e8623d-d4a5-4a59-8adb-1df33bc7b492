package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * 概览-节省
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "reportDate",
    "totalSaveAmount",
    "companySaveAmount",
    "companySaveRate",
    "companyPerQtySave",
    "corpSaveRate",
    "corpPerQtySave",
    "industrySaveRate",
    "industryPerQtySave"
})
public class GeneralSaveInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public GeneralSaveInfo(
        String reportDate,
        BigDecimal totalSaveAmount,
        BigDecimal companySaveAmount,
        BigDecimal companySaveRate,
        BigDecimal companyPerQtySave,
        BigDecimal corpSaveRate,
        BigDecimal corpPerQtySave,
        BigDecimal industrySaveRate,
        BigDecimal industryPerQtySave) {
        this.reportDate = reportDate;
        this.totalSaveAmount = totalSaveAmount;
        this.companySaveAmount = companySaveAmount;
        this.companySaveRate = companySaveRate;
        this.companyPerQtySave = companyPerQtySave;
        this.corpSaveRate = corpSaveRate;
        this.corpPerQtySave = corpPerQtySave;
        this.industrySaveRate = industrySaveRate;
        this.industryPerQtySave = industryPerQtySave;
    }

    public GeneralSaveInfo() {
    }

    /**
     * 消费记录开始时间
     */
    @JsonProperty("reportDate")
    public String reportDate;

    /**
     * 总潜节省（从有消费记录开始）
     */
    @JsonProperty("totalSaveAmount")
    public BigDecimal totalSaveAmount;

    /**
     * 公司节省金额
     */
    @JsonProperty("companySaveAmount")
    public BigDecimal companySaveAmount;

    /**
     * 公司节省率
     */
    @JsonProperty("companySaveRate")
    public BigDecimal companySaveRate;

    /**
     * 公司平均每张、每间夜节省
     */
    @JsonProperty("companyPerQtySave")
    public BigDecimal companyPerQtySave;

    /**
     * 商旅节省率
     */
    @JsonProperty("corpSaveRate")
    public BigDecimal corpSaveRate;

    /**
     * 商旅平均每张、每间夜节省
     */
    @JsonProperty("corpPerQtySave")
    public BigDecimal corpPerQtySave;

    /**
     * 行业节省率
     */
    @JsonProperty("industrySaveRate")
    public BigDecimal industrySaveRate;

    /**
     * 行业平均每张、每间夜节省
     */
    @JsonProperty("industryPerQtySave")
    public BigDecimal industryPerQtySave;

    /**
     * 消费记录开始时间
     */
    public String getReportDate() {
        String s = Optional.ofNullable(reportDate).orElse("");
        reportDate = s;
        return s;
    }

    /**
     * 消费记录开始时间
     */
    public void setReportDate(final String reportDate) {
        this.reportDate = reportDate;
    }

    /**
     * 总潜节省（从有消费记录开始）
     */
    public BigDecimal getTotalSaveAmount() {
        BigDecimal bigDecimal = Optional.ofNullable(totalSaveAmount).orElse(BigDecimal.ZERO);
        totalSaveAmount = bigDecimal;
        return bigDecimal;
    }

    /**
     * 总潜节省（从有消费记录开始）
     */
    public void setTotalSaveAmount(final BigDecimal totalSaveAmount) {
        this.totalSaveAmount = totalSaveAmount;
    }

    /**
     * 公司节省金额
     */
    public BigDecimal getCompanySaveAmount() {
        BigDecimal bigDecimal = Optional.ofNullable(companySaveAmount).orElse(BigDecimal.ZERO);
        companySaveAmount = bigDecimal;
        return bigDecimal;
    }

    /**
     * 公司节省金额
     */
    public void setCompanySaveAmount(final BigDecimal companySaveAmount) {
        this.companySaveAmount = companySaveAmount;
    }

    /**
     * 公司节省率
     */
    public BigDecimal getCompanySaveRate() {
        BigDecimal bigDecimal = Optional.ofNullable(companySaveRate).orElse(BigDecimal.ZERO);
        companySaveRate = bigDecimal;
        return bigDecimal;
    }

    /**
     * 公司节省率
     */
    public void setCompanySaveRate(final BigDecimal companySaveRate) {
        this.companySaveRate = companySaveRate;
    }

    /**
     * 公司平均每张、每间夜节省
     */
    public BigDecimal getCompanyPerQtySave() {
        BigDecimal bigDecimal = Optional.ofNullable(companyPerQtySave).orElse(BigDecimal.ZERO);
        companyPerQtySave = bigDecimal;
        return bigDecimal;
    }

    /**
     * 公司平均每张、每间夜节省
     */
    public void setCompanyPerQtySave(final BigDecimal companyPerQtySave) {
        this.companyPerQtySave = companyPerQtySave;
    }

    /**
     * 商旅节省率
     */
    public BigDecimal getCorpSaveRate() {
        BigDecimal bigDecimal = Optional.ofNullable(corpSaveRate).orElse(BigDecimal.ZERO);
        corpSaveRate = bigDecimal;
        return bigDecimal;
    }

    /**
     * 商旅节省率
     */
    public void setCorpSaveRate(final BigDecimal corpSaveRate) {
        this.corpSaveRate = corpSaveRate;
    }

    /**
     * 商旅平均每张、每间夜节省
     */
    public BigDecimal getCorpPerQtySave() {
        BigDecimal bigDecimal = Optional.ofNullable(corpPerQtySave).orElse(BigDecimal.ZERO);
        corpPerQtySave = bigDecimal;
        return bigDecimal;
    }

    /**
     * 商旅平均每张、每间夜节省
     */
    public void setCorpPerQtySave(final BigDecimal corpPerQtySave) {
        this.corpPerQtySave = corpPerQtySave;
    }

    /**
     * 行业节省率
     */
    public BigDecimal getIndustrySaveRate() {
        BigDecimal bigDecimal = Optional.ofNullable(industrySaveRate).orElse(BigDecimal.ZERO);
        industrySaveRate = bigDecimal;
        return bigDecimal;
    }

    /**
     * 行业节省率
     */
    public void setIndustrySaveRate(final BigDecimal industrySaveRate) {
        this.industrySaveRate = industrySaveRate;
    }

    /**
     * 行业平均每张、每间夜节省
     */
    public BigDecimal getIndustryPerQtySave() {
        BigDecimal bigDecimal = Optional.ofNullable(industryPerQtySave).orElse(BigDecimal.ZERO);
        industryPerQtySave = bigDecimal;
        return bigDecimal;
    }

    /**
     * 行业平均每张、每间夜节省
     */
    public void setIndustryPerQtySave(final BigDecimal industryPerQtySave) {
        this.industryPerQtySave = industryPerQtySave;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final GeneralSaveInfo other = (GeneralSaveInfo)obj;
        return
            Objects.equal(this.reportDate, other.reportDate) &&
            Objects.equal(this.totalSaveAmount, other.totalSaveAmount) &&
            Objects.equal(this.companySaveAmount, other.companySaveAmount) &&
            Objects.equal(this.companySaveRate, other.companySaveRate) &&
            Objects.equal(this.companyPerQtySave, other.companyPerQtySave) &&
            Objects.equal(this.corpSaveRate, other.corpSaveRate) &&
            Objects.equal(this.corpPerQtySave, other.corpPerQtySave) &&
            Objects.equal(this.industrySaveRate, other.industrySaveRate) &&
            Objects.equal(this.industryPerQtySave, other.industryPerQtySave);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.reportDate == null ? 0 : this.reportDate.hashCode());
        result = 31 * result + (this.totalSaveAmount == null ? 0 : this.totalSaveAmount.hashCode());
        result = 31 * result + (this.companySaveAmount == null ? 0 : this.companySaveAmount.hashCode());
        result = 31 * result + (this.companySaveRate == null ? 0 : this.companySaveRate.hashCode());
        result = 31 * result + (this.companyPerQtySave == null ? 0 : this.companyPerQtySave.hashCode());
        result = 31 * result + (this.corpSaveRate == null ? 0 : this.corpSaveRate.hashCode());
        result = 31 * result + (this.corpPerQtySave == null ? 0 : this.corpPerQtySave.hashCode());
        result = 31 * result + (this.industrySaveRate == null ? 0 : this.industrySaveRate.hashCode());
        result = 31 * result + (this.industryPerQtySave == null ? 0 : this.industryPerQtySave.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("reportDate", reportDate)
            .add("totalSaveAmount", totalSaveAmount)
            .add("companySaveAmount", companySaveAmount)
            .add("companySaveRate", companySaveRate)
            .add("companyPerQtySave", companyPerQtySave)
            .add("corpSaveRate", corpSaveRate)
            .add("corpPerQtySave", corpPerQtySave)
            .add("industrySaveRate", industrySaveRate)
            .add("industryPerQtySave", industryPerQtySave)
            .toString();
    }
}
