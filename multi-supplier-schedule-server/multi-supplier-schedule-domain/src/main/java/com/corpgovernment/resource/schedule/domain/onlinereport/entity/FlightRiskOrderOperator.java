package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 风险订单机票-操作记录
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "orderId",
    "passengerName",
    "flightCity",
    "riskScene",
    "operateStatus",
    "operatorTime",
    "operator",
    "operatorMark"
})
public class FlightRiskOrderOperator implements Serializable {
    private static final long serialVersionUID = 1L;

    

    

    public FlightRiskOrderOperator(
        String orderId,
        String passengerName,
        String flightCity,
        String riskScene,
        Integer operateStatus,
        String operatorTime,
        String operator,
        String operatorMark) {
        this.orderId = orderId;
        this.passengerName = passengerName;
        this.flightCity = flightCity;
        this.riskScene = riskScene;
        this.operateStatus = operateStatus;
        this.operatorTime = operatorTime;
        this.operator = operator;
        this.operatorMark = operatorMark;
    }

    public FlightRiskOrderOperator() {
    }

    /**
     * 订单号
     */
    @JsonProperty("orderId")
    public String orderId;

    /**
     * 乘机人
     */
    @JsonProperty("passengerName")
    public String passengerName;

    /**
     * 航段
     */
    @JsonProperty("flightCity")
    public String flightCity;

    /**
     * 风险场景
     */
    @JsonProperty("riskScene")
    public String riskScene;

    /**
     * 操作状态
     */
    @JsonProperty("operateStatus")
    public Integer operateStatus;

    /**
     * 操作时间
     */
    @JsonProperty("operatorTime")
    public String operatorTime;

    /**
     * 操作人
     */
    @JsonProperty("operator")
    public String operator;

    /**
     * 操作说明
     */
    @JsonProperty("operatorMark")
    public String operatorMark;

    /**
     * 订单号
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 订单号
     */
    public void setOrderId(final String orderId) {
        this.orderId = orderId;
    }

    /**
     * 乘机人
     */
    public String getPassengerName() {
        return passengerName;
    }

    /**
     * 乘机人
     */
    public void setPassengerName(final String passengerName) {
        this.passengerName = passengerName;
    }

    /**
     * 航段
     */
    public String getFlightCity() {
        return flightCity;
    }

    /**
     * 航段
     */
    public void setFlightCity(final String flightCity) {
        this.flightCity = flightCity;
    }

    /**
     * 风险场景
     */
    public String getRiskScene() {
        return riskScene;
    }

    /**
     * 风险场景
     */
    public void setRiskScene(final String riskScene) {
        this.riskScene = riskScene;
    }

    /**
     * 操作状态
     */
    public Integer getOperateStatus() {
        return operateStatus;
    }

    /**
     * 操作状态
     */
    public void setOperateStatus(final Integer operateStatus) {
        this.operateStatus = operateStatus;
    }

    /**
     * 操作时间
     */
    public String getOperatorTime() {
        return operatorTime;
    }

    /**
     * 操作时间
     */
    public void setOperatorTime(final String operatorTime) {
        this.operatorTime = operatorTime;
    }

    /**
     * 操作人
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作人
     */
    public void setOperator(final String operator) {
        this.operator = operator;
    }

    /**
     * 操作说明
     */
    public String getOperatorMark() {
        return operatorMark;
    }

    /**
     * 操作说明
     */
    public void setOperatorMark(final String operatorMark) {
        this.operatorMark = operatorMark;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightRiskOrderOperator other = (FlightRiskOrderOperator)obj;
        return
            Objects.equal(this.orderId, other.orderId) &&
            Objects.equal(this.passengerName, other.passengerName) &&
            Objects.equal(this.flightCity, other.flightCity) &&
            Objects.equal(this.riskScene, other.riskScene) &&
            Objects.equal(this.operateStatus, other.operateStatus) &&
            Objects.equal(this.operatorTime, other.operatorTime) &&
            Objects.equal(this.operator, other.operator) &&
            Objects.equal(this.operatorMark, other.operatorMark);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.orderId == null ? 0 : this.orderId.hashCode());
        result = 31 * result + (this.passengerName == null ? 0 : this.passengerName.hashCode());
        result = 31 * result + (this.flightCity == null ? 0 : this.flightCity.hashCode());
        result = 31 * result + (this.riskScene == null ? 0 : this.riskScene.hashCode());
        result = 31 * result + (this.operateStatus == null ? 0 : this.operateStatus.hashCode());
        result = 31 * result + (this.operatorTime == null ? 0 : this.operatorTime.hashCode());
        result = 31 * result + (this.operator == null ? 0 : this.operator.hashCode());
        result = 31 * result + (this.operatorMark == null ? 0 : this.operatorMark.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("orderId", orderId)
            .add("passengerName", passengerName)
            .add("flightCity", flightCity)
            .add("riskScene", riskScene)
            .add("operateStatus", operateStatus)
            .add("operatorTime", operatorTime)
            .add("operator", operator)
            .add("operatorMark", operatorMark)
            .toString();
    }
}
