/*
package com.corpgovernment.resource.schedule.domain.onlinereport.customreport;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.CustomReportTemplateBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.dao.CustomReportTemplateDAOExt;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

*/
/**
 * Auther:abguo
 * Date:2019/8/17
 * Description:
 * Project:onlinereportweb
 *//*

@Service
public class CustomReportTemplateService {
    @Autowired
    private CustomReportTemplateDAOExt customReportTemplateDAOExt;
    @Autowired
    private CustomReportTemplateMapper mapper;

    public List<CustomReportTemplateBO> queryByIds(List<Long> ids) throws SQLException {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        List<List<Long>> list = new ArrayList<>();
        int batchSize = 50;
        if (ids.size() > batchSize) {
            list = Lists.partition(ids, batchSize);
        } else {
            list.add(ids);
        }

        List<CustomReportTemplateBO> result = new ArrayList<>();
        for (List<Long> item : list) {
            List<CustomReportTemplatePO> entitys = customReportTemplateDAOExt.queryByIds(item);
            if (CollectionUtils.isEmpty(entitys)) {
                return null;
            }
            result.addAll(entitys.stream().map(p -> mapper.mapper(p)).collect(Collectors.toList()));
        }
        return result;
    }

    public CustomReportTemplateBO queryById(Long id) throws SQLException {
        CustomReportTemplatePO entity = customReportTemplateDAOExt.queryByPk(id);
        if (entity == null) {
            return null;
        }
        return mapper.mapper(entity);
    }

    public int count(CustomReportTemplateBO info) throws SQLException {
        CustomReportTemplatePO po = mapper.mapper(info);
        List<CustomReportTemplatePO> list = customReportTemplateDAOExt.queryBy(po);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        } else {
            return list.size();
        }
    }

    public List<CustomReportTemplateBO> queryByCondition(CustomReportTemplateBO info) throws SQLException {
        CustomReportTemplatePO po = mapper.mapper(info);
        List<CustomReportTemplatePO> list = customReportTemplateDAOExt.queryBy(po);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(mapper::mapper).collect(Collectors.toList());
    }

    public List<CustomReportTemplateBO> queryAll(CustomReportTemplateBO info) throws SQLException {
        CustomReportTemplatePO po = mapper.mapper(info);
        List<CustomReportTemplatePO> list = customReportTemplateDAOExt.queryAll();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(mapper::mapper).collect(Collectors.toList());
    }

    public int insert(CustomReportTemplateBO info) throws SQLException {
        CustomReportTemplatePO po = mapper.mapper(info);
        if (po == null) {
            return 0;
        }
        KeyHolder keyHolder = new KeyHolder();
        int result = customReportTemplateDAOExt.insertWithKeyHolder(keyHolder, po);
        if (result > 0 && keyHolder.getKey() != null) {
            return keyHolder.getKey().intValue();
        }
        return 0;
    }

    public int update(CustomReportTemplateBO info) throws SQLException {
        CustomReportTemplatePO po = mapper.mapper(info);
        if (po == null) {
            return 0;
        }
        return customReportTemplateDAOExt.update(po);
    }

    public int delete(CustomReportTemplateBO info) throws SQLException {
        CustomReportTemplatePO po = mapper.mapper(info);
        if (po == null) {
            return 0;
        }

        return customReportTemplateDAOExt.delete(po);
    }


    public CustomReportTemplatePO queryByUid(String uid) throws SQLException {
        List<CustomReportTemplatePO> list = customReportTemplateDAOExt.queryByUID(uid);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
*/
