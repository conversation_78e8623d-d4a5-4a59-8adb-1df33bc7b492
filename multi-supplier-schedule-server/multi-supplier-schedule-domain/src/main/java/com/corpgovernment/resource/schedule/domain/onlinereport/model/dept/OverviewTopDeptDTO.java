package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
public class OverviewTopDeptDTO {

    @Column(name = "corp_name")
    @Type(value = Types.VARCHAR)
    private String corp_name;

    @Column(name = "aggId")
    @Type(value = Types.VARCHAR)
    private String aggId;

    @Column(name = "aggType")
    @Type(value = Types.VARCHAR)
    private String aggType;

    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;

    @Column(name = "totalOverAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalOverAmount;

    @Column(name = "totalRcTimes")
    @Type(value = Types.INTEGER)
    private Integer totalRcTimes;

    @Column(name = "totalOrderCount")
    @Type(value = Types.INTEGER)
    private Integer totalOrderCount;

    @Column(name = "totalCarbonEmission")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCarbonEmission;

}
