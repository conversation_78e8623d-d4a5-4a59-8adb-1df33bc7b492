package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 事件
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "eventId",
    "eventName",
    "eventTime",
    "eventContent",
    "levelTwoId",
    "responseLevel"
})
public class EventInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    

    

    public EventInfo(
        Long eventId,
        String eventName,
        String eventTime,
        String eventContent,
        Integer levelTwoId,
        Integer responseLevel) {
        this.eventId = eventId;
        this.eventName = eventName;
        this.eventTime = eventTime;
        this.eventContent = eventContent;
        this.levelTwoId = levelTwoId;
        this.responseLevel = responseLevel;
    }

    public EventInfo() {
    }

    /**
     * 事件id
     */
    @JsonProperty("eventId")
    public Long eventId;

    /**
     * 事件名称
     */
    @JsonProperty("eventName")
    public String eventName;

    /**
     * 事件发生时间
     */
    @JsonProperty("eventTime")
    public String eventTime;

    /**
     * 事件内容
     */
    @JsonProperty("eventContent")
    public String eventContent;

    /**
     * 二级分类 台风：1001,暴雪：1003,泥石流:1020,地震：1014,新冠疫情本土确诊事件：1305
     */
    @JsonProperty("levelTwoId")
    public Integer levelTwoId;

    /**
     * 响应级别
     */
    @JsonProperty("responseLevel")
    public Integer responseLevel;

    /**
     * 事件id
     */
    public Long getEventId() {
        return eventId;
    }

    /**
     * 事件id
     */
    public void setEventId(final Long eventId) {
        this.eventId = eventId;
    }

    /**
     * 事件名称
     */
    public String getEventName() {
        return eventName;
    }

    /**
     * 事件名称
     */
    public void setEventName(final String eventName) {
        this.eventName = eventName;
    }

    /**
     * 事件发生时间
     */
    public String getEventTime() {
        return eventTime;
    }

    /**
     * 事件发生时间
     */
    public void setEventTime(final String eventTime) {
        this.eventTime = eventTime;
    }

    /**
     * 事件内容
     */
    public String getEventContent() {
        return eventContent;
    }

    /**
     * 事件内容
     */
    public void setEventContent(final String eventContent) {
        this.eventContent = eventContent;
    }

    /**
     * 二级分类 台风：1001,暴雪：1003,泥石流:1020,地震：1014,新冠疫情本土确诊事件：1305
     */
    public Integer getLevelTwoId() {
        return levelTwoId;
    }

    /**
     * 二级分类 台风：1001,暴雪：1003,泥石流:1020,地震：1014,新冠疫情本土确诊事件：1305
     */
    public void setLevelTwoId(final Integer levelTwoId) {
        this.levelTwoId = levelTwoId;
    }

    /**
     * 响应级别
     */
    public Integer getResponseLevel() {
        return responseLevel;
    }

    /**
     * 响应级别
     */
    public void setResponseLevel(final Integer responseLevel) {
        this.responseLevel = responseLevel;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final EventInfo other = (EventInfo)obj;
        return
            Objects.equal(this.eventId, other.eventId) &&
            Objects.equal(this.eventName, other.eventName) &&
            Objects.equal(this.eventTime, other.eventTime) &&
            Objects.equal(this.eventContent, other.eventContent) &&
            Objects.equal(this.levelTwoId, other.levelTwoId) &&
            Objects.equal(this.responseLevel, other.responseLevel);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eventId == null ? 0 : this.eventId.hashCode());
        result = 31 * result + (this.eventName == null ? 0 : this.eventName.hashCode());
        result = 31 * result + (this.eventTime == null ? 0 : this.eventTime.hashCode());
        result = 31 * result + (this.eventContent == null ? 0 : this.eventContent.hashCode());
        result = 31 * result + (this.levelTwoId == null ? 0 : this.levelTwoId.hashCode());
        result = 31 * result + (this.responseLevel == null ? 0 : this.responseLevel.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("eventId", eventId)
            .add("eventName", eventName)
            .add("eventTime", eventTime)
            .add("eventContent", eventContent)
            .add("levelTwoId", levelTwoId)
            .add("responseLevel", responseLevel)
            .toString();
    }
}
