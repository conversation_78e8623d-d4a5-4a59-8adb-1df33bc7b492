package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.hotel;

/**
 * @Description: 酒店城市价格分析明细
 * <AUTHOR>
 * @Date 2019/5/7
 */
public class HotelPriceAnysisDetailBO {
    private String cityName;
    private Integer city;
    private Integer quantity;
    private String memAvgPrice;
    private String argAvgPrice;
    private String corpMemAvgPrice;
    private String corpArgAvgPrice;

    public HotelPriceAnysisDetailBO(){
        this.city=0;
        this.cityName="";
        this.quantity=0;
        this.memAvgPrice="0";
        this.argAvgPrice="0";
        this.corpMemAvgPrice="0";
        this.corpArgAvgPrice="0";
//        this.star=0;
//        this.countryID=0;
//        this.provinceID=0;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getMemAvgPrice() {
        return memAvgPrice;
    }

    public void setMemAvgPrice(String memAvgPrice) {
        this.memAvgPrice = memAvgPrice;
    }

    public String getArgAvgPrice() {
        return argAvgPrice;
    }

    public void setArgAvgPrice(String argAvgPrice) {
        this.argAvgPrice = argAvgPrice;
    }

    public String getCorpMemAvgPrice() {
        return corpMemAvgPrice;
    }

    public void setCorpMemAvgPrice(String corpMemAvgPrice) {
        this.corpMemAvgPrice = corpMemAvgPrice;
    }

    public String getCorpArgAvgPrice() {
        return corpArgAvgPrice;
    }

    public void setCorpArgAvgPrice(String corpArgAvgPrice) {
        this.corpArgAvgPrice = corpArgAvgPrice;
    }

//    public Integer getStar() {
//        return star;
//    }
//
//    public void setStar(Integer star) {
//        this.star = star;
//    }
//
//    public Integer getCountryID() {
//        return countryID;
//    }
//
//    public void setCountryID(Integer countryID) {
//        this.countryID = countryID;
//    }
//
//    public Integer getProvinceID() {
//        return provinceID;
//    }
//
//    public void setProvinceID(Integer provinceID) {
//        this.provinceID = provinceID;
//    }
}
