package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.email;


import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/11 13:50
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class ReportSaveEmailBO {

    private String local;

    private String showRiskOrder;

    private List<String> email;

    private String defaultEmail;

    public List<String> getEmail() {
        return email;
    }

    public void setEmail(List<String> email) {
        this.email = email;
    }

    public String getShowRiskOrder() {
        return showRiskOrder;
    }

    public void setShowRiskOrder(String showRiskOrder) {
        this.showRiskOrder = showRiskOrder;
    }

    public String getDefaultEmail() {
        return defaultEmail;
    }

    public void setDefaultEmail(String defaultEmail) {
        this.defaultEmail = defaultEmail;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    @Override
    public String toString() {
        return "ReportSaveEmailBO{" +
                "showRiskOrder='" + showRiskOrder + '\'' +
                ", email=" + email + '\'' +
                ", defaultEmail=" +  defaultEmail + '\'' +
                ", local=" + local +
                '}';
    }
}
