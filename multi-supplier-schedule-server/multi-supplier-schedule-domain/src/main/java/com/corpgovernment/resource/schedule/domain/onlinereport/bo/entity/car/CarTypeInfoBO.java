package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.car;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2019/6/19 11:17
 */
public class CarTypeInfoBO {

    /*均价*/
    private Double averagePrice;
    /*租车天数 */
    private float days;
    /*车型*/
    private String name;
    /*订单数*/
    private Integer orderNumber;
    /*总金额*/
    private Double totalPrice;
    /*前五消费部门*/
    private String top5Depart;

    private String depart;

    /**
     * 部门分析
     */
    private List<CarTypeInfoBO> deptAnalysisInfoBOList;

    public List<CarTypeInfoBO> getDeptAnalysisInfoBOList() {
        return deptAnalysisInfoBOList;
    }

    public void setDeptAnalysisInfoBOList(List<CarTypeInfoBO> deptAnalysisInfoBOList) {
        this.deptAnalysisInfoBOList = deptAnalysisInfoBOList;
    }

    public String getDepart() {
        return depart;
    }

    public void setDepart(String depart) {
        this.depart = depart;
    }

    public Double getAveragePrice() {
        return averagePrice;
    }

    public void setAveragePrice(Double averagePrice) {
        this.averagePrice = averagePrice;
    }

    public float getDays() {
        return days;
    }

    public void setDays(float days) {
        this.days = days;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Integer orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getTop5Depart() {
        return top5Depart;
    }

    public void setTop5Depart(String top5Depart) {
        this.top5Depart = top5Depart;
    }

    public CarTypeInfoBO(){
        this.averagePrice=0.0;
        this.days=0;
        this.depart="";
        this.deptAnalysisInfoBOList=new ArrayList<>(0);
        this.name="";
        this.top5Depart="";
    }
}
