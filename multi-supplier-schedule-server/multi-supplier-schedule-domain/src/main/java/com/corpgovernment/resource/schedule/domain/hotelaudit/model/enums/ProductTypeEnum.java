package com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-24 21:57
 */
@AllArgsConstructor
@Getter
public enum ProductTypeEnum {
    
    HOTEL("hotel"),
    HOTEL_INTL("hotelIntl");
    
    private final String code;
    
    private static final Map<String, ProductTypeEnum> map = new HashMap<>();
    
    static {
        for (ProductTypeEnum tmpEnum : values()) {
            map.put(tmpEnum.getCode(), tmpEnum);
            map.put(tmpEnum.name(), tmpEnum);
        }
    }
    
    public static ProductTypeEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return map.get(key);
    }
    
}
