package com.corpgovernment.resource.schedule.domain.onlinereport.po;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * @Description: 自定义报表持久实体
 * <AUTHOR>
 * @Date 2019/2/27
 */
@Entity
////@Database(name = "CorpReportDB")
@Table(name = "customreportinfonew")
public class CustomReportInfoNewPO{

    /**
     * key
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 报表名称
     */
    @Column(name = "name")
    @Type(value = Types.VARCHAR)
    private String name;

    /**
     * 所属UID
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    /**
     * 报表
     */
    @Column(name = "reportCondition")
    @Type(value = Types.VARCHAR)
    private String reportCondition;

    /**
     * 报表
     */
    @Column(name = "isValid")
    @Type(value = Types.BIT)
    private Boolean isValid;

    /**
     * 报表描述
     */
    @Column(name = "remark")
    @Type(value = Types.VARCHAR)
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "dataChange_createTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 修改时间
     */
    @Column(name = "dataChange_lastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 报表类型
     */
    @Column(name = "reportType")
    @Type(value = Types.SMALLINT)
    private Integer reportType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getReportCondition() {
        return reportCondition;
    }

    public void setReportCondition(String reportCondition) {
        this.reportCondition = reportCondition;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Boolean getValid() {
        return isValid;
    }

    public void setValid(Boolean valid) {
        isValid = valid;
    }

    public Integer getReportType() {
        return reportType;
    }

    public void setReportType(Integer reportType) {
        this.reportType = reportType;
    }
}
