package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo;


import com.corpgovernment.common.enums.RcTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TravelPositionStepEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.risk.RiskOrderOperateBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.vo.budget.BudgetPanelConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.AnalysisObjectOrgInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.BaseQueryCondition;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.Pager;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.TravelPositionDetail;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/*
 * <AUTHOR>
 * @date 2021/11/8 11:01
 * @Desc
 */
@Setter
@Getter
public class BaseQueryConditionBO {
    // 产线
    private String queryBu;
    // 一个图表多个卡片的情况下作为卡片索引
    private String index;
    // 行程-金额
    private String queryType;
    // 查询聚合维度-日期
    private String dateDimension;
    // 查询聚合方式
    private String aggType;
    // 基础查询条件
    private BaseQueryCondition baseQueryCondition;
    // 拓展字段
    private Map<String, String> extData;
    // 语言
    private String lang;
    // 数据类型（COMPANY-公司；CORP-商旅；INDUSTRY-行业）
    private String dataType;

    /**
     * 同比类型（同比去年/同比前年）
     */
    private String yoy_type;

    /**
     * 分析对象
     */
    private String analysisObject;

    /**
     * 分页数据
     */
    private Pager pager;

    /**
     * 子产线
     */
    private int subQueryBu;

    /**
     * 为了兼容老版的top5目的地、top5成本中心
     */
    private String item;

    /*
     * 机票全部(all)、国际(inter)、国内(dom)、
     * 酒店全部(all)、海外(inter)、国内(dom)
     * */
    private String productType;

    /**
     * RC类型 {@link RcTypeEnum}
     */
    private String rcType;

    /**
     * 是否三方协议 C：是；NC：否
     */
    public String contractType;

    /**
     * 订单id
     */
    private List<String> orderids;
    /**
     * 出差申请单
     */
    private List<String> bizTravelNos;
    /**
     * 风险订单uids
     */
    private List<String> uids;

    private String sortKey;

    private boolean asc;

    private String riskScene;

    private String cityName;
    private String provinceName;
    private String countryName;

    private List<Integer> operatorStatus;
    private RiskOrderOperateBO riskOrderOperateBO;
    /**
     * 出发城市ID
     */
    List<Integer> startCityId;
    /**
     * 到达城市ID
     */
    List<Integer> arriveCityId;
    /**
     * 热门航段格式:上海-北京
     */
    String flightCity;
    /**
     * 1：热门航线 2：离开城市 3：到达城市 4:热点城市
     */
    private Integer queryCityType;

    private List<QueryReportBuTypeEnum> queryBus;
    private TravelPositionStepEnum travelStep;
    private List<TravelPositionDetail> queryColumn;
    private List<String> riskReasons;

    /*单程：ONEWAY，总计:TOTAL*/
    private String trendDimensionType;
    /*是否超标*/
    private List<String> exceedStandard;
    /*订单状态*/
    private List<String> orderstatusList;
    /*出行人卡号或姓名*/
    private List<String> passengers;
    /*预订人卡号或姓名*/
    private List<String> users;
    /*申请人卡号或姓名*/
    private List<String> approvers;
    /*报告库明细用户自定义列*/
    private List<String> reportColumns;
    /*星级*/
    private Integer star;
    /*持卡人员工编号*/
    private List<String> employeIds;
    /*预订人姓名*/
    private List<String> userNames;
    /*国家id*/
    private List<Integer> countryIds;
    /*城市id*/
    private List<Integer> cityIds;
    /*城市类型列表*/
    private List<Integer> cityTypeIds;
    /*职级id多选*/
    private List<Integer> rankIds;
    /*差标计算器星级*/
    private List<Integer> stars;
    /*差标计算器品牌*/
    private List<Integer> brands;
    /*差标计算器商圈*/
    private List<Integer> zones;
    /*cityId*/
    private Integer cityId;
    private String currency;
    private Double price;
    /*建议code筛选*/
    private List<Integer> suggestions;
    /*品牌、地区搜索关键词*/
    private String searchKey;
    private String indicator;
    Long parentId;
    private AnalysisObjectOrgInfo analysisObjectOrgInfo;
    /*预订人卡号或姓名*/

    private String user;
    /*下转对象*/
    private String drillDownObjectEnum;
    /*下转对象值*/
    private String drillDownVal;
    /*分析对象具体值*/
    private String analysisObjectVal;
    /*预算分析卡片*/
    private BudgetPanelConditionBO budgetPanel;
    /*分析对象具体值集合*/
    private List<String> analysisObjectDimIds;

    /*酒店*/
    public List<String> hotelIds;

    public List<String> airLines;

    public List<String> hotelGroups;

    /*航班号*/
    private List<String> flightNos;
    private String temptemptemptemptemptemptemptemption;
}
