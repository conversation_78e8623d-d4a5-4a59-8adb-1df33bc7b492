package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisObjectEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.AnalysisTypeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 前5部门超标
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "eId",
    "basecondition",
    "analysisObjectEnum",
    "analysisTypeEnum",
    "page"
})
public class OnlineReportTopFiveDeptAnalysisRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportTopFiveDeptAnalysisRequest(
        String eId,
        BaseQueryCondition basecondition,
        AnalysisObjectEnum analysisObjectEnum,
        AnalysisTypeEnum analysisTypeEnum,
        Pager page) {
        this.eId = eId;
        this.basecondition = basecondition;
        this.analysisObjectEnum = analysisObjectEnum;
        this.analysisTypeEnum = analysisTypeEnum;
        this.page = page;
    }

    public OnlineReportTopFiveDeptAnalysisRequest() {
    }

    @JsonProperty("eId")
    public String eId;

    /**
     * 基本查询条件
     */
    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    /**
     * 分析对象
     */
    @JsonProperty("analysisObjectEnum")
    public AnalysisObjectEnum analysisObjectEnum;

    /**
     * 查询产线
     */
    @JsonProperty("analysisTypeEnum")
    public AnalysisTypeEnum analysisTypeEnum;

    /**
     * page翻页
     */
    @JsonProperty("page")
    public Pager page;

    public String getEId() {
        return eId;
    }

    public void setEId(final String eId) {
        this.eId = eId;
    }

    /**
     * 基本查询条件
     */
    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    /**
     * 基本查询条件
     */
    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * 分析对象
     */
    public AnalysisObjectEnum getAnalysisObjectEnum() {
        return analysisObjectEnum;
    }

    /**
     * 分析对象
     */
    public void setAnalysisObjectEnum(final AnalysisObjectEnum analysisObjectEnum) {
        this.analysisObjectEnum = analysisObjectEnum;
    }

    /**
     * 查询产线
     */
    public AnalysisTypeEnum getAnalysisTypeEnum() {
        return analysisTypeEnum;
    }

    /**
     * 查询产线
     */
    public void setAnalysisTypeEnum(final AnalysisTypeEnum analysisTypeEnum) {
        this.analysisTypeEnum = analysisTypeEnum;
    }

    /**
     * page翻页
     */
    public Pager getPage() {
        return page;
    }

    /**
     * page翻页
     */
    public void setPage(final Pager page) {
        this.page = page;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTopFiveDeptAnalysisRequest other = (OnlineReportTopFiveDeptAnalysisRequest)obj;
        return
            Objects.equal(this.eId, other.eId) &&
            Objects.equal(this.basecondition, other.basecondition) &&
            Objects.equal(this.analysisObjectEnum, other.analysisObjectEnum) &&
            Objects.equal(this.analysisTypeEnum, other.analysisTypeEnum) &&
            Objects.equal(this.page, other.page);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eId == null ? 0 : this.eId.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.analysisObjectEnum == null ? 0 : this.analysisObjectEnum.hashCode());
        result = 31 * result + (this.analysisTypeEnum == null ? 0 : this.analysisTypeEnum.hashCode());
        result = 31 * result + (this.page == null ? 0 : this.page.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("eId", eId)
            .add("basecondition", basecondition)
            .add("analysisObjectEnum", analysisObjectEnum)
            .add("analysisTypeEnum", analysisTypeEnum)
            .add("page", page)
            .toString();
    }
}
