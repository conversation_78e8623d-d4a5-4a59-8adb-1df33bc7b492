package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 机票折扣分布
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "title",
    "discountDistNotice",
    "legends",
    "data"
})
public class FlightDiscountDist implements Serializable {
    private static final long serialVersionUID = 1L;





    public FlightDiscountDist(
        String title,
        String discountDistNotice,
        List<OnlineReportTrendLegend> legends,
        List<OnlineReportTrendPoint> data) {
        this.title = title;
        this.discountDistNotice = discountDistNotice;
        this.legends = legends;
        this.data = data;
    }

    public FlightDiscountDist() {
    }

    /**
     * 标题
     */
    @JsonProperty("title")
    public String title;

    /**
     * 折扣文案
     */
    @JsonProperty("discountDistNotice")
    public String discountDistNotice;

    @JsonProperty("legends")
    public List<OnlineReportTrendLegend> legends;

    @JsonProperty("data")
    public List<OnlineReportTrendPoint> data;

    /**
     * 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 标题
     */
    public void setTitle(final String title) {
        this.title = title;
    }

    /**
     * 折扣文案
     */
    public String getDiscountDistNotice() {
        return discountDistNotice;
    }

    /**
     * 折扣文案
     */
    public void setDiscountDistNotice(final String discountDistNotice) {
        this.discountDistNotice = discountDistNotice;
    }
    public List<OnlineReportTrendLegend> getLegends() {
        return legends;
    }

    public void setLegends(final List<OnlineReportTrendLegend> legends) {
        this.legends = legends;
    }
    public List<OnlineReportTrendPoint> getData() {
        return data;
    }

    public void setData(final List<OnlineReportTrendPoint> data) {
        this.data = data;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightDiscountDist other = (FlightDiscountDist)obj;
        return
            Objects.equal(this.title, other.title) &&
            Objects.equal(this.discountDistNotice, other.discountDistNotice) &&
            Objects.equal(this.legends, other.legends) &&
            Objects.equal(this.data, other.data);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.title == null ? 0 : this.title.hashCode());
        result = 31 * result + (this.discountDistNotice == null ? 0 : this.discountDistNotice.hashCode());
        result = 31 * result + (this.legends == null ? 0 : this.legends.hashCode());
        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("title", title)
            .add("discountDistNotice", discountDistNotice)
            .add("legends", legends)
            .add("data", data)
            .toString();
    }
}
