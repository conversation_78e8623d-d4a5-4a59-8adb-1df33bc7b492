package com.corpgovernment.resource.schedule.domain.onlinereport.utils;


import com.corpgovernment.resource.schedule.domain.onlinereport.constant.CommonConst;
import onlinereport.enums.enums.DateFormatEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间格式化操作类
 */
public final class DateFormatUtil {

    private DateFormatUtil() {
    }

    /**
     * 字符串转换为long
     * yyyyMMdd HHmmss -> 1529380800000
     * /Date(1529380800000+0800)/->1529380800000
     *
     * @param strTime
     * @return
     * @throws ParseException
     */
    public static long toTimestamp(String strTime) throws DateTimeException {
        String result = strTime.toLowerCase();
        try {

            if (result.indexOf("date") != -1) {
                result = result.substring(6, strTime.length() - 2);
                if (result.length() > 13) {
                    result = result.substring(0, 13);
                }
            }
            result = String.valueOf(new Timestamp(Long.valueOf(result)).getTime());
        } catch (Exception ex) {
            try {
                Date tempDate = DateFormatUtil.toDate(strTime, DateFormatEnum.SIMPLE.getFormat());
                result = String.valueOf(tempDate.getTime());
            } catch (Exception ex1) {
                throw new DateTimeException("toTimestamp error", ex1);
            }
        }
        return Long.valueOf(result);
    }

    /**
     * 时间格式化,simple格式:yyyyMMdd HHmmss
     *
     * @param date
     * @return
     */
    public static String toSimple(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(DateFormatEnum.SIMPLE.getFormat()).format(date);
    }

    /**
     * 时间格式化,simple格式:yyyyMMdd HHmmss
     *
     * @param time 时间的long值
     * @return
     */
    public static String toSimple(long time) {
        return new SimpleDateFormat(DateFormatEnum.SIMPLE.getFormat()).format(new Date(time));

    }

    /**
     * 字符串转换为normal时间格式
     * yyyyMMdd HHmmss -> yyyy-MM-dd HH:mm:ss
     *
     * @param original
     * @return
     * @throws ParseException
     */
    public static String toNormal(String original) {
        if (original == null) {
            return "";
        }
        try {
            Date date = toDate(original, DateFormatEnum.SIMPLE.getFormat());
            return format(date, DateFormatEnum.NORMAL);//转换成string
        } catch (ParseException e) {
            return "";
        }
    }

    /**
     * 字符串转换为normal时间格式(yyyy-MM-dd HH:mm:ss)
     *
     * @param originalDate
     * @return
     * @throws ParseException
     */
    public static String toNormal(Date originalDate) {
        if (originalDate == null) {
            return "";
        }
        try {
            return format(originalDate, DateFormatEnum.NORMAL);//转换成string
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 字符串转换为normal时间格式(yyyy-MM-dd HH:mm:ss)
     *
     * @param originalTime
     * @return
     * @throws ParseException
     */
    public static String toNormal(Long originalTime) {
        if (originalTime == null) {
            return "";
        }
        try {
            Date originalDate = new Date(originalTime);
            return format(originalDate, DateFormatEnum.NORMAL);//转换成string
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 字符串转换为对应格式
     * yyyyMMdd HHmmss -> yyyy-MM-dd HH:mm:ss
     *
     * @param string
     * @return
     */
    public static String format(String string, DateFormatEnum dateFormat) {
        if (string == null) {
            return "";
        }
        try {
            Date date = toDate(string, DateFormatEnum.SIMPLE.getFormat());//转换成date
            return format(date, dateFormat);//转换成string
        } catch (ParseException e) {
            return "";
        }
    }

    /**
     * 字符串转换为normal时间格式
     * yyyyMMdd HHmmss -> yyyy-MM-dd HH:mm:ss
     *
     * @param string
     * @param dateFormat
     * @return
     */
    public static String format(String string, String dateFormat) {
        if (string == null) {
            return "";
        }
        try {
            Date date = toDate(string, DateFormatEnum.SIMPLE.getFormat());//转换成date
            return format(date, dateFormat);//转换成string
        } catch (ParseException e) {
            return "";
        }
    }

    /**
     * 时间戳转换为对应时间格式
     * yyyyMMdd HHmmss -> DateFormatEnum
     *
     * @param timestamp
     * @return
     * @throws ParseException
     */
    public static String format(long timestamp, DateFormatEnum dateFormat) {
        if (timestamp <= 0) {
            return "";
        }
        Date date = new Date(timestamp);
        return format(date, dateFormat);//转换成string
    }

    /**
     * 字符串转换为对应格式
     * yyyyMMdd HHmmss -> yyyy-MM-dd HH:mm:ss
     *
     * @param string
     * @return
     * @throws ParseException
     */
    public static String format(String string, String inFormat, String outFormat) {
        if (string == null) {
            return "";
        }
        SimpleDateFormat inDateFormat = new SimpleDateFormat(inFormat);
        SimpleDateFormat outDateFormat = new SimpleDateFormat(outFormat);
        try {
            Date inDate = inDateFormat.parse(string);
            return outDateFormat.format(inDate);
        } catch (ParseException e) {
            return "";
        }
    }

    /**
     * 转换时间，格式如yyyy-MM-dd HH:mm:ss
     *
     * @param date
     * @return
     */
    public static String format(Date date, DateFormatEnum dateFormatEnum) {
        return format(date, dateFormatEnum.getFormat());
    }


    /**
     * 转换时间，格式如yyyy-MM-dd HH:mm:ss
     *
     * @param date
     * @return
     */
    public static String format(Date date, String dateFormat) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
        return formatter.format(date);
    }

    /**
     * timestamp to canlendar
     *
     * @param timestamp
     * @return
     */
    public static Calendar toCalendar(Date timestamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(timestamp);
        return calendar;
    }

    /**
     * 字符串转换为对应日期
     *
     * @param string
     * @param pattern
     * @return
     * @throws ParseException
     */
    public static Date toDate(String string, String pattern) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        Date date = dateFormat.parse(string);
        return date;
    }

    /**
     * 字符串转换成Date
     *
     * @param string  待转换字符串
     * @param pattern 字符串格式
     * @return
     */
    public static Date parseDate(String string, String pattern) {
        if (StringUtils.isBlank(string)) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        try {
            format.setLenient(false);
            return format.parse(string);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * Date转Timestamp
     *
     * @param date
     * @return
     */
    public static Timestamp toTimestamp(Date date) {
        try {
            return new Timestamp(date.getTime());
        } catch (Exception e) {
            return null;
        }
    }

    public static String getDateTimeOfFirstDay(String dateTime) {
        if (StringUtils.isBlank(dateTime)) {
            return StringUtils.EMPTY;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(CommonConst.DATE_FORMAT2);
        LocalDate localDate = LocalDate.parse(dateTime, dateTimeFormatter);
        // 获取月份-第一天
        return localDate.withDayOfMonth(NumberUtils.INTEGER_ONE).toString();
    }

    public static String getDateTimeOfLastDay(String dateTime) {
        if (StringUtils.isBlank(dateTime)) {
            return StringUtils.EMPTY;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(CommonConst.DATE_FORMAT2);
        LocalDate localDate = LocalDate.parse(dateTime, dateTimeFormatter);
        // 获取月份-最后一天
        return localDate.withDayOfMonth(localDate.lengthOfMonth()).toString();
    }
}
