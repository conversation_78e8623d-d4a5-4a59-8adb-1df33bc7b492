package com.corpgovernment.resource.schedule.domain.onlinereport.utils;


import com.corpgovernment.resource.schedule.domain.onlinereport.constant.CommonConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import onlinereport.enums.DimensionSetEnum;
import onlinereport.enums.enums.DateFormatEnum;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Pattern;

/**
 * @Description: 公共工具类
 * <AUTHOR>
 * @Date 2019/4/2
 */
public class CommonUtils {

    private static volatile Properties prop = null;


    /*当前时间*/
    public static Timestamp getDateTimeNow() {
        Timestamp d = new Timestamp(System.currentTimeMillis());
        return d;
    }

    /*格式化时间*/
    public static String dateFormat(Date d, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(d);
    }

    public static String dateFormat(long d, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(d);
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(calendar.getTime());
    }

    /*格式化时间*/
    public static String dateFormat(Timestamp d, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(d);
    }

    /*转换成Timestamp时间*/
    public static Timestamp convertToTimestamp(int year, int month, int day, String format) {
        String dateStr = String.format(CommonConst.DATE_FORMAT4 + " 0:0:0", year, month, day);
        return convertToTimestamp(dateStr, format);
    }

    /*转换成Timestamp时间,ym 201801*/
    public static Timestamp convertToTimestamp(int ym, String format) {
        String ymStr = String.valueOf(ym);
        String dateStr = String.format(CommonConst.DATE_FORMAT4 + " 0:0:0", ymStr.substring(0, 4), ymStr.substring(4), 1);
        return convertToTimestamp(dateStr, format);
    }

    /*string转换成Timestamp时间*/
    public static Timestamp convertToTimestamp(String date, String format) {
        try {
            Timestamp t = Timestamp.valueOf(date);
            return t;
        } catch (Exception ex) {
            return null;
        }
    }

    /*string转换成Date时间*/
    public static Date convertToDate(String date, String format) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(format);
            Date t = dateFormat.parse(date);
            return t;
        } catch (Exception ex) {
            return null;
        }
    }

    /*获取时间的年月日，type=Calendar.YEAR,Calendar.MONTH...*/
    public static int getDatePart(Date d, int type) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        int v = c.get(type);
        if (type == Calendar.MONTH) {
            v = v + 1;
        }
        return v;
    }

    //获取该月份的天数
    public static int daysInMonth(int year, int month) {
        String str = String.format(CommonConst.DATE_FORMAT3, year, month, 1, 0, 0, 0);
        Date d = convertToDate(str, CommonConst.DATE_FORMAT);
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(Calendar.MONTH, 1);
        c.add(Calendar.DAY_OF_MONTH, -1);
        Date lastDayOfMonth = c.getTime();

        c.setTime(lastDayOfMonth);
        int v = c.get(Calendar.DAY_OF_MONTH);
        return v;
    }

    //日期加减
    public static Timestamp dateAdd(Timestamp d, Integer field, Integer amount) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.add(field, amount);
        Timestamp t = new Timestamp(c.getTimeInMillis());
        return t;
    }

    //计算两个日期之间相差的月份
    public static Integer getMonthSpace(Timestamp date1, Timestamp date2) {
        Calendar bef = Calendar.getInstance();
        Calendar aft = Calendar.getInstance();
        bef.setTime(date1);
        aft.setTime(date2);
        return getMonthSpace(bef, aft);
    }

    /**
     * 计算两个日期之间相差的月份
     *
     * @param bef
     * @param aft
     * @return
     */
    public static Integer getMonthSpace(Calendar bef, Calendar aft) {
        int result = aft.get(Calendar.MONTH) - bef.get(Calendar.MONTH);
        int month = (aft.get(Calendar.YEAR) - bef.get(Calendar.YEAR)) * 12;
        return Math.abs(month + result);
    }

    //反斜杠转义
    public static String replayBackslash(String str, boolean isReplaceQuotes) {
        if (str != null && !str.isEmpty()) {
            str = str.replace("\\", "/");
            if (isReplaceQuotes) {
                //替换“''”为“'”
                str = str.replace("''", "'");
            }
        }
        return str;
    }

    //获取明细分表
    public static List<String> getFltDownloadPartTable(Integer startTime, Integer endTime) {
        Integer startYear = Integer.parseInt(startTime.toString().substring(0, 4));
        Integer startMonth = Integer.parseInt(startTime.toString().substring(4));
        Integer endYear = Integer.parseInt(endTime.toString().substring(0, 4));
        Integer endMonth = Integer.parseInt(endTime.toString().substring(4));

        String startStr = String.format(CommonConst.DATE_FORMAT3, startYear, startMonth, 1, 0, 0, 0);
        String endStr = String.format(CommonConst.DATE_FORMAT3, endYear, endMonth, 1, 0, 0, 0);

        Timestamp t1 = convertToTimestamp(startStr, CommonConst.DATE_FORMAT);
        Timestamp t2 = convertToTimestamp(endStr, CommonConst.DATE_FORMAT);
        return getFltDownloadPartTable(t1, t2);
    }

    //获取明细分表
    public static List<String> getFltDownloadPartTable(Timestamp dtStart, Timestamp dtEnd) {
        HashSet<String> listTable = new HashSet<String>();
        String tbName = "";
        Integer count = 0;
        Integer limit = 1000;
        while (dtStart.compareTo(dtEnd) <= 0) {
            Integer y = getDatePart(dtStart, Calendar.YEAR);
            Integer m = getDatePart(dtStart, Calendar.MONTH);
            tbName = String.format("corp_flt_download_%s_%s", y % 4, ((m - 1) / 3 + 1));
            listTable.add(tbName);
            dtStart = dateAdd(dtStart, Calendar.MONTH, 1);
            count++;
            if (count > limit) {
                break;
            }
        }
        List<String> list = new ArrayList<>(listTable);
        return list;
    }

    //根据月份设置分区值
    public static Date getPartitionVal(Integer y, Integer m) {
            /*
             表分区必须时间格式，所以固定为 2015-MM-01、2016-MM-01、2017-MM-01、2018-MM-01 的格式
             * 其他年份数据落入对应分区即可
             */
        Integer partitionYear = getPartitionYear(y);
        String dateStr = String.format(CommonConst.DATE_FORMAT3, partitionYear, m, 1, 0, 0, 0);
        Date dt = convertToDate(dateStr, CommonConst.DATE_FORMAT2);
        return dt;
    }

    //根据月份设置分区值
    public static Date getPartitionVal(Integer y, Integer m, boolean isDownload) {
        /*
         表分区必须时间格式，所以固定为 2015-MM-01、2016-MM-01、2017-MM-01、2018-MM-01 的格式
         * 其他年份数据落入对应分区即可
         */
        Integer partitionYear = y;
        if (isDownload) {
            partitionYear = getPartitionYear(y);
        }
        String dateStr = String.format(CommonConst.DATE_FORMAT3, partitionYear, m, 1, 0, 0, 0);
        Date dt = convertToDate(dateStr, CommonConst.DATE_FORMAT2);
        return dt;
    }

    //根据数据年份获取分区年
    public static int getPartitionYear(Integer y) {
        Integer yKey = y % 4;
        Integer partitionYear = 0;
        if (yKey == (2015 % 4)) {
            partitionYear = 2015;
        } else if (yKey == (2016 % 4)) {
            partitionYear = 2016;
        } else if (yKey == (2017 % 4)) {
            partitionYear = 2017;
        } else if (yKey == (2018 % 4)) {
            partitionYear = 2018;
        }
        return partitionYear;
    }

    //年月转换成yyyyMM格式
    public static Integer getYM(Integer y, Integer m) {
        if (y == null || m == null) {
            return 0;
        }
        return y * 100 + m;
    }

    public static String getYSlashM(Integer y, Integer m) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, y);
        calendar.set(Calendar.MONTH, m - 1);

        DateFormat df = new SimpleDateFormat("yyyy/MM");
        return df.format(calendar.getTime());
    }

    //年月转换成yyyyMM格式
    public static Integer getYM(Timestamp dt) {
        if (dt == null) {
            return 0;
        }
        return getDatePart(dt, Calendar.YEAR) * 100 + getDatePart(dt, Calendar.MONTH);
    }

    /**
     * 时间转换成yyyyMM格式
     *
     * @param calendar
     * @return
     */
    public static Integer getYM(Calendar calendar) {
        return calendar.get(Calendar.YEAR) * 100 + (calendar.get(Calendar.MONTH) + 1);
    }

    //生成整数数列
    public static List<Integer> range(Integer start, Integer count) {
        List<Integer> list = new ArrayList<>();
        Integer size = start + count;
        for (Integer i = start; i < size; i++) {
            list.add(i);
        }
        return list;
    }

    //根据维度类型获取对应字段
    public static String getDimensionSuffixName(String dimensionType) {
        return getDimensionSuffixName(dimensionType, false);
    }

    /**
     * 根据维度类型获取对应字段
     *
     * @param dimensionType
     * @param isField
     * @return
     */
    public static String getDimensionSuffixName(String dimensionType, boolean isField) {
        Integer dimensionVal = Integer.parseInt(dimensionType);
        DimensionSetEnum dimensionSet = DimensionSetEnum.values()[dimensionVal];
        if (isField) {
            if (dimensionSet.compareTo(DimensionSetEnum.account) == 0) {
                return "accountid";
            } else if (dimensionSet.compareTo(DimensionSetEnum.corporation) == 0) {
                return "corp_corporation";
            }
        }
        return dimensionSet.toString();
    }

    /**
     * 是否整数
     *
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        Pattern pattern = Pattern.compile("-?[0-9]+");
        return pattern.matcher(str).matches();
    }

    /**
     * 是否整数
     *
     * @param str
     * @return
     */
    public static boolean isNumeric1(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        //有的数值类型经过千分位符号处理，需要先去除千分位符号
        Pattern pattern = Pattern.compile("-?[0-9]+");
        return pattern.matcher(str.replaceAll(",", "")).matches();
    }

    /**
     * 是否数值
     *
     * @param str
     * @return
     */
    public static boolean isNum(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        //有的数值类型经过千分位符号处理，需要先去除千分位符号
        Pattern pattern = Pattern.compile("^[-+]?[0-9]+(\\.[0-9]+)?$");
        return pattern.matcher(str.replaceAll(",", "")).matches();
    }

    /**
     * 是否数值
     *
     * @param str
     * @return
     */
    public static boolean isNum1(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[-+]?[0-9]+(\\.[0-9]+)?$");
        return pattern.matcher(str).matches();
    }

    /**
     * 是否是数字包含千分位
     *
     * @param str
     * @return true：包含，false：不包含
     */
    public static boolean isContainThousand(String str) {
        return isNum(str) && str.contains(GlobalConst.SEPARATOR);
    }

    /**
     * 是否百分数
     *
     * @param str
     * @return
     */
    public static boolean isPercent(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        //有的数值类型经过千分位符号处理，需要先去除千分位符号
        Pattern pattern = Pattern.compile("^[-+]?[0-9]+(\\.[0-9]+)?%$");
        return pattern.matcher(str.replaceAll(",", "")).matches();
    }


    /**
     * 千分位格式化
     *
     * @param target
     * @return
     */
    public static String fmtMicrometer(String target) {
        if (CommonUtils.isNum(target)) {
            if (target.indexOf(".") > 0 && target.indexOf(",") < 0) {
                int digit = target.length() - target.indexOf(".") - 1;
                StringBuffer stringBuffer = new StringBuffer("###,##0.");
                for (int i = 1; i <= digit; i++) {
                    stringBuffer.append("0");
                }
                DecimalFormat df = new DecimalFormat(stringBuffer.toString());
                return df.format(DigitParseUtils.tryParseDouble(target));
            }
        }
        return target;
    }

    /**
     * 千分位格式化
     *
     * @param target
     * @return
     */
    public static Object fmtMicrometer(Object target) {
        if (target instanceof BigDecimal || target instanceof Double || target instanceof Float) {
            String temp = target.toString();
            int digit = temp.length() - temp.indexOf(".") - 1;
            StringBuffer stringBuffer = new StringBuffer("###,##0.");
            for (int i = 1; i <= digit; i++) {
                stringBuffer.append("0");
            }
            DecimalFormat df = new DecimalFormat(stringBuffer.toString());
            return df.format(((BigDecimal) target).doubleValue());
        }
        return target;
    }

    /**
     * 千分位格式化
     *
     * @param target
     * @return
     */
    public static String fmtMicrometer(double target) {
        DecimalFormat df = new DecimalFormat("###,##0.00");
        return df.format(target);
    }

    /**
     * 千分位格式化
     *
     * @param target
     * @return
     */
    public static String fmtMicrometer(BigDecimal target) {
        DecimalFormat df = new DecimalFormat("###,##0.00");
        return df.format(target);
    }

    /**
     * 四舍五入
     *
     * @param number
     * @param digit
     * @return
     */
    public static double roundingOff(double number, int digit) {
        BigDecimal bigDecimal = new BigDecimal(new Double(number).toString());
        double rounding = bigDecimal.setScale(digit, BigDecimal.ROUND_HALF_UP).doubleValue();
        return rounding;
    }

    /**
     * 除法运算
     *
     * @param numerator
     * @param denominator
     * @return
     */
    public static double divisionByInterger(int numerator, int denominator) {
        if (denominator == 0) {
            return 0;
        }
        return (double) numerator / (double) denominator;
    }

    /**
     * tryParseInt
     *
     * @param intVal
     * @param defaultVal
     * @return
     */
    public static int tryParseInt(String intVal, int defaultVal) {
        try {
            return Integer.parseInt(intVal);
        } catch (Exception ex) {
            return defaultVal;
        }
    }

    /**
     * 转换时间
     *
     * @param millis
     * @return
     */
    public static Calendar convertCalendar(long millis) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(millis);
        return calendar;
    }

    /**
     * 转换时间
     *
     * @param year
     * @param month
     * @param day
     * @return
     */
    public static Calendar convertCalendar(int year, int month, int day) {
        Timestamp timestamp = convertToTimestamp(year, month, day, DateFormatEnum.NORMAL.getFormat());
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timestamp.getTime());
        return calendar;
    }

    /**
     * 根据月份获取季度
     *
     * @param month
     * @return
     */
    public static int getQuarterByMonth(int month) {
        return (month - 1) / 3 + 1;
    }


    //降序排序
    public static <K, V extends Comparable<? super V>> Map<K, V> sortByValueDescending(Map<K, V> map) {
        List<Map.Entry<K, V>> list = new LinkedList<Map.Entry<K, V>>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<K, V>>() {
            @Override
            public int compare(Map.Entry<K, V> o1, Map.Entry<K, V> o2) {
                int compare = (o1.getValue()).compareTo(o2.getValue());
                return -compare;
            }
        });

        Map<K, V> result = new LinkedHashMap<K, V>();
        for (Map.Entry<K, V> entry : list) {
            result.put(entry.getKey(), entry.getValue());
        }
        return result;
    }

    //升序排序
    public static <K, V extends Comparable<? super V>> Map<K, V> sortByValueAscending(Map<K, V> map) {
        List<Map.Entry<K, V>> list = new LinkedList<Map.Entry<K, V>>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<K, V>>() {
            @Override
            public int compare(Map.Entry<K, V> o1, Map.Entry<K, V> o2) {
                int compare = (o1.getValue()).compareTo(o2.getValue());
                return compare;
            }
        });

        Map<K, V> result = new LinkedHashMap<K, V>();
        for (Map.Entry<K, V> entry : list) {
            result.put(entry.getKey(), entry.getValue());
        }
        return result;
    }

    /**
     * 深度拷贝
     *
     * @param src
     * @param <>
     * @return
     * @throws IOException
     * @throws ClassNotFoundException
     */
    public static <T> List<T> deepCopyList(List<T> src) throws IOException, ClassNotFoundException {
        ObjectOutputStream out = null;
        ObjectInputStream in = null;
        @SuppressWarnings("unchecked")
        List<T> dest;
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            out = new ObjectOutputStream(byteOut);
            out.writeObject(src);

            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            in = new ObjectInputStream(byteIn);
            dest = (List<T>) in.readObject();
        } finally {
            if (in != null) {
                in.close();
            }

            if (out != null) {
                out.close();
            }
        }
        return dest;
    }

    /**
     * 通过BeanCopyUtils实现深度拷贝，并且支持其他List类型
     *
     * @param src
     * @param <T>
     * @return
     * @throws NoSuchMethodException
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     * @throws InstantiationException
     */
    public static <T> List<T> deepCopyListNew(List<T> src) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException, InstantiationException {
        if (src == null) {
            return null;
        }

        Class<? extends List> listClass = src.getClass();
        List dest = listClass.getConstructor().newInstance();
        if (src.isEmpty()) {
            return dest;
        }

        for (T t : src) {
            T copy;
            if (t instanceof HashMap) {
                copy = (T) new HashMap<>();
                ((HashMap) copy).putAll((HashMap) t);
            } else {
                copy = (T) BeanCopyUtil.copy(t, t.getClass());
            }
            dest.add(copy);
        }
        return dest;
    }

    /**
     * 深拷贝Map
     *
     * @param source
     * @return
     */
    public static <K, V> Map<K, V> deepCopyMap(Map<K, V> source) {
        // HashMap可以实现真正意义上深拷贝，注意不是Map
        HashMap<K, V> paramMap = new HashMap();
        // 实现深拷贝：使用HashMap.putAll()
        paramMap.putAll(source);
        return paramMap;
    }

    public static String replacePercent(String source) {
        return source.replace("%", "");
    }

    /**
     * 转换百分数为小数
     *
     * @param percent
     * @return
     */
    public static String transferPercent(String percent) {
        try {
            NumberFormat nf = NumberFormat.getPercentInstance();
            Double m = nf.parse(percent).doubleValue();
            DecimalFormat df = new DecimalFormat("######0.0000");
            df.format(m);
            return df.format(m);
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }

    public static String transferPercentTest(String percent) {
        try {
            NumberFormat nf = NumberFormat.getPercentInstance();
            nf.setMaximumFractionDigits(4);
            Double m = (Double) nf.parse(percent);
            NumberFormat nf1 = NumberFormat.getNumberInstance();
            return nf1.format(m);
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }

    /**
     * 取消'+'符号
     *
     * @param symbol
     * @return
     */
    public static String cancelSymbol(String symbol) {
        if (StringUtils.isBlank(symbol)) {
            return StringUtils.EMPTY;
        }
        return StringUtils.remove(symbol, CommonConst.DEFAULT_PLUS);
    }

    public static int getNumberLen(String val) {
        int len = 0;
        if (CommonUtils.isNum(val)) {
            for (char c : val.toCharArray()) {
                if (c >= '0' && c <= '9') {
                    len++;
                }
            }
        }
        return len;
    }

    public static void main(String[] args) {
        String str = "20%";
        String str1 = "20.1%";
        String str2 = "20.0%";
        String str3 = "20.0";
        String str4 = "0.0%";
        String str5 = ".0%";
        String str6 = "0%";
        String str7 = "%";
        String str8 = "";
        String str9 = " ";
        String str10 = null;
        System.out.println(isPercent(str));
        System.out.println(isPercent(str1));
        System.out.println(isPercent(str2));
        System.out.println(isPercent(str3));
        System.out.println(isPercent(str4));
        System.out.println(isPercent(str5));
        System.out.println(isPercent(str6));
        System.out.println(isPercent(str7));
        System.out.println(transferPercent(str1));
        System.out.println(isPercent(str8));
        System.out.println(isPercent(str9));
        System.out.println(isPercent(str10));
        String str11 = "100.0%";
        String str12 = "0.0%";
        String str13 = "0%";
        String str14 = "100.1%";
        String str15 = "200.0%";
        String str16 = "200.2%";
        String str17 = "200.8%";
        System.out.println(transferPercentTest(str11));
        System.out.println(transferPercentTest(str12));
        System.out.println(transferPercentTest(str13));
        System.out.println(transferPercent(str14));
        System.out.println(transferPercent(str15));
        System.out.println(transferPercent(str16));
        System.out.println(transferPercent(str17));

        System.out.println("isPercent>>" + isPercent("82,449.00"));
    }
}
