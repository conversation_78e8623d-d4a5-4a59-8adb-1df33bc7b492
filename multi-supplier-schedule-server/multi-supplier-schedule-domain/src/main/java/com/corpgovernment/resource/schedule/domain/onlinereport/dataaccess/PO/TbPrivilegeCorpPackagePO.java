package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * @Description: 权限配置
 * <AUTHOR>
 * @Date 2019/4/2
 */
@Entity
//@Database(name="CorpReportDB")
@Table(name="tb_privilege_corp_package")
public class TbPrivilegeCorpPackagePO {

    // 主键
    @Id
    @Column(name="id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value= Types.BIGINT)
    private Long id;

    // 公司id
    @Column(name="corp_id")
    @Type(value=Types.VARCHAR)
    private String corp_id;

    // 集团id
    @Column(name="group_id")
    @Type(value=Types.VARCHAR)
    private String group_id;

    // 角色id
    @Column(name="package_id")
    @Type(value=Types.INTEGER)
    private Integer packageId;

    // 角色id
    @Column(name="corp_type")
    @Type(value=Types.INTEGER)
    private Integer corp_type;


    /**
     * 有效期开始时间
     */
    @Column(name = "valid_starttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp validStarttime;

    /**
     * 有效期结束时间
     */
    @Column(name = "valid_endtime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp validEndtime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCorp_id() {
        return corp_id;
    }

    public void setCorp_id(String corp_id) {
        this.corp_id = corp_id;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public Integer getPackageId() {
        return packageId;
    }

    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

    public Integer getCorp_type() {
        return corp_type;
    }

    public void setCorp_type(Integer corp_type) {
        this.corp_type = corp_type;
    }

    public Timestamp getValidStarttime() {
        return validStarttime;
    }

    public void setValidStarttime(Timestamp validStarttime) {
        this.validStarttime = validStarttime;
    }

    public Timestamp getValidEndtime() {
        return validEndtime;
    }

    public void setValidEndtime(Timestamp validEndtime) {
        this.validEndtime = validEndtime;
    }
}
