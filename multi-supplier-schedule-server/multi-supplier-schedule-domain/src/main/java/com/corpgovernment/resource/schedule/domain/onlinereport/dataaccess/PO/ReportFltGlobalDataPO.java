package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import java.sql.Types;


/**
 * @Description: report_fltglobaldata实体
 * <AUTHOR>
 * @Date 2019/5/22
 */
public class ReportFltGlobalDataPO {

    /**
     * 类型
     */
    @Column(name = "FlightClass")
    @Type(value = Types.CHAR)
    private String flightClass;

    /**
     * 支付类型
     */
    @Column(name = "PrepayType")
    @Type(value = Types.VARCHAR)
    private String prepayType;

    /**
     * 航线
     */
    @Column(name = "FlightCity")
    @Type(value = Types.VARCHAR)
    private String flightCity;

    /**
     * 行业
     */
    @Column(name = "IndustryType")
    @Type(value = Types.VARCHAR)
    private String industryType;

    /**
     * 公司体量
     */
    @Column(name = "customer_type")
    @Type(value = Types.VARCHAR)
    private String customerType;

    /**
     * 舱位
     */
    @Column(name = "Class")
    @Type(value = Types.CHAR)
    private String fltClass;//class 关键字

    /**
     * 预定渠道
     */
    @Column(name = "IsOnline")
    @Type(value = Types.CHAR)
    private String isOnline;

    /**
     * 成交净价
     */
    @Column(name = "Price")
    @Type(value = Types.DOUBLE)
    private Double price;

    /**
     * 张数
     */
    @Column(name = "Quantity")
    @Type(value = Types.INTEGER)
    private Integer quantity;

    /**
     * 里程
     */
    @Column(name = "TPM_S")
    @Type(value = Types.DOUBLE)
    private Double tpmS;

    /**
     * 提前天数
     */
    @Column(name = "PreorderDate")
    @Type(value = Types.INTEGER)
    private Integer preorderDate;

    /**
     * 全价票张数
     */
    @Column(name = "FullQuantity")
    @Type(value = Types.INTEGER)
    private Integer fullQuantity;

    /**
     * 折扣
     */
    @Column(name = "PriceRate")
    @Type(value = Types.DOUBLE)
    private Double priceRate;

    /**
     * 退票张数
     */
    @Column(name = "refundQuantity")
    @Type(value = Types.INTEGER)
    private Integer refundQuantity;

    /**
     * 改签张数
     */
    @Column(name = "rebookQuantity")
    @Type(value = Types.INTEGER)
    private Integer rebookQuantity;

    /**
     * 违反低价rc张数
     */
    @Column(name = "lowRCQuantity")
    @Type(value = Types.INTEGER)
    private Integer lowRCQuantity;

    /**
     * 国际机票航程1提前预定天数
     */
    @Column(name = "preorderDateS1")
    @Type(value = Types.INTEGER)
    private Integer preorderDateS1;
    /**
     * 国际机票航程1张数
     */
    @Column(name = "quantityS1")
    @Type(value = Types.INTEGER)
    private Integer quantityS1;

    /**
     * 最低价预定张数
     */
    @Column(name = "LowPriceQuantity")
    @Type(value = Types.INTEGER)
    private Integer lowPriceQuantity;

    /**
     * 出票张数
     */
    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    /**
     * rc管控为T的出票张数
     */
    @Column(name = "quantityInCharge")
    @Type(value = Types.INTEGER)
    private Integer quantityInCharge;


    /**
     * rc管控为T的违反低价RC张数
     */
    @Column(name = "lowRCQuantityInCharge")
    @Type(value = Types.INTEGER)
    private Integer lowRCQuantityInCharge;

    /**
     * 时间
     */
    @Column(name = "Print_DateTime")
    @Type(value = Types.INTEGER)
    private Integer printDatetime;


    public String getFlightClass() {
        return flightClass;
    }

    public void setFlightClass(String flightClass) {
        this.flightClass = flightClass;
    }

    public String getPrepayType() {
        return prepayType;
    }

    public void setPrepayType(String prepayType) {
        this.prepayType = prepayType;
    }

    public String getFlightCity() {
        return flightCity;
    }

    public void setFlightCity(String flightCity) {
        this.flightCity = flightCity;
    }

    public String getIndustryType() {
        return industryType;
    }

    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getFltClass() {
        return fltClass;
    }

    public void setFltClass(String fltClass) {
        this.fltClass = fltClass;
    }

    public String getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(String isOnline) {
        this.isOnline = isOnline;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Double getTpmS() {
        return tpmS;
    }

    public void setTpmS(Double tpmS) {
        this.tpmS = tpmS;
    }

    public Integer getPreorderDate() {
        return preorderDate;
    }

    public void setPreorderDate(Integer preorderDate) {
        this.preorderDate = preorderDate;
    }

    public Integer getFullQuantity() {
        return fullQuantity;
    }

    public void setFullQuantity(Integer fullQuantity) {
        this.fullQuantity = fullQuantity;
    }

    public Double getPriceRate() {
        return priceRate;
    }

    public void setPriceRate(Double priceRate) {
        this.priceRate = priceRate;
    }

    public Integer getRefundQuantity() {
        return refundQuantity;
    }

    public void setRefundQuantity(Integer refundQuantity) {
        this.refundQuantity = refundQuantity;
    }

    public Integer getRebookQuantity() {
        return rebookQuantity;
    }

    public void setRebookQuantity(Integer rebookQuantity) {
        this.rebookQuantity = rebookQuantity;
    }

    public Integer getLowRCQuantity() {
        return lowRCQuantity;
    }

    public void setLowRCQuantity(Integer lowRCQuantity) {
        this.lowRCQuantity = lowRCQuantity;
    }

    public Integer getPreorderDateS1() {
        return preorderDateS1;
    }

    public void setPreorderDateS1(Integer preorderDateS1) {
        this.preorderDateS1 = preorderDateS1;
    }

    public Integer getLowPriceQuantity() {
        return lowPriceQuantity;
    }

    public void setLowPriceQuantity(Integer lowPriceQuantity) {
        this.lowPriceQuantity = lowPriceQuantity;
    }

    public Integer getQuantityS1() {
        return quantityS1;
    }

    public void setQuantityS1(Integer quantityS1) {
        this.quantityS1 = quantityS1;
    }

    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Integer getQuantityInCharge() {
        return quantityInCharge;
    }

    public void setQuantityInCharge(Integer quantityInCharge) {
        this.quantityInCharge = quantityInCharge;
    }

    public Integer getLowRCQuantityInCharge() {
        return lowRCQuantityInCharge;
    }

    public void setLowRCQuantityInCharge(Integer lowRCQuantityInCharge) {
        this.lowRCQuantityInCharge = lowRCQuantityInCharge;
    }

    public Integer getPrintDatetime() {
        return printDatetime;
    }

    public void setPrintDatetime(Integer printDatetime) {
        this.printDatetime = printDatetime;
    }
}
