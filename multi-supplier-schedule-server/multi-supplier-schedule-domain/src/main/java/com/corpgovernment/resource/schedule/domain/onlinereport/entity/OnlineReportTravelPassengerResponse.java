package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 差旅出行-出行人resp
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "passengerList",
    "extData"
})
public class OnlineReportTravelPassengerResponse implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportTravelPassengerResponse(
        Integer responseCode,
        String responseDesc,
        List<TravelPassengerInfo> passengerList,
        Map<String, String> extData) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.passengerList = passengerList;
        this.extData = extData;
        
    }

    public OnlineReportTravelPassengerResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    /**
     * 足迹
     */
    @JsonProperty("passengerList")
    public List<TravelPassengerInfo> passengerList;

    @JsonProperty("extData")
    public Map<String, String> extData;



    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }

    /**
     * 足迹
     */
    public List<TravelPassengerInfo> getPassengerList() {
        return passengerList;
    }

    /**
     * 足迹
     */
    public void setPassengerList(final List<TravelPassengerInfo> passengerList) {
        this.passengerList = passengerList;
    }
    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }



    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTravelPassengerResponse other = (OnlineReportTravelPassengerResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc) &&
            Objects.equal(this.passengerList, other.passengerList) &&
            Objects.equal(this.extData, other.extData) ;
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.passengerList == null ? 0 : this.passengerList.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());
        

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            .add("passengerList", passengerList)
            .add("extData", extData)
            
            .toString();
    }
}
