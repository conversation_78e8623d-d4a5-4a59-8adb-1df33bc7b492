package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;



import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/14 20:41
 * @description：
 * @modified By：qiuyang
 * @version: $
 */
@Entity
//@Database(name = "CorpReportDB")
@Table(name = "report_user_setsave")
public class CorpReportEmailSavePO {

    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 用户卡号
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;



    /**
     * 疑似订单显示
     */
    @Column(name = "showRiskOrder")
    @Type(value = Types.VARCHAR)
    private String showRiskOrder;


    /**
     * email
     */
    @Column(name = "email")
    @Type(value = Types.VARCHAR)
    private String email;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 最后修改时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * reportType 大客订阅的类型
     */
    @Column(name = "reportType")
    @Type(value = Types.VARCHAR)
    private String reportType;

    /**
     * 邮件语种
     */
    @Column(name = "local")
    @Type(value = Types.VARCHAR)
    private String local;

    public String getShowRiskOrder() {
        return showRiskOrder;
    }

    public void setShowRiskOrder(String showRiskOrder) {
        this.showRiskOrder = showRiskOrder;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    @Override
    public String toString() {
        return "CorpReportEmailSavePO{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", showRiskOrder=" + showRiskOrder +
                ", email='" + email + '\'' +
                ", datachangeCreatetime=" + datachangeCreatetime + '\'' +
                ", datachangeLasttime=" + datachangeLasttime + '\'' +
                ", reportType=" + reportType + '\'' +
                ", local=" + local +
                '}';
    }
}
