package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import lombok.Data;

import java.math.BigDecimal;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
public class HtlDeptDTO {

    private String aggId;

    private String aggType;

    private BigDecimal totalAmount;

    private Integer totalQuantity;

    private BigDecimal totalRoomPrice;

    private BigDecimal totalAmountTa;

    private Integer totalQuantityTa;

    private BigDecimal totalRoomPriceTa;

    private BigDecimal totalAmountNta;

    private Integer totalQuantityNta;

    private BigDecimal totalRoomPriceNta;

    private BigDecimal totalAmountDom;

    private Integer totalQuantityDom;

    private BigDecimal totalRoomPriceDom;

    private BigDecimal totalAmountInter;

    private Integer totalQuantityInter;

    private BigDecimal totalRoomPriceInter;

    private BigDecimal totalOverAmount;

    private Integer totalRcTimes;

    private Integer totalOrderCount;

    private BigDecimal totalSaveAmount3c;

    private BigDecimal totalCorpRealPay3c;

    private BigDecimal totalSaveAmountPremium;

    private BigDecimal totalCorpRealPayPremium;

    private BigDecimal totalSaveAmountPromotion;

    private BigDecimal totalCorpRealPayPromotion;

    private BigDecimal totalControlSave;

    private BigDecimal totalControlCorpRealPay;

    private BigDecimal avgPrice;

    private BigDecimal avgPriceTa;

    private BigDecimal avgPriceNta;

    private BigDecimal avgPriceDom;

    private BigDecimal avgPriceInter;

    private BigDecimal rcPercent;

    private BigDecimal saveAmount3cRate;
    private BigDecimal saveAmount2cRate;
    private BigDecimal controlSaveRate;
    private BigDecimal saveAmountPromotionRate;

    private BigDecimal totalRefundloss;

    private String hotCity;

    private Integer hotStar;

    private BigDecimal totalDeadPriceOnenightQantiy;

    private Integer totalPersonsQuantity;

    private Integer totalCancelQuaity;

    private Integer totalAllQuaity;

    private Integer totalAllOrderCount;

}
