package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 概览
 * ****************************************新碳排放s*********************************************
 * ****************************************预算分析e*********************************************
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "totalCarbons",
    "avgMonthCarbons",
    "totalYoy",
    "totalMom",
    "fltCarbons",
    "fltYoy",
    "fltMom",
    "fltPercentage",
    "htlCarbons",
    "htlYoy",
    "htlMom",
    "htlPercentage",
    "trainCarbons",
    "trainYoy",
    "trainMom",
    "trainPercentage",
    "carCarbons",
    "carYoy",
    "carMom",
    "carPercentage"
})
public class CarbonsViewInfoV2 implements Serializable {
    private static final long serialVersionUID = 1L;





    public CarbonsViewInfoV2(
        Double totalCarbons,
        Double avgMonthCarbons,
        Double totalYoy,
        Double totalMom,
        Double fltCarbons,
        Double fltYoy,
        Double fltMom,
        Double fltPercentage,
        Double htlCarbons,
        Double htlYoy,
        Double htlMom,
        Double htlPercentage,
        Double trainCarbons,
        Double trainYoy,
        Double trainMom,
        Double trainPercentage,
        Double carCarbons,
        Double carYoy,
        Double carMom,
        Double carPercentage) {
        this.totalCarbons = totalCarbons;
        this.avgMonthCarbons = avgMonthCarbons;
        this.totalYoy = totalYoy;
        this.totalMom = totalMom;
        this.fltCarbons = fltCarbons;
        this.fltYoy = fltYoy;
        this.fltMom = fltMom;
        this.fltPercentage = fltPercentage;
        this.htlCarbons = htlCarbons;
        this.htlYoy = htlYoy;
        this.htlMom = htlMom;
        this.htlPercentage = htlPercentage;
        this.trainCarbons = trainCarbons;
        this.trainYoy = trainYoy;
        this.trainMom = trainMom;
        this.trainPercentage = trainPercentage;
        this.carCarbons = carCarbons;
        this.carYoy = carYoy;
        this.carMom = carMom;
        this.carPercentage = carPercentage;
    }

    public CarbonsViewInfoV2() {
    }

    @JsonProperty("totalCarbons")
    public Double totalCarbons;

    @JsonProperty("avgMonthCarbons")
    public Double avgMonthCarbons;

    @JsonProperty("totalYoy")
    public Double totalYoy;

    @JsonProperty("totalMom")
    public Double totalMom;

    @JsonProperty("fltCarbons")
    public Double fltCarbons;

    @JsonProperty("fltYoy")
    public Double fltYoy;

    @JsonProperty("fltMom")
    public Double fltMom;

    @JsonProperty("fltPercentage")
    public Double fltPercentage;

    @JsonProperty("htlCarbons")
    public Double htlCarbons;

    @JsonProperty("htlYoy")
    public Double htlYoy;

    @JsonProperty("htlMom")
    public Double htlMom;

    @JsonProperty("htlPercentage")
    public Double htlPercentage;

    @JsonProperty("trainCarbons")
    public Double trainCarbons;

    @JsonProperty("trainYoy")
    public Double trainYoy;

    @JsonProperty("trainMom")
    public Double trainMom;

    @JsonProperty("trainPercentage")
    public Double trainPercentage;

    @JsonProperty("carCarbons")
    public Double carCarbons;

    @JsonProperty("carYoy")
    public Double carYoy;

    @JsonProperty("carMom")
    public Double carMom;

    @JsonProperty("carPercentage")
    public Double carPercentage;

    public Double getTotalCarbons() {
        return totalCarbons;
    }

    public void setTotalCarbons(final Double totalCarbons) {
        this.totalCarbons = totalCarbons;
    }
    public Double getAvgMonthCarbons() {
        return avgMonthCarbons;
    }

    public void setAvgMonthCarbons(final Double avgMonthCarbons) {
        this.avgMonthCarbons = avgMonthCarbons;
    }
    public Double getTotalYoy() {
        return totalYoy;
    }

    public void setTotalYoy(final Double totalYoy) {
        this.totalYoy = totalYoy;
    }
    public Double getTotalMom() {
        return totalMom;
    }

    public void setTotalMom(final Double totalMom) {
        this.totalMom = totalMom;
    }
    public Double getFltCarbons() {
        return fltCarbons;
    }

    public void setFltCarbons(final Double fltCarbons) {
        this.fltCarbons = fltCarbons;
    }
    public Double getFltYoy() {
        return fltYoy;
    }

    public void setFltYoy(final Double fltYoy) {
        this.fltYoy = fltYoy;
    }
    public Double getFltMom() {
        return fltMom;
    }

    public void setFltMom(final Double fltMom) {
        this.fltMom = fltMom;
    }
    public Double getFltPercentage() {
        return fltPercentage;
    }

    public void setFltPercentage(final Double fltPercentage) {
        this.fltPercentage = fltPercentage;
    }
    public Double getHtlCarbons() {
        return htlCarbons;
    }

    public void setHtlCarbons(final Double htlCarbons) {
        this.htlCarbons = htlCarbons;
    }
    public Double getHtlYoy() {
        return htlYoy;
    }

    public void setHtlYoy(final Double htlYoy) {
        this.htlYoy = htlYoy;
    }
    public Double getHtlMom() {
        return htlMom;
    }

    public void setHtlMom(final Double htlMom) {
        this.htlMom = htlMom;
    }
    public Double getHtlPercentage() {
        return htlPercentage;
    }

    public void setHtlPercentage(final Double htlPercentage) {
        this.htlPercentage = htlPercentage;
    }
    public Double getTrainCarbons() {
        return trainCarbons;
    }

    public void setTrainCarbons(final Double trainCarbons) {
        this.trainCarbons = trainCarbons;
    }
    public Double getTrainYoy() {
        return trainYoy;
    }

    public void setTrainYoy(final Double trainYoy) {
        this.trainYoy = trainYoy;
    }
    public Double getTrainMom() {
        return trainMom;
    }

    public void setTrainMom(final Double trainMom) {
        this.trainMom = trainMom;
    }
    public Double getTrainPercentage() {
        return trainPercentage;
    }

    public void setTrainPercentage(final Double trainPercentage) {
        this.trainPercentage = trainPercentage;
    }
    public Double getCarCarbons() {
        return carCarbons;
    }

    public void setCarCarbons(final Double carCarbons) {
        this.carCarbons = carCarbons;
    }
    public Double getCarYoy() {
        return carYoy;
    }

    public void setCarYoy(final Double carYoy) {
        this.carYoy = carYoy;
    }
    public Double getCarMom() {
        return carMom;
    }

    public void setCarMom(final Double carMom) {
        this.carMom = carMom;
    }
    public Double getCarPercentage() {
        return carPercentage;
    }

    public void setCarPercentage(final Double carPercentage) {
        this.carPercentage = carPercentage;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final CarbonsViewInfoV2 other = (CarbonsViewInfoV2)obj;
        return
            Objects.equal(this.totalCarbons, other.totalCarbons) &&
            Objects.equal(this.avgMonthCarbons, other.avgMonthCarbons) &&
            Objects.equal(this.totalYoy, other.totalYoy) &&
            Objects.equal(this.totalMom, other.totalMom) &&
            Objects.equal(this.fltCarbons, other.fltCarbons) &&
            Objects.equal(this.fltYoy, other.fltYoy) &&
            Objects.equal(this.fltMom, other.fltMom) &&
            Objects.equal(this.fltPercentage, other.fltPercentage) &&
            Objects.equal(this.htlCarbons, other.htlCarbons) &&
            Objects.equal(this.htlYoy, other.htlYoy) &&
            Objects.equal(this.htlMom, other.htlMom) &&
            Objects.equal(this.htlPercentage, other.htlPercentage) &&
            Objects.equal(this.trainCarbons, other.trainCarbons) &&
            Objects.equal(this.trainYoy, other.trainYoy) &&
            Objects.equal(this.trainMom, other.trainMom) &&
            Objects.equal(this.trainPercentage, other.trainPercentage) &&
            Objects.equal(this.carCarbons, other.carCarbons) &&
            Objects.equal(this.carYoy, other.carYoy) &&
            Objects.equal(this.carMom, other.carMom) &&
            Objects.equal(this.carPercentage, other.carPercentage);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.totalCarbons == null ? 0 : this.totalCarbons.hashCode());
        result = 31 * result + (this.avgMonthCarbons == null ? 0 : this.avgMonthCarbons.hashCode());
        result = 31 * result + (this.totalYoy == null ? 0 : this.totalYoy.hashCode());
        result = 31 * result + (this.totalMom == null ? 0 : this.totalMom.hashCode());
        result = 31 * result + (this.fltCarbons == null ? 0 : this.fltCarbons.hashCode());
        result = 31 * result + (this.fltYoy == null ? 0 : this.fltYoy.hashCode());
        result = 31 * result + (this.fltMom == null ? 0 : this.fltMom.hashCode());
        result = 31 * result + (this.fltPercentage == null ? 0 : this.fltPercentage.hashCode());
        result = 31 * result + (this.htlCarbons == null ? 0 : this.htlCarbons.hashCode());
        result = 31 * result + (this.htlYoy == null ? 0 : this.htlYoy.hashCode());
        result = 31 * result + (this.htlMom == null ? 0 : this.htlMom.hashCode());
        result = 31 * result + (this.htlPercentage == null ? 0 : this.htlPercentage.hashCode());
        result = 31 * result + (this.trainCarbons == null ? 0 : this.trainCarbons.hashCode());
        result = 31 * result + (this.trainYoy == null ? 0 : this.trainYoy.hashCode());
        result = 31 * result + (this.trainMom == null ? 0 : this.trainMom.hashCode());
        result = 31 * result + (this.trainPercentage == null ? 0 : this.trainPercentage.hashCode());
        result = 31 * result + (this.carCarbons == null ? 0 : this.carCarbons.hashCode());
        result = 31 * result + (this.carYoy == null ? 0 : this.carYoy.hashCode());
        result = 31 * result + (this.carMom == null ? 0 : this.carMom.hashCode());
        result = 31 * result + (this.carPercentage == null ? 0 : this.carPercentage.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("totalCarbons", totalCarbons)
            .add("avgMonthCarbons", avgMonthCarbons)
            .add("totalYoy", totalYoy)
            .add("totalMom", totalMom)
            .add("fltCarbons", fltCarbons)
            .add("fltYoy", fltYoy)
            .add("fltMom", fltMom)
            .add("fltPercentage", fltPercentage)
            .add("htlCarbons", htlCarbons)
            .add("htlYoy", htlYoy)
            .add("htlMom", htlMom)
            .add("htlPercentage", htlPercentage)
            .add("trainCarbons", trainCarbons)
            .add("trainYoy", trainYoy)
            .add("trainMom", trainMom)
            .add("trainPercentage", trainPercentage)
            .add("carCarbons", carCarbons)
            .add("carYoy", carYoy)
            .add("carMom", carMom)
            .add("carPercentage", carPercentage)
            .toString();
    }
}
