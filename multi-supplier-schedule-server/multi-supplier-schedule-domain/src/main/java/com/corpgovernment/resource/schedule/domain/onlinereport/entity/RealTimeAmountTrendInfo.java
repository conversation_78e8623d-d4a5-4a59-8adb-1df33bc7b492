package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 实时金额数据分析
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "point",
    "currentAmount",
    "preAmount"
})
public class RealTimeAmountTrendInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public RealTimeAmountTrendInfo(
        String point,
        BigDecimal currentAmount,
        BigDecimal preAmount) {
        this.point = point;
        this.currentAmount = currentAmount;
        this.preAmount = preAmount;
    }

    public RealTimeAmountTrendInfo() {
    }

    @JsonProperty("point")
    public String point;

    /**
     * 当前金额
     */
    @JsonProperty("currentAmount")
    public BigDecimal currentAmount;

    /**
     * 昨日/上周同期金额
     */
    @JsonProperty("preAmount")
    public BigDecimal preAmount;

    public String getPoint() {
        return point;
    }

    public void setPoint(final String point) {
        this.point = point;
    }

    /**
     * 当前金额
     */
    public BigDecimal getCurrentAmount() {
        return currentAmount;
    }

    /**
     * 当前金额
     */
    public void setCurrentAmount(final BigDecimal currentAmount) {
        this.currentAmount = currentAmount;
    }

    /**
     * 昨日/上周同期金额
     */
    public BigDecimal getPreAmount() {
        return preAmount;
    }

    /**
     * 昨日/上周同期金额
     */
    public void setPreAmount(final BigDecimal preAmount) {
        this.preAmount = preAmount;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final RealTimeAmountTrendInfo other = (RealTimeAmountTrendInfo)obj;
        return
            Objects.equal(this.point, other.point) &&
            Objects.equal(this.currentAmount, other.currentAmount) &&
            Objects.equal(this.preAmount, other.preAmount);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.point == null ? 0 : this.point.hashCode());
        result = 31 * result + (this.currentAmount == null ? 0 : this.currentAmount.hashCode());
        result = 31 * result + (this.preAmount == null ? 0 : this.preAmount.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("point", point)
            .add("currentAmount", currentAmount)
            .add("preAmount", preAmount)
            .toString();
    }
}
