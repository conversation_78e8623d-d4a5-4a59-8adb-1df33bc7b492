package com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz;

import com.corpgovernment.resource.schedule.domain.onlinereport.constant.ExportConstant;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailAmountDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportDataDetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReprotBodyData;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.CommonUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: com.ctrip.corp.onlinereportweb.domainreport.adaptor.biz
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-15 15:30
 **/
@Service
public class FlightReportDetailService {


    /**
     * 机票-整体
     *
     * @param flightDetailData
     * @return
     */
    public List<ChartExcelEntity> flightAmountAllBodyData(DetailData flightDetailData) {
        return flightAmountDetail(flightDetailData, ExportConstant.ZERO);
    }

    /**
     * 机票-协议/非协议
     *
     * @param flightDetailData
     * @return
     */
    public List<ChartExcelEntity> flightAgreementNonAgreementBodyData(DetailAmountDto flightDetailData) {
        List<ChartExcelEntity> agreementNonAgreementList = new ArrayList<>();
        List<ChartExcelEntity> agreementExcelEntity = flightAmountDetail(flightDetailData.getAgreementtDetail(), ExportConstant.ZERO);
        List<ChartExcelEntity> nonAgreementExcelEntity = flightAmountDetail(flightDetailData.getNotAgreementDetail(), ExportConstant.ONE);
        agreementNonAgreementList.addAll(agreementExcelEntity);
        agreementNonAgreementList.addAll(nonAgreementExcelEntity);
        return agreementNonAgreementList;
    }

    /**
     * 机票-协议/非协议
     *
     * @param flightData
     * @return
     */
    public List<ChartExcelEntity> flightDomesticInternationalBodyData(DetailAmountDto flightData) {
        List<ChartExcelEntity> domesticInternationalList = new ArrayList<>();
        List<ChartExcelEntity> agreementExcelEntity = flightAmountDetail(flightData.getDomesticDetail(), ExportConstant.ZERO);
        List<ChartExcelEntity> nonAgreementExcelEntity = flightAmountDetail(flightData.getInternationalDetail(), ExportConstant.ONE);
        domesticInternationalList.addAll(agreementExcelEntity);
        domesticInternationalList.addAll(nonAgreementExcelEntity);
        return domesticInternationalList;
    }

    private List<ChartExcelEntity> flightAmountDetail(DetailData flightDetailData, int sheetNum) {
        if (Objects.isNull(flightDetailData)) {
            return Lists.newArrayList();
        }
        ChartExcelEntity amountAllEntity = new ChartExcelEntity();
        // title
        amountAllEntity.setSheetTitle(flightDetailData.getHeaderData().get(ExportConstant.ZERO).getHeaderValue());
        // header
        amountAllEntity.setHeaders(flightDetailData.getHeaderData().stream().map(HeaderKeyValMap::getHeaderValue).collect(Collectors.toList()));
        amountAllEntity.setSheetNum(sheetNum);
        List<List<String>> flightAmountAllList = Lists.newArrayList();
        List<String> flightExcelBodyData = null;
        for (ReprotBodyData bodyData : flightDetailData.getBodyList()) {
            flightExcelBodyData = Lists.newArrayList();
            flightExcelBodyData.add(bodyData.getDimension());
            ReportDataDetailData flightReportData = bodyData.getDetailData();
            // 成交净价
            flightExcelBodyData.add(flightReportData.getNetfare());
            // 机建税
            flightExcelBodyData.add(flightReportData.getTax());
            // 燃油费
            flightExcelBodyData.add(flightReportData.getOilFee());
            // 基础管理服务费
            flightExcelBodyData.add(flightReportData.getServiceFee());
            // 保险费
            flightExcelBodyData.add(flightReportData.getInsuranceFee());
            // 配送费
            flightExcelBodyData.add(flightReportData.getSendTicketFee());
            // 增值服务包费
            flightExcelBodyData.add(flightReportData.getServicepackageFee());
            // 绑定酒店优惠券
            flightExcelBodyData.add(flightReportData.getBindAmount());
            // 改签费
            flightExcelBodyData.add(flightReportData.getChangeFee());
            // 改签差价
            flightExcelBodyData.add(flightReportData.getRebookPriceDifferential());
            // 改签燃油差
            flightExcelBodyData.add(flightReportData.getOilfeedifferential());
            // 税差
            flightExcelBodyData.add(flightReportData.getTaxDifferential());
            // 改签商旅管理服务费
            flightExcelBodyData.add(flightReportData.getRebookServiceFee());
            // 退票费
            flightExcelBodyData.add(flightReportData.getRefundFee());
            // 退票商旅管理服务费
            flightExcelBodyData.add(flightReportData.getRefundServiceFee());
            // 退票行程单商旅管理服务费
            flightExcelBodyData.add(flightReportData.getRefundItineraryFee());
            // 后收商旅管理服务费
            flightExcelBodyData.add(flightReportData.getTicketBehindServicefee());
            // 后收改签商旅管理服务费
            flightExcelBodyData.add(flightReportData.getRebookBehindServiceFee());
            // 后收退票商旅管理服务费
            flightExcelBodyData.add(flightReportData.getRefundBehindServiceFee());
            // 总计
            flightExcelBodyData.add(flightReportData.getTotalV());
            flightAmountAllList.add(flightExcelBodyData);
        }
        amountAllEntity.setData(flightAmountAllList);
        return Lists.newArrayList(amountAllEntity);
    }


    /**
     * 机票-金额/票张 明细
     *
     * @param flightDetailData
     * @return
     */
    public List<ChartExcelEntity> flightAmountTicketBodyData(String sheetTitle, DetailData flightDetailData) {
        if (Objects.isNull(flightDetailData)) {
            return Lists.newArrayList();
        }
        ChartExcelEntity amountAllEntity = new ChartExcelEntity();
        // title
        amountAllEntity.setSheetTitle(sheetTitle);
        // header
        amountAllEntity.setHeaders(flightDetailData.getHeaderData().stream().map(HeaderKeyValMap::getHeaderValue).collect(Collectors.toList()));
        amountAllEntity.setSheetNum(ExportConstant.ZERO);
        List<List<String>> flightAmountAllList = Lists.newArrayList();
        List<String> flightExcelBodyData = null;

        for (ReprotBodyData bodyData : flightDetailData.getBodyList()) {
            flightExcelBodyData = Lists.newArrayList();
            flightExcelBodyData.add(bodyData.getDimension());
            ReportDataDetailData flightReportData = bodyData.getDetailData();

            flightExcelBodyData.add(flightReportData.getAgreementV());
            flightExcelBodyData.add(CommonUtils.cancelSymbol(flightReportData.getAgreementYoy()));
            flightExcelBodyData.add(CommonUtils.cancelSymbol(flightReportData.getAgreementMom()));

            flightExcelBodyData.add(flightReportData.getUnAgreementV());
            flightExcelBodyData.add(CommonUtils.cancelSymbol(flightReportData.getUnAgreementYoy()));
            flightExcelBodyData.add(CommonUtils.cancelSymbol(flightReportData.getUnAgreementMom()));

            flightExcelBodyData.add(flightReportData.getDomesticV());
            flightExcelBodyData.add(CommonUtils.cancelSymbol(flightReportData.getDomesticYoy()));
            flightExcelBodyData.add(CommonUtils.cancelSymbol(flightReportData.getDomesticMom()));

            flightExcelBodyData.add(flightReportData.getInternationalV());
            flightExcelBodyData.add(CommonUtils.cancelSymbol(flightReportData.getInternationalYoy()));
            flightExcelBodyData.add(CommonUtils.cancelSymbol(flightReportData.getInternationalMom()));

            flightExcelBodyData.add(flightReportData.getTotalV());
            flightExcelBodyData.add(CommonUtils.cancelSymbol(flightReportData.getTotalYoy()));
            flightExcelBodyData.add(CommonUtils.cancelSymbol(flightReportData.getTotalMom()));

            flightAmountAllList.add(flightExcelBodyData);
        }

        amountAllEntity.setData(flightAmountAllList);
        return Lists.newArrayList(amountAllEntity);
    }
}
