package com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity;

import com.corpgovernment.common.enums.CorpPayTypeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.OrderSourceEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.PayTypeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.ProductTypeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelModeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 订单信息
 * @create 2024-12-23 14:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderInfo {
    
    // 订单号
    private String orderId;
    
    // 下单时间
    private String orderTime;
    
    // 订单状态
    private String orderStatus;
    
    // 订单来源
    private OrderSourceEnum orderSourceEnum;
    
    // 因公因私
    private TravelModeEnum travelModeEnum;
    
    // 预定token
    private String token;
    
    // 入住日期
    private String checkInDate;
    
    // 离店日期
    private String checkOutDate;
    
    // 是否超标
    private Boolean overLimit;
    
    // 支付方式
    private PayTypeEnum payTypeEnum;
    
    // 公帐支付金额
    private BigDecimal publicPayPrice;
    
    // 个人支付金额
    private BigDecimal personPayPrice;
    
    // 订单总价
    private BigDecimal totalOrderPrice;
    
    // 间夜均格
    private BigDecimal roomNightAvgPrice;
    
    // 每日房价
    private List<DailyRoomPrice> dailyRoomPriceList;
    
    // 产线
    private ProductTypeEnum productTypeEnum;
    
}
