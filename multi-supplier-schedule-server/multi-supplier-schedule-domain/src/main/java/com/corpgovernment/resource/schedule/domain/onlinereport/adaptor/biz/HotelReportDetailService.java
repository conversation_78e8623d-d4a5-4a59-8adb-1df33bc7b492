package com.corpgovernment.resource.schedule.domain.onlinereport.adaptor.biz;


import com.corpgovernment.resource.schedule.domain.onlinereport.constant.ExportConstant;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailAmountDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.DetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.HeaderKeyValMap;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReportDataDetailData;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ReprotBodyData;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: com.ctrip.corp.onlinereportweb.domainreport.adaptor.biz
 * @description:
 * @author: <PERSON>
 * @create: 2021-11-15 15:31
 **/
@Service
public class HotelReportDetailService {

    /**
     * 酒店-整体
     *
     * @param hotelDetailData
     * @return
     */
    public List<ChartExcelEntity> hotelAmountAllBodyData(DetailData hotelDetailData) {
        return hotelAmountDetail(hotelDetailData, ExportConstant.ZERO);
    }

    /**
     * 协议/非协议
     *
     * @param hotelDetailData
     * @return
     */
    public List<ChartExcelEntity> hotelAgreementNonAgreementBodyData(DetailAmountDto hotelDetailData) {
        List<ChartExcelEntity> agreementNonAgreementList = Lists.newArrayList();
        List<ChartExcelEntity> agreementExcelEntity = hotelAmountDetail(hotelDetailData.getAgreementtDetail(), ExportConstant.ZERO);
        List<ChartExcelEntity> nonAgreementExcelEntity = hotelAmountDetail(hotelDetailData.getNotAgreementDetail(), ExportConstant.ONE);
        agreementNonAgreementList.addAll(agreementExcelEntity);
        agreementNonAgreementList.addAll(nonAgreementExcelEntity);
        return agreementNonAgreementList;
    }

    /**
     * 国内/国际
     *
     * @param hotelDetailData
     * @return
     */
    public List<ChartExcelEntity> hotelDomesticInternationalBodyData(DetailAmountDto hotelDetailData) {
        List<ChartExcelEntity> domesticInternationalList = Lists.newArrayList();
        List<ChartExcelEntity> agreementExcelEntity = hotelAmountDetail(hotelDetailData.getDomesticDetail(), ExportConstant.ZERO);
        List<ChartExcelEntity> nonAgreementExcelEntity = hotelAmountDetail(hotelDetailData.getInternationalDetail(), ExportConstant.ONE);
        domesticInternationalList.addAll(agreementExcelEntity);
        domesticInternationalList.addAll(nonAgreementExcelEntity);
        return domesticInternationalList;
    }


    private List<ChartExcelEntity> hotelAmountDetail(DetailData hotelDetailData, int sheetNum) {
        if (Objects.isNull(hotelDetailData)) {
            return Lists.newArrayList();
        }
        ChartExcelEntity amountAllEntity = new ChartExcelEntity();
        // title
        amountAllEntity.setSheetTitle(hotelDetailData.getHeaderData().get(ExportConstant.ZERO).getHeaderValue());
        // header
        amountAllEntity.setHeaders(hotelDetailData.getHeaderData().stream().map(HeaderKeyValMap::getHeaderValue).collect(Collectors.toList()));
        amountAllEntity.setSheetNum(sheetNum);
        List<List<String>> hotelAmountAllList = Lists.newArrayList();
        List<String> hotelExcelBodyData = null;
        for (ReprotBodyData bodyData : hotelDetailData.getBodyList()) {
            hotelExcelBodyData = Lists.newArrayList();
            hotelExcelBodyData.add(bodyData.getDimension());
            ReportDataDetailData hotelBodyData = bodyData.getDetailData();
            hotelExcelBodyData.add(hotelBodyData.getRoomPrice());
            // 取消酒店优惠券
            // hotelExcelBodyData.add(hotelBodyData.getCouponAmount());
            hotelExcelBodyData.add(hotelBodyData.getServiceFee());
            hotelExcelBodyData.add(hotelBodyData.getHotelPostServiceFee());
            hotelExcelBodyData.add(hotelBodyData.getTotalV());

            hotelAmountAllList.add(hotelExcelBodyData);
        }
        amountAllEntity.setData(hotelAmountAllList);
        return Lists.newArrayList(amountAllEntity);
    }
}
