package com.corpgovernment.resource.schedule.domain.cleanup.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FieldInfo {

    @NotBlank
    public String fieldName;

    /**
     * 需要加解密 true-需要 false-不需要
     */
    @NotNull
    public Boolean needCrypt;


}
