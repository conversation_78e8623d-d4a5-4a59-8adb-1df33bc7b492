package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 风险订单-明细查询response
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "fltOrderList"
})
public class OnlineReportRcOrderDetailResponse implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportRcOrderDetailResponse(
        Integer responseCode,
        String responseDesc,
        List<OnlineReportFlightOrderInfo> fltOrderList) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.fltOrderList = fltOrderList;

    }

    public OnlineReportRcOrderDetailResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    /**
     * 机票退款至个人
     */
    @JsonProperty("fltOrderList")
    public List<OnlineReportFlightOrderInfo> fltOrderList;



    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }

    /**
     * 机票退款至个人
     */
    public List<OnlineReportFlightOrderInfo> getFltOrderList() {
        return fltOrderList;
    }

    /**
     * 机票退款至个人
     */
    public void setFltOrderList(final List<OnlineReportFlightOrderInfo> fltOrderList) {
        this.fltOrderList = fltOrderList;
    }



    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportRcOrderDetailResponse other = (OnlineReportRcOrderDetailResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc) &&
            Objects.equal(this.fltOrderList, other.fltOrderList) ;
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.fltOrderList == null ? 0 : this.fltOrderList.hashCode());
        

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            .add("fltOrderList", fltOrderList)
            
            .toString();
    }
}
