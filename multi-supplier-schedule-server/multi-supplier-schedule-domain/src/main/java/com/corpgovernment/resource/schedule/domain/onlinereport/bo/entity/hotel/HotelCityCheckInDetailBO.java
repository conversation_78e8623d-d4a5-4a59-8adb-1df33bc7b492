package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.hotel;

/**
 * @Description: 酒店城市入住情况
 * <AUTHOR>
 * @Date 2019/4/3
 */
public class HotelCityCheckInDetailBO {
    private String cityName;
    private String hotelName;
    private Integer star;
    private Double amount;
    private Integer quantity;
    private Double avgPrice;
    private Double quantityPercent;
    private Integer hotelID;
    private Integer cityID;
    private String isOversea;

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public Integer getStar() {
        return star;
    }

    public void setStar(Integer star) {
        this.star = star;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Double getAvgPrice() {
        return avgPrice;
    }

    public void setAvgPrice(Double avgPrice) {
        this.avgPrice = avgPrice;
    }

    public Double getQuantityPercent() {
        return quantityPercent;
    }

    public void setQuantityPercent(Double quantityPercent) {
        this.quantityPercent = quantityPercent;
    }

    public Integer getHotelID() {
        return hotelID;
    }

    public void setHotelID(Integer hotelID) {
        this.hotelID = hotelID;
    }

    public Integer getCityID() {
        return cityID;
    }

    public void setCityID(Integer cityID) {
        this.cityID = cityID;
    }

    public String getIsOversea() {
        return isOversea;
    }

    public void setIsOversea(String isOversea) {
        this.isOversea = isOversea;
    }

    public  HotelCityCheckInDetailBO(){
        amount=0.0;
        quantity=0;
        avgPrice=0.0;
        cityName= "";
        hotelName="";
        quantityPercent=0.0;
        star=0;
        hotelID=0;
        cityID=0;
        isOversea="";
    }
}
