package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用车行为分析
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dim",
    "totalOrderCount",
    "orderCountPercent",
    "totalAmount",
    "amountPercent",
    "avgPrice",
    "totalSettlementAccntamt",
    "totalSettlementPersonamt",
    "personamtPercent"
})
public class CarBehaviorInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public CarBehaviorInfo(
        String dim,
        Integer totalOrderCount,
        Double orderCountPercent,
        BigDecimal totalAmount,
        Double amountPercent,
        BigDecimal avgPrice,
        BigDecimal totalSettlementAccntamt,
        BigDecimal totalSettlementPersonamt,
        Double personamtPercent) {
        this.dim = dim;
        this.totalOrderCount = totalOrderCount;
        this.orderCountPercent = orderCountPercent;
        this.totalAmount = totalAmount;
        this.amountPercent = amountPercent;
        this.avgPrice = avgPrice;
        this.totalSettlementAccntamt = totalSettlementAccntamt;
        this.totalSettlementPersonamt = totalSettlementPersonamt;
        this.personamtPercent = personamtPercent;
    }

    public CarBehaviorInfo() {
    }

    /**
     * 维度
     */
    @JsonProperty("dim")
    public String dim;

    /**
     * 订单数
     */
    @JsonProperty("totalOrderCount")
    public Integer totalOrderCount;

    /**
     * 订单数占比
     */
    @JsonProperty("orderCountPercent")
    public Double orderCountPercent;

    /**
     * 消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;

    /**
     * 消费金额占比
     */
    @JsonProperty("amountPercent")
    public Double amountPercent;

    /**
     * 订单均价
     */
    @JsonProperty("avgPrice")
    public BigDecimal avgPrice;

    /**
     * 公司支付
     */
    @JsonProperty("totalSettlementAccntamt")
    public BigDecimal totalSettlementAccntamt;

    /**
     * 个人支付
     */
    @JsonProperty("totalSettlementPersonamt")
    public BigDecimal totalSettlementPersonamt;

    /**
     * 个人支付占比
     */
    @JsonProperty("personamtPercent")
    public Double personamtPercent;

    /**
     * 维度
     */
    public String getDim() {
        return dim;
    }

    /**
     * 维度
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 订单数
     */
    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }

    /**
     * 订单数
     */
    public void setTotalOrderCount(final Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }

    /**
     * 订单数占比
     */
    public Double getOrderCountPercent() {
        return orderCountPercent;
    }

    /**
     * 订单数占比
     */
    public void setOrderCountPercent(final Double orderCountPercent) {
        this.orderCountPercent = orderCountPercent;
    }

    /**
     * 消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 消费金额占比
     */
    public Double getAmountPercent() {
        return amountPercent;
    }

    /**
     * 消费金额占比
     */
    public void setAmountPercent(final Double amountPercent) {
        this.amountPercent = amountPercent;
    }

    /**
     * 订单均价
     */
    public BigDecimal getAvgPrice() {
        return avgPrice;
    }

    /**
     * 订单均价
     */
    public void setAvgPrice(final BigDecimal avgPrice) {
        this.avgPrice = avgPrice;
    }

    /**
     * 公司支付
     */
    public BigDecimal getTotalSettlementAccntamt() {
        return totalSettlementAccntamt;
    }

    /**
     * 公司支付
     */
    public void setTotalSettlementAccntamt(final BigDecimal totalSettlementAccntamt) {
        this.totalSettlementAccntamt = totalSettlementAccntamt;
    }

    /**
     * 个人支付
     */
    public BigDecimal getTotalSettlementPersonamt() {
        return totalSettlementPersonamt;
    }

    /**
     * 个人支付
     */
    public void setTotalSettlementPersonamt(final BigDecimal totalSettlementPersonamt) {
        this.totalSettlementPersonamt = totalSettlementPersonamt;
    }

    /**
     * 个人支付占比
     */
    public Double getPersonamtPercent() {
        return personamtPercent;
    }

    /**
     * 个人支付占比
     */
    public void setPersonamtPercent(final Double personamtPercent) {
        this.personamtPercent = personamtPercent;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final CarBehaviorInfo other = (CarBehaviorInfo)obj;
        return
            Objects.equal(this.dim, other.dim) &&
            Objects.equal(this.totalOrderCount, other.totalOrderCount) &&
            Objects.equal(this.orderCountPercent, other.orderCountPercent) &&
            Objects.equal(this.totalAmount, other.totalAmount) &&
            Objects.equal(this.amountPercent, other.amountPercent) &&
            Objects.equal(this.avgPrice, other.avgPrice) &&
            Objects.equal(this.totalSettlementAccntamt, other.totalSettlementAccntamt) &&
            Objects.equal(this.totalSettlementPersonamt, other.totalSettlementPersonamt) &&
            Objects.equal(this.personamtPercent, other.personamtPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.totalOrderCount == null ? 0 : this.totalOrderCount.hashCode());
        result = 31 * result + (this.orderCountPercent == null ? 0 : this.orderCountPercent.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.amountPercent == null ? 0 : this.amountPercent.hashCode());
        result = 31 * result + (this.avgPrice == null ? 0 : this.avgPrice.hashCode());
        result = 31 * result + (this.totalSettlementAccntamt == null ? 0 : this.totalSettlementAccntamt.hashCode());
        result = 31 * result + (this.totalSettlementPersonamt == null ? 0 : this.totalSettlementPersonamt.hashCode());
        result = 31 * result + (this.personamtPercent == null ? 0 : this.personamtPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dim", dim)
            .add("totalOrderCount", totalOrderCount)
            .add("orderCountPercent", orderCountPercent)
            .add("totalAmount", totalAmount)
            .add("amountPercent", amountPercent)
            .add("avgPrice", avgPrice)
            .add("totalSettlementAccntamt", totalSettlementAccntamt)
            .add("totalSettlementPersonamt", totalSettlementPersonamt)
            .add("personamtPercent", personamtPercent)
            .toString();
    }
}
