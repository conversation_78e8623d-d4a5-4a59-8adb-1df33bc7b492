package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 评分指标
 * ****************************************差旅评分-行为分析s******************************************
 * ****************************************报告库订单明细e******************************************
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dim",
    "companyMetric",
    "corpMetric",
    "industryMetric"
})
public class MarkMetricInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public MarkMetricInfo(
        String dim,
        BigDecimal companyMetric,
        BigDecimal corpMetric,
        BigDecimal industryMetric) {
        this.dim = dim;
        this.companyMetric = companyMetric;
        this.corpMetric = corpMetric;
        this.industryMetric = industryMetric;
    }

    public MarkMetricInfo() {
    }

    /**
     * 维度
     */
    @JsonProperty("dim")
    public String dim;

    /**
     * 公司
     */
    @JsonProperty("companyMetric")
    public BigDecimal companyMetric;

    /**
     * 商旅
     */
    @JsonProperty("corpMetric")
    public BigDecimal corpMetric;

    /**
     * 行业
     */
    @JsonProperty("industryMetric")
    public BigDecimal industryMetric;

    /**
     * 维度
     */
    public String getDim() {
        return dim;
    }

    /**
     * 维度
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 公司
     */
    public BigDecimal getCompanyMetric() {
        return companyMetric;
    }

    /**
     * 公司
     */
    public void setCompanyMetric(final BigDecimal companyMetric) {
        this.companyMetric = companyMetric;
    }

    /**
     * 商旅
     */
    public BigDecimal getCorpMetric() {
        return corpMetric;
    }

    /**
     * 商旅
     */
    public void setCorpMetric(final BigDecimal corpMetric) {
        this.corpMetric = corpMetric;
    }

    /**
     * 行业
     */
    public BigDecimal getIndustryMetric() {
        return industryMetric;
    }

    /**
     * 行业
     */
    public void setIndustryMetric(final BigDecimal industryMetric) {
        this.industryMetric = industryMetric;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final MarkMetricInfo other = (MarkMetricInfo)obj;
        return
            Objects.equal(this.dim, other.dim) &&
            Objects.equal(this.companyMetric, other.companyMetric) &&
            Objects.equal(this.corpMetric, other.corpMetric) &&
            Objects.equal(this.industryMetric, other.industryMetric);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.companyMetric == null ? 0 : this.companyMetric.hashCode());
        result = 31 * result + (this.corpMetric == null ? 0 : this.corpMetric.hashCode());
        result = 31 * result + (this.industryMetric == null ? 0 : this.industryMetric.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dim", dim)
            .add("companyMetric", companyMetric)
            .add("corpMetric", corpMetric)
            .add("industryMetric", industryMetric)
            .toString();
    }
}
