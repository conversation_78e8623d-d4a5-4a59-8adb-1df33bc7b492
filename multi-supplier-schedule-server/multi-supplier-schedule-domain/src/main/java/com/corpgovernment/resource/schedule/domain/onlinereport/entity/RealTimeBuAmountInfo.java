package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 实时产线金额数据分析
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "flightAmount",
    "hotelAmount",
    "trainAmount",
    "carAmount"
})
public class RealTimeBuAmountInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public RealTimeBuAmountInfo(
        BigDecimal flightAmount,
        BigDecimal hotelAmount,
        BigDecimal trainAmount,
        BigDecimal carAmount) {
        this.flightAmount = flightAmount;
        this.hotelAmount = hotelAmount;
        this.trainAmount = trainAmount;
        this.carAmount = carAmount;
    }

    public RealTimeBuAmountInfo() {
    }

    /**
     * 机票当前金额
     */
    @JsonProperty("flightAmount")
    public BigDecimal flightAmount;

    /**
     * 酒店当前金额
     */
    @JsonProperty("hotelAmount")
    public BigDecimal hotelAmount;

    /**
     * 火车当前金额
     */
    @JsonProperty("trainAmount")
    public BigDecimal trainAmount;

    /**
     * 用车当前金额
     */
    @JsonProperty("carAmount")
    public BigDecimal carAmount;

    /**
     * 机票当前金额
     */
    public BigDecimal getFlightAmount() {
        return flightAmount;
    }

    /**
     * 机票当前金额
     */
    public void setFlightAmount(final BigDecimal flightAmount) {
        this.flightAmount = flightAmount;
    }

    /**
     * 酒店当前金额
     */
    public BigDecimal getHotelAmount() {
        return hotelAmount;
    }

    /**
     * 酒店当前金额
     */
    public void setHotelAmount(final BigDecimal hotelAmount) {
        this.hotelAmount = hotelAmount;
    }

    /**
     * 火车当前金额
     */
    public BigDecimal getTrainAmount() {
        return trainAmount;
    }

    /**
     * 火车当前金额
     */
    public void setTrainAmount(final BigDecimal trainAmount) {
        this.trainAmount = trainAmount;
    }

    /**
     * 用车当前金额
     */
    public BigDecimal getCarAmount() {
        return carAmount;
    }

    /**
     * 用车当前金额
     */
    public void setCarAmount(final BigDecimal carAmount) {
        this.carAmount = carAmount;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final RealTimeBuAmountInfo other = (RealTimeBuAmountInfo)obj;
        return
            Objects.equal(this.flightAmount, other.flightAmount) &&
            Objects.equal(this.hotelAmount, other.hotelAmount) &&
            Objects.equal(this.trainAmount, other.trainAmount) &&
            Objects.equal(this.carAmount, other.carAmount);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.flightAmount == null ? 0 : this.flightAmount.hashCode());
        result = 31 * result + (this.hotelAmount == null ? 0 : this.hotelAmount.hashCode());
        result = 31 * result + (this.trainAmount == null ? 0 : this.trainAmount.hashCode());
        result = 31 * result + (this.carAmount == null ? 0 : this.carAmount.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("flightAmount", flightAmount)
            .add("hotelAmount", hotelAmount)
            .add("trainAmount", trainAmount)
            .add("carAmount", carAmount)
            .toString();
    }
}
