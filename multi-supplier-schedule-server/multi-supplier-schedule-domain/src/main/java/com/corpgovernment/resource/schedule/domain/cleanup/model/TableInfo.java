package com.corpgovernment.resource.schedule.domain.cleanup.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TableInfo {

    /**
     * 数据库表名
     */
    @NotBlank
    public String tableName;

    /**
     * 该表的字段
     */
    @NotNull
    public List<FieldInfo> fieldInfos;

    /**
     * 每次查询、修改条数
     */
    public Integer fetchSize = 1;

    /**
     * 每次查询、修改分页的起始位置
     */
    public Integer fetchStart = 0;

    /**
     * ID。非必传（通过ID查询、更新指定数据。）
     */
    public List<Long> ids;


}
