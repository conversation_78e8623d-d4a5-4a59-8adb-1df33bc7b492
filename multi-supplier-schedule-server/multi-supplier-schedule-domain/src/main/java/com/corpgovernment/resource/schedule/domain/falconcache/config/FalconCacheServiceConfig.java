package com.corpgovernment.resource.schedule.domain.falconcache.config;

import com.ctrip.corp.obt.generic.utils.EnvironmentHolder;

/**
 * <AUTHOR>
 * @date 2024/5/24
 */
public class FalconCacheServiceConfig {
    private static final String PREFIX = "falconcache.";

    private static final FalconCacheConfig DEFAULT_CONFIG = k -> EnvironmentHolder.getProperty(PREFIX + k);

    private FalconCacheServiceConfig() {
    }

    /**
     * Returns a default implementation of the registry config backed by system properties.
     */
    public static FalconCacheConfig defaultConfig() {
        return DEFAULT_CONFIG;
    }
}
