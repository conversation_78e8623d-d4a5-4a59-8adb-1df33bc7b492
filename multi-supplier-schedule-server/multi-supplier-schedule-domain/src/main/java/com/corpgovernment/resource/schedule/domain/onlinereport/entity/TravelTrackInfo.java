package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 位置事件
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "startPosition",
    "endPosition",
    "tripCount",
    "tripHMcount",
    "tripWcount"
})
public class TravelTrackInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public TravelTrackInfo(
        PositionInfo startPosition,
        PositionInfo endPosition,
        Integer tripCount,
        Integer tripHMcount,
        Integer tripWcount) {
        this.startPosition = startPosition;
        this.endPosition = endPosition;
        this.tripCount = tripCount;
        this.tripHMcount = tripHMcount;
        this.tripWcount = tripWcount;
    }

    public TravelTrackInfo() {
    }

    /**
     * 出发城市
     */
    @JsonProperty("startPosition")
    public PositionInfo startPosition;

    /**
     * 到达城市
     */
    @JsonProperty("endPosition")
    public PositionInfo endPosition;

    /**
     * 出行或计划行程数
     */
    @JsonProperty("tripCount")
    public Integer tripCount;

    /**
     * 可能到过
     */
    @JsonProperty("tripHMcount")
    public Integer tripHMcount;

    /**
     * 将要去
     */
    @JsonProperty("tripWcount")
    public Integer tripWcount;

    /**
     * 出发城市
     */
    public PositionInfo getStartPosition() {
        return startPosition;
    }

    /**
     * 出发城市
     */
    public void setStartPosition(final PositionInfo startPosition) {
        this.startPosition = startPosition;
    }

    /**
     * 到达城市
     */
    public PositionInfo getEndPosition() {
        return endPosition;
    }

    /**
     * 到达城市
     */
    public void setEndPosition(final PositionInfo endPosition) {
        this.endPosition = endPosition;
    }

    /**
     * 出行或计划行程数
     */
    public Integer getTripCount() {
        return tripCount;
    }

    /**
     * 出行或计划行程数
     */
    public void setTripCount(final Integer tripCount) {
        this.tripCount = tripCount;
    }

    /**
     * 可能到过
     */
    public Integer getTripHMcount() {
        return tripHMcount;
    }

    /**
     * 可能到过
     */
    public void setTripHMcount(final Integer tripHMcount) {
        this.tripHMcount = tripHMcount;
    }

    /**
     * 将要去
     */
    public Integer getTripWcount() {
        return tripWcount;
    }

    /**
     * 将要去
     */
    public void setTripWcount(final Integer tripWcount) {
        this.tripWcount = tripWcount;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TravelTrackInfo other = (TravelTrackInfo)obj;
        return
            Objects.equal(this.startPosition, other.startPosition) &&
            Objects.equal(this.endPosition, other.endPosition) &&
            Objects.equal(this.tripCount, other.tripCount) &&
            Objects.equal(this.tripHMcount, other.tripHMcount) &&
            Objects.equal(this.tripWcount, other.tripWcount);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.startPosition == null ? 0 : this.startPosition.hashCode());
        result = 31 * result + (this.endPosition == null ? 0 : this.endPosition.hashCode());
        result = 31 * result + (this.tripCount == null ? 0 : this.tripCount.hashCode());
        result = 31 * result + (this.tripHMcount == null ? 0 : this.tripHMcount.hashCode());
        result = 31 * result + (this.tripWcount == null ? 0 : this.tripWcount.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("startPosition", startPosition)
            .add("endPosition", endPosition)
            .add("tripCount", tripCount)
            .add("tripHMcount", tripHMcount)
            .add("tripWcount", tripWcount)
            .toString();
    }
}
