package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 位置事件
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "mCount",
    "hCount",
    "wCount"
})
public class TravelPositionSummaryInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public TravelPositionSummaryInfo(
        Integer mCount,
        Integer hCount,
        Integer wCount) {
        this.mCount = mCount;
        this.hCount = hCount;
        this.wCount = wCount;
    }

    public TravelPositionSummaryInfo() {
    }

    /**
     * 可能在
     */
    @JsonProperty("mCount")
    public Integer mCount;

    /**
     * 曾到过
     */
    @JsonProperty("hCount")
    public Integer hCount;

    /**
     * 将要去
     */
    @JsonProperty("wCount")
    public Integer wCount;

    /**
     * 可能在
     */
    public Integer getMCount() {
        return mCount;
    }

    /**
     * 可能在
     */
    public void setMCount(final Integer mCount) {
        this.mCount = mCount;
    }

    /**
     * 曾到过
     */
    public Integer getHCount() {
        return hCount;
    }

    /**
     * 曾到过
     */
    public void setHCount(final Integer hCount) {
        this.hCount = hCount;
    }

    /**
     * 将要去
     */
    public Integer getWCount() {
        return wCount;
    }

    /**
     * 将要去
     */
    public void setWCount(final Integer wCount) {
        this.wCount = wCount;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TravelPositionSummaryInfo other = (TravelPositionSummaryInfo)obj;
        return
            Objects.equal(this.mCount, other.mCount) &&
            Objects.equal(this.hCount, other.hCount) &&
            Objects.equal(this.wCount, other.wCount);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.mCount == null ? 0 : this.mCount.hashCode());
        result = 31 * result + (this.hCount == null ? 0 : this.hCount.hashCode());
        result = 31 * result + (this.wCount == null ? 0 : this.wCount.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("mCount", mCount)
            .add("hCount", hCount)
            .add("wCount", wCount)
            .toString();
    }
}
