package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "headerKey",
    "headerValue",
    "dataType"
})
public class HeaderKeyValDataType implements Serializable {
    private static final long serialVersionUID = 1L;





    public HeaderKeyValDataType(
        String headerKey,
        String headerValue,
        Integer dataType) {
        this.headerKey = headerKey;
        this.headerValue = headerValue;
        this.dataType = dataType;
    }

    public HeaderKeyValDataType() {
    }

    /**
     * 对应字段-如:flightV
     */
    @JsonProperty("headerKey")
    public String headerKey;

    /**
     * 对应字段值-如:999,999,999
     */
    @JsonProperty("headerValue")
    public String headerValue;

    /**
     * 数据类型
     */
    @JsonProperty("dataType")
    public Integer dataType;

    /**
     * 对应字段-如:flightV
     */
    public String getHeaderKey() {
        return headerKey;
    }

    /**
     * 对应字段-如:flightV
     */
    public void setHeaderKey(final String headerKey) {
        this.headerKey = headerKey;
    }

    /**
     * 对应字段值-如:999,999,999
     */
    public String getHeaderValue() {
        return headerValue;
    }

    /**
     * 对应字段值-如:999,999,999
     */
    public void setHeaderValue(final String headerValue) {
        this.headerValue = headerValue;
    }

    /**
     * 数据类型
     */
    public Integer getDataType() {
        return dataType;
    }

    /**
     * 数据类型
     */
    public void setDataType(final Integer dataType) {
        this.dataType = dataType;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final HeaderKeyValDataType other = (HeaderKeyValDataType)obj;
        return
            Objects.equal(this.headerKey, other.headerKey) &&
            Objects.equal(this.headerValue, other.headerValue) &&
            Objects.equal(this.dataType, other.dataType);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.headerKey == null ? 0 : this.headerKey.hashCode());
        result = 31 * result + (this.headerValue == null ? 0 : this.headerValue.hashCode());
        result = 31 * result + (this.dataType == null ? 0 : this.dataType.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("headerKey", headerKey)
            .add("headerValue", headerValue)
            .add("dataType", dataType)
            .toString();
    }
}
