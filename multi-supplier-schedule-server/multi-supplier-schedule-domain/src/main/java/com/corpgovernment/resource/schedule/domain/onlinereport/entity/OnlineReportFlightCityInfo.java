package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dPosition",
    "aPosition",
    "totalPersons",
    "personsPercent"
})
public class OnlineReportFlightCityInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportFlightCityInfo(
        PositionInfo dPosition,
        PositionInfo aPosition,
        Integer totalPersons,
        Double personsPercent) {
        this.dPosition = dPosition;
        this.aPosition = aPosition;
        this.totalPersons = totalPersons;
        this.personsPercent = personsPercent;
    }

    public OnlineReportFlightCityInfo() {
    }

    /**
     * 出发城市
     */
    @JsonProperty("dPosition")
    public PositionInfo dPosition;

    /**
     * 到达城市
     */
    @JsonProperty("aPosition")
    public PositionInfo aPosition;

    /**
     * 总人数
     */
    @JsonProperty("totalPersons")
    public Integer totalPersons;

    /**
     * 人数占比
     */
    @JsonProperty("personsPercent")
    public Double personsPercent;

    /**
     * 出发城市
     */
    public PositionInfo getDPosition() {
        return dPosition;
    }

    /**
     * 出发城市
     */
    public void setDPosition(final PositionInfo dPosition) {
        this.dPosition = dPosition;
    }

    /**
     * 到达城市
     */
    public PositionInfo getAPosition() {
        return aPosition;
    }

    /**
     * 到达城市
     */
    public void setAPosition(final PositionInfo aPosition) {
        this.aPosition = aPosition;
    }

    /**
     * 总人数
     */
    public Integer getTotalPersons() {
        return totalPersons;
    }

    /**
     * 总人数
     */
    public void setTotalPersons(final Integer totalPersons) {
        this.totalPersons = totalPersons;
    }

    /**
     * 人数占比
     */
    public Double getPersonsPercent() {
        return personsPercent;
    }

    /**
     * 人数占比
     */
    public void setPersonsPercent(final Double personsPercent) {
        this.personsPercent = personsPercent;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportFlightCityInfo other = (OnlineReportFlightCityInfo)obj;
        return
            Objects.equal(this.dPosition, other.dPosition) &&
            Objects.equal(this.aPosition, other.aPosition) &&
            Objects.equal(this.totalPersons, other.totalPersons) &&
            Objects.equal(this.personsPercent, other.personsPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dPosition == null ? 0 : this.dPosition.hashCode());
        result = 31 * result + (this.aPosition == null ? 0 : this.aPosition.hashCode());
        result = 31 * result + (this.totalPersons == null ? 0 : this.totalPersons.hashCode());
        result = 31 * result + (this.personsPercent == null ? 0 : this.personsPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dPosition", dPosition)
            .add("aPosition", aPosition)
            .add("totalPersons", totalPersons)
            .add("personsPercent", personsPercent)
            .toString();
    }
}
