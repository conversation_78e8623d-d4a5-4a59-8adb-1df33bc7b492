package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2019/5/31 14:14
 */
public class FlightAirlineAnysisBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 列表详情
     */
    public List<FlightAirlineAnysisDetailBO> fltAirlineAnysisList;

    /**
     * 汇总
     */
    public FlightAirlineAnysisDetailBO fltAirlineAnysisSum;

    public List<FlightAirlineAnysisDetailBO> getFltAirlineAnysisList() {
        return fltAirlineAnysisList;
    }

    public void setFltAirlineAnysisList(List<FlightAirlineAnysisDetailBO> fltAirlineAnysisList) {
        this.fltAirlineAnysisList = fltAirlineAnysisList;
    }

    public FlightAirlineAnysisDetailBO getFltAirlineAnysisSum() {
        return fltAirlineAnysisSum;
    }

    public void setFltAirlineAnysisSum(FlightAirlineAnysisDetailBO fltAirlineAnysisSum) {
        this.fltAirlineAnysisSum = fltAirlineAnysisSum;
    }
}
