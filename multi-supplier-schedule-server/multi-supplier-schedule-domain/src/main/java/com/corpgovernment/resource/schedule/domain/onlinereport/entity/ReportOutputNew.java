package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import java.util.List;

/**
 * Auther:abguo
 * Date:2019/9/7
 * Description:
 * Project:onlinereportweb
 */
public class ReportOutputNew {
    int totalCount;
    List<List<String>> rowList;
    List<String> titleList;
    int currentPage;
    int columnCount;
    int rowCount;

    public List<List<String>> getRowList() {
        return rowList;
    }

    public void setRowList(List<List<String>> rowList) {
        this.rowList = rowList;
    }

    public int getColumnCount() {
        return columnCount;
    }

    public void setColumnCount(int columnCount) {
        this.columnCount = columnCount;
    }

    public int getRowCount() {
        return rowCount;
    }

    public void setRowCount(int rowCount) {
        this.rowCount = rowCount;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<String> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<String> titleList) {
        this.titleList = titleList;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }
}
