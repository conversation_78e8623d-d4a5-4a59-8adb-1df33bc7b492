package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight.flightgeneral;

import java.io.Serializable;

/**
 * @Auther: ddzhan
 * @Date: 2019/6/5
 * @Description: 机票总概部门top分析 BO
 */
public class FlightGeneralDeptTopAnysisBO implements Serializable {
    public String name;
    public Integer quantity;
    public String price;
    public Integer refundQuantity;
    public Integer changeQuantity;
    public Integer rcTimes;
    private Integer orderNumber;
    public String type;

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Integer orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public Integer getRefundQuantity() {
        return refundQuantity;
    }

    public void setRefundQuantity(Integer refundQuantity) {
        this.refundQuantity = refundQuantity;
    }

    public Integer getChangeQuantity() {
        return changeQuantity;
    }

    public void setChangeQuantity(Integer changeQuantity) {
        this.changeQuantity = changeQuantity;
    }

    public Integer getRcTimes() {
        return rcTimes;
    }

    public void setRcTimes(Integer rcTimes) {
        this.rcTimes = rcTimes;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
