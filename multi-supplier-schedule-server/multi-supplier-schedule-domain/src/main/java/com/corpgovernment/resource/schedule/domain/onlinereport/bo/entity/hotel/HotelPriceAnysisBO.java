package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.hotel;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 酒店城市价格分析详情
 * <AUTHOR>
 * @Date 2019/5/7
 */
public class HotelPriceAnysisBO {
    private List<HotelPriceAnysisDetailBO> allAnysisInfo;
    private List<HotelPriceAnysisDetailBO> anysisInfoLv2;
    private List<HotelPriceAnysisDetailBO> anysisInfoLv3;
    private List<HotelPriceAnysisDetailBO> anysisInfoLv4;
    private List<HotelPriceAnysisDetailBO> anysisInfoLv5;

    public HotelPriceAnysisBO(){
        this.allAnysisInfo=new ArrayList<>(0);
        this.anysisInfoLv2=new ArrayList<>(0);
        this.anysisInfoLv3=new ArrayList<>(0);
        this.anysisInfoLv4=new ArrayList<>(0);
        this.anysisInfoLv5=new ArrayList<>(0);
    }

    public List<HotelPriceAnysisDetailBO> getAllAnysisInfo() {
        return allAnysisInfo;
    }

    public void setAllAnysisInfo(List<HotelPriceAnysisDetailBO> allAnysisInfo) {
        this.allAnysisInfo = allAnysisInfo;
    }

    public List<HotelPriceAnysisDetailBO> getAnysisInfoLv2() {
        return anysisInfoLv2;
    }

    public void setAnysisInfoLv2(List<HotelPriceAnysisDetailBO> anysisInfoLv2) {
        this.anysisInfoLv2 = anysisInfoLv2;
    }

    public List<HotelPriceAnysisDetailBO> getAnysisInfoLv3() {
        return anysisInfoLv3;
    }

    public void setAnysisInfoLv3(List<HotelPriceAnysisDetailBO> anysisInfoLv3) {
        this.anysisInfoLv3 = anysisInfoLv3;
    }

    public List<HotelPriceAnysisDetailBO> getAnysisInfoLv4() {
        return anysisInfoLv4;
    }

    public void setAnysisInfoLv4(List<HotelPriceAnysisDetailBO> anysisInfoLv4) {
        this.anysisInfoLv4 = anysisInfoLv4;
    }

    public List<HotelPriceAnysisDetailBO> getAnysisInfoLv5() {
        return anysisInfoLv5;
    }

    public void setAnysisInfoLv5(List<HotelPriceAnysisDetailBO> anysisInfoLv5) {
        this.anysisInfoLv5 = anysisInfoLv5;
    }
}
