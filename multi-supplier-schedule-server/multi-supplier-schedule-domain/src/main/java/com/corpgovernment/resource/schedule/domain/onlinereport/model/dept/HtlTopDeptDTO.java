package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
public class HtlTopDeptDTO {

    @Column(name = "aggId")
    @Type(value = Types.VARCHAR)
    private String aggId;

    @Column(name = "aggType")
    @Type(value = Types.VARCHAR)
    private String aggType;

    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;

    @Column(name = "totalQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalQuantity;

    @Column(name = "totalRoomPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRoomPrice;

    @Column(name = "totalAmount_ta")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmountTa;

    @Column(name = "totalQuantity_ta")
    @Type(value = Types.INTEGER)
    private Integer totalQuantityTa;

    @Column(name = "totalRoomPrice_ta")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRoomPriceTa;

    @Column(name = "totalAmount_nta")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmountNta;

    @Column(name = "totalQuantity_nta")
    @Type(value = Types.INTEGER)
    private Integer totalQuantityNta;

    @Column(name = "totalRoomPrice_nta")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRoomPriceNta;

    @Column(name = "totalAmount_dom")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmountDom;

    @Column(name = "totalQuantity_dom")
    @Type(value = Types.INTEGER)
    private Integer totalQuantityDom;

    @Column(name = "totalRoomPrice_dom")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRoomPriceDom;

    @Column(name = "totalAmount_inter")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmountInter;

    @Column(name = "totalQuantity_inter")
    @Type(value = Types.INTEGER)
    private Integer totalQuantityInter;

    @Column(name = "totalRoomPrice_inter")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRoomPriceInter;

    @Column(name = "totalOverAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalOverAmount;

    @Column(name = "totalRcTimes")
    @Type(value = Types.INTEGER)
    private Integer totalRcTimes;

    @Column(name = "totalOrderCount")
    @Type(value = Types.INTEGER)
    private Integer totalOrderCount;

    @Column(name = "totalSaveAmount3c")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalSaveAmount3c;

    @Column(name = "totalCorpRealPay3c")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCorpRealPay3c;

    @Column(name = "totalSaveAmountPremium")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalSaveAmountPremium;

    @Column(name = "totalCorpRealPayPremium")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCorpRealPayPremium;

    @Column(name = "totalSaveAmountPromotion")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalSaveAmountPromotion;

    @Column(name = "totalCorpRealPayPromotion")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCorpRealPayPromotion;

    @Column(name = "totalControlSave")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalControlSave;

    @Column(name = "totalControlCorpRealPay")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalControlCorpRealPay;

    @Column(name = "totalRefundloss")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalRefundloss;

    @Column(name = "hotCity")
    @Type(value = Types.VARCHAR)
    private String hotCity;

    @Column(name = "hotStar")
    @Type(value = Types.INTEGER)
    private Integer hotStar;

    @Column(name = "totalCancelQuaity")
    @Type(value = Types.INTEGER)
    private Integer totalCancelQuaity;

    @Column(name = "totalAllQuaity")
    @Type(value = Types.INTEGER)
    private Integer totalAllQuaity;

    @Column(name = "totalAllOrderCount")
    @Type(value = Types.INTEGER)
    private Integer totalAllOrderCount;

    @Column(name = "totalDeadPriceOnenightQantiy")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalDeadPriceOnenightQantiy;

    @Column(name = "totalPersonsQuantity")
    @Type(value = Types.INTEGER)
    private Integer totalPersonsQuantity;
}
