package com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.impl;

import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.*;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.HotelAuditRuleEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelModeEnum;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.IHotelAuditDomainService;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.IHotelAuditRule;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardTypeEnum.FLOAT_TRAVEL_STANDARD;
import static com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardTypeEnum.STANDARD_TRAVEL_STANDARD;

/**
 * <AUTHOR>
 * @description
 * @create 2024-12-24 14:06
 */
@Slf4j
@Service
public class MaxPriceAuditRule implements IHotelAuditRule {
    
    @Resource
    private IHotelAuditDomainService hotelAuditDomainService;
    
    @Override
    public HotelAuditResult audit(HotelAuditItem hotelAuditItem) {
        // 间夜价格
        BigDecimal roomNightAvgPrice = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getOrderInfo)
                .map(OrderInfo::getRoomNightAvgPrice)
                .orElse(null);
        // 是否超标
        Boolean overLimit = this.judgePriceOverLimit(hotelAuditItem);
        // 差标项
        List<TravelStandardItem> usedTravelStandardItemList = hotelAuditDomainService.getUsedTravelStandardItemList(hotelAuditItem);
        if (CollectionUtils.isEmpty(usedTravelStandardItemList)) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                    .auditDescList(null)
                    .auditPass(true)
                    .build();
        }
        // 校验
        List<String> auditDescList = new ArrayList<>();
        for (TravelStandardItem travelStandardItem : usedTravelStandardItemList) {
            BigDecimal maxPrice = Optional.ofNullable(travelStandardItem)
                    .map(TravelStandardItem::getMaxPrice)
                    .orElse(null);
            Integer index = Optional.ofNullable(travelStandardItem)
                    .map(TravelStandardItem::getIndex)
                    .orElse(null);
            if (roomNightAvgPrice != null && maxPrice != null && roomNightAvgPrice.compareTo(maxPrice) > 0 && !Boolean.TRUE.equals(overLimit)) {
                auditDescList.add(String.format("索引%s，订单间夜平均价格%s大于差标%s，订单处于非超标状态，不符合%s规则", index, roomNightAvgPrice.stripTrailingZeros().toPlainString(), maxPrice.stripTrailingZeros().toPlainString(), getHotelAuditRuleEnum().getInfo()));
            }
            if (roomNightAvgPrice != null && maxPrice != null && roomNightAvgPrice.compareTo(maxPrice) <= 0 && Boolean.TRUE.equals(overLimit)) {
                auditDescList.add(String.format("索引%s，订单间夜平均价格%s小于等于差标%s，订单处于超标状态，不符合%s规则", index, roomNightAvgPrice.stripTrailingZeros().toPlainString(), maxPrice.stripTrailingZeros().toPlainString(), getHotelAuditRuleEnum().getInfo()));
            }
        }
        if (CollectionUtils.isNotEmpty(auditDescList)) {
            return HotelAuditResult.builder()
                    .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                    .auditDescList(auditDescList)
                    .auditPass(false)
                    .errorLabel(getHotelAuditRuleEnum().getErrorLabel())
                    .build();
        }
        return HotelAuditResult.builder()
                .hotelAuditRuleEnum(getHotelAuditRuleEnum())
                .auditDescList(null)
                .auditPass(true)
                .build();
    }
    
    @Override
    public HotelAuditRuleEnum getHotelAuditRuleEnum() {
        return HotelAuditRuleEnum.MAX_PRICE;
    }
    
    @Override
    public List<TravelModeEnum> getSupportTravelModeEnumList() {
        return Collections.singletonList(TravelModeEnum.PUB);
    }

    private Boolean judgePriceOverLimit(HotelAuditItem hotelAuditItem){
        // 差标类型判断
        OrderTravelStandardResult.AvgPriceRuleResult avgPriceRuleResult = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getTokenInfo)
                .map(TokenInfo::getOrderTravelStandardResult)
                .map(OrderTravelStandardResult::getMaxAvgPriceResult)
                .orElse(null);
        if (avgPriceRuleResult == null){
            return false;
        }
        // 若超浮动差标一定超标准差标，所以直接返回超标状态
        return Boolean.TRUE.equals(avgPriceRuleResult.getOverLimit());
    }
    
}
