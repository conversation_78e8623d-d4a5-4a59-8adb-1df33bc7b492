package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight.flightgeneral;

/**
 * @Auther: zzd
 * @Date: 2019/6/6
 * @Description: 机票总概月分明细
 */
public class FlightMonthDetailBO {

    public String name;
    public String nameId;
    public Integer year;
    public Integer month;
    public String amount;
    public String domAmount;
    public String intAmount;
    public String monthOnMonth;
    public String yearOnYear;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameId() {
        return this.nameId;
    }

    public void setNameId(String nameId) {
        this.nameId = nameId;
    }

    public Integer getYear() {
        return this.year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return this.month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public String getAmount() {
        return this.amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getDomAmount() {
        return this.domAmount;
    }

    public void setDomAmount(String domAmount) {
        this.domAmount = domAmount;
    }

    public String getIntAmount() {
        return this.intAmount;
    }

    public void setIntAmount(String intAmount) {
        this.intAmount = intAmount;
    }

    public String getMonthOnMonth() {
        return this.monthOnMonth;
    }

    public void setMonthOnMonth(String monthOnMonth) {
        this.monthOnMonth = monthOnMonth;
    }

    public String getYearOnYear() {
        return this.yearOnYear;
    }

    public void setYearOnYear(String yearOnYear) {
        this.yearOnYear = yearOnYear;
    }

}
