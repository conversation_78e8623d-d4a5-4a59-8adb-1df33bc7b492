package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 差标计算器-酒店差标建议
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "productType",
    "corpIds",
    "cityTypes",
    "cities",
    "rankIds"
})
public class OnlineReportTravelStandardAdviceRequest implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportTravelStandardAdviceRequest(
        String productType,
        List<String> corpIds,
        List<Integer> cityTypes,
        List<Integer> cities,
        List<Integer> rankIds) {
        this.productType = productType;
        this.corpIds = corpIds;
        this.cityTypes = cityTypes;
        this.cities = cities;
        this.rankIds = rankIds;
    }

    public OnlineReportTravelStandardAdviceRequest() {
    }

    /**
     * 国内dom、国际inter、全部all
     */
    @JsonProperty("productType")
    public String productType;

    /**
     * 公司
     */
    @JsonProperty("corpIds")
    public List<String> corpIds;

    /**
     * 城市类别ids
     */
    @JsonProperty("cityTypes")
    public List<Integer> cityTypes;

    /**
     * 城市ids
     */
    @JsonProperty("cities")
    public List<Integer> cities;

    /**
     * 职级ids
     */
    @JsonProperty("rankIds")
    public List<Integer> rankIds;

    /**
     * 国内dom、国际inter、全部all
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 国内dom、国际inter、全部all
     */
    public void setProductType(final String productType) {
        this.productType = productType;
    }

    /**
     * 公司
     */
    public List<String> getCorpIds() {
        return corpIds;
    }

    /**
     * 公司
     */
    public void setCorpIds(final List<String> corpIds) {
        this.corpIds = corpIds;
    }

    /**
     * 城市类别ids
     */
    public List<Integer> getCityTypes() {
        return cityTypes;
    }

    /**
     * 城市类别ids
     */
    public void setCityTypes(final List<Integer> cityTypes) {
        this.cityTypes = cityTypes;
    }

    /**
     * 城市ids
     */
    public List<Integer> getCities() {
        return cities;
    }

    /**
     * 城市ids
     */
    public void setCities(final List<Integer> cities) {
        this.cities = cities;
    }

    /**
     * 职级ids
     */
    public List<Integer> getRankIds() {
        return rankIds;
    }

    /**
     * 职级ids
     */
    public void setRankIds(final List<Integer> rankIds) {
        this.rankIds = rankIds;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTravelStandardAdviceRequest other = (OnlineReportTravelStandardAdviceRequest)obj;
        return
            Objects.equal(this.productType, other.productType) &&
            Objects.equal(this.corpIds, other.corpIds) &&
            Objects.equal(this.cityTypes, other.cityTypes) &&
            Objects.equal(this.cities, other.cities) &&
            Objects.equal(this.rankIds, other.rankIds);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.productType == null ? 0 : this.productType.hashCode());
        result = 31 * result + (this.corpIds == null ? 0 : this.corpIds.hashCode());
        result = 31 * result + (this.cityTypes == null ? 0 : this.cityTypes.hashCode());
        result = 31 * result + (this.cities == null ? 0 : this.cities.hashCode());
        result = 31 * result + (this.rankIds == null ? 0 : this.rankIds.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("productType", productType)
            .add("corpIds", corpIds)
            .add("cityTypes", cityTypes)
            .add("cities", cities)
            .add("rankIds", rankIds)
            .toString();
    }
}
