package com.corpgovernment.resource.schedule.domain.onlinereport.dao;


import com.corpgovernment.resource.schedule.domain.onlinereport.po.CustomReportInfoNewPO;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;

/**
 * @Description: 自定义报表持久化(扩展类)
 * <AUTHOR>
 * @Date 2019/2/27
 */
@Component
public class CustomReportInfoNewDAOExt extends CustomReportInfoNewDAO {
    public CustomReportInfoNewDAOExt() throws SQLException {
    }

    /**
     * 根据UID查询相关自定义报表信息
     *
     * @param uid uid
     * @return 自定义报表集合
     */
    public List<CustomReportInfoNewPO> queryByUID(String uid) throws SQLException {
        /*CustomReportInfoNewPO po = new CustomReportInfoNewPO();
        po.setUid(uid);
        return queryBy(po);*/

        // TODO 用户自定的报表查询配置信息，实现持久化到程曦自己的数据库
        return null;
    }

    public List<CustomReportInfoNewPO> queryByUID(String uid, Integer reportType) throws SQLException {
        /*CustomReportInfoNewPO po = new CustomReportInfoNewPO();
        po.setUid(uid);
        po.setReportType(reportType);
        return queryBy(po);*/
        // TODO 用户自定的报表查询配置信息，实现持久化到程曦自己的数据库
        return null;
    }

    public List<CustomReportInfoNewPO> queryByIds(List<Long> ids) throws SQLException {
        /*SelectSqlBuilder builder = (SelectSqlBuilder) new SelectSqlBuilder().selectAll().in("id", ids, Types.BIGINT);
        DalHints hints = DalHints.createIfAbsent(null);
        return client.query(builder, hints);*/
        // TODO 用户自定的报表查询配置信息，实现持久化到程曦自己的数据库
        return null;
    }
}
