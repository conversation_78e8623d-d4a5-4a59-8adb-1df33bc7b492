package com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 每日房价
 * @create 2024-12-23 15:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DailyRoomPrice {

    // 日期
    private String effectDate;
    
    // 间夜价格
    private BigDecimal roomNightPrice;

}
