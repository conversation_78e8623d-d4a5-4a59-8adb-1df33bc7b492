package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.List;

/**
 * 部门uid消费明细
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "fltList",
        "htlList",
        "trainList",
        "carList",
        "overViewList",
        "busList",
        "vasoList",
        "welfareList",
        "totalRecords"
})
public class OnlineReportDeptUidConsumeDetailInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    @JsonProperty("fltList")
    public List<OnlineReportFltTopDeptConsume> fltList;
    @JsonProperty("htlList")
    public List<OnlineReportHtlTopDeptConsume> htlList;
    @JsonProperty("trainList")
    public List<OnlineReportTrainTopDeptConsume> trainList;
    @JsonProperty("carList")
    public List<OnlineReportCarTopDeptConsume> carList;
    @JsonProperty("overViewList")
    public List<OnlineReportOverviewTopDeptConsume> overViewList;
    @JsonProperty("busList")
    public List<OnlineReportBusTopDeptConsume> busList;
    @JsonProperty("vasoList")
    public List<OnlineReportVasoTopDeptConsume> vasoList;
    @JsonProperty("welfareList")
    public List<OnlineReportWelfareTopDeptConsume> welfareList;
    /**
     * 数据总条数
     */
    @JsonProperty("totalRecords")
    public Integer totalRecords;

    public OnlineReportDeptUidConsumeDetailInfo(
            List<OnlineReportFltTopDeptConsume> fltList,
            List<OnlineReportHtlTopDeptConsume> htlList,
            List<OnlineReportTrainTopDeptConsume> trainList,
            List<OnlineReportCarTopDeptConsume> carList,
            List<OnlineReportOverviewTopDeptConsume> overViewList,
            List<OnlineReportBusTopDeptConsume> busList,
            List<OnlineReportVasoTopDeptConsume> vasoList,
            List<OnlineReportWelfareTopDeptConsume> welfareList,
            Integer totalRecords) {
        this.fltList = fltList;
        this.htlList = htlList;
        this.trainList = trainList;
        this.carList = carList;
        this.overViewList = overViewList;
        this.busList = busList;
        this.vasoList = vasoList;
        this.welfareList = welfareList;
        this.totalRecords = totalRecords;
    }

    public OnlineReportDeptUidConsumeDetailInfo() {
    }

    public List<OnlineReportFltTopDeptConsume> getFltList() {
        return fltList;
    }

    public void setFltList(final List<OnlineReportFltTopDeptConsume> fltList) {
        this.fltList = fltList;
    }

    public List<OnlineReportHtlTopDeptConsume> getHtlList() {
        return htlList;
    }

    public void setHtlList(final List<OnlineReportHtlTopDeptConsume> htlList) {
        this.htlList = htlList;
    }

    public List<OnlineReportTrainTopDeptConsume> getTrainList() {
        return trainList;
    }

    public void setTrainList(final List<OnlineReportTrainTopDeptConsume> trainList) {
        this.trainList = trainList;
    }

    public List<OnlineReportCarTopDeptConsume> getCarList() {
        return carList;
    }

    public void setCarList(final List<OnlineReportCarTopDeptConsume> carList) {
        this.carList = carList;
    }

    public List<OnlineReportOverviewTopDeptConsume> getOverViewList() {
        return overViewList;
    }

    public void setOverViewList(final List<OnlineReportOverviewTopDeptConsume> overViewList) {
        this.overViewList = overViewList;
    }

    public List<OnlineReportBusTopDeptConsume> getBusList() {
        return busList;
    }

    public void setBusList(final List<OnlineReportBusTopDeptConsume> busList) {
        this.busList = busList;
    }

    public List<OnlineReportVasoTopDeptConsume> getVasoList() {
        return vasoList;
    }

    public void setVasoList(final List<OnlineReportVasoTopDeptConsume> vasoList) {
        this.vasoList = vasoList;
    }

    public List<OnlineReportWelfareTopDeptConsume> getWelfareList() {
        return welfareList;
    }

    public void setWelfareList(final List<OnlineReportWelfareTopDeptConsume> welfareList) {
        this.welfareList = welfareList;
    }

    /**
     * 数据总条数
     */
    public Integer getTotalRecords() {
        return totalRecords;
    }

    /**
     * 数据总条数
     */
    public void setTotalRecords(final Integer totalRecords) {
        this.totalRecords = totalRecords;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.fltList;
            case 1:
                return this.htlList;
            case 2:
                return this.trainList;
            case 3:
                return this.carList;
            case 4:
                return this.overViewList;
            case 5:
                return this.busList;
            case 6:
                return this.vasoList;
            case 7:
                return this.welfareList;
            case 8:
                return this.totalRecords;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.fltList = (List<OnlineReportFltTopDeptConsume>) fieldValue;
                break;
            case 1:
                this.htlList = (List<OnlineReportHtlTopDeptConsume>) fieldValue;
                break;
            case 2:
                this.trainList = (List<OnlineReportTrainTopDeptConsume>) fieldValue;
                break;
            case 3:
                this.carList = (List<OnlineReportCarTopDeptConsume>) fieldValue;
                break;
            case 4:
                this.overViewList = (List<OnlineReportOverviewTopDeptConsume>) fieldValue;
                break;
            case 5:
                this.busList = (List<OnlineReportBusTopDeptConsume>) fieldValue;
                break;
            case 6:
                this.vasoList = (List<OnlineReportVasoTopDeptConsume>) fieldValue;
                break;
            case 7:
                this.welfareList = (List<OnlineReportWelfareTopDeptConsume>) fieldValue;
                break;
            case 8:
                this.totalRecords = (Integer) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportDeptUidConsumeDetailInfo other = (OnlineReportDeptUidConsumeDetailInfo) obj;
        return
                Objects.equal(this.fltList, other.fltList) &&
                        Objects.equal(this.htlList, other.htlList) &&
                        Objects.equal(this.trainList, other.trainList) &&
                        Objects.equal(this.carList, other.carList) &&
                        Objects.equal(this.overViewList, other.overViewList) &&
                        Objects.equal(this.busList, other.busList) &&
                        Objects.equal(this.vasoList, other.vasoList) &&
                        Objects.equal(this.welfareList, other.welfareList) &&
                        Objects.equal(this.totalRecords, other.totalRecords);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.fltList == null ? 0 : this.fltList.hashCode());
        result = 31 * result + (this.htlList == null ? 0 : this.htlList.hashCode());
        result = 31 * result + (this.trainList == null ? 0 : this.trainList.hashCode());
        result = 31 * result + (this.carList == null ? 0 : this.carList.hashCode());
        result = 31 * result + (this.overViewList == null ? 0 : this.overViewList.hashCode());
        result = 31 * result + (this.busList == null ? 0 : this.busList.hashCode());
        result = 31 * result + (this.vasoList == null ? 0 : this.vasoList.hashCode());
        result = 31 * result + (this.welfareList == null ? 0 : this.welfareList.hashCode());
        result = 31 * result + (this.totalRecords == null ? 0 : this.totalRecords.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("fltList", fltList)
                .add("htlList", htlList)
                .add("trainList", trainList)
                .add("carList", carList)
                .add("overViewList", overViewList)
                .add("busList", busList)
                .add("vasoList", vasoList)
                .add("welfareList", welfareList)
                .add("totalRecords", totalRecords)
                .toString();
    }
}
