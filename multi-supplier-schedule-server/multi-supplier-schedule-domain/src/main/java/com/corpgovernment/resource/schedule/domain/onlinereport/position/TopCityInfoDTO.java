package com.corpgovernment.resource.schedule.domain.onlinereport.position;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-08-18 11:27
 */
@Data
public class TopCityInfoDTO {
    // 城市id
    @Column(name = "city")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    // 金额
    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;

}
