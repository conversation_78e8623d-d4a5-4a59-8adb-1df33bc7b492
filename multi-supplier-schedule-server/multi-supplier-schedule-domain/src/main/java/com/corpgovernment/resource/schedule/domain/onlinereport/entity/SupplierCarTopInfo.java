package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "sumInfo",
    "otherInfo",
    "topList"
})
public class SupplierCarTopInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public SupplierCarTopInfo(
        OnlineReportSupplierCarTop sumInfo,
        OnlineReportSupplierCarTop otherInfo,
        List<OnlineReportSupplierCarTop> topList) {
        this.sumInfo = sumInfo;
        this.otherInfo = otherInfo;
        this.topList = topList;
    }

    public SupplierCarTopInfo() {
    }

    /**
     * sum
     */
    @JsonProperty("sumInfo")
    public OnlineReportSupplierCarTop sumInfo;

    /**
     * other
     */
    @JsonProperty("otherInfo")
    public OnlineReportSupplierCarTop otherInfo;

    /**
     * top
     */
    @JsonProperty("topList")
    public List<OnlineReportSupplierCarTop> topList;

    /**
     * sum
     */
    public OnlineReportSupplierCarTop getSumInfo() {
        return sumInfo;
    }

    /**
     * sum
     */
    public void setSumInfo(final OnlineReportSupplierCarTop sumInfo) {
        this.sumInfo = sumInfo;
    }

    /**
     * other
     */
    public OnlineReportSupplierCarTop getOtherInfo() {
        return otherInfo;
    }

    /**
     * other
     */
    public void setOtherInfo(final OnlineReportSupplierCarTop otherInfo) {
        this.otherInfo = otherInfo;
    }

    /**
     * top
     */
    public List<OnlineReportSupplierCarTop> getTopList() {
        return topList;
    }

    /**
     * top
     */
    public void setTopList(final List<OnlineReportSupplierCarTop> topList) {
        this.topList = topList;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final SupplierCarTopInfo other = (SupplierCarTopInfo)obj;
        return
            Objects.equal(this.sumInfo, other.sumInfo) &&
            Objects.equal(this.otherInfo, other.otherInfo) &&
            Objects.equal(this.topList, other.topList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.sumInfo == null ? 0 : this.sumInfo.hashCode());
        result = 31 * result + (this.otherInfo == null ? 0 : this.otherInfo.hashCode());
        result = 31 * result + (this.topList == null ? 0 : this.topList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("sumInfo", sumInfo)
            .add("otherInfo", otherInfo)
            .add("topList", topList)
            .toString();
    }
}
