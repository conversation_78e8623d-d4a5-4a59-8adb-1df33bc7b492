package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 退票、退订分析
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "responseCode",
    "responseDesc",
    "refundInfo",
    "refundPercentInfo"
})
public class OnlineReportRefundTrendResponse implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportRefundTrendResponse(
        Integer responseCode,
        String responseDesc,
        RefundInfo refundInfo,
        RefundPercentInfo refundPercentInfo) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.refundInfo = refundInfo;
        this.refundPercentInfo = refundPercentInfo;
        
    }

    public OnlineReportRefundTrendResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    @JsonProperty("refundInfo")
    public RefundInfo refundInfo;

    @JsonProperty("refundPercentInfo")
    public RefundPercentInfo refundPercentInfo;



    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }
    public RefundInfo getRefundInfo() {
        return refundInfo;
    }

    public void setRefundInfo(final RefundInfo refundInfo) {
        this.refundInfo = refundInfo;
    }
    public RefundPercentInfo getRefundPercentInfo() {
        return refundPercentInfo;
    }

    public void setRefundPercentInfo(final RefundPercentInfo refundPercentInfo) {
        this.refundPercentInfo = refundPercentInfo;
    }



    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportRefundTrendResponse other = (OnlineReportRefundTrendResponse)obj;
        return
            Objects.equal(this.responseCode, other.responseCode) &&
            Objects.equal(this.responseDesc, other.responseDesc) &&
            Objects.equal(this.refundInfo, other.refundInfo) &&
            Objects.equal(this.refundPercentInfo, other.refundPercentInfo) ;
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.refundInfo == null ? 0 : this.refundInfo.hashCode());
        result = 31 * result + (this.refundPercentInfo == null ? 0 : this.refundPercentInfo.hashCode());
        

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("responseCode", responseCode)
            .add("responseDesc", responseDesc)
            .add("refundInfo", refundInfo)
            .add("refundPercentInfo", refundPercentInfo)
            
            .toString();
    }
}
