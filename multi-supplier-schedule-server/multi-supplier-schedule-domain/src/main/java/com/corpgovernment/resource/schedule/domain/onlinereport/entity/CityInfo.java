package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "cityId",
    "cityName",
    "cityTypeId",
    "cityTypeName"
})
public class CityInfo implements Serializable {
    private static final long serialVersionUID = 1L;



    

    public CityInfo(
        Integer cityId,
        String cityName,
        Integer cityTypeId,
        String cityTypeName) {
        this.cityId = cityId;
        this.cityName = cityName;
        this.cityTypeId = cityTypeId;
        this.cityTypeName = cityTypeName;
    }

    public CityInfo() {
    }

    /**
     * 城市id
     */
    @JsonProperty("cityId")
    public Integer cityId;

    /**
     * 城市名称
     */
    @JsonProperty("cityName")
    public String cityName;

    /**
     * 城市类别id
     */
    @JsonProperty("cityTypeId")
    public Integer cityTypeId;

    /**
     * 城市类别名称
     */
    @JsonProperty("cityTypeName")
    public String cityTypeName;

    /**
     * 城市id
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 城市id
     */
    public void setCityId(final Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 城市名称
     */
    public String getCityName() {
        return cityName;
    }

    /**
     * 城市名称
     */
    public void setCityName(final String cityName) {
        this.cityName = cityName;
    }

    /**
     * 城市类别id
     */
    public Integer getCityTypeId() {
        return cityTypeId;
    }

    /**
     * 城市类别id
     */
    public void setCityTypeId(final Integer cityTypeId) {
        this.cityTypeId = cityTypeId;
    }

    /**
     * 城市类别名称
     */
    public String getCityTypeName() {
        return cityTypeName;
    }

    /**
     * 城市类别名称
     */
    public void setCityTypeName(final String cityTypeName) {
        this.cityTypeName = cityTypeName;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final CityInfo other = (CityInfo)obj;
        return
            Objects.equal(this.cityId, other.cityId) &&
            Objects.equal(this.cityName, other.cityName) &&
            Objects.equal(this.cityTypeId, other.cityTypeId) &&
            Objects.equal(this.cityTypeName, other.cityTypeName);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.cityId == null ? 0 : this.cityId.hashCode());
        result = 31 * result + (this.cityName == null ? 0 : this.cityName.hashCode());
        result = 31 * result + (this.cityTypeId == null ? 0 : this.cityTypeId.hashCode());
        result = 31 * result + (this.cityTypeName == null ? 0 : this.cityTypeName.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("cityId", cityId)
            .add("cityName", cityName)
            .add("cityTypeId", cityTypeId)
            .add("cityTypeName", cityTypeName)
            .toString();
    }
}
