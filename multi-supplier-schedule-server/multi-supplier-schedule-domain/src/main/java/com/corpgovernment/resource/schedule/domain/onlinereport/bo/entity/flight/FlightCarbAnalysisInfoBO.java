package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 机票-碳排放-分析结果
 * <AUTHOR>
 * @Date 2019/5/20
 */
public class FlightCarbAnalysisInfoBO {
    private FlightCarbInfoBO tolCarbAnalysisInfo;
    private List<FlightCarbInfoBO> carbInfoList;

    public FlightCarbAnalysisInfoBO(){
        this.tolCarbAnalysisInfo=new FlightCarbInfoBO();
        this.carbInfoList=new ArrayList<>(0);
    }

    public FlightCarbInfoBO getTolCarbAnalysisInfo() {
        return tolCarbAnalysisInfo;
    }

    public void setTolCarbAnalysisInfo(FlightCarbInfoBO tolCarbAnalysisInfo) {
        this.tolCarbAnalysisInfo = tolCarbAnalysisInfo;
    }

    public List<FlightCarbInfoBO> getCarbInfoList() {
        return carbInfoList;
    }

    public void setCarbInfoList(List<FlightCarbInfoBO> carbInfoList) {
        this.carbInfoList = carbInfoList;
    }
}
