package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.risk;

import lombok.Getter;
import lombok.Setter;

/*
 * <AUTHOR>
 * @date 2022/8/8 11:01
 * @Desc
 */
@Setter
@Getter
public class RiskOrderReportHotelCashOutConditionB0 {
    // 选择的酒店 及订单 公司 主账户 成本中心 部门信息
    private String corporation;
    private String masterHotelId;
    private String masterHotelName;
    private String accountId;
    private String dept1;
    private String dept2;
    private String dept3;
    private String dept4;
    private String dept5;
    private String dept6;
    private String dept7;
    private String dept8;
    private String dept9;
    private String dept10;
    private String costcenter1;
    private String costcenter2;
    private String costcenter3;
    private String costcenter4;
    private String costcenter5;
    private String costcenter6;
}
