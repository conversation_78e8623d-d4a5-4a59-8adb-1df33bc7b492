package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机票部门消费
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "dimId",
        "dim",
        "totalAmount",
        "totalQuantity",
        "totalFullfaretkt",
        "avgTpmsPrice",
        "avgPrice",
        "avgDiscount",
        "fullfaretktPercent",
        "totalSaveAmount",
        "saveRate",
        "totalRefundFee",
        "refundRate",
        "totalRebookFee",
        "rebookRate",
        "rcPercent",
        "totalOverAmount",
        "saveAmount3cRate",
        "saveAmount2cRate",
        "controlSaveRate",
        "totalCarbons",
        "carbonSaveRate"
})
public class OnlineReportFltTopDeptConsume implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    @JsonProperty("dimId")
    public String dimId;
    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    @JsonProperty("dim")
    public String dim;
    /**
     * 消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;
    /**
     * 票张
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;
    /**
     * 全价票张
     */
    @JsonProperty("totalFullfaretkt")
    public Integer totalFullfaretkt;
    /**
     * 里程均价
     */
    @JsonProperty("avgTpmsPrice")
    public BigDecimal avgTpmsPrice;
    /**
     * 平均票价
     */
    @JsonProperty("avgPrice")
    public BigDecimal avgPrice;
    /**
     * 平均折扣
     */
    @JsonProperty("avgDiscount")
    public BigDecimal avgDiscount;
    /**
     * 全价票张占比
     */
    @JsonProperty("fullfaretktPercent")
    public BigDecimal fullfaretktPercent;
    /**
     * 节省金额
     */
    @JsonProperty("totalSaveAmount")
    public BigDecimal totalSaveAmount;
    /**
     * 节省率
     */
    @JsonProperty("saveRate")
    public BigDecimal saveRate;
    /**
     * 退票费
     */
    @JsonProperty("totalRefundFee")
    public BigDecimal totalRefundFee;
    /**
     * 退票率
     */
    @JsonProperty("refundRate")
    public BigDecimal refundRate;
    /**
     * 改签费
     */
    @JsonProperty("totalRebookFee")
    public BigDecimal totalRebookFee;
    /**
     * 改签率
     */
    @JsonProperty("rebookRate")
    public BigDecimal rebookRate;
    /**
     * rc次数占比
     */
    @JsonProperty("rcPercent")
    public BigDecimal rcPercent;
    /**
     * 超标损失
     */
    @JsonProperty("totalOverAmount")
    public BigDecimal totalOverAmount;
    /**
     * 三方节省率
     */
    @JsonProperty("saveAmount3cRate")
    public BigDecimal saveAmount3cRate;
    /**
     * 两方节省率
     */
    @JsonProperty("saveAmount2cRate")
    public BigDecimal saveAmount2cRate;
    /**
     * 管控节省率
     */
    @JsonProperty("controlSaveRate")
    public BigDecimal controlSaveRate;
    /**
     * 碳排放
     */
    @JsonProperty("totalCarbons")
    public BigDecimal totalCarbons;
    /**
     * 碳排放节省率
     */
    @JsonProperty("carbonSaveRate")
    public BigDecimal carbonSaveRate;
    /**
     * 潜在节省金额（退、改、RC）
     */
    @JsonProperty("totalSavePotential")
    public BigDecimal totalSavePotential;
    /**
     * Top航线
     */
    @JsonProperty("topFlightCity")
    public String topFlightCity;
    /**
     * 提前预订天数
     */
    @JsonProperty("avgPreOrderDate")
    public Integer avgPreOrderDate;
    /**
     * 里程碳排
     */
    @JsonProperty("avgTpmsCarbons")
    public BigDecimal avgTpmsCarbons;
    /**
     * 消费金额占比
     */
    @JsonProperty("amountPercent")
    public BigDecimal amountPercent;
    /**
     * 订单数
     */
    @JsonProperty("totalAllOrderCount")
    public Integer totalAllOrderCount;
    /**
     * RC订单数
     */
    @JsonProperty("totalRcTimes")
    public Integer totalRcTimes;

    public OnlineReportFltTopDeptConsume(
            String dimId,
            String dim,
            BigDecimal totalAmount,
            Integer totalQuantity,
            Integer totalFullfaretkt,
            BigDecimal avgTpmsPrice,
            BigDecimal avgPrice,
            BigDecimal avgDiscount,
            BigDecimal fullfaretktPercent,
            BigDecimal totalSaveAmount,
            BigDecimal saveRate,
            BigDecimal totalRefundFee,
            BigDecimal refundRate,
            BigDecimal totalRebookFee,
            BigDecimal rebookRate,
            BigDecimal rcPercent,
            BigDecimal totalOverAmount,
            BigDecimal saveAmount3cRate,
            BigDecimal saveAmount2cRate,
            BigDecimal controlSaveRate,
            BigDecimal totalCarbons,
            BigDecimal carbonSaveRate,
            BigDecimal totalSavePotential,
            String topFlightCity,
            Integer avgPreOrderDate,
            BigDecimal avgTpmsCarbons,
            BigDecimal amountPercent,
            Integer totalAllOrderCount,
            Integer totalRcTimes) {
        this.dimId = dimId;
        this.dim = dim;
        this.totalAmount = totalAmount;
        this.totalQuantity = totalQuantity;
        this.totalFullfaretkt = totalFullfaretkt;
        this.avgTpmsPrice = avgTpmsPrice;
        this.avgPrice = avgPrice;
        this.avgDiscount = avgDiscount;
        this.fullfaretktPercent = fullfaretktPercent;
        this.totalSaveAmount = totalSaveAmount;
        this.saveRate = saveRate;
        this.totalRefundFee = totalRefundFee;
        this.refundRate = refundRate;
        this.totalRebookFee = totalRebookFee;
        this.rebookRate = rebookRate;
        this.rcPercent = rcPercent;
        this.totalOverAmount = totalOverAmount;
        this.saveAmount3cRate = saveAmount3cRate;
        this.saveAmount2cRate = saveAmount2cRate;
        this.controlSaveRate = controlSaveRate;
        this.totalCarbons = totalCarbons;
        this.carbonSaveRate = carbonSaveRate;
        this.totalSavePotential = totalSavePotential;
        this.topFlightCity = topFlightCity;
        this.avgPreOrderDate = avgPreOrderDate;
        this.avgTpmsCarbons = avgTpmsCarbons;
        this.amountPercent = amountPercent;
        this.totalAllOrderCount = totalAllOrderCount;
        this.totalRcTimes = totalRcTimes;
    }

    public OnlineReportFltTopDeptConsume() {
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public String getDimId() {
        return dimId;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public void setDimId(final String dimId) {
        this.dimId = dimId;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public String getDim() {
        return dim;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 票张
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 票张
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 全价票张
     */
    public Integer getTotalFullfaretkt() {
        return totalFullfaretkt;
    }

    /**
     * 全价票张
     */
    public void setTotalFullfaretkt(final Integer totalFullfaretkt) {
        this.totalFullfaretkt = totalFullfaretkt;
    }

    /**
     * 里程均价
     */
    public BigDecimal getAvgTpmsPrice() {
        return avgTpmsPrice;
    }

    /**
     * 里程均价
     */
    public void setAvgTpmsPrice(final BigDecimal avgTpmsPrice) {
        this.avgTpmsPrice = avgTpmsPrice;
    }

    /**
     * 平均票价
     */
    public BigDecimal getAvgPrice() {
        return avgPrice;
    }

    /**
     * 平均票价
     */
    public void setAvgPrice(final BigDecimal avgPrice) {
        this.avgPrice = avgPrice;
    }

    /**
     * 平均折扣
     */
    public BigDecimal getAvgDiscount() {
        return avgDiscount;
    }

    /**
     * 平均折扣
     */
    public void setAvgDiscount(final BigDecimal avgDiscount) {
        this.avgDiscount = avgDiscount;
    }

    /**
     * 全价票张占比
     */
    public BigDecimal getFullfaretktPercent() {
        return fullfaretktPercent;
    }

    /**
     * 全价票张占比
     */
    public void setFullfaretktPercent(final BigDecimal fullfaretktPercent) {
        this.fullfaretktPercent = fullfaretktPercent;
    }

    /**
     * 节省金额
     */
    public BigDecimal getTotalSaveAmount() {
        return totalSaveAmount;
    }

    /**
     * 节省金额
     */
    public void setTotalSaveAmount(final BigDecimal totalSaveAmount) {
        this.totalSaveAmount = totalSaveAmount;
    }

    /**
     * 节省率
     */
    public BigDecimal getSaveRate() {
        return saveRate;
    }

    /**
     * 节省率
     */
    public void setSaveRate(final BigDecimal saveRate) {
        this.saveRate = saveRate;
    }

    /**
     * 退票费
     */
    public BigDecimal getTotalRefundFee() {
        return totalRefundFee;
    }

    /**
     * 退票费
     */
    public void setTotalRefundFee(final BigDecimal totalRefundFee) {
        this.totalRefundFee = totalRefundFee;
    }

    /**
     * 退票率
     */
    public BigDecimal getRefundRate() {
        return refundRate;
    }

    /**
     * 退票率
     */
    public void setRefundRate(final BigDecimal refundRate) {
        this.refundRate = refundRate;
    }

    /**
     * 改签费
     */
    public BigDecimal getTotalRebookFee() {
        return totalRebookFee;
    }

    /**
     * 改签费
     */
    public void setTotalRebookFee(final BigDecimal totalRebookFee) {
        this.totalRebookFee = totalRebookFee;
    }

    /**
     * 改签率
     */
    public BigDecimal getRebookRate() {
        return rebookRate;
    }

    /**
     * 改签率
     */
    public void setRebookRate(final BigDecimal rebookRate) {
        this.rebookRate = rebookRate;
    }

    /**
     * rc次数占比
     */
    public BigDecimal getRcPercent() {
        return rcPercent;
    }

    /**
     * rc次数占比
     */
    public void setRcPercent(final BigDecimal rcPercent) {
        this.rcPercent = rcPercent;
    }

    /**
     * 超标损失
     */
    public BigDecimal getTotalOverAmount() {
        return totalOverAmount;
    }

    /**
     * 超标损失
     */
    public void setTotalOverAmount(final BigDecimal totalOverAmount) {
        this.totalOverAmount = totalOverAmount;
    }

    /**
     * 三方节省率
     */
    public BigDecimal getSaveAmount3cRate() {
        return saveAmount3cRate;
    }

    /**
     * 三方节省率
     */
    public void setSaveAmount3cRate(final BigDecimal saveAmount3cRate) {
        this.saveAmount3cRate = saveAmount3cRate;
    }

    /**
     * 两方节省率
     */
    public BigDecimal getSaveAmount2cRate() {
        return saveAmount2cRate;
    }

    /**
     * 两方节省率
     */
    public void setSaveAmount2cRate(final BigDecimal saveAmount2cRate) {
        this.saveAmount2cRate = saveAmount2cRate;
    }

    /**
     * 管控节省率
     */
    public BigDecimal getControlSaveRate() {
        return controlSaveRate;
    }

    /**
     * 管控节省率
     */
    public void setControlSaveRate(final BigDecimal controlSaveRate) {
        this.controlSaveRate = controlSaveRate;
    }

    /**
     * 碳排放
     */
    public BigDecimal getTotalCarbons() {
        return totalCarbons;
    }

    /**
     * 碳排放
     */
    public void setTotalCarbons(final BigDecimal totalCarbons) {
        this.totalCarbons = totalCarbons;
    }

    /**
     * 碳排放节省率
     */
    public BigDecimal getCarbonSaveRate() {
        return carbonSaveRate;
    }

    /**
     * 碳排放节省率
     */
    public void setCarbonSaveRate(final BigDecimal carbonSaveRate) {
        this.carbonSaveRate = carbonSaveRate;
    }

    /**
     * 潜在节省金额（退、改、RC）
     */
    public BigDecimal getTotalSavePotential() {
        return totalSavePotential;
    }

    /**
     * 潜在节省金额（退、改、RC）
     */
    public void setTotalSavePotential(final BigDecimal totalSavePotential) {
        this.totalSavePotential = totalSavePotential;
    }

    /**
     * Top航线
     */
    public String getTopFlightCity() {
        return topFlightCity;
    }

    /**
     * Top航线
     */
    public void setTopFlightCity(final String topFlightCity) {
        this.topFlightCity = topFlightCity;
    }

    /**
     * 提前预订天数
     */
    public Integer getAvgPreOrderDate() {
        return avgPreOrderDate;
    }

    /**
     * 提前预订天数
     */
    public void setAvgPreOrderDate(final Integer avgPreOrderDate) {
        this.avgPreOrderDate = avgPreOrderDate;
    }

    /**
     * 里程碳排
     */
    public BigDecimal getAvgTpmsCarbons() {
        return avgTpmsCarbons;
    }

    /**
     * 里程碳排
     */
    public void setAvgTpmsCarbons(final BigDecimal avgTpmsCarbons) {
        this.avgTpmsCarbons = avgTpmsCarbons;
    }

    /**
     * 消费金额占比
     */
    public BigDecimal getAmountPercent() {
        return amountPercent;
    }

    /**
     * 消费金额占比
     */
    public void setAmountPercent(final BigDecimal amountPercent) {
        this.amountPercent = amountPercent;
    }

    /**
     * 订单数
     */
    public Integer getTotalAllOrderCount() {
        return totalAllOrderCount;
    }

    /**
     * 订单数
     */
    public void setTotalAllOrderCount(final Integer totalAllOrderCount) {
        this.totalAllOrderCount = totalAllOrderCount;
    }

    /**
     * RC订单数
     */
    public Integer getTotalRcTimes() {
        return totalRcTimes;
    }

    /**
     * RC订单数
     */
    public void setTotalRcTimes(final Integer totalRcTimes) {
        this.totalRcTimes = totalRcTimes;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.dimId;
            case 1:
                return this.dim;
            case 2:
                return this.totalAmount;
            case 3:
                return this.totalQuantity;
            case 4:
                return this.totalFullfaretkt;
            case 5:
                return this.avgTpmsPrice;
            case 6:
                return this.avgPrice;
            case 7:
                return this.avgDiscount;
            case 8:
                return this.fullfaretktPercent;
            case 9:
                return this.totalSaveAmount;
            case 10:
                return this.saveRate;
            case 11:
                return this.totalRefundFee;
            case 12:
                return this.refundRate;
            case 13:
                return this.totalRebookFee;
            case 14:
                return this.rebookRate;
            case 15:
                return this.rcPercent;
            case 16:
                return this.totalOverAmount;
            case 17:
                return this.saveAmount3cRate;
            case 18:
                return this.saveAmount2cRate;
            case 19:
                return this.controlSaveRate;
            case 20:
                return this.totalCarbons;
            case 21:
                return this.carbonSaveRate;
            case 22:
                return this.totalSavePotential;
            case 23:
                return this.topFlightCity;
            case 24:
                return this.avgPreOrderDate;
            case 25:
                return this.avgTpmsCarbons;
            case 26:
                return this.amountPercent;
            case 27:
                return this.totalAllOrderCount;
            case 28:
                return this.totalRcTimes;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.dimId = (String) fieldValue;
                break;
            case 1:
                this.dim = (String) fieldValue;
                break;
            case 2:
                this.totalAmount = (BigDecimal) fieldValue;
                break;
            case 3:
                this.totalQuantity = (Integer) fieldValue;
                break;
            case 4:
                this.totalFullfaretkt = (Integer) fieldValue;
                break;
            case 5:
                this.avgTpmsPrice = (BigDecimal) fieldValue;
                break;
            case 6:
                this.avgPrice = (BigDecimal) fieldValue;
                break;
            case 7:
                this.avgDiscount = (BigDecimal) fieldValue;
                break;
            case 8:
                this.fullfaretktPercent = (BigDecimal) fieldValue;
                break;
            case 9:
                this.totalSaveAmount = (BigDecimal) fieldValue;
                break;
            case 10:
                this.saveRate = (BigDecimal) fieldValue;
                break;
            case 11:
                this.totalRefundFee = (BigDecimal) fieldValue;
                break;
            case 12:
                this.refundRate = (BigDecimal) fieldValue;
                break;
            case 13:
                this.totalRebookFee = (BigDecimal) fieldValue;
                break;
            case 14:
                this.rebookRate = (BigDecimal) fieldValue;
                break;
            case 15:
                this.rcPercent = (BigDecimal) fieldValue;
                break;
            case 16:
                this.totalOverAmount = (BigDecimal) fieldValue;
                break;
            case 17:
                this.saveAmount3cRate = (BigDecimal) fieldValue;
                break;
            case 18:
                this.saveAmount2cRate = (BigDecimal) fieldValue;
                break;
            case 19:
                this.controlSaveRate = (BigDecimal) fieldValue;
                break;
            case 20:
                this.totalCarbons = (BigDecimal) fieldValue;
                break;
            case 21:
                this.carbonSaveRate = (BigDecimal) fieldValue;
                break;
            case 22:
                this.totalSavePotential = (BigDecimal) fieldValue;
                break;
            case 23:
                this.topFlightCity = (String) fieldValue;
                break;
            case 24:
                this.avgPreOrderDate = (Integer) fieldValue;
                break;
            case 25:
                this.avgTpmsCarbons = (BigDecimal) fieldValue;
                break;
            case 26:
                this.amountPercent = (BigDecimal) fieldValue;
                break;
            case 27:
                this.totalAllOrderCount = (Integer) fieldValue;
                break;
            case 28:
                this.totalRcTimes = (Integer) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportFltTopDeptConsume other = (OnlineReportFltTopDeptConsume) obj;
        return
                Objects.equal(this.dimId, other.dimId) &&
                        Objects.equal(this.dim, other.dim) &&
                        Objects.equal(this.totalAmount, other.totalAmount) &&
                        Objects.equal(this.totalQuantity, other.totalQuantity) &&
                        Objects.equal(this.totalFullfaretkt, other.totalFullfaretkt) &&
                        Objects.equal(this.avgTpmsPrice, other.avgTpmsPrice) &&
                        Objects.equal(this.avgPrice, other.avgPrice) &&
                        Objects.equal(this.avgDiscount, other.avgDiscount) &&
                        Objects.equal(this.fullfaretktPercent, other.fullfaretktPercent) &&
                        Objects.equal(this.totalSaveAmount, other.totalSaveAmount) &&
                        Objects.equal(this.saveRate, other.saveRate) &&
                        Objects.equal(this.totalRefundFee, other.totalRefundFee) &&
                        Objects.equal(this.refundRate, other.refundRate) &&
                        Objects.equal(this.totalRebookFee, other.totalRebookFee) &&
                        Objects.equal(this.rebookRate, other.rebookRate) &&
                        Objects.equal(this.rcPercent, other.rcPercent) &&
                        Objects.equal(this.totalOverAmount, other.totalOverAmount) &&
                        Objects.equal(this.saveAmount3cRate, other.saveAmount3cRate) &&
                        Objects.equal(this.saveAmount2cRate, other.saveAmount2cRate) &&
                        Objects.equal(this.controlSaveRate, other.controlSaveRate) &&
                        Objects.equal(this.totalCarbons, other.totalCarbons) &&
                        Objects.equal(this.carbonSaveRate, other.carbonSaveRate) &&
                        Objects.equal(this.totalSavePotential, other.totalSavePotential) &&
                        Objects.equal(this.topFlightCity, other.topFlightCity) &&
                        Objects.equal(this.avgPreOrderDate, other.avgPreOrderDate) &&
                        Objects.equal(this.avgTpmsCarbons, other.avgTpmsCarbons) &&
                        Objects.equal(this.amountPercent, other.amountPercent) &&
                        Objects.equal(this.totalAllOrderCount, other.totalAllOrderCount) &&
                        Objects.equal(this.totalRcTimes, other.totalRcTimes);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dimId == null ? 0 : this.dimId.hashCode());
        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.totalFullfaretkt == null ? 0 : this.totalFullfaretkt.hashCode());
        result = 31 * result + (this.avgTpmsPrice == null ? 0 : this.avgTpmsPrice.hashCode());
        result = 31 * result + (this.avgPrice == null ? 0 : this.avgPrice.hashCode());
        result = 31 * result + (this.avgDiscount == null ? 0 : this.avgDiscount.hashCode());
        result = 31 * result + (this.fullfaretktPercent == null ? 0 : this.fullfaretktPercent.hashCode());
        result = 31 * result + (this.totalSaveAmount == null ? 0 : this.totalSaveAmount.hashCode());
        result = 31 * result + (this.saveRate == null ? 0 : this.saveRate.hashCode());
        result = 31 * result + (this.totalRefundFee == null ? 0 : this.totalRefundFee.hashCode());
        result = 31 * result + (this.refundRate == null ? 0 : this.refundRate.hashCode());
        result = 31 * result + (this.totalRebookFee == null ? 0 : this.totalRebookFee.hashCode());
        result = 31 * result + (this.rebookRate == null ? 0 : this.rebookRate.hashCode());
        result = 31 * result + (this.rcPercent == null ? 0 : this.rcPercent.hashCode());
        result = 31 * result + (this.totalOverAmount == null ? 0 : this.totalOverAmount.hashCode());
        result = 31 * result + (this.saveAmount3cRate == null ? 0 : this.saveAmount3cRate.hashCode());
        result = 31 * result + (this.saveAmount2cRate == null ? 0 : this.saveAmount2cRate.hashCode());
        result = 31 * result + (this.controlSaveRate == null ? 0 : this.controlSaveRate.hashCode());
        result = 31 * result + (this.totalCarbons == null ? 0 : this.totalCarbons.hashCode());
        result = 31 * result + (this.carbonSaveRate == null ? 0 : this.carbonSaveRate.hashCode());
        result = 31 * result + (this.totalSavePotential == null ? 0 : this.totalSavePotential.hashCode());
        result = 31 * result + (this.topFlightCity == null ? 0 : this.topFlightCity.hashCode());
        result = 31 * result + (this.avgPreOrderDate == null ? 0 : this.avgPreOrderDate.hashCode());
        result = 31 * result + (this.avgTpmsCarbons == null ? 0 : this.avgTpmsCarbons.hashCode());
        result = 31 * result + (this.amountPercent == null ? 0 : this.amountPercent.hashCode());
        result = 31 * result + (this.totalAllOrderCount == null ? 0 : this.totalAllOrderCount.hashCode());
        result = 31 * result + (this.totalRcTimes == null ? 0 : this.totalRcTimes.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("dimId", dimId)
                .add("dim", dim)
                .add("totalAmount", totalAmount)
                .add("totalQuantity", totalQuantity)
                .add("totalFullfaretkt", totalFullfaretkt)
                .add("avgTpmsPrice", avgTpmsPrice)
                .add("avgPrice", avgPrice)
                .add("avgDiscount", avgDiscount)
                .add("fullfaretktPercent", fullfaretktPercent)
                .add("totalSaveAmount", totalSaveAmount)
                .add("saveRate", saveRate)
                .add("totalRefundFee", totalRefundFee)
                .add("refundRate", refundRate)
                .add("totalRebookFee", totalRebookFee)
                .add("rebookRate", rebookRate)
                .add("rcPercent", rcPercent)
                .add("totalOverAmount", totalOverAmount)
                .add("saveAmount3cRate", saveAmount3cRate)
                .add("saveAmount2cRate", saveAmount2cRate)
                .add("controlSaveRate", controlSaveRate)
                .add("totalCarbons", totalCarbons)
                .add("carbonSaveRate", carbonSaveRate)
                .add("totalSavePotential", totalSavePotential)
                .add("topFlightCity", topFlightCity)
                .add("avgPreOrderDate", avgPreOrderDate)
                .add("avgTpmsCarbons", avgTpmsCarbons)
                .add("amountPercent", amountPercent)
                .add("totalAllOrderCount", totalAllOrderCount)
                .add("totalRcTimes", totalRcTimes)
                .toString();
    }
}
