package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 酒店-间夜均价分布
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "title",
    "roomNightNotice",
    "legends",
    "data"
})
public class HotelRoomNightDist implements Serializable {
    private static final long serialVersionUID = 1L;





    public HotelRoomNightDist(
        String title,
        String roomNightNotice,
        List<OnlineReportTrendLegend> legends,
        List<OnlineReportTrendPoint> data) {
        this.title = title;
        this.roomNightNotice = roomNightNotice;
        this.legends = legends;
        this.data = data;
    }

    public HotelRoomNightDist() {
    }

    /**
     * 标题
     */
    @JsonProperty("title")
    public String title;

    /**
     * 文案
     */
    @JsonProperty("roomNightNotice")
    public String roomNightNotice;

    @JsonProperty("legends")
    public List<OnlineReportTrendLegend> legends;

    @JsonProperty("data")
    public List<OnlineReportTrendPoint> data;

    /**
     * 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 标题
     */
    public void setTitle(final String title) {
        this.title = title;
    }

    /**
     * 文案
     */
    public String getRoomNightNotice() {
        return roomNightNotice;
    }

    /**
     * 文案
     */
    public void setRoomNightNotice(final String roomNightNotice) {
        this.roomNightNotice = roomNightNotice;
    }
    public List<OnlineReportTrendLegend> getLegends() {
        return legends;
    }

    public void setLegends(final List<OnlineReportTrendLegend> legends) {
        this.legends = legends;
    }
    public List<OnlineReportTrendPoint> getData() {
        return data;
    }

    public void setData(final List<OnlineReportTrendPoint> data) {
        this.data = data;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final HotelRoomNightDist other = (HotelRoomNightDist)obj;
        return
            Objects.equal(this.title, other.title) &&
            Objects.equal(this.roomNightNotice, other.roomNightNotice) &&
            Objects.equal(this.legends, other.legends) &&
            Objects.equal(this.data, other.data);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.title == null ? 0 : this.title.hashCode());
        result = 31 * result + (this.roomNightNotice == null ? 0 : this.roomNightNotice.hashCode());
        result = 31 * result + (this.legends == null ? 0 : this.legends.hashCode());
        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("title", title)
            .add("roomNightNotice", roomNightNotice)
            .add("legends", legends)
            .add("data", data)
            .toString();
    }
}
