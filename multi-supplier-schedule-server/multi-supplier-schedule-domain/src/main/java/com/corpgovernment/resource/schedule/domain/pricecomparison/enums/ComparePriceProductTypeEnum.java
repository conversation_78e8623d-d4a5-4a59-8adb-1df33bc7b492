package com.corpgovernment.resource.schedule.domain.pricecomparison.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
@ToString
public enum ComparePriceProductTypeEnum {
    HOTEL(1, "HOTEL","酒店比价"),
    FLIGHT(2, "FLIGHT","机票比价"),
    FLIGHT_TOP(21, "FLIGHT_TOP","机票热门航线比价"),
    ;

    private final Integer code;
    private final String name;
    private final String desc;

    private static final Map<Integer, String> CODE_NAME_MAP = new HashMap<>();
    static {
        for (ComparePriceProductTypeEnum type : values()) {
            CODE_NAME_MAP.put(type.getCode(), type.getName());
        }
    }

    public static String getNameByCode(Integer code) {
        return code != null ? CODE_NAME_MAP.get(code) : null;
    }
}
