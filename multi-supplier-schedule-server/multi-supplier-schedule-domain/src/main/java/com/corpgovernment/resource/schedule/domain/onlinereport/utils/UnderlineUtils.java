package com.corpgovernment.resource.schedule.domain.onlinereport.utils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * @Auther: ddzhan
 * @Date: 2019/4/1 15:16
 * @Description: 驼峰工具类
 */
public class UnderlineUtils {

    public static final char UNDERLINE = '_';


    public static Map<String, String> toReplaceKeyLow(Map<String, String> map) {
        Map reMap = new HashMap();
        if (reMap != null) {
            Iterator var2 = map.entrySet().iterator();

            while (var2.hasNext()) {
                Map.Entry<String, String> entry = (Map.Entry<String, String>) var2.next();
                reMap.put(underlineToCamel((String) entry.getKey()), map.get(entry.getKey()));
            }

            map.clear();
        }

        return reMap;
    }

    /**
     * 下划线格式字符串转换为驼峰格式字符串
     *
     * @param param
     * @return
     */
    public static String underlineToCamel(String param) {
        if (param == null || "".equals(param.trim())) {
            return "";
        }
        int len = param.length();
        StringBuilder sb = new StringBuilder(len);
        for (int i = 0; i < len; i++) {
            char c = param.charAt(i);
            if (c == UNDERLINE) {
                if (++i < len) {
                    sb.append(Character.toUpperCase(param.charAt(i)));
                }
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

}
