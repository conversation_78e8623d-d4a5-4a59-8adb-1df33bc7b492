package com.corpgovernment.resource.schedule.domain.onlinereport.utils;

import org.apache.commons.beanutils.BeanUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ObjectMapUtils {

    private ObjectMapUtils() {

    }

    /**
     * 实现简单的实体映射
     * 不适用场景： 1. 内部类
     *
     * @param source 数据源
     * @param clazz  转换类型
     * @param <T2>   转换类型
     * @param <T1>   数据源类型
     * @return 映射的目标类型数据
     * @throws IllegalAccessException IllegalAccessException
     * @throws InstantiationException InstantiationException
     */
    public static <T2 extends Object, T1 extends Object> T2 map(T1 source, Class<T2> clazz) throws IllegalAccessException, InstantiationException {
        if (source == null) {
            return null;
        }

        T2 target = clazz.newInstance();

        for (Field t1Field : source.getClass().getDeclaredFields()) {
            String fieldName = t1Field.getName();
            t1Field.setAccessible(true);

            try {
                Field t2Field = clazz.getDeclaredField(fieldName);

                if (null != t2Field) {
                    t2Field.setAccessible(true);
                    t2Field.set(target, t1Field.get(source));
                }
            } catch (NoSuchFieldException ex) {
                System.out.println(ex);
            }

        }

        return target;
    }

    /**
     * 构建结果
     *
     * @param clazz 类型
     * @return 构建结果
     */
    public static <T> List<T> buildMapToEntity(final Class<T> clazz, List<Map<String, String>> resList) {
        T bean = null;
        List list = new ArrayList();
        try {
            for (Map<String, String> map : resList) {
                bean = clazz.newInstance();
                Map entityMap = UnderlineUtils.toReplaceKeyLow(map);
                BeanUtils.copyProperties(bean, entityMap);
                list.add(bean);
            }
        } catch (Exception e) {
        }
        return list;
    }
}
