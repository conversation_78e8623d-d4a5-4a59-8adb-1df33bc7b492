package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 心程贝部门消费明细
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "dimId",
        "dim",
        "allotAmount",
        "allotAmountPercent",
        "returnAmount",
        "returnAmountPercent",
        "refundAmount",
        "refundAmountPercent",
        "deductAmount",
        "deductAmountPercent",
        "totalBalance"
})
public class OnlineReportWelfareTopDeptConsume implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 分析对象：公司|主账户|成本中心|部门｜用户｜组织
     */
    @JsonProperty("dimId")
    public String dimId;
    /**
     * 分析对象：公司|主账户|成本中心|部门｜用户｜组织
     */
    @JsonProperty("dim")
    public String dim;
    /**
     * 心程贝发放金额
     */
    @JsonProperty("allotAmount")
    public BigDecimal allotAmount;
    /**
     * 心程贝发放金额占比
     */
    @JsonProperty("allotAmountPercent")
    public BigDecimal allotAmountPercent;
    /**
     * 心程贝回收金额
     */
    @JsonProperty("returnAmount")
    public BigDecimal returnAmount;
    /**
     * 心程贝回收金额占比
     */
    @JsonProperty("returnAmountPercent")
    public BigDecimal returnAmountPercent;
    /**
     * 心程贝退还金额
     */
    @JsonProperty("refundAmount")
    public BigDecimal refundAmount;
    /**
     * 心程贝退还金额占比
     */
    @JsonProperty("refundAmountPercent")
    public BigDecimal refundAmountPercent;
    /**
     * 心程贝扣减金额
     */
    @JsonProperty("deductAmount")
    public BigDecimal deductAmount;
    /**
     * 心程贝扣减金额占比
     */
    @JsonProperty("deductAmountPercent")
    public BigDecimal deductAmountPercent;
    /**
     * 发放余额
     */
    @JsonProperty("totalBalance")
    public BigDecimal totalBalance;

    public OnlineReportWelfareTopDeptConsume(
            String dimId,
            String dim,
            BigDecimal allotAmount,
            BigDecimal allotAmountPercent,
            BigDecimal returnAmount,
            BigDecimal returnAmountPercent,
            BigDecimal refundAmount,
            BigDecimal refundAmountPercent,
            BigDecimal deductAmount,
            BigDecimal deductAmountPercent,
            BigDecimal totalBalance) {
        this.dimId = dimId;
        this.dim = dim;
        this.allotAmount = allotAmount;
        this.allotAmountPercent = allotAmountPercent;
        this.returnAmount = returnAmount;
        this.returnAmountPercent = returnAmountPercent;
        this.refundAmount = refundAmount;
        this.refundAmountPercent = refundAmountPercent;
        this.deductAmount = deductAmount;
        this.deductAmountPercent = deductAmountPercent;
        this.totalBalance = totalBalance;
    }

    public OnlineReportWelfareTopDeptConsume() {
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门｜用户｜组织
     */
    public String getDimId() {
        return dimId;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门｜用户｜组织
     */
    public void setDimId(final String dimId) {
        this.dimId = dimId;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门｜用户｜组织
     */
    public String getDim() {
        return dim;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门｜用户｜组织
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 心程贝发放金额
     */
    public BigDecimal getAllotAmount() {
        return allotAmount;
    }

    /**
     * 心程贝发放金额
     */
    public void setAllotAmount(final BigDecimal allotAmount) {
        this.allotAmount = allotAmount;
    }

    /**
     * 心程贝发放金额占比
     */
    public BigDecimal getAllotAmountPercent() {
        return allotAmountPercent;
    }

    /**
     * 心程贝发放金额占比
     */
    public void setAllotAmountPercent(final BigDecimal allotAmountPercent) {
        this.allotAmountPercent = allotAmountPercent;
    }

    /**
     * 心程贝回收金额
     */
    public BigDecimal getReturnAmount() {
        return returnAmount;
    }

    /**
     * 心程贝回收金额
     */
    public void setReturnAmount(final BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }

    /**
     * 心程贝回收金额占比
     */
    public BigDecimal getReturnAmountPercent() {
        return returnAmountPercent;
    }

    /**
     * 心程贝回收金额占比
     */
    public void setReturnAmountPercent(final BigDecimal returnAmountPercent) {
        this.returnAmountPercent = returnAmountPercent;
    }

    /**
     * 心程贝退还金额
     */
    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    /**
     * 心程贝退还金额
     */
    public void setRefundAmount(final BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    /**
     * 心程贝退还金额占比
     */
    public BigDecimal getRefundAmountPercent() {
        return refundAmountPercent;
    }

    /**
     * 心程贝退还金额占比
     */
    public void setRefundAmountPercent(final BigDecimal refundAmountPercent) {
        this.refundAmountPercent = refundAmountPercent;
    }

    /**
     * 心程贝扣减金额
     */
    public BigDecimal getDeductAmount() {
        return deductAmount;
    }

    /**
     * 心程贝扣减金额
     */
    public void setDeductAmount(final BigDecimal deductAmount) {
        this.deductAmount = deductAmount;
    }

    /**
     * 心程贝扣减金额占比
     */
    public BigDecimal getDeductAmountPercent() {
        return deductAmountPercent;
    }

    /**
     * 心程贝扣减金额占比
     */
    public void setDeductAmountPercent(final BigDecimal deductAmountPercent) {
        this.deductAmountPercent = deductAmountPercent;
    }

    /**
     * 发放余额
     */
    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    /**
     * 发放余额
     */
    public void setTotalBalance(final BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.dimId;
            case 1:
                return this.dim;
            case 2:
                return this.allotAmount;
            case 3:
                return this.allotAmountPercent;
            case 4:
                return this.returnAmount;
            case 5:
                return this.returnAmountPercent;
            case 6:
                return this.refundAmount;
            case 7:
                return this.refundAmountPercent;
            case 8:
                return this.deductAmount;
            case 9:
                return this.deductAmountPercent;
            case 10:
                return this.totalBalance;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.dimId = (String) fieldValue;
                break;
            case 1:
                this.dim = (String) fieldValue;
                break;
            case 2:
                this.allotAmount = (BigDecimal) fieldValue;
                break;
            case 3:
                this.allotAmountPercent = (BigDecimal) fieldValue;
                break;
            case 4:
                this.returnAmount = (BigDecimal) fieldValue;
                break;
            case 5:
                this.returnAmountPercent = (BigDecimal) fieldValue;
                break;
            case 6:
                this.refundAmount = (BigDecimal) fieldValue;
                break;
            case 7:
                this.refundAmountPercent = (BigDecimal) fieldValue;
                break;
            case 8:
                this.deductAmount = (BigDecimal) fieldValue;
                break;
            case 9:
                this.deductAmountPercent = (BigDecimal) fieldValue;
                break;
            case 10:
                this.totalBalance = (BigDecimal) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportWelfareTopDeptConsume other = (OnlineReportWelfareTopDeptConsume) obj;
        return
                Objects.equal(this.dimId, other.dimId) &&
                        Objects.equal(this.dim, other.dim) &&
                        Objects.equal(this.allotAmount, other.allotAmount) &&
                        Objects.equal(this.allotAmountPercent, other.allotAmountPercent) &&
                        Objects.equal(this.returnAmount, other.returnAmount) &&
                        Objects.equal(this.returnAmountPercent, other.returnAmountPercent) &&
                        Objects.equal(this.refundAmount, other.refundAmount) &&
                        Objects.equal(this.refundAmountPercent, other.refundAmountPercent) &&
                        Objects.equal(this.deductAmount, other.deductAmount) &&
                        Objects.equal(this.deductAmountPercent, other.deductAmountPercent) &&
                        Objects.equal(this.totalBalance, other.totalBalance);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dimId == null ? 0 : this.dimId.hashCode());
        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.allotAmount == null ? 0 : this.allotAmount.hashCode());
        result = 31 * result + (this.allotAmountPercent == null ? 0 : this.allotAmountPercent.hashCode());
        result = 31 * result + (this.returnAmount == null ? 0 : this.returnAmount.hashCode());
        result = 31 * result + (this.returnAmountPercent == null ? 0 : this.returnAmountPercent.hashCode());
        result = 31 * result + (this.refundAmount == null ? 0 : this.refundAmount.hashCode());
        result = 31 * result + (this.refundAmountPercent == null ? 0 : this.refundAmountPercent.hashCode());
        result = 31 * result + (this.deductAmount == null ? 0 : this.deductAmount.hashCode());
        result = 31 * result + (this.deductAmountPercent == null ? 0 : this.deductAmountPercent.hashCode());
        result = 31 * result + (this.totalBalance == null ? 0 : this.totalBalance.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("dimId", dimId)
                .add("dim", dim)
                .add("allotAmount", allotAmount)
                .add("allotAmountPercent", allotAmountPercent)
                .add("returnAmount", returnAmount)
                .add("returnAmountPercent", returnAmountPercent)
                .add("refundAmount", refundAmount)
                .add("refundAmountPercent", refundAmountPercent)
                .add("deductAmount", deductAmount)
                .add("deductAmountPercent", deductAmountPercent)
                .add("totalBalance", totalBalance)
                .toString();
    }
}
