package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机票风险订单
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "orderId",
    "warningLevel",
    "orderStatus",
    "ticketStatus",
    "segmentStatus",
    "uid",
    "uidName",
    "passengerName",
    "corporationName",
    "ticketNo",
    "segment",
    "flightNo",
    "takeOffTime",
    "quantity",
    "price",
    "operator",
    "operatorTime",
    "operateStatus",
    "uidIsvalid",
    "passengerIsvalid",
    "journeyNo",
    "operatorMark",
    "accountId",
    "dept1",
    "dept2",
    "dept3",
    "dept4",
    "dept5",
    "dept6",
    "dept7",
    "dept8",
    "dept9",
    "dept10",
    "costcenter1",
    "costcenter2",
    "costcenter3",
    "costcenter4",
    "costcenter5",
    "costcenter6",
    "orderDate",
    "airlineName",
    "departurePortName",
    "arrivalDateTime",
    "arrivalPortName",
    "riskType",
    "reasonType"
})
public class FlightRiskOrderDetail implements Serializable {
    private static final long serialVersionUID = 1L;





    public FlightRiskOrderDetail(
        String orderId,
        String warningLevel,
        String orderStatus,
        String ticketStatus,
        String segmentStatus,
        String uid,
        String uidName,
        String passengerName,
        String corporationName,
        String ticketNo,
        String segment,
        String flightNo,
        String takeOffTime,
        Integer quantity,
        BigDecimal price,
        String operator,
        String operatorTime,
        Integer operateStatus,
        String uidIsvalid,
        String passengerIsvalid,
        String journeyNo,
        String operatorMark,
        String accountId,
        String dept1,
        String dept2,
        String dept3,
        String dept4,
        String dept5,
        String dept6,
        String dept7,
        String dept8,
        String dept9,
        String dept10,
        String costcenter1,
        String costcenter2,
        String costcenter3,
        String costcenter4,
        String costcenter5,
        String costcenter6,
        String orderDate,
        String airlineName,
        String departurePortName,
        String arrivalDateTime,
        String arrivalPortName,
        String riskType,
        String reasonType) {
        this.orderId = orderId;
        this.warningLevel = warningLevel;
        this.orderStatus = orderStatus;
        this.ticketStatus = ticketStatus;
        this.segmentStatus = segmentStatus;
        this.uid = uid;
        this.uidName = uidName;
        this.passengerName = passengerName;
        this.corporationName = corporationName;
        this.ticketNo = ticketNo;
        this.segment = segment;
        this.flightNo = flightNo;
        this.takeOffTime = takeOffTime;
        this.quantity = quantity;
        this.price = price;
        this.operator = operator;
        this.operatorTime = operatorTime;
        this.operateStatus = operateStatus;
        this.uidIsvalid = uidIsvalid;
        this.passengerIsvalid = passengerIsvalid;
        this.journeyNo = journeyNo;
        this.operatorMark = operatorMark;
        this.accountId = accountId;
        this.dept1 = dept1;
        this.dept2 = dept2;
        this.dept3 = dept3;
        this.dept4 = dept4;
        this.dept5 = dept5;
        this.dept6 = dept6;
        this.dept7 = dept7;
        this.dept8 = dept8;
        this.dept9 = dept9;
        this.dept10 = dept10;
        this.costcenter1 = costcenter1;
        this.costcenter2 = costcenter2;
        this.costcenter3 = costcenter3;
        this.costcenter4 = costcenter4;
        this.costcenter5 = costcenter5;
        this.costcenter6 = costcenter6;
        this.orderDate = orderDate;
        this.airlineName = airlineName;
        this.departurePortName = departurePortName;
        this.arrivalDateTime = arrivalDateTime;
        this.arrivalPortName = arrivalPortName;
        this.riskType = riskType;
        this.reasonType = reasonType;
    }

    public FlightRiskOrderDetail() {
    }

    /**
     * 订单号
     */
    @JsonProperty("orderId")
    public String orderId;

    /**
     * 风险等级
     */
    @JsonProperty("warningLevel")
    public String warningLevel;

    /**
     * 订单状态
     */
    @JsonProperty("orderStatus")
    public String orderStatus;

    /**
     * 票张状态
     */
    @JsonProperty("ticketStatus")
    public String ticketStatus;

    /**
     * 航段状态
     */
    @JsonProperty("segmentStatus")
    public String segmentStatus;

    /**
     * uid
     */
    @JsonProperty("uid")
    public String uid;

    /**
     * 持卡人
     */
    @JsonProperty("uidName")
    public String uidName;

    /**
     * 乘客姓名
     */
    @JsonProperty("passengerName")
    public String passengerName;

    /**
     * 公司名称
     */
    @JsonProperty("corporationName")
    public String corporationName;

    /**
     * 票号
     */
    @JsonProperty("ticketNo")
    public String ticketNo;

    /**
     * 航段
     */
    @JsonProperty("segment")
    public String segment;

    /**
     * 航班号
     */
    @JsonProperty("flightNo")
    public String flightNo;

    /**
     * 起飞时间
     */
    @JsonProperty("takeOffTime")
    public String takeOffTime;

    /**
     * 票张
     */
    @JsonProperty("quantity")
    public Integer quantity;

    /**
     * 成交净价（含改签差价）
     */
    @JsonProperty("price")
    public BigDecimal price;

    /**
     * 操作人
     */
    @JsonProperty("operator")
    public String operator;

    /**
     * 操作时间
     */
    @JsonProperty("operatorTime")
    public String operatorTime;

    /**
     * 操作状态
     */
    @JsonProperty("operateStatus")
    public Integer operateStatus;

    /**
     * 预订人是否在职
     */
    @JsonProperty("uidIsvalid")
    public String uidIsvalid;

    /**
     * 乘机人是否在职
     */
    @JsonProperty("passengerIsvalid")
    public String passengerIsvalid;

    /**
     * 关联行程号
     */
    @JsonProperty("journeyNo")
    public String journeyNo;

    /**
     * 操作说明
     */
    @JsonProperty("operatorMark")
    public String operatorMark;

    /**
     * 主账户id
     */
    @JsonProperty("accountId")
    public String accountId;

    /**
     * 部门1
     */
    @JsonProperty("dept1")
    public String dept1;

    /**
     * 部门2
     */
    @JsonProperty("dept2")
    public String dept2;

    /**
     * 部门3
     */
    @JsonProperty("dept3")
    public String dept3;

    /**
     * 部门4
     */
    @JsonProperty("dept4")
    public String dept4;

    /**
     * 部门5
     */
    @JsonProperty("dept5")
    public String dept5;

    /**
     * 部门6
     */
    @JsonProperty("dept6")
    public String dept6;

    /**
     * 部门7
     */
    @JsonProperty("dept7")
    public String dept7;

    /**
     * 部门8
     */
    @JsonProperty("dept8")
    public String dept8;

    /**
     * 部门9
     */
    @JsonProperty("dept9")
    public String dept9;

    /**
     * 部门10
     */
    @JsonProperty("dept10")
    public String dept10;

    /**
     * 成本中心1
     */
    @JsonProperty("costcenter1")
    public String costcenter1;

    /**
     * 成本中心2
     */
    @JsonProperty("costcenter2")
    public String costcenter2;

    /**
     * 成本中心3
     */
    @JsonProperty("costcenter3")
    public String costcenter3;

    /**
     * 成本中心4
     */
    @JsonProperty("costcenter4")
    public String costcenter4;

    /**
     * 成本中心5
     */
    @JsonProperty("costcenter5")
    public String costcenter5;

    /**
     * 成本中心6
     */
    @JsonProperty("costcenter6")
    public String costcenter6;

    /**
     * 预订时间
     */
    @JsonProperty("orderDate")
    public String orderDate;

    /**
     * 航司
     */
    @JsonProperty("airlineName")
    public String airlineName;

    /**
     * 起飞机场
     */
    @JsonProperty("departurePortName")
    public String departurePortName;

    /**
     * 到达时间
     */
    @JsonProperty("arrivalDateTime")
    public String arrivalDateTime;

    /**
     * 到达机场
     */
    @JsonProperty("arrivalPortName")
    public String arrivalPortName;

    /**
     * 具体原因
     */
    @JsonProperty("riskType")
    public String riskType;

    /**
     * 疑似异常原因
     */
    @JsonProperty("reasonType")
    public String reasonType;

    /**
     * 订单号
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 订单号
     */
    public void setOrderId(final String orderId) {
        this.orderId = orderId;
    }

    /**
     * 风险等级
     */
    public String getWarningLevel() {
        return warningLevel;
    }

    /**
     * 风险等级
     */
    public void setWarningLevel(final String warningLevel) {
        this.warningLevel = warningLevel;
    }

    /**
     * 订单状态
     */
    public String getOrderStatus() {
        return orderStatus;
    }

    /**
     * 订单状态
     */
    public void setOrderStatus(final String orderStatus) {
        this.orderStatus = orderStatus;
    }

    /**
     * 票张状态
     */
    public String getTicketStatus() {
        return ticketStatus;
    }

    /**
     * 票张状态
     */
    public void setTicketStatus(final String ticketStatus) {
        this.ticketStatus = ticketStatus;
    }

    /**
     * 航段状态
     */
    public String getSegmentStatus() {
        return segmentStatus;
    }

    /**
     * 航段状态
     */
    public void setSegmentStatus(final String segmentStatus) {
        this.segmentStatus = segmentStatus;
    }

    /**
     * uid
     */
    public String getUid() {
        return uid;
    }

    /**
     * uid
     */
    public void setUid(final String uid) {
        this.uid = uid;
    }

    /**
     * 持卡人
     */
    public String getUidName() {
        return uidName;
    }

    /**
     * 持卡人
     */
    public void setUidName(final String uidName) {
        this.uidName = uidName;
    }

    /**
     * 乘客姓名
     */
    public String getPassengerName() {
        return passengerName;
    }

    /**
     * 乘客姓名
     */
    public void setPassengerName(final String passengerName) {
        this.passengerName = passengerName;
    }

    /**
     * 公司名称
     */
    public String getCorporationName() {
        return corporationName;
    }

    /**
     * 公司名称
     */
    public void setCorporationName(final String corporationName) {
        this.corporationName = corporationName;
    }

    /**
     * 票号
     */
    public String getTicketNo() {
        return ticketNo;
    }

    /**
     * 票号
     */
    public void setTicketNo(final String ticketNo) {
        this.ticketNo = ticketNo;
    }

    /**
     * 航段
     */
    public String getSegment() {
        return segment;
    }

    /**
     * 航段
     */
    public void setSegment(final String segment) {
        this.segment = segment;
    }

    /**
     * 航班号
     */
    public String getFlightNo() {
        return flightNo;
    }

    /**
     * 航班号
     */
    public void setFlightNo(final String flightNo) {
        this.flightNo = flightNo;
    }

    /**
     * 起飞时间
     */
    public String getTakeOffTime() {
        return takeOffTime;
    }

    /**
     * 起飞时间
     */
    public void setTakeOffTime(final String takeOffTime) {
        this.takeOffTime = takeOffTime;
    }

    /**
     * 票张
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * 票张
     */
    public void setQuantity(final Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * 成交净价（含改签差价）
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 成交净价（含改签差价）
     */
    public void setPrice(final BigDecimal price) {
        this.price = price;
    }

    /**
     * 操作人
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作人
     */
    public void setOperator(final String operator) {
        this.operator = operator;
    }

    /**
     * 操作时间
     */
    public String getOperatorTime() {
        return operatorTime;
    }

    /**
     * 操作时间
     */
    public void setOperatorTime(final String operatorTime) {
        this.operatorTime = operatorTime;
    }

    /**
     * 操作状态
     */
    public Integer getOperateStatus() {
        return operateStatus;
    }

    /**
     * 操作状态
     */
    public void setOperateStatus(final Integer operateStatus) {
        this.operateStatus = operateStatus;
    }

    /**
     * 预订人是否在职
     */
    public String getUidIsvalid() {
        return uidIsvalid;
    }

    /**
     * 预订人是否在职
     */
    public void setUidIsvalid(final String uidIsvalid) {
        this.uidIsvalid = uidIsvalid;
    }

    /**
     * 乘机人是否在职
     */
    public String getPassengerIsvalid() {
        return passengerIsvalid;
    }

    /**
     * 乘机人是否在职
     */
    public void setPassengerIsvalid(final String passengerIsvalid) {
        this.passengerIsvalid = passengerIsvalid;
    }

    /**
     * 关联行程号
     */
    public String getJourneyNo() {
        return journeyNo;
    }

    /**
     * 关联行程号
     */
    public void setJourneyNo(final String journeyNo) {
        this.journeyNo = journeyNo;
    }

    /**
     * 操作说明
     */
    public String getOperatorMark() {
        return operatorMark;
    }

    /**
     * 操作说明
     */
    public void setOperatorMark(final String operatorMark) {
        this.operatorMark = operatorMark;
    }

    /**
     * 主账户id
     */
    public String getAccountId() {
        return accountId;
    }

    /**
     * 主账户id
     */
    public void setAccountId(final String accountId) {
        this.accountId = accountId;
    }

    /**
     * 部门1
     */
    public String getDept1() {
        return dept1;
    }

    /**
     * 部门1
     */
    public void setDept1(final String dept1) {
        this.dept1 = dept1;
    }

    /**
     * 部门2
     */
    public String getDept2() {
        return dept2;
    }

    /**
     * 部门2
     */
    public void setDept2(final String dept2) {
        this.dept2 = dept2;
    }

    /**
     * 部门3
     */
    public String getDept3() {
        return dept3;
    }

    /**
     * 部门3
     */
    public void setDept3(final String dept3) {
        this.dept3 = dept3;
    }

    /**
     * 部门4
     */
    public String getDept4() {
        return dept4;
    }

    /**
     * 部门4
     */
    public void setDept4(final String dept4) {
        this.dept4 = dept4;
    }

    /**
     * 部门5
     */
    public String getDept5() {
        return dept5;
    }

    /**
     * 部门5
     */
    public void setDept5(final String dept5) {
        this.dept5 = dept5;
    }

    /**
     * 部门6
     */
    public String getDept6() {
        return dept6;
    }

    /**
     * 部门6
     */
    public void setDept6(final String dept6) {
        this.dept6 = dept6;
    }

    /**
     * 部门7
     */
    public String getDept7() {
        return dept7;
    }

    /**
     * 部门7
     */
    public void setDept7(final String dept7) {
        this.dept7 = dept7;
    }

    /**
     * 部门8
     */
    public String getDept8() {
        return dept8;
    }

    /**
     * 部门8
     */
    public void setDept8(final String dept8) {
        this.dept8 = dept8;
    }

    /**
     * 部门9
     */
    public String getDept9() {
        return dept9;
    }

    /**
     * 部门9
     */
    public void setDept9(final String dept9) {
        this.dept9 = dept9;
    }

    /**
     * 部门10
     */
    public String getDept10() {
        return dept10;
    }

    /**
     * 部门10
     */
    public void setDept10(final String dept10) {
        this.dept10 = dept10;
    }

    /**
     * 成本中心1
     */
    public String getCostcenter1() {
        return costcenter1;
    }

    /**
     * 成本中心1
     */
    public void setCostcenter1(final String costcenter1) {
        this.costcenter1 = costcenter1;
    }

    /**
     * 成本中心2
     */
    public String getCostcenter2() {
        return costcenter2;
    }

    /**
     * 成本中心2
     */
    public void setCostcenter2(final String costcenter2) {
        this.costcenter2 = costcenter2;
    }

    /**
     * 成本中心3
     */
    public String getCostcenter3() {
        return costcenter3;
    }

    /**
     * 成本中心3
     */
    public void setCostcenter3(final String costcenter3) {
        this.costcenter3 = costcenter3;
    }

    /**
     * 成本中心4
     */
    public String getCostcenter4() {
        return costcenter4;
    }

    /**
     * 成本中心4
     */
    public void setCostcenter4(final String costcenter4) {
        this.costcenter4 = costcenter4;
    }

    /**
     * 成本中心5
     */
    public String getCostcenter5() {
        return costcenter5;
    }

    /**
     * 成本中心5
     */
    public void setCostcenter5(final String costcenter5) {
        this.costcenter5 = costcenter5;
    }

    /**
     * 成本中心6
     */
    public String getCostcenter6() {
        return costcenter6;
    }

    /**
     * 成本中心6
     */
    public void setCostcenter6(final String costcenter6) {
        this.costcenter6 = costcenter6;
    }

    /**
     * 预订时间
     */
    public String getOrderDate() {
        return orderDate;
    }

    /**
     * 预订时间
     */
    public void setOrderDate(final String orderDate) {
        this.orderDate = orderDate;
    }

    /**
     * 航司
     */
    public String getAirlineName() {
        return airlineName;
    }

    /**
     * 航司
     */
    public void setAirlineName(final String airlineName) {
        this.airlineName = airlineName;
    }

    /**
     * 起飞机场
     */
    public String getDeparturePortName() {
        return departurePortName;
    }

    /**
     * 起飞机场
     */
    public void setDeparturePortName(final String departurePortName) {
        this.departurePortName = departurePortName;
    }

    /**
     * 到达时间
     */
    public String getArrivalDateTime() {
        return arrivalDateTime;
    }

    /**
     * 到达时间
     */
    public void setArrivalDateTime(final String arrivalDateTime) {
        this.arrivalDateTime = arrivalDateTime;
    }

    /**
     * 到达机场
     */
    public String getArrivalPortName() {
        return arrivalPortName;
    }

    /**
     * 到达机场
     */
    public void setArrivalPortName(final String arrivalPortName) {
        this.arrivalPortName = arrivalPortName;
    }

    /**
     * 具体原因
     */
    public String getRiskType() {
        return riskType;
    }

    /**
     * 具体原因
     */
    public void setRiskType(final String riskType) {
        this.riskType = riskType;
    }

    /**
     * 疑似异常原因
     */
    public String getReasonType() {
        return reasonType;
    }

    /**
     * 疑似异常原因
     */
    public void setReasonType(final String reasonType) {
        this.reasonType = reasonType;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightRiskOrderDetail other = (FlightRiskOrderDetail)obj;
        return
            Objects.equal(this.orderId, other.orderId) &&
            Objects.equal(this.warningLevel, other.warningLevel) &&
            Objects.equal(this.orderStatus, other.orderStatus) &&
            Objects.equal(this.ticketStatus, other.ticketStatus) &&
            Objects.equal(this.segmentStatus, other.segmentStatus) &&
            Objects.equal(this.uid, other.uid) &&
            Objects.equal(this.uidName, other.uidName) &&
            Objects.equal(this.passengerName, other.passengerName) &&
            Objects.equal(this.corporationName, other.corporationName) &&
            Objects.equal(this.ticketNo, other.ticketNo) &&
            Objects.equal(this.segment, other.segment) &&
            Objects.equal(this.flightNo, other.flightNo) &&
            Objects.equal(this.takeOffTime, other.takeOffTime) &&
            Objects.equal(this.quantity, other.quantity) &&
            Objects.equal(this.price, other.price) &&
            Objects.equal(this.operator, other.operator) &&
            Objects.equal(this.operatorTime, other.operatorTime) &&
            Objects.equal(this.operateStatus, other.operateStatus) &&
            Objects.equal(this.uidIsvalid, other.uidIsvalid) &&
            Objects.equal(this.passengerIsvalid, other.passengerIsvalid) &&
            Objects.equal(this.journeyNo, other.journeyNo) &&
            Objects.equal(this.operatorMark, other.operatorMark) &&
            Objects.equal(this.accountId, other.accountId) &&
            Objects.equal(this.dept1, other.dept1) &&
            Objects.equal(this.dept2, other.dept2) &&
            Objects.equal(this.dept3, other.dept3) &&
            Objects.equal(this.dept4, other.dept4) &&
            Objects.equal(this.dept5, other.dept5) &&
            Objects.equal(this.dept6, other.dept6) &&
            Objects.equal(this.dept7, other.dept7) &&
            Objects.equal(this.dept8, other.dept8) &&
            Objects.equal(this.dept9, other.dept9) &&
            Objects.equal(this.dept10, other.dept10) &&
            Objects.equal(this.costcenter1, other.costcenter1) &&
            Objects.equal(this.costcenter2, other.costcenter2) &&
            Objects.equal(this.costcenter3, other.costcenter3) &&
            Objects.equal(this.costcenter4, other.costcenter4) &&
            Objects.equal(this.costcenter5, other.costcenter5) &&
            Objects.equal(this.costcenter6, other.costcenter6) &&
            Objects.equal(this.orderDate, other.orderDate) &&
            Objects.equal(this.airlineName, other.airlineName) &&
            Objects.equal(this.departurePortName, other.departurePortName) &&
            Objects.equal(this.arrivalDateTime, other.arrivalDateTime) &&
            Objects.equal(this.arrivalPortName, other.arrivalPortName) &&
            Objects.equal(this.riskType, other.riskType) &&
            Objects.equal(this.reasonType, other.reasonType);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.orderId == null ? 0 : this.orderId.hashCode());
        result = 31 * result + (this.warningLevel == null ? 0 : this.warningLevel.hashCode());
        result = 31 * result + (this.orderStatus == null ? 0 : this.orderStatus.hashCode());
        result = 31 * result + (this.ticketStatus == null ? 0 : this.ticketStatus.hashCode());
        result = 31 * result + (this.segmentStatus == null ? 0 : this.segmentStatus.hashCode());
        result = 31 * result + (this.uid == null ? 0 : this.uid.hashCode());
        result = 31 * result + (this.uidName == null ? 0 : this.uidName.hashCode());
        result = 31 * result + (this.passengerName == null ? 0 : this.passengerName.hashCode());
        result = 31 * result + (this.corporationName == null ? 0 : this.corporationName.hashCode());
        result = 31 * result + (this.ticketNo == null ? 0 : this.ticketNo.hashCode());
        result = 31 * result + (this.segment == null ? 0 : this.segment.hashCode());
        result = 31 * result + (this.flightNo == null ? 0 : this.flightNo.hashCode());
        result = 31 * result + (this.takeOffTime == null ? 0 : this.takeOffTime.hashCode());
        result = 31 * result + (this.quantity == null ? 0 : this.quantity.hashCode());
        result = 31 * result + (this.price == null ? 0 : this.price.hashCode());
        result = 31 * result + (this.operator == null ? 0 : this.operator.hashCode());
        result = 31 * result + (this.operatorTime == null ? 0 : this.operatorTime.hashCode());
        result = 31 * result + (this.operateStatus == null ? 0 : this.operateStatus.hashCode());
        result = 31 * result + (this.uidIsvalid == null ? 0 : this.uidIsvalid.hashCode());
        result = 31 * result + (this.passengerIsvalid == null ? 0 : this.passengerIsvalid.hashCode());
        result = 31 * result + (this.journeyNo == null ? 0 : this.journeyNo.hashCode());
        result = 31 * result + (this.operatorMark == null ? 0 : this.operatorMark.hashCode());
        result = 31 * result + (this.accountId == null ? 0 : this.accountId.hashCode());
        result = 31 * result + (this.dept1 == null ? 0 : this.dept1.hashCode());
        result = 31 * result + (this.dept2 == null ? 0 : this.dept2.hashCode());
        result = 31 * result + (this.dept3 == null ? 0 : this.dept3.hashCode());
        result = 31 * result + (this.dept4 == null ? 0 : this.dept4.hashCode());
        result = 31 * result + (this.dept5 == null ? 0 : this.dept5.hashCode());
        result = 31 * result + (this.dept6 == null ? 0 : this.dept6.hashCode());
        result = 31 * result + (this.dept7 == null ? 0 : this.dept7.hashCode());
        result = 31 * result + (this.dept8 == null ? 0 : this.dept8.hashCode());
        result = 31 * result + (this.dept9 == null ? 0 : this.dept9.hashCode());
        result = 31 * result + (this.dept10 == null ? 0 : this.dept10.hashCode());
        result = 31 * result + (this.costcenter1 == null ? 0 : this.costcenter1.hashCode());
        result = 31 * result + (this.costcenter2 == null ? 0 : this.costcenter2.hashCode());
        result = 31 * result + (this.costcenter3 == null ? 0 : this.costcenter3.hashCode());
        result = 31 * result + (this.costcenter4 == null ? 0 : this.costcenter4.hashCode());
        result = 31 * result + (this.costcenter5 == null ? 0 : this.costcenter5.hashCode());
        result = 31 * result + (this.costcenter6 == null ? 0 : this.costcenter6.hashCode());
        result = 31 * result + (this.orderDate == null ? 0 : this.orderDate.hashCode());
        result = 31 * result + (this.airlineName == null ? 0 : this.airlineName.hashCode());
        result = 31 * result + (this.departurePortName == null ? 0 : this.departurePortName.hashCode());
        result = 31 * result + (this.arrivalDateTime == null ? 0 : this.arrivalDateTime.hashCode());
        result = 31 * result + (this.arrivalPortName == null ? 0 : this.arrivalPortName.hashCode());
        result = 31 * result + (this.riskType == null ? 0 : this.riskType.hashCode());
        result = 31 * result + (this.reasonType == null ? 0 : this.reasonType.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("orderId", orderId)
            .add("warningLevel", warningLevel)
            .add("orderStatus", orderStatus)
            .add("ticketStatus", ticketStatus)
            .add("segmentStatus", segmentStatus)
            .add("uid", uid)
            .add("uidName", uidName)
            .add("passengerName", passengerName)
            .add("corporationName", corporationName)
            .add("ticketNo", ticketNo)
            .add("segment", segment)
            .add("flightNo", flightNo)
            .add("takeOffTime", takeOffTime)
            .add("quantity", quantity)
            .add("price", price)
            .add("operator", operator)
            .add("operatorTime", operatorTime)
            .add("operateStatus", operateStatus)
            .add("uidIsvalid", uidIsvalid)
            .add("passengerIsvalid", passengerIsvalid)
            .add("journeyNo", journeyNo)
            .add("operatorMark", operatorMark)
            .add("accountId", accountId)
            .add("dept1", dept1)
            .add("dept2", dept2)
            .add("dept3", dept3)
            .add("dept4", dept4)
            .add("dept5", dept5)
            .add("dept6", dept6)
            .add("dept7", dept7)
            .add("dept8", dept8)
            .add("dept9", dept9)
            .add("dept10", dept10)
            .add("costcenter1", costcenter1)
            .add("costcenter2", costcenter2)
            .add("costcenter3", costcenter3)
            .add("costcenter4", costcenter4)
            .add("costcenter5", costcenter5)
            .add("costcenter6", costcenter6)
            .add("orderDate", orderDate)
            .add("airlineName", airlineName)
            .add("departurePortName", departurePortName)
            .add("arrivalDateTime", arrivalDateTime)
            .add("arrivalPortName", arrivalPortName)
            .add("riskType", riskType)
            .add("reasonType", reasonType)
            .toString();
    }
}
