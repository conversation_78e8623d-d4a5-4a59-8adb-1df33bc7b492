package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 总体概况总体消费金额
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "totalAmount",
    "avgMonthAmount",
    "yoyAmountLast",
    "yoyAmountBeforeLast",
    "momAmount",
    "totalOneAmount",
    "yoyOneAmountLast",
    "yoyOneAmountBeforeLast",
    "momOneAmount",
    "oneAmountPercentage",
    "totalTwoAmount",
    "yoyTwoAmountLast",
    "yoyTwoAmountBeforeLast",
    "momTwoAmount",
    "twoAmountPercentage",
    "totalThreeAmount",
    "yoyThreeAmountLast",
    "yoyThreeAmountBeforeLast",
    "momThreeAmount",
    "threeAmountPercentage",
    "totalFourAmount",
    "yoyFourAmountLast",
    "yoyFourAmountBeforeLast",
    "momFourAmount",
    "fourAmountPercentage",
    "totalFiveAmount",
    "yoyFiveAmountLast",
    "yoyFiveAmountBeforeLast",
    "momFiveAmount",
    "fiveAmountPercentage",
    "totalSixAmount",
    "yoySixAmountLast",
    "yoySixAmountBeforeLast",
    "momSixAmount",
    "sixAmountPercentage",
    "totalSevenAmount",
    "yoySevenAmountLast",
    "yoySevenAmountBeforeLast",
    "momSevenAmount",
    "sevenAmountPercentage",
    "avgPrice",
    "totalCorpServiceFee",
    "avgOtherPrice",
    "totalQuantity",
    "avgMonthQuantity",
    "yoyQuantityLast",
    "yoyQuantityBeforeLast",
    "momQuantity",
    "totalOneQuantity",
    "yoyOneQuantityLast",
    "yoyOneQuantityBeforeLast",
    "momOneQuantity",
    "oneQuantityPercentage",
    "totalTwoQuantity",
    "yoyTwoQuantityLast",
    "yoyTwoQuantityBeforeLast",
    "momTwoQuantity",
    "twoQuantityPercentage",
    "totalThreeQuantity",
    "yoyThreeQuantityLast",
    "yoyThreeQuantityBeforeLast",
    "momThreeQuantity",
    "threeQuantityPercentage",
    "totalFourQuantity",
    "yoyFourQuantityLast",
    "yoyFourQuantityBeforeLast",
    "momFourQuantity",
    "fourQuantityPercentage",
    "totalFiveQuantity",
    "yoyFiveQuantityLast",
    "yoyFiveQuantityBeforeLast",
    "momFiveQuantity",
    "fiveQuantityPercentage",
    "totalSixQuantity",
    "yoySixQuantityLast",
    "yoySixQuantityBeforeLast",
    "momSixQuantity",
    "sixQuantityPercentage",
    "totalSevenQuantity",
    "yoySevenQuantityLast",
    "yoySevenQuantityBeforeLast",
    "momSevenQuantity",
    "sevenQuantityPercentage",
    "refundQuantity",
    "reBookQuantity",
    "totalCntOrder"
})
public class OnlineReportConsume implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportConsume(
        BigDecimal totalAmount,
        BigDecimal avgMonthAmount,
        BigDecimal yoyAmountLast,
        BigDecimal yoyAmountBeforeLast,
        BigDecimal momAmount,
        BigDecimal totalOneAmount,
        BigDecimal yoyOneAmountLast,
        BigDecimal yoyOneAmountBeforeLast,
        BigDecimal momOneAmount,
        BigDecimal oneAmountPercentage,
        BigDecimal totalTwoAmount,
        BigDecimal yoyTwoAmountLast,
        BigDecimal yoyTwoAmountBeforeLast,
        BigDecimal momTwoAmount,
        BigDecimal twoAmountPercentage,
        BigDecimal totalThreeAmount,
        BigDecimal yoyThreeAmountLast,
        BigDecimal yoyThreeAmountBeforeLast,
        BigDecimal momThreeAmount,
        BigDecimal threeAmountPercentage,
        BigDecimal totalFourAmount,
        BigDecimal yoyFourAmountLast,
        BigDecimal yoyFourAmountBeforeLast,
        BigDecimal momFourAmount,
        BigDecimal fourAmountPercentage,
        BigDecimal totalFiveAmount,
        BigDecimal yoyFiveAmountLast,
        BigDecimal yoyFiveAmountBeforeLast,
        BigDecimal momFiveAmount,
        BigDecimal fiveAmountPercentage,
        BigDecimal totalSixAmount,
        BigDecimal yoySixAmountLast,
        BigDecimal yoySixAmountBeforeLast,
        BigDecimal momSixAmount,
        BigDecimal sixAmountPercentage,
        BigDecimal totalSevenAmount,
        BigDecimal yoySevenAmountLast,
        BigDecimal yoySevenAmountBeforeLast,
        BigDecimal momSevenAmount,
        BigDecimal sevenAmountPercentage,
        BigDecimal avgPrice,
        BigDecimal totalCorpServiceFee,
        BigDecimal avgOtherPrice,
        Integer totalQuantity,
        BigDecimal avgMonthQuantity,
        BigDecimal yoyQuantityLast,
        BigDecimal yoyQuantityBeforeLast,
        BigDecimal momQuantity,
        Integer totalOneQuantity,
        BigDecimal yoyOneQuantityLast,
        BigDecimal yoyOneQuantityBeforeLast,
        BigDecimal momOneQuantity,
        BigDecimal oneQuantityPercentage,
        Integer totalTwoQuantity,
        BigDecimal yoyTwoQuantityLast,
        BigDecimal yoyTwoQuantityBeforeLast,
        BigDecimal momTwoQuantity,
        BigDecimal twoQuantityPercentage,
        Integer totalThreeQuantity,
        BigDecimal yoyThreeQuantityLast,
        BigDecimal yoyThreeQuantityBeforeLast,
        BigDecimal momThreeQuantity,
        BigDecimal threeQuantityPercentage,
        Integer totalFourQuantity,
        BigDecimal yoyFourQuantityLast,
        BigDecimal yoyFourQuantityBeforeLast,
        BigDecimal momFourQuantity,
        BigDecimal fourQuantityPercentage,
        Integer totalFiveQuantity,
        BigDecimal yoyFiveQuantityLast,
        BigDecimal yoyFiveQuantityBeforeLast,
        BigDecimal momFiveQuantity,
        BigDecimal fiveQuantityPercentage,
        Integer totalSixQuantity,
        BigDecimal yoySixQuantityLast,
        BigDecimal yoySixQuantityBeforeLast,
        BigDecimal momSixQuantity,
        BigDecimal sixQuantityPercentage,
        Integer totalSevenQuantity,
        BigDecimal yoySevenQuantityLast,
        BigDecimal yoySevenQuantityBeforeLast,
        BigDecimal momSevenQuantity,
        BigDecimal sevenQuantityPercentage,
        Integer refundQuantity,
        Integer reBookQuantity,
        Integer totalCntOrder) {
        this.totalAmount = totalAmount;
        this.avgMonthAmount = avgMonthAmount;
        this.yoyAmountLast = yoyAmountLast;
        this.yoyAmountBeforeLast = yoyAmountBeforeLast;
        this.momAmount = momAmount;
        this.totalOneAmount = totalOneAmount;
        this.yoyOneAmountLast = yoyOneAmountLast;
        this.yoyOneAmountBeforeLast = yoyOneAmountBeforeLast;
        this.momOneAmount = momOneAmount;
        this.oneAmountPercentage = oneAmountPercentage;
        this.totalTwoAmount = totalTwoAmount;
        this.yoyTwoAmountLast = yoyTwoAmountLast;
        this.yoyTwoAmountBeforeLast = yoyTwoAmountBeforeLast;
        this.momTwoAmount = momTwoAmount;
        this.twoAmountPercentage = twoAmountPercentage;
        this.totalThreeAmount = totalThreeAmount;
        this.yoyThreeAmountLast = yoyThreeAmountLast;
        this.yoyThreeAmountBeforeLast = yoyThreeAmountBeforeLast;
        this.momThreeAmount = momThreeAmount;
        this.threeAmountPercentage = threeAmountPercentage;
        this.totalFourAmount = totalFourAmount;
        this.yoyFourAmountLast = yoyFourAmountLast;
        this.yoyFourAmountBeforeLast = yoyFourAmountBeforeLast;
        this.momFourAmount = momFourAmount;
        this.fourAmountPercentage = fourAmountPercentage;
        this.totalFiveAmount = totalFiveAmount;
        this.yoyFiveAmountLast = yoyFiveAmountLast;
        this.yoyFiveAmountBeforeLast = yoyFiveAmountBeforeLast;
        this.momFiveAmount = momFiveAmount;
        this.fiveAmountPercentage = fiveAmountPercentage;
        this.totalSixAmount = totalSixAmount;
        this.yoySixAmountLast = yoySixAmountLast;
        this.yoySixAmountBeforeLast = yoySixAmountBeforeLast;
        this.momSixAmount = momSixAmount;
        this.sixAmountPercentage = sixAmountPercentage;
        this.totalSevenAmount = totalSevenAmount;
        this.yoySevenAmountLast = yoySevenAmountLast;
        this.yoySevenAmountBeforeLast = yoySevenAmountBeforeLast;
        this.momSevenAmount = momSevenAmount;
        this.sevenAmountPercentage = sevenAmountPercentage;
        this.avgPrice = avgPrice;
        this.totalCorpServiceFee = totalCorpServiceFee;
        this.avgOtherPrice = avgOtherPrice;
        this.totalQuantity = totalQuantity;
        this.avgMonthQuantity = avgMonthQuantity;
        this.yoyQuantityLast = yoyQuantityLast;
        this.yoyQuantityBeforeLast = yoyQuantityBeforeLast;
        this.momQuantity = momQuantity;
        this.totalOneQuantity = totalOneQuantity;
        this.yoyOneQuantityLast = yoyOneQuantityLast;
        this.yoyOneQuantityBeforeLast = yoyOneQuantityBeforeLast;
        this.momOneQuantity = momOneQuantity;
        this.oneQuantityPercentage = oneQuantityPercentage;
        this.totalTwoQuantity = totalTwoQuantity;
        this.yoyTwoQuantityLast = yoyTwoQuantityLast;
        this.yoyTwoQuantityBeforeLast = yoyTwoQuantityBeforeLast;
        this.momTwoQuantity = momTwoQuantity;
        this.twoQuantityPercentage = twoQuantityPercentage;
        this.totalThreeQuantity = totalThreeQuantity;
        this.yoyThreeQuantityLast = yoyThreeQuantityLast;
        this.yoyThreeQuantityBeforeLast = yoyThreeQuantityBeforeLast;
        this.momThreeQuantity = momThreeQuantity;
        this.threeQuantityPercentage = threeQuantityPercentage;
        this.totalFourQuantity = totalFourQuantity;
        this.yoyFourQuantityLast = yoyFourQuantityLast;
        this.yoyFourQuantityBeforeLast = yoyFourQuantityBeforeLast;
        this.momFourQuantity = momFourQuantity;
        this.fourQuantityPercentage = fourQuantityPercentage;
        this.totalFiveQuantity = totalFiveQuantity;
        this.yoyFiveQuantityLast = yoyFiveQuantityLast;
        this.yoyFiveQuantityBeforeLast = yoyFiveQuantityBeforeLast;
        this.momFiveQuantity = momFiveQuantity;
        this.fiveQuantityPercentage = fiveQuantityPercentage;
        this.totalSixQuantity = totalSixQuantity;
        this.yoySixQuantityLast = yoySixQuantityLast;
        this.yoySixQuantityBeforeLast = yoySixQuantityBeforeLast;
        this.momSixQuantity = momSixQuantity;
        this.sixQuantityPercentage = sixQuantityPercentage;
        this.totalSevenQuantity = totalSevenQuantity;
        this.yoySevenQuantityLast = yoySevenQuantityLast;
        this.yoySevenQuantityBeforeLast = yoySevenQuantityBeforeLast;
        this.momSevenQuantity = momSevenQuantity;
        this.sevenQuantityPercentage = sevenQuantityPercentage;
        this.refundQuantity = refundQuantity;
        this.reBookQuantity = reBookQuantity;
        this.totalCntOrder = totalCntOrder;
    }

    public OnlineReportConsume() {
    }

    /**
     * 总金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;

    /**
     * 月均消费
     */
    @JsonProperty("avgMonthAmount")
    public BigDecimal avgMonthAmount;

    /**
     * 同比去年总金额
     */
    @JsonProperty("yoyAmountLast")
    public BigDecimal yoyAmountLast;

    /**
     * 同比前年总金额
     */
    @JsonProperty("yoyAmountBeforeLast")
    public BigDecimal yoyAmountBeforeLast;

    /**
     * 环比金额
     */
    @JsonProperty("momAmount")
    public BigDecimal momAmount;

    /**
     * 金额1
     */
    @JsonProperty("totalOneAmount")
    public BigDecimal totalOneAmount;

    /**
     * 金额1同比去年
     */
    @JsonProperty("yoyOneAmountLast")
    public BigDecimal yoyOneAmountLast;

    /**
     * 金额1同比前年
     */
    @JsonProperty("yoyOneAmountBeforeLast")
    public BigDecimal yoyOneAmountBeforeLast;

    /**
     * 金额1环比
     */
    @JsonProperty("momOneAmount")
    public BigDecimal momOneAmount;

    /**
     * 金额1占比
     */
    @JsonProperty("oneAmountPercentage")
    public BigDecimal oneAmountPercentage;

    /**
     * 金额2
     */
    @JsonProperty("totalTwoAmount")
    public BigDecimal totalTwoAmount;

    /**
     * 金额2同比去年
     */
    @JsonProperty("yoyTwoAmountLast")
    public BigDecimal yoyTwoAmountLast;

    /**
     * 金额2同比前年
     */
    @JsonProperty("yoyTwoAmountBeforeLast")
    public BigDecimal yoyTwoAmountBeforeLast;

    /**
     * 金额2环比
     */
    @JsonProperty("momTwoAmount")
    public BigDecimal momTwoAmount;

    /**
     * 金额2占比
     */
    @JsonProperty("twoAmountPercentage")
    public BigDecimal twoAmountPercentage;

    /**
     * 金额3
     */
    @JsonProperty("totalThreeAmount")
    public BigDecimal totalThreeAmount;

    /**
     * 金额3同比去年
     */
    @JsonProperty("yoyThreeAmountLast")
    public BigDecimal yoyThreeAmountLast;

    /**
     * 金额3同比前年
     */
    @JsonProperty("yoyThreeAmountBeforeLast")
    public BigDecimal yoyThreeAmountBeforeLast;

    /**
     * 金额3环比
     */
    @JsonProperty("momThreeAmount")
    public BigDecimal momThreeAmount;

    /**
     * 金额3占比
     */
    @JsonProperty("threeAmountPercentage")
    public BigDecimal threeAmountPercentage;

    /**
     * 金额4
     */
    @JsonProperty("totalFourAmount")
    public BigDecimal totalFourAmount;

    /**
     * 金额4同比去年
     */
    @JsonProperty("yoyFourAmountLast")
    public BigDecimal yoyFourAmountLast;

    /**
     * 金额4同比前年
     */
    @JsonProperty("yoyFourAmountBeforeLast")
    public BigDecimal yoyFourAmountBeforeLast;

    /**
     * 金额4环比
     */
    @JsonProperty("momFourAmount")
    public BigDecimal momFourAmount;

    /**
     * 金额4占比
     */
    @JsonProperty("fourAmountPercentage")
    public BigDecimal fourAmountPercentage;

    /**
     * 金额5
     */
    @JsonProperty("totalFiveAmount")
    public BigDecimal totalFiveAmount;

    /**
     * 金额5同比去年
     */
    @JsonProperty("yoyFiveAmountLast")
    public BigDecimal yoyFiveAmountLast;

    /**
     * 金额5同比前年
     */
    @JsonProperty("yoyFiveAmountBeforeLast")
    public BigDecimal yoyFiveAmountBeforeLast;

    /**
     * 金额5环比
     */
    @JsonProperty("momFiveAmount")
    public BigDecimal momFiveAmount;

    /**
     * 金额5占比
     */
    @JsonProperty("fiveAmountPercentage")
    public BigDecimal fiveAmountPercentage;

    /**
     * 金额6
     */
    @JsonProperty("totalSixAmount")
    public BigDecimal totalSixAmount;

    /**
     * 金额6同比去年
     */
    @JsonProperty("yoySixAmountLast")
    public BigDecimal yoySixAmountLast;

    /**
     * 金额6同比前年
     */
    @JsonProperty("yoySixAmountBeforeLast")
    public BigDecimal yoySixAmountBeforeLast;

    /**
     * 金额6环比
     */
    @JsonProperty("momSixAmount")
    public BigDecimal momSixAmount;

    /**
     * 金额6占比
     */
    @JsonProperty("sixAmountPercentage")
    public BigDecimal sixAmountPercentage;

    /**
     * 金额7
     */
    @JsonProperty("totalSevenAmount")
    public BigDecimal totalSevenAmount;

    /**
     * 金额7同比去年
     */
    @JsonProperty("yoySevenAmountLast")
    public BigDecimal yoySevenAmountLast;

    /**
     * 金额7同比前年
     */
    @JsonProperty("yoySevenAmountBeforeLast")
    public BigDecimal yoySevenAmountBeforeLast;

    /**
     * 金额7环比
     */
    @JsonProperty("momSevenAmount")
    public BigDecimal momSevenAmount;

    /**
     * 金额7占比
     */
    @JsonProperty("sevenAmountPercentage")
    public BigDecimal sevenAmountPercentage;

    /**
     * 根据querybu不同字段意义不同（机票均价、酒店间夜均价、火车票均价、用车订单均价）
     */
    @JsonProperty("avgPrice")
    public BigDecimal avgPrice;

    /**
     * 根据querybu不同字段意义不同（机票、酒店、火车、用车）商旅管理服务业费
     */
    @JsonProperty("totalCorpServiceFee")
    public BigDecimal totalCorpServiceFee;

    /**
     * 目前只有机票里程均价
     */
    @JsonProperty("avgOtherPrice")
    public BigDecimal avgOtherPrice;

    /**
     * 总行程/票张/订单/间夜
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;

    /**
     * 月均行程/票张/订单/间夜
     */
    @JsonProperty("avgMonthQuantity")
    public BigDecimal avgMonthQuantity;

    /**
     * 同比去年行程/票张/订单/间夜
     */
    @JsonProperty("yoyQuantityLast")
    public BigDecimal yoyQuantityLast;

    /**
     * 同比前年行程/票张/订单/间夜
     */
    @JsonProperty("yoyQuantityBeforeLast")
    public BigDecimal yoyQuantityBeforeLast;

    /**
     * 环比行程/票张/订单/间夜
     */
    @JsonProperty("momQuantity")
    public BigDecimal momQuantity;

    /**
     * 票张/间夜/订单1
     */
    @JsonProperty("totalOneQuantity")
    public Integer totalOneQuantity;

    /**
     * 同比去年行程/票张/订单/间夜1
     */
    @JsonProperty("yoyOneQuantityLast")
    public BigDecimal yoyOneQuantityLast;

    /**
     * 同比前年行程/票张/订单/间夜1
     */
    @JsonProperty("yoyOneQuantityBeforeLast")
    public BigDecimal yoyOneQuantityBeforeLast;

    /**
     * 环比行程/票张/订单/间夜1
     */
    @JsonProperty("momOneQuantity")
    public BigDecimal momOneQuantity;

    /**
     * 行程/票张/订单/间夜1占比
     */
    @JsonProperty("oneQuantityPercentage")
    public BigDecimal oneQuantityPercentage;

    /**
     * 行程/票张/间夜/订单2
     */
    @JsonProperty("totalTwoQuantity")
    public Integer totalTwoQuantity;

    /**
     * 同比去年行程/票张/订单/间夜2
     */
    @JsonProperty("yoyTwoQuantityLast")
    public BigDecimal yoyTwoQuantityLast;

    /**
     * 同比前年行程/票张/订单/间夜2
     */
    @JsonProperty("yoyTwoQuantityBeforeLast")
    public BigDecimal yoyTwoQuantityBeforeLast;

    /**
     * 环比行程/票张/订单/间夜2
     */
    @JsonProperty("momTwoQuantity")
    public BigDecimal momTwoQuantity;

    /**
     * 行程/票张/订单/间夜2占比
     */
    @JsonProperty("twoQuantityPercentage")
    public BigDecimal twoQuantityPercentage;

    /**
     * 票张/间夜/订单3
     */
    @JsonProperty("totalThreeQuantity")
    public Integer totalThreeQuantity;

    /**
     * 同比去年行程/票张/订单/间夜3
     */
    @JsonProperty("yoyThreeQuantityLast")
    public BigDecimal yoyThreeQuantityLast;

    /**
     * 同比前年行程/票张/订单/间夜3
     */
    @JsonProperty("yoyThreeQuantityBeforeLast")
    public BigDecimal yoyThreeQuantityBeforeLast;

    /**
     * 环比行程/票张/订单/间夜3
     */
    @JsonProperty("momThreeQuantity")
    public BigDecimal momThreeQuantity;

    /**
     * 行程/票张/订单/间夜3占比
     */
    @JsonProperty("threeQuantityPercentage")
    public BigDecimal threeQuantityPercentage;

    /**
     * 票张/间夜/订单4
     */
    @JsonProperty("totalFourQuantity")
    public Integer totalFourQuantity;

    /**
     * 同比去年行程/票张/订单/间夜4
     */
    @JsonProperty("yoyFourQuantityLast")
    public BigDecimal yoyFourQuantityLast;

    /**
     * 同比前年行程/票张/订单/间夜4
     */
    @JsonProperty("yoyFourQuantityBeforeLast")
    public BigDecimal yoyFourQuantityBeforeLast;

    /**
     * 环比行程/票张/订单/间夜4
     */
    @JsonProperty("momFourQuantity")
    public BigDecimal momFourQuantity;

    /**
     * 行程/票张/订单/间夜4占比
     */
    @JsonProperty("fourQuantityPercentage")
    public BigDecimal fourQuantityPercentage;

    /**
     * 票张/间夜/订单5
     */
    @JsonProperty("totalFiveQuantity")
    public Integer totalFiveQuantity;

    /**
     * 同比去年行程/票张/订单/间夜5
     */
    @JsonProperty("yoyFiveQuantityLast")
    public BigDecimal yoyFiveQuantityLast;

    /**
     * 同比前年行程/票张/订单/间夜5
     */
    @JsonProperty("yoyFiveQuantityBeforeLast")
    public BigDecimal yoyFiveQuantityBeforeLast;

    /**
     * 环比行程/票张/订单/间夜5
     */
    @JsonProperty("momFiveQuantity")
    public BigDecimal momFiveQuantity;

    /**
     * 行程/票张/订单/间夜5占比
     */
    @JsonProperty("fiveQuantityPercentage")
    public BigDecimal fiveQuantityPercentage;

    /**
     * 票张/间夜/订单6
     */
    @JsonProperty("totalSixQuantity")
    public Integer totalSixQuantity;

    /**
     * 同比去年行程/票张/订单/间夜6
     */
    @JsonProperty("yoySixQuantityLast")
    public BigDecimal yoySixQuantityLast;

    /**
     * 同比前年行程/票张/订单/间夜6
     */
    @JsonProperty("yoySixQuantityBeforeLast")
    public BigDecimal yoySixQuantityBeforeLast;

    /**
     * 环比行程/票张/订单/间夜6
     */
    @JsonProperty("momSixQuantity")
    public BigDecimal momSixQuantity;

    /**
     * 行程/票张/订单/间夜6占比
     */
    @JsonProperty("sixQuantityPercentage")
    public BigDecimal sixQuantityPercentage;

    /**
     * 票张/间夜/订单7
     */
    @JsonProperty("totalSevenQuantity")
    public Integer totalSevenQuantity;

    /**
     * 同比去年行程/票张/订单/间夜7
     */
    @JsonProperty("yoySevenQuantityLast")
    public BigDecimal yoySevenQuantityLast;

    /**
     * 同比前年行程/票张/订单/间夜7
     */
    @JsonProperty("yoySevenQuantityBeforeLast")
    public BigDecimal yoySevenQuantityBeforeLast;

    /**
     * 环比行程/票张/订单/间夜7
     */
    @JsonProperty("momSevenQuantity")
    public BigDecimal momSevenQuantity;

    /**
     * 行程/票张/订单/间夜7占比
     */
    @JsonProperty("sevenQuantityPercentage")
    public BigDecimal sevenQuantityPercentage;

    /**
     * 根据querybu不同字段意义不同（机票退票张数、酒店退订间夜、火车退票张数）
     */
    @JsonProperty("refundQuantity")
    public Integer refundQuantity;

    /**
     * 根据querybu不同字段意义不同（机票改签张数、火车改签张数）
     */
    @JsonProperty("reBookQuantity")
    public Integer reBookQuantity;

    /**
     * 总订单数(机、酒、火)
     */
    @JsonProperty("totalCntOrder")
    public Integer totalCntOrder;

    /**
     * 总金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 总金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 月均消费
     */
    public BigDecimal getAvgMonthAmount() {
        return avgMonthAmount;
    }

    /**
     * 月均消费
     */
    public void setAvgMonthAmount(final BigDecimal avgMonthAmount) {
        this.avgMonthAmount = avgMonthAmount;
    }

    /**
     * 同比去年总金额
     */
    public BigDecimal getYoyAmountLast() {
        return yoyAmountLast;
    }

    /**
     * 同比去年总金额
     */
    public void setYoyAmountLast(final BigDecimal yoyAmountLast) {
        this.yoyAmountLast = yoyAmountLast;
    }

    /**
     * 同比前年总金额
     */
    public BigDecimal getYoyAmountBeforeLast() {
        return yoyAmountBeforeLast;
    }

    /**
     * 同比前年总金额
     */
    public void setYoyAmountBeforeLast(final BigDecimal yoyAmountBeforeLast) {
        this.yoyAmountBeforeLast = yoyAmountBeforeLast;
    }

    /**
     * 环比金额
     */
    public BigDecimal getMomAmount() {
        return momAmount;
    }

    /**
     * 环比金额
     */
    public void setMomAmount(final BigDecimal momAmount) {
        this.momAmount = momAmount;
    }

    /**
     * 金额1
     */
    public BigDecimal getTotalOneAmount() {
        return totalOneAmount;
    }

    /**
     * 金额1
     */
    public void setTotalOneAmount(final BigDecimal totalOneAmount) {
        this.totalOneAmount = totalOneAmount;
    }

    /**
     * 金额1同比去年
     */
    public BigDecimal getYoyOneAmountLast() {
        return yoyOneAmountLast;
    }

    /**
     * 金额1同比去年
     */
    public void setYoyOneAmountLast(final BigDecimal yoyOneAmountLast) {
        this.yoyOneAmountLast = yoyOneAmountLast;
    }

    /**
     * 金额1同比前年
     */
    public BigDecimal getYoyOneAmountBeforeLast() {
        return yoyOneAmountBeforeLast;
    }

    /**
     * 金额1同比前年
     */
    public void setYoyOneAmountBeforeLast(final BigDecimal yoyOneAmountBeforeLast) {
        this.yoyOneAmountBeforeLast = yoyOneAmountBeforeLast;
    }

    /**
     * 金额1环比
     */
    public BigDecimal getMomOneAmount() {
        return momOneAmount;
    }

    /**
     * 金额1环比
     */
    public void setMomOneAmount(final BigDecimal momOneAmount) {
        this.momOneAmount = momOneAmount;
    }

    /**
     * 金额1占比
     */
    public BigDecimal getOneAmountPercentage() {
        return oneAmountPercentage;
    }

    /**
     * 金额1占比
     */
    public void setOneAmountPercentage(final BigDecimal oneAmountPercentage) {
        this.oneAmountPercentage = oneAmountPercentage;
    }

    /**
     * 金额2
     */
    public BigDecimal getTotalTwoAmount() {
        return totalTwoAmount;
    }

    /**
     * 金额2
     */
    public void setTotalTwoAmount(final BigDecimal totalTwoAmount) {
        this.totalTwoAmount = totalTwoAmount;
    }

    /**
     * 金额2同比去年
     */
    public BigDecimal getYoyTwoAmountLast() {
        return yoyTwoAmountLast;
    }

    /**
     * 金额2同比去年
     */
    public void setYoyTwoAmountLast(final BigDecimal yoyTwoAmountLast) {
        this.yoyTwoAmountLast = yoyTwoAmountLast;
    }

    /**
     * 金额2同比前年
     */
    public BigDecimal getYoyTwoAmountBeforeLast() {
        return yoyTwoAmountBeforeLast;
    }

    /**
     * 金额2同比前年
     */
    public void setYoyTwoAmountBeforeLast(final BigDecimal yoyTwoAmountBeforeLast) {
        this.yoyTwoAmountBeforeLast = yoyTwoAmountBeforeLast;
    }

    /**
     * 金额2环比
     */
    public BigDecimal getMomTwoAmount() {
        return momTwoAmount;
    }

    /**
     * 金额2环比
     */
    public void setMomTwoAmount(final BigDecimal momTwoAmount) {
        this.momTwoAmount = momTwoAmount;
    }

    /**
     * 金额2占比
     */
    public BigDecimal getTwoAmountPercentage() {
        return twoAmountPercentage;
    }

    /**
     * 金额2占比
     */
    public void setTwoAmountPercentage(final BigDecimal twoAmountPercentage) {
        this.twoAmountPercentage = twoAmountPercentage;
    }

    /**
     * 金额3
     */
    public BigDecimal getTotalThreeAmount() {
        return totalThreeAmount;
    }

    /**
     * 金额3
     */
    public void setTotalThreeAmount(final BigDecimal totalThreeAmount) {
        this.totalThreeAmount = totalThreeAmount;
    }

    /**
     * 金额3同比去年
     */
    public BigDecimal getYoyThreeAmountLast() {
        return yoyThreeAmountLast;
    }

    /**
     * 金额3同比去年
     */
    public void setYoyThreeAmountLast(final BigDecimal yoyThreeAmountLast) {
        this.yoyThreeAmountLast = yoyThreeAmountLast;
    }

    /**
     * 金额3同比前年
     */
    public BigDecimal getYoyThreeAmountBeforeLast() {
        return yoyThreeAmountBeforeLast;
    }

    /**
     * 金额3同比前年
     */
    public void setYoyThreeAmountBeforeLast(final BigDecimal yoyThreeAmountBeforeLast) {
        this.yoyThreeAmountBeforeLast = yoyThreeAmountBeforeLast;
    }

    /**
     * 金额3环比
     */
    public BigDecimal getMomThreeAmount() {
        return momThreeAmount;
    }

    /**
     * 金额3环比
     */
    public void setMomThreeAmount(final BigDecimal momThreeAmount) {
        this.momThreeAmount = momThreeAmount;
    }

    /**
     * 金额3占比
     */
    public BigDecimal getThreeAmountPercentage() {
        return threeAmountPercentage;
    }

    /**
     * 金额3占比
     */
    public void setThreeAmountPercentage(final BigDecimal threeAmountPercentage) {
        this.threeAmountPercentage = threeAmountPercentage;
    }

    /**
     * 金额4
     */
    public BigDecimal getTotalFourAmount() {
        return totalFourAmount;
    }

    /**
     * 金额4
     */
    public void setTotalFourAmount(final BigDecimal totalFourAmount) {
        this.totalFourAmount = totalFourAmount;
    }

    /**
     * 金额4同比去年
     */
    public BigDecimal getYoyFourAmountLast() {
        return yoyFourAmountLast;
    }

    /**
     * 金额4同比去年
     */
    public void setYoyFourAmountLast(final BigDecimal yoyFourAmountLast) {
        this.yoyFourAmountLast = yoyFourAmountLast;
    }

    /**
     * 金额4同比前年
     */
    public BigDecimal getYoyFourAmountBeforeLast() {
        return yoyFourAmountBeforeLast;
    }

    /**
     * 金额4同比前年
     */
    public void setYoyFourAmountBeforeLast(final BigDecimal yoyFourAmountBeforeLast) {
        this.yoyFourAmountBeforeLast = yoyFourAmountBeforeLast;
    }

    /**
     * 金额4环比
     */
    public BigDecimal getMomFourAmount() {
        return momFourAmount;
    }

    /**
     * 金额4环比
     */
    public void setMomFourAmount(final BigDecimal momFourAmount) {
        this.momFourAmount = momFourAmount;
    }

    /**
     * 金额4占比
     */
    public BigDecimal getFourAmountPercentage() {
        return fourAmountPercentage;
    }

    /**
     * 金额4占比
     */
    public void setFourAmountPercentage(final BigDecimal fourAmountPercentage) {
        this.fourAmountPercentage = fourAmountPercentage;
    }

    /**
     * 金额5
     */
    public BigDecimal getTotalFiveAmount() {
        return totalFiveAmount;
    }

    /**
     * 金额5
     */
    public void setTotalFiveAmount(final BigDecimal totalFiveAmount) {
        this.totalFiveAmount = totalFiveAmount;
    }

    /**
     * 金额5同比去年
     */
    public BigDecimal getYoyFiveAmountLast() {
        return yoyFiveAmountLast;
    }

    /**
     * 金额5同比去年
     */
    public void setYoyFiveAmountLast(final BigDecimal yoyFiveAmountLast) {
        this.yoyFiveAmountLast = yoyFiveAmountLast;
    }

    /**
     * 金额5同比前年
     */
    public BigDecimal getYoyFiveAmountBeforeLast() {
        return yoyFiveAmountBeforeLast;
    }

    /**
     * 金额5同比前年
     */
    public void setYoyFiveAmountBeforeLast(final BigDecimal yoyFiveAmountBeforeLast) {
        this.yoyFiveAmountBeforeLast = yoyFiveAmountBeforeLast;
    }

    /**
     * 金额5环比
     */
    public BigDecimal getMomFiveAmount() {
        return momFiveAmount;
    }

    /**
     * 金额5环比
     */
    public void setMomFiveAmount(final BigDecimal momFiveAmount) {
        this.momFiveAmount = momFiveAmount;
    }

    /**
     * 金额5占比
     */
    public BigDecimal getFiveAmountPercentage() {
        return fiveAmountPercentage;
    }

    /**
     * 金额5占比
     */
    public void setFiveAmountPercentage(final BigDecimal fiveAmountPercentage) {
        this.fiveAmountPercentage = fiveAmountPercentage;
    }

    /**
     * 金额6
     */
    public BigDecimal getTotalSixAmount() {
        return totalSixAmount;
    }

    /**
     * 金额6
     */
    public void setTotalSixAmount(final BigDecimal totalSixAmount) {
        this.totalSixAmount = totalSixAmount;
    }

    /**
     * 金额6同比去年
     */
    public BigDecimal getYoySixAmountLast() {
        return yoySixAmountLast;
    }

    /**
     * 金额6同比去年
     */
    public void setYoySixAmountLast(final BigDecimal yoySixAmountLast) {
        this.yoySixAmountLast = yoySixAmountLast;
    }

    /**
     * 金额6同比前年
     */
    public BigDecimal getYoySixAmountBeforeLast() {
        return yoySixAmountBeforeLast;
    }

    /**
     * 金额6同比前年
     */
    public void setYoySixAmountBeforeLast(final BigDecimal yoySixAmountBeforeLast) {
        this.yoySixAmountBeforeLast = yoySixAmountBeforeLast;
    }

    /**
     * 金额6环比
     */
    public BigDecimal getMomSixAmount() {
        return momSixAmount;
    }

    /**
     * 金额6环比
     */
    public void setMomSixAmount(final BigDecimal momSixAmount) {
        this.momSixAmount = momSixAmount;
    }

    /**
     * 金额6占比
     */
    public BigDecimal getSixAmountPercentage() {
        return sixAmountPercentage;
    }

    /**
     * 金额6占比
     */
    public void setSixAmountPercentage(final BigDecimal sixAmountPercentage) {
        this.sixAmountPercentage = sixAmountPercentage;
    }

    /**
     * 金额7
     */
    public BigDecimal getTotalSevenAmount() {
        return totalSevenAmount;
    }

    /**
     * 金额7
     */
    public void setTotalSevenAmount(final BigDecimal totalSevenAmount) {
        this.totalSevenAmount = totalSevenAmount;
    }

    /**
     * 金额7同比去年
     */
    public BigDecimal getYoySevenAmountLast() {
        return yoySevenAmountLast;
    }

    /**
     * 金额7同比去年
     */
    public void setYoySevenAmountLast(final BigDecimal yoySevenAmountLast) {
        this.yoySevenAmountLast = yoySevenAmountLast;
    }

    /**
     * 金额7同比前年
     */
    public BigDecimal getYoySevenAmountBeforeLast() {
        return yoySevenAmountBeforeLast;
    }

    /**
     * 金额7同比前年
     */
    public void setYoySevenAmountBeforeLast(final BigDecimal yoySevenAmountBeforeLast) {
        this.yoySevenAmountBeforeLast = yoySevenAmountBeforeLast;
    }

    /**
     * 金额7环比
     */
    public BigDecimal getMomSevenAmount() {
        return momSevenAmount;
    }

    /**
     * 金额7环比
     */
    public void setMomSevenAmount(final BigDecimal momSevenAmount) {
        this.momSevenAmount = momSevenAmount;
    }

    /**
     * 金额7占比
     */
    public BigDecimal getSevenAmountPercentage() {
        return sevenAmountPercentage;
    }

    /**
     * 金额7占比
     */
    public void setSevenAmountPercentage(final BigDecimal sevenAmountPercentage) {
        this.sevenAmountPercentage = sevenAmountPercentage;
    }

    /**
     * 根据querybu不同字段意义不同（机票均价、酒店间夜均价、火车票均价、用车订单均价）
     */
    public BigDecimal getAvgPrice() {
        return avgPrice;
    }

    /**
     * 根据querybu不同字段意义不同（机票均价、酒店间夜均价、火车票均价、用车订单均价）
     */
    public void setAvgPrice(final BigDecimal avgPrice) {
        this.avgPrice = avgPrice;
    }

    /**
     * 根据querybu不同字段意义不同（机票、酒店、火车、用车）商旅管理服务业费
     */
    public BigDecimal getTotalCorpServiceFee() {
        return totalCorpServiceFee;
    }

    /**
     * 根据querybu不同字段意义不同（机票、酒店、火车、用车）商旅管理服务业费
     */
    public void setTotalCorpServiceFee(final BigDecimal totalCorpServiceFee) {
        this.totalCorpServiceFee = totalCorpServiceFee;
    }

    /**
     * 目前只有机票里程均价
     */
    public BigDecimal getAvgOtherPrice() {
        return avgOtherPrice;
    }

    /**
     * 目前只有机票里程均价
     */
    public void setAvgOtherPrice(final BigDecimal avgOtherPrice) {
        this.avgOtherPrice = avgOtherPrice;
    }

    /**
     * 总行程/票张/订单/间夜
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 总行程/票张/订单/间夜
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 月均行程/票张/订单/间夜
     */
    public BigDecimal getAvgMonthQuantity() {
        return avgMonthQuantity;
    }

    /**
     * 月均行程/票张/订单/间夜
     */
    public void setAvgMonthQuantity(final BigDecimal avgMonthQuantity) {
        this.avgMonthQuantity = avgMonthQuantity;
    }

    /**
     * 同比去年行程/票张/订单/间夜
     */
    public BigDecimal getYoyQuantityLast() {
        return yoyQuantityLast;
    }

    /**
     * 同比去年行程/票张/订单/间夜
     */
    public void setYoyQuantityLast(final BigDecimal yoyQuantityLast) {
        this.yoyQuantityLast = yoyQuantityLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜
     */
    public BigDecimal getYoyQuantityBeforeLast() {
        return yoyQuantityBeforeLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜
     */
    public void setYoyQuantityBeforeLast(final BigDecimal yoyQuantityBeforeLast) {
        this.yoyQuantityBeforeLast = yoyQuantityBeforeLast;
    }

    /**
     * 环比行程/票张/订单/间夜
     */
    public BigDecimal getMomQuantity() {
        return momQuantity;
    }

    /**
     * 环比行程/票张/订单/间夜
     */
    public void setMomQuantity(final BigDecimal momQuantity) {
        this.momQuantity = momQuantity;
    }

    /**
     * 票张/间夜/订单1
     */
    public Integer getTotalOneQuantity() {
        return totalOneQuantity;
    }

    /**
     * 票张/间夜/订单1
     */
    public void setTotalOneQuantity(final Integer totalOneQuantity) {
        this.totalOneQuantity = totalOneQuantity;
    }

    /**
     * 同比去年行程/票张/订单/间夜1
     */
    public BigDecimal getYoyOneQuantityLast() {
        return yoyOneQuantityLast;
    }

    /**
     * 同比去年行程/票张/订单/间夜1
     */
    public void setYoyOneQuantityLast(final BigDecimal yoyOneQuantityLast) {
        this.yoyOneQuantityLast = yoyOneQuantityLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜1
     */
    public BigDecimal getYoyOneQuantityBeforeLast() {
        return yoyOneQuantityBeforeLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜1
     */
    public void setYoyOneQuantityBeforeLast(final BigDecimal yoyOneQuantityBeforeLast) {
        this.yoyOneQuantityBeforeLast = yoyOneQuantityBeforeLast;
    }

    /**
     * 环比行程/票张/订单/间夜1
     */
    public BigDecimal getMomOneQuantity() {
        return momOneQuantity;
    }

    /**
     * 环比行程/票张/订单/间夜1
     */
    public void setMomOneQuantity(final BigDecimal momOneQuantity) {
        this.momOneQuantity = momOneQuantity;
    }

    /**
     * 行程/票张/订单/间夜1占比
     */
    public BigDecimal getOneQuantityPercentage() {
        return oneQuantityPercentage;
    }

    /**
     * 行程/票张/订单/间夜1占比
     */
    public void setOneQuantityPercentage(final BigDecimal oneQuantityPercentage) {
        this.oneQuantityPercentage = oneQuantityPercentage;
    }

    /**
     * 行程/票张/间夜/订单2
     */
    public Integer getTotalTwoQuantity() {
        return totalTwoQuantity;
    }

    /**
     * 行程/票张/间夜/订单2
     */
    public void setTotalTwoQuantity(final Integer totalTwoQuantity) {
        this.totalTwoQuantity = totalTwoQuantity;
    }

    /**
     * 同比去年行程/票张/订单/间夜2
     */
    public BigDecimal getYoyTwoQuantityLast() {
        return yoyTwoQuantityLast;
    }

    /**
     * 同比去年行程/票张/订单/间夜2
     */
    public void setYoyTwoQuantityLast(final BigDecimal yoyTwoQuantityLast) {
        this.yoyTwoQuantityLast = yoyTwoQuantityLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜2
     */
    public BigDecimal getYoyTwoQuantityBeforeLast() {
        return yoyTwoQuantityBeforeLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜2
     */
    public void setYoyTwoQuantityBeforeLast(final BigDecimal yoyTwoQuantityBeforeLast) {
        this.yoyTwoQuantityBeforeLast = yoyTwoQuantityBeforeLast;
    }

    /**
     * 环比行程/票张/订单/间夜2
     */
    public BigDecimal getMomTwoQuantity() {
        return momTwoQuantity;
    }

    /**
     * 环比行程/票张/订单/间夜2
     */
    public void setMomTwoQuantity(final BigDecimal momTwoQuantity) {
        this.momTwoQuantity = momTwoQuantity;
    }

    /**
     * 行程/票张/订单/间夜2占比
     */
    public BigDecimal getTwoQuantityPercentage() {
        return twoQuantityPercentage;
    }

    /**
     * 行程/票张/订单/间夜2占比
     */
    public void setTwoQuantityPercentage(final BigDecimal twoQuantityPercentage) {
        this.twoQuantityPercentage = twoQuantityPercentage;
    }

    /**
     * 票张/间夜/订单3
     */
    public Integer getTotalThreeQuantity() {
        return totalThreeQuantity;
    }

    /**
     * 票张/间夜/订单3
     */
    public void setTotalThreeQuantity(final Integer totalThreeQuantity) {
        this.totalThreeQuantity = totalThreeQuantity;
    }

    /**
     * 同比去年行程/票张/订单/间夜3
     */
    public BigDecimal getYoyThreeQuantityLast() {
        return yoyThreeQuantityLast;
    }

    /**
     * 同比去年行程/票张/订单/间夜3
     */
    public void setYoyThreeQuantityLast(final BigDecimal yoyThreeQuantityLast) {
        this.yoyThreeQuantityLast = yoyThreeQuantityLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜3
     */
    public BigDecimal getYoyThreeQuantityBeforeLast() {
        return yoyThreeQuantityBeforeLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜3
     */
    public void setYoyThreeQuantityBeforeLast(final BigDecimal yoyThreeQuantityBeforeLast) {
        this.yoyThreeQuantityBeforeLast = yoyThreeQuantityBeforeLast;
    }

    /**
     * 环比行程/票张/订单/间夜3
     */
    public BigDecimal getMomThreeQuantity() {
        return momThreeQuantity;
    }

    /**
     * 环比行程/票张/订单/间夜3
     */
    public void setMomThreeQuantity(final BigDecimal momThreeQuantity) {
        this.momThreeQuantity = momThreeQuantity;
    }

    /**
     * 行程/票张/订单/间夜3占比
     */
    public BigDecimal getThreeQuantityPercentage() {
        return threeQuantityPercentage;
    }

    /**
     * 行程/票张/订单/间夜3占比
     */
    public void setThreeQuantityPercentage(final BigDecimal threeQuantityPercentage) {
        this.threeQuantityPercentage = threeQuantityPercentage;
    }

    /**
     * 票张/间夜/订单4
     */
    public Integer getTotalFourQuantity() {
        return totalFourQuantity;
    }

    /**
     * 票张/间夜/订单4
     */
    public void setTotalFourQuantity(final Integer totalFourQuantity) {
        this.totalFourQuantity = totalFourQuantity;
    }

    /**
     * 同比去年行程/票张/订单/间夜4
     */
    public BigDecimal getYoyFourQuantityLast() {
        return yoyFourQuantityLast;
    }

    /**
     * 同比去年行程/票张/订单/间夜4
     */
    public void setYoyFourQuantityLast(final BigDecimal yoyFourQuantityLast) {
        this.yoyFourQuantityLast = yoyFourQuantityLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜4
     */
    public BigDecimal getYoyFourQuantityBeforeLast() {
        return yoyFourQuantityBeforeLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜4
     */
    public void setYoyFourQuantityBeforeLast(final BigDecimal yoyFourQuantityBeforeLast) {
        this.yoyFourQuantityBeforeLast = yoyFourQuantityBeforeLast;
    }

    /**
     * 环比行程/票张/订单/间夜4
     */
    public BigDecimal getMomFourQuantity() {
        return momFourQuantity;
    }

    /**
     * 环比行程/票张/订单/间夜4
     */
    public void setMomFourQuantity(final BigDecimal momFourQuantity) {
        this.momFourQuantity = momFourQuantity;
    }

    /**
     * 行程/票张/订单/间夜4占比
     */
    public BigDecimal getFourQuantityPercentage() {
        return fourQuantityPercentage;
    }

    /**
     * 行程/票张/订单/间夜4占比
     */
    public void setFourQuantityPercentage(final BigDecimal fourQuantityPercentage) {
        this.fourQuantityPercentage = fourQuantityPercentage;
    }

    /**
     * 票张/间夜/订单5
     */
    public Integer getTotalFiveQuantity() {
        return totalFiveQuantity;
    }

    /**
     * 票张/间夜/订单5
     */
    public void setTotalFiveQuantity(final Integer totalFiveQuantity) {
        this.totalFiveQuantity = totalFiveQuantity;
    }

    /**
     * 同比去年行程/票张/订单/间夜5
     */
    public BigDecimal getYoyFiveQuantityLast() {
        return yoyFiveQuantityLast;
    }

    /**
     * 同比去年行程/票张/订单/间夜5
     */
    public void setYoyFiveQuantityLast(final BigDecimal yoyFiveQuantityLast) {
        this.yoyFiveQuantityLast = yoyFiveQuantityLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜5
     */
    public BigDecimal getYoyFiveQuantityBeforeLast() {
        return yoyFiveQuantityBeforeLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜5
     */
    public void setYoyFiveQuantityBeforeLast(final BigDecimal yoyFiveQuantityBeforeLast) {
        this.yoyFiveQuantityBeforeLast = yoyFiveQuantityBeforeLast;
    }

    /**
     * 环比行程/票张/订单/间夜5
     */
    public BigDecimal getMomFiveQuantity() {
        return momFiveQuantity;
    }

    /**
     * 环比行程/票张/订单/间夜5
     */
    public void setMomFiveQuantity(final BigDecimal momFiveQuantity) {
        this.momFiveQuantity = momFiveQuantity;
    }

    /**
     * 行程/票张/订单/间夜5占比
     */
    public BigDecimal getFiveQuantityPercentage() {
        return fiveQuantityPercentage;
    }

    /**
     * 行程/票张/订单/间夜5占比
     */
    public void setFiveQuantityPercentage(final BigDecimal fiveQuantityPercentage) {
        this.fiveQuantityPercentage = fiveQuantityPercentage;
    }

    /**
     * 票张/间夜/订单6
     */
    public Integer getTotalSixQuantity() {
        return totalSixQuantity;
    }

    /**
     * 票张/间夜/订单6
     */
    public void setTotalSixQuantity(final Integer totalSixQuantity) {
        this.totalSixQuantity = totalSixQuantity;
    }

    /**
     * 同比去年行程/票张/订单/间夜6
     */
    public BigDecimal getYoySixQuantityLast() {
        return yoySixQuantityLast;
    }

    /**
     * 同比去年行程/票张/订单/间夜6
     */
    public void setYoySixQuantityLast(final BigDecimal yoySixQuantityLast) {
        this.yoySixQuantityLast = yoySixQuantityLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜6
     */
    public BigDecimal getYoySixQuantityBeforeLast() {
        return yoySixQuantityBeforeLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜6
     */
    public void setYoySixQuantityBeforeLast(final BigDecimal yoySixQuantityBeforeLast) {
        this.yoySixQuantityBeforeLast = yoySixQuantityBeforeLast;
    }

    /**
     * 环比行程/票张/订单/间夜6
     */
    public BigDecimal getMomSixQuantity() {
        return momSixQuantity;
    }

    /**
     * 环比行程/票张/订单/间夜6
     */
    public void setMomSixQuantity(final BigDecimal momSixQuantity) {
        this.momSixQuantity = momSixQuantity;
    }

    /**
     * 行程/票张/订单/间夜6占比
     */
    public BigDecimal getSixQuantityPercentage() {
        return sixQuantityPercentage;
    }

    /**
     * 行程/票张/订单/间夜6占比
     */
    public void setSixQuantityPercentage(final BigDecimal sixQuantityPercentage) {
        this.sixQuantityPercentage = sixQuantityPercentage;
    }

    /**
     * 票张/间夜/订单7
     */
    public Integer getTotalSevenQuantity() {
        return totalSevenQuantity;
    }

    /**
     * 票张/间夜/订单7
     */
    public void setTotalSevenQuantity(final Integer totalSevenQuantity) {
        this.totalSevenQuantity = totalSevenQuantity;
    }

    /**
     * 同比去年行程/票张/订单/间夜7
     */
    public BigDecimal getYoySevenQuantityLast() {
        return yoySevenQuantityLast;
    }

    /**
     * 同比去年行程/票张/订单/间夜7
     */
    public void setYoySevenQuantityLast(final BigDecimal yoySevenQuantityLast) {
        this.yoySevenQuantityLast = yoySevenQuantityLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜7
     */
    public BigDecimal getYoySevenQuantityBeforeLast() {
        return yoySevenQuantityBeforeLast;
    }

    /**
     * 同比前年行程/票张/订单/间夜7
     */
    public void setYoySevenQuantityBeforeLast(final BigDecimal yoySevenQuantityBeforeLast) {
        this.yoySevenQuantityBeforeLast = yoySevenQuantityBeforeLast;
    }

    /**
     * 环比行程/票张/订单/间夜7
     */
    public BigDecimal getMomSevenQuantity() {
        return momSevenQuantity;
    }

    /**
     * 环比行程/票张/订单/间夜7
     */
    public void setMomSevenQuantity(final BigDecimal momSevenQuantity) {
        this.momSevenQuantity = momSevenQuantity;
    }

    /**
     * 行程/票张/订单/间夜7占比
     */
    public BigDecimal getSevenQuantityPercentage() {
        return sevenQuantityPercentage;
    }

    /**
     * 行程/票张/订单/间夜7占比
     */
    public void setSevenQuantityPercentage(final BigDecimal sevenQuantityPercentage) {
        this.sevenQuantityPercentage = sevenQuantityPercentage;
    }

    /**
     * 根据querybu不同字段意义不同（机票退票张数、酒店退订间夜、火车退票张数）
     */
    public Integer getRefundQuantity() {
        return refundQuantity;
    }

    /**
     * 根据querybu不同字段意义不同（机票退票张数、酒店退订间夜、火车退票张数）
     */
    public void setRefundQuantity(final Integer refundQuantity) {
        this.refundQuantity = refundQuantity;
    }

    /**
     * 根据querybu不同字段意义不同（机票改签张数、火车改签张数）
     */
    public Integer getReBookQuantity() {
        return reBookQuantity;
    }

    /**
     * 根据querybu不同字段意义不同（机票改签张数、火车改签张数）
     */
    public void setReBookQuantity(final Integer reBookQuantity) {
        this.reBookQuantity = reBookQuantity;
    }

    /**
     * 总订单数(机、酒、火)
     */
    public Integer getTotalCntOrder() {
        return totalCntOrder;
    }

    /**
     * 总订单数(机、酒、火)
     */
    public void setTotalCntOrder(final Integer totalCntOrder) {
        this.totalCntOrder = totalCntOrder;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportConsume other = (OnlineReportConsume)obj;
        return
            Objects.equal(this.totalAmount, other.totalAmount) &&
            Objects.equal(this.avgMonthAmount, other.avgMonthAmount) &&
            Objects.equal(this.yoyAmountLast, other.yoyAmountLast) &&
            Objects.equal(this.yoyAmountBeforeLast, other.yoyAmountBeforeLast) &&
            Objects.equal(this.momAmount, other.momAmount) &&
            Objects.equal(this.totalOneAmount, other.totalOneAmount) &&
            Objects.equal(this.yoyOneAmountLast, other.yoyOneAmountLast) &&
            Objects.equal(this.yoyOneAmountBeforeLast, other.yoyOneAmountBeforeLast) &&
            Objects.equal(this.momOneAmount, other.momOneAmount) &&
            Objects.equal(this.oneAmountPercentage, other.oneAmountPercentage) &&
            Objects.equal(this.totalTwoAmount, other.totalTwoAmount) &&
            Objects.equal(this.yoyTwoAmountLast, other.yoyTwoAmountLast) &&
            Objects.equal(this.yoyTwoAmountBeforeLast, other.yoyTwoAmountBeforeLast) &&
            Objects.equal(this.momTwoAmount, other.momTwoAmount) &&
            Objects.equal(this.twoAmountPercentage, other.twoAmountPercentage) &&
            Objects.equal(this.totalThreeAmount, other.totalThreeAmount) &&
            Objects.equal(this.yoyThreeAmountLast, other.yoyThreeAmountLast) &&
            Objects.equal(this.yoyThreeAmountBeforeLast, other.yoyThreeAmountBeforeLast) &&
            Objects.equal(this.momThreeAmount, other.momThreeAmount) &&
            Objects.equal(this.threeAmountPercentage, other.threeAmountPercentage) &&
            Objects.equal(this.totalFourAmount, other.totalFourAmount) &&
            Objects.equal(this.yoyFourAmountLast, other.yoyFourAmountLast) &&
            Objects.equal(this.yoyFourAmountBeforeLast, other.yoyFourAmountBeforeLast) &&
            Objects.equal(this.momFourAmount, other.momFourAmount) &&
            Objects.equal(this.fourAmountPercentage, other.fourAmountPercentage) &&
            Objects.equal(this.totalFiveAmount, other.totalFiveAmount) &&
            Objects.equal(this.yoyFiveAmountLast, other.yoyFiveAmountLast) &&
            Objects.equal(this.yoyFiveAmountBeforeLast, other.yoyFiveAmountBeforeLast) &&
            Objects.equal(this.momFiveAmount, other.momFiveAmount) &&
            Objects.equal(this.fiveAmountPercentage, other.fiveAmountPercentage) &&
            Objects.equal(this.totalSixAmount, other.totalSixAmount) &&
            Objects.equal(this.yoySixAmountLast, other.yoySixAmountLast) &&
            Objects.equal(this.yoySixAmountBeforeLast, other.yoySixAmountBeforeLast) &&
            Objects.equal(this.momSixAmount, other.momSixAmount) &&
            Objects.equal(this.sixAmountPercentage, other.sixAmountPercentage) &&
            Objects.equal(this.totalSevenAmount, other.totalSevenAmount) &&
            Objects.equal(this.yoySevenAmountLast, other.yoySevenAmountLast) &&
            Objects.equal(this.yoySevenAmountBeforeLast, other.yoySevenAmountBeforeLast) &&
            Objects.equal(this.momSevenAmount, other.momSevenAmount) &&
            Objects.equal(this.sevenAmountPercentage, other.sevenAmountPercentage) &&
            Objects.equal(this.avgPrice, other.avgPrice) &&
            Objects.equal(this.totalCorpServiceFee, other.totalCorpServiceFee) &&
            Objects.equal(this.avgOtherPrice, other.avgOtherPrice) &&
            Objects.equal(this.totalQuantity, other.totalQuantity) &&
            Objects.equal(this.avgMonthQuantity, other.avgMonthQuantity) &&
            Objects.equal(this.yoyQuantityLast, other.yoyQuantityLast) &&
            Objects.equal(this.yoyQuantityBeforeLast, other.yoyQuantityBeforeLast) &&
            Objects.equal(this.momQuantity, other.momQuantity) &&
            Objects.equal(this.totalOneQuantity, other.totalOneQuantity) &&
            Objects.equal(this.yoyOneQuantityLast, other.yoyOneQuantityLast) &&
            Objects.equal(this.yoyOneQuantityBeforeLast, other.yoyOneQuantityBeforeLast) &&
            Objects.equal(this.momOneQuantity, other.momOneQuantity) &&
            Objects.equal(this.oneQuantityPercentage, other.oneQuantityPercentage) &&
            Objects.equal(this.totalTwoQuantity, other.totalTwoQuantity) &&
            Objects.equal(this.yoyTwoQuantityLast, other.yoyTwoQuantityLast) &&
            Objects.equal(this.yoyTwoQuantityBeforeLast, other.yoyTwoQuantityBeforeLast) &&
            Objects.equal(this.momTwoQuantity, other.momTwoQuantity) &&
            Objects.equal(this.twoQuantityPercentage, other.twoQuantityPercentage) &&
            Objects.equal(this.totalThreeQuantity, other.totalThreeQuantity) &&
            Objects.equal(this.yoyThreeQuantityLast, other.yoyThreeQuantityLast) &&
            Objects.equal(this.yoyThreeQuantityBeforeLast, other.yoyThreeQuantityBeforeLast) &&
            Objects.equal(this.momThreeQuantity, other.momThreeQuantity) &&
            Objects.equal(this.threeQuantityPercentage, other.threeQuantityPercentage) &&
            Objects.equal(this.totalFourQuantity, other.totalFourQuantity) &&
            Objects.equal(this.yoyFourQuantityLast, other.yoyFourQuantityLast) &&
            Objects.equal(this.yoyFourQuantityBeforeLast, other.yoyFourQuantityBeforeLast) &&
            Objects.equal(this.momFourQuantity, other.momFourQuantity) &&
            Objects.equal(this.fourQuantityPercentage, other.fourQuantityPercentage) &&
            Objects.equal(this.totalFiveQuantity, other.totalFiveQuantity) &&
            Objects.equal(this.yoyFiveQuantityLast, other.yoyFiveQuantityLast) &&
            Objects.equal(this.yoyFiveQuantityBeforeLast, other.yoyFiveQuantityBeforeLast) &&
            Objects.equal(this.momFiveQuantity, other.momFiveQuantity) &&
            Objects.equal(this.fiveQuantityPercentage, other.fiveQuantityPercentage) &&
            Objects.equal(this.totalSixQuantity, other.totalSixQuantity) &&
            Objects.equal(this.yoySixQuantityLast, other.yoySixQuantityLast) &&
            Objects.equal(this.yoySixQuantityBeforeLast, other.yoySixQuantityBeforeLast) &&
            Objects.equal(this.momSixQuantity, other.momSixQuantity) &&
            Objects.equal(this.sixQuantityPercentage, other.sixQuantityPercentage) &&
            Objects.equal(this.totalSevenQuantity, other.totalSevenQuantity) &&
            Objects.equal(this.yoySevenQuantityLast, other.yoySevenQuantityLast) &&
            Objects.equal(this.yoySevenQuantityBeforeLast, other.yoySevenQuantityBeforeLast) &&
            Objects.equal(this.momSevenQuantity, other.momSevenQuantity) &&
            Objects.equal(this.sevenQuantityPercentage, other.sevenQuantityPercentage) &&
            Objects.equal(this.refundQuantity, other.refundQuantity) &&
            Objects.equal(this.reBookQuantity, other.reBookQuantity) &&
            Objects.equal(this.totalCntOrder, other.totalCntOrder);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.avgMonthAmount == null ? 0 : this.avgMonthAmount.hashCode());
        result = 31 * result + (this.yoyAmountLast == null ? 0 : this.yoyAmountLast.hashCode());
        result = 31 * result + (this.yoyAmountBeforeLast == null ? 0 : this.yoyAmountBeforeLast.hashCode());
        result = 31 * result + (this.momAmount == null ? 0 : this.momAmount.hashCode());
        result = 31 * result + (this.totalOneAmount == null ? 0 : this.totalOneAmount.hashCode());
        result = 31 * result + (this.yoyOneAmountLast == null ? 0 : this.yoyOneAmountLast.hashCode());
        result = 31 * result + (this.yoyOneAmountBeforeLast == null ? 0 : this.yoyOneAmountBeforeLast.hashCode());
        result = 31 * result + (this.momOneAmount == null ? 0 : this.momOneAmount.hashCode());
        result = 31 * result + (this.oneAmountPercentage == null ? 0 : this.oneAmountPercentage.hashCode());
        result = 31 * result + (this.totalTwoAmount == null ? 0 : this.totalTwoAmount.hashCode());
        result = 31 * result + (this.yoyTwoAmountLast == null ? 0 : this.yoyTwoAmountLast.hashCode());
        result = 31 * result + (this.yoyTwoAmountBeforeLast == null ? 0 : this.yoyTwoAmountBeforeLast.hashCode());
        result = 31 * result + (this.momTwoAmount == null ? 0 : this.momTwoAmount.hashCode());
        result = 31 * result + (this.twoAmountPercentage == null ? 0 : this.twoAmountPercentage.hashCode());
        result = 31 * result + (this.totalThreeAmount == null ? 0 : this.totalThreeAmount.hashCode());
        result = 31 * result + (this.yoyThreeAmountLast == null ? 0 : this.yoyThreeAmountLast.hashCode());
        result = 31 * result + (this.yoyThreeAmountBeforeLast == null ? 0 : this.yoyThreeAmountBeforeLast.hashCode());
        result = 31 * result + (this.momThreeAmount == null ? 0 : this.momThreeAmount.hashCode());
        result = 31 * result + (this.threeAmountPercentage == null ? 0 : this.threeAmountPercentage.hashCode());
        result = 31 * result + (this.totalFourAmount == null ? 0 : this.totalFourAmount.hashCode());
        result = 31 * result + (this.yoyFourAmountLast == null ? 0 : this.yoyFourAmountLast.hashCode());
        result = 31 * result + (this.yoyFourAmountBeforeLast == null ? 0 : this.yoyFourAmountBeforeLast.hashCode());
        result = 31 * result + (this.momFourAmount == null ? 0 : this.momFourAmount.hashCode());
        result = 31 * result + (this.fourAmountPercentage == null ? 0 : this.fourAmountPercentage.hashCode());
        result = 31 * result + (this.totalFiveAmount == null ? 0 : this.totalFiveAmount.hashCode());
        result = 31 * result + (this.yoyFiveAmountLast == null ? 0 : this.yoyFiveAmountLast.hashCode());
        result = 31 * result + (this.yoyFiveAmountBeforeLast == null ? 0 : this.yoyFiveAmountBeforeLast.hashCode());
        result = 31 * result + (this.momFiveAmount == null ? 0 : this.momFiveAmount.hashCode());
        result = 31 * result + (this.fiveAmountPercentage == null ? 0 : this.fiveAmountPercentage.hashCode());
        result = 31 * result + (this.totalSixAmount == null ? 0 : this.totalSixAmount.hashCode());
        result = 31 * result + (this.yoySixAmountLast == null ? 0 : this.yoySixAmountLast.hashCode());
        result = 31 * result + (this.yoySixAmountBeforeLast == null ? 0 : this.yoySixAmountBeforeLast.hashCode());
        result = 31 * result + (this.momSixAmount == null ? 0 : this.momSixAmount.hashCode());
        result = 31 * result + (this.sixAmountPercentage == null ? 0 : this.sixAmountPercentage.hashCode());
        result = 31 * result + (this.totalSevenAmount == null ? 0 : this.totalSevenAmount.hashCode());
        result = 31 * result + (this.yoySevenAmountLast == null ? 0 : this.yoySevenAmountLast.hashCode());
        result = 31 * result + (this.yoySevenAmountBeforeLast == null ? 0 : this.yoySevenAmountBeforeLast.hashCode());
        result = 31 * result + (this.momSevenAmount == null ? 0 : this.momSevenAmount.hashCode());
        result = 31 * result + (this.sevenAmountPercentage == null ? 0 : this.sevenAmountPercentage.hashCode());
        result = 31 * result + (this.avgPrice == null ? 0 : this.avgPrice.hashCode());
        result = 31 * result + (this.totalCorpServiceFee == null ? 0 : this.totalCorpServiceFee.hashCode());
        result = 31 * result + (this.avgOtherPrice == null ? 0 : this.avgOtherPrice.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.avgMonthQuantity == null ? 0 : this.avgMonthQuantity.hashCode());
        result = 31 * result + (this.yoyQuantityLast == null ? 0 : this.yoyQuantityLast.hashCode());
        result = 31 * result + (this.yoyQuantityBeforeLast == null ? 0 : this.yoyQuantityBeforeLast.hashCode());
        result = 31 * result + (this.momQuantity == null ? 0 : this.momQuantity.hashCode());
        result = 31 * result + (this.totalOneQuantity == null ? 0 : this.totalOneQuantity.hashCode());
        result = 31 * result + (this.yoyOneQuantityLast == null ? 0 : this.yoyOneQuantityLast.hashCode());
        result = 31 * result + (this.yoyOneQuantityBeforeLast == null ? 0 : this.yoyOneQuantityBeforeLast.hashCode());
        result = 31 * result + (this.momOneQuantity == null ? 0 : this.momOneQuantity.hashCode());
        result = 31 * result + (this.oneQuantityPercentage == null ? 0 : this.oneQuantityPercentage.hashCode());
        result = 31 * result + (this.totalTwoQuantity == null ? 0 : this.totalTwoQuantity.hashCode());
        result = 31 * result + (this.yoyTwoQuantityLast == null ? 0 : this.yoyTwoQuantityLast.hashCode());
        result = 31 * result + (this.yoyTwoQuantityBeforeLast == null ? 0 : this.yoyTwoQuantityBeforeLast.hashCode());
        result = 31 * result + (this.momTwoQuantity == null ? 0 : this.momTwoQuantity.hashCode());
        result = 31 * result + (this.twoQuantityPercentage == null ? 0 : this.twoQuantityPercentage.hashCode());
        result = 31 * result + (this.totalThreeQuantity == null ? 0 : this.totalThreeQuantity.hashCode());
        result = 31 * result + (this.yoyThreeQuantityLast == null ? 0 : this.yoyThreeQuantityLast.hashCode());
        result = 31 * result + (this.yoyThreeQuantityBeforeLast == null ? 0 : this.yoyThreeQuantityBeforeLast.hashCode());
        result = 31 * result + (this.momThreeQuantity == null ? 0 : this.momThreeQuantity.hashCode());
        result = 31 * result + (this.threeQuantityPercentage == null ? 0 : this.threeQuantityPercentage.hashCode());
        result = 31 * result + (this.totalFourQuantity == null ? 0 : this.totalFourQuantity.hashCode());
        result = 31 * result + (this.yoyFourQuantityLast == null ? 0 : this.yoyFourQuantityLast.hashCode());
        result = 31 * result + (this.yoyFourQuantityBeforeLast == null ? 0 : this.yoyFourQuantityBeforeLast.hashCode());
        result = 31 * result + (this.momFourQuantity == null ? 0 : this.momFourQuantity.hashCode());
        result = 31 * result + (this.fourQuantityPercentage == null ? 0 : this.fourQuantityPercentage.hashCode());
        result = 31 * result + (this.totalFiveQuantity == null ? 0 : this.totalFiveQuantity.hashCode());
        result = 31 * result + (this.yoyFiveQuantityLast == null ? 0 : this.yoyFiveQuantityLast.hashCode());
        result = 31 * result + (this.yoyFiveQuantityBeforeLast == null ? 0 : this.yoyFiveQuantityBeforeLast.hashCode());
        result = 31 * result + (this.momFiveQuantity == null ? 0 : this.momFiveQuantity.hashCode());
        result = 31 * result + (this.fiveQuantityPercentage == null ? 0 : this.fiveQuantityPercentage.hashCode());
        result = 31 * result + (this.totalSixQuantity == null ? 0 : this.totalSixQuantity.hashCode());
        result = 31 * result + (this.yoySixQuantityLast == null ? 0 : this.yoySixQuantityLast.hashCode());
        result = 31 * result + (this.yoySixQuantityBeforeLast == null ? 0 : this.yoySixQuantityBeforeLast.hashCode());
        result = 31 * result + (this.momSixQuantity == null ? 0 : this.momSixQuantity.hashCode());
        result = 31 * result + (this.sixQuantityPercentage == null ? 0 : this.sixQuantityPercentage.hashCode());
        result = 31 * result + (this.totalSevenQuantity == null ? 0 : this.totalSevenQuantity.hashCode());
        result = 31 * result + (this.yoySevenQuantityLast == null ? 0 : this.yoySevenQuantityLast.hashCode());
        result = 31 * result + (this.yoySevenQuantityBeforeLast == null ? 0 : this.yoySevenQuantityBeforeLast.hashCode());
        result = 31 * result + (this.momSevenQuantity == null ? 0 : this.momSevenQuantity.hashCode());
        result = 31 * result + (this.sevenQuantityPercentage == null ? 0 : this.sevenQuantityPercentage.hashCode());
        result = 31 * result + (this.refundQuantity == null ? 0 : this.refundQuantity.hashCode());
        result = 31 * result + (this.reBookQuantity == null ? 0 : this.reBookQuantity.hashCode());
        result = 31 * result + (this.totalCntOrder == null ? 0 : this.totalCntOrder.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("totalAmount", totalAmount)
            .add("avgMonthAmount", avgMonthAmount)
            .add("yoyAmountLast", yoyAmountLast)
            .add("yoyAmountBeforeLast", yoyAmountBeforeLast)
            .add("momAmount", momAmount)
            .add("totalOneAmount", totalOneAmount)
            .add("yoyOneAmountLast", yoyOneAmountLast)
            .add("yoyOneAmountBeforeLast", yoyOneAmountBeforeLast)
            .add("momOneAmount", momOneAmount)
            .add("oneAmountPercentage", oneAmountPercentage)
            .add("totalTwoAmount", totalTwoAmount)
            .add("yoyTwoAmountLast", yoyTwoAmountLast)
            .add("yoyTwoAmountBeforeLast", yoyTwoAmountBeforeLast)
            .add("momTwoAmount", momTwoAmount)
            .add("twoAmountPercentage", twoAmountPercentage)
            .add("totalThreeAmount", totalThreeAmount)
            .add("yoyThreeAmountLast", yoyThreeAmountLast)
            .add("yoyThreeAmountBeforeLast", yoyThreeAmountBeforeLast)
            .add("momThreeAmount", momThreeAmount)
            .add("threeAmountPercentage", threeAmountPercentage)
            .add("totalFourAmount", totalFourAmount)
            .add("yoyFourAmountLast", yoyFourAmountLast)
            .add("yoyFourAmountBeforeLast", yoyFourAmountBeforeLast)
            .add("momFourAmount", momFourAmount)
            .add("fourAmountPercentage", fourAmountPercentage)
            .add("totalFiveAmount", totalFiveAmount)
            .add("yoyFiveAmountLast", yoyFiveAmountLast)
            .add("yoyFiveAmountBeforeLast", yoyFiveAmountBeforeLast)
            .add("momFiveAmount", momFiveAmount)
            .add("fiveAmountPercentage", fiveAmountPercentage)
            .add("totalSixAmount", totalSixAmount)
            .add("yoySixAmountLast", yoySixAmountLast)
            .add("yoySixAmountBeforeLast", yoySixAmountBeforeLast)
            .add("momSixAmount", momSixAmount)
            .add("sixAmountPercentage", sixAmountPercentage)
            .add("totalSevenAmount", totalSevenAmount)
            .add("yoySevenAmountLast", yoySevenAmountLast)
            .add("yoySevenAmountBeforeLast", yoySevenAmountBeforeLast)
            .add("momSevenAmount", momSevenAmount)
            .add("sevenAmountPercentage", sevenAmountPercentage)
            .add("avgPrice", avgPrice)
            .add("totalCorpServiceFee", totalCorpServiceFee)
            .add("avgOtherPrice", avgOtherPrice)
            .add("totalQuantity", totalQuantity)
            .add("avgMonthQuantity", avgMonthQuantity)
            .add("yoyQuantityLast", yoyQuantityLast)
            .add("yoyQuantityBeforeLast", yoyQuantityBeforeLast)
            .add("momQuantity", momQuantity)
            .add("totalOneQuantity", totalOneQuantity)
            .add("yoyOneQuantityLast", yoyOneQuantityLast)
            .add("yoyOneQuantityBeforeLast", yoyOneQuantityBeforeLast)
            .add("momOneQuantity", momOneQuantity)
            .add("oneQuantityPercentage", oneQuantityPercentage)
            .add("totalTwoQuantity", totalTwoQuantity)
            .add("yoyTwoQuantityLast", yoyTwoQuantityLast)
            .add("yoyTwoQuantityBeforeLast", yoyTwoQuantityBeforeLast)
            .add("momTwoQuantity", momTwoQuantity)
            .add("twoQuantityPercentage", twoQuantityPercentage)
            .add("totalThreeQuantity", totalThreeQuantity)
            .add("yoyThreeQuantityLast", yoyThreeQuantityLast)
            .add("yoyThreeQuantityBeforeLast", yoyThreeQuantityBeforeLast)
            .add("momThreeQuantity", momThreeQuantity)
            .add("threeQuantityPercentage", threeQuantityPercentage)
            .add("totalFourQuantity", totalFourQuantity)
            .add("yoyFourQuantityLast", yoyFourQuantityLast)
            .add("yoyFourQuantityBeforeLast", yoyFourQuantityBeforeLast)
            .add("momFourQuantity", momFourQuantity)
            .add("fourQuantityPercentage", fourQuantityPercentage)
            .add("totalFiveQuantity", totalFiveQuantity)
            .add("yoyFiveQuantityLast", yoyFiveQuantityLast)
            .add("yoyFiveQuantityBeforeLast", yoyFiveQuantityBeforeLast)
            .add("momFiveQuantity", momFiveQuantity)
            .add("fiveQuantityPercentage", fiveQuantityPercentage)
            .add("totalSixQuantity", totalSixQuantity)
            .add("yoySixQuantityLast", yoySixQuantityLast)
            .add("yoySixQuantityBeforeLast", yoySixQuantityBeforeLast)
            .add("momSixQuantity", momSixQuantity)
            .add("sixQuantityPercentage", sixQuantityPercentage)
            .add("totalSevenQuantity", totalSevenQuantity)
            .add("yoySevenQuantityLast", yoySevenQuantityLast)
            .add("yoySevenQuantityBeforeLast", yoySevenQuantityBeforeLast)
            .add("momSevenQuantity", momSevenQuantity)
            .add("sevenQuantityPercentage", sevenQuantityPercentage)
            .add("refundQuantity", refundQuantity)
            .add("reBookQuantity", reBookQuantity)
            .add("totalCntOrder", totalCntOrder)
            .toString();
    }
}
