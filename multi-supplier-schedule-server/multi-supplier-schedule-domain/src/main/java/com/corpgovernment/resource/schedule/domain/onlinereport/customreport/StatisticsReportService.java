/*
package com.corpgovernment.resource.schedule.domain.onlinereport.customreport;


import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.AccountInfoBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.CorpAndAccountInfoBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.CustomReportInfo;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.DimCity;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.DimCountry;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.DimProvince;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.ReportUserDefinedOutput;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.UserDefinedFilter;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.GlobalConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.RedisKeyConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.dao.CustomReportInfoDAO;
import com.corpgovernment.resource.schedule.domain.onlinereport.mapper.MapperUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.JsonUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.SharkUtils;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.DimensionEnum;
import onlinereport.enums.FilterNameEnum;
import onlinereport.enums.SharkLocaleEnum;
import onlinereport.enums.YesOrNotEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

*/
/**
 * Auther:abguo
 * Date:2019/8/17
 * Description:
 * Project:onlinereportweb
 *//*

@Slf4j
public abstract class StatisticsReportService {

    protected static final String LOG_TITLE = StatisticsReportService.class.getSimpleName();

    */
/**
     * 日记tag上下文
     *//*

    @Autowired
    protected ThreadContext logTagContext;

    @Autowired
    private CustomReportService service;


    private ReportStaticDataQueryService reportStaticDataQueryService;


    protected abstract CustomStatisticsDataHandler getStatisticsDataHandler();

    */
/**
     * 添加用户自定义项的值
     *
     * @param result
     * @param input
     *//*

    protected void addUserDefiendDataToResult(ReportUserDefinedOutput result, ReportUserDefinedInput input) {
        //[筛选项，[小写值]]
        Map<String, HashSet<String>> mapFilterValue = getFilterMap(input);
        if (!mapFilterValue.containsKey(FilterNameEnum.CUSTOMREPORTINFOID.getName())) {
            return;
        }
        long customReportInfoID = Long.parseLong(mapFilterValue.get(FilterNameEnum.CUSTOMREPORTINFOID.getName()).iterator().next());
        String logonUID = getFilterValue(input.getFilterList(), FilterNameEnum.LOGONUID);
        String lang = getFilterValue(input.getFilterList(), FilterNameEnum.LANG);
        List<Map<String, String>> dataSourceList = result.getDataSourceList();
        //从数据库查询配置的维度
        HashSet<String> dimensionSet = getConfigDimensionList(mapFilterValue, customReportInfoID);
        if (CollectionUtils.isEmpty(dimensionSet)) {
            saveFilterToRedis(input, customReportInfoID);
            updateReportFilter(input.getFilterList(), customReportInfoID);
            return;
        }
        //初始化需要返回的筛选项[FilterNameEnum，initdata]
        Map<String, List<UserDefinedFilter>> mapResult = getFilterNameEnumUserDefinedFilterMap(dimensionSet);
        //从dataSourceList获取对应字段的值填入mapResult
        addFilterValueFromSource(dataSourceList, dimensionSet, mapResult);
        //添加公司主账户信息
        addAccountAndCorpToResult(mapResult, logonUID, lang);
        //过滤页面传入的筛选项,如果mapResult中不存在，需要补充到mapResult
        addNonExistFilterValue(mapFilterValue, mapResult, lang);
        //转换名称
        convertIDToName(mapResult, result, input);
        saveFilterToRedis(input, customReportInfoID);
        updateReportFilter(input.getFilterList(), customReportInfoID);
        sortValue(mapResult);
        result.setFilterList(mapResult);
        saveFilterToRedis(result, logonUID, customReportInfoID);
    }

    */
/**
     * 排序
     *
     * @param mapResult
     *//*

    private void sortValue(Map<String, List<UserDefinedFilter>> mapResult) {
        for (String key :
                mapResult.keySet()) {
            List<UserDefinedFilter> sortList = mapResult.get(key).stream().sorted((data1, data2) -> {
                if (StringUtils.isEmpty(data1.getName()) || StringUtils.isEmpty(data1.getValue())) {
                    return 1;
                }
                if (StringUtils.isEmpty(data2.getName()) || StringUtils.isEmpty(data2.getValue())) {
                    return -1;
                }

                return data1.getValue().compareTo(data2.getValue());
            }).collect(Collectors.toList());
            mapResult.put(key, sortList);
        }
    }

    */
/**
     * 将过滤项转成map[FilterNameEnum String,value]
     *
     * @param input
     * @return
     *//*

    protected Map<String, HashSet<String>> getFilterMap(ReportUserDefinedInput input) {
        Map<String, HashSet<String>> mapFilterValue = new HashMap<>();
        for (Filter filter :
                input.getFilterList()) {
            List<String> list = filter.getValue();
            if (CollectionUtils.isNotEmpty(list)) {
                list.stream().forEach(value -> value = MapperUtils.trimString(value));
            }
            try {
                if (FilterNameEnum.isFilterEnum(filter.getName().toUpperCase())) {
                    FilterNameEnum filterNameEnum = FilterNameEnum.valueOf(filter.getName().toUpperCase());
                    HashSet<String> dataSet = CollectionUtils.isNotEmpty(list) ? new HashSet<>(list) : new HashSet<>();
                    mapFilterValue.put(filterNameEnum.getName(), dataSet);
                }
            } catch (Exception ex) {
                log.error(ex, logTagContext.getMap());
            }
        }
        return mapFilterValue;
    }

    */
/**
     * 查询配置的维度
     *
     * @param mapFilterValue
     * @return
     * @throws SQLException
     *//*

    protected HashSet<String> getConfigDimensionList(Map<String, HashSet<String>> mapFilterValue, long customReportInfoID) {
        try {
            CustomReportInfoPO config = new CustomReportInfoDAO().queryByPk(customReportInfoID);
            if (config == null) {
                return null;
            }
            List<String> dimensionList = (List<String>) JacksonUtil.deserialize(config.getDimension(), List.class);
            HashSet<String> dimensionSet = new HashSet<>();
            for (int i = 0; i < dimensionList.size(); i++) {
                if (FilterNameEnum.isFilterEnum(dimensionList.get(i))) {
                    dimensionSet.add(dimensionList.get(i));
                }
            }
            //默认三项
            dimensionSet.add(DimensionEnum.CORP.getName());
            dimensionSet.add(DimensionEnum.ACCOUNTID.getName());
            dimensionSet.add(DimensionEnum.DEPT1.getName());
            return dimensionSet;
        } catch (Exception ex) {
            log.error(ex, logTagContext.getMap());
            return null;
        }
    }

    */
/**
     * 创建需要返回的过滤项值
     *
     * @param dimensionSet
     * @return
     *//*

    protected Map<String, List<UserDefinedFilter>> getFilterNameEnumUserDefinedFilterMap(HashSet<String> dimensionSet) {
        Map<String, List<UserDefinedFilter>> mapResult = new HashMap<>();
        for (String column :
                dimensionSet) {
            if (!FilterNameEnum.isFilterEnum(column.toUpperCase())) {
                continue;
            }
            FilterNameEnum filterNameEnum = FilterNameEnum.valueOf(column.toUpperCase());
            if (!filterNeedReturn(filterNameEnum)) {
                continue;
            }
            mapResult.put(filterNameEnum.getName(), new ArrayList<>());
        }
        //默认需要返回的三项,corp：公司名称、accountid：主账号、dept1：部门1,
        mapResult.put(FilterNameEnum.CORP.getName(), new ArrayList<>());
        mapResult.put(FilterNameEnum.ACCOUNTID.getName(), new ArrayList<>());
        mapResult.put(FilterNameEnum.DEPT1.getName(), new ArrayList<>());

        return mapResult;
    }

    */
/**
     * 筛选项是否返回
     *
     * @param filterNameEnum
     * @return
     *//*

    private boolean filterNeedReturn(FilterNameEnum filterNameEnum) {
        boolean needReturn = true;
        switch (filterNameEnum) {
            case BOOKTYPE:
            case FLT_FLIGHTSTATUS:
            case ORDERTYPE:
            case FLT_REALCLASS:
            case HTL_TYPE:
            case HTL_ISMIXPAYMENT:
                needReturn = false;
                break;
        }
        return needReturn;
    }

    */
/**
     * 从数据源的值获取自定义筛选项值
     *
     * @param dataSourceList
     * @param dimensionSet
     * @param mapResult
     *//*

    protected abstract void addFilterValueFromSource(List<Map<String, String>> dataSourceList, HashSet<String> dimensionSet, Map<String, List<UserDefinedFilter>> mapResult);

    */
/**
     * 添加公共项的值
     *
     * @param dimensionSet
     * @param mapResult
     * @param mapExist
     * @param row
     *//*

    protected void addCommonFilterValueFromSource(HashSet<String> dimensionSet, Map<String, List<UserDefinedFilter>> mapResult, Map<String, HashSet<String>> mapExist, Map<String, String> row) {
        addFilterValueToMap(FilterNameEnum.DEPT1, DimensionEnum.DEPT1, DimensionEnum.DEPT1, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.DEPT2, DimensionEnum.DEPT2, DimensionEnum.DEPT2, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.DEPT3, DimensionEnum.DEPT3, DimensionEnum.DEPT3, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.DEPT4, DimensionEnum.DEPT4, DimensionEnum.DEPT4, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.DEPT5, DimensionEnum.DEPT5, DimensionEnum.DEPT5, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.DEPT6, DimensionEnum.DEPT6, DimensionEnum.DEPT6, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.COSTCENTER1, DimensionEnum.COSTCENTER1, DimensionEnum.COSTCENTER1, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.COSTCENTER2, DimensionEnum.COSTCENTER2, DimensionEnum.COSTCENTER2, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.COSTCENTER3, DimensionEnum.COSTCENTER3, DimensionEnum.COSTCENTER3, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.COSTCENTER4, DimensionEnum.COSTCENTER4, DimensionEnum.COSTCENTER4, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.COSTCENTER5, DimensionEnum.COSTCENTER5, DimensionEnum.COSTCENTER5, dimensionSet, row, mapResult, mapExist);
        addFilterValueToMap(FilterNameEnum.COSTCENTER6, DimensionEnum.COSTCENTER6, DimensionEnum.COSTCENTER6, dimensionSet, row, mapResult, mapExist);

    }

    */
/**
     * 添加数据到map，key、value 全部小写去空格
     *
     * @param nameKey
     * @param value
     * @param dimensionSet
     * @param row
     * @param mapResult
     *//*

    protected void addFilterValueToMap(FilterNameEnum mapKey, DimensionEnum nameKey, DimensionEnum value, HashSet<String> dimensionSet
            , Map<String, String> row, Map<String, List<UserDefinedFilter>> mapResult, Map<String, HashSet<String>> mapExist) {

        boolean isDefalut = isDefaultField(value.getName());
        if (!dimensionSet.contains(mapKey.getName()) && !isDefalut) {
            return;
        }
        if (!mapExist.containsKey(nameKey.getName())) {
            mapExist.put(nameKey.getName(), new HashSet<>());
        }

        String name = MapperUtils.trimString(row.get(nameKey.getName()));
        String val = MapperUtils.trimString(row.get(value.getName()));
        if (mapExist.get(nameKey.getName()).contains(name)) {
            return;
        }
        UserDefinedFilter userDefinedFilter = getUserDefinedFilter(name, val, false);
        userDefinedFilter.setIsSelected(YesOrNotEnum.F.toString());
        mapResult.get(mapKey.getName()).add(userDefinedFilter);
        mapExist.get(nameKey.getName()).add(name);
    }

    */
/**
     * 过滤页面传入的筛选项,如果mapResult中不存在，需要补充到mapResult
     *
     * @param mapFilterValue
     * @param mapResult
     *//*

    protected void addNonExistFilterValue(Map<String, HashSet<String>> mapFilterValue, Map<String, List<UserDefinedFilter>> mapResult, String lang) {

        String emptyString = SharkUtils.get("lbl_Empty", "无", lang);
        for (String key :
                mapResult.keySet()) {

            HashSet<String> nameSet = getNameSet(mapResult.get(key));
            //如果传入的reques中已包含，IsSelected设置为T
            mapResult.get(key).stream().forEach(data -> {
                if (!mapFilterValue.containsKey(key)) {
                    //mapFilterValue中没有key这一项，默认选中
                    data.setIsSelected(YesOrNotEnum.T.toString());
                } else {
                    if (mapFilterValue.get(key).contains(data.getName())) {
                        //mapFilterValue中没有key这一项,并且有data这个值，默认选中
                        data.setIsSelected(YesOrNotEnum.T.toString());
                    } else {
                        data.setIsSelected(YesOrNotEnum.F.toString());
                    }
                }
                //为空时应显示为“无”
                if (StringUtils.isEmpty(data.getValue())) {
                    data.setValue(emptyString);
                }
            });
            if (!mapFilterValue.containsKey(key)) {
                continue;
            }
            //mapResult中不存在
            List<String> list = mapFilterValue.get(key).stream().filter(value -> !nameSet.contains(value)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            addDefaultData(mapResult, key, nameSet, list);
        }
    }

    */
/**
     * 将filterList中实体的name属性转换成hashset
     *
     * @param filterList
     * @return
     *//*

    protected HashSet<String> getNameSet(List<UserDefinedFilter> filterList) {
        HashSet<String> filterSet = new HashSet<>();
        for (UserDefinedFilter filter :
                filterList) {
            filterSet.add(filter.getName());
        }
        return filterSet;
    }

    */
/**
     * 默认需要的字段
     *
     * @param name
     * @return
     *//*

    protected boolean isDefaultField(String name) {
        return DimensionEnum.CORP.getName().equalsIgnoreCase(name)
                || DimensionEnum.CORPID.getName().equalsIgnoreCase(name)
                || DimensionEnum.ACCOUNTID.getName().equalsIgnoreCase(name)
                || DimensionEnum.ACCOUNT.getName().equalsIgnoreCase(name)
                || DimensionEnum.DEPT1.getName().equalsIgnoreCase(name);
    }

    */
/**
     * 初始化UserDefinedFilter对象
     *
     * @param name
     * @param val
     * @param needTrim
     * @return
     *//*

    protected UserDefinedFilter getUserDefinedFilter(String name, String val, boolean needTrim) {
        UserDefinedFilter userDefinedFilter = new UserDefinedFilter();
        if (needTrim) {
            userDefinedFilter.setName(MapperUtils.trimString(name));
            userDefinedFilter.setValue(MapperUtils.trimString(val));
        } else {
            userDefinedFilter.setName(name);
            userDefinedFilter.setValue(val);
        }
        return userDefinedFilter;
    }

    */
/**
     * 从filterList获取对应filterNameEnum的值
     *
     * @param filterList
     * @param filterNameEnum
     * @return
     *//*

    protected String getFilterValue(List<Filter> filterList, FilterNameEnum filterNameEnum) {
        if (CollectionUtils.isEmpty(filterList)) {
            return setFilterDefaultValue(filterNameEnum);
        }
        if (!filterList.stream().anyMatch(f -> filterNameEnum.getName().equalsIgnoreCase(f.getName()))) {
            return setFilterDefaultValue(filterNameEnum);
        }
        Filter filter = filterList.stream().filter(f -> filterNameEnum.getName().equalsIgnoreCase(f.getName())).collect(Collectors.toList()).get(0);
        if (CollectionUtils.isEmpty(filter.getValue())) {
            return setFilterDefaultValue(filterNameEnum);
        }
        return filter.getValue().get(0);
    }

    */
/**
     * 从filterList获取对应filterNameEnum的值
     *
     * @param filterList
     * @param filterNameEnum
     * @return
     *//*

    protected List<String> getFilterValueList(List<Filter> filterList, FilterNameEnum filterNameEnum) {
        if (CollectionUtils.isEmpty(filterList)) {
            return new ArrayList<>();
        }
        if (!filterList.stream().anyMatch(f -> filterNameEnum.getName().equalsIgnoreCase(f.getName()))) {
            return new ArrayList<>();
        }
        Filter filter = filterList.stream().filter(f -> filterNameEnum.getName().equalsIgnoreCase(f.getName())).collect(Collectors.toList()).get(0);
        if (CollectionUtils.isEmpty(filter.getValue())) {
            return new ArrayList<>();
        }
        return filter.getValue();
    }

    */
/**
     * 默认FilterValue
     *
     * @param filterNameEnum
     * @return
     *//*

    protected String setFilterDefaultValue(FilterNameEnum filterNameEnum) {
        String defaultValue = GlobalConst.STRING_EMPTY;
        switch (filterNameEnum) {
            case LANG:
                defaultValue = SharkLocaleEnum.ZH_CN.getLoacal();
                break;
        }
        return defaultValue;
    }

    */
/**
     * 将input保存到redis
     *
     * @param input
     * @param customReportInfoID
     *//*

    protected void saveFilterToRedis(ReportUserDefinedInput input, long customReportInfoID) {
        String logonUID = getFilterValue(input.getFilterList(), FilterNameEnum.LOGONUID);
        String key = String.format(RedisKeyConst.COIREPORT_USERDEFINED_FILTER, logonUID.trim().toLowerCase(), customReportInfoID);
        if (CollectionUtils.isNotEmpty(input.getFilterList())) {
            //input保存redis时，去掉无效项
            input.getFilterList().removeIf(filter -> FilterNameEnum.LOGONUID.getName().equalsIgnoreCase(filter.getName()));
            input.getFilterList().removeIf(filter -> FilterNameEnum.NEEDDATASOURCE.getName().equalsIgnoreCase(filter.getName()));
        }
        RedisCacheUtils.set(key, input, true);
    }

    */
/**
     * 将output中的FilterList保存到redis
     *
     * @param output
     * @param customReportInfoID
     *//*

    protected void saveFilterToRedis(ReportUserDefinedOutput output, String logonUID, long customReportInfoID) {
        if (output == null || output.getFilterList() == null || output.getFilterList().isEmpty()) {
            return;
        }
        String key = String.format(RedisKeyConst.COIREPORT_USERDEFINED_OUTPUT_FILTER, logonUID.trim().toLowerCase(), customReportInfoID);
        RedisCacheUtils.set(key, output.getFilterList(), true);
    }


    */
/**
     * 添加公司主账户信息
     *
     * @param mapResult
     * @param logonUID
     * @param lang
     *//*

    protected void addAccountAndCorpToResult(Map<String, List<UserDefinedFilter>> mapResult, String logonUID, String lang) {
        List<CorpAndAccountInfoBO> list = reportStaticDataQueryService.queryCorpAndAccount(logonUID, lang, logTagContext.getMap());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<UserDefinedFilter> corpList = new ArrayList<>();
        List<UserDefinedFilter> accountList = new ArrayList<>();

        for (CorpAndAccountInfoBO item :
                list) {
            UserDefinedFilter corp = getUserDefinedFilter(item.getCorpId(), item.getCorpName(), true);
            corpList.add(corp);

            if (item.getAccountInfos() == null || item.getAccountInfos().isEmpty()) {
                continue;
            }

            for (AccountInfoBO accountInfoBO :
                    item.getAccountInfos().values()) {
                UserDefinedFilter account = getUserDefinedFilter(accountInfoBO.getId(), accountInfoBO.getName(), true);
                accountList.add(account);
            }
        }
        mapResult.put(FilterNameEnum.CORP.getName(), corpList);
        mapResult.put(FilterNameEnum.ACCOUNTID.getName(), accountList);
    }

    //region 转换名称

    */
/**
     * 转换名称
     *
     * @param mapResult
     * @param input
     *//*

    protected abstract void convertIDToName(Map<String, List<UserDefinedFilter>> mapResult, ReportUserDefinedOutput result, ReportUserDefinedInput input);

    */
/**
     * 不做其他转换的数据
     *
     * @param mapResult
     * @param key
     * @param nameSet
     * @param list
     *//*

    protected void addDefaultData(Map<String, List<UserDefinedFilter>> mapResult, String key, HashSet<String> nameSet, List<String> list) {
        for (int i = 0; i < list.size(); i++) {
            if (nameSet.contains(list.get(i))) {
                continue;
            }
            UserDefinedFilter userDefinedFilter = getUserDefinedFilter(list.get(i), list.get(i), true);
            userDefinedFilter.setIsSelected(YesOrNotEnum.T.toString());
            mapResult.get(key).add(userDefinedFilter);
            nameSet.add(MapperUtils.trimString(list.get(i)));
        }
    }

    */
/**
     * 城市数据
     *
     * @param mapResult
     * @param isEN
     * @param key
     * @param list
     *//*

    protected void addCityData(Map<String, List<UserDefinedFilter>> mapResult, boolean isEN
            , String key, List<String> list) {
        Map<String, DimCity> dimCity = BaseDataUtils.getDimCityMap(list);

        for (UserDefinedFilter filter :
                mapResult.get(key)) {
            if (dimCity.containsKey(filter.getName())) {
                DimCity d = dimCity.get(filter.getName());
                filter.setValue(MapperUtils.trimString(isEN ? d.getCityEngName() : d.getCityName()));
            }
        }

    }

    */
/**
     * 添加省份数据
     *
     * @param mapResult
     * @param isEN
     * @param key
     * @param list
     *//*

    protected void addProvinceData(Map<String, List<UserDefinedFilter>> mapResult, boolean isEN, String key, List<String> list) {
        Map<String, DimProvince> provinceMap = BaseDataUtils.getProvinceMap(list, logTagContext.getMap());
        if (provinceMap == null || provinceMap.isEmpty()) {
            return;
        }
        for (UserDefinedFilter filter :
                mapResult.get(key)) {
            if (provinceMap.containsKey(filter.getName())) {
                DimProvince data = provinceMap.get(filter.getName());
                filter.setValue(MapperUtils.trimString(isEN ? data.getNameEN() : data.getName()));
            }
        }
    }

    */
/**
     * 添加国家数据
     *
     * @param mapResult
     * @param isEN
     * @param key
     * @param list
     *//*

    protected void addCountryData(Map<String, List<UserDefinedFilter>> mapResult, boolean isEN, String key, List<String> list) {
        Map<String, DimCountry> countryMap = BaseDataUtils.getCountryMap(list, logTagContext.getMap());
        if (countryMap == null || countryMap.isEmpty()) {
            return;
        }
        for (UserDefinedFilter filter :
                mapResult.get(key)) {
            if (countryMap.containsKey(filter.getName())) {
                DimCountry data = countryMap.get(filter.getName());
                filter.setValue(MapperUtils.trimString(isEN ? data.getNameEN() : data.getName()));
            }
        }
    }

    */
/**
     * 公司 or 主账户名称
     *//*

    protected void addCorpOrAccountData(Map<String, List<UserDefinedFilter>> mapResult
            , String key, List<String> IDList, String lang, String logUID, DimensionEnum dimensionEnum) {
        Map<String, String> mapName = BaseDataUtils.getCorpOrAccountName(dimensionEnum, lang, IDList, logUID, logTagContext.getMap());
        for (UserDefinedFilter filter :
                mapResult.get(key)) {
            if (mapName.containsKey(filter.getName())) {
                filter.setValue(MapperUtils.trimString(mapName.get(filter.getName())));
            }
        }
    }


    //endregion

    protected void updateReportFilter(List<Filter> filterList, long customReportInfoID) {
        try {
            CustomReportInfo customReportInfo = service.queryByKey(customReportInfoID);
            customReportInfo.setFilter(JsonUtils.objectToString(filterList));
            service.update(customReportInfo);
        } catch (Exception ex) {
            log.info("updateReportFilter", ex);
        }
    }
}
*/
