package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;

/**
 * 同比-去年/前年
 */
public enum QueryYoyTypeEnum {

    /**
     * 同比-去年
     */
    yoy(0),

    /**
     * 同比-前年
     */
    yoy_pre(1);

    private final int value;

    QueryYoyTypeEnum(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public int getValue() {
        return value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public static QueryYoyTypeEnum findByValue(int value) {
        switch (value) {
            case 0:
                return yoy;
            case 1:
                return yoy_pre;
            default:
                return null;
        }
    }
}
