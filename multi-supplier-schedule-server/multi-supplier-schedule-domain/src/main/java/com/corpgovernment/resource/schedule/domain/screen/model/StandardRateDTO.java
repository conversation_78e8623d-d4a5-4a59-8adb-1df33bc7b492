package com.corpgovernment.resource.schedule.domain.screen.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Slf4j
public class StandardRateDTO {

    /**
     * 租户id
     */
    private String tenantId;


    /**
     * 国内、国际
     */
    private String areaType;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    public boolean overseas() {
        return !domestic();
    }

    public boolean domestic() {
        if (areaType == null) {
            log.error("areaType is null");
            return false;
        }
        return "国内".equals(areaType);
    }
}
