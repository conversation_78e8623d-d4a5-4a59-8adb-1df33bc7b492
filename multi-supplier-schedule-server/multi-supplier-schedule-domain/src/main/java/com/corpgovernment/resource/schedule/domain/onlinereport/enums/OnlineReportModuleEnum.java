package com.corpgovernment.resource.schedule.domain.onlinereport.enums;

/**
 * <AUTHOR>
 * @date 2024-01-02 14:05
 * @desc 在线报告模块枚举
 */
public enum OnlineReportModuleEnum {

    // 中国站公共模块
    COMMON_CN("onlineReportChinaStationCommonModule"),
    // 中国站风险订单
    RISKORDER_CN("onlineReportRiskOrderModule"),
    // 中国站差旅定位
    POSITION_CN("onlineReportChinaStationTravelFootprint"),
    // 蓝色空间公共模块
    COMMON_BS("onlineReportBlueSpaceCommonModule"),
    // 蓝色空间差旅定位
    POSITION_BS("onlineReportBlueSpaceTravelFootprint"),
    // 大屏数据
    DASHBOARD_CN("largeScreenCommonModule"),
    // 个人足迹
    FOOTPRINT_CN("personalFootprint");

    private String moduleCode;

    OnlineReportModuleEnum(String code) {
        this.moduleCode = code;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public static OnlineReportModuleEnum getModuleEnum(String moduleCode) {
        for (OnlineReportModuleEnum moduleEnum : OnlineReportModuleEnum.values()) {
            if (moduleEnum.getModuleCode().equals(moduleCode)) {
                return moduleEnum;
            }
        }
        return null;
    }
}
