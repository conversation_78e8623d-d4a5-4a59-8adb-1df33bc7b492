package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

import java.io.Serializable;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2019/6/10 13:27
 */
public class FlightCarrierAndAirlineBasicBO implements Serializable{

    private static final long serialVersionUID = 1L;

    /*成交净价*/
    private double price ;
    /*张数*/
    private int quantity ;
    /*里程数*/
    private double tpmS ;
    /*洲id*/
    private int continent ;
    /*洲名称*/
    private String continentName ;
    /*航司id*/
    private String airline ;
    /*航司名称*/
    private String airLineName ;
    /*航线名称*/
    private String flightCity ;

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public double getTpmS() {
        return tpmS;
    }

    public void setTpmS(double tpmS) {
        this.tpmS = tpmS;
    }

    public int getContinent() {
        return continent;
    }

    public void setContinent(int continent) {
        this.continent = continent;
    }

    public String getContinentName() {
        return continentName;
    }

    public void setContinentName(String continentName) {
        this.continentName = continentName;
    }

    public String getAirline() {
        return airline;
    }

    public void setAirline(String airline) {
        this.airline = airline;
    }

    public String getAirLineName() {
        return airLineName;
    }

    public void setAirLineName(String airLineName) {
        this.airLineName = airLineName;
    }

    public String getFlightCity() {
        return flightCity;
    }

    public void setFlightCity(String flightCity) {
        this.flightCity = flightCity;
    }
}
