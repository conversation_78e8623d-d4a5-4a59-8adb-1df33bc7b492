package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity;

/**
 * @Auther: ddzhan
 * @Date: 2019/4/1 15:23
 * @Description:
 */
public class HotelDeptAnysisBo {
    private Integer htlQuantity;
    private Double htlAmount;
    private Double htlAvgprice;
    private Double htlDeadprice;
    private Double htlSave;
    private Double htlLoss;

    //会员
    private Integer htlMemberQuantity;
    private Double htlMemberAmount;
    private Double htlMemberAvgprice;

    //协议
    private Integer htlAgreementQuantity;
    private Double htlAgreementAmount;
    private Double htlAgreementAvgprice;

    //大陆
    private Integer htlDomQuantity;
    private Double htlDomAmount;
    private Double htlDomAvgprice;

    //港澳台
    private Integer htlHkmtQuantity;
    private Double htlHkmtAmount;
    private Double htlHkmtAvgprice;

    //海外
    private Integer htlIntQuantity;
    private Double htlIntAmount;
    private Double htlIntAvgprice;

    private Integer ordnum;

    private Integer htlRcTimesMember;
    private Double htlLossAmountMember;
    private Integer htlOrderNumberMember;
    private Integer htlDeptNumber;
    private Integer htlRctimes;
    private Double htlRctimesRate;
    private Double htlDeadpriceMember;


    private String dept1;
    private String dept2;
    private String dept3;
    private String dept4;
    private String dept5;
    private String dept6;
    private String dept7;
    private String dept8;
    private String dept9;
    private String dept10;

    private String costcenter1;
    private String costcenter2;
    private String costcenter3;
    private String costcenter4;
    private String costcenter5;
    private String costcenter6;


    private String corpCorporation;
    private String corpid;
    private String corp;
    private String accountid;
    private String account;
    private String uid;




    public String getCorpid() {
        return corpid;
    }

    public void setCorpid(String corpid) {
        this.corpid = corpid;
    }

    public Double getHtlDeadpriceMember() {
        return htlDeadpriceMember;
    }

    public void setHtlDeadpriceMember(Double htlDeadpriceMember) {
        this.htlDeadpriceMember = htlDeadpriceMember;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getCorpCorporation() {
        return corpCorporation;
    }

    public void setCorpCorporation(String corpCorporation) {
        this.corpCorporation = corpCorporation;
    }

    public String getAccountid() {
        return accountid;
    }

    public void setAccountid(String accountid) {
        this.accountid = accountid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getDept4() {
        return dept4;
    }

    public void setDept4(String dept4) {
        this.dept4 = dept4;
    }

    public String getDept10() {
        return dept10;
    }

    public void setDept10(String dept10) {
        this.dept10 = dept10;
    }

    public String getDept7() {
        return dept7;
    }

    public void setDept7(String dept7) {
        this.dept7 = dept7;
    }

    public String getDept8() {
        return dept8;
    }

    public void setDept8(String dept8) {
        this.dept8 = dept8;
    }

    public String getDept9() {
        return dept9;
    }

    public void setDept9(String dept9) {
        this.dept9 = dept9;
    }

    public Double getHtlRctimesRate() {
        return htlRctimesRate;
    }

    public void setHtlRctimesRate(Double htlRctimesRate) {
        this.htlRctimesRate = htlRctimesRate;
    }

    public Integer getHtlDeptNumber() {
        return htlDeptNumber;
    }

    public void setHtlDeptNumber(Integer htlDeptNumber) {
        this.htlDeptNumber = htlDeptNumber;
    }

    public Integer getHtlRctimes() {
        return htlRctimes;
    }

    public void setHtlRctimes(Integer htlRctimes) {
        this.htlRctimes = htlRctimes;
    }

    public Integer getOrdnum() {
        return ordnum;
    }

    public void setOrdnum(Integer ordnum) {
        this.ordnum = ordnum;
    }

    public String getDept1() {
        return dept1;
    }

    public void setDept1(String dept1) {
        this.dept1 = dept1;
    }

    public String getDept2() {
        return dept2;
    }

    public void setDept2(String dept2) {
        this.dept2 = dept2;
    }

    public String getDept3() {
        return dept3;
    }

    public void setDept3(String dept3) {
        this.dept3 = dept3;
    }

    public String getDept5() {
        return dept5;
    }

    public void setDept5(String dept5) {
        this.dept5 = dept5;
    }

    public String getDept6() {
        return dept6;
    }

    public void setDept6(String dept6) {
        this.dept6 = dept6;
    }

    public String getCostcenter1() {
        return costcenter1;
    }

    public void setCostcenter1(String costcenter1) {
        this.costcenter1 = costcenter1;
    }

    public String getCostcenter2() {
        return costcenter2;
    }

    public void setCostcenter2(String costcenter2) {
        this.costcenter2 = costcenter2;
    }

    public String getCostcenter3() {
        return costcenter3;
    }

    public void setCostcenter3(String costcenter3) {
        this.costcenter3 = costcenter3;
    }

    public String getCostcenter4() {
        return costcenter4;
    }

    public void setCostcenter4(String costcenter4) {
        this.costcenter4 = costcenter4;
    }

    public String getCostcenter5() {
        return costcenter5;
    }

    public void setCostcenter5(String costcenter5) {
        this.costcenter5 = costcenter5;
    }

    public String getCostcenter6() {
        return costcenter6;
    }

    public void setCostcenter6(String costcenter6) {
        this.costcenter6 = costcenter6;
    }

    public Integer getHtlQuantity() {
        return htlQuantity;
    }

    public void setHtlQuantity(Integer htlQuantity) {
        this.htlQuantity = htlQuantity;
    }

    public Double getHtlAmount() {
        return htlAmount;
    }

    public void setHtlAmount(Double htlAmount) {
        this.htlAmount = htlAmount;
    }

    public Double getHtlAvgprice() {
        return htlAvgprice;
    }

    public void setHtlAvgprice(Double htlAvgprice) {
        this.htlAvgprice = htlAvgprice;
    }

    public Double getHtlDeadprice() {
        return htlDeadprice;
    }

    public void setHtlDeadprice(Double htlDeadprice) {
        this.htlDeadprice = htlDeadprice;
    }

    public Double getHtlSave() {
        return htlSave;
    }

    public void setHtlSave(Double htlSave) {
        this.htlSave = htlSave;
    }

    public Double getHtlLoss() {
        return htlLoss;
    }

    public void setHtlLoss(Double htlLoss) {
        this.htlLoss = htlLoss;
    }

    public Integer getHtlMemberQuantity() {
        return htlMemberQuantity;
    }

    public void setHtlMemberQuantity(Integer htlMemberQuantity) {
        this.htlMemberQuantity = htlMemberQuantity;
    }

    public Double getHtlMemberAmount() {
        return htlMemberAmount;
    }

    public void setHtlMemberAmount(Double htlMemberAmount) {
        this.htlMemberAmount = htlMemberAmount;
    }

    public Double getHtlMemberAvgprice() {
        return htlMemberAvgprice;
    }

    public void setHtlMemberAvgprice(Double htlMemberAvgprice) {
        this.htlMemberAvgprice = htlMemberAvgprice;
    }

    public Integer getHtlAgreementQuantity() {
        return htlAgreementQuantity;
    }

    public void setHtlAgreementQuantity(Integer htlAgreementQuantity) {
        this.htlAgreementQuantity = htlAgreementQuantity;
    }

    public Double getHtlAgreementAmount() {
        return htlAgreementAmount;
    }

    public void setHtlAgreementAmount(Double htlAgreementAmount) {
        this.htlAgreementAmount = htlAgreementAmount;
    }

    public Double getHtlAgreementAvgprice() {
        return htlAgreementAvgprice;
    }

    public void setHtlAgreementAvgprice(Double htlAgreementAvgprice) {
        this.htlAgreementAvgprice = htlAgreementAvgprice;
    }

    public Integer getHtlDomQuantity() {
        return htlDomQuantity;
    }

    public void setHtlDomQuantity(Integer htlDomQuantity) {
        this.htlDomQuantity = htlDomQuantity;
    }

    public Double getHtlDomAmount() {
        return htlDomAmount;
    }

    public void setHtlDomAmount(Double htlDomAmount) {
        this.htlDomAmount = htlDomAmount;
    }

    public Double getHtlDomAvgprice() {
        return htlDomAvgprice;
    }

    public void setHtlDomAvgprice(Double htlDomAvgprice) {
        this.htlDomAvgprice = htlDomAvgprice;
    }

    public Integer getHtlHkmtQuantity() {
        return htlHkmtQuantity;
    }

    public void setHtlHkmtQuantity(Integer htlHkmtQuantity) {
        this.htlHkmtQuantity = htlHkmtQuantity;
    }

    public Double getHtlHkmtAmount() {
        return htlHkmtAmount;
    }

    public void setHtlHkmtAmount(Double htlHkmtAmount) {
        this.htlHkmtAmount = htlHkmtAmount;
    }

    public Double getHtlHkmtAvgprice() {
        return htlHkmtAvgprice;
    }

    public void setHtlHkmtAvgprice(Double htlHkmtAvgprice) {
        this.htlHkmtAvgprice = htlHkmtAvgprice;
    }

    public Integer getHtlIntQuantity() {
        return htlIntQuantity;
    }

    public void setHtlIntQuantity(Integer htlIntQuantity) {
        this.htlIntQuantity = htlIntQuantity;
    }

    public Double getHtlIntAmount() {
        return htlIntAmount;
    }

    public void setHtlIntAmount(Double htlIntAmount) {
        this.htlIntAmount = htlIntAmount;
    }

    public Double getHtlIntAvgprice() {
        return htlIntAvgprice;
    }

    public void setHtlIntAvgprice(Double htlIntAvgprice) {
        this.htlIntAvgprice = htlIntAvgprice;
    }

    public Integer getHtlRcTimesMember() {
        return htlRcTimesMember;
    }

    public void setHtlRcTimesMember(Integer htlRcTimesMember) {
        this.htlRcTimesMember = htlRcTimesMember;
    }

    public Double getHtlLossAmountMember() {
        return htlLossAmountMember;
    }

    public void setHtlLossAmountMember(Double htlLossAmountMember) {
        this.htlLossAmountMember = htlLossAmountMember;
    }


    public Integer getHtlOrderNumberMember() {
        return htlOrderNumberMember;
    }

    public void setHtlOrderNumberMember(Integer htlOrderNumberMember) {
        this.htlOrderNumberMember = htlOrderNumberMember;
    }


    public String getCorp() {
        return corp;
    }

    public void setCorp(String corp) {
        this.corp = corp;
    }
}
