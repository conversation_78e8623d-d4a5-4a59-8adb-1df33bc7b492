package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * 位置事件
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "passengeruid",
    "pinYinUseName",
    "processedUserName"
})
public class TravelPassengerInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public TravelPassengerInfo(
        String passengeruid,
        String pinYinUseName,
        String processedUserName) {
        this.passengeruid = passengeruid;
        this.pinYinUseName = pinYinUseName;
        this.processedUserName = processedUserName;
    }

    public TravelPassengerInfo() {
    }

    /**
     * 出行人uid
     */
    @JsonProperty("passengeruid")
    public String passengeruid;

    /**
     * 用户姓名（拼音）
     */
    @JsonProperty("pinYinUseName")
    public String pinYinUseName;

    /**
     * 用户姓名
     */
    @JsonProperty("processedUserName")
    public String processedUserName;

    /**
     * 出行人uid
     */
    public String getPassengeruid() {
        return passengeruid;
    }

    /**
     * 出行人uid
     */
    public void setPassengeruid(final String passengeruid) {
        this.passengeruid = passengeruid;
    }

    /**
     * 用户姓名（拼音）
     */
    public String getPinYinUseName() {
        return pinYinUseName;
    }

    /**
     * 用户姓名（拼音）
     */
    public void setPinYinUseName(final String pinYinUseName) {
        this.pinYinUseName = pinYinUseName;
    }

    /**
     * 用户姓名
     */
    public String getProcessedUserName() {
        return processedUserName;
    }

    /**
     * 用户姓名
     */
    public void setProcessedUserName(final String processedUserName) {
        this.processedUserName = processedUserName;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TravelPassengerInfo other = (TravelPassengerInfo)obj;
        return
            Objects.equal(this.passengeruid, other.passengeruid) &&
            Objects.equal(this.pinYinUseName, other.pinYinUseName) &&
            Objects.equal(this.processedUserName, other.processedUserName);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.passengeruid == null ? 0 : this.passengeruid.hashCode());
        result = 31 * result + (this.pinYinUseName == null ? 0 : this.pinYinUseName.hashCode());
        result = 31 * result + (this.processedUserName == null ? 0 : this.processedUserName.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("passengeruid", passengeruid)
            .add("pinYinUseName", pinYinUseName)
            .add("processedUserName", processedUserName)
            .toString();
    }
}
