package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

/**
 * @Description: 机票舱位分析BO
 * <AUTHOR>
 * @Date 2019/5/28
 */
public class FlightClassStatInfoBO {
    private Double firstClassPrice;
    private Double businessClassPrice;
    private Double fullFareEconomyClassPrice;
    private Double discountedEconomyClassPrice;
    private Double upFrontDiscountEconomyClassPrice;
    private Double firstClassQuantity;
    private Double businessClassQuantity;
    private Double fullFareEconomyClassQuantity;
    private Double discountedEconomyClassQuantity;
    private Double upFrontDiscountEconomyClassQuantity;

    public Double getFirstClassPrice() {
        return firstClassPrice;
    }

    public void setFirstClassPrice(Double firstClassPrice) {
        this.firstClassPrice = firstClassPrice;
    }

    public Double getBusinessClassPrice() {
        return businessClassPrice;
    }

    public void setBusinessClassPrice(Double businessClassPrice) {
        this.businessClassPrice = businessClassPrice;
    }

    public Double getFullFareEconomyClassPrice() {
        return fullFareEconomyClassPrice;
    }

    public void setFullFareEconomyClassPrice(Double fullFareEconomyClassPrice) {
        this.fullFareEconomyClassPrice = fullFareEconomyClassPrice;
    }

    public Double getDiscountedEconomyClassPrice() {
        return discountedEconomyClassPrice;
    }

    public void setDiscountedEconomyClassPrice(Double discountedEconomyClassPrice) {
        this.discountedEconomyClassPrice = discountedEconomyClassPrice;
    }

    public Double getUpFrontDiscountEconomyClassPrice() {
        return upFrontDiscountEconomyClassPrice;
    }

    public void setUpFrontDiscountEconomyClassPrice(Double upFrontDiscountEconomyClassPrice) {
        this.upFrontDiscountEconomyClassPrice = upFrontDiscountEconomyClassPrice;
    }

    public Double getFirstClassQuantity() {
        return firstClassQuantity;
    }

    public void setFirstClassQuantity(Double firstClassQuantity) {
        this.firstClassQuantity = firstClassQuantity;
    }

    public Double getBusinessClassQuantity() {
        return businessClassQuantity;
    }

    public void setBusinessClassQuantity(Double businessClassQuantity) {
        this.businessClassQuantity = businessClassQuantity;
    }

    public Double getFullFareEconomyClassQuantity() {
        return fullFareEconomyClassQuantity;
    }

    public void setFullFareEconomyClassQuantity(Double fullFareEconomyClassQuantity) {
        this.fullFareEconomyClassQuantity = fullFareEconomyClassQuantity;
    }

    public Double getDiscountedEconomyClassQuantity() {
        return discountedEconomyClassQuantity;
    }

    public void setDiscountedEconomyClassQuantity(Double discountedEconomyClassQuantity) {
        this.discountedEconomyClassQuantity = discountedEconomyClassQuantity;
    }

    public Double getUpFrontDiscountEconomyClassQuantity() {
        return upFrontDiscountEconomyClassQuantity;
    }

    public void setUpFrontDiscountEconomyClassQuantity(Double upFrontDiscountEconomyClassQuantity) {
        this.upFrontDiscountEconomyClassQuantity = upFrontDiscountEconomyClassQuantity;
    }
}
