package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.hotel;

import java.util.List;

/**
 * @Description: 酒店星级详情分析
 * <AUTHOR>
 * @Date 2019/4/3
 */
public class HotelStarDetailAnalysisBO {
    private List<HotelStarDetailBO> allAnalysisInfo;
    private List<HotelStarDetailBO> level2AnalysisInfo;
    private List<HotelStarDetailBO> level3AnalysisInfo;
    private List<HotelStarDetailBO> level4AnalysisInfo;
    private List<HotelStarDetailBO> level5AnalysisInfo;

    public List<HotelStarDetailBO> getAllAnalysisInfo() {
        return allAnalysisInfo;
    }

    public void setAllAnalysisInfo(List<HotelStarDetailBO> allAnalysisInfo) {
        this.allAnalysisInfo = allAnalysisInfo;
    }

    public List<HotelStarDetailBO> getLevel2AnalysisInfo() {
        return level2AnalysisInfo;
    }

    public void setLevel2AnalysisInfo(List<HotelStarDetailBO> level2AnalysisInfo) {
        this.level2AnalysisInfo = level2AnalysisInfo;
    }

    public List<HotelStarDetailBO> getLevel3AnalysisInfo() {
        return level3AnalysisInfo;
    }

    public void setLevel3AnalysisInfo(List<HotelStarDetailBO> level3AnalysisInfo) {
        this.level3AnalysisInfo = level3AnalysisInfo;
    }

    public List<HotelStarDetailBO> getLevel4AnalysisInfo() {
        return level4AnalysisInfo;
    }

    public void setLevel4AnalysisInfo(List<HotelStarDetailBO> level4AnalysisInfo) {
        this.level4AnalysisInfo = level4AnalysisInfo;
    }

    public List<HotelStarDetailBO> getLevel5AnalysisInfo() {
        return level5AnalysisInfo;
    }

    public void setLevel5AnalysisInfo(List<HotelStarDetailBO> level5AnalysisInfo) {
        this.level5AnalysisInfo = level5AnalysisInfo;
    }
}
