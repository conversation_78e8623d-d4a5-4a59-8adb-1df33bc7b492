package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;

/**
 * 节省损失-概览
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "responseCode",
        "responseDesc",
        "buSaveInfo",
        "saveInfo",
        "saveDistributionInfo",
        "buSaveDisInfo",
        "potentialSaveInfo",
        "responseStatus"
})
public class OnlineReportSaveGeneralResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    public OnlineReportSaveGeneralResponse(
            Integer responseCode,
            String responseDesc,
            BuSaveInfo buSaveInfo,
            GeneralSaveInfo saveInfo,
            GeneralSaveDistributionInfo saveDistributionInfo,
            BuSaveDistributionInfo buSaveDisInfo,
            GeneralPotentialSaveInfo potentialSaveInfo) {
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.buSaveInfo = buSaveInfo;
        this.saveInfo = saveInfo;
        this.saveDistributionInfo = saveDistributionInfo;
        this.buSaveDisInfo = buSaveDisInfo;
        this.potentialSaveInfo = potentialSaveInfo;

    }

    public OnlineReportSaveGeneralResponse() {
    }

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    /**
     * 产线节省
     */
    @JsonProperty("buSaveInfo")
    public BuSaveInfo buSaveInfo;

    /**
     * 节省
     */
    @JsonProperty("saveInfo")
    public GeneralSaveInfo saveInfo;

    /**
     * 节省分布
     */
    @JsonProperty("saveDistributionInfo")
    public GeneralSaveDistributionInfo saveDistributionInfo;

    /**
     * 产线节省分布
     */
    @JsonProperty("buSaveDisInfo")
    public BuSaveDistributionInfo buSaveDisInfo;

    /**
     * 潜在节省
     */
    @JsonProperty("potentialSaveInfo")
    public GeneralPotentialSaveInfo potentialSaveInfo;


    /**
     * 必写固定字段
     */
    public Integer getResponseCode() {
        return responseCode;
    }

    /**
     * 必写固定字段
     */
    public void setResponseCode(final Integer responseCode) {
        this.responseCode = responseCode;
    }

    /**
     * 必写固定字段
     */
    public String getResponseDesc() {
        return responseDesc;
    }

    /**
     * 必写固定字段
     */
    public void setResponseDesc(final String responseDesc) {
        this.responseDesc = responseDesc;
    }

    /**
     * 产线节省
     */
    public BuSaveInfo getBuSaveInfo() {
        return buSaveInfo;
    }

    /**
     * 产线节省
     */
    public void setBuSaveInfo(final BuSaveInfo buSaveInfo) {
        this.buSaveInfo = buSaveInfo;
    }

    /**
     * 节省
     */
    public GeneralSaveInfo getSaveInfo() {
        return saveInfo;
    }

    /**
     * 节省
     */
    public void setSaveInfo(final GeneralSaveInfo saveInfo) {
        this.saveInfo = saveInfo;
    }

    /**
     * 节省分布
     */
    public GeneralSaveDistributionInfo getSaveDistributionInfo() {
        return saveDistributionInfo;
    }

    /**
     * 节省分布
     */
    public void setSaveDistributionInfo(final GeneralSaveDistributionInfo saveDistributionInfo) {
        this.saveDistributionInfo = saveDistributionInfo;
    }

    /**
     * 产线节省分布
     */
    public BuSaveDistributionInfo getBuSaveDisInfo() {
        return buSaveDisInfo;
    }

    /**
     * 产线节省分布
     */
    public void setBuSaveDisInfo(final BuSaveDistributionInfo buSaveDisInfo) {
        this.buSaveDisInfo = buSaveDisInfo;
    }

    /**
     * 潜在节省
     */
    public GeneralPotentialSaveInfo getPotentialSaveInfo() {
        return potentialSaveInfo;
    }

    /**
     * 潜在节省
     */
    public void setPotentialSaveInfo(final GeneralPotentialSaveInfo potentialSaveInfo) {
        this.potentialSaveInfo = potentialSaveInfo;
    }


    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.responseCode = (Integer) fieldValue;
                break;
            case 1:
                this.responseDesc = (String) fieldValue;
                break;
            case 2:
                this.buSaveInfo = (BuSaveInfo) fieldValue;
                break;
            case 3:
                this.saveInfo = (GeneralSaveInfo) fieldValue;
                break;
            case 4:
                this.saveDistributionInfo = (GeneralSaveDistributionInfo) fieldValue;
                break;
            case 5:
                this.buSaveDisInfo = (BuSaveDistributionInfo) fieldValue;
                break;
            case 6:
                this.potentialSaveInfo = (GeneralPotentialSaveInfo) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportSaveGeneralResponse other = (OnlineReportSaveGeneralResponse) obj;
        return
                Objects.equal(this.responseCode, other.responseCode) &&
                        Objects.equal(this.responseDesc, other.responseDesc) &&
                        Objects.equal(this.buSaveInfo, other.buSaveInfo) &&
                        Objects.equal(this.saveInfo, other.saveInfo) &&
                        Objects.equal(this.saveDistributionInfo, other.saveDistributionInfo) &&
                        Objects.equal(this.buSaveDisInfo, other.buSaveDisInfo) &&
                        Objects.equal(this.potentialSaveInfo, other.potentialSaveInfo);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.responseCode == null ? 0 : this.responseCode.hashCode());
        result = 31 * result + (this.responseDesc == null ? 0 : this.responseDesc.hashCode());
        result = 31 * result + (this.buSaveInfo == null ? 0 : this.buSaveInfo.hashCode());
        result = 31 * result + (this.saveInfo == null ? 0 : this.saveInfo.hashCode());
        result = 31 * result + (this.saveDistributionInfo == null ? 0 : this.saveDistributionInfo.hashCode());
        result = 31 * result + (this.buSaveDisInfo == null ? 0 : this.buSaveDisInfo.hashCode());
        result = 31 * result + (this.potentialSaveInfo == null ? 0 : this.potentialSaveInfo.hashCode());


        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("responseCode", responseCode)
                .add("responseDesc", responseDesc)
                .add("buSaveInfo", buSaveInfo)
                .add("saveInfo", saveInfo)
                .add("saveDistributionInfo", saveDistributionInfo)
                .add("buSaveDisInfo", buSaveDisInfo)
                .add("potentialSaveInfo", potentialSaveInfo)

                .toString();
    }
}
