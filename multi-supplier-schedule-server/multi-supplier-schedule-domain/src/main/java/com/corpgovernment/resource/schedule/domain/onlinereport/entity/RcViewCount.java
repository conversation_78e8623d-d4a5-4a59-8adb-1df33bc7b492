package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

/**
 * Rc数量及占比
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "rcTimes",
    "rcTimesPercent",
    "noRcTimes",
    "noRcTimesPercent",
    "corpRcTimes",
    "corpRcTimesPercent",
    "corpNoRcTimes",
    "corpNoRcTimesPercent",
    "industryRcTimes",
    "industryRcTimesPercent",
    "industryNoRcTimes",
    "industryNoRcTimesPercent"
})
public class RcViewCount implements Serializable {
    private static final long serialVersionUID = 1L;





    public RcViewCount(
        Integer rcTimes,
        Double rcTimesPercent,
        Integer noRcTimes,
        Double noRcTimesPercent,
        Integer corpRcTimes,
        Double corpRcTimesPercent,
        Integer corpNoRcTimes,
        Double corpNoRcTimesPercent,
        Integer industryRcTimes,
        Double industryRcTimesPercent,
        Integer industryNoRcTimes,
        Double industryNoRcTimesPercent) {
        this.rcTimes = rcTimes;
        this.rcTimesPercent = rcTimesPercent;
        this.noRcTimes = noRcTimes;
        this.noRcTimesPercent = noRcTimesPercent;
        this.corpRcTimes = corpRcTimes;
        this.corpRcTimesPercent = corpRcTimesPercent;
        this.corpNoRcTimes = corpNoRcTimes;
        this.corpNoRcTimesPercent = corpNoRcTimesPercent;
        this.industryRcTimes = industryRcTimes;
        this.industryRcTimesPercent = industryRcTimesPercent;
        this.industryNoRcTimes = industryNoRcTimes;
        this.industryNoRcTimesPercent = industryNoRcTimesPercent;
    }

    public RcViewCount() {
    }

    /**
     * 超标次数
     */
    @JsonProperty("rcTimes")
    public Integer rcTimes;

    /**
     * 超标次数占比
     */
    @JsonProperty("rcTimesPercent")
    public Double rcTimesPercent;

    /**
     * 执行差标次数
     */
    @JsonProperty("noRcTimes")
    public Integer noRcTimes;

    /**
     * 执行差标次数占比
     */
    @JsonProperty("noRcTimesPercent")
    public Double noRcTimesPercent;

    /**
     * 商旅超标次数
     */
    @JsonProperty("corpRcTimes")
    public Integer corpRcTimes;

    /**
     * 商旅超标次数占比
     */
    @JsonProperty("corpRcTimesPercent")
    public Double corpRcTimesPercent;

    /**
     * 商旅执行差标次数
     */
    @JsonProperty("corpNoRcTimes")
    public Integer corpNoRcTimes;

    /**
     * 商旅执行差标次数占比
     */
    @JsonProperty("corpNoRcTimesPercent")
    public Double corpNoRcTimesPercent;

    /**
     * 行业超标次数
     */
    @JsonProperty("industryRcTimes")
    public Integer industryRcTimes;

    /**
     * 行业超标次数占比
     */
    @JsonProperty("industryRcTimesPercent")
    public Double industryRcTimesPercent;

    /**
     * 行业差标次数
     */
    @JsonProperty("industryNoRcTimes")
    public Integer industryNoRcTimes;

    /**
     * 行业差标次数占比
     */
    @JsonProperty("industryNoRcTimesPercent")
    public Double industryNoRcTimesPercent;

    /**
     * 超标次数
     */
    public Integer getRcTimes() {
        return rcTimes;
    }

    /**
     * 超标次数
     */
    public void setRcTimes(final Integer rcTimes) {
        this.rcTimes = rcTimes;
    }

    /**
     * 超标次数占比
     */
    public Double getRcTimesPercent() {
        return rcTimesPercent;
    }

    /**
     * 超标次数占比
     */
    public void setRcTimesPercent(final Double rcTimesPercent) {
        this.rcTimesPercent = rcTimesPercent;
    }

    /**
     * 执行差标次数
     */
    public Integer getNoRcTimes() {
        return noRcTimes;
    }

    /**
     * 执行差标次数
     */
    public void setNoRcTimes(final Integer noRcTimes) {
        this.noRcTimes = noRcTimes;
    }

    /**
     * 执行差标次数占比
     */
    public Double getNoRcTimesPercent() {
        return noRcTimesPercent;
    }

    /**
     * 执行差标次数占比
     */
    public void setNoRcTimesPercent(final Double noRcTimesPercent) {
        this.noRcTimesPercent = noRcTimesPercent;
    }

    /**
     * 商旅超标次数
     */
    public Integer getCorpRcTimes() {
        return corpRcTimes;
    }

    /**
     * 商旅超标次数
     */
    public void setCorpRcTimes(final Integer corpRcTimes) {
        this.corpRcTimes = corpRcTimes;
    }

    /**
     * 商旅超标次数占比
     */
    public Double getCorpRcTimesPercent() {
        return corpRcTimesPercent;
    }

    /**
     * 商旅超标次数占比
     */
    public void setCorpRcTimesPercent(final Double corpRcTimesPercent) {
        this.corpRcTimesPercent = corpRcTimesPercent;
    }

    /**
     * 商旅执行差标次数
     */
    public Integer getCorpNoRcTimes() {
        return corpNoRcTimes;
    }

    /**
     * 商旅执行差标次数
     */
    public void setCorpNoRcTimes(final Integer corpNoRcTimes) {
        this.corpNoRcTimes = corpNoRcTimes;
    }

    /**
     * 商旅执行差标次数占比
     */
    public Double getCorpNoRcTimesPercent() {
        return corpNoRcTimesPercent;
    }

    /**
     * 商旅执行差标次数占比
     */
    public void setCorpNoRcTimesPercent(final Double corpNoRcTimesPercent) {
        this.corpNoRcTimesPercent = corpNoRcTimesPercent;
    }

    /**
     * 行业超标次数
     */
    public Integer getIndustryRcTimes() {
        return industryRcTimes;
    }

    /**
     * 行业超标次数
     */
    public void setIndustryRcTimes(final Integer industryRcTimes) {
        this.industryRcTimes = industryRcTimes;
    }

    /**
     * 行业超标次数占比
     */
    public Double getIndustryRcTimesPercent() {
        return industryRcTimesPercent;
    }

    /**
     * 行业超标次数占比
     */
    public void setIndustryRcTimesPercent(final Double industryRcTimesPercent) {
        this.industryRcTimesPercent = industryRcTimesPercent;
    }

    /**
     * 行业差标次数
     */
    public Integer getIndustryNoRcTimes() {
        return industryNoRcTimes;
    }

    /**
     * 行业差标次数
     */
    public void setIndustryNoRcTimes(final Integer industryNoRcTimes) {
        this.industryNoRcTimes = industryNoRcTimes;
    }

    /**
     * 行业差标次数占比
     */
    public Double getIndustryNoRcTimesPercent() {
        return industryNoRcTimesPercent;
    }

    /**
     * 行业差标次数占比
     */
    public void setIndustryNoRcTimesPercent(final Double industryNoRcTimesPercent) {
        this.industryNoRcTimesPercent = industryNoRcTimesPercent;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final RcViewCount other = (RcViewCount)obj;
        return
            Objects.equal(this.rcTimes, other.rcTimes) &&
            Objects.equal(this.rcTimesPercent, other.rcTimesPercent) &&
            Objects.equal(this.noRcTimes, other.noRcTimes) &&
            Objects.equal(this.noRcTimesPercent, other.noRcTimesPercent) &&
            Objects.equal(this.corpRcTimes, other.corpRcTimes) &&
            Objects.equal(this.corpRcTimesPercent, other.corpRcTimesPercent) &&
            Objects.equal(this.corpNoRcTimes, other.corpNoRcTimes) &&
            Objects.equal(this.corpNoRcTimesPercent, other.corpNoRcTimesPercent) &&
            Objects.equal(this.industryRcTimes, other.industryRcTimes) &&
            Objects.equal(this.industryRcTimesPercent, other.industryRcTimesPercent) &&
            Objects.equal(this.industryNoRcTimes, other.industryNoRcTimes) &&
            Objects.equal(this.industryNoRcTimesPercent, other.industryNoRcTimesPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.rcTimes == null ? 0 : this.rcTimes.hashCode());
        result = 31 * result + (this.rcTimesPercent == null ? 0 : this.rcTimesPercent.hashCode());
        result = 31 * result + (this.noRcTimes == null ? 0 : this.noRcTimes.hashCode());
        result = 31 * result + (this.noRcTimesPercent == null ? 0 : this.noRcTimesPercent.hashCode());
        result = 31 * result + (this.corpRcTimes == null ? 0 : this.corpRcTimes.hashCode());
        result = 31 * result + (this.corpRcTimesPercent == null ? 0 : this.corpRcTimesPercent.hashCode());
        result = 31 * result + (this.corpNoRcTimes == null ? 0 : this.corpNoRcTimes.hashCode());
        result = 31 * result + (this.corpNoRcTimesPercent == null ? 0 : this.corpNoRcTimesPercent.hashCode());
        result = 31 * result + (this.industryRcTimes == null ? 0 : this.industryRcTimes.hashCode());
        result = 31 * result + (this.industryRcTimesPercent == null ? 0 : this.industryRcTimesPercent.hashCode());
        result = 31 * result + (this.industryNoRcTimes == null ? 0 : this.industryNoRcTimes.hashCode());
        result = 31 * result + (this.industryNoRcTimesPercent == null ? 0 : this.industryNoRcTimesPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("rcTimes", rcTimes)
            .add("rcTimesPercent", rcTimesPercent)
            .add("noRcTimes", noRcTimes)
            .add("noRcTimesPercent", noRcTimesPercent)
            .add("corpRcTimes", corpRcTimes)
            .add("corpRcTimesPercent", corpRcTimesPercent)
            .add("corpNoRcTimes", corpNoRcTimes)
            .add("corpNoRcTimesPercent", corpNoRcTimesPercent)
            .add("industryRcTimes", industryRcTimes)
            .add("industryRcTimesPercent", industryRcTimesPercent)
            .add("industryNoRcTimes", industryNoRcTimes)
            .add("industryNoRcTimesPercent", industryNoRcTimesPercent)
            .toString();
    }
}
