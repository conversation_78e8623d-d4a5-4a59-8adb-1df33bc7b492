package com.corpgovernment.resource.schedule.domain.pricecomparison.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Getter
@AllArgsConstructor
@ToString
public enum TaskStatusComparisonEnum {

    READY(1, "待执行"),
    SUCCESS(2, "执行成功"),
    FAIL(3, "执行失败"),
    INQUIRY(4, "执行中"),
    ;

    private final Integer code;
    private final String desc;

}
