package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 差旅出行-app入口概览
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "topEventCityInfoList",
    "topTripCityInfo",
    "topWTripCityInfo"
})
public class TravelGeneralInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public TravelGeneralInfo(
        List<EventCityInfo> topEventCityInfoList,
        TopTripCityInfo topTripCityInfo,
        TopTripCityInfo topWTripCityInfo) {
        this.topEventCityInfoList = topEventCityInfoList;
        this.topTripCityInfo = topTripCityInfo;
        this.topWTripCityInfo = topWTripCityInfo;
    }

    public TravelGeneralInfo() {
    }

    /**
     * top事件
     */
    @JsonProperty("topEventCityInfoList")
    public List<EventCityInfo> topEventCityInfoList;

    /**
     * top城市行程
     */
    @JsonProperty("topTripCityInfo")
    public TopTripCityInfo topTripCityInfo;

    /**
     * top城市行程
     */
    @JsonProperty("topWTripCityInfo")
    public TopTripCityInfo topWTripCityInfo;

    /**
     * top事件
     */
    public List<EventCityInfo> getTopEventCityInfoList() {
        return topEventCityInfoList;
    }

    /**
     * top事件
     */
    public void setTopEventCityInfoList(final List<EventCityInfo> topEventCityInfoList) {
        this.topEventCityInfoList = topEventCityInfoList;
    }

    /**
     * top城市行程
     */
    public TopTripCityInfo getTopTripCityInfo() {
        return topTripCityInfo;
    }

    /**
     * top城市行程
     */
    public void setTopTripCityInfo(final TopTripCityInfo topTripCityInfo) {
        this.topTripCityInfo = topTripCityInfo;
    }

    /**
     * top城市行程
     */
    public TopTripCityInfo getTopWTripCityInfo() {
        return topWTripCityInfo;
    }

    /**
     * top城市行程
     */
    public void setTopWTripCityInfo(final TopTripCityInfo topWTripCityInfo) {
        this.topWTripCityInfo = topWTripCityInfo;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TravelGeneralInfo other = (TravelGeneralInfo)obj;
        return
            Objects.equal(this.topEventCityInfoList, other.topEventCityInfoList) &&
            Objects.equal(this.topTripCityInfo, other.topTripCityInfo) &&
            Objects.equal(this.topWTripCityInfo, other.topWTripCityInfo);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.topEventCityInfoList == null ? 0 : this.topEventCityInfoList.hashCode());
        result = 31 * result + (this.topTripCityInfo == null ? 0 : this.topTripCityInfo.hashCode());
        result = 31 * result + (this.topWTripCityInfo == null ? 0 : this.topWTripCityInfo.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("topEventCityInfoList", topEventCityInfoList)
            .add("topTripCityInfo", topTripCityInfo)
            .add("topWTripCityInfo", topWTripCityInfo)
            .toString();
    }
}
