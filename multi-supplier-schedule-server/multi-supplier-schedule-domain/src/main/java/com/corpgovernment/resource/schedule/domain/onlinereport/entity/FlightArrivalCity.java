package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

/**
 * 到达城市
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dataTop",
    "data"
})
public class FlightArrivalCity implements Serializable {
    private static final long serialVersionUID = 1L;





    public FlightArrivalCity(
        List<FlightArrivalEntity> dataTop,
        List<FlightArrivalEntity> data) {
        this.dataTop = dataTop;
        this.data = data;
    }

    public FlightArrivalCity() {
    }

    /**
     * 热门城市top10
     */
    @JsonProperty("dataTop")
    public List<FlightArrivalEntity> dataTop;

    @JsonProperty("data")
    public List<FlightArrivalEntity> data;

    /**
     * 热门城市top10
     */
    public List<FlightArrivalEntity> getDataTop() {
        return dataTop;
    }

    /**
     * 热门城市top10
     */
    public void setDataTop(final List<FlightArrivalEntity> dataTop) {
        this.dataTop = dataTop;
    }
    public List<FlightArrivalEntity> getData() {
        return data;
    }

    public void setData(final List<FlightArrivalEntity> data) {
        this.data = data;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightArrivalCity other = (FlightArrivalCity)obj;
        return
            Objects.equal(this.dataTop, other.dataTop) &&
            Objects.equal(this.data, other.data);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dataTop == null ? 0 : this.dataTop.hashCode());
        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dataTop", dataTop)
            .add("data", data)
            .toString();
    }
}
