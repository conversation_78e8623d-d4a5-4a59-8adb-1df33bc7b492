package com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/28 11:16
 */
@Data
public class ReportLibHiveResultEntity {

    private List<Map<String, String>> result;

    private List<List<String>> data;

    private String scrollId;

    private Integer totalCount;

    private List<String> titleList;

    private Integer rowCount = 1;

    private Integer columnCount = 0;

    private List<List<String>> rowList;

}
