package com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 审计枚举
 * @create 2024-12-23 16:13
 */
@AllArgsConstructor
@Getter
public enum HotelAuditRuleEnum {
    
    CONTEXT_INFO_LOSS("contextInfoLoss", "上下文信息缺失", "contextInfoLossError"),
    NO_TOKEN_ORDER("noTokenOrder", "无token订单", "noTokenOrderError"),
    SUPPLEMENT_ORDER("supplementOrder", "补录订单", "supplementOrderError"),
    
    MAX_PRICE("maxPrice", "价格上限", "maxPriceError"),
    MAX_PRICE_UN_LIMIT("maxPriceUnLimit", "价格上限为不限", "maxPriceUnLimitError"),
    FLOAT_PRICE("floatPrice","浮动价格","floatPriceError"),
    XUGONG("xugong", "徐工", "xugongError"),
    COHABIT_ROOM_UN_LIMIT("cohabitRoomUnLimit", "同住房间管控差标不限", "cohabitRoomUnLimitError"),
    OVER_LIMIT_CONTROL_MODE("overLimitControlMode", "超标管控方式", "overLimitControlModeError"),
    STAR("star", "星级管控", "starError"),
    
    ;
    
    private final String code;
    
    private final String info;
    
    private final String errorLabel;
    
}
