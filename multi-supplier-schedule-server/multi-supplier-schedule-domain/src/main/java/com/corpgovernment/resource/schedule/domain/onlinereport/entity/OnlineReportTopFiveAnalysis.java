package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 前5部门超标、里程均价、间夜均价分析
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dept",
    "result"
})
public class OnlineReportTopFiveAnalysis implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportTopFiveAnalysis(
        String dept,
        BigDecimal result) {
        this.dept = dept;
        this.result = result;
    }

    public OnlineReportTopFiveAnalysis() {
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    @JsonProperty("dept")
    public String dept;

    /**
     * 金额|占比
     */
    @JsonProperty("result")
    public BigDecimal result;

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public String getDept() {
        return dept;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public void setDept(final String dept) {
        this.dept = dept;
    }

    /**
     * 金额|占比
     */
    public BigDecimal getResult() {
        return result;
    }

    /**
     * 金额|占比
     */
    public void setResult(final BigDecimal result) {
        this.result = result;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTopFiveAnalysis other = (OnlineReportTopFiveAnalysis)obj;
        return
            Objects.equal(this.dept, other.dept) &&
            Objects.equal(this.result, other.result);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dept == null ? 0 : this.dept.hashCode());
        result = 31 * result + (this.result == null ? 0 : this.result.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dept", dept)
            .add("result", result)
            .toString();
    }
}
