package com.corpgovernment.resource.schedule.domain.onlinereport.config;

import com.corpgovernment.resource.schedule.domain.onlinereport.config.inner.AirlineBo;
import com.corpgovernment.resource.schedule.domain.onlinereport.config.inner.TimeScaleBo;
import lombok.Data;

import java.util.List;

/**
 * 供应商监测
 * <AUTHOR>
 * @date 2024-06-13
 */
@Data
public class SupplierMonitorDto {
    private List<AirlineBo> excludedAirlines;
    private TimeScaleBo timeScale;
}
