package com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.impl;

import com.corpgovernment.resource.schedule.domain.hotelaudit.model.entity.*;
import com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.*;
import com.corpgovernment.resource.schedule.domain.hotelaudit.service.audit.IHotelAuditRule;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardTypeEnum.FLOAT_TRAVEL_STANDARD;
import static com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums.TravelStandardTypeEnum.STANDARD_TRAVEL_STANDARD;

/**
 * <AUTHOR>
 * @description
 * @create 2025-01-06 19:32
 */
@Slf4j
@Service
public class OverLimitControlModeAuditRule implements IHotelAuditRule {
    
    @Override
    public HotelAuditResult audit(HotelAuditItem hotelAuditItem) {
        Boolean overLimit = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getOrderInfo)
                .map(OrderInfo::getOverLimit)
                .orElse(null);
        
        PayTypeEnum payTypeEnum = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getOrderInfo)
                .map(OrderInfo::getPayTypeEnum)
                .orElse(null);

        TokenInfo tokenInfo = Optional.ofNullable(hotelAuditItem)
                .map(HotelAuditItem::getTokenInfo).orElse(null);

        // 未超标
        if (!Boolean.TRUE.equals(overLimit)) {
            return HotelAuditResult.buildHotelAuditResult(getHotelAuditRuleEnum(), null);
        }
        
        // 只考虑标准，不考虑浮动
        List<OverLimitModeEnum> overLimitModeEnumList = getOverLimitModeEnumList(tokenInfo);
        log.info("OverLimitControlModeAuditRule overLimitModeEnumList:{}", overLimitModeEnumList);
        
        List<String> errorDescList = new ArrayList<>();
        if (CollectionUtils.isEmpty(overLimitModeEnumList)) {
            errorDescList.add("超标管控方式为空，不能下单");
        } else if (overLimitModeEnumList.contains(OverLimitModeEnum.F)) {
            errorDescList.add("超标管控方式为禁止预订，不能下单");
        } else if (Objects.equals(payTypeEnum, PayTypeEnum.MIXED_PAY) && !overLimitModeEnumList.contains(OverLimitModeEnum.M)) {
            errorDescList.add("订单是混付单，但是超标管控方式没有混付");
        } else if ((Objects.equals(payTypeEnum, PayTypeEnum.PERSON_PAY)
                || Objects.equals(payTypeEnum, PayTypeEnum.CASH_PAY)
                || Objects.equals(payTypeEnum, PayTypeEnum.PUBLIC_PAY))
                && !overLimitModeEnumList.contains(OverLimitModeEnum.C)) {
            errorDescList.add("订单不是混付单，但是超标管控方式没有选择原因后继续预订");
        }
        
        return HotelAuditResult.buildHotelAuditResult(getHotelAuditRuleEnum(), errorDescList);
    }
    
    @Override
    public HotelAuditRuleEnum getHotelAuditRuleEnum() {
        return HotelAuditRuleEnum.OVER_LIMIT_CONTROL_MODE;
    }
    
    @Override
    public List<TravelModeEnum> getSupportTravelModeEnumList() {
        return Collections.singletonList(TravelModeEnum.PUB);
    }


    private List<OverLimitModeEnum> getOverLimitModeEnumList(TokenInfo tokenInfo){
        if (tokenInfo == null){
            return Collections.emptyList();
        }
        List<TravelStandardTypeEnum> travelStandardTypeEnumList = Optional.ofNullable(tokenInfo.getTravelStandard())
                .map(TravelStandard::getTravelStandardTypeEnumList)
                .orElse(new ArrayList<>());
        List<OverLimitModeEnum> overLimitModeEnumList = Optional.ofNullable(tokenInfo.getTravelStandard())
                .map(TravelStandard::getOverLimitModeEnumList)
                .orElse(Collections.emptyList());
        // 无浮动情况  返回标准超标拒绝策略
        OrderTravelStandardResult orderTravelStandardResult = tokenInfo.getOrderTravelStandardResult();
        if (!travelStandardTypeEnumList.contains(FLOAT_TRAVEL_STANDARD) || orderTravelStandardResult == null){
            return overLimitModeEnumList;
        }

        // 判断差标类型
        OrderTravelStandardResult.AvgPriceRuleResult avgPriceRuleResult = Optional.ofNullable(orderTravelStandardResult.getMaxAvgPriceResult())
                .orElse(new OrderTravelStandardResult.AvgPriceRuleResult());
        // 浮动命中取浮动的
        boolean standardPriceExceed = false;
        boolean floatPriceExceed = false;
        if (STANDARD_TRAVEL_STANDARD.getCode().equalsIgnoreCase(avgPriceRuleResult.getOverLimitType())){
            standardPriceExceed = Boolean.TRUE.equals(avgPriceRuleResult.getOverLimit());
        }
        if (FLOAT_TRAVEL_STANDARD.getCode().equalsIgnoreCase(avgPriceRuleResult.getOverLimitType())){
            floatPriceExceed = Boolean.TRUE.equals(avgPriceRuleResult.getOverLimit());
        }
        // 超浮动差标 返回标准差标拒绝策略
        if (floatPriceExceed){
            return overLimitModeEnumList;
        }
        // 命中浮动(即超标准差标但不超浮动差标) 且超其它标准差标，则与标准差标取交集
        boolean exceedBrandAndStarRule = judgeExceedBrandAndStarRule(orderTravelStandardResult);
        List<OverLimitModeEnum> floatOverLimitModeEnumList = Optional.ofNullable(tokenInfo.getTravelStandard())
                .map(TravelStandard::getFloatOverLimitModeEnumList)
                .orElse(Collections.emptyList());
        if (standardPriceExceed){
            // 超其它标准差标，则取交集，不超则返回浮动拒绝策略
            return exceedBrandAndStarRule ? floatOverLimitModeEnumList.stream().filter(overLimitModeEnumList::contains).distinct().collect(Collectors.toList()) : floatOverLimitModeEnumList;
        }
        return Collections.emptyList();
    }

    private boolean judgeExceedBrandAndStarRule(OrderTravelStandardResult orderTravelStandardResult){
        if (Objects.isNull(orderTravelStandardResult)){
            return false;
        }
        OrderTravelStandardResult.BrandRuleResult brandRuleResult = orderTravelStandardResult.getBrandRuleResult();
        OrderTravelStandardResult.StarRuleResult starRuleResult = orderTravelStandardResult.getStarRuleResult();
        return Optional.ofNullable(brandRuleResult).map(OrderTravelStandardResult.BrandRuleResult::getOverLimit).orElse(false)
                || Optional.ofNullable(starRuleResult).map(OrderTravelStandardResult.StarRuleResult::getOverLimit).orElse(false);
    }
    
}
