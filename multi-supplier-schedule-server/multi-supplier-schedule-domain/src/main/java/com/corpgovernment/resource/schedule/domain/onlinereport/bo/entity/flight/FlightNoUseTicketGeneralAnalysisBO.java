package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 机票乘客未使用机票分析详情
 * <AUTHOR>
 * @Date 2019/5/23
 */
public class FlightNoUseTicketGeneralAnalysisBO {
    /**
     * 未使用机票列表
     */
    private List<FlightTicketInfoBO> noUseTicketInfoList;

    /**
     * 总概分析详情
     */
    private FlightNoUseTicketGeneralAnalysisInfoBO generalAnalysisInfo;

    public FlightNoUseTicketGeneralAnalysisBO(){
        this.noUseTicketInfoList=new ArrayList<>(0);
        this.generalAnalysisInfo=new FlightNoUseTicketGeneralAnalysisInfoBO();
    }

    public List<FlightTicketInfoBO> getNoUseTicketInfoList() {
        return noUseTicketInfoList;
    }

    public void setNoUseTicketInfoList(List<FlightTicketInfoBO> noUseTicketInfoList) {
        this.noUseTicketInfoList = noUseTicketInfoList;
    }

    public FlightNoUseTicketGeneralAnalysisInfoBO getGeneralAnalysisInfo() {
        return generalAnalysisInfo;
    }

    public void setGeneralAnalysisInfo(FlightNoUseTicketGeneralAnalysisInfoBO generalAnalysisInfo) {
        this.generalAnalysisInfo = generalAnalysisInfo;
    }
}
