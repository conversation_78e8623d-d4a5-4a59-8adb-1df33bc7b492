package com.corpgovernment.resource.schedule.domain.falconcache.service;

import com.corpgovernment.resource.schedule.domain.falconcache.config.FalconCacheServiceConfig;
import com.corpgovernment.resource.schedule.domain.falconcache.model.KeyModel;
import com.corpgovernment.resource.schedule.domain.falconcache.model.TimeRoundSector;
import com.ctrip.corp.obt.falconcache.core.dto.AccessAmountDTO;
import com.ctrip.corp.obt.falconcache.core.dto.BatchReportMessage;
import com.ctrip.corp.obt.falconcache.core.util.DefaultBatchSender;
import com.ctrip.corp.obt.falconcache.core.util.DefaultTimer;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.corp.obt.metric.Metrics;
import com.ctrip.corp.obt.metric.spectator.api.Id;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/8
 */
@Component
@Slf4j
public class CalculateHotKeyService {

    private static final Id MEMORY_SIZE_METRIC = Metrics.REGISTRY.createId("falconcache.memory.size");
    /**
     * 时间轮 Map< appName , Map< uniqueKey , 时间轮>>
     */
    private volatile Map<String, Map<KeyModel, LinkedBlockingQueue<TimeRoundSector>>> timeRoundMap = new ConcurrentHashMap<>();
    /**
     * 时间轮的数量
     */
    private static final String TIME_ROUND_SIZE = "timeround.size";
    /**
     * 时间轮一个分片的时间
     */
    private static final String TIME_ROUND_TIME = "timeround.time";

    /**
     * 热key数量
     */
    private static final String HOT_KEY_NUM_KEY = "hotkey.num";

    private static final String HOT_KEY_MEMORY = "hotkey.memory";

    /**
     * 热key白名单
     */
    private static final String WHITE_LIST_KEY = "key.whitelist";
    private static final int DEFAULT_SIZE = 20;

    private static final int DEFAULT_SECTOR_TIME = 3;
    private static final int DEFAULT_HOT_KEY_NUM = 30;
    /**
     * 5M数据
     */
    private static final int DEFAULT_HOT_KEY_MEMORY_SIZE = 5 * 1024 * 1024;
    /**
     * 时间轮大小
     */
    private volatile Integer CONFIG_SIZE;
    /**
     * 时间轮一个分片的时间
     */
    private volatile Integer SECTOR_TIME;

    private volatile Long HOT_KEY_MEMORY_SIZE;
    /**
     * 时间轮的总时间
     */
    private volatile Long TIME_ROUND_ALL_TIME;

    /**
     * 热key数量
     */
    private  Integer HOT_KEY_TOP;

    /**
     * 白名单
     */
    private List<String> WHITE_LIST;

    private DefaultTimer defaultTimer = DefaultTimer.INSTANCE;

    private final  AtomicBoolean inited = new AtomicBoolean(false);
    @Autowired
    private DistributeHotKeyService distributeHotKeyService;

    @Resource
    @Qualifier("FalconRedisRedisTemplate")
    private RedisTemplate<String, byte[]> redisTemplate;
    @Autowired
    public CalculateHotKeyService(){
        initRoundSize();
    }


    private DefaultBatchSender<BatchReportMessage,  Map<String, Map<KeyModel, LinkedBlockingQueue<TimeRoundSector>>>> defaultBatchSender;

    private void doBatch(Map<String, Map<KeyModel, LinkedBlockingQueue<TimeRoundSector>>> timeRoundMap){
        if (!inited.get()){
            return;
        }
        Map<String, Map<KeyModel, Integer>> accumulateMap = new HashMap<>();
        Map<String, List<KeyModel>> appKeyRank = new HashMap<>();
        // 遍历整个时间轮
        timeRoundMap.forEach((k, v) -> {
            // 单个key的count计算
            Map<KeyModel, Integer> singleAccumlatedMap = new HashMap<>();
            accumulateMap.put(k, singleAccumlatedMap);
            v.forEach((appkey, appValue) -> {
                handleOutOfTime(appValue, TIME_ROUND_ALL_TIME);
                if (appValue.peek() != null){
                    int totalAmount = 0;
                    for (TimeRoundSector sector : appValue) {
                        totalAmount += sector.getAmount();
                    }
                    singleAccumlatedMap.put(appkey, totalAmount);
                }
            });

            List<KeyModel> topNKeys = ensureSizeMemory(singleAccumlatedMap);
            if (CollectionUtils.isNotEmpty(topNKeys)){
                appKeyRank.put(k, topNKeys.stream().map(t->new KeyModel(t.getKey(), t.getKeyPrefix())).collect(Collectors.toList()));
            }
        });
        distributeHotKeyService.distributeHotKey(appKeyRank);

    }

    private List<KeyModel> ensureSizeMemory(Map<KeyModel, Integer> singleAccumlatedMap) {
        List<Map.Entry<KeyModel, Integer>> list = new ArrayList<>(singleAccumlatedMap.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<KeyModel, Integer>>() {
            @Override
            public int compare(Map.Entry<KeyModel, Integer> o1, Map.Entry<KeyModel, Integer> o2) {
                return o2.getValue().compareTo(o1.getValue());
            }
        });
        int N = HOT_KEY_TOP; // 取前 N 个元素
        List<Map.Entry<KeyModel, Integer>> topNList = list.subList(0, Math.min(N - 1, list.size()));

        List<KeyModel> topNKeys = new ArrayList<>();
        for (Map.Entry<KeyModel, Integer> entry : topNList) {
            topNKeys.add(entry.getKey());
        }
        topNKeys = deleteValueListIfExceedsMemoryLimit(topNKeys);
        return topNKeys;
    }
    private List<KeyModel> deleteValueListIfExceedsMemoryLimit(List<KeyModel> topNKeys) {
        if(CollectionUtils.isEmpty(topNKeys)){
            return Collections.emptyList();
        }
        List<String> topNString = topNKeys.stream().map(t->{
            if (StringUtils.isBlank(t.getKeyPrefix())){
                return t.getKey();
            }
            StringJoiner joiner = new StringJoiner("");
            joiner.add(t.getKeyPrefix());
            if (t.getKey() != null) {
                joiner.add(t.getKey());
            }
            return joiner.toString();
        }).collect(Collectors.toList());
        List<byte[]> valueList = redisTemplate.opsForValue().multiGet(topNString);
        if(CollectionUtils.isEmpty(valueList)){
            return Collections.emptyList();
        }
//        topNKeys = new LinkedList<>(topNKeys);
//        for (int i = 0; i < valueList.size(); i++) {
//            if (valueList.get(i) == null) {
//                topNKeys.remove(i);
//            }
//        }
        if (CollectionUtils.isEmpty(topNKeys)){
            return Collections.emptyList();
        }
        valueList = new LinkedList<>(valueList);
        long size = 0L;
        while ((size=calculateMemoryUsage(valueList)) > HOT_KEY_MEMORY_SIZE) {
            removeLast(topNKeys);
            removeLast(valueList);
        }
        recordMemorySize(size);
        return topNKeys;
    }

    private void recordMemorySize(long size) {
        try {
            Metrics.REGISTRY.distributionSummary(MEMORY_SIZE_METRIC).record(size);
        }catch (Exception e){
            log.error("record memory size error", e);
        }

    }

    private long calculateMemoryUsage(List<byte[]> valueList) {
        if (CollectionUtils.isEmpty(valueList)){
            return 0L;
        }
        long totalSize = 0;
        for (byte[] value : valueList) {
            if(value == null){
                continue;
            }
            totalSize += value.length;
        }
        return totalSize;
    }

    private <T> void removeLast(List<T> topNKeys) {
        if (!topNKeys.isEmpty()) {
            topNKeys.remove(topNKeys.size() - 1);
        }
    }



    /**
     * 将单次汇总记录输入
     * @param cacheMessage
     */
    private void doSingle(BatchReportMessage cacheMessage){
        if (!inited.get()){
            return;
        }
        if (cacheMessage == null){
            return;
        }
        if (cacheMessage.getReportData() == null || cacheMessage.getReportData().size() == 0){
            return;
        }
        List<AccessAmountDTO> keyList = cacheMessage.getReportData();
        String area = cacheMessage.getArea();
        Map<KeyModel, LinkedBlockingQueue<TimeRoundSector>> uniqueKeyMap =
                timeRoundMap.computeIfAbsent(area, k -> new HashMap<>());
        keyList.forEach(key -> {
            LinkedBlockingQueue<TimeRoundSector> timeRoundQueue =
                    uniqueKeyMap.computeIfAbsent(convert(key), k -> new LinkedBlockingQueue<>(CONFIG_SIZE));
            TimeRoundSector timeRoundSector = new TimeRoundSector(key.getAmount(), defaultTimer.currentTimeMillis());
            if (!timeRoundQueue.offer(timeRoundSector)) {
                timeRoundQueue.poll();
                timeRoundQueue.offer(timeRoundSector);
                handleOutOfTime(timeRoundQueue, TIME_ROUND_ALL_TIME);
            }
        });
    }
    private KeyModel convert(AccessAmountDTO accessAmountDTO){
        KeyModel keyModel = new KeyModel();
        keyModel.setKeyPrefix(accessAmountDTO.getKeyPrefix());
        keyModel.setKey(accessAmountDTO.getKeyName());
        return keyModel;

    }

    /**
     * 移除过期的数据
     * @param timeRoundQueue
     * @param outTime
     */
    private void handleOutOfTime(LinkedBlockingQueue<TimeRoundSector> timeRoundQueue, long outTime){
        long currentTime = defaultTimer.currentTimeMillis();
        TimeRoundSector timeRoundSector = timeRoundQueue.peek();
        while ( timeRoundSector != null && currentTime - timeRoundSector.getTimestamp() > outTime){
            timeRoundSector = timeRoundQueue.poll();
        }
    }


    public void accumulateHotKey(BatchReportMessage cacheMessage){
        defaultBatchSender.doAccumulate(cacheMessage);
    }

    private void initRoundSize() {
        if (!inited.compareAndSet(false, true)){
            return;
        }
        if (CONFIG_SIZE != null) {
            return;
        }
        String timeRoundSize = FalconCacheServiceConfig.defaultConfig().get(TIME_ROUND_SIZE);
        String timeRoundTime = FalconCacheServiceConfig.defaultConfig().get(TIME_ROUND_TIME);
        String whiteList = FalconCacheServiceConfig.defaultConfig().get(WHITE_LIST_KEY);
        String hotKeyNumString = FalconCacheServiceConfig.defaultConfig().get(HOT_KEY_NUM_KEY);
        String hotKeyMemory = FalconCacheServiceConfig.defaultConfig().get(HOT_KEY_MEMORY);
        int roundSize;
        int roundTime;
        int hotKeyNum;
        int hotKeyMemorySize;
        try {
            roundSize = StringUtils.isBlank(timeRoundSize) ? DEFAULT_SIZE : Integer.parseInt(timeRoundSize);
            roundTime = StringUtils.isBlank(timeRoundTime) ? DEFAULT_SECTOR_TIME : Integer.parseInt(timeRoundTime);
            hotKeyNum = StringUtils.isBlank(hotKeyNumString) ? DEFAULT_HOT_KEY_NUM : Integer.parseInt(hotKeyNumString);
            hotKeyMemorySize = StringUtils.isBlank(hotKeyMemory) ? DEFAULT_HOT_KEY_MEMORY_SIZE : Integer.parseInt(hotKeyMemory);
        } catch (Exception e) {
            log.error("timeround size convert error, original size string:{},original time string:{}", timeRoundSize, timeRoundTime);
            roundSize = DEFAULT_SIZE;
            hotKeyNum = DEFAULT_HOT_KEY_NUM;
            roundTime = DEFAULT_SECTOR_TIME;
            hotKeyMemorySize = DEFAULT_HOT_KEY_MEMORY_SIZE;
        }
        CONFIG_SIZE = roundSize;
        SECTOR_TIME = roundTime;
        TIME_ROUND_ALL_TIME = CONFIG_SIZE * SECTOR_TIME * 1000L;
        WHITE_LIST = StringUtils.isNotBlank(whiteList) ? Arrays.asList((whiteList.split(","))) : Collections.emptyList();
        HOT_KEY_TOP = hotKeyNum + WHITE_LIST.size();
        HOT_KEY_MEMORY_SIZE = (long) hotKeyMemorySize;
        defaultBatchSender = new DefaultBatchSender<>(
                "CalculateHotKeyService", timeRoundMap, this::doSingle, this::doBatch,1,3000,500);
    }

}
