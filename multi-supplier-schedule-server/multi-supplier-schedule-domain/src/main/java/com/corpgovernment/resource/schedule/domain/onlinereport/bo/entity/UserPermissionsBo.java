package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.search.MainAccountsList;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/28 14:58
 */
@Data
public class UserPermissionsBo {

    private String uid;
    //private ManagerTypeEnum managerType;
    private String groupId;
    private List<String> resourceKeys;
    private List<String> corpIds;
    //private List<ProductLineEnum> productLines;
    private Boolean hasIndustryData;
    private Map<String, MainAccountsList> corpAccountsMap;
    //private List<CostCenterAndDept> costcenters;
    //private List<CostCenterAndDept> depts;
}
