package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.search.MainAccountsList;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 用户权限业务对象
 * 该类用于封装用户的各种权限信息，包括用户ID、所属组ID、资源权限、公司ID列表等信息
 *
 * <AUTHOR>
 */
@Data
public class UserPermissionsBo {

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 用户所属组ID
     */
    private String groupId;

    /**
     * 用户拥有的资源权限键列表
     */
    private List<String> resourceKeys;

    /**
     * 用户可访问的公司ID列表
     */
    private List<String> corpIds;

    /**
     * 是否拥有行业数据权限
     */
    private Boolean hasIndustryData;

    /**
     * 公司账户映射关系，key为公司ID，value为该公司下的主账户列表
     */
    private Map<String, MainAccountsList> corpAccountsMap;

}