package com.corpgovernment.resource.schedule.domain.onlinereport.model.dept;

import lombok.Data;

import java.math.BigDecimal;

/*
 * <AUTHOR>
 * @date 2021/12/15 14:56
 * @Desc
 */
@Data
public class CarDeptDTO {

    private String aggId;

    private String aggType;

    private BigDecimal totalAmount;

    private BigDecimal totalAmountAirportpickDom;

    private BigDecimal totalAmountAirportpickInter;

    private BigDecimal totalAmountCharter;

    private BigDecimal totalAmountRent;

    private BigDecimal totalAmountTax;

    private Integer totalOrderCount;

    private BigDecimal totalCarbons;
    private BigDecimal totalCarbonsTpms;

    private BigDecimal totalNormalDistance;
}
