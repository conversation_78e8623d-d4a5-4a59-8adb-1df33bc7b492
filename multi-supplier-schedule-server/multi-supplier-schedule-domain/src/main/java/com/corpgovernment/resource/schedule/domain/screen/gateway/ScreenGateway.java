package com.corpgovernment.resource.schedule.domain.screen.gateway;


import com.corpgovernment.resource.schedule.domain.screen.model.ConsumeRankBO;
import com.corpgovernment.resource.schedule.domain.screen.model.ConsumeRankDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.HotCityBO;
import com.corpgovernment.resource.schedule.domain.screen.model.HotCityDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapBO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDestinationInfoBO;
import com.corpgovernment.resource.schedule.domain.screen.model.MapDestinationInfoDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.StandardRateBO;
import com.corpgovernment.resource.schedule.domain.screen.model.StandardRateDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.TotalAmountDTO;
import com.corpgovernment.resource.schedule.domain.screen.model.TotalCountBO;
import com.corpgovernment.resource.schedule.domain.screen.model.TripRankBO;
import com.corpgovernment.resource.schedule.domain.screen.model.TripRankDTO;
import screen.response.AggregateResp;

import java.util.List;

/**
 * <AUTHOR> Smith
 */
public interface ScreenGateway {
    List<TotalCountBO> totalAmountAndOrder(TotalAmountDTO totalAmountDTO);

    List<ConsumeRankBO> consumeRankDept(ConsumeRankDTO consumeRankDTO);

    List<HotCityBO> hotCity(HotCityDTO hotCityDTO);

    StandardRateBO standardRate(StandardRateDTO standardRateDTO);

    List<TripRankBO> tripRank(TripRankDTO tripRankDTO);

    List<MapBO> map(MapDTO mapDTO);

    List<ConsumeRankBO> consumeRankCostCenter(ConsumeRankDTO consumeRankDTO);

    List<MapDestinationInfoBO> destinationInfo(MapDestinationInfoDTO mapDestinationInfoDTO);

    void getSaveAmount(TotalAmountDTO totalAmountDTO, AggregateResp aggregateResp);

    List<ConsumeRankBO> consumeLegalRank(ConsumeRankDTO consumeRankDTO);
}
