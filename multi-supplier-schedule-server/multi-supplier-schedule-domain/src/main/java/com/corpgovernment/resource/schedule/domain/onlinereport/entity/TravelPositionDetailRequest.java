package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.QueryReportBuTypeEnum;
import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TravelPositionStepEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 差旅足迹明细查询
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "eId",
        "lang",
        "basecondition",
        "page",
        "queryBus",
        "travelStep",
        "queryColumn",
        "productType",
        "extData"
})
public class TravelPositionDetailRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    public TravelPositionDetailRequest(
            String eId,
            String lang,
            BaseQueryCondition basecondition,
            Pager page,
            List<QueryReportBuTypeEnum> queryBus,
            TravelPositionStepEnum travelStep,
            List<TravelPositionDetail> queryColumn,
            String productType,
            Map<String, String> extData) {
        this.eId = eId;
        this.lang = lang;
        this.basecondition = basecondition;
        this.page = page;
        this.queryBus = queryBus;
        this.travelStep = travelStep;
        this.queryColumn = queryColumn;
        this.productType = productType;
        this.extData = extData;
    }

    public TravelPositionDetailRequest() {
    }

    @JsonProperty("eId")
    public String eId;

    /**
     * 语言
     */
    @JsonProperty("lang")
    public String lang;

    @JsonProperty("basecondition")
    public BaseQueryCondition basecondition;

    /**
     * page翻页
     */
    @JsonProperty("page")
    public Pager page;

    /**
     * 机酒火
     */
    @JsonProperty("queryBus")
    public List<QueryReportBuTypeEnum> queryBus;

    /**
     * 正在出差、将要去、已离开
     */
    @JsonProperty("travelStep")
    public TravelPositionStepEnum travelStep;

    /**
     * 基本查询字段
     */
    @JsonProperty("queryColumn")
    public List<TravelPositionDetail> queryColumn;

    /**
     * 国内：dom，国际：inter
     */
    @JsonProperty("productType")
    public String productType;

    /**
     * 扩展字段
     */
    @JsonProperty("extData")
    public Map<String, String> extData;

    public String getEId() {
        return eId;
    }

    public void setEId(final String eId) {
        this.eId = eId;
    }

    /**
     * 语言
     */
    public String getLang() {
        return lang;
    }

    /**
     * 语言
     */
    public void setLang(final String lang) {
        this.lang = lang;
    }

    public BaseQueryCondition getBasecondition() {
        return basecondition;
    }

    public void setBasecondition(final BaseQueryCondition basecondition) {
        this.basecondition = basecondition;
    }

    /**
     * page翻页
     */
    public Pager getPage() {
        return page;
    }

    /**
     * page翻页
     */
    public void setPage(final Pager page) {
        this.page = page;
    }

    /**
     * 机酒火
     */
    public List<QueryReportBuTypeEnum> getQueryBus() {
        return queryBus;
    }

    /**
     * 机酒火
     */
    public void setQueryBus(final List<QueryReportBuTypeEnum> queryBus) {
        this.queryBus = queryBus;
    }

    /**
     * 正在出差、将要去、已离开
     */
    public TravelPositionStepEnum getTravelStep() {
        return travelStep;
    }

    /**
     * 正在出差、将要去、已离开
     */
    public void setTravelStep(final TravelPositionStepEnum travelStep) {
        this.travelStep = travelStep;
    }

    /**
     * 基本查询字段
     */
    public List<TravelPositionDetail> getQueryColumn() {
        return queryColumn;
    }

    /**
     * 基本查询字段
     */
    public void setQueryColumn(final List<TravelPositionDetail> queryColumn) {
        this.queryColumn = queryColumn;
    }

    /**
     * 国内：dom，国际：inter
     */
    public String getProductType() {
        return productType;
    }

    /**
     * 国内：dom，国际：inter
     */
    public void setProductType(final String productType) {
        this.productType = productType;
    }

    /**
     * 扩展字段
     */
    public Map<String, String> getExtData() {
        return extData;
    }

    /**
     * 扩展字段
     */
    public void setExtData(final Map<String, String> extData) {
        this.extData = extData;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.eId;
            case 1:
                return this.lang;
            case 2:
                return this.basecondition;
            case 3:
                return this.page;
            case 4:
                return this.queryBus;
            case 5:
                return this.travelStep;
            case 6:
                return this.queryColumn;
            case 7:
                return this.productType;
            case 8:
                return this.extData;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.eId = (String) fieldValue;
                break;
            case 1:
                this.lang = (String) fieldValue;
                break;
            case 2:
                this.basecondition = (BaseQueryCondition) fieldValue;
                break;
            case 3:
                this.page = (Pager) fieldValue;
                break;
            case 4:
                this.queryBus = (List<QueryReportBuTypeEnum>) fieldValue;
                break;
            case 5:
                this.travelStep = (TravelPositionStepEnum) fieldValue;
                break;
            case 6:
                this.queryColumn = (List<TravelPositionDetail>) fieldValue;
                break;
            case 7:
                this.productType = (String) fieldValue;
                break;
            case 8:
                this.extData = (Map<String, String>) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TravelPositionDetailRequest other = (TravelPositionDetailRequest) obj;
        return
                Objects.equal(this.eId, other.eId) &&
                        Objects.equal(this.lang, other.lang) &&
                        Objects.equal(this.basecondition, other.basecondition) &&
                        Objects.equal(this.page, other.page) &&
                        Objects.equal(this.queryBus, other.queryBus) &&
                        Objects.equal(this.travelStep, other.travelStep) &&
                        Objects.equal(this.queryColumn, other.queryColumn) &&
                        Objects.equal(this.productType, other.productType) &&
                        Objects.equal(this.extData, other.extData);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.eId == null ? 0 : this.eId.hashCode());
        result = 31 * result + (this.lang == null ? 0 : this.lang.hashCode());
        result = 31 * result + (this.basecondition == null ? 0 : this.basecondition.hashCode());
        result = 31 * result + (this.page == null ? 0 : this.page.hashCode());
        result = 31 * result + (this.queryBus == null ? 0 : this.queryBus.hashCode());
        result = 31 * result + (this.travelStep == null ? 0 : this.travelStep.hashCode());
        result = 31 * result + (this.queryColumn == null ? 0 : this.queryColumn.hashCode());
        result = 31 * result + (this.productType == null ? 0 : this.productType.hashCode());
        result = 31 * result + (this.extData == null ? 0 : this.extData.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("eId", eId)
                .add("lang", lang)
                .add("basecondition", basecondition)
                .add("page", page)
                .add("queryBus", queryBus)
                .add("travelStep", travelStep)
                .add("queryColumn", queryColumn)
                .add("productType", productType)
                .add("extData", extData)
                .toString();
    }
}
