package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.corpgovernment.resource.schedule.domain.onlinereport.commonenum.TravelPositionDetailQueryColnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "queryKey",
        "queryValue"
})
public class TravelPositionDetail implements Serializable {
    private static final long serialVersionUID = 1L;


    public TravelPositionDetail(
            TravelPositionDetailQueryColnum queryKey,
            String queryValue) {
        this.queryKey = queryKey;
        this.queryValue = queryValue;
    }

    public TravelPositionDetail() {
    }

    @JsonProperty("queryKey")
    public TravelPositionDetailQueryColnum queryKey;

    @JsonProperty("queryValue")
    public String queryValue;

    public TravelPositionDetailQueryColnum getQueryKey() {
        return queryKey;
    }

    public void setQueryKey(final TravelPositionDetailQueryColnum queryKey) {
        this.queryKey = queryKey;
    }

    public String getQueryValue() {
        return queryValue;
    }

    public void setQueryValue(final String queryValue) {
        this.queryValue = queryValue;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final TravelPositionDetail other = (TravelPositionDetail) obj;
        return
                Objects.equal(this.queryKey, other.queryKey) &&
                        Objects.equal(this.queryValue, other.queryValue);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.queryKey == null ? 0 : this.queryKey.hashCode());
        result = 31 * result + (this.queryValue == null ? 0 : this.queryValue.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("queryKey", queryKey)
                .add("queryValue", queryValue)
                .toString();
    }
}
