package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用车部门消费
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "dimId",
        "dim",
        "totalAmount",
        "totalAmountAirportpickDom",
        "totalAmountAirportpickInter",
        "totalAmountCharter",
        "totalAmountRent",
        "totalAmountTax",
        "totalOrderCount"
})
public class OnlineReportCarTopDeptConsume implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    @JsonProperty("dimId")
    public String dimId;
    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    @JsonProperty("dim")
    public String dim;
    /**
     * 消费金额
     */
    @JsonProperty("totalAmount")
    public BigDecimal totalAmount;
    /**
     * 国内接送机消费金额
     */
    @JsonProperty("totalAmountAirportpickDom")
    public BigDecimal totalAmountAirportpickDom;
    /**
     * 国际接送机消费金额
     */
    @JsonProperty("totalAmountAirportpickInter")
    public BigDecimal totalAmountAirportpickInter;
    /**
     * 包车消费金额
     */
    @JsonProperty("totalAmountCharter")
    public BigDecimal totalAmountCharter;
    /**
     * 租车消费金额
     */
    @JsonProperty("totalAmountRent")
    public BigDecimal totalAmountRent;
    /**
     * 打车消费金额
     */
    @JsonProperty("totalAmountTax")
    public BigDecimal totalAmountTax;
    /**
     * 订单数
     */
    @JsonProperty("totalOrderCount")
    public Integer totalOrderCount;
    /**
     * 消费金额占比
     */
    @JsonProperty("amountPercent")
    public BigDecimal amountPercent;
    /**
     * 碳排
     */
    @JsonProperty("totalCarbons")
    public BigDecimal totalCarbons;
    /**
     * 里程碳排
     */
    @JsonProperty("avgTpmsCarbons")
    public BigDecimal avgTpmsCarbons;
    /**
     * 里程均价
     */
    @JsonProperty("avgTpmsPrice")
    public BigDecimal avgTpmsPrice;

    public OnlineReportCarTopDeptConsume(
            String dimId,
            String dim,
            BigDecimal totalAmount,
            BigDecimal totalAmountAirportpickDom,
            BigDecimal totalAmountAirportpickInter,
            BigDecimal totalAmountCharter,
            BigDecimal totalAmountRent,
            BigDecimal totalAmountTax,
            Integer totalOrderCount,
            BigDecimal amountPercent,
            BigDecimal totalCarbons,
            BigDecimal avgTpmsCarbons,
            BigDecimal avgTpmsPrice) {
        this.dimId = dimId;
        this.dim = dim;
        this.totalAmount = totalAmount;
        this.totalAmountAirportpickDom = totalAmountAirportpickDom;
        this.totalAmountAirportpickInter = totalAmountAirportpickInter;
        this.totalAmountCharter = totalAmountCharter;
        this.totalAmountRent = totalAmountRent;
        this.totalAmountTax = totalAmountTax;
        this.totalOrderCount = totalOrderCount;
        this.amountPercent = amountPercent;
        this.totalCarbons = totalCarbons;
        this.avgTpmsCarbons = avgTpmsCarbons;
        this.avgTpmsPrice = avgTpmsPrice;
    }

    public OnlineReportCarTopDeptConsume() {
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public String getDimId() {
        return dimId;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public void setDimId(final String dimId) {
        this.dimId = dimId;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public String getDim() {
        return dim;
    }

    /**
     * 分析对象：公司|主账户|成本中心|部门
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 消费金额
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * 消费金额
     */
    public void setTotalAmount(final BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * 国内接送机消费金额
     */
    public BigDecimal getTotalAmountAirportpickDom() {
        return totalAmountAirportpickDom;
    }

    /**
     * 国内接送机消费金额
     */
    public void setTotalAmountAirportpickDom(final BigDecimal totalAmountAirportpickDom) {
        this.totalAmountAirportpickDom = totalAmountAirportpickDom;
    }

    /**
     * 国际接送机消费金额
     */
    public BigDecimal getTotalAmountAirportpickInter() {
        return totalAmountAirportpickInter;
    }

    /**
     * 国际接送机消费金额
     */
    public void setTotalAmountAirportpickInter(final BigDecimal totalAmountAirportpickInter) {
        this.totalAmountAirportpickInter = totalAmountAirportpickInter;
    }

    /**
     * 包车消费金额
     */
    public BigDecimal getTotalAmountCharter() {
        return totalAmountCharter;
    }

    /**
     * 包车消费金额
     */
    public void setTotalAmountCharter(final BigDecimal totalAmountCharter) {
        this.totalAmountCharter = totalAmountCharter;
    }

    /**
     * 租车消费金额
     */
    public BigDecimal getTotalAmountRent() {
        return totalAmountRent;
    }

    /**
     * 租车消费金额
     */
    public void setTotalAmountRent(final BigDecimal totalAmountRent) {
        this.totalAmountRent = totalAmountRent;
    }

    /**
     * 打车消费金额
     */
    public BigDecimal getTotalAmountTax() {
        return totalAmountTax;
    }

    /**
     * 打车消费金额
     */
    public void setTotalAmountTax(final BigDecimal totalAmountTax) {
        this.totalAmountTax = totalAmountTax;
    }

    /**
     * 订单数
     */
    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }

    /**
     * 订单数
     */
    public void setTotalOrderCount(final Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }

    /**
     * 消费金额占比
     */
    public BigDecimal getAmountPercent() {
        return amountPercent;
    }

    /**
     * 消费金额占比
     */
    public void setAmountPercent(final BigDecimal amountPercent) {
        this.amountPercent = amountPercent;
    }

    /**
     * 碳排
     */
    public BigDecimal getTotalCarbons() {
        return totalCarbons;
    }

    /**
     * 碳排
     */
    public void setTotalCarbons(final BigDecimal totalCarbons) {
        this.totalCarbons = totalCarbons;
    }

    /**
     * 里程碳排
     */
    public BigDecimal getAvgTpmsCarbons() {
        return avgTpmsCarbons;
    }

    /**
     * 里程碳排
     */
    public void setAvgTpmsCarbons(final BigDecimal avgTpmsCarbons) {
        this.avgTpmsCarbons = avgTpmsCarbons;
    }

    /**
     * 里程均价
     */
    public BigDecimal getAvgTpmsPrice() {
        return avgTpmsPrice;
    }

    /**
     * 里程均价
     */
    public void setAvgTpmsPrice(final BigDecimal avgTpmsPrice) {
        this.avgTpmsPrice = avgTpmsPrice;
    }

    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            case 0:
                return this.dimId;
            case 1:
                return this.dim;
            case 2:
                return this.totalAmount;
            case 3:
                return this.totalAmountAirportpickDom;
            case 4:
                return this.totalAmountAirportpickInter;
            case 5:
                return this.totalAmountCharter;
            case 6:
                return this.totalAmountRent;
            case 7:
                return this.totalAmountTax;
            case 8:
                return this.totalOrderCount;
            case 9:
                return this.amountPercent;
            case 10:
                return this.totalCarbons;
            case 11:
                return this.avgTpmsCarbons;
            case 12:
                return this.avgTpmsPrice;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            case 0:
                this.dimId = (String) fieldValue;
                break;
            case 1:
                this.dim = (String) fieldValue;
                break;
            case 2:
                this.totalAmount = (BigDecimal) fieldValue;
                break;
            case 3:
                this.totalAmountAirportpickDom = (BigDecimal) fieldValue;
                break;
            case 4:
                this.totalAmountAirportpickInter = (BigDecimal) fieldValue;
                break;
            case 5:
                this.totalAmountCharter = (BigDecimal) fieldValue;
                break;
            case 6:
                this.totalAmountRent = (BigDecimal) fieldValue;
                break;
            case 7:
                this.totalAmountTax = (BigDecimal) fieldValue;
                break;
            case 8:
                this.totalOrderCount = (Integer) fieldValue;
                break;
            case 9:
                this.amountPercent = (BigDecimal) fieldValue;
                break;
            case 10:
                this.totalCarbons = (BigDecimal) fieldValue;
                break;
            case 11:
                this.avgTpmsCarbons = (BigDecimal) fieldValue;
                break;
            case 12:
                this.avgTpmsPrice = (BigDecimal) fieldValue;
                break;
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportCarTopDeptConsume other = (OnlineReportCarTopDeptConsume) obj;
        return
                Objects.equal(this.dimId, other.dimId) &&
                        Objects.equal(this.dim, other.dim) &&
                        Objects.equal(this.totalAmount, other.totalAmount) &&
                        Objects.equal(this.totalAmountAirportpickDom, other.totalAmountAirportpickDom) &&
                        Objects.equal(this.totalAmountAirportpickInter, other.totalAmountAirportpickInter) &&
                        Objects.equal(this.totalAmountCharter, other.totalAmountCharter) &&
                        Objects.equal(this.totalAmountRent, other.totalAmountRent) &&
                        Objects.equal(this.totalAmountTax, other.totalAmountTax) &&
                        Objects.equal(this.totalOrderCount, other.totalOrderCount) &&
                        Objects.equal(this.amountPercent, other.amountPercent) &&
                        Objects.equal(this.totalCarbons, other.totalCarbons) &&
                        Objects.equal(this.avgTpmsCarbons, other.avgTpmsCarbons) &&
                        Objects.equal(this.avgTpmsPrice, other.avgTpmsPrice);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dimId == null ? 0 : this.dimId.hashCode());
        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.totalAmount == null ? 0 : this.totalAmount.hashCode());
        result = 31 * result + (this.totalAmountAirportpickDom == null ? 0 : this.totalAmountAirportpickDom.hashCode());
        result = 31 * result + (this.totalAmountAirportpickInter == null ? 0 : this.totalAmountAirportpickInter.hashCode());
        result = 31 * result + (this.totalAmountCharter == null ? 0 : this.totalAmountCharter.hashCode());
        result = 31 * result + (this.totalAmountRent == null ? 0 : this.totalAmountRent.hashCode());
        result = 31 * result + (this.totalAmountTax == null ? 0 : this.totalAmountTax.hashCode());
        result = 31 * result + (this.totalOrderCount == null ? 0 : this.totalOrderCount.hashCode());
        result = 31 * result + (this.amountPercent == null ? 0 : this.amountPercent.hashCode());
        result = 31 * result + (this.totalCarbons == null ? 0 : this.totalCarbons.hashCode());
        result = 31 * result + (this.avgTpmsCarbons == null ? 0 : this.avgTpmsCarbons.hashCode());
        result = 31 * result + (this.avgTpmsPrice == null ? 0 : this.avgTpmsPrice.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("dimId", dimId)
                .add("dim", dim)
                .add("totalAmount", totalAmount)
                .add("totalAmountAirportpickDom", totalAmountAirportpickDom)
                .add("totalAmountAirportpickInter", totalAmountAirportpickInter)
                .add("totalAmountCharter", totalAmountCharter)
                .add("totalAmountRent", totalAmountRent)
                .add("totalAmountTax", totalAmountTax)
                .add("totalOrderCount", totalOrderCount)
                .add("amountPercent", amountPercent)
                .add("totalCarbons", totalCarbons)
                .add("avgTpmsCarbons", avgTpmsCarbons)
                .add("avgTpmsPrice", avgTpmsPrice)
                .toString();
    }
}
