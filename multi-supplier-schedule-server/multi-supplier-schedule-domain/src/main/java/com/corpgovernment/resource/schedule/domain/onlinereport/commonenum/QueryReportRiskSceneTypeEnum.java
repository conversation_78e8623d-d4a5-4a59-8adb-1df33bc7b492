package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;

/**
 * 风险场景
 */
public enum QueryReportRiskSceneTypeEnum {

    /**
     * 机票场景1 - 退款至个人
     */
    FLT_REFUND(0),

    /**
     * 酒店场景1 - 协同套现
     */
    HTL_CASH_OUT(1),

    /**
     * 用车场景1 - 重复下单
     */
    CAR_DOUBLE_BOOK(2),

    /**
     * 酒店场景2 - 提前离店
     */
    HTL_UNDER_STAY(3);

    private final int value;

    QueryReportRiskSceneTypeEnum(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public int getValue() {
        return value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public static QueryReportRiskSceneTypeEnum findByValue(int value) {
        switch (value) {
            case 0:
                return FLT_REFUND;
            case 1:
                return HTL_CASH_OUT;
            case 2:
                return CAR_DOUBLE_BOOK;
            case 3:
                return HTL_UNDER_STAY;
            default:
                return null;
        }
    }
}
