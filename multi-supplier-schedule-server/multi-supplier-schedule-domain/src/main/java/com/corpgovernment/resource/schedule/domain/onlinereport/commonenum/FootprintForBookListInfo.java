package com.corpgovernment.resource.schedule.domain.onlinereport.commonenum;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "tripdaysWeekend",
    "mintime",
    "maxtime",
    "longtripDays",
    "longtripTpm",
    "mostVisitCity",
    "carbons",
    "mintimeCity",
    "mintimeDate",
    "maxtimeCity",
    "maxtimeDate",
    "longtripDcity",
    "longtripAcity",
    "longtripDdate",
    "longtripAdate"
})
public class FootprintForBookListInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public FootprintForBookListInfo(
        Integer tripdaysWeekend,
        String mintime,
        String maxtime,
        Integer longtripDays,
        BigDecimal longtripTpm,
        String mostVisitCity,
        BigDecimal carbons,
        String mintimeCity,
        String mintimeDate,
        String maxtimeCity,
        String maxtimeDate,
        String longtripDcity,
        String longtripAcity,
        String longtripDdate,
        String longtripAdate) {
        this.tripdaysWeekend = tripdaysWeekend;
        this.mintime = mintime;
        this.maxtime = maxtime;
        this.longtripDays = longtripDays;
        this.longtripTpm = longtripTpm;
        this.mostVisitCity = mostVisitCity;
        this.carbons = carbons;
        this.mintimeCity = mintimeCity;
        this.mintimeDate = mintimeDate;
        this.maxtimeCity = maxtimeCity;
        this.maxtimeDate = maxtimeDate;
        this.longtripDcity = longtripDcity;
        this.longtripAcity = longtripAcity;
        this.longtripDdate = longtripDdate;
        this.longtripAdate = longtripAdate;
    }

    public FootprintForBookListInfo() {
    }

    /**
     * 周末出差天数
     */
    @JsonProperty("tripdaysWeekend")
    public Integer tripdaysWeekend;

    /**
     * 最早出行时间
     */
    @JsonProperty("mintime")
    public String mintime;

    /**
     * 最晚出行时间
     */
    @JsonProperty("maxtime")
    public String maxtime;

    /**
     * 最长行程天数
     */
    @JsonProperty("longtripDays")
    public Integer longtripDays;

    /**
     * 最长行程公里数
     */
    @JsonProperty("longtripTpm")
    public BigDecimal longtripTpm;

    /**
     * 最常访问城市
     */
    @JsonProperty("mostVisitCity")
    public String mostVisitCity;

    /**
     * 碳排放量
     */
    @JsonProperty("carbons")
    public BigDecimal carbons;

    /**
     * *
     * * 最早出行时间所在行程
     */
    @JsonProperty("mintimeCity")
    public String mintimeCity;

    /**
     * *
     * * 最早出行时间所在行程日期
     */
    @JsonProperty("mintimeDate")
    public String mintimeDate;

    /**
     * *
     * * 最晚出行时间所在行程
     */
    @JsonProperty("maxtimeCity")
    public String maxtimeCity;

    /**
     * *
     * * 最晚出行时间所在行程日期
     */
    @JsonProperty("maxtimeDate")
    public String maxtimeDate;

    /**
     * *
     * * 最长行程起点
     */
    @JsonProperty("longtripDcity")
    public String longtripDcity;

    /**
     * *
     * * 最长行程终点
     */
    @JsonProperty("longtripAcity")
    public String longtripAcity;

    /**
     * *
     * * 最长行程开始日期
     */
    @JsonProperty("longtripDdate")
    public String longtripDdate;

    /**
     * *
     * * 最长行程结束日期
     */
    @JsonProperty("longtripAdate")
    public String longtripAdate;

    /**
     * 周末出差天数
     */
    public Integer getTripdaysWeekend() {
        return tripdaysWeekend;
    }

    /**
     * 周末出差天数
     */
    public void setTripdaysWeekend(final Integer tripdaysWeekend) {
        this.tripdaysWeekend = tripdaysWeekend;
    }

    /**
     * 最早出行时间
     */
    public String getMintime() {
        return mintime;
    }

    /**
     * 最早出行时间
     */
    public void setMintime(final String mintime) {
        this.mintime = mintime;
    }

    /**
     * 最晚出行时间
     */
    public String getMaxtime() {
        return maxtime;
    }

    /**
     * 最晚出行时间
     */
    public void setMaxtime(final String maxtime) {
        this.maxtime = maxtime;
    }

    /**
     * 最长行程天数
     */
    public Integer getLongtripDays() {
        return longtripDays;
    }

    /**
     * 最长行程天数
     */
    public void setLongtripDays(final Integer longtripDays) {
        this.longtripDays = longtripDays;
    }

    /**
     * 最长行程公里数
     */
    public BigDecimal getLongtripTpm() {
        return longtripTpm;
    }

    /**
     * 最长行程公里数
     */
    public void setLongtripTpm(final BigDecimal longtripTpm) {
        this.longtripTpm = longtripTpm;
    }

    /**
     * 最常访问城市
     */
    public String getMostVisitCity() {
        return mostVisitCity;
    }

    /**
     * 最常访问城市
     */
    public void setMostVisitCity(final String mostVisitCity) {
        this.mostVisitCity = mostVisitCity;
    }

    /**
     * 碳排放量
     */
    public BigDecimal getCarbons() {
        return carbons;
    }

    /**
     * 碳排放量
     */
    public void setCarbons(final BigDecimal carbons) {
        this.carbons = carbons;
    }

    /**
     * *
     * * 最早出行时间所在行程
     */
    public String getMintimeCity() {
        return mintimeCity;
    }

    /**
     * *
     * * 最早出行时间所在行程
     */
    public void setMintimeCity(final String mintimeCity) {
        this.mintimeCity = mintimeCity;
    }

    /**
     * *
     * * 最早出行时间所在行程日期
     */
    public String getMintimeDate() {
        return mintimeDate;
    }

    /**
     * *
     * * 最早出行时间所在行程日期
     */
    public void setMintimeDate(final String mintimeDate) {
        this.mintimeDate = mintimeDate;
    }

    /**
     * *
     * * 最晚出行时间所在行程
     */
    public String getMaxtimeCity() {
        return maxtimeCity;
    }

    /**
     * *
     * * 最晚出行时间所在行程
     */
    public void setMaxtimeCity(final String maxtimeCity) {
        this.maxtimeCity = maxtimeCity;
    }

    /**
     * *
     * * 最晚出行时间所在行程日期
     */
    public String getMaxtimeDate() {
        return maxtimeDate;
    }

    /**
     * *
     * * 最晚出行时间所在行程日期
     */
    public void setMaxtimeDate(final String maxtimeDate) {
        this.maxtimeDate = maxtimeDate;
    }

    /**
     * *
     * * 最长行程起点
     */
    public String getLongtripDcity() {
        return longtripDcity;
    }

    /**
     * *
     * * 最长行程起点
     */
    public void setLongtripDcity(final String longtripDcity) {
        this.longtripDcity = longtripDcity;
    }

    /**
     * *
     * * 最长行程终点
     */
    public String getLongtripAcity() {
        return longtripAcity;
    }

    /**
     * *
     * * 最长行程终点
     */
    public void setLongtripAcity(final String longtripAcity) {
        this.longtripAcity = longtripAcity;
    }

    /**
     * *
     * * 最长行程开始日期
     */
    public String getLongtripDdate() {
        return longtripDdate;
    }

    /**
     * *
     * * 最长行程开始日期
     */
    public void setLongtripDdate(final String longtripDdate) {
        this.longtripDdate = longtripDdate;
    }

    /**
     * *
     * * 最长行程结束日期
     */
    public String getLongtripAdate() {
        return longtripAdate;
    }

    /**
     * *
     * * 最长行程结束日期
     */
    public void setLongtripAdate(final String longtripAdate) {
        this.longtripAdate = longtripAdate;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FootprintForBookListInfo other = (FootprintForBookListInfo)obj;
        return
            Objects.equal(this.tripdaysWeekend, other.tripdaysWeekend) &&
            Objects.equal(this.mintime, other.mintime) &&
            Objects.equal(this.maxtime, other.maxtime) &&
            Objects.equal(this.longtripDays, other.longtripDays) &&
            Objects.equal(this.longtripTpm, other.longtripTpm) &&
            Objects.equal(this.mostVisitCity, other.mostVisitCity) &&
            Objects.equal(this.carbons, other.carbons) &&
            Objects.equal(this.mintimeCity, other.mintimeCity) &&
            Objects.equal(this.mintimeDate, other.mintimeDate) &&
            Objects.equal(this.maxtimeCity, other.maxtimeCity) &&
            Objects.equal(this.maxtimeDate, other.maxtimeDate) &&
            Objects.equal(this.longtripDcity, other.longtripDcity) &&
            Objects.equal(this.longtripAcity, other.longtripAcity) &&
            Objects.equal(this.longtripDdate, other.longtripDdate) &&
            Objects.equal(this.longtripAdate, other.longtripAdate);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.tripdaysWeekend == null ? 0 : this.tripdaysWeekend.hashCode());
        result = 31 * result + (this.mintime == null ? 0 : this.mintime.hashCode());
        result = 31 * result + (this.maxtime == null ? 0 : this.maxtime.hashCode());
        result = 31 * result + (this.longtripDays == null ? 0 : this.longtripDays.hashCode());
        result = 31 * result + (this.longtripTpm == null ? 0 : this.longtripTpm.hashCode());
        result = 31 * result + (this.mostVisitCity == null ? 0 : this.mostVisitCity.hashCode());
        result = 31 * result + (this.carbons == null ? 0 : this.carbons.hashCode());
        result = 31 * result + (this.mintimeCity == null ? 0 : this.mintimeCity.hashCode());
        result = 31 * result + (this.mintimeDate == null ? 0 : this.mintimeDate.hashCode());
        result = 31 * result + (this.maxtimeCity == null ? 0 : this.maxtimeCity.hashCode());
        result = 31 * result + (this.maxtimeDate == null ? 0 : this.maxtimeDate.hashCode());
        result = 31 * result + (this.longtripDcity == null ? 0 : this.longtripDcity.hashCode());
        result = 31 * result + (this.longtripAcity == null ? 0 : this.longtripAcity.hashCode());
        result = 31 * result + (this.longtripDdate == null ? 0 : this.longtripDdate.hashCode());
        result = 31 * result + (this.longtripAdate == null ? 0 : this.longtripAdate.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("tripdaysWeekend", tripdaysWeekend)
            .add("mintime", mintime)
            .add("maxtime", maxtime)
            .add("longtripDays", longtripDays)
            .add("longtripTpm", longtripTpm)
            .add("mostVisitCity", mostVisitCity)
            .add("carbons", carbons)
            .add("mintimeCity", mintimeCity)
            .add("mintimeDate", mintimeDate)
            .add("maxtimeCity", maxtimeCity)
            .add("maxtimeDate", maxtimeDate)
            .add("longtripDcity", longtripDcity)
            .add("longtripAcity", longtripAcity)
            .add("longtripDdate", longtripDdate)
            .add("longtripAdate", longtripAdate)
            .toString();
    }
}
