package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import  java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.Map;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "axis",
    "data"
})
public class OnlineReportTrendPoint implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportTrendPoint(
        String axis,
        Map<String,BigDecimal> data) {
        this.axis = axis;
        this.data = data;
    }

    public OnlineReportTrendPoint() {
    }

    @JsonProperty("axis")
    public String axis;

    /**
     * 数据
     */
    @JsonProperty("data")
    public Map<String,BigDecimal> data;

    public String getAxis() {
        return axis;
    }

    public void setAxis(final String axis) {
        this.axis = axis;
    }

    /**
     * 数据
     */
    public Map<String,BigDecimal> getData() {
        return data;
    }

    /**
     * 数据
     */
    public void setData(final Map<String,BigDecimal> data) {
        this.data = data;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportTrendPoint other = (OnlineReportTrendPoint)obj;
        return
            Objects.equal(this.axis, other.axis) &&
            Objects.equal(this.data, other.data);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.axis == null ? 0 : this.axis.hashCode());
        result = 31 * result + (this.data == null ? 0 : this.data.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("axis", axis)
            .add("data", data)
            .toString();
    }
}
