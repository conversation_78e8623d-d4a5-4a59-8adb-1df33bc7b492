package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Rc概览
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
        "rcTimes",
        "lossPrice",
        "rcPercent"
})
public class RcView implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * rc次数
     */
    @JsonProperty("rcTimes")
    public Integer rcTimes;
    /**
     * 超标/损失金额
     */
    @JsonProperty("lossPrice")
    public BigDecimal lossPrice;
    /**
     * rc占比
     */
    @JsonProperty("rcPercent")
    public BigDecimal rcPercent;

    public RcView(
            Integer rcTimes,
            BigDecimal lossPrice,
            BigDecimal rcPercent) {
        this.rcTimes = rcTimes;
        this.lossPrice = lossPrice;
        this.rcPercent = rcPercent;
    }

    public RcView() {
    }

    /**
     * rc次数
     */
    public Integer getRcTimes() {
        return rcTimes;
    }

    /**
     * rc次数
     */
    public void setRcTimes(final Integer rcTimes) {
        this.rcTimes = rcTimes;
    }

    /**
     * 超标/损失金额
     */
    public BigDecimal getLossPrice() {
        return lossPrice;
    }

    /**
     * 超标/损失金额
     */
    public void setLossPrice(final BigDecimal lossPrice) {
        this.lossPrice = lossPrice;
    }

    /**
     * rc占比
     */
    public BigDecimal getRcPercent() {
        return rcPercent;
    }

    /**
     * rc占比
     */
    public void setRcPercent(final BigDecimal rcPercent) {
        this.rcPercent = rcPercent;
    }

    // Used by DatumWriter. Applications should not call

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final RcView other = (RcView) obj;
        return
                Objects.equal(this.rcTimes, other.rcTimes) &&
                        Objects.equal(this.lossPrice, other.lossPrice) &&
                        Objects.equal(this.rcPercent, other.rcPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.rcTimes == null ? 0 : this.rcTimes.hashCode());
        result = 31 * result + (this.lossPrice == null ? 0 : this.lossPrice.hashCode());
        result = 31 * result + (this.rcPercent == null ? 0 : this.rcPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("rcTimes", rcTimes)
                .add("lossPrice", lossPrice)
                .add("rcPercent", rcPercent)
                .toString();
    }
}
