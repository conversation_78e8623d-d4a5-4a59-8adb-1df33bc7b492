package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity;


import java.util.List;

/**
 * Auther:abguo
 * Date:2019/7/25
 * Description:
 * Project:onlinereportweb
 */
public class SendMessageBO {

    /**
     * 卡号
     *
     */
    private String uID;


    private String oper;

    private String sessionId;

    private String uniqueId;

    /**
     * 发送地址
     *
     */
    private String sender;


    private String senderName;

    /**
     * 收件地址
     *
     */
    private List recipient;

    /**
     * 抄送人地址
     */
    private List cCRecipient;

    /**
     * 邮件主题
     *
     */
    private String subject;

    /**
     * 邮件模板
     */
    private int templateID;

    /**
     *发送代码
     */
    private String sendCode;

    private String serviceFrom;

    /**
     * 消息内容
     *
     */
    private String content;

    private String messageType;

    private String clientType;

    /**
     * 附件
     *
     */
    private List<AttachmentInfo> attachmentList;

    /**
     * 编码
     */
    private String charset;

    private String corpId;

    public String getuID() {
        return uID;
    }

    public void setuID(String uID) {
        this.uID = uID;
    }

    public String getOper() {
        return oper;
    }

    public void setOper(String oper) {
        this.oper = oper;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public List getRecipient() {
        return recipient;
    }

    public void setRecipient(List recipient) {
        this.recipient = recipient;
    }

    public List getcCRecipient() {
        return cCRecipient;
    }

    public void setcCRecipient(List cCRecipient) {
        this.cCRecipient = cCRecipient;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public int getTemplateID() {
        return templateID;
    }

    public void setTemplateID(int templateID) {
        this.templateID = templateID;
    }

    public String getSendCode() {
        return sendCode;
    }

    public void setSendCode(String sendCode) {
        this.sendCode = sendCode;
    }

    public String getServiceFrom() {
        return serviceFrom;
    }

    public void setServiceFrom(String serviceFrom) {
        this.serviceFrom = serviceFrom;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<AttachmentInfo> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<AttachmentInfo> attachmentList) {
        this.attachmentList = attachmentList;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }
}
