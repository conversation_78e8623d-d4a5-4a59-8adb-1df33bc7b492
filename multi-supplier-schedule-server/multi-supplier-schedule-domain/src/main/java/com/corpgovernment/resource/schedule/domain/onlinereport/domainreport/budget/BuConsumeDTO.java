package com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.budget;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2021/11/8 18:57
 * @Desc
 */
@Data
public class BuConsumeDTO {

    @Column(name = "dim")
    @Type(value = Types.VARCHAR)
    private String dim;

    @Column(name = "totalFltAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalFlightAmount;

    @Column(name = "totalHtlAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalHotelAmount;

    @Column(name = "totalTrainAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalTrainAmount;

    @Column(name = "totalCarAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCarAmount;

    @Column(name = "totalBusAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalBusAmount;

    @Column(name = "totalVasAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalVasAmount;
}
