package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;




import java.util.List;

/**
 * @Description: 机票航线BO
 * <AUTHOR>
 * @Date 2019/5/29
 */
public class FlightRoutesReportBO {
    private List<FlightRoutesReportDataBO> flightRoutesReportDataBOList;
    private List<FlightRoutesReportSumDataBO> flightRoutesReportSumDataBOList;

    public List<FlightRoutesReportDataBO> getFlightRoutesReportDataBOList() {
        return flightRoutesReportDataBOList;
    }

    public void setFlightRoutesReportDataBOList(List<FlightRoutesReportDataBO> flightRoutesReportDataBOList) {
        this.flightRoutesReportDataBOList = flightRoutesReportDataBOList;
    }

    public List<FlightRoutesReportSumDataBO> getFlightRoutesReportSumDataBOList() {
        return flightRoutesReportSumDataBOList;
    }

    public void setFlightRoutesReportSumDataBOList(List<FlightRoutesReportSumDataBO> flightRoutesReportSumDataBOList) {
        this.flightRoutesReportSumDataBOList = flightRoutesReportSumDataBOList;
    }
}
