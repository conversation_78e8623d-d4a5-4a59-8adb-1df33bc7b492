package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机票行为分析
 */
@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "dim",
    "totalQuantity",
    "quantityPercent",
    "totalPrice",
    "pricePercent",
    "avgPrice",
    "avgTpmsPrice",
    "avgDiscount",
    "avgPreOrderDate",
    "totalRefundQuantity",
    "refundQuantityPercent",
    "totalRebookQuantity",
    "rebookQuantityPercent"
})
public class FlightBehaviorInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public FlightBehaviorInfo(
        String dim,
        Integer totalQuantity,
        Double quantityPercent,
        BigDecimal totalPrice,
        Double pricePercent,
        BigDecimal avgPrice,
        BigDecimal avgTpmsPrice,
        BigDecimal avgDiscount,
        Integer avgPreOrderDate,
        Integer totalRefundQuantity,
        Double refundQuantityPercent,
        Integer totalRebookQuantity,
        Double rebookQuantityPercent) {
        this.dim = dim;
        this.totalQuantity = totalQuantity;
        this.quantityPercent = quantityPercent;
        this.totalPrice = totalPrice;
        this.pricePercent = pricePercent;
        this.avgPrice = avgPrice;
        this.avgTpmsPrice = avgTpmsPrice;
        this.avgDiscount = avgDiscount;
        this.avgPreOrderDate = avgPreOrderDate;
        this.totalRefundQuantity = totalRefundQuantity;
        this.refundQuantityPercent = refundQuantityPercent;
        this.totalRebookQuantity = totalRebookQuantity;
        this.rebookQuantityPercent = rebookQuantityPercent;
    }

    public FlightBehaviorInfo() {
    }

    /**
     * 维度
     */
    @JsonProperty("dim")
    public String dim;

    /**
     * 票张
     */
    @JsonProperty("totalQuantity")
    public Integer totalQuantity;

    /**
     * 张数占比
     */
    @JsonProperty("quantityPercent")
    public Double quantityPercent;

    /**
     * 成交净价
     */
    @JsonProperty("totalPrice")
    public BigDecimal totalPrice;

    /**
     * 成交净价占比
     */
    @JsonProperty("pricePercent")
    public Double pricePercent;

    /**
     * 均价
     */
    @JsonProperty("avgPrice")
    public BigDecimal avgPrice;

    /**
     * 里程均价
     */
    @JsonProperty("avgTpmsPrice")
    public BigDecimal avgTpmsPrice;

    /**
     * 平均折扣
     */
    @JsonProperty("avgDiscount")
    public BigDecimal avgDiscount;

    /**
     * 平均提前预预订天数
     */
    @JsonProperty("avgPreOrderDate")
    public Integer avgPreOrderDate;

    /**
     * 退票张数
     */
    @JsonProperty("totalRefundQuantity")
    public Integer totalRefundQuantity;

    /**
     * 退票占比
     */
    @JsonProperty("refundQuantityPercent")
    public Double refundQuantityPercent;

    /**
     * 改签张数
     */
    @JsonProperty("totalRebookQuantity")
    public Integer totalRebookQuantity;

    /**
     * 改签占比
     */
    @JsonProperty("rebookQuantityPercent")
    public Double rebookQuantityPercent;

    /**
     * 维度
     */
    public String getDim() {
        return dim;
    }

    /**
     * 维度
     */
    public void setDim(final String dim) {
        this.dim = dim;
    }

    /**
     * 票张
     */
    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    /**
     * 票张
     */
    public void setTotalQuantity(final Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    /**
     * 张数占比
     */
    public Double getQuantityPercent() {
        return quantityPercent;
    }

    /**
     * 张数占比
     */
    public void setQuantityPercent(final Double quantityPercent) {
        this.quantityPercent = quantityPercent;
    }

    /**
     * 成交净价
     */
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    /**
     * 成交净价
     */
    public void setTotalPrice(final BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    /**
     * 成交净价占比
     */
    public Double getPricePercent() {
        return pricePercent;
    }

    /**
     * 成交净价占比
     */
    public void setPricePercent(final Double pricePercent) {
        this.pricePercent = pricePercent;
    }

    /**
     * 均价
     */
    public BigDecimal getAvgPrice() {
        return avgPrice;
    }

    /**
     * 均价
     */
    public void setAvgPrice(final BigDecimal avgPrice) {
        this.avgPrice = avgPrice;
    }

    /**
     * 里程均价
     */
    public BigDecimal getAvgTpmsPrice() {
        return avgTpmsPrice;
    }

    /**
     * 里程均价
     */
    public void setAvgTpmsPrice(final BigDecimal avgTpmsPrice) {
        this.avgTpmsPrice = avgTpmsPrice;
    }

    /**
     * 平均折扣
     */
    public BigDecimal getAvgDiscount() {
        return avgDiscount;
    }

    /**
     * 平均折扣
     */
    public void setAvgDiscount(final BigDecimal avgDiscount) {
        this.avgDiscount = avgDiscount;
    }

    /**
     * 平均提前预预订天数
     */
    public Integer getAvgPreOrderDate() {
        return avgPreOrderDate;
    }

    /**
     * 平均提前预预订天数
     */
    public void setAvgPreOrderDate(final Integer avgPreOrderDate) {
        this.avgPreOrderDate = avgPreOrderDate;
    }

    /**
     * 退票张数
     */
    public Integer getTotalRefundQuantity() {
        return totalRefundQuantity;
    }

    /**
     * 退票张数
     */
    public void setTotalRefundQuantity(final Integer totalRefundQuantity) {
        this.totalRefundQuantity = totalRefundQuantity;
    }

    /**
     * 退票占比
     */
    public Double getRefundQuantityPercent() {
        return refundQuantityPercent;
    }

    /**
     * 退票占比
     */
    public void setRefundQuantityPercent(final Double refundQuantityPercent) {
        this.refundQuantityPercent = refundQuantityPercent;
    }

    /**
     * 改签张数
     */
    public Integer getTotalRebookQuantity() {
        return totalRebookQuantity;
    }

    /**
     * 改签张数
     */
    public void setTotalRebookQuantity(final Integer totalRebookQuantity) {
        this.totalRebookQuantity = totalRebookQuantity;
    }

    /**
     * 改签占比
     */
    public Double getRebookQuantityPercent() {
        return rebookQuantityPercent;
    }

    /**
     * 改签占比
     */
    public void setRebookQuantityPercent(final Double rebookQuantityPercent) {
        this.rebookQuantityPercent = rebookQuantityPercent;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final FlightBehaviorInfo other = (FlightBehaviorInfo)obj;
        return
            Objects.equal(this.dim, other.dim) &&
            Objects.equal(this.totalQuantity, other.totalQuantity) &&
            Objects.equal(this.quantityPercent, other.quantityPercent) &&
            Objects.equal(this.totalPrice, other.totalPrice) &&
            Objects.equal(this.pricePercent, other.pricePercent) &&
            Objects.equal(this.avgPrice, other.avgPrice) &&
            Objects.equal(this.avgTpmsPrice, other.avgTpmsPrice) &&
            Objects.equal(this.avgDiscount, other.avgDiscount) &&
            Objects.equal(this.avgPreOrderDate, other.avgPreOrderDate) &&
            Objects.equal(this.totalRefundQuantity, other.totalRefundQuantity) &&
            Objects.equal(this.refundQuantityPercent, other.refundQuantityPercent) &&
            Objects.equal(this.totalRebookQuantity, other.totalRebookQuantity) &&
            Objects.equal(this.rebookQuantityPercent, other.rebookQuantityPercent);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.dim == null ? 0 : this.dim.hashCode());
        result = 31 * result + (this.totalQuantity == null ? 0 : this.totalQuantity.hashCode());
        result = 31 * result + (this.quantityPercent == null ? 0 : this.quantityPercent.hashCode());
        result = 31 * result + (this.totalPrice == null ? 0 : this.totalPrice.hashCode());
        result = 31 * result + (this.pricePercent == null ? 0 : this.pricePercent.hashCode());
        result = 31 * result + (this.avgPrice == null ? 0 : this.avgPrice.hashCode());
        result = 31 * result + (this.avgTpmsPrice == null ? 0 : this.avgTpmsPrice.hashCode());
        result = 31 * result + (this.avgDiscount == null ? 0 : this.avgDiscount.hashCode());
        result = 31 * result + (this.avgPreOrderDate == null ? 0 : this.avgPreOrderDate.hashCode());
        result = 31 * result + (this.totalRefundQuantity == null ? 0 : this.totalRefundQuantity.hashCode());
        result = 31 * result + (this.refundQuantityPercent == null ? 0 : this.refundQuantityPercent.hashCode());
        result = 31 * result + (this.totalRebookQuantity == null ? 0 : this.totalRebookQuantity.hashCode());
        result = 31 * result + (this.rebookQuantityPercent == null ? 0 : this.rebookQuantityPercent.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("dim", dim)
            .add("totalQuantity", totalQuantity)
            .add("quantityPercent", quantityPercent)
            .add("totalPrice", totalPrice)
            .add("pricePercent", pricePercent)
            .add("avgPrice", avgPrice)
            .add("avgTpmsPrice", avgTpmsPrice)
            .add("avgDiscount", avgDiscount)
            .add("avgPreOrderDate", avgPreOrderDate)
            .add("totalRefundQuantity", totalRefundQuantity)
            .add("refundQuantityPercent", refundQuantityPercent)
            .add("totalRebookQuantity", totalRebookQuantity)
            .add("rebookQuantityPercent", rebookQuantityPercent)
            .toString();
    }
}
