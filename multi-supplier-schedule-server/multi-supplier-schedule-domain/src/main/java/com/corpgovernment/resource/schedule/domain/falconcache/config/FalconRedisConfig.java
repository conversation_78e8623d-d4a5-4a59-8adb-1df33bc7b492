package com.corpgovernment.resource.schedule.domain.falconcache.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

/**
 * <AUTHOR>
 * @date 2024/4/18
 */
@Configuration
public class FalconRedisConfig {
//    @Bean
//    @ConditionalOnMissingBean
//    public LettuceConnectionFactory redisConnectionFactory() {
//        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
//        String host = Config.defaultConfig().get("spring.redis.host");
//        String port = Config.defaultConfig().get("spring.redis.port");
//        String password = Config.defaultConfig().get("spring.redis.password");
//        redisConfig.setHostName(host);
//        redisConfig.setPort(Integer.parseInt(port));
//        redisConfig.setPassword(RedisPassword.of(password));
//
//        LettuceClientConfiguration lettuceConfig = LettucePoolingClientConfiguration.builder()
//                .poolConfig(new GenericObjectPoolConfig<>())
//                .build();
//
//        return new LettuceConnectionFactory(redisConfig, lettuceConfig);
//    }

    @Bean
    @ConditionalOnMissingBean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }
    @Bean
    public RedisMessageListenerContainer falconRedisMessageListenerContainer(RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        return container;
    }

    @Bean
    public RedisMessageListenerContainer falconRedisMessagePublisherContainer(RedisConnectionFactory lettuceConnectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(lettuceConnectionFactory);
        return container;
    }


}
