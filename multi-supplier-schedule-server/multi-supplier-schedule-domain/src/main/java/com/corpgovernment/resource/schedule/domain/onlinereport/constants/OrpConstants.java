package com.corpgovernment.resource.schedule.domain.onlinereport.constants;

import com.ctrip.framework.foundation.internals.NetworkInterfaceManager;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.common.constants
 * @description: 常用常量
 * @author: <PERSON>
 * @create: 2021-11-03 17:56
 **/
public class OrpConstants {

    public static final Object NULL = null;
    public static final String LOCAL_IP = "**************";
    public static final String IP = NetworkInterfaceManager.INSTANCE.getLocalHostAddress();
    public static final String CHINESE_FILE = "Chinese.properties";

    /**
     * click house
     */
    public static final String CK_CLIENT_NAME_PRO = "corp-online-report-platform-service";
    public static final String DB_NAME_PRO = "corpbi_onlinereport";
    /**
     public static final String CK_URL_PRO = "*******************************************************************************";
     public static final String DB_USER = "ch_jdbc_ck100038130";
     public static final String DB_PASSWORD = "oVpN79fSjs";
     public static final String CK_URL_UAT = "****************************************************************************************";
     public static final String DB_USER_UAT = "ch_jdbc_ck100032438";
     public static final String DB_PASSWORD_UAT = "Ge0nqtphaH";
     **/
    /**
     * click house
     */
    public static final String CORP_INSIGHT_CK_CLIENT_NAME_PRO = "corp-insight-service";
    public static final String CORP_INSIGHT_DB_NAME_PRO = "ck_corpbi_corpinsightdb";

    /**
     * star rocks
     */
    public static final String SR_DB_FAT = "corpbi_onlinereport_srdb";
    public static final String SR_DB_PROD = "project_corpdb";
    /**
     * public static final String SR_USER = "bicorp";
     * public static final String SR_PWD_FAT = "a5c544d24654a3e6d998c6f4002d88e7";
     * public static final String SR_PWD_PROD = "QohaGtZaFuK1EyHL";
     * public static final String URL_FAT = "*********************************************************************************";
     * public static final String URL_PROD = "*************************************************************************************";
     **/
    public static final String REDIS_CLUSTER = "CORP_OnlineReport_Cache";

    public static final String READ_CLICKHOUSE_PARTITION = "READ_CLICKHOUSE_PARTITION";

    public static final int CONNECTION_TIME_OUT = 5000;
    public static final int FIFTY = 50;
    public static final int SR_WAITE = 10000;
    public static final int SOCKET_TIME_OUT = 300000;

    public static final Integer EXPIRE = 60 * 60;
    public static final String POINT = ".";
    public static final String COMMA = ",";
    public static final String EMPTY = "";
    public static final String BLANK = " ";
    public static final String UNDER_LING = "_";
    public static final String MIDDLE_LINE = "-";
    public static final String QUESTION_REMARK = "?";
    public static final String LEFT_PARENTHESES = " ( ";
    /**
     * 中文小括号-左
     */
    public static final String LEFT_PARENTHESES_CN = "（";
    public static final String RIGHT_PARENTHESES = " ) ";
    /**
     * 中文小括号-右
     */
    public static final String RIGHT_PARENTHESES_CN = "）";
    public static final int ZERO = 0;
    public static final Long ZERO_L = 0L;
    public static final String ZERO_CHAR = "0";
    public static final String ZERO_CHAR_2 = "0.00";
    public static final String ZERO_CHAR_1 = "0.0";
    public static final int ONE = 1;
    public static final int MINUS_ONE = -1;
    public static final int TWO = 2;
    public static final int THREE = 3;
    public static final int FOUR = 4;
    public static final int FIVE = 5;
    public static final int SIX = 6;
    public static final int SEVEN = 7;
    public static final int EIGHT = 8;
    public static final int NINE = 9;
    public static final int TEN = 10;
    public static final int ELEVEN = 11;

    public static final int EIGHTEEN = 18;
    public static final int TWELVE = 12;

    public static final int TWENTY = 20;

    public static final int MINUS_TWENTY = -20;

    public static final int HOUR_24 = 24;

    public static final int MONTH_24 = 24;
    public static final int HUNDRED_1 = 100;
    public static final int SUCCESS_CODE = 200;
    public static final String PERCENT = "%";
    public static final String SINGLE_COMMA = "'";
    public static final String SET = "set";
    public static final int LOG_PAGE_SIZE = 20000;
    public static final int LOG_NO_PAGE_SIZE = 40000;
    public static final int HANDRED = 100;
    public static final String P1 = "$P1";
    public static final String P2 = "$P2";
    public static final String P3 = "$P3";
    public static final String P4 = "$P4";
    public static final String P5 = "$P5";
    public static final String P6 = "$P6";
    public static final String FILED = "$P_FILED";
    public static final String TABLE = "$P_TABLE";
    public static final String T = "T";
    public static final String ONE_EQ_ONE = " 1=1 ";
    public static final String FROM = " from ";
    public static final String AND = " and ";
    public static final String IN = " in ";
    public static final String OR = " or ";
    public static final String LIKE = " like ";
    public static final String WHERE = " where ";
    //public static final String PRE_WHERE = " prewhere ";
    public static final String PRE_WHERE = " where ";

    public static final String TABLE_START_WITH = "adm_";
    public static final String PARTITION = "partition";
    public static final String GROUP_BY = " group by ";
    public static final String ORDER_BY = " order by ";
    public static final String ASC = " asc ";
    public static final String DESC = " desc ";
    public static final String[] DEFAULT_ARR = new String[]{};
    public static final String DEFAULT_P = "0.00%";
    public static final String PLUS = "+";
    public static final String MINUS = "-";

    public static final String ANY = " any ";

    public static final String DEFAULT_LANG = "zh-CN";

    public static final int ONE_YEAR_DAYS = 364;

    public static final int TWO_YEAR_DAYS = 728;

    // 日本站
    public static final String POS_JP = "JA-JP";

    // 香港站
    public static final String POS_HK = "ZH-HK";

    // ocs分片数
    public static final Integer OCS_SHARD = 16;

    public static final String START = "date_start";

    public static final String END = "date_end";
    public static final String READ_CLICKHOUSE_SINGLE_PARTITION = "READ_CLICKHOUSE_SINGLE_PARTITION";

    public static final String REPORT_DATE = "report_date";

    public static final String QUERY_DATE = "querydate";

    public static final String ORDER_DATE = "substr(order_date, 1, 10)";

    public static final String HOTEL_PREMIUM_EARLIEST_DAY = "2022-07-24";

    public static final String DEFAULT_SAVE_MIN_DATE = "2019-01-01";

    public static final String ORDERDT = "orderdt";
    /**
     * 交易时间
     */
    public static final String PAY_TIME = "paytime";

    public static final int STRING = 0;

    public static final int DECIMAL = 1;

    public static final int PERCENT_F = 3;

    public static final int INTEGER = 2;

    public static final String ALL = "all";

    public static final String READ_STARROCKS_SINGLE_PARTITION = "READ_STARROCKS_SINGLE_PARTITION";
    public static final String QUERY_LATEST_PARTITION_VERSION = "queryLatestPartitionVersion";

    // 日元
    public static final String CURRENCY_JP = "JPY";
    // 千
    public static final Integer THOUSAND = 1000;
}
