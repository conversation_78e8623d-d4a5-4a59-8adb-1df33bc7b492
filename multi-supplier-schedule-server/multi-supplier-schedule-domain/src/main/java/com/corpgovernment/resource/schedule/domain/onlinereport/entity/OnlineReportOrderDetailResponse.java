package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@SuppressWarnings("all")
@Data
public class OnlineReportOrderDetailResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseCode")
    public Integer responseCode;

    /**
     * 必写固定字段
     */
    @JsonProperty("responseDesc")
    public String responseDesc;

    @JsonProperty("data")
    public List<String> data;

    @JsonProperty("orderDetailInfo")
    public OnlineReportOrderDetailInfo orderDetailInfo;

    /**
     * 数据总条数
     */
    @JsonProperty("totalRecords")
    public Integer totalRecords;

    @JsonProperty("extData")
    public Map<String, String> extData;

    private BaseQueryResponse baseQueryResponse;

}
