package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.flight;


import java.io.Serializable;

/**
 * @Description: 起飞时间段分析BO
 * <AUTHOR>
 * @Date 2019/5/23 16:02
 */
public class FlightTakeOffTimeBO implements Serializable{

    private static final long serialVersionUID = 1L;

    String takeOffTime;

    Double avgPriceRate;

    Integer number;

    String percent;



    public String getTakeOffTime() {
        return takeOffTime;
    }

    public void setTakeOffTime(String takeOffTime) {
        this.takeOffTime = takeOffTime;
    }

    public Double getAvgPriceRate() {
        return avgPriceRate;
    }

    public void setAvgPriceRate(Double avgPriceRate) {
        this.avgPriceRate = avgPriceRate;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getPercent() {
        return percent;
    }

    public void setPercent(String percent) {
        this.percent = percent;
    }
}
