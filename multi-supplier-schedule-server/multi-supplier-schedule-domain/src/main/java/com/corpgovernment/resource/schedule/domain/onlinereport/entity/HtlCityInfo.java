package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "cityId",
    "cityName",
    "pcitylevel"
})
public class HtlCityInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public HtlCityInfo(
        Integer cityId,
        String cityName,
        String pcitylevel) {
        this.cityId = cityId;
        this.cityName = cityName;
        this.pcitylevel = pcitylevel;
    }

    public HtlCityInfo() {
    }

    /**
     * 城市id
     */
    @JsonProperty("cityId")
    public Integer cityId;

    /**
     * 城市名称
     */
    @JsonProperty("cityName")
    public String cityName;

    /**
     * 城市等级
     */
    @JsonProperty("pcitylevel")
    public String pcitylevel;

    /**
     * 城市id
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * 城市id
     */
    public void setCityId(final Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 城市名称
     */
    public String getCityName() {
        return cityName;
    }

    /**
     * 城市名称
     */
    public void setCityName(final String cityName) {
        this.cityName = cityName;
    }

    /**
     * 城市等级
     */
    public String getPcitylevel() {
        return pcitylevel;
    }

    /**
     * 城市等级
     */
    public void setPcitylevel(final String pcitylevel) {
        this.pcitylevel = pcitylevel;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final HtlCityInfo other = (HtlCityInfo)obj;
        return
            Objects.equal(this.cityId, other.cityId) &&
            Objects.equal(this.cityName, other.cityName) &&
            Objects.equal(this.pcitylevel, other.pcitylevel);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.cityId == null ? 0 : this.cityId.hashCode());
        result = 31 * result + (this.cityName == null ? 0 : this.cityName.hashCode());
        result = 31 * result + (this.pcitylevel == null ? 0 : this.pcitylevel.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("cityId", cityId)
            .add("cityName", cityName)
            .add("pcitylevel", pcitylevel)
            .toString();
    }
}
