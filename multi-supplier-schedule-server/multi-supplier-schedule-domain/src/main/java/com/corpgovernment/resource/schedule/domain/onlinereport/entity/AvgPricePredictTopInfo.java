package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "risingQuantity",
    "descendQuantity",
    "avgPriceOften",
    "avgPricePredictTrendList"
})
public class AvgPricePredictTopInfo implements Serializable {
    private static final long serialVersionUID = 1L;



    

    public AvgPricePredictTopInfo(
        Integer risingQuantity,
        Integer descendQuantity,
        Double avgPriceOften,
        List<AvgPricePredictTrendInfo> avgPricePredictTrendList) {
        this.risingQuantity = risingQuantity;
        this.descendQuantity = descendQuantity;
        this.avgPriceOften = avgPriceOften;
        this.avgPricePredictTrendList = avgPricePredictTrendList;
    }

    public AvgPricePredictTopInfo() {
    }

    /**
     * 显著上涨数量
     */
    @JsonProperty("risingQuantity")
    public Integer risingQuantity;

    /**
     * 显著下降数量
     */
    @JsonProperty("descendQuantity")
    public Integer descendQuantity;

    /**
     * 显著上涨城市或航线均价
     */
    @JsonProperty("avgPriceOften")
    public Double avgPriceOften;

    @JsonProperty("avgPricePredictTrendList")
    public List<AvgPricePredictTrendInfo> avgPricePredictTrendList;

    /**
     * 显著上涨数量
     */
    public Integer getRisingQuantity() {
        return risingQuantity;
    }

    /**
     * 显著上涨数量
     */
    public void setRisingQuantity(final Integer risingQuantity) {
        this.risingQuantity = risingQuantity;
    }

    /**
     * 显著下降数量
     */
    public Integer getDescendQuantity() {
        return descendQuantity;
    }

    /**
     * 显著下降数量
     */
    public void setDescendQuantity(final Integer descendQuantity) {
        this.descendQuantity = descendQuantity;
    }

    /**
     * 显著上涨城市或航线均价
     */
    public Double getAvgPriceOften() {
        return avgPriceOften;
    }

    /**
     * 显著上涨城市或航线均价
     */
    public void setAvgPriceOften(final Double avgPriceOften) {
        this.avgPriceOften = avgPriceOften;
    }
    public List<AvgPricePredictTrendInfo> getAvgPricePredictTrendList() {
        return avgPricePredictTrendList;
    }

    public void setAvgPricePredictTrendList(final List<AvgPricePredictTrendInfo> avgPricePredictTrendList) {
        this.avgPricePredictTrendList = avgPricePredictTrendList;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final AvgPricePredictTopInfo other = (AvgPricePredictTopInfo)obj;
        return
            Objects.equal(this.risingQuantity, other.risingQuantity) &&
            Objects.equal(this.descendQuantity, other.descendQuantity) &&
            Objects.equal(this.avgPriceOften, other.avgPriceOften) &&
            Objects.equal(this.avgPricePredictTrendList, other.avgPricePredictTrendList);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.risingQuantity == null ? 0 : this.risingQuantity.hashCode());
        result = 31 * result + (this.descendQuantity == null ? 0 : this.descendQuantity.hashCode());
        result = 31 * result + (this.avgPriceOften == null ? 0 : this.avgPriceOften.hashCode());
        result = 31 * result + (this.avgPricePredictTrendList == null ? 0 : this.avgPricePredictTrendList.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("risingQuantity", risingQuantity)
            .add("descendQuantity", descendQuantity)
            .add("avgPriceOften", avgPriceOften)
            .add("avgPricePredictTrendList", avgPricePredictTrendList)
            .toString();
    }
}
