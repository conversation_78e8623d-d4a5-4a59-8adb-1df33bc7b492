package com.corpgovernment.resource.schedule.domain.onlinereport.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Date:2019/9/6
 * Description:
 * Project:onlinereportweb
 *
 * <AUTHOR>
 */
@Data
public class ReportLibFilterBO {

    private Long startTime;
    private Long endTime;
    private String queryType;
    private String bizType;
    private Integer pageSize;
    private Integer pageIndex;
    private String scrollId;
    private String uniqued;
    private String groupId;
    private List<String> corpIds;
    private List<Long> accountIds;
    private List<Integer> warningLevelList;
    private String uid;
    private Integer limit;
    private List<String> bizTypes;
    private String timeDimension;
    private String switchType;
    private String passengerName;
    private String aggType;
    private List<Integer> cityIds;
    private List<String> searchTypes;
    private List<CostCenterAndDept> costcenters;
    private List<CostCenterAndDept> depts;
    private Integer star;
    private List<String> cityLevel;
    private String hotelType;
    private Integer timeType;
    /**
     * 筛选项
     */
    private Map<String, Object> filterList;

    //报表自定义字段
    private String customColumn;

    private String lang;
}
