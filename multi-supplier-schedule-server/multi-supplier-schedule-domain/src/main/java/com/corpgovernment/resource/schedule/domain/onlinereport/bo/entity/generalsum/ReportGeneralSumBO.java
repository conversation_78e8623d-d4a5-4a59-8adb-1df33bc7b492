package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity.generalsum;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2019/6/22
 */
public class ReportGeneralSumBO {

    //消费预警标识
    private boolean flag;
    //消费超出百分比
    private String warnPer;
    //预警金额
    private double limitConsum;
    //总体消费
    private double allconsumption;
    //总节省
    private double allsaving;
    //国内机票消费
    private double domFlightPrice;
    //国际机票消费
    private double interFlightPrice;
    //协议酒店消费
    private double argHotelPrice;
    //会员酒店消费
    private double memHotelPrice;
    //火车票消费
    private double trainPrice;
    //用车总概
    private double vehiclePrice;
    //协议酒店月
    private List<ReportGeneralDetailBO> arghotel_Moth;
    //会员酒店月
    private List<ReportGeneralDetailBO> memhotel_Moth;
    //国内机票月
    private List<ReportGeneralDetailBO> domflight_Moth;
    //国际机票月
    private List<ReportGeneralDetailBO> inteflight_Moth;
    //火车票月
    private List<ReportGeneralDetailBO> train_Moth;
    //用车月度
    private List<ReportGeneralDetailBO> vechile_Month;
    //协议酒店季度
    private List<ReportGeneralDetailBO> arghotel_Quar;
    //会员酒店季度
    private List<ReportGeneralDetailBO> memhotel_Quar;
    //国内机票季度
    private List<ReportGeneralDetailBO> domflight_Quar;
    //国际机票季度
    private List<ReportGeneralDetailBO> inteflight_Quar;
    //火车票季度
    private List<ReportGeneralDetailBO> train_Quar;
    //用车季度
    private List<ReportGeneralDetailBO> vechile_Quar;

    //总额-同比
    private String allConsumptionTonb;
    // 总额-环比
    private String allConsumptionHunb;
    //汇总数据的同比[国内机票,国际机票,协议酒店,会员酒店,火车票,用车]
    private List<String> consumptionTonb = new ArrayList<>();
    //汇总数据的环比[国内机票,国际机票,协议酒店,会员酒店,火车票,用车]
    private List<String> consumptionHunb = new ArrayList<>();
    //月度 同环比
    private List<ReportGeneralDetailBO> consumMonth = new ArrayList<>();
    //季度-同环比
    private List<ReportGeneralDetailBO> consumQuar = new ArrayList<>();

    public boolean isFlag() {
        return flag;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public String getWarnPer() {
        return warnPer;
    }

    public void setWarnPer(String warnPer) {
        this.warnPer = warnPer;
    }

    public double getLimitConsum() {
        return limitConsum;
    }

    public void setLimitConsum(double limitConsum) {
        this.limitConsum = limitConsum;
    }

    public double getAllconsumption() {
        return allconsumption;
    }

    public void setAllconsumption(double allconsumption) {
        this.allconsumption = allconsumption;
    }

    public double getAllsaving() {
        return allsaving;
    }

    public void setAllsaving(double allsaving) {
        this.allsaving = allsaving;
    }

    public double getDomFlightPrice() {
        return domFlightPrice;
    }

    public void setDomFlightPrice(double domFlightPrice) {
        this.domFlightPrice = domFlightPrice;
    }

    public double getInterFlightPrice() {
        return interFlightPrice;
    }

    public void setInterFlightPrice(double interFlightPrice) {
        this.interFlightPrice = interFlightPrice;
    }

    public double getArgHotelPrice() {
        return argHotelPrice;
    }

    public void setArgHotelPrice(double argHotelPrice) {
        this.argHotelPrice = argHotelPrice;
    }

    public double getMemHotelPrice() {
        return memHotelPrice;
    }

    public void setMemHotelPrice(double memHotelPrice) {
        this.memHotelPrice = memHotelPrice;
    }

    public double getTrainPrice() {
        return trainPrice;
    }

    public void setTrainPrice(double trainPrice) {
        this.trainPrice = trainPrice;
    }

    public double getVehiclePrice() {
        return vehiclePrice;
    }

    public void setVehiclePrice(double vehiclePrice) {
        this.vehiclePrice = vehiclePrice;
    }

    public List<ReportGeneralDetailBO> getArghotel_Moth() {
        return arghotel_Moth;
    }

    public void setArghotel_Moth(List<ReportGeneralDetailBO> arghotel_Moth) {
        this.arghotel_Moth = arghotel_Moth;
    }

    public List<ReportGeneralDetailBO> getMemhotel_Moth() {
        return memhotel_Moth;
    }

    public void setMemhotel_Moth(List<ReportGeneralDetailBO> memhotel_Moth) {
        this.memhotel_Moth = memhotel_Moth;
    }

    public List<ReportGeneralDetailBO> getDomflight_Moth() {
        return domflight_Moth;
    }

    public void setDomflight_Moth(List<ReportGeneralDetailBO> domflight_Moth) {
        this.domflight_Moth = domflight_Moth;
    }

    public List<ReportGeneralDetailBO> getInteflight_Moth() {
        return inteflight_Moth;
    }

    public void setInteflight_Moth(List<ReportGeneralDetailBO> inteflight_Moth) {
        this.inteflight_Moth = inteflight_Moth;
    }

    public List<ReportGeneralDetailBO> getTrain_Moth() {
        return train_Moth;
    }

    public void setTrain_Moth(List<ReportGeneralDetailBO> train_Moth) {
        this.train_Moth = train_Moth;
    }

    public List<ReportGeneralDetailBO> getVechile_Month() {
        return vechile_Month;
    }

    public void setVechile_Month(List<ReportGeneralDetailBO> vechile_Month) {
        this.vechile_Month = vechile_Month;
    }

    public List<ReportGeneralDetailBO> getArghotel_Quar() {
        return arghotel_Quar;
    }

    public void setArghotel_Quar(List<ReportGeneralDetailBO> arghotel_Quar) {
        this.arghotel_Quar = arghotel_Quar;
    }

    public List<ReportGeneralDetailBO> getMemhotel_Quar() {
        return memhotel_Quar;
    }

    public void setMemhotel_Quar(List<ReportGeneralDetailBO> memhotel_Quar) {
        this.memhotel_Quar = memhotel_Quar;
    }

    public List<ReportGeneralDetailBO> getDomflight_Quar() {
        return domflight_Quar;
    }

    public void setDomflight_Quar(List<ReportGeneralDetailBO> domflight_Quar) {
        this.domflight_Quar = domflight_Quar;
    }

    public List<ReportGeneralDetailBO> getInteflight_Quar() {
        return inteflight_Quar;
    }

    public void setInteflight_Quar(List<ReportGeneralDetailBO> inteflight_Quar) {
        this.inteflight_Quar = inteflight_Quar;
    }

    public List<ReportGeneralDetailBO> getTrain_Quar() {
        return train_Quar;
    }

    public void setTrain_Quar(List<ReportGeneralDetailBO> train_Quar) {
        this.train_Quar = train_Quar;
    }

    public List<ReportGeneralDetailBO> getVechile_Quar() {
        return vechile_Quar;
    }

    public void setVechile_Quar(List<ReportGeneralDetailBO> vechile_Quar) {
        this.vechile_Quar = vechile_Quar;
    }

    public String getAllConsumptionTonb() {
        return allConsumptionTonb;
    }

    public void setAllConsumptionTonb(String allConsumptionTonb) {
        this.allConsumptionTonb = allConsumptionTonb;
    }

    public String getAllConsumptionHunb() {
        return allConsumptionHunb;
    }

    public void setAllConsumptionHunb(String allConsumptionHunb) {
        this.allConsumptionHunb = allConsumptionHunb;
    }

    public List<String> getConsumptionTonb() {
        return consumptionTonb;
    }

    public void setConsumptionTonb(List<String> consumptionTonb) {
        this.consumptionTonb = consumptionTonb;
    }

    public List<String> getConsumptionHunb() {
        return consumptionHunb;
    }

    public void setConsumptionHunb(List<String> consumptionHunb) {
        this.consumptionHunb = consumptionHunb;
    }

    public List<ReportGeneralDetailBO> getConsumMonth() {
        return consumMonth;
    }

    public void setConsumMonth(List<ReportGeneralDetailBO> consumMonth) {
        this.consumMonth = consumMonth;
    }

    public List<ReportGeneralDetailBO> getConsumQuar() {
        return consumQuar;
    }

    public void setConsumQuar(List<ReportGeneralDetailBO> consumQuar) {
        this.consumQuar = consumQuar;
    }
}
