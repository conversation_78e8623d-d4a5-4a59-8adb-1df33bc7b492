/*
package com.corpgovernment.resource.schedule.domain.onlinereport.utils;


import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

*/
/**
 * @Description: Redis工具类
 * <AUTHOR>
 * @Date 2019/3/1
 *//*

@Slf4j
public class RedisCacheUtils {

    public static credis.java.client.CacheProvider CacheProvider = null;

    private RedisCacheUtils() {
    }

    public static void init() {
        CacheProvider = CacheFactory.GetProvider("CORP_COI_Report");
    }

    public static void init(String redisName) {
        CacheProvider = CacheFactory.GetProvider(redisName);
    }

    public static String get(String key) {
        String retval = CacheProvider.get(key);
        return retval;
    }

    public static <T> T get(String key, Class<T> cls) {
        if (null == CacheProvider) {
            init("CORP_BICrossRecommend");
        }
        String retval = CacheProvider.get(key);
        return JsonUtils.jsonTobject(retval, cls);

    }

    public static boolean set(String key, String value) {
        long s0 = System.currentTimeMillis();
        boolean retval = CacheProvider.set(key, value);

        if (true) {
            long s1 = System.currentTimeMillis();
            log.info("redis-set take time ：" + String.valueOf(s1 - s0) + "ms");
        }
        return retval;
    }

    */
/**
     * 事务操作-设置带时间的缓存到redis
     *
     * @param key
     * @param value
     * @param times
     * @return
     *//*

    public static boolean setValueExpire(String key, String value, int times) {
        long s0 = System.currentTimeMillis();
        //保证事务
        boolean retval = CacheProvider.setex(key, times, value);
        if (retval) {
            long s1 = System.currentTimeMillis();
            log.info("redis-set take time ：" + String.valueOf(s1 - s0) + "ms");
        }
        return retval;
    }

    public static boolean hmset(String key, Map<String, String> hash, boolean logIsOn) {
        long s0 = System.currentTimeMillis();
        boolean retval = CacheProvider.hmset(key, hash);

        if (logIsOn) {
            long s1 = System.currentTimeMillis();
            log.info("redis-hmset take time ：" + String.valueOf(s1 - s0) + "ms");
        }
        return retval;
    }

    public static <T> List<T> hmget(String var1, Class<T> tClass, String... var2) {
        List<String> vals = CacheProvider.hmget(var1, var2);
        if (vals != null && !vals.isEmpty()) {
            List<T> listData = new ArrayList<>();

            for (int i = 0; i < vals.size(); i++) {
                T obj = JsonUtils.jsonTobject(vals.get(i), tClass);
                listData.add(obj);
            }
            return listData;
        }
        return null;
    }

    public static boolean expire(String key, int t, boolean logIsOn) {
        long s0 = System.currentTimeMillis();
        boolean retval = CacheProvider.expire(key, t);
        if (logIsOn) {
            long s1 = System.currentTimeMillis();
            log.info("redis-expire take time ：" + String.valueOf(s1 - s0) + "ms");
        }
        return retval;
    }

    public static List<String> hvals(String key, boolean logIsOn) {
        long s0 = System.currentTimeMillis();
        List<String> retval = CacheProvider.hvals(key);
        if (logIsOn) {
            long s1 = System.currentTimeMillis();
            log.info("redis-hvals take time ：" + String.valueOf(s1 - s0) + "ms");
        }
        return retval;
    }

    */
/**
     * 删除redis中的key
     *
     * @param key
     * @param logIsOn
     * @return
     *//*

    public static boolean delKey(String key, boolean logIsOn) {
        long s0 = System.currentTimeMillis();
        boolean isSuccess = CacheProvider.del(key);
        if (logIsOn) {
            long s1 = System.currentTimeMillis();
            log.info("redis-delKey take time ：" + String.valueOf(s1 - s0) + "ms");
        }
        return isSuccess;
    }

    */
/**
     * 将list数组插入redis list中
     *
     * @param listKey
     * @param items
     * @param logIsOn
     * @return
     *//*

    public static <T> void addItemsToList(String listKey, List<T> items, boolean logIsOn) {
        long s0 = System.currentTimeMillis();

        for (T item : items) {
            CacheProvider.lpush(listKey, JsonUtils.objectToString(item));
        }
        if (logIsOn) {
            long s1 = System.currentTimeMillis();
            log.info("redis-addItemsToList take time ：" + String.valueOf(s1 - s0) + "ms");
        }
    }

    public static void addStringItemsToList(String listKey, List<String> items, boolean logIsOn) {
        long s0 = System.currentTimeMillis();
        for (int i = 0; i < items.size(); i++) {
            CacheProvider.lpush(listKey, items.get(i));
        }
        if (logIsOn) {
            long s1 = System.currentTimeMillis();
            log.info("redis-addItemsToList take time ：" + String.valueOf(s1 - s0) + "ms");
        }
    }

    public static List<String> addStringItemsToList(String listKey, List<String> items, boolean logIsOn, List<String> listFailed) {
        long s0 = System.currentTimeMillis();
        for (int i = 0; i < items.size(); i++) {
            if (CacheProvider.lpush(listKey, items.get(i)) <= 0) {
                listFailed.add(items.get(i));
            }
        }
        if (logIsOn) {
            long s1 = System.currentTimeMillis();
            log.info("redis-addItemsToList take time ：" + String.valueOf(s1 - s0) + "ms");
        }
        return listFailed;
    }

    public static <T> void set(String Key, T value, boolean writeLog) {
        String jsonString = JsonUtils.objectToString(value);
        CacheProvider.set(Key, jsonString);
        if (writeLog) {
            log.info("setToRedis", "Key:" + Key + " value:" + jsonString);
        }
    }

    public static <T> void set(String key, String value, int expire, boolean writeLog) {
        String jsonString = JsonUtils.objectToString(value);
        CacheProvider.setex(key, expire, value);
        if (writeLog) {
            log.info("setToRedis", "Key:" + key + " value:" + jsonString);
        }
    }

    public static <T> T get(String Key, boolean logIsOn, Class<T> tclass) {
        long s0 = System.currentTimeMillis();
        String item = CacheProvider.get(Key);
        if (logIsOn) {
            long s1 = System.currentTimeMillis();
            log.info("redis-getListAllItems take time ：" + String.valueOf(s1 - s0) + "ms");
        }
        if (!item.isEmpty()) {
            T obj = JsonUtils.jsonTobject(item, tclass);
            ;
            return obj;
        }
        return null;
    }

    */
/**
     * 批量 key 获取
     *
     * @param clazz
     * @param keys
     * @param <T>
     * @return
     *//*

    public static <T> List<T> mget(Class<T> clazz, List<String> keys) {
        List<String> datas = mget(keys);
        if (0 == datas.size()) {
            return Collections.emptyList();
        }
        List<T> list = new ArrayList<>();
        if (!datas.isEmpty()) {
            for (int i = 0; i < datas.size(); i++) {
                String item = datas.get(i);
                if (item == null || item.isEmpty()) {
                    continue;
                }
                T obj = JsonUtils.jsonTobject(item, clazz);
                list.add(obj);
            }
        }
        return list;
    }

    */
/**
     * 批量 keys 获取
     *
     * @param keys
     * @return
     *//*

    public static List<String> mget(List<String> keys) {
        String[] keyArr = new String[keys.size()];
        return CacheProvider.mget(keys.toArray(keyArr));
    }

    */
/**
     * 读取list中所有数据
     *
     * @param listKey
     * @param logIsOn
     * @return
     *//*

    public static <T> List<T> getListAllItems(String listKey, boolean logIsOn, Class<T> tclass) {
        long s0 = System.currentTimeMillis();
        List<String> items = CacheProvider.sortbyalpha(listKey);
        if (logIsOn) {
            long s1 = System.currentTimeMillis();
            log.info("redis-getListAllItems take time ：" + String.valueOf(s1 - s0) + "ms");
        }
        if (!items.isEmpty()) {

            List<T> objItems = new ArrayList<>();

            for (String item : items) {
                T obj = JsonUtils.jsonTobject(item, tclass);
                objItems.add(obj);
            }
            return objItems;
        }
        return new ArrayList<T>();
    }

    */
/**
     * 读取list中所有数据
     *
     * @param key
     * @return
     *//*

    public static List<String> getStringItemsFromList(String key) {
        long size = CacheProvider.llen(key);
        if (size <= 0) {
            return null;
        }
        return CacheProvider.lrange(key, 0, size);
    }

    public static List<String> getStringItemsFromList(String key, long start, long end) {
        return CacheProvider.lrange(key, start, end);
    }

    */
/**
     * 读取list长度
     *
     * @param key
     * @return
     *//*

    public static Long llen(String key) {
        return CacheProvider.llen(key);
    }

    */
/**
     * 根据参数 count 的值，移除列表中与参数 val 相等的元素。
     *
     * @param key
     * @param count count > 0 : 从表头开始向表尾搜索，移除与 value 相等的元素，数量为 count 。
     *              count < 0 : 从表尾开始向表头搜索，移除与 value 相等的元素，数量为 count 的绝对值。
     *              count = 0 : 移除表中所有与 value 相等的值。
     * @param val
     * @return
     *//*

    public static Long lrem(String key, long count, String val) {
        return CacheProvider.lrem(key, count, val);
    }

}
*/
