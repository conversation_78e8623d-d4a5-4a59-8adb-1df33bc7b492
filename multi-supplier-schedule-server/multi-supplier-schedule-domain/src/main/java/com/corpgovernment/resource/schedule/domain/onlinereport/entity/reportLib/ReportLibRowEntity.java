package com.corpgovernment.resource.schedule.domain.onlinereport.entity.reportLib;

/**
 * <AUTHOR>
 * @Date 2020/10/14
 * @Description：excel模板行数定义
 */
public class ReportLibRowEntity {
    /**
     * ALL-消费概况-开始行
     */
    public static final Integer CONSUMPTION_SURVEY_START_ROW = 48;
    /**
     * 机票-机票KPI月度累计-开始行
     */
    public static final Integer FLIGHT_KPI_START_ROW = 58;
    /**
     * 机票-机票月度明细-国内机票-开始行
     */
    public static final Integer FLIGHT_MONTH_DETAIL_N_START_ROW = 2;
    /**
     * 机票-机票月度明细-国际机票-开始行
     */
    public static final Integer FLIGHT_MONTH_DETAIL_I_START_ROW = 6;
    /**
     * 机票-top5承运商-国内-开始行
     */
    public static final Integer FLIGHT_CARRIER_DOM_START_ROW = 671;
    /**
     * 机票-top5承运商-国际-开始行
     */
    public static final Integer FLIGHT_CARRIER_INT_START_ROW = 678;
    /**
     * 机票-top5航线-国内-开始行
     */
    public static final Integer FLIGHT_CITY_DOM_START_ROW = 686;
    /**
     * 机票-top5航线-国际-开始行
     */
    public static final Integer FLIGHT_CITY_INT_START_ROW = 695;
    /**
     * 机票-Top5城市对-Top5承运商-国内-开始行
     */
    public static final Integer FLIGHT_CITY_AND_CARRIER_DOM_START_ROW = 705;
    /**
     * 机票-Top5城市对-Top5承运商-国际-开始行
     */
    public static final Integer FLIGHT_CITY_AND_CARRIER_INT_START_ROW = 715;
    /**
     * 机票-协议航空采购-开始行
     */
    public static final Integer FLIGHT_AGREEMENT_AIR_PURCHASE_START_ROW = 1729;
    /**
     * 机票-协议航空采购-承运商总计行
     */
    public static final Integer FLIGHT_AGREEMENT_AIR_PURCHASE_CARRIER_ROW = 2275;
    /**
     * 机票-TOP 10航线和提前预订天数-开始行
     */
    public static final Integer FLIGHT_CITY_AND_PRE_ORDER_START_ROW = 629;
    /**
     * 机票-舱位分析-国内-开始行
     */
    public static final Integer FLIGHT_CABIN_N_START_ROW = 645;
    /**
     * 机票-舱位分析-国际-开始行
     */
    public static final Integer FLIGHT_CABIN_I_START_ROW = 650;
    /**
     * 机票-机票扣率分析-开始行
     */
    public static final Integer FLIGHT_RATE_START_ROW = 656;
    /**
     * 火车-火车月度明细-开始行
     */
    public static final Integer TRAIN_MONTH_DETAIL_START_ROW = 31;
    /**
     * 火车-火车票KPI月度累计-开始行
     */
    public static final Integer TRAIN_KPI_START_ROW = 1241;
    /**
     * 火车-部门火车票消费-开始行
     */
    public static final Integer TRAIN_DEPT_START_ROW = 1255;
    /**
     * 火车-火车票预订方式-开始行
     */
    public static final Integer TRAIN_BOOK_TYPE_START_ROW = 1468;
    /**
     * 火车-top5火车票行程分析-开始行
     */
    public static final Integer TRAIN_LINE_CITY_START_ROW = 1459;
    /**
     * 用车-用车月度明细-开始行
     */
    public static final Integer CAR_MONTH_DETAIL_START_ROW = 35;
    /**
     * 用车-部门用车消费-开始行
     */
    public static final Integer CAR_DEPT_START_ROW = 1473;
    /**
     * 用车-TOP 5用车城市-开始行
     */
    public static final Integer CAR_CITY_START_ROW = 1677;
    /**
     * 用车-TOP 5车型-国内接送机-开始行
     */
    public static final Integer CAR_TYPE_DOM_START_ROW = 1687;
    /**
     * 用车-TOP 5车型-国际接送机-开始行
     */
    public static final Integer CAR_TYPE_INT_START_ROW = 1694;
    /**
     * 用车-TOP 5车型-租车-开始行
     */
    public static final Integer CAR_TYPE_RENT_START_ROW = 1701;
    /**
     * 用车-TOP 5车型-包车-开始行
     */
    public static final Integer CAR_TYPE_CHARTERED_START_ROW = 1708;
    /**
     * 用车-TOP 5车型-随叫随到-开始行
     */
    public static final Integer CAR_TYPE_ONCALL_START_ROW = 1715;
    /**
     * 酒店-部门酒店消费-开始行
     */
    public static final Integer HOTEL_DEPT_START_ROW = 852;
    /**
     * 机票-机票金额明细-月份-开始行
     */
    public static final Integer FLIGHT_AMOUNT_DETAIL_MONTH_START_ROW = 41;
    /**
     * 机票-机票金额明细-开始行
     */
    public static final Integer FLIGHT_AMOUNT_DETAIL_START_ROW = 42;
    /**
     * 机票-部门机票消费-国内-开始行
     */
    public static final Integer FLIGHT_DEPT_DOM_START_ROW = 113;
    /**
     * 机票-部门机票消费-国际-开始行
     */
    public static final Integer FLIGHT_DEPT_INT_START_ROW = 222;
    /**
     * 机票-机票预订方式-开始行
     */
    public static final Integer FLIGHT_BOOK_TYPE_START_ROW = 420;
    /**
     * 机票-节省与损失-开始行
     */
    public static final Integer FLIGHT_SAVE_AND_LOSE_START_ROW = 424;
    /**
     * 机票-损失明细-国内损失明细-开始行
     */
    public static final Integer FLIGHT_LOSS_DETAIL_START_ROW = 434;
    /**
     * 机票-损失明细-前五部门-部门1-开始行
     */
    public static final Integer FLIGHT_LOSS_DEPT1_START_ROW = 455;
    /**
     * 机票-损失明细-前五部门-部门2-开始行
     */
    public static final Integer FLIGHT_LOSS_DEPT2_START_ROW = 476;
    /**
     * 机票-损失明细-前五部门-部门3-开始行
     */
    public static final Integer FLIGHT_LOSS_DEPT3_START_ROW = 497;
    /**
     * 机票-损失明细-前五部门-部门4-开始行
     */
    public static final Integer FLIGHT_LOSS_DEPT4_START_ROW = 518;
    /**
     * 机票-损失明细-前五部门-部门5-开始行
     */
    public static final Integer FLIGHT_LOSS_DEPT5_START_ROW = 539;
    /**
     * 机票-提前预订天数-国内-开始行
     */
    public static final Integer FLIGHT_PRE_DATE_N_START_ROW = 565;
    /**
     * 机票-提前预订天数-国际-开始行
     */
    public static final Integer FLIGHT_PRE_DATE_I_START_ROW = 574;
    /**
     * 机票-提前预订天数-国内前五部门1-开始行
     */
    public static final Integer FLIGHT_PRE_DATE_DEPT1_START_ROW = 584;
    /**
     * 机票-提前预订天数-国内前五部门2-开始行
     */
    public static final Integer FLIGHT_PRE_DATE_DEPT2_START_ROW = 593;
    /**
     * 机票-提前预订天数-国内前五部门3-开始行
     */
    public static final Integer FLIGHT_PRE_DATE_DEPT3_START_ROW = 602;
    /**
     * 机票-提前预订天数-国内前五部门4-开始行
     */
    public static final Integer FLIGHT_PRE_DATE_DEPT4_START_ROW = 611;
    /**
     * 机票-提前预订天数-国内前五部门5-开始行
     */
    public static final Integer FLIGHT_PRE_DATE_DEPT5_START_ROW = 620;
    /**
     * 机票-二氧化碳排放量-开始行
     */
    public static final Integer FLIGHT_CARBON_START_ROW = 814;
    /**
     * 酒店-酒店月度明细-会员酒店-开始行
     */
    public static final Integer HOTEL_MONTH_DETAIL_M_START_ROW = 11;
    /**
     * 酒店-酒店月度明细-协议酒店-开始行
     */
    public static final Integer HOTEL_MONTH_DETAIL_C_START_ROW = 15;
    /**
     * 酒店-酒店月度明细-国内酒店-开始行
     */
    public static final Integer HOTEL_MONTH_DETAIL_T_START_ROW = 19;
    /**
     * 酒店-酒店月度明细-港澳台酒店-开始行
     */
    public static final Integer HOTEL_MONTH_DETAIL_O_START_ROW = 23;
    /**
     * 酒店-酒店月度明细-海外酒店-开始行
     */
    public static final Integer HOTEL_MONTH_DETAIL_F_START_ROW = 27;
    /**
     * 酒店-酒店KPI月度累计-海外酒店-开始行
     */
    public static final Integer HOTEL_KPI_START_ROW = 821;
    /**
     * 酒店-当月酒店金额-开始行
     */
    public static final Integer HOTEL_CURRENT_MONTH_AMOUNT_START_ROW = 843;
    /**
     * 酒店-酒店预订方式-开始行
     */
    public static final Integer HOTEL_BOOK_TYPE_START_ROW = 1056;
    /**
     * 酒店-酒店星级分析-会员酒店-开始行
     */
    public static final Integer HOTEL_STAR_M_START_ROW = 1063;
    /**
     * 酒店-酒店星级分析-协议酒店-开始行
     */
    public static final Integer HOTEL_STAR_C_START_ROW = 1070;
    /**
     * 酒店-top5酒店城市分析-开始行
     */
    public static final Integer HOTEL_CITY_START_ROW = 1078;
    /**
     * 酒店-酒店RC-低价RC-开始行
     */
    public static final Integer HOTEL_LOW_RC_START_ROW = 1146;
    /**
     * 酒店-酒店RC-低价RC说明-开始行
     */
    public static final Integer HOTEL_LOW_RC_CODE_START_ROW = 1169;
    /**
     * 酒店-酒店RC-协议RC-开始行
     */
    public static final Integer HOTEL_AGREEMENT_RC_START_ROW = 1192;
    /**
     * 酒店-酒店RC-协议RC说明-开始行
     */
    public static final Integer HOTEL_AGREEMENT_RC_CODE_START_ROW = 1215;
    /**
     * 酒店-间夜采购前五城市前三酒店-会员酒店-城市1-开始行
     */
    public static final Integer HOTEL_CITY1_NIGHT_M_START_ROW = 1089;
    /**
     * 酒店-间夜采购前五城市前三酒店-会员酒店-城市2-开始行
     */
    public static final Integer HOTEL_CITY2_NIGHT_M_START_ROW = 1094;
    /**
     * 酒店-间夜采购前五城市前三酒店-会员酒店-城市3-开始行
     */
    public static final Integer HOTEL_CITY3_NIGHT_M_START_ROW = 1099;
    /**
     * 酒店-间夜采购前五城市前三酒店-会员酒店-城市4-开始行
     */
    public static final Integer HOTEL_CITY4_NIGHT_M_START_ROW = 1104;
    /**
     * 酒店-间夜采购前五城市前三酒店-会员酒店-城市5-开始行
     */
    public static final Integer HOTEL_CITY5_NIGHT_M_START_ROW = 1109;
    /**
     * 酒店-间夜采购前五城市前三酒店-协议酒店-城市1-开始行
     */
    public static final Integer HOTEL_CITY1_NIGHT_C_START_ROW = 1117;
    /**
     * 酒店-间夜采购前五城市前三酒店-协议酒店-城市2-开始行
     */
    public static final Integer HOTEL_CITY2_NIGHT_C_START_ROW = 1122;
    /**
     * 酒店-间夜采购前五城市前三酒店-协议酒店-城市3-开始行
     */
    public static final Integer HOTEL_CITY3_NIGHT_C_START_ROW = 1127;
    /**
     * 酒店-间夜采购前五城市前三酒店-协议酒店-城市4-开始行
     */
    public static final Integer HOTEL_CITY4_NIGHT_C_START_ROW = 1132;
    /**
     * 酒店-间夜采购前五城市前三酒店-协议酒店-城市5-开始行
     */
    public static final Integer HOTEL_CITY5_NIGHT_C_START_ROW = 1137;

    /**
     * 汽车消费月度累计
     */
    public static final Integer BUS_MONTH_CONSUME_AMOUNT_ROW = 2375;

    /**
     * 增值消费月度累计
     */
    public static final Integer ADD_MONTH_CONSUME_AMOUNT_ROW = 2385;

    /**
     * 机票潜在节省分析
     */
    public static final Integer FLIGHT_PROPORTION_SAVE_START_ROW = 2395;


    /**
     * 酒店-节省损失分析-开始行
     */
    public static final Integer HOTEL_SAVE_AND_LOSE_START_ROW = 2405;

    /**
     * 酒店潜在节省分析
     */
    public static final Integer HOTEL_PROPORTION_SAVE_START_ROW = 2425;

}
