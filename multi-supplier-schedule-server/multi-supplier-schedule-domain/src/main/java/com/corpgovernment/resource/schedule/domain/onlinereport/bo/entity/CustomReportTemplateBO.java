package com.corpgovernment.resource.schedule.domain.onlinereport.bo.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2019-02-27
 */
@Data
public class CustomReportTemplateBO {
    /**
     * key
     */
    private Long id;

    /**
     * 所属UID
     */
    private String createUser;

    /**
     * 具体内容
     */
    private String reportStyle;

    /**
     * 是否有效
     */
    private Boolean isValid;

    /**
     * 备注
     */
    private String remark;

    /**
     * 具体内容
     */
    private Integer followCount;


    /**
     * 创建时间
     */
    private Timestamp datachangeCreatetime;

    /**
     * 修改时间
     */
    private Timestamp datachangeLasttime;
}
