package com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO;


import com.corpgovernment.resource.schedule.domain.onlinereport.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2019/11/28 13:04
 * @Desc
 */

@Entity
////@Database(name = "CorpReportDB")
@Table(name = "corp_report_custom_column")
public class CorpReportCustomColumnPO {

    /**
     * 主键Id
     */
    @Id
    @Column(name = "custom_column_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long customColumnId;

    /**
     * 卡号
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;


    /**
     * 公司id
     */
    @Column(name = "corp_id")
    @Type(value = Types.VARCHAR)
    private String corpId;

    /**
     * 条件类型
     */
    @Column(name = "report_key")
    @Type(value = Types.VARCHAR)
    private String reportKey;

    /**
     * 自定义列
     */
    @Column(name = "custom_columns")
    @Type(value = Types.VARCHAR)
    private String customColumns;


    /**
     * 最后修改时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    public Long getCustomColumnId() {
        return customColumnId;
    }

    public void setCustomColumnId(Long customColumnId) {
        this.customColumnId = customColumnId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getReportKey() {
        return reportKey;
    }

    public void setReportKey(String reportKey) {
        this.reportKey = reportKey;
    }

    public String getCustomColumns() {
        return customColumns;
    }

    public void setCustomColumns(String customColumns) {
        this.customColumns = customColumns;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }
}
