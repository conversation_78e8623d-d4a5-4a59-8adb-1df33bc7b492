package com.corpgovernment.resource.schedule.domain.hotel.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/9
 */
@Data
public class SupplierControl {

    // 供应商code
    private String supplierCode;
    // 流量控制
    private Integer hotelIdTaskFlow;
    private Integer hotelStaticResourceTaskFlow;
    private Integer hotelMatchTaskFlow;
    private Integer hotelRoomMatchTaskFlow;
    private Integer hotelSelfMatchTaskFlow;
    // 流量
    private Integer taskFlow;
    // 开启全匹配
    private Boolean enableFullHotelMatch;
    // 酒店城市id获取
    private String listHotelCitySql;
    // 静态详情批量数量
    private Integer hotelStaticDetailBatchSize;

}
