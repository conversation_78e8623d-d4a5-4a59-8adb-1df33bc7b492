package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "allStock",
    "availableStock"
})
public class WelfareStockData implements Serializable {
    private static final long serialVersionUID = 1L;





    public WelfareStockData(
        Double allStock,
        Double availableStock) {
        this.allStock = allStock;
        this.availableStock = availableStock;
    }

    public WelfareStockData() {
    }

    @JsonProperty("allStock")
    public Double allStock;

    @JsonProperty("availableStock")
    public Double availableStock;

    public Double getAllStock() {
        return allStock;
    }

    public void setAllStock(final Double allStock) {
        this.allStock = allStock;
    }
    public Double getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(final Double availableStock) {
        this.availableStock = availableStock;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final WelfareStockData other = (WelfareStockData)obj;
        return
            Objects.equal(this.allStock, other.allStock) &&
            Objects.equal(this.availableStock, other.availableStock);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.allStock == null ? 0 : this.allStock.hashCode());
        result = 31 * result + (this.availableStock == null ? 0 : this.availableStock.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("allStock", allStock)
            .add("availableStock", availableStock)
            .toString();
    }
}
