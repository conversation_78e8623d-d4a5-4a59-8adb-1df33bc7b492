package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "type",
    "typeNum",
    "typeTimes",
    "timesRate",
    "corpTimesRate",
    "industryTimesRate"
})
public class OnlineReportPotentionSaveProportionDetail implements Serializable {
    private static final long serialVersionUID = 1L;





    public OnlineReportPotentionSaveProportionDetail(
        String type,
        BigDecimal typeNum,
        Integer typeTimes,
        BigDecimal timesRate,
        BigDecimal corpTimesRate,
        BigDecimal industryTimesRate) {
        this.type = type;
        this.typeNum = typeNum;
        this.typeTimes = typeTimes;
        this.timesRate = timesRate;
        this.corpTimesRate = corpTimesRate;
        this.industryTimesRate = industryTimesRate;
    }

    public OnlineReportPotentionSaveProportionDetail() {
    }

    /**
     * 类型
     */
    @JsonProperty("type")
    public String type;

    /**
     * 类型数值
     */
    @JsonProperty("typeNum")
    public BigDecimal typeNum;

    /**
     * 类型次数
     */
    @JsonProperty("typeTimes")
    public Integer typeTimes;

    /**
     * 公司次数占比
     */
    @JsonProperty("timesRate")
    public BigDecimal timesRate;

    /**
     * 商旅次数占比
     */
    @JsonProperty("corpTimesRate")
    public BigDecimal corpTimesRate;

    /**
     * 行业次数占比
     */
    @JsonProperty("industryTimesRate")
    public BigDecimal industryTimesRate;

    /**
     * 类型
     */
    public String getType() {
        return type;
    }

    /**
     * 类型
     */
    public void setType(final String type) {
        this.type = type;
    }

    /**
     * 类型数值
     */
    public BigDecimal getTypeNum() {
        return typeNum;
    }

    /**
     * 类型数值
     */
    public void setTypeNum(final BigDecimal typeNum) {
        this.typeNum = typeNum;
    }

    /**
     * 类型次数
     */
    public Integer getTypeTimes() {
        return typeTimes;
    }

    /**
     * 类型次数
     */
    public void setTypeTimes(final Integer typeTimes) {
        this.typeTimes = typeTimes;
    }

    /**
     * 公司次数占比
     */
    public BigDecimal getTimesRate() {
        return timesRate;
    }

    /**
     * 公司次数占比
     */
    public void setTimesRate(final BigDecimal timesRate) {
        this.timesRate = timesRate;
    }

    /**
     * 商旅次数占比
     */
    public BigDecimal getCorpTimesRate() {
        return corpTimesRate;
    }

    /**
     * 商旅次数占比
     */
    public void setCorpTimesRate(final BigDecimal corpTimesRate) {
        this.corpTimesRate = corpTimesRate;
    }

    /**
     * 行业次数占比
     */
    public BigDecimal getIndustryTimesRate() {
        return industryTimesRate;
    }

    /**
     * 行业次数占比
     */
    public void setIndustryTimesRate(final BigDecimal industryTimesRate) {
        this.industryTimesRate = industryTimesRate;
    }

    // Used by DatumWriter. Applications should not call.
    

    // Used by DatumReader. Applications should not call.
    

    



    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final OnlineReportPotentionSaveProportionDetail other = (OnlineReportPotentionSaveProportionDetail)obj;
        return
            Objects.equal(this.type, other.type) &&
            Objects.equal(this.typeNum, other.typeNum) &&
            Objects.equal(this.typeTimes, other.typeTimes) &&
            Objects.equal(this.timesRate, other.timesRate) &&
            Objects.equal(this.corpTimesRate, other.corpTimesRate) &&
            Objects.equal(this.industryTimesRate, other.industryTimesRate);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.type == null ? 0 : this.type.hashCode());
        result = 31 * result + (this.typeNum == null ? 0 : this.typeNum.hashCode());
        result = 31 * result + (this.typeTimes == null ? 0 : this.typeTimes.hashCode());
        result = 31 * result + (this.timesRate == null ? 0 : this.timesRate.hashCode());
        result = 31 * result + (this.corpTimesRate == null ? 0 : this.corpTimesRate.hashCode());
        result = 31 * result + (this.industryTimesRate == null ? 0 : this.industryTimesRate.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("type", type)
            .add("typeNum", typeNum)
            .add("typeTimes", typeTimes)
            .add("timesRate", timesRate)
            .add("corpTimesRate", corpTimesRate)
            .add("industryTimesRate", industryTimesRate)
            .toString();
    }
}
