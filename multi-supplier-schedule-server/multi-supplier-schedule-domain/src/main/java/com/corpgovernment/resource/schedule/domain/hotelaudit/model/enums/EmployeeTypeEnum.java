package com.corpgovernment.resource.schedule.domain.hotelaudit.model.enums;

import com.ctrip.corp.obt.generic.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 用户类型枚举
 * @create 2024-09-10 15:08
 */
@Getter
@AllArgsConstructor
public enum EmployeeTypeEnum {
    
    REGULAR_EMPLOYEE("regularEmployee", "0", "正式员工"),
    NO_REGULAR_EMPLOYEE("noRegularEmployee", "1", "非正式员工"),
    EXTERNAL_EMPLOYEE("externalEmployee", "2", "外部员工");
    
    private final String code;
    
    private final String codeNum;
    
    private final String info;
    
    private static final Map<String, EmployeeTypeEnum> map = new HashMap<>();
    
    static {
        for (EmployeeTypeEnum tmpEnum : values()) {
            map.put(tmpEnum.getCode(), tmpEnum);
            map.put(tmpEnum.getInfo(), tmpEnum);
            map.put(tmpEnum.getCodeNum(), tmpEnum);
        }
    }
    
    public static EmployeeTypeEnum getEnum(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return map.get(key);
    }
    
}
