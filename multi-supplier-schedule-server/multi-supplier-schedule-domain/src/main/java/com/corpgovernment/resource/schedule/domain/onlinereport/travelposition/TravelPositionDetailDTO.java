package com.corpgovernment.resource.schedule.domain.onlinereport.travelposition;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * @program: com.ctrip.corp.bi.online.report.platform.travelposition
 * @description:
 * @author: md_wang
 * @create: 2022-08-19 15:54
 **/
@Data
public class TravelPositionDetailDTO {

    /**
     * 因公 因私
     */
    @Column(name = "isSelf")
    @Type(value = Types.VARCHAR)
    private String isSelf;

    /**
     * 业务类型
     */
    @Column(name = "order_type")
    @Type(value = Types.VARCHAR)
    private String orderType;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    @Type(value = Types.BIGINT)
    private Long orderId;


    /**
     * 出行人姓名
     */
    @Column(name = "passengerName")
    @Type(value = Types.VARCHAR)
    private String passengerName;


    /**
     * 用户姓名（处理后的）
     */
    @Column(name = "processed_user_name")
    @Type(value = Types.VARCHAR)
    private String processedUserName;

    /**
     * 预订人id，已经删去空值
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;
    /**
     * 预订人姓名
     */
    @Column(name = "uidName")
    @Type(value = Types.VARCHAR)
    private String uidName;

    /**
     * 航班号/火车车次/酒店名称
     */
    @Column(name = "flightno_trainno_hotelname")
    @Type(value = Types.VARCHAR)
    private String flightnoTrainnoHotelname;

    /**
     * 出发省份
     */
    @Column(name = "startProvinceName")
    @Type(value = Types.VARCHAR)
    private String startProvinceName;

    /**
     * 出发城市
     */
    @Column(name = "startCityName")
    @Type(value = Types.VARCHAR)
    private String startCityName;

    /**
     * 到达省份
     */
    @Column(name = "endProvinceName")
    @Type(value = Types.VARCHAR)
    private String endProvinceName;

    /**
     * 到达城市
     */
    @Column(name = "endCityName")
    @Type(value = Types.VARCHAR)
    private String endCityName;

    @Column(name = "start_sub_trip_date")
    @Type(value = Types.VARCHAR)
    private String startSubTripDate;

    @Column(name = "end_sub_trip_date")
    @Type(value = Types.VARCHAR)
    private String endSubTripDate;


    @Column(name = "start_time")
    @Type(value = Types.VARCHAR)
    private String startTime;
    @Column(name = "end_time")
    @Type(value = Types.VARCHAR)
    private String endTime;

    @Column(name = "dept1")
    @Type(value = Types.VARCHAR)
    private String dept1;
    @Column(name = "dept2")
    @Type(value = Types.VARCHAR)
    private String dept2;
    @Column(name = "dept3")
    @Type(value = Types.VARCHAR)
    private String dept3;
    @Column(name = "dept4")
    @Type(value = Types.VARCHAR)
    private String dept4;
    @Column(name = "dept5")
    @Type(value = Types.VARCHAR)
    private String dept5;
    @Column(name = "dept6")
    @Type(value = Types.VARCHAR)
    private String dept6;
    @Column(name = "dept7")
    @Type(value = Types.VARCHAR)
    private String dept7;
    @Column(name = "dept8")
    @Type(value = Types.VARCHAR)
    private String dept8;
    @Column(name = "dept9")
    @Type(value = Types.VARCHAR)
    private String dept9;
    @Column(name = "dept10")
    @Type(value = Types.VARCHAR)
    private String dept10;

    @Column(name = "costcenter1")
    @Type(value = Types.VARCHAR)
    private String costcenter1;
    @Column(name = "costcenter2")
    @Type(value = Types.VARCHAR)
    private String costcenter2;
    @Column(name = "costcenter3")
    @Type(value = Types.VARCHAR)
    private String costcenter3;
    @Column(name = "costcenter4")
    @Type(value = Types.VARCHAR)
    private String costcenter4;
    @Column(name = "costcenter5")
    @Type(value = Types.VARCHAR)
    private String costcenter5;
    @Column(name = "costcenter6")
    @Type(value = Types.VARCHAR)
    private String costcenter6;

    /**
     * 出发省份id
     */
    @Column(name = "start_province_id")
    @Type(value = Types.INTEGER)
    private Integer startProvinceId;

    /**
     * 出发城市id
     */
    @Column(name = "start_city_id")
    @Type(value = Types.INTEGER)
    private Integer startCityId;

    /**
     * 到达省份id
     */
    @Column(name = "end_province_id")
    @Type(value = Types.INTEGER)
    private Integer endProvinceId;

    /**
     * 到达城市id
     */
    @Column(name = "end_city_id")
    @Type(value = Types.INTEGER)
    private Integer endCityId;

    /**
     * 母酒店id,关联多语言
     */
    @Column(name = "masterhotelid")
    @Type(value = Types.INTEGER)
    private Integer masterhotelid;
}
