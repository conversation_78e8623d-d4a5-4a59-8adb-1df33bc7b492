package com.corpgovernment.resource.schedule.domain.onlinereport.bo.common;

/**
 * 业务异常类
 * Created by zw_chen on 2018/4/3.
 */
public class BusinessException extends Exception {
    private final int code;
    private final String msg;

    public BusinessException(int code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
