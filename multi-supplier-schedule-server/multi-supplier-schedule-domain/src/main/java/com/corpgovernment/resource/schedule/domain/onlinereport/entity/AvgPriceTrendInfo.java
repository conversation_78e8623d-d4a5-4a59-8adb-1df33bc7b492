package com.corpgovernment.resource.schedule.domain.onlinereport.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import java.io.Serializable;
import java.math.BigDecimal;

@SuppressWarnings("all")
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE, setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({
    "rioRecent3m",
    "avgDeadPrice",
    "usageRate",
    "avgPrice",
    "avgPricePredictTrendInfo"
})
public class AvgPriceTrendInfo implements Serializable {
    private static final long serialVersionUID = 1L;





    public AvgPriceTrendInfo(
        Double rioRecent3m,
        BigDecimal avgDeadPrice,
        Double usageRate,
        Double avgPrice,
        AvgPricePredictTrendInfo avgPricePredictTrendInfo) {
        this.rioRecent3m = rioRecent3m;
        this.avgDeadPrice = avgDeadPrice;
        this.usageRate = usageRate;
        this.avgPrice = avgPrice;
        this.avgPricePredictTrendInfo = avgPricePredictTrendInfo;
    }

    public AvgPriceTrendInfo() {
    }

    /**
     * 未来3月均价相对过去3个月增长率
     */
    @JsonProperty("rioRecent3m")
    public Double rioRecent3m;

    @JsonProperty("avgDeadPrice")
    public BigDecimal avgDeadPrice;

    @JsonProperty("usageRate")
    public Double usageRate;

    @JsonProperty("avgPrice")
    public Double avgPrice;

    @JsonProperty("avgPricePredictTrendInfo")
    public AvgPricePredictTrendInfo avgPricePredictTrendInfo;

    /**
     * 未来3月均价相对过去3个月增长率
     */
    public Double getRioRecent3m() {
        return rioRecent3m;
    }

    /**
     * 未来3月均价相对过去3个月增长率
     */
    public void setRioRecent3m(final Double rioRecent3m) {
        this.rioRecent3m = rioRecent3m;
    }
    public BigDecimal getAvgDeadPrice() {
        return avgDeadPrice;
    }

    public void setAvgDeadPrice(final BigDecimal avgDeadPrice) {
        this.avgDeadPrice = avgDeadPrice;
    }
    public Double getUsageRate() {
        return usageRate;
    }

    public void setUsageRate(final Double usageRate) {
        this.usageRate = usageRate;
    }
    public Double getAvgPrice() {
        return avgPrice;
    }

    public void setAvgPrice(final Double avgPrice) {
        this.avgPrice = avgPrice;
    }
    public AvgPricePredictTrendInfo getAvgPricePredictTrendInfo() {
        return avgPricePredictTrendInfo;
    }

    public void setAvgPricePredictTrendInfo(final AvgPricePredictTrendInfo avgPricePredictTrendInfo) {
        this.avgPricePredictTrendInfo = avgPricePredictTrendInfo;
    }

    // Used by DatumWriter. Applications should not call.


    // Used by DatumReader. Applications should not call.






    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (getClass() != obj.getClass()) return false;

        final AvgPriceTrendInfo other = (AvgPriceTrendInfo)obj;
        return
            Objects.equal(this.rioRecent3m, other.rioRecent3m) &&
            Objects.equal(this.avgDeadPrice, other.avgDeadPrice) &&
            Objects.equal(this.usageRate, other.usageRate) &&
            Objects.equal(this.avgPrice, other.avgPrice) &&
            Objects.equal(this.avgPricePredictTrendInfo, other.avgPricePredictTrendInfo);
    }

    @Override
    public int hashCode() {
        int result = 1;

        result = 31 * result + (this.rioRecent3m == null ? 0 : this.rioRecent3m.hashCode());
        result = 31 * result + (this.avgDeadPrice == null ? 0 : this.avgDeadPrice.hashCode());
        result = 31 * result + (this.usageRate == null ? 0 : this.usageRate.hashCode());
        result = 31 * result + (this.avgPrice == null ? 0 : this.avgPrice.hashCode());
        result = 31 * result + (this.avgPricePredictTrendInfo == null ? 0 : this.avgPricePredictTrendInfo.hashCode());

        return result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("rioRecent3m", rioRecent3m)
            .add("avgDeadPrice", avgDeadPrice)
            .add("usageRate", usageRate)
            .add("avgPrice", avgPrice)
            .add("avgPricePredictTrendInfo", avgPricePredictTrendInfo)
            .toString();
    }
}
