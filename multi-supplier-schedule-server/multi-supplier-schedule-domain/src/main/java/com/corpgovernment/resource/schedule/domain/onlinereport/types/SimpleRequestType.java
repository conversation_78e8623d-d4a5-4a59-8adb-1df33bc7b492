package com.corpgovernment.resource.schedule.domain.onlinereport.types;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@SuppressWarnings("all")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SimpleRequestType")
public class SimpleRequestType implements Serializable {
    private static final long serialVersionUID = 1L;


    // Used by DatumWriter. Applications should not call.
    public Object get(int fieldPos) {
        switch (fieldPos) {
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in get()");
        }
    }

    // Used by DatumReader. Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int fieldPos, Object fieldValue) {
        switch (fieldPos) {
            default:
                throw new RuntimeException("Bad index " + fieldPos + " in put()");
        }
    }


}
