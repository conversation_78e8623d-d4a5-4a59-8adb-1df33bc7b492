package com.corpgovernment.resource.schedule.domain.onlinereport.position;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-08-18 11:27
 */
@Data
public class TripInfoDTO {
    // 国家id
    @Column(name = "sub_trip_city_countryid")
    @Type(value = Types.INTEGER)
    private Integer countryId;
    // 国家名称
    @Column(name = "sub_trip_city_countryname")
    @Type(value = Types.VARCHAR)
    private String countryName;
    // 城市id
    @Column(name = "sub_trip_city_id")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    // 城市名称
    @Column(name = "subTripCityName")
    @Type(value = Types.VARCHAR)
    private String cityName;

    // 正在出差
    @Column(name = "m_trip_cnt")
    @Type(value = Types.INTEGER)
    private Integer mCount;

    // 曾到过，已离开
    @Column(name = "h_trip_cnt")
    @Type(value = Types.INTEGER)
    private Integer hCount;

    // 将要去
    @Column(name = "w_trip_cnt")
    @Type(value = Types.INTEGER)
    private Integer wCount;

    // 将要去
    @Column(name = "w_htl_trip_cnt")
    @Type(value = Types.INTEGER)
    private Integer wHtlTripCnt;

    // 城市纬度
    @Column(name = "sub_trip_city_glat")
    @Type(value = Types.VARCHAR)
    private String cityGlat;

    // 城市经度
    @Column(name = "sub_trip_city_glon")
    @Type(value = Types.VARCHAR)
    private String cityGlon;
}
