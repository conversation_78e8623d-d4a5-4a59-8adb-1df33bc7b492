package com.corpgovernment.resource.schedule.domain.onlinereport.compliance;

import com.corpgovernment.resource.schedule.domain.onlinereport.Type;
import lombok.Data;

import javax.persistence.Column;
import java.sql.Types;

/*
 * <AUTHOR>
 * @date 2022/4/8 10:44
 * @Desc
 */
@Data
public class FlightRcTimesDTO {
    @Column(name = "lowRcTimes")
    @Type(value = Types.INTEGER)
    private Integer lowRcTimes;
    @Column(name = "classRcTimes")
    @Type(value = Types.INTEGER)
    private Integer classRcTimes;
    @Column(name = "agreementRcTimes")
    @Type(value = Types.INTEGER)
    private Integer agreementRcTimes;
    @Column(name = "preRcTimes")
    @Type(value = Types.INTEGER)
    private Integer preRcTimes;
    @Column(name = "timeRcTimes")
    @Type(value = Types.INTEGER)
    private Integer timeRcTimes;
    @Column(name = "refundRcTimes")
    @Type(value = Types.INTEGER)
    private Integer refundRcTimes;
    @Column(name = "discountRcTimes")
    @Type(value = Types.INTEGER)
    private Integer discountRcTimes;
    @Column(name = "orderCount")
    @Type(value = Types.INTEGER)
    private Integer orderCount;
    @Column(name = "rcTimes")
    @Type(value = Types.INTEGER)
    private Integer rcTimes;
}
