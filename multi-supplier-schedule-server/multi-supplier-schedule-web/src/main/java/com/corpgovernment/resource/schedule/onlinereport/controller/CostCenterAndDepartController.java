package com.corpgovernment.resource.schedule.onlinereport.controller;


import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.ViewResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.customreport.ReportStaticDataQueryService;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPrivilegeService;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.CostCenterAndDepartmentSearchRequestType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.CostCenterAndDepartmentSearchResponseType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.GetMainAccountIdsByCorpIdsRequestType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.OperatorCorpRangeRequestType;
import com.corpgovernment.resource.schedule.domain.onlinereport.search.OperatorCorpRangeResponseType;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import onlinereport.enums.SharkLocaleEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/21 15:51
 */
@RestController
@RequestMapping("/privilege")
public class CostCenterAndDepartController {

    /*@Autowired
    private OnlineReportWebSiteLogon logon;*/

    @Autowired
    private ICorpOnlineReportPrivilegeService corpOnlineReportPrivilegeService;

    @Autowired
    private ReportStaticDataQueryService reportStaticDataQueryService;

/*    @Autowired
    private ICorpOnlineReportEsService iCorpOnlineReportEsService;

    @Autowired
    private PrivilegeService privilegeService;*/

    /**
     * 搜索成本中心和部门
     * @param requestType
     * @return
     * @throws Exception
     */
    @RequestMapping("/costCenterAndDepart/search")
    public JSONResult<CostCenterAndDepartmentSearchResponseType> costCenterAndDepartmentSearch(@RequestBody CostCenterAndDepartmentSearchRequestType requestType) throws Exception {
        BaseUserInfo baseUserInfo = UserInfoContext.getContextParams(BaseUserInfo.class);
        String uid = baseUserInfo.getUid();

        requestType.setOperator(uid);
/*        if (CollectionUtils.isEmpty(requestType.getCorpIds())){
            OperatorCorpRangeRequestType operatorCorpRangeRequestType = new OperatorCorpRangeRequestType();
            operatorCorpRangeRequestType.setOperator(uid);
            OperatorCorpRangeResponseType operatorCorpRangeResponseType = corpOnlineReportPrivilegeService.searchCorpRange(operatorCorpRangeRequestType);
            if (operatorCorpRangeResponseType != null && CollectionUtils.isNotEmpty(operatorCorpRangeResponseType.getCorpList())){
                List<String> corpIds = operatorCorpRangeResponseType.getCorpList().stream().map(i->i.getCorpId()).collect(Collectors.toList());
                requestType.setCorpIds(corpIds);
            }
        }*/

        if (CollectionUtils.isEmpty(requestType.getCorpIds()) && StringUtils.isEmpty(requestType.getKeyword()) && !requestType.getSearchCk()) {
            return JSONResult.success(new CostCenterAndDepartmentSearchResponseType());
        }

        if (requestType.getSearchCk()){
            return JSONResult.success(corpOnlineReportPrivilegeService.costCenterAndDepartSearchByCk(requestType));
        }

        return JSONResult.success(corpOnlineReportPrivilegeService.costCenterAndDepartSearch(requestType));
    }
/*

    */
/**
     * 搜索成本中心和部门
     * @param requestType
     * @return
     * @throws Exception
     *//*

    @RequestMapping("/costCenterAndDepart/get")
    public CostCenterAndDepartmentSearchResponseType getCostCenterAndDeptByUid(@RequestBody CostCenterAndDepartmentSearchRequestType requestType) throws Exception {
        String uid = logon.getUidByUserInfoContext();
        requestType.setOperator(uid);
        if (CollectionUtils.isEmpty(requestType.getCorpIds())){
            OperatorCorpRangeRequestType operatorCorpRangeRequestType = new OperatorCorpRangeRequestType();
            operatorCorpRangeRequestType.setOperator(uid);
            OperatorCorpRangeResponseType operatorCorpRangeResponseType = corpOnlineReportPrivilegeService.searchCorpRange(operatorCorpRangeRequestType);
            if (operatorCorpRangeResponseType != null && CollectionUtils.isNotEmpty(operatorCorpRangeResponseType.getCorpList())){
                List<String> corpIds = operatorCorpRangeResponseType.getCorpList().stream().map(i->i.getCorpId()).collect(Collectors.toList());
                requestType.setCorpIds(corpIds);
            }
        }
        return corpOnlineReportPrivilegeService.getCostCenterAndDeptByUid(requestType);
    }

    @RequestMapping("/rank/search")
    public Object searchRank(@RequestBody GetMainAccountIdsByCorpIdsRequestType requestType) throws Exception {
        ViewResponse result = new ViewResponse();;
        String uid = logon.getUidByUserInfoContext();
        List<String> corpIds = requestType.getCorpIds();
        if (CollectionUtils.isEmpty(corpIds)){
            OperatorCorpRangeRequestType operatorCorpRangeRequestType = new OperatorCorpRangeRequestType();
            operatorCorpRangeRequestType.setOperator(uid);
            OperatorCorpRangeResponseType operatorCorpRangeResponseType = corpOnlineReportPrivilegeService.searchCorpRange(operatorCorpRangeRequestType);
            if (operatorCorpRangeResponseType != null && CollectionUtils.isNotEmpty(operatorCorpRangeResponseType.getCorpList())){
                corpIds = operatorCorpRangeResponseType.getCorpList().stream().map(i->i.getCorpId()).collect(Collectors.toList());
            }
        }
        List<Map<String, String>> rankList = reportStaticDataQueryService.queryCorpRanksFromSoa(corpIds, null, SharkLocaleEnum.ZH_CN.toString());
        result.setCode(0);
        result.setResponse(rankList);
        return result;
    }


    */
/**
     * 搜索协议航班
     * @param requestType
     * @return
     * @throws Exception
     *//*

    @RequestMapping("/agreementAir/search")
    public Map agreementAirSearch(@RequestBody GetAgreementAirRequestType requestType) throws Exception {
        Map result = new HashMap();
        Map resultStatus = new HashMap();
        resultStatus.put("code",0);
        resultStatus.put("message","");
        result.put("resultStatus",resultStatus);
        String uid = logon.getUidByUserInfoContext();
        if (requestType.getCorpIdList() == null || requestType.getCorpIdList().size() == 0){
            OperatorCorpRangeRequestType operatorCorpRangeRequestType = new OperatorCorpRangeRequestType();
            operatorCorpRangeRequestType.setOperator(uid);
            OperatorCorpRangeResponseType operatorCorpRangeResponseType = corpOnlineReportPrivilegeService.searchCorpRange(operatorCorpRangeRequestType);
            if (operatorCorpRangeResponseType != null && CollectionUtils.isNotEmpty(operatorCorpRangeResponseType.getCorpList())){
                List<String> corpIds = operatorCorpRangeResponseType.getCorpList().stream().map(i->i.getCorpId()).collect(Collectors.toList());
                requestType.setCorpIdList(corpIds);
            }
        }
        GetAgreementAirResponseType responseType = iCorpOnlineReportEsService.getAgreementAir(requestType);
        if (responseType != null && CollectionUtils.isNotEmpty(responseType.getAgreementAirInfos())){
            result.put("agreementAirInfos",responseType.getAgreementAirInfos());
//            result.put("totalCount",responseType.getTotalCount());
        }
        return result;
    }

    @RequestMapping("/costCenterAndDepart/searchv2")
    @ResponseTrace
    public Object costCenterAndDepartmentSearchV2(@RequestBody CorpBaseInfoConditionVO conditionVO) throws Exception {
        conditionVO.setOperator(logon.getUidByUserInfoContext());
        return privilegeService.getDeptAndCostcenterAlreadyBudget(conditionVO);
    }
*/

}
