package com.corpgovernment.resource.schedule.hotelaudit.job;

import cn.hutool.core.lang.Pair;
import com.corpgovernment.resource.schedule.hotelaudit.IHotelAuditService;
import com.corpgovernment.resource.schedule.hotelaudit.vo.AuditHotelReqVo;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/11/28
 */
@Component
@Slf4j
public class HotelAuditJob {
    
    @Resource
    private IHotelAuditService hotelAuditService;

    @XxlJob("multiSupplier.hotelAudit.manualAudit")
    public ReturnT<String> manualAudit(String param) {
        TraceContext.setRequestId(UUID.randomUUID().toString());
        log.info("手动酒店审计开始");
        // 获取参数
        AuditHotelReqVo auditHotelReqVo = buildAuditHotelReqVo(param);
        // 审计
        hotelAuditService.auditHotel(auditHotelReqVo);
        return ReturnT.SUCCESS;
    }
    
    @XxlJob("multiSupplier.hotelAudit.autoAuditEveryHalfHour")
    public ReturnT<String> autoAuditEveryHalfHour(String param) {
        TraceContext.setRequestId(UUID.randomUUID().toString());
        log.info("每半个小时自动酒店审计开始");
        if (StringUtils.isBlank(param)) {
            return ReturnT.SUCCESS;
        }
        // 获取参数
        List<String> tenantIdList = null;
        try {
            tenantIdList = JsonUtils.parse(param, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            log.error("参数解析失败", e);
        }
        if (CollectionUtils.isEmpty(tenantIdList)) {
            return ReturnT.SUCCESS;
        }
        
        // 起止时间
        Pair<String, String> halfHour = getPreHalfHour();
        
        // 审计
        AuditHotelReqVo auditHotelReqVo = AuditHotelReqVo.builder()
                .timeRangeMode(AuditHotelReqVo.TimeRangeMode.builder()
                        .startOrderTime(halfHour.getKey())
                        .endOrderTime(halfHour.getValue())
                        .tenantIdList(tenantIdList)
                        .build()).build();
        log.info("auditHotelReqVo={}", JsonUtils.toJsonString(auditHotelReqVo));
        hotelAuditService.auditHotel(auditHotelReqVo);
        return ReturnT.SUCCESS;
    }
    
    private static Pair<String, String> getPreHalfHour() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start = now.truncatedTo(ChronoUnit.HOURS)
                .plusMinutes(((now.getMinute() - 30) / 30) * 30);
        LocalDateTime end = start.plusMinutes(30);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startStr = start.format(formatter);
        String endStr = end.format(formatter);
        return new Pair<>(startStr, endStr);
    }
    
    private AuditHotelReqVo buildAuditHotelReqVo(String param) {
        if (StringUtils.isBlank(param)) {
            return null;
        }
        
        try {
            return JsonUtils.parse(param, AuditHotelReqVo.class);
        } catch (Exception e) {
            log.error("参数解析失败", e);
            return null;
        }
    }

}
