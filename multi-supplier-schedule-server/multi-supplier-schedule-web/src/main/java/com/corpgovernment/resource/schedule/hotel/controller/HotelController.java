package com.corpgovernment.resource.schedule.hotel.controller;

import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.hotel.dao.mapper.HotelCityMapper;
import com.corpgovernment.resource.schedule.hotel.impl.HotelIdTaskService;
import com.corpgovernment.resource.schedule.hotel.impl.HotelMatchTaskService;
import com.corpgovernment.resource.schedule.hotel.impl.HotelRoomMatchTaskService;
import com.corpgovernment.resource.schedule.hotel.impl.HotelStaticResourceTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/10/27
 */
@RestController
@RequestMapping("/hotel")
public class HotelController {

    @Autowired
    private HotelStaticResourceTaskService hotelStaticResourceTaskService;

    @Autowired
    private HotelRoomMatchTaskService roomMatchTaskService;

    @Autowired
    private HotelMatchTaskService hotelMatchTaskService;

    @Autowired
    private HotelIdTaskService hotelIdTaskService;
    @Resource
    private HotelCityMapper hotelCityMapper;

    @PostMapping("/createHotelIdTask")
    public JSONResult<String> createHotelIdTask() {
        hotelIdTaskService.create();
        return JSONResult.success("createHotelIdTask");
    }

    @PostMapping("/executeHotelIdTask")
    public JSONResult<String> executeHotelIdTask() {
        hotelIdTaskService.execute();
        return JSONResult.success("executeHotelIdTask");
    }

    @PostMapping("/executeHotelStaticResourceTask")
    public JSONResult<String> executeHotelStaticResourceTask() {
        hotelStaticResourceTaskService.execute();
        return JSONResult.success("executeHotelStaticResourceTask");
    }

    @PostMapping("/executeHotelMatchTask")
    public JSONResult<String> executeHotelMatchTask() {
        hotelMatchTaskService.execute();
        return JSONResult.success("executeHotelMatchTask");
    }

    @PostMapping("/executeRoomMatchTask")
    public JSONResult<String> executeRoomMatchTask() {
        roomMatchTaskService.execute();
        return JSONResult.success("executeRoomMatchTask");
    }

    @PostMapping("/resetTask")
    public JSONResult<String> resetTask() {
        hotelIdTaskService.retryTask(3);
        return JSONResult.success("resetTask");
    }

}
