package com.corpgovernment.resource.schedule.geography.controller;

import cn.hutool.log.Log;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.geography.service.GeographyTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> zhang
 * @date 2023/12/26 10:55
 */
@Slf4j
@RestController
@RequestMapping("/multiSupplier/schedule/geography")
public class GeographyController {

    @Autowired
    private GeographyTaskService geographyTaskService;

    /**
     * 大任务初始化
     */
//    @PostMapping("/pullDomesticZoneInfo")
//    public JSONResult<String> pullDomesticZoneInfo() {
//        geographyTaskService.pullDomesticZoneInfo(false);
//        return JSONResult.success("executeGeographyInitTask");
//    }

}
