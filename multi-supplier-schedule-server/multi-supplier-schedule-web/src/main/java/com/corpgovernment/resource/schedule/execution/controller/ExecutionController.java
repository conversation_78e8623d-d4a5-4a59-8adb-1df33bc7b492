package com.corpgovernment.resource.schedule.execution.controller;

import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.execution.IExecutionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-07 00:15
 */
@RestController
@RequestMapping("/execution")
public class ExecutionController {

    @Autowired
    private IExecutionService executionService;

    @PostMapping("startTask")
    public JSONResult<String> startTask() {
        executionService.startTask();
        return new JSONResult<>("");
    }

    @PostMapping("submitExecutionPlan")
    public JSONResult<String> submitExecutionPlan() {
        executionService.submitExecutionPlan();
        return new JSONR<PERSON>ult<>("");
    }

    @PostMapping("retryExecutionUnit")
    public JSONResult<String> retryExecutionUnit() {
        executionService.retryExecutionUnit();
        return new JSONResult<>("");
    }


}
