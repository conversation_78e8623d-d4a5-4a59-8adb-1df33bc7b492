package com.corpgovernment.resource.schedule;

import com.corpgovernment.api.supplier.soa.SupplierCompanyClient;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.ctrip.corp.obt.generic.utils.CollectionUtils;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;


@Service
@Slf4j
@RequiredArgsConstructor
public class UserOpenCardSyncJob {

    private final SupplierCompanyClient supplierCompanyClient;

    @XxlJob("jobcenter.multisupplier.sync.useropencard")
    public ReturnT<String> syncUserOpenCard(String param) {
        log.info("同步用户开卡信息");
        if (StringUtils.isBlank(param)) {
            return ReturnT.FAIL;
        }

        SyncUserOpenCardRequest req = JsonUtils.parse(param, SyncUserOpenCardRequest.class);
        if (req == null || req.getDetails() == null || req.getDetails().isEmpty()) {
            return ReturnT.FAIL;
        }

        for (SyncUserOpenCardRequestDetail reqDetail : req.getDetails()) {
            log.info("开始同步租户{}的用户开卡信息", JsonUtils.toJsonString(reqDetail));
            try {

                String tenantId = reqDetail.getTenantId();
                if(StringUtils.isBlank(tenantId)){
                    log.error("租户ID为空");
                    continue;
                }

                TenantContext.setTenantId(tenantId);
                TenantContext.setClientId("UserOpenCardSyncJobClient");
                TraceContext.setRequestId(String.valueOf(UUID.randomUUID()));
                //同步Tenant下所有的corp

                if(CollectionUtils.isNotEmpty(reqDetail.getCorpIds())){
                    reqDetail.getCorpIds().forEach(corpId -> {
                        try {
                            log.info("开始同步租户{}的企业{}", tenantId, corpId);
                            supplierCompanyClient.syncUserOpenCards(corpId);
                        } catch (Exception e) {
                            log.error("同步租户{}的企业{}失败", tenantId, corpId, e);
                        }
                    });
                }else{
                    try {
                        log.info("开始同步租户{}全部开卡数据", tenantId);
                        supplierCompanyClient.syncUserOpenCards(null);
                    } catch (Exception e) {
                        log.error("同步租户{}全部开卡数据异常", tenantId, e);
                    }
                }

            } finally {
               TenantContext.unset();
            }
        }

        return ReturnT.SUCCESS;
    }

    @Data
    public static class SyncUserOpenCardRequest{
        private List<SyncUserOpenCardRequestDetail> details;
    }

    @Data
    public static class SyncUserOpenCardRequestDetail{
        private String tenantId;
        private List<String> corpIds;
    }

}
