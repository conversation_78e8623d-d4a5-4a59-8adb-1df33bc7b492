package com.corpgovernment.resource.schedule.onlinereport.controller;


import com.corpgovernment.api.organization.dto.org.OrgCommonSaveVO;
import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.UerDefinedResponse;
import com.corpgovernment.resource.schedule.domain.onlinereport.common.QConfigUtils;
import com.corpgovernment.resource.schedule.domain.onlinereport.constant.CommonConst;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpReportCustomColumnPO;
import com.corpgovernment.resource.schedule.domain.onlinereport.dataaccess.PO.CorpreportDownloadTaskPO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.SearchDeptAndCostcneterEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.rpc.ICorpOnlineReportPrivilegeService;
import com.corpgovernment.resource.schedule.domain.onlinereport.task.CorpreportDownloadTaskService;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.JsonUtils;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.CorpReportCustomColumnService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.ReportLibAdaptorService;
import com.corpgovernment.resource.schedule.onlinereport.reportlib.reportfactory.ReportService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import onlinereport.enums.ReportTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Auther:abguo
 * Date:2019/7/29
 * Description:报表库
 * Project:onlinereportweb
 */
@Controller
@RequestMapping({"new/reportLib", "new/reportDownload"})
@Slf4j
public class ReportLibController extends BaseController {

    protected static final String LOGTITLE = ReportLibController.class.getSimpleName();
    private final static int EXPIREDAYS = 2; //默认过期天数
    @Autowired
    private CorpreportDownloadTaskService corpreportDownloadTaskService;
    @Autowired
    private CorpReportCustomColumnService reportColumnService;
    @Autowired
    private ReportLibAdaptorService reportLibAdaptorService;
    @Autowired
    private ICorpOnlineReportPrivilegeService corpOnlineReportPrivilegeService;

    @Autowired
    private ReportService reportService;

    private String lang = CommonConst.ZH_CN;

    private String runAsUid = "";

    @RequestMapping(value = "/saveCustomColumns", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    // @ResponseTrace
    public Object saveCustomColumns(@RequestBody Map input) throws BusinessException, SQLException {
        String reportKey = (String) input.get("reportKey");
        String reportColumns = (String) input.get("reportColumns");
        reportColumnService.insert(runAsUid, reportKey, reportColumns);
        return PageErrorCodeEnum.SUCESS.getDescription();
    }

    @RequestMapping(value = "/getCustomColumns", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    // @ResponseTrace
    public Object getCustomColumns(@RequestBody Map input) throws SQLException {
        Map map = new HashMap();
        String reportKey = null;
        if (input.containsKey("reportKey")) {
            reportKey = (String) input.get("reportKey");
            CorpReportCustomColumnPO corpReportCustomColumnPO = reportColumnService.getCustomColumn(runAsUid, reportKey);
            if (corpReportCustomColumnPO != null) {
                map.put("reportColumns", corpReportCustomColumnPO.getCustomColumns());
                map.put("customColumnId", corpReportCustomColumnPO.getCustomColumnId());
                map.put("reportKey", corpReportCustomColumnPO.getReportKey());
            }
        }
        return map;
    }


    //订单明细查询
    @RequestMapping(value = "/queryNewOrderDetails", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    // @ResponseTrace
    public Object queryNewOrderDetails(@RequestBody BaseQueryConditionBO request) throws BusinessException {
        String runAsUid = "";
        convertOrg(request);
        return JSONResult.success(reportLibAdaptorService.adaptor(runAsUid, request, lang));
    }

    @RequestMapping("export")
    public void exportReport(@RequestBody BaseQueryConditionBO request, HttpServletResponse response,
                             BaseUserInfo baseUserInfo) throws Exception {
        String lang = "zh_CN";
        request.setLang(lang);
        // deptList 只保留一个即可
        convertOrg(request);
        reportService.exportReport(request, response, baseUserInfo);
    }

    @RequestMapping("/createNewDownLoadTask")
    @ResponseBody
    // @ResponseTrace
    public Object createNewDownLoadTask(@RequestBody BaseQueryConditionBO request) throws BusinessException, JsonProcessingException, SQLException {
        if (isInDownloadTaskBlackList(runAsUid)) {
            throw new BusinessException(PageErrorCodeEnum.Unauthorized.getValue(), PageErrorCodeEnum.Unauthorized.getDescription());
        }
        String lang = "";
        // deptList 只保留一个即可
        convertOrg(request);
        request.setLang(lang);
        return corpreportDownloadTaskService.addTask(request, lang, runAsUid);
    }

    private void convertOrg(BaseQueryConditionBO request) {
        // deptList 只保留一个即可
        if(request.getBaseQueryCondition()!=null && CollectionUtils.isNotEmpty(request.getBaseQueryCondition().getDeptList())){
            SearchDeptAndCostcneterEntity deptList = request.getBaseQueryCondition().getDeptList().get(0);
            if(CollectionUtils.isNotEmpty(deptList.getOrgList())||CollectionUtils.isNotEmpty(deptList.getCurrentList())){
                OrgCommonSaveVO orgList = new OrgCommonSaveVO();
                orgList.setOrgList(deptList.getOrgList());
                orgList.setCurrentList(deptList.getCurrentList());
                List<String> orgIdList = corpOnlineReportPrivilegeService.getOrgs(orgList);
                deptList.setVals(orgIdList);
                deptList.setKey(1);
                deptList.setSelectAll(false);
            }
            request.getBaseQueryCondition().setDeptList(Arrays.asList(deptList));
        }
    }

    @RequestMapping("/queryDownLoadTask")
    @ResponseBody
    // @ResponseTrace
    public Object queryDownLoadTask(@RequestBody Map input) throws BusinessException {
        Integer taskId = (Integer) input.get("taskId");
        if (Objects.isNull(taskId) || StringUtils.isEmpty(runAsUid)) {
            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), PageErrorCodeEnum.InvalidOperation.getDescription());
        }
        CorpreportDownloadTaskPO corpreportDownloadTaskPO = corpreportDownloadTaskService.queryByPk(taskId.longValue());
        if (corpreportDownloadTaskPO == null || !StringUtils.equalsIgnoreCase(corpreportDownloadTaskPO.getUid(), runAsUid)) {
            throw new BusinessException(PageErrorCodeEnum.NO_DATA.getValue(), PageErrorCodeEnum.NO_DATA.getDescription());
        }
        return convertMap(corpreportDownloadTaskPO);
    }

    @RequestMapping("/delDownLoadTask")
    @ResponseBody
    // @ResponseTrace
    public Object delDownLoadTask(@RequestBody Map input) throws BusinessException, SQLException {
        /**
         * 在Spring MVC中，如果你的入参是一个Map类型，并且Map的值类型是Integer，但你尝试将其转换为Long类型时，可能会报错。
         * 这是因为Spring MVC默认使用的数据绑定器无法自动将Integer类型转换为Long类型。
         * 要解决这个问题，你可以自定义一个数据转换器来处理这种类型转换。以下是一个示例：
         * 1.创建一个自定义的数据转换器类，实现Converter接口：
         * import org.springframework.core.convert.converter.Converter;
         *
         * public class IntegerToLongConverter implements Converter<Integer, Long> {
         *
         *     @Override
         *     public Long convert(Integer source) {
         *         return source.longValue();
         *     }
         * }
         * 2.在Spring MVC的配置类中注册这个自定义的数据转换器：
         * import org.springframework.context.annotation.Configuration;
         * import org.springframework.format.FormatterRegistry;
         * import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
         *
         * @Configuration
         * public class WebMvcConfig implements WebMvcConfigurer {
         *
         *     @Override
         *     public void addFormatters(FormatterRegistry registry) {
         *         registry.addConverter(new IntegerToLongConverter());
         *     }
         * }
         * 在上述代码中，我们创建了一个IntegerToLongConverter类，实现了Converter接口，并重写了convert方法，将Integer类型转换为Long类型。
         * 然后，在WebMvcConfig类中，我们通过重写addFormatters方法，将自定义的数据转换器注册到FormatterRegistry中。
         * 这样，当Spring MVC接收到Map类型的入参，并尝试将其中的Integer值转换为Long类型时，就会使用我们自定义的数据转换器进行转换，避免报错。
         */
        Integer taskId = (Integer) input.get("taskId");
        if (Objects.isNull(taskId) || StringUtils.isEmpty(runAsUid)) {
            throw new BusinessException(PageErrorCodeEnum.InvalidOperation.getValue(), PageErrorCodeEnum.InvalidOperation.getDescription());
        }
        return corpreportDownloadTaskService.delete(taskId.longValue(), runAsUid);
    }

    @RequestMapping("/getDownLoadTaskList")
    @ResponseBody
    public Object getDownLoadTaskList() {
        UerDefinedResponse definedResponse = new UerDefinedResponse();
        try {
            String a = QConfigUtils.getNullDefaultValue("expireDays", String.valueOf(EXPIREDAYS));
            int expireDay = Integer.valueOf(a);
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1 * expireDay);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 1);
            Timestamp timestamp = new Timestamp(calendar.getTimeInMillis());
            List<CorpreportDownloadTaskPO> list = corpreportDownloadTaskService.queryTask(runAsUid, timestamp);
            List<Map<String, String>> result = convert(list);
            definedResponse.sucess(result);
            log.info("getDownLoadTaskList", String.format("response:%s", JsonUtils.objectToString(definedResponse)));
        } catch (RuntimeException e) {
            log.warn(LOGTITLE, e);
            definedResponse.fail(e.getMessage());
        } catch (Exception e) {
            log.error(LOGTITLE, e);
            definedResponse.fail(e.getMessage());
        }
        return definedResponse;
    }

    private List<Map<String, String>> convert(List<CorpreportDownloadTaskPO> list) {
        List<Map<String, String>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (CorpreportDownloadTaskPO item : list) {
            result.add(convertMap(item));
        }
        return result;
    }

    private Map convertMap(CorpreportDownloadTaskPO item) {
        String downloadUrl = QConfigUtils.getValue("config.properties", "risk_download_url");

        Calendar calendar = Calendar.getInstance();
        Map<String, Object> map = new HashMap<>();
        calendar.setTimeInMillis(item.getDatachangeCreatetime().getTime());
        calendar.add(Calendar.DAY_OF_MONTH, 2);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        map.put("taskId", item.getTaskId());
        map.put("reportType", item.getReportType());
        map.put("reportName", getReportName(item));
        map.put("taskStatus", item.getTaskStatus().toString());
        map.put("expireTime", calendar.getTimeInMillis());
        if (ReportTypeEnum.OneKeyExportPPT.getValue() == item.getReportType() && item.getTaskStatus() != 2) {
            map.put("downLoadUrl", StringUtils.EMPTY);
        } else if (ReportTypeEnum.OneKeyExportPPT.getValue() == item.getReportType() && item.getTaskStatus() == 2) {
            map.put("downLoadUrl", downloadUrl + item.getDownloadUrl());
            map.put("reportName", item.getDownloadUrl());
        } else {
            map.put("downLoadUrl", item.getDownloadUrl());
        }
        map.put("createTime", String.valueOf(item.getDatachangeCreatetime().getTime()));
        map.put("conditions", item.getConditions());
        map.put("retryTimes", Optional.ofNullable(item.getRetryTimes()).orElse(0));
        return map;
    }

    /**
     * 是否在生成下载任务黑名单中
     *
     * @param uid
     * @return
     */
    private boolean isInDownloadTaskBlackList(String uid) {
        if (StringUtils.isEmpty(uid)) {
            return false;
        }
        String createdownloadTaskBlackList = QConfigUtils.getValue("createdownloadTaskBlackList");
        if (StringUtils.isNotEmpty(createdownloadTaskBlackList) && StringUtils.isNotBlank(createdownloadTaskBlackList)) {
            List blackList = Arrays.asList(createdownloadTaskBlackList.split(","));
            return blackList.contains(uid);
        }
        return false;
    }

    private String getReportName(CorpreportDownloadTaskPO corpreportDownloadTaskPO) {
        String url = corpreportDownloadTaskPO.getDownloadUrl();
        if (StringUtils.isNotEmpty(url) && StringUtils.isNotBlank(url)) {
            String[] urlParts = url.split("/");
            return urlParts[urlParts.length - 1];
        }
        return corpreportDownloadTaskService.getReportNameByPrefix(corpreportDownloadTaskPO.getConditions(), corpreportDownloadTaskPO.getTaskId(), corpreportDownloadTaskPO.getReportType(), corpreportDownloadTaskPO.getDatachangeCreatetime().getTime());
    }



}
