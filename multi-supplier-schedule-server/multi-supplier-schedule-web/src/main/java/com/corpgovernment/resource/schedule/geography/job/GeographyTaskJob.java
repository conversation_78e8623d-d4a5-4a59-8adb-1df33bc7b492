package com.corpgovernment.resource.schedule.geography.job;

import com.corpgovernment.resource.schedule.geography.service.GeographyTaskService;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zhang
 * @date 2023/12/26 10:58
 */
@Slf4j
@Component
public class GeographyTaskJob {

    @Autowired
    private GeographyTaskService geographyTaskService;

    @XxlJob("jobcenter.multisupplier.geographyTaskInit")
    public ReturnT<String> executeGeographyInitTask(String param) {
        log.info("begin jobcenter.multisupplier.geographyTaskInit,param:{}",param);
        geographyTaskService.initGeographyTask();
        log.info("end jobcenter.multisupplier.geographyTaskInit");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.geographyTaskInitByCountryIds")
    public ReturnT<String> executeGeographyInitTaskByCountryIds(String param) {
        log.info("begin jobcenter.multisupplier.geographyTaskInitByCountryIds,param:{}",param);
        geographyTaskService.initRedisGeographyInfoByCountryIds();
        log.info("end jobcenter.multisupplier.geographyTaskInitByCountryIds");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.ctripAuthTask")
    public ReturnT<String> executeCtripAuthTask(String param) {
        log.info("begin jobcenter.multisupplier.authTask param:{}",param);
        geographyTaskService.pullAuthTicket();
        log.info("end jobcenter.multisupplier.authTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.countryTask")
    public ReturnT<String> executeCountryTask(String param) {
        log.info("begin jobcenter.multisupplier.countryTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullCountryInfo(update);
        log.info("end jobcenter.multisupplier.countryTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.provinceTask")
    public ReturnT<String> executeProvinceTask(String param) {
        log.info("begin jobcenter.multisupplier.provinceTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullProvinceInfo(update);
        log.info("end jobcenter.multisupplier.provinceTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.cityTask")
    public ReturnT<String> executeCityTask(String param) {
        log.info("begin jobcenter.multisupplier.cityTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullCityInfo(update);
        log.info("end jobcenter.multisupplier.cityTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.airportTask")
    public ReturnT<String> executeAirportTask(String param) {
        log.info("begin jobcenter.multisupplier.airportTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullAirportInfo(update);
        log.info("end jobcenter.multisupplier.airportTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.trainStationTask")
    public ReturnT<String> executeTrainStationTask(String param) {
        log.info("begin jobcenter.multisupplier.trainStationTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullTrainStationInfo(update);
        log.info("end jobcenter.multisupplier.trainStationTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.busStationTask")
    public ReturnT<String> executeBusStationTask(String param) {
        log.info("begin jobcenter.multisupplier.busStationTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullBusStationInfo(update);
        log.info("end jobcenter.multisupplier.busStationTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.domesticZoneTask")
    public ReturnT<String> executeDomesticZoneTask(String param) {
        log.info("begin jobcenter.multisupplier.domesticZoneTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullDomesticZoneInfo(update);
        log.info("end jobcenter.multisupplier.domesticZoneTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.timeZoneTask")
    public ReturnT<String> executeTimeZoneTask(String param) {
        log.info("begin jobcenter.multisupplier.timeZoneTask param:{}", param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullTimeZoneInfo(update);
        log.info("end jobcenter.multisupplier.timeZoneTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.corpBrandTask")
    public ReturnT<String> executeCorpBrandTask(String param) {
        log.info("begin jobcenter.multisupplier.corpBrandTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullCorpBrandInfo(update);
        log.info("end jobcenter.multisupplier.corpBrandTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.metroLineTask")
    public ReturnT<String> executeMetroLineTask(String param) {
        log.info("begin jobcenter.multisupplier.metroLineTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullMetroLineInfo(update);
        log.info("end jobcenter.multisupplier.metroLineTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.cityLandmarkTask")
    public ReturnT<String> executeCityLandmarkTask(String param) {
        log.info("begin jobcenter.multisupplier.cityLandmarkTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullCityLandmarkInfo(update);
        log.info("end jobcenter.multisupplier.cityLandmarkTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.hotelCityTask")
    public ReturnT<String> executeHotelCityTask(String param) {
        log.info("begin jobcenter.multisupplier.hotelCityTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullHotelCityInfo(update);
        log.info("end jobcenter.multisupplier.hotelCityTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.meiYaCityTask")
    public ReturnT<String> executeMeiYaCityTask(String param) {
        log.info("begin jobcenter.multisupplier.meiYaCityTask param:{}",param);
        boolean update = StringUtils.isBlank(param);
        geographyTaskService.pullMeiYaCityInfo(update);
        log.info("end jobcenter.multisupplier.meiYaCityTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.geographyErrorTask")
    public ReturnT<String> executeGeographyErrorTask(String param) {
        log.info("begin jobcenter.multisupplier.executeGeographyErrorTask param:{}",param);
        geographyTaskService.executeGeographyError(param);
        log.info("end jobcenter.multisupplier.executeGeographyErrorTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.updateGeographyInfoTask")
    public ReturnT<String> updateGeographyInfoByTypeAndId(String param) {
        log.info("begin jobcenter.multisupplier.updateGeographyInfoTask param:{}",param);
        geographyTaskService.updateGeographyInfoByTypeAndId(param);
        log.info("end jobcenter.multisupplier.updateGeographyInfoTask");
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.mappingCityInfoTask")
    public ReturnT<String> mappingCityInfo(String param) {
        log.info("begin jobcenter.multisupplier.mappingCityInfoTask param:{}",param);
        geographyTaskService.mappingCityInfo(param);
        log.info("end jobcenter.multisupplier.mappingCityInfoTask");
        return ReturnT.SUCCESS;
    }

}
