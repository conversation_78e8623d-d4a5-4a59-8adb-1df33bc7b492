package com.corpgovernment.resource.schedule.redemptioncode.job;

import com.corpgovernment.resource.schedule.execution.ExecutionService;
import com.corpgovernment.resource.schedule.redemptioncode.RedemptionCodeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedemptionCodeJob {

    @Resource
    private RedemptionCodeService redemptionCodeService;

    /**
     * 定时发放兑换码
     */
    @XxlJob("jobCenter.job.sendRedemptionCode")
    public ReturnT<String> sendRedemptionCode(String param) {
        XxlJobLogger.log("start send redemption code");
        log.info("start send redemption code");
        redemptionCodeService.sendRedemptionCode();
        return ReturnT.SUCCESS;
    }
}
