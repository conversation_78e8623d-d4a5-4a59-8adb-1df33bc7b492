package com.corpgovernment.resource.schedule.management;

import com.corpgovernment.management.ManagementReq;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RestController
@RequestMapping("/management")
public class ManagementDailyWeeklyJob {

    @Resource
    private ManagementDailyWeeklyService managementDailyWeeklyService;


    /**
     * 管控日报
     */
    @XxlJob("multiSupplier.job.managementDaily")
    @RequestMapping("/managementDaily")
    public ReturnT<String> managementDaily(String param) throws JsonProcessingException {
        XxlJobLogger.log("start management daily" + param);
        log.info("start management daily {}", param);

        if (StringUtils.isBlank(param)) {
            return ReturnT.FAIL;
        }

        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        ManagementReq req = mapper.readValue(param, ManagementReq.class);
        if (req.getStartTime() == null || req.getEndTime() == null) {
            req.setStartTime(LocalDate.now());
            req.setEndTime(LocalDate.now());
        }


        // FIXME 发送邮件目前必须先设置
        TenantContext.setTenantId(req.getTenantId());
        TenantContext.setClientId(req.getClientId());

        managementDailyWeeklyService.managementDaily(req);

        return ReturnT.SUCCESS;
    }

    /**
     * 管控周报
     */
    @XxlJob("multiSupplier.job.managementWeekly")
    @RequestMapping("/managementWeekly")
    public ReturnT<String> managementWeekly(String param) throws JsonProcessingException {
        XxlJobLogger.log("start management weekly" + param);
        log.info("start management weekly {}", param);
        if (StringUtils.isBlank(param)) {
            return ReturnT.FAIL;
        }

        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        ManagementReq req = mapper.readValue(param, ManagementReq.class);
        if (req.getStartTime() == null || req.getEndTime() == null) {
            req.setStartTime(LocalDate.now().minusDays(7));
            req.setEndTime(LocalDate.now());
        }

        // FIXME 发送邮件目前必须先设置
        TenantContext.setTenantId(req.getTenantId());
        TenantContext.setClientId(req.getClientId());

        managementDailyWeeklyService.managementWeekly(req);

        return ReturnT.SUCCESS;
    }

}
