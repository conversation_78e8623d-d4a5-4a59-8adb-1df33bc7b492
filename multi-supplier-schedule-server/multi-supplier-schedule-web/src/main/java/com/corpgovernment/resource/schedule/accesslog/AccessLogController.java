package com.corpgovernment.resource.schedule.accesslog;

import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.domain.accesslog.model.req.ReportQueryReq;
import com.corpgovernment.resource.schedule.domain.accesslog.model.req.ReportReq;
import com.corpgovernment.resource.schedule.domain.accesslog.model.resp.ReportQueryResp;
import com.corpgovernment.resource.schedule.domain.accesslog.service.AccessLogService;
import com.ctrip.corp.obt.generic.utils.StringUtils;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 高频问题访问日志。数据存放在StarRocks中。目前只是BYD租户使用。
 *
 * <AUTHOR> Smith
 */
@RestController
@RequestMapping("/accessLog")
@Slf4j
public class AccessLogController {

    @Resource
    private AccessLogService accessLogService;

    /**
     * 上报高频问题访问日志。直接使用SR的stream_load进行上报。
     *
     * <pre>
     *     curl --location-trusted -u root:xxx -H "label:label_20241115_test_json_040"
     * -H "Expect:100-continue"
     * -H "column_separator:,"
     * -H "timeout:100"
     * -H "max_filter_ratio:0.2"
     * -H "columns:access_time,uid,question_id,type,source"
     * -H "Content-Type: application/json"
     * -d '2024-11-11 15:15:00,user020,101,hotel,WEB'
     * -XPUT http://10.121.5.105:8030/api/dw_corpgovernmentdb/ods_faq_access_log/_stream_load
     * </pre>
     */
    @RequestMapping("/report")
    public void report(@RequestBody @Valid ReportReq reportReq) throws URISyntaxException {
        Config appConfig = ConfigService.getAppConfig();

        String label = "label_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));

        String user = appConfig.getProperty("BYD.accessLog.basicAuth.user", null);
        String password = appConfig.getProperty("BYD.accessLog.basicAuth.password", null);
        if (StringUtils.isBlank(user) || StringUtils.isBlank(password)) {
            throw new RuntimeException("BYD.accessLog.basicAuth.user or BYD.accessLog.basicAuth.password is empty");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(user, password);

        headers.set("label", label);
        headers.set("Expect", "100-continue");
        headers.set("column_separator", ",");
        headers.set("timeout", "100");
        headers.set("max_filter_ratio", "0.2");
        headers.set("columns", "access_time,uid,question_id,type,source");
        headers.setContentType(MediaType.APPLICATION_JSON);

        String requestBody = reportReq.getAccessTime() + "," + reportReq.getUid() + "," + reportReq.getQuestionId()
                + "," + reportReq.getType() + "," + reportReq.getSource();
        log.info("the request body is: {}", requestBody);
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

        String url = appConfig.getProperty("BYD.accessLog.url", null);
        if (StringUtils.isBlank(url)) {
            throw new RuntimeException("BYD.accessLog.url is empty");
        }

        RestTemplate restTemplate = new RestTemplate(new HttpComponentsClientHttpRequestFactory());

        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.PUT,
                requestEntity,
                String.class);

        log.info("Response Status Code: {}, Response Body: {}", response.getStatusCode(), response.getBody());

        if (!response.getStatusCode().is3xxRedirection()) {
            log.info("Response Status Code: {}, Response Body: {}", response.getStatusCode(), response.getBody());
            return;
        }

        URI redirectUri = response.getHeaders().getLocation();
        if (redirectUri == null) {
            log.error("Redirect location is null, response status code: {}, response body: {}",
                    response.getStatusCode(), response.getBody());
            return;
        }

        log.info("Redirecting to: {}", redirectUri);
        response = restTemplate.exchange(redirectUri, HttpMethod.PUT, requestEntity, String.class);
        log.info("Redirected Response Status Code: {}, Redirected Response Body: {}", response.getStatusCode(), response.getBody());
    }

    /**
     * 查询高频问题访问日志
     */
    @RequestMapping("/query")
    public JSONResult<ReportQueryResp> query(@RequestBody @Valid ReportQueryReq reportQueryReq) {
        ReportQueryResp reportQueryResp = accessLogService.query(reportQueryReq.setTopNum(5));
        return JSONResult.success(reportQueryResp);
    }

    /**
     * 导出高频问题访问日志
     */
    @RequestMapping("/export")
    public void export(@RequestBody @Valid ReportQueryReq reportQueryReq, HttpServletResponse response) {
        accessLogService.export(reportQueryReq, response);
    }
}
