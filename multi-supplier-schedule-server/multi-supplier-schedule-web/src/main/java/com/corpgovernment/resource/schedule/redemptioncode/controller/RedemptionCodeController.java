package com.corpgovernment.resource.schedule.redemptioncode.controller;

import com.corpgovernment.resource.schedule.redemptioncode.RedemptionCodeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/redemptionCode")
public class RedemptionCodeController {

    @Resource
    private RedemptionCodeService redemptionCodeService;

    /**
     * 定时发放兑换码
     */
    @RequestMapping("/sendRedemptionCode")
    public ReturnT<String> sendRedemptionCode() {
        log.info("start send redemption code");
        redemptionCodeService.sendRedemptionCode();
        return ReturnT.SUCCESS;
    }
}
