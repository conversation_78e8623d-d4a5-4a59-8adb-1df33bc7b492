package com.corpgovernment.resource.schedule.onlinereport.controller;

import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.risk.RiskOrderReportBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.JsonUtils;
import com.corpgovernment.resource.schedule.onlinereport.IndicatorSetDisplayService;
import lombok.extern.slf4j.Slf4j;
import onlinereport.enums.PageErrorCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * Auther:p_yin
 * Date:2022//29
 * Description:风险订单自定义展示指标
 * Project:
 */
@RestController
@RequestMapping("/risk")
@Slf4j
public class RiskOrderDetailColumnController {

    protected static final String LOG_TITLE = RiskOrderDetailColumnController.class.getSimpleName();


    @Autowired
    private IndicatorSetDisplayService indicatorSetDisplayService;



    @RequestMapping("/getCustomColumns")
    @ResponseBody
    public Object getCustomColumns(@RequestBody RiskOrderReportBO input, BaseUserInfo baseUserInfo) throws BusinessException {
        log.info(LOG_TITLE, " getCustomColumns param:" + JsonUtils.objectToString(input));
        try {
            String runAsUid = baseUserInfo.getUid();
            input.setUid(runAsUid);
            String lang = "";
            return JSONResult.success(indicatorSetDisplayService.query(input, lang));
        }catch (Exception e){
            log.error(LOG_TITLE, e);
            if (e instanceof BusinessException) {
                throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            } else {
                throw new BusinessException(PageErrorCodeEnum.Error.getValue(), PageErrorCodeEnum.Error.getDescription());
            }
        }
    }

    @RequestMapping(value = "/updateCustomColumns")
    @ResponseBody
    public Object updateCustomColumns(@RequestBody RiskOrderReportBO input, BaseUserInfo baseUserInfo) throws BusinessException {
        log.info(LOG_TITLE, " updateCustomColumns param:" + JsonUtils.objectToString(input));
        try {
            String runAsUid = baseUserInfo.getUid();
            input.setUid(runAsUid);
            return JSONResult.success(indicatorSetDisplayService.update(input));
        } catch (Exception e) {
            log.error(LOG_TITLE, e);
            if (e instanceof BusinessException) {
                throw new BusinessException(PageErrorCodeEnum.ParamError.getValue(), PageErrorCodeEnum.ParamError.getDescription());
            } else {
                throw new BusinessException(PageErrorCodeEnum.Error.getValue(), PageErrorCodeEnum.Error.getDescription());
            }
        }

    }



}
