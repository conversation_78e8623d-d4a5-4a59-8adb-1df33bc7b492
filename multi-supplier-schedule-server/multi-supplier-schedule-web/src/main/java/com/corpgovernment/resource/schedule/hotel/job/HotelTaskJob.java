package com.corpgovernment.resource.schedule.hotel.job;

import com.corpgovernment.resource.schedule.hotel.impl.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/28
 */
@Component
@Slf4j
public class HotelTaskJob {

    @Autowired
    private HotelStaticResourceTaskService hotelStaticResourceTaskService;

    @Autowired
    private HotelIdTaskService hotelIdTaskService;

    @Autowired
    private HotelMatchTaskService hotelMatchTaskService;

    @Autowired
    private HotelRoomMatchTaskService roomMatchTaskService;

    @Autowired
    private HotelSelfMatchTaskService hotelSelfMatchTaskService;

    @XxlJob("jobcenter.multisupplier.hotelidtask.create")
    public ReturnT<String> createHotelIdTask(String param) {
        log.info("酒店ID任务创建器开始执行");
        hotelIdTaskService.create();
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.hotelidtask.execute")
    public ReturnT<String> executeHotelIdTask(String param) {
        log.info("酒店ID任务执行器开始执行");
        hotelIdTaskService.execute();
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.hotelstaticresourcetask.execute")
    public ReturnT<String> executeHotelStaticResourceTask(String param) {
        log.info("酒店静态资源任务执行器开始执行");
        hotelStaticResourceTaskService.execute();
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.hotelmatchtask.execute")
    public ReturnT<String> executeHotelMatchTask(String param) {
        log.info("酒店匹配任务执行器开始执行");
        hotelMatchTaskService.execute();
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.roommatchtask.execute")
    public ReturnT<String> executeRoomMatchTask(String param) {
        log.info("房型匹配任务执行器开始执行");
        roomMatchTaskService.execute();
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.compensateTask")
    public ReturnT<String> compensateTask(String param) {
        log.info("任务补偿器开始执行 param={}", param);
        hotelIdTaskService.retryTask(Integer.valueOf(param));
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.releaseDeadLockTask")
    public ReturnT<String> releaseDeadLockTask(String param) {
        log.info("释放死锁任务开始执行");
        hotelIdTaskService.releaseUnFinishedTask();
        return ReturnT.SUCCESS;
    }

    @XxlJob("jobcenter.multisupplier.hotelselfmatchtask.create")
    public ReturnT<String> createHotelSelfMatchTask(String param) {
        log.info("酒店自匹配任务开始创建");
        hotelSelfMatchTaskService.create();
        return ReturnT.SUCCESS;
    }

//    @Scheduled(fixedDelay = 5000)
//    public void test1() {
//        hotelIdTaskService.execute();
//    }
//
//    @Scheduled(fixedDelay = 5000)
//    public void test2() {
//        hotelStaticResourceTaskService.execute();
//    }
//
//    @Scheduled(fixedDelay = 5000)
//    public void test3() {
//        hotelMatchTaskService.execute();
//    }
//
//    @Scheduled(fixedDelay = 5000)
//    public void test4() {
//        roomMatchTaskService.execute();
//    }

}
