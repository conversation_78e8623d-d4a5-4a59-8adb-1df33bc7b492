package com.corpgovernment.resource.schedule.execution.job;

import com.corpgovernment.resource.schedule.execution.IExecutionService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @create 2024-07-06 14:45
 */
@Component
@Slf4j
public class ExecutionJob {

    @Autowired
    private IExecutionService executionService;

    @XxlJob("multiSupplier.startTask")
    public ReturnT<String> startTask(String param) {
        log.info("定时任务 startTask");
        executionService.startTask();
        return ReturnT.SUCCESS;
    }

    @XxlJob("multiSupplier.submitExecutionPlan")
    public ReturnT<String> submitExecutionPlan(String param) {
        log.info("定时任务 submitExecutionPlan");
        executionService.submitExecutionPlan();
        return ReturnT.SUCCESS;
    }

    @XxlJob("multiSupplier.retryExecutionUnit")
    public ReturnT<String> retryExecutionUnit(String param) {
        log.info("定时任务 retryExecutionUnit");
        executionService.retryExecutionUnit();
        return ReturnT.SUCCESS;
    }
    
    @XxlJob("multiSupplier.manualStartTask")
    public ReturnT<String> manualStartTask(String param) {
        log.info("定时任务 manualStartTask param={}", param);
        executionService.manualStartTask(param);
        return ReturnT.SUCCESS;
    }

    @XxlJob("multiSupplier.clearDeadExecutionUnit")
    public ReturnT<String> clearDeadExecutionUnit(String param) {
        log.info("定时任务 clearDeadExecutionUnit");
        executionService.clearDeadExecutionUnit(param);
        return ReturnT.SUCCESS;
    }
    
    @XxlJob("multiSupplier.resetDeadExecutionUnit")
    public ReturnT<String> resetDeadExecutionUnit(String param) {
        log.info("定时任务 resetDeadExecutionUnit");
        executionService.resetDeadExecutionUnit(param);
        return ReturnT.SUCCESS;
    }
    
    @XxlJob("multiSupplier.loopStartMatchTask")
    public ReturnT<String> loopStartMatchTask(String param) {
        log.info("定时任务 loopStartMatchTask");
        executionService.loopStartMatchTask(param);
        return ReturnT.SUCCESS;
    }

}
