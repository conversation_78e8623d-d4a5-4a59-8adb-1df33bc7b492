package com.corpgovernment.resource.schedule.onlinereport.controller;


import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.domain.onlinereport.bo.common.BusinessException;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.bo.BaseQueryConditionBO;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.vo.tenant.TenantInfoVo;
import com.corpgovernment.resource.schedule.domain.onlinereport.entity.ChartExcelEntity;
import com.corpgovernment.resource.schedule.domain.onlinereport.utils.ExceLite;
import com.corpgovernment.resource.schedule.onlinereport.ReportDataNewAdaptorService;
import com.corpgovernment.resource.schedule.onlinereport.TenantInfoService;
import com.ctrip.corp.obt.generic.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

/**
 * @Auther: he_chen
 * @Date: 2019/8/9 16:17
 * @Description: 总概报表
 */
@Controller
@RequestMapping({"generalReport"})
@Slf4j
public class GeneralReportController {

    @Resource
    private ExceLite exceLite;

    @Autowired
    private ReportDataNewAdaptorService reportDataNewAdaptorService;


    @Autowired
    private TenantInfoService tenantInfoService;
    /**
     * 查询视图报表数据
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryNewReportData", method = RequestMethod.POST)
    @ResponseBody
    public JSONResult<Object> queryNewReportData(@RequestBody BaseQueryConditionBO request) throws BusinessException {
        String runAsUid = "";
        if (StringUtils.isNotEmpty(runAsUid)) {
            log.info("queryNewReportData", "RunAsLogon:" + runAsUid);
        }
        if (request.getBaseQueryCondition()!=null && !isValidDate(request.getBaseQueryCondition().getStartTime())) {
            throw new IllegalArgumentException("Invalid startTime format");
        }
        if (request.getBaseQueryCondition()!=null && !isValidDate(request.getBaseQueryCondition().getEndTime())) {
            throw new IllegalArgumentException("Invalid endTime format");
        }
        String lang = "";
        log.info("runAsUid {}、request {}、lang {}", runAsUid, request, lang);
        return JSONResult.success(reportDataNewAdaptorService.adaptor(runAsUid, request, lang));
    }

    /**
     * Query the base data last updated time
     */
    @RequestMapping(value = "/queryBaseDataLastUpdateTime", method = RequestMethod.POST)
    @ResponseBody
    public JSONResult<String> queryBaseDataLastUpdateTime() {
        return JSONResult.success(reportDataNewAdaptorService.queryBaseDataLastUpdateTime());
    }

    /**
     * 页面图表导出excel
     *
     * @param httpRequest
     * @param httpResponse
     * @return
     */
    @RequestMapping("/exportNewExcel")
    @ResponseBody
    public void exportNewExcel(HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws BusinessException {
        String reportCondition = httpRequest.getParameter("reportCondition");
        log.info("exportNewExcel", reportCondition);
        if (StringUtils.isEmpty(reportCondition)) {
            log.warn("exportExcel", "reportCondition is empty");
            return;
        }
        try {
            String lang = "";
            BaseQueryConditionBO request = JsonUtils.parse(reportCondition, BaseQueryConditionBO.class);
            String runAsUid = "";
            String formFileName = URLEncoder.encode(reportDataNewAdaptorService.getFileName(runAsUid, request, lang) + ".xls", "UTF-8");
            httpResponse.setContentType("APPLICATION/vnd.ms-excel;charset=UTF-8");
            httpResponse.setHeader("Content-disposition", "attachment; filename=" + formFileName);
            HSSFWorkbook workbook = new HSSFWorkbook();
            List<ChartExcelEntity> chartExcelEntityList = reportDataNewAdaptorService.exportExcel(runAsUid, request, lang);
            OutputStream os = httpResponse.getOutputStream();
            if (CollectionUtils.isEmpty(chartExcelEntityList)) {
                workbook.createSheet();
            } else {
                for (ChartExcelEntity entity : chartExcelEntityList) {
                    exceLite.exportMergeExcel(workbook, entity.getSheetNum(), entity.getSheetTitle(), entity.getHeaders(),
                            entity.getRangeHeaders(), entity.getData(), entity.getRangedata());
                }
            }
            workbook.write(os);
            os.flush();
        } catch (Exception e) {
            log.error("exportExcel-request-error", e);
        }
    }
    private boolean isValidDate(String dateStr) {
        try {
            LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }


    @RequestMapping(value = "/queryTenantInfo", method = RequestMethod.POST)
    @ResponseBody
    public JSONResult<TenantInfoVo> queryTenantInfo() {

        return JSONResult.success(tenantInfoService.getTenantNameByTenantId());
    }

}
