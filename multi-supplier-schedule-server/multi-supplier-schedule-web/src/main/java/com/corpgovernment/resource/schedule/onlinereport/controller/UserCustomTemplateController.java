package com.corpgovernment.resource.schedule.onlinereport.controller;

import com.corpgovernment.common.base.BaseUserInfo;
import com.corpgovernment.common.base.JSONResult;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.dto.orderdetail.usertemplate.ChangeCustomTemplateDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.dto.orderdetail.usertemplate.DeleteCustomTemplateDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.dto.orderdetail.usertemplate.QueryCustomTemplateDto;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.vo.usertemplate.ChangeCustomTemplateVo;
import com.corpgovernment.resource.schedule.domain.onlinereport.domainreport.vo.usertemplate.DeleteCustomTemplateVo;
import com.corpgovernment.resource.schedule.onlinereport.usertemplate.UserCustomTemplateService;
import com.ctrip.corp.obt.generic.core.context.UserInfoContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户自定义模版controller
 *
 * <AUTHOR>
 * @date 2024/12/5
 */
@RestController
@RequestMapping("/reportlib/user/template")
@Slf4j
public class UserCustomTemplateController {
    @Resource
    private UserCustomTemplateService userCustomTemplateService;

    /**
     * 变更模版（新增/变更）
     *
     * @param request
     * @return
     */
    @PostMapping("/change")
    Object changeCustomTemplate(@RequestBody @Validated ChangeCustomTemplateDto request, BaseUserInfo baseUserInfo) throws Exception {

        String uid = baseUserInfo.getUid();
        String language = "";
        ChangeCustomTemplateVo changeCustomTemplateVo = userCustomTemplateService.changeCustomTemplate(request, uid, language);
        if (changeCustomTemplateVo.getResult()){
            return JSONResult.success(changeCustomTemplateVo);
        }
        return JSONResult.errorMsg(changeCustomTemplateVo.getResultMsg());
    }

    /**
     * 删除模版
     *
     * @param request
     * @return
     */
    @PostMapping("/delete")
    Object deleteCustomTemplate(@RequestBody @Validated DeleteCustomTemplateDto request, BaseUserInfo baseUserInfo) throws Exception {
        DeleteCustomTemplateVo deleteCustomTemplateVo = userCustomTemplateService.deleteCustomTemplate(request);
        if (deleteCustomTemplateVo.getResult()){
            return JSONResult.success(deleteCustomTemplateVo);
        }
        return JSONResult.errorMsg(deleteCustomTemplateVo.getResultMsg());
    }

    /**
     * 查询模版
     *
     * @param request
     * @return
     */
    @PostMapping("/list")
    Object queryCustomTemplate(@RequestBody QueryCustomTemplateDto request,BaseUserInfo baseUserInfo) throws Exception {
        String uid = baseUserInfo.getUid();
        request.setUid(uid);
        return JSONResult.success(userCustomTemplateService.queryCustomTemplate(request, uid));
    }

}
