package com.corpgovernment.resource.schedule.pricecomparison.job;

import com.corpgovernment.resource.schedule.domain.pricecomparison.enums.ComparePriceProductTypeEnum;
import com.corpgovernment.resource.schedule.pricecomparison.FlightHotelPriceComparisonService;
import com.corpgovernment.resource.schedule.pricecomparison.FlightTopCompareService;
import com.ctrip.corp.obt.generic.core.context.TenantContext;
import com.ctrip.corp.obt.generic.core.context.TraceContext;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@Slf4j
public class PriceComparisonJob {
    @Autowired
    private FlightHotelPriceComparisonService flightHotelPriceComparisonService;

    @Autowired
    private FlightTopCompareService flightTopCompareService;

    /**
     * 获取机票和酒店比价任务
     * @param param
     * @return
     */
    @XxlJob("jobcenter.multisupplier.flight.hotel.price.comparison")
    public ReturnT<String> flightHotelPriceComparison(String param) {
        TraceContext.setRequestId(String.valueOf(UUID.randomUUID()));
        log.info("获取机票和酒店比价任务开始执行");
        flightHotelPriceComparisonService.flightHotelPriceComparison(param);
        return ReturnT.SUCCESS;
    }

    /**
     * 航线top100比价主任务（每8天执行一次）
     * 1. 获取Apollo配置
     * 2. 查询top100航线
     * 3. 组装任务数据
     * 4. 批量插入任务数据
     * @param param
     * @return
     */
    @XxlJob("jobcenter.multisupplier.flight.top.compare.task.create")
    public ReturnT<String> createFlightTopCompareTask(String param) {
        TraceContext.setRequestId(String.valueOf(UUID.randomUUID()));
        log.info("开始执行航线top100比价主任务（每8天一次）");
        flightTopCompareService.createFlightTopCompareTask(param);
        log.info("结束执行航线top100比价主任务（每8天一次）");
        return ReturnT.SUCCESS;
    }


    /**
     * 航线top100比价子任务（30秒执行一次）
     * 查询带执行任务执行供应商调用
     * @param param
     * @return
     */
    @XxlJob("jobcenter.multisupplier.flight.top.compare.task.execute")
    public ReturnT<String> executeFlightTopCompareTask(String param) {
        TraceContext.setRequestId(String.valueOf(UUID.randomUUID()));
        log.info("开始执行航线top100比价子任务---");
        Boolean flag = flightHotelPriceComparisonService.executeFlightHotelCompareTask(ComparePriceProductTypeEnum.FLIGHT_TOP.getName());
        log.info("结束执行航线top100比价子任务: {}", flag);
        return ReturnT.SUCCESS;
    }

    /**
     * 指定比价子任务（30秒执行一次）
     * 查询带执行任务执行供应商调用
     * @param param
     * @return
     */
    @XxlJob("jobcenter.multisupplier.flight.price.compare.task.execute")
    public ReturnT<String> executeFlightPriceCompareTask(String param) {
        TraceContext.setRequestId(String.valueOf(UUID.randomUUID()));
        log.info("开始执行机票指定比价子任务---");
        Boolean flag = flightHotelPriceComparisonService.executeFlightHotelCompareTask(ComparePriceProductTypeEnum.FLIGHT.getName());
        log.info("结束执行机票指定比价子任务: {}", flag);
        return ReturnT.SUCCESS;
    }

    /**
     * 指定比价子任务（30秒执行一次）
     * 查询带执行任务执行供应商调用
     * @param param
     * @return
     */
    @XxlJob("jobcenter.multisupplier.hotel.price.compare.task.execute")
    public ReturnT<String> executeHotelPriceCompareTask(String param) {
        TraceContext.setRequestId(String.valueOf(UUID.randomUUID()));
        log.info("开始执行酒店指定比价子任务---");
        Boolean flag = flightHotelPriceComparisonService.executeFlightHotelCompareTask(ComparePriceProductTypeEnum.HOTEL.getName());
        log.info("结束执行酒店指定比价子任务: {}", flag);
        return ReturnT.SUCCESS;
    }
}
